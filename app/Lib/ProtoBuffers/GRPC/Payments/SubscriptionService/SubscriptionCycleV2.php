<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: subscription_service.proto

namespace GRPC\Payments\SubscriptionService;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>grpc.SubscriptionCycleV2</code>
 */
class SubscriptionCycleV2 extends \Google\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>int32 ID = 1;</code>
     */
    protected $ID = 0;
    /**
     * Generated from protobuf field <code>string PeriodStart = 2;</code>
     */
    protected $PeriodStart = '';
    /**
     * Generated from protobuf field <code>string PeriodEnd = 3;</code>
     */
    protected $PeriodEnd = '';
    /**
     * Generated from protobuf field <code>string EventID = 4;</code>
     */
    protected $EventID = '';
    /**
     * Generated from protobuf field <code>int32 SubscriptionID = 5;</code>
     */
    protected $SubscriptionID = 0;
    /**
     * Generated from protobuf field <code>int32 CurrentRetries = 6;</code>
     */
    protected $CurrentRetries = 0;
    /**
     * Generated from protobuf field <code>.grpc.SubscriptionCycleStatus Status = 7;</code>
     */
    protected $Status = 0;
    /**
     * Generated from protobuf field <code>.grpc.SubscriptionCycleRetryStyle RetryStyle = 8;</code>
     */
    protected $RetryStyle = 0;
    /**
     * Generated from protobuf field <code>.grpc.SubscriptionCycleConfirmation Confirmed = 9;</code>
     */
    protected $Confirmed = 0;
    /**
     * Generated from protobuf field <code>.grpc.SubscriptionCycleLatestTrx LatestTransaction = 10 [deprecated = true];</code>
     * @deprecated
     */
    protected $LatestTransaction = null;
    /**
     * Generated from protobuf field <code>string InvoiceID = 11;</code>
     */
    protected $InvoiceID = '';
    /**
     * Generated from protobuf field <code>uint32 CycleNum = 12;</code>
     */
    protected $CycleNum = 0;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int $ID
     *     @type string $PeriodStart
     *     @type string $PeriodEnd
     *     @type string $EventID
     *     @type int $SubscriptionID
     *     @type int $CurrentRetries
     *     @type int $Status
     *     @type int $RetryStyle
     *     @type int $Confirmed
     *     @type \GRPC\Payments\SubscriptionService\SubscriptionCycleLatestTrx $LatestTransaction
     *     @type string $InvoiceID
     *     @type int $CycleNum
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\SubscriptionService::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>int32 ID = 1;</code>
     * @return int
     */
    public function getID()
    {
        return $this->ID;
    }

    /**
     * Generated from protobuf field <code>int32 ID = 1;</code>
     * @param int $var
     * @return $this
     */
    public function setID($var)
    {
        GPBUtil::checkInt32($var);
        $this->ID = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string PeriodStart = 2;</code>
     * @return string
     */
    public function getPeriodStart()
    {
        return $this->PeriodStart;
    }

    /**
     * Generated from protobuf field <code>string PeriodStart = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setPeriodStart($var)
    {
        GPBUtil::checkString($var, True);
        $this->PeriodStart = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string PeriodEnd = 3;</code>
     * @return string
     */
    public function getPeriodEnd()
    {
        return $this->PeriodEnd;
    }

    /**
     * Generated from protobuf field <code>string PeriodEnd = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setPeriodEnd($var)
    {
        GPBUtil::checkString($var, True);
        $this->PeriodEnd = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string EventID = 4;</code>
     * @return string
     */
    public function getEventID()
    {
        return $this->EventID;
    }

    /**
     * Generated from protobuf field <code>string EventID = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setEventID($var)
    {
        GPBUtil::checkString($var, True);
        $this->EventID = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 SubscriptionID = 5;</code>
     * @return int
     */
    public function getSubscriptionID()
    {
        return $this->SubscriptionID;
    }

    /**
     * Generated from protobuf field <code>int32 SubscriptionID = 5;</code>
     * @param int $var
     * @return $this
     */
    public function setSubscriptionID($var)
    {
        GPBUtil::checkInt32($var);
        $this->SubscriptionID = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 CurrentRetries = 6;</code>
     * @return int
     */
    public function getCurrentRetries()
    {
        return $this->CurrentRetries;
    }

    /**
     * Generated from protobuf field <code>int32 CurrentRetries = 6;</code>
     * @param int $var
     * @return $this
     */
    public function setCurrentRetries($var)
    {
        GPBUtil::checkInt32($var);
        $this->CurrentRetries = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>.grpc.SubscriptionCycleStatus Status = 7;</code>
     * @return int
     */
    public function getStatus()
    {
        return $this->Status;
    }

    /**
     * Generated from protobuf field <code>.grpc.SubscriptionCycleStatus Status = 7;</code>
     * @param int $var
     * @return $this
     */
    public function setStatus($var)
    {
        GPBUtil::checkEnum($var, \GRPC\Payments\SubscriptionService\SubscriptionCycleStatus::class);
        $this->Status = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>.grpc.SubscriptionCycleRetryStyle RetryStyle = 8;</code>
     * @return int
     */
    public function getRetryStyle()
    {
        return $this->RetryStyle;
    }

    /**
     * Generated from protobuf field <code>.grpc.SubscriptionCycleRetryStyle RetryStyle = 8;</code>
     * @param int $var
     * @return $this
     */
    public function setRetryStyle($var)
    {
        GPBUtil::checkEnum($var, \GRPC\Payments\SubscriptionService\SubscriptionCycleRetryStyle::class);
        $this->RetryStyle = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>.grpc.SubscriptionCycleConfirmation Confirmed = 9;</code>
     * @return int
     */
    public function getConfirmed()
    {
        return $this->Confirmed;
    }

    /**
     * Generated from protobuf field <code>.grpc.SubscriptionCycleConfirmation Confirmed = 9;</code>
     * @param int $var
     * @return $this
     */
    public function setConfirmed($var)
    {
        GPBUtil::checkEnum($var, \GRPC\Payments\SubscriptionService\SubscriptionCycleConfirmation::class);
        $this->Confirmed = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>.grpc.SubscriptionCycleLatestTrx LatestTransaction = 10 [deprecated = true];</code>
     * @return \GRPC\Payments\SubscriptionService\SubscriptionCycleLatestTrx|null
     * @deprecated
     */
    public function getLatestTransaction()
    {
        if (isset($this->LatestTransaction)) {
            @trigger_error('LatestTransaction is deprecated.', E_USER_DEPRECATED);
        }
        return $this->LatestTransaction;
    }

    public function hasLatestTransaction()
    {
        if (isset($this->LatestTransaction)) {
            @trigger_error('LatestTransaction is deprecated.', E_USER_DEPRECATED);
        }
        return isset($this->LatestTransaction);
    }

    public function clearLatestTransaction()
    {
        @trigger_error('LatestTransaction is deprecated.', E_USER_DEPRECATED);
        unset($this->LatestTransaction);
    }

    /**
     * Generated from protobuf field <code>.grpc.SubscriptionCycleLatestTrx LatestTransaction = 10 [deprecated = true];</code>
     * @param \GRPC\Payments\SubscriptionService\SubscriptionCycleLatestTrx $var
     * @return $this
     * @deprecated
     */
    public function setLatestTransaction($var)
    {
        @trigger_error('LatestTransaction is deprecated.', E_USER_DEPRECATED);
        GPBUtil::checkMessage($var, \GRPC\Payments\SubscriptionService\SubscriptionCycleLatestTrx::class);
        $this->LatestTransaction = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string InvoiceID = 11;</code>
     * @return string
     */
    public function getInvoiceID()
    {
        return $this->InvoiceID;
    }

    /**
     * Generated from protobuf field <code>string InvoiceID = 11;</code>
     * @param string $var
     * @return $this
     */
    public function setInvoiceID($var)
    {
        GPBUtil::checkString($var, True);
        $this->InvoiceID = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>uint32 CycleNum = 12;</code>
     * @return int
     */
    public function getCycleNum()
    {
        return $this->CycleNum;
    }

    /**
     * Generated from protobuf field <code>uint32 CycleNum = 12;</code>
     * @param int $var
     * @return $this
     */
    public function setCycleNum($var)
    {
        GPBUtil::checkUint32($var);
        $this->CycleNum = $var;

        return $this;
    }

}

