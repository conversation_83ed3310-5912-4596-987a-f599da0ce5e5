<?php

declare(strict_types=1);

namespace Glofox\Infrastructure\GoogleRecaptcha\Exception;

use Glofox\Infrastructure\GoogleRecaptcha\Enum\ErrorCode;

class CouldNotVerifyRecaptcha extends \RuntimeException
{
    private const MESSAGE = 'Could not verify recaptcha due to error(s): %s';

    private function __construct(string $message, int $code = 0, \Throwable $previous = null)
    {
        parent::__construct($message, $code, $previous);
    }

    public static function wrap(\Throwable $throwable): self
    {
        return new self(
            \sprintf(self::MESSAGE, $throwable->getMessage()),
            $throwable->getCode(),
            $throwable
        );
    }

    /**
     * @param array<int, ErrorCode::*> $errorCodes
     */
    public static function dueToErrors(array $errorCodes): self
    {
        return new self(
            \sprintf(self::MESSAGE, implode(',', $errorCodes))
        );
    }
}