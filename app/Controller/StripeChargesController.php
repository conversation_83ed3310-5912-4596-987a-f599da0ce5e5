<?php

use Aws\Sns\MessageValidator;
use Glofox\Authentication\Exceptions\InvalidTokenDataException;
use Glofox\Datasource\Exceptions\InvalidMongoIdException;
use Glofox\Domain\Authentication\Auth;
use Glofox\Domain\Branches\Exceptions\BranchNotFoundException;
use Glofox\Domain\Branches\Models\Branch;
use Glofox\Domain\Branches\Repositories\BranchesRepository;
use Glofox\Domain\Charges\Events\ChargeReceiptWasRequested;
use Glofox\Domain\Charges\Events\ChargeWasRefunded;
use Glofox\Domain\Charges\Exceptions\ChargeNotFoundException;
use Glofox\Domain\Charges\Handlers\Commands\FindByIntent;
use Glofox\Domain\Charges\Handlers\Commands\ResolvePurchasedItem;
use Glofox\Domain\Charges\Handlers\FindByIntentHandler;
use Glofox\Domain\Charges\Handlers\ResolvePurchasedItemHandler;
use Glofox\Domain\Charges\Handlers\ResolvePurchasedPaymentMethodHandler;
use Glofox\Domain\Charges\Http\RequestAuthorisationAction;
use Glofox\Domain\Charges\Models\Charge;
use Glofox\Domain\Charges\Models\ChargesGroup;
use Glofox\Domain\Charges\Repositories\ChargesRepository;
use Glofox\Domain\Charges\Requests\FindByIntentRequest;
use Glofox\Domain\Charges\Requests\FindByInvoiceRequest;
use Glofox\Domain\Charges\Requests\FindByTransactionProvider;
use Glofox\Domain\Charges\Requests\RequestAuthorisationRequest;
use Glofox\Domain\Charges\Requests\ResendAuthorisationRequest;
use Glofox\Domain\Charges\Requests\SyncRequest;
use Glofox\Domain\Charges\Search\Expressions\InvoiceId;
use Glofox\Domain\Charges\Search\Expressions\MetadataBranchId;
use Glofox\Domain\Charges\Search\Expressions\MetadataNamespace;
use Glofox\Domain\Charges\Search\Expressions\MetadataUserId;
use Glofox\Domain\Charges\Search\Expressions\TransactionGroupId;
use Glofox\Domain\Charges\Search\Expressions\TransactionProviderId;
use Glofox\Domain\Charges\Services\ChargeSynchronizer;
use Glofox\Domain\FeatureFlags\FeatureFlagInterface;
use Glofox\Domain\Invoice\Services\InvoiceServiceInterface;
use Glofox\Domain\Locker\WebhookLockerInterface;
use Glofox\Domain\PaymentMethods\Exceptions\PaymentMethodNotFoundException;
use Glofox\Domain\PaymentMethods\Repositories\PaymentMethodsRepository;
use Glofox\Domain\PaymentMethods\Search\Expressions\ProviderAccountId;
use Glofox\Domain\PaymentMethodUsers\Repository\PaymentMethodUsersRepository;
use Glofox\Domain\PaymentMethodUsers\Search\Expressions\CustomerProviderId;
use Glofox\Domain\PaymentProviders\Exceptions\PaymentProviderNotFoundException;
use Glofox\Domain\Refunds\Repositories\RefundsRepository;
use Glofox\Domain\Refunds\Status;
use Glofox\Domain\SalesTaxes\Models\PriceBreakdown;
use Glofox\Domain\SalesTaxes\Repositories\PriceBreakdownsRepository;
use Glofox\Domain\SalesTaxes\Search\Expressions\ChargeId;
use Glofox\Domain\Transactional\Templates\Views\ReceiptTemplateAttachmentV2;
use Glofox\Domain\Transactional\Templates\Views\ReceiptTemplateAttachmentV3;
use Glofox\Domain\Translation\Translator;
use Glofox\Domain\Users\Exceptions\UserNotFoundException;
use Glofox\Domain\Users\Models\User;
use Glofox\Domain\Users\Repositories\UsersRepository;
use Glofox\Domain\Users\Validation\Guard\ReceptionistRestrictionRevenueGuard;
use Glofox\Events\EventManager;
use Glofox\Exception as GlofoxException;
use Glofox\Export\Exporter;
use Glofox\Export\Strategies\DomPdfStrategy;
use Glofox\Http\Proxy\NotAuthorisedException;
use Glofox\Infrastructure\Flags\Flag;
use Glofox\Infrastructure\Services\PriceCalculatorHttpService;
use Glofox\Payments\Entities\Transaction\Exceptions\ChargeRetrievalException;
use Glofox\Payments\Entities\WebHook\Models\WebhookEvent;
use Glofox\Payments\Exceptions\CreditPackAlreadyExistsException;
use Glofox\Payments\Exceptions\NoClassHandlerForEventTypeException;
use Glofox\Payments\Exceptions\UnsupportedPaymentMethodTypeException;
use Glofox\Payments\Exceptions\WebhookEventAlreadyReceivedException;
use Glofox\Payments\Services\InvoiceForgiverUseCase;
use Glofox\Payments\Services\InvoiceRetryUseCase;
use Glofox\Payments\Util;
use Glofox\Repositories\Search\Expressions\Shared\Id;
use Glofox\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;
use Psr\Log\LoggerInterface;

App::uses('AppController', 'Controller');


/**
 * StripeChargesController class.
 *
 * @uses AppController
 *
 * @property Branch $Branch
 * @property Membership $Membership
 * @property StripeCharge $StripeCharge
 * @property User $User
 * @property JWTComponent $JWT
 */
class StripeChargesController extends AppController
{
    /**
     * @var array<int, string>
     */
    public $components = [
        'Notification',
        'Utility',
        'JWT',
    ];

    private LoggerInterface $logger;
    private EventManager $eventManager;
    private ChargeSynchronizer $chargeSynchronizer;
    private ReceptionistRestrictionRevenueGuard $receptionistRevenueActionRestrictionGuard;
    private FeatureFlagInterface $flagger;

    public function __construct($request, $response)
    {
        parent::__construct($request, $response);

        $this->logger = app()->make(LoggerInterface::class);
        $this->eventManager = app()->make(EventManager::class);
        $this->chargeSynchronizer = app()->make(ChargeSynchronizer::class);
        $this->receptionistRevenueActionRestrictionGuard = app()->make(ReceptionistRestrictionRevenueGuard::class);
        $this->flagger = app()->make(FeatureFlagInterface::class);
    }

    /**
     * Dispatcher for Model.
     *
     * @param string $identifier [description]
     *
     * @return string
     */
    public function dispatcher($identifier = null)
    {
        return parent::dispatch($identifier, $this->StripeCharge);
    }

    public function findByIntent(FindByIntentRequest $request, FindByIntentHandler $handler): JsonResponse
    {
        $data = $handler->handle(
            new FindByIntent(
                $request->intentId(),
                $request->paymentMethodId(),
                $request->customerProviderId(),
            )
        );

        return new JsonResponse([
            'data' => $data->toArray(),
        ], 200);
    }

    /**
     * @param FindByInvoiceRequest $request
     * @param ChargesRepository $repository
     * @return JsonResponse
     * @throws ChargeNotFoundException
     * @throws NotAuthorisedException
     */
    public function findByInvoiceId(
        FindByInvoiceRequest $request,
        ChargesRepository $repository
    ): JsonResponse {
        $authUser = Auth::user();
        if (!$authUser || !$authUser->isStaff()) {
            throw new NotAuthorisedException();
        }

        $invoiceId = $request->invoiceId();
        $charges = $repository
            ->addCriteria(new InvoiceId($invoiceId))
            ->addCriteria(new MetadataBranchId($authUser->currentBranchId()))
            ->order(['created' => -1])
            ->fetch();

        if (!$charges) {
            throw ChargeNotFoundException::withInvoiceId($invoiceId);
        }

        return new JsonResponse([
            'charges' => $charges,
        ], 200);
    }

    /**
     * @throws NotAuthorisedException
     */
    public function findTransactionProviderId(
        FindByTransactionProvider $request,
        ChargesRepository $repository
    ): JsonResponse {
        $authUser = Auth::user();
        if (!$authUser || !$authUser->isAdmin()) {
            throw new NotAuthorisedException();
        }

        $transactionProviderId = $request->transactionProviderId();
        $charge = $repository
            ->addCriteria(new TransactionProviderId($transactionProviderId))
            ->addCriteria(new MetadataBranchId($authUser->currentBranchId()))
            ->order(['created' => -1])
            ->firstOrFail(function () use ($transactionProviderId) {
                throw ChargeNotFoundException::withTransactionProviderId($transactionProviderId);
            });

        return new JsonResponse([
            'data' => $charge->toArray(),
        ], 200);
    }

    /**
     * @throws ChargeRetrievalException
     * @throws InvalidMongoIdException
     */
    public function resendAuthorisation(
        ResendAuthorisationRequest $request,
        ChargesRepository $chargesRepository,
        RequestAuthorisationAction $action
    ): JsonResponse {
        /** @var Charge $charge */
        $charge = $chargesRepository
            ->addCriteria(new Id($request->chargeId()))
            ->firstOrFail();

        $paymentHandler = payments()->provider($charge->branchId(), $charge->paymentMethod(false));

        try {
            $providerTransaction = $paymentHandler->charges()->getById($charge->transactionProviderId());
        } catch (\Exception $exception) {
            logs()->push(sprintf('Payment %s, not found', $charge->transactionProviderId()));
            throw new ChargeRetrievalException($exception->getMessage());
        }

        $clientSecret = collect(
            json_decode(
                $providerTransaction->serviceProviderResponse(),
                true,
                512,
                JSON_PARTIAL_OUTPUT_ON_ERROR
            )
        )->get('client_secret');

        if (empty($clientSecret)) {
            throw new ChargeRetrievalException(sprintf('Charge %s does not contain a client_secret', $charge->id()));
        }

        $actionReq = new RequestAuthorisationRequest([
            'payment_method_id' => $paymentHandler->paymentMethod()->id(),
            'payment_intent_id' => $providerTransaction->serviceProviderIntentId(),
            'client_secret' => $clientSecret,
        ]);

        $status = $action($actionReq)->getStatusCode();

        return new JsonResponse([
            'success' => $status === 200,
        ], $status);
    }

    /**
     * @return JsonResponse
     *
     * @throws UnsuccessfulOperation
     * @throws InvalidMongoIdException
     * @throws PaymentMethodNotFoundException
     * @throws PaymentProviderNotFoundException
     */
    public function webhooksPaymentsSNS()
    {
        $message = \Aws\Sns\Message::fromRawPostData();
        $validator = new MessageValidator();

        try {
            $validator->validate($message);
        } catch (Aws\Sns\Exception\InvalidSnsMessageException $e) {
            \CakeLog::write(LOG_ERR, $e->getMessage());
            $exception = new UnsuccessfulOperation('SNS Message Validation Error: ' . $e->getMessage());
            $exception->setCode(400);
            throw $exception;
        }

        if ('SubscriptionConfirmation' === $message['Type']) {
            \CakeLog::write(LOG_INFO, 'SNS SubscriptionConfirmation to ' . $message['SubscribeURL']);
            $status = (new \GuzzleHttp\Client())->get($message['SubscribeURL'])->getStatusCode();

            return response()->json(
                [],
                $status
            );
        }

        $payload = collect(json_decode($message['Message'], true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR));

        return $this->processGatewayWebhookPayload($payload);
    }

    public function refund($stripeChargeId)
    {
        $request = Request::capture();

        $isValidMongoid = $this->Utility->is_valid_mongoid($stripeChargeId);
        if (empty($isValidMongoid)) {
            return json_encode(
                ['success' => false, 'message' => __('Invalid Charge Id')],
                JSON_PARTIAL_OUTPUT_ON_ERROR
            );
        }

        $user = Auth::user();
        // to avoid issues with roaming users we use namespace instead of branch_id
        $namespace = $user->namespace() != '' ? $user->namespace() : '';
        // staff should not have originBranchId so we use currentBranchId
        $branchId = $user->currentBranchId() != '' ? $user->currentBranchId() : '';

        if (!($user->isAdmin() || $user->isReceptionist() || $user->isSuperAdmin())) {
            throw new UnauthorizedException();
        }
        $this->receptionistRevenueActionRestrictionGuard->check($user);

        try {
            $refundAmount = $request->get('amount', 0);

            if (!is_numeric($refundAmount)) {
                throw new Exception('Refund amount requested does not have a valid value');
            }

            $refundAmount = round((float)$refundAmount, 2);
            if ($refundAmount <= 0) {
                throw new Exception('Refund amount requested cannot be lower than or equal to zero');
            }

            $chargesRepository = app()->make(ChargesRepository::class);
            $chargesRepository->addCriteria(new Id($stripeChargeId));
            // Check stripe charge belongs to user's namespace
            $chargesRepository->addCriteria(new MetadataNamespace($namespace));

            /** @var Charge $charge */
            $charge = $chargesRepository
                ->skipCallbacks()
                ->firstOrFail(function () use ($stripeChargeId) {
                    throw ChargeNotFoundException::withId($stripeChargeId);
                });

            $metadata = $charge->metadata();
            $paymentProvider = Auth::payments($metadata['payment_method']);

            $doesRefundedAmountExceedTotal = round($charge->amountRefunded() + $refundAmount, 2) > round(
                $charge->amount(),
                2
            );

            // handle the case of Cash, Bank Transfer where provider is not restricting the refund amount
            if ($doesRefundedAmountExceedTotal) {
                throw new Exception('Refund amount requested is more than charge amount');
            }

            // execute refund
            $refund = $paymentProvider->charges()->refund($charge->transactionProviderId(), $refundAmount);
            $refundAmount = empty($refund->amount()) ? $refundAmount : Util::convertFromCents($refund->amount());

            $charge->put('transaction_status', Glofox\Domain\Charges\Status::PARTIAL_REFUNDED);

            $charge->recordRefundAmount($refundAmount);

            if ($charge->isFullyRefunded()) {
                $charge->put('transaction_status', Glofox\Domain\Charges\Status::REFUNDED);
            }

            $charge['paid'] = false;
            $charge['refunded'] = true;

            // capture the refund record
            $refundsRepository = app()->make(RefundsRepository::class);
            $refundId = new MongoId();
            $refundsRepository->legacySaveOrFail([
                '_id' => $refundId,
                'refund_id' => (string)$refundId,
                'parent_id' => $charge->id(),
                'namespace' => $metadata['namespace'],
                'branch_id' => $metadata['branch_id'],
                'user_id' => $metadata['user_id'],
                'refund_user_id' => $user['_id'],
                'amount' => $refundAmount > 0 ? $refundAmount : $charge->amount(),
                'currency' => $charge->currency(),
                'payment_method' => $metadata['payment_method'],
                'status' => Status::SUCCESS,
            ]);

            // update the charge
            $charge->addRefundId((string)$refundId);
            $charge = $chargesRepository->legacySaveOrFail($charge->toArray());
            $charge = Charge::make($charge['StripeCharge']);

            $this->eventManager->emit(ChargeWasRefunded::class, [$charge]);

            $this->logger->info(sprintf("Charge %s was refunded.", $charge->id()));

            return response()->json([
                'success' => true,
                'message_code' => 'CHARGE_SUCCESSFULLY_REFUNDED',
            ]);
        } catch (Exception $exception) {
            $this->logger->error('Error handling refund', [
                'chargeId' => $stripeChargeId,
                'branchId' => $branchId,
                'error' => $exception->getMessage(),
                'trace' => $exception->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message_code' => $exception->getMessage(),
            ]);
        }
    }

    /**
     * @return JsonResponse
     *
     * @throws UnsuccessfulOperation
     * @throws InvalidMongoIdException
     * @throws PaymentMethodNotFoundException
     * @throws PaymentProviderNotFoundException
     * @throws \Glofox\NotFoundException
     * @throws UnsupportedPaymentMethodTypeException
     */
    public function retry(string $userId, string $invoiceId)
    {
        $user = Auth::user();
        // to avoid issues with roaming users we use namespace instead of branch_id
        $namespace = $user->namespace() != '' ? $user->namespace() : '';

        if (!($user->isAdmin() || $user->isReceptionist() || $user->isSuperAdmin())) {
            throw new UnauthorizedException();
        }

        $chargesRepository = app()->make(ChargesRepository::class);
        $chargesRepository->addCriteria(new MetadataUserId($userId))
            ->addCriteria(new InvoiceId($invoiceId))
            // Check stripe charge belongs to user's branch
            ->addCriteria(new MetadataNamespace($namespace));

        /** @var Charge $charge */
        $charge = $chargesRepository
            ->skipCallbacks()
            ->firstOrFail(function () use ($userId, $invoiceId) {
                $errorMsg = sprintf('Charge not found for %s - %s', $invoiceId, $userId);
                throw new \Glofox\NotFoundException($errorMsg);
            });

        $paymentHandler = Auth::payments($charge->paymentMethod(false));
        $invoiceRetry = new InvoiceRetryUseCase($paymentHandler);

        $this->logger->info(
            sprintf("Invoice [%s] for user [%s] being retried by requester [%s]", $invoiceId, $userId, $user->id())
        );

        $isPaid = $invoiceRetry->retry($charge, $userId, $user);

        $this->chargeSynchronizer->byInvoice($paymentHandler, $invoiceId, $isPaid);

        return response()->json([
            'success' => true,
            'message_code' => 'RETRY_TRANSACTION_SUCCESS',
            'message' => 'Transaction Retry Success',
        ]);
    }

    /**
     * @return JsonResponse
     *
     * @throws InvalidMongoIdException
     * @throws PaymentMethodNotFoundException
     * @throws PaymentProviderNotFoundException
     * @throws \Glofox\NotFoundException
     * @throws UnsupportedPaymentMethodTypeException
     */
    public function forgive(string $userId, string $invoiceId)
    {
        $user = Auth::user();
        // to avoid issues with roaming users we use namespace instead of branch_id
        $namespace = $user->namespace() !== '' ? $user->namespace() : '';

        if (!$user->isAdmin()) {
            throw new UnauthorizedException();
        }

        $this->receptionistRevenueActionRestrictionGuard->check($user);

        $chargesRepository = app()->make(ChargesRepository::class);
        $chargesRepository
            ->addCriteria(new MetadataUserId($userId))
            ->addCriteria(new InvoiceId($invoiceId))
            ->addCriteria(new MetadataNamespace($namespace));

        /** @var Charge $charge */
        $charge = $chargesRepository
            ->skipCallbacks()
            ->firstOrFail(function () use ($userId, $invoiceId) {
                $errorMsg = sprintf('Charge not found for %s - %s', $invoiceId, $userId);
                throw new \Glofox\NotFoundException($errorMsg);
            });

        $request = Request::capture();
        $description = $request->get('description', '');

        $paymentHandler = Auth::payments($charge->paymentMethod(false));
        $invoiceForgiver = new InvoiceForgiverUseCase($paymentHandler);

        // The following try-catch block will prevent the API call to fail when the invoice service returns:
        // "invoice '<invoice_id>' with status 'FORGIVEN' cannot be forgiven".
        // This error message can be safely ignored, since the invoice was already forgiven, but MongoDB is out of sync.
        // The subsequent call to the chargeSynchronizer->byInvoice() will re-sync the databases.
        // For additional info, check CI-3865
        try {
            $invoiceForgiver->forgive($charge, $invoiceId, $description);
        } catch (Exception $e) {
            if (!str_contains($e->getMessage(), 'with status \'FORGIVEN\' cannot be forgiven')) {
                throw $e;
            }
            $this->logger->warning(
                sprintf(
                    'ignoring the following error and letting it solve itself: %s',
                    $e->getMessage()
                )
            );
        }

        $this->chargeSynchronizer->byInvoice($paymentHandler, $invoiceId, false);

        return response()->json([
            'success' => true,
            'message_code' => 'FORGIVE_INVOICE_SUCCESS',
            'message' => 'Forgive Invoice Success',
        ]);
    }

    /**
     * Resend the receipt email for charge.
     *
     * @param $stripeChargeId
     *
     * @return false|JsonResponse|string
     */
    public function resendReceipt($stripeChargeId)
    {
        $isValidMongoid = $this->Utility->is_valid_mongoid($stripeChargeId);
        if (empty($isValidMongoid)) {
            return json_encode(
                ['success' => false, 'message' => __('Invalid Charge Id')],
                JSON_PARTIAL_OUTPUT_ON_ERROR
            );
        }

        $user = Auth::user();
        // to avoid issues with roaming users we use namespace instead of branch_id
        $namespace = $user->namespace() != '' ? $user->namespace() : '';

        if (!$user->isAdmin()) {
            throw new UnauthorizedException();
        }

        try {
            $chargesRepository = app()->make(ChargesRepository::class);
            $chargesRepository
                ->addCriteria(new Id($stripeChargeId))
                ->addCriteria(new MetadataNamespace($namespace));

            /** @var Charge $charge */
            $charge = $chargesRepository
                ->skipCallbacks()
                ->firstOrFail(function () use ($stripeChargeId) {
                    throw ChargeNotFoundException::withId($stripeChargeId);
                });

            $this->logger->info(sprintf("Charge receipt was requested for charge %s", $stripeChargeId));

            event()->emit(ChargeReceiptWasRequested::class, [$charge, true]);

            return response()->json([
                'success' => true,
                'message_code' => 'RECEIPT_SUCCESSFULLY_RESENT',
                'message' => 'Receipt resent successfully',
            ]);
        } catch (Exception $exception) {
            return response()->json([
                'success' => false,
                'message' => $exception->getMessage(),
            ]);
        }
    }

    /**
     * Download a user transaction receipt.
     *
     * @param $stripeChargeId
     *
     * @return void
     *
     * @throws InvalidTokenDataException
     * @throws UnsuccessfulOperation
     */
    public function downloadReceipt($stripeChargeId)
    {
        $this->logger->info('downloadReceipt : start', ['chargeId' => $stripeChargeId]);

        $user = Auth::user();
        // to avoid issues with roaming users we use namespace instead of branch_id
        $namespace = $user->namespace() != '' ? $user->namespace() : '';
        // staff should not have originBranchId so we use currentBranchId
        $branchId = $user->currentBranchId() != '' ? $user->currentBranchId() : '';

        if (!($user->isAdmin() || $user->isReceptionist())) {
            throw new UnauthorizedException();
        }

        try {
            $this->logger->info('downloadReceipt : fetching charge', ['chargeId' => $stripeChargeId]);

            /** @var ChargesRepository $chargesRepository */
            $chargesRepository = app()->make(ChargesRepository::class);
            /** @var Charge $charge */
            $charge = $chargesRepository
                ->addCriteria(new Id($stripeChargeId))
                // check charge belongs to users branch
                ->addCriteria(new MetadataNamespace($namespace))
                ->skipCallbacks()
                ->firstOrFail(function () use ($stripeChargeId) {
                    throw ChargeNotFoundException::withId($stripeChargeId);
                });

            if (!$charge->metadata()) {
                throw new Exception("This charge doesn't contain metadata");
            }

            $userId = $charge->userId();

            $this->logger->info('downloadReceipt : fetching user', ['userId' => $userId]);

            /** @var UsersRepository $usersRepository */
            $usersRepository = app()->make(UsersRepository::class);
            /** @var User $user */
            $user = $usersRepository
                ->addCriteria(new Id($userId))
                ->skipCallbacks()
                ->firstOrFail(function () use ($userId) {
                    throw UserNotFoundException::withId($userId);
                });

            $userBranchId = $charge->branchId() ?? $branchId;

            /** @var BranchesRepository $branchesRepository */
            $branchesRepository = app()->make(BranchesRepository::class);
            /** @var \Glofox\Domain\Branches\Models\Branch $branch */
            $branch = $branchesRepository
                ->addCriteria(new Id($userBranchId))
                ->skipCallbacks()
                ->firstOrFail(function () use ($userBranchId) {
                    throw BranchNotFoundException::withId($userBranchId);
                });

            $this->logger->info('downloadReceipt : fetching translator', ['branchId' => $branch->id()]);

            /** @var Translator $translator */
            $translator = app()->make(Translator::class);
            $translator->forBranch($branch);

            $chargesGroup = new ChargesGroup(
                $charge->invoiceId(),
                $charge->transactionGroupId(),
                collect([Charge::make($charge)])
            );

            if (!empty($charge->transactionGroupId())) {
                $chargesGroupFromRepo = $chargesRepository
                    ->addCriteria(new TransactionGroupId($charge->transactionGroupId()))
                    ->addCriteria(new InvoiceId($charge->invoiceId()))
                    ->find();

                $chargesGroup = new ChargesGroup(
                    $charge->invoiceId(),
                    $charge->transactionGroupId(),
                    collect($chargesGroupFromRepo)
                );
            }

            $this->logger->info('downloadReceipt : creating template');

            $templateClass = $this->resolveTemplateClass($branch);
            $template = app()->make($templateClass);

            $template->with('user', $user);
            $template->with('branch', $branch);
            $template->with('user', $user);
            $template->with('chargesGroup', $chargesGroup);

            /** @var ResolvePurchasedItemHandler $resolvePurchasedItemHandler */
            $resolvePurchasedItemHandler = app()->make(ResolvePurchasedItemHandler::class);
            $itemResolverCommand = new ResolvePurchasedItem($charge, $branch);
            $items = $resolvePurchasedItemHandler->handle($itemResolverCommand);
            $template->with('items', $items);

            $paymentMethodResolverCommand = app()->make(ResolvePurchasedPaymentMethodHandler::class);
            $paymentMethods = $paymentMethodResolverCommand->handle($chargesGroup, $branch);
            $template->with('paymentMethods', $paymentMethods);

            $this->downloadPdf($template->render(), $template->filename());

        } catch (Exception $exception) {
            $this->logger->error('downloadReceipt failed', ['exception' => $exception]);
            throw new UnsuccessfulOperation($exception->getMessage());
        }
    }

    private function downloadPdf(string $content, string $filename): void
    {
        $this->logger->info('downloadReceipt : starting download strategy');

        /** @var DomPdfStrategy $strategy */
        $strategy = app()->make(DomPdfStrategy::class);
        $exporter = new Exporter($strategy);
        $exporter->strategy()->download($content, $filename);
        $this->response->type('application/pdf');
    }

    /**
     * Returns the receipt template class to be used for the given branch
     */
    private function resolveTemplateClass(Branch $branch): string
    {
        if ($this->flagger->withFlag(Flag::POLISH_PDF_RECEIPT_RENDER())->hasByBranchId($branch->Id())) {
            return ReceiptTemplateAttachmentV3::class;
        }
        
        return ReceiptTemplateAttachmentV2::class;
    }

    public function sync(
        SyncRequest $request,
        ChargesRepository $chargesRepository
    ): JsonResponse {
        $allowedRoles = [UserType::ADMIN, UserType::SUPERADMIN];

        $user = Auth::user();
        // to avoid issues with roaming users we use namespace instead of branch_id
        $namespace = $user->namespace() != '' ? $user->namespace() : '';

        if (!in_array($user->type(), $allowedRoles)) {
            return response()->json([
                'success' => false,
                'message' => 'unauthorised access'
            ]);
        }

        logs()->push(sprintf('Charge [%s] will be synchronized with payments', $request->chargeId()));

        $namespace = new MetadataNamespace($namespace);
        try {
            $chargeId = new Id($request->chargeId());
        } catch (InvalidMongoIdException $e) {
            $chargeId = new TransactionProviderId($request->chargeId());
        }

        /** @var Charge $charge */
        $charge = $chargesRepository
            ->addCriteria($chargeId)
            ->addCriteria($namespace)
            ->firstOrFail();

        $paymentHandler = payments()->provider(
            $charge->branchId(),
            $charge->paymentMethod(false)
        );

        $this->chargeSynchronizer->byCharge($paymentHandler, $charge);

        return response()->json([
            'success' => true,
        ]);
    }

    /**
     * @return JsonResponse
     *
     * @throws UnsuccessfulOperation
     * @throws InvalidMongoIdException
     * @throws PaymentMethodNotFoundException
     * @throws PaymentProviderNotFoundException
     */
    private function processGatewayWebhookPayload(Collection $payload)
    {
        $accountId = (string)$payload->get('account_id');

        if ($payload->isEmpty()) {
            $exception = new UnsuccessfulOperation('empty payload');
            $exception->setCode(400);
            throw $exception;
        }

        /** @var PaymentMethodUsersRepository $paymentMethodUserRepository */
        $paymentMethodUserRepository = app()->make(PaymentMethodUsersRepository::class);

        /** @var PaymentMethodsRepository $paymentMethodsRepository */
        $paymentMethodsRepository = app()->make(PaymentMethodsRepository::class);

        /** @var \Glofox\Domain\PaymentMethods\Models\PaymentMethod $paymentMethod */
        $paymentMethod = $paymentMethodsRepository->addCriteria(new ProviderAccountId($accountId))
            ->addCriteria(new Glofox\Repositories\Search\Expressions\Shared\Active(true))
            ->first();

        // if the payment method can not be found using the `account_id` directly, we will try to find it
        // assuming it's a customer account id
        if (empty($paymentMethod)) {
            /** @var Glofox\Domain\PaymentMethodUsers\Models\PaymentMethodUser $userPaymentMethod */
            $userPaymentMethod = $paymentMethodUserRepository
                ->addCriteria(new CustomerProviderId($accountId))
                ->firstOrFail(function () use ($accountId) {
                    throw new Exception(sprintf('No customer provider id was found for account id: %s', $accountId));
                });

            $paymentMethod = $paymentMethodsRepository
                ->addCriteria(new Id($userPaymentMethod->paymentMethodId()))
                ->firstOrFail(function () {
                    throw new GlofoxException('webhook account owner could not be found', 500 /* trigger SNS retry */);
                });
        }

        return response()->json(
            $this->webhookEventHandler($paymentMethod, $payload),
            $this->response->statusCode()
        );
    }

    /**
     * @param \Glofox\Domain\PaymentMethods\Models\PaymentMethod $paymentMethod
     * @param Collection $eventPayload
     * @return array
     * @throws InvalidMongoIdException
     * @throws PaymentMethodNotFoundException
     * @throws PaymentProviderNotFoundException
     * @throws UnsupportedPaymentMethodTypeException
     */
    private function webhookEventHandler(
        Glofox\Domain\PaymentMethods\Models\PaymentMethod $paymentMethod,
        Collection $eventPayload
    ): array {
        /** @var LoggerInterface $logger */
        $logger = app()->make(LoggerInterface::class);

        /** @var WebhookLockerInterface $locker */
        $locker = app()->make(WebhookLockerInterface::class);

        // Dummy user that will related to all the transactions that are performed by webhooks
        $webhookBotUser = User::make([
            'branch_id' => $paymentMethod->branchId(),
            'first_name' => 'Glofox',
            'last_name' => 'Bot',
            'type' => UserType::ADMIN,
        ]);

        // Log in globally as the bot user
        Auth::loginAs($webhookBotUser);

        // get the payment handler based on the payment method
        $paymentHandler = Auth::payments($paymentMethod->typeId());
        $webhooksHandler = $paymentHandler->webHooks();

        $eventId = (string)$eventPayload->get('id');
        $webhookEvent = new WebhookEvent($eventId);

        try {
            $locker->lock($webhookEvent);

            $webhooksHandler->setEventPayload($eventPayload);
            $webhooksHandler->processCurrentEvent();

            $status = $paymentHandler->webHooks()->getResponseStatus();
            http_response_code($status);
            $this->response->statusCode($status);

            $response = [
                'success' => true,
                'message' => 'Event processed successfully',
                'status' => 200,
                'logs' => logs()->toArray(),
            ];
        } catch (Exception $e) {
            $logger->error($e->getMessage());

            if (
                $e instanceof WebhookEventAlreadyReceivedException ||
                $e instanceof NoClassHandlerForEventTypeException ||
                $e instanceof CreditPackAlreadyExistsException
            ) {
                $status = $e->getCode();
            } else {
                // trigger SNS retry for unsuccessful status code
                $status = $e->getCode() >= 200 && $e->getCode() <= 299 ? $e->getCode() : 500;
            }

            $response = [
                'message' => $e->getMessage(),
                'success' => false,
                'status' => $status,
                'logs' => logs()->toArray(),
            ];

            http_response_code($status);
            $this->response->statusCode($status);
        } finally {
            $locker->unlock($webhookEvent);
        }

        $logger->info(sprintf('Webhook Event [%s]', $eventPayload->get('id')), $response);

        return $response;
    }

    /**
     * Creates / returns a price breakdown for a given chargeId
     *
     * @param $stripeChargeId
     *
     * @return JsonResponse
     *
     * @throws InvalidTokenDataException
     * @throws UnsuccessfulOperation
     */
    public function createPriceBreakdown($stripeChargeId)
    {
        $user = Auth::user();
        $namespace = $user->namespace();

        if (!($user->isAdmin() || $user->isSuperAdmin() || $user->isReceptionist())) {
            throw new InvalidTokenDataException('Unauthorized Action.');
        }

        try {
            /** @var ChargesRepository $chargesRepository */
            $chargesRepository = app()->make(ChargesRepository::class);
            /** @var Charge $charge */
            $charge = $chargesRepository
                ->addCriteria(new Id($stripeChargeId))
                ->addCriteria(new MetadataNamespace($namespace))
                ->skipCallbacks()
                ->firstOrFail(function () use ($stripeChargeId) {
                    throw ChargeNotFoundException::withId($stripeChargeId);
                });

            if (!$charge->metadata()) {
                throw new Exception("This change doesn't contain metadata");
            }

            $branchId = $charge->metadata()['branch_id'];

            /** @var BranchesRepository $branchesRepository */
            $branchesRepository = app()->make(BranchesRepository::class);
            /** @var \Glofox\Domain\Branches\Models\Branch $branch */
            $branch = $branchesRepository
                ->addCriteria(new Id($branchId))
                ->skipCallbacks()
                ->firstOrFail(function () use ($branchId) {
                    throw BranchNotFoundException::withId($branchId);
                });

            /** @var PriceBreakdownsRepository $priceBreakdownsRepository */
            $priceBreakdownsRepository = app()->make(PriceBreakdownsRepository::class);
            $existingPriceBreakdown = $priceBreakdownsRepository->addCriteria(new ChargeId($stripeChargeId))->first();
            if ($existingPriceBreakdown != null) {
                $this->logger->info(sprintf('Price breakdown already exists for charge %s', $charge->id()));
                return response()->json([
                    'success' => true,
                    'message_code' => 'PRICE_BREAKDOWN_EXISTS',
                    'message' => 'Price breakdown already exits',
                    'result' => json_encode($existingPriceBreakdown, JSON_PARTIAL_OUTPUT_ON_ERROR),
                ]);
            }

            /** @var InvoiceServiceInterface $invoiceService */
            $invoiceService = app()->make(InvoiceServiceInterface::class);

            $result = $invoiceService->getTransactionById($branch->id(), $charge->invoiceId());
            $transaction = $result->getData($assoc = true);

            if (!$transaction || !isset($transaction['taxes'])) {
                $error = 'Cannot create price breakdown since there is not a tax calculation for this charge';
                $this->logger->info($error, $charge->toArray());

                throw new Exception($error);
            }

            $breakdown = PriceCalculatorHttpService::convertPriceBreakdownResponse($transaction);

            $priceBreakdown = PriceBreakdown::make([
                'branch_id' => $charge->metadata()['branch_id'],
                'charge_id' => $charge->id(),
                'invoice_id' => $charge->invoiceId(),
                'taxes' => $breakdown['taxes'],
                'discounts' => $breakdown['discounts'],
            ]);

            $priceBreakdownsRepository->saveOrFail($priceBreakdown);
            $newPriceBreakdown = $priceBreakdownsRepository->addCriteria(new ChargeId($stripeChargeId))->first();

            $this->logger->info(sprintf('Successfully created price breakdown for charge %s', $charge->id()));

            return response()->json([
                'success' => true,
                'message_code' => 'PRICE_BREAKDOWN_CREATED',
                'message' => 'Price breakdown created successfully',
                'result' => json_encode($newPriceBreakdown, JSON_PARTIAL_OUTPUT_ON_ERROR),
            ]);
        } catch (Exception $exception) {
            throw new UnsuccessfulOperation($exception->getMessage());
        }
    }
}
