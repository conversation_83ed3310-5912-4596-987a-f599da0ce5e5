<?php

namespace Glofox\Traversable;

use Glofox\Traversable\Exceptions\StrictTypeViolationException;
use Illuminate\Support\Collection;

abstract class StrictTypeCollection extends Collection
{
    public function __construct($items = [])
    {
        foreach ($items as $item) {
            $this->test($item);
        }

        parent::__construct($items);
    }

    /**
     * Test the item against the allowed type
     *
     * @param $item
     *
     * @return mixed
     * @throws StrictTypeViolationException
     */
    protected function test($item)
    {
        $expected = $this->allowedType();

        if (!is_array($expected)) {
            $expected = [ $expected ];
        }

        foreach ($expected as $className) {
            if (is_a($item, $className)) {
                return true;
            }
        }

        throw StrictTypeViolationException::withExpectedAndActual(\implode(' or ', $expected), get_class($item));
    }

    public function push($value)
    {
        $this->test($value);
        return parent::push($value);
    }

    public function put($key, $value)
    {
        $this->test($value);
        return parent::put($key, $value);
    }

    abstract protected function allowedType();
}
