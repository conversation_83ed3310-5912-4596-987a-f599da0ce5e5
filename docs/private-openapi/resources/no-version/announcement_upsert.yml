post:
  summary: ''
  operationId: announcements-upsert
  tags:
    - Announcements
  description: Create new announcement and update an exisiting announcement.
  requestBody:
    content:
      application/json:
        schema:
          allOf:
            - $ref: '../../schemas/announcement.yml'
            - type: object
              properties:
                is_scheduled:
                  type: boolean
                start_date:
                  type: string
                end_date:
                  type: string
  responses:
    '200':
      description: OK
      content:
        application/json:
          schema:
            oneOf:
              - type: object
                properties:
                  success:
                    type: boolean
                    default: true
                  Announcement:
                    $ref: '../../schemas/announcement.yml'
              - $ref: '../../schemas/error.yml'
          examples:
            Success response:
              value:
                success: true
                Announcement:
                  _id: string
                  branch_id: string
                  namespace: string
                  active: true
                  title: string
                  content: string
                  type: IMAGE
                  author:
                    user_id: string
                    first_name: string
                    last_name: string
                  image_url: string
                  video_url: string
                  scheduled:
                    is_scheduled: true
                    start_date: 0
                    end_date: 0
                  tags:
                    - News
                  restrictions:
                    memberships:
                      - null
                  reasons:
                    - string
                  created: 0
                  modified: 0
                  published: 0
            Malicious text was detected:
              value:
                success: false
                message: We needed to edit your text as it contained forbidden symbols. Please review the content of the post before saving.
                message_code: CONTENT_HTML_SANITISER_FAILED
                message_data:
                  sanitized_content: <p>Some sanitized content</p>
                errors:
                  - CONTENT_HTML_SANITISER_FAILED
            Video URLs must be from Youtube:
              value:
                success: false
                message: Video URLs must be from Youtube
                message_code: Video URLs must be from Youtube
                message_data:
                  - string
                errors:
                  - Video URLs must be from Youtube
            Wrong Youtube format:
              value:
                success: false
                message: 'YouTube URLs are required to be in the "youtube.com/watch?v={videoId}" or "youtu.be/{videoId}" formats'
                message_code: 'YouTube URLs are required to be in the "youtube.com/watch?v={videoId}" or "youtu.be/{videoId}" formats'
                message_data:
                  - string
                errors:
                  - 'YouTube URLs are required to be in the "youtube.com/watch?v={videoId}" or "youtu.be/{videoId}" formats'