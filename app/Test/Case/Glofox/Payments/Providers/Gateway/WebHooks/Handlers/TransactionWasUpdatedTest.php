<?php
declare(strict_types=1);

namespace CakeTestCases\Glofox\Payments\Providers\Gateway\WebHooks\Handlers;

use Glofox\Payments\Contracts\PaymentProviderContract;
use Glofox\Payments\Providers\Gateway\WebHooks\Handlers\TransactionWasUpdated;
use Glofox\Payments\Entities\Transaction\Exceptions\ChargeRetrievalException;
use Glofox\Domain\Charges\Exceptions\ChargeNotFoundException;
use Glofox\Domain\Charges\Models\Charge;
use Glofox\Domain\Charges\Repositories\ChargesRepository;
use Glofox\Domain\Charges\Services\ChargeSynchronizer;
use Glofox\Domain\PaymentProviders\Models\PaymentProvider;
use Glofox\Payments\Entities\Transaction\Contracts\ChargeHandlerContract;
use Glofox\Payments\Entities\Transaction\Models\Transaction;

\App::import('Test/Case', 'GlofoxTestCase');

class TransactionWasUpdatedTest extends \GlofoxTestCase {
    
    public function tearDown()
    {
        app()->forgetInstance(ChargesRepository::class);
        app()->forgetInstance(ChargeSynchronizer::class);
        \Mockery::close();
        parent::tearDown();
    }

    public function testHandleTransactionNotFound(): void 
    {
        $event = collect([
            'id' => 'evt_123',
            'psp_response' => json_encode([
                'payload' => [
                    "id" => "invalid_id"
                ],
            ]),
        ]);

        $paymentHandler = \Mockery::mock(PaymentProviderContract::class);

        $handler = new TransactionWasUpdated(
            $event,
            $paymentHandler
        );

        $this->setExpectedException(ChargeRetrievalException::class);
        $handler->handle();
    }

    public function testHandleChargeNotFound() {
        $event = collect([
            'id' => 'evt_123',
            'psp_response' => json_encode([
                'payload' => [
                    "id" => "pspuuid"
                ],
            ]),
        ]);

        $transaction = (new Transaction())->setId("transaction_id");
        
        $paymentHandler = \Mockery::mock(PaymentProviderContract::class)
            ->shouldReceive('charges')
            ->andReturn(
                \Mockery::mock(ChargeHandlerContract::class)
                    ->shouldReceive('getById')
                    ->andReturns($transaction)
                    ->getMock()
            )
            ->getMock();

        $handler = new TransactionWasUpdated(
            $event,
            $paymentHandler
        );

        $this->setExpectedException(ChargeNotFoundException::class,"Charge not found - transaction provider id: transaction_id");
        $handler->handle();
    }

    public function testHandleUpdatedChargeIsNull() {
        $event = collect([
            'id' => 'evt_123',
            'psp_response' => json_encode([
                'payload' => [
                    "id" => "pspuuid"
                ],
            ]),
        ]);

        $transaction = (new Transaction())->setId("transaction_id");
        $paymentHandler = \Mockery::mock(PaymentProviderContract::class)
            ->shouldReceive('charges')
            ->andReturn(
                \Mockery::mock(ChargeHandlerContract::class)
                    ->shouldReceive('getById')
                    ->andReturns($transaction)
                    ->getMock()
            )
            ->getMock();
        
        $chargeRepository = \Mockery::mock(ChargesRepository::class);
        $chargeRepository->shouldReceive('findByTransactionProviderId')
            ->andReturn(new Charge())
            ->getMock();

        app()->instance(ChargesRepository::class, $chargeRepository);

        $chargeSynchronizer = \Mockery::mock(ChargeSynchronizer::class);
        $chargeSynchronizer->shouldReceive('byCharge')
            ->andReturn(null)
            ->getMock();
        
        app()->instance(ChargeSynchronizer::class, $chargeSynchronizer);
        
        $handler = new TransactionWasUpdated(
            $event,
            $paymentHandler
        );

        $status = $handler->handle();
        $this->assertEquals(404,$status);
    }

    public function testHandleSuccessful() {
        $event = collect([
            'id' => 'evt_123',
            'psp_response' => json_encode([
                'payload' => [
                    "id" => "pspuuid"
                ],
            ]),
        ]);

        $provider = new PaymentProvider([
            'gateway_id' => '1',
            'name' => 'PROVIDER',
        ]);

        $transaction = (new Transaction())->setId("transaction_id");
        $paymentHandler = \Mockery::mock(PaymentProviderContract::class)
            ->shouldReceive('charges')
            ->andReturn(
                \Mockery::mock(ChargeHandlerContract::class)
                    ->shouldReceive('getById')
                    ->andReturns($transaction)
                    ->getMock()
            )
            ->getMock()
            ->shouldReceive('paymentProvider')
            ->andReturn($provider)
            ->getMock();
        
        $chargeRepository = \Mockery::mock(ChargesRepository::class);
        $chargeRepository->shouldReceive('findByTransactionProviderId')
            ->andReturn(new Charge(['transaction_status'=>'PENDING']))
            ->getMock();

        app()->instance(ChargesRepository::class, $chargeRepository);

        $chargeSynchronizer = \Mockery::mock(ChargeSynchronizer::class);
        $chargeSynchronizer->shouldReceive('byCharge')
            ->andReturn(new Charge(['transaction_status'=>'SUCCESS']))
            ->getMock();
        
        app()->instance(ChargeSynchronizer::class, $chargeSynchronizer);
        
        $handler = new TransactionWasUpdated(
            $event,
            $paymentHandler
        );

        $status = $handler->handle();
        $this->assertEquals(200,$status);
    }

    public function testHandleSuccessfulEzypay() {
        $event = collect([
            'id' => 'evt_123',
            'psp_response' => json_encode([
                'payload' => [
                    "id" => "pspuuid"
                ],
            ]),
        ]);

        $provider = new PaymentProvider([
            'gateway_id' => '40',
            'name' => 'EZYPAY',
        ]);

        $transaction = (new Transaction())->setId("transaction_id");
        $paymentHandler = \Mockery::mock(PaymentProviderContract::class)
            ->shouldReceive('charges')
            ->andReturn(
                \Mockery::mock(ChargeHandlerContract::class)
                    ->shouldReceive('getById')
                    ->andReturns($transaction)
                    ->getMock()
            )
            ->getMock();

        $paymentHandler->shouldReceive('paymentProvider')
            ->andReturn($provider);
        
        $chargeRepository = \Mockery::mock(ChargesRepository::class);
        $chargeRepository->shouldReceive('findByTransactionProviderId')
            ->andReturn(new Charge(['transaction_status'=>'PENDING']))
            ->getMock();

        app()->instance(ChargesRepository::class, $chargeRepository);

        $chargeSynchronizer = \Mockery::mock(ChargeSynchronizer::class);
        $chargeSynchronizer->shouldReceive('byCharge')
            ->andReturn(new Charge(['transaction_status'=>'SUCCESS']))
            ->getMock();
        
        app()->instance(ChargeSynchronizer::class, $chargeSynchronizer);
        
        $handler = new TransactionWasUpdated(
            $event,
            $paymentHandler
        );

        $status = $handler->handle();
        $this->assertEquals(200,$status);
    }
}