<?php

use Glofox\Domain\Authorization\Abilities;

class RoleFixture extends CakeTestFixture
{
    public $import = ['model' => 'Role'];

    public function init()
    {
        $this->records = [
            [
                '_id' => new MongoId('5c79e36f7fb4afd6e2c806ea'),
                'role' => 'MEMBER',
                'abilities' => [
                    Abilities\BranchAbility::LIST,
                    Abilities\BranchAbility::UPDATE,
                    Abilities\UserAbility::LIST_LINKED_ACCOUNTS,
                ],
            ],
            [
                '_id' => new MongoId('5c79e3c27fb4afd6e2c806f5'),
                'role' => 'ADMIN',
                'abilities' => [
                    Abilities\BranchAbility::LIST_BY_NAMESPACE,
                    Abilities\BranchAbility::LIST,
                    Abilities\BranchAbility::UPDATE,
                    Abilities\BranchAbility::CREATE,
                    Abilities\BranchAbility::DELETE,
                ],
            ],
            [
                '_id' => new MongoId('5c79e3c27fb4afd6e2c806f6'),
                'role' => 'CHILD',
                'abilities' => [],
            ],
        ];
        parent::init();
    }
}
