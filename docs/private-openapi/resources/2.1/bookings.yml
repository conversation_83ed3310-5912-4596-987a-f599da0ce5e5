post:
  tags:
    - Bookings
  summary: Create a booking
  operationId: create-booking
  description: |
    Use this call to book a user into a class. If a class is full the user will be added to a waitlist if there are spaces remaining on the waitlist.
    
    If the booking fails it returns and an error code and reason for failure.
  security:
    - coreapi_auth: [ ]
  requestBody:
    required: true
    content:
      application/json:
        schema:
          $ref: '../../requests/2.1/create_booking.yml'
  responses:
    '200':
      description: OK
      content:
        application/json:
          schema:
            $ref: '../../schemas/booking.yml'
