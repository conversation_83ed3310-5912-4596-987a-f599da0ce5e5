<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\Memberships\Models;

use App;
use Glofox\Domain\Memberships\Models\MinimumPrice;
use Glofox\Exception;
use GlofoxTestCase;

App::import('Test/Case', 'GlofoxTestCase');

class MinimumPriceTest extends GlofoxTestCase
{
    public $fixtures = [];
    
    /**
     * @dataProvider constructorDataProvider
     * @throws Exception
     */
    public function testConstructor(string $expectedPlanCode, float $expectedPrice, string $expectedException): void
    {
        if ($expectedException !== '') {
            $this->expectExceptionMessage($expectedException);
        }

        $minimumPrice = new MinimumPrice('test-membership-id', $expectedPlanCode, $expectedPrice);

        if ($expectedException === '') {
            self::assertEquals($expectedPlanCode, $minimumPrice->planCode());
            self::assertEquals($expectedPrice, $minimumPrice->price());
        }
    }

    public function constructorDataProvider(): array
    {
        return [
            'when empty plan code and negative price are provided, ' .
            'then it throws a plan code cannot be empty exception' => [
                'planCode' => '',
                'price' => -1,
                'exception' => 'Plan code cannot be empty',
            ],
            'when empty plan code is provided, then it throws a plan code cannot be empty exception' => [
                'planCode' => '',
                'price' => 2,
                'exception' => 'Plan code cannot be empty',
            ],
            'when negative price is provided, then it throws a minimum price cannot be negative exception' => [
                'planCode' => '123456',
                'price' => -3,
                'exception' => 'Minimum price cannot be negative',
            ],
            'when valid parameters are provided, then it creates the new instance correctly' => [
                'planCode' => '987654',
                'price' => 4,
                'exception' => '',
            ],
        ];
    }
}
