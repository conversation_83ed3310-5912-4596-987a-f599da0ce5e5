<?php

declare(strict_types=1);

namespace V22;

use Glofox\Datasource\Exceptions\InvalidMongoIdException;
use Glofox\Domain\Branches\Http\Actions\ListAllBookingsAction;
use Glofox\Domain\Branches\Requests\ListAllBookingsRequest;
use Illuminate\Http\JsonResponse;

\App::uses('AppController', 'Controller');

class BranchesController extends \AppController
{
    /**
     * @throws InvalidMongoIdException
     */
    public function listAllBookings(ListAllBookingsRequest $request, ListAllBookingsAction $action): JsonResponse
    {
        return $action($request);
    }
}
