<?php

declare(strict_types=1);

namespace Glofox\Domain\Users\Transformers;

use Glofox\Domain\Users\Models\User;
use League\Fractal\TransformerAbstract;

class LinkedAccountTransformer extends TransformerAbstract
{
    public function transform(User $child): array
    {
        return [
            '_id' => $child->id(),
            'name' => $child->fullName(),
            'first_name' => $child->firstName(),
            'last_name' => $child->lastName(),
            'branch_id' => $child->currentBranchId(),
            'namespace' => $child->namespace(),
            'email' => $child->email(),
            'use_parent_email' => $child->childIsUsingParentEmail(),
            'phone' => $child->phone(),
            'use_parent_phone' => $child->childIsUsingParentPhone(),
            'type' => $child->type(),
            'role' => $child->role(),
            'gender' => $this->getGender($child),
            'parent_id' => $child->parentId(),
            'avatar' => (string) $child->avatar(),
            'created' => $child->created()->toDateTimeString(),
            'modified' => $child->modified()->toDateTimeString(),
        ];
    }

    private function getGender(User $child): array
    {
        return [
            'name' => $child->gender()->name(),
            'label' => $child->gender()->label(),
        ];
    }
}
