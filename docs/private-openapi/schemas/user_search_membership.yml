type: object
properties:
  _id:
    type: string
  branch_id:
    type: string
  branch_name:
    type: string
  type:
    $ref: './membership_type.yml'
  booked_events:
    type: integer
  membership_group_id:
    type: string
  plan_code:
    type: integer
  plan_price:
    type: integer
  starts_on:
    type: string
    enum:
      - PURCHASE_DATE
      - FIRST_BOOKING_DATE
  roaming_enabled:
    type: boolean
  user_membership_id:
    type: string
  trial:
    type: boolean
  plan_upfront_fee:
    type: boolean
  status:
    $ref: './membership_status.yml'
  branches:
    type: array
    items:
      type: string
  start_date:
    type: number
    description: The date the current membership cycle starts. This is returned in UTC epoch format.
  expiry_date:
    type: number
    description: The date the current membership cycle ends. This is returned in UTC epoch format.
  duration_time_unit:
    type: string
    enum:
      - month
      - week
      - day
  duration_time_unit_count:
    type: integer
  name:
    type: string
