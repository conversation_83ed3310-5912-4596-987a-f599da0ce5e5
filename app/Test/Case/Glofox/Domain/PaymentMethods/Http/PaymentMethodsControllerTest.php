<?php

namespace CakeTestCases\Glofox\Domain\PaymentMethods\Http;

\App::import('Test/Case', 'GlofoxControllerTestCase');

use Glofox\Domain\Authentication\Token\TokenGeneratorDto;
use Glofox\Domain\Authentication\TokenGenerator;
use Glofox\Domain\Branches\Search\Expressions\BranchId;
use Glofox\Domain\PaymentMethods\Models\PaymentMethod;
use Glofox\Domain\PaymentMethods\Repositories\PaymentMethodsRepository;
use  Glofox\Domain\PaymentMethods\Requests\Filters\Type as FilterType;
use Glofox\Domain\PaymentMethods\Search\Expressions\TypeId;
use Glofox\Domain\PaymentMethods\Type;
use Glofox\Domain\PaymentMethods\Type as PaymentMethodType;
use Glofox\Domain\TerminalReaders\Repositories\TerminalReadersRepository;
use Glofox\Domain\Users\Repositories\UsersRepository;
use Glofox\Infrastructure\Flags\Flag;
use Glofox\Infrastructure\Flags\Flagger;
use Mockery;

/**
 * Class PaymentMethodsControllerTest.
 */
class PaymentMethodsControllerTest extends \GlofoxControllerTestCase
{
    private static $token;
    private static $tokenStripeDD;
    private static $tokenTerminalPOS;
    private static $tokenNicepay;
    private const NICEPAY_BRANCH = '682b43bcfc88abbdcb0e867d';

    private PaymentMethodsRepository $paymentMethodsRepository;
    private UsersRepository $usersRepository;
    private TerminalReadersRepository $terminalReadersRepository;

    public function setUp()
    {
        parent::setUp();

        $this->paymentMethodsRepository = app()->make(PaymentMethodsRepository::class);
        $this->usersRepository = app()->make(UsersRepository::class);
        $this->terminalReadersRepository = app()->make(TerminalReadersRepository::class);

        if (self::$token && self::$tokenStripeDD) {
            return;
        }

        self::$token = app()->make(TokenGenerator::class)
            ->generate(new TokenGeneratorDto([
                '_id' => '59a7011a05c677bda916612a',
                'namespace' => 'glofox',
                'branch_id' => '49a7011a05c677b9a916612a',
                'first_name' => 'admin',
                'last_name' => 'admin',
                'type' => \UserType::ADMIN,
            ]));

        self::$tokenStripeDD = app()->make(TokenGenerator::class)
            ->generate(new TokenGeneratorDto([
                '_id' => '59a7011a02c677bda916612b',
                'namespace' => 'stripedd',
                'branch_id' => '49a7011a08c677b9a916612a',
                'first_name' => 'admin',
                'last_name' => 'admin',
                'type' => \UserType::ADMIN,
            ]));

        self::$tokenTerminalPOS = app()->make(TokenGenerator::class)
            ->generate(new TokenGeneratorDto([
                '_id' => '59a7011a02c677bda916612c',
                'namespace' => 'terminalpos',
                'branch_id' => '59a7011a02c677bda916612c',
                'first_name' => 'admin',
                'last_name' => 'admin',
                'type' => \UserType::ADMIN,
            ]));

        self::$tokenNicepay = app()->make(TokenGenerator::class)
            ->generate(new TokenGeneratorDto([
                '_id' => self::NICEPAY_BRANCH,
                'namespace' => 'nicepaytestbranch',
                'branch_id' => self::NICEPAY_BRANCH,
                'first_name' => 'admin',
                'last_name' => 'admin',
                'type' => \UserType::ADMIN,
            ]));
    }

    private function mockFlagger(): void
    {
        $flaggers = Mockery::mock(Flagger::class);
        $flaggers
            ->shouldReceive('withFlag')
            ->with(Flag::SHOW_LINK_ON_PAYMENT_COLLECTOR())
            ->andReturnSelf()
            ->shouldReceive('withFlag')
            ->with(Flag::IS_PAYMENT_COLLECTOR_V2_ENABLED())
            ->andReturnSelf()
            ->shouldReceive('withFlag')
            ->with(Flag::USE_PAYMENT_COLLECTOR_V2_SETUP_INTENT())
            ->andReturnSelf()
            ->shouldReceive('hasByBranchId')
            ->andReturnTrue()
            ->getMock();
        app()->instance(
            Flagger::class,
            $flaggers
        );
    }

    public function test_serve_all_total_payment_methods_without_any_restriction()
    {
        $_SERVER['HTTP_AUTHORIZATION'] = self::$token;

        $this->mockFlagger();

        $result = $this->testAction('/2.1/branches/49a7011a05c677b9a916612a/payment-methods', ['method' => 'get']);
        $result = json_decode($result, null, 512, JSON_THROW_ON_ERROR);

        $this->assertTrue($result->success);
        $this->assertCount(7, $result->data);
    }

    public function test_it_should_apply_filter_for_selected_payment_method_on_demand()
    {
        $_SERVER['HTTP_AUTHORIZATION'] = self::$token;
        $this->mockFlagger();

        $data = [
            'filters' => [
                FilterType::TYPE_ID_FILTER => [
                    PaymentMethodType::CASH,
                    PaymentMethodType::CARD,
                ],
            ],
        ];

        $qs = http_build_query($data);
        $url = sprintf('/2.1/branches/49a7011a05c677b9a916612a/payment-methods?%s', $qs);

        $result = $this->testAction($url, ['method' => 'get']);
        $result = json_decode($result, null, 512, JSON_THROW_ON_ERROR);

        $this->assertTrue($result->success);
        $this->assertCount(2, $result->data);

        foreach ($result->data as $paymentMethod) {
            $this->assertTrue(\in_array($paymentMethod->type_id, $data['filters'][FilterType::TYPE_ID_FILTER]));
        }
    }

    public function test_it_should_add_jwt_token_to_iframe_url()
    {
        $_SERVER['HTTP_AUTHORIZATION'] = self::$token;

        $this->mockFlagger();

        $data = [
            'filters' => [
                FilterType::TYPE_ID_FILTER => [
                    PaymentMethodType::CASH,
                    PaymentMethodType::CARD,
                ],
            ],
            'headers' => [
                'X-Glofox-Source' => "member_app"
                ]
        ];

        $qs = http_build_query($data);
        $url = sprintf('/2.1/branches/49a7011a05c677b9a916612a/payment-methods?%s', $qs);
        $this->headers['X-Glofox-Source'] = "member_app";
        $_SERVER['X-Glofox-Source'] = 'MEMBER_APP';

        $result = $this->testAction($url, ['method' => 'get']);
        $result = json_decode($result, null, 512, JSON_THROW_ON_ERROR);

        $this->assertTrue($result->success);
        $this->assertCount(2, $result->data);

        foreach ($result->data as $paymentMethod) {
            $this->assertTrue(\in_array($paymentMethod->type_id, $data['filters'][FilterType::TYPE_ID_FILTER]));
        }
    }

    public function test_it_should_filter_the_available_payment_method_given_an_app_version_header()
    {
        $_SERVER['HTTP_AUTHORIZATION'] = self::$token;
        $this->mockFlagger();

        $_SERVER['HTTP_' . strtoupper(GLOFOX_APP_VERSION_HEADER)] = '8.1.4';

        $result = $this->testAction('/2.1/branches/49a7011a05c677b9a916612a/payment-methods', [
            'method' => 'get',
        ]);

        $result = json_decode($result, null, 512, JSON_THROW_ON_ERROR);

        $this->assertTrue($result->success);
        $this->assertCount(5, $result->data);

        foreach ($result->data as $paymentMethod) {
            $this->assertTrue(\in_array($paymentMethod->type_id, [
                PaymentMethodType::CASH,
                PaymentMethodType::CARD,
                PaymentMethodType::COMPLIMENTARY,
                PaymentMethodType::BANK_TRANSFER,
                PaymentMethodType::PAY_LATER,
            ]));
        }

        unset($_SERVER['HTTP_' . strtoupper(GLOFOX_APP_VERSION_HEADER)]);
    }

    public function test_it_should_not_alter_the_available_payment_method_given_a_member_app_version_header()
    {
        $_SERVER['HTTP_AUTHORIZATION'] = self::$token;
        $_SERVER['HTTP_X_GLOFOX_SOURCE'] = 'MEMBER_APP';
        $_SERVER['HTTP_' . strtoupper(GLOFOX_APP_VERSION_HEADER)] = '8.1.6';

        $this->mockFlagger();
        $result = $this->testAction('/2.1/branches/49a7011a05c677b9a916612a/payment-methods', [
            'method' => 'get',
        ]);

        $result = json_decode($result, null, 512, JSON_THROW_ON_ERROR);

        $this->assertTrue($result->success);
        $this->assertCount(7, $result->data);

        unset($_SERVER['HTTP_X_GLOFOX_SOURCE']);
        unset($_SERVER['HTTP_' . strtoupper(GLOFOX_APP_VERSION_HEADER)]);
    }

    /**
     * We are certain about the member app being the request source, but are not able to identify the app version
     * We must assume that the app is not direct debit ready and only 5 payment methods should be available.
     */
    public function test_it_should_filter_the_available_payment_method_given_a_member_app_source_header()
    {
        $_SERVER['HTTP_AUTHORIZATION'] = self::$token;
        $_SERVER['HTTP_X_GLOFOX_SOURCE'] = 'MEMBER_APP';

        $this->mockFlagger();
        $result = $this->testAction('/2.1/branches/49a7011a05c677b9a916612a/payment-methods', [
            'method' => 'get',
        ]);

        $result = json_decode($result, null, 512, JSON_THROW_ON_ERROR);

        $this->assertTrue($result->success);
        $this->assertCount(5, $result->data);

        foreach ($result->data as $paymentMethod) {
            $this->assertTrue(\in_array($paymentMethod->type_id, [
                PaymentMethodType::CASH,
                PaymentMethodType::CARD,
                PaymentMethodType::COMPLIMENTARY,
                PaymentMethodType::BANK_TRANSFER,
                PaymentMethodType::PAY_LATER,
            ]));
        }

        unset($_SERVER['HTTP_X_GLOFOX_SOURCE']);
    }

    /**
     * We are certain about the member app being the request source, but are not able to identify the app version
     * We must assume that the app is not direct debit ready and only 5 payment methods should be available.
     */
    public function test_it_should_return_payment_collector_v2_in_iframe_url_given_a_member_app_source_header()
    {
        $_SERVER['HTTP_AUTHORIZATION'] = self::$token;
        $_SERVER['HTTP_X_GLOFOX_SOURCE'] = 'MEMBER_APP';

        $this->mockFlagger();
        $result = $this->testAction('/2.1/branches/49a7011a05c677b9a916612a/payment-methods?includes=provider,iframe', [
            'method' => 'get',
        ]);

        $result = json_decode($result, null, 512, JSON_THROW_ON_ERROR);

        $this->assertTrue($result->success);
        $this->assertCount(5, $result->data);
        $this->assertContains("/payment-collector/v2/#/stripe/payment-element?", $result->data[4]->iframe->full_path_v2);
        $this->assertContains("&jwt-token=", $result->data[4]->iframe->full_path_v2);
        $this->assertContains("&publishable-key=", $result->data[4]->iframe->full_path_v2);
        $this->assertContains("merchant[id]=1111", $result->data[4]->iframe->full_path_v2);

        foreach ($result->data as $paymentMethod) {
            $this->assertTrue(\in_array($paymentMethod->type_id, [
                PaymentMethodType::CASH,
                PaymentMethodType::CARD,
                PaymentMethodType::COMPLIMENTARY,
                PaymentMethodType::BANK_TRANSFER,
                PaymentMethodType::PAY_LATER,
            ]));
        }

        unset($_SERVER['HTTP_X_GLOFOX_SOURCE']);
    }

    public function test_it_should_return_payment_collector_setup_intent_url_in_iframe_url_if_ff_is_on_and_dd()
    {
        $_SERVER['HTTP_AUTHORIZATION'] = self::$token;
        $_SERVER['HTTP_X_GLOFOX_SOURCE'] = 'DASHBOARD';

        $this->mockFlagger();
        $result = $this->testAction('/2.1/branches/49a7011a05c677b9a916612a/payment-methods?includes=provider,iframe', [
            'method' => 'get',
        ]);

        $result = json_decode($result, null, 512, JSON_THROW_ON_ERROR);

        $this->assertTrue($result->success);
        $this->assertCount(7, $result->data);
        $this->assertContains("/payment-collector/v2/#/stripe/payment-element/setup-intent?", $result->data[5]->iframe->full_path);
        $this->assertContains("&jwt-token=", $result->data[5]->iframe->full_path);
        $this->assertContains("&publishable-key=", $result->data[5]->iframe->full_path);
        $this->assertContains("merchant[id]=1111", $result->data[5]->iframe->full_path);
        $this->assertContains("currency=cad", $result->data[5]->iframe->full_path);

        foreach ($result->data as $paymentMethod) {
            $this->assertTrue(\in_array($paymentMethod->type_id, [
                PaymentMethodType::CASH,
                PaymentMethodType::CARD,
                PaymentMethodType::COMPLIMENTARY,
                PaymentMethodType::BANK_TRANSFER,
                PaymentMethodType::PAY_LATER,
                PaymentMethodType::DIRECT_DEBIT,
                PaymentMethodType::WALLET,
            ]));
        }

        unset($_SERVER['HTTP_X_GLOFOX_SOURCE']);
    }

    public function test_it_should_return_payment_collector_v2_in_iframe_url_if_provider_is_nicepay()
    {
        $_SERVER['HTTP_AUTHORIZATION'] = self::$tokenNicepay;
        $this->mockFlagger();

        $result = $this->testAction('/2.1/branches/' . self::NICEPAY_BRANCH . '/payment-methods?includes=provider,iframe', [
            'method' => 'get',
        ]);
        
        $result = json_decode($result, null, 512, JSON_THROW_ON_ERROR);
        $this->assertTrue($result->success);
        $this->assertCount(1, $result->data);
        $this->assertContains("/payment-collector/v2/#/payment-method", $result->data[0]->iframe->full_path);
        $this->assertContains("jwt-token=", $result->data[0]->iframe->full_path);

        foreach ($result->data as $paymentMethod) {
            $this->assertTrue(\in_array($paymentMethod->type_id, [
                PaymentMethodType::CASH,
                PaymentMethodType::CARD,
                PaymentMethodType::COMPLIMENTARY,
                PaymentMethodType::BANK_TRANSFER,
                PaymentMethodType::PAY_LATER,
                PaymentMethodType::DIRECT_DEBIT,
                PaymentMethodType::WALLET,
            ]));
        }

        unset($_SERVER['HTTP_X_GLOFOX_SOURCE']);
    }

    /**
     * We are certain about the admin app being the request source, but are not able to identify the app version
     * We must assume that the app is not direct debit ready and only 5 payment methods should be available.
     */
    public function test_it_should_filter_the_available_payment_method_given_an_admin_app_source_header()
    {
        $_SERVER['HTTP_AUTHORIZATION'] = self::$token;
        $this->mockFlagger();
        $_SERVER['HTTP_X_GLOFOX_PLATFORM'] = 'admin app';

        $result = $this->testAction('/2.1/branches/49a7011a05c677b9a916612a/payment-methods', [
            'method' => 'get',
        ]);

        $result = json_decode($result, null, 512, JSON_THROW_ON_ERROR);

        $this->assertTrue($result->success);
        $this->assertCount(5, $result->data);

        foreach ($result->data as $paymentMethod) {
            $this->assertTrue(\in_array($paymentMethod->type_id, [
                PaymentMethodType::CASH,
                PaymentMethodType::CARD,
                PaymentMethodType::COMPLIMENTARY,
                PaymentMethodType::BANK_TRANSFER,
                PaymentMethodType::PAY_LATER,
            ]));
        }

        unset($_SERVER['HTTP_X_GLOFOX_PLATFORM']);
    }

    public function test_it_should_not_alter_the_available_payment_method_given_an_admin_app_version_header()
    {
        $_SERVER['HTTP_AUTHORIZATION'] = self::$token;
        $_SERVER['HTTP_X_GLOFOX_SOURCE'] = 'ADMIN_APP';
        $_SERVER['HTTP_' . strtoupper(GLOFOX_APP_VERSION_HEADER)] = '1.1.3';

        $this->mockFlagger();
        $result = $this->testAction('/2.1/branches/49a7011a05c677b9a916612a/payment-methods', [
            'method' => 'get',
        ]);

        $result = json_decode($result, null, 512, JSON_THROW_ON_ERROR);

        $this->assertTrue($result->success);
        $this->assertCount(7, $result->data);

        unset($_SERVER['HTTP_X_GLOFOX_SOURCE']);
        unset($_SERVER['HTTP_' . strtoupper(GLOFOX_APP_VERSION_HEADER)]);
    }

    public function test_it_should_serve_all_total_payment_methods_without_any_restriction_for_stripe_dd()
    {
        $_SERVER['HTTP_AUTHORIZATION'] = self::$tokenStripeDD;
        $this->mockFlagger();
        $result = $this->testAction('/2.1/branches/49a7011a08c677b9a916612a/payment-methods', ['method' => 'get']);
        $result = json_decode($result, null, 512, JSON_THROW_ON_ERROR);

        $this->assertTrue($result->success);
        $this->assertCount(3, $result->data);

        $count = 0;
        foreach ($result->data as $paymentMethod) {
            if (!empty($paymentMethod->same_card_and_dd) && $paymentMethod->same_card_and_dd) {
                ++$count;
            }
        }

        $this->assertEquals(1, $count);
    }

    public function test_it_should_serve_pos_terminal_when_the_source_is_different_than_the_apps()
    {
        $_SERVER['HTTP_AUTHORIZATION'] = self::$tokenTerminalPOS;
        $this->mockFlagger();
        $result = $this->testAction('/2.1/branches/59a7011a02c677bda916612c/payment-methods', ['method' => 'get']);
        $result = json_decode($result, null, 512, JSON_THROW_ON_ERROR);

        $this->assertTrue($result->success);
        $this->assertCount(1, $result->data);
        $this->assertEquals(Type::POS_TERMINAL, $result->data[0]->type_id);
    }

    public function test_it_should_serve_exclude_terminal_pos_when_requesting_from_the_apps()
    {
        $_SERVER['HTTP_AUTHORIZATION'] = self::$tokenTerminalPOS;
        $this->mockFlagger();
        $_SERVER['HTTP_X_GLOFOX_SOURCE'] = 'ADMIN_APP';
        $result = $this->testAction('/2.1/branches/59a7011a02c677bda916612c/payment-methods', ['method' => 'get']);
        $result = json_decode($result, null, 512, JSON_THROW_ON_ERROR);

        $this->assertTrue($result->success);
        $this->assertCount(0, $result->data);
        unset($_SERVER['HTTP_X_GLOFOX_SOURCE']);
    }

    public function test_it_should_disable_and_enable_a_payment_method(): void
    {
        $_SERVER['HTTP_AUTHORIZATION'] = self::$token;
        $this->mockFlagger();
        $branchId = '49a7011a05c677b9a916612a';

        /** @var PaymentMethod $paymentMethod */
        $paymentMethod = $this->findPaymentMethodByBranchAndType($branchId, Type::DIRECT_DEBIT);

        // disabling
        $paymentMethod->put('active', false);

        $url = sprintf('/2.1/branches/%s/payment-methods/%s', $branchId, $paymentMethod->id());
        $this->testAction($url, ['method' => 'patch', 'data' => $paymentMethod->toArray()]);

        /** @var PaymentMethod $paymentMethod */
        $paymentMethod = $this->findPaymentMethodByBranchAndType($branchId, Type::DIRECT_DEBIT);
        $this->assertFalse($paymentMethod->isActive());

        // enabling
        $paymentMethod->put('active', true);

        $url = sprintf('/2.1/branches/%s/payment-methods/%s', $branchId, $paymentMethod->id());
        $this->testAction($url, ['method' => 'patch', 'data' => $paymentMethod->toArray()]);

        /** @var PaymentMethod $paymentMethod */
        $paymentMethod = $this->findPaymentMethodByBranchAndType($branchId, Type::DIRECT_DEBIT);
        $this->assertTrue($paymentMethod->isActive());
    }

    public function test_it_should_prevent_a_payment_method_from_being_disabled_if_there_are_active_subscriptions(): void
    {
        $_SERVER['HTTP_AUTHORIZATION'] = self::$token;
        $this->mockFlagger();
        $branchId = '49a7011a05c677b9a916612a';

        $originalUser = $this->fetchUser('a9a2222a05c677bda916613d');

        $userSwitchedToDDMembership = $originalUser->toArray();
        $userSwitchedToDDMembership['membership']['subscription']['payment_method_type_id'] = Type::DIRECT_DEBIT;

        $userSwitchedToDDMembership = $this->usersRepository->legacySaveOrFail($userSwitchedToDDMembership)['User'];
        $this->assertEquals(Type::DIRECT_DEBIT, $userSwitchedToDDMembership['membership']['subscription']['payment_method_type_id']);

        /** @var PaymentMethod $paymentMethod */
        $paymentMethod = $this->findPaymentMethodByBranchAndType($branchId, Type::DIRECT_DEBIT);

        $paymentMethod->put('active', false);

        $url = sprintf('/2.1/branches/%s/payment-methods/%s', $branchId, $paymentMethod->id());
        $result = $this->testAction($url, ['method' => 'patch', 'data' => $paymentMethod->toArray()]);
        $result = json_decode($result, true, 512, JSON_THROW_ON_ERROR);

        $this->assertFalse($result['success']);
        $this->assertEquals('UNABLE_TO_DISABLE_PAYMENT_METHOD_DUE_TO_ACTIVE_SUBSCRIPTIONS', $result['message_code']);

        // restoring user
        $this->usersRepository->legacySaveOrFail($originalUser->toArray());
    }

    public function test_it_should_prevent_a_pos_terminal_payment_method_from_being_enabled_if_there_are_no_terminal_readers(): void
    {
        $_SERVER['HTTP_AUTHORIZATION'] = self::$tokenTerminalPOS;
        $branchId = '59a7011a02c677bda916612c';
        $this->mockFlagger();
        // deactivate payment method
        $paymentMethod = $this->findPaymentMethodByBranchAndType($branchId, Type::POS_TERMINAL);
        $paymentMethod->put('active', false);
        $this->paymentMethodsRepository->legacySaveOrFail($paymentMethod->toArray());

        // attempt to enable the payment method
        $paymentMethod->put('active', true);
        $url = sprintf('/2.1/branches/%s/payment-methods/%s', $branchId, $paymentMethod->id());
        $result = $this->testAction($url, ['method' => 'patch', 'data' => $paymentMethod->toArray()]);
        $result = json_decode($result, true, 512, JSON_THROW_ON_ERROR);

        $this->assertFalse($result['success']);
        $this->assertEquals('UNABLE_TO_ENABLE_PAYMENT_METHOD_DUE_REQUIRING_AT_LEAST_ONE_TERMINAL_READER', $result['message_code']);

        // restoring payment method
        $this->paymentMethodsRepository->legacySaveOrFail($paymentMethod->toArray());
    }


    private function findPaymentMethodByBranchAndType(string $branchId, string $type): PaymentMethod
    {
        return $this->paymentMethodsRepository
            ->addCriteria(new BranchId($branchId))
            ->addCriteria(new TypeId($type))
            ->firstOrFail();
    }
}
