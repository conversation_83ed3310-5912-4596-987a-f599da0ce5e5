get:
  summary: Retrieve lead statuses
  operationId: get-lead-status
  tags:
    - Leads
  security:
    - coreapi_auth: [ ]
  description: Retrieve a list of available lead statuses.
  parameters:
    - schema:
        type: string
        pattern: '^[a-f\d]{24}$'
      name: branchId
      in: path
      required: true
  responses:
    '200':
      description: OK
      content:
        application/json:
          schema:
            type: array
            items:
              $ref: "../../schemas/lead_status.yml"
    '400':
      description: Bad Request
    '401':
      description: Unauthorized
    '404':
      description: Not Found
      content:
        application/json:
          schema:
            $ref: "../../schemas/exceptions/branch_not_found.yml"
    '500':
      description: Internal Server Error
