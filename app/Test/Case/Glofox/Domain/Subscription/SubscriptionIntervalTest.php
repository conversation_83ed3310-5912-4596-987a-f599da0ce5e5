<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\Subscription;

use Glofox\Domain\Memberships\Models\Plan;
use Glofox\Domain\Subscription\SubscriptionInterval;
use Glofox\Domain\Users\Models\UserMembershipSubscription;

\App::import('Test/Case', 'GlofoxTestCase');

class SubscriptionIntervalTest extends \GlofoxTestCase
{
    /**
     * @param int $intervalCount
     * @param string $interval
     * @param string $result
     * @dataProvider intervalDataProvider
     */
    public function test_it_formats_string_for_humans(int $intervalCount, string $interval, string $result): void
    {
        $subscription = new UserMembershipSubscription([
            'interval' => $interval,
            'interval_count' => $intervalCount
        ]);

        $subscriptionInterval = SubscriptionInterval::createFromSubscription($subscription);

        $this->assertSame($result, $subscriptionInterval->forHumans());
        $this->assertSame($result, (string)$subscriptionInterval);
    }

    public function test_it_creates_instance_from_interval_and_count(): void
    {
        $subscriptionInterval = SubscriptionInterval::create('month', 1);

        $this->assertSame('month', $subscriptionInterval->getInterval());
        $this->assertSame(1, $subscriptionInterval->getIntervalCount());
    }

    public function test_it_creates_instance_from_plan(): void
    {
        $plan = new Plan([
            'duration_time_unit' => 'month',
            'duration_time_unit_count' => 1,
        ]);

        $subscriptionInterval = SubscriptionInterval::createFromPlan($plan);

        $this->assertSame('month', $subscriptionInterval->getInterval());
        $this->assertSame(1, $subscriptionInterval->getIntervalCount());
    }

    public function intervalDataProvider(): array
    {
        return [
            [1, 'month', 'Month'],
            [2, 'month', '2 Months'],
            [1, 'week', 'Week'],
            [2, 'week', '2 Weeks'],
            [1, 'day', 'Day'],
            [3, 'day', '3 Days'],
            [1, 'year', 'Year'],
            [2, 'year', '2 Years'],
        ];
    }
}
