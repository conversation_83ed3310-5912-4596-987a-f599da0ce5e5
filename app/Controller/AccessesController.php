<?php

use Glofox\Domain\Accesses\Http\Requests\AccessesReportRequest;
use Glofox\Domain\Accesses\Model\AccessStatus;
use Glofox\Domain\Accesses\Services\AccessesPublisher;
use Glofox\Domain\AsyncEvents\Events\AccessGrantedEventMeta;
use Glofox\Domain\AsyncEvents\Events\AccessGrantedEventPayload;
use Glofox\Domain\Authentication\Auth;
use Glofox\Domain\Branches\Repositories\BranchesRepository;
use Glofox\Domain\Accesses\Repositories\AccessesRepository;
use Glofox\Domain\Users\Formatters\LegacyChildResponseFormatter;
use Illuminate\Http\JsonResponse;

App::uses('Folder', 'Utility');
App::uses('File', 'Utility');
App::uses('AppController', 'Controller');

App::import('Lib', 'BarcodeHelper');

class AccessesController extends \Glofox\Domain\Accesses\Http\AccessesController
{
    public $components = [
        'Utility',
        'S3',
        'JWT',
    ];

    public $uses = [
        'User',
        'Branch',
        'Access',
        'UserCredit',
    ];

    private $branchesRepository;

    private $accessesPublisher;

    public function __construct($request, $response)
    {
        parent::__construct($request, $response);

        $this->branchesRepository = app()->make(BranchesRepository::class);
        $this->accessesRepository = app()->make(AccessesRepository::class);
        $this->accessesPublisher = app()->make(AccessesPublisher::class);
    }

    /*******************************************************************************************
     *                                API 2.0 New Dashboard
     ******************************************************************************************/

    /**
     * Dispatcher for Model.
     *
     * @param string $identifier [description]
     *
     * @return string
     */
    public function dispatcher($identifier = null)
    {
        return parent::dispatch($identifier, $this->Access);
    }

    /**
     * Save an access object.
     *
     * @internal   some data validation required since its a complex object
     */
    public function upsert()
    {
        $response = $this->accessUpsert();
        $response = $this->Access->findOrFail($response['access']['_id']);

        return json_encode($response, JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    /**
     * Save an access object.
     * Same as the upsert method but returns the 2.0 response format
     *
     * @internal   some data validation required since its a complex object
     */
    public function upsert20()
    {
        $response = $this->accessUpsert();

        return json_encode($response, JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    /**
     * private function to upsert an access entry and emit the access event
     *
     * @internal   some data validation required since its a complex object
     */
    private function accessUpsert()
    {
        $user = $this->getUser();
        $payload = $this->getPayload();
        $this->loadModel('Access');
        $provider = $this->Access;

        $provider->namespace = $user['namespace'];
        $provider->setUpInjector([], null, $user);
        $response = $provider->post($payload);

        $this->postAccessUpsert($response);

        return $response;
    }

    /*!
     * Count total records in this collection
     * @return     [type]                   [description]
     */

    /**
     * @param $date
     *
     * @return string
     */
    public function count($date)
    {
        $user = $this->JWT->parseToken();
        $dtStart = $this->Access->beginOfDay($date);
        $dtEnd = $dtStart + (36 * 60 * 60);
        $date_condition = [
            '$gte' => new MongoDate($dtStart),
            '$lte' => new MongoDate($dtEnd),
        ];
        $params = [
            'conditions' => [
                'branch_id' => $user['branch_id'],
                'created' => $date_condition,
            ],
        ];
        $result = $this->Access->find('count', $params);

        return json_encode($result, JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    /**
     * Delete access item given its _id.
     *
     * @param string $id Course _id
     *
     * @return object {Course:object, success:boolean}
     * @internal   Logic delete, setting "active" attribute to false
     *
     * @example    https://www.glofoxlogin/courses/remove/{id}
     *
     */
    public function remove($id)
    {
        $this->JWT->parseToken();

        $result = $this->Access->delete($id);
        $result = ['success' => $result];

        $response = json_encode($result, JSON_PARTIAL_OUTPUT_ON_ERROR);

        return $response;
    }

    public function report(AccessesReportRequest $request): JsonResponse
    {
        $user = Auth::user();
        $branch = $user->branch();

        // Load the timezone offset
        $timezone_offset = $this->Branch->getTimeZoneOffset($branch->toArray());

        // Get the date parameter
        $start = new MongoDate($request->start() + $timezone_offset);
        $end = new MongoDate($request->end() + $timezone_offset);

        $response = $this->Access->getReport($user->toArray(), $start, $end);

        $formatter = new LegacyChildResponseFormatter();
        $response = $formatter->format($response);

        return response()->json($response);
    }

    private function postAccessUpsert($response)
    {
        if ($response['success'] && AccessStatus::GRANTED === $response['access']['status']) {
            $branch = $this->branchesRepository->findById($response['access']['branch_id']);

            $meta = new AccessGrantedEventMeta([
                'branchId' => $response['access']['branch_id'],
                'gympassGymId' => $branch['Branch']['features']['gympass']['id'] ?? null,
                'gympassProductId' => $branch['Branch']['features']['gympass']['product_id'] ?? null,
                'gympassPassTypeNumber' => $branch['Branch']['features']['gympass']['pass_type_number'] ?? null,
                'gympassValidationApiAuthToken' => $branch['Branch']['features']['gympass']['validation_api_auth_token'] ?? null,
                'gympassClientId' => $branch['Branch']['features']['gympass']['client_id'] ?? null,
                'gympassClientSecret' => $branch['Branch']['features']['gympass']['client_secret'] ?? null,
            ]);
            $payload = new AccessGrantedEventPayload([
                'userId' => $response['access']['user_id'],
            ]);

            $this->accessesPublisher->sendAccessGrantedEvent($meta, $payload);
        }
    }
}
