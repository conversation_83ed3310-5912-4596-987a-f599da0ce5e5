<?php

namespace Glofox\Infrastructure\Locker;

use Carbon\Carbon;
use Glofox\Domain\Cache\CacheClientInterface;
use Glofox\Domain\Locker\WebhookAlreadyLockedException;
use Glofox\Domain\Locker\WebhookLockerInterface;
use Glofox\Payments\Entities\WebHook\Contracts\WebhookEventInterface;
use Psr\Log\LoggerInterface;

class CacheWebhookLocker implements WebhookLockerInterface
{
    private \Glofox\Domain\Cache\CacheClientInterface $cache;

    private \Psr\Log\LoggerInterface $logger;

    public function __construct(LoggerInterface $logger, CacheClientInterface $cache)
    {
        $this->logger = $logger;
        $this->cache = $cache;
    }

    public function lock(WebhookEventInterface $webhookEvent): void
    {
        $eventId = $webhookEvent->getEventId();

        $this->logger->info(sprintf('Attempting to lock Webhook Event %s', $eventId));

        if ($this->cache->has($eventId)) {
            $this->logger->error(sprintf('Lock already exists for Webhook Event %s', $eventId));
            throw WebhookAlreadyLockedException::withWebhookEvent($webhookEvent);
        }

        $expiresAt = $this->maxTtl();

        $this->cache->set($eventId, $eventId);
        $this->cache->expiresAt($eventId, $expiresAt);

        $this->logger->info(sprintf('Lock successfully created for Webhook Event %s and set to expire at %s', $eventId, $expiresAt->toISOString()));
    }

    public function unlock(WebhookEventInterface $webhookEvent): void
    {
        $eventId = $webhookEvent->getEventId();

        $this->logger->info(sprintf('Attempting to unlock Webhook Event %s', $eventId));

        $this->cache->delete(
            $eventId
        );
    }

    private function maxTtl(): Carbon
    {
        return Carbon::now()->addMinutes(5);
    }
}
