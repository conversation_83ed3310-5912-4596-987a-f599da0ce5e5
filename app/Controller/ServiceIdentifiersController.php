<?php

use Glofox\Domain\SalesTaxes\UseCase\GetAllServiceIdentifiersByBranchId;
use Glofox\Request;
use Illuminate\Http\JsonResponse;
use Psr\Log\LoggerInterface;

\App::uses('AppController', 'Controller');

/**
 * Class ServiceIdentifiersController.
 */
class ServiceIdentifiersController extends \AppController
{
    /**
     * @throws UnsuccessfulOperation
     */
    public function index(Request $req, GetAllServiceIdentifiersByBranchId $useCase, LoggerInterface $logger): JsonResponse
    {
        $branchId = $req->cakeRouteParams()->get('branchId');

        $logger->info(sprintf('Attempt to get all service identifiers for branch %s', $branchId));

        try {
            $serviceIds = $useCase->execute($branchId); 

            $logger->info(sprintf('Service identifiers of branch %s successfully fetched', $branchId));

            return new JsonResponse(['service_identifiers' => $serviceIds], 200);
        } catch (Exception $e) {
            $logger->error(sprintf('Error fetching service identifiers for branch %s - %s', $branchId, $e->getMessage()));

            throw new UnsuccessfulOperation('ERROR_FETCHING_SERVICE_IDENTIFIERS');
        }
    }
}
