parameters:
  - schema:
      type: string
    name: modelId
    in: path
    required: true
    description: The unique identifier of the requested event
  - schema:
      type: string
      enum:
        - class
        - course
        - program
        - appointment
    name: modelIdentifier
    in: path
    required: true
    description: The model of the requested event
get:
  tags:
    - Booking
  summary: Get bookings
  operationId: list-bookings
  description:
    Use this call to list all the bookings for a given event. It will trigger a bad request response if the required
    parameters are not added to the request.
  security:
    - coreapi_auth: [ ]
  responses:
    '200':
      description: OK
      content:
        application/json:
          schema:
            allOf:
              - $ref: '../../schemas/booking.yml'
              - type: object
                properties:
                  batch_id:
                    type: string
                    description: "The batch this booking belongs to"
    '400':
      description: Bad request
      content:
        application/json:
          schema:
            $ref: "../../schemas/error.yml"
