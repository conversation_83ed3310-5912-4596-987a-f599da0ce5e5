@startuml
start

:process_time_slot_booking(
  timeSlotID,
  member,
  autoGenerated = false,
  payGym = false,
  isWebAdmin = false,
  paymentMethod = 'credit_card',
  price = null,
  offsession = true,
  batchID = null
);
if (timeslotIsBooked) then (yes)
  stop
else (not)
  :entity = FACILITY || APPOINTMENT || TRAINER;
  if (isAutogenerated) then (yes)
  else (not)
    if (isSingleBooking) then (yes)
      if (bookingWindow) then (not)
        stop
      else (yes)
      endif
    else (not)
    endif
    if (notHasAddons && membershipsIsNotValid) then (yes)
      stop
    else (not)
    endif
    #pink:findCreditForBooking();
    if (notAllowedGroup && notHasCredits && notHasAddons) then (yes)
      stop
    else (not)
    endif
    :resolvePrice();
    if (isSingleBooking && isStaff) then (yes)
      :overwritePrice;
    else (not)
    endif
    if (notHasAddons && isSingleBooking && isFreeBooking && notHasCredits`) then (yes)
      stop
    else (not)
    endif
    if (isNotPaygym && notHasCredits && notHasAddons) then (yes)
      #pink:validatePayment();
    else (not)
    endif
  endif
  if (isSingleBooking && hasAddons) then (yes)
    :setAddons();
  else (not)
  endif
  if (isSingleBooking && notHasAddons && hasCredits) then (yes)
    :setCredits();
  else (not)
  endif
  #green:createTimeSlotBooking(createTimeSlotBookingParameters);
  if (bookingWasCreated) then (not)
    stop
  else (yes)
  if (isSingleBooking && payWithAddons) then (yes)
    :consumeAddonCredits();
  else (not)
  endif
  if (isSingleBooking && wasPaidWithCredits && creditsAreForFirstBookingDate && creditsDoesNotHaveStartDate) then (yes)
    :updateCreditPackDates();
  else (not)
  endif
  :updateMembershipDatesBasedOnBooking();
  :sendBookingEmailToStudio();

stop
@enduml
