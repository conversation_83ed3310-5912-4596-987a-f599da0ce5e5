type: object
properties:
  branch_id:
    type: string
    pattern: '^[a-f\d]{24}$'
  user_id:
    type: string
    pattern: '^[a-f\d]{24}$'
  date_month:
    type: string
    format: date-time
  last_vist_at:
    type: string
    format: date-time
  local_last_visit_at:
    type: string
    format: date-time
  local_visits_executed_at:
    type: string
    format: date-time
  total_unique_visits_last_30_days:
    type: integer