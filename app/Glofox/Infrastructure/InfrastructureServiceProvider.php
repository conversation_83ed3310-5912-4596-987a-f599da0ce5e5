<?php

declare(strict_types=1);

namespace Glofox\Infrastructure;

use Glofox\Application;
use Glofox\Domain\Bookings\Repositories\BookingsRepository;
use Glofox\Domain\ElectronicAgreements\Services\ElectronicAgreementsServiceInterface;
use Glofox\Domain\Experiments\Services\ValidateExperimentAvailability;
use Glofox\Domain\FeatureFlags\FeatureFlagInterface;
use Glofox\Domain\Memberships\Services\AddonServiceInterface;
use Glofox\Domain\Reports\Services\ReportsServiceInterface;
use Glofox\Domain\RequestLog\Repositories\RequestLogRepository;
use Glofox\Domain\SalesTaxes\Services\PriceCalculatorClient;
use Glofox\Domain\SalesTaxes\Services\PriceCalculatorServiceInterface;
use Glofox\Domain\StaffAvailability\Services\StaffAvailabilityHttpClient;
use Glofox\Domain\TimeSlotPatterns\Repositories\TimeSlotPatternsRepository;
use Glofox\Domain\TimeSlots\Repositories\TimeSlotRepository;
use Glofox\Domain\Users\Repositories\UsersRepository;
use Glofox\Http\Requests\TracingContextInterface;
use Glofox\Infrastructure\ElectronicAgreements\ElectronicAgreementsHttpClient;
use Glofox\Infrastructure\GoogleRecaptcha\Factory\VerifyRequestFactory;
use Glofox\Infrastructure\GoogleRecaptcha\Factory\VerifyResponseFactory;
use Glofox\Infrastructure\GoogleRecaptcha\RecaptchaVerificationClient;
use Glofox\Infrastructure\Honeycomb\HoneycombTracker;
use Glofox\Infrastructure\Services\AddonsHttpService;
use Glofox\Infrastructure\Services\HttpLiveStreamInstaller;
use Glofox\Infrastructure\Services\LiveStreamInstallerInterface;
use Glofox\Infrastructure\Services\PriceCalculatorHttpService;
use Glofox\Infrastructure\Services\Reports\HttpReportsService;
use Glofox\Infrastructure\UrlSigner\Md5UrlSignerFactory;
use GuzzleHttp\Client as HttpClient;
use Illuminate\Support\ServiceProvider;
use MongoDB\Database;
use MongoDB\Driver\ReadConcern;
use Psr\Log\LoggerInterface;
use StringTemplate\Engine;

class InfrastructureServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->app->bind(LiveStreamInstallerInterface::class, fn(Application $app) => new HttpLiveStreamInstaller(
            new HttpClient([
                'base_uri' => trim(env('LIVE_STREAM_INSTALLER_URL'), '/') . '/',
            ]),
            $app->make(LoggerInterface::class)
        ));

        $this->app->bind(ElectronicAgreementsServiceInterface::class, fn(Application $app) => new ElectronicAgreementsHttpClient(
            $app->make(LoggerInterface::class),
            $app->make(ValidateExperimentAvailability::class),
            new HttpClient([
                'base_uri' => trim(env('ELECTRONIC_AGREEMENTS_SERVICE_URL'), '/') . '/',
            ])
        ));

        $this->app->bind(StaffAvailabilityHttpClient::class, fn(Application $app) => new StaffAvailabilityHttpClient(
            $app->make(LoggerInterface::class),
            new HttpClient([
                'base_uri' => trim(env('GLOFOX_ROOT_DOMAIN'), '/') . '/',
            ]),
            $app->make(FeatureFlagInterface::class)
        ));

        $this->app->bind(ReportsServiceInterface::class, static fn(Application $app) => new HttpReportsService(
            new HttpClient([
                'base_uri' => trim(env('REPORTS_SERVICE_URL'), '/') . '/',
                'http_errors' => true,
            ]),
            $app->make(LoggerInterface::class),
            $app->make(TracingContextInterface::class)
        ));

        $this->app->bind(PriceCalculatorServiceInterface::class, static function (Application $app) {
            $guzzle = new HttpClient([
                'base_uri' => trim(env('SALES_TAX_SERVICE_URL'), '/') . '/',
                'http_errors' => true,
            ]);

            $client = new PriceCalculatorClient(
                $guzzle,
                $app->make(RequestLogRepository::class),
                $app->make(LoggerInterface::class),
                $app->make(TracingContextInterface::class)
            );

            return new PriceCalculatorHttpService(
                $client,
                $app->make(LoggerInterface::class)
            );
        });

        $this->app->singleton(Database::class, function () {
            $host = env('GLOFOX_DATABASE_HOST');
            $port = (int)env('GLOFOX_DATABASE_PORT');

            $options = [];

            $username = env('GLOFOX_DATABASE_USERNAME');
            if ($username) {
                $options['username'] = $username;
                $options['password'] = env('GLOFOX_DATABASE_PASSWORD');
                $options['authSource'] = env('GLOFOX_DATABASE_FOR_AUTHENTICATION') ?: env('GLOFOX_DATABASE_NAME');
                $options['authMechanism'] = "SCRAM-SHA-1";
            }

            $replicaSet = env('GLOFOX_DATABASE_REPLICASET_NAME');
            if ($replicaSet) {
                $options['ssl'] = env('GLOFOX_DATABASE_SSL') ?: 0;
                $options['replicaSet'] = $replicaSet;
                $options['readConcern'] = ReadConcern::MAJORITY;
            }

            $client = new \MongoDB\Client("mongodb://$host:$port", $options);

            return $client->selectDatabase(getenv('GLOFOX_DATABASE_NAME'));
        });

        $this->app->bind(BookingsRepository::class, function (Application $app) {
            $bookingCakeModel = app()->make(\Booking::class);
            $branchCakeModel = app()->make(\Branch::class);
            $stringTemplate = new Engine('{{', '}}');

            return new BookingsRepository($bookingCakeModel, $branchCakeModel, $stringTemplate);
        });

        $this->app->bind(UsersRepository::class, function (Application $app) {
            /** @var Database $db */
            $db = $app->make(Database::class);
            $collection = $db
                ->selectCollection('users')
                ->withOptions([
                    'readConcern' => new ReadConcern(ReadConcern::AVAILABLE),
                ]);

            return new UsersRepository($collection);
        });

        $this->app->bind(TimeSlotPatternsRepository::class, function (Application $app) {
            $cakeModel = app()->make(\TimeSlotPattern::class);

            /** @var Database $db */
            $db = $app->make(Database::class);
            $collection = $db
                ->selectCollection('time_slot_patterns')
                ->withOptions([
                    'readConcern' => new ReadConcern(ReadConcern::AVAILABLE),
                ]);

            return new TimeSlotPatternsRepository(
                $cakeModel,
                $collection
            );
        });

        $this->app->bind(TimeSlotRepository::class, function (Application $app) {
            $cakeModel = app()->make(\TimeSlot::class);

            /** @var Database $db */
            $db = $app->make(Database::class);
            $collection = $db
                ->selectCollection('time_slots')
                ->withOptions([
                    'readConcern' => new ReadConcern(ReadConcern::AVAILABLE),
                ]);

            return new TimeSlotRepository(
                $cakeModel,
                $collection
            );
        });

        $this->app->bind(AddonServiceInterface::class, static fn(Application $app) => new AddonsHttpService(
            new HttpClient([
                'base_uri' => trim(env('ADDON_SERVICE_URL'), '/') . '/',
                'http_errors' => true,
                'headers' => [
                    'x-glofox-api-token' => env('ADDON_SERVICE_API_TOKEN'),
                ],
            ]),
            $app->make(LoggerInterface::class)
        ));

        $this->app->bind(HoneycombTracker::class, static fn() => new HoneycombTracker(
            new HttpClient([
                'base_uri' => trim(env('HONEYCOMB_BASE_URL'), '/') . '/',
                'http_errors' => true,
                'headers' => [
                    'x-honeycomb-team' => env('HONEYCOMB_API_KEY'),
                    'content-type' => 'application/json',
                ],
            ]),
            env('HONEYCOMB_DATASET_NAME'),
        ));

        $this->app->singleton(Md5UrlSignerFactory::class, fn() => new Md5UrlSignerFactory(
            env('URL_SIGNATURE_KEY')
        ));

        $this->app->singleton(
            RecaptchaVerificationClient::class,
            function (Application $app): RecaptchaVerificationClient {
                $client = new HttpClient([
                    'base_uri' => trim(env('GOOGLE_RECAPTCHA_BASE_URL'), '/') . '/',
                    'http_errors' => true,
                ]);

                return new RecaptchaVerificationClient(
                    $client,
                    $app->make(VerifyResponseFactory::class),
                    $app->make(LoggerInterface::class),
                );
            }
        );

        $this->app->singleton(VerifyRequestFactory::class, fn(Application $app): VerifyRequestFactory => new VerifyRequestFactory(
            env('GOOGLE_RECAPTCHA_SECRET')
        ));
    }
}
