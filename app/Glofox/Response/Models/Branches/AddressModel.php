<?php

namespace Glofox\Response\Models\Branches;

use Glofox\Response\BaseModel;

/**
 * @SuppressWarnings(PHPMD)
 */
class AddressModel extends BaseModel
{
    /**
     * @var string
     */
    public $street;

    /**
     * @var string
     */
    public $city;

    /**
     * @var string
     */
    public $state;

    /**
     * @var string
     */
    public $country;

    /**
     * @var string
     */
    public $country_code;

    /**
     * @var string
     */
    public $district;

    /**
     * @var float
     */
    public $latitude;

    /**
     * @var float
     */
    public $longitude;

    /**
     * @var string
     */
    public $currency;

    /**
     * @var string
     */
    public $continent;

    /**
     * @var string
     */
    public $timezone_id;

    /**
     * @var string
     */
    public $timezone_name;

    /**
     * @var string
     */
    public $postal_code;

    /**
     * @var string
     */
    public $location;

    protected array $allowedFields = [
        'street',
        'city',
        'state',
        'country',
        'country_code',
        'district',
        'latitude',
        'longitude',
        'currency',
        'continent',
        'timezone_id',
        'timezone_name',
        'postal_code',
        'location',
    ];

    public function __construct(array $data)
    {
        $this->setModelFields($data);
    }
}
