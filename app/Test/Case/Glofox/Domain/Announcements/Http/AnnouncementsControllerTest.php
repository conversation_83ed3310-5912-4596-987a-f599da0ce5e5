<?php

namespace CakeTestCases\Glofox\Domain\Announcements\Http;

use App;
use Glofox\Domain\Announcements\Models\Announcement;
use Glofox\Domain\Announcements\Http\Transformers\AnnouncementTransformer;
use Glofox\Domain\Announcements\UseCase\ListAnnouncements;
use Glofox\Domain\Announcements\UseCase\ListAnnouncementsParams;
use GlofoxControllerTestCase;
use League\Fractal\Manager;
use League\Fractal\Resource\Collection;
use League\Fractal\Scope;
use Mockery;

App::import('Test/Case', 'GlofoxControllerTestCase');

class AnnouncementsControllerTest extends GlofoxControllerTestCase
{
    /** @var ListAnnouncements */
    private $listAnnouncements;

    /** @var Manager */
    private $fractalManager;

    public function setUp()
    {
        parent::setUp();

        $this->listAnnouncements = Mockery::mock(ListAnnouncements::class);
        $this->fractalManager = Mockery::mock(Manager::class);
    }

    public function tearDown()
    {
        parent::tearDown();

        Mockery::close();
    }

    public function test_execute_method_of_use_case_is_called_with_the_right_parameters()
    {
        $paygUser = $this->fetchUser('5b19430ea0d988945a164337');
        $this->loginAsUser($paygUser);

        $branchId = '49a7011a05c677b9a916612a';

        $this->listAnnouncements
            ->shouldReceive('execute')
            ->withArgs(function(ListAnnouncementsParams $params) use ($branchId, $paygUser) {
                self::assertSame($branchId, $params->branchId());
                self::assertSame($paygUser->id(), $params->user()->id());

                return true;
            })
            ->andReturn([])
            ->once();

        app()->instance(ListAnnouncements::class, $this->listAnnouncements);

        $url = sprintf('/2.2/branches/%s/announcements', $branchId);

        $this->testAction($url, ['method' => 'GET']);

        app()->forgetInstance(ListAnnouncements::class);
    }

    public function test_list_should_return_empty_if_there_are_not_announcements_for_branch()
    {
        $paygUser = $this->fetchUser('5b19430ea0d988945a164337');
        $this->loginAsUser($paygUser);

        $branchId = '49a7011a05c677b9a916612a';

        $this->listAnnouncements
            ->shouldReceive('execute')
            ->andReturn([])
            ->once();

        app()->instance(ListAnnouncements::class, $this->listAnnouncements);

        $url = sprintf('/2.2/branches/%s/announcements', $branchId);

        $response = $this->testAction($url, ['method' => 'GET']);

        $response = json_decode($response, true, 512, JSON_THROW_ON_ERROR);

        self::assertEmpty($response['data']);

        app()->forgetInstance(ListAnnouncements::class);
    }

    public function test_list_should_return_found_announcements_for_branch()
    {
        $paygUser = $this->fetchUser('5b19430ea0d988945a164337');
        $this->loginAsUser($paygUser);

        $branchId = '49a7011a05c677b9a916612a';

        $this->listAnnouncements
            ->shouldReceive('execute')
            ->andReturn($data = [
                Announcement::make(['_id' => '49b7012a05c677c9a512503c']),
            ]);

        app()->instance(ListAnnouncements::class, $this->listAnnouncements);

        $this->fractalManager
            ->shouldReceive('createData')
            ->withArgs(
                function (Collection $resource) use ($data) {
                    $this->assertSame($data, $resource->getData());
                    $this->assertInstanceOf(AnnouncementTransformer::class, $resource->getTransformer());

                    return true;
                }
            )
            ->andReturn(
                Mockery::mock(Scope::class)
                    ->shouldReceive('toArray')
                    ->andReturn(compact('data'))
                    ->getMock()
            );

        app()->instance(Manager::class, $this->fractalManager);

        $url = sprintf('/2.2/branches/%s/announcements', $branchId);
        $response = $this->testAction($url, ['method' => 'GET']);
        $response = json_decode($response, true, 512, JSON_THROW_ON_ERROR);

        self::assertNotEmpty($response['data']);

        app()->forgetInstance(ListAnnouncements::class);
        app()->forgetInstance(Manager::class);
    }
}
