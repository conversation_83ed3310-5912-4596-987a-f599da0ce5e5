<?php

declare(strict_types=1);

namespace Glofox\Domain\Communications\Services;

use Carbon\Carbon;
use Glofox\Domain\Cache\CacheClientInterface;
use Glofox\Domain\Communications\UseCase\ModerateMessageParams;
use Glofox\Domain\Http\Services\HttpServiceClient;
use Glofox\Domain\RequestLog\Repositories\RequestLogRepository;
use Glofox\Domain\Users\Models\User;
use Glofox\Exception;
use Glofox\Http\Requests\TracingContext;
use Glofox\Http\Requests\TracingContextInterface;
use GuzzleHttp\ClientInterface;
use GuzzleHttp\Exception\GuzzleException;
use Psr\Http\Message\ResponseInterface;
use Psr\Log\LoggerInterface;
use Throwable;
use Glofox\Infrastructure\Honeycomb\HoneycombTracker;

class CommunicationsClient extends HttpServiceClient
{
    private const ENDPOINTS = [
        'UPLOAD_ATTACHMENT' => 'v1/upload-attachment',
        'MESSAGES_MODERATE' => 'v1/messages/moderate',
    ];
    private TracingContextInterface $tracingContext;
    private CacheClientInterface $cacheClient;
    private HoneycombTracker $honeycombTracker;

    public function __construct(
        ClientInterface         $httpClient,
        RequestLogRepository    $logRepository,
        LoggerInterface         $logger,
        TracingContextInterface $tracingContext,
        CacheClientInterface    $cacheClient,
        HoneycombTracker        $honeycombTracker
    )
    {
        $this->tracingContext = $tracingContext;
        $this->cacheClient = $cacheClient;
        $this->honeycombTracker = $honeycombTracker;

        parent::__construct('communications', $httpClient, $logRepository, $logger);
    }

    protected function getEndpoints(): array
    {
        return self::ENDPOINTS;
    }

    /**
     * @throws GuzzleException
     * @throws Exception
     */
    public function uploadAttachment(array $attachments, User $user): string
    {
        $this->logger->info('Uploading attachment for user', ['user' => $user->id()]);
        $headers = $this->setRequestHeaders($user->originBranchId());
        $attachments = reset($attachments);
        $content = $this->processAttachment($attachments);
        $body = $this->encodeAttachment($content);

        $endpoints = $this->getEndpoints();
        $uri = $endpoints['UPLOAD_ATTACHMENT'];

        $response = $this->httpClient->request(
            'POST',
            $uri,
            [
                'headers' => $headers,
                'http_errors' => false,
                'body' => $body,
            ]
        );

        $this->validateCommunicationsResponse($response);

        $resp = parent::normalizeResponseData($response);

        return $resp->url;
    }

    /**
     * @throws Throwable
     */
    public function moderateMessage(ModerateMessageParams $params)
    {
        $cacheKey = $this->generateKeyForHtml($params->message());
        $cached = $this->cacheClient->get($cacheKey);

        if ($cached !== null) {
            return json_decode($cached, null, 512, JSON_THROW_ON_ERROR);
        }

        $this->logger->info('Calling Communications Moderate endpoint for branch ' . $params->branchId());

        $headers = $this->setRequestHeaders($params->branchId());
        $uri = $this->getEndpoints()['MESSAGES_MODERATE'];

        $this->trackHoneycombData($params->identifier());

        $response = $this->sendModerationRequest($params, $headers, $uri);
        $this->validateCommunicationsResponse($response);

        $normalizedResponse = parent::normalizeResponseData($response);

        $expiresAt = Carbon::now()->addMinutes(10);

        $added = $this->cacheClient->setIfNotExist($cacheKey, json_encode($normalizedResponse, JSON_THROW_ON_ERROR));

        if ($added) {
            $this->cacheClient->expiresAt($cacheKey, $expiresAt);
        }

        return $normalizedResponse;
    }

    private function sendModerationRequest(ModerateMessageParams $params, array $headers, string $uri): ResponseInterface
    {
        try {
            return $this->httpClient->request(
                'POST',
                $uri,
                [
                    'headers' => $headers,
                    'http_errors' => false,
                    'body' => json_encode(['message' => $params->message()], JSON_THROW_ON_ERROR),
                ]
            );
        } catch (\JsonException $jsonException) {
            $this->logger->error('Error building request body: ' . $jsonException->getMessage());
            throw $jsonException;
        } catch (GuzzleException $guzzleException) {
            $this->logger->error('Error calling moderation endpoint: ' . $guzzleException->getMessage());
            throw $guzzleException;
        }
    }

    /**
     * @throws Exception
     */
    private function validateCommunicationsResponse(ResponseInterface $response): void
    {
        if ($response->getStatusCode() !== 200) {
            throw new Exception(sprintf('Received a failed response from Communications API: %s', $response->getBody()));
        }
    }

    private function setRequestHeaders(string $branchId): array
    {
        $headers = [];

        $headers[TracingContext::TRACING_HEADER] = $this->tracingContext->getTracingHeaderForPropagation();
        $headers['Content-Type'] = 'application/json';
        $headers['x-glofox-branch-id'] = $branchId;
        $headers['X-Glofox-Api-Token'] = sprintf('Bearer %s', env('COMMUNICATIONS_HTTP_API_KEY'));

        if (isset($_SERVER['HTTP_X_REQUEST_ID'])) {
            $headers['x-request-id'] = $_SERVER['HTTP_X_REQUEST_ID'];
        }

        return $headers;
    }

    /**
     * @throws Exception
     */
    private function processAttachment(array $attachment): string
    {
        if (isset($attachment['file'])) {
            $filePath = $attachment['file'];
            if (file_exists($filePath)) {
                return file_get_contents($filePath);
            } else {
                throw new Exception("File not found: $filePath");
            }
        }
        throw new Exception("Invalid attachment format");
    }

    /**
     * @throws Exception
     */
    private function encodeAttachment(string $content): string
    {
        $contentBytes = array_values(unpack('C*', $content));
        try {
            return json_encode(['content' => $contentBytes], JSON_THROW_ON_ERROR);
        } catch (\JsonException $e) {
            throw new Exception('Failed to encode attachment content to JSON: ' . $e->getMessage());
        }
    }

    private function generateKeyForHtml(string $html): string
    {
        return 'html_eval:' . hash('sha256', $html);
    }

    private function trackHoneycombData(string $messageType): void
    {
        try {
            $honeycombData = [
                'service.name' => 'api',
                'service_name' => 'api',
                'trace.span_id' => $this->tracingContext->getSpanId(),
                'trace.trace_id' => $this->tracingContext->getTraceId(),
                'glofox.context.message.type' => $messageType,
                'name' => 'ModerateMessage',
            ];

            $this->honeycombTracker->track($honeycombData);

        } catch (Throwable $e) {
            $errorMsg = "Something went wrong while sending the CommunicationsClient data to Honeycomb."
                . " Error: " . $e->getMessage();
            $this->logger->error($errorMsg);
        }
    }
}
