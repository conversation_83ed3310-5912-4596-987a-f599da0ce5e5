<?php

namespace Glofox\Payments\Providers\Gateway\ProtoParsers;

use Carbon\Carbon;
use Glofox\Domain\Branches\Models\Branch;
use Glofox\Domain\Branches\Repositories\BranchesRepository;
use Glofox\Infrastructure\Flags\Flagger;
use Glofox\Infrastructure\Flags\Flag;
use Glofox\Payments\Entities\Invoice\Models\InvoiceAddress;
use Glofox\Payments\Entities\Invoice\Models\InvoiceAmount;
use Glofox\Payments\Entities\Merchant\Exceptions\MerchantRetrievalException;
use Glofox\Payments\Entities\Merchant\Models\Account;
use Glofox\Payments\Util;
use Glofox\Repositories\Search\Expressions\Shared\Id;
use Google\Protobuf\Internal\RepeatedField;
use Glofox\Payments\Entities\Invoice\Models\Invoice as InvoiceProvider;
use Glofox\Payments\Providers\Gateway\GRPC\GRPC;
use GRPC\Payments\AccountService\GetFromProviderReq;
use GRPC\Payments\TransactionService\Invoice;
use Psr\Log\LoggerInterface;

/**
 * Class InvoiceParser
 * @package Glofox\Payments\ProtoParsers
 */
class InvoiceParser
{
    use GRPC;
    private \Glofox\Payments\Providers\Gateway\ProtoParsers\AccountParser $accountParser;

    private \Glofox\Domain\Branches\Repositories\BranchesRepository $branchesRepository;

    /**
     * InvoiceParser constructor.
     */
    public function __construct()
    {
        $this->accountParser = new AccountParser();
        $this->branchesRepository = new BranchesRepository();
        $this->flagger = app()->make(Flagger::class);
    }

    /**
     * @param Invoice $invoice
     * @param string $branchId
     * @return InvoiceProvider
     */
    public function parseFromProtoMessage($invoice, string $branchId): InvoiceProvider
    {
        $providerInvoice = new InvoiceProvider();

        $providerInvoice->setMonth($invoice->getMonth())
            ->setYear($invoice->getYear())
            ->setDate(
                Carbon::parse($invoice->getCreatedAt())->getTimestamp()
            );

        if (!is_callable([$invoice, 'getMerchant'])) {
            return $providerInvoice;
        }

        $useProviderAccountAddress = false;

        if ($this->flagger->withFlag(Flag::IS_FETCHING_FINANCIAL_ADDRESS_FROM_STRIPE_ENABLED())->hasByBranchId($branchId)) {

            $getFromProviderReq = new GetFromProviderReq();
            $getFromProviderReq->setID($invoice->getMerchant()->getID());

            /** @var $reply \GRPC\Payments\AccountService\Account */
            [$reply, $status] = $this->GRCPClient()->accountService->GetFromProvider($getFromProviderReq)->wait();

            if (0 != $status->code || !($reply instanceof \GRPC\Payments\AccountService\Account)) {
                throw new MerchantRetrievalException($this->getErrorFromStatus($status));
            }

            $account = $this->accountParser->parseFromProtoMessage($reply);
            $useProviderAccountAddress = true;

        } else {
            $account = $this->accountParser->parseFromProtoMessage($invoice->getMerchant());
        }

        /** @var Branch $branch */
        $branch = $this->branchesRepository
            ->addCriteria(new Id($branchId))
            ->firstOrFail();

        $taxNumber = $branch->taxNumber();

        $providerInvoice->setCurrency($account->currency())
            ->setAccountNumber($account->serviceProviderId())
            ->setVatNumber($taxNumber)
            ->setNumber($this->getInvoiceNumber($providerInvoice, $branch))
            ->setAmounts($this->getInvoiceAmount($invoice, $account))
            ->setAddress($this->getInvoiceAddress($account, $branch, $useProviderAccountAddress));

        return $providerInvoice;
    }

    /**
     * @param RepeatedField|Invoice[] $invoices
     * @param string $branchId
     * @return InvoiceProvider[]
     */
    public function parseFromProtoMessageCollection(RepeatedField $invoices, string $branchId): ?array
    {
        $providerInvoices = null;

        if (empty($invoices)) {
            return $providerInvoices;
        }

        foreach ($invoices as $invoice) {
            $providerInvoices[] = $this->parseFromProtoMessage($invoice, $branchId);
        }

        return $providerInvoices;
    }

    /**
     * @param $invoice
     * @param Account $account
     * @return InvoiceAmount|null
     */
    private function getInvoiceAmount(Invoice $invoice, Account $account): ?InvoiceAmount
    {
        $amount = new InvoiceAmount();

        $amount->setDisputeFees(
            Util::convertFromCents($invoice->getTotalFeesDisputes())
        )
            ->setDisputeFeesVat(0)
            ->setProcessingFees(
                Util::convertFromCents($invoice->getTotalFeesCharges())
            )
            ->setProcessingFeesVat(0)
            ->setTotal(
                Util::convertFromCents($invoice->getTotalFees())
            )
            ->setTotalVat(0)
            ->setDebited($amount->total())
            ->setTotalDue(0);

        if (Util::isVATChargeable($account->legalEntity()->address()->country())) {
            $this->convertToVATInclusive($amount);
        }

        return $amount;
    }

    /**
     * @param $account Account
     * @param Branch $branch
     * @return InvoiceAddress|null
     */
    private function getInvoiceAddress(Account $account, Branch $branch, bool $useProviderAccountAddress): InvoiceAddress
    {
        $address = new InvoiceAddress();

        $address->setName($account->legalEntity()->businessName())
            ->setOwner($account->companyRepresentative()->fullName())
            ->setAddressLine1($branch->address()->street())
            ->setAddressLine2($branch->address()->city())
            ->setAddressLine3($branch->address()->country());

        if ($useProviderAccountAddress) {
            $pspResponse = $account->serviceProviderResponse();
            $address->setAddressLine1($this->extractCompanyAddress($pspResponse)['street'])
                ->setAddressLine2($this->extractCompanyAddress($pspResponse)['city'])
                ->setAddressLine3($this->extractCompanyAddress($pspResponse)['country']);

        }

        return $address;
    }

    /**
     * @param InvoiceProvider $invoice
     * @param array $branch
     * @return string
     */
    private function getInvoiceNumber(InvoiceProvider $invoice, Branch $branch): string
    {
        $branchCode = substr($branch->id(), -5) . substr($branch->id(), 5, 5);

        return strtoupper($branchCode) . '-' . $invoice->year() . $invoice->month();
    }

    /**
     * @param InvoiceAmount $amount
     * @return InvoiceAmount|null
     */
    private function convertToVATInclusive(InvoiceAmount $amount): ?InvoiceAmount
    {
        $vatProcessingFees = Util::calculateInclusiveVAT($amount->processingFees());
        $vatDisputes = Util::calculateInclusiveVAT($amount->disputeFees());

        $amount->setProcessingFeesVat($vatProcessingFees)
            ->setDisputeFeesVat($vatDisputes)
            ->setTotalVat($vatProcessingFees + $vatDisputes)

            ->setProcessingFees($amount->processingFees() - $vatProcessingFees)
            ->setDisputeFees($amount->disputeFees() - $vatDisputes);

        return $amount;
    }

    /**
     * Extracts company address from PSP response
     * This is based on Stripe PSP response, since the monthly invoice
     * is currently available just for stripe
     * @param string $pspResponse
     * @return array{street: string, city: string, country: string}
     */
    private function extractCompanyAddress(string $pspResponse): array
    {
        $logger = app()->make(LoggerInterface::class);
        $logger->info(sprintf('Extracting company address from PSP response: %s', $pspResponse));

        $pspResponseArray = json_decode($pspResponse, true, 512, JSON_THROW_ON_ERROR);
        $companyAddress = $pspResponseArray['company']['address'] ?? [];

        $street = $companyAddress['line1'] ?? '';
        $street .= $companyAddress['line2'] ? ', ' . $companyAddress['line2'] : '';

        $city = ($companyAddress['postal_code'] ?? '') . " ";
        $city .= $companyAddress['city'] ?? '';

        return [
            'street' => $street,
            'city' => $city,
            'country' => $companyAddress['country'] ?? '',
        ];
    }
}
