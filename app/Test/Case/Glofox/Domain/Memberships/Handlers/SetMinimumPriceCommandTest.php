<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\Memberships\Handlers;

use App;
use Glofox\Domain\Memberships\Handlers\SetMinimumPriceCommand;
use Glofox\Domain\Memberships\Models\MinimumPrice;
use Glofox\Exception;
use GlofoxTestCase;

App::import('Test/Case', 'GlofoxTestCase');

class SetMinimumPriceCommandTest extends GlofoxTestCase
{
    private const TEST_MEMBERSHIP_ID = 'test-membership-id';
    private const VALID_MINIMUM_PRICE_RAW = '123:12,456:45,789:78';
    public $fixtures = [];

    /**
     * @dataProvider createFromRawDataProvider
     * @throws Exception
     */
    public function testCreateFromRaw(
        string $membershipId,
        array $plansPerMembership,
        string $expectedException,
        ?array $expectedMinimumPrice = null
    ): void
    {
        if ($expectedException !== "") {
            $this->expectExceptionMessage($expectedException);
        }

        $command = SetMinimumPriceCommand::createFromRaw($membershipId, $plansPerMembership);

        if ($expectedException === "") {
            self::assertEquals($membershipId, $command->membershipId());
            self::assertEquals($expectedMinimumPrice, $command->minimumPrices());
        }
    }

    /**
     * @throws Exception
     */
    public function createFromRawDataProvider(): array
    {
        return [
            'when membership id is empty, then it throws a membership id should be provided exception' => [
                'membershipId' => '',
                'plansPerMembership' => [],
                'expectedException' => 'Membership ID and Minimum Price by membership should be provided',
                'expectedMinimumPrice' => null,
            ],
            'when minimum prices raw is empty, then it throws a minimum price should be provided exception' => [
                'membershipId' => self::TEST_MEMBERSHIP_ID,
                'plansPerMembership' => [],
                'expectedException' => 'Membership ID and Minimum Price by membership should be provided',
                'expectedMinimumPrice' => null,
            ],
            'when minimum prices raw is not numeric, then it throws a minimum price should be number exception' => [
                'membershipId' => self::TEST_MEMBERSHIP_ID,
                'plansPerMembership' => ['aaa:bbb'],
                'expectedException' => 'Minimum price should be a number',
                'expectedMinimumPrice' => null,
            ],
            'when valid params provided, then it returns an array of prices' => [
                'membershipId' => self::TEST_MEMBERSHIP_ID,
                'plansPerMembership' => explode(',', self::VALID_MINIMUM_PRICE_RAW),
                'expectedException' => '',
                'expectedMinimumPrice' => [
                    '123' => new MinimumPrice(self::TEST_MEMBERSHIP_ID, '123', 12),
                    '456' => new MinimumPrice(self::TEST_MEMBERSHIP_ID, '456', 45),
                    '789' => new MinimumPrice(self::TEST_MEMBERSHIP_ID, '789', 78),
                ],
            ],
        ];
    }
}
