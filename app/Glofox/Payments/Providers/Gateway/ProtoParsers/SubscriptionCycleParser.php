<?php


namespace Glofox\Payments\Providers\Gateway\ProtoParsers;

use Carbon\Carbon;
use Glofox\Exception;
use Glofox\Payments\Entities\Subscription\Models\SubscriptionCycle as ProviderSubscriptionCycle;
use Glofox\Payments\Entities\Subscription\Models\SubscriptionCycleConfirmation;
use Glofox\Payments\Entities\Subscription\Models\SubscriptionCycleStatus;
use Glofox\Payments\Entities\Subscription\Models\SubscriptionCycleStyle;
use GRPC\Payments\SubscriptionService\SubscriptionCycle;
use GRPC\Payments\SubscriptionService\SubscriptionCycleRetryStyle;
use GRPC\Payments\SubscriptionService\SubscriptionCycleStatus as GRPCSubscriptionCycleStatus;

class SubscriptionCycleParser
{
    public function parseFromProtoMessage(SubscriptionCycle $cycle): ProviderSubscriptionCycle
    {
        $providerSubscriptionCycle = new ProviderSubscriptionCycle();

        $periodStart = Carbon::parse($cycle->getPeriodStart(), 'UTC');
        $periodEnd = Carbon::parse($cycle->getPeriodEnd(), 'UTC');
        $status = $this->parseStatus($cycle->getStatus());
        $retryStyle = $this->parseRetryStyle($cycle->getRetryStyle());
        $confirmed = SubscriptionCycleConfirmation::byValue($cycle->getConfirmed());

        $providerSubscriptionCycle
            ->setId((string) $cycle->getID())
            ->setEventId($cycle->getEventID())
            ->setPeriodStart($periodStart)
            ->setPeriodEnd($periodEnd)
            ->setCurrentRetries($cycle->getCurrentRetries())
            ->setStatus($status)
            ->setRetryStyle($retryStyle)
            ->setConfirmation($confirmed);

        return $providerSubscriptionCycle;
    }

    private function parseRetryStyle($style): SubscriptionCycleStyle
    {
        switch ($style) {
            case SubscriptionCycleRetryStyle::LockSub:
                return SubscriptionCycleStyle::LOCK_SUB();
            case SubscriptionCycleRetryStyle::Parallel:
                return SubscriptionCycleStyle::PARALLEL();
            default:
                throw new Exception(sprintf('Unable to parse subscription cycle retry style [%s]', $style));
        }
    }

    private function parseStatus($status): SubscriptionCycleStatus
    {
        switch ($status) {
            case GRPCSubscriptionCycleStatus::Open:
                return SubscriptionCycleStatus::OPEN();
            case GRPCSubscriptionCycleStatus::Paid:
                return SubscriptionCycleStatus::PAID();
            case GRPCSubscriptionCycleStatus::Pending:
                return SubscriptionCycleStatus::PENDING();
            case GRPCSubscriptionCycleStatus::PastDue:
                return SubscriptionCycleStatus::PAST_DUE();
            case GRPCSubscriptionCycleStatus::Unpaid:
                return SubscriptionCycleStatus::UNPAID();
            case GRPCSubscriptionCycleStatus::Forgiven:
                return SubscriptionCycleStatus::FORGIVEN();
            default:
                throw new Exception(sprintf('Unable to parse subscription cycle status [%s]', $status));
        }
    }
}
