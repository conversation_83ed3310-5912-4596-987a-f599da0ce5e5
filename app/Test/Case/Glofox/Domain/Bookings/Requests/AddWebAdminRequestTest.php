<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\Bookings\Requests;

use App;
use Glofox\Domain\Bookings\Requests\AddWebAdminRequest;
use GlofoxTestCase;

App::import('Test/Case', 'GlofoxTestCase');
class AddWebAdminRequestTest extends GlofoxTestCase
{

    /**
     * @test
     * @dataProvider chargeDataProvider
     * @param $input
     * @param $expected
     */
    public function charge($input, $expected): void
    {
        $request = new AddWebAdminRequest($input);
        self::assertSame($expected, $request->charge());
    }

    public function chargeDataProvider(): array
    {
        return [
            '1. When charge is null Then return true' => [['charge' => null], true],
            '2. When charge is boolean true Then return true' => [['charge' => true], true],
            '3. When charge is boolean false Then return false' => [['charge' => false], false],
            '4. When charge is string "true" Then return true' => [['charge' => 'true'], true],
            '4. When charge is string "false" Then return true' => [['charge' => 'false'], true],
            '5. When charge is string Then return true' => [['charge' => 'abcd'], true],
            '6. When charge is integer 1 Then return true' => [['charge' => 1], true],
            '7. When charge is integer 0 Then return true' => [['charge' => 0], true],
        ];
    }
}