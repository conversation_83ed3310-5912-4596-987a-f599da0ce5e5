<?php

App::uses('Folder', 'Utility');
App::uses('File', 'Utility');
App::uses('AppController', 'Controller');

use Glofox\CdnProvider;
use Glofox\Datasource\Exceptions\InvalidMongoIdException;
use Glofox\Domain\AsyncEvents\Events\ProgramUpdatedEventMeta;
use Glofox\Domain\AsyncEvents\Events\ProgramUpdatedEventPayload;
use Glofox\Domain\Authentication\Auth;
use Glofox\Domain\Bookings\Validation\BookingRootValidator;
use Glofox\Domain\Plans\Exceptions\PlanNotFoundException;
use Glofox\Domain\Programs\Calendar\ProgramCalendarGenerator;
use Glofox\Domain\Programs\Models\Program as ProgramModel;
use Glofox\Domain\Programs\Requests\GetSlotsRequest;
use Glofox\Domain\Programs\Requests\UpsertProgramRequest;
use Glofox\Domain\Programs\Services\ProgramsPublisher;
use Glofox\Domain\Programs\Services\ProgramUpsertService;
use Glofox\Domain\Programs\Transformers\AddVirtualOnlineFieldTransformer;
use Glofox\Domain\Users\Models\User as UserModel;
use Illuminate\Http\JsonResponse;

/**
 * ProgramsController class.
 *
 * @uses          AppController
 *
 * @property Branch $Branch
 * @property Event $Event
 * @property Program $Program
 * @property User $User
 * @property Facility $Facility
 * @property Booking $Booking
 * @property Membership $Membership
 * @property StripeCharge $StripeCharge
 * @property JWTComponent $JWT
 */
class ProgramsController extends \Glofox\Domain\Programs\Http\ProgramsController
{
    /**
     * @var
     */
    public $feature_name;

    /**
     * @var
     */
    public $image_default_name;

    /**
     * @var array
     */
    public $components = [
        'S3',
        'Utility',
        'Notification',
        'JWT',
    ];

    /**
     * @var string
     */
    public $name = 'Programs';

    /**
     * @var array
     */
    public $uses = [
        'Branch',
        'Event',
        'Program',
        'User',
        'Facility',
        'Booking',
        'Membership',
        'StripeCharge',
    ];

    /** @var ProgramsPublisher */
    private $programsPublisher;

    public function __construct($request = null, $response = null)
    {
        parent::__construct($request, $response);

        $this->programsPublisher = app()->make(ProgramsPublisher::class);
    }

    public function beforeFilter()
    {
        parent::beforeFilter();
        $this->feature_name = 'programs';
        $this->image_default_name = 'default.png';
    }

    /**
     * Dispatcher for Model.
     *
     * @param string $identifier [description]
     *
     * @return string
     */
    public function dispatcher($identifier = null)
    {
        return parent::dispatch($identifier, $this->Program);
    }

    public function view($id): JsonResponse
    {
        $user = $this->JWT->parseToken();
        $params = ['branch_id' => $user['branch_id']];

        if ($id) {
            $params['_id'] = $id;
        }

        $results = $this->Program->find('all', [
            'fields' => [
                'schedule_default._trainers.password' => 0
            ],
            'conditions' => array_merge($params, [
                'active' => true
            ])
        ]);

        $programs = $this->sanitisePrograms($results);

        return response()->json($programs);
    }

    /*!
     * Search list with params and pagination
     * @param      [type]                   $start  [description]
     * @param      [type]                   $limit  [description]
     * @param      [type]                   $search [description]
     * @return     [type]                           [description]
     */

    /**
     * @param int $page
     * @param int $limit
     * @param null $search
     * @param string $active
     *
     * @return JsonResponse
     */
    public function listview($page = 1, $limit = 30, $search = null, $active = '1')
    {
        //Force return json
        $this->response->type('json');
        $this->autoRender = false;
        //Obtain data
        $user = $this->JWT->parseToken();
        $active = '0' == $active ? false : true;
        $params = ['branch_id' => $user['branch_id'], 'active' => $active];
        $this->encodeSearchRegex($params, $search, $this->Program); //<- add to params built Regex for global search
        $page = $page > 0 ? (int)$page : 1;
        $limit = $limit > 0 ? (int)$limit : 200;
        $result = $this->Program->find(
            'all',
            [
                'conditions' => $params,
                'limit' => $limit,
                'page' => $page,
                'order' => ['name' => 1],
            ]
        );

        $transformedResult = $this->sanitisePrograms($result);

        return response()->json($transformedResult);
    }

    /*!
     * Count total records in this collection
     * @return     [type]                   [description]
     */

    /**
     * @param string $active
     *
     * @return JsonResponse
     */
    public function count($active = '1')
    {
        $user = $this->JWT->parseToken();
        $active = '0' == $active ? false : true;
        $params = ['branch_id' => $user['branch_id']];
        $result = $this->Program->find('count', ['conditions' => array_merge($params, ['active' => $active])]);

        return response()->json($result);
    }

    /**
     * Save a program object including schedules and patterns, if object does not have an _id
     * it will add it new, else it will update that current instance, This does not upload images
     * to AWS. You must use the image upload service as a separate request to upload images.
     *
     * @return JsonResponse {Program:object,success:boolean}
     *
     * @throws Exception
     * @internal   Some data validation required since its a complex object. TO_DO
     *
     * @example    https://www.glofoxlogin/programs/upsert Payload: Program object directly
     */
    public function upsert(UpsertProgramRequest $request, ProgramUpsertService $programUpsertService): JsonResponse
    {
        $input = $request->data()->toArray();

        $program = $programUpsertService->execute($input, Auth::user());

        // TODO move this to a Transformer
        $program = $this->Program->convertFieldsToDate($program, ['date_start', 'date_finish'], 'date');

        return response()->json($program);
    }

    /**
     * Delete programs given its _id.
     *
     * Logic delete, setting "active" attribute to false
     *
     * @example    https://www.glofoxlogin/programs/remove/{id}
     *
     * @param string $id Program _id
     *
     * @return JsonResponse
     *
     * @throws Exception
     */
    public function remove($id)
    {
        $this->JWT->parseToken();

        $allowedRoles = [
            UserType::ADMIN,
            UserType::SUPERADMIN,
        ];

        $result = $this->JWT->validate($allowedRoles);

        if (!$result['success']) {
            throw new UnauthorizedException();
        }

        $item = $this->Program->findById($id);

        if (!$item) {
            throw new Exception('Program not found');
        }

        $program = $item['Program'];

        // We'll keep this as it has the logic to delete all events based on the Schedule.
        // This is going to be kept here for consistency.
        $this->Event->deleteFrom($program);

        // And this one will ensure that we have deleted every future event. Including
        // single and manually added events.
        $this->Event->deleteAllFutureEventsFromProgram($program);

        // We delete the schedule, so it isn't considered in future generations. And the
        // client is forced to revisit it after restoring a deleted class.
        $program['schedule'] = [];
        $program['active'] = false;

        $program = $this->Program->save($program);

        $program = ProgramModel::make($program['Program']);

        $branchGympassConfiguration = $program->branch()->features()->gympass();

        $meta = new ProgramUpdatedEventMeta([
            'branchId' => $program->branchId(),
            'gympassGymId' => $branchGympassConfiguration ? $branchGympassConfiguration->getId() : null,
            'gympassProductId' => $branchGympassConfiguration ? $branchGympassConfiguration->getProductId() : null,
            'gympassPassTypeNumber' => $branchGympassConfiguration ? $branchGympassConfiguration->getPassTypeNumber() : null,
            'gympassValidationApiAuthToken' => $branchGympassConfiguration ? $branchGympassConfiguration->getValidationApiTokenAuthToken() : null,
            'gympassClientId' => $branchGympassConfiguration ? $branchGympassConfiguration->getClientId() : null,
            'gympassClientSecret' => $branchGympassConfiguration ? $branchGympassConfiguration->getClientSecret() : null,
        ]);

        $payload = new ProgramUpdatedEventPayload([
            'programId' => $program->id(),
            'name' => $program->name(),
            'description' => $program->description(),
            'active' => $program->isActive(),
            'gympassEnabled' => $program->isGympassEnabled(),
        ]);

        $this->programsPublisher->sendProgramUpdatedEvent($meta, $payload);

        return response()->json(
            $program->toLegacy()
        );
    }

    /*********************************** NEW DASHBOARD UTILS *******************************************/

    /****************************** REST WEBSERVICE ACTIONS  *******************************************/

    /**
     * Get all programs for a given branch /branch/<branch_id>/programs/<filter>.
     *
     * @param type $id
     * @param null $user_id
     * @param bool $fetch_images
     * @param bool $fetch_credits
     *
     * @return string
     */
    public function findByBranchId($id = null, $user_id = null, $fetch_images = false, $fetch_credits = false)
    {
        $this->response->type('json');
        $this->autoRender = false;

        $params = [
            'conditions' => ['branch_id' => $id, 'active' => true],
            'order' => ['name' => 1],
            'limit' => 1000,
            'page' => 1,
        ];
        $results = $this->Program->find('all', $params);

        foreach ($results as $key => &$result) {
            $results[$key]['Program']['categories'] = !empty($results[$key]['Program']['categories'])
                ? $results[$key]['Program']['categories']
                : [];

            // Fetch Images
            $fetch_images = filter_var($fetch_images, FILTER_VALIDATE_BOOLEAN);
            if ($fetch_images) {
                $namespace = $result['Program']['namespace'];
                $branch_id = $result['Program']['branch_id'];
                $feature = 'programs';

                $results[$key]['Program']['image_url'] = $this->S3->getCrudCdnImageUrl(
                    $namespace,
                    $branch_id,
                    $feature,
                    $result['Program']['_id'],
                    $results[$key]['Program']['modified']
                );
            }

            // Fetch Price
            if ($user_id) {
                $results[$key] = $this->findProgramPrice($result, $user_id);

                // Fetch Credits
                $fetch_credits = filter_var($fetch_credits, FILTER_VALIDATE_BOOLEAN);
                if ($fetch_credits) {
                    $this->loadModel('UserCredit');
                    $branch_id = $id;
                    $model = $this->Program->useTable;
                    $model_id = $results[$key]['Program']['_id'];
                    $categories = $results[$key]['Program']['categories'];
                    $return_model = false;
                    $credits = $this->UserCredit->findAllCredits(
                        $branch_id,
                        $user_id,
                        $model,
                        $model_id,
                        $categories,
                        $return_model
                    );
                    $results[$key]['Program']['credits'] = $credits ?: [];
                }
            }
        }

        return response()->json($results);
    }

    /**
     * Find one by program id and branch id /branch/<branch_id>/program/<program_id>.
     *
     * @param null $branch_id
     * @param null $program_id
     * @param null $user_id
     *
     * @return type
     *
     * @internal param type $id
     * @internal param type $id_prog
     */
    public function findByBranchIdAndProgramId($branch_id = null, $program_id = null, $user_id = null)
    {
        $this->response->type('json');
        $this->autoRender = false;

        $is_valid_mongoid = $this->Utility->is_valid_mongoid($branch_id);
        if (empty($is_valid_mongoid)) {
            return response()->json(['success' => false, 'message' => __('Invalid Branch Id')]);
        }

        $is_valid_mongoid = $this->Utility->is_valid_mongoid($program_id);
        if (empty($is_valid_mongoid)) {
            return response()->json(['success' => false, 'message' => __('Invalid Program Id')]);
        }

        $program = $this->Program->findByIdAndBranchId($program_id, $branch_id);

        if ($user_id) {
            $program = $this->findProgramPrice($program, $user_id);
        }

        return response()->json(['success' => true, 'Program' => $program['Program']]);
    }

    /**
     * Logic delete method /branch/<branch_id>/program/remove/<program_id>.
     *
     * @param mixed $id null
     */
    public function delete($id)
    {
        return $this->remove($id);
    }

    /**
     * @return string
     *
     * @throws Exception
     */
    public function uploads(
        Glofox\Request $request
    ) {
        $user = Auth::user();
        $fileKey = 'programsImage';
        $feature = 'programs';

        if (!$request->hasFile($fileKey)) {
            throw new UnsuccessfulOperation('THERE_IS_NO_FILE_TO_UPLOAD');
        }

        $saveImage = $this->S3->saveCrudImage(
            $_FILES[$fileKey],
            $user->namespace(),
            $user->currentBranchId(),
            $feature
        );

        if (!$saveImage['success']) {
            throw new UnsuccessfulOperation($saveImage['message']);
        }

        $fileUrl = $this->S3->getCrudImageUrl($user->namespace(), $user->currentBranchId(), $feature);
        $cdnUrl = str_replace('https://glofox.s3.amazonaws.com/', \sprintf('%s/', CdnProvider::getUrl()), $fileUrl);

        return response()->json([
            'success' => true,
            'url' => $cdnUrl,
        ]);
    }

    /**
     * @throws PlanNotFoundException
     * @throws InvalidMongoIdException
     */
    public function getSlots(
        GetSlotsRequest $request,
        BookingRootValidator $validator,
        User $user,
        ProgramCalendarGenerator $calendarGenerator
    ) {
        $branch = Auth::branch();
        $user = UserModel::make($user->findById($request->userId())['User']);
        $cakeProgram = $this->Program->getByIdAndBranchId($request->programId(), $branch->id());
        $program = ProgramModel::make($cakeProgram['Program']);
        $slots = $calendarGenerator
            ->forModel($program)
            ->forUser($user)
            ->withValidator($validator)
            ->withSchedules($request->scheduleCodes())
            ->generate($branch, $request->startTime(), $request->endTime());

        return json_encode($slots, JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    /**
     * Given a program and an user, will calculate the program price and
     * set the attributes default_price and is_allowed to the program.
     *
     * @param $program_id
     * @param [type] $program_id [description]
     *
     * @return array [type]             [description]
     */
    private function findProgramPrice($program_id, $user_id = null)
    {
        $program = is_array($program_id) ? $program_id : $this->Program->findById($program_id);
        $user = is_array($user_id) ? $user_id : $this->User->findById($user_id);
        $membership_type = $user['User']['membership']['type'] ?? $this->User->get_membership_type('payg');
        $membership_id = $user['User']['membership']['_id'] ?? null;
        $membership_group_id = $user['User']['membership']['membership_group_id'] ?? null;
        $program['Program']['default_price'] ??= null;
        $allowed_member_types = $program['Program']['allowed_member_types'] ?? [];

        $price_and_permission = $this->User->get_price_and_permission(
            $allowed_member_types,
            $membership_type,
            $membership_id,
            $membership_group_id,
            $program['Program']['default_price']
        );

        $program['Program']['default_price'] = $price_and_permission['price'];
        $program['Program']['is_allowed'] = $price_and_permission['is_allowed'];

        if (!$price_and_permission['is_allowed']) {
            $this->loadModel('UserCredit');
            $credits = $this->UserCredit->findAllAvailableByBranchIdAndUserId(
                $program['Program']['branch_id'],
                $user_id
            );
            foreach ($credits as $credit) {
                if (isset($credit['UserCredit']['membership_id'])) {
                    $membership_type = 'member';
                    $membership_id = $credit['UserCredit']['membership_id'];
                    $membership_group_id = null;
                    $programId = $program['Program']['_id'];

                    if (!empty($credit['UserCredit']['model_ids']) && !in_array(
                        $programId,
                        $credit['UserCredit']['model_ids']
                    )) {
                        continue;
                    }

                    $price_and_permission = $this->User->get_price_and_permission(
                        $allowed_member_types,
                        $membership_type,
                        $membership_id,
                        $membership_group_id,
                        $program['Program']['default_price']
                    );

                    if ($price_and_permission['is_allowed']) {
                        $program['Program']['default_price'] = $price_and_permission['price'];
                        $program['Program']['is_allowed'] = $price_and_permission['is_allowed'];
                        break;
                    }
                }
            }
        }

        return $program;
    }

    private function sanitisePrograms($result)
    {
        // For some reason, the freaking passwords of trainers are being stored
        // in the Programs collection and this response is not being filtered
        // Because of it, we need to make this very costy operation
        // to remove the password completely from the response.
        foreach ($result as &$row) {
            if (isset($row['Program']['schedule_default']['_trainers'])) {
                $row['Program']['schedule_default']['_trainers'] =
                    $this->removePasswordFromTrainers($row['Program']['schedule_default']['_trainers'] ?? []);
            }
            $row['Program']['schedule'] = $this->sanitiseSchedules($row['Program']['schedule'] ?? []);
        }
        unset($row);

        $programs = collect($result)->map(fn(array $program) => ProgramModel::make($program['Program']));

        /** @var AddVirtualOnlineFieldTransformer $addVirtualOnlineFieldTransformer */
        $addVirtualOnlineFieldTransformer = app()->make(AddVirtualOnlineFieldTransformer::class);
        $programs = $addVirtualOnlineFieldTransformer->execute($programs);

        return $programs->map(fn(ProgramModel $program) => $program->toLegacy());
    }

    private function removePasswordFromTrainers(array $trainers): array
    {
        return array_map(
            function ($trainer) {
                unset($trainer['password']);

                return $trainer;
            },
            $trainers
        );
    }

    private function sanitiseSchedules(array $schedules): array
    {
        return array_map(
            function ($schedule) {
                $schedule['_trainers'] = $this->removePasswordFromTrainers($schedule['_trainers'] ?? []);

                return $schedule;
            },
            $schedules
        );
    }
}
