<?php

use Glofox\Console\ShellHelper;
use Glofox\Domain\Activities\Repositories\ActivitiesRepository;
use Glofox\Domain\Branches\Repositories\BranchesRepository;
use Glofox\Domain\Charges\Onboarding\Model\ImportedDiscount;
use Glofox\Domain\Charges\Onboarding\Model\ImportedHistoricTransactionModel;
use Glofox\Domain\Charges\Onboarding\Model\ImportedTax;
use Glofox\Domain\Charges\Onboarding\Service\DataMapperService;
use Glofox\Domain\Charges\Onboarding\Usecase\TransactionHistoryHandler;
use Glofox\Domain\Charges\Repositories\ChargesRepository;
use Glofox\Domain\PaymentMethods\Type;
use Glofox\Domain\SalesTaxes\Handlers\PriceBreakdownImportHandler;
use Glofox\Domain\SalesTaxes\Internal\PriceBreakdownImportParameters;
use Glofox\Domain\Users\Repositories\UsersRepository;
use League\Csv\AbstractCsv;
use League\Csv\Reader;
use League\Csv\Writer;
use Psr\Log\LoggerInterface;

App::uses('AmazonS3', 'AmazonS3.Lib');

class TransactionHistoryImportShell extends AppShell
{
    use ShellHelper;

    /**
     * @var string
     */
    private $executionMethod;
    /**
     * @var string
     */
    private $sourceFilePath;
    /**
     * @var bool
     */
    private $isCloud;
    /**
     * @var string
     */
    private $reportPath;
    private ?string $reportFilePath = null;
    /**
     * @var bool
     */
    private $uploadReport;
    /**
     * @var UsersRepository
     */
    private $usersRepository;
    /**
     * @var ChargesRepository
     */
    private $chargesRepository;
    /**
     * @var BranchesRepository
     */
    private $branchesRepository;
    /**
     * @var DataMapperService
     */
    private $dataMapperService;
    /**
     * @var LoggerInterface
     */
    private $logger;
    private array $reportHeaderNameMap = [];
    private ?AbstractCsv $reportCsvWriter = null;
    /**
     * @var Shell
     */
    private $branchId;
    /**
     * @var ActivitiesRepository
     */
    private $activitiesRepository;
    /**
     * @var string
     */
    private $taxMode;
    /** @var PriceBreakdownImportHandler */
    private $salesTaxImportHandler;
    private ?array $taxes = null;
    private ?array $discounts = null;

    public function __construct($stdout = null, $stderr = null, $stdin = null)
    {
        parent::__construct($stdout, $stderr, $stdin);
    }

    public function main()
    {
        try {
            $this->parseParams();
            $this->init();

            $customersFilePath = $this->buildCsvFilePath();
            $sourceFileReader = Reader::createFromPath($customersFilePath, 'r');
            $records = $sourceFileReader->fetchAssoc();
            $records->next();
            $headers = $records->current();

            $this->initPriceBreakdowns(array_keys($headers));

            foreach ($records as $row) {
                if (!is_countable($row) || count($row) < 17) {
                    $this->log('Not enough columns in row');
                    continue;
                }

                if ($row['process_name'] != 'transaction-history') {
                    $this->log('Incorrect process name ' . $row['process_name']);
                    continue;
                }

                try {
                    $historicTransactionModel = $this->buildTransactionHistoryModel($row);

                    /** @var TransactionHistoryHandler $onboardingService */
                    $transactionHistoryHandler = new TransactionHistoryHandler(
                        $this->chargesRepository,
                        $this->activitiesRepository,
                        $this->dataMapperService,
                        $historicTransactionModel,
                        $this->logger,
                        $this->branchId
                    );

                    $result = $transactionHistoryHandler->execute();
                    if ($result->getExecutionStatus() == 'SUCCESS') {
                        $this->savePriceBreakdowns($row, $result);
                    }

                    $this->insertReportEntry($result->toArray());
                } catch (\Glofox\Domain\Branches\Exceptions\BranchNotFoundException $e) {
                    $this->log($e->getMessage());
                    $this->log($e->getTraceAsString());
                    throw $e;
                } catch (\Exception $e) {
                    $this->log($e->getMessage());
                    $this->log($e->getTraceAsString());
                    $row['error'] = $e->getMessage();
                    $this->insertReportEntry($row);
                }
            }

            if ($this->uploadReport) {
                $s3Path = $this->getS3PathForBranchEnvironment($this->branchId);
                $this->uploadReportToS3($this->reportFilePath, $s3Path);
            }
        } catch (\Exception $e) {
            $this->log($e->getMessage());
            $this->log($e->getTraceAsString());
        }
    }

    /**
     * @return ConsoleOptionParser
     */
    public function getOptionParser()
    {
        $parser = parent::getOptionParser();
        $parser->addOption('method', [
            'short' => 'm',
            'help' => 'The method to execute',
            'default' => 'dry-run',
        ]);

        $parser->addOption('branch-id', [
            'short' => 'b',
            'help' => 'The branch to use in this execution',
            'default' => '',
        ]);

        $parser->addOption('source-file', [
            'short' => 's',
            'help' => 'The customers csv file',
            'default' => '',
        ]);

        $parser->addOption('is-cloud', [
            'short' => 'i',
            'help' => 'The source file location',
            'boolean' => true,
        ]);

        $parser->addOption('report-path', [
            'short' => 'r',
            'help' => 'The path to the folder to output csv the report',
            'default' => null,
        ]);

        $parser->addOption('upload-report', [
            'short' => 'u',
            'help' => 'Upload report to s3',
            'boolean' => true,
        ]);
        $parser->addOption('tax-mode', [
            'short' => 'u',
            'help' => 'Studio Tax mode',
            'default' => 'inclusive',
        ]);

        return $parser;
    }

    private function savePriceBreakdowns(array $row, ImportedHistoricTransactionModel $historicTransactionModel): void
    {
        $appliedTaxes = $this->calculateAppliedTaxes($row);
        $appliedDiscounts = $this->calculateAppliedDiscounts($row);
        $this->salesTaxImportHandler->handle(
            new PriceBreakdownImportParameters(
                $this->taxMode,
                $appliedTaxes,
                $row['amount'],
                $historicTransactionModel->getGlofoxId(),
                $historicTransactionModel->getInvoiceId(),
                $this->branchId,
                $appliedDiscounts
            )
        );
    }

    private function initPriceBreakdowns(array $header): void
    {
        $this->taxes = [];
        collect($header)->filter(fn($key) => stripos(str_replace(' ', '', $key), 'tax_') === 0)->map(function ($key) {
            $this->taxes[] = new ImportedTax($key);
        });

        $this->discounts = [];
        collect($header)->filter(fn($key) => stripos(str_replace(' ', '', $key), 'discount_') === 0)->map(
            function ($key) {
                $this->discounts[] = new ImportedDiscount($key);
            }
        );
    }

    private function calculateAppliedTaxes(array $row): array
    {
        $appliedTaxes = [];
        foreach ($this->taxes as $tax) {
            $price = $row[$tax->getKey()];
            if (is_numeric($price)) {
                $tax->setPrice($price);
                $appliedTaxes[] = $tax;
            }
        }

        return $appliedTaxes;
    }

    private function calculateAppliedDiscounts(array $row): array
    {
        $appliedDiscounts = [];
        foreach ($this->discounts as $discount) {
            $amount = $row[$discount->getKey()];
            if (is_numeric($amount)) {
                $discount->setAmount($amount);
                $appliedDiscounts[] = $discount;
            }
        }

        return $appliedDiscounts;
    }

    /**
     * @param $row
     *
     * @return ImportedHistoricTransactionModel
     */
    private function buildTransactionHistoryModel(array $row)
    {
        if (empty($row['payment_method'])) {
            $row['payment_method'] = Type::CARD;
        }

        $accountBalance = $row['account_balance'] ?? 0;
        $salesAttributionName = $row['sales_attribution_name'] ?? null;

        return new ImportedHistoricTransactionModel(
            $row['process_name'],
            $row['external_id'],
            $row['transaction_id'],
            $row['club_number'],
            $row['customer_id'],
            $row['payment_method'],
            $row['status'],
            $row['currency'],
            $row['transaction_date'],
            $row['transaction_type'],
            $row['reference_code'],
            $row['item_id'],
            $row['item_type'],
            $row['item_category'],
            $row['item_name'],
            $row['adjusted_item_name'],
            $row['amount'],
            $row['sales_attribution_user_id'],
            $salesAttributionName,
            $accountBalance
        );
    }

    private function buildCsvFilePath(): string
    {
        $sourceFilePath = $this->sourceFilePath;
        if ($this->isCloud) {
            $sourceFilePath = $this->getS3DataSourceFile($this->sourceFilePath);
        }

        return $sourceFilePath;
    }

    /**
     * @throws Exception
     */
    private function parseParams(): void
    {
        $this->branchId = $this->param('branch-id');
        if ('' == $this->branchId) {
            throw new Exception('The branch id is missing');
        }

        $this->sourceFilePath = $this->param('source-file');
        if ('' == $this->sourceFilePath) {
            throw new Exception('The import csv file is missing');
        }

        $this->executionMethod = $this->param('method');
        if ('dry-run' != $this->executionMethod && 'execute' != $this->executionMethod) {
            $this->executionMethod = 'dry-run';
        }

        $this->isCloud = $this->params['is-cloud'];

        $this->reportPath = $this->param('report-path');
        if ('' == $this->reportPath) {
            $this->reportPath = LOGS;
        }

        $this->reportFilePath = sprintf(
            '%s/transaction-history-import-%s.csv',
            rtrim($this->reportPath, '/'),
            uniqid('', false)
        );

        $this->log('Report path is: ' . $this->reportFilePath, LOG_INFO);

        $this->uploadReport = $this->params['upload-report'];

        $this->taxMode = $this->params['tax-mode'];
    }

    private function init(): void
    {
        $this->usersRepository = app()->make(UsersRepository::class);
        $this->chargesRepository = app()->make(ChargesRepository::class);
        $this->branchesRepository = app()->make(BranchesRepository::class);
        $this->logger = app()->make(LoggerInterface::class);
        $this->dataMapperService = app()->make(DataMapperService::class);
        $this->activitiesRepository = app()->make(ActivitiesRepository::class);
        $this->salesTaxImportHandler = app()->make(PriceBreakdownImportHandler::class);

        $reportHeaders = $this->buildReportHeaders();
        foreach ($reportHeaders as $header) {
            $this->reportHeaderNameMap[$header] = 'n/a';
        }

        $this->reportCsvWriter = $this->buildReportWriter();
    }

    private function buildReportWriter(): AbstractCsv
    {
        $reportCsvWriter = Writer::createFromPath($this->reportFilePath, 'w+');
        $reportCsvWriter->insertOne(array_keys($this->reportHeaderNameMap));

        return $reportCsvWriter;
    }

    /**
     * @param $reportCsvWriter
     * @param ImportedHistoricTransactionModel $result
     */
    private function insertReportEntry(array $result): void
    {
        $entry = [];
        foreach ($this->reportHeaderNameMap as $key => $defaultValue) {
            $entry[$key] = $result[$key] ?? $defaultValue;
        }

        $this->reportCsvWriter->insertOne($entry);
    }

    /**
     * @return string[]
     */
    private function buildReportHeaders(): array
    {
        return [
            'process_name',
            'id',
            'branch_id',
            'user_id',
            'status',
            'currency',
            'external_id',
            'transaction_id',
            'club_number',
            'customer_id',
            'transaction_date',
            'transaction_type',
            'reference_code',
            'item_id',
            'item_type',
            'item_category',
            'item_name',
            'adjusted_item_name',
            'amount',
            'payment_method',
            'account_balance',
            'sales_attribution_user_id',
            'sales_attribution_name',
            'dry_run_status',
            'execution_status',
            'error',
        ];
    }
}
