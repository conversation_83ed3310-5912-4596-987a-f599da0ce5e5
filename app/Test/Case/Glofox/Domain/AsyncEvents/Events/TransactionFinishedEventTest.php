<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\AsyncEvents\Events;

use Glofox\Domain\AsyncEvents\Events\TransactionFinishedEvent;
use Glofox\Domain\AsyncEvents\Events\TransactionFinishedEventMeta;
use Glofox\Domain\AsyncEvents\Events\TransactionFinishedEventPayload;

\App::import('Test/Case', 'GlofoxTestCase');

class TransactionFinishedEventTest extends \GlofoxTestCase
{
    public $fixtures = [];

    public function test_it_should_serialize_to_json(): void
    {
        $payload = \Mockery::mock(TransactionFinishedEventPayload::class);

        $payload
            ->shouldReceive('jsonSerialize')
            ->andReturn(['stripe_charge' => ['id' => 'foo']]);

        $meta = new TransactionFinishedEventMeta(['_id' => 'bar']);
        $event = new TransactionFinishedEvent($meta, $payload);

        $json = json_encode($event, JSON_THROW_ON_ERROR);
        $this->assertTextContains('"type":"TRANSACTION_FINISHED"', $json);
        $this->assertTextContains('"correlation":{"_id":"bar"}', $json);
        $this->assertTextContains('"payload":{"stripe_charge":{"id":"foo"}}', $json);
    }
}
