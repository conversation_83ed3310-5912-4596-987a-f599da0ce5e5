get:
  tags:
    - BookingSpots
  summary: ''
  operationId: list-booking-spots
  description: This endpoint lists booking spots for a given event
  x-internal: true
  security:
    - coreapi_auth: [ ]
  parameters:
    - schema:
        type: string
      name: branchId
      in: path
      required: true
    - schema:
        type: string
      name: eventId
      in: path
      required: true
    - schema:
        type: string
      name: user_id
      in: query
      required: false
      description: When provided, it will return only spots reserved by the given user
  responses:
    '200':
      description: OK
      content:
        application/json:
          schema:
            type: object
            properties:
              has_more:
                type: boolean
              data:
                type: array
                items:
                  $ref: "../../schemas/booking_spot.yml"
    '400':
      description: Bad request
      content:
        application/json:
          schema:
            $ref : "../../schemas/error.yml"
post:
  tags:
    - BookingSpots
  summary: ''
  operationId: reserve-booking-spot
  description: This endpoint reserves a spot for a given event and booking
  x-internal: true
  security:
    - coreapi_auth: [ ]
  parameters:
    - schema:
        type: string
      name: branchId
      in: path
      required: true
    - schema:
        type: string
      name: eventId
      in: path
      required: true
  requestBody:
    required: true
    content:
      application/json:
        schema:
          type: object
          properties:
            booking_id:
              type: string
            spot_id:
              type: string
          required:
            - booking_id
            - spot_id
  responses:
    '201':
      description: OK
    '400':
      description: Bad request
      content:
        application/json:
          schema:
            $ref: "../../schemas/error.yml"