<?php

namespace CakeTestCases\Glofox\Domain\BookingRequests\Repositories;

use Glofox\Domain\BookingRequests\Models\BookingRequestType;
use Glofox\Domain\BookingRequests\Repositories\BookingRequestsCache;
use Glofox\Domain\Bookings\Models\Booking;
use Glofox\Domain\Bookings\Status;
use Glofox\Infrastructure\Cache\PredisClientFactory;
use Predis\Client;

\App::import('Test/Case', 'GlofoxTestCase');

class BookingRequestsCacheTest extends \GlofoxTestCase
{
    public $fixtures = [];
    /** @var PredisClientFactory */
    private $predisClientFactory;

    public function setUp()
    {
        parent::setUp();

        $this->predisClientFactory = app()->make(PredisClientFactory::class);
    }

    public function tearDown()
    {
        parent::tearDown();
        \Mockery::close();
    }

    public function test_it_removes_cache_by_event_ids()
    {
        $redisClient = $this->predisClientFactory->createFromEnvironment();
        $redisClient->set('123', '123');
        $redisClient->set('234', '234');

        self::assertNotNull($redisClient->get('123'));
        self::assertNotNull($redisClient->get('234'));

        $cache = new BookingRequestsCache($redisClient);
        $cache->removeByEventIds(['123', '234']);

        self::assertNull($redisClient->get('123'));
        self::assertNull($redisClient->get('234'));
    }

    public function test_it_counts_cache_by_event_id()
    {
        $redisClient = $this->predisClientFactory->createFromEnvironment();
        $redisClient->zadd('ABC-BOOKING', 1, 'mockedUserId1');
        $redisClient->zadd('ABC-BOOKING', 2, 'mockedUserId2');

        $cache = new BookingRequestsCache($redisClient);
        $count = $cache->countByEventId('ABC', BookingRequestType::BOOKING());

        self::assertEquals(2, $count);
    }

    public function test_it_saves_cache()
    {
        $redisClient = $this->predisClientFactory->createFromEnvironment();

        $cache = new BookingRequestsCache($redisClient);

        $booking = new Booking([
            'event_id' => '999',
            'created' => '2019-01-01',
            'user_id' => '234',
            'status' => Status::BOOKED,
        ]);

        $cache->save($booking, time() + 60);

        $count = $redisClient->zcount('999-BOOKING', '-inf', '+inf');
        self::assertEquals(1, $count);
    }

    public function test_it_saves_booking_with_guests()
    {
        $redisClient = $this->predisClientFactory->createFromEnvironment();

        $cache = new BookingRequestsCache($redisClient);

        $booking = new Booking([
            'event_id' => '888',
            'created' => '2019-01-01',
            'user_id' => '234',
            'guest_bookings' => 1,
            'status' => Status::BOOKED,
        ]);
        $cache->save($booking, time() + 60);

        $count = $redisClient->zcount('888-BOOKING', '-inf', '+inf');
        self::assertEquals(2, $count);
    }

    public function test_it_should_remove_cache_by_event_ids()
    {
        $redisClient = $this->getMock(Client::class, ['del']);
        $redisClient
            ->expects($this->exactly(2))
            ->method('del');

        $cache = new BookingRequestsCache($redisClient);

        $cache->removeByEventIds(['123', '234']);
    }

    public function test_it_should_count_cache_by_event_id()
    {
        $redisClient = $this->getMock(Client::class, ['zcount']);
        $redisClient
            ->expects($this->once())
            ->method('zcount')
            ->with('123-BOOKING', '-inf', '+inf');

        $cache = new BookingRequestsCache($redisClient);

        $cache->countByEventId('123', BookingRequestType::BOOKING());
    }

    public function test_it_should_save_booking()
    {
        $redisClient = $this->getMock(Client::class, ['zadd', 'expireat']);
        $redisClient
            ->expects($this->once())
            ->method('zadd')
            ->with('123-BOOKING', 1_546_300_800, '234');
        $redisClient
            ->expects($this->once())
            ->method('expireat')
            ->with('123-BOOKING', 123);

        $cache = new BookingRequestsCache($redisClient);

        $booking = new Booking([
            'event_id' => '123',
            'created' => '2019-01-01',
            'user_id' => '234',
            'status' => Status::BOOKED,
        ]);
        $cache->save($booking, 123);
    }

    public function test_it_should_save_booking_with_guest_bookings()
    {
        $redisClient = $this->getMock(Client::class, ['zadd', 'expireat']);
        $redisClient
            ->expects($this->exactly(2))
            ->method('zadd');
        $redisClient
            ->expects($this->once())
            ->method('expireat')
            ->with('123-BOOKING', 123);

        $cache = new BookingRequestsCache($redisClient);

        $booking = new Booking([
            'event_id' => '123',
            'created' => '2019-01-01',
            'user_id' => '234',
            'guest_bookings' => 1,
            'status' => Status::BOOKED,
        ]);
        $cache->save($booking, 123);
    }

    /**
     * @description Test whether the injections are working as expected
     */
    public function test_it_sets_and_gets_from_cache(): void
    {
        /** @var BookingRequestsCache $instance */
        $instance = app()->make(BookingRequestsCache::class);
        self::assertInstanceOf(BookingRequestsCache::class, $instance);
    }
}
