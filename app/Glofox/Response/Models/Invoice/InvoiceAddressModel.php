<?php

namespace Glofox\Response\Models\Invoice;

use Glofox\Response\BaseModel;

/**
 * @SuppressWarnings(PHPMD)
 */
class InvoiceAddressModel extends BaseModel
{
    /**
     * @var string
     */
    public $owner = "";
    /**
     * @var string
     */
    public $name = "";
    /**
     * @var string
     */
    public $address_first = "";
    /**
     * @var string
     */
    public $address_second = "";
    /**
     * @var string
     */
    public $address_third = "";

    protected array $allowedFields = [
        'owner',
        'name',
        'address_first',
        'address_second',
        'address_third'
    ];

    public function __construct(array $data)
    {
        $this->setModelFields($data);
    }
}
