<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Date;

use Carbon\Carbon;
use Glofox\Date\IntervalType;
use Glofox\Date\NoOverflowIntervalTransformer;
use Glofox\Domain\FeatureFlags\FeatureFlagInterface;
use Glofox\Infrastructure\Flags\Flag;
use Mockery;

\App::import('Test/Case', 'GlofoxTestCase');

class NoOverflowIntervalApplierTest extends \GlofoxTestCase
{
    public $fixtures = [];

    private const TEST_BRANCH_ID = 'test-branch-id';

    public function setUp(): void
    {
        parent::setUp();

        app()->instance(FeatureFlagInterface::class, $this->getFeatureFlagInterfaceMock());
    }

    public function tearDown(): void
    {
        parent::tearDown();

        app()->forgetInstance(FeatureFlagInterface::class);

        Mockery::close();
    }

    public function testFFNotCheckedIfBranchIsNotProvided(): void
    {
        $flagger = Mockery::mock(FeatureFlagInterface::class);
        $flagger
            ->shouldReceive('withFlag')->with(Flag::IS_NO_MONTH_OVERFLOW_ENABLED())->andReturnSelf()
            ->getMock();
        $flagger->shouldNotReceive('hasByBranchId');

        app()->instance(FeatureFlagInterface::class, $flagger);

        NoOverflowIntervalTransformer::addInterval(
            Carbon::now(),
            1,
            IntervalType::MONTH_INTERVAL()
        );

        app()->forgetInstance(FeatureFlagInterface::class);
    }

    public function testFFIsCheckedIfBranchIsProvided(): void
    {
        $flagger = Mockery::mock(FeatureFlagInterface::class);
        $flagger
            ->shouldReceive('withFlag')->with(Flag::IS_NO_MONTH_OVERFLOW_ENABLED())->andReturnSelf()
            ->getMock();
        $flagger->shouldReceive('hasByBranchId')->with(self::TEST_BRANCH_ID)->andReturn(true);

        app()->instance(FeatureFlagInterface::class, $flagger);

        NoOverflowIntervalTransformer::addInterval(
            Carbon::now(),
            1,
            IntervalType::MONTH_INTERVAL(),
            self::TEST_BRANCH_ID
        );

        app()->forgetInstance(FeatureFlagInterface::class);
    }

    public function monthsNoOverflowDataProvider(): array
    {
        return [
            'As 31st of January and leap year, Given 1 month in advance, Then expected date should be the last date of February' => [
                'date' => '2024-01-31',
                'monthCount' => 1,
                'expectedDate' => '2024-02-29'
            ],
            'As 31st of January, Given 2 months in advance, Then expected date should be the last date of March' => [
                'date' => '2024-01-31',
                'monthCount' => 2,
                'expectedDate' => '2024-03-31'
            ],
            'As 31st of August, Given 1 month in advance, Then expected date is last date of September' => [
                'date' => '2024-08-31',
                'monthCount' => 1,
                'expectedDate' => '2024-09-30'
            ],
            'As 30th of September, Given 1 month in advance, Then expected date should be the 30th of October' => [
                'date' => '2024-09-30',
                'monthCount' => 1,
                'expectedDate' => '2024-10-30'
            ],
            'As 31st of July, Given 1 month in advance, Then expected date should be the 31st of August' => [
                'date' => '2024-07-31',
                'monthCount' => 1,
                'expectedDate' => '2024-08-31'
            ],
            'As 28th of February, Given 1 month in advance, Then expected date should be the 28th of March' => [
                'date' => '2024-02-28',
                'monthCount' => 1,
                'expectedDate' => '2024-03-28'
            ],
            'As 30th of December, Given 2 months in advance, Then expected date should be the 28th of February' => [
                'date' => '2024-12-30',
                'monthCount' => 2,
                'expectedDate' => '2025-02-28'
            ],
            'As 30th of December in a leap year, Given 2 months in advance, Then expected date should be the 29th of February' => [
                'date' => '2027-12-30',
                'monthCount' => 2,
                'expectedDate' => '2028-02-29'
            ],
        ];
    }

    /**
     * @dataProvider monthsNoOverflowDataProvider
     */
    public function testAddMonthsNoOverflow(string $date, int $monthsCount, string $expectedDate): void
    {
        $date = Carbon::parse($date);

        $result = NoOverflowIntervalTransformer::addInterval(
            $date,
            $monthsCount,
            IntervalType::MONTH_INTERVAL(),
            self::TEST_BRANCH_ID
        );
        $this->assertEquals($expectedDate, $result->toDateString());
    }

    public function monthOverflowDataProvider(): array
    {
        return [
            'As 30th of January, Given one month in advance and FF is disabled, Then expected date is 1st of March' => [
                'date' => '2024-01-30',
                'expectedDate' => '2024-03-01',
                'isFFEnabled' => false
            ],
            'As 30th of January, Given one month in advance and FF is enabled, Then expected date is 29th of February' => [
                'date' => '2024-01-30',
                'expectedDate' => '2024-02-29',
                'isFFEnabled' => true
            ],
            'As 31st of January, Given one month in advance and FF is disabled, Then expected date is 2nd of March' => [
                'date' => '2024-01-31',
                'expectedDate' => '2024-03-02',
                'isFFEnabled' => false
            ],
            'As 31st of January, Given one month in advance and FF is enabled, Then expected date is 29th of February' => [
                'date' => '2024-01-31',
                'expectedDate' => '2024-02-29',
                'isFFEnabled' => true
            ],
        ];
    }

    /**
     * @dataProvider monthOverflowDataProvider
     */
    public function testNoMonthOverflowIsControlledWithFeatureFlag(
        string $startDate,
        string $expectedEndDate,
        bool $isFFEnabled
    ): void {
        app()->instance(FeatureFlagInterface::class, $this->getFeatureFlagInterfaceMock($isFFEnabled));

        $date = Carbon::parse($startDate);

        $result = NoOverflowIntervalTransformer::addInterval(
            $date,
            1,
            IntervalType::MONTH_INTERVAL(),
            self::TEST_BRANCH_ID
        );
        $this->assertEquals($expectedEndDate, $result->toDateString());

        app()->forgetInstance(FeatureFlagInterface::class);
    }

    public function yearsNoOverflowDataProvider(): array
    {
        return [
            'As 29th of February, Given 1 year in advance, Then expected date should be the 28th of February' => [
                'date' => '2024-02-29',
                'yearsCount' => 1,
                'expectedDate' => '2025-02-28'
            ],
            'As 29th of February, Given 4 years in advance, Then expected date should be the 29th of February' => [
                'date' => '2024-02-29',
                'yearsCount' => 4,
                'expectedDate' => '2028-02-29'
            ],
        ];
    }

    /**
     * @dataProvider yearsNoOverflowDataProvider
     */
    public function testAddYearsNoOverflow(string $date, int $yearsCount, string $expectedDate): void
    {
        $date = Carbon::parse($date);

        $result = NoOverflowIntervalTransformer::addInterval(
            $date,
            $yearsCount,
            IntervalType::YEAR_INTERVAL(),
            self::TEST_BRANCH_ID
        );
        $this->assertEquals($expectedDate, $result->toDateString());
    }

    public function testAddDays(): void
    {
        $date = Carbon::parse('2024-01-31');
        $result = NoOverflowIntervalTransformer::addInterval(
            $date,
            10,
            IntervalType::DAY_INTERVAL(),
            self::TEST_BRANCH_ID
        );
        $this->assertEquals('2024-02-10', $result->toDateString());
    }

    public function testAddWeeks(): void
    {
        $date = Carbon::parse('2024-01-31');
        $result = NoOverflowIntervalTransformer::addInterval(
            $date,
            2,
            IntervalType::WEEK_INTERVAL(),
            self::TEST_BRANCH_ID
        );
        $this->assertEquals('2024-02-14', $result->toDateString());
    }

    private function getFeatureFlagInterfaceMock(bool $shouldBeEnabled = true): FeatureFlagInterface
    {
        $flagger = Mockery::mock(FeatureFlagInterface::class);
        $flagger
            ->shouldReceive('withFlag')->with(Flag::IS_NO_MONTH_OVERFLOW_ENABLED())->andReturnSelf()
            ->getMock();
        $flagger->shouldReceive('hasByBranchId')->andReturn($shouldBeEnabled);

        return $flagger;
    }
}
