//UPDATE - To delete users date of birth, see STAF-1813 (https://glofox.atlassian.net/browse/STAF-1813) for details

const userEmailToUpdate = [
    "<EMAIL>",
];

const modified = new Date();
let documentsUpdated = 0;

userEmailToUpdate.forEach(userEmail => {
    const result = db.users.updateOne(
        {
            email: userEmail,
            namespace: "namespace",
            type: 'MEMBER'
        },
        {
            $unset: {
                birth: ""
            },
            $set: {
                modified: modified
            }
        }
    );

    if (result.modifiedCount > 0) {
        documentsUpdated++;
    }
});

print(`Total Documents Updated: ${documentsUpdated}`);
