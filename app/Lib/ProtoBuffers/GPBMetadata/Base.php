<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: base.proto

namespace GPBMetadata;

class Base
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(
            "\x0A\xC5\x02\x0A\x0Abase.proto\x12\x04grpc\"l\x0A\x0ARangeQuery\x12\x13\x0A\x0BGreaterThan\x18\x01 \x01(\x03\x12\x1A\x0A\x12GreaterThanOrEqual\x18\x02 \x01(\x03\x12\x12\x0A\x0ALesserThan\x18\x03 \x01(\x03\x12\x19\x0A\x11LesserThanOrEqual\x18\x04 \x01(\x03\"(\x0A\x0AQueryScope\x12\x1A\x0A\x12IncludeSoftDeletes\x18\x01 \x01(\x08B\x90\x01\x0AAcom.glofox.payments.adapter.provider.service.gateway.service.baseZ6github.com/glofoxinc/payments/generated_code/base/grpc\xCA\x02\x12GRPC\\Payments\\Baseb\x06proto3"
        , true);

        static::$is_initialized = true;
    }
}

