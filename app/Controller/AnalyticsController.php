<?php

App::uses('Folder', 'Utility');
App::uses('File', 'Utility');
App::uses('AppController', 'Controller');

use Carbon\Carbon;
use Glofox\Datasource\Exceptions\InvalidMongoIdException;
use Glofox\Datasource\MongoCollection as GlofoxMongoCollection;
use Glofox\Domain\Analytics\Events\TransactionReportWasDownloaded;
use Glofox\Domain\Analytics\Http\Requests\MembersAnalyticsRequest;
use Glofox\Domain\Analytics\Http\Requests\RevenueAnalyticsRequest;
use Glofox\Domain\Authentication\Auth;
use Glofox\Domain\Bookings\Status as BookingStatus;
use Glofox\Domain\Branches\Exceptions\BranchNotFoundException;
use Glofox\Domain\Branches\Models\Branch;
use Glofox\Domain\Branches\Repositories\BranchesRepository;
use Glofox\Domain\Charges\Models\Charge;
use Glofox\Domain\Events\Transformers\AddVirtualOnlineFieldTransformer;
use Glofox\Domain\Memberships\Services\AddonServiceInterface;
use Glofox\Domain\Refunds\Status;
use Glofox\Domain\Report\Requests\ActiveCreditPacksBreakdownRequest;
use Glofox\Domain\Report\Requests\ActiveCreditPacksRequest;
use Glofox\Domain\Reports\Requests\AnalyticsBookingsRequest;
use Glofox\Domain\Reports\Requests\AnalyticsCapacityRequest;
use Glofox\Domain\Reports\Requests\AnalyticsEventsRequest;
use Glofox\Domain\Reports\Requests\AnalyticsReportRequest;
use Glofox\Domain\Reports\Requests\ClassPerformanceRequest;
use Glofox\Domain\Reports\Requests\TrainerPerformanceRequest;
use Glofox\Domain\Analytics\Validation\Validators\CanAccessReportValidator;
use Glofox\Domain\Analytics\Exception\CannotAccessReportException;
use Glofox\Domain\Users\Exceptions\UserNotFoundException;
use Glofox\Events\EventManager;
use Glofox\Report\ActiveCreditPacks;
use Glofox\Report\ActiveCreditPacksBreakdown;
use Glofox\Report\ClassPerformance;
use Glofox\Report\Csv\CsvReportFactory;
use Glofox\Report\RevenueReport;
use Glofox\Report\TrainerPerformance;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use League\Csv\Writer;
use RRule\RRule;

/**
 * WebsiteController class.
 *
 * @uses          AppController
 *
 * @property Facility $Facility
 * @property User $User
 * @property Event $Event
 * @property Branch $Branch
 * @property Booking $Booking
 * @property Program $Program
 * @property Membership $Membership
 * @property StripeCharge $StripeCharge
 * @property Refund $Refund
 * @property Category $Category
 * @property Course $Course
 * @property Access $Access
 * @property Client $Client
 * @property Product $Product
 * @property TimeSlot $TimeSlot
 */
class AnalyticsController extends AppController
{
    public $components = [
        'Paginator',
        'Utility',
        'JWT',
        'Notification',
    ];
    public $uses = [
        'Facility',
        'User',
        'Event',
        'Branch',
        'Booking',
        'Program',
        'Membership',
        'StripeCharge',
        'Category',
        'Course',
        'Access',
        'Client',
        'Product',
        'TimeSlot',
        'UserCredit',
        'Refund',
    ];
    private AddonServiceInterface $addonsHttpService;

    public function __construct($request = null, $response = null)
    {
        parent::__construct($request, $response);
        $this->addonsHttpService = app()->make(AddonServiceInterface::class);
    }

    public function activeCreditPacksBreakdown(ActiveCreditPacksBreakdownRequest $request)
    {
        $user = Auth::user();
        $branch = $user->branch();
        $date = $request->getDate();

        $activeCreditPacksBreakdownReport = (new ActiveCreditPacksBreakdown())
            ->withBranch($branch)
            ->withDate($date)
            ->fetchCredits()
            ->fetchUsers()
            ->fetchMemberships()
            ->generate();

        return response()->json($activeCreditPacksBreakdownReport->toArray());
    }

    public function activeCreditPacks(ActiveCreditPacksRequest $request)
    {
        $user = Auth::user();
        $branch = $user->branch();
        $date = $request->getDate();

        $activeCreditPacksReport = (new ActiveCreditPacks())
            ->withBranch($branch)
            ->withDate($date)
            ->fetchCredits()
            ->fetchUsers()
            ->fetchMemberships()
            ->generate();

        return response()->json($activeCreditPacksReport->toArray());
    }

    public function classPerformance(ClassPerformanceRequest $request)
    {
        // Get the branch and user
        $user = $this->getUser();
        $branch = $this->Branch->findById($user['branch_id']);

        // Validate branch and user
        if (empty($user) || empty($branch) || !isset($branch['Branch'])) {
            throw new BadRequestException(__('Invalid user or branch, we could not find branch with _id'));
        }
        $branch = $branch['Branch'];

        // Get and validate the params
        $params = $this->request->query;
        if (empty($params['start']) || empty($params['end'])) {
            throw new BadRequestException(__('Invalid start and end date paramaters'));
        }

        // Get the date params
        $timezone = $this->Branch->getTimeZone($branch['_id']);
        $start = new MongoDate(Carbon::createFromTimestamp($params['start'], $timezone)->getTimestamp());
        $end = new MongoDate(Carbon::createFromTimestamp($params['end'], $timezone)->getTimestamp());

        // Get events (for a trainer or all)
        if (!empty($params['trainer_id'])) {
            $trainerId = $params['trainer_id'];
            $events = $this->Event->findByDateRangeAndTrainerId(
                $branch,
                (int)($params['start']),
                (int)($params['end']),
                $trainerId
            );
        } else {
            $events = $this->Event->findByDateRangeAndTrainerId(
                $branch,
                (int)($params['start']),
                (int)($params['end'])
            );
        }
        if (!isset($events)) {
            throw new BadRequestException('Error fetching events for class performance report');
        }

        // Generate a list of program and event ids
        $eventIds = [];
        $programIds = [];
        foreach ($events as $event) {
            $eventIds[] = $event['Event']['_id'];
            $programIds[] = $event['Event']['program_id'];
        }

        // Get programs
        $programs = $this->Program->findAllByProgramIds($branch, $programIds);
        if (!isset($programs)) {
            throw new BadRequestException('Error fetching programs for class performance report');
        }

        // Get bookings
        $bookings = $this->Booking->findBookedByEventIds($eventIds);
        if (!isset($bookings)) {
            throw new BadRequestException('Error fetching bookings for class performance report');
        }

        // Generate the report
        $classPerformanceReport = (new ClassPerformance())->withPrograms($programs)
            ->withEvents($events)
            ->withBookings($bookings)
            ->generate();

        $result = [
            'success' => true,
            'report' => $classPerformanceReport->toArray(),
        ];

        return response()->json($result);
    }

    public function trainerPerformance(TrainerPerformanceRequest $request)
    {
        // Get the branch and user
        $user = $this->getUser();
        $branch = $this->Branch->findById($user['branch_id']);

        // Validate branch and user
        if (empty($user) || empty($branch) || !isset($branch['Branch'])) {
            throw new BadRequestException(__('Invalid user or branch, we could not find branch with _id'));
        }
        $branch = $branch['Branch'];

        // Get and validate the params
        $params = $this->request->query;
        if (empty($params['start']) || empty($params['end'])) {
            throw new BadRequestException(__('Invalid start and end date paramaters'));
        }

        // Get the timezone offset
        $timezone = $this->Branch->getTimeZone($branch['_id']);
        $start = new MongoDate(Carbon::createFromTimestamp($params['start'], $timezone)->getTimestamp());
        $end = new MongoDate(Carbon::createFromTimestamp($params['end'], $timezone)->getTimestamp());

        // Get trainers
        $trainers = $this->User->getStaffByNamespaceAndBranchId($branch['namespace'], $branch['_id']);
        if (!isset($trainers)) {
            throw new BadRequestException('Error fetching trainers for trainer performance report');
        }

        // Get events
        $events = $this->Event->findByDateRangeAndTrainerId($branch, (int)($params['start']), (int)($params['end']));
        if (!isset($events)) {
            throw new BadRequestException('Error fetching events for trainer performance report');
        }

        // Get timeslots
        $timeslots = $this->TimeSlot->findAllByDateRangeAndModelId(
            $branch['_id'],
            (int)($params['start']),
            (int)($params['end'])
        );
        if (!isset($timeslots)) {
            throw new BadRequestException('Error fetching timeslots for trainer performance report');
        }

        // Get bookings
        $bookings = $this->Booking->findBookedInDateRangeByTypeAndModelId(
            $branch['_id'],
            $params['start'],
            $params['end']
        );
        if (!isset($bookings)) {
            throw new BadRequestException('Error fetching bookings for trainer performance report');
        }

        // Generate the report
        $trainerPerformanceReport = (new TrainerPerformance())->withTrainers($trainers)
            ->withEvents($events)
            ->withTimeslots($timeslots)
            ->withBookings($bookings)
            ->generate();

        $result = [
            'success' => true,
            'report' => $trainerPerformanceReport->toArray(),
        ];

        return response()->json($result);
    }

    public function revenue(RevenueAnalyticsRequest $request)
    {
        $report = (new RevenueReport($this->StripeCharge))
            ->injectModelDependencies([
                'Membership' => $this->Membership,
                'Event' => $this->Event,
                'Course' => $this->Course,
                'Facility' => $this->Facility,
                'User' => $this->User,
                'Product' => $this->Product,
            ])
            ->generate($request);

        return response()->json($report);
    }

    /*!
     * Create the array of totals
     * @param  [type] $list             [description] List of the arrays containing the totals corresponding to the date range
     * @param  [type] $interval         [description] The interval the totals should be grouped by, e.g. day, week, month
     * @param  [type] $start            [description] The start date of the date range
     * @param  [type] $end              [description] The end date of the date range
     * @return [type]                   [description] Array of the totals
     */

    /**
     * @param $list
     *
     * @return array
     */
    public function getTotals(array $list = [], string $interval, DateTime $dateStart, DateTime $dateEnd)
    {
        $difference = $dateStart->diff($dateEnd);

        // Determine what values to compare
        $compare = [
            'day' => false,
            'week' => false,
            'month' => false,
            'year' => false,
        ];

        // Get the count of intervals between the two dates
        switch ($interval) {
            case 'month':
                $increments = ($difference->m + ($difference->y * 12));
                $incrementAmount = 1;
                $incrementType = 'months';
                $compare['year'] = true;
                $compare['month'] = true;
                break;
            case 'week':
                $increments = ($difference->days / 7);
                $incrementAmount = 7;
                $incrementType = 'days';
                $compare['year'] = true;
                $compare['week'] = true;
                break;
            default:
                $increments = $difference->days;
                $incrementAmount = 1;
                $incrementType = 'days';
                $compare['year'] = true;
                $compare['month'] = true;
                $compare['day'] = true;
                break;
        }

        // Set the start and end object for the while loop
        $currentDate = $dateStart;
        //$lastDate = $dateEnd;
        //$lastDateSeconds = strtotime($dateEnd->format('Y-m-d').' + 1 '.$interval.'s');
        //date_timestamp_set($lastDate, $lastDateSeconds);

        // Create the totals array and variable which holds the index of the added totals
        $totals = [];
        $totalsAdded = 0;

        // Go through all of the periods between start and end date and add the total and date
        for ($i = 0; $i <= $increments; ++$i) {
            $isItemSet = isset($list[$totalsAdded]);
            $isItemIdSet = isset($list[$totalsAdded]['_id']);

            $isItemYearSet = isset($list[$totalsAdded]['_id']['year']);
            $isItemMonthSet = isset($list[$totalsAdded]['_id']['month']);
            $isItemWeekSet = isset($list[$totalsAdded]['_id']['week']);
            $isItemDaySet = isset($list[$totalsAdded]['_id']['day']);

            $isSameYear = $isItemYearSet && (int)($currentDate->format('Y')) == $list[$totalsAdded]['_id']['year'];
            $isSameMonth = $isItemMonthSet && (int)($currentDate->format('m')) == $list[$totalsAdded]['_id']['month'];
            $isSameWeek = $isItemWeekSet && (int)($currentDate->format('W')) == $list[$totalsAdded]['_id']['week'];
            $isSameDay = $isItemDaySet && (int)($currentDate->format('d')) == $list[$totalsAdded]['_id']['day'];

            if (
                !$isItemSet
                || !$isItemIdSet
                || ($compare['year'] && !$isSameYear)
                || ($compare['month'] && !$isSameMonth)
                || ($compare['week'] && !$isSameWeek)
                || ($compare['day'] && !$isSameDay)
            ) {
                $totals[] = 0;
            } else {
                $totals[] = $list[$totalsAdded]['total'];
                ++$totalsAdded;
            }

            $timeString = sprintf('+ %s %s', $incrementAmount, $incrementType);
            $currentDate->modify($timeString);
        }

        // Return the totals
        return $totals;
    }

    public function revenueDownload(RevenueAnalyticsRequest $request)
    {
        $this->layout = false;
        $this->response->type('text/csv');
        $this->autoRender = false;
        $user = $this->getUser();

        // Get the params
        $paramsUrl = $this->request->query;

        // Get the date parameter
        $start = new MongoDate($paramsUrl['start']);
        $end = new MongoDate($paramsUrl['end']);

        // Set the types of glofox events to be gotten
        $glofox_events = [
            'subscription_payment',
            'upfront_payment',
            'subscription_prorate',
            'book_class',
            'book_course',
            'book_time_slot',
            'buy_product',
            'custom_charge',
        ];

        // Set the query params
        $params = [
            [
                '$match' => [
                    'metadata.namespace' => $user['namespace'],
                    'metadata.branch_id' => $user['branch_id'],
                    'metadata.glofox_event' => ['$in' => $glofox_events],
                    'paid' => true,
                    '$or' => [
                        ['refunded' => ['$exists' => false]],
                        ['refunded' => false],
                    ],
                    'created' => [
                        '$type' => 9,
                        '$gte' => $start,
                        '$lte' => $end,
                    ],
                ],
            ],
            [
                '$project' => [
                    '_id' => 1,
                    'id' => 1,
                    'created' => 1,
                    'amount' => 1,
                    'paid' => 1,
                    'metadata' => 1,
                ],
            ],
            ['$sort' => ['_id' => 1]],
        ];

        // Do the query
        $mongo = $this->StripeCharge->getDataSource();
        $mongoCollectionObject = $mongo->getMongoCollection($this->StripeCharge);

        $customCollection = new GlofoxMongoCollection($mongoCollectionObject);
        $results = $customCollection->aggregation($params);

        // Macintosh compatibility line endings
        if (!ini_get('auto_detect_line_endings')) {
            ini_set('auto_detect_line_endings', '1');
        }

        // Set up the csv with the header
        $headers = [
            'Date / Time',
            'Receipt number',
            'User name',
            'Item group',
            'Item name',
            'Payment method',
            'Total amount',
            'Payment successful?',
        ];
        $writer = Writer::createFromFileObject(new SplTempFileObject());
        $writer->insertOne($headers);

        foreach ($results as $stripeCharge) {
            // Get the info used to get the id of a stripe charged
            $idParameter = $this->getStripeChargeMetadataId($stripeCharge);
            $entityIdParameter = $this->getStripeChargeMetadataEntityId($stripeCharge);

            // Build and add transaction to the csv
            $transaction = $this->buildTransaction($stripeCharge, $idParameter, $entityIdParameter);
            $writer->insertOne($transaction);
        }

        return $writer;
    }

    /*!
     * Get the stripe charge metadata id
     * @param  [type] $stripeCharge     [description] Stripe charge object
     * @return [type]                   [description] Parameter name
     */

    private function getStripeChargeMetadataId(array $stripeCharge): ?string
    {
        switch ($stripeCharge['metadata']['glofox_event']) {
            case 'subscription_payment':
            case 'upfront_payment':
            case 'subscription_prorate':
                return 'membership_id';
            case 'book_class':
                return 'event_id';
            case 'book_course':
                return 'course_id';
            case 'book_time_slot':
                return 'model_id';
            case 'buy_product':
                return 'product_id';
            default:
                return null;
        }
    }

    private function getStripeChargeMetadataEntityId(array $stripeCharge): ?string
    {
        switch ($stripeCharge['metadata']['glofox_event']) {
            case 'subscription_payment':
            case 'upfront_payment':
            case 'subscription_prorate':
                return 'plan_code';
            case 'book_course':
                return 'session_id';
            case 'buy_product':
                return 'presentation_id';
            case 'book_class':
            case 'book_time_slot':
            default:
                return null;
        }
    }

    /**
     * @param $stripeCharge
     * @param $idParameter
     * @param $entityIdParameter
     *
     * @return array
     */
    private function buildTransaction($stripeCharge, $idParameter, $entityIdParameter)
    {
        $transaction = [
            'date' => '',                   // Column A: Date / Time
            'id' => '',                     // Column B: Receipt Number
            'user_name' => '',              // Column C: User name (i.e. person who made the purchase)
            'name' => '',                   // Column D: Item Group (e.g. Membership, Class, Product etc.)
            'entity_name' => '',            // Column E : Item Name (e.g. Membership renewal, Yoga, Protein etc.)
            'payment_method' => '',         // Column F: Payment method
            'amount' => '',                 // Column G: Total amount (include tax)
            'paid' => false,                 // Column H: Payment Successful?
        ];

        if (null !== $stripeCharge['created']) {
            $dateTime = new DateTime();
            date_timestamp_set($dateTime, $stripeCharge['created']->sec);
            $transaction['date'] = $dateTime->format('Y-m-d H:i:s');
        }
        if (null !== $stripeCharge['_id']) {
            $transaction['id'] = (string)$stripeCharge['_id'];
        }
        if (null !== $stripeCharge['metadata']['user_name']) {
            $transaction['user_name'] = $stripeCharge['metadata']['user_name'];
        }
        if (null !== $stripeCharge['metadata'][$idParameter]) {
            $transaction['name'] = $stripeCharge['metadata'][$idParameter];
        }
        if (null !== $stripeCharge['metadata'][$entityIdParameter]) {
            $transaction['entity_name'] = $stripeCharge['metadata'][$entityIdParameter];
        }
        if (null !== $stripeCharge['metadata']['payment_method']) {
            $transaction['payment_method'] = $stripeCharge['metadata']['payment_method'];
        }
        if (null !== $stripeCharge['amount']) {
            $transaction['amount'] = $stripeCharge['amount'];
        }
        if (null !== $stripeCharge['paid']) {
            $transaction['paid'] = $stripeCharge['paid'];
        }

        return array_values($transaction);
    }

    public function bookings(AnalyticsBookingsRequest $request)
    {
        $this->layout = false;
        $this->response->type('json');
        $this->autoRender = false;

        $user = Auth::user();
        if ($user === null) {
            throw UserNotFoundException::withNullId();
        }

        $branchesRepository = app()->make(BranchesRepository::class);
        $branch = $branchesRepository->getById($user->branchId());

        $by = strtolower($request->by());
        $end = Carbon::tomorrow($branch->timezone())->startOfDay();
        $start = Carbon::today($branch->timezone());

        switch ($by) {
            case PeriodInterval::Month:
                $start = $start->subWeeks(4)->startOfDay();
                $mongoUnit = 'month';
                break;
            case PeriodInterval::Year:
                $start = $start->subYear()->startOfDay();
                $mongoUnit = 'year';
                break;
            case PeriodInterval::Week:
                $start = $start->subWeek()->startOfDay();
                $mongoUnit = 'week';
                break;
            case PeriodInterval::Today:
                $start = $start->startOfDay();
                $mongoUnit = 'day';
                break;
            default:
                throw new BadRequestException(__('Invalid "by" filter, must be week, year or month'));
        }

        $params = [
            [
                '$match' => [
                    'namespace' => $branch->namespace(),
                    'branch_id' => $branch->id(),
                    'status' => BookingStatus::BOOKED,
                    'event_id' => ['$exists' => true],
                    'time_start' => [
                        '$type' => 'date',
                        '$gte' => new MongoDate(Carbon::parse($start->toDateTimeString())->getTimestamp()),
                        '$lte' => new MongoDate(Carbon::parse($end->toDateTimeString())->getTimestamp())
                    ],
                ],
            ],
            [
                '$project' => [
                    'bookingDate' => [
                        '$dateTrunc' => ['date' => '$time_start', 'unit' => $mongoUnit]
                    ],
                    'guest_bookings' => 1,
                ],
            ],
            [
                '$group' => [
                    '_id' => ['bookingDate' => '$bookingDate'],
                    'total' => ['$sum' => 1],
                    'guests' => ['$sum' => '$guest_bookings'],
                ],
            ]
        ];

        $mongo = $this->Booking->getDataSource();
        $mongoCollectionObject = $mongo->getMongoCollection($this->Booking);

        $customCollection = new GlofoxMongoCollection($mongoCollectionObject);
        $results = $customCollection->aggregation($params);

        return new JsonResponse([
            'frequency' => $by,
            'total' => $this->totalize($results),
        ]);
    }

    public function capacity(AnalyticsCapacityRequest $request): Illuminate\Http\JsonResponse
    {
        $user = Auth::user();
        $branch = $user->branch();
        $timezone = $branch->timezone();

        $data = $request->data();

        $start = (int)$data->get('start');
        $end = (int)$data->get('end');

        $parsingFormat = 'Y-m-d H:i:s';

        // We, unfortunately, need to do this to address the issue with dates we have in the database.
        $startDateTime = Carbon::createFromTimestamp($start, $timezone)->format($parsingFormat);
        $endDateTime = Carbon::createFromTimestamp($end, $timezone)->format($parsingFormat);

        $startTimestamp = Carbon::createFromFormat($parsingFormat, $startDateTime)->getTimestamp();
        $endTimestamp = Carbon::createFromFormat($parsingFormat, $endDateTime)->getTimestamp();
        // The workaround ends here

        $groupId = ['year' => '$year', 'month' => '$month', 'day' => '$day'];

        //Retrieve bookings
        $params = [
            [
                '$match' => [
                    'namespace' => $user['namespace'],
                    'branch_id' => $user['branch_id'],
                    'status' => 'BOOKED',
                    'event_id' => ['$exists' => true],
                    'time_start' => [
                        '$type' => 9,
                        '$gte' => new MongoDate($startTimestamp),
                        '$lt' => new MongoDate($endTimestamp),
                    ],
                ],
            ],
            [
                '$project' => [
                    'year' => ['$year' => '$created'],
                    'month' => ['$month' => '$created'],
                    'week' => ['$week' => '$created'],
                    'day' => ['$dayOfMonth' => '$created'],
                    '_id' => 1,
                    'weight' => 1,
                    'amount' => 1,
                    'guest_bookings' => 1,
                ],
            ],
            ['$group' => ['_id' => $groupId, 'total' => ['$sum' => 1], 'guests' => ['$sum' => '$guest_bookings']]],
            ['$sort' => ['_id' => 1]],
        ];
        $mongo = $this->Booking->getDataSource();
        $mongoCollectionObject = $mongo->getMongoCollection($this->Booking);
        $customCollection = new GlofoxMongoCollection($mongoCollectionObject);
        $results = $customCollection->aggregation($params);

        //Retrieve capacity
        $params = [
            [
                '$match' => [
                    'branch_id' => $user['branch_id'],
                    'active' => true,
                    'date' => [
                        '$type' => 9,
                        '$gte' => new MongoDate($startTimestamp),
                        '$lt' => new MongoDate($endTimestamp),
                    ],
                ],
            ],
            ['$group' => ['_id' => '$namespace', 'total' => ['$sum' => '$size']]],
        ];
        $mongoCollectionObject = $mongo->getMongoCollection($this->Event);
        $customCollection = new GlofoxMongoCollection($mongoCollectionObject);
        $capacity = $customCollection->aggregation($params);

        $capacity = $this->totalize($capacity);
        $allocation = null;

        if ($capacity) {
            $usage = $this->totalize($results);
            $allocation = (($usage / $capacity) * 100);
            $allocation = (float)(number_format($allocation, 2));
        }

        $response = [
            'startTimestamp' => $start,
            'endTimestamp' => $end,
            'allocation' => $allocation,
        ];

        return response()->json($response);
    }

    public function members(MembersAnalyticsRequest $request)
    {
        $this->injectAPIVersion($this->User, '2.0');

        $this->layout = false;
        $this->response->type('json');
        $this->autoRender = false;

        $user = $this->getUser();
        $branch = $this->Branch->findById($user['branch_id'])['Branch'];

        //Validate user and branch
        if (empty($user) || empty($branch)) {
            throw new BadRequestException(
                __('Invalid user or branch, we could not find branch with _id ' . $user['branch_id'])
            );
        }

        $report = new MembersTotal();

        $formatter = new \Glofox\Domain\Users\Formatters\LegacyChildResponseFormatter();

        $report->newest = $formatter->format($this->User->getLatestSignups($branch));
        $report->expiring = $formatter->format($this->User->getExpiring($branch));

        return json_encode($report, JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    public function events(AnalyticsEventsRequest $request)
    {
        $this->injectAPIVersion($this->User, '2.0');

        $this->layout = false;
        $this->response->type('json');
        $this->autoRender = false;

        $user = $this->getUser();
        $branch = $this->Branch->findById($user['branch_id'])['Branch'];

        //Validate user and branch
        if (empty($user) || empty($branch)) {
            throw new BadRequestException(
                __('Invalid user or branch, we could not find branch with _id ' . $user['branch_id'])
            );
        }

        //Force new afterfind
        $this->Event->API = '>2.0';
        $this->TimeSlot->API = '>2.0';

        $timezone = $this->Branch->getTimeZone($branch['_id']);
        $time = Carbon::now($timezone);

        $todayStart = strtotime($time->startOfDay()->format('Y-m-d H:i:s'));
        $todayEnd = strtotime($time->endOfDay()->format('Y-m-d H:i:s'));

        $tomorrowStart = strtotime($time->addDay(1)->startOfDay()->format('Y-m-d H:i:s'));
        $tomorrowEnd = strtotime($time->endOfDay()->format('Y-m-d H:i:s'));

        $todayEvents = $this->Event->findByDateRangeAndTrainerId($branch, (int)$todayStart, (int)$todayEnd);
        $todayEvents = array_map([$this->Event, 'mapObject'], $todayEvents);

        $tomorrowEvents = $this->Event->findByDateRangeAndTrainerId($branch, (int)$tomorrowStart, (int)$tomorrowEnd);
        $tomorrowEvents = array_map([$this->Event, 'mapObject'], $tomorrowEvents);

        /** @var AddVirtualOnlineFieldTransformer $onlineFieldTransformer */
        $onlineFieldTransformer = app()->make(AddVirtualOnlineFieldTransformer::class);
        $todayEvents = $onlineFieldTransformer->execute($todayEvents);
        $tomorrowEvents = $onlineFieldTransformer->execute($tomorrowEvents);

        $timetable = new Timetable();
        $timetable->today = $todayEvents;
        $timetable->todayPer = $this->calculateCapacity($todayEvents);
        $timetable->tomorrow = $tomorrowEvents;
        $timetable->tomorrowPer = $this->calculateCapacity($tomorrowEvents);

        return json_encode($timetable, JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    private function calculateCapacity(array &$list): float
    {
        $capacity = 0;
        $booked = 0;
        //Sort asc
        $list = Hash::sort($list, '{n}.time_start', 'asc');
        //Calc bookings
        foreach ($list as $event) {
            $capacity += $event['size'];
            $booked += $event['booked'];
        }
        //Avoid division between 0
        if ($capacity < 1) {
            $capacity = 1;
        }

        return $booked * 100 / $capacity;
    }

    /**
     * @param $list
     * @param $by
     * @param $start
     *
     * @return array
     */
    private function formatPeriod($list, $by, $start)
    {
        //Get all numbers between the first _id in list and last _id in list
        $period = 'WEEKLY';
        $count = 5;
        switch ($by) {
            case PeriodInterval::Year:
                $period = 'MONTHLY';
                $count = 12;
                break;
            case PeriodInterval::Week:
                $period = 'DAILY';
                $count = 7;
                break;
        }
        //Obtain all dates to display
        $rrule = new RRule([
            'FREQ' => $period,
            'INTERVAL' => 1,
            'DTSTART' => gmdate('Y-m-d', $start->sec),
            'COUNT' => $count,
        ]);
        //Prepare result list and inject the values of those dates who have result
        $curatedList = [];
        foreach ($rrule as $date) {
            $period = new Period();
            $period->_id = $this->getIdForPeriod($by, $date);
            $period->total = $this->getValueForPeriod($period, $list);
            $curatedList[] = $period;
        }

        return $curatedList;
    }

    private function getIdForPeriod($period, $date): array
    {
        switch ($period) {
            case PeriodInterval::Month:
                $id = [
                    'year' => (int)($date->format('Y')),
                    'week' => (int)($date->format('W')),
                ];
                break;
            case PeriodInterval::Year:
                $id = [
                    'year' => (int)($date->format('Y')),
                    'month' => (int)($date->format('m')),
                ];
                break;
            case PeriodInterval::Week:
            case PeriodInterval::Today:
                $id = [
                    'year' => (int)($date->format('Y')),
                    'month' => (int)($date->format('m')),
                    'week' => (int)($date->format('W')),
                    'day' => (int)($date->format('d')),
                ];
                break;
            default:
                throw new \Exception('Unexpected value');
        }

        return $id;
    }

    private function getValueForPeriod($period, &$list)
    {
        //Empty return
        if (empty($list) || !is_countable($list) || count($list) === 0) {
            return 0;
        }
        //Peek first item in list (previously sorted by date)
        $data = $list[0];
        $total = 0;
        //Is weekly report OR monthly report OR yearly report
        if (
            (!empty($data['_id']['day']) && (int)($data['_id']['day']) == (int)($period->_id['day'])) ||
            (!empty($data['_id']['week']) && (int)($data['_id']['week']) == (int)($period->_id['week'])) ||
            (!empty($data['_id']['month']) && (int)($data['_id']['month']) == (int)($period->_id['month']))
        ) {
            $total = $data['total'];
            //Shift first item in array to mark it as already set
            array_shift($list);

            return $total;
        }

        return $total;
    }

    public function totalize(array $list)
    {
        $result = 0;
        foreach ($list as $item) {
            $result += $item['total'];
            if (isset($item['guests'])) {
                $result += $item['guests'];
            }
        }

        return $result;
    }

    public function findMembershipBreakdownById($branch_id)
    {
        $memberships = $this->Membership->findAllByBranchId($branch_id);
        $return_data = [];
        foreach ($memberships as $key => $membership) {
            $return_data[$key]['name'] = $memberships[$key]['Membership']['name'];
            $return_data[$key]['total_members'] = $this->User->find('count', [
                'conditions' => [
                    'active' => true,
                    'branch_id' => $branch_id,
                    'membership._id' => $membership['Membership']['_id'],
                ],
            ]);
        }
        $return_data[$key + 1]['name'] = 'Pay As You Go';
        $return_data[$key + 1]['total_members'] = $this->User->find('count', [
            'conditions' => [
                'active' => true,
                'branch_id' => $branch_id,
                'membership.type' => 'payg',
            ],
        ]);

        return json_encode($return_data, JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    /**
     * http://localhost/development/Analytics/report then it will use the Model name specified, however all params are evaluated within
     * the form (POST). Based on Aggregation Framework
     * INPUT
     *     model      -> model name
     *     conditions -> array of conditions.
     **/
    /**
     * @throws BranchNotFoundException
     * @throws InvalidMongoIdException
     * @throws CannotAccessReportException
     */
    public function report(
        AnalyticsReportRequest $request,
        EventManager $eventManager
    ) {
        $data = file_get_contents('php://input');
        $data = json_decode($data, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        $data ??= $this->request->data;

        $userBelongsToBranchValidator = app()->make(CanAccessReportValidator::class);
        $userBelongsToBranchValidator->validate($data);

        $request = \Glofox\Request::createFromGlobals();

        $model_report = $data['model'];

        if ('DefaultMembership' != $model_report && 'DefaultMember' != $model_report && 'DefaultStrikes' != $model_report) {
            $startDate = $data['start'];
            $data['start'] = Carbon::createFromTimestamp($startDate);
            $endDate = $data['end'];
            $data['end'] = Carbon::createFromTimestamp($endDate);
        }

        if (isset($data['filter']['CompareToRanges']) && $data['filter']['CompareToRanges']) {
            $secondStartDate = $data['secondStart'];
            $data['secondStart'] = (new DateTime("@$secondStartDate"))->format('Y-m-d H:i:s');
            $secondEndDate = $data['secondEnd'];
            $data['secondEnd'] = (new DateTime("@$secondEndDate"))->format('Y-m-d H:i:s');
        }

        $result = [];

        if ('Membership' == $model_report) {
            $result = $this->membershipReport($data);
        } elseif (
            ('Sale' == $model_report) ||
            ('SaleByTrainer' == $model_report)
        ) {
            $result = $this->saleReport($data);
        } elseif ('EntryList' == $model_report) {
            $result = $this->entryListReport($data);
        } elseif ('BookingAttendance' == $model_report) {
            $result = $this->bookingAttendanceReport($data);
        } elseif ('FailedPayments' == $model_report) {
            $result = $this->failedPaymentsReport($data);
        } elseif ('DefaultMembership' == $model_report) {
            $result = $this->defaultMembershipReport($data);
        } elseif ('DefaultMember' == $model_report) {
            $result = $this->defaultMemberReport($data);
        } elseif ('TransactionsList' == $model_report) {
            $result = $this->transactionsListReport($data);
        } elseif ('DefaultStrikes' == $model_report) {
            $result = $this->defaultStrikesReport($data);
        }

        if ('text/csv' === $request->header('accept')) {
            $filename = sprintf('%s.csv', Str::kebab($model_report));
            $report = CsvReportFactory::make($model_report, $data['branch_id']);
            $this->response->body($report->generate($result));
            $this->response->download($filename);
            $this->response->type('text/csv; charset=UTF-8');
            $eventManager->emit(TransactionReportWasDownloaded::class, [$data]);

            return $this->response;
        }

        return response()->json($result);
    }

    private function membershipReport($data)
    {
        $user = Auth::user();
        $branch = $user->branch();

        // We reiceve the data in the branch's timezone format
        $start = Carbon::createFromFormat('Y-m-d H:i:s', $data['start']->format('Y-m-d H:i:s'), $branch->timezone());
        $end = Carbon::createFromFormat('Y-m-d H:i:s', $data['end']->format('Y-m-d H:i:s'), $branch->timezone());

        // But the `created` field that is to be searched stores the dates in GMT, so we need to
        // convert the dates to `GMT`
        $startTimestamp = $start->startOfDay()->getTimestamp();
        $endTimestamp = $end->endOfDay()->getTimestamp();

        $conditions = [
            'metadata.branch_id' => $data['branch_id'],
            'metadata.namespace' => $data['namespace'],
            'created' => [
                '$gte' => new MongoDate($startTimestamp),
                '$lte' => new MongoDate($endTimestamp),
            ],
            'paid' => true,
            'metadata.glofox_event' => 'upfront_payment',
        ];

        $params = [
            'conditions' => $conditions,
            'order' => [
                'created' => 1,
            ],
        ];

        $result = $this->StripeCharge->find('all', $params);

        $membershipMap = new Collection();

        foreach ($result as $stripeCharge) {
            $stripeCharge = $stripeCharge['StripeCharge'];
            if (isset($stripeCharge['metadata']) && isset($stripeCharge['metadata']['membership_id'])) {
                $membershipId = $stripeCharge['metadata']['membership_id'];
                if ($membershipMap->has($membershipId)) {
                    $membershipMap->put($membershipId, $membershipMap->get($membershipId) + 1);
                } else {
                    $membershipMap->put($membershipId, 1);
                }
            }
        }

        return $membershipMap->toArray();
    }

    private function saleReport($data)
    {
        $result = [];

        foreach ($data['filter']['Sales'] as $filter) {
            if ('Memberships' == $filter['id']) {
                $result[$filter['id']] = $this->createMembershipSaleReport($data);
                $result[$filter['id']]['header'] = 'Memberships';
            } elseif ('Programs' == $filter['id']) {
                $result[$filter['id']] = $this->createClassSaleReport($data);
                $result[$filter['id']]['header'] = 'Programs';
            } elseif ('Courses' == $filter['id']) {
                $result[$filter['id']] = $this->createCourseSaleReport($data);
                $result[$filter['id']]['header'] = 'Courses';
            } elseif ('Appointments' == $filter['id']) {
                $result[$filter['id']] = $this->createAppointmentSaleReport($data);
                $result[$filter['id']]['header'] = 'Appointments';
            } elseif ('CustomCharges' == $filter['id']) {
                $result[$filter['id']] = $this->createCustomChargeSaleReport($data);
                $result[$filter['id']]['header'] = 'Custom Charges';
            } elseif ('Instructors' == $filter['id']) {
                $result[$filter['id']] = $this->createInstructorSaleReport($data);
                $result[$filter['id']]['header'] = 'Instructors';
            } elseif ('Products' == $filter['id']) {
                $result[$filter['id']] = $this->createProductSaleReport($data);
                $result[$filter['id']]['header'] = 'Products';
            }
        }

        return $result;
    }

    private function createMembershipSaleReport($data)
    {
        $result = [];
        $total_sales = 0;

        $conditions = [
            'metadata.branch_id' => $data['branch_id'],
            'metadata.namespace' => $data['namespace'],
            'created' => [
                '$gte' => new MongoDate(strtotime($data['start'])),
                '$lte' => new MongoDate(strtotime($data['end'])),
            ],
            'paid' => true,
            'metadata.glofox_event' => [
                '$in' => [
                    'upfront_payment',
                    'subscription_prorate',
                    'subscription_create',
                    'subscription_payment',
                ],
            ],
        ];

        $params = [
            'conditions' => $conditions,
            'order' => ['created' => 1],
        ];

        $stripe_charges = $this->StripeCharge->find('all', $params);

        $members = [];

        foreach ($stripe_charges as $stripe_charge) {
            $resultTemp = [];

            if (!isset($stripe_charge['StripeCharge']['metadata']['plan_code'])) {
                $total_sales = $total_sales + $stripe_charge['StripeCharge']['amount'];
                $resultTemp['id'] = 'none';
                $date = explode(' ', $stripe_charge['StripeCharge']['created']);
                $resultTemp['date'] = $date[0];
                $resultTemp['sale'] = $stripe_charge['StripeCharge']['amount'];
                $result['total_sales'] = $total_sales;
                $result['details'][] = $resultTemp;
            } else {
                foreach ($data['filter']['Plans'] as $plan) {
                    if ($stripe_charge['StripeCharge']['metadata']['plan_code'] == $plan['id']) {
                        $total_sales = $total_sales + $stripe_charge['StripeCharge']['amount'];
                        $resultTemp['id'] = $plan['id'];
                        $date = explode(' ', $stripe_charge['StripeCharge']['created']);
                        $resultTemp['date'] = $date[0];
                        $resultTemp['sale'] = $stripe_charge['StripeCharge']['amount'];
                        $result['total_sales'] = $total_sales;
                        $result['details'][] = $resultTemp;

                        if (isset($data['filter']['ReportByMembers']) && $data['filter']['ReportByMembers']) {
                            if (!in_array($stripe_charge['StripeCharge']['metadata']['user_id'], $members)) {
                                $members[] = $stripe_charge['StripeCharge']['metadata']['user_id'];
                            }
                        }
                        break;
                    }
                }
            }
        }

        if (count($members) > 0) {
            foreach ($members as $member) {
                $conditions['metadata.user_id'] = $member;
                $expenses = $this->StripeCharge->find('all', ['conditions' => $conditions]);
                $total_expenses = 0;

                foreach ($expenses as $spending) {
                    $total_expenses = $total_expenses + $spending['StripeCharge']['amount'];
                }
                $memberTemp = [];
                $memberTemp['_id'] = $member;
                $memberTemp['name'] = '';
                $memberTemp['email'] = '';
                $memberTemp['expenses'] = $total_expenses;
                $memberTemp['charges'] = is_countable($expenses) ? count($expenses) : 0;

                if (isset($data['filter']['CompareToRanges']) && $data['filter']['CompareToRanges']) {
                    $second_conditions = $conditions;
                    $second_conditions['created'] = [
                        '$gte' => new MongoDate(strtotime($data['secondStart'])),
                        '$lte' => new MongoDate(strtotime($data['secondEnd'])),
                    ];
                    $second_expenses = $this->StripeCharge->find('all', ['conditions' => $second_conditions]);
                    $second_total_expenses = 0;

                    foreach ($second_expenses as $second_spending) {
                        $second_total_expenses = $second_total_expenses + $second_spending['StripeCharge']['amount'];
                    }

                    $memberTemp['second_expenses'] = $second_total_expenses;
                    $memberTemp['second_charges'] = is_countable($second_expenses) ? count($second_expenses) : 0;
                }

                $result['members'][] = $memberTemp;
            }
        }

        return $result;
    }

    private function createClassSaleReport($data)
    {
        $result = [];
        $total_sales = 0;
        $when_class_starts = 'class_starts' ==
        $data['filter']['DateRangeFor']['value'] ?
            true :
            false;

        $programs_ids = [];
        $members = [];

        foreach ($data['filter']['ClassNames'] as $program_id) {
            $programs_ids[] = $program_id['id'];
        }

        if ($when_class_starts) {
            $event_conditions = [
                'branch_id' => $data['branch_id'],
                'namespace' => $data['namespace'],
                'active' => true,
                'date' => [
                    '$gte' => new MongoDate(strtotime($data['start'])),
                    '$lte' => new MongoDate(strtotime($data['end'])),
                ],
                'program_id' => ['$in' => $programs_ids],
            ];

            $event_params = [
                'conditions' => $event_conditions,
                'order' => ['created' => 1],
            ];

            $events = $this->Event->find('all', $event_params);

            foreach ($events as $event) {
                $resultTemp = [];

                $stripe_charge_condition = [
                    'conditions' => [
                        'metadata.event_id' => $event['Event']['_id'],
                    ],
                ];

                $stripe_charges = $this->StripeCharge->find('all', $stripe_charge_condition);

                foreach ($stripe_charges as $stripe_charge) {
                    $total_sales = $total_sales + $stripe_charge['StripeCharge']['amount'];
                    $resultTemp['id'] = $event['Event']['program_id'];
                    $date = explode(' ', $event['Event']['date']);
                    $resultTemp['date'] = $date[0];
                    $resultTemp['sale'] = $stripe_charge['StripeCharge']['amount'];
                    $result['total_sales'] = $total_sales;
                    $result['details'][] = $resultTemp;
                }
            }
        } else {
            $stripe_charges_conditions = [
                'metadata.branch_id' => $data['branch_id'],
                'metadata.namespace' => $data['namespace'],
                'created' => [
                    '$gte' => new MongoDate(strtotime($data['start'])),
                    '$lte' => new MongoDate(strtotime($data['end'])),
                ],
                'paid' => true,
                'metadata.glofox_event' => 'book_class',
            ];

            $stripe_charges_params = [
                'conditions' => $stripe_charges_conditions,
                'order' => ['created' => 1],
            ];

            $stripe_charges = $this->StripeCharge->find('all', $stripe_charges_params);

            foreach ($stripe_charges as $stripe_charge) {
                $resultTemp = [];

                $event_condition = [
                    'conditions' => [
                        '_id' => $stripe_charge['StripeCharge']['metadata']['event_id'],
                        'program_id' => ['$in' => $programs_ids],
                    ],
                ];

                $event = $this->Event->find('first', $event_condition);

                if ($event) {
                    $total_sales = $total_sales + $stripe_charge['StripeCharge']['amount'];
                    $resultTemp['id'] = $event['Event']['program_id'];
                    $date = explode(' ', $stripe_charge['StripeCharge']['created']);
                    $resultTemp['date'] = $date[0];
                    $resultTemp['sale'] = $stripe_charge['StripeCharge']['amount'];
                    $result['total_sales'] = $total_sales;
                    $result['details'][] = $resultTemp;

                    if (isset($data['filter']['ReportByMembers']) && $data['filter']['ReportByMembers']) {
                        if (!in_array($stripe_charge['StripeCharge']['metadata']['user_id'], $members)) {
                            $members[] = $stripe_charge['StripeCharge']['metadata']['user_id'];
                        }
                    }
                }
            }
        }

        if (count($members) > 0) {
            // unset($stripe_charges_conditions['metadata.glofox_event']);
            $booking_conditions = [
                'branch_id' => $data['branch_id'],
                'namespace' => $data['namespace'],
                'created' => [
                    '$gte' => new MongoDate(strtotime($data['start'])),
                    '$lte' => new MongoDate(strtotime($data['end'])),
                ],
                'type' => 'events',
                'status' => ['$ne' => 'CANCELED'],
            ];

            foreach ($members as $member) {
                $stripe_charges_conditions['metadata.user_id'] = $member;
                $booking_conditions['user_id'] = $member;
                $expenses = $this->StripeCharge->find('all', ['conditions' => $stripe_charges_conditions]);
                $total_expenses = 0;

                foreach ($expenses as $spending) {
                    $total_expenses = $total_expenses + $spending['StripeCharge']['amount'];
                }
                $memberTemp = [];
                $memberTemp['_id'] = $member;
                $memberTemp['name'] = '';
                $memberTemp['email'] = '';
                $memberTemp['expenses'] = $total_expenses;
                $memberTemp['bookings'] = $this->Booking->find('count', ['conditions' => $booking_conditions]);

                if (isset($data['filter']['CompareToRanges']) && $data['filter']['CompareToRanges']) {
                    $second_conditions = $stripe_charges_conditions;
                    $second_conditions['created'] = [
                        '$gte' => new MongoDate(strtotime($data['secondStart'])),
                        '$lte' => new MongoDate(strtotime($data['secondEnd'])),
                    ];
                    $second_booking_conditions = $booking_conditions;
                    $second_booking_conditions['created'] = [
                        '$gte' => new MongoDate(strtotime($data['secondStart'])),
                        '$lte' => new MongoDate(strtotime($data['secondEnd'])),
                    ];
                    $second_expenses = $this->StripeCharge->find('all', ['conditions' => $second_conditions]);
                    $second_total_expenses = 0;

                    foreach ($second_expenses as $second_spending) {
                        $second_total_expenses = $second_total_expenses + $second_spending['StripeCharge']['amount'];
                    }

                    $memberTemp['second_expenses'] = $second_total_expenses;
                    $memberTemp['second_bookings'] = $this->Booking->find(
                        'count',
                        ['conditions' => $second_booking_conditions]
                    );
                }

                $result['members'][] = $memberTemp;
            }
        }

        return $result;
    }

    private function createCourseSaleReport($data)
    {
        $result = [];
        $total_sales = 0;

        $conditions = [
            'metadata.branch_id' => $data['branch_id'],
            'metadata.namespace' => $data['namespace'],
            'created' => [
                '$gte' => new MongoDate(strtotime($data['start'])),
                '$lte' => new MongoDate(strtotime($data['end'])),
            ],
            'paid' => true,
            'metadata.glofox_event' => 'book_course',
        ];

        $params = [
            'conditions' => $conditions,
            'order' => ['created' => 1],
        ];

        $stripe_charges = $this->StripeCharge->find('all', $params);

        $members = [];

        foreach ($stripe_charges as $stripe_charge) {
            $resultTemp = [];

            foreach ($data['filter']['CourseNames'] as $course) {
                if ($stripe_charge['StripeCharge']['metadata']['course_id'] == $course['id']) {
                    $total_sales = $total_sales + $stripe_charge['StripeCharge']['amount'];
                    $resultTemp['id'] = $course['id'];
                    $date = explode(' ', $stripe_charge['StripeCharge']['created']);
                    $resultTemp['date'] = $date[0];
                    $resultTemp['sale'] = $stripe_charge['StripeCharge']['amount'];
                    $result['total_sales'] = $total_sales;
                    $result['details'][] = $resultTemp;

                    if (isset($data['filter']['ReportByMembers']) && $data['filter']['ReportByMembers']) {
                        if (!in_array($stripe_charge['StripeCharge']['metadata']['user_id'], $members)) {
                            $members[] = $stripe_charge['StripeCharge']['metadata']['user_id'];
                        }
                    }
                    break;
                }
            }
        }

        if (count($members) > 0) {
            // unset($conditions['metadata.glofox_event']);
            $booking_conditions = [
                'branch_id' => $data['branch_id'],
                'namespace' => $data['namespace'],
                'created' => [
                    '$gte' => new MongoDate(strtotime($data['start'])),
                    '$lte' => new MongoDate(strtotime($data['end'])),
                ],
                'type' => 'courses',
                'status' => ['$ne' => 'CANCELED'],
            ];

            foreach ($members as $member) {
                $conditions['metadata.user_id'] = $member;
                $booking_conditions['user_id'] = $member;
                $expenses = $this->StripeCharge->find('all', ['conditions' => $conditions]);
                $total_expenses = 0;

                foreach ($expenses as $spending) {
                    $total_expenses = $total_expenses + $spending['StripeCharge']['amount'];
                }
                $memberTemp = [];
                $memberTemp['_id'] = $member;
                $memberTemp['name'] = '';
                $memberTemp['email'] = '';
                $memberTemp['expenses'] = $total_expenses;
                $memberTemp['bookings'] = $this->Booking->find('count', ['conditions' => $booking_conditions]);

                if (isset($data['filter']['CompareToRanges']) && $data['filter']['CompareToRanges']) {
                    $second_conditions = $conditions;
                    $second_conditions['created'] = [
                        '$gte' => new MongoDate(strtotime($data['secondStart'])),
                        '$lte' => new MongoDate(strtotime($data['secondEnd'])),
                    ];
                    $second_booking_conditions = $booking_conditions;
                    $second_booking_conditions['created'] = [
                        '$gte' => new MongoDate(strtotime($data['secondStart'])),
                        '$lte' => new MongoDate(strtotime($data['secondEnd'])),
                    ];
                    $second_expenses = $this->StripeCharge->find('all', ['conditions' => $second_conditions]);
                    $second_total_expenses = 0;

                    foreach ($second_expenses as $second_spending) {
                        $second_total_expenses = $second_total_expenses + $second_spending['StripeCharge']['amount'];
                    }

                    $memberTemp['second_expenses'] = $second_total_expenses;
                    $memberTemp['second_bookings'] = $this->Booking->find(
                        'count',
                        ['conditions' => $second_booking_conditions]
                    );
                }

                $result['members'][] = $memberTemp;
            }
        }

        return $result;
    }

    private function createAppointmentSaleReport($data)
    {
        $result = [];
        $total_sales = 0;

        $conditions = [
            'metadata.branch_id' => $data['branch_id'],
            'metadata.namespace' => $data['namespace'],
            'created' => [
                '$gte' => new MongoDate(strtotime($data['start'])),
                '$lte' => new MongoDate(strtotime($data['end'])),
            ],
            'paid' => true,
            'metadata.glofox_event' => 'book_time_slot',
        ];

        $params = [
            'conditions' => $conditions,
            'order' => ['created' => 1],
        ];

        $stripe_charges = $this->StripeCharge->find('all', $params);

        $members = [];

        foreach ($stripe_charges as $stripe_charge) {
            $resultTemp = [];

            foreach ($data['filter']['AppointmentNames'] as $appointment_name) {
                if ($stripe_charge['StripeCharge']['metadata']['model_id'] == $appointment_name['id']) {
                    $total_sales = $total_sales + $stripe_charge['StripeCharge']['amount'];
                    $resultTemp['id'] = $appointment_name['id'];
                    $date = explode(' ', $stripe_charge['StripeCharge']['created']);
                    $resultTemp['date'] = $date[0];
                    $resultTemp['sale'] = $stripe_charge['StripeCharge']['amount'];
                    $result['total_sales'] = $total_sales;
                    $result['details'][] = $resultTemp;

                    if (isset($data['filter']['ReportByMembers']) && $data['filter']['ReportByMembers']) {
                        if (!in_array($stripe_charge['StripeCharge']['metadata']['user_id'], $members)) {
                            $members[] = $stripe_charge['StripeCharge']['metadata']['user_id'];
                        }
                    }
                    break;
                }
            }
        }

        if (count($members) > 0) {
            // unset($conditions['metadata.glofox_event']);
            $booking_conditions = [
                'branch_id' => $data['branch_id'],
                'namespace' => $data['namespace'],
                'created' => [
                    '$gte' => new MongoDate(strtotime($data['start'])),
                    '$lte' => new MongoDate(strtotime($data['end'])),
                ],
                'type' => 'time_slots',
                'status' => ['$ne' => 'CANCELED'],
            ];

            foreach ($members as $member) {
                $conditions['metadata.user_id'] = $member;
                $booking_conditions['user_id'] = $member;
                $expenses = $this->StripeCharge->find('all', ['conditions' => $conditions]);
                $total_expenses = 0;

                foreach ($expenses as $spending) {
                    $total_expenses = $total_expenses + $spending['StripeCharge']['amount'];
                }
                $memberTemp = [];
                $memberTemp['_id'] = $member;
                $memberTemp['name'] = '';
                $memberTemp['email'] = '';
                $memberTemp['expenses'] = $total_expenses;
                $memberTemp['bookings'] = $this->Booking->find('count', ['conditions' => $booking_conditions]);

                if (isset($data['filter']['CompareToRanges']) && $data['filter']['CompareToRanges']) {
                    $second_conditions = $conditions;
                    $second_conditions['created'] = [
                        '$gte' => new MongoDate(strtotime($data['secondStart'])),
                        '$lte' => new MongoDate(strtotime($data['secondEnd'])),
                    ];
                    $second_booking_conditions = $booking_conditions;
                    $second_booking_conditions['created'] = [
                        '$gte' => new MongoDate(strtotime($data['secondStart'])),
                        '$lte' => new MongoDate(strtotime($data['secondEnd'])),
                    ];
                    $second_expenses = $this->StripeCharge->find('all', ['conditions' => $second_conditions]);
                    $second_total_expenses = 0;

                    foreach ($second_expenses as $second_spending) {
                        $second_total_expenses = $second_total_expenses + $second_spending['StripeCharge']['amount'];
                    }

                    $memberTemp['second_expenses'] = $second_total_expenses;
                    $memberTemp['second_bookings'] = $this->Booking->find(
                        'count',
                        ['conditions' => $second_booking_conditions]
                    );
                }

                $result['members'][] = $memberTemp;
            }
        }

        return $result;
    }

    private function createCustomChargeSaleReport($data)
    {
        $result = [];
        $total_sales = 0;

        $conditions = [
            'metadata.branch_id' => $data['branch_id'],
            'metadata.namespace' => $data['namespace'],
            'created' => [
                '$gte' => new MongoDate(strtotime($data['start'])),
                '$lte' => new MongoDate(strtotime($data['end'])),
            ],
            'paid' => true,
            'metadata.glofox_event' => 'custom_charge',
        ];

        $params = [
            'conditions' => $conditions,
            'order' => ['created' => 1],
        ];

        $stripe_charges = $this->StripeCharge->find('all', $params);

        $members = [];

        foreach ($stripe_charges as $stripe_charge) {
            $resultTemp = [];

            $total_sales = $total_sales + $stripe_charge['StripeCharge']['amount'];
            $date = explode(' ', $stripe_charge['StripeCharge']['created']);
            $resultTemp['date'] = $date[0];
            $resultTemp['sale'] = $stripe_charge['StripeCharge']['amount'];
            $result['total_sales'] = $total_sales;
            $result['details'][] = $resultTemp;

            if (isset($data['filter']['ReportByMembers']) && $data['filter']['ReportByMembers']) {
                if (in_array($stripe_charge['StripeCharge']['metadata']['user_id'], $members)) {
                    foreach ($result['members'] as &$member) {
                        if ($member['_id'] == $stripe_charge['StripeCharge']['metadata']['user_id']) {
                            $member['expenses'] = $member['expenses'] + $stripe_charge['StripeCharge']['amount'];
                            $member['charges'] = $member['charges'] + 1;
                        }
                    }
                } else {
                    $members[] = $stripe_charge['StripeCharge']['metadata']['user_id'];

                    $memberTemp = [];
                    $memberTemp['_id'] = $stripe_charge['StripeCharge']['metadata']['user_id'];
                    $memberTemp['name'] = '';
                    $memberTemp['email'] = '';
                    $memberTemp['expenses'] = $stripe_charge['StripeCharge']['amount'];
                    $memberTemp['charges'] = 1;
                    $result['members'][] = $memberTemp;
                }
            }
        }

        if (
            isset($data['filter']['CompareToRanges']) && $data['filter']['CompareToRanges'] &&
            isset($result['members']) && count($result['members']) > 0
        ) {
            $conditions['created'] = [
                '$gte' => new MongoDate(strtotime($data['secondStart'])),
                '$lte' => new MongoDate(strtotime($data['secondEnd'])),
            ];

            foreach ($result['members'] as &$member) {
                $conditions['metadata.user_id'] = $member['_id'];

                $second_expenses = $this->StripeCharge->find('all', ['conditions' => $conditions]);
                $second_total_expenses = 0;

                foreach ($second_expenses as $second_spending) {
                    $second_total_expenses = $second_total_expenses + $second_spending['StripeCharge']['amount'];
                }

                $member['second_expenses'] = $second_total_expenses;
                $member['second_charges'] = is_countable($second_expenses) ? count($second_expenses) : 0;
            }
        }

        return $result;
    }

    private function createInstructorSaleReport($data)
    {
        $result = [];
        $total_sales = 0;

        $conditions = [
            'metadata.branch_id' => $data['branch_id'],
            'metadata.namespace' => $data['namespace'],
            'created' => [
                '$gte' => new MongoDate(strtotime($data['start'])),
                '$lte' => new MongoDate(strtotime($data['end'])),
            ],
            'paid' => true,
            'metadata.glofox_event' => 'book_time_slot',
        ];

        $params = [
            'conditions' => $conditions,
            'order' => ['created' => 1],
        ];

        $stripe_charges = $this->StripeCharge->find('all', $params);

        foreach ($stripe_charges as $stripe_charge) {
            $resultTemp = [];

            foreach ($data['filter']['InstructorNames'] as $instructor_name) {
                if ($stripe_charge['StripeCharge']['metadata']['model_id'] == $instructor_name['id']) {
                    $total_sales = $total_sales + $stripe_charge['StripeCharge']['amount'];
                    $resultTemp['id'] = $instructor_name['id'];
                    $date = explode(' ', $stripe_charge['StripeCharge']['created']);
                    $resultTemp['date'] = $date[0];
                    $resultTemp['sale'] = $stripe_charge['StripeCharge']['amount'];
                    $result['total_sales'] = $total_sales;
                    $result['details'][] = $resultTemp;
                    break;
                }
            }
        }

        return $result;
    }

    private function createProductSaleReport($data)
    {
        $result = [];
        $total_sales = 0;

        $conditions = [
            'metadata.branch_id' => $data['branch_id'],
            'metadata.namespace' => $data['namespace'],
            'created' => [
                '$gte' => new MongoDate(strtotime($data['start'])),
                '$lte' => new MongoDate(strtotime($data['end'])),
            ],
            'paid' => true,
            'metadata.glofox_event' => 'buy_product',
        ];

        $params = [
            'conditions' => $conditions,
            'order' => ['created' => 1],
        ];

        $stripe_charges = $this->StripeCharge->find('all', $params);

        $members = [];

        // $this->loadModel('Product');

        foreach ($stripe_charges as $stripe_charge) {
            $resultTemp = [];

            foreach ($data['filter']['ProductNames'] as $filteredProduct) {
                if ($stripe_charge['StripeCharge']['metadata']['product_id'] == $filteredProduct['id']) {
                    $total_sales = $total_sales + $stripe_charge['StripeCharge']['amount'];
                    $resultTemp['id'] = $filteredProduct['id'];
                    $date = explode(' ', $stripe_charge['StripeCharge']['created']);
                    $resultTemp['date'] = $date[0];
                    $resultTemp['sale'] = $stripe_charge['StripeCharge']['amount'];
                    $result['total_sales'] = $total_sales;
                    $result['details'][] = $resultTemp;
                    break;
                }
            }

            if (isset($data['filter']['ReportByMembers']) && $data['filter']['ReportByMembers']) {
                if (in_array($stripe_charge['StripeCharge']['metadata']['user_id'], $members)) {
                    foreach ($result['members'] as &$member) {
                        if ($member['_id'] == $stripe_charge['StripeCharge']['metadata']['user_id']) {
                            $memberTemp['name'] = '';
                            $memberTemp['email'] = '';
                            $member['expenses'] = $member['expenses'] + $stripe_charge['StripeCharge']['amount'];
                            $member['units'] = $member['units'] + 1;
                        }
                    }
                } else {
                    $members[] = $stripe_charge['StripeCharge']['metadata']['user_id'];

                    $memberTemp = [];
                    $memberTemp['_id'] = $stripe_charge['StripeCharge']['metadata']['user_id'];
                    $memberTemp['name'] = '';
                    $memberTemp['email'] = '';
                    $memberTemp['expenses'] = $stripe_charge['StripeCharge']['amount'];
                    $memberTemp['units'] = 1;
                    $result['members'][] = $memberTemp;
                }
            }
        }

        if (
            isset($data['filter']['CompareToRanges']) && $data['filter']['CompareToRanges'] &&
            isset($result['members']) && count($result['members']) > 0
        ) {
            $conditions['created'] = [
                '$gte' => new MongoDate(strtotime($data['secondStart'])),
                '$lte' => new MongoDate(strtotime($data['secondEnd'])),
            ];

            foreach ($result['members'] as &$member) {
                $conditions['metadata.user_id'] = $member['_id'];

                $second_expenses = $this->StripeCharge->find('all', ['conditions' => $conditions]);
                $second_total_expenses = 0;

                foreach ($second_expenses as $second_spending) {
                    $second_total_expenses = $second_total_expenses + $second_spending['StripeCharge']['amount'];
                }

                $member['second_expenses'] = $second_total_expenses;
                $member['second_units'] = is_countable($second_expenses) ? count($second_expenses) : 0;
            }
        }

        return $result;
    }

    private function entryListReport($data)
    {
        $result = [];

        $conditions = [
            'branch_id' => $data['branch_id'],
            'namespace' => $data['namespace'],
            'entry_time' => [
                '$gte' => new MongoDate(strtotime($data['start'])),
                '$lte' => new MongoDate(strtotime($data['end'])),
            ],
        ];

        $params = [
            'conditions' => $conditions,
            'order' => ['entry_time' => 1],
        ];

        $accesses = $this->Access->find('all', $params);

        $result['EntryList']['details'] = $accesses;
        $result['EntryList']['header'] = 'Entry List';

        return $result;
    }

    private function bookingAttendanceReport($data)
    {
        $result = [];

        $event_conditions = [
            'branch_id' => $data['branch_id'],
            'namespace' => $data['namespace'],
            'active' => true,
            'date' => [
                '$gte' => new MongoDate(strtotime($data['start'])),
                '$lte' => new MongoDate(strtotime($data['end'])),
            ],
        ];

        $event_params = [
            'conditions' => $event_conditions,
            'order' => ['created' => 1],
        ];

        $events = $this->Event->find('all', $event_params);

        $showAdvanceOptions = $data['filter']['showAdvanceOptions'];

        if (true == $showAdvanceOptions) {
            $today = date('Y-m-d', strtotime($this->Utility->getCurrentDate()));
            $events_to_remove = [];

            $advance_event_conditions = [
                'branch_id' => $data['branch_id'],
                'namespace' => $data['namespace'],
                'active' => true,
            ];

            if ('showBookedSince' == $data['filter']['advanceFeature']) {
                $advance_event_conditions['date'] = [
                    '$gte' => new MongoDate(strtotime($data['filter']['advanceBookedSince'])),
                    '$lte' => new MongoDate(strtotime($today)),
                ];
            }
            if ('showBookedPeriod' == $data['filter']['advanceFeature']) {
                $advance_event_conditions['date'] = [
                    '$gte' => new MongoDate(strtotime($data['filter']['advanceBookedFrom'])),
                    '$lte' => new MongoDate(strtotime($data['filter']['advanceBookedTo'])),
                ];
            }
            if ('showAttendedSince' == $data['filter']['advanceFeature']) {
                $advance_event_conditions['date'] = [
                    '$gte' => new MongoDate(strtotime($data['filter']['advanceAttendedSince'])),
                    '$lte' => new MongoDate(strtotime($today)),
                ];
            }
            if ('showAttendedPeriod' == $data['filter']['advanceFeature']) {
                $advance_event_conditions['date'] = [
                    '$gte' => new MongoDate(strtotime($data['filter']['advanceAttendedFrom'])),
                    '$lte' => new MongoDate(strtotime($data['filter']['advanceAttendedTo'])),
                ];
            }

            $advance_event_params = [
                'conditions' => $advance_event_conditions,
                'order' => ['created' => 1],
            ];

            $advance_events = $this->Event->find('all', $advance_event_params);
        }

        $totalUnique = $data['filter']['totalUnique'];
        $firstTimeBooking = $data['filter']['firstTimeBooking'];
        $bookedBuddy = $data['filter']['bookedBuddy'];
        $users = [];

        foreach ($events as $event_key => &$event) {
            $is_needed = false;

            if (!empty($data['filter']['ClassNames'])) {
                foreach ($data['filter']['ClassNames'] as $className) {
                    if ($event['Event']['program_id'] == $className['id']) {
                        $is_needed = true;
                    }
                }
            }

            if (!empty($data['filter']['InstructorNames'])) {
                foreach ($data['filter']['InstructorNames'] as $instructorName) {
                    foreach ($event['Event']['trainers'] as $trainer) {
                        if ($trainer == $instructorName['id']) {
                            $is_needed = true;
                        }
                    }
                }
            }

            if (true == $is_needed) {
                $booking_conditions = [
                    'branch_id' => $event['Event']['branch_id'],
                    'namespace' => $event['Event']['namespace'],
                    'event_id' => $event['Event']['_id'],
                    'status' => 'BOOKED',
                ];

                if (true == $bookedBuddy) {
                    $booking_conditions['guest_bookings'] = [
                        '$gt' => 0,
                    ];
                }
                $booking_params = [
                    'conditions' => $booking_conditions,
                ];
                $bookings = $this->Booking->find('all', $booking_params);

                if (!empty($bookings) && is_countable($bookings) && count($bookings) > 0) {
                    if (true == $totalUnique) {
                        $remove = [];

                        foreach ($bookings as $booking_key => $booking) {
                            if (in_array($booking['Booking']['user_id'], $users)) {
                                $remove[] = $booking_key;
                            } else {
                                $users[] = $booking['Booking']['user_id'];
                            }
                        }

                        foreach ($remove as $index_to_remove) {
                            unset($bookings[$index_to_remove]);
                        }
                    }

                    if (true == $firstTimeBooking) {
                        $remove = [];

                        foreach ($bookings as $booking_key => $booking) {
                            $first_time_conditions = [
                                'branch_id' => $event['Event']['branch_id'],
                                'namespace' => $event['Event']['namespace'],
                                'user_id' => $booking['Booking']['user_id'],
                                'status' => 'BOOKED',
                            ];
                            $first_time_params = [
                                'conditions' => $first_time_conditions,
                            ];
                            $user_bookings = $this->Booking->find('count', $first_time_params);

                            if ($user_bookings > 1) {
                                $remove[] = $booking_key;
                            }
                        }

                        foreach ($remove as $index_to_remove) {
                            unset($bookings[$index_to_remove]);
                        }
                    }

                    $event['Event']['bookings'] = $bookings;
                }

                if (true == $showAdvanceOptions && null != $advance_events) {
                    foreach ($advance_events as $advance_event) {
                        if ($event['Event']['program_id'] == $advance_event['Event']['program_id']) {
                            $advance_booking_conditions = [
                                'branch_id' => $advance_event['Event']['branch_id'],
                                'namespace' => $advance_event['Event']['namespace'],
                                'event_id' => $advance_event['Event']['_id'],
                                'status' => 'BOOKED',
                            ];
                            $advance_booking_params = [
                                'conditions' => $advance_booking_conditions,
                            ];
                            $advance_bookings = $this->Booking->find('all', $advance_booking_params);

                            if (
                                'showBookedSince' == $data['filter']['advanceFeature'] ||
                                'showBookedPeriod' == $data['filter']['advanceFeature']
                            ) {
                                if (!is_countable($advance_bookings) || count($advance_bookings) === 0) {
                                    $events_to_remove[] = $event_key;
                                }
                            } elseif (
                                'showAttendedSince' == $data['filter']['advanceFeature'] ||
                                'showAttendedPeriod' == $data['filter']['advanceFeature']
                            ) {
                                $somebody_attended = false;

                                foreach ($advance_bookings as $advance_booking) {
                                    if (
                                        isset($advance_booking['Booking']['attended']) &&
                                        true == $advance_booking['Booking']['attended']
                                    ) {
                                        $somebody_attended = true;
                                    }
                                }

                                if (false == $somebody_attended) {
                                    $events_to_remove[] = $event_key;
                                }
                            }
                        }
                    }
                }

                $result['Bookings and Attendances']['details'][] = $event;
            }
        }

        foreach ($events_to_remove as $index_to_remove) {
            unset($result['Bookings and Attendances']['details'][$index_to_remove]);
        }

        $result['Bookings and Attendances']['header'] = 'Bookings and Attendances';

        return $result;
    }

    private function failedPaymentsReport($data)
    {
        $user = Auth::user();
        $branch = $user->branch();
        $timezone = $branch->timezone();

        /** @var Carbon $start */
        $start = $data['start'];

        /** @var Carbon $end */
        $end = $data['end'];

        // 'created' is saved in UTC. This endpoint is receiving a formatted date string. So we need to find what's the UTC
        // unix representation of the date provided - in the Branch's timezone.
        $unixStartInBranchTimezone = (new Carbon())->setTimezone($timezone)->setDateFrom($start)->setTimeFrom(
            $start
        )->getTimestamp();
        $unixEndInBranchTimezone = (new Carbon())->setTimezone($timezone)->setDateFrom($end)->setTimeFrom(
            $end
        )->getTimestamp();

        // Get the date parameter
        $start = new MongoDate($unixStartInBranchTimezone);
        $end = new MongoDate($unixEndInBranchTimezone);

        $result = [];

        $conditions = [
            'metadata.branch_id' => $data['branch_id'],
            'metadata.namespace' => $data['namespace'],
            'metadata.glofox_event' => 'subscription_payment_failed',
            'created' => [
                '$gte' => $start,
                '$lte' => $end,
            ],
        ];

        $params = [
            'conditions' => $conditions,
            'order' => ['created' => 1],
        ];

        $failedPayments = $this->StripeCharge->find('all', $params);

        $result['FailedPayments']['details'] = $failedPayments;
        $result['FailedPayments']['header'] = 'Failed Payments';

        return $result;
    }

    private function defaultMembershipReport($data)
    {
        $result = [];
        $pre_result = [];

        $user = Auth::user();

        $conditions = [
            'branch_id' => $data['branch_id'],
            'namespace' => $data['namespace'],
            'type' => 'MEMBER',
            'active' => true,
            'membership' => [
                '$exists' => true,
            ],
            'membership.branch_id' => $user['branch_id'],
        ];

        $params = [
            'conditions' => $conditions,
            'order' => ['created' => 1],
        ];

        $users = $this->User->find('all', $params);

        foreach ($users as $user) {
            $resultTemp = [];

            foreach ($data['filter']['Memberships'] as $membership) {
                if ($user['User']['membership']['_id'] == $membership['id']) {
                    $resultTemp['id'] = $membership['id'];
                    $pre_result['details'][] = $resultTemp;
                    break;
                }
            }

            if ('payg' == $user['User']['membership']['type']) {
                $resultTemp['id'] = 'payg';
                $pre_result['details'][] = $resultTemp;
            }
        }
        $result['Memberships'] = $pre_result;
        $result['Memberships']['header'] = 'Memberships';

        return $result;
    }

    private function defaultMemberReport($data)
    {
        $result = [];

        foreach ($data['filter'] as $key => $filter) {
            if ('ActiveMembers' == $key) {
                $result['ActiveMembers']['details'] = $data['filter']['ActiveMembers'];
            } elseif ('ExpiredMembers' == $key) {
                $result['ExpiredMembers']['details'] = $data['filter']['ExpiredMembers'];
            }
        }
        $result['ActiveMembers']['header'] = 'Active Members';
        $result['ExpiredMembers']['header'] = 'Expired Members';

        return $result;
    }

    private function transactionsListReport($data)
    {
        $result = [];

        $user = Auth::user();
        $branch = $user->branch();
        $timezone = $branch->timezone();

        /** @var Carbon $start */
        $start = $data['start'];

        /** @var Carbon $end */
        $end = $data['end'];

        // 'created' is saved in UTC. This endpoint is receiving a formatted date string. So we need to find what's the UTC
        // unix representation of the date provided - in the Branch's timezone.
        $unixStartInBranchTimezone = (new Carbon())->setTimezone($timezone)->setDateFrom($start)->setTimeFrom(
            $start
        )->getTimestamp();
        $unixEndInBranchTimezone = (new Carbon())->setTimezone($timezone)->setDateFrom($end)->setTimeFrom(
            $end
        )->getTimestamp();

        // Get the date parameter
        $start = new MongoDate($unixStartInBranchTimezone);
        $end = new MongoDate($unixEndInBranchTimezone);

        $transactionsResponse = [];

        $conditions = [
            'metadata.branch_id' => $data['branch_id'],
            'metadata.namespace' => $data['namespace'],
            'created' => [
                '$gte' => $start,
                '$lte' => $end,
            ],
        ];

        $params = [
            'conditions' => $conditions,
            'order' => ['created' => -1],
        ];

        $transactions = $this->StripeCharge->find('all', $params);

        foreach ($transactions as $transaction) {
            $transactionsResponse[] = $this->createRecordForTransactionList($transaction);
        }

        $refundTransactions = $this->Refund->find('all', [
            'conditions' => [
                'branch_id' => $data['branch_id'],
                'namespace' => $data['namespace'],
                'created' => [
                    '$gte' => $start,
                    '$lte' => $end,
                ],
            ],
            'order' => ['created' => -1],
        ]);

        foreach ($refundTransactions as $refund) {
            $refundModel = $refund['Refund'];
            $stripeCharge = $this->StripeCharge->findById($refundModel['parent_id']);
            $refundTransaction = $this->createRecordForTransactionList($stripeCharge);
            $refundTransaction['StripeCharge']['status'] = $refundModel['status'] == Status::SUCCESS ?
                'refunded' :
                'refund failed';
            $refundTransaction['StripeCharge']['transaction_status'] = $refundModel['status'] == Status::SUCCESS ?
                'REFUNDED' :
                'REFUND_FAILED';
            $refundTransaction['StripeCharge']['created'] = $refundModel['created'];
            $refundTransaction['StripeCharge']['amount'] = -($refundModel['amount']);
            $refundTransaction['StripeCharge']['payout'] = $refundModel['payout'] ?? null;

            if (PaymentMethods::WALLET === strtolower($stripeCharge['StripeCharge']['metadata']['payment_method'])) {
                $refundTransaction['StripeCharge']['metadata']['balance'] = -($refundModel['amount']);
                $refundTransaction['StripeCharge']['amount'] = 0;
            }

            if ('wallet_top_up' == $stripeCharge['StripeCharge']['metadata']['glofox_event']) {
                $refundTransaction['StripeCharge']['metadata']['balance'] = isset($transaction['StripeCharge']['metadata']['amount']) && $transaction['StripeCharge']['metadata']['amount'] > 0 ? $transaction['StripeCharge']['metadata']['amount'] : $transaction['StripeCharge']['amount'];
                $refundTransaction['StripeCharge']['amount'] = -(isset($transaction['StripeCharge']['metadata']['amount']) && $transaction['StripeCharge']['metadata']['amount'] > 0 ? $transaction['StripeCharge']['metadata']['amount'] : $transaction['StripeCharge']['amount']);;
            }

            $transactionsResponse[] = $refundTransaction;
        }

        usort(
            $transactionsResponse,
            fn($tx1, $tx2) => $tx2['StripeCharge']['created'] <=> $tx1['StripeCharge']['created']
        );

        $result['TransactionsList']['details'] = $transactionsResponse;
        $result['TransactionsList']['header'] = 'Transactions List';

        return $result;
    }

    private function createRecordForTransactionList($transaction)
    {
        $transactionModel = Charge::make($transaction['StripeCharge']);
        $transactionStatus = $transaction['StripeCharge']['transaction_status'] ?? null;
        $glofoxEvent = $transaction['StripeCharge']['metadata']['glofox_event'];

        if (
            (isset($transaction['StripeCharge']['paid']) && true == $transaction['StripeCharge']['paid']) ||
            (isset($transaction['StripeCharge']['refunded']) && true == $transaction['StripeCharge']['refunded'])
        ) {
            $transaction['StripeCharge']['status'] = 'paid';
            $transaction['StripeCharge']['transaction_status'] = 'PAID';
        } elseif (Glofox\Domain\Charges\Status::PENDING === $transactionStatus) {
            $transaction['StripeCharge']['status'] = 'pending';
        } elseif ($transactionModel->isPendingAuthorization()) {
            $transaction['StripeCharge']['status'] = 'pending authorization';
        } else {
            $transaction['StripeCharge']['status'] = 'failed';
            $transaction['StripeCharge']['failed_amount'] = $transaction['StripeCharge']['amount'];
            $transaction['StripeCharge']['amount'] = 0;
        }

        if (!isset($transaction['StripeCharge']['metadata']['balance'])) {
            $transaction['StripeCharge']['metadata']['balance'] = 0;
        }

        if (isset($transaction['StripeCharge']['metadata']['event_id'])) {
            $transaction['StripeCharge']['description'] = $this->Event->find(
                'first',
                [
                    'conditions' => ['_id' => $transaction['StripeCharge']['metadata']['event_id']],
                    'fields' => ['name'],
                ]
            )['Event']['name'];
        } elseif (!empty($transaction['StripeCharge']['metadata']['membership_id'])) {
            $transaction['StripeCharge']['description'] = $this->Membership->find(
                'first',
                [
                    'conditions' => ['_id' => $transaction['StripeCharge']['metadata']['membership_id']],
                    'fields' => ['name'],
                ]
            )['Membership']['name'];
        } elseif (isset($transaction['StripeCharge']['metadata']['product_id'])) {
            $transaction['StripeCharge']['description'] = $this->Product->find(
                'first',
                [
                    'conditions' => ['_id' => $transaction['StripeCharge']['metadata']['product_id']],
                    'fields' => ['name'],
                ]
            )['Product']['name'];
        } elseif ($glofoxEvent === \Glofox\Domain\Charges\Type::BOOK_COURSE) {
            $serviceName = null;
            if (isset($transaction['StripeCharge']['metadata']['course_id'])) {
                $serviceName = $this->Course->find(
                    'first',
                    [
                        'conditions' => ['_id' => $transaction['StripeCharge']['metadata']['course_id']],
                        'fields' => ['name'],
                    ]
                )['Course']['name'];
            }
            if ($serviceName) {
                $transaction['StripeCharge']['description'] = $serviceName;
            }
        } elseif (!empty($transaction['StripeCharge']['metadata']['plan_code'])) {
            $membership = $this->Membership->find(
                'first',
                [
                    'conditions' => ['plans.code' => $transaction['StripeCharge']['metadata']['plan_code']],
                    'fields' => ['plans.code', 'plans.name'],
                ]
            );
            if (!empty($membership) && !empty($membership['Membership']['plans'])) {
                foreach ($membership['Membership']['plans'] as $plan) {
                    if ($plan['code'] == $transaction['StripeCharge']['metadata']['plan_code']) {
                        $transaction['StripeCharge']['description'] = $plan['name'];
                        break;
                    }
                }
            }
        } elseif ('book_time_slot' == $transaction['StripeCharge']['metadata']['glofox_event']) {
            if (
                isset($transaction['StripeCharge']['metadata']['model']) &&
                'users' == $transaction['StripeCharge']['metadata']['model']
            ) {
                if (isset($transaction['StripeCharge']['metadata']['model_id'])) {
                    $transaction['StripeCharge']['description'] = $this->User->find(
                        'first',
                        [
                            'conditions' => ['_id' => $transaction['StripeCharge']['metadata']['model_id']],
                            'fields' => ['name'],
                        ]
                    )['User']['name'];
                }
            }
        } elseif ('custom_charge' == $transaction['StripeCharge']['metadata']['glofox_event']) {
            $transaction['StripeCharge']['description'] = $transaction['StripeCharge']['metadata']['description'] ?? 'Custom Charge';
        } elseif ('subscription_payment' == $transaction['StripeCharge']['metadata']['glofox_event']) {
            $transaction['StripeCharge']['description'] = 'Subscription Membership';
        } elseif ('wallet_top_up' == $transaction['StripeCharge']['metadata']['glofox_event']) {
            $transaction['StripeCharge']['description'] = $transaction['StripeCharge']['metadata']['description'] ?? 'Wallet Top Up';
            $transaction['StripeCharge']['metadata']['balance'] = -(isset($transaction['StripeCharge']['metadata']['amount']) && $transaction['StripeCharge']['metadata']['amount'] > 0 ? $transaction['StripeCharge']['metadata']['amount'] : $transaction['StripeCharge']['amount']);
            $transaction['StripeCharge']['amount'] = isset($transaction['StripeCharge']['metadata']['amount']) && $transaction['StripeCharge']['metadata']['amount'] > 0 ? $transaction['StripeCharge']['metadata']['amount'] : $transaction['StripeCharge']['amount'];;
        } elseif ('imported_historic_charge' == $transaction['StripeCharge']['metadata']['glofox_event']) {
            $transaction['StripeCharge']['description'] = $transaction['StripeCharge']['metadata']['description'] ?? 'Historic Imported Charge';
        } elseif ('service_prepaid_payment' == $transaction['StripeCharge']['metadata']['glofox_event'] || 'service_upfront_payment' == $transaction['StripeCharge']['metadata']['glofox_event']) {
            if (!empty($transaction['StripeCharge']['metadata']['services'])) {
                $addon = $this->addonsHttpService->getAddon(
                    $transaction['StripeCharge']['metadata']['services'][0]['service_id'],
                    $transaction['StripeCharge']['metadata']['branch_id']
                );
                $transaction['StripeCharge']['description'] = isset($addon) ? $addon->serviceDefinitionName() : '---';
            }
        }

        if (PaymentMethods::WALLET === strtolower($transaction['StripeCharge']['metadata']['payment_method'])) {
            $transaction['StripeCharge']['metadata']['balance'] = isset($transaction['StripeCharge']['metadata']['amount']) && $transaction['StripeCharge']['metadata']['amount'] > 0 ? $transaction['StripeCharge']['metadata']['amount'] : $transaction['StripeCharge']['amount'];
            $transaction['StripeCharge']['amount'] = 0;
        }

        $transaction['StripeCharge']['taxes'] ??= null;
        if ($transaction['StripeCharge']['status'] == 'failed') {
            $transaction['StripeCharge']['taxes'] = 0;
        }

        return $transaction;
    }

    private function defaultStrikesReport($data)
    {
        $result = [];

        $branch_strike_system = $this->Branch->find(
            'first',
            [
                'conditions' => [
                    '_id' => $data['branch_id'],
                    'namespace' => $data['namespace'],
                    'active' => true,
                ],
                'fields' => ['features.booking.strike_system'],
            ]
        );

        if (!empty($branch_strike_system) && true === $branch_strike_system['Branch']['features']['booking']['strike_system']['enabled']) {
            $conditions = [
                'branch_id' => $data['branch_id'],
                'namespace' => $data['namespace'],
                'active' => true,
                'type' => 'MEMBER',
                'strike' => ['$gt' => 0],
            ];
            $fields = ['name', 'email', 'phone', 'strike', 'parent_id'];

            $members = $this->User->find(
                'all',
                [
                    'conditions' => $conditions,
                    'fields' => $fields,
                ]
            );
        }

        $formatter = new \Glofox\Domain\Users\Formatters\LegacyChildResponseFormatter();

        $result['Strikes']['details'] = array_map(fn($e) => $formatter->format($e['User']), $members);
        $result['Strikes']['header'] = 'Strikes';

        return $result;
    }
}
