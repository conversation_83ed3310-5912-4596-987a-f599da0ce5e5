{"name": "glofox/api", "description": "Glofox Booking API", "type": "project", "keywords": [], "homepage": "https://api.glofox.com/", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "7.4.*", "ext-json": "*", "ext-intl": "*", "ext-mongodb": "*", "wrep/notificato": "^1.1", "emarref/jwt": "dev-unicode", "ramsey/array_column": "^1.1", "zendesk/zendesk_api_client_php": "2.2.11", "ralouphie/getallheaders": "^2.0", "rlanvin/php-rrule": "^2.0", "mongodb/mongodb": "1.9.0", "cakephp/cakephp": "^2.10.24", "glofox/cakephp-mongodb": "1.1.3", "gianksp/cakephp-swagger": "dev-master", "gianksp/cakephp-amazon-s3": "dev-master", "dompdf/dompdf": "^3.0.0", "alcaeus/mongo-php-adapter": "1.2.4", "league/event": "2.1.2", "illuminate/http": "^5.4", "illuminate/events": "^5.4", "illuminate/support": "^5.4", "illuminate/validation": "5.4", "illuminate/routing": "^5.4", "illuminate/console": "^5.4", "illuminate/translation": "^5.4", "illuminate/config": "5.4", "illuminate/view": "^5.4", "league/csv": "^8.0", "nesbot/carbon": "^1.22", "marc-mabe/php-enum": "^3.0", "illuminate/container": "^5.4", "google/protobuf": "v3.6.1.3", "grpc/grpc": "v1.6.0", "guzzlehttp/guzzle": "^6.3", "ramsey/uuid": "^3.7", "nicmart/string-template": "~0.1", "saritasa/laravel-fluent-validation": "^1.0", "webmozart/expression": "^1.0", "league/fractal": "^0.17.0", "illuminate/auth": "^5.4", "moneyphp/money": "^3.1", "eluceo/ical": "^0.14.0", "sentry/sentry": "^3.21", "league/iso3166": "^4.3", "intervention/image": "^2.4", "cakephp/monolog": "^2.0", "league/flysystem-aws-s3-v3": "^1.0", "aws/aws-sdk-php": "^3.91", "glofoxinc/eventkit-php": "1.2.19", "glofoxinc/message-libs-php": "v0.0.89", "predis/predis": "^1.1", "giggsey/libphonenumber-for-php": "^8.10", "sabre/vobject": "4.1", "mixpanel/mixpanel-php": "^2.8", "spatie/url-signer": "^1.2", "psr/http-message": "^1.0", "launchdarkly/server-sdk": "^4.0", "kevinrob/guzzle-cache-middleware": "^1.4.0", "tgalopin/html-sanitizer": "^1.5", "ext-openssl": "*", "php-http/guzzle6-adapter": "*", "http-interop/http-factory-guzzle": "*", "propaganistas/laravel-phone": "4.0.3"}, "require-dev": {"phpunit/phpunit": "5.7.27", "phpunit/php-token-stream": "1.4.12", "codacy/coverage": "dev-master", "brainmaestro/composer-git-hooks": "^2.4", "brunty/cigar": "^1.8", "filp/whoops": "^2.2", "mockery/mockery": "^1.1", "squizlabs/php_codesniffer": "^3.3", "friendsofphp/php-cs-fixer": "v2.19.3", "justinrainbow/json-schema": "^5.2", "phpcompatibility/php-compatibility": "^9.3", "rector/rector": "^0.18.1", "composer/composer": "^2.2", "smalot/pdfparser": "*"}, "prefer-stable": true, "repositories": [{"type": "vcs", "url": "https://github.com/glofoxinc/jwt"}, {"type": "vcs", "url": "**************:glofoxinc/eventkit-php.git"}, {"type": "vcs", "url": "**************:glofoxinc/cakephp-mongodb.git"}, {"type": "vcs", "url": "**************:glofoxinc/message-libs-php.git"}], "bin": ["lib/Cake/Console/cake"], "autoload": {"psr-0": {"": "src"}, "psr-4": {"": "app/Lib/ProtoBuffers", "Glofox\\": "./app/Glofox/", "CakeTestCases\\": "./app/Test/Case/"}, "files": ["app/Utility/ObjectCollection.php"]}, "config": {"process-timeout": 0, "allow-plugins": {"composer/installers": true, "kylekatarnls/update-helper": true, "php-http/discovery": true}}, "extra": {"hooks": {"prepare-commit-msg": "./contrib/hooks/prepare-commit-msg.sh $1 $2", "pre-commit": "./contrib/hooks/pre-commit.sh", "pre-push": "./contrib/hooks/pre-push.sh"}, "installer-paths": {"app/Plugin/AmazonS3": ["gianksp/cakephp-amazon-s3"], "app/Plugin/Mongodb": ["glofox/cakephp-mongodb"], "app/Plugin/Swagger": ["gianksp/cakephp-swagger"], "app/Plugin/Monolog": ["cakephp/monolog"]}}, "scripts": {"post-install-cmd": ["\"vendor/bin/phpcs\" --config-set installed_paths vendor/phpcompatibility/php-compatibility", "./contrib/scripts/post-install.sh"], "post-update-cmd": ["\"vendor/bin/phpcs\" --config-set installed_paths vendor/phpcompatibility/php-compatibility", "./contrib/scripts/post-update.sh"], "test": ["./contrib/scripts/test.sh"], "docs": ["./contrib/scripts/docs/concatenate-blueprints.sh", "./contrib/scripts/docs/lint-blueprints.sh", "./contrib/scripts/docs/generate-preview-html.sh", "./contrib/scripts/docs/remove-temporary-blueprints.sh"]}}