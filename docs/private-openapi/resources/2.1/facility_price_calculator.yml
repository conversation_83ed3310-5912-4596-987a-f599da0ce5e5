parameters:
  - schema:
      type: string
    name: branchId
    in: path
    required: true
  - schema:
      type: string
    name: facilityId
    in: path
    required: true
post:
  tags:
    - Facilities
  summary: ''
  operationId: calculate-price-of-facility-by-id
  description: This endpoint calculates the price of a facility for a specific member
  x-internal: true
  security:
    - coreapi_auth: [ ]
  requestBody:
    required: true
    content:
      application/json:
        schema:
          type: object
          properties: 
            member_id:
              type: string
            time_start:
              type: integer
          required:
            - member_id
            - time_start
  responses:
    '200':
      description: OK
      content:
        application/json:
          schema:
            type: object
            properties:
              success:
                type: boolean
              data:
                type: object
                properties:
                  credits:
                    type: integer
                  price:
                    type: integer
                  currency:
                    type: string
    '400':
      description: Bad request
      content:
        application/json:
          schema:
            $ref : "../../schemas/error.yml"
    '404':
      description: Not found
      content:
        application/json:
          schema:
            $ref: "../../schemas/error.yml"
