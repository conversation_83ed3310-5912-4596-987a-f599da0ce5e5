<?php

namespace Glofox\Datasource;

class MongoCollection
{
    protected $collection;

    protected static $version;

    /**
     * @var array
     */
    protected $aggregationOptions = [
        'allowDiskUse' => true,
        'cursor' => [
            'batchSize' => 1000
        ]
    ];

    private MongoMaxQueryDurationThrottler $mongoMaxQueryDurationThrottler;

    /**
     * MongoCollection constructor.
     * @param $collection
     */
    public function __construct($collection)
    {
        $this->collection = $collection;
        $this->mongoMaxQueryDurationThrottler = app()->make(MongoMaxQueryDurationThrottler::class);
    }

    /**
     * @return float
     */
    protected function version()
    {
        if (!self::$version) {
            $mongoConfiguration = $this->collection->db->command(['buildinfo'=>true]);
            $version = (float)($mongoConfiguration['version']);
            self::$version = $version;
        }

        return self::$version;
    }

    /**
     * @return bool
     */
    protected function isCompatible()
    {
        return $this->version() >= 3.4;
    }

    /**
     * @param $options
     */
    public function setAggreagationOptions($options)
    {
        $this->aggregationOptions = $options;
    }

    /**
     * @param array $pipeline
     * @param array $options
     * @return array
     */
    public function aggregation(
        array $pipeline,
        array $options = []
    ) {
        if (!$this->isCompatible()) {
            $results = $this->collection->aggregate($pipeline);
            return $results['result'];
        }

        // @see https://stackoverflow.com/questions/47569871/aggregatecursor-issue-with-mongodb-3-6
        // This the worst yet only approach to be made in the case of the cakephp-mongodb driver.
        // The adoption of a better driver is suggested.
        ini_set('mongo.native_long', false);
        ini_set('mongo.long_as_object', true);

        if (empty($options)) {
            $options = $this->aggregationOptions;
        }

        $options = $this->mongoMaxQueryDurationThrottler->apply($options);
        $result = $this->collection->aggregateCursor($pipeline, $options);
        $result = iterator_to_array($result);

        // We need to revert this setting otherwise any integer value will be fetched as MongoInt64 objects that can't
        // be casted to int, generating a number of errors across the entire application.
        ini_set('mongo.native_long', true);
        ini_set('mongo.long_as_object', false);

        // @see https://github.com/jenssegers/laravel-mongodb/issues/324
        array_walk_recursive($result, [$this, 'convertMongoInt64ToInteger']);

        return $result;
    }

    /**
     * @param $item
     */
    public function convertMongoInt64ToInteger(&$item)
    {
        // @see http://php.net/manual/en/function.array-walk-recursive.php#106146
        if ($item instanceof \MongoInt64) {
            $item = $item->value;
        }
    }

    /**
     * @param $name
     * @param $arguments
     * @return mixed
     */
    public function __call($name, $arguments)
    {
        if (!method_exists($this->collection, $name)) {
            return false;
        }

        return call_user_func_array([$this->collection, $name], $arguments);
    }
}
