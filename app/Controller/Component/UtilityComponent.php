<?php

/**
 * Class UtilityComponent.
 */
class UtilityComponent extends Component
{
    public $components = ['S3'];

    /**
     * @param        $from
     * @param        $to
     * @param        $subject
     * @param        $message
     * @param string $name
     */
    public function sendHTMLmail($from, $to, $subject, $message, $name = '')
    {
        $headers = 'MIME-Version: 1.0' . "\r\n";
        $headers .= 'Content-type: text/html; charset=iso-8859-1' . "\r\n";
        $headers .= 'From: ' . $from . "\n";
        mail($to, $subject, $message, $headers);
    }

    /**
     * @param $messageBody
     *
     * @return string
     */
    public function create_email_body($messageBody)
    {
        $message = '<html><head><title></title></head>'
            . '<body style="text-align:center">'
            . '<div style="width:600px;  border:#0099FF 2px solid; float:left">'
            . '<div style="float:left; width:596px; text-align:left;">'
            . '</div>'
            . '<div style="float:left; width:596px; text-align:center; padding:5px;">'
            . '<div style="text-align:left;padding-left:20px; width:100%;float:left;" >';
        $message .= $messageBody;
        $message .= '<br>'
            . '</div>'
            . '</div>'
            . '</div>'
            . '</body>'
            . '</html>';

        return $message;
    }

    /*plain email*/

    /**
     * @param $messageBody
     *
     * @return string
     */
    public function create_plain_email_body($messageBody)
    {
        $message = $messageBody;
        $message .= 'brandiruas.vishnu';

        return $message;
    }

    /*plain email ends here*/

    /**
     * @param        $file_array
     * @param        $directory
     * @param string $filename
     * @param bool $is_webroot
     *
     * @return array
     */
    public function upload_file($file_array, $directory, $filename = '', $is_webroot = false)
    {
        if (!is_dir((string)$directory) && !mkdir($directory) && !is_dir($directory)) {
            throw new \RuntimeException(sprintf('Directory "%s" was not created', $directory));
        }

        if (false == $is_webroot) {// this is needed for uploading to the Vendor folder. Itmean the upload link must be set in the controller
            $directory = str_replace('./', '', $directory);
            $directory = WWW_ROOT . $directory;
        }

        if (is_uploaded_file($file_array['tmp_name']) && '' != trim($file_array['name'])) {
            $file_size = $file_array['size'];
            $file_name_split = explode('.', $file_array['name']);
            $type_arr = explode('/', $file_array['type']);
            $file_array['type'] = $type_arr[0];
            $file_array['extension'] = $type_arr[1];

            $file_array['new_name'] = (!empty($filename)) ? $filename : 'default.png';

            if ('exe' != $file_array['extension'] && 'bat' != $file_array['extension'] && 'js' != $file_array['extension']) {
                if (move_uploaded_file($file_array['tmp_name'], $directory . $file_array['new_name'])) {
                    ImageTool::autorotate([
                        'input' => $directory . $file_array['new_name'],
                        'output' => $directory . $file_array['new_name'],
                    ]);

                    return $file_array;
                }
            }
        }

        return [];
    }

    /**
     * @param        $namespace
     * @param string $branch_id
     * @param string $feature
     * @param string $id
     *
     * @return string
     */
    public function check_folder_structure($namespace, $branch_id = '', $feature = '', $id = '')
    {
        $base_path = './img/' . $namespace . '/';
        $branch_path = $base_path . 'branches/' . $branch_id . '/';
        $files_path = $base_path;

        if (!empty($branch_id)) {
            $files_path = $branch_path;
        }

        if (!empty($feature)) {
            if ('users' == $feature) {
                $files_path = $branch_path . $feature . '/';
            } else {
                $files_path = $branch_path . $feature . '/' . $id . '/';
            }
        }

        if (!is_dir($files_path) && !mkdir($files_path, 0777, true) && !is_dir($files_path)) {
            throw new \RuntimeException(sprintf('Directory "%s" was not created', $files_path));
        }

        return $files_path;
    }

    /**
     * @param $str
     *
     * @return string
     */
    public function cleanPosUrl($str)
    {
        return stripslashes($str);
    }

    /**
     * @return string
     */
    public function GetBrowser()
    {
        $browserinfo = '';
        $useragent_am = stristr($_SERVER['HTTP_USER_AGENT'], 'amaya');
        $useragent_cb = stristr($_SERVER['HTTP_USER_AGENT'], 'Crazy Browser');
        $useragent_mx = stristr($_SERVER['HTTP_USER_AGENT'], 'Maxthon');
        $useragent_ab = stristr($_SERVER['HTTP_USER_AGENT'], 'Avant Browser');
        $useragent_fl = stristr($_SERVER['HTTP_USER_AGENT'], 'Flock');
        $useragent_ff = stristr($_SERVER['HTTP_USER_AGENT'], 'Firefox');
        $useragent_ns = stristr($_SERVER['HTTP_USER_AGENT'], 'Netscape');
        $useragent_sa = stristr($_SERVER['HTTP_USER_AGENT'], 'Safari');
        $useragent_ga = stristr($_SERVER['HTTP_USER_AGENT'], 'Galeon');
        $useragent_kq = stristr($_SERVER['HTTP_USER_AGENT'], 'Konqueror');
        $useragent_lx = stristr($_SERVER['HTTP_USER_AGENT'], 'Lynx');
        $useragent_op = stristr($_SERVER['HTTP_USER_AGENT'], 'Opera');
        $useragent_ie = stristr($_SERVER['HTTP_USER_AGENT'], 'MSIE');

        // IE and FireFox are last in the list because some browsers will have
        // them listed in their UA strings before their browsers version.
        if ($useragent_am) {
            $browserinfo = 'am';
        } elseif ($useragent_cb) {
            $browserinfo = 'cb';
        } elseif ($useragent_mx) {
            $browserinfo = 'mx';
        } elseif ($useragent_ab) {
            $browserinfo = 'ab';
        } elseif ($useragent_fl) {
            $browserinfo = 'fl';
        } elseif ($useragent_ns) {
            $browserinfo = 'ns';
        } elseif ($useragent_sa) {
            $browserinfo = 'sa';
        } elseif ($useragent_ga) {
            $browserinfo = 'ga';
        } elseif ($useragent_kq) {
            $browserinfo = 'kq';
        } elseif ($useragent_lx) {
            $browserinfo = 'lx';
        } elseif ($useragent_op) {
            $browserinfo = 'op';
        } elseif ($useragent_ff) {
            $browserinfo = 'ff';
        } elseif ($useragent_ie) {
            $browserinfo = 'ie';
        } else {
            $browserinfo = '';
        }

        return $browserinfo;
    }

    // call createThumb function and pass to it as parameters the path
    // to the directory that contains images, the path to the directory
    // in which thumbnails will be placed and the thumbnail's width.
    // We are assuming that the path will be a relative path working
    // both in the filesystem, and through the web for links
    //createThumbs("upload/","upload/thumbs/",100);
    //createThumbs("upload/","upload/thumbs/",400);

    /**
     * @param $pathToImages
     * @param $pathToThumbs
     * @param $thumbWidth
     */
    public function createImageThumbs($pathToImages, $pathToThumbs, $thumbWidth)
    {
        $thumbsFolder = $this->cut_string_using_last('/', $pathToThumbs, 'left', false);
        if (!is_dir($thumbsFolder) && !mkdir($thumbsFolder) && !is_dir($thumbsFolder)) {
            throw new \RuntimeException(sprintf('Directory "%s" was not created', $thumbsFolder));
        }

        // parse path for the extension
        $info = pathinfo($pathToImages);

        if (isset($info['extension'])) {
            // continue only if this is a JPEG image
            switch (strtolower($info['extension'])) {
                case 'jpg':
                case 'jpeg':
                    // load image and get image size
                    $img = imagecreatefromjpeg("$pathToImages");
                    $width = imagesx($img);
                    $height = imagesy($img);
                    // calculate thumbnail size
                    if ($thumbWidth < $width) {
                        $new_width = $thumbWidth;
                        $new_height = floor($height * ($thumbWidth / $width));
                    } else {
                        $new_width = $width;
                        $new_height = $height;
                    }
                    // create a new temporary image
                    $tmp_img = imagecreatetruecolor($new_width, $new_height);

                    // copy and resize old image into new image
                    imagecopyresized($tmp_img, $img, 0, 0, 0, 0, $new_width, $new_height, $width, $height);

                    // save thumbnail into a file
                    imagejpeg($tmp_img, "$pathToThumbs");
                    break;
                case 'gif':
                    // load image and get image size
                    $img = imagecreatefromgif("$pathToImages");
                    $width = imagesx($img);
                    $height = imagesy($img);

                    // calculate thumbnail size
                    $new_width = $thumbWidth;
                    $new_height = floor($height * ($thumbWidth / $width));

                    // create a new temporary image
                    $tmp_img = imagecreatetruecolor($new_width, $new_height);

                    // copy and resize old image into new image
                    imagecopyresized($tmp_img, $img, 0, 0, 0, 0, $new_width, $new_height, $width, $height);

                    // save thumbnail into a file
                    imagegif($tmp_img, "$pathToThumbs");
                    break;
                case 'png':
                    // load image and get image size
                    $img = imagecreatefrompng($pathToImages);
                    $width = imagesx($img);
                    $height = imagesy($img);

                    // calculate thumbnail size
                    $new_width = $thumbWidth;
                    $new_height = floor($height * ($thumbWidth / $width));

                    // create a new temporary image
                    $tmp_img = imagecreatetruecolor($new_width, $new_height);
                    $background = imagecolorallocate($tmp_img, 0, 0, 0);
                    imagecolortransparent($tmp_img, $background);
                    imagealphablending($tmp_img, false);
                    imagesavealpha($tmp_img, true);
                    // copy and resize old image into new image
                    imagecopyresized($tmp_img, $img, 0, 0, 0, 0, $new_width, $new_height, $width, $height);

                    // save thumbnail into a file

                    imagepng($tmp_img, "$pathToThumbs");
                    break;
            }
            unset($tmp_img);
            unset($img);
        }
    }

    /**
     * Removes the preceeding or proceeding portion of a string
     * relative to the last occurrence of the specified character.
     * The character selected may be retained or discarded.
     *
     * Example usage:
     * <code>
     * $example = 'http://example.com/path/file.php';
     * $cwd_relative[] = cut_string_using_last('/', $example, 'left', true);
     * $cwd_relative[] = cut_string_using_last('/', $example, 'left', false);
     * $cwd_relative[] = cut_string_using_last('/', $example, 'right', true);
     * $cwd_relative[] = cut_string_using_last('/', $example, 'right', false);
     * foreach($cwd_relative as $string) {
     *     echo "$string <br>".PHP_EOL;
     * }
     * </code>
     *
     * Outputs:
     * <code>
     * http://example.com/path/
     * http://example.com/path
     * /file.php
     * file.php
     * </code>
     *
     * @param string $character the character to search for
     * @param string $string the string to search through
     * @param string $side determines whether text to the left or the right of the character is returned.
     *                               Options are: left, or right.
     * @param bool $keep_character determines whether or not to keep the character.
     *                               Options are: true, or false.
     *
     * @return string
     */
    public function cut_string_using_last($character, $string, $side, $keep_character = true)
    {
        $offset = ($keep_character ? 1 : 0);
        $whole_length = strlen($string);
        $right_length = (strlen(strrchr($string, $character)) - 1);
        $left_length = ($whole_length - $right_length - 1);
        switch ($side) {
            case 'left':
                $piece = substr($string, 0, ($left_length + $offset));
                break;
            case 'right':
                $start = (0 - ($right_length + $offset));
                $piece = substr($string, $start);
                break;
            default:
                $piece = false;
                break;
        }

        return $piece;
    }

    /**
     * @param      $records
     * @param      $field
     * @param bool $reverse
     *
     * @return array
     */
    public function record_sort($records, $field, $reverse = false)
    {
        $hash = [];

        foreach ($records as $record) {
            $hash[$record[$field]] = $record;
        }

        ($reverse) ? krsort($hash) : ksort($hash);

        $records = [];

        foreach ($hash as $record) {
            $records[] = $record;
        }

        return $records;
    }

    /**
     * @param        $date
     * @param bool $allow_empty
     * @param string $type
     *
     * @return array
     */
    public function validate_date_format($date, $allow_empty = false, $type = 'date')
    {
        if ('date' == $type) {
            $pattern = '/^[0-9]{4}-(0[1-9]|1[0-2])-(0[1-9]|[1-2][0-9]|3[0-1])$/';
            $format = 'YYYY-MM-DD';
        } elseif ('datetime' == $type) {
            $pattern = "/^(\d{4})-(\d{2})-(\d{2}) (\d{2}):(\d{2}):(\d{2})$/";
            $format = 'YYYY-MM-DD hh:mm:ss';
        }

        if (!$allow_empty) {
            if (empty($date)) {
                return ['success' => false, 'message' => 'The date is empty'];
            }
        }

        if ($allow_empty && empty($date)) {
            return ['success' => true];
        }

        if (preg_match($pattern, $date)) {
            return ['success' => true];
        } else {
            return ['success' => false, 'message' => 'Invalid Date Format. Should be ' . $format];
        }
    }

    /**
     * @param $id
     *
     * @return bool|MongoId
     */
    public function is_valid_mongoid($id)
    {
        try {
            if (!empty($id)) {
                return new MongoId($id);
            } else {
                false;
            }
        } catch (MongoException $ex) {
            return false;
        }
    }

    // Get the difference of hours given 2 timestamps

    /**
     * @param $time1
     * @param $time2
     *
     * @return float|int
     */
    public function get_hours_difference($time1, $time2)
    {
        return ($time1 - $time2) / 3600; // 3600 seconds in hour
    }

    /**
     * @param null $time_zone
     *
     * @return int
     */
    public function getCurrentTime($time_zone = null)
    {
        $time_zone = (null != $time_zone) ? $time_zone : 'UTC';
        $date = new DateTime(null, new DateTimeZone($time_zone));
        $current_time = $date->getTimestamp() + $date->getOffset();

        return $current_time;
    }

    /**
     * @param string $format
     *
     * @return false|string
     */
    public function getCurrentDate($format = 'Y-m-d H:i:s')
    {
        // Set TimeZome First Depending on Branch Address
        //date_default_timezone_set('Europe/Dublin');
        date_default_timezone_set('UTC');

        return date($format);
    }

    /**
     * @return array
     */
    public function getMonths()
    {
        return $months = [
            'January',
            'Febuary',
            'March',
            'April',
            'May',
            'June',
            'July',
            'August',
            'September',
            'October',
            'November',
            'December',
        ];
    }

    /**
     * @param $file_name
     * @param $path
     */
    public function delete_file($file_name, $path)
    {
        if (file_exists($path . $file_name)) {
            unlink($path . $file_name);
        }
    }

    /**
     * @param $file
     * @param $directory
     * @param $name
     * @param $key
     *
     * @return array
     */
    public function upload_pdf($file, $directory, $name, $key)
    {
        if (!is_dir((string)$directory) && !mkdir($directory) && !is_dir($directory)) {
            throw new \RuntimeException(sprintf('Directory "%s" was not created', $directory));
        }

        $filename = $name . '_' . $key . '.pdf';
        try {
            file_put_contents($directory . '/' . $filename, $file);
        } catch (Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }

        return ['success' => true, 'filename' => $filename];
    }

    // Check if a remote file exists

    /**
     * @param $url
     *
     * @return bool
     */
    public function checkRemoteFile($url)
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        // don't download content
        curl_setopt($ch, CURLOPT_NOBODY, 1);
        curl_setopt($ch, CURLOPT_FAILONERROR, 1);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        if (false !== curl_exec($ch)) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * [saveCropedImage description].
     *
     * @param $namespace
     * @param $branch_id
     * @param $feature
     * @param $id
     * @param $request_data
     *
     * @return array [type]               [description]
     *
     * @internal param $ [type] $namespace    [description]
     * @internal param $ [type] $branch_id    [description]
     * @internal param $ [type] $feature      [description]
     * @internal param $ [type] $id           [description]
     * @internal param $ [type] $request_data [description]
     */
    public function saveCropedImage($namespace, $branch_id, $feature, $id, $request_data)
    {
        // Save Image
        if (!empty($request_data['image_library'])) {
            $path = $this->check_folder_structure($namespace, $branch_id, $feature, $id);
            $imagePath = APP . 'webroot' . str_replace('./', '/', $path . 'default.png');
            if (file_exists($imagePath)) {
                $imagePathNew = APP . 'webroot' . str_replace('./', '/', $path . 'default' . time() . '.png');
                rename($imagePath, $imagePathNew);
            }
            copy(APP . 'webroot/' . $request_data['image_library'], $imagePath);
        } else {
            if (0 == $request_data['image_url']['error']) {
                $path = $this->check_folder_structure($namespace, $branch_id, $feature, $id);
                $file_array = $this->upload_file($request_data['image_url'], $path);
                //image crop
                if (isset($request_data['crop_x']) && 'true' == $request_data['crop_saved']) {
                    $imagePath = APP . 'webroot' . str_replace('./', '/', $path . 'default.png');
                    switch (exif_imagetype($imagePath)) {
                        case 2:
                            $src = imagecreatefromjpeg($imagePath);
                            $dest = imagecreatetruecolor($request_data['crop_w'], $request_data['crop_h']);

                            imagecopy(
                                $dest,
                                $src,
                                0,
                                0,
                                $request_data['crop_x'],
                                $request_data['crop_y'],
                                $request_data['crop_w'],
                                $request_data['crop_h']
                            );

                            header('Content-type: image/jpeg');
                            imagejpeg($dest, $imagePath);

                            imagedestroy($dest);
                            imagedestroy($src);
                            break;
                        case 3:
                            $src = imagecreatefrompng($imagePath);
                            $dest = imagecreatetruecolor($request_data['crop_w'], $request_data['crop_h']);
                            imagecopy(
                                $dest,
                                $src,
                                0,
                                0,
                                $request_data['crop_x'],
                                $request_data['crop_y'],
                                $request_data['crop_w'],
                                $request_data['crop_h']
                            );

                            header('Content-Type: image/png');
                            imagepng($dest, $imagePath);

                            imagedestroy($dest);
                            imagedestroy($src);
                            break;
                    }
                }
            }
        }

        return ['success' => true];
    }

    /**
     * @param array $array1
     * @param array $array2
     *
     * @return array
     */
    public function intersectKeysRecursively(array $array1, array $array2)
    {
        $array1 = array_intersect_key($array1, $array2);
        foreach ($array1 as $key => &$value) {
            if (is_array($value)) {
                $value = is_array($array2[$key]) ? $this->intersectKeysRecursively($value, $array2[$key]) : $value;
            }
        }

        return $array1;
    }

    /**
     * @param $format
     *
     * @return string
     */
    public function convertMomentJsDateFormatToPhpFormat($format)
    {
        return momentJsToPhpFormat($format);
    }
}
