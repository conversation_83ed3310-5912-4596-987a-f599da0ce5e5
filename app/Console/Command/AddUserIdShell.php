<?php

declare(strict_types=1);

use Glofox\Domain\Users\Models\User;
use Glofox\Domain\Users\Repositories\UsersRepository;
use Glofox\Domain\Users\Search\Expressions\UserId;
use Illuminate\Support\Collection;
use User as CakeUserModel;

class AddUserIdShell extends Shell
{
    public function main(): void
    {
        $users = $this->fetchUsers();

        if ($users->isEmpty()) {
            $this->log('All users already have user_id field.', LOG_INFO);

            return;
        }

        $this->addUserIdField($users);

        $message = sprintf('user_id field added for users: %s', $users->implode('_id', ','));
        $this->log($message, LOG_INFO);
    }

    private function fetchUsers(): Collection
    {
        /** @var UsersRepository $usersRepository */
        $usersRepository = app()->make(UsersRepository::class);

        $users = $usersRepository
            ->addCriteria(new UserId(null))
            ->find();

        return new Collection($users);
    }

    private function addUserIdField(Collection $users): void
    {
        /** @var CakeUserModel $userCakeModel */
        $userCakeModel = \ClassRegistry::init('User');

        $users->each(function (User $user) use ($userCakeModel) {
            $userCakeModel->id = $user->id();
            $userCakeModel->saveField(
                'user_id', $user->id(), ['callbacks' => false]
            );
        });
    }
}
