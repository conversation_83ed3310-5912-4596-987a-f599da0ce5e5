; This file contains the various settings for the New Relic PHP agent. There
; are many options, all of which are described in detail at the following URL:
; https://docs.newrelic.com/docs/agents/php-agent/configuration/php-agent-configuration
;

; If you use a full path to the extension you insulate yourself from the
; extension directory changing if you change PHP installations or versions.
; If you do not use an absolute path then the file must be installed in the
; active configuration's extension directory.
extension=newrelic.so

[newrelic]
; Setting: newrelic.enabled
; Type   : boolean
; Scope  : per-directory
; Default: true
; Info   : Enable or disable the agent. Please note that you cannot globally
;          disable the agent and then selectively enable it on a per-directory
;          basis. If you disable the agent in the global INI file then the
;          agent will not initialize at all. However, you can selectively
;          disable the agent on a per-directory basis.
;
;newrelic.enabled = true

; Setting: newrelic.license
; Type   : string
; Scope  : per-directory
; Default: none
; Info   : Sets the New Relic license key to use. This can vary from directory
;          to directory if you are running a multi-tenant system. By special
;          dispensation if you upgraded from a previous version of the agent
;          where the license key was set in the daemon, the installation and
;          upgrade script will have preserved your license key from the file
;          /etc/newrelic/newrelic.cfg, but ONLY if you installed via rpm/yum
;          or dpkg. The key is saved in /etc/newrelic/upgrade_please.key
;          and the agent will look for that file if you do not specify a valid
;          license here.
;          It is *STRONGLY* recommended that you set the license key in your
;          INI file(s) and do not rely on the key file being present. Also
;          please note that even if you are not letting the agent start the
;          daemon and are still using newrelic.cfg (see below) the license
;          keyword in that file is no longer obeyed. Instead the agent will
;          use the preserved value of that license from the key file.
;          Once you have updated your INI files to contain the license we
;          urge you to remove /etc/newrelic/upgrade_please.key in order to
;          eliminate the potential for confusion about exactly where the key
;          is coming from.
;
newrelic.license = ""

; Setting: newrelic.logfile
; Type   : string
; Scope  : system
; Default: none
; Info   : Sets the name of the file to send log messages to.
;
newrelic.logfile = "/var/log/newrelic/php_agent.log"

; Setting: newrelic.loglevel
; Type   : string
; Scope  : system
; Default: "info"
; Info   : Sets the level of detail to include in the log file. You should
;          rarely need to change this from the default, and usually only under
;          the guidance of technical support.
;          Must be one of the following values:
;            always, error, warning, info, verbose, debug, verbosedebug
;
;newrelic.loglevel = "info"

; Setting: newrelic.high_security
; Type   : boolean
; Scope  : system
; Default: false
; Info   : Enables high security for all applications. When high security is
;          enabled, the following behavior will take effect:
;          * Data will not be sent to New Relic unless the newrelic-daemon
;            is using SSL. If the PHP process spawns a newrelic-daemon, it
;            will be configured to use SSL regardless of the value of
;            ```newrelic.daemon.ssl```.
;          * Raw SQL strings will never be gathered, regardless of the value of
;            newrelic.transaction_tracer.record_sql.
;          * Request parameters will never be captured, regardless of the
;            newrelic.attributes configuration settings.
;          * The following API functions will have no effect, and will return
;            false:
;            newrelic_add_custom_parameter
;            newrelic_set_user_attributes
;            newrelic_record_custom_event
;
;          IMPORTANT:  If you change this setting, you must also change the RPM
;          UI security setting. If the two settings do not match, then no data
;          will be collected.
;
;newrelic.high_security = false

; Setting: newrelic.appname
; Type   : string
; Scope  : per-directory
; Default: "PHP Application"
; Info   : Sets the name of the application that metrics will be reported into.
;          This can in fact be a list of up to 3 application names, each of
;          which must be separated by a semi-colon. The first name in any such
;          list is considered the 'primary' application name and must be unique
;          for each account / license key.
;
newrelic.appname = ""

; Setting: newrelic.process_host.display_name
; Type   : string
; Scope  : system
; Default: none
; Info   : Sets a custom display name for your application server in the New
;          Relic UI. Servers are normally identified by host and port number.
;          This setting allows you to give your hosts more recognizable names.
;
;newrelic.process_host.display_name = ""

;
; Beginning with version 3.0 of the agent, the daemon can be automatically
; started by the agent. There is no need to start the daemon before starting
; Apache or PHP-FPM. All of the newrelic.daemon.* settings are options that
; control the behavior of the daemon. These settings are converted into the
; appropriate command line options when the agent starts the daemon. This is
; now the preferred method of starting the daemon. There are still usage cases
; (such as using a single daemon for serving multiple Apache instances) where
; you may want to start the daemon via it's init script, but for most users,
; this is the best place to configure and start the daemon.
;
; The agent will only launch the daemon if one isn't already running. Also
; note that the agent will NOT stop the daemon once it has started. If you
; want control over exactly when the daemon starts and stops you can still
; achieve that by creating a daemon configuration file (located by default at
; /etc/newrelic/newrelic.cfg) and running the chkconfig or equivalent command.
; Please see the newrelic.cfg template file for details. That template file
; is located at /usr/lib/newrelic-php5/scripts/newrelic.cfg.template.
;
; Also please note that the options here and in newrelic.cfg are identical,
; except that in this file they are preceded with "newrelic.daemon.".
;

; Setting: newrelic.daemon.logfile
; Type   : string
; Scope  : system
; Default: none
; Info   : Sets the name of the file to send daemon log messages to.
;
newrelic.daemon.logfile = "/var/log/newrelic/newrelic-daemon.log"

; Setting: newrelic.daemon.loglevel
; Type   : string
; Scope  : system
; Default: "info"
; Info   : Sets the level of detail to include in the daemon log. You should
;          rarely need to change this from the default, and usually only under
;          the guidance of technical support.
;          Must be one of the following values:
;            always, error, warning, info, debug
;
;          The values verbose and verbosedebug are deprecated aliases for debug.
;
;newrelic.daemon.loglevel = "info"

; Setting: newrelic.daemon.port
; Type   : string or integer
; Scope  : system
; Default: /tmp/.newrelic.sock
; Info   : Sets how the agent and daemon communicate. How this is set can impact
;          performance. The default is to use a UNIX-domain socket located at
;          /tmp/.newrelic.sock. If you want to use UNIX domain sockets then
;          this value must begin with a "/". If you set this to an integer
;          value in the range 1-65534, then this will instruct the agent to use
;          a normal TCP socket on the port specified. This may be easier to use
;          if you are using a chroot environment. On Linux, an abstract socket
;          can be created by prefixing the socket name with '@'. Support for
;          abstract sockets was added in PHP agent version 5.2.
;
;newrelic.daemon.port = "/tmp/.newrelic.sock"

; Setting: newrelic.daemon.ssl
; Type   : boolean
; Scope  : system
; Default: true (as of version 3.6)
; Info   : Sets whether or not communication with New Relic data collectors
;          should use a secure HTTP connection or not.
;
;newrelic.daemon.ssl = true

; Setting: newrelic.daemon.ssl_ca_bundle
; Type   : string
; Scope  : system
; Default: none
; Info   : Sets the location of a file containing CA certificates in PEM
;          format. When set, the certificates in this file will be used to
;          authenticate the New Relic collector servers. If
;          newrelic.daemon.ssl_ca_path is also set (see below), the
;          certificates in this file will be searched first, followed by the
;          certificates contained in the newrelic.daemon.ssl_ca_path
;          directory. This setting has no effect when newrelic.daemon.ssl is
;          set to false.
;
;newrelic.daemon.ssl_ca_bundle = ""

; Setting: newrelic.daemon.ssl_ca_path
; Type   : string
; Scope  : system
; Default: none
; Info   : Sets the location of a directory containing trusted CA certificates
;          in PEM format. When set, the certificates in this directory will be
;          used to authenticate the New Relic collector servers. If
;          newrelic.daemon.ssl_ca_bundle is also set (see above), it will be
;          searched first followed by the certificates contained in
;          newrelic.daemon.ssl_ca_path. This setting has no effect when
;          newrelic.daemon.ssl is set to false.
;
;newrelic.daemon.ssl_ca_path = ""

; Setting: newrelic.daemon.proxy
; Type   : string
; Scope  : system
; Default: none
; Info   : Sets the host and user credentials to use as an egress proxy. This
;          is only used if your site requires a proxy in order to access
;          external servers on the internet, in this case the New Relic data
;          collection servers. This is expressed in one of the following forms:
;             hostname
;             hostname:port
;             user@hostname
;             user@hostname:port
;             user:password@hostname
;             user:password@hostname:port
;
;newrelic.daemon.proxy = ""

; Setting: newrelic.daemon.pidfile
; Type   : string
; Scope  : system
; Default: OS dependent
; Info   : Sets the name of the file to store the running daemon's process ID
;          (PID) in. This file is used by the daemon startup and shutdown
;          script to determine whether or not the daemon is already running.
;
;newrelic.daemon.pidfile = ""

; Setting: newrelic.daemon.location
; Type   : string
; Scope  : system
; Default: /usr/bin/newrelic-daemon
; Info   : Sets the name of the daemon executable to launch.
;          Please note that on OpenSolaris where /usr is frequently a read-only
;          file system, the default daemon location is
;          /opt/newrelic/bin/newrelic-daemon.
;
newrelic.daemon.location = "/opt/newrelic/daemon/newrelic-daemon.x64"

; Setting: newrelic.daemon.collector_host
; Type   : string
; Scope  : system
; Default: collector.newrelic.com
; Info   : Sets the host name of the New Relic data collector host to use.
;          Please note that this is NOT any form of local host. It refers to
;          the New Relic provided host. There is very little reason to ever
;          change this from the default except in certain very special
;          circumstances, and then only on instruction from a New Relic sales
;          person or support staff member.
;
;newrelic.daemon.collector_host = "collector.newrelic.com"

; Setting: newrelic.daemon.dont_launch
; Type   : integer (0, 1, 2 or 3)
; Scope  : system
; Default: 0
; Info   : If you prefer to have the daemon launched externally before the
;          agent starts up, set this variable to non-zero. The value you
;          choose determines exactly when the agent is allowed to start the
;          daemon:
;          0 - agent can start the daemon any time it needs to
;          1 - non-CLI (i.e Apache / php-fpm) agents can start the daemon
;          2 - only CLI agents can start the daemon
;          3 - the agent will never start the daemon
;
;newrelic.daemon.dont_launch = 0

; Setting: newrelic.daemon.utilization.detect_aws
; Type   : boolean
; Scope  : system
; Default: true
; Info   : Enable detection of whether the system is running on AWS. This will
;          create a small amount of network traffic on daemon startup.
;
;newrelic.daemon.utilization.detect_aws = true

; Setting: newrelic.daemon.utilization.detect_docker
; Type   : boolean
; Scope  : system
; Default: true
; Info   : Enable detection of a system running on Docker. This will be used
;          to support future features.
;
;newrelic.daemon.utilization.detect_docker = true

; Setting: newrelic.daemon.app_timeout
; Type   : time specification string ("5m", "1h20m", etc)
; Scope  : system
; Default: 10m
; Info   : Sets the elapsed time after which an application will be considered
;          inactive. Inactive applications do not count against the maximum
;          limit of 250 applications. Allowed units are "ns", "us", "ms", "s",
;          "m", and "h".
;
;newrelic.daemon.app_timeout = 10m

; Setting: newrelic.error_collector.enabled
; Type   : boolean
; Scope  : per-directory
; Default: true
; Info   : Enable the New Relic error collector. This will record the 20 most
;          severe errors per harvest cycle. It is rare to want to disable this.
;          Please also note that your New Relic subscription level may force
;          this to be disabled regardless of any value you set for it.
;
;newrelic.error_collector.enabled = true

; Setting: newrelic.error_collector.ignore_user_exception_handler
; Type   : boolean
; Scope  : per-directory
; Default: false
; Info   : If enabled, the New Relic error collector will ignore any exceptions
;          that are handled by an exception handler installed with
;          set_exception_handler().
;
;          If an exception handler has not been installed, this setting will
;          have no effect, as PHP will turn the uncaught exception into a fatal
;          error and it will be handled accordingly by the New Relic error
;          collector.
;
;newrelic.error_collector.ignore_user_exception_handler = false

; Setting: newrelic.error_collector.ignore_exceptions
; Type:    string
; Scope:   per-directory
; Default: none
; Info:    A comma separated list of exception classes that the agent should
;          ignore. When an unhandled exception occurs, the agent will perform
;          the equivalent of `$exception instanceof Class` for each of the
;          classes listed. If any of those checks returns true, the agent
;          will not record an error.
;
;          Please note that this setting only applies to uncaught exceptions.
;          Exceptions recorded using the newrelic_notice_error API are not
;          subject to filtering.
;
;newrelic.error_collector.ignore_exceptions = ""

; Setting: newrelic.error_collector.ignore_errors
; Type:    string
; Scope:   per-directory
; Default: none
; Info:    Sets the error levels that the agent should ignore.
;
;          Please note that this setting does not apply to errors recorded
;          using the newrelic_notice_error API.
;
;newrelic.error_collector.ignore_errors = ""

; Setting: newrelic.error_collector.record_database_errors
; Type   : boolean
; Scope  : per-directory
; Default: false
; Info   : Currently only supported for MySQL database functions. If enabled,
;          this will cause errors returned by various MySQL functions to be
;          treated as if they were PHP errors, and thus subject to error
;          collection. This is only obeyed if the error collector is enabled
;          above and the account subscription level permits error trapping.
;
;newrelic.error_collector.record_database_errors = false

; Setting: newrelic.error_collector.prioritize_api_errors
; Type   : boolean
; Scope  : per-directory
; Default: false
; Info   : If the error collector is enabled and you use the New Relic API to
;          notice an error, if this is set to true then assign the highest
;          priority to such errors.
;
;newrelic.error_collector.prioritize_api_errors = false

; Setting: newrelic.browser_monitoring.auto_instrument
; Type   : boolean
; Scope  : per-directory
; Default: true
; Info   : Enables or disables automatic real user monitoring ("auto-RUM").
;          When enabled will cause the agent to insert a header and a footer
;          in HTML output that will time the actual end-user experience.
;
;newrelic.browser_monitoring.auto_instrument = true

; Setting: newrelic.transaction_tracer.enabled
; Type   : boolean
; Scope  : per-directory
; Default: true
; Info   : Enables or disables the transaction tracer. When enabled this will
;          produce a detailed call graph for any transaction that exceeds a
;          certain threshold (see next entry). Only one transaction trace per
;          application per harvest cycle is stored and it is always the slowest
;          transaction during that cycle. Transaction traces are extremely
;          useful when diagnosing problem spots in your application. Please
;          note that TT's may be disabled by your account subscription level
;          regardless of what you set here.
;
;newrelic.transaction_tracer.enabled = true

; Setting: newrelic.transaction_tracer.threshold
; Type   : string with a time specification or the word "apdex_f"
; Scope  : per-directory
; Default: "apdex_f"
; Info   : Specifies the threshold above which a transaction becomes a
;          candidate for the transaction tracer. This can either be an absolute
;          time value like "200ms" or "1s250ms" or "1h30m" or "750us" or the
;          word "apdex_f". This last value, "apdex_f", means "4 times apdex_t".
;          Thus the threshold changes according to your apdex_t setting. This
;          is the default.
;
;newrelic.transaction_tracer.threshold = "apdex_f"

; Setting: newrelic.transaction_tracer.detail
; Type   : integer in the range 0-1
; Scope  : per-directory
; Default: 1
; Info   : Sets the level of detail in a transaction trace. Setting this to 0
;          will only show the relatively few PHP functions that New Relic has
;          deemed to be "interesting", as well as any custom functions you set
;          (see below). A setting of 1 will trace and time all user functions.
;
;          In earlier releases of the agent this was known as "top100".
;
;newrelic.transaction_tracer.detail = 1

; Setting: newrelic.transaction_tracer.slow_sql
; Type   : boolean
; Scope  : per-directory
; Default: true
; Info   : Enables or disables the "slow SQL" tracer. When enabled, this will
;          record the top 10 slowest SQL calls along with a stack trace of
;          where the call occurred in your code.
;
;newrelic.transaction_tracer.slow_sql = true

; Setting: newrelic.transaction_tracer.stack_trace_threshold
; Type   : time specification string ("500ms", "1s750ms" etc)
; Scope  : per-directory
; Default: 500ms
; Info   : Sets the threshold above which the New Relic agent will record a
;          stack trace for a transaction trace.
;
;newrelic.transaction_tracer.stack_trace_threshold = 500

; Setting: newrelic.transaction_tracer.explain_enabled
; Type   : boolean
; Scope  : per-directory
; Default: true
; Info   : Enables or disables requesting "explain plans" from MySQL databases
;          accessed via MySQLi or PDO_MySQL for slow SQL calls. The threshold
;          for requesting explain plans is defined below.
;
;newrelic.transaction_tracer.explain_enabled = true

; Setting: newrelic.transaction_tracer.explain_threshold
; Type   : time specification string ("750ms", "1s 500ms" etc)
; Scope  : per-directory
; Default: 500ms
; Info   : Used by the slow SQL tracer to set the threshold above which an SQL
;          statement is considered "slow", and to set the threshold above which
;          the transaction tracer will request an "explain plan" from the data-
;          base for slow SQL. This latter feature may not be active yet, please
;          refer to the agent release notes to see when it becomes available.
;          Only relevant if explain_enabled above is set to true.
;
;newrelic.transaction_tracer.explain_threshold = 500

; Setting: newrelic.transaction_tracer.record_sql
; Type   : "off", "raw" or "obfuscated"
; Scope  : per-directory
; Default: "obfuscated"
; Info   : Sets how SQL statements are recorded (if at all). If this is set to
;          "raw" then no attempt is made at obfuscating SQL statements.
;          USING "raw" IS HIGHLY DISCOURAGED IN PRODUCTION ENVIRONMENTS!
;          Setting this to "raw" has considerable security implications as it
;          can expose sensitive and private customer data.
;
;newrelic.transaction_tracer.record_sql = "obfuscated"

; Setting: newrelic.transaction_tracer.custom
; Type   : string
; Scope  : per-directory
; Default: none
; Info   : Sets the name(s) of additional functions you want to instrument and
;          appear in transaction traces. This is only meaningful if you have
;          set newrelic.transaction_tracer.detail to 0. This can be a comma-
;          separated list of function or class method names.
;
;newrelic.transaction_tracer.custom = ""

; Setting: newrelic.framework
; Type   : string
; Scope  : per-directory
; Default: empty (auto-detect framework)
; Info   : Forces the framework to be one of the supported frameworks. Since
;          this should only be used if the auto-detection fails, please let
;          us know at support.newrelic.com if you encounter a failure.
;          Must be one of the following values:
;          cakephp, codeigniter, drupal, drupal8, joomla, kohana, laravel,
;          magento, magento2, mediawiki, silex, slim, symfony1, symfony2,
;          wordpress, yii, zend, zend2, no_framework
;
;          Note that "drupal" covers only Drupal 6 and 7.
;
;newrelic.framework = ""

; Setting: newrelic.webtransaction.name.remove_trailing_path
; Type   : boolean
; Scope  : per-directory
; Default: false
; Info   : Used to aid naming transactions correctly when an unsupported
;          framework is being used. This option will cause anything after the
;          script name to be stripped from a URL. For example, setting this
;          would cause the "/xyz/zy" to be stripped from a URL such as
;          "/path/to/foo.php/xyz/zy".
;
;newrelic.webtransaction.name.remove_trailing_path = false

; Setting: newrelic.webtransaction.name.functions
; Type   : string
; Scope  : per-directory
; Default: none
; Info   : Unless a specific framework such as Drupal or Wordpress has been
;          detected, transactions are named according to the first script
;          encountered, such as login.php. However, if you use a dispatcher
;          file such as index.php this produces less useful data. If you use
;          a dispatcher to redirect to actions such as "login", "show", "edit"
;          etc, you can set this to the top level functions for those actions,
;          and the function names specified here will be used to name the
;          transaction.
;
;newrelic.webtransaction.name.functions = ""

; Setting: newrelic.webtransaction.name.files
; Type   : string
; Scope  : per-directory
; Default: none
; Info   : Same as newrelic.webtransaction.name.functions above but using file
;          names instead of function names. Accepts standard POSIX regular
;          expressions.
;
;newrelic.webtransaction.name.files = ""

; Setting: newrelic.daemon.auditlog
; Type   : string
; Scope  : system
; Default: none
; Info   : Sets the name of a file to record all uncompressed, un-encoded
;          content that is sent from your machine to the New Relic servers.
;          This includes the full URL for each command along with the payload
;          delivered with the command. This allows you to satisfy yourself
;          that the agent is not sending any sensitive data to our servers.
;          This file must be a different file the the newrelic.daemon.logfile
;          setting above. If you set it to the same name,
;          then audit logging will be silently ignored.
;
;newrelic.daemon.auditlog = "/var/log/newrelic/audit.log"

; Setting: newrelic.transaction_events.enabled
; Type   : boolean
; Scope  : per-directory
; Default: true
; Info   : Collect and report transaction analytics event data. Event data
;          allows the New Relic UI to show additional information such as
;          histograms. This setting was formerly called
;          newrelic.analytics_events.enabled.
;
newrelic.transaction_events.enabled = true

; Setting: newrelic.attributes.enabled
; Type   : boolean
; Scope  : per-directory
; Default: true
; Info   : Enable or disable the collection of attributes generated by the
;          agent or generated by the user though newrelic_add_custom_parameter.
;          This setting will take precedence over all other attribute
;          configuration settings. For more information, please refer to:
;          https://docs.newrelic.com/docs/agents/manage-apm-agents/agent-metrics/agent-attributes
;
newrelic.attributes.enabled = true

; Setting: newrelic.transaction_events.attributes.enabled
;          newrelic.transaction_tracer.attributes.enabled
;          newrelic.error_collector.attributes.enabled
;          newrelic.browser_monitoring.attributes.enabled
; Type   : boolean
; Scope  : per-directory
; Default: true, except for browser_monitoring.attributes.enabled
; Info   : Control which destinations receive attributes.
;          These configuration settings will override the .include and .exclude
;          settings below. For more information, please refer to:
;          https://docs.newrelic.com/docs/agents/manage-apm-agents/agent-metrics/agent-attributes
;
;          These settings were formerly called:
;          newrelic.transaction_tracer.capture_attributes
;          newrelic.error_collector.capture_attributes
;          newrelic.analytics_events.capture_attributes
;          newrelic.browser_monitoring.capture_attributes
;
;newrelic.transaction_events.attributes.enabled = true
;newrelic.transaction_tracer.attributes.enabled = true
;newrelic.error_collector.attributes.enabled = true
;newrelic.browser_monitoring.attributes.enabled = false

; Setting: newrelic.attributes.include
;          newrelic.attributes.exclude
;
;          newrelic.transaction_events.attributes.include
;          newrelic.transaction_events.attributes.exclude
;
;          newrelic.transaction_tracer.attributes.include
;          newrelic.transaction_tracer.attributes.exclude
;
;          newrelic.error_collector.attributes.include
;          newrelic.error_collector.attributes.exclude
;
;          newrelic.browser_monitoring.attributes.include
;          newrelic.browser_monitoring.attributes.exclude
;
; Type   : string
; Scope  : per-directory
; Default: none
; Info   : Each attribute has a default set of destinations. For example, the
;          'request_uri' attribute's default destinations are errors and
;          transaction traces. The 'httpResponseCode' attribute's default
;          destinations are errors, transaction traces, and transaction events.
;
;          These configuration options allow complete control over the
;          destinations of attributes.
;
;          To include the attribute whose key is 'alpha' in errors, the
;          configuration is:
;          newrelic.error_collector.include = alpha
;
;          To exclude the attribute whose key is 'alpha' from errors, the
;          configuration is:
;          newrelic.error_collector.exclude = alpha
;
;          The newrelic.attributes.exclude and newrelic.attributes.include
;          settings affect all destinations.
;
;          To exclude the attributes 'beta' and 'gamma' from all destinations,
;          the configuration is:
;          newrelic.attributes.exclude = beta,gamma
;
;          If one of the values in the comma separated list ends in a '*',
;          it will match any suffix. For example, to exclude any attributes
;          which begin with 'psi', the configuration is:
;          newrelic.attributes.exclude = psi*
;
;          For more information, please refer to:
;          https://docs.newrelic.com/docs/agents/manage-apm-agents/agent-metrics/agent-attributes
;
;newrelic.attributes.include = ""
;newrelic.attributes.exclude = ""
;
;newrelic.transaction_events.attributes.include = ""
;newrelic.transaction_events.attributes.exclude = ""
;
;newrelic.transaction_tracer.attributes.include = ""
;newrelic.transaction_tracer.attributes.exclude = ""
;
;newrelic.error_collector.attributes.include = ""
;newrelic.error_collector.attributes.exclude = ""
;
;newrelic.browser_monitoring.attributes.include = ""
;newrelic.browser_monitoring.attributes.exclude = ""

; Setting: newrelic.feature_flag
; Type   : string
; Scope  : system
; Default: none
; Info   : Enables new and experimental features within the PHP agent. These
;          flags are used to selectively enable features that are intended to be
;          enabled by default in later versions of the PHP agent.
;
;newrelic.feature_flag = ""

; Setting: newrelic.custom_insights_events.enabled
; Type   : boolean
; Scope  : per-directory
; Default: true
; Info   : Enables or disables the API function newrelic_record_custom_event.
;
newrelic.custom_insights_events.enabled = true

; Setting: newrelic.labels
; Type   : string (Use quotes)
; Scope  : per-directory
; Default: none
; Info   : Sets the label names and values to associate with the application.
;          The list is a semi-colon delimited list of colon-separated name and
;          value pairs.
;
;          There are a maximum of 64 label name/value pairs allowed.
;
;          The maximum length of the name and value is 255 characters each.
;
;          Leading or trailing whitespace in the name or value will be trimmed.
;
;          UTF-8 characters are allowed.
;
;          E.g., "Server:One;Data Center:Primary"
;
;newrelic.labels = ""

; Setting: newrelic.synthetics.enabled
; Type   : boolean
; Scope  : per-directory
; Default: true
; Info   : Enables or disables support for Synthetics transactions.
;          For more information, please see:
;          https://docs.newrelic.com/docs/synthetics/new-relic-synthetics/getting-started/new-relic-synthetics
;
;newrelic.synthetics.enabled = true

; Setting: newrelic.cross_application_tracer.enabled
; Type   : boolean
; Scope  : per-directory
; Default: true
; Info   : Enables or disables support for Cross Application Tracing, aka "CAT".
;
;newrelic.cross_application_tracer.enabled = true

; Setting: newrelic.transaction_tracer.gather_input_queries
; Type   : boolean
; Scope  : per-directory
; Default: true
; Info   : Enables or disables support for tracing Doctrine DQL with Slow SQL queries.
;          This requires Slow SQLs to be enabled.
;
;newrelic.transaction_tracer.gather_input_queries = true

; Setting: newrelic.error_collector.capture_events
; Type   : boolean
; Scope  : per-directory
; Default: true
; Info   : Enables or disables capturing error events, which are displayed as
;          Error Analytics in the UI.
;
;newrelic.error_collector.capture_events = true

; Setting: newrelic.guzzle.enabled
; Type   : boolean
; Scope  : per-directory
; Default: true
; Info   : Enables or disables support for the Guzzle library.
;
;newrelic.guzzle.enabled = true

; Setting: newrelic.phpunit_events.enabled
; Type   : boolean
; Scope  : per-directory
; Default: false
; Info   : Collect and report PHPUnit (https://phpunit.de/) data as custom
;          Insights events. Test suite summary data are sent as "TestSuite"
;          events, while individual test cases are sent as "Test" events.
;          Depending on your events retention policy, enabling this setting may
;          impact your billing statement.
;
;          Please note that exception messages are collected and sent with
;          events. Additionally, if you use PHPUnit's --disallow-test-output
;          flag, any offending output from a risky test will also be included.
;
;newrelic.phpunit_events.enabled = false

; Setting: newrelic.datastore_tracer.instance_reporting.enabled
; Type   : boolean
; Scope  : per-directory
; Default: true
; Info   : Enables or disables capturing datastore instance information,
;          specifically host and port_path_or_id. This information is sent as a
;          metric and as attributes on transaction traces and slow SQL traces.
;
;newrelic.datastore_tracer.instance_reporting.enabled = true

; Setting: newrelic.datastore_tracer.database_name_reporting.enabled
; Type   : boolean
; Scope  : per-directory
; Default: true
; Info   : Enables or disables capturing information about database names. This
;          information is sent as an attribute on transaction traces and slow
;          SQL traces.
;
;newrelic.datastore_tracer.database_name_reporting.enabled = true
