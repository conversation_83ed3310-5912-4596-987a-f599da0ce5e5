<?php

declare(strict_types=1);

App::import('Test/Case', 'GlofoxTestCase');

use Glofox\Response\Models\Branches\Features\FacilitiesModel;

class FacilitiesModelTest extends GlofoxTestCase
{
    public $fixtures = [];

    /**
     * @dataProvider modelProvider
     */
    public function testModel(array $data, FacilitiesModel $expectedResult): void
    {
        $actualResult = new FacilitiesModel($data);
        $this->assertEquals($expectedResult, $actualResult);
    }

    public function modelProvider(): array
    {
        return [
            'when data has expected fields, then it creates the model' => [
                [
                    'order' => 0,
                    'enabled' => true,
                    'booking' => null,
                    '_booking_open_window' => true,
                    '_booking_close_window' => false,
                    'booking_enabled' => false,
                    'booking_weeks_display' => 0,
                    'booking_cancel_window' => 1,
                    'booking_open_window' => 1,
                    'booking_close_window' => 1,
                    'booking_time_slot_length' => 1,
                ],
                new FacilitiesModel(
                    [
                        'order' => 0,
                        'enabled' => true,
                        'booking' => null,
                        '_booking_open_window' => true,
                        '_booking_close_window' => false,
                        'booking_enabled' => false,
                        'booking_weeks_display' => 0,
                        'booking_cancel_window' => 1,
                        'booking_open_window' => 1,
                        'booking_close_window' => 1,
                        'booking_time_slot_length' => 1,
                    ]
                ),
            ],
            'when data has missing fields, then it creates the model with default values for those fields' => [
                [
                    'enabled' => false,
                    'booking' => null,
                    '_booking_open_window' => false,
                    '_booking_close_window' => false,
                    'booking_enabled' => false,
                    'booking_weeks_display' => 0,
                    'booking_cancel_window' => 1,
                    'booking_open_window' => 1,
                    'booking_close_window' => 1,
                    'booking_time_slot_length' => 1,
                ],
                new FacilitiesModel(
                    [
                        'order' => 0,
                        'enabled' => false,
                        'booking' => null,
                        '_booking_open_window' => false,
                        '_booking_close_window' => false,
                        'booking_enabled' => false,
                        'booking_weeks_display' => 0,
                        'booking_cancel_window' => 1,
                        'booking_open_window' => 1,
                        'booking_close_window' => 1,
                        'booking_time_slot_length' => 1,
                    ]
                ),
            ],
            'when data has additional fields, then it creates the model ignoring those fields' => [
                [
                    'extra' => '1230',
                    'order' => 0,
                    'enabled' => false,
                    'booking' => null,
                    '_booking_open_window' => false,
                    '_booking_close_window' => false,
                    'booking_enabled' => false,
                    'booking_weeks_display' => 0,
                    'booking_cancel_window' => 1,
                    'booking_open_window' => 1,
                    'booking_close_window' => 1,
                    'booking_time_slot_length' => 1,
                ],
                new FacilitiesModel(
                    [
                        'order' => 0,
                        'enabled' => false,
                        'booking' => null,
                        '_booking_open_window' => false,
                        '_booking_close_window' => false,
                        'booking_enabled' => false,
                        'booking_weeks_display' => 0,
                        'booking_cancel_window' => 1,
                        'booking_open_window' => 1,
                        'booking_close_window' => 1,
                        'booking_time_slot_length' => 1,
                    ]
                ),
            ],
            'when data is empty, then it creates the model with default values and expected structure' => [
                [],
                new FacilitiesModel(
                    [
                        'order' => 0,
                        'enabled' => false,
                        'booking' => null,
                        '_booking_open_window' => false,
                        '_booking_close_window' => false,
                        'booking_enabled' => false,
                        'booking_weeks_display' => 0,
                        'booking_cancel_window' => 0,
                        'booking_open_window' => 0,
                        'booking_close_window' => 0,
                        'booking_time_slot_length' => 0,
                    ]
                ),
            ],
        ];
    }
}
