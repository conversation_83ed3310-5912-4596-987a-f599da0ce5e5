type: object
properties:
  _id:
    type: string
  branch_id:
    type: string
  namespace:
    type: string
  model:
    type: string
    enum:
      - appointments
      - facilities
      - users
  model_id:
    type: string
  active:
    type: boolean
  booked:
    type: boolean
  private:
    type: boolean
  staff_id:
    type: string
  time_slot_pattern_id:
    type: string
  time_start:
    type: string
    description: Date time Y-m-d H:i:s or timestamp
  time_finish:
    type: string
    description: Date time Y-m-d H:i:s or timestamp
  date:
    type: string
    description: Date or timestamp
  week_day:
    type: integer
  name:
    type: string
  modified:
    type: string
    description: date-time or object with seconds and microseconds
  created:
    type: string
    description: date-time or object with seconds and microseconds
  appointment:
    $ref: './appointment.yml'
  staff:
    $ref: './user.yml'
