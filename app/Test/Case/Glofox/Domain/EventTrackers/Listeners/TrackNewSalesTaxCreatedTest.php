<?php

namespace CakeTestCases\Glofox\Domain\EventTrackers\Listeners;

use App;
use CakeTestCases\Glofox\Domain\Authentication\Token\TokenGeneration;
use CakeTestCases\Glofox\Domain\Users\Traits\FetchUsersTrait;
use Glofox\Domain\AsyncEvents\Events\TrackingEventMeta;
use Glofox\Domain\AsyncEvents\Events\TrackingEventPayload;
use Glofox\Domain\Branches\Models\Branch;
use Glofox\Domain\EventTrackers\Listeners\TrackNewSalesTaxCreated;
use Glofox\Domain\EventTrackers\Services\TrackEventPublisher;
use GlofoxTestCase;
use League\Event\EventInterface;
use Mockery;

App::import('Test/Case', 'GlofoxTestCase');

class TrackNewSalesTaxCreatedTest extends GlofoxTestCase
{
    use FetchUsersTrait;
    use TokenGeneration;

    /** @var Branch */
    private $branch;

    private ?array $tax = null;

    public function setUp()
    {
        parent::setUp();

        $this->branch = Branch::make([
            '_id' => '5dc5bb4d7d54963c7c841234',
            'name' => 'Test',
            'namespace' => 'testing',
            'address' => ['country_code' => 'test'],
            'stripe_plan_code' => 'platinum',
        ]);

        $this->tax = ['id' => '*********', 'name' => 'test tax', 'rate' => 12.34];
    }

    public function tearDown()
    {
        parent::tearDown();

        Mockery::close();
    }

    public function test_handle_method_tracking_the_proper_data(): void
    {
        $adminUser = $this->fetchUser('5dc5bb4d7d54963c7c846b5a');
        $this->loginAsUser($adminUser);

        $trackEventPublisher = Mockery::mock(TrackEventPublisher::class);
        $trackEventPublisher
            ->shouldReceive('sendEventToTrack')
            ->withArgs(function (TrackingEventMeta $meta, TrackingEventPayload $payload) {
                $this->assertEquals(
                    new TrackingEventPayload(
                        [
                            'eventName' => 'New sales tax created',
                            'branchId' => $this->branch->id(),
                            'userId' => '5dc5bb4d7d54963c7c846b5a',
                            'platform' => 'CORE-API',
                            'data' => [
                                'taxId' => '*********',
                                'taxName' => 'test tax',
                                'taxRate' => 12.34,
                            ]
                        ]
                    ),
                    $payload
                );

                return true;
            });
        $listener = new TrackNewSalesTaxCreated($trackEventPublisher);
        $listener->handle($this->mockEvent());
    }

    private function mockEvent(): EventInterface
    {
        $event = Mockery::mock(EventInterface::class);
        $event
            ->shouldReceive('getTax')
            ->andReturn($this->tax)
            ->once()
            ->getMock()
            ->shouldReceive('getBranchId')
            ->andReturn($this->branch->id())
            ->once();


        return $event;
    }
}
