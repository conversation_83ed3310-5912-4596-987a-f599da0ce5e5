<?php

class IntegrationFixture extends CakeTestFixture
{
    public $import = 'Integration';

    public function init()
    {
        $this->records = [
            [
                '_id' => new MongoId('49b7012a05c677c9a512503c'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => false,
                'service' => 'classpass',
                'settings' => [
                    'access_token' => 'abc',
                    'refresh_token' => 'abc',
                    'end_of_life' => time() + 1000,
                ],
                'subscriptions' => [],
            ],
            [
                '_id' => new MongoId('29b7012a05c677c9a512503d'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'service' => 'mailchimp',
                'settings' => [],
            ],
            // Classpass Integrated
            [
                '_id' => new MongoId('5addc9549c426316719302e6'),
                'branch_id' => '5addc25383266f65abf515c4',
                'namespace' => 'classpass_integrated',
                'active' => true,
                'service' => 'classpass',
                'settings' => [],
            ],
        ];
        parent::init();
    }
}
