type: object
properties:
  batch_id:
    type: string
  total_instances:
    type: integer
    description: Total number of slots by batch.
  bookings:
    type: array
    items:
      type: object
      properties:
        id:
          type: string
        status:
          type: string
          enum:
            - BOOKED
            - RESERVED
            - FAILED
            - PENDING
        time_start:
          type: integer
          example: 1722431941
          description: Booking start time in epoch time.
        failure_reason:
          type: string
