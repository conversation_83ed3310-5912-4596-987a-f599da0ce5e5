<?php

namespace Glofox\Payments\Providers\Gateway\Merchants\DataModellers;

use Glofox\Domain\Branches\Models\Branch;
use Glofox\Domain\PaymentProviders\Models\PaymentProvider;
use Illuminate\Support\Collection;

class ApsDataModeller extends DefaultDataModeller
{
    /**
     * @param PaymentProvider $paymentProvider
     * @param Branch $branch
     * @param Collection $payload
     * @return Collection
     * @throws \UnsuccessfulOperation
     */
    public function modelForCreating(PaymentProvider $paymentProvider, Branch $branch, Collection $payload): Collection
    {
        $payload = parent::modelForCreating($paymentProvider, $branch, $payload);
        if ($payload->isEmpty()) {
            return $payload;
        }

        return $this->format($branch, $payload);
    }

    /**
     * @param Branch $branch
     * @param Collection $payload
     * @return Collection
     * @throws \UnsuccessfulOperation
     */
    private function format(Branch $branch, Collection $payload): Collection
    {
        return collect([
            'email' => $branch->email(),
            'phone' => $branch->phone(),
            'country' => $branch->address()->countryCode('alpha3'),
            'currency' => strtolower($branch->currency()),
            'business_name' => $branch->name(),
            'auth_object' => [
                'merchant_id' => $payload->get('merchant_id'),
                'access_code' => $payload->get('access_code'),
                'sha_type' => $payload->get('sha_type'),
                'sha_request_phrase' => $payload->get('sha_request_phrase'),
                'sha_response_phrase' => $payload->get('sha_response_phrase'),
                'payment_api_url' => $payload->get('payment_api_url'),
                'payment_page_url' => $payload->get('payment_page_url'),
                'mode' => $payload->get('mode'),
            ],
        ]);
    }
}
