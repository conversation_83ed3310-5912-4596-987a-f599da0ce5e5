<?php

namespace Glofox\Authentication;

use Emarref\Jwt\Encoding\Base64;
use Emarref\Jwt\Verification;
use Glofox\AuditLog\Resolvers\OriginByRequestResolver;
use Glofox\Authentication\Verification\ExpirationVerifier;
use Psr\Log\LoggerInterface;
use Glofox\Domain\Authentication\Token\Verification as CustomVerifiers;

class Jwt extends \Emarref\Jwt\Jwt
{
    protected static ?OriginByRequestResolver $originByRequestResolver = null;
    protected static ?LoggerInterface $logger = null;

    protected function getVerifiers(Verification\Context $context): array
    {
        return array_filter([
            // Basic verifiers provided by the library
            new Verification\EncryptionVerifier($context->getEncryption(), new Base64()),
            new Verification\SubjectVerifier($context->getSubject()),
            new Verification\NotBeforeVerifier(),

            new ExpirationVerifier(),

            // Custom verifiers according to the business rules
            new CustomVerifiers\UserClaimVerifier(),
            app()->make(CustomVerifiers\UserBranchRelationVerifier::class),
        ]);
    }
}