<?php

declare(strict_types=1);

namespace CakeTestCases\Console\Command;

use Carbon\Carbon;
use Glofox\Domain\AsyncEvents\Events\ReservationConfirmationRequestedEventMeta;
use Glofox\Domain\AsyncEvents\Events\ReservationConfirmationRequestedEventPayload;
use Glofox\Domain\Bookings\Models\Booking;
use Glofox\Domain\Bookings\Repositories\BookingsRepository;
use Glofox\Domain\Bookings\Services\BookingsPublisher;
use Glofox\Domain\Branches\Models\Branch;
use Glofox\Domain\Branches\Repositories\BranchesRepository;
use Glofox\Domain\Branches\Services\BranchesPublisher;
use Glofox\Infrastructure\Honeycomb\HoneycombTracker;

\App::import('Test/Case', 'GlofoxTestCase');
\App::import('Console/Command', 'ReservationProcessorShell');

class ReservationProcessorShellTest extends \GlofoxTestCase
{
    public function setUp()
    {
        parent::setUp();

        $this->mockLogger();
    }

    public function tearDown()
    {
        parent::tearDown();

        \Mockery::close();
    }

    public function test_it_should_publish_event_when_reservation_found(): void
    {
        $reservations = [
            Booking::make([
                '_id' => 'test-booking-id',
                'branch_id' => 'test-branch-id',
            ]),
        ];

        $bookingsRepository = \Mockery::mock(BookingsRepository::class);
        $bookingsRepository->shouldReceive('findNonConfirmedBookingsInBookingOpenWindowByBranchId')
            ->andReturn($reservations);

        app()->instance(BookingsRepository::class, $bookingsRepository);

        $branches = [
            Branch::make([
                '_id' => 'test-branch-id',
                'timezone' => 'Europe/Dublin',
            ]),
        ];

        $branchesRepository = \Mockery::mock(BranchesRepository::class);
        $branchesRepository->shouldReceive('findAllActive')
            ->andReturn($branches);

        app()->instance(BranchesRepository::class, $branchesRepository);

        $bookingsPublisher = \Mockery::mock(BookingsPublisher::class);
        $bookingsPublisher->shouldReceive('sendReservationConfirmationRequestedEvent')
            ->withArgs(function (
                ReservationConfirmationRequestedEventMeta $meta,
                ReservationConfirmationRequestedEventPayload $payload
            ) {
                $data = $payload->jsonSerialize();
                self::assertEquals($data['bookingId'], 'test-booking-id');
                self::assertEquals($data['branchId'], 'test-branch-id');

                return true;
            });

        app()->instance(BookingsPublisher::class, $bookingsPublisher);
        $honeycombTracker = \Mockery::mock(HoneycombTracker::class);
        $honeycombTracker->shouldReceive('track')
            ->once();
        app()->instance(HoneycombTracker::class, $honeycombTracker);
        $command = new \ReservationProcessorShell();

        $command->main();

        app()->forgetInstance(BookingsRepository::class);
        app()->forgetInstance(BranchesRepository::class);
        app()->forgetInstance(BranchesPublisher::class);
        app()->forgetInstance(HoneycombTracker::class);
    }

    public function maxStartDatesProvider(): array
    {
        return [
            'within hours' => [5, 1, '2024-05-01 17:00:00'],
            'negative booking window and negative display weeks' => [-9, -7, '2024-06-12 12:00:00'],
            'negative booking window and zero display weeks' => [-9, 0, '2024-06-12 12:00:00'],
            'negative booking window and with display weeks' => [-9, 1, '2024-05-08 12:00:00'],
            'more than 6 weeks, negative display week setting' => [1_593_188_352, -999, '2024-06-12 12:00:00'],
            'more than 6 weeks but with display week setting' => [1_593_188_352, 1, '2024-05-08 12:00:00'],
        ];
    }

    /**
     * @dataProvider maxStartDatesProvider
     */
    public function testMaxStartTimeShouldNotGoMoreThanSixWeeks(
        float $openWindowHours,
        int $weeksDisplay,
        string $expectedDateString
    ): void {
        Carbon::setTestNow('2024-05-01 12:00:00');

        $bookingsRepository = \Mockery::mock(BookingsRepository::class);
        $bookingsRepository->shouldReceive('findNonConfirmedBookingsInBookingOpenWindowByBranchId')
            ->withArgs(function (...$args) use ($expectedDateString) {
                $this->assertNotEmpty($args[1]);
                $this->assertInstanceOf(Carbon::class, $args[1]);
                $this->assertEquals($expectedDateString, ($args[1])->toDateTimeString());

                return true;
            })
            ->andReturn([]);

        app()->instance(BookingsRepository::class, $bookingsRepository);

        $branches = [
            Branch::make([
                '_id' => 'test-branch-id',
                'timezone' => 'Europe/Dublin',
                'features' => [
                    'booking' => [
                        'booking_open_window' => $openWindowHours,
                    ],
                    'classes' => [
                        'weeks_display' => $weeksDisplay,
                    ],
                ],
            ]),
        ];

        $branchesRepository = \Mockery::mock(BranchesRepository::class);
        $branchesRepository->shouldReceive('findAllActive')
            ->andReturn($branches);

        app()->instance(BranchesRepository::class, $branchesRepository);

        $honeycombTracker = \Mockery::mock(HoneycombTracker::class);
        $honeycombTracker->shouldReceive('track')
            ->once();
        app()->instance(HoneycombTracker::class, $honeycombTracker);

        $command = new \ReservationProcessorShell();

        $command->main();

        app()->forgetInstance(BookingsRepository::class);
        app()->forgetInstance(BranchesRepository::class);
        app()->forgetInstance(HoneycombTracker::class);
        Carbon::setTestNow();
    }
}
