<?php

use Carbon\Carbon;

class EventFixture extends CakeTestFixture
{
    private const GLOFOX_BRANCH_ID = '49a7011a05c677b9a916612a';
    private const GLOFOX_NAMESPACE = 'glofox';
    private const GLOFOX_PROGRAM_ID = '13b7411a15c676bda824611b';
    private const VIRTUAL_SLOT_BRANCH_ID = '6461ed6fdef3e8f4c6d4b00c';
    private const VIRTUAL_SLOT_BRANCH_NAMESPACE = 'virtualslotstest';
    private const VIRTUAL_SLOT_STAFF_ID = '6461eeb3bd01a2e1dc88a96d';
    private const VIRTUAL_SLOT_FACILITY_ID = '64620bdd9a5784fd0da79869';
    private const VIRTUAL_SLOT_PROGRAM_ID = '64620b36de9120be8b00ed33';
    private const TRAINER_ID = '67425efa1d395d128dde1ef2';
    public $import = 'Event';

    public function init()
    {
        $this->records = [
            [
                '_id' => new MongoId('49b7012a05c677c9a512503c'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'program_id' => '13b7411a15c676bda824611b',
                'schedule_code' => uniqid('', false),
                'level' => 'Advanced',
                'name' => 'Test',
                'description' => 'Class',
                'size' => 20,
                'duration' => 60,
                'date' => new MongoDate(strtotime('-2 minutes')),
                'time_start' => new MongoDate(strtotime('-2 minutes')),
                'time_finish' => new MongoDate(strtotime('-1 minute')),
                'trainers' => ['58568a8fa875ab19530041a7'],
                'facility' => '52a7011a05c677bda826611b',
                'modified' => 1_702_571_656,
            ],
            [
                '_id' => new MongoId('49b7012a05c677c9a5125123'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'program_id' => '13b7411a15c676bda824611b',
                'schedule_code' => uniqid('', false),
                'level' => 'Advanced',
                'name' => 'Test',
                'description' => 'Class',
                'size' => 20,
                'duration' => 60,
                'date' => new MongoDate(strtotime('-2 minutes')),
                'time_start' => new MongoDate(strtotime('-2 minutes')),
                'time_finish' => new MongoDate(strtotime('-1 minute')),
                'trainers' => ['58568a8fa875ab19530041a7'],
                'facility' => '52a7011a05c677bda826611b',
            ],
            [
                '_id' => new MongoId('29b7012a05c677c9a512503e'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'program_id' => '13b7411a15c676bda824611b',
                'schedule_code' => uniqid('', false),
                'level' => 'Advanced',
                'name' => 'Test',
                'description' => 'Class',
                'size' => 20,
                'duration' => 60,
                'date' => new MongoDate(strtotime('+80 hours')),
                'time_start' => new MongoDate(strtotime('+80 hour')),
                'time_finish' => new MongoDate(strtotime('+86 hour')),
                'trainers' => ['58568a8fa875ab19530041a7'],
                'facility' => '52a7011a05c677bda826611b',
            ],
            [
                '_id' => new MongoId('29b7012a05c677c9a512503d'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'program_id' => '13b7411a15c676bda824611b',
                'schedule_code' => uniqid('', false),
                'level' => 'Advanced',
                'name' => 'Test',
                'description' => 'Class',
                'size' => 20,
                'duration' => 60,
                'date' => new MongoDate(strtotime('+4 hours')),
                'time_start' => new MongoDate(strtotime('+4 hour')),
                'time_finish' => new MongoDate(strtotime('+6 hour')),
                'trainers' => ['58568a8fa875ab19530041a7'],
                'facility' => '52a7011a05c677bda826611b',
            ],
            [
                '_id' => new MongoId('29b7012a05c677c9a512504d'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'program_id' => '13b7411a15c676bda824611b',
                'schedule_code' => uniqid('', false),
                'level' => 'Advanced',
                'name' => 'Test',
                'description' => 'Class',
                'size' => 20,
                'duration' => 60,
                'date' => new MongoDate(strtotime('+4 hours')),
                'time_start' => new MongoDate(strtotime('+4 hour')),
                'time_finish' => new MongoDate(strtotime('+6 hour')),
                'trainers' => ['58568a8fa875ab19530041a7'],
                'facility' => '52a7011a05c677bda826611b',
            ],
            [
                '_id' => new MongoId('29b7012a05c677c9a512504e'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'program_id' => '13b7411a15c676bda824611b',
                'schedule_code' => uniqid('', false),
                'level' => 'Advanced',
                'name' => 'Test',
                'description' => 'Class',
                'size' => 20,
                'duration' => 60,
                'date' => new MongoDate(strtotime('+20 minute')),
                'time_start' => new MongoDate(strtotime('+20 minute')),
                'time_finish' => new MongoDate(strtotime('+2 hour')),
                'trainers' => ['58568a8fa875ab19530041a7'],
                'facility' => '52a7011a05c677bda826611b',
            ],
            [
                '_id' => new MongoId('39b7012a05c677c9a512504d'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'program_id' => '13b7411a15c676bda824611b',
                'schedule_code' => uniqid('', false),
                'level' => 'Advanced',
                'name' => 'Test',
                'description' => 'Class',
                'size' => 20,
                'duration' => 60,
                'date' => new MongoDate(strtotime('+4 hours')),
                'time_start' => new MongoDate(strtotime('+4 hour')),
                'time_finish' => new MongoDate(strtotime('+6 hour')),
                'trainers' => ['58568a8fa875ab19530041a7'],
                'facility' => '52a7011a05c677bda826611b',
            ],
            [
                '_id' => new MongoId('59ca3b6ac75de1633c2b972b'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'program_id' => '59ca3a2f570b4bde3da3decd',
                'schedule_code' => uniqid('', false),
                'level' => 'Advanced',
                'name' => 'Test',
                'description' => 'Class',
                'size' => 20,
                'duration' => 60,
                'date' => new MongoDate(strtotime('+1 weeks')),
                'time_start' => new MongoDate(strtotime('+1 weeks 8 hours')),
                'time_finish' => new MongoDate(strtotime('+1 weeks 9 hours')),
                'trainers' => ['58568a8fa875ab19530041a7'],
                'facility' => '52a7011a05c677bda826611b',
                'total_bookings' => 0,
            ],
            [
                '_id' => new MongoId('59ca3b6ac75de1633c2b972a'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'program_id' => '59ca3a2f570b4bde3da3decd',
                'schedule_code' => uniqid('', false),
                'level' => 'Advanced',
                'name' => 'Test price being edited',
                'description' => 'Class',
                'size' => 20,
                'duration' => 60,
                'date' => new MongoDate(strtotime('+1 weeks')),
                'time_start' => new MongoDate(strtotime('+10 weeks 8 hours')),
                'time_finish' => new MongoDate(strtotime('+10 weeks 9 hours')),
                'trainers' => ['58568a8fa875ab19530041a7'],
                'facility' => '52a7011a05c677bda826611b',
                'total_bookings' => 0,
            ],
            [
                '_id' => new MongoId('59ca3b6ac75de1633c3b972b'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'program_id' => '59ca3a2f570b4bde3da3decd',
                'schedule_code' => '61aa0cc77d30f',
                'level' => 'Advanced',
                'name' => 'Test',
                'description' => 'Class',
                'size' => 20,
                'duration' => 60,
                'date' => new MongoDate(strtotime('+1 weeks')),
                'time_start' => new MongoDate(strtotime('+1 weeks 8 hours')),
                'time_finish' => new MongoDate(strtotime('+1 weeks 9 hours')),
                'trainers' => ['58568a8fa875ab19630041a7'],
                'facility' => '52a7011a05c687bda826611b',
                'total_bookings' => 0,
            ],
            [
                '_id' => new MongoId('59e63079e71da189c2b0ea00'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'program_id' => '59ca3a2f570b4bde3da3decd',
                'schedule_code' => uniqid('', false),
                'level' => 'Advanced',
                'name' => 'Test',
                'description' => 'Class',
                'size' => 1,
                'duration' => 60,
                'date' => new MongoDate(strtotime('+1 weeks')),
                'time_start' => new MongoDate(strtotime('+1 weeks 8 hours')),
                'time_finish' => new MongoDate(strtotime('+1 weeks 9 hours')),
                'trainers' => ['58568a8fa875ab19530041a7'],
                'facility' => '52a7011a05c677bda826611b',
                'total_bookings' => 1,
            ],
            [
                '_id' => new MongoId('59e63079e71da189c2b0ea01'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'program_id' => '59ca3a2f570b4bde3da3decd',
                'level' => 'Advanced',
                'name' => 'Test',
                'description' => 'Class',
                'size' => 1,
                'duration' => 60,
                'date' => new MongoDate(strtotime('+1 weeks')),
                'time_start' => new MongoDate(strtotime('+1 weeks 8 hours')),
                'time_finish' => new MongoDate(strtotime('+1 weeks 9 hours')),
                'trainers' => ['58568a8fa875ab19530041a7'],
                'facility' => '52a7011a05c677bda826611b',
                'total_bookings' => 1,
            ],

            [
                '_id' => new MongoId('59e63079e71da189c2b0ea02'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'program_id' => '59ca3a2f570b4bde3da3decd',
                'level' => 'Advanced',
                'name' => 'Test',
                'description' => 'Class',
                'size' => 1,
                'duration' => 60,
                'date' => new MongoDate(strtotime('+1 weeks')),
                'time_start' => new MongoDate(strtotime('+1 weeks 8 hours')),
                'time_finish' => new MongoDate(strtotime('+1 weeks 9 hours')),
                'trainers' => ['58568a8fa875ab19530041a7'],
                'facility' => '52a7011a05c677bda826611b',
                'total_bookings' => 1,
            ],
            [
                '_id' => new MongoId('29b7012a05c677c9a512004d'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'program_id' => '13b7411a15c676bda824611b',
                'schedule_code' => uniqid('', false),
                'level' => 'Advanced',
                'name' => 'Test',
                'description' => 'Class',
                'size' => 20,
                'duration' => 60,
                'date' => new MongoDate(strtotime('+4 hours')),
                'time_start' => new MongoDate(strtotime('+4 hour')),
                'time_finish' => new MongoDate(strtotime('+6 hour')),
                'trainers' => ['58568a8fa875ab19530041a7'],
                'facility' => '52a7011a05c677bda826611b',
            ],
            [
                '_id' => new MongoId('59ca3b6ac75de1603c2b972b'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'program_id' => '59ca3a2f570b4bde3da3decd',
                'schedule_code' => uniqid('', false),
                'level' => 'Advanced',
                'name' => 'Test',
                'description' => 'Class',
                'size' => 20,
                'duration' => 60,
                'date' => new MongoDate(strtotime('+1 hours')),
                'time_start' => new MongoDate(strtotime('+1 hour')),
                'time_finish' => new MongoDate(strtotime('+2 hour')),
                'trainers' => ['58568a8fa875ab19530041a7'],
                'facility' => '52a7011a05c677bda826611b',
                'total_bookings' => 0,
            ],
            // Classpass size 3 and glofox size 10
            [
                '_id' => new MongoId('5adf43c0db6b6587ebcb2c5b'),
                'branch_id' => '5addc25383266f65abf515c4',
                'namespace' => 'classpass_integrated',
                'active' => true,
                'program_id' => '5adf442bb2db1df852f44df0',
                'schedule_code' => uniqid('', false),
                'name' => 'Classpass size 3 and glofox size 10',
                'description' => 'Classpass size 3 and glofox size 10',
                'size' => 10,
                'duration' => 60,
                'date' => new MongoDate(strtotime('+1 weeks')),
                'time_start' => new MongoDate(strtotime('+1 weeks 5 hours')),
                'time_finish' => new MongoDate(strtotime('+1 weeks 6 hours')),
                'trainers' => ['5adf4d52f35bbcfb4251f960'],
                'facility' => '5adf4cb5fca91f2d25f62df4',
                'total_bookings' => 3,
            ],
            // Classpass size 0 and glofox size 10
            [
                '_id' => new MongoId('5adf53fa32d8a6d54137ffb4'),
                'branch_id' => '5addc25383266f65abf515c4',
                'namespace' => 'classpass_integrated',
                'active' => true,
                'program_id' => '5adf44e7cdd89bdf93f1df8e',
                'schedule_code' => uniqid('', false),
                'name' => 'Classpass size 0 and glofox size 10',
                'description' => 'Classpass size 0 and glofox size 10',
                'size' => 10,
                'duration' => 60,
                'date' => new MongoDate(strtotime('+1 weeks')),
                'time_start' => new MongoDate(strtotime('+1 weeks 7 hours')),
                'time_finish' => new MongoDate(strtotime('+1 weeks 8 hours')),
                'trainers' => ['5adf4d52f35bbcfb4251f960'],
                'facility' => '5adf4cb5fca91f2d25f62df4',
                'total_bookings' => 3,
            ],
            // No classpass size set and glofox size 10
            [
                '_id' => new MongoId('5adf5449f71d2fb4f4b9c5ea'),
                'branch_id' => '5addc25383266f65abf515c4',
                'namespace' => 'classpass_integrated',
                'active' => true,
                'program_id' => '5adf44ee8181e92a30c4f6ab',
                'schedule_code' => uniqid('', false),
                'name' => 'No classpass size set and glofox size 10',
                'description' => 'No classpass size set and glofox size 10',
                'size' => 10,
                'duration' => 60,
                'date' => new MongoDate(strtotime('+1 weeks')),
                'time_start' => new MongoDate(strtotime('+1 weeks 9 hours')),
                'time_finish' => new MongoDate(strtotime('+1 weeks 10 hours')),
                'trainers' => ['5adf4d52f35bbcfb4251f960'],
                'facility' => '5adf4cb5fca91f2d25f62df4',
                'total_bookings' => 3,
            ],
            // Classpass size 1 and glofox size 9
            [
                '_id' => new MongoId('5ae1b7a0df7366855fd28702'),
                'branch_id' => '5addc25383266f65abf515c4',
                'namespace' => 'classpass_integrated',
                'active' => true,
                'program_id' => '5ae1b7d13b484d636f8982d2',
                'schedule_code' => uniqid('', false),
                'name' => 'Classpass size 1 and glofox size 9',
                'description' => 'Classpass size 1 and glofox size 9',
                'size' => 10,
                'duration' => 60,
                'date' => new MongoDate(strtotime('+1 weeks')),
                'time_start' => new MongoDate(strtotime('+1 weeks 5 hours')),
                'time_finish' => new MongoDate(strtotime('+1 weeks 6 hours')),
                'trainers' => ['5adf4d52f35bbcfb4251f960'],
                'facility' => '5adf4cb5fca91f2d25f62df4',
                'total_bookings' => 3,
            ],
            // Classpass size 3 and glofox size 4 and 2 bookings
            [
                '_id' => new MongoId('5ae99803c53fbc7079cfd7fc'),
                'branch_id' => '5addc25383266f65abf515c4',
                'namespace' => 'classpass_integrated',
                'active' => true,
                'program_id' => '5ae897cf98cd3d0a0f2cffd0',
                'schedule_code' => uniqid('', false),
                'name' => 'Classpass size 3 and glofox size 4 and 2 bookings',
                'description' => 'Classpass size 3 and glofox size 4 and 2 bookings',
                'size' => 10,
                'duration' => 60,
                'date' => new MongoDate(strtotime('+1 weeks')),
                'time_start' => new MongoDate(strtotime('+1 weeks 5 hours')),
                'time_finish' => new MongoDate(strtotime('+1 weeks 6 hours')),
                'trainers' => ['5adf4d52f35bbcfb4251f960'],
                'facility' => '5adf4cb5fca91f2d25f62df4',
                'total_bookings' => 2,
            ],
            // Event full with one waiting list booking
            [
                '_id' => new MongoId('5b043d0d45702d48c57deda7'),
                'branch_id' => '5addc25383266f65abf515c4',
                'namespace' => 'classpass_integrated',
                'active' => true,
                'program_id' => '5b057507b8e809c018bb33c1',
                'schedule_code' => uniqid('', false),
                'level' => 'Advanced',
                'name' => 'Event full with one waiting list booking',
                'description' => 'Event full with one waiting list booking',
                'size' => 1,
                'duration' => 60,
                'date' => new MongoDate(strtotime('+1 weeks')),
                'time_start' => new MongoDate(strtotime('+1 weeks 6 hours')),
                'time_finish' => new MongoDate(strtotime('+1 weeks 7 hours')),
                'trainers' => ['58568a8fa875ab19530041a7'],
                'facility' => '52a7011a05c677bda826611b',
                'total_bookings' => 0,
            ],
            // Event from program with Single Price - 5 members and 8 payg
            [
                '_id' => new MongoId('5b17f057720d8541a113f0fb'),
                'branch_id' => '5addc25383266f65abf515c4',
                'namespace' => 'classpass_integrated',
                'active' => true,
                'program_id' => '5b17ee4386631b726f173fab',
                'schedule_code' => uniqid('', false),
                'name' => 'Program with Single Price - 5 members and 8 payg',
                'description' => 'Description',
                'size' => 10,
                'duration' => 60,
                'date' => new MongoDate(strtotime('+8 days')),
                'time_start' => new MongoDate(strtotime('+8 days 6 hours')),
                'time_finish' => new MongoDate(strtotime('+8 days 7 hours')),
                'trainers' => ['58568a8fa875ab19530041a7'],
                'facility' => '52a7011a05c677bda826611b',
                'total_bookings' => 0,
                'level' => 'Advanced',
                'external_provider_stream_url' => 'https://youtu.be/oavMtUWDBTM',
                'created' => Carbon::now()->getTimestamp(),
                'modified' => Carbon::now()->getTimestamp(),
            ],
            // Event from program with Single Price - 7 members and no payg price
            [
                '_id' => new MongoId('5b181037876d8793067a880c'),
                'branch_id' => '5addc25383266f65abf515c4',
                'namespace' => 'classpass_integrated',
                'active' => true,
                'program_id' => '5b18105eac03203e32b30eca',
                'schedule_code' => uniqid('', false),
                'name' => 'program with Single Price - 7 members and no payg price',
                'size' => 10,
                'duration' => 60,
                'date' => new MongoDate(strtotime('today +1 days')),
                'time_start' => new MongoDate(strtotime('today +1 days 4 hours')),
                'time_finish' => new MongoDate(strtotime('today +1 days 5 hours')),
                'trainers' => ['58568a8fa875ab19530041a7'],
                'facility' => '52a7011a05c677bda826611b',
                'total_bookings' => 0,
            ],
            // Event from program with Custom Price for one membership and price for payg
            [
                '_id' => new MongoId('5b19496a3afda09bd6f8557e'),
                'branch_id' => '5addc25383266f65abf515c4',
                'namespace' => 'classpass_integrated',
                'active' => true,
                'program_id' => '5b19488b6b62ac9661ad6655',
                'schedule_code' => uniqid('', false),
                'name' => 'program with Custom Price for one membership and price for payg',
                'size' => 10,
                'duration' => 60,
                'date' => new MongoDate(strtotime('+9 days')),
                'time_start' => new MongoDate(strtotime('+9 days 6 hours')),
                'time_finish' => new MongoDate(strtotime('+9 days 7 hours')),
                'trainers' => ['58568a8fa875ab19530041a7'],
                'facility' => '52a7011a05c677bda826611b',
                'total_bookings' => 0,
            ],
            // Event from program with Single Price - 5 members and 8 payg
            [
                '_id' => new MongoId('5b1ffbb93ea27bbd65e91758'),
                'branch_id' => '5addc25383266f65abf515c4',
                'namespace' => 'classpass_integrated',
                'active' => true,
                'program_id' => '5b17ee4386631b726f173fab',
                'schedule_code' => uniqid('', false),
                'name' => 'Program with Single Price - 5 members and 8 payg',
                'size' => 10,
                'duration' => 60,
                'date' => new MongoDate(strtotime('+4 days')),
                'time_start' => new MongoDate(strtotime('+4 days 6 hours')),
                'time_finish' => new MongoDate(strtotime('+4 days 7 hours')),
                'trainers' => ['58568a8fa875ab19530041a7'],
                'facility' => '52a7011a05c677bda826611b',
                'total_bookings' => 0,
            ],

            [
                '_id' => new MongoId('5b1ffbb93ea27bbd65e91760'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'program_id' => '5b19488b6b62ac9661ad6656',
                'schedule_code' => uniqid('', false),
                'level' => 'Advanced',
                'name' => 'Test',
                'description' => 'Class',
                'size' => 20,
                'duration' => 60,
                'date' => new MongoDate(1_527_138_000),
                'time_start' => new MongoDate(1_527_138_000),
                'time_finish' => new MongoDate(1_527_140_000),
                'trainers' => ['58568a8fa875ab19530041b1'],
                'facility' => '52a7011a05c677bda826611b',
            ],

            [
                '_id' => new MongoId('5b1ffbb93ea27bbd65e91761'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'program_id' => '5b19488b6b62ac9661ad6657',
                'schedule_code' => uniqid('', false),
                'level' => 'Advanced',
                'name' => 'Test',
                'description' => 'Class',
                'size' => 20,
                'duration' => 60,
                'date' => new MongoDate(1_527_138_000),
                'time_start' => new MongoDate(1_527_138_000),
                'time_finish' => new MongoDate(1_527_140_000),
                'trainers' => ['58568a8fa875ab19530041b1'],
                'facility' => '52a7011a05c677bda826611b',
            ],
            [
                '_id' => new MongoId('5b1ffbb93ea27bbd65e91762'),
                'branch_id' => '5addc25383266f65abf515c4',
                'namespace' => 'classpass_integrated',
                'active' => true,
                'program_id' => '5b19488b6b62ac9661ad6658',
                'schedule_code' => uniqid('', false),
                'level' => 'Advanced',
                'name' => 'Test',
                'description' => 'Class',
                'size' => 20,
                'duration' => 60,
                'date' => new MongoDate(strtotime('+4 days')),
                'time_start' => new MongoDate(strtotime('+4 days 6 hours')),
                'time_finish' => new MongoDate(strtotime('+4 days 7 hours')),
                'trainers' => ['58568a8fa875ab19530041b1'],
                'facility' => '52a7011a05c677bda826611b',
            ],

            [
                '_id' => new MongoId('5b1ffbb93ea27bbd65e91763'),
                'branch_id' => '5addc25383266f65abf515c4',
                'namespace' => 'classpass_integrated',
                'active' => true,
                'program_id' => '5b19488b6b62ac9661ad6658',
                'schedule_code' => uniqid('', false),
                'level' => 'Advanced',
                'name' => 'Test',
                'description' => 'Class',
                'size' => 20,
                'duration' => 60,
                'date' => new MongoDate(strtotime('+4 days')),
                'time_start' => new MongoDate(strtotime('+4 days 6 hours')),
                'time_finish' => new MongoDate(strtotime('+4 days 7 hours')),
                'trainers' => ['58568a8fa875ab19530041b1'],
                'facility' => '52a7011a05c677bda826611b',
            ],

            // Event from program with Custom Price for one membership and price for payg
            [
                '_id' => new MongoId('5b5b2883be993a2666f47071'),
                'branch_id' => '5b5b265bce2cf6bdb9ad6d9a',
                'namespace' => 'ukclient',
                'active' => true,
                'program_id' => '5b5b28a73355dafa1a354d87',
                'schedule_code' => uniqid('', false),
                'name' => 'program with Custom Price for one membership and price for payg',
                'size' => 10,
                'duration' => 60,
                'date' => new MongoDate(strtotime('+8 days')),
                'time_start' => new MongoDate(strtotime('+8 days 6 hours')),
                'time_finish' => new MongoDate(strtotime('+8 days 7 hours')),
                'trainers' => ['58568a8fa875ab19530041a7'],
                'facility' => '52a7011a05c677bda826611b',
                'total_bookings' => 0,
            ],
            [
                '_id' => new MongoId('5b7bee2222ffc68fdb72b745'),
                'branch_id' => '5addc25383266f65abf515c4',
                'namespace' => 'classpass_integrated',
                'active' => true,
                'program_id' => '5b7bede071c02a37fc6b3699',
                'schedule_code' => uniqid('', false),
                'level' => 'Advanced 123',
                'name' => 'Test 123',
                'description' => 'Class',
                'size' => 20,
                'duration' => 60,
                'date' => new MongoDate(strtotime('+4 days')),
                'time_start' => new MongoDate(strtotime('+4 days 6 hours')),
                'time_finish' => new MongoDate(strtotime('+4 days 7 hours')),
                'trainers' => ['58568a8fa875ab19530041b1'],
                'facility' => '52a7011a05c677bda826611b',
            ],
            // For testing adding the is_online virtual field for events
            [
                '_id' => new MongoId('5e7cb66a3d36311bf52c63d6'),
                'branch_id' => '5e7cb6a53d36311bf52c63d8',
                'namespace' => 'namespace-5e7cb6a53d36311bf52c63d8',
                'active' => true,
                'program_id' => '5b7bede071c02a37fc6b3699',
                'level' => 'basic',
                'name' => 'Online Event 1',
                'description' => 'Online Event 1',
                'size' => 20,
                'private' => false,
                'duration' => 60,
                'schedule_code' => uniqid('EVENT_', false),
                'date' => new MongoDate(strtotime('today')),
                'time_start' => new MongoDate(strtotime('+5 minutes')),
                'time_finish' => new MongoDate(strtotime('+65 minutes')),
                'trainers' => ['58568a8fa875ab19530041b1'],
                'facility' => '5e7cb5743d36311bf52c63d5', // An online facility
                'manually_deleted' => false,
            ],
            [
                '_id' => new MongoId('5e7cb6713d36311bf52c63d7'),
                'branch_id' => '5e7cb6a53d36311bf52c63d8',
                'namespace' => 'namespace-5e7cb6a53d36311bf52c63d8',
                'active' => true,
                'program_id' => '5b7bede071c02a37fc6b3699',
                'schedule_code' => uniqid('', false),
                'level' => 'basic',
                'name' => 'On-site Event 2',
                'description' => 'On-site Event 2',
                'size' => 20,
                'duration' => 60,
                'date' => new MongoDate(strtotime('+4 days')),
                'time_start' => new MongoDate(strtotime('+1 day 5 minutes')),
                'time_finish' => new MongoDate(strtotime('+1 day 5 minutes')),
                'trainers' => ['58568a8fa875ab19530041b1'],
                'facility' => '52a7011a05c677bda826611b', // Not an online facility
            ],
            [
                '_id' => new MongoId('5e7cb6713d36321bf52c63d7'),
                'branch_id' => '5d56d214be51ef060f38408f',
                'namespace' => 'namespace-5d56d214be51ef060f38408f',
                'active' => true,
                'schedule_code' => '123456654',
                'program_id' => '5b7bede071c02a37fc6b3699',
                'level' => 'basic',
                'name' => 'Testing bookings times change when event is edited',
                'description' => 'Bookings times',
                'size' => 20,
                'duration' => 60,
                'date' => new MongoDate(strtotime('2020-01-01 00:00:00')),
                'time_start' => new MongoDate(strtotime('2020-01-01 13:00:00')),
                'time_finish' => new MongoDate(strtotime('2020-01-01 14:00:00')),
                'trainers' => ['58568a8fa875ab19530041b1'],
                'facility' => '52a7011a05c677bda826611b',
            ],
            [
                '_id' => new MongoId('5e7cb6703d37321bf52c63d7'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'program_id' => '13b7411a15c676bda824611b',
                'schedule_code' => uniqid('', false),
                'level' => 'Advanced',
                'name' => 'Testing Attandance Submission',
                'description' => 'Class',
                'size' => 2,
                'duration' => 60,
                'date' => date('Y-m-d H:i:s', strtotime('2021-01-01 00:00')),
                'time_start' => date('Y-m-d H:i:s', strtotime('2021-01-01 13:05')),
                'time_finish' => date('Y-m-d H:i:s', strtotime('2021-01-01 14:05')),
                'trainers' => ['58568a8fa875ab19530041b5'],
                'facility' => '52a7011a05c677bda826611b',
            ],
            [
                '_id' => new MongoId('5e7cb6703d37321bf52c63d2'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'program_id' => '13b7411a15c676bda824611b',
                'schedule_code' => uniqid('', false),
                'level' => 'Advanced',
                'name' => 'Testing Attandance Submission cannot be submitted for classes in the future',
                'description' => 'Class',
                'size' => 2,
                'duration' => 60,
                'date' => new MongoDate(strtotime('+100 days')),
                'time_start' => new MongoDate(strtotime('+100 days 6 hours')),
                'time_finish' => new MongoDate(strtotime('+100 days 7 hours')),
                'trainers' => ['58568a8fa875ab19530041a7'],
                'facility' => '52a7011a05c677bda826611b',
            ],
            [
                '_id' => new MongoId('5e7cb6703d37321bf52c63d1'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'program_id' => '13b7411a15c676bda824611b',
                'schedule_code' => uniqid('', false),
                'level' => 'Advanced',
                'name' => 'Testing Attandance Submission cannot be submitted twice',
                'attendance_submitted' => true,
                'description' => 'Class',
                'size' => 2,
                'duration' => 60,
                'date' => new MongoDate(strtotime('-10 days')),
                'time_start' => new MongoDate(strtotime('-10 days 7 hours')),
                'time_finish' => new MongoDate(strtotime('-10 days 6 hours')),
                'trainers' => ['58568a8fa875ab19530041a7'],
                'facility' => '52a7011a05c677bda826611b',
            ],
            [
                '_id' => new MongoId('79b7012a05c677c9a512504d'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'program_id' => '5b5b28a73355dafa1a354d88',
                'level' => 'Advanced',
                'name' => 'RecurringBookings',
                'description' => 'Class',
                'size' => 20,
                'duration' => 60,
                'date' => new MongoDate(strtotime('this Sunday')),
                'time_start' => new MongoDate(strtotime('this Sunday 8am')),
                'time_finish' => new MongoDate(strtotime('this Sunday 9am')),
                'trainers' => ['58568a8fa875ab19530041aa'],
                'facility' => '52a7011a05c677bda82661bb',
                'schedule_code' => '5856c03455fd1',
            ],
            [
                '_id' => new MongoId('61a4f20c2df7cc24846a5d46'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'program_id' => '13b7411a15c676bda824611b',
                'schedule_code' => uniqid('', false),
                'level' => 'Advanced',
                'name' => 'Event for reservation',
                'attendance_submitted' => true,
                'description' => 'Class',
                'size' => 10,
                'duration' => 60,
                'date' => new MongoDate(strtotime('-10 days')),
                'time_start' => new MongoDate(strtotime('-10 days 7 hours')),
                'time_finish' => new MongoDate(strtotime('-10 days 6 hours')),
                'trainers' => ['59a7011a05c677bda916612c'],
                'facility' => '52a7011a05c677bda826611b',
            ],
            [
                '_id' => new MongoId('62790d43840f2b6eee694503'),
                'branch_id' => '6279159a1d7cc351e50674bb',
                'namespace' => 'midnightb',
                'active' => true,
                'program_id' => '13b7411a15c676bda824611b',
                'schedule_code' => uniqid('', false),
                'level' => 'Advanced',
                'name' => 'Midnight Event',
                'attendance_submitted' => true,
                'description' => 'Class',
                'size' => 10,
                'date' => new MongoDate(strtotime('2022-01-01 00:00:00')),
                'time_start' => new MongoDate(strtotime('2022-01-01 23:00:00')),
                'time_finish' => new MongoDate(strtotime('2022-01-01 00:00:00')),
                'trainers' => ['59a7011a05c677bda916612c'],
                'facility' => '52a7011a05c677bda826611b',
            ],

            ...$this->addEventForCi1808UseCase(),
            ...$this->addEventForCi1263UseCase(),
            ...$this->addEventForMaximumBookingUseCase(),
            ...$this->addEventsForTestingBookingsOverlappingValidations(),
            ...$this->addEventsForGetTotalBookingsAndTotalWaiting(),
            ...$this->addEventsForBookingsControllerDateRangeTest(),
            ...$this->forVirtualSlotTest(),
            ...$this->forBookingWindowTest(),
            ...$this->forGetAppointmentStaffTest(),
            ...$this->forFindLastEventByProgramId(),
            ...$this->forClassPassOverBooking(),
            ...$this->forEventsEndpointModifiedFiltering(),
            ...$this->forRecurringClassBookings(),
            ...$this->forEventsBatchId(),
            ...$this->forActiveLimitBookings(),
            ...$this->forForceOverBookSizeChange(),
            ...$this->forIntegrationTests(),
            ...$this->forEventsForGeneratorComponent(),
            ...$this->forEventBookingsTotalTests(),
            ...$this->forEventDeletedEventHandle(),
            ...$this->forRestrictedMembershipNoCreditsNextCycle(),
            ...$this->forFailureReason(),
            ...$this->forEventsUpdate(),
            ...$this->forEventsUpdateBasedOnProgramChanges(),
            ...$this->forRoamingWaitingList(),
            ...$this->forEventsUpdateEventsGeneratorBasedOnProgramChanges(),
            ...$this->forEventGeneratorComponent(),
            ...$this->forWaitingListEmailOnRoaming(),
            ...$this->forProgramUpsertService(),
            ...$this->forOverdueMembershipBookings(),
            ...$this->forNonDeleteEventsWithCanceledBookings(),
            ...$this->forCategoryAndModelValidations(),
        ];
        parent::init();
    }

    private function forEventsForGeneratorComponent(): array
    {
        return [
            [
                '_id' => new MongoId('f1b993566536aa7a3f542201'),
                'branch_id' => '49a7011a05c677bfa916612a',
                'date' => new MongoDate(strtotime('2024-04-29T00:00:00+02:00')),
                'time_start' => new MongoDate(strtotime('2024-04-29T08:00:00+02:00')),
                'time_finish' => new MongoDate(strtotime('2024-04-29T09:00:00+02:00')),
                'private' => false,
                'active' => true,
                'program_id' => '5adf442bb2db1df852f44df1',
                'schedule_code' => '5856c03455ffa',
                'size' => 2,
                'namespace' => 'glofox',
                'week_day' => 1,
                'modified' => new MongoDate(strtotime('2024-05-04T02:30:00+02:00')),
                'created' => new MongoDate(strtotime('2024-05-04T02:30:00+02:00')),
                'manually_deleted' => false,
            ]
        ];
    }

    private function addEventForCi1808UseCase(): array
    {
        return [
            [
                '_id' => new MongoId('61b993566536aa7a3f542201'),
                'private' => false,
                'level' => '',
                'active' => true,
                'date' => new MongoDate(strtotime('+16 days')),
                'description' => '',
                'facility' => '5fbd7c16f43e994fe22bb849',
                'name' => 'Barre Special BTS',
                'program_id' => '60e27f21b35f5115330cf25b',
                'schedule_code' => '1639551829370',
                'size' => 2,
                'time_finish' => new MongoDate(strtotime('+16 days')),
                'time_start' => new MongoDate(strtotime('+17 days')),
                'trainers' => [
                    '5fd1ab5251bb5b027b30ed5f',
                ],
                'branch_id' => '5a9591bcdb07bce527400717',
                'namespace' => 'glofox',
                'week_day' => 5,
                'modified' => new MongoDate(strtotime('2021-12-23 06:20:34')),
                'created' => new MongoDate(strtotime('2021-12-15 07:03:50')),
                'total_bookings' => 1,
                'total_waitings' => 1,
            ],
            [
                '_id' => new MongoId('61e92de584f9284bae3ee72d'),
                'private' => false,
                'level' => 'Mixed Levels',
                'active' => true,
                'date' => new MongoDate(strtotime('+1 days')),
                'description' => '',
                'facility' => '5fbd7c16f43e994fe22bb849',
                'name' => 'Reformer Abs Arms Ass',
                'program_id' => '61c2ee6eff7d573d344c7445',
                'schedule_code' => '1642671588560',
                'size' => 2,
                'time_finish' => new MongoDate(strtotime('+1 day')),
                'time_start' => new MongoDate(strtotime('+2 day')),
                'trainers' => [
                    '5fd19359e64bf27d9615c7f5',
                ],
                'branch_id' => '5a9591bcdb07bce527400717',
                'namespace' => 'glofox',
                'week_day' => 3,
                'modified' => new MongoDate(strtotime('2022-02-07 12:17:20')),
                'created' => new MongoDate(strtotime('2022-01-20 09:39:49')),
                'total_bookings' => 1,
                'total_waitings' => 1,
            ],
        ];
    }

    private function addEventForCi1263UseCase(): array
    {
        return [
            [
                "_id" => new MongoId("620327ba71db7e659f3f8cdd"),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                "program_id" => "5d198f6dfa10dd3f4b688d72",
                "schedule_code" => "5ec2dcd1a2a9d",
                "active" => true,
                "name" => "Open Gym",
                "description" => "Open Gym at BODYZONE",
                "date" => new MongoDate(strtotime("+7 days")),
                "time_start" => new MongoDate(strtotime("+7 days")),
                "time_finish" => new MongoDate(strtotime("+8 days")),
                "week_day" => "3",
                "level" => "All Levels",
                "size" => 10,
                "facility" => "5ccb4c2aed1ea1058865db35",
                "trainers" => [
                    "5d1c95cf16715f02fe16e819",
                ],
            ],
        ];
    }

    private function addEventForMaximumBookingUseCase(): array
    {
        $baseDate = "2024-10-01";
        return [
            [
                "_id" => new MongoId("620327ba71db7e659f3f8cd1"),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                "program_id" => "5b5b28a73355dafa1a354d88",
                "schedule_code" => "5ec2dcd1a2a9d",
                "active" => true,
                "name" => "Max1",
                "description" => "Max 2 Bookings Class",
                "date" => new MongoDate(strtotime("$baseDate 00:00:00")),
                "time_start" => new MongoDate(strtotime("$baseDate 08:00:00")),
                "time_finish" => new MongoDate(strtotime("$baseDate 09:00:00")),
                "week_day" => "3",
                "level" => "All Levels",
                "size" => 10,
                "facility" => "5ccb4c2aed1ea1058865db35",
                "trainers" => [
                    "59a7011a05c677bda916612c",
                ],
            ],
            [
                "_id" => new MongoId("620327ba71db7e659f3f8cd2"),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                "program_id" => "5d198f6dfa10dd3f4b688d72",
                "schedule_code" => "5ec2dcd1a2a9d",
                "active" => true,
                "name" => "Max2",
                "description" => "Max 2 Bookings Class",
                "date" => new MongoDate(strtotime("$baseDate 00:00:00")),
                "time_start" => new MongoDate(strtotime("$baseDate 13:00:00")),
                "time_finish" => new MongoDate(strtotime("$baseDate 14:00:00")),
                "week_day" => "3",
                "level" => "All Levels",
                "size" => 10,
                "facility" => "5ccb4c2aed1ea1058865db35",
                "trainers" => [
                    "59a7011a05c677bda916612c",
                ],
            ],
            [
                "_id" => new MongoId("620327ba71db7e659f3f8cd3"),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                "program_id" => "5d198f6dfa10dd3f4b688d72",
                "schedule_code" => "5ec2dcd1a2a9d",
                "active" => true,
                "name" => "Max3",
                "description" => "Booking will be denied",
                "date" => new MongoDate(strtotime("$baseDate 00:00:00")),
                "time_start" => new MongoDate(strtotime("$baseDate 18:00:00")),
                "time_finish" => new MongoDate(strtotime("$baseDate 19:00:00")),
                "week_day" => "3",
                "level" => "All Levels",
                "size" => 10,
                "facility" => "5ccb4c2aed1ea1058865db35",
                "trainers" => [
                    "59a7011a05c677bda916612c",
                ],
            ],
        ];
    }

    private function addEventsForTestingBookingsOverlappingValidations(): array
    {
        return [
            [
                "_id" => new MongoId("637ddb3ec20e797066dbdb9e"),
                'branch_id' => static::GLOFOX_BRANCH_ID,
                'namespace' => static::GLOFOX_NAMESPACE,
                "program_id" => static::GLOFOX_PROGRAM_ID,
                "schedule_code" => "5ec2dcd1a2a9d",
                "active" => true,
                "name" => "Test overlapping validations",
                "description" => "Test overlapping validations description",
                "date" => new MongoDate(strtotime("+107 days 1 hours")),
                "time_start" => new MongoDate(strtotime("2022-01-01 15:00:00")),
                "time_finish" => new MongoDate(strtotime("2022-01-01 16:00:00")),
                "week_day" => "6",
                "level" => "All Levels",
                "size" => 10,
                "facility" => "5ccb4c2aed1ea1058865db35",
                "trainers" => [
                    "58568a8fa875ab19530041b8",
                ],
            ],
        ];
    }

    private function addEventsForGetTotalBookingsAndTotalWaiting(): array
    {
        return [
            [
                "_id" => new MongoId("63d93312ece438f37486ee2c"),
                'branch_id' => static::GLOFOX_BRANCH_ID,
                'namespace' => static::GLOFOX_NAMESPACE,
                "program_id" => static::GLOFOX_PROGRAM_ID,
                "schedule_code" => "5ec2dcd1a2a9d",
                "active" => true,
                "name" => "Abs intense workout",
                "description" => "Abs intense workout description",
                "date" => null,
                "time_start" => null,
                "time_finish" => null,
                "level" => "All Levels",
                "size" => 10,
                "facility" => "5ccb4c2aed1ea1058865db35",
                "trainers" => [
                    "59a7011a05c677bda916612c",
                ],
            ],
            [
                "_id" => new MongoId("63d9408704d422118e7116e0"),
                'branch_id' => static::GLOFOX_BRANCH_ID,
                'namespace' => static::GLOFOX_NAMESPACE,
                "program_id" => static::GLOFOX_PROGRAM_ID,
                "schedule_code" => "5ec2dcd1a2a9d",
                "active" => true,
                "name" => "Abs intense workout",
                "description" => "Abs intense workout description",
                "date" => null,
                "time_start" => null,
                "time_finish" => null,
                "level" => "All Levels",
                "size" => 10,
                "facility" => "5ccb4c2aed1ea1058865db35",
                "trainers" => [
                    "59a7011a05c677bda916612c",
                ],
            ],
        ];
    }

    private function addEventsForBookingsControllerDateRangeTest(): array
    {
        return [
            [
                '_id' => new MongoId('63e0ca1064f091a1d8eaf376'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'program_id' => '63e0ca85ef7ce00f799d1c65',
                'schedule_code' => uniqid('', false),
                'level' => 'Advanced',
                'name' => 'Event for reservation',
                'attendance_submitted' => true,
                'description' => 'Class',
                'size' => 10,
                'duration' => 60,
                'date' => new MongoDate(strtotime('2020-01-01')),
                'time_start' => new MongoDate(strtotime('2020-01-01 08:00:00')),
                'time_finish' => new MongoDate(strtotime('2020-01-01 09:00:00')),
                'trainers' => ['59a7011a05c677bda916612c'],
                'facility' => '52a7011a05c677bda826611b',
            ],
            [
                '_id' => new MongoId('63e0cb32a7064e73bd5833d6'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'program_id' => '63e0ca85ef7ce00f799d1c65',
                'schedule_code' => uniqid('', false),
                'level' => 'Advanced',
                'name' => 'Event for reservation',
                'attendance_submitted' => true,
                'description' => 'Class',
                'size' => 10,
                'duration' => 60,
                'date' => new MongoDate(strtotime('2020-01-02')),
                'time_start' => new MongoDate(strtotime('2020-01-02 08:00:00')),
                'time_finish' => new MongoDate(strtotime('2020-01-02 09:00:00')),
                'trainers' => ['59a7011a05c677bda916612c'],
                'facility' => '52a7011a05c677bda826611b',
            ],
        ];
    }

    private function forVirtualSlotTest(): array
    {
        return [
            [
                '_id' => new MongoId('64620e8a3bfae9d753b2200a'),
                'branch_id' => static::VIRTUAL_SLOT_BRANCH_ID,
                'namespace' => static::VIRTUAL_SLOT_BRANCH_NAMESPACE,
                'active' => true,
                'program_id' => static::VIRTUAL_SLOT_PROGRAM_ID,
                'schedule_code' => uniqid('', true),
                'level' => 'Advanced',
                'name' => 'Virtual slot class event',
                'attendance_submitted' => true,
                'description' => 'Class virtual slot test',
                'size' => 100,
                'duration' => 60,
                'date' => new MongoDate(strtotime('2023-04-28')),
                'time_start' => new MongoDate(strtotime('2023-04-28 08:00:00')),
                'time_finish' => new MongoDate(strtotime('2023-04-28 09:00:00')),
                'trainers' => [static::VIRTUAL_SLOT_STAFF_ID],
                'facility' => static::VIRTUAL_SLOT_FACILITY_ID,
            ],
        ];
    }

    private function forBookingWindowTest(): array
    {
        return [
            [
                '_id' => new MongoId('6477510f6938500d745c4edb'),
                'branch_id' => '64784ace2c8a824784ab4cc4',
                'namespace' => 'classbookingwindow',
                'active' => true,
                'program_id' => '6477520fb95e96bf7792d62d',
                'schedule_code' => uniqid('', false),
                'level' => 'Advanced',
                'name' => 'Test',
                'description' => 'Class',
                'size' => 20,
                'duration' => 60,
                'date' => new MongoDate(strtotime('+4 hours')),
                'time_start' => new MongoDate(Carbon::now()->AddHours(170)->getTimestamp()),
                'time_finish' => new MongoDate(Carbon::now()->AddHours(171)->getTimestamp()),
                'trainers' => ['64784b4628c2ddf21e169901'],
                'facility' => '647753003940c6c48aab0441',
            ],
        ];
    }

    private function forGetAppointmentStaffTest()
    {
        return [
            [
                '_id' => new MongoId('49b7012a05c677c9a512503e'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'program_id' => '13b7411a15c676bda824611d',
                'schedule_code' => uniqid('', false),
                'level' => 'Advanced',
                'name' => 'Test App Staff',
                'description' => 'App Staff',
                'size' => 20,
                'duration' => 60,
                //2023-07-28
                'date' => new MongoDate(1_690_513_200),
                //2023-07-28 17:00:00
                'time_start' => new MongoDate(1_690_574_400),
                //2023-07-28 18:00:00
                'time_finish' => new MongoDate(1_690_578_000),
                'trainers' => ['58568a8fa875ab19530041a1'],
                'facility' => '52a7011a05c677bda826611b',
            ],
        ];
    }

    private function forFindLastEventByProgramId(): array
    {
        return [
            [
                '_id' => new MongoId('6538ded562890c7a3f572b00'),
                'active' => false,
                'program_id' => '6538df22436c97f1c19098a2',
                'schedule_code' => 'test-schedule-code-3785',
                'date' => new MongoDate(strtotime('2023-11-29 00:00:00')),
            ],
            [
                '_id' => new MongoId('6538e282233bd1dba5b9c933'),
                'active' => false,
                'program_id' => '6538df22436c97f1c19098a2',
                'schedule_code' => 'test-schedule-code-3785',
                'date' => new MongoDate(strtotime('2023-11-29 00:00:00')),
                'manually_deleted' => false,
            ],
            [
                '_id' => new MongoId('6538e512b8f451878da1d12e'),
                'active' => true,
                'program_id' => '6538df22436c97f1c19098a2',
                'date' => new MongoDate(strtotime('2023-11-30 00:00:00')),
                'manually_deleted' => false,
            ],
            [
                '_id' => new MongoId('6538e28b3ec71ef37c33cb39'),
                'active' => true,
                'program_id' => '6538df22436c97f1c19098a2',
                'schedule_code' => 'test-schedule-code-3785',
                'date' => new MongoDate(strtotime('2023-11-29 00:00:00')),
                'manually_deleted' => false,
            ],
            [
                '_id' => new MongoId('6538e293f0d65baa43dca2f3'),
                'active' => true,
                'program_id' => '6538df22436c97f1c19098a2',
                'schedule_code' => 'test-schedule-code-3785',
                'date' => new MongoDate(strtotime('2023-11-26 00:00:00')),
            ],
            [
                '_id' => new MongoId('6538e29aee60be61de93c213'),
                'active' => true,
                'program_id' => '6538df22436c97f1c19098a2',
                'schedule_code' => 'test-schedule-code-3785',
                'manually_deleted' => false,
                'date' => new MongoDate(strtotime('2023-11-22 00:00:00')),
            ],
            [
                '_id' => new MongoId('6538e962fdcca97fee567610'),
                'active' => false,
                'program_id' => '6538e932d5bd0f28f8ebc3c3',
                'schedule_code' => 'test-schedule-code-4569',
                'date' => new MongoDate(strtotime('2023-11-30 00:00:00')),
                'manually_deleted' => false,
            ],
            [
                '_id' => new MongoId('6538e968cb5695cdedb2a61c'),
                'active' => true,
                'program_id' => '6538e932d5bd0f28f8ebc3c3',
                'date' => new MongoDate(strtotime('2023-11-30 00:00:00')),
                'manually_deleted' => false,
            ],
            [
                '_id' => new MongoId('6538e96f2c8f1b0a8fc21229'),
                'active' => true,
                'program_id' => '6538e932d5bd0f28f8ebc3c3',
                'schedule_code' => 'test-schedule-code-4569',
                'date' => new MongoDate(strtotime('2023-11-29 00:00:00')),
                'manually_deleted' => false,
            ],
            [
                '_id' => new MongoId('6538e9abb7cec97cec642dc8'),
                'active' => true,
                'program_id' => '6538e932d5bd0f28f8ebc3c3',
                'schedule_code' => 'test-schedule-code-4569',
                'date' => new MongoDate(strtotime('2023-11-22 00:00:00')),
                'manually_deleted' => false,
            ],
        ];
    }

    private function forClassPassOverBooking(): array
    {
        return [
            [
                '_id' => new MongoId('6477510f6938600d745c4edb'),
                'branch_id' => '64784ace2c8a824784ab4cc4',
                'namespace' => 'classpassoverbooking',
                'active' => true,
                'program_id' => '6477520fb96e96bf7792d62d',
                'schedule_code' => uniqid('', false),
                'level' => 'Advanced',
                'name' => 'Test',
                'description' => 'ClassPass over booking',
                'size' => 2,
                'duration' => 60,
                'date' => new MongoDate(Carbon::today()->getTimestamp()),
                'time_start' => new MongoDate(Carbon::now()->AddHours(2)->getTimestamp()),
                'time_finish' => new MongoDate(Carbon::now()->AddHours(3)->getTimestamp()),
                'trainers' => ['64784b4628c2ddf21e169901'],
                'facility' => '647753003940c6c48aab0441',
                'private' => false,
            ],
        ];
    }

    private function forEventsEndpointModifiedFiltering(): array
    {
        return [
            $this->createEvent([
                '_id' => new MongoId('6538ded562890c7a3f572b01'),
                'modified' => new MongoDate(strtotime('2024-01-11 08:00:00')),
            ]),
            $this->createEvent([
                '_id' => new MongoId('6538ded562890c7a3f572b02'),
                'modified' => new MongoDate(strtotime('2024-01-11 13:00:00')),
            ]),
            $this->createEvent([
                '_id' => new MongoId('6538ded562890c7a3f572b03'),
                'modified' => new MongoDate(strtotime('2024-01-11 14:00:00')),
            ]),
            $this->createEvent([
                '_id' => new MongoId('6538ded562890c7a3f572b04'),
            ]),
        ];
    }

    private function forRecurringClassBookings(): array
    {
        $programId = '6477520fb96e96bf7792d63d';
        $programId2 = '6477520fb96e96bf7792d63e';
        $start = Carbon::tomorrow()->setTime(8, 0);
        $end = Carbon::tomorrow()->setTime(9, 0);
        $startNextWeek = clone $start;
        $endNextWeek = clone $end;

        return [
            $this->createEvent([
                '_id' => new MongoId('6538ded562890c7a3f572b05'),
                'program_id' => $programId,
                'time_start' => new MongoDate($start->getTimestamp()),
                'time_finish' => new MongoDate($end->getTimestamp()),
                'schedule_code' => '5833c03455fs1',
                'name' => 'RecurringTestEvent1',
            ]),
            $this->createEvent([
                '_id' => new MongoId('6538ded562890c7a3f572b06'),
                'program_id' => $programId,
                'time_start' => new MongoDate($start->addDay()->getTimestamp()),
                'time_finish' => new MongoDate($end->addDay()->getTimestamp()),
                'schedule_code' => '5833c03455fs2',
                'name' => 'RecurringTestEvent2',
            ]),
            $this->createEvent([
                '_id' => new MongoId('6538ded562890c7a3f572b07'),
                'program_id' => $programId,
                'time_start' => new MongoDate($start->addDay()->getTimestamp()),
                'time_finish' => new MongoDate($end->addDay()->getTimestamp()),
                'schedule_code' => '5833c03455fs3',
                'name' => 'RecurringTestEvent3',
            ]),
            $this->createEvent([
                '_id' => new MongoId('6538ded562890c7a3f572b08'),
                'program_id' => $programId2,
                'time_start' => new MongoDate($startNextWeek->getTimestamp()),
                'time_finish' => new MongoDate($endNextWeek->getTimestamp()),
                'schedule_code' => '5833c03455fs4',
                'name' => 'RecurringTestEvent4',
            ]),
            $this->createEvent([
                '_id' => new MongoId('6538ded562890c7a3f572b09'),
                'program_id' => $programId2,
                'time_start' => new MongoDate($startNextWeek->addWeek()->getTimestamp()),
                'time_finish' => new MongoDate($endNextWeek->addWeek()->getTimestamp()),
                'schedule_code' => '5833c03455fs4',
                'name' => 'RecurringTestEvent5',
            ]),
        ];
    }

    private function forEventsBatchId(): array
    {
        return [
            $this->createEvent([
                '_id' => new MongoId('6538ded562890c7a3f572b18'),
                'program_id' => '6477520fb96e96bf7792d63d',
                'schedule_code' => '5833c03455fs3',
                'time_start' => new MongoDate(Carbon::parse('2024-04-20 18:00:00')->getTimestamp()),
                'time_finish' => new MongoDate(Carbon::parse('2024-04-20 19:00:00')->getTimestamp()),
                'name' => 'BatchIdEvent',
            ]),
        ];
    }

    private function forActiveLimitBookings(): array
    {
        $programId = '6672862317c4f80ebdb0ea51';
        $name = 'ActiveLimitBookingsEvent';
        return [
            $this->createEvent([
                '_id' => new MongoId('667289bfd03be8240fea6c27'),
                'program_id' => $programId,
                'schedule_code' => '5833c03455fs5',
                'time_start' => new MongoDate(Carbon::yesterday()->setTime(8, 0)->getTimestamp()),
                'time_finish' => new MongoDate(Carbon::yesterday()->setTime(9, 0)->getTimestamp()),
                'name' => $name,
            ]),
            $this->createEvent([
                '_id' => new MongoId('66728eb2949dfbc725dfc63d'),
                'program_id' => $programId,
                'schedule_code' => '5833c03455fs6',
                'time_start' => new MongoDate(Carbon::today()->subDays(3)->setTime(8, 0)->getTimestamp()),
                'time_finish' => new MongoDate(Carbon::today()->subDays(3)->setTime(9, 0)->getTimestamp()),
                'name' => $name,
            ]),
            $this->createEvent([
                '_id' => new MongoId('66728bdd22ffb1d8a4dd06de'),
                'program_id' => $programId,
                'schedule_code' => '5833c03455fs7',
                'time_start' => new MongoDate(Carbon::today()->subDays(5)->setTime(8, 0)->getTimestamp()),
                'time_finish' => new MongoDate(Carbon::today()->subDays(5)->setTime(9, 0)->getTimestamp()),
                'name' => $name,
            ]),
            $this->createEvent([
                '_id' => new MongoId('66728c1340ad856f51d52639'),
                'program_id' => $programId,
                'schedule_code' => '5833c03455fs8',
                'time_start' => new MongoDate(Carbon::tomorrow()->setTime(8, 0)->getTimestamp()),
                'time_finish' => new MongoDate(Carbon::tomorrow()->setTime(9, 0)->getTimestamp()),
                'name' => $name,
            ]),
            $this->createEvent([
                '_id' => new MongoId('66728c577602a0aba68e5802'),
                'program_id' => $programId,
                'schedule_code' => '5833c03455fs9',
                'time_start' => new MongoDate(Carbon::today()->addDays(3)->setTime(8, 0)->getTimestamp()),
                'time_finish' => new MongoDate(Carbon::today()->addDays(3)->setTime(9, 0)->getTimestamp()),
                'name' => $name,
            ]),
            $this->createEvent([
                '_id' => new MongoId('66728c679528d5ede7cc5a38'),
                'program_id' => $programId,
                'schedule_code' => '5833c03455ft1',
                'time_start' => new MongoDate(Carbon::today()->addDays(5)->setTime(8, 0)->getTimestamp()),
                'time_finish' => new MongoDate(Carbon::today()->addDays(5)->setTime(9, 0)->getTimestamp()),
                'name' => $name,
            ]),
            $this->createEvent([
                '_id' => new MongoId('66728c991c57b6dcccfa9fa6'),
                'program_id' => $programId,
                'schedule_code' => '5833c03455ft2',
                'time_start' => new MongoDate(Carbon::today()->addDays(7)->setTime(8, 0)->getTimestamp()),
                'time_finish' => new MongoDate(Carbon::today()->addDays(7)->setTime(9, 0)->getTimestamp()),
                'name' => $name,
            ]),
        ];
    }

    private function createEvent(array $override): array
    {
        return array_replace_recursive([
            '_id' => new MongoId(),
            'branch_id' => self::GLOFOX_BRANCH_ID,
            'namespace' => self::GLOFOX_NAMESPACE,
            'active' => true,
            'program_id' => '6477520fb96e96bf7792d62d',
            'schedule_code' => uniqid('', false),
            'level' => 'Advanced',
            'name' => 'TestEvent',
            'description' => 'Test Event Description',
            'size' => 10,
            'duration' => 60,
            'date' => new MongoDate(strtotime('2024-01-11 19:00:00')),
            'time_start' => new MongoDate(strtotime('2024-01-11 18:00:00')),
            'time_finish' => new MongoDate(strtotime('2024-01-11 19:00:00')),
            'trainers' => ['64784b4628c2ddf21e169901'],
            'facility' => '647753003940c6c48aab0441',
            'private' => false,
        ], $override);
    }

    private function forForceOverBookSizeChange(): array
    {
        $start = Carbon::tomorrow()->setTime(8, 0);
        return [
            $this->createEvent([
                '_id' => new MongoId('6538ded562890c7a3f572b19'),
                'branch_id' => '64784ace2c8a824784ab4cc4',
                'program_id' => '6477520fb95e96bf7792d62d',
                'schedule_code' => '5833c03455fs3',
                'time_start' => new MongoDate($start->getTimestamp()),
                'time_finish' => new MongoDate($start->addHour()->getTimestamp()),
                'name' => 'ForceOverBookingSizeChangeEvent',
                'size' => 1
            ]),
        ];
    }

    private function forIntegrationTests(): array
    {
        $programId = '6672862317c4f80ebdb0ea52';
        return [
            $this->createEvent([
                '_id' => new MongoId('667289bfd03be8240fea6c18'),
                'program_id' => $programId,
                'schedule_code' => '5833c03455fs5',
                'time_start' => new MongoDate(Carbon::tomorrow()->setTime(8, 0)->getTimestamp()),
                'time_finish' => new MongoDate(Carbon::tomorrow()->setTime(9, 0)->getTimestamp()),
                'name' => 'IntegrationEvent1',
            ]),
            $this->createEvent([
                '_id' => new MongoId('66aa128eb9f557add420cb1b'),
                'program_id' => $programId,
                'schedule_code' => '5833c03455fs6',
                'time_start' => new MongoDate(Carbon::tomorrow()->addDays(2)->setTime(8, 0)->getTimestamp()),
                'time_finish' => new MongoDate(Carbon::tomorrow()->addDays(2)->setTime(9, 0)->getTimestamp()),
                'name' => 'IntegrationEvent2',
            ]),
        ];
    }

    private function forEventBookingsTotalTests(): array
    {
        $programId = '6672862317c4f80ebdb0ea52';
        return [
            $this->createEvent([
                '_id' => new MongoId('667289bfd03be8240fea6c19'),
                'program_id' => $programId,
                'schedule_code' => '5833c03455fs5',
                'time_start' => new MongoDate(Carbon::tomorrow()->setTime(8, 0)->getTimestamp()),
                'time_finish' => new MongoDate(Carbon::tomorrow()->setTime(9, 0)->getTimestamp()),
                'name' => 'EventBookingsTotal I',
            ]),
            $this->createEvent([
                '_id' => new MongoId('667289bfd03be8240fea6c20'),
                'program_id' => $programId,
                'schedule_code' => '5833c03455fs5',
                'time_start' => new MongoDate(Carbon::tomorrow()->setTime(8, 0)->getTimestamp()),
                'time_finish' => new MongoDate(Carbon::tomorrow()->setTime(9, 0)->getTimestamp()),
                'name' => 'EventBookingsTotal II',
            ]),
            $this->createEvent([
                '_id' => new MongoId('667289bfd03be8240fea6c21'),
                'program_id' => $programId,
                'schedule_code' => '5833c03455fs5',
                'time_start' => new MongoDate(Carbon::tomorrow()->setTime(8, 0)->getTimestamp()),
                'time_finish' => new MongoDate(Carbon::tomorrow()->setTime(9, 0)->getTimestamp()),
                'name' => 'EventBookingsTotal III',
            ])
        ];
    }

    private function forEventDeletedEventHandle(): array
    {
        return [
            [
                '_id' => new MongoId('66aa128eb9f5579999990000'),
                'branch_id' => '666666777777888888000000',
                'namespace' => 'test-branch-event-deleted',
                'program_id' => '66aa128eb9f5579999990001',
                'name' => 'Test Class',
                'description' => 'Test Class Description',
                'size' => 10,
                'active' => true,
                'trainers' => [
                    '66aa128eb9f5579999990002'
                ],
                'date' => '2024-09-01',
                'time_start' => '2024-09-01T08:00:00+00:00',
                'time_finish' => '2024-09-01T09:00:00+00:00',
                'created' => '2024-08-29T09:00:00+00:00',
                'modified' => '2024-08-29T09:00:00+00:00'
            ]
        ];
    }

    private function forRestrictedMembershipNoCreditsNextCycle(): array
    {
        return [
            $this->createEvent([
                '_id' => new MongoId('997289bfd03be8240fea6c20'),
                'program_id' => '6672862317c4f80ebdb0ea53',
                'schedule_code' => '5833c03455fs5',
                'time_start' => new MongoDate(Carbon::today()->addMonths(1)->getTimestamp()),
                'time_finish' => new MongoDate(Carbon::today()->addMonths(1)->addHours(4)->getTimestamp()),
                'name' => 'RestrictedMembershipNoCreditsNextCycle Event',
            ]),
        ];
    }

    private function forFailureReason(): array
    {
        return [
            [
                '_id' => new MongoId('66aa128eb9f5579999990900'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'test-branch-event-deleted',
                'program_id' => '13b7411a15c676bda824611b',
                'name' => 'Test Class',
                'description' => 'Test Class Description',
                'size' => 10,
                'active' => true,
                'trainers' => [
                    '66aa128eb9f5579999990002'
                ],
                'date' => '2024-09-01',
                'time_start' => '2024-09-01T08:00:00+00:00',
                'time_finish' => '2024-09-01T09:00:00+00:00',
                'created' => '2024-08-29T09:00:00+00:00',
                'modified' => '2024-08-29T09:00:00+00:00'
            ]
        ];
    }

    private function forEventsUpdate(): array
    {
        return [
            [
                '_id' => new MongoId('6742631de09448fa155f9e18'),
                'branch_id' => '67425b982a70f7afae14dc75',
                'namespace' => 'events-update-plus-utc',
                'program_id' => '674261b978cfad5769eadf3a',
                'name' => 'Test Update Plus Class',
                'description' => 'Test Plus UTC Description',
                'size' => 10,
                'active' => true,
                'trainers' => [
                    '67425eecfc4508dbeff26724'
                ],
                'date' => '2024-12-11',
                'time_start' => '2024-12-11T13:00:00+00:00',
                'time_finish' => '2024-12-11T14:00:00+00:00',
                'created' => '2024-12-07T09:00:00+00:00',
                'modified' => '2024-12-07T09:00:00+00:00',
            ],
            [
                '_id' => new MongoId('67426323a338619555c318a8'),
                'branch_id' => '67425ec9a14b1b40651b4990',
                'namespace' => 'events-update-minus-utc',
                'program_id' => '674261be71678f5e860b3eee',
                'name' => 'Test Update Minus Class',
                'description' => 'Test Minus UTC Description',
                'size' => 10,
                'active' => true,
                'trainers' => [
                    self::TRAINER_ID
                ],
                'date' => '2024-12-18',
                'time_start' => '2024-12-18T08:00:00+00:00',
                'time_finish' => '2024-12-18T09:00:00+00:00',
                'created' => '2024-12-05T09:00:00+00:00',
                'modified' => '2024-12-05T09:00:00+00:00',
            ]
        ];
    }

    private function forEventsUpdateBasedOnProgramChanges(): array
    {
        $programId = '6672862317c4f80ebdb0e165';
        $name = 'Program Update Test';
        $description = 'Program Update Test Description';
        $trainer = self::TRAINER_ID;
        $datetime = '2024-01-03T09:00:00+00:00';
        $scheduleCode = '5833c03665fs4';
        $facility = '67426295783c43f929bade95';
        $level = 'advanced';
        return [
            $this->createEvent([
                '_id' => new MongoId('674dc93d05a22c3c78580ba4'),
                'program_id' => $programId,
                'name' => $name,
                'description' => $description,
                'size' => 10,
                'trainers' => [
                    $trainer
                ],
                'date' => '2024-01-04',
                'time_start' => '2024-01-04T08:00:00+00:00',
                'time_finish' => '2024-01-04T09:00:00+00:00',
                'created' => $datetime,
                'modified' => $datetime,
                'schedule_code' => $scheduleCode,
                'week_day' => 3,
                'level' => $level,
                'facility' => $facility,
                'private' => false,
            ]),
            $this->createEvent([
                '_id' => new MongoId('674dc9433d4e5c7de3686b1c'),
                'program_id' => $programId,
                'name' => $name,
                'description' => $description,
                'size' => 10,
                'trainers' => [
                    $trainer
                ],
                'date' => '2024-01-11',
                'time_start' => '2024-01-11T08:00:00+00:00',
                'time_finish' => '2024-01-11T09:00:00+00:00',
                'created' => $datetime,
                'modified' => $datetime,
                'schedule_code' => $scheduleCode,
                'week_day' => 3,
                'level' => $level,
                'facility' => $facility,
                'private' => false,
            ]),
            $this->createEvent([
                '_id' => new MongoId('674dc95fe36559ff37dde452'),
                'program_id' => $programId,
                'name' => $name,
                'description' => 'Program Update Test Description',
                'size' => 10,
                'trainers' => [
                    $trainer
                ],
                'date' => '2024-01-18',
                'time_start' => '2024-01-18T08:00:00+00:00',
                'time_finish' => '2024-01-18T09:00:00+00:00',
                'created' => $datetime,
                'modified' => $datetime,
                'schedule_code' => $scheduleCode,
                'week_day' => 3,
                'level' => 'all levels',
                'facility' => $facility,
                'private' => false,
            ]),
            $this->createEvent([
                '_id' => new MongoId('674ec286ca44d907833cc5fd'),
                'program_id' => $programId,
                'name' => $name,
                'description' => $description,
                'size' => 10,
                'trainers' => [
                    '67425eecfc4508dbeff26724'
                ],
                'date' => '2024-01-25',
                'time_start' => '2024-01-25T08:00:00+00:00',
                'time_finish' => '2024-01-25T09:00:00+00:00',
                'created' => $datetime,
                'modified' => $datetime,
                'schedule_code' => $scheduleCode,
                'week_day' => 3,
                'level' => $level,
                'facility' => $facility,
                'private' => false,
            ]),
            $this->createEvent([
                '_id' => new MongoId('674ec286ca44d907833cc5fe'),
                'program_id' => $programId,
                'name' => $name,
                'description' => 'This description is different than the programs description',
                'size' => 10,
                'trainers' => [
                    '67425eecfc4508dbeff26724'
                ],
                'date' => '2024-02-01',
                'time_start' => '2024-02-01T18:00:00+00:00',
                'time_finish' => '2024-02-01T19:00:00+00:00',
                'created' => $datetime,
                'modified' => $datetime,
                'schedule_code' => 'manually-created-event',
                'week_day' => 3,
                'level' => $level,
                'facility' => $facility,
                'private' => false,
            ]),
            $this->createEvent([
                '_id' => new MongoId('674ec286ca44d907833cc5f2'),
                'program_id' => $programId,
                'name' => 'Event with custom Time Start and Time Finish',
                'description' => 'This description is different than the programs description',
                'size' => 10,
                'trainers' => [
                    '67425eecfc4508dbeff26724'
                ],
                'date' => '2024-02-01',
                'time_start' => '2024-02-02T18:30:00+00:00',
                'time_finish' => '2024-02-02T19:30:00+00:00',
                'created' => $datetime,
                'modified' => $datetime,
                'schedule_code' => 'manually-created-event',
                'week_day' => 3,
                'level' => $level,
                'facility' => $facility,
                'private' => false,
            ]),
        ];
    }

    private function forRoamingWaitingList(): array
    {
        return [
            [
                '_id' => new MongoId('67766e4fc6833eb44d419feb'),
                'private' => false,
                'level' => '',
                'active' => true,
                'date' => new MongoDate(strtotime('2021-08-25')),
                'description' => 'To test if a member of another branch can join wait list for a class in a different branch under the sme namespace',
                'facility' => '5fbd7c16f43e994fe22bb849',
                'name' => 'Roaming Class',
                'program_id' => '60e27f21b35f5115330cf25b',
                'schedule_code' => '1639551829370',
                'size' => 0,
                'time_start' => new MongoDate(strtotime('2021-08-25T01:00:00.000')),
                'time_finish' => new MongoDate(strtotime('2021-08-25T02:00:00.000')),
                'trainers' => [
                    '5fd1ab5251bb5b027b30ed5f',
                ],
                'branch_id' => '5a9591bcdb07bce527400717',
                'namespace' => 'glofox',
                'week_day' => 0,
                'modified' => new MongoDate(strtotime('2021-12-23 06:20:34')),
                'created' => new MongoDate(strtotime('2021-12-15 07:03:50')),
                'total_bookings' => 1,
                'total_waitings' => 1,
            ],
        ];
    }

    private function forEventsUpdateEventsGeneratorBasedOnProgramChanges(): array
    {
        $programId = '677d35c0ca57fd9d73e9dbb8';
        $name = 'Program Update Event Generator Test';
        $description = 'Program Update Event Generator Test Description';
        $trainer = self::TRAINER_ID;
        $datetime = '2024-01-01T12:00:00+00:00';
        $scheduleCode = '5211b01775et1';
        $facility = '67426295783c43f929bade95';
        $level = 'advanced';
        $size = 2;
        return [
            $this->createEvent([
                '_id' => new MongoId('677d372bbcd79b6087921a9d'),
                'program_id' => $programId,
                'name' => $name,
                'description' => $description,
                'size' => $size,
                'trainers' => [
                    $trainer
                ],
                'active' => true,
                'date' => '2024-01-02',
                'time_start' => '2024-01-02T12:00:00+00:00',
                'time_finish' => '2024-01-02T12:00:00+00:00',
                'created' => $datetime,
                'modified' => $datetime,
                'schedule_code' => $scheduleCode,
                'week_day' => 2,
                'level' => $level,
                'facility' => $facility,
                'private' => false,
            ]),
            $this->createEvent([
                '_id' => new MongoId('677d3a7f1e3dad2c28ec16c6'),
                'program_id' => $programId,
                'name' => $name,
                'description' => $description,
                'size' => $size,
                'trainers' => [
                    $trainer
                ],
                'active' => true,
                'date' => '2024-01-09',
                'time_start' => '2024-01-09T12:00:00+00:00',
                'time_finish' => '2024-01-09T13:00:00+00:00',
                'created' => $datetime,
                'modified' => $datetime,
                'schedule_code' => $scheduleCode,
                'week_day' => 2,
                'level' => $level,
                'facility' => $facility,
                'private' => false,
            ]),
            $this->createEvent([
                '_id' => new MongoId('677d3aab060825d29375ae7c'),
                'program_id' => $programId,
                'name' => $name,
                'description' => $description,
                'size' => $size,
                'trainers' => [
                    $trainer
                ],
                'active' => true,
                'date' => '2024-01-16',
                'time_start' => '2024-01-16T12:00:00+00:00',
                'time_finish' => '2024-01-16T13:00:00+00:00',
                'created' => $datetime,
                'modified' => $datetime,
                'schedule_code' => $scheduleCode,
                'week_day' => 2,
                'level' => $level,
                'facility' => $facility,
                'private' => false,
            ]),
            $this->createEvent([
                '_id' => new MongoId('677d3ad7bf40db4c8b77f95a'),
                'program_id' => $programId,
                'name' => $name,
                'description' => $description,
                'size' => $size,
                'trainers' => [
                    $trainer
                ],
                'active' => true,
                'date' => '2024-01-23',
                'time_start' => '2024-01-23T12:00:00+00:00',
                'time_finish' => '2024-01-23T13:00:00+00:00',
                'created' => $datetime,
                'modified' => $datetime,
                'schedule_code' => $scheduleCode,
                'week_day' => 2,
                'level' => $level,
                'facility' => $facility,
                'private' => false,
            ]),
            $this->createEvent([
                '_id' => new MongoId('677d3af823fc3d1b3b2ec151'),
                'program_id' => $programId,
                'name' => $name,
                'description' => $description,
                'size' => $size,
                'trainers' => [
                    $trainer
                ],
                'active' => false,
                'date' => '2024-01-30',
                'time_start' => '2024-01-30T12:00:00+00:00',
                'time_finish' => '2024-01-30T13:00:00+00:00',
                'created' => $datetime,
                'modified' => $datetime,
                'schedule_code' => $scheduleCode,
                'manually_deleted' => true,
                'week_day' => 2,
                'level' => $level,
                'facility' => $facility,
                'private' => false,
            ]),
            $this->createEvent([
                '_id' => new MongoId('677d3b5ada40a40fc53eccf6'),
                'program_id' => $programId,
                'name' => $name,
                'description' => $description,
                'size' => $size,
                'trainers' => [
                    $trainer
                ],
                'active' => false,
                'date' => '2024-02-06',
                'time_start' => '2024-02-06T12:00:00+00:00',
                'time_finish' => '2024-02-06T13:00:00+00:00',
                'created' => $datetime,
                'modified' => $datetime,
                'schedule_code' => $scheduleCode,
                'week_day' => 2,
                'level' => $level,
                'facility' => $facility,
                'private' => false,
            ]),
            $this->createEvent([
                '_id' => new MongoId('6780dd7791140cc446a52e6f'),
                'program_id' => $programId,
                'name' => $name,
                'description' => $description,
                'size' => $size,
                'trainers' => [
                    $trainer
                ],
                'active' => false,
                'date' => '2024-02-13',
                'time_start' => '2024-02-13T12:00:00+00:00',
                'time_finish' => '2024-02-13T13:00:00+00:00',
                'created' => $datetime,
                'modified' => $datetime,
                'manually_deleted' => null,
                'schedule_code' => $scheduleCode,
                'week_day' => 2,
                'level' => $level,
                'facility' => $facility,
                'private' => false,
            ]),
        ];
    }

    private function forEventGeneratorComponent(): array
    {
        $commonFields = $this->getEventCommonFields();

        return [
            $this->createEvent(array_merge($commonFields,[
                'program_id' => '6672862317c4f80ebdb0e167',
                'date' => new MongoDate(strtotime('2024-01-01T00:00:00+00:00')),
                'time_start' => new MongoDate(strtotime('2024-01-01T08:00:00+00:00')),
                'time_finish' => new MongoDate(strtotime('2024-01-01T09:00:00+00:00')),
                'schedule_code' => '80ebdb0e001',
            ])),
            $this->createEvent(array_merge($commonFields,[
                'active' => false,
                'program_id' => '6672862317c4f80ebdb0e167',
                'date' => new MongoDate(strtotime('2024-01-22T00:00:00+00:00')),
                'time_start' => new MongoDate(strtotime('2024-01-22T08:00:00+00:00')),
                'time_finish' => new MongoDate(strtotime('2024-01-22T09:00:00+00:00')),
                'schedule_code' => '80ebdb0e001',
                'manually_deleted' => true,
            ])),
            $this->createEvent(array_merge($commonFields,[
                'active' => false,
                'program_id' => '6672862317c4f80ebdb0e168',
                'branch_id' => '675acdcdf1aef12a46e66bcf',
                'date' => new MongoDate(strtotime('2024-01-22T00:00:00+00:00')),
                'time_start' => new MongoDate(strtotime('2024-01-22T08:00:00+00:00')),
                'time_finish' => new MongoDate(strtotime('2024-01-22T09:00:00+00:00')),
                'schedule_code' => '80ebdb0e002',
            ])),
            $this->createEvent(array_merge($commonFields,[
                'active' => false,
                'program_id' => '6672862317c4f80ebdb0e169',
                'branch_id' => '675acdcdf1aef12a46e66bd0',
                'date' => new MongoDate(strtotime('2024-10-07T00:00:00+00:00')),
                'time_start' => new MongoDate(strtotime('2024-10-07T08:00:00+00:00')),
                'time_finish' => new MongoDate(strtotime('2024-10-07T09:00:00+00:00')),
                'schedule_code' => '80ebdb0e003',
                'manually_deleted' => true,
            ])),
            $this->createEvent(array_merge($commonFields,[
                'active' => false,
                'program_id' => '6672862317c4f80ebdb0e16a',
                'branch_id' => '675acdcdf1aef12a46e66bd1',
                'date' => new MongoDate(strtotime('2024-10-07T00:00:00+00:00')),
                'time_start' => new MongoDate(strtotime('2024-10-07T08:00:00+00:00')),
                'time_finish' => new MongoDate(strtotime('2024-10-07T09:00:00+00:00')),
                'schedule_code' => '80ebdb0e004',
            ])),
        ];
    }

    public function forWaitingListEmailOnRoaming(): array
    {
        return [
            $this->createEvent([
                '_id' => new MongoId('6780dd7791140cc446a52e70'),
                'active' => true,
                'program_id' => '0072862317c4f80ebdb0e16a',
                'branch_id' => '49a7011a05c677b9a916612b',
                'date' => new MongoDate(strtotime('2024-11-05T00:00:00+00:00')),
                'time_start' => new MongoDate(strtotime('2024-11-05T08:00:00+00:00')),
                'time_finish' => new MongoDate(strtotime('2024-11-05T09:00:00+00:00')),
                'schedule_code' => 'a0ebdb0e004',
            ]),
        ];
    }

    private function getEventCommonFields(): array
    {
        $creationDate = new MongoDate(strtotime('2023-12-01T00:00:00+00:00'));

        return [
            'active' => true,
            'branch_id' => '675acdcdf1aef12a46e66bce',
            'name' => 'Scheduled class',
            'description' => 'Scheduled class description',
            'created' => $creationDate,
            'modified' => $creationDate,
            'week_day' => 1,
            'private' => false,
            'manually_deleted' => false,
            'facility' => '80ebdb0e0000000000000000',
            'level' => 'advanced',
            'trainers' => ['80ebdb0e0000000000000001'],
            'size' => 10,
        ];
    }

    private function forProgramUpsertService(): array
    {
        $commonFields = $this->getEventCommonFields();
        $branchId = '675acdcdf1aef12a46e66bd2';
        $programId = '6672862317c4f80ebdb0e16b';
        $scheduleCode1 = '80ebdb0e005';
        $scheduleCode2 = '80ebdb0e006';
        $manuallyCreatedScheduleCode = '1234567890123';
        $timeStart = Carbon::parse('2023-12-25T00:00:00+00:00');
        $timeStart2 = Carbon::parse('2023-12-27T00:00:00+00:00');
        $weeks_display = 3;

        $events = [];

        // First we insert 1 event per schedule with _id to create bookings on them
        $events[] = $this->createEvent(array_merge($commonFields, [
            '_id' => new MongoId('6780dd7791140cc446a52e71'),
            'branch_id' => $branchId,
            'program_id' => $programId,
            'date' => new MongoDate(strtotime($timeStart->format('2024-01-15T00:00:00.000+00:00'))),
            'time_start' => new MongoDate(strtotime($timeStart->format('2024-01-15T08:15:00.000+00:00'))),
            'time_finish' => new MongoDate(strtotime($timeStart->format('2024-01-15T09:15:00.000+00:00'))),
            'schedule_code' => $scheduleCode1,
            'size' => 11,
        ]));

        $events[] = $this->createEvent(array_merge($commonFields, [
            '_id' => new MongoId('6780dd7791140cc446a52e72'),
            'branch_id' => $branchId,
            'program_id' => $programId,
            'date' => new MongoDate(strtotime($timeStart2->format('2024-01-17T00:00:00.000+00:00'))),
            'time_start' => new MongoDate(strtotime($timeStart2->format('2024-01-17T08:25:00.000+00:00'))),
            'time_finish' => new MongoDate(strtotime($timeStart2->format('2024-01-17T09:25:00.000+00:00'))),
            'schedule_code' => $scheduleCode2,
            'week_day' => 3,
            'size' => 12,
            'private' => true,
        ]));

        $events[] = $this->createEvent(array_merge($commonFields, [
            '_id' => new MongoId('6780dd7791140cc446a52e99'),
            'branch_id' => $branchId,
            'program_id' => $programId,
            'date' => new MongoDate(strtotime($timeStart2->format('2025-09-17T00:00:00.000+00:00'))),
            'time_start' => new MongoDate(strtotime($timeStart2->format('2025-09-17T08:25:00.000+00:00'))),
            'time_finish' => new MongoDate(strtotime($timeStart2->format('2025-09-17T09:25:00.000+00:00'))),
            'schedule_code' => $scheduleCode1,
            'size' => 11,
        ]));

        // Then we add 7 scheduled events more (per schedule)
        for ($i = 0; $i < $weeks_display; $i++) {
            $events[] = $this->createEvent(array_merge($commonFields, [
                'branch_id' => $branchId,
                'program_id' => $programId,
                'date' => new MongoDate(strtotime($timeStart->format('Y-m-dT00:00:00.000+00:00'))),
                'time_start' => new MongoDate(strtotime($timeStart->format('Y-m-dT08:15:00.000+00:00'))),
                'time_finish' => new MongoDate(strtotime($timeStart->format('Y-m-dT09:15:00.000+00:00'))),
                'schedule_code' => $scheduleCode1,
                'size' => 11,
            ]));

            $events[] = $this->createEvent(array_merge($commonFields, [
                'branch_id' => $branchId,
                'program_id' => $programId,
                'date' => new MongoDate(strtotime($timeStart2->format('Y-m-dT00:00:00.000+00:00'))),
                'time_start' => new MongoDate(strtotime($timeStart2->format('Y-m-dT08:25:00.000+00:00'))),
                'time_finish' => new MongoDate(strtotime($timeStart2->format('Y-m-dT09:25:00.000+00:00'))),
                'schedule_code' => $scheduleCode2,
                'week_day' => 3,
                'size' => 12,
                'private' => true,
            ]));

            $timeStart->addWeek();
            $timeStart2->addWeek();
        }

        // Finally, we add some manually created events
        $events[] = $this->createEvent(array_merge($commonFields, [
            'name' => 'Manually created event',
            'description' => 'Manually created event description',
            'branch_id' => $branchId,
            'program_id' => $programId,
            'date' => new MongoDate(strtotime($timeStart->format('2024-01-09T00:00:00.000+00:00'))),
            'time_start' => new MongoDate(strtotime($timeStart->format('2024-01-09T08:45:00.000+00:00'))),
            'time_finish' => new MongoDate(strtotime($timeStart->format('2024-01-09T09:45:00.000+00:00'))),
            'schedule_code' => $manuallyCreatedScheduleCode,
            'week_day' => 2,
            'size' => 74,
        ]));

        $events[] = $this->createEvent(array_merge($commonFields, [
            'name' => 'Manually created event 2',
            'description' => 'Manually created event description 2',
            'branch_id' => $branchId,
            'program_id' => $programId,
            'date' => new MongoDate(strtotime($timeStart->format('2024-01-16T00:00:00.000+00:00'))),
            'time_start' => new MongoDate(strtotime($timeStart->format('2024-01-16T08:35:00.000+00:00'))),
            'time_finish' => new MongoDate(strtotime($timeStart->format('2024-01-16T09:35:00.000+00:00'))),
            'schedule_code' => $manuallyCreatedScheduleCode,
            'week_day' => 2,
            'size' => 88,
            'private' => true,
        ]));

        return $events;
    }

    private function forOverdueMembershipBookings(): array
    {
        return [
            $this->createEvent([
                '_id' => new MongoId('6780dd7791140cc446a52e81'),
                'active' => true,
                'program_id' => '6672862317c4f80ebdb0e178',
                'branch_id' => self::GLOFOX_BRANCH_ID,
                'date' => new MongoDate(strtotime('2024-11-05T00:00:00+00:00')),
                'time_start' => new MongoDate(strtotime('2024-11-05T08:00:00+00:00')),
                'time_finish' => new MongoDate(strtotime('2024-11-05T09:00:00+00:00')),
                'schedule_code' => 'a0ebdb0e014',
            ]),
            $this->createEvent([
                '_id' => new MongoId('67c6de478b2e0eee4851d77b'),
                'active' => true,
                'branch_id' => self::GLOFOX_BRANCH_ID,
                'date' => new MongoDate(strtotime('2024-11-06T00:00:00+00:00')),
                'time_start' => new MongoDate(strtotime('2024-11-06T08:00:00+00:00')),
                'time_finish' => new MongoDate(strtotime('2024-11-06T09:00:00+00:00')),
            ]),
            $this->createEvent([
                '_id' => new MongoId('67c6de50d619c7de12a7ecaa'),
                'active' => true,
                'program_id' => '6672862317c4f80ebdb0e178',
                'branch_id' => self::GLOFOX_BRANCH_ID,
                'date' => new MongoDate(strtotime('2024-11-06T00:00:00+00:00')),
                'time_start' => new MongoDate(strtotime('2024-11-06T08:00:00+00:00')),
                'time_finish' => new MongoDate(strtotime('2024-11-06T09:00:00+00:00')),
                'schedule_code' => '5833c03665fs5',
            ]),
            $this->createEvent([
                '_id' => new MongoId('67c703e391604cfa8b801e0e'),
                'active' => true,
                'program_id' => '6672862317c4f80ebdb0e178',
                'branch_id' => self::GLOFOX_BRANCH_ID,
                'date' => new MongoDate(strtotime('2024-11-12T00:00:00+00:00')),
                'time_start' => new MongoDate(strtotime('2024-11-12T08:00:00+00:00')),
                'time_finish' => new MongoDate(strtotime('2024-11-12T09:00:00+00:00')),
                'schedule_code' => '5833c03665fs4',
            ]),
            $this->createEvent([
                '_id' => new MongoId('67c7060ea0dfed3a57d0a7b9'),
                'active' => true,
                'program_id' => '6672862317c4f80ebdb0e178',
                'branch_id' => self::GLOFOX_BRANCH_ID,
                'date' => new MongoDate(strtotime('2024-11-13T00:00:00+00:00')),
                'time_start' => new MongoDate(strtotime('2024-11-13T08:00:00+00:00')),
                'time_finish' => new MongoDate(strtotime('2024-11-13T09:00:00+00:00')),
                'schedule_code' => '5833c03665fs5',
            ]),
        ];
    }

    private function forNonDeleteEventsWithCanceledBookings(): array
    {
        $programId = '67e508db1f2ea29d1b38cbc0';
        $scheduleCode = '5211b01775aa2';
        return [
            $this->createEvent([
                '_id' => new MongoId('67e508c0eb798f7699a6ae61'),
                'active' => true,
                'program_id' => $programId,
                'branch_id' => self::GLOFOX_BRANCH_ID,
                'date' => new MongoDate(strtotime('2024-02-17T00:00:00+00:00')),
                'time_start' => new MongoDate(strtotime('2024-02-17T18:00:00+00:00')),
                'time_finish' => new MongoDate(strtotime('2024-02-17T19:00:00+00:00')),
                'schedule_code' => $scheduleCode
            ]),
            $this->createEvent([
                '_id' => new MongoId('67e508c0eb798f7699a6ae62'),
                'active' => false,
                'program_id' => $programId,
                'branch_id' => self::GLOFOX_BRANCH_ID,
                'date' => new MongoDate(strtotime('2024-02-24T00:00:00+00:00')),
                'time_start' => new MongoDate(strtotime('2024-02-24T18:00:00+00:00')),
                'time_finish' => new MongoDate(strtotime('2024-02-24T19:00:00+00:00')),
                'schedule_code' => $scheduleCode
            ]),
            $this->createEvent([
                '_id' => new MongoId('67e508c0eb798f7699a6ae63'),
                'active' => true,
                'program_id' => $programId,
                'branch_id' => self::GLOFOX_BRANCH_ID,
                'date' => new MongoDate(strtotime('2024-03-02T00:00:00+00:00')),
                'time_start' => new MongoDate(strtotime('2024-03-02T18:00:00+00:00')),
                'time_finish' => new MongoDate(strtotime('2024-03-02T19:00:00+00:00')),
                'schedule_code' => $scheduleCode
            ]),
            $this->createEvent([
                '_id' => new MongoId('67e508c0eb798f7699a6ae64'),
                'active' => true,
                'program_id' => $programId,
                'branch_id' => self::GLOFOX_BRANCH_ID,
                'date' => new MongoDate(strtotime('2024-03-09T00:00:00+00:00')),
                'time_start' => new MongoDate(strtotime('2024-03-09T18:00:00+00:00')),
                'time_finish' => new MongoDate(strtotime('2024-03-09T19:00:00+00:00')),
                'schedule_code' => $scheduleCode
            ]),
            $this->createEvent([
                '_id' => new MongoId('67e508c0eb798f7699a6ae65'),
                'active' => true,
                'program_id' => $programId,
                'branch_id' => self::GLOFOX_BRANCH_ID,
                'date' => new MongoDate(strtotime('2024-03-16T00:00:00+00:00')),
                'time_start' => new MongoDate(strtotime('2024-03-16T18:00:00+00:00')),
                'time_finish' => new MongoDate(strtotime('2024-03-16T19:00:00+00:00')),
                'schedule_code' => $scheduleCode
            ]),
            $this->createEvent([
                '_id' => new MongoId('67e508c0eb798f7699a6ae66'),
                'active' => false,
                'program_id' => $programId,
                'branch_id' => self::GLOFOX_BRANCH_ID,
                'date' => new MongoDate(strtotime('2024-03-29T00:00:00+00:00')),
                'time_start' => new MongoDate(strtotime('2024-03-23T18:00:00+00:00')),
                'time_finish' => new MongoDate(strtotime('2024-03-23T19:00:00+00:00')),
                'schedule_code' => $scheduleCode,
            ]),
            $this->createEvent([
                '_id' => new MongoId('67e508c0eb798f7699a6ae67'),
                'active' => true,
                'program_id' => $programId,
                'branch_id' => self::GLOFOX_BRANCH_ID,
                'date' => new MongoDate(strtotime('2024-03-30T00:00:00+00:00')),
                'time_start' => new MongoDate(strtotime('2024-03-30T18:00:00+00:00')),
                'time_finish' => new MongoDate(strtotime('2024-03-30T19:00:00+00:00')),
                'schedule_code' => $scheduleCode
            ]),
        ];
    }

    private function forCategoryAndModelValidations(): array
    {
        $programId = '67e508db1f2ea29d1b38cbc1';
        $scheduleCode = '5211b01775aa2';

        return [
            $this->createEvent([
                '_id' => new MongoId('67e508c0eb798f7699a6ae68'),
                'active' => true,
                'program_id' => $programId,
                'branch_id' => self::GLOFOX_BRANCH_ID,
                'date' => new MongoDate(Carbon::parse('2025-04-10 10:00:00')->getTimestamp()),
                'time_start' => new MongoDate(Carbon::parse('2025-04-10 10:00:00')->getTimestamp()),
                'time_finish' => new MongoDate(Carbon::parse('2025-04-10 11:00:00')->getTimestamp()),
                'schedule_code' => $scheduleCode
            ]),
            $this->createEvent([
                '_id' => new MongoId('67e508c0eb798f7699a6ae69'),
                'active' => true,
                'program_id' => $programId,
                'branch_id' => self::GLOFOX_BRANCH_ID,
                'date' => new MongoDate(Carbon::parse('2025-04-10 11:00:00')->getTimestamp()),
                'time_start' => new MongoDate(Carbon::parse('2025-04-11 10:00:00')->getTimestamp()),
                'time_finish' => new MongoDate(Carbon::parse('2025-04-11 11:00:00')->getTimestamp()),
                'schedule_code' => $scheduleCode
            ])
        ];
    }
}
