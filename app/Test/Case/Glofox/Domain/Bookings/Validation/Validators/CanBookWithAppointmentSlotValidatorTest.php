<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\Bookings\Validation\Validators;

use App;
use Carbon\Carbon;
use Glofox\Datasource\Exceptions\InvalidMongoIdException;
use Glofox\Domain\Bookings\Repositories\BookingsRepository;
use Glofox\Domain\Bookings\Validation\Validators\CanBookWithAppointmentSlotValidator;
use Glofox\Domain\Bookings\Validation\Validators\CanMemberBookWithAppointmentSlotValidator;
use Glofox\Domain\Bookings\Validation\Validators\CanTrainerBookWithAppointmentSlot;
use Glofox\Domain\Events\Repositories\EventsRepository;
use Glofox\Domain\TimeSlots\Models\TimeSlot;
use Glofox\Domain\TimeSlots\Repositories\TimeSlotRepository;
use Glofox\Domain\Users\Models\User;
use Glofox\Exception;
use Glofox\Validation\Exceptions\ValidationException;
use GlofoxTestCase;
use Mockery;

App::import('Test/Case', 'GlofoxTestCase');

/**
 * <AUTHOR>
 */
class CanBookWithAppointmentSlotValidatorTest extends GlofoxTestCase
{
    public $fixtures = [];
    private const TEST_APPOINTMENT_SLOT_ID = 'test-appointment-slot-id';
    private const TEST_STAFF_ID = 'test-staff-id';
    private const TEST_MEMBER_ID = 'test-member-id';
    private const TEST_BRANCH_ID = 'test-branch-id';
    private const TEST_EVENT_ID = 'test-event-id';
    private const TEST_BOOKING_ID = 'test-booking-id';
    private const VALIDATION_EXCEPTION_EXPECTED = 'Validation exception expected';
    private const NO_ERROR = 'No error';
    private Carbon $timeStart;
    private Carbon $timeFinish;

    public function setUp()
    {
        parent::setUp();

        $this->timeStart = Carbon::today();
        $this->timeFinish = Carbon::today()->addHour();
    }

    public function tearDown()
    {
        parent::tearDown();
        Mockery::close();
    }

    /**
     * @throws ValidationException
     * @throws InvalidMongoIdException
     * @throws Exception
     */
    public function testAppointmentSlotCanBeBooked(): void
    {
        $this->runValidator();
        $this->assertTrue(true);
    }

    /**
     * @throws ValidationException
     * @throws InvalidMongoIdException
     * @throws Exception
     */
    public function testAppointmentSlotCannotBeBookedByTrainerBecauseOverlappingSlots(): void
    {
        $timeSlotExpectedReturn = 1;
        $timeSlotExpectedCalls = 1;
        $eventsExpectedReturn = 0;
        $eventsExpectedCalls = 0;
        $bookingsExpectedReturn = [];
        $bookingsExpectedCalls = 0;

        try {
            $this->runValidator(
                $timeSlotExpectedReturn,
                $timeSlotExpectedCalls,
                $eventsExpectedReturn,
                $eventsExpectedCalls,
                $bookingsExpectedReturn,
                $bookingsExpectedCalls
            );
            $this->assertTextEquals(static::VALIDATION_EXCEPTION_EXPECTED, static::NO_ERROR);
        } catch (ValidationException $e) {
            $this->assertTextEquals(CanTrainerBookWithAppointmentSlot::ERROR_MESSAGE, $e->validationErrors());
        }
    }

    /**
     * @throws ValidationException
     * @throws InvalidMongoIdException
     * @throws Exception
     */
    public function testAppointmentSlotCannotBeBookedByTrainerBecauseOverlappingEvents(): void
    {
        $timeSlotExpectedReturn = 0;
        $timeSlotExpectedCalls = 1;
        $eventsExpectedReturn = 1;
        $eventsExpectedCalls = 1;
        $bookingsExpectedReturn = [];
        $bookingsExpectedCalls = 0;

        try {
            $this->runValidator(
                $timeSlotExpectedReturn,
                $timeSlotExpectedCalls,
                $eventsExpectedReturn,
                $eventsExpectedCalls,
                $bookingsExpectedReturn,
                $bookingsExpectedCalls
            );
            $this->assertTextEquals(static::VALIDATION_EXCEPTION_EXPECTED, static::NO_ERROR);
        } catch (ValidationException $e) {
            $this->assertTextEquals(CanTrainerBookWithAppointmentSlot::ERROR_MESSAGE, $e->validationErrors());
        }
    }

    /**
     * @throws ValidationException
     * @throws InvalidMongoIdException
     * @throws Exception
     */
    public function testAppointmentSlotCannotBeBookedByMemberBecauseOverlappingBookings(): void
    {
        $timeSlotExpectedReturn = 0;
        $timeSlotExpectedCalls = 1;
        $eventsExpectedReturn = 0;
        $eventsExpectedCalls = 1;
        $bookingsExpectedReturn = [
            [
                '_id' => static::TEST_BOOKING_ID,
            ],
        ];
        $bookingsExpectedCalls = 1;

        try {
            $this->runValidator(
                $timeSlotExpectedReturn,
                $timeSlotExpectedCalls,
                $eventsExpectedReturn,
                $eventsExpectedCalls,
                $bookingsExpectedReturn,
                $bookingsExpectedCalls
            );
            $this->assertTextEquals(static::VALIDATION_EXCEPTION_EXPECTED, static::NO_ERROR);
        } catch (ValidationException $e) {
            $this->assertTextEquals(CanMemberBookWithAppointmentSlotValidator::ERROR_MESSAGE, $e->validationErrors());
        }
    }

    /**
     * @throws ValidationException
     * @throws InvalidMongoIdException
     * @throws Exception
     */
    private function runValidator(
        int $timeSlotExpectedReturn = 0,
        int $timeSlotExpectedCalls = 1,
        int $eventsExpectedReturn = 0,
        int $eventsExpectedCalls = 1,
        array $bookingsExpectedReturn = [],
        int $bookingsExpectedCalls = 1
    ): void {
        $timeSlotRepository = $this->mockTimeSlotRepository($timeSlotExpectedReturn, $timeSlotExpectedCalls);
        $eventsRepository = $this->mockEventsRepository($eventsExpectedReturn, $eventsExpectedCalls);
        $bookingsRepository = $this->mockBookingsRepository($bookingsExpectedReturn, $bookingsExpectedCalls);

        $validator = new CanBookWithAppointmentSlotValidator(
            $timeSlotRepository,
            $eventsRepository,
            $bookingsRepository
        );
        $validator->validate(
            static::TEST_MEMBER_ID,
            static::TEST_STAFF_ID,
            static::TEST_BRANCH_ID,
            $this->timeStart,
            $this->timeFinish,
            static::TEST_APPOINTMENT_SLOT_ID
        );
    }

    private function mockTimeSlotRepository(int $expectedReturn = 0, int $expectedCalls = 1): TimeSlotRepository
    {
        $staffId = static::TEST_STAFF_ID;
        $branchId = static::TEST_BRANCH_ID;
        $timeSlotId = static::TEST_APPOINTMENT_SLOT_ID;
        $timeStart = $this->timeStart;
        $timeFinish = $this->timeFinish;
        $booked = true;

        $timeSlotRepository = Mockery::mock(TimeSlotRepository::class);

        $timeSlot = Mockery::mock(TimeSlot::class);
        $trainer = Mockery::mock(User::class);

        $trainer->shouldReceive('isActive')
            ->once()
            ->andReturn(true);

        $timeSlot->shouldReceive('staff')
            ->twice()
            ->andReturn($trainer);

        $timeSlotRepository->shouldReceive('getActiveSlotById')
            ->with($timeSlotId)
            ->once()
            ->andReturn($timeSlot);

        $timeSlotRepository->shouldReceive('countOverLappingSlots')
            ->withArgs(
                function (
                    string $staffIdParams,
                    string $branchIdParams,
                    Carbon $timeStartParams,
                    Carbon $timeFinishParams,
                    string $timeSlotIdParams,
                    bool $bookedParams
                ) use ($staffId, $branchId, $timeStart, $timeFinish, $timeSlotId, $booked) {
                    self::assertEquals($staffId, $staffIdParams);
                    self::assertEquals($branchId, $branchIdParams);
                    self::assertEquals($timeStart, $timeStartParams);
                    self::assertEquals($timeFinish, $timeFinishParams);
                    self::assertEquals($timeSlotId, $timeSlotIdParams);
                    self::assertEquals($booked, $bookedParams);

                    return true;
                }
            )
            ->times($expectedCalls)
            ->andReturn($expectedReturn);

        return $timeSlotRepository;
    }

    private function mockEventsRepository(int $expectedReturn = 0, int $expectedCalls = 1): EventsRepository
    {
        $staffId = static::TEST_STAFF_ID;
        $timeStart = $this->timeStart;
        $timeFinish = $this->timeFinish;
        $branchId = static::TEST_BRANCH_ID;

        $eventsRepository = Mockery::mock(EventsRepository::class);
        $eventsRepository->shouldReceive('countOverlappingEvents')
            ->withArgs(
                function (
                    string $staffIdParams,
                    Carbon $timeStartParams,
                    Carbon $timeFinishParams,
                    string $branchIdParams
                ) use ($staffId, $timeStart, $timeFinish, $branchId) {
                    self::assertEquals($staffId, $staffIdParams);
                    self::assertEquals($timeStart, $timeStartParams);
                    self::assertEquals($timeFinish, $timeFinishParams);
                    self::assertEquals($branchId, $branchIdParams);

                    return true;
                }
            )
            ->times($expectedCalls)
            ->andReturn($expectedReturn);

        return $eventsRepository;
    }

    private function mockBookingsRepository(array $expectedReturn = [], int $expectedCalls = 1): BookingsRepository
    {
        $memberId = static::TEST_MEMBER_ID;
        $branchId = static::TEST_BRANCH_ID;
        $timeStart = $this->timeStart;
        $timeFinish = $this->timeFinish;
        $model = null;
        $modelId = null;
        $limit = 1;

        $bookingsRepository = Mockery::mock(BookingsRepository::class);
        $bookingsRepository->shouldReceive('getOverLappingBookings')
            ->withArgs(
                function (
                    string $memberIdParams,
                    string $branchIdParams,
                    Carbon $timeStartParams,
                    Carbon $timeFinishParams,
                    ?string $modelParams,
                    ?string $modelIdParams,
                    ?int $limitParams
                ) use ($memberId, $branchId, $timeStart, $timeFinish, $model, $modelId, $limit) {
                    self::assertEquals($memberId, $memberIdParams);
                    self::assertEquals($branchId, $branchIdParams);
                    self::assertEquals($timeStart, $timeStartParams);
                    self::assertEquals($timeFinish, $timeFinishParams);
                    self::assertEquals($model, $modelParams);
                    self::assertEquals($modelId, $modelIdParams);
                    self::assertEquals($limit, $limitParams);

                    return true;
                }
            )
            ->times($expectedCalls)
            ->andReturn($expectedReturn);

        return $bookingsRepository;
    }
}
