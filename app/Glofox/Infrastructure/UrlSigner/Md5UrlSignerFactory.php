<?php

namespace Glofox\Infrastructure\UrlSigner;

use Glofox\Domain\Users\Models\User;
use Spatie\UrlSigner\MD5UrlSigner as SpatieMD5UrlSigner;

class Md5UrlSignerFactory
{
    private string $signatureKey;

    public function __construct(string $signatureKey)
    {
        $this->signatureKey = $signatureKey;
    }

    public function make(User $user): Md5UrlSigner
    {
        $userBasedSignatureKey = sprintf('%s::%s', $this->signatureKey, $user->id());

        $signerEngine = new SpatieMD5UrlSigner(
            $userBasedSignatureKey
        );

        return new Md5UrlSigner(
            $signerEngine,
            $user
        );
    }
}