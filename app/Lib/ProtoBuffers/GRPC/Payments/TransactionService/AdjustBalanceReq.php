<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: action_request.proto

namespace GRPC\Payments\TransactionService;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>grpc.AdjustBalanceReq</code>
 */
class AdjustBalanceReq extends \Google\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string RequestUUID = 1;</code>
     */
    protected $RequestUUID = '';
    /**
     * Generated from protobuf field <code>int32 CustomerAccountID = 2;</code>
     */
    protected $CustomerAccountID = 0;
    /**
     * Generated from protobuf field <code>int32 Amount = 3;</code>
     */
    protected $Amount = 0;
    /**
     * Generated from protobuf field <code>bool AllowOverdraft = 4;</code>
     */
    protected $AllowOverdraft = false;
    /**
     * Generated from protobuf field <code>string Description = 5;</code>
     */
    protected $Description = '';
    /**
     * Generated from protobuf field <code>int32 ActionRequestID = 6;</code>
     */
    protected $ActionRequestID = 0;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $RequestUUID
     *     @type int $CustomerAccountID
     *     @type int $Amount
     *     @type bool $AllowOverdraft
     *     @type string $Description
     *     @type int $ActionRequestID
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\ActionRequest::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string RequestUUID = 1;</code>
     * @return string
     */
    public function getRequestUUID()
    {
        return $this->RequestUUID;
    }

    /**
     * Generated from protobuf field <code>string RequestUUID = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setRequestUUID($var)
    {
        GPBUtil::checkString($var, True);
        $this->RequestUUID = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 CustomerAccountID = 2;</code>
     * @return int
     */
    public function getCustomerAccountID()
    {
        return $this->CustomerAccountID;
    }

    /**
     * Generated from protobuf field <code>int32 CustomerAccountID = 2;</code>
     * @param int $var
     * @return $this
     */
    public function setCustomerAccountID($var)
    {
        GPBUtil::checkInt32($var);
        $this->CustomerAccountID = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 Amount = 3;</code>
     * @return int
     */
    public function getAmount()
    {
        return $this->Amount;
    }

    /**
     * Generated from protobuf field <code>int32 Amount = 3;</code>
     * @param int $var
     * @return $this
     */
    public function setAmount($var)
    {
        GPBUtil::checkInt32($var);
        $this->Amount = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>bool AllowOverdraft = 4;</code>
     * @return bool
     */
    public function getAllowOverdraft()
    {
        return $this->AllowOverdraft;
    }

    /**
     * Generated from protobuf field <code>bool AllowOverdraft = 4;</code>
     * @param bool $var
     * @return $this
     */
    public function setAllowOverdraft($var)
    {
        GPBUtil::checkBool($var);
        $this->AllowOverdraft = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string Description = 5;</code>
     * @return string
     */
    public function getDescription()
    {
        return $this->Description;
    }

    /**
     * Generated from protobuf field <code>string Description = 5;</code>
     * @param string $var
     * @return $this
     */
    public function setDescription($var)
    {
        GPBUtil::checkString($var, True);
        $this->Description = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 ActionRequestID = 6;</code>
     * @return int
     */
    public function getActionRequestID()
    {
        return $this->ActionRequestID;
    }

    /**
     * Generated from protobuf field <code>int32 ActionRequestID = 6;</code>
     * @param int $var
     * @return $this
     */
    public function setActionRequestID($var)
    {
        GPBUtil::checkInt32($var);
        $this->ActionRequestID = $var;

        return $this;
    }

}

