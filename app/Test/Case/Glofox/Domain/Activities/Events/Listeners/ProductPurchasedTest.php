<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\Activities\Events\Listeners;

use CakeTestCases\Glofox\Domain\Users\Traits\FetchUsersTrait;
use Glofox\Domain\Activities\Events\Listeners\ProductPurchased;
use Glofox\Domain\Store\Sales\Events\ProductWasPurchased;
use Glofox\Domain\Store\Products\Models\Product;
use Glofox\Domain\Store\Products\Repositories\ProductsRepository;
use Glofox\Repositories\Search\Expressions\Shared\Id;

\App::import('Test/Case', 'GlofoxTestCase');

final class ProductPurchasedTest extends \GlofoxTestCase
{
    use FetchUsersTrait;

    public $fixtures = [
        'app.user',
        'app.branch',
        'app.product',
        'app.stripe_charge',
    ];

    public function test_it_adds_the_sales_attribution_field_when_a_product_is_purchased(): void
    {
        $product = $this->fetchProduct('5b44ae18b13d95b5f633fd06');
        $adminUser = $this->fetchUser('a9a5521a05c687bda917785c');

        $event = new ProductWasPurchased(
            $product->toLegacy(),
            $adminUser->toLegacy(),
            $collectionCode = 'fake-code',
            $presentationId = 1_506_964_157_657,
            $quantity = 1,
            '',
            10,
            'EUR'
        );

        $listener = new ProductPurchased();
        $contextBuilder = $listener->getActivityContextBuilder();
        $data = $contextBuilder->build($event);

        $this->assertArrayHasKey('sold_by_user_id', $data);
        $this->assertSame('a9a5521a05c687bda917755c', $data['sold_by_user_id']);
    }

    private function fetchProduct(string $id): Product
    {
        return app()->make(ProductsRepository::class)
            ->addCriteria(new Id($id))
            ->firstOrFail();
    }
}
