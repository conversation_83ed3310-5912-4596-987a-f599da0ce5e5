<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\PaymentMethods\Events\Listeners;

use Glofox\Domain\Branches\Events\BranchWasCreated;
use Glofox\Domain\Branches\Models\Branch;
use Glofox\Domain\PaymentMethods\Events\Listeners\UpdateAbTestsOnPaymentMethodUpdate;
use Glofox\Infrastructure\Flags\IrisGenericFlagger;

\App::import('Test/Case', 'GlofoxTestCase');

class UpdateAbTestsOnPaymentMethodUpdateTest extends \GlofoxTestCase
{
    public $fixtures = [];
    
    public function test_membership_flags_is_enabled_called_for_correct_branch(): void
    {
        $pastDueSubsFlagger = \Mockery::mock(IrisGenericFlagger::class);
        $pastDueSubsFlagger->shouldReceive('shouldEnable')
            ->andReturnFalse();
        $pastDueSubsFlagger
            ->shouldReceive('disable')
            ->withArgs(fn (Branch $branch) => $branch->id() === 'branch1');

        $branchModel = Branch::make([
            '_id' => "branch1",
            'namespace' => 'test',
        ]);

        $listener = new UpdateAbTestsOnPaymentMethodUpdate();
        $listener->addFlaggers([$pastDueSubsFlagger]);
        $listener->handle(new BranchWasCreated($branchModel));
    }
}
