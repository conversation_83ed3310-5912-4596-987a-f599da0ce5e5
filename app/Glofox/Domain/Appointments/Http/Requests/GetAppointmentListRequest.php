<?php

declare(strict_types=1);

namespace Glofox\Domain\Appointments\Http\Requests;

use Glofox\Request;
use Glofox\Validation\Rules\RulesCollection;
use Saritasa\Laravel\Validation\Rule;

class GetAppointmentListRequest extends Request
{
    private const DEFAULT_LIMIT = 50;
    private const MAX_LIMIT = 100;

    public function rules(): array
    {
        $rules = RulesCollection::make([
            'search-query' => Rule::string()->sometimes()->max(50),
            'sort-by' => Rule::string()->sometimes(),
            'page' => Rule::int()->min(1)->sometimes(),
            'limit' => Rule::int()->min(1)->max(self::MAX_LIMIT)->sometimes(),
            'active' => Rule::string()->sometimes(),
            'include' => Rule::string()->sometimes()->in(['staff']),
            'staff-id' => Rule::string()->sometimes(),
        ]);

        return $rules->toArray();
    }

    public function branchId(): string
    {
        return $this->cakeRouteParams()->get('branchId');
    }

    public function sortBy(): string
    {
        if (!$this->has('sort-by')) {
            return 'name';
        }

        return trim($this->get('sort-by'), '-');
    }

    public function sortByDirection(): int
    {
        if (!$this->has('sort-by')) {
            return 1;
        }

        return $this->get('sort-by')[0] === '-' ? -1 : 1;
    }

    public function page(): int
    {
        if (!$this->has('page')) {
            return 1;
        }

        return (int) $this->get('page');
    }

    public function limit(): int
    {
        if (!$this->has('limit')) {
            return self::DEFAULT_LIMIT;
        }

        return (int) $this->get('limit');
    }

    public function searchQuery(): string
    {
        if (!$this->has('search-query') || empty($this->get('search-query'))) {
            return '';
        }

        return $this->get('search-query');
    }

    public function staffId(): string
    {
        if (!$this->has('staff-id') || empty($this->get('staff-id'))) {
            return '';
        }

        return $this->get('staff-id');
    }

    public function active(): bool
    {
        return $this->get('active') === 'true'
            || $this->get('active') === null;
    }

    public function includeStaff(): bool
    {
        return $this->get('include') === 'staff';
    }
}
