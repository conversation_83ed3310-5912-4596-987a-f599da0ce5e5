<?php

ini_set('max_execution_time', 600);

App::uses('Shell', 'Console');
App::uses('File', 'Utility');
App::uses('Folder', 'Utility');

use Glofox\CdnProvider;
use Glofox\Console\RegionSpecific;
use Glofox\Domain\Branches\Models;
use Glofox\Domain\Branches\Models\Branch;
use Glofox\Domain\Region\ContinentResolver;
use Glofox\Domain\Transactional\Composer;
use Glofox\Domain\Transactional\MessageDispatcher;
use Glofox\Domain\Transactional\Messages\Factory as TransactionalMessageFactory;
use Glofox\Domain\Transactional\Sender;
use Glofox\Domain\Transactional\Templates\Identifier;
use Glofox\Domain\Transactional\Templates\Views\ExpiringMembersTemplateEmail;
use Glofox\Domain\Users\Repositories\UsersRepository;

class EmailShell extends Shell
{
    use RegionSpecific;

    public $uses = ['User', 'Branch', 'Utility', 'Membership'];
    private Sender $sender;
    private TransactionalMessageFactory $transactionalMessageFactory;
    private MessageDispatcher $messageDispatcher;
    private UsersRepository $usersRepository;

    public function __construct($stdout = null, $stderr = null, $stdin = null)
    {
        parent::__construct($stdout, $stderr, $stdin);

        $this->sender = app()->make(Sender::class);
        $this->transactionalMessageFactory = app()->make(TransactionalMessageFactory::class);
        $this->messageDispatcher = app()->make(MessageDispatcher::class);
        $this->usersRepository = app()->make(UsersRepository::class);
    }

    public function main()
    {
        $this->validateRegionParameter();

        error_reporting(0);
        $day = date('l');
        if (in_array($day, ['Monday', 'Friday'])) {
            $this->weekly_expired();
        }
    }

    /**
     * @deprecated this email must be deprecated by the following report
     * @see https://app.glofox.com/dashboard/#/reports/expiring-members-gf
     */
    public function weekly_expired()
    {
        // @TODO: Commented for tests purpose
        if (GlofoxEnvironment::PLATFORM !== $this->User->get_current_environment()) {
            return;
        }

        $this->log('~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~', LOG_INFO);
        $this->log('Cron Started: weekly_expired', LOG_INFO);

        $conditions = [
            'active' => true,
            'email_notifications.weekly_expired' => true,
        ];

        // @TODO use Criteria classes here
        /** @var ContinentResolver $continentResolver */
        $continentResolver = $this->container()->make(ContinentResolver::class);
        $conditions['address.continent'] = ['$in' => $continentResolver->resolve($this->region)];

        $branches = $this->Branch->find('all', [
            'conditions' => $conditions,
        ]);

        $today = $this->Utility->getCurrentDate('Y-m-d');
        $next_week = date('Y-m-d', strtotime($today . '+ 1 weeks'));
        $today_time = strtotime($today);
        $next_week_time = strtotime($next_week);
        $date_condition = ['$gte' => new MongoDate($today_time), '$lte' => new MongoDate($next_week_time)];

        foreach ($branches as $branch) {
            $users = $this->User->find('all', [
                'conditions' => [
                    'active' => true,
                    'type' => 'MEMBER',
                    'branch_id' => $branch['Branch']['_id'],
                    'namespace' => $branch['Branch']['namespace'],
                    'membership.expiry_date' => $date_condition,
                ],
            ]);

            $this->log("Sending 'weekly_expired' to Branch " . $branch['Branch']['_id'], LOG_INFO);
            $this->log('Users: ' . (is_countable($users) ? count($users) : 0), LOG_INFO);

            $dateFormatter = function (int $expiryDate) use ($branch): string {
                $branchObj = Models\Branch::make($branch['Branch']);
                $dateFormat = momentJsToPhpFormat($branchObj->configuration()->dateFormat());

                return date($dateFormat, $expiryDate);
            };

            if (!is_countable($users) || count($users) === 0) {
                continue;
            }

            $adminUsers = $this->usersRepository->getActiveAdminsAndSuperAdminsByBranchId($branch['Branch']['_id']);
            $env = $this->User->get_current_environment();
            $colors = $this->getColorsForBranch($branch);
            $logoUrl = sprintf('%s/%s/glofox/glofox-logo-horizontal.png', CdnProvider::getUrl(), $env);

            try {
                $branchName = $branch['Branch']['name'];
                $subject = 'Members Expiring/Renewing This Week - ' . $branchName;

                $templateCallback = function (ExpiringMembersTemplateEmail $template) use (
                    $branch,
                    $users,
                    $dateFormatter,
                    $colors,
                    $logoUrl,
                    $today_time,
                    $next_week_time
                ) {
                    $template->with('branch', $branch);
                    $template->with('users', $users);
                    $template->with('dateFormatter', $dateFormatter);
                    $template->with('colors', $colors);
                    $template->with('logoUrl', $logoUrl);
                    $template->with('today_time', $today_time);
                    $template->with('next_week_time', $next_week_time);
                };

                $transactionalMessage = $this->transactionalMessageFactory->create(
                    [
                        'subject' => $subject,
                        'template' => Identifier::EXPIRING_MEMBERS,
                        'variables' => [],
                        'content' => '',
                    ]
                );

                $variables = collect([]);
                $parsedMessage = (new Composer($transactionalMessage, $variables))->compose();

                foreach ($adminUsers as $admin) {
                    $this->messageDispatcher
                        ->withBranch($branch)
                        ->withTemplateCallback($templateCallback)
                        ->withMessageDefinition($transactionalMessage)
                        ->dispatchTo($admin->toLegacy(), $parsedMessage);
                }
            } catch (Exception $e) {
                $this->log('Exception ' . $e, LOG_ERR);
            }
        }

        $this->log('Cron Finished: weekly_expired', LOG_INFO);
    }

    /**
     * Get specific colors values if branch is platinum.
     *
     * @param array $branch
     *
     * @return array
     */
    private function getColorsForBranch(array $branch)
    {
        $branchModel = Branch::make($branch['Branch']);
        $branchColors = $branchModel->configuration()->webPortal()->colors();
        $colors = [
            'background' => $branchColors->background()->hexadecimal(),
            'accent' => $branchColors->accent()->hexadecimal(),
            'text' => $branchColors->text()->hexadecimal(),
            'textRGB' => $branchColors->text()->rgb(),
        ];

        return $colors;
    }
}

