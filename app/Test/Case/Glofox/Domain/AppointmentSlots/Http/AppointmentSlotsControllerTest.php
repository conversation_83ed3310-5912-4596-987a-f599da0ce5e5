<?php

declare(strict_types=1);

use CakeTestCases\Glofox\Domain\Users\Traits\AuthenticateUsersTrait;
use Carbon\Carbon;
use Glofox\Domain\AppointmentSlots\CreatedBy;
use Glofox\Domain\AppointmentSlots\Exceptions\MembersCannotDeleteSlot;
use Glofox\Domain\Bookings\Services\BookingsPublisher;
use Glofox\Domain\Locker\EventBookingLockerInterface;
use Glofox\Domain\TimeSlots\ModelList;
use Glofox\Domain\TimeSlots\Repositories\TimeSlotRepository;

App::import('Test/Case', 'GlofoxControllerTestCase');

class AppointmentSlotsControllerTest extends GlofoxControllerTestCase
{
    use AuthenticateUsersTrait;

    public $fixtures = [
        'app.payment_method',
        'app.payment_provider',
        'app.branch',
        'app.user',
        'app.client',
        'app.membership',
        'app.event',
        'app.booking',
        'app.program',
        'app.facility',
        'app.role',
        'app.time_slot_pattern',
        'app.time_slot',
        'app.push_notification',
    ];
    /**
     * @const string Message when appointment slot is not found. It must receive the string slot id as param.
     */
    private const SLOT_NOT_FOUND = 'Appointment slot not found - id: %s';
    private const STAFF_ID = '59a7011a05c677bda916612c';
    private const CREATE_SLOT_ENDPOINT = '/2.1/branches/%s/appointments/%s/slots';
    private const BRANCH_ID = '49a7011a05c677b9a916612a';
    private TimeSlotRepository $timeSlotRepository;

    public function setUp()
    {
        parent::setUp();
        $this->timeSlotRepository = app()->make(TimeSlotRepository::class);
        $this->mockBookingAppointmentLocker();
    }

    public function testCreateSlotEndpoint(): void
    {
        Carbon::setTestNow("2024-10-25 09:00:00");

        $this->authenticateAsAdmin();

        $appointmentId = '62a70487b9be8ef0bf86d521';
        $staffId = self::STAFF_ID;

        $uri = sprintf(self::CREATE_SLOT_ENDPOINT, self::BRANCH_ID, $appointmentId);
        $data = [
            'time_start' => Carbon::now()->getTimestamp(),
            'staff_id' => $staffId,
            'private' => true,
        ];

        $response = $this->testAction($uri, [
            'method' => 'post',
            'data' => $data,
        ]);

        $result = json_decode($response, true, 512, JSON_THROW_ON_ERROR);

        self::assertEquals(201, $this->response->statusCode());
        self::assertTrue($result['active']);
        self::assertTrue($result['private']);
        self::assertFalse($result['booked']);
        self::assertEquals('appointments', $result['model']);
        self::assertEquals($appointmentId, $result['model_id']);
        self::assertEquals($appointmentId, $result['appointment']['_id']);
        self::assertEquals($staffId, $result['staff_id']);
        self::assertEquals('Personal Training', $result['name']);
        self::assertEquals($appointmentId, $result['time_slot_pattern_id']);
        self::assertEquals('', $result['created_by']);
    }

    public function testTrainersCannotCreateSlotsForOtherTrainers(): void
    {
        $trainer = $this->fetchUser('58568a8fa875ab19530041a7');
        $this->loginAsUser($trainer);
        $appointmentId = '62a70487b9be8ef0bf86d521';
        $staffId = self::STAFF_ID;

        $uri = sprintf(self::CREATE_SLOT_ENDPOINT, self::BRANCH_ID, $appointmentId);
        $data = [
            'time_start' => Carbon::now()->getTimestamp(),
            'staff_id' => $staffId,
            'private' => true,
        ];

        $response = $this->testAction($uri, [
            'method' => 'post',
            'data' => $data,
        ]);

        $result = json_decode($response, true, 512, JSON_THROW_ON_ERROR);

        self::assertEquals(400, $this->response->statusCode());
        self::assertFalse($result['success']);
        self::assertEquals('TRAINERS_CAN_ONLY_ADD_SLOTS_FOR_THEMSELVES', $result['message_code']);
        self::assertEquals('Trainers can only add slots for themselves', $result['message']);
    }

    public function testDeleteSlotEndpoint(): void
    {
        $this->authenticateAsAdmin();

        $appointmentId = '62a70487b9be8ef0bf86d521';
        $slotId = '62a9d1b6a3a129be24ee6f3f';

        $uri = sprintf('/2.2/branches/%s/appointments/%s/slots/%s', self::BRANCH_ID, $appointmentId, $slotId);

        $this->testAction($uri, [
            'method' => 'delete',
        ]);

        self::assertEquals(204, $this->response->statusCode());
    }

    public function testDeleteSlotEndpointForNonExistingSlot(): void
    {
        $this->authenticateAsAdmin();

        $appointmentId = '62a70487b9be8ef0bf86d521';
        $slotId = '62ac441ff06316c5bbf6b03b';

        $uri = sprintf('/2.2/branches/%s/appointments/%s/slots/%s', self::BRANCH_ID, $appointmentId, $slotId);

        $response = $this->testAction($uri, [
            'method' => 'delete',
        ]);

        $result = json_decode($response, true, 512, JSON_THROW_ON_ERROR);

        self::assertEquals(404, $this->response->statusCode());
        self::assertFalse($result['success']);
        self::assertEquals(sprintf(self::SLOT_NOT_FOUND, $slotId), $result['message']);
    }

    public function testGetSlotEndpoint(): void
    {
        $this->authenticateAsAdmin();

        $appointmentId = '62a70487b9be8ef0bf86d521';
        $slotId = '62ac6f00ce337a1e6fae131e';

        $uri = sprintf('/2.2/branches/%s/appointments/%s/slots/%s', self::BRANCH_ID, $appointmentId, $slotId);

        $response = $this->testAction($uri, [
            'method' => 'get',
        ]);

        $result = json_decode($response, true, 512, JSON_THROW_ON_ERROR);

        self::assertEquals(200, $this->response->statusCode());
        self::assertArrayHasKey('branch_id', $result);
        self::assertArrayHasKey('namespace', $result);
        self::assertArrayHasKey('model', $result);
        self::assertArrayHasKey('model_id', $result);
        self::assertArrayHasKey('staff_id', $result);
        self::assertArrayHasKey('active', $result);
        self::assertArrayHasKey('booked', $result);
        self::assertArrayHasKey('appointment', $result);
        self::assertArrayHasKey('staff', $result);
    }

    public function testGetSlotEndpointForNonExistingSlot(): void
    {
        $this->authenticateAsAdmin();

        $appointmentId = '62a70487b9be8ef0bf86d521';
        $slotId = '62ac7695d0271abe9eeb14b4';

        $uri = sprintf('/2.2/branches/%s/appointments/%s/slots/%s', self::BRANCH_ID, $appointmentId, $slotId);

        $response = $this->testAction($uri, [
            'method' => 'get',
        ]);

        $result = json_decode($response, true, 512, JSON_THROW_ON_ERROR);

        self::assertEquals(404, $this->response->statusCode());
        self::assertFalse($result['success']);
        self::assertEquals(sprintf(self::SLOT_NOT_FOUND, $slotId), $result['message']);
        self::assertEquals(sprintf(self::SLOT_NOT_FOUND, $slotId), $result['message_code']);
    }

    public function testGetSlotEndpointForNonActiveSlot(): void
    {
        $this->authenticateAsAdmin();

        $appointmentId = '62a70487b9be8ef0bf86d521';
        $slotId = '63314f58d3d4296446a9c56d';

        $uri = sprintf('/2.2/branches/%s/appointments/%s/slots/%s', self::BRANCH_ID, $appointmentId, $slotId);

        $response = $this->testAction($uri, [
            'method' => 'get',
        ]);

        $result = json_decode($response, true, 512, JSON_THROW_ON_ERROR);

        self::assertEquals(404, $this->response->statusCode());
        self::assertFalse($result['success']);
        self::assertEquals(sprintf(self::SLOT_NOT_FOUND, $slotId), $result['message']);
        self::assertEquals(sprintf(self::SLOT_NOT_FOUND, $slotId), $result['message_code']);
    }

    public function testMemberCannotCreateSlotEndpoint(): void
    {
        $this->authenticateAsMember();

        $appointmentId = '62a70487b9be8ef0bf86d521';
        $uri = sprintf('/2.1/branches/%s/appointments/%s/slots', self::BRANCH_ID, $appointmentId);
        $data = [
            'time_start' => Carbon::now()->getTimestamp(),
            'staff_id' => self::STAFF_ID,
            'private' => true,
        ];

        $this->testAction($uri, [
            'method' => 'post',
            'data' => $data,
        ]);

        self::assertEquals(400, $this->response->statusCode());
    }

    public function testMemberCannotDeleteSlotEndpoint(): void
    {
        $this->authenticateAsMember();

        $appointmentId = '62a70487b9be8ef0bf86d521';
        $slotId = '62a9d1b6a3a129be24ee6f3f';

        $uri = sprintf('/2.2/branches/%s/appointments/%s/slots/%s', self::BRANCH_ID, $appointmentId, $slotId);

        $response = $this->testAction($uri, [
            'method' => 'delete',
        ]);
        $result = json_decode($response, true, 512, JSON_THROW_ON_ERROR);

        self::assertEquals(403, $this->response->statusCode());
        self::assertEquals(
            MembersCannotDeleteSlot::becauseTheyDontHavePermission()->getMessageCode(),
            $result['message_code']
        );
    }

    public function testUpdateSlotEndpoint(): void
    {
        $this->authenticateAsAdmin();

        $appointmentId = '62a70487b9be8ef0bf86d521';
        $slotId = '62ac6f00ce337a1e6fae131e';

        $uri = sprintf('/2.2/branches/%s/appointments/%s/slots/%s', self::BRANCH_ID, $appointmentId, $slotId);
        $data = [
            'time_start' => time() + 7200, // Update time_start instead of active
        ];

        $response = $this->testAction($uri, [
            'method' => 'put',
            'data' => $data,
        ]);

        $result = json_decode($response, true, 512, JSON_THROW_ON_ERROR);

        self::assertEquals(200, $this->response->statusCode());
        self::assertArrayHasKey('_id', $result);
        self::assertEquals($slotId, $result['_id']);
    }

    public function testUpdateSlotEndpointForNonExistingSlot(): void
    {
        $this->authenticateAsAdmin();

        $appointmentId = '62a70487b9be8ef0bf86d521';
        $slotId = '62ac441ff06316c5bbf6b03b';

        $uri = sprintf('/2.2/branches/%s/appointments/%s/slots/%s', self::BRANCH_ID, $appointmentId, $slotId);
        $data = [
            'active' => false,
        ];

        $response = $this->testAction($uri, [
            'method' => 'put',
            'data' => $data,
        ]);

        $result = json_decode($response, true, 512, JSON_THROW_ON_ERROR);

        self::assertEquals(404, $this->response->statusCode());
        self::assertFalse($result['success']);
        self::assertEquals(sprintf(self::SLOT_NOT_FOUND, $slotId), $result['message']);
    }

    public function testTrainerAvailabilitySlotCreationAndClientBooked()
    {
        Carbon::setTestNow("2024-10-25 09:00:00");

        $this->authenticateAsAdmin();
        $appointmentId = '62a70487b9be8ef0bf86d521';
        $userId = '59a3011a05c677bda916612d';

        $bookingsPublisher = Mockery::mock(BookingsPublisher::class);
        $bookingsPublisher->shouldReceive('sendBookingCreatedEvent');
        app()->instance(BookingsPublisher::class, $bookingsPublisher);

        // make request to 2.1 endpoint with new params
        $uri = sprintf('/2.1/bookings');
        $response = $this->testAction($uri, [
            'method' => 'post',
            'data' => [
                'create_slot' => 'appointment',
                'user_id' => $userId,
                'model' => 'timeslot',
                'model_id' => $appointmentId,
                'time_start' => Carbon::now()->getTimestamp(),
                'staff_id' => self::STAFF_ID,
                'private' => true,
                'branch_id' => self::BRANCH_ID,
                'payment_method' => 'cash',
                'price' => 11,
            ],
        ]);

        $result = json_decode($response, true, 512, JSON_THROW_ON_ERROR);
        self::assertEquals(200, $this->response->statusCode());
        self::assertTrue($result['success']);
        self::assertTrue($result['Booking']['paid']);
        self::assertEquals('BOOKED', $result['Booking']['status']);
        self::assertEquals('appointments', $result['Booking']['model']);
        self::assertEquals($appointmentId, $result['Booking']['model_id']);
        self::assertEquals($userId, $result['Booking']['user_id']);
        self::assertTrue(!empty($result['Booking']['time_slot_id']));

        // check the new slot created from the request
        $newSlot = app()->make(TimeSlotRepository::class)->getActiveSlotById($result['Booking']['time_slot_id']);
        self::assertEquals(self::STAFF_ID, $newSlot->staffId());
        self::assertEquals('appointments', $newSlot->model()->getValue());
        self::assertEquals($appointmentId, $newSlot->modelId());
        self::assertEquals(CreatedBy::TRAINER_AVAILABILITY, $newSlot->createdBy());

        app()->forgetInstance(BookingsPublisher::class);
    }

    public function testTrainerAvailabilityCreateSlotDuringBookingHandleError()
    {
        Carbon::setTestNow("2024-10-25 09:00:00");

        $this->authenticateAsAdmin();
        $appointmentId = '633bc4e9368fcc708a1861ee';

        // make request to 2.1 endpoint with new params
        $uri = sprintf('/2.1/bookings');
        $response = $this->testAction($uri, [
            'method' => 'post',
            'data' => [
                'create_slot' => 'appointment',
                'user_id' => 'thiswillfail',
                'model' => 'timeslot',
                'model_id' => $appointmentId,
                'time_start' => Carbon::now()->getTimestamp(),
                'staff_id' => self::STAFF_ID,
                'private' => true,
                'branch_id' => self::BRANCH_ID,
                'payment_method' => 'cash',
                'price' => 11,
            ],
        ]);

        $result = json_decode($response, true, 512, JSON_THROW_ON_ERROR);
        self::assertFalse($result['success']);
        self::assertEquals('Invalid User Id', $result['message_code']);

        // this appointment should have no slots associated with it. If there are any, then the
        // slot delete code has failed, or somebody has added extra fixture data to this appointment
        $slots = app()->make(TimeSlotRepository::class)->getSlotsByModelId(
            $appointmentId,
            ModelList::APPOINTMENTS
        );
        self::assertTrue(empty($slots));
    }

    public function testTrainerAvailabilityTestAppointmentSlotValidation()
    {
        $this->loginAsUser($this->fetchUser('58568a8fa875ab19530041a7'));
        $userId = '59a3011a05c677bda916612d';
        $appointmentId = '633bc4e9368fcc708a1861ee';

        // trigger the staff validator error
        $uri = sprintf('/2.1/bookings');
        $response = $this->testAction($uri, [
            'method' => 'post',
            'data' => [
                'create_slot' => 'appointment',
                'user_id' => $userId,
                'model' => 'timeslot',
                'model_id' => $appointmentId,
                'time_start' => Carbon::now()->getTimestamp(),
                'staff_id' => self::STAFF_ID,
                'private' => true,
                'branch_id' => self::BRANCH_ID,
                'payment_method' => 'cash',
                'price' => 11,
            ],
        ]);

        $result = json_decode($response, true, 512, JSON_THROW_ON_ERROR);
        self::assertFalse($result['success']);
        self::assertEquals('TRAINERS_CAN_ONLY_ADD_SLOTS_FOR_THEMSELVES', $result['message_code']);

        // this appointment should have no slots associated with it. If there are any, then the
        // slot delete code has failed, or somebody has added extra fixture data to this appointment
        $slots = app()->make(TimeSlotRepository::class)->getSlotsByModelId(
            $appointmentId,
            ModelList::APPOINTMENTS
        );
        self::assertTrue(empty($slots));
    }

    private function mockBookingAppointmentLocker(): void
    {
        $bookingAppointmentLocker = Mockery::mock(EventBookingLockerInterface::class);
        $bookingAppointmentLocker->shouldReceive('lock')->zeroOrMoreTimes();
        $bookingAppointmentLocker->shouldReceive('unlock')->zeroOrMoreTimes();
        $bookingAppointmentLocker
            ->shouldReceive('getEventIdFromAppointment')
            ->andReturn('test-event')
            ->zeroOrMoreTimes();

        app()->forgetInstance(EventBookingLockerInterface::class);
        app()->instance(EventBookingLockerInterface::class, $bookingAppointmentLocker);
    }

    /**
     * @dataProvider semiPrivateAppointmentsFirstBookingTestData
     * @param array $payload
     * @param Closure $assertionClosure
     * @return void
     */
    public function testBookingSemiPrivateAppointmentsSize(array $payload, Closure $assertionClosure): void
    {
        $this->authenticateAsAdmin();

        $bookingsPublisher = Mockery::mock(BookingsPublisher::class);
        $bookingsPublisher->shouldReceive('sendBookingCreatedEvent');
        app()->instance(BookingsPublisher::class, $bookingsPublisher);

        $result = $this->testAction(
            '/2.1/bookings',
            [
                'method' => 'post',
                'data' => $payload
            ]
        );
        $record = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        $this->assertNotEmpty($record);
        $assertionClosure->call($this, $record);

        app()->forgetInstance(BookingsPublisher::class);
    }

    public function semiPrivateAppointmentsFirstBookingTestData(): array
    {
        $payload = [
            'branch_id' => self::BRANCH_ID,
            'create_slot' => 'appointment',
            'discounts' => [],
            'model' => 'timeslot',
            'model_id' => '66ebc6fcf728da7a01baa990',
            'pay_gym' => false,
            'payment_method' => 'cash',
            'price' => null,
            'private' => true,
            'staff_id' => '59a7011a05c677bda916612c',
            'time_start' => Carbon::tomorrow()->getTimestamp(),
            'user_id' => 'eeeeeaaaaabbbbbccccc0000',
        ];

        $payloadWithSize = $payload;
        $payloadWithSize['size'] = 3;

        $payloadWithSizeAboveLimit = $payload;
        $payloadWithSizeAboveLimit['size'] = 11;

        $payloadWithWrongSizeValue = $payload;
        $payloadWithWrongSizeValue['size'] = "wrong-data";

        $payloadWithWrongButStartingWithNumberSizeValue = $payload;
        $payloadWithWrongButStartingWithNumberSizeValue['size'] = "5wrong-data";

        $payloadWithEmptyArrayAsSizeValue = $payload;
        $payloadWithEmptyArrayAsSizeValue['size'] = [];

        $payloadWithNonEmptyArrayAsSizeValue = $payload;
        $payloadWithNonEmptyArrayAsSizeValue['size'] = [1,2,3];

        return [
            '1. Given a staff member, when they try to book a non-existent semi-private appointment with no size set in the request' .
            'Then the booking will be successful and the time slot will have a size of 1' => [
                $payload,
                function ($response) {
                    $this->semiPrivateAppointmentsFirstBookingSuccessAssertion($response, 1);
                }
            ],
            '2. Given a staff member, when they try to book a non-existent semi-private appointment with size set to 3 in the request' .
            'Then the booking will be successful and the time slot will have a size of 3' => [
                $payloadWithSize,
                function ($response) {
                    $this->semiPrivateAppointmentsFirstBookingSuccessAssertion($response, 3);
                }
            ],
            '3. Given a staff member, when they try to book a non-existent semi-private appointment with size set to 11 in the request' .
            'Then the booking will be unsuccessful and an exception will be thrown.' => [
                $payloadWithSizeAboveLimit,
                function ($response) {
                    $this->semiPrivateAppointmentsFirstBookingErrorAssertion($response);
                }
            ],
            '4. Given a staff member, when they try to book a non-existent semi-private appointment with size set to invalid value in the request' .
            'Then the booking will be unsuccessful and an exception will be thrown.' => [
                $payloadWithWrongSizeValue,
                function ($response) {
                    $this->semiPrivateAppointmentsFirstBookingErrorAssertion($response);
                }
            ],
            '5. Given a staff member, when they try to book a non-existent semi-private appointment with size set to invalid value (but it starts with a number) in the request' .
            'Then the booking will be unsuccessful and an exception will be thrown.' => [
                $payloadWithWrongButStartingWithNumberSizeValue,
                function ($response) {
                    $this->semiPrivateAppointmentsFirstBookingErrorAssertion($response);
                }
            ],
            '6. Given a staff member, when they try to book a non-existent semi-private appointment with size set to empty array in the request' .
            'Then the booking will be unsuccessful and an exception will be thrown.' => [
                $payloadWithEmptyArrayAsSizeValue,
                function ($response) {
                    $this->semiPrivateAppointmentsFirstBookingErrorAssertion($response);
                }
            ],
            '7. Given a staff member, when they try to book a non-existent semi-private appointment with size set to non-empty array in the request' .
            'Then the booking will be unsuccessful and an exception will be thrown.' => [
                $payloadWithNonEmptyArrayAsSizeValue,
                function ($response) {
                    $this->semiPrivateAppointmentsFirstBookingErrorAssertion($response);
                }
            ],
        ];
    }

    private function semiPrivateAppointmentsFirstBookingSuccessAssertion(array $response, int $expectedSize): void
    {
        $this->assertTrue($response['success']);
        $this->assertNotNull($response['Booking']['time_slot_id']);
        $this->assertEquals($expectedSize, $this->timeSlotRepository->getById($response['Booking']['time_slot_id'])->size());
    }

    private function semiPrivateAppointmentsFirstBookingErrorAssertion(array $response): void
    {
        $this->assertFalse($response['success']);
        $this->assertEquals('APPOINTMENT_SLOT_CANNOT_BE_CREATED', $response['message_code']);
    }
}
