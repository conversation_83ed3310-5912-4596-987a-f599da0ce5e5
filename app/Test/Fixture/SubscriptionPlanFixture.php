<?php

class SubscriptionPlanFixture extends CakeTestFixture
{
    public $import = ['model' => 'SubscriptionPlan'];

    public function init()
    {
        $records = [
            [
                '_id' => '5a53b98625f149480f000001',
                'currency' => 'USD',
                'active' => true,
                'name' => '10 USD every 1 days',
                'namespace' => 'glofox',
                'branch_id' => '49a7011a05c677b9a916612a',
                'amount' => 10,
                'interval' => 'month',
                'interval_count' => 1,
                'modified' => new MongoDate(strtotime('2018-01-08T18:33:42.916+00:00')),
                'created' => new MongoDate(strtotime('2018-01-08T18:33:42.916+00:00')),
                'description' => '10 USD every 1 days',
            ],
            [
                '_id' => new MongoId('5a53b98625f149480f000002'),
                'currency' => 'USD',
                'active' => true,
                'name' => '10 USD every 1 days',
                'namespace' => 'glofox',
                'branch_id' => '49a7011a05c677b9a916612a',
                'amount' => 10,
                'interval' => 'month',
                'interval_count' => 1,
                'modified' => new MongoDate(strtotime('2018-01-08T18:33:42.916+00:00')),
                'created' => new MongoDate(strtotime('2018-01-08T18:33:42.916+00:00')),
                'description' => '10 USD every 1 days',
                'payment_gateway_id' => '11'
            ],
            [
                '_id' => new \MongoId('59a6948625f149e204000001'),
                'currency' => 'EUR',
                'active' => true,
                'name' => '500 EUR every 1 months',
                'namespace' => 'yogawithtimmy',
                'branch_id' => '49a7011a05c677b9a916612a',
                'amount' => 500.0,
                'interval' => 'month',
                'interval_count' => 1,
                'modified' => new \MongoDate(strtotime('2018-11-26T15:45:10')),
                'created' => new \MongoDate(strtotime('2018-11-26T15:45:10')),
                'description' => '500 EUR every 1 months',
                'payment_gateway_id' => '306'
            ],
            [
                '_id' => new \MongoId('59a6948625f149e204000002'),
                'currency' => 'EUR',
                'active' => true,
                'name' => '500 EUR every 1 months',
                'namespace' => 'yogawithtimmy',
                'branch_id' => '49a7011a05c677b9a916612a',
                'amount' => 500.0,
                'interval' => 'month',
                'interval_count' => 2,
                'modified' => new \MongoDate(strtotime('2018-11-26T15:45:10')),
                'created' => new \MongoDate(strtotime('2018-11-26T15:45:10')),
                'description' => '500 EUR every 1 months',
                'payment_gateway_id' => '307'
            ],
            [
                '_id' => new \MongoId('622a11275822922a3460f232'),
                'currency' => 'EUR',
                'active' => true,
                'name' => '20 EUR every 1 months',
                'namespace' => 'glofox',
                'branch_id' => '49a7011a05c677b9a916612b',
                'amount' => 20.0,
                'interval' => 'month',
                'interval_count' => 1,
                'modified' => new \MongoDate(strtotime('2018-11-26T15:45:10')),
                'created' => new \MongoDate(strtotime('2018-11-26T15:45:10')),
                'description' => '20 EUR every 1 months',
                'payment_gateway_id' => '255'
            ],
        ];

        $this->records = $records;

        parent::init();
    }
}
