<?php

namespace Glofox\Response\Models\Branches\Features;

use Glofox\Response\BaseModel;

/**
 * @SuppressWarnings(PHPMD)
 */
class SubscriptionsModel extends BaseModel
{
    /**
     * @var int
     */
    public $order = 0;

    /**
     * @var bool
     */
    public $edit_price = false;

    protected array $allowedFields = [
        'order',
        'edit_price',
    ];

    public function __construct(array $data)
    {
        $this->setModelFields($data);
    }
}
