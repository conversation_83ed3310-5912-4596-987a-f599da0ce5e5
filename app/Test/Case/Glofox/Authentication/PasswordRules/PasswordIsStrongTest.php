<?php

namespace CakeTestCases\Glofox\Authentication\PasswordRules;

use App;
use Glofox\Authentication\PasswordRules\PasswordIsStrong;
use Glofox\Domain\Users\Models\User;

App::import('Test/Case', 'GlofoxTestCase');

class PasswordIsStrongTest extends \GlofoxTestCase
{
    /** @var User */
    private $target;

    public function setUp()
    {
        parent::setUp();

        $this->target = User::make();
    }

    public function testItShouldFailWhenPasswordHasNoUpperCaseLetter()
    {
        $rule = new PasswordIsStrong();
        $result = $rule->check($this->target, 'password82098$');

        self::assertFalse($result);
    }

    public function testItShouldFailWhenPasswordHasNoLowerCaseLetter()
    {
        $rule = new PasswordIsStrong();
        $result = $rule->check($this->target, 'PASSWORD82098$');

        self::assertFalse($result);
    }

    public function testItShouldFailWhenPasswordHasNoDigit()
    {
        $rule = new PasswordIsStrong();
        $result = $rule->check($this->target, 'PASaaaSWORD$');

        self::assertFalse($result);
    }

    public function testItShouldFailWhenPasswordHasNoSpecialCharacter()
    {
        $rule = new PasswordIsStrong();
        $result = $rule->check($this->target, 'PASSaaWORD739843');

        self::assertFalse($result);
    }

    public function testItShouldPassWhenCriteriaIsMet()
    {
        $rule = new PasswordIsStrong();
        $result = $rule->check($this->target, 'PASSaaWORD739843$');

        self::assertTrue($result);
    }
}
