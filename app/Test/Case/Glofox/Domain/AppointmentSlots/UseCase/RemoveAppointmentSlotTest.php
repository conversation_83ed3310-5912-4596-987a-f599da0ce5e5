<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\AppointmentSlots\UseCase;

use App;
use Glofox\Domain\AppointmentSlots\UseCase\RemoveAppointmentSlot;
use Glofox\Domain\TimeSlots\Repositories\TimeSlotRepository;
use GlofoxTestCase;
use Mockery;

App::import('Test/Case', 'GlofoxTestCase');

class RemoveAppointmentSlotTest extends GlofoxTestCase
{
    public $fixtures = [];
    private TimeSlotRepository $timeSlotRepository;

    public function setUp()
    {
        parent::setUp();

        $this->timeSlotRepository = Mockery::mock(TimeSlotRepository::class);
    }

    public function tearDown()
    {
        parent::tearDown();

        Mockery::close();
    }

    public function testDeleteAppointmentSlot(): void
    {
        $id = 'test-slot-id';
        $bookingId = 'test-booking-id';

        $this->timeSlotRepository
            ->shouldReceive('removeAppointmentSlot')
            ->withArgs(function (
                string $slotId,
                string $bookingIdParam
            ) use (
                $id,
                $bookingId
            ) {
                self::assertEquals($id, $slotId);
                self::assertEquals($bookingId, $bookingIdParam);

                return true;
            })
            ->andReturnTrue();

        $useCase = new RemoveAppointmentSlot($this->timeSlotRepository);
        $useCase->execute($id, $bookingId);
    }
}
