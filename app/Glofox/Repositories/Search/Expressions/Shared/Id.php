<?php

namespace Glofox\Repositories\Search\Expressions\Shared;

use Glofox\Datasource\Exceptions\InvalidMongoIdException;
use Glofox\Repositories\Filters\Evaluable;
use Glofox\Repositories\Search\Expressions\Conditionable;
use Glofox\Repositories\Search\Expressions\HasConditions;
use Glofox\Repositories\Search\Expressions\Key;
use Webmozart\Expression\Expr;


class Id extends Key implements Evaluable, Conditionable
{
    use HasConditions;

    public function __construct(string $id)
    {
        if (!\MongoId::isValid($id)) {
            throw InvalidMongoIdException::withId($id);
        }

        parent::__construct('_id', Expr::equals($id));
    }

    public function field() : string
    {
        return '_id';
    }

    protected function dbValueFromExpression()
    {
        return new \MongoId(
            parent::dbValueFromExpression()
        );
    }
}
