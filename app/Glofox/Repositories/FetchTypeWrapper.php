<?php

namespace Glofox\Repositories;

class FetchTypeWrapper
{
    protected $repository;

    protected $fetchType;

    public function __construct(Repository $repository, FetchType $fetchType)
    {
        $this->repository = $repository;
        $this->fetchType = $fetchType;
    }

    public function __call($name, $arguments)
    {
        $this->repository->withFetchType($this->fetchType);

        $method = new \ReflectionMethod($this->repository, $name);

        $method->setAccessible(true);

        $result = $method->invokeArgs($this->repository, $arguments);

        $this->repository->resetFetchType();

        return $result;
    }
}
