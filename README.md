# Core API

[![CodeScene Code Health](https://codescene.io/projects/57618/status-badges/code-health)](https://codescene.io/projects/57618)
[![Quality Gate Status](https://sonarcloud.io/api/project_badges/measure?project=glofoxinc_api&metric=alert_status&token=5cbd8e5b193f6ab7f1c73aebc8696e16d8d42d34)](https://sonarcloud.io/summary/new_code?id=glofoxinc_net-billing)
[![Code Smells](https://sonarcloud.io/api/project_badges/measure?project=glofoxinc_api&metric=code_smells&token=5cbd8e5b193f6ab7f1c73aebc8696e16d8d42d34)](https://sonarcloud.io/summary/new_code?id=glofoxinc_net-billing)
[![Coverage](https://sonarcloud.io/api/project_badges/measure?project=glofoxinc_api&metric=coverage&token=5cbd8e5b193f6ab7f1c73aebc8696e16d8d42d34)](https://sonarcloud.io/summary/new_code?id=glofoxinc_net-billing)
[![Duplicated Lines (%)](https://sonarcloud.io/api/project_badges/measure?project=glofoxinc_api&metric=duplicated_lines_density&token=5cbd8e5b193f6ab7f1c73aebc8696e16d8d42d34)](https://sonarcloud.io/summary/new_code?id=glofoxinc_net-billing)
[![Vulnerabilities](https://sonarcloud.io/api/project_badges/measure?project=glofoxinc_api&metric=vulnerabilities&token=5cbd8e5b193f6ab7f1c73aebc8696e16d8d42d34)](https://sonarcloud.io/summary/new_code?id=glofoxinc_net-billing)
[![Bugs](https://sonarcloud.io/api/project_badges/measure?project=glofoxinc_api&metric=bugs&token=5cbd8e5b193f6ab7f1c73aebc8696e16d8d42d34)](https://sonarcloud.io/summary/new_code?id=glofoxinc_net-billing)
[![Security Rating](https://sonarcloud.io/api/project_badges/measure?project=glofoxinc_api&metric=security_rating&token=5cbd8e5b193f6ab7f1c73aebc8696e16d8d42d34)](https://sonarcloud.io/summary/new_code?id=glofoxinc_net-billing)
[![CodeScene Code Health](https://codescene.io/projects/57618/status-badges/code-health)](https://codescene.io/projects/57618)
[![Quality Gate Status](https://sonarcloud.io/api/project_badges/measure?project=glofoxinc_api&metric=alert_status&token=5cbd8e5b193f6ab7f1c73aebc8696e16d8d42d34)](https://sonarcloud.io/summary/new_code?id=glofoxinc_api)
[![Code Smells](https://sonarcloud.io/api/project_badges/measure?project=glofoxinc_api&metric=code_smells&token=5cbd8e5b193f6ab7f1c73aebc8696e16d8d42d34)](https://sonarcloud.io/summary/new_code?id=glofoxinc_api)
[![Coverage](https://sonarcloud.io/api/project_badges/measure?project=glofoxinc_api&metric=coverage&token=5cbd8e5b193f6ab7f1c73aebc8696e16d8d42d34)](https://sonarcloud.io/summary/new_code?id=glofoxinc_api)
[![Duplicated Lines (%)](https://sonarcloud.io/api/project_badges/measure?project=glofoxinc_api&metric=duplicated_lines_density&token=5cbd8e5b193f6ab7f1c73aebc8696e16d8d42d34)](https://sonarcloud.io/summary/new_code?id=glofoxinc_api)
[![Vulnerabilities](https://sonarcloud.io/api/project_badges/measure?project=glofoxinc_api&metric=vulnerabilities&token=5cbd8e5b193f6ab7f1c73aebc8696e16d8d42d34)](https://sonarcloud.io/summary/new_code?id=glofoxinc_api)
[![Bugs](https://sonarcloud.io/api/project_badges/measure?project=glofoxinc_api&metric=bugs&token=5cbd8e5b193f6ab7f1c73aebc8696e16d8d42d34)](https://sonarcloud.io/summary/new_code?id=glofoxinc_api)
[![Security Rating](https://sonarcloud.io/api/project_badges/measure?project=glofoxinc_api&metric=security_rating&token=5cbd8e5b193f6ab7f1c73aebc8696e16d8d42d34)](https://sonarcloud.io/summary/new_code?id=glofoxinc_api)

The Core API is one of the core elements of the technology within Glofox. It's currently running CakePHP 2.10 on PHP 7.4, with some components totally independent of frameworks (the long-term goal).

---

- [Core API](#core-api)
  - [Requirements](#requirements)
  - [Coding Standards and Architecture](#coding-standards-and-architecture)
  - [Quick start](#quick-start)
    - [Setup using Docker Compose](#setup-using-docker-compose)
    - [Local Database](#local-database)
    - [Walk-through Video](#walk-through-video)
  - [Debugging](#debugging)
    - [Set debugger in MacOS for PhpStorm](#set-debugger-in-macos-for-phpstorm)
      - [Setup debug port](#setup-debug-port)
      - [Setup debugger server](#setup-debugger-server)
      - [Setup PHP interpreter](#setup-php-interpreter)
      - [Using the debugger in PhpStorm](#using-the-debugger-in-phpstorm)
    - [Set debugger in MacOS for VS Code](#set-debugger-in-macos-for-vs-code)
      - [VS Code debugger requirements](#vs-code-debugger-requirements)
      - [Using the debugger in VS Code](#using-the-debugger-in-vs-code)
  - [Running Unit Tests](#running-unit-tests)
    - [Tests CI/CD Workflow](#tests-cicd-workflow)
    - [Single tests](#single-tests)
    - [All tests](#all-tests)
    - [Run tests like CI](#run-tests-like-ci)
    - [Test cron job scripts](#test-cron-job-scripts)
  - [Profiling](#profiling)
    - [Profiling set up](#profiling-set-up)
    - [Visualization tools](#visualization-tools)
      - [Setting up qcachegrind for MacOS](#setting-up-qcachegrind-for-macos)
      - [Setting up kcachegrind for Linux/Ubuntu](#setting-up-kcachegrind-for-linuxubuntu)
  - [API Documentation](#api-documentation)
  - [How to update docker containers](#how-to-update-docker-containers)
  - [Code Version Compatibility](#code-version-compatibility)
  - [Hooks](#hooks)
  - [Rector](#rector)
    - [Fixing Rector CI Warnings](#fixing-rector-ci-warnings)
  - [MongoDB Upgrade](#mongodb-upgrade)
    - [MongoDB Upgrade: Docker Container](#mongodb-upgrade-docker-container)
  - [Other references](#other-references)

---

## Requirements

Make sure your machine meets the following requirements before proceeding:

- Docker and Docker Compose with [Rancher](https://rancherdesktop.io/).
- [Rancher setup](https://www.notion.so/glofox/Runbook-Install-Rancher-Desktop-9c5b23a599ab40949edeaaea9d8acfe9?pvs=4#f4e502eab59a463caa759d936b43ac29).
- [Glofox's AWS Account](https://aws.amazon.com/) (request account creation to your manager).
- [Glofox's Github's Account](https://github.com/glofoxinc/) (request account creation to your manager).
- [Github Personal Access Token](https://help.github.com/en/articles/creating-a-personal-access-token-for-the-command-line) (with `repo` permissions enabled).
- [Download and setup your MFA repository](https://github.com/glofoxinc/workflow_automation).

## Coding Standards and Architecture

- [Redis Locker](./docs/standards-architecture/redis-locker.md)

## Quick start

### Setup using Docker Compose

We have 3 services in `docker-compose.yml`: `app`, `db`, and `cache`:

- `app` is the service with PHP and Nginx. It runs in localhost under port `8889`.
- `db` is our MongoDB database. It runs in localhost under port `27036`.
- `cache` is a cache service using Redis (used for bookings logic and lockers).

In `docker/config/local/override` the files can be used to override default variables. It is important to define the right values for `JWT_SALT_GENERATE` and `JWT_SALT_VERIFY` at least, as they are needed for core functionalities, like testing controllers. It is recommended to use a non-production environment for those vars (like development).

To override the docker-compose data for local purposes, you need to create a `docker-compose.override.yml` file. `docker-compose.override.yml.dist` includes some interesting configurations for it.

If you are using a Mac with M1 or M2, is required to create the docker compose override with the suggested settings provided inside the `dist` file.

Make sure you have set your `GITHUB_ACCESS_TOKEN` in your personal `.env` file inside `workflow_automation`, and that the token has `repo` permissions.

Before building the containers, you need to authenticate into AWS running:

```bash
awsmfa-login {{aws-mfa-token}}
docker login -u AWS -p $(aws ecr get-login-password --region eu-west-1) 108272622497.dkr.ecr.eu-west-1.amazonaws.com
```

**IMPORTANT**: Both commands must be executed into the same terminal.

Then, run `make build` to build and start the containers.

> **NOTE**: For an unknown reason, sometimes the first build will fail because of permissions related to mounted
> volumes fail, but the second one work. To avoid it to happen, create the mounted folders in advance.

### Local Database

If you want to use a local database, you need to edit the `docker/config/local/app.env` updating the `GLOFOX_DATABASE_NAME` environment variable, and then restart the container.

```yaml
GLOFOX_DATABASE_NAME={{dbname}}
```

### Walk-through Video

> ⚠️ Outdated

If you're a visual person we have this video recorded going through the basic processes to get Core API up and running. On this video we cover:

- Installation using Docker Compose;
- All required setup using PhpStorm;
- How to configure Xdebug on MacOSX and PhpStorm;
- How to run tests via command line.

Video: [How to setup Core API on Mac using Docker.mp4](https://drive.google.com/file/d/1Dw70dJIZf-joJagJXKfWp-atdBDn9rYc/view?usp=sharing)

---

## Debugging

### Set debugger in MacOS for PhpStorm

*[Set debugger in MacOS for PhpStorm video](https://abcfinancial.sharepoint.com/:v:/t/MsO365-IntegrationUpdatesandInformation/EV0Unxnz2yBGhoNyhVUPztYBmKKKfOsFb5tg7G_ihihFhQ?e=siImoM)*

First of all, be sure that env `PHP_XDEBUG_DISCOVER_CLIENT_HOST` is equal to 0 or does not exist at `/docker/config/local/override/app.env`. To apply any changes for this env, run `make rebuild-container container=app`.

#### Setup debug port

Xdebug 3 uses port `9003` for connection instead of `9000`. If your PhpStorm has `9000` configured, change it to `9003`.

Go to `Settings`, `PHP`, `Debug` and set `Debug port` to `9003`.

#### Setup debugger server

Go to `Run` and `Edit Configurations`, you will see the `Run/Debug Configuration` window.

We need to create a new Xdebug remote configuration. For that, click on the `+` icon and select `PHP Remote Debug`.

At this point, if you're configuring Core API for the first time, you don't have a "Server" configured yet. First check `Filter debug connection by IDE key` option. Then, close to `Server` click on the `...`. Create a new server and give it the name `xdebug-docker-php7`. Make sure that:

- `Host` is `127.0.0.1` (your localhost).
- `Port` is `8889`.
- `Debugger` is Xdebug.
- `Use path mappings` is checked.
- You're mapping `{AbsolutePath}/api` directory to `/var/www` on the server.

Save the server and set the IDE key (session id) as `docker`. Save the configuration.

#### Setup PHP interpreter

Go to `Settings`, `PHP` and select `PHP language level` as 7.4.

Now we need to configure our `CLI Interpreter`. Click on the `...` and we will add a new entry there. Create a new interpreter going to the `+` and choosing `From Docker, Vagrant...` option.

You should select `Docker Compose` and the other fields will be filled automatically. Select the service as `app` and save.

Before saving, if there is a docker error when reloading php info, like `Cannot connect to the Docker daemon at unix:///var/run/docker.sock. Is the docker daemon running?`, follow the next steps to fix it:

1. Go to `Settings`, `Build, Execution, Deployment` and `Docker`.
2. Select the right docker for Mac socket. Its options will change based on your docker versions (Rancher, Colima, Docker Desktop...).

Then you should edit `Additional / Debugger extension` with the following value:

```bash
/usr/local/lib/php/extensions/no-debug-non-zts-20190902/xdebug.so
```

#### Using the debugger in PhpStorm

At the top-right of the IDE, select `docker` as `Run/Debug Configurations` option and then `Start Listening for PHP Debug Connections`, which is at the left of the previous selector.

When the debugging is finished, just stop listening using the same button.

### Set debugger in MacOS for VS Code

*[Set debugger in MacOS for VS Code video](https://abcfinancial.sharepoint.com/:v:/t/MsO365-IntegrationUpdatesandInformation/EX4KJ-jAMcBCqtuh38MK1lwBNnu4GXr9dDicjYm-NGRR6Q?e=eBN1Ve)*

#### VS Code debugger requirements

- Container up and running. See [Setup using Docker Compose](#setup-using-docker-compose) section.
- Install recommended extension `xdebug.php-debug`.

#### Using the debugger in VS Code

First of all, be sure that env `PHP_XDEBUG_DISCOVER_CLIENT_HOST` is equal to 1 at `/docker/config/local/override/app.env`. To apply any changes for this env, run `make rebuild-container container=app`.

1. In the `Run and Debug` menu, by default on the left-hand side of VS Code, select the task `Listen for Xdebug` and run it.
2. Add any breakpoints in VS Code.
3. Either run a unit test through the `make test` method or hit and endpoint if Core API is running.
4. Assert that the debugger hijacks your code.

---

## Running Unit Tests

> ⚠️ Before running unit tests it's very important to have defined in `docker/config/local/override/app.env` the database as `GLOFOX_DATABASE_NAME=test`, otherwise some tests may fail.

Unit tests are automatically triggered per every push made to the repository by CircleCI, and mark its status in GitHub.

### Tests CI/CD Workflow

Tests are run in CI/CD workflow in parallel. All the tests are grouped in different nodes and each node is run in parallel, balancing them between the nodes based on their weight, ensuring that all nodes take a similar amount of time to run.

Currently, we have nodes from 0 to 3 balanced between all the tests, except for [Legacy Bookings Controller Test](./app/Test/Case/Controller/BookingsControllerTest.php), which is run in node 4 and this node only runs this test, because it takes long time to run.

Review [AllTests documentation](./app/Test/Case/AllTests.md) to learn more about this logic and how to configure it.

### Single tests

To run the tests for a single file, assuming the file is: `app/Test/Case/Glofox/Domain/AppointmentSlots/UseCase/GetVirtualAppointmentSlotsTest.php`, you should use the following command:

```bash
make up # Only if container wasn't initialized before
make test file=Glofox/Domain/AppointmentSlots/UseCase/GetVirtualAppointmentSlots
```

Notice that `app/Test/Case/...` and `...Test.php` are removed from the relative path.

To run a single test for a single file, you should use the following command:

```bash
make up # Only if container wasn't initialized before
make test file=Glofox/Domain/AppointmentSlots/UseCase/GetVirtualAppointmentSlots test=testNoTimeStartTimeFinish
```

### All tests

Execute the following command to run all the available tests in the repository:

```bash
make test-all
```

### Run tests like CI

Our CI system (currently CircleCI) splits the tests in different nodes, and each node runs different tests. It currently has 5 nodes.

Execute the following command to simulate a node execution:

```bash
make test-ci nodeTotal={{totalNodes}} nodeIndex={{nodeIndex}}
```

Where:

- `totalNodes`: is the number of total available nodes, from 2.
- `nodeIndex`: is the node index that will be executed, where `nodexIndex=0` is the first node.

For example, to run the 2nd node of 5 total nodes:

```bash
make test-cci nodeTotal=5 nodeIndex=1
```

### Test cron job scripts

*[This document](https://www.notion.so/glofox/Test-Core-API-Cron-Jobs-10e14b641fbb80879575d834da355d29)* outlines the procedure and best practices for testing cron jobs. The goal is to ensure that scheduled tasks execute as expected, both in terms of timing and functionality, before deployment to production.

---

## Profiling

### Profiling set up

If you want to enable xdebug profiling, please edit the `docker/config/local/override/app.env`
setting the `PHP_XDEBUG_MODE` environment variable to `profile` mode, `PHP_XDEBUG_ENABLE` to 1 and restart the container.

```bash
PHP_XDEBUG_ENABLE=1
PHP_XDEBUG_MODE=profile
```

All the profiling files will be located at `profiles/` folder.

When you finish, please comment `PHP_XDEBUG_MODE` and restart the container, as this option
impacts on local environment performance.

### Visualization tools

#### Setting up qcachegrind for MacOS

On your terminal, you need to run `brew install qcachegrind`. You will need always to open it
from your terminal with `qcachegrind`, if not some functionalities of the application doesn't
work properly.

#### Setting up kcachegrind for Linux/Ubuntu

On your terminal, you need to run `sudo apt install kcachegrind`.

## API Documentation

OpenAPI documentation can be edited with any openapi editor such as Stoplight Studio or [Swagger online editor](https://editor.swagger.io/).

See [Notion page](https://www.notion.so/glofox/Documenting-APIs-d6316000cf4347969c32a2548873b096) for more info.

## How to update docker containers

When a docker container is updated, is mandatory to run `make rebuild-container container=${containerServiceName}` command. For more info, see [Docker related commands](./makefile.md#docker-related-commands) section.

---

## Code Version Compatibility

To check if the current code is compatible with newer PHP versions, we use [PHPCompatibility](https://github.com/PHPCompatibility/PHPCompatibility) library. Then, you can run the following command to receive a command line report.

```{bash}
make check-compatibility version={{phpVersion}}

# Example for PHP8.1
make check-compatibility version=8.1
```

If you want to store the report to a file, you should set `report` argument.

```{bash}
make check-compatibility version={{phpVersion}} report={{filePath}}

# Example for PHP8.1 and phpcompatiblity/php-81 report file
make check-compatibility version=8.1 report=phpcompatiblity/php81
```

---

## Hooks

Check `composer.json`, you'll see the available hooks under `extra.hooks`. They are installed automatically when you set up the project.

[Click here to read through the Git Hooks documentation.](https://glofox.atlassian.net/wiki/spaces/ENG/pages/669777921/How+we+facilitate+to+comply+with+Engineering+Coding+Standards)

---

## Rector

Rector is a library to automatically update the PHP code. More info [here](https://getrector.com/documentation).

Its configuration is located into `rector.php` file.

Use the following command to run rector in dry-run mode:

```{bash}
docker exec glofox_app ./vendor/bin/rector process --dry-run
```

After it, apply the changes found by dry-run mode executing:

```{bash}
docker exec glofox_app ./vendor/bin/rector process
```

### Fixing Rector CI Warnings

Rector is part of our CI pipeline, an required to pass to deploy a PR to master. If there's any error on that job, run the following commands:

```bash
make generate-rector-paths
docker exec glofox_app ./vendor/bin/rector process
```

These commands will automatically fix the code. It's important to run `make generate-rector-paths`, as it generates a list of PHP files to be review by Rector, otherwise the second command will process *all* the files, which will take a long time to run. Those files from the make command are the once that were changed in the current branch.

## MongoDB Upgrade

When upgrading MongoDB version in production or other environments, Docker and CircleCI files must be updated to be aligned with the environment version.

To do it, run the following make command:

```bash
make mongo-upgrade version=${version}
```

For example, `make mongo-upgrade version=7.0.18` to use v6.

This will automatically create the corresponding `/docker/config/mongo-${version}/Dockerfile` with the expected version, update `/.circleci/config.yml` mongo-version setting and remove the `/docker/config/mongo-X` of the version that we were using.

For the new Dockerfile, it is important to review the selected NodeJs version (e.g. `https://deb.nodesource.com/setup_20.x`), as it must be compatible with the new MongoDB version. Mongo already provides a compatibility table that can be found [here](https://www.mongodb.com/docs/drivers/node/v6.0/compatibility/).

When everything is configured properly, update the container
[update the container](#how-to-update-docker-containers).

### MongoDB Upgrade: Docker Container

When updating mongo container, there can be some issues:

- When persisting the mongo db files, the new version can be incompatible with the existing files. In this case, just remove the container folder located in your laptop.
- If the mongo container doesn't work, and in the container logs there's an error like `requires a CPU with AVX support`, override the docker compose file, adding to `db` service the parameter `platform: linux/arm64`.

## Other references

- [Code Style](https://www.notion.so/glofox/Code-Style-bf89d0a985f84276b346583422cdb258)
- [Core API Code standards and Architecture](https://www.notion.so/glofox/Core-API-Code-standards-and-Architecture-018d1cbc1a4542cca83130dfb6f5f95f)
- [Engineering](https://www.notion.so/glofox/Engineering-2c805b28899e48bc8e2230bedaedcd1a)
- [Make Commands Documentation](./makefile.md)
