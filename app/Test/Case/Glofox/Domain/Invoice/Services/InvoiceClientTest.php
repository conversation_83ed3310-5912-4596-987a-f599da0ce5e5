<?php

namespace CakeTestCases\Glofox\Domain\Invoice\Services;

use CakeTestCases\Glofox\Domain\Authentication\Token\TokenGeneration;
use CakeTestCases\Glofox\Domain\Users\Traits\FetchUsersTrait;
use Exception;
use Glofox\Domain\Invoice\Services\InvoiceClient;
use Glofox\Domain\RequestLog\Repositories\RequestLogRepository;
use Glofox\Http\Requests\TracingContextInterface;
use GuzzleHttp\ClientInterface;
use GuzzleHttp\Exception\ServerException;
use Glofox\Infrastructure\Exception\HttpClientResponseException;
use Psr\Http\Message\ResponseInterface;
use Psr\Log\LoggerInterface;

\App::import('Test/Case', 'GlofoxTestCase');

class InvoiceClientTest extends \GlofoxTestCase
{
    use TokenGeneration;
    use FetchUsersTrait;

    protected $httpClient;

    protected $logRepository;

    protected $logger;

    protected $currentRequestIdService;

    protected $tracingContext;

    public function setUp()
    {
        parent::setUp();

        $this->logger = \Mockery::mock(LoggerInterface::class);
        $this->logger->shouldReceive('info');
        $this->logger->shouldReceive('warning');
        $this->logger->shouldReceive('error');

        $this->tracingContext = \Mockery::mock(TracingContextInterface::class);
        $this->tracingContext->shouldReceive('getTracingHeaderForPropagation')
            ->andReturn('1;trace_id=********************************,parent_id=7248651b52e67446');

        $_SERVER['HTTP_X_GLOFOX_SOURCE'] = 'DASHBOARD';
        $_SERVER['HTTP_X_GLOFOX_BRANCH_ID'] = 'fake-branch-id';
        $_SERVER['HTTP_X_REQUEST_ID'] = 'fake-request-id';
    }

    public function tearDown()
    {
        parent::tearDown();
        \Mockery::close();
        unset($_SERVER['HTTP_X_GLOFOX_SOURCE']);
    }

    public function test_should_create_a_get_transaction_request()
    {
        $this->httpClient = \Mockery::mock(\GuzzleHttp\ClientInterface::class);
        $this->httpClient
            ->shouldReceive('request')
            ->with('GET', 'v1/studios/123456789/transactions/abc123', [
                'http_errors' => true,
                'headers' => [
                    'X-Honeycomb-Trace' => '1;trace_id=********************************,parent_id=7248651b52e67446',
                    'X-Glofox-Source' => 'DASHBOARD',
                    'x-glofox-branch-id' => 'fake-branch-id',
                    'x-request-id' => 'fake-request-id',
                    'Content-Type' => 'application/json'
                ],
                'verify' => true
            ])
            ->andReturn(
                \Mockery::mock(\Psr\Http\Message\ResponseInterface::class)
                    ->shouldReceive('getBody')
                    ->andReturn('{"foo":"bar"}')
                    ->shouldReceive('getStatusCode')
                    ->andReturn(200)
                    ->getMock()
            );
        $this->logRepository = \Mockery::mock(RequestLogRepository::class);

        $response = $this->createClient()->getTransactionById('123456789', 'abc123');

        $this->assertEquals('bar', $response->getData()->foo);
    }

    public function test_should_log_failed_get_transaction_request()
    {
        $this->httpClient = \Mockery::mock(\GuzzleHttp\ClientInterface::class);
        $this->httpClient
            ->shouldReceive('request')
            ->with('GET', 'v1/studios/123456789/transactions/abc123', [
                'http_errors' => true,
                'headers' => [
                    'X-Honeycomb-Trace' => '1;trace_id=********************************,parent_id=7248651b52e67446',
                    'X-Glofox-Source' => 'DASHBOARD',
                    'x-glofox-branch-id' => 'fake-branch-id',
                    'x-request-id' => 'fake-request-id',
                    'Content-Type' => 'application/json'
                ],
                'verify' => true
            ])
            ->andThrow(
                new ServerException(
                    'Foo bar',
                    new \GuzzleHttp\Psr7\Request('GET', 'v1/studios/123456789/transactions/abc123'),
                    new \GuzzleHttp\Psr7\Response(500)
                )
            );
        $this->logRepository = \Mockery::mock(RequestLogRepository::class);
        $this->logRepository
            ->shouldReceive('saveOrFail')
            ->with([
                'service' => 'invoice',
                'status_code' => 500,
                'request_uri' => 'v1/studios/123456789/transactions/abc123',
            ]);

        $this->setExpectedException(HttpClientResponseException::class);

        $response = $this->createClient()->getTransactionById('123456789', 'abc123');
    }

    public function test_should_log_failed_get_transaction_request_generic_exception()
    {
        $this->httpClient = \Mockery::mock(\GuzzleHttp\ClientInterface::class);
        $this->httpClient
            ->shouldReceive('request')
            ->with('GET', 'v1/studios/123456789/transactions/abc123', [
                'http_errors' => true,
                'headers' => [
                    'X-Honeycomb-Trace' => '1;trace_id=********************************,parent_id=7248651b52e67446',
                    'X-Glofox-Source' => 'DASHBOARD',
                    'x-glofox-branch-id' => 'fake-branch-id',
                    'x-request-id' => 'fake-request-id',
                    'Content-Type' => 'application/json'
                ],
                'verify' => true
            ])
            ->andThrow(
                new Exception(
                    'Foo bar',
                )
            );
        $this->logRepository = \Mockery::mock(RequestLogRepository::class);
        $this->logRepository
            ->shouldReceive('saveOrFail')
            ->with([
                'service' => 'invoice',
                'status_code' => 0,
                'request_uri' => 'v1/studios/123456789/transactions/abc123',
            ]);

        $this->setExpectedException(HttpClientResponseException::class);

        $response = $this->createClient()->getTransactionById('123456789', 'abc123');
    }

    public function test_should_create_a_get_invoice_v2_request()
    {
        $this->httpClient = \Mockery::mock(\GuzzleHttp\ClientInterface::class);
        $this->httpClient
            ->shouldReceive('request')
            ->with('GET', 'v2/invoices/abc-123', [
                'http_errors' => true,
                'headers' => [
                    'X-Honeycomb-Trace' => '1;trace_id=********************************,parent_id=7248651b52e67446',
                    'X-Glofox-Source' => 'DASHBOARD',
                    'x-glofox-branch-id' => 'fake-branch-id',
                    'x-request-id' => 'fake-request-id',
                    'Content-Type' => 'application/json'
                ],
                'verify' => true
            ])
            ->andReturn(
                \Mockery::mock(\Psr\Http\Message\ResponseInterface::class)
                    ->shouldReceive('getBody')
                    ->andReturn('{"foo":"bar"}')
                    ->shouldReceive('getStatusCode')
                    ->andReturn(200)
                    ->getMock()
            );
        $this->logRepository = \Mockery::mock(RequestLogRepository::class);

        $response = $this->createClient()->getV2('abc-123');

        $this->assertEquals('bar', $response->getData()->foo);
    }

    public function test_should_log_failed_get_invoice_v2_request()
    {
        $this->httpClient = \Mockery::mock(\GuzzleHttp\ClientInterface::class);
        $this->httpClient
            ->shouldReceive('request')
            ->with('GET', 'v2/invoices/abc-123', [
                'http_errors' => true,
                'headers' => [
                    'X-Honeycomb-Trace' => '1;trace_id=********************************,parent_id=7248651b52e67446',
                    'X-Glofox-Source' => 'DASHBOARD',
                    'x-glofox-branch-id' => 'fake-branch-id',
                    'x-request-id' => 'fake-request-id',
                    'Content-Type' => 'application/json'
                ],
                'verify' => true
            ])
            ->andThrow(
                new ServerException(
                    'Foo bar',
                    new \GuzzleHttp\Psr7\Request('GET', 'v2/invoices/abc-123'),
                    new \GuzzleHttp\Psr7\Response(500)
                )
            );
        $this->logRepository = \Mockery::mock(RequestLogRepository::class);
        $this->logRepository
            ->shouldReceive('saveOrFail')
            ->with([
                'service' => 'invoice',
                'status_code' => 500,
                'request_uri' => 'v2/invoices/abc-123',
            ]);

        $this->setExpectedException(HttpClientResponseException::class);

        $response = $this->createClient()->getV2('abc-123');
    }

    public function test_should_log_failed_get_invoice_v2_request_generic_exception()
    {
        $this->httpClient = \Mockery::mock(\GuzzleHttp\ClientInterface::class);
        $this->httpClient
            ->shouldReceive('request')
            ->with('GET', 'v2/invoices/abc-123', [
                'http_errors' => true,
                'headers' => [
                    'X-Honeycomb-Trace' => '1;trace_id=********************************,parent_id=7248651b52e67446',
                    'X-Glofox-Source' => 'DASHBOARD',
                    'x-glofox-branch-id' => 'fake-branch-id',
                    'x-request-id' => 'fake-request-id',
                    'Content-Type' => 'application/json'
                ],
                'verify' => true
            ])
            ->andThrow(
                new Exception(
                    'Foo bar',
                )
            );
        $this->logRepository = \Mockery::mock(RequestLogRepository::class);
        $this->logRepository
            ->shouldReceive('saveOrFail')
            ->with([
                'service' => 'invoice',
                'status_code' => 0,
                'request_uri' => 'v2/invoices/abc-123',
            ]);

        $this->setExpectedException(HttpClientResponseException::class);

        $response = $this->createClient()->getV2('abc-123');
    }

    private function createClient()
    {
        return new InvoiceClient($this->httpClient, $this->logRepository, $this->logger, $this->tracingContext);
    }
}
