<?php

declare(strict_types=1);

use Carbon\Carbon;
use Glofox\Domain\Branches\Models\Branch as BranchModel;
use Glofox\Domain\Facilities\Repositories\FacilitiesRepository;
use Glofox\Domain\TimeSlotPatterns\Models\TimeSlotPattern as TimeSlotPatternModel;
use Glofox\Domain\TimeSlotPatterns\Repositories\TimeSlotPatternsRepository;
use Glofox\Domain\TimeSlots\Repositories\TimeSlotRepository;
use Glofox\Domain\Users\Repositories\UsersRepository;
use Glofox\Infrastructure\Honeycomb\HoneycombTracker;
use Psr\Log\LoggerInterface;

App::uses('Component', 'Controller');

class TimeSlotGeneratorComponent extends Component
{
    public ?Controller $controller = null;
    private Branch $Branch;
    private TimeSlot $TimeSlot;

    private TimeSlotPatternsRepository $timeSlotPatternsRepo;
    private TimeSlotRepository $timeSlotRepo;
    private FacilitiesRepository $facilitiesRepo;
    private UsersRepository $usersRepo;
    private LoggerInterface $logger;
    private HoneycombTracker $honeycombTracker;

    public function initialize(Controller $controller)
    {
        $this->loadModels();
    }

    public function loadModels(): void
    {
        $this->Branch = ClassRegistry::init('Branch');
        $this->TimeSlot = ClassRegistry::init('TimeSlot');

        $this->timeSlotPatternsRepo = app()->make(TimeSlotPatternsRepository::class);
        $this->timeSlotRepo = app()->make(TimeSlotRepository::class);
        $this->facilitiesRepo = app()->make(FacilitiesRepository::class);
        $this->usersRepo = app()->make(UsersRepository::class);
        $this->logger = app()->make(LoggerInterface::class);
        $this->honeycombTracker = app()->make(HoneycombTracker::class);
    }

    public function generateByBranch(BranchModel $branch): void
    {
        $weeksToDisplay = $this->Branch->findWeeksToDisplay($branch->toLegacy());
        $timeSlotPatterns = $this->timeSlotPatternsRepo->getAllActiveByBranchId($branch->id());

        foreach ($timeSlotPatterns as $timeSlotPattern) {
            $result = $this->generateByTimeSlotPattern(
                $branch,
                TimeSlotPatternModel::make($timeSlotPattern->toArray()),
                $weeksToDisplay
            );
            if (!$result['success']) {
                $this->logger->info($result['message']);
            }
        }
    }

    private function generateByTimeSlotPattern(
        BranchModel $branch,
        TimeSlotPatternModel $timeSlotPattern,
        array $weeksToDisplay
    ): array {
        if (empty($timeSlotPattern->model()) || empty($timeSlotPattern->modelId())) {
            return [
                'success' => false,
                'message' => 'There are no Time Slots to create. Invalid model or model_id',
            ];
        }

        if (!$timeSlotPattern->isFacility() && !$timeSlotPattern->isUser()) {
            return [
                'success' => false,
                'message' => 'There are no Time Slots to create. Invalid model',
            ];
        }

        $weeksToDisplay = $weeksToDisplay[$timeSlotPattern->model()];

        $lastTimeSlot = $this->timeSlotRepo->getLatestByPatternId($timeSlotPattern->id());
        $lastTimeSlotDate = Carbon::today($branch->timezone());
        $displayEndDate = $lastTimeSlotDate->copy()->addWeeks($weeksToDisplay);

        if ($lastTimeSlot->isNotEmpty()) {
            $lastTimeSlotDate = $lastTimeSlot->date($branch->timezone())->addDay();
        }

        if ($lastTimeSlotDate->greaterThanOrEqualTo($displayEndDate)) {
            return [
                'success' => false,
                'message' => 'There are no Time Slots to create. Display end date is less than last time slot date',
            ];
        }

        if ($timeSlotPattern->isFacility()) {
            try {
                $entity = $this->facilitiesRepo->findActiveBookableFacilityById($timeSlotPattern->modelId());
            } catch (\Glofox\Exception $e) {
                return [
                    'success' => false,
                    'message' => sprintf(
                        'There are no Time Slots to create. Facility not found or not bookable - %s',
                        $timeSlotPattern->modelId()
                    ),
                ];
            }
        } else {
            try {
                $entity = $this->usersRepo->findActiveBookableUserById($timeSlotPattern->modelId());
            } catch (\Glofox\Exception $e) {
                return [
                    'success' => false,
                    'message' => sprintf(
                        'There are no Time Slots to create. User not found or not bookable - %s',
                        $timeSlotPattern->modelId()
                    ),
                ];
            }
        }

        try {
            $this->TimeSlot->generateFrom(
                $timeSlotPattern->toArray(),
                $entity->toLegacy(),
                $branch->closingTimes(),
                $lastTimeSlotDate,
                $displayEndDate
            );
        } catch (Throwable $e) {
            $this->trackException($e, $branch);

            return [
                'success' => false,
                'message' => sprintf(
                    'There are no Time Slots to create. An error occurred while generating time slots: %s',
                    $e->getMessage()
                ),
            ];
        }

        return ['success' => true];
    }

    private function trackException(Throwable $e, BranchModel $branch): void
    {
        $this->honeycombTracker->track([
            'name' => 'time-slot-generator-component',
            'exceptionMessage' => $e->getMessage(),
            'exception' => $e,
            'branchId' => $branch->id(),
            'branchName' => $branch->name(),
        ]);
    }
}
