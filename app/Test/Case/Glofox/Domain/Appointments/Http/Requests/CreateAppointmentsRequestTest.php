<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\Appointments\Http\Requests;

use App;
use CakeTestCases\Glofox\Domain\Users\Traits\AuthenticateUsersTrait;
use JsonException;

App::import('Test/Case', 'GlofoxControllerTestCase');

class CreateAppointmentsRequestTest extends \GlofoxControllerTestCase
{
    use AuthenticateUsersTrait;

    public $fixtures = [
        'app.branch',
        'app.user',
    ];

    private const BRANCH_ID = '49a7011a05c677b9a916612a';

    public function tearDown()
    {
        parent::tearDown();
        \Mockery::close();
    }

    /**
     * @dataProvider createAppointmentPayloadDataProvider
     * @param array $payload
     * @param array $expectedResponse
     * @return void
     * @throws JsonException
     */
    public function testRules(array $payload, array $expectedResponse): void
    {
        $this->authenticateAsAdmin();

        $url = sprintf('/2.1/branches/%s/appointments', self::BRANCH_ID);
        $result = json_decode(
            $this->testAction($url, ['method' => 'POST', 'data' => $payload]),
            true,
            512,
            JSON_THROW_ON_ERROR
        );

        $this->assertEquals($expectedResponse['success'], isset($result['_id']));
        if ($expectedResponse['errors']) {
            foreach ($expectedResponse['errors'] as $expectedError) {
                $this->assertTrue(in_array($expectedError, $result['errors']));
            }
        }
    }

    public function createAppointmentPayloadDataProvider(): array
    {
        return [
            'When size provided and is less than one.' => [
                'payload' => [
                    'size' => 0,
                ],
                'expectedResponse' => [
                    'success' => false,
                    'errors' => [
                        'APPOINTMENT_SIZE_LESS_THAN_ONE'
                    ],
                ],
            ],
            'When size provided and is more than ten.' => [
                'payload' => [
                    'size' => 11,
                ],
                'expectedResponse' => [
                    'success' => false,
                    'errors' => [
                        'APPOINTMENT_SIZE_MORE_THAN_TEN'
                    ],
                ],
            ],
            'When size provided and is non-numeric.' => [
                'payload' => [
                    'size' => 'abc',
                ],
                'expectedResponse' => [
                    'success' => false,
                    'errors' => [
                        'APPOINTMENT_SIZE_MUST_BE_NUMERIC'
                    ],
                ],
            ],
        ];
    }
}
