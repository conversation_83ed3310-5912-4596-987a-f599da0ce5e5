<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\Memberships\Services\EditPause;

use App;
use Glofox\Domain\Memberships\Services\EditPause\EditPauseService;
use Glofox\Domain\Memberships\Services\EditPause\PutPauseParams;
use Glofox\Domain\Memberships\Services\EditPause\GetPauseParams;
use Glofox\Domain\Memberships\Services\MembershipsClient;
use Glofox\Domain\Memberships\Services\Request\PutPauseRequest;
use Glofox\Domain\Memberships\Services\Request\GetPauseRequest;
use Glofox\Infrastructure\Exception\HttpClientResponseException;
use GlofoxTestCase;
use GuzzleHttp\Psr7\Response;
use Illuminate\Http\JsonResponse;
use Mockery;
use Mockery\MockInterface;

App::import('Test/Case', 'GlofoxTestCase');

class EditPauseServiceTest extends GlofoxTestCase
{
    public $fixtures = [];
    private MockInterface $membershipsClient;

    public function setUp(): void
    {
        parent::setUp();
        $this->membershipsClient = \Mockery::mock(MembershipsClient::class);
    }

    public function tearDown(): void
    {
        parent::tearDown();
        Mockery::close();
    }

    public function test_it_calls_getPauseStatus_in_memberships_client(): void
    {
        $membershipId = 'membership-123';

        $this->membershipsClient
            ->shouldReceive('getPause')
            ->withArgs(
                function (GetPauseRequest $request) use ($membershipId) {
                    $this->assertSame($membershipId, $request->getUserMembershipId());

                    return true;
                }
            )
            ->andReturn([
                'foo' => 'bar',
            ]);

        $this->membershipsClient
            ->shouldReceive('getLastResponse')
            ->andReturn(new Response(200));

        $service = $this->createService();
        $jsonResponse = $service->get(
            new GetPauseParams($membershipId)
        );

        $this->assertInstanceOf(JsonResponse::class, $jsonResponse);
        $this->assertSame(200, $jsonResponse->getStatusCode());
        $this->assertSame('bar', $jsonResponse->getData()->foo);
    }

    public function test_it_handles_exceptions_in_getPauseStatus_using_key_error(): void
    {
        $membershipId = 'membership-234';

        $this->membershipsClient
            ->shouldReceive('getPause')
            ->andThrow(\UnsuccessfulOperation::class);

        $this->membershipsClient
            ->shouldReceive('getLastResponse')
            ->andReturn(
                new Response(403, [], '{"key":"FOO"}')
            );

        $this->setExpectedException(HttpClientResponseException::class, 'FOO', 403);

        $this->createService()
            ->get(
                new GetPauseParams($membershipId)
            );
    }

    public function test_it_handles_exceptions_in_getPauseStatus_without_key_error(): void
    {
        $membershipId = 'membership-234';

        $this->membershipsClient
            ->shouldReceive('getPause')
            ->andThrow(\UnsuccessfulOperation::class);

        $this->membershipsClient
            ->shouldReceive('getLastResponse')
            ->andReturn(
                new Response(403, [], '{"foo":"BAR"}')
            );

        $this->setExpectedException(\UnsuccessfulOperation::class, 'No error code found from MS response', 200);

        $this->createService()
            ->get(
                new GetPauseParams($membershipId)
            );
    }

    public function test_it_calls_putPause_in_memberships_client(): void
    {
        $membershipId = 'membership-123';
        $pauseStartDate = '2020-05-06T16:15:31+01:00';
        $durationAmount = 5;
        $durationUnit = 'DAY';

        $this->membershipsClient
            ->shouldReceive('putPause')
            ->withArgs(
                function (PutPauseRequest $request) use (
                    $durationUnit,
                    $durationAmount,
                    $pauseStartDate,
                    $membershipId
                ) {
                    $this->assertSame($membershipId, $request->getUserMembershipId());
                    $this->assertSame($pauseStartDate, $request->getPauseStartDate());
                    $this->assertSame($durationAmount, $request->getDurationAmount());
                    $this->assertSame($durationUnit, $request->getDurationUnit());

                    return true;
                }
            )
            ->andReturn([
                'foo' => 'bar',
            ]);

        $this->membershipsClient
            ->shouldReceive('getLastResponse')
            ->andReturn(new Response(200));

        $jsonResponse = $this
            ->createService()
            ->put(
                new PutPauseParams($membershipId, $pauseStartDate, $durationAmount, $durationUnit)
            );

        $this->assertInstanceOf(JsonResponse::class, $jsonResponse);
        $this->assertSame(200, $jsonResponse->getStatusCode());
        $this->assertSame('bar', $jsonResponse->getData()->foo);
    }

    public function test_it_handles_exceptions_in_putPause(): void
    {
        $membershipId = 'membership-234';
        $pauseStartDate = '2020-05-06T16:15:31+01:00';
        $durationAmount = 5;
        $durationUnit = 'DAY';

        $this->membershipsClient
            ->shouldReceive('putPause')
            ->andThrow(\UnsuccessfulOperation::class);

        $this->membershipsClient
            ->shouldReceive('getLastResponse')
            ->andReturn(
                new Response(403, [], '{"key":"FOO"}')
            );

        $this->setExpectedException(HttpClientResponseException::class, 'FOO', 403);

        $this
            ->createService()
            ->put(
                new PutPauseParams($membershipId, $pauseStartDate, $durationAmount, $durationUnit)
            );
    }

    public function test_it_calls_deletePause_in_memberships_client(): void
    {
        $membershipId = 'membership-123';

        $this->membershipsClient
            ->shouldReceive('deletePause')
            ->withArgs(
                function (GetPauseRequest $request) use ($membershipId) {
                    $this->assertSame($membershipId, $request->getUserMembershipId());

                    return true;
                }
            )
            ->andReturn([]);

        $this->membershipsClient
            ->shouldReceive('getLastResponse')
            ->andReturn(new Response(204));

        $jsonResponse = $this
            ->createService()
            ->delete(
                new GetPauseParams($membershipId)
            );

        $this->assertInstanceOf(JsonResponse::class, $jsonResponse);
        $this->assertSame(204, $jsonResponse->getStatusCode());
        $this->assertSame('{}', $jsonResponse->getContent());
    }

    public function test_it_handles_exceptions_in_deletePause(): void
    {
        $membershipId = 'membership-234';

        $this->membershipsClient
            ->shouldReceive('deletePause')
            ->andThrow(\UnsuccessfulOperation::class);

        $this->membershipsClient
            ->shouldReceive('getLastResponse')
            ->andReturn(
                new Response(403, [], '{"key":"FOO"}')
            );

        $this->setExpectedException(HttpClientResponseException::class, 'FOO', 403);

        $this
            ->createService()
            ->delete(
                new GetPauseParams($membershipId)
            );
    }

    public function test_it_calls_postPause_in_memberships_client(): void
    {
        $membershipId = 'membership-123';
        $pauseStartDate = '2020-05-06T16:15:31+01:00';
        $durationAmount = 5;
        $durationUnit = 'DAY';

        $this->membershipsClient
            ->shouldReceive('postPause')
            ->withArgs(
                function (PutPauseRequest $request) use (
                    $durationUnit,
                    $durationAmount,
                    $pauseStartDate,
                    $membershipId
                ) {
                    $this->assertSame($membershipId, $request->getUserMembershipId());
                    $this->assertSame($pauseStartDate, $request->getPauseStartDate());
                    $this->assertSame($durationAmount, $request->getDurationAmount());
                    $this->assertSame($durationUnit, $request->getDurationUnit());

                    return true;
                }
            )
            ->andReturn([
                'foo' => 'bar',
            ]);

        $this->membershipsClient
            ->shouldReceive('getLastResponse')
            ->andReturn(new Response(201));

        $jsonResponse = $this
            ->createService()
            ->post(
                new PutPauseParams($membershipId, $pauseStartDate, $durationAmount, $durationUnit)
            );

        $this->assertInstanceOf(JsonResponse::class, $jsonResponse);
        $this->assertSame(201, $jsonResponse->getStatusCode());
        $this->assertSame('bar', $jsonResponse->getData()->foo);
    }

    public function test_it_handles_exceptions_in_postPause(): void
    {
        $membershipId = 'membership-234';
        $pauseStartDate = '2020-05-06T16:15:31+01:00';
        $durationAmount = 5;
        $durationUnit = 'DAY';

        $this->membershipsClient
            ->shouldReceive('postPause')
            ->andThrow(\UnsuccessfulOperation::class);

        $this->membershipsClient
            ->shouldReceive('getLastResponse')
            ->andReturn(
                new Response(403, [], '{"key":"FOO"}')
            );

        $this->setExpectedException(HttpClientResponseException::class, 'FOO', 403);

        $this
            ->createService()
            ->post(
                new PutPauseParams($membershipId, $pauseStartDate, $durationAmount, $durationUnit)
            );
    }

    private function createService(): EditPauseService
    {
        return new EditPauseService(
            $this->membershipsClient
        );
    }
}
