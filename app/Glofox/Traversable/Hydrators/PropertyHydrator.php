<?php

namespace Glofox\Traversable\Hydrators;

use Glofox\Traversable\Hydrators\Contracts\HydratorContract;

class PropertyHydrator extends Hydrator implements HydratorContract
{
    public function fill(array $items)
    {
        $instance = $this->instance ?? app()->make($this->expected);

        foreach ($items as $property => $value) {
            if (property_exists($instance, $property)) {
                $instance->{$property} = $value;
            }
        }

        return $instance;
    }
}