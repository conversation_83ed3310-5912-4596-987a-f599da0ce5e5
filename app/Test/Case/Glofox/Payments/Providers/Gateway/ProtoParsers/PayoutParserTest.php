<?php

namespace CakeTestCases\Glofox\Payments\Providers\Gateway\ProtoParsers;

use Carbon\Carbon;
use Glofox\Domain\Charges\Models\Charge;
use Glofox\Domain\Charges\Repositories\ChargesRepository;
use Glofox\Domain\Charges\Search\Expressions\TransactionProviderIdIn;
use Glofox\Domain\Users\Models\User;
use Glofox\Domain\Users\Repositories\UsersRepository;
use Glofox\Payments\Entities\Payout\Models\Payout as ProviderPayout;
use Glofox\Payments\Entities\Transaction\Models\Transaction;
use Glofox\Payments\Providers\Gateway\ProtoParsers\PayoutParser;

use Glofox\Payments\Entities\Payout\Models\PayoutTransaction;
use Glofox\Repositories\Search\Expressions\Shared\InIds;

\App::import('Test/Case', 'GlofoxTestCase');

class PayoutParserTest extends \GlofoxTestCase
{
    public function testParseFromProtoMessageToCSV()
    {
        $payoutTransactions = [];
        $returnedCharges = [];
        $returnedUsers = [];

        $date = Carbon::parse('2019-01-03 00:00:00')->getTimestamp();

        for ($x = 0; $x < 10; $x++) {
            $transaction = new Transaction();
            $transaction->setId('transaction-' . strval($x));
            $transaction->setServiceProviderID('service_provider_id');
            $transaction->setCurrency('EUR');
            $transaction->setType('Type');
            $transaction->setAmount(4000 + $x);
            $transaction->setFee(5000 + $x);
            $transaction->setNetAmount(6000 + $x);
            $transaction->setCreatedAt($date);

            $payoutTransaction = new PayoutTransaction();
            $payoutTransaction->setID('payout-transaction-' . strval($x));
            $payoutTransaction->setGrossAmount(10 + $x);
            $payoutTransaction->setFee(20 + $x);
            $payoutTransaction->setNetAmount(30 + $x);
            $payoutTransaction->setTransaction($transaction);
            $payoutTransactions[] = $payoutTransaction;

            $returnedCharges[] = Charge::make([
                'transaction_provider_id' => 'transaction-' . strval($x),
                'metadata' => [
                    'user_id' => '5f5f313787faaa266713061' . strval($x),
                    'glofox_event' => ($x % 2 == 0) ? 'refund' : 'payment'
                ],
                'created' => Carbon::now(),
            ]);

            $returnedUsers[] = User::make([
                '_id' => '5f5f313787faaa266713061' . strval($x),
                'first_name' => 'First',
                'last_name' => 'Last',
                'email' => 'Email'
            ]);
        }

        $chargesRepository = \Mockery::mock(ChargesRepository::class);
        $chargesRepository
            ->shouldReceive('addCriteria')
            ->withArgs(function (TransactionProviderIdIn $criteria) {
                self::assertEquals('in("transaction-0", "transaction-1", "transaction-2", "transaction-3", "transaction-4", "transaction-5", "transaction-6", "transaction-7", "transaction-8", "transaction-9")', $criteria->getExpression()->toString());
                return true;
            })
            ->andReturnSelf()
            ->shouldReceive('fields')->andReturnSelf()
            ->shouldReceive('find')
            ->andReturn($returnedCharges);
        app()->instance(ChargesRepository::class, $chargesRepository);

        $userRepository = \Mockery::mock(UsersRepository::class);
        $userRepository
            ->shouldReceive('addCriteria')
            ->withArgs(fn(InIds $criteria) => true)
            ->andReturnSelf()
            ->shouldReceive('fields')->andReturnSelf()
            ->shouldReceive('findOrFail')
            ->andReturn($returnedUsers);
        app()->instance(UsersRepository::class, $userRepository);

        $parser = new PayoutParser();
        $providerPayout = new ProviderPayout();

        $providerPayout->setArrivalDate($date);

        $providerPayout->setPayoutTransactions($payoutTransactions);

        $parsed = $parser->parseFromProtoMessageToCSV($providerPayout);

        self::assertTrue(0 === strpos($parsed,'Transaction ID,Customer Name,Customer Email,Currency,Type,Gross Amount,Fee,Net Amount,Payout Date,Date,Time,Glofox Event'));
        self::assertTrue(false !== strpos($parsed,'10,20,30'), $parsed);
        self::assertTrue(false !== strpos($parsed,'11,21,31'), $parsed);
        self::assertTrue(false !== strpos($parsed,'12,22,32'), $parsed);
        self::assertTrue(false !== strpos($parsed,'13,23,33'), $parsed);

        $providerPayout = new ProviderPayout();
        $parsed = $parser->parseFromProtoMessageToCSV($providerPayout);
        self::assertTrue(0 === strpos($parsed,'Transaction ID,Customer Name,Customer Email,Currency,Type,Gross Amount,Fee,Net Amount,Payout Date,Date,Time,Glofox Event'));
        self::assertFalse(false !== strpos($parsed,'10,20,30'), $parsed);
        self::assertFalse(false !== strpos($parsed,'11,21,31'), $parsed);

        $providerPayout = new ProviderPayout();

        $providerPayout->setPayoutTransactions([]);

        $parsed = $parser->parseFromProtoMessageToCSV($providerPayout);
        self::assertTrue(0 === strpos($parsed,'Transaction ID,Customer Name,Customer Email,Currency,Type,Gross Amount,Fee,Net Amount,Payout Date,Date,Time,Glofox Event'));
        self::assertFalse(false !== strpos($parsed,'10,20,30'), $parsed);
        self::assertFalse(false !== strpos($parsed,'11,21,31'), $parsed);

        app()->forgetInstance(ChargesRepository::class);
        app()->forgetInstance(UsersRepository::class);
    }

    public function testParseFromProtoMessageToCSVNonFractionalCurrency()
    {
        $payoutTransactions = [];
        $returnedCharges = [];
        $returnedUsers = [];

        $date = Carbon::parse('2019-01-03 00:00:00')->getTimestamp();

        for ($x = 0; $x < 10; $x++) {
            $transaction = new Transaction();
            $transaction->setId('transaction-' . strval($x));
            $transaction->setServiceProviderID('service_provider_id');
            $transaction->setCurrency('JPY');
            $transaction->setType('Type');
            $transaction->setAmount(4000 + $x);
            $transaction->setFee(5000 + $x);
            $transaction->setNetAmount(6000 + $x);
            $transaction->setCreatedAt($date);

            $payoutTransaction = new PayoutTransaction();
            $payoutTransaction->setID('payout-transaction-' . strval($x));
            $payoutTransaction->setGrossAmount(1000 + $x);
            $payoutTransaction->setFee(2000 + $x);
            $payoutTransaction->setNetAmount(3000 + $x);
            $payoutTransaction->setTransaction($transaction);
            $payoutTransactions[] = $payoutTransaction;

            $returnedCharges[] = Charge::make([
                'transaction_provider_id' => 'transaction-' . strval($x),
                'metadata' => [
                    'user_id' => '5f5f313787faaa266713061' . strval($x),
                    'glofox_event' => ($x % 2 == 0) ? 'refund' : 'payment'
                ],
                'created' => Carbon::now(),
            ]);

            $returnedUsers[] = User::make([
                '_id' => '5f5f313787faaa266713061' . strval($x),
                'first_name' => 'First',
                'last_name' => 'Last',
                'email' => 'Email'
            ]);
        }

        $chargesRepository = \Mockery::mock(ChargesRepository::class);
        $chargesRepository
            ->shouldReceive('addCriteria')
            ->withArgs(function (TransactionProviderIdIn $criteria) {
                self::assertEquals('in("transaction-0", "transaction-1", "transaction-2", "transaction-3", "transaction-4", "transaction-5", "transaction-6", "transaction-7", "transaction-8", "transaction-9")', $criteria->getExpression()->toString());
                return true;
            })
            ->andReturnSelf()
            ->shouldReceive('fields')->andReturnSelf()
            ->shouldReceive('find')
            ->andReturn($returnedCharges);
        app()->instance(ChargesRepository::class, $chargesRepository);

        $userRepository = \Mockery::mock(UsersRepository::class);
        $userRepository
            ->shouldReceive('addCriteria')
            ->withArgs(fn(InIds $criteria) => true)
            ->andReturnSelf()
            ->shouldReceive('fields')->andReturnSelf()
            ->shouldReceive('findOrFail')
            ->andReturn($returnedUsers);
        app()->instance(UsersRepository::class, $userRepository);

        $parser = new PayoutParser();
        $providerPayout = new ProviderPayout();

        $providerPayout->setArrivalDate($date);

        $providerPayout->setPayoutTransactions($payoutTransactions);

        $parsed = $parser->parseFromProtoMessageToCSV($providerPayout);

        self::assertTrue(0 === strpos($parsed,'Transaction ID,Customer Name,Customer Email,Currency,Type,Gross Amount,Fee,Net Amount,Payout Date,Date,Time,Glofox Event'));
        self::assertTrue(false !== strpos($parsed,'1000,2000,3000'), $parsed);
        self::assertTrue(false !== strpos($parsed,'1001,2001,3001'), $parsed);
        self::assertTrue(false !== strpos($parsed,'1002,2002,3002'), $parsed);
        self::assertTrue(false !== strpos($parsed,'1003,2003,3003'), $parsed);

        $providerPayout = new ProviderPayout();
        $parsed = $parser->parseFromProtoMessageToCSV($providerPayout);
        self::assertTrue(0 === strpos($parsed,'Transaction ID,Customer Name,Customer Email,Currency,Type,Gross Amount,Fee,Net Amount,Payout Date,Date,Time,Glofox Event'));
        self::assertFalse(false !== strpos($parsed,'1000,2000,3000'), $parsed);
        self::assertFalse(false !== strpos($parsed,'1001,2001,3001'), $parsed);

        $providerPayout = new ProviderPayout();

        $providerPayout->setPayoutTransactions([]);

        $parsed = $parser->parseFromProtoMessageToCSV($providerPayout);
        self::assertTrue(0 === strpos($parsed,'Transaction ID,Customer Name,Customer Email,Currency,Type,Gross Amount,Fee,Net Amount,Payout Date,Date,Time,Glofox Event'));
        self::assertFalse(false !== strpos($parsed,'1000,2000,3000'), $parsed);
        self::assertFalse(false !== strpos($parsed,'1001,2001,3001'), $parsed);

        app()->forgetInstance(ChargesRepository::class);
        app()->forgetInstance(UsersRepository::class);
    }
}
