get:
  tags:
    - UserStorage
  summary: ''
  operationId: list-user-storage
  description: This endpoint lists storage keys for a given branch and user
  x-internal: true
  security:
    - coreapi_auth: [ ]
  parameters:
    - schema:
        type: string
      name: branchId
      in: path
      required: true
    - schema:
        type: string
      name: userId
      in: path
      required: true
  responses:
    '200':
      description: OK
      content:
        application/json:
          schema:
            type: object
            properties:
              data:
                type: array
                items:
                  $ref: "../../schemas/user_storage.yml"
    '400':
      description: Bad request
      content:
        application/json:
          schema:
            $ref : "../../schemas/error.yml"
post:
  tags:
    - UserStorage
  summary: ''
  operationId: upsert-user-storage
  description: This endpoint upsert new and existing keys with the user storage
  x-internal: true
  security:
    - coreapi_auth: [ ]
  parameters:
    - schema:
        type: string
      name: branchId
      in: path
      required: true
    - schema:
        type: string
      name: userId
      in: path
      required: true
  requestBody:
    required: true
    content:
      application/json:
        schema:
          type: array
          items:
            properties:
              key:
                type: string
              value:
                type: string
              type:
                type: string
                enum:
                  - string
            required:
              - key
              - value
              - type
  responses:
    '200':
      description: OK
      content:
        application/json:
          schema:
            type: object
            properties:
              data:
                type: array
                items:
                  $ref: "../../schemas/user_storage.yml"
    '400':
      description: Bad request
      content:
        application/json:
          schema:
            $ref: "../../schemas/error.yml"