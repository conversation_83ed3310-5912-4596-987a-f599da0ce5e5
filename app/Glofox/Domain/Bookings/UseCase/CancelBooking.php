<?php

declare(strict_types=1);

namespace Glofox\Domain\Bookings\UseCase;

use Carbon\Carbon;
use Glofox\CdnProvider;
use Glofox\Datasource\Exceptions\InvalidMongoIdException;
use Glofox\Domain\AppointmentSlots\CreatedBy;
use Glofox\Domain\AsyncEvents\Events\BookingCancelledEventMetaV2;
use Glofox\Domain\AsyncEvents\Events\BookingCancelledEventPayloadV2;
use Glofox\Domain\AsyncEvents\Events\CourseBookingCancelledEventMeta;
use Glofox\Domain\AsyncEvents\Events\CourseBookingCancelledEventPayload;
use Glofox\Domain\AsyncEvents\Events\UserRequestedBookingCancellingEventMeta;
use Glofox\Domain\AsyncEvents\Events\UserRequestedBookingCancellingEventPayload;
use Glofox\Domain\Authentication\Auth;
use Glofox\Domain\BookingRequests\Services\BookingRequestsPublisher;
use Glofox\Domain\Bookings\Events\BookingWasCancelled;
use Glofox\Domain\Bookings\Exceptions\BookingNotFoundException;
use Glofox\Domain\Bookings\Models\Booking;
use Glofox\Domain\Bookings\Repositories\BookingsRepository;
use Glofox\Domain\Bookings\Services\BookingsPublisher;
use Glofox\Domain\Bookings\Status;
use Glofox\Domain\Bookings\Type;
use Glofox\Domain\Branches\Exceptions\BranchNotFoundException;
use Glofox\Domain\Branches\Exceptions\InvalidBranchIdException;
use Glofox\Domain\Branches\Models\Branch;
use Glofox\Domain\Branches\Repositories\BranchesRepository;
use Glofox\Domain\Courses\Exceptions\CourseNotFoundException;
use Glofox\Domain\Courses\Models\Course;
use Glofox\Domain\Courses\Repositories\CoursesRepository;
use Glofox\Domain\Events\Exceptions\EventNotFoundException;
use Glofox\Domain\Events\Models\Event;
use Glofox\Domain\Events\Repositories\EventsRepository;
use Glofox\Domain\Events\Services\EventUpdatedEventSender;
use Glofox\Domain\Memberships\Services\AddonServiceInterface;
use Glofox\Domain\Notifications\Action;
use Glofox\Domain\Programs\Exceptions\ProgramNotFoundException;
use Glofox\Domain\Programs\Models\Program;
use Glofox\Domain\Programs\Repositories\ProgramsRepository;
use Glofox\Domain\Services\Type as CreditServiceType;
use Glofox\Domain\TimeSlots\Exceptions\TimeSlotNotFoundException;
use Glofox\Domain\TimeSlots\ModelList;
use Glofox\Domain\TimeSlots\Repositories\TimeSlotRepository;
use Glofox\Domain\Users\Exceptions\UserNotFoundException;
use Glofox\Domain\Users\Exceptions\UserOriginBranchIdException;
use Glofox\Domain\Users\Models\User;
use Glofox\Domain\Users\Repositories\UsersRepository;
use Glofox\Events\EventManager;
use Glofox\Exception;
use Glofox\Infrastructure\Flags\Flag;
use Illuminate\Support\Collection;
use Psr\Log\LoggerInterface;

class CancelBooking
{
    private const EMAIL_CONFIG = 'email_for_booking';
    private const TRAINER_EMAIL_CONFIG = 'sendgrid';
    private const EMAIL_FORMAT = 'html';
    private const EMAIL_SUBJECT = 'Booking Cancelled - %s';
    private const TRAINER_EMAIL_SUBJECT = 'Booking Cancelled';
    private const COURSE_EMAIL_TEMPLATE = 'course_booking';
    private const EVENT_EMAIL_TEMPLATE = 'class_booking';
    private const TRAINER_EMAIL_TEMPLATE = 'cancelled_trainer_booking';

    private UsersRepository $usersRepo;
    private BookingsRepository $bookingsRepo;
    private BranchesRepository $branchesRepo;
    private CoursesRepository $coursesRepo;
    private TimeSlotRepository $timeSlotsRepo;
    private ProgramsRepository $programsRepo;
    private EventsRepository $eventsRepo;

    private \BookingCancellation $bookingCancellation;
    private \StripeCharge $stripeChargeCakeModel;
    private \Membership $membershipCakeModel;
    private \UserCredit $userCreditCakeModel;
    private \CakeEmail $emailProvider;

    private BookingRequestsPublisher $bookingRequestsPublisher;
    private BookingsPublisher $bookingPublisher;
    private EventUpdatedEventSender $eventUpdatedEventSender;
    private AddonServiceInterface $addonService;
    private EventManager $eventManager;
    private LoggerInterface $logger;

    private ?Course $cachedCourse = null;

    public function __construct(
        UsersRepository $usersRepo,
        BookingsRepository $bookingsRepo,
        BranchesRepository $branchesRepo,
        CoursesRepository $coursesRepo,
        TimeSlotRepository $timeSlotsRepo,
        ProgramsRepository $programsRepo,
        EventsRepository $eventsRepo,
        \BookingCancellation $bookingCancellation,
        \StripeCharge $stripeChargeCakeModel,
        \Membership $membershipCakeModel,
        \UserCredit $userCreditCakeModel,
        \CakeEmail $emailProvider,
        BookingRequestsPublisher $bookingRequestsPublisher,
        BookingsPublisher $bookingPublisher,
        EventUpdatedEventSender $eventUpdatedEventSender,
        AddonServiceInterface $addonService,
        EventManager $eventManager,
        LoggerInterface $logger
    ) {
        $this->usersRepo = $usersRepo;
        $this->bookingsRepo = $bookingsRepo;
        $this->branchesRepo = $branchesRepo;
        $this->coursesRepo = $coursesRepo;
        $this->timeSlotsRepo = $timeSlotsRepo;
        $this->programsRepo = $programsRepo;
        $this->eventsRepo = $eventsRepo;

        $this->bookingCancellation = $bookingCancellation;
        $this->stripeChargeCakeModel = $stripeChargeCakeModel;
        $this->membershipCakeModel = $membershipCakeModel;
        $this->userCreditCakeModel = $userCreditCakeModel;
        $this->emailProvider = $emailProvider;

        $this->bookingRequestsPublisher = $bookingRequestsPublisher;
        $this->bookingPublisher = $bookingPublisher;
        $this->eventUpdatedEventSender = $eventUpdatedEventSender;
        $this->addonService = $addonService;
        $this->eventManager = $eventManager;
        $this->logger = $logger;
    }

    public function execute(CancelBookingParams $params): array
    {
        try {
            $requestedByUser = $params->requestedByUser();
            $member = $this->resolveMember($requestedByUser, $params->userId());
            $booking = $this->resolveBooking($params->bookingId());

            $isCancellingSelfBooking = $requestedByUser->id() === $booking->userId() &&
                $requestedByUser->id() === $member->id();
            if (!$isCancellingSelfBooking && !$requestedByUser->isStaff()) {
                return [
                    'success' => false,
                    'message' => 'You do not have permission to cancel this booking',
                    'message_code' => 'YOU_DO_NOT_HAVE_PERMISSION_TO_CANCEL_THIS_BOOKING',
                ];
            }

            if ($booking->isCanceled()) {
                return [
                    'success' => false,
                    'message' => 'Booking is already canceled',
                    'message_code' => 'BOOKING_ALREADY_CANCELED',
                ];
            }

            $previousBookingStatus = $booking->status();

            $branch = $this->resolveBranch($booking->branchId());

            if ($booking->isWaiting()) {
                $this->bookingsRepo->delete($booking->id());

                return $this->publishBookingCancellation($booking, $member, $branch, $previousBookingStatus);
            }

            $timeStart = $this->resolveTimeStart($booking);
            $timezone = $branch->timezone();

            $this->validateIsNotInThePast($requestedByUser, $timezone, $timeStart);

            [$isLateCancellation, $cancellationWindow] = $this->isLateCancellationAndGetCancellationWindow(
                $requestedByUser,
                $booking,
                $branch,
                $timeStart
            );
            if ($booking->isBooked() || $booking->isWaiting()) {
                $now = Carbon::now($timezone);
                $booking->offsetSet('is_late_cancellation', $isLateCancellation);
                $booking->offsetSet('canceled_at', new \MongoDate(strtotime($now->toDateTimeString())));
            }

            $booking->markAsCancelled();
            $booking->offsetSet('batch_id', null);

            $booking = $this->setFailureReason($booking, $params->failureReason());

            if ($booking->type()->is(Type::COURSES())) {
                $this->sendBookingCourseEmail($branch, $member, $booking);
            }

            $this->bookingCancellation->logCancellation($booking->toLegacy(), $requestedByUser->toArray());

            $this->sendPushNotification($previousBookingStatus, $booking, $timeStart);

            try {
                $cancelBooking = $this->bookingsRepo->save($booking->toLegacy());
            } catch (\Exception $e) {
                return [
                    'success' => false,
                    'message' => 'Booking could not be canceled',
                    'message_code' => 'BOOKING_CANNOT_BE_CANCELED',
                ];
            }
            $result = $this->publishBookingCancellation($booking, $member, $branch, $previousBookingStatus);

            if (!$cancelBooking) {
                return [
                    'success' => false,
                    'message' => 'Booking could not be canceled',
                    'message_code' => 'BOOKING_CANNOT_BE_CANCELED',
                ];
            }

            $this->eventManager->emit(BookingWasCancelled::class, [$booking]);

            $isSlotRemoved = false;
            if ($previousBookingStatus->is(Status::BOOKED())) {
                $isSlotRemoved = $this->removeSlotFromBooking($booking);
                if (!$isLateCancellation && $timeStart->isAfter(Carbon::now($timezone))) {
                    $this->refundAddonOrCredit($branch, $requestedByUser, $member, $booking, $params->authorId());
                    $this->resetFirstBookingDates($member, $booking);
                }
            }

            if ($previousBookingStatus->is(Status::BOOKED()) && $booking->type()->is(Type::EVENTS())) {
                $program = $this->resolveProgram($booking->programId());
                $event = $this->resolveEvent($booking->eventId());

                if ($program->emailAdmin()) {
                    $this->sendBookingEventEmail($branch, $member, $event, $booking);
                }

                $this->eventUpdatedEventSender->execute($branch, $event);
            }

            if (in_array($booking->model(), [ModelList::USERS, ModelList::APPOINTMENTS], true)) {
                $this->sendTrainerCancelEmail($member, $booking);
            }

            if ($isSlotRemoved) {
                $result['data']['removed_slot'] = true;
            }

            if ($isLateCancellation && $requestedByUser->isAdmin()) {
                $result['message'] = sprintf(
                    'THIS_IS_A_LATE_CANCELLATION_PLEASE_CANCEL_AT_LEAST %s HOURS_BEFORE_NEXT_TIME',
                    $cancellationWindow
                );
            }

            if ($booking->type()->is(Type::COURSES())) {
                $this->bookingPublisher->sendCourseBookingCancelledEvent(
                    new CourseBookingCancelledEventMeta([
                        'location_id' =>  $booking->branchId(),
                    ]),
                    new CourseBookingCancelledEventPayload([
                        'id' => $booking->id(),
                        'namespace' => $booking->namespace(),
                        'branch_id' => $booking->branchId(),
                        'user_id' => $booking->userId(),
                        'course_id' => $booking->courseId(),
                        'status' => $booking->status()->getValue(),
                        'date_start' => $booking->startDate()->toDateTimeString(),
                        'date_finish' => $booking->finishDate()->toDateTimeString(),
                        'guest_bookings' => $booking->guestBookings(),
                        'paid' => $booking['paid'] ?: null,
                        'attended' => $booking->attended(),
                        'schedule' => $this->resolveScheduleForCourseBookingFinishedEvent($booking),
                        'created' => $booking->created()->toDateTimeString(),
                        'modified' => $booking->modified()->toDateTimeString(),
                    ])
                );
            }

            if ($booking->type()->is(Type::TIME_SLOTS) || $booking->type()->is(Type::FACILITIES())) {
                $this->bookingPublisher->sendBookingCancelledEventV2(
                    new BookingCancelledEventMetaV2([]),
                    new BookingCancelledEventPayloadV2([
                        'id' => $booking->id(),
                        'namespace' => $booking->namespace(),
                        'branch_id' => $booking->branchId(),
                        'user_id' => $booking->userId(),
                        'model' => $booking->model(),
                        'model_id' => $booking->timeSlotId(),
                        'model_name' => $booking->eventName(),
                        'status' => $booking->status()->getValue(),
                        'time_start' => $booking->startingTime()->toDateTimeString(),
                        'time_finish' => $booking->finishTime()->toDateTimeString(),
                        'guest_bookings' => 0,
                        'paid' => $booking['paid'] ?? null,
                        'attended' => $booking->attended(),
                        'created' => $booking->created()->toDateTimeString(),
                        'modified' => $booking->modified()->toDateTimeString(),
                    ])
                );
            }

            return $result;
        } catch (\Throwable $e) {
            $result = [
                'success' => false,
                'message' => $e->getMessage(),
            ];

            if ($e instanceof \UnsuccessfulOperation) {
                $result['message_code'] = $e->getMessageCode();
            }

            return $result;
        }
    }

    /**
     * @throws \UnsuccessfulOperation
     */
    private function resolveMember(User $requestedByUser, ?string $userId): User
    {
        if ($userId !== null) {
            try {
                $member = $this->usersRepo->getById($userId);

                if (!$member->isActive()) {
                    throw (new \UnsuccessfulOperation('Your account is not active.'))
                        ->setMessageCode('ACCOUNT_NOT_ACTIVE');
                }

                return $member;
            } catch (InvalidMongoIdException $e) {
                throw (new \UnsuccessfulOperation('Invalid User Id'))
                    ->setMessageCode('INVALID_USER_ID');
            } catch (UserNotFoundException $e) {
                throw (new \UnsuccessfulOperation('Member Not Found'))
                    ->setMessageCode('MEMBER_NOT_FOUND');
            }
        }

        return $requestedByUser;
    }

    /**
     * @throws \UnsuccessfulOperation
     */
    private function resolveBooking(string $bookingId): Booking
    {
        try {
            return $this->bookingsRepo->getById($bookingId);
        } catch (InvalidMongoIdException $e) {
            throw (new \UnsuccessfulOperation('Invalid Booking Id'))
                ->setMessageCode('INVALID_BOOKING_ID');
        } catch (BookingNotFoundException $e) {
            throw (new \UnsuccessfulOperation('Booking Not Found'))
                ->setMessageCode('BOOKING_NOT_FOUND');
        }
    }

    /**
     * @throws \UnsuccessfulOperation
     */
    private function resolveBranch(string $branchId): Branch
    {
        try {
            return $this->branchesRepo->getById($branchId);
        } catch (InvalidMongoIdException $e) {
            throw (new \UnsuccessfulOperation('Invalid Branch Id'))
                ->setMessageCode('INVALID_BRANCH_ID');
        } catch (BranchNotFoundException $e) {
            throw (new \UnsuccessfulOperation('Branch Not Found'))
                ->setMessageCode('BRANCH_NOT_FOUND');
        }
    }

    /**
     * @throws \UnsuccessfulOperation
     */
    private function publishBookingCancellation(
        Booking $booking,
        User $member,
        Branch $branch,
        Status $previousStatus
    ): array {
        if (!$booking->type()->is(Type::EVENTS())) {
            return [
                'success' => true,
                'data' => [],
            ];
        }

        $gympassDetails = $branch->features()->gympass();

        try {
            $snsMessageId = $this->bookingRequestsPublisher->sendUserRequestedBookingCancellingEvent(
                new UserRequestedBookingCancellingEventMeta([
                    'memberId' => $member->id(),
                    'eventId' => $booking->eventId(),
                    'programId' => $booking->programId(),
                    'branchId' => $booking->branchId(),
                    'gympassGymId' => $gympassDetails ? $gympassDetails->getId() : null,
                    'gympassProductId' => $gympassDetails ? $gympassDetails->getProductId() : null,
                    'gympassPassTypeNumber' => $gympassDetails ? $gympassDetails->getPassTypeNumber() : null,
                    'gympassValidationApiAuthToken' => $gympassDetails ?
                        $gympassDetails->getValidationApiTokenAuthToken() :
                        null,
                    'gympassClientId' => $gympassDetails ? $gympassDetails->getClientId() : null,
                    'gympassClientSecret' => $gympassDetails ? $gympassDetails->getClientSecret() : null,
                ]),
                new UserRequestedBookingCancellingEventPayload([
                    'bookingId' => $booking->id(),
                    'guestBookings' => $booking->guestBookings(),
                    'previousBookingStatus' => $previousStatus->getValue(),
                ])
            );

            return [
                'success' => true,
                'data' => ['messageId' => $snsMessageId],
            ];
        } catch (\Exception $e) {
            throw (new \UnsuccessfulOperation('Error publishing booking cancellation'))
                ->setMessageCode('ERROR_PUBLISHING_BOOKING_CANCELLATION');
        }
    }

    /**
     * @throws \UnsuccessfulOperation
     */
    private function resolveTimeStart(Booking $booking): Carbon
    {
        try {
            if ($booking->has('time_start')) {
                return $booking->startingTime();
            }

            $startDate = $booking->startDate();
            if ($startDate !== null) {
                return $startDate;
            }
        } catch (InvalidBranchIdException $e) {
            throw (new \UnsuccessfulOperation('Invalid Branch Id'))
                ->setMessageCode('INVALID_BRANCH_ID');
        }

        throw (new \UnsuccessfulOperation('Booking start time and start date not provided'))
            ->setMessageCode('BOOKING_START_TIME_AND_START_DATE_NOT_PROVIDED');
    }

    /**
     * @throws \UnsuccessfulOperation
     */
    private function validateIsNotInThePast(User $requestedByUser, \DateTimeZone $timezone, Carbon $timeStart): void
    {
        $now = Carbon::now($timezone);
        if ($timeStart->isBefore($now) && !$requestedByUser->isStaff()) {
            throw (new \UnsuccessfulOperation('This Event has already passed.'))
                ->setMessageCode('THIS_EVENT_HAS_ALREADY_PASSED');
        }
    }

    /**
     * @throws Exception
     */
    private function isLateCancellationAndGetCancellationWindow(
        User $requestedByUser,
        Booking $booking,
        Branch $branch,
        Carbon $timeStart
    ): array {
        if ($booking->type()->is(Type::EVENTS())) {
            $cancellationWindow = $branch->features()->bookingCancelWindow();
        } else {
            $model = $booking->model();
            if ($booking->model() === ModelList::USERS || $booking->model() === ModelList::APPOINTMENTS) {
                $model = 'trainers';
            }
            $cancellationWindow = $branch->features()->bookingCancelWindowForModel($model);
        }

        if (!$booking->isBooked() && !$booking->isWaiting()) {
            return [false, $cancellationWindow];
        }

        $now = Carbon::now($branch->timezone());
        if ($cancellationWindow === 0 || $cancellationWindow <= $now->diffInHours($timeStart)) {
            return [false, $cancellationWindow];
        }

        $isLateCancellationEnabled = $branch->features()->isLateCancellationEnabled();
        if ($isLateCancellationEnabled || $requestedByUser->isStaff()) {
            return [$isLateCancellationEnabled, $cancellationWindow];
        }

        $message = sprintf(
            'You cannot cancel this booking. You have to cancel at least %s hours before the class starts.',
            $cancellationWindow
        );

        throw (new Exception(__($message)));
    }

    /**
     * @throws \UnsuccessfulOperation
     */
    private function sendBookingCourseEmail(Branch $branch, User $member, Booking $booking): void
    {
        $course = $this->getCourseById($booking->courseId());
        $sendTo = $this->resolveAllRecipientsForCourseEmails($course, $booking);
        $env = \GlofoxEnvironment::currentEnvironment();
        $logoUrl = sprintf('%s/%s/glofox/glofox-logo-horizontal.png', CdnProvider::getUrl(), $env);

        foreach ($course->schedule() as $schedule) {
            if ($schedule['id'] === $booking->sessionId()) {
                $course->offsetSet('start_date', Carbon::parse($schedule['start_date'])->format('D jS \of M'));
                $course->offsetSet('end_date', Carbon::parse($schedule['end_date'])->format('D jS \of M'));
                break;
            }
        }

        $data = [
            'booking' => $booking->toArray(),
            'course' => $course->toArray(),
            'member' => $member->toArray(),
            'logoUrl' => $logoUrl,
        ];

        try {
            $this->emailProvider
                ->config(static::EMAIL_CONFIG)
                ->emailFormat(static::EMAIL_FORMAT)
                ->subject(sprintf(static::EMAIL_SUBJECT, $branch->name()))
                ->template(static::COURSE_EMAIL_TEMPLATE)
                ->cc($sendTo)
                ->viewVars($data)
                ->send();
        } catch (Exception $e) {
            $this->logger->error($e->getMessage());
        }
    }

    /**
     * @throws \UnsuccessfulOperation
     */
    private function resolveAllRecipientsForCourseEmails(Course $course, Booking $booking): array
    {
        $result = [];

        $trainersIds = [];
        foreach ($course->schedule() as $schedule) {
            if ($schedule['id'] === $booking->sessionId()) {
                $trainersIds = $schedule['trainers'];
                break;
            }
        }

        if (empty($trainersIds)) {
            $defaultTrainers = $course->defaultTrainers();
            foreach ($defaultTrainers as $defaultTrainer) {
                $trainersIds[] = is_array($defaultTrainer) ? $defaultTrainer['id'] : $defaultTrainer;
            }
        }

        $trainers = $this->usersRepo->findManyByIds($trainersIds, ['email']);
        foreach ($trainers as $trainer) {
            $result[] = $trainer->email();
        }

        $admins = $this->usersRepo->getActiveAdminsAndSuperAdminsByBranchId($course->branchId(), ['email']);
        foreach ($admins as $admin) {
            $result[] = User::make($admin)->email();
        }

        return $result;
    }

    private function sendPushNotification(Status $previousBookingStatus, Booking $booking, Carbon $timeStart): void
    {
        $msg = $previousBookingStatus->is(Status::WAITING()) ?
            'You have withdrawn from the waiting list for %s on %s' :
            'Your booking has been cancelled for %s on %s';
        $msg = sprintf($msg, $booking->eventName() ?? $booking->modelName(), $timeStart->format('D jS \of M  h:i A'));

        /** @var Collection $pushMessageCollection */
        $pushMessageCollection = app()->make('push-message-bag');
        $pushMessageCollection->put('PushMessage', $msg);
        $pushMessageCollection->put('PushAction', Action::REFRESH_BOOKINGS_SCHEDULE());
    }

    private function removeSlotFromBooking(Booking $booking): bool
    {
        if ($booking->model() !== ModelList::APPOINTMENTS) {
            return false;
        }

        $timeSlot = $booking->timeSlot();
        if (
            $timeSlot &&
            $timeSlot->active() &&
            $timeSlot->createdBy() === CreatedBy::TRAINER_AVAILABILITY &&
            $timeSlot->size() === 1
        ) {
            $this->timeSlotsRepo->softDelete($booking->timeSlotId());

            return true;
        }

        return false;
    }

    /**
     * @throws \UnsuccessfulOperation
     */
    private function refundAddonOrCredit(
        Branch $branch,
        User $requestedByUser,
        User $member,
        Booking $booking,
        ?string $authorId
    ): void {
        $isCreditBasedAddonUsed = isset($booking->metadata()['service']) &&
            $booking->metadata()['service']['type'] === CreditServiceType::ADD_ON;

        if ($isCreditBasedAddonUsed) {
            try {
                $originBranchId = $member->originBranchId();
                $addon = $this->addonService->getAddon($booking->metadata()['service']['id'], $originBranchId);
                if ($addon->isCreditsBased()) {
                    $this->addonService->refundCreditByAddOnIdAndBookingId(
                        $originBranchId,
                        $booking->metadata()['service']['id'],
                        $booking->id()
                    );
                }
            } catch (UserOriginBranchIdException | \UnsuccessfulOperation $e) {
                $this->logger->error(
                    sprintf(
                        'An error occurred when refunding add-on. Status code: %s, Error: %s',
                        $e->getCode(),
                        $e->getMessageCode()
                    )
                );
            } finally {
                return;
            }
        }

        if (!$booking->has('paid') || $booking->paid()) {
            $this->revertCredits($branch, $requestedByUser, $member, $booking, $authorId);

            return;
        }

        $this->logger->info(
            sprintf(
                'Credit pack not reverted when booking %s was cancelled because booking was not marked as paid',
                $booking->id(),
            )
        );
    }

    /**
     * @throws \UnsuccessfulOperation
     */
    private function revertCredits(
        Branch $branch,
        User $requestedByUser,
        User $member,
        Booking $booking,
        ?string $authorId
    ): void {
        $numRevertedCredits = $this->userCreditCakeModel->revertCreditsSpent(
            $branch->id(),
            $member->id(),
            $booking->id()
        );

        if ($numRevertedCredits < $booking->totalBookings()) {
            $isPaid = $this->stripeChargeCakeModel->hasPaidForBooking($booking->toLegacy());

            if ($isPaid && !empty($branch->features()->refundedCreditExpiryForBookings())) {
                $numCreditsToCreate = $booking->totalBookings() - $numRevertedCredits;

                if ($authorId !== null && Auth::integrator() !== null) {
                    try {
                        $author = $this->usersRepo->getById($authorId);
                    } catch (InvalidMongoIdException $e) {
                        throw (new \UnsuccessfulOperation('Invalid Author Id'))
                            ->setMessageCode('INVALID_AUTHOR_ID');
                    } catch (UserNotFoundException $e) {
                        throw (new \UnsuccessfulOperation('Author Not Found'))
                            ->setMessageCode('AUTHOR_NOT_FOUND');
                    }
                }

                $this->userCreditCakeModel->createCreditForBookingCancelled(
                    $author ?? $requestedByUser,
                    $booking->toLegacy(),
                    $numCreditsToCreate
                );
            }
        }
    }

    private function resetFirstBookingDates(User $member, Booking $booking): void
    {
        if (!$member->membership()->isDOFB() || !$member->membership()->isTimeBased()) {
            return;
        }

        $isCreditBasedAddonUsed = isset($booking->metadata()['service']) &&
            $booking->metadata()['service']['type'] === CreditServiceType::ADD_ON;
        $membershipReset = $this->membershipCakeModel->resetFirstBookingDateMembership($member->toArray());

        if ($membershipReset['success'] && !$isCreditBasedAddonUsed) {
            $user = $membershipReset['member'] ?: $this->usersRepo->findById($member->id());
            $this->userCreditCakeModel->resetFirstBookingDateCredits($user);
        }
    }

    private function sendBookingEventEmail(Branch $branch, User $member, Event $event, Booking $booking): void
    {
        $sendTo = $this->resolveAllRecipientsForEventEmails($event);
        $env = \GlofoxEnvironment::currentEnvironment();
        $logoUrl = sprintf('%s/%s/glofox/glofox-logo-horizontal.png', CdnProvider::getUrl(), $env);

        $event->offsetSet('date', $event->date()->format('D jS \of M'));
        $event->offsetSet('start_time', $event->timeStart()->format('h:i A'));
        $event->offsetSet('end_time', $event->timeFinish()->format('h:i A'));

        $data = [
            'booking' => $booking->toArray(),
            'event' => $event->toArray(),
            'member' => $member->toArray(),
            'logoUrl' => $logoUrl,
        ];

        try {
            $this->emailProvider
                ->config(static::EMAIL_CONFIG)
                ->emailFormat(static::EMAIL_FORMAT)
                ->subject(sprintf(static::EMAIL_SUBJECT, $branch->name()))
                ->template(static::EVENT_EMAIL_TEMPLATE)
                ->cc($sendTo)
                ->viewVars($data)
                ->send();

            $this->logger->info('Booking confirmation email sent to staff', compact('sendTo'));
        } catch (Exception $e) {
            $this->logger->error(
                'Could not send booking confirmation email to staff',
                [
                    'emails' => $sendTo,
                    'data' => $data,
                    'exception' => $e->getTrace(),
                ]
            );
        }
    }

    private function resolveAllRecipientsForEventEmails(Event $event): array
    {
        $result = [];

        $trainers = $this->usersRepo->findManyByIds($event->trainerIds(), ['email']);
        /** @var User $trainer */
        foreach ($trainers as $trainer) {
            $result[] = $trainer->email();
        }

        $admins = $this->usersRepo->getActiveAdminsAndSuperAdminsByBranchId($event->branchId(), ['email']);
        foreach ($admins as $admin) {
            $result[] = User::make($admin)->email();
        }

        return $result;
    }

    /**
     * @throws \UnsuccessfulOperation
     */
    private function resolveProgram(string $programId): Program
    {
        try {
            return $this->programsRepo->getById($programId);
        } catch (InvalidMongoIdException $e) {
            throw (new \UnsuccessfulOperation('Invalid Program Id'))
                ->setMessageCode('INVALID_PROGRAM_ID');
        } catch (ProgramNotFoundException $e) {
            throw (new \UnsuccessfulOperation('Program Not Found'))
                ->setMessageCode('PROGRAM_NOT_FOUND');
        }
    }

    /**
     * @throws \UnsuccessfulOperation
     */
    private function resolveEvent(string $eventId): Event
    {
        try {
            return $this->eventsRepo->getById($eventId);
        } catch (InvalidMongoIdException $e) {
            throw (new \UnsuccessfulOperation('Invalid event Id'))
                ->setMessageCode('INVALID_EVENT_ID');
        } catch (EventNotFoundException $e) {
            throw (new \UnsuccessfulOperation('Event Not Found'))
                ->setMessageCode('EVENT_NOT_FOUND');
        }
    }

    /**
     * @throws \UnsuccessfulOperation
     */
    private function sendTrainerCancelEmail(User $member, Booking $booking): void
    {
        try {
            $timeSlot = $this->timeSlotsRepo->getById($booking->timeSlotId());
        } catch (InvalidMongoIdException $e) {
            throw (new \UnsuccessfulOperation('Invalid Time Slot Id'))
                ->setMessageCode('INVALID_TIME_SLOT_ID');
        } catch (TimeSlotNotFoundException $e) {
            throw (new \UnsuccessfulOperation('Time Slot Not Found'))
                ->setMessageCode('TIME_SLOT_NOT_FOUND');
        }

        $trainer = $timeSlot->staff();
        if ($trainer === null) {
            return;
        }

        $env = \GlofoxEnvironment::currentEnvironment();
        $logoUrl = sprintf('%s/%s/glofox/glofox-logo-horizontal.png', CdnProvider::getUrl(), $env);

        $data = [
            'trainerName' => $trainer->name(),
            'timeSlotDate' => $timeSlot->date()->format('l jS \of F'),
            'timeSlotTimeStart' => $timeSlot->timeStart()->format('h:i:s A'),
            'timeSlotTimeFinish' => $timeSlot->timeFinish()->format('h:i:s A'),
            'memberName' => $member->name(),
            'memberEmail' => $member->email(),
            'memberPhone' => $member->phone(),
            'logoUrl' => $logoUrl,
        ];

        try {
            $this->emailProvider
                ->config(static::TRAINER_EMAIL_CONFIG)
                ->emailFormat(static::EMAIL_FORMAT)
                ->subject(sprintf(static::TRAINER_EMAIL_SUBJECT))
                ->template(static::TRAINER_EMAIL_TEMPLATE)
                ->to($trainer->email())
                ->viewVars($data)
                ->send();
        } catch (\Exception $e) {
            $this->logger->error(sprintf('[CancelBooking::execute::sendTrainerCancelEmail] %s', $e->getMessage()));
        }
    }

    /**
     * @throws \UnsuccessfulOperation
     */
    private function resolveScheduleForCourseBookingFinishedEvent(Booking $booking): array
    {
        $course = $this->getCourseById($booking->courseId());

        $schedule = [];
        foreach ($course->schedule() as $sch) {
            if ($sch['id'] == $booking->sessionId()) {
                $schedule = $sch;
            }
        }

        $scheduleDays = [];
        foreach ($schedule['days'] as $day) {
            $days = [];
            foreach ($day['days'] as $value) {
                $days[] = [
                    'id' => $value['id'],
                    'label' => $value['label'],
                ];
            }

            $scheduleDays[] = [
                'day' => $days[0],
                'time_start' => Carbon::parse($day['start_time'])->toTimeString(),
                'time_end' => Carbon::parse($day['end_time'])->toTimeString(),
            ];
        }

        return [
            'id' => (string)$schedule['id'],
            'name' => $schedule['label'],
            'days' => $scheduleDays,
        ];
    }

    /**
     * @throws \UnsuccessfulOperation
     */
    private function getCourseById(string $id): Course
    {
        if ($this->cachedCourse === null) {
            try {
                $this->cachedCourse = $this->coursesRepo->getById($id);
            } catch (InvalidMongoIdException $e) {
                throw (new \UnsuccessfulOperation('Invalid Course Id'))
                    ->setMessageCode('INVALID_COURSE_ID');
            } catch (CourseNotFoundException $e) {
                throw (new \UnsuccessfulOperation('Course Not Found'))
                    ->setMessageCode('COURSE_NOT_FOUND');
            }
        }

        return $this->cachedCourse;
    }

    private function setFailureReason(Booking $booking, ?string $failureReason): Booking
    {
        if (empty($failureReason)) {
            return $booking;
        }

        $allFailureReasons = $booking->allFailureReasons() ?? [];
        $booking->offsetSet('all_failure_reasons', [$failureReason, ...$allFailureReasons]);
        $booking->offsetSet('failure_reason', $failureReason);

        return $booking;
    }
}
