<?php

declare(strict_types=1);

use Glofox\Domain\Activities\Contexts\BookingActivityContext;
use Glofox\Domain\Bookings\Events\TimeslotBookingWasCreated;
use Glofox\Domain\Memberships\Models\Membership;
use Glofox\Domain\Memberships\Models\Plan;
use Glofox\Domain\Memberships\Repositories\MembershipsRepository;
use League\Event\EventInterface;

App::import('Test/Case', 'GlofoxTestCase');

class BookingActivityContextTest extends \GlofoxTestCase
{
    private const MEMBERSHIP_ID = '645d17af73c3b909510c9663';
    private const MEMBERSHIP_PLAN_CODE = 'test-plan-code';
    private const USER_ID = 'a9a5521a05c687bda917785c';
    private const BRANCH_ID = '49a7011a05c677b9a916612a';
    private MembershipsRepository $membershipsRepository;
    public $fixtures = [
        'app.activity',
        'app.stripe_charge',
        'app.user',
    ];

    public function setUp()
    {
        parent::setUp();

        $this->mockLogger();
        $this->membershipsRepository = Mockery::mock(MembershipsRepository::class);
    }

    public function tearDown()
    {
        parent::tearDown();

        $this->teardownLogger();

        Mockery::close();
    }

    /**
     * @dataProvider getBuildDataProvider
     * @param EventInterface $event
     * @param array $expected
     * @return void
     */
    public function testBuild(EventInterface $event, array $expected): void
    {
        $this->membershipsRepository
            ->shouldReceive('addCriteria')
            ->andReturnSelf()
            ->shouldReceive('first')
            ->andReturn(
                Membership::make([
                    '_id' => self::MEMBERSHIP_ID,
                    'name' => 'Membership name',
                    'plans' => [
                        Plan::make([
                            'name' => 'Test Membership plan',
                            'code' => self::MEMBERSHIP_PLAN_CODE,
                        ]),
                    ],
                ])
            );

        app()->instance(MembershipsRepository::class, $this->membershipsRepository);

        $context = (new BookingActivityContext())->build($event);

        self::assertEquals($expected, $context);
    }

    public function getBuildDataProvider(): array
    {
        return [
            'All data are passed within appointment event' => [
                'event' => $this->getTimeslotBookingWasCreatedEvent(
                    ['_id' => self::BRANCH_ID],
                    ['_id' => self::USER_ID],
                    [
                        'Booking' => [
                            '_id' => 'test-booking-id',
                            'event_name' => 'Appointment name 1',
                            'model' => 'appointments',
                            'model_id' => 'test-model-id',
                            'time_start' => strtotime('2023-05-10 10:00:00'),
                            'time_finish' => strtotime('2023-05-10 10:30:00'),
                        ],
                    ],
                    ['StripeCharge' => $this->getStripeCharge()],
                    []
                ),
                'expected' => [
                    'booking_id' => 'test-booking-id',
                    'booking_name' => 'Appointment name 1',
                    'model' => 'appointments',
                    'time_start' => 1_683_712_800,
                    'time_finish' => 1_683_714_600,
                    'charge_id' => 'test-charge-id',
                    'charge_amount' => 30.0,
                    'payment_method' => 'card',
                    'description' => 'some description',
                    'refunded' => false,
                    'paid' => true,
                    'transaction_status' => 'PAID',
                    'invoice_id' => '46563gd263',
                    'quantity' => 1,
                    'total_bookings' => 1,
                    'sold_by_user_id' => 'test-user_id',
                    'wallet_balance_after' => 0,
                    'membership_name' => 'Membership name',
                    'membership_id' => self::MEMBERSHIP_ID,
                    'plan_code' => self::MEMBERSHIP_PLAN_CODE,
                    'plan_name' => 'Test Membership plan',
                ],
            ],
            'Pass data with empty charge within appointment event, no activity linked with the charge' => [
                'event' => $this->getTimeslotBookingWasCreatedEvent(
                    ['_id' => self::BRANCH_ID],
                    ['_id' => self::USER_ID],
                    [
                        'Booking' => [
                            '_id' => 'test-booking-id',
                            'event_name' => 'Appointment name 1',
                            'type' => 'time_slots',
                            'model_id' => '6461e3f11639388b04f5965b',
                            'model' => 'appointments',
                            'time_start' => strtotime('2023-05-10 10:00:00'),
                            'time_finish' => strtotime('2023-05-10 10:30:00'),
                        ],
                    ],
                    ['StripeCharge' => []],
                    []
                ),
                'expected' => [
                    'booking_id' => 'test-booking-id',
                    'booking_name' => 'Appointment name 1',
                    'model' => 'appointments',
                    'time_start' => 1_683_712_800,
                    'time_finish' => 1_683_714_600,
                    'charge_id' => '6461e347088ef26a07a3c4c8',
                    'charge_amount' => 10,
                    'payment_method' => 'flexible',
                    'description' => 'Test',
                    'refunded' => false,
                    'paid' => true,
                    'transaction_status' => 'PAYED',
                    'invoice_id' => '6461e73a76b7ca0225864ff9',
                    'wallet_balance_after' => 0.0,
                ],
            ],
            'Pass data with empty charge within class event, existing activity linked with the charge' => [
                'event' => $this->getTimeslotBookingWasCreatedEvent(
                    ['_id' => self::BRANCH_ID],
                    ['_id' => self::USER_ID],
                    [
                        'Booking' => [
                            '_id' => 'test-booking-id',
                            'event_name' => 'Class name 1',
                            'type' => 'events',
                            'event_id' => '64624dc9a858dcae189920a9',
                            'time_start' => strtotime('2023-05-10 10:00:00'),
                            'time_finish' => strtotime('2023-05-10 10:30:00'),
                        ],
                    ],
                    ['StripeCharge' => []],
                    []
                ),
                'expected' => [
                    'booking_id' => 'test-booking-id',
                    'booking_name' => 'Class name 1',
                    'model' => 'events',
                    'time_start' => 1_683_712_800,
                    'time_finish' => 1_683_714_600,
                ],
            ],
            'Pass data with empty charge within course event, no activity linked with the charge.' => [
                'event' => $this->getTimeslotBookingWasCreatedEvent(
                    ['_id' => self::BRANCH_ID],
                    ['_id' => self::USER_ID],
                    [
                        'Booking' => [
                            '_id' => 'test-booking-id',
                            'model_name' => 'Course name 1',
                            'type' => 'courses',
                            'model_id' => '64625c6d54d1d41f5dbd542b',
                            'model' => 'courses',
                            'date_start' => strtotime('2023-05-10 10:00:00'),
                            'date_finish' => strtotime('2023-05-10 10:30:00'),
                        ],
                    ],
                    ['StripeCharge' => $this->getStripeCharge()],
                    []
                ),
                'expected' => [
                    'booking_id' => 'test-booking-id',
                    'booking_name' => 'Course name 1',
                    'model' => 'courses',
                    'time_start' => 1_683_712_800,
                    'time_finish' => 1_683_714_600,
                    'charge_id' => 'test-charge-id',
                    'charge_amount' => 30.0,
                    'payment_method' => 'card',
                    'description' => 'some description',
                    'refunded' => false,
                    'paid' => true,
                    'transaction_status' => 'PAID',
                    'invoice_id' => '46563gd263',
                    'quantity' => 1,
                    'total_bookings' => 1,
                    'sold_by_user_id' => 'test-user_id',
                    'wallet_balance_after' => 0,
                    'membership_name' => 'Membership name',
                    'membership_id' => self::MEMBERSHIP_ID,
                    'plan_code' => self::MEMBERSHIP_PLAN_CODE,
                    'plan_name' => 'Test Membership plan',
                ],
            ],
        ];
    }

    private function getTimeslotBookingWasCreatedEvent(
        array $branch,
        array $user,
        array $booking,
        array $charge,
        array $timeslot
    ): TimeslotBookingWasCreated {
        return new TimeslotBookingWasCreated($branch, $user, $booking, $charge, $timeslot);
    }

    private function getStripeCharge(array $attributes = []): array
    {
        return array_merge([
            '_id' => 'test-charge-id',
            'amount' => 30.0,
            'description' => 'some description',
            'paid' => true,
            'transaction_status' => 'PAID',
            'invoice_id' => '46563gd263',
            'quantity' => 1,
            'total_bookings' => 1,
            'sold_by_user_id' => 'test-user_id',
            'metadata' => [
                'user_id' => self::USER_ID,
                'branch_id' => self::BRANCH_ID,
                'payment_method' => 'card',
                'wallet_balance_after' => 0,
                'membership_id' => self::MEMBERSHIP_ID,
                'plan_code' => self::MEMBERSHIP_PLAN_CODE,
            ],
        ], $attributes);
    }
}
