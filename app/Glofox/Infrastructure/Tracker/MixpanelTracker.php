<?php

declare(strict_types=1);

namespace Glofox\Infrastructure\Tracker;

use Glofox\Domain\EventTrackers\Events\Event;
use Glofox\Domain\EventTrackers\Models\TrackingData;
use Glofox\Domain\EventTrackers\TrackerInterface;
use Mixpanel;
use Psr\Log\LoggerInterface;

class MixpanelTracker implements TrackerInterface
{
    private \Mixpanel $instance;

    private \Psr\Log\LoggerInterface $logger;

    public function __construct(Mixpanel $instance, LoggerInterface $logger)
    {
        $this->instance = $instance;
        $this->logger = $logger;
    }

    public function track(Event $event, TrackingData $data): void
    {
        $eventName = (string) $event;

        $this->logger->info(sprintf('Tracking event %s', $eventName), $data->toArray());

        $this->instance->identify($data['userId']);
        $this->instance->track($eventName, $data->toArray());
        $this->instance->flush();
    }
}
