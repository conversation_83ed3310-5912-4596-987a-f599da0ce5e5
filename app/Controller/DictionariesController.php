<?php

use Glofox\Domain\Authentication\Auth;
use Glofox\Domain\Dictionaries\Http\Responses\FindDictionariesResponse;
use Glofox\Domain\Http\Exceptions\UnauthorizedOperation;

/**
 * DictionariesController class.
 *
 * @uses          AppController
 *
 * @property Dictionary $Dictionary
 */
class Dictionaries<PERSON>ontroller extends AppController
{
    public $name = 'Dictionaries';
    public $uses = [
        'Dictionary',
    ];
    public $components = [
        'Notification',
        'JWT',
    ];
    /****************************** REST WEBSERVICE ACTIONS  *******************************************/

    /**
     * Dispatcher for Model.
     *
     * @param string $identifier [description]
     *
     * @return string
     */
    public function dispatcher($identifier = null)
    {
        if (!$this->isUserLogged() && 'GET' == $this->request->method() && !$identifier) {
            $result = $this->container()->call([$this->Dictionary, 'getAll'], ['params' => $this->request->query]);

            return response()->json($result);
        }

        return parent::dispatch($identifier, $this->Dictionary);
    }

    /**
     * Find all the courses assossiated to a branch.
     *
     * @param string $lang
     *
     * @return string [type] [description]
     */
    public function findByLanguage($lang = 'en')
    {
        $lang = isset($this->request['lang']) && !empty($this->request['lang']) ? $this->request['lang'] : $lang;

        $conditions = json_decode($this->request->input(), true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        $conditions = $conditions ?: [];

        $dictionary = $this->Dictionary->findByLanguageCode($lang, $conditions);

        $result = [];

        if ($dictionary) {
            $result = $dictionary['Dictionary']['dictionary'];
        }

        return (new FindDictionariesResponse($result))->toJsonResponse();
    }

    /**
     * @param string $branch_id
     *
     * @return \Illuminate\Http\JsonResponse|string
     */
    public function findByBranchId($branch_id = '')
    {
        $dictionary = $this->Dictionary->findByBranchId($branch_id);
        $input_data = json_decode($this->request->input(), true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        $language = !empty($input_data['language']) ? $input_data['language'] : 'en';

        if ($dictionary) {
            $result = $dictionary['Dictionary']['dictionary'];
            return (new FindDictionariesResponse($result))->toJsonResponse();
        }

        $dictionary = $this->Dictionary->findByLanguageCode($language, []);

        if ($dictionary) {
            $result = $dictionary['Dictionary']['dictionary'];
            return (new FindDictionariesResponse($result))->toJsonResponse();
        }

        return $this->findByLanguage('en');
    }

    /**
     * @return array
     */
    public function udpateWord()
    {
        return $this->updateWord();
    }

    /**
     * This method will receive a json object with the following keys: language_code, category_id
     * namespace, branch_id, word_key, new_value. And is going to update that word in the
     * corresponding scope (language or category or namespace or branch).
     *
     * @return array is going to return a json obejct in the form of { "success": boolean, "message_code": string}
     */
    public function updateWord()
    {
        $input = json_decode($this->request->input(), true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        // Validations
        if (empty($input['namespace'])) {
            $response = ['success' => false, 'message_code' => 'ERROR_NAMESPACE_REQUIRED'];

            return json_encode($response);
        }

        if (empty($input['language_code'])) {
            $response = ['success' => false, 'message_code' => 'ERROR_LANGUAGE_CODE_REQUIRED'];

            return json_encode($response);
        }

        if (empty($input['word_key'])) {
            $response = ['success' => false, 'message_code' => 'ERROR_WORD_KEY_REQUIRED'];

            return json_encode($response);
        }

        if (empty($input['new_value'])) {
            $response = ['success' => false, 'message_code' => 'ERROR_WORD_VALUE_REQUIRED'];

            return json_encode($response);
        }

        $language = $input['language_code'] ?? null;
        $category_id = $input['category_id'] ?? null;
        $namespace = $input['namespace'] ?? null;
        $branch_id = $input['branch_id'] ?? null;
        $word_key = $input['word_key'] ?? null;
        $new_value = $input['new_value'] ?? null;

        //legacy - does not check for namespace access
        $has_access = $this->JWT->validate(['admin'], $namespace, $branch_id);

        if (!$has_access['success']) {
            return json_encode($has_access, JSON_PARTIAL_OUTPUT_ON_ERROR);
        }

        //checking namespace access
        $user = Auth::user();
        if($user !== null && ($user->namespace() !== $input['namespace'])){
            throw UnauthorizedOperation::becauseUnauthorizedAction();
        }

        $this->Dictionary->updateWord($language, $category_id, $namespace, $branch_id, $word_key, $new_value);

        $result = [
            'success' => true,
            'message_code' => 'UPDATE_WORD_SUCCESS',
        ];

        $response = json_encode($result);

        return $response;
    }

    /**
     * @return string
     */
    public function listLanguages()
    {
        $result = $this->Dictionary->listLanguages();

        $response = json_encode($result, JSON_PARTIAL_OUTPUT_ON_ERROR);

        return $response;
    }
}
