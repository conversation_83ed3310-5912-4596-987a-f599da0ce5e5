<?php

namespace CakeTestCases\Glofox\Payments\Providers\Gateway\ProtoParsers;

use Glofox\Payments\Providers\Gateway\ProtoParsers\CardParser;
use GRPC\Payments\AccountService\Card;
use GRPC\Payments\AccountService\SetupIntent;

\App::import('Test/Case', 'GlofoxTestCase');

class CardParserTest extends \GlofoxTestCase
{
    public function testParseFromProtoMessage()
    {
        $c = $this->getBaseGRPCCard();

        $parser = new CardParser();

        $parsed = $parser->parseFromProtoMessage($c);
        $this->assertBaseGRPCCard($parsed);
        self::assertNull($parsed->setupIntent());
    }

    public function testParseFromProtoMessageWithSetupIntent()
    {
        $setupIntent = new SetupIntent();
        $setupIntent->setPSPUUID('id')
            ->setPaymentMethodID('pm id')
            ->setStatus('status');

        $c = $this->getBaseGRPCCard();
        $c->setSetupIntent($setupIntent);

        $parser = new CardParser();

        $parsed = $parser->parseFromProtoMessage($c);
        $this->assertBaseGRPCCard($parsed);

        self::assertNotNull($parsed->setupIntent());
        self::assertEquals('id', $parsed->setupIntent()->getId());
        self::assertEquals('pm id', $parsed->setupIntent()->getPaymentMethodID());
        self::assertEquals('status', $parsed->setupIntent()->getStatus());
    }

    protected function getBaseGRPCCard(): Card
    {
        $c = new Card();

        return $c->setID(1)
            ->setProviderID(2)
            ->setMonth(3)
            ->setYear(4)
            ->setLastFour(1234)
            ->setBrand('VISA')
            ->setPSPUUID('psp uuid')
            ->setPSPResponse('psp response');
    }

    protected function assertBaseGRPCCard(\Glofox\Payments\Entities\Card\Models\Card $parsed)
    {
        self::assertEquals(1, $parsed->id());
        self::assertEquals(2, $parsed->serviceProviderId());
        self::assertEquals(3, $parsed->month());
        self::assertEquals(4, $parsed->year());
        self::assertEquals(1234, $parsed->lastFour());
        self::assertEquals('VISA', $parsed->brand());
        self::assertEquals('psp uuid', $parsed->paymentServiceId());
        self::assertEquals('psp response', $parsed->paymentServiceResponse());
    }
}
