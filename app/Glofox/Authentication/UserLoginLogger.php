<?php

declare(strict_types=1);

namespace Glofox\Authentication;

use Glofox\Domain\Users\Models\User;
use Glofox\Request;
use Psr\Log\LoggerInterface;

class UserLoginLogger
{
    private \Psr\Log\LoggerInterface $logger;

    private \Glofox\Request $request;

    public function __construct(Request $request, LoggerInterface $logger)
    {
        $this->request = $request;
        $this->logger = $logger;
    }

    public function log(User $user): void
    {
        $this->logger->info(
            sprintf('User %s logged in', (string) $user->id()),
            $this->contextData($user)
        );
    }

    private function contextData(User $user): array
    {
        return [
            'userId' => (string) $user->id(),
            'branchId' => $user->currentBranchId(),
            'namespace' => $user->namespace(),
            'userType' => $user->type(),
            'remoteIps' => $this->getClientIps(),
        ];
    }

    /**
     * @return string[]
     */
    private function getClientIps(): array
    {
        $header = $this->request->header('X-Forwarded-For');
        if (!$header) {
            return [];
        }

        return explode(', ', $header);
    }
}
