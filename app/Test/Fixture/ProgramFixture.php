<?php

use Carbon\Carbon;

class ProgramFixture extends CakeTestFixture
{
    private const VIRTUAL_SLOT_BRANCH_ID = '6461ed6fdef3e8f4c6d4b00c';
    private const VIRTUAL_SLOT_BRANCH_NAMESPACE = 'virtualslotstest';
    private const VIRTUAL_SLOT_STAFF_ID = '6461eeb3bd01a2e1dc88a96d';
    private const VIRTUAL_SLOT_FACILITY_ID = '64620bdd9a5784fd0da79869';
    private const MEMBERSHIP_ID = '5f159e696222aaa4d0ee4ce3';
    private const NAMESPACE = 'glofox';
    private const BRANCH_ID = '49a7011a05c677b9a916612a';
    private const FACILITY_ID = '67426295783c43f929bade95';
    private const TRAINER_ID = '67425efa1d395d128dde1ef2';
    private const BRANCH_ONE_WEEK_TO_DISPLAY = '4fa7011a05c677bfa9166121';

    public string $import = 'Program';

    /**
     * @throws MongoException
     */
    public function init()
    {
        $this->records = [
            $this->createBase(
                [
                    '_id' => '5adf442bb2db1df852f44df1',
                    'branch_id' => self::BRANCH_ONE_WEEK_TO_DISPLAY,
                    'name' => 'Test for missing events, one week to display, event already created, spanish summer end date math with last display day date in utc',
                    'namespace' => self::NAMESPACE,
                    'date_finish' => new MongoDate(strtotime('2024-05-06T00:00:00+00:00')),
                    'schedule' =>  [
                        [
                            'days_week' => 1,
                            'start_time' => '8:00',
                            'end_time' => '09:00',
                            'active' => true,
                            'facility' => ['52a7011a05c677bda826611b'],
                            'level' => 'advanced',
                            'trainers' => ['59a7011a05c677bda916612c'],
                            'size' => 100,
                            'code' => '5856c03455ffa',
                        ],
                    ],
                ]
            ),
            [
                '_id' => new MongoId('13b7411a15c676bda824611b'),
                'branch_id' => self::BRANCH_ID,
                'namespace' => self::NAMESPACE,
                'active' => true,
                'schedule' => [
                    [
                        'days_week' => 0,
                        'start_time' => '8:00',
                        'end_time' => '09:00',
                        'active' => true,
                        'facility' => ['52a7011a05c677bda826611b'],
                        'level' => 'advanced',
                        'trainers' => ['59a7011a05c677bda916612c'],
                        'size' => 100,
                        'code' => '5856c03455ff0',
                    ],
                    [
                        'days_week' => 1,
                        'start_time' => '8:00',
                        'end_time' => '09:00',
                        'active' => true,
                        'facility' => ['52a7011a05c677bda826611b'],
                        'level' => 'advanced',
                        'trainers' => ['59a7011a05c677bda916612c'],
                        'size' => 100,
                        'code' => '5856c03455ff1',
                    ],
                    [
                        'days_week' => 2,
                        'start_time' => '8:00',
                        'end_time' => '09:00',
                        'active' => true,
                        'facility' => ['52a7011a05c677bda826611b'],
                        'level' => 'advanced',
                        'trainers' => ['59a7011a05c677bda916612c'],
                        'size' => 100,
                        'code' => '5856c03455ff2',
                    ],
                    [
                        'days_week' => 3,
                        'start_time' => '8:00',
                        'end_time' => '09:00',
                        'active' => true,
                        'facility' => ['52a7011a05c677bda826611b'],
                        'level' => 'advanced',
                        'trainers' => ['59a7011a05c677bda916612c'],
                        'size' => 100,
                        'code' => '5856c03455ff3',
                    ],
                    [
                        'days_week' => 4,
                        'start_time' => '8:00',
                        'end_time' => '09:00',
                        'active' => true,
                        'facility' => ['52a7011a05c677bda826611b'],
                        'level' => 'advanced',
                        'trainers' => ['59a7011a05c677bda916612c'],
                        'size' => 100,
                        'code' => '5856c03455ff4',
                    ],
                    [
                        'days_week' => 5,
                        'start_time' => '8:00',
                        'end_time' => '09:00',
                        'active' => true,
                        'facility' => ['52a7011a05c677bda826611b'],
                        'level' => 'advanced',
                        'trainers' => ['59a7011a05c677bda916612c'],
                        'size' => 100,
                        'code' => '5856c03455ff5',
                    ],
                    [
                        'days_week' => 6,
                        'start_time' => '8:00',
                        'end_time' => '09:00',
                        'active' => true,
                        'facility' => ['52a7011a05c677bda826611b'],
                        'level' => 'advanced',
                        'trainers' => ['59a7011a05c677bda916612c'],
                        'size' => 100,
                        'code' => '5856c03455ff6',
                    ],
                ],
                'schedule_default' => [
                    'facility' => ['52a7011a05c677bda826611b'],
                    'trainers' => ['59a7011a05c677bda916612c'],
                    'size' => 100,
                    'level' => 'advanced',
                    '_level' => 'advanced',
                    '_trainers' => [
                        [
                            'branch_id' => '53fb0a22fe25f0c84c8b4575',
                            'namespace' => self::NAMESPACE,
                            'active' => true,
                            '_id' => '58568a8fa875ab19530041a7',
                            'categories' => [],
                            'type' => 'TRAINER',
                            'first_name' => 'Timmy',
                            'last_name' => 'Fisher',
                            'phone' => '0899991827',
                            'description' => 'Worst Trainer Ever!',
                            'email' => '<EMAIL>',
                            'password' => 'e10adc3949ba59abbe56e057f20f883e',
                            'bookable' => false,
                            'login' => '<EMAIL>',
                            'name' => 'Timmy Fisher',
                        ],
                    ],
                ],
                'categories' => [],
                'name' => 'Test',
                'description' => 'Class',
                'allowed_member_types' => [
                    [
                        'price' => 0.0,
                        'memberships' => [
                            [
                                'id' => 0,
                                'label' => 'PAYG',
                            ],
                        ],
                        'type' => 'payg',
                    ],
                    [
                        'price' => 0.0,
                        'memberships' => [
                            [
                                'id' => 0,
                                'label' => 'MEMBER',
                            ],
                        ],
                        'type' => 'member',
                    ],
                ],
                'date_start' => new MongoDate(strtotime('now')),
                'date_finish' => null,
                'email_admin' => false,
                'private' => false,
                'pricing' => 'CUSTOM',
                'metadata' => [
                    'gympass' => true,
                ],
            ],
            $this->createBase(['_id' => '59ba622a4116e7fc5a91c57b', 'categories' => ['59ba57b0e5c2b7ae3683a017']]),
            $this->createBase(
                [
                    '_id' => '59ca3a2f570b4bde3da3decd',
                    'pricing' => 'CUSTOM',
                    'allowed_member_types' => [
                        ['price' => 5, 'type' => 'member'],
                        ['price' => 10, 'type' => 'payg'],
                    ],
                    'default_price' => 5,
                ]
            ),
            // Classpass size 3 and glofox size 10
            $this->createBase(
                [
                    '_id' => '5adf442bb2db1df852f44df0',
                    'branch_id' => '5addc25383266f65abf515c4',
                    'name' => 'Classpass size 3 and glofox size 10',
                    'namespace' => 'classpass_integrated',
                    'pricing' => 'CUSTOM',
                    'allowed_member_types' => [
                        ['price' => 5, 'type' => 'member'],
                        ['price' => 10, 'type' => 'payg'],
                    ],
                    'default_price' => 5,
                    'schedule' => [
                        'days_week' => 2,
                        'start_time' => '8:00',
                        'end_time' => '09:00',
                        'active' => true,
                        'facility' => ['5adf4cb5fca91f2d25f62df4'],
                        'level' => 'advanced',
                        'trainers' => ['5adf4d52f35bbcfb4251f960'],
                        'size' => 10,
                        'code' => '5833c03455ff0',
                    ],
                    'schedule_default' => [
                        'size' => 10,
                    ],
                    'metadata' => [
                        'classpass' => [
                            'size' => 3,
                        ],
                    ],
                ]
            ),
            // Classpass size 0 and glofox size 10
            $this->createBase(
                [
                    '_id' => '5adf44e7cdd89bdf93f1df8e',
                    'branch_id' => '5addc25383266f65abf515c4',
                    'namespace' => 'classpass_integrated',
                    'name' => 'Classpass size 0 and glofox size 10',
                    'pricing' => 'CUSTOM',
                    'allowed_member_types' => [
                        ['price' => 5, 'type' => 'member'],
                        ['price' => 10, 'type' => 'payg'],
                    ],
                    'default_price' => 5,
                    'schedule' => [
                        'days_week' => 3,
                        'start_time' => '8:00',
                        'end_time' => '09:00',
                        'active' => true,
                        'facility' => ['5adf4cb5fca91f2d25f62df4'],
                        'level' => 'advanced',
                        'trainers' => ['5adf4d52f35bbcfb4251f960'],
                        'size' => 10,
                        'code' => '743c03455lls1',
                    ],
                    'metadata' => [
                        'classpass' => [
                            'size' => 0,
                        ],
                    ],
                ]
            ),
            // no classpass size set and glofox size 10
            $this->createBase(
                [
                    '_id' => '5adf44ee8181e92a30c4f6ab',
                    'branch_id' => '5addc25383266f65abf515c4',
                    'namespace' => 'classpass_integrated',
                    'name' => ' Classpass integrated and size 0',
                    'pricing' => 'CUSTOM',
                    'allowed_member_types' => [
                        ['price' => 5, 'type' => 'member'],
                        ['price' => 10, 'type' => 'payg'],
                    ],
                    'default_price' => 5,
                    'schedule' => [
                        'days_week' => 4,
                        'start_time' => '8:00',
                        'end_time' => '09:00',
                        'active' => true,
                        'facility' => ['5adf4cb5fca91f2d25f62df4'],
                        'level' => 'advanced',
                        'trainers' => ['5adf4d52f35bbcfb4251f960'],
                        'size' => 10,
                        'code' => '93823c03453210',
                    ],
                    'metadata' => [],
                ]
            ),
            // Classpass size 1 and glofox size 9
            $this->createBase(
                [
                    '_id' => '5ae1b7d13b484d636f8982d2',
                    'branch_id' => '5addc25383266f65abf515c4',
                    'namespace' => 'classpass_integrated',
                    'name' => 'Classpass size 1 and glofox size 9',
                    'pricing' => 'CUSTOM',
                    'allowed_member_types' => [
                        ['price' => 5, 'type' => 'member'],
                        ['price' => 10, 'type' => 'payg'],
                    ],
                    'default_price' => 5,
                    'schedule' => [
                        'days_week' => 3,
                        'start_time' => '8:00',
                        'end_time' => '09:00',
                        'active' => true,
                        'facility' => ['5adf4cb5fca91f2d25f62df4'],
                        'level' => 'advanced',
                        'trainers' => ['5adf4d52f35bbcfb4251f960'],
                        'size' => 9,
                        'code' => '743c03455lls1',
                    ],
                    'metadata' => [
                        'classpass' => [
                            'size' => 1,
                        ],
                    ],
                ]
            ),
            // Classpass size 3 and glofox size 4
            $this->createBase(
                [
                    '_id' => '5ae897cf98cd3d0a0f2cffd0',
                    'branch_id' => '5addc25383266f65abf515c4',
                    'namespace' => 'classpass_integrated',
                    'name' => 'Classpass size 3 and glofox size 4',
                    'pricing' => 'CUSTOM',
                    'allowed_member_types' => [
                        ['price' => 5, 'type' => 'member'],
                        ['price' => 10, 'type' => 'payg'],
                    ],
                    'default_price' => 5,
                    'schedule' => [
                        'days_week' => 3,
                        'start_time' => '8:00',
                        'end_time' => '09:00',
                        'active' => true,
                        'facility' => ['5adf4cb5fca91f2d25f62df4'],
                        'level' => 'advanced',
                        'trainers' => ['5adf4d52f35bbcfb4251f960'],
                        'size' => 9,
                        'code' => '743c03455lls1',
                    ],
                    'metadata' => [
                        'classpass' => [
                            'size' => 3,
                        ],
                    ],
                ]
            ),
            // Testing cancel bookings
            $this->createBase(
                [
                    '_id' => '5b057507b8e809c018bb33c1',
                    'branch_id' => '5addc25383266f65abf515c4',
                    'namespace' => 'classpass_integrated',
                    'name' => 'Testing cancel bookings',
                    'pricing' => 'CUSTOM',
                    'allowed_member_types' => [
                        ['price' => 5, 'type' => 'member'],
                        ['price' => 10, 'type' => 'payg'],
                    ],
                    'default_price' => 5,
                    'schedule' => [
                        'days_week' => 3,
                        'start_time' => '8:00',
                        'end_time' => '09:00',
                        'active' => true,
                        'facility' => ['5adf4cb5fca91f2d25f62df4'],
                        'level' => 'advanced',
                        'trainers' => ['5adf4d52f35bbcfb4251f960'],
                        'size' => 9,
                        'code' => '743c03455lls1',
                    ],
                ]
            ),
            // Program with Single Price - 5 members and 8 payg
            $this->createBase(
                [
                    '_id' => '5b17ee4386631b726f173fab',
                    'branch_id' => '5addc25383266f65abf515c4',
                    'namespace' => 'classpass_integrated',
                    'name' => 'Program with Single Price - 5 members and 8 payg',
                    'pricing' => 'SINGLE',
                    'allowed_member_types' => [
                        ['price' => 5, 'type' => 'member'],
                        ['price' => 10, 'type' => 'payg'],
                    ],
                    'default_price' => 5,
                ]
            ),
            // program with Single Price - 7 members and no payg price
            $this->createBase(
                [
                    '_id' => '5b18105eac03203e32b30eca',
                    'branch_id' => '5addc25383266f65abf515c4',
                    'namespace' => 'classpass_integrated',
                    'name' => 'program with Single Price - 7 members and no payg price',
                    'pricing' => 'SINGLE',
                    'allowed_member_types' => [
                        ['price' => 7, 'type' => 'member'],
                    ],
                    'default_price' => 5,
                ]
            ),

            // program with Custom Price for one membership and price for payg
            $this->createBase(
                [
                    '_id' => '5b19488b6b62ac9661ad6655',
                    'branch_id' => '5addc25383266f65abf515c4',
                    'namespace' => 'classpass_integrated',
                    'name' => 'program with Custom Price for one membership and and price for payg',
                    'pricing' => 'CUSTOM',
                    'allowed_member_types' => [
                        [
                            'price' => 8,
                            'type' => 'member',
                            'membership_id' => '5b19486ac13acae3b31cf562',
                        ],
                        [
                            'price' => 16,
                            'type' => 'payg',
                        ],
                    ],
                    'default_price' => 5,
                ]
            ),

            $this->createBase(
                [
                    '_id' => '5b19488b6b62ac9661ad6656',
                    'branch_id' => self::BRANCH_ID,
                    'namespace' => self::NAMESPACE,
                    'name' => 'program with single price for member and payg',
                    'pricing' => 'SINGLE',
                    'allowed_member_types' => [
                        ['price' => 5, 'type' => 'member'],
                        ['price' => 10, 'type' => 'payg'],
                    ],
                    'default_price' => 5,
                ]
            ),

            $this->createBase(
                [
                    '_id' => '5b19488b6b62ac9661ad6657',
                    'branch_id' => self::BRANCH_ID,
                    'namespace' => self::NAMESPACE,
                    'name' => 'program with single price for member and not payg',
                    'pricing' => 'SINGLE',
                    'allowed_member_types' => [
                        ['price' => 5, 'type' => 'member'],
                    ],
                    'default_price' => 5,
                ]
            ),

            // program with Custom Price for one membership and price for payg
            $this->createBase(
                [
                    '_id' => '5b5b28a73355dafa1a354d87',
                    'branch_id' => '5b5b265bce2cf6bdb9ad6d9a',
                    'namespace' => 'ukclient',
                    'name' => 'program with Custom Price for one membership and and price for payg',
                    'pricing' => 'CUSTOM',
                    'allowed_member_types' => [
                        [
                            'price' => 8,
                            'type' => 'member',
                            'membership_id' => '5b19486ac13acae3b31cf562',
                        ],
                        [
                            'price' => 16,
                            'type' => 'payg',
                        ],
                    ],
                    'default_price' => 5,
                ]
            ),

            $this->createBase(
                [
                    '_id' => '5b19488b6b62ac9661ad6658',
                    'branch_id' => '5addc25383266f65abf515c4',
                    'namespace' => 'classpass_integrated',
                    'name' => 'program with single price, free for member and payg not allowed',
                    'pricing' => 'SINGLE',
                    'allowed_member_types' => [
                        ['price' => 0, 'type' => 'member'],
                    ],
                    'default_price' => 0,
                ]
            ),

            $this->createBase(
                [
                    '_id' => '5b7bede071c02a37fc6b3699',
                    'branch_id' => '5addc25383266f65abf515c4',
                    'namespace' => 'classpass_integrated',
                    'name' => 'program with single price, free for member and payg',
                    'pricing' => 'SINGLE',
                    'allowed_member_types' => [
                        ['price' => 20, 'type' => 'member'],
                        ['price' => 30, 'type' => 'payg'],
                    ],
                    'default_price' => 20,
                ]
            ),

            $this->createBase(
                [
                    '_id' => '5b5b28a73355dafa1a354d88',
                    'branch_id' => self::BRANCH_ID,
                    'namespace' => self::NAMESPACE,
                    'name' => 'program for testing recurring bookings',
                    'pricing' => 'CUSTOM',
                    'allowed_member_types' => [
                        [
                            'price' => 10,
                            'type' => 'member',
                        ],
                        [
                            'price' => 16,
                            'type' => 'payg',
                        ],
                    ],
                    'schedule' => [
                        [
                            'days_week' => 0,
                            'start_time' => '08:00',
                            'end_time' => '09:00',
                            'active' => true,
                            'facility' => ['52a7011a05c677bda826611b'],
                            'level' => 'advanced',
                            'trainers' => ['59a7011a05c677bda916612c'],
                            'size' => 100,
                            'code' => '5856c03455fd1',
                        ],
                    ]
                ]
            ),

            $this->createBase(
                [
                    '_id' => '6279163b4a3f5116c8ad9cd6',
                    'branch_id' => '6279159a1d7cc351e50674bb',
                    'namespace' => 'midnightb',
                    'name' => 'Program to test midnight ending event',
                    'pricing' => 'SINGLE',
                    'allowed_member_types' => [
                        ['price' => 20, 'type' => 'member'],
                        ['price' => 30, 'type' => 'payg'],
                    ],
                    'default_price' => 20,
                ]
            ),

            ...$this->addProgramForCi1808UseCase(),
            ...$this->addProgramForCi1263UseCase(),
            ...$this->addProgramForBookingsControllerDateRangeTest(),
            ...$this->forVirtualSlotTest(),
            ...$this->forBookingWindowTest(),
            ...$this->forPayrollRatesTest(),
            ...$this->forClassPassOverBooking(),
            ...$this->forRecurringClassBookings(),
            ...$this->forActiveLimitBookings(),
            ...$this->forOverBookingForceSizeChange(),
            ...$this->forIntegrationTests(),
            ...$this->forRestrictedMembershipNoCreditsNextCycle(),
            ...$this->forGympassUpdate(),
            ...$this->forEventsUpdate(),
            ...$this->forProgramsUpdate(),
            ...$this->forProgramsUpdateEventGenerator(),
            ...$this->forEventGeneratorComponent(),
            ...$this->forProgramUpsertService(),
            ...$this->forOverdueMembershipBookings(),
            ...$this->forFacilityDeleteCheck(),
            ...$this->forNonDeleteEventsWithCanceledBookings(),
            ...$this->forCategoryAndModelValidations(),
        ];
        parent::init();
    }

    /**
     * This method create a basic program, having defaults for most fields in order
     * to call it with the least amount of fields set, just the ones that are relevant
     * to our test.
     *
     * @param [type] $data [description]
     *
     * @return array [type] [description]
     * @throws MongoException
     */
    public function createBase($data): array
    {
        return [
            '_id' => isset($data['_id']) ? new MongoId($data['_id']) : new MongoId(),
            'branch_id' => $data['branch_id'] ?? self::BRANCH_ID,
            'namespace' => $data['namespace'] ?? self::NAMESPACE,
            'active' => $data['active'] ?? true,
            'schedule' => $data['schedule'] ?? [
                    [
                        'days_week' => 2,
                        'start_time' => '8:00',
                        'end_time' => '09:00',
                        'active' => true,
                        'facility' => ['52a7011a05c677bda826611b'],
                        'level' => 'advanced',
                        'trainers' => ['59a7011a05c677bda916612c'],
                        'size' => 100,
                        'code' => '5856c03455ff0',
                        'private' => $data['private'] ?? false,
                    ],
                ],
            'schedule_default' => $data['schedule_default'] ?? [],
            'categories' => $data['categories'] ?? [],
            'name' => $data['name'] ?? 'Test',
            'description' => $data['description'] ?? 'Class',
            'allowed_member_types' => $data['allowed_member_types'] ?? [
                    [
                        'price' => 0.0,
                        'memberships' => [
                            [
                                'id' => 0,
                                'label' => 'PAYG',
                            ],
                        ],
                        'type' => 'payg',
                    ],
                ],
            'date_start' => $data['date_start'] ?? new MongoDate(strtotime('date')),
            'date_finish' => $data['date_finish'] ?? null,
            'email_admin' => $data['email_admin'] ?? false,
            'private' => $data['private'] ?? false,
            'pricing' => $data['pricing'] ?? 'CUSTOM',
            'metadata' => $data['metadata'] ?? [],
        ];
    }

    private function addProgramForCi1808UseCase(): array
    {
        return [
            [
                '_id' => new MongoId('60e27f21b35f5115330cf25b'),
                'private' => false,
                'email_admin' => false,
                'branch_id' => '5a9591bcdb07bce527400717',
                'namespace' => self::NAMESPACE,
                'active' => true,
                'schedule' => [
                    0 => [
                        'days_week' => 0,
                        'start_time' => '13:30',
                        'end_time' => '14:25',
                        'active' => true,
                        'facility' => '5fbd7c16f43e994fe22bb849',
                        'level' => 'Mixed Levels',
                        'trainers' => [
                            0 => '5fd19628e9df084cbe1968a3',
                        ],
                        'size' => 18,
                        'displayAdd' => true,
                        '_start_time' => new MongoDate('2021-08-28T05:30:00.000Z'),
                        '_end_time' => new MongoDate('2021-08-28T06:25:00.000Z'),
                        '_toggle' => true,
                        '_facility' => [
                            'branch_id' => '5fbd7c12f43e994fe22bb843',
                            'namespace' => self::NAMESPACE,
                            'active' => true,
                            '_id' => '5fbd7c16f43e994fe22bb849',
                            'categories' => [
                                0 => '574ed6f6eb8ce82e65834bbc',
                            ],
                            'name' => 'Lab Studios Joo Chiat',
                            'list_visible' => true,
                            'modified' => [
                                'sec' => 1_606_253_590,
                                'usec' => 959000,
                            ],
                            'created' => [
                                'sec' => 1_606_253_590,
                                'usec' => 959000,
                            ],
                        ],
                        '_level' => [
                            '_id' => 'Mixed Levels',
                            'name' => 'Mixed Levels',
                        ],
                        '_trainers' => [
                            0 => [
                                'id' => '5fd19628e9df084cbe1968a3',
                            ],
                        ],
                        'code' => '60e27f21166f8',
                        'private' => false,
                    ],
                ],
                'schedule_default' => [
                    'facility' => '5fbd7c16f43e994fe22bb849',
                    'trainers' => [
                        0 => '5fd19628e9df084cbe1968a3',
                    ],
                    'size' => 18,
                    'level' => 'Mixed Levels',
                    '_level' => 'Mixed Levels'
                ],
                'categories' => [],
                'name' => 'Barre (Tik Tok Theme Inspired)',
                'description' => '',
                'allowed_member_types' => [
                    [
                        'price' => 0,
                        'type' => 'payg',
                    ],
                ],
                'date_start' => new MongoDate('2021-07-11T01:00:00.000+01:00'),
                'date_finish' => new MongoDate('2021-08-28T01:00:00.000+01:00'),
                'pricing' => 'SINGLE',
                'modified' => new MongoDate('2021-08-28T05:18:36.066+01:00'),
                'created' => new MongoDate('2021-07-05T04:40:17.091+01:00'),
                'metadata' => [
                    'classpass' => [
                        'size' => 0,
                    ],
                ],
            ],
            [
                '_id' => new MongoId('61c2ee6eff7d573d344c7445'),
                'private' => true,
                'email_admin' => false,
                'branch_id' => '5a9591bcdb07bce527400717',
                'namespace' => self::NAMESPACE,
                'active' => true,
                'schedule' => [
                    [
                        'days_week' => 1,
                        'start_time' => '10:30',
                        'end_time' => '11:30',
                        'active' => true,
                        'facility' => '5fbd7c16f43e994fe22bb849',
                        'level' => 'All Levels',
                        'trainers' => [
                            '5fd19323cd3c6417c946d261',
                        ],
                        'size' => 9,
                        'displayAdd' => false,
                        '_start_time' => new MongoDate(strtotime('2022-01-19T02:30:00.000Z')),
                        '_end_time' => new MongoDate(strtotime('2022-01-19T03:30:00.000Z')),
                        '_toggle' => true,
                        '_facility' => [
                            'branch_id' => '5a9591bcdb07bce527400717',
                            'namespace' => self::NAMESPACE,
                            'active' => true,
                            '_id' => '5fbd7c16f43e994fe22bb849',
                            'categories' => [
                                0 => '574ed6f6eb8ce82e65834bbc',
                            ],
                            'name' => 'Lab Studios Joo Chiat',
                            'list_visible' => true,
                            'modified' => [
                                'sec' => 1_606_253_590,
                                'usec' => 959000,
                            ],
                            'created' => [
                                'sec' => 1_606_253_590,
                                'usec' => 959000,
                            ],
                        ],
                        '_level' => [
                            '_id' => 'All Levels',
                            'name' => 'All Levels',
                        ],
                        '_trainers' => [],
                        'code' => '61c2ee6ea6e6e',
                        'private' => true,
                    ],
                    [
                        'days_week' => 1,
                        'start_time' => '18:45',
                        'end_time' => '19:45',
                        'active' => true,
                        'facility' => '5fbd7c16f43e994fe22bb849',
                        'level' => 'All Levels',
                        'trainers' => [
                            '5fd19323cd3c6417c946d261',
                        ],
                        'size' => 9,
                        'displayAdd' => true,
                        '_start_time' => new MongoDate(strtotime('2022-01-19T10:45:00.000Z')),
                        '_end_time' => new MongoDate(strtotime('2022-01-19T11:45:00.000Z')),
                        'code' => '61c2ee6ea6e71',
                        '_trainers' => [],
                        '_facility' => '5fbd7c16f43e994fe22bb849',
                        '_level' => 'All Levels',
                        'private' => true,
                    ],
                ],
                'schedule_default' => [
                    'facility' => '5fbd7c16f43e994fe22bb849',
                    'trainers' => [
                        '61c47db3d439dd531659823e',
                    ],
                    'size' => 9,
                    'level' => 'All Levels',
                    '_level' => 'All Levels',
                    '_facility' => [],
                    '_trainers' => [],
                ],
                'categories' => [
                    '55f29b6d7cad3635078b457d',
                    '5512df607cad3663268b456d',
                ],
                'name' => 'Reformer Abs Arms Ass',
                'description' => '',
                'allowed_member_types' => [],
                'date_start' => new MongoDate(strtotime('2022-01-03 00:00:00')),
                'date_finish' => new MongoDate(strtotime('2022-01-10 00:00:00')),
                'pricing' => 'SINGLE',
                'modified' => new MongoDate(strtotime('2022-01-19 08:41:49')),
                'created' => new MongoDate(strtotime('2021-12-22 09:22:54')),
                'metadata' => [
                    'classpass' => [
                        'size' => 0,
                    ],
                ],
            ]
        ];
    }

    private function addProgramForCi1263UseCase(): array
    {
        return [
            [
                '_id' => new MongoId('5d198f6dfa10dd3f4b688d72'),
                'private' => true,
                'email_admin' => false,
                'branch_id' => self::BRANCH_ID,
                'namespace' => self::NAMESPACE,
                'active' => true,
                'schedule' => [
                    0 => [
                        'days_week' => 0,
                        'start_time' => '8:00',
                        'end_time' => '18:00',
                        'active' => true,
                        'facility' => '5ccb4c2aed1ea1058865db35',
                        'level' => 'All Levels',
                        'trainers' => [
                            0 => '5d1c95cf16715f02fe16e819',
                        ],
                        'size' => 10,
                        'displayAdd' => true,
                        '_start_time' => '2022-02-23T13:00:00.000Z',
                        '_end_time' => '2022-02-23T23:00:00.000Z',
                        'private' => true,
                        'code' => '5ec2dcd1a2a93',
                        '_trainers' => [
                            0 => [
                                'branch_id' => self::BRANCH_ID,
                                'namespace' => self::NAMESPACE,
                                'active' => true,
                                '_id' => '5d1c95cf16715f02fe16e819',
                                'type' => 'TRAINER',
                                'categories' => [
                                ],
                                'first_name' => 'Open',
                                'last_name' => 'Gym',
                                'phone' => null,
                                'description' => null,
                                'image' => null,
                                'email' => '<EMAIL>',
                                'bookable' => false,
                                '_pattern' => [
                                    'allowed_member_types' => null,
                                ],
                                'login' => '<EMAIL>',
                                'modified' => '2019-07-03 11:47:28',
                                'created' => '2019-07-03 11:47:27',
                                'lead_status' => 'WARM',
                                'name' => 'Open Gym',
                                'source' => 'DASHBOARD',
                                'user_id' => '5d1c95cf16715f02fe16e819',
                                'device_os' => null,
                                'device_app_version' => null,
                            ],
                        ],
                        '_facility' => '5ccb4c2aed1ea1058865db35',
                        '_level' => 'All Levels',
                    ],
                    1 => [
                        'days_week' => 1,
                        'start_time' => '05:30',
                        'end_time' => '21:00',
                        'active' => true,
                        'facility' => '5ccb4c2aed1ea1058865db35',
                        'level' => 'All Levels',
                        'trainers' => [
                            0 => '5d1c95cf16715f02fe16e819',
                        ],
                        'size' => 10,
                        'displayAdd' => true,
                        '_start_time' => '2022-02-23T10:30:00.000Z',
                        '_end_time' => '2022-02-24T02:00:00.000Z',
                        'private' => true,
                        'code' => '5ec2dcd1a2a99',
                        '_trainers' => [
                            0 => [
                                'branch_id' => self::BRANCH_ID,
                                'namespace' => self::NAMESPACE,
                                'active' => true,
                                '_id' => '5d1c95cf16715f02fe16e819',
                                'type' => 'TRAINER',
                                'categories' => [
                                ],
                                'first_name' => 'Open',
                                'last_name' => 'Gym',
                                'phone' => null,
                                'description' => null,
                                'image' => null,
                                'email' => '<EMAIL>',
                                'bookable' => false,
                                '_pattern' => [
                                    'allowed_member_types' => null,
                                ],
                                'login' => '<EMAIL>',
                                'modified' => '2019-07-03 11:47:28',
                                'created' => '2019-07-03 11:47:27',
                                'lead_status' => 'WARM',
                                'name' => 'Open Gym',
                                'source' => 'DASHBOARD',
                                'user_id' => '5d1c95cf16715f02fe16e819',
                                'device_os' => null,
                                'device_app_version' => null,
                            ],
                        ],
                        '_facility' => '5ccb4c2aed1ea1058865db35',
                        '_level' => 'All Levels',
                    ],
                    2 => [
                        'days_week' => 2,
                        'start_time' => '05:30',
                        'end_time' => '21:00',
                        'active' => true,
                        'facility' => '5ccb4c2aed1ea1058865db35',
                        'level' => 'All Levels',
                        'trainers' => [
                            0 => '5d1c95cf16715f02fe16e819',
                        ],
                        'size' => 10,
                        'displayAdd' => true,
                        '_start_time' => '2022-02-23T10:30:00.000Z',
                        '_end_time' => '2022-02-24T02:00:00.000Z',
                        'private' => true,
                        'code' => '5ec2dcd1a2a9b',
                        '_trainers' => [
                            0 => [
                                'branch_id' => self::BRANCH_ID,
                                'namespace' => self::NAMESPACE,
                                'active' => true,
                                '_id' => '5d1c95cf16715f02fe16e819',
                                'type' => 'TRAINER',
                                'categories' => [
                                ],
                                'first_name' => 'Open',
                                'last_name' => 'Gym',
                                'phone' => null,
                                'description' => null,
                                'image' => null,
                                'email' => '<EMAIL>',
                                'bookable' => false,
                                '_pattern' => [
                                    'allowed_member_types' => null,
                                ],
                                'login' => '<EMAIL>',
                                'modified' => '2019-07-03 11:47:28',
                                'created' => '2019-07-03 11:47:27',
                                'lead_status' => 'WARM',
                                'name' => 'Open Gym',
                                'source' => 'DASHBOARD',
                                'user_id' => '5d1c95cf16715f02fe16e819',
                                'device_os' => null,
                                'device_app_version' => null,
                            ],
                        ],
                        '_facility' => '5ccb4c2aed1ea1058865db35',
                        '_level' => 'All Levels',
                    ],
                    3 => [
                        'days_week' => 3,
                        'start_time' => '05:30',
                        'end_time' => '21:00',
                        'active' => true,
                        'facility' => '5ccb4c2aed1ea1058865db35',
                        'level' => 'All Levels',
                        'trainers' => [
                            0 => '5d1c95cf16715f02fe16e819',
                        ],
                        'size' => 10,
                        'displayAdd' => true,
                        '_start_time' => '2022-02-23T10:30:00.000Z',
                        '_end_time' => '2022-02-24T02:00:00.000Z',
                        'private' => true,
                        'code' => '5ec2dcd1a2a9d',
                        '_trainers' => [
                            0 => [
                                'branch_id' => self::BRANCH_ID,
                                'namespace' => self::NAMESPACE,
                                'active' => true,
                                '_id' => '5d1c95cf16715f02fe16e819',
                                'type' => 'TRAINER',
                                'categories' => [
                                ],
                                'first_name' => 'Open',
                                'last_name' => 'Gym',
                                'phone' => null,
                                'description' => null,
                                'image' => null,
                                'email' => '<EMAIL>',
                                'bookable' => false,
                                '_pattern' => [
                                    'allowed_member_types' => null,
                                ],
                                'login' => '<EMAIL>',
                                'modified' => '2019-07-03 11:47:28',
                                'created' => '2019-07-03 11:47:27',
                                'lead_status' => 'WARM',
                                'name' => 'Open Gym',
                                'source' => 'DASHBOARD',
                                'user_id' => '5d1c95cf16715f02fe16e819',
                                'device_os' => null,
                                'device_app_version' => null,
                            ],
                        ],
                        '_facility' => '5ccb4c2aed1ea1058865db35',
                        '_level' => 'All Levels',
                    ],
                    4 => [
                        'days_week' => 4,
                        'start_time' => '05:30',
                        'end_time' => '21:00',
                        'active' => true,
                        'facility' => '5ccb4c2aed1ea1058865db35',
                        'level' => 'All Levels',
                        'trainers' => [
                            0 => '5d1c95cf16715f02fe16e819',
                        ],
                        'size' => 10,
                        'displayAdd' => true,
                        '_start_time' => '2022-02-23T10:30:00.000Z',
                        '_end_time' => '2022-02-24T02:00:00.000Z',
                        'private' => true,
                        'code' => '5ec2dcd1a2a9f',
                        '_trainers' => [
                            0 => [
                                'branch_id' => self::BRANCH_ID,
                                'namespace' => self::NAMESPACE,
                                'active' => true,
                                '_id' => '5d1c95cf16715f02fe16e819',
                                'type' => 'TRAINER',
                                'categories' => [
                                ],
                                'first_name' => 'Open',
                                'last_name' => 'Gym',
                                'phone' => null,
                                'description' => null,
                                'image' => null,
                                'email' => '<EMAIL>',
                                'bookable' => false,
                                '_pattern' => [
                                    'allowed_member_types' => null,
                                ],
                                'login' => '<EMAIL>',
                                'modified' => '2019-07-03 11:47:28',
                                'created' => '2019-07-03 11:47:27',
                                'lead_status' => 'WARM',
                                'name' => 'Open Gym',
                                'source' => 'DASHBOARD',
                                'user_id' => '5d1c95cf16715f02fe16e819',
                                'device_os' => null,
                                'device_app_version' => null,
                            ],
                        ],
                        '_facility' => '5ccb4c2aed1ea1058865db35',
                        '_level' => 'All Levels',
                    ],
                    5 => [
                        'days_week' => 5,
                        'start_time' => '05:30',
                        'end_time' => '21:00',
                        'active' => true,
                        'facility' => '5ccb4c2aed1ea1058865db35',
                        'level' => 'All Levels',
                        'trainers' => [
                            0 => '5d1c95cf16715f02fe16e819',
                        ],
                        'size' => 10,
                        'displayAdd' => true,
                        '_start_time' => '2022-02-23T10:30:00.000Z',
                        '_end_time' => '2022-02-24T02:00:00.000Z',
                        'private' => true,
                        'code' => '5ec2dcd1a2aa1',
                        '_trainers' => [
                            0 => [
                                'branch_id' => self::BRANCH_ID,
                                'namespace' => self::NAMESPACE,
                                'active' => true,
                                '_id' => '5d1c95cf16715f02fe16e819',
                                'type' => 'TRAINER',
                                'categories' => [
                                ],
                                'first_name' => 'Open',
                                'last_name' => 'Gym',
                                'phone' => null,
                                'description' => null,
                                'image' => null,
                                'email' => '<EMAIL>',
                                'bookable' => false,
                                '_pattern' => [
                                    'allowed_member_types' => null,
                                ],
                                'login' => '<EMAIL>',
                                'modified' => '2019-07-03 11:47:28',
                                'created' => '2019-07-03 11:47:27',
                                'lead_status' => 'WARM',
                                'name' => 'Open Gym',
                                'source' => 'DASHBOARD',
                                'user_id' => '5d1c95cf16715f02fe16e819',
                                'device_os' => null,
                                'device_app_version' => null,
                            ],
                        ],
                        '_facility' => '5ccb4c2aed1ea1058865db35',
                        '_level' => 'All Levels',
                    ],
                    6 => [
                        'days_week' => 6,
                        'start_time' => '8:00',
                        'end_time' => '20:00',
                        'active' => true,
                        'facility' => '5ccb4c2aed1ea1058865db35',
                        'level' => 'All Levels',
                        'trainers' => [
                            0 => '5d1c95cf16715f02fe16e819',
                        ],
                        'size' => 10,
                        'displayAdd' => true,
                        '_start_time' => '2022-02-23T13:00:00.000Z',
                        '_end_time' => '2022-02-24T01:00:00.000Z',
                        'private' => true,
                        'code' => '5ec2dcd1a2aa4',
                        '_trainers' => [
                            0 => [
                                'branch_id' => self::BRANCH_ID,
                                'namespace' => self::NAMESPACE,
                                'active' => true,
                                '_id' => '5d1c95cf16715f02fe16e819',
                                'type' => 'TRAINER',
                                'categories' => [
                                ],
                                'first_name' => 'Open',
                                'last_name' => 'Gym',
                                'phone' => null,
                                'description' => null,
                                'image' => null,
                                'email' => '<EMAIL>',
                                'bookable' => false,
                                '_pattern' => [
                                    'allowed_member_types' => null,
                                ],
                                'login' => '<EMAIL>',
                                'modified' => '2019-07-03 11:47:28',
                                'created' => '2019-07-03 11:47:27',
                                'lead_status' => 'WARM',
                                'name' => 'Open Gym',
                                'source' => 'DASHBOARD',
                                'user_id' => '5d1c95cf16715f02fe16e819',
                                'device_os' => null,
                                'device_app_version' => null,
                            ],
                        ],
                        '_facility' => '5ccb4c2aed1ea1058865db35',
                        '_level' => 'All Levels',
                    ],
                ],
                'schedule_default' => [
                    'facility' => '5ccb4c2aed1ea1058865db35',
                    'trainers' => [
                        0 => '5d1c95cf16715f02fe16e819',
                    ],
                    'size' => 10,
                    'level' => 'All Levels',
                    '_trainers' => [
                        0 => [
                            'branch_id' => self::BRANCH_ID,
                            'namespace' => self::NAMESPACE,
                            'active' => true,
                            '_id' => '5d1c95cf16715f02fe16e819',
                            'type' => 'TRAINER',
                            'categories' => [
                            ],
                            'first_name' => 'Open',
                            'last_name' => 'Gym',
                            'phone' => null,
                            'description' => null,
                            'image' => null,
                            'email' => '<EMAIL>',
                            'bookable' => false,
                            '_pattern' => [
                                'allowed_member_types' => null,
                            ],
                            'login' => '<EMAIL>',
                            'modified' => '2021-03-19 15:36:27',
                            'created' => '2019-07-03 11:47:27',
                            'lead_status' => 'WARM',
                            'name' => 'Open Gym',
                            'source' => 'DASHBOARD',
                            'user_id' => '5d1c95cf16715f02fe16e819',
                            'device_os' => null,
                            'device_app_version' => null,
                        ],
                    ],
                    '_facility' => [
                        'branch_id' => self::BRANCH_ID,
                        'namespace' => self::NAMESPACE,
                        'active' => true,
                        '_id' => '5ccb4c2aed1ea1058865db35',
                        'categories' => [
                            0 => '5a33ee977af829acf2f3d240',
                        ],
                        'name' => 'Open Gym',
                        'description' => 'Open Gym',
                        'list_visible' => true,
                        'bookable' => false,
                        'modified' => [
                            'sec' => 1_556_827_178,
                            'usec' => 896000,
                        ],
                        'created' => [
                            'sec' => 1_556_827_178,
                            'usec' => 896000,
                        ],
                        'is_online' => false,
                    ],
                    '_level' => 'All Levels',
                ],
                'categories' => [
                    0 => '559bd74b7cad369fad8b4573',
                ],
                'name' => 'Open Gym',
                'description' => 'Open Gym at BODYZONE',
                'allowed_member_types' => [
                    0 => [
                        'price' => 15,
                        'memberships' => [
                            0 => [
                                'id' => 0,
                                'label' => 'PAYG',
                            ],
                        ],
                        'type' => 'payg',
                    ],
                    1 => [
                        'price' => 15,
                        'memberships' => [
                            0 => [
                                'id' => '5ccb4714040f1204ca7ebac6',
                                'label' => 'Day & Week Passes',
                            ],
                        ],
                        'type' => 'member',
                        'membership_id' => '5ccb4714040f1204ca7ebac6',
                    ],
                ],
                'date_start' => new MongoDate(strtotime('2019-06-30 01:00:00')),
                'date_finish' => null,
                'pricing' => 'CUSTOM',
                'modified' => new MongoDate(strtotime('2022-02-23 18:07:18')),
                'created' => new MongoDate(strtotime('2019-07-01 05:43:25')),
                'metadata' => [
                ],
            ]
        ];
    }

    private function addProgramForBookingsControllerDateRangeTest(): array
    {
        return [
            [
                '_id' => new MongoId('63e0ca85ef7ce00f799d1c65'),
                'branch_id' => self::BRANCH_ID,
                'namespace' => self::NAMESPACE,
                'active' => true,
                'schedule' => [
                    [
                        'days_week' => 0,
                        'start_time' => '8:00',
                        'end_time' => '09:00',
                        'active' => true,
                        'facility' => ['52a7011a05c677bda826611b'],
                        'level' => 'advanced',
                        'trainers' => ['59a7011a05c677bda916612c'],
                        'size' => 100,
                        'code' => '5856c03455ff0',
                    ],
                    [
                        'days_week' => 1,
                        'start_time' => '8:00',
                        'end_time' => '09:00',
                        'active' => true,
                        'facility' => ['52a7011a05c677bda826611b'],
                        'level' => 'advanced',
                        'trainers' => ['59a7011a05c677bda916612c'],
                        'size' => 100,
                        'code' => '5856c03455ff1',
                    ],
                    [
                        'days_week' => 2,
                        'start_time' => '8:00',
                        'end_time' => '09:00',
                        'active' => true,
                        'facility' => ['52a7011a05c677bda826611b'],
                        'level' => 'advanced',
                        'trainers' => ['59a7011a05c677bda916612c'],
                        'size' => 100,
                        'code' => '5856c03455ff2',
                    ],
                    [
                        'days_week' => 3,
                        'start_time' => '8:00',
                        'end_time' => '09:00',
                        'active' => true,
                        'facility' => ['52a7011a05c677bda826611b'],
                        'level' => 'advanced',
                        'trainers' => ['59a7011a05c677bda916612c'],
                        'size' => 100,
                        'code' => '5856c03455ff3',
                    ],
                    [
                        'days_week' => 4,
                        'start_time' => '8:00',
                        'end_time' => '09:00',
                        'active' => true,
                        'facility' => ['52a7011a05c677bda826611b'],
                        'level' => 'advanced',
                        'trainers' => ['59a7011a05c677bda916612c'],
                        'size' => 100,
                        'code' => '5856c03455ff4',
                    ],
                    [
                        'days_week' => 5,
                        'start_time' => '8:00',
                        'end_time' => '09:00',
                        'active' => true,
                        'facility' => ['52a7011a05c677bda826611b'],
                        'level' => 'advanced',
                        'trainers' => ['59a7011a05c677bda916612c'],
                        'size' => 100,
                        'code' => '5856c03455ff5',
                    ],
                    [
                        'days_week' => 6,
                        'start_time' => '8:00',
                        'end_time' => '09:00',
                        'active' => true,
                        'facility' => ['52a7011a05c677bda826611b'],
                        'level' => 'advanced',
                        'trainers' => ['59a7011a05c677bda916612c'],
                        'size' => 100,
                        'code' => '5856c03455ff6',
                    ],
                ],
                'schedule_default' => [
                    'facility' => ['52a7011a05c677bda826611b'],
                    'trainers' => ['59a7011a05c677bda916612c'],
                    'size' => 100,
                    'level' => 'advanced',
                    '_level' => 'advanced',
                    '_trainers' => [
                        [
                            'branch_id' => '53fb0a22fe25f0c84c8b4575',
                            'namespace' => self::NAMESPACE,
                            'active' => true,
                            '_id' => '58568a8fa875ab19530041a7',
                            'categories' => [],
                            'type' => 'TRAINER',
                            'first_name' => 'Timmy',
                            'last_name' => 'Fisher',
                            'phone' => '0899991827',
                            'description' => 'Worst Trainer Ever!',
                            'email' => '<EMAIL>',
                            'password' => 'e10adc3949ba59abbe56e057f20f883e',
                            'bookable' => false,
                            'login' => '<EMAIL>',
                            'name' => 'Timmy Fisher',
                        ],
                    ],
                ],
                'categories' => [],
                'name' => 'Test',
                'description' => 'Class',
                'allowed_member_types' => [
                    [
                        'price' => 0.0,
                        'memberships' => [
                            [
                                'id' => 0,
                                'label' => 'PAYG',
                            ],
                        ],
                        'type' => 'payg',
                    ],
                    [
                        'price' => 0.0,
                        'memberships' => [
                            [
                                'id' => 0,
                                'label' => 'MEMBER',
                            ],
                        ],
                        'type' => 'member',
                    ],
                ],
                'date_start' => new MongoDate(strtotime('2020-01-01 00:00:00')),
                'date_finish' => null,
                'email_admin' => false,
                'private' => false,
                'pricing' => 'CUSTOM',
                'metadata' => [
                    'gympass' => false,
                ],
            ],
        ];
    }

    private function forVirtualSlotTest(): array
    {
        return [
            [
                '_id' => new MongoId('64620b36de9120be8b00ed33'),
                'branch_id' => static::VIRTUAL_SLOT_BRANCH_ID,
                'namespace' => static::VIRTUAL_SLOT_BRANCH_NAMESPACE,
                'active' => true,
                'schedule' => [
                    [
                        'days_week' => 4,
                        'start_time' => '08:00',
                        'end_time' => '09:00',
                        'active' => true,
                        'facility' => [static::VIRTUAL_SLOT_FACILITY_ID],
                        'level' => 'advanced',
                        'trainers' => [static::VIRTUAL_SLOT_STAFF_ID],
                        'size' => 100,
                        'code' => '9856c03455ff5',
                    ],
                ],
                'schedule_default' => [
                    'facility' => [static::VIRTUAL_SLOT_FACILITY_ID],
                    'trainers' => [static::VIRTUAL_SLOT_STAFF_ID],
                    'size' => 100,
                    'level' => 'advanced',
                    '_level' => 'advanced',
                    '_trainers' => [
                        [
                            'branch_id' => static::VIRTUAL_SLOT_BRANCH_ID,
                            'namespace' => static::VIRTUAL_SLOT_BRANCH_NAMESPACE,
                            'active' => true,
                            '_id' => static::VIRTUAL_SLOT_STAFF_ID,
                            'categories' => [],
                            'type' => 'TRAINER',
                            'first_name' => 'Random',
                            'last_name' => 'Trainer',
                            'name' => 'Random Trainer',
                            'phone' => '0899991827',
                            'description' => 'Random Trainer for testing',
                            'email' => '<EMAIL>',
                            'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                            'login' => '<EMAIL>',
                            'bookable' => true,
                        ],
                    ],
                ],
                'categories' => [],
                'name' => 'Test virtual slot class',
                'description' => 'Class for testing virtual slots',
                'allowed_member_types' => [
                    [
                        'price' => 0.0,
                        'memberships' => [
                            [
                                'id' => 0,
                                'label' => 'PAYG',
                            ],
                        ],
                        'type' => 'payg',
                    ],
                    [
                        'price' => 0.0,
                        'memberships' => [
                            [
                                'id' => 0,
                                'label' => 'MEMBER',
                            ],
                        ],
                        'type' => 'member',
                    ],
                ],
                'date_start' => new MongoDate(strtotime('2023-04-28 00:00:00')),
                'date_finish' => new MongoDate(strtotime('2023-04-28 23:59:59')),
                'email_admin' => false,
                'private' => false,
                'pricing' => 'CUSTOM',
                'metadata' => [
                    'gympass' => true,
                ],
            ],
        ];
    }

    private function forBookingWindowTest(): array
    {
        return [
            $this->createBase(
                [
                    '_id' => '6477520fb95e96bf7792d62d',
                    'branch_id' => '64784ace2c8a824784ab4cc4',
                    'namespace' => 'classbookingwindow',
                    'name' => 'Class to test booking window',
                    'pricing' => 'SINGLE',
                    'allowed_member_types' => [
                        ['price' => 20, 'type' => 'member'],
                        ['price' => 30, 'type' => 'payg'],
                    ],
                    'default_price' => 20,
                ]
            ),
        ];
    }

    private function forPayrollRatesTest(): array
    {
        return [
            $this->createBase(
                [
                    '_id' => '64b7ec83ca6e0791480419d3',
                    'branch_id' => '64b803ccfc8ba4d6f8d99c9c',
                    'namespace' => 'payroll',
                    'name' => 'Payroll Class',
                    'pricing' => 'SINGLE',
                    'allowed_member_types' => [
                        ['price' => 20, 'type' => 'member'],
                        ['price' => 30, 'type' => 'payg'],
                    ],
                    'default_price' => 20,
                ]
            ),
            $this->createBase([
                [
                    '_id' => '650825ec1722aff51db49f82',
                    'branch_id' => '64b803ccfc8ba4d6f8d99c9c',
                    'namespace' => 'payroll',
                    'name' => 'Payroll Class 2',
                    'pricing' => 'SINGLE',
                    'allowed_member_types' => [
                        ['price' => 20, 'type' => 'member'],
                        ['price' => 30, 'type' => 'payg'],
                    ],
                    'default_price' => 20,
                ]
            ]),
            $this->createBase([
                [
                    '_id' => '657303f2ff55fc3620d426d9',
                    'branch_id' => '64b803ccfc8ba4d6f8d99c9c',
                    'namespace' => 'payroll',
                    'name' => 'Payroll Class 3',
                    'pricing' => 'SINGLE',
                    'allowed_member_types' => [
                        ['price' => 20, 'type' => 'member'],
                        ['price' => 30, 'type' => 'payg'],
                    ],
                    'default_price' => 20,
                ]
            ]),
        ];
    }

    private function forClassPassOverBooking(): array
    {
        return [
            $this->createBase(
                [
                    '_id' => '6477520fb96e96bf7792d62d',
                    'branch_id' => '64784ace2c8a824784ab4cc4',
                    'namespace' => 'classpassoverbooking',
                    'name' => 'Class to test classpass overbooking',
                    'pricing' => 'SINGLE',
                    'allowed_member_types' => [
                        ['price' => 20, 'type' => 'member'],
                        ['price' => 30, 'type' => 'payg'],
                    ],
                    'default_price' => 20,
                    'metadata' => [
                        'classpass' => [
                            'size' => 1,
                        ],
                    ],
                ]
            ),
        ];
    }

    /**
     * @throws MongoException
     */
    private function forRecurringClassBookings(): array
    {
        return [
            $this->createBase(
                [
                    '_id' => '6477520fb96e96bf7792d63d',
                    'branch_id' => self::BRANCH_ID,
                    'namespace' => self::NAMESPACE,
                    'active' => true,
                    'name' => 'Recurring Class',
                    'pricing' => 'CUSTOM',
                    'allowed_member_types' => [
                        [
                            'price' => 0.0,
                            'memberships' => [
                                [
                                    'id' => '5b19486ac13acae3b31cf563',
                                    'label' => 'MEMBER',
                                ],
                            ],
                            'type' => 'member',
                        ],
                        [
                            'price' => 0.0,
                            'memberships' => [
                                [
                                    'id' => self::MEMBERSHIP_ID,
                                    'label' => 'MEMBER',
                                ],
                            ],
                            'type' => 'member',
                        ],
                        ['price' => 10, 'type' => 'payg'],
                    ],
                    'default_price' => 20,
                    'schedule' => [
                        [
                            'days_week' => Carbon::tomorrow()->dayOfWeek,
                            'start_time' => '08:00',
                            'end_time' => '09:00',
                            'active' => true,
                            'facility' => ['52a7011a05c677bda826611b'],
                            'level' => 'advanced',
                            'trainers' => ['59a7011a05c677bda916612c'],
                            'size' => 100,
                            'code' => '5833c03455fs1',
                        ],
                        [
                            'days_week' => Carbon::tomorrow()->addDays(1)->dayOfWeek,
                            'start_time' => '08:00',
                            'end_time' => '09:00',
                            'active' => true,
                            'facility' => ['52a7011a05c677bda826611b'],
                            'level' => 'advanced',
                            'trainers' => ['59a7011a05c677bda916612c'],
                            'size' => 100,
                            'code' => '5833c03455fs2',
                        ],
                        [
                            'days_week' => Carbon::tomorrow()->addDays(2)->dayOfWeek,
                            'start_time' => '08:00',
                            'end_time' => '09:00',
                            'active' => true,
                            'facility' => ['52a7011a05c677bda826611b'],
                            'level' => 'advanced',
                            'trainers' => ['59a7011a05c677bda916612c'],
                            'size' => 100,
                            'code' => '5833c03455fs3',
                        ]
                    ],
                    'schedule_default' => [
                        'size' => 100,
                    ],
                ]
            ),
            $this->createBase(
                [
                    '_id' => '6477520fb96e96bf7792d63e',
                    'branch_id' => self::BRANCH_ID,
                    'namespace' => self::NAMESPACE,
                    'active' => true,
                    'name' => 'Recurring Class II',
                    'pricing' => 'CUSTOM',
                    'allowed_member_types' => [
                        [
                            'price' => 0.0,
                            'memberships' => [
                                [
                                    'id' => self::MEMBERSHIP_ID,
                                    'label' => 'MEMBER',
                                ],
                            ],
                            'type' => 'member',
                        ],
                        ['price' => 10, 'type' => 'payg'],
                    ],
                    'default_price' => 20,
                    'schedule' => [
                        [
                            'days_week' => Carbon::tomorrow()->dayOfWeek,
                            'start_time' => '08:00',
                            'end_time' => '09:00',
                            'active' => true,
                            'facility' => ['52a7011a05c677bda826611b'],
                            'level' => 'advanced',
                            'trainers' => ['59a7011a05c677bda916612c'],
                            'size' => 100,
                            'code' => '5833c03455fs4',
                        ]
                    ],
                    'schedule_default' => [
                        'size' => 100,
                    ],
                ]
            ),
        ];
    }

    /**
     * @throws MongoException
     */
    private function forActiveLimitBookings(): array
    {
        return [
            $this->createBase(
                [
                    '_id' => '6672862317c4f80ebdb0ea51',
                    'branch_id' => self::BRANCH_ID,
                    'namespace' => self::NAMESPACE,
                    'active' => true,
                    'name' => 'Active Limit Bookings Class',
                    'pricing' => 'CUSTOM',
                    'allowed_member_types' => [
                        [
                            'price' => 0.0,
                            'memberships' => [
                                [
                                    'id' => '5b19486ac13acae3b31cf563',
                                    'label' => 'MEMBER',
                                ],
                            ],
                            'type' => 'member',
                        ],
                        [
                            'price' => 0.0,
                            'memberships' => [
                                [
                                    'id' => self::MEMBERSHIP_ID,
                                    'label' => 'MEMBER',
                                ],
                            ],
                            'type' => 'member',
                        ],
                        ['price' => 0, 'type' => 'payg'],
                    ],
                    'default_price' => 0,
                    'schedule' => [
                        [
                            'days_week' => Carbon::yesterday()->dayOfWeek,
                            'start_time' => '08:00',
                            'end_time' => '09:00',
                            'active' => true,
                            'facility' => ['52a7011a05c677bda826611b'],
                            'level' => 'advanced',
                            'trainers' => ['59a7011a05c677bda916612c'],
                            'size' => 100,
                            'code' => '5833c03455fs5',
                        ],
                        [
                            'days_week' => Carbon::today()->subDays(3)->dayOfWeek,
                            'start_time' => '08:00',
                            'end_time' => '09:00',
                            'active' => true,
                            'facility' => ['52a7011a05c677bda826611b'],
                            'level' => 'advanced',
                            'trainers' => ['59a7011a05c677bda916612c'],
                            'size' => 100,
                            'code' => '5833c03455fs6',
                        ],
                        [
                            'days_week' => Carbon::today()->subDays(5)->dayOfWeek,
                            'start_time' => '08:00',
                            'end_time' => '09:00',
                            'active' => true,
                            'facility' => ['52a7011a05c677bda826611b'],
                            'level' => 'advanced',
                            'trainers' => ['59a7011a05c677bda916612c'],
                            'size' => 100,
                            'code' => '5833c03455fs7',
                        ],
                        [
                            'days_week' => Carbon::tomorrow()->dayOfWeek,
                            'start_time' => '08:00',
                            'end_time' => '09:00',
                            'active' => true,
                            'facility' => ['52a7011a05c677bda826611b'],
                            'level' => 'advanced',
                            'trainers' => ['59a7011a05c677bda916612c'],
                            'size' => 100,
                            'code' => '5833c03455fs8',
                        ],
                        [
                            'days_week' => Carbon::today()->addDays(3)->dayOfWeek,
                            'start_time' => '08:00',
                            'end_time' => '09:00',
                            'active' => true,
                            'facility' => ['52a7011a05c677bda826611b'],
                            'level' => 'advanced',
                            'trainers' => ['59a7011a05c677bda916612c'],
                            'size' => 100,
                            'code' => '5833c03455fs9',
                        ],
                        [
                            'days_week' => Carbon::today()->addDays(5)->dayOfWeek,
                            'start_time' => '08:00',
                            'end_time' => '09:00',
                            'active' => true,
                            'facility' => ['52a7011a05c677bda826611b'],
                            'level' => 'advanced',
                            'trainers' => ['59a7011a05c677bda916612c'],
                            'size' => 100,
                            'code' => '5833c03455ft1',
                        ],
                        [
                            'days_week' => Carbon::tomorrow()->addDays(6)->dayOfWeek,
                            'start_time' => '08:00',
                            'end_time' => '09:00',
                            'active' => true,
                            'facility' => ['52a7011a05c677bda826611b'],
                            'level' => 'advanced',
                            'trainers' => ['59a7011a05c677bda916612c'],
                            'size' => 100,
                            'code' => '5833c03455ft2',
                        ],
                    ],
                    'schedule_default' => [
                        'size' => 100,
                    ],
                ]
            ),
        ];
    }

    private function forOverBookingForceSizeChange(): array
    {
        return [
            $this->createBase(
                [
                    '_id' => '6477520fb96e96bf7792d62a',
                    'branch_id' => '646de8d3c539cfc3cb649547',
                    'namespace' => 'appointmentsbookingwindow',
                    'name' => 'Forced Overbooked Event',
                    'pricing' => 'SINGLE',
                    'allowed_member_types' => [
                        ['price' => 20, 'type' => 'member'],
                        ['price' => 30, 'type' => 'payg'],
                    ],
                    'default_price' => 20
                ]
            ),
        ];
    }

    /**
     * @throws MongoException
     */
    private function forIntegrationTests(): array
    {
        return [
            $this->createBase(
                [
                    '_id' => '6672862317c4f80ebdb0ea52',
                    'branch_id' => self::BRANCH_ID,
                    'namespace' => self::NAMESPACE,
                    'active' => true,
                    'name' => 'Integration Test Class',
                    'pricing' => 'CUSTOM',
                    'allowed_member_types' => [
                        [
                            'price' => 0.0,
                            'memberships' => [
                                [
                                    'id' => '5b19486ac13acae3b31cf563',
                                    'label' => 'MEMBER',
                                ],
                            ],
                            'type' => 'member',
                        ],
                        [
                            'price' => 0.0,
                            'memberships' => [
                                [
                                    'id' => self::MEMBERSHIP_ID,
                                    'label' => 'MEMBER',
                                ],
                            ],
                            'type' => 'member',
                        ],
                        ['price' => 25, 'type' => 'payg'],
                    ],
                    'default_price' => 40,
                    'schedule' => [
                        [
                            'days_week' => Carbon::yesterday()->dayOfWeek,
                            'start_time' => '08:00',
                            'end_time' => '09:00',
                            'active' => true,
                            'facility' => ['52a7011a05c677bda826611b'],
                            'level' => 'advanced',
                            'trainers' => ['59a7011a05c677bda916612c'],
                            'size' => 100,
                            'code' => '5833c03455fs5',
                        ],
                    ],
                    'schedule_default' => [
                        'size' => 100,
                    ],
                    'metadata' => [
                        'classpass' => [
                            'size' => 10,
                        ]
                    ],
                ]
            ),
        ];
    }

    private function forRestrictedMembershipNoCreditsNextCycle(): array
    {
        return [
            $this->createBase([
                '_id' => '6672862317c4f80ebdb0ea53',
                'branch_id' => self::BRANCH_ID,
                'namespace' => self::NAMESPACE,
                'active' => true,
                'name' => 'Test Class',
                'pricing' => 'CUSTOM',
                'allowed_member_types' => [
                    [
                        'price' => 0.0,
                        'memberships' => [
                            [
                                'id' => '5f159e696222aaa4d0ee4ce5',
                                'label' => 'MEMBER',
                            ],
                        ],
                        'type' => 'member',
                    ],
                ],
                'date_start' => new MongoDate(Carbon::today()->addMonths(1)->getTimestamp()),
            ])
        ];
    }

    private function forGympassUpdate(): array
    {
        return [
            $this->createBase([
                '_id' => '6672862317c4f80ebdb0e154',
                'branch_id' => self::BRANCH_ID,
                'namespace' => self::NAMESPACE,
                'active' => true,
                'name' => 'Test Class',
                'pricing' => 'CUSTOM',
                'allowed_member_types' => [
                    [
                        'price' => 0.0,
                        'memberships' => [
                            [
                                'id' => '5f159e696222aaa4d0ee4ce5',
                                'label' => 'MEMBER',
                            ],
                        ],
                        'type' => 'member',
                    ],
                ],
                'date_start' => new MongoDate(Carbon::today()->addMonths(1)->getTimestamp()),
                'metadata' => [
                    'gympass' => true,
                ],
            ])
        ];
    }

    private function forEventsUpdate(): array
    {
        return [
            $this->createBase(
                [
                    '_id' => '674261b978cfad5769eadf3a',
                    'branch_id' => '67425b982a70f7afae14dc75',
                    'namespace' => 'events-update-plus-utc',
                    'active' => true,
                    'name' => 'Events Update Plus UTC',
                    'pricing' => 'CUSTOM',
                    'allowed_member_types' => [
                        [
                            'price' => 0.0,
                            'memberships' => [
                                [
                                    'id' => self::MEMBERSHIP_ID,
                                    'label' => 'MEMBER',
                                ],
                            ],
                            'type' => 'member',
                        ],
                        ['price' => 25, 'type' => 'payg'],
                    ],
                    'default_price' => 20,
                    'schedule' => [
                        [
                            'days_week' => 3,
                            'start_time' => '08:00',
                            'end_time' => '09:00',
                            'active' => true,
                            'facility' => ['674262905c91aea9b550c9c7'],
                            'level' => 'advanced',
                            'trainers' => ['67425eecfc4508dbeff26724'],
                            'size' => 10,
                            'code' => '5866c03455fs4',
                        ]
                    ],
                    'schedule_default' => [
                        'size' => 10,
                    ],
                ]
            ),
            $this->createBase(
                [
                    '_id' => '674261be71678f5e860b3eee',
                    'branch_id' => '67425ec9a14b1b40651b4990',
                    'namespace' => 'events-update-minus-utc',
                    'active' => true,
                    'name' => 'Events Update Minus UTC',
                    'pricing' => 'CUSTOM',
                    'allowed_member_types' => [
                        [
                            'price' => 0.0,
                            'memberships' => [
                                [
                                    'id' => self::MEMBERSHIP_ID,
                                    'label' => 'MEMBER',
                                ],
                            ],
                            'type' => 'member',
                        ],
                        ['price' => 10, 'type' => 'payg'],
                    ],
                    'default_price' => 20,
                    'schedule' => [
                        [
                            'days_week' => 3,
                            'start_time' => '08:00',
                            'end_time' => '09:00',
                            'active' => true,
                            'facility' => [self::FACILITY_ID],
                            'level' => 'advanced',
                            'trainers' => [self::TRAINER_ID],
                            'size' => 10,
                            'code' => '5833c03665fs4',
                        ]
                    ],
                    'schedule_default' => [
                        'size' => 10,
                    ],
                ]
            ),
        ];
    }

    private function forProgramsUpdate(): array
    {
        return [
            $this->createBase(
                [
                    '_id' => '6672862317c4f80ebdb0e165',
                    'branch_id' => self::BRANCH_ID,
                    'namespace' => 'glofox',
                    'active' => true,
                    'name' => 'Program Update Test',
                    'description' => 'Program Update Test Description',
                    'pricing' => 'CUSTOM',
                    'default_price' => 20,
                    'date_start' => new MongoDate((new Carbon('06-05-2024','UTC'))->getTimestamp()),
                    'date_finish' => null,
                    'schedule' => [
                        [
                            'days_week' => 3,
                            'start_time' => '08:00',
                            'end_time' => '09:00',
                            'active' => true,
                            'facility' => self::FACILITY_ID,
                            'level' => 'advanced',
                            'trainers' => [self::TRAINER_ID],
                            'size' => 10,
                            'code' => '5833c03665fs4',
                        ]
                    ],
                    'schedule_default' => [
                        'size' => 10,
                    ],
                    'categories' => [
                        '56c1d609639cd18a87c0b854',
                    ],
                    'allowed_member_types' => [
                        [
                            'price' => 0.0,
                            'memberships' => [
                                [
                                    'label' => 'PAYG',
                                ],
                            ],
                            'type' => 'payg',
                        ],
                    ],
                    'metadata' => [
                        'gympass' => false,
                    ],
                    'private' => false
                ]
            ),
        ];
    }

    private function forProgramsUpdateEventGenerator(): array
    {
        return [
            $this->createBase(
                [
                    '_id' => '677d35c0ca57fd9d73e9dbb8',
                    'branch_id' => self::BRANCH_ID,
                    'namespace' => 'glofox',
                    'active' => true,
                    'name' => 'Program Update Event Generator Test',
                    'description' => 'Program Update Event Generator Test Description',
                    'pricing' => 'CUSTOM',
                    'default_price' => 30,
                    'date_start' => new MongoDate((new Carbon('01-01-2024','UTC'))->getTimestamp()),
                    'date_finish' => new MongoDate((new Carbon('25-01-2024','UTC'))->getTimestamp()),
                    'schedule' => [
                        [
                            'days_week' => 2,
                            'start_time' => '12:00',
                            'end_time' => '13:00',
                            'active' => true,
                            'facility' => self::FACILITY_ID,
                            'level' => 'advanced',
                            'trainers' => [self::TRAINER_ID],
                            'size' => 2,
                            'code' => '5211b01775et1',
                        ]
                    ],
                    'schedule_default' => [
                        'size' => 2,
                    ],
                    'categories' => [
                        '56c1d609639cd18a87c0b854',
                    ],
                    'allowed_member_types' => [
                        [
                            'price' => 0.0,
                            'memberships' => [
                                [
                                    'label' => 'PAYG',
                                ],
                            ],
                            'type' => 'payg',
                        ],
                    ],
                    'metadata' => [
                        'gympass' => false,
                    ],
                ]
            ),
          ];
    }

    private function forEventGeneratorComponent(): array
    {
        $common = $this->getCommonBaseDetails();
        $scheduleCommonDetails = $this->getScheduleCommonDetails();

        return [
            $this->createBase(array_merge($common,[
                '_id' => new MongoId("6672862317c4f80ebdb0e166"),
                'branch_id' => '675acdcdf1aef12a46e66bcd',
                'name' => 'Class with no deleted events in the future',
                'schedule' => [
                    $scheduleCommonDetails
                ],
            ])),
            $this->createBase(array_merge($common,[
                '_id' => new MongoId("6672862317c4f80ebdb0e167"),
                'branch_id' => '675acdcdf1aef12a46e66bce',
                'name' => 'Class with a manually deleted event in the future',
                'schedule' => [
                    array_merge($scheduleCommonDetails, ['code' => '80ebdb0e001']),
                ],
            ])),
            $this->createBase(array_merge($common,[
                '_id' => new MongoId("6672862317c4f80ebdb0e168"),
                'branch_id' => '675acdcdf1aef12a46e66bcf',
                'name' => 'Class with a soft deleted event in the future',
                'schedule' => [
                    array_merge($scheduleCommonDetails, ['code' => '80ebdb0e002']),
                ],
            ])),
            $this->createBase(array_merge($common,[
                '_id' => new MongoId("6672862317c4f80ebdb0e169"),
                'branch_id' => '675acdcdf1aef12a46e66bd0',
                'name' => 'Class with a manually deleted event beyond programs end date',
                'schedule' => [
                    array_merge($scheduleCommonDetails, ['code' => '80ebdb0e003']),
                ],
            ])),
            $this->createBase(array_merge($common,[
                '_id' => new MongoId("6672862317c4f80ebdb0e16a"),
                'branch_id' => '675acdcdf1aef12a46e66bd1',
                'name' => 'Class with a soft deleted event beyond programs end date',
                'schedule' => [
                    array_merge($scheduleCommonDetails, ['code' => '80ebdb0e004']),
                ],
            ])),
        ];
    }

    private function forProgramUpsertService(): array
    {
        $common = $this->getCommonBaseDetails();
        $scheduleCommonDetails = $this->getScheduleCommonDetails();
        $scheduleCommonDetails['start_time'] = '10:00';
        $scheduleCommonDetails['end_time'] = '11:00';
        // In normal circumstances this should be de array passed to createBase method,
        // but it does not return the 'spot_booking_enabled' field and it is inserted after the first upsert
        // resulting in a comparison error
        $programDetails = array_merge($common,[
            '_id' => new MongoId("6672862317c4f80ebdb0e16b"),
            'branch_id' => '675acdcdf1aef12a46e66bd2',
            'name' => 'Class to test integral updates',
            'date_start' => new MongoDate((new Carbon('2023-12-04','UTC'))->getTimestamp()),
            'schedule' => [
                array_merge($scheduleCommonDetails, ['code' => '80ebdb0e005']),
                array_merge($scheduleCommonDetails, [
                    'code' => '80ebdb0e006',
                    'days_week' => 3,
                    'size' => 20,
                    'private' => true
                ]),
            ],
        ]);
        return [
            array_merge($this->createBase([]), $programDetails),
        ];
    }

    private function forFacilityDeleteCheck(): array
    {
        $common = $this->getCommonBaseDetails();
        $scheduleCommonDetails = $this->getScheduleCommonDetails();
        $baseDetailsFacilitySet = $common;
        $baseDetailsFacilitySet['schedule_default']['facility'] = '80ebdb0e0000000000000001';
        $scheduleFacilitySet = $scheduleCommonDetails;
        $scheduleFacilitySet['facility'] = '80ebdb0e0000000000000002';
        $baseDetailsFacilityAnotherBranchSet = $common;
        $baseDetailsFacilityAnotherBranchSet['schedule_default']['facility'] = '80ebdb0e0000000000000003';

        return [
            $this->createBase(array_merge($common, [
                '_id' => new MongoId("6672862317c4f80ebdb0e176"),
                'name' => 'Class without Facility set in base details or schedules'
            ])),
            $this->createBase(array_merge($baseDetailsFacilitySet, [
                '_id' => new MongoId("6672862317c4f80ebdb0e177"),
                'name' => 'Class with facility set in base details'
            ])),
            $this->createBase(array_merge($common, [
                '_id' => new MongoId("6672862317c4f80ebdb0e180"),
                'name' => 'Class without Facility set in base details, but it is set in a schedule',
                'schedule' => [
                    $scheduleFacilitySet
                ],
            ])),
            $this->createBase(array_merge($baseDetailsFacilityAnotherBranchSet, [
                '_id' => new MongoId("6672862317c4f80ebdb0e179"),
                'branch_id' => "49a7011a05c677b9a9166112",
                'name' => 'Class with Facility set in base details, but belongs to another branch',
            ])),
        ];
    }

    private function getCommonBaseDetails(): array
    {
        return [
            'active' => true,
            'private' => false,
            'date_start' => new MongoDate((new Carbon('2024-01-01', 'UTC'))->getTimestamp()),
            'date_finish' => null,
            'schedule_default' => [
                'facility' => '80ebdb0e0000000000000000',
                'trainers' => [
                    0 => '80ebdb0e0000000000000001',
                ],
                'size' => 20,
                'level' => 'Mixed Levels',
                '_level' => 'Mixed Levels'
            ],
            'categories' => [
                '56c1d609639cd18a87c0b854',
            ],
            'spot_booking_enabled' => false,
            'email_admin' => false,
        ];
    }

    private function getScheduleCommonDetails(): array
    {
        return [
            'days_week' => 1,
            'start_time' => '08:00',
            'end_time' => '09:00',
            'active' => true,
            'code' => '80ebdb0e000',
            'private' => false,
            'facility' => '80ebdb0e0000000000000000',
            'level' => 'advanced',
            'trainers' => ['80ebdb0e0000000000000001'],
            'size' => 10,
            'displayAdd' => false,
        ];
    }

    private function forOverdueMembershipBookings(): array
    {
        return [
            $this->createBase(
                [
                    '_id' => '6672862317c4f80ebdb0e178',
                    'branch_id' => self::BRANCH_ID,
                    'namespace' => self::NAMESPACE,
                    'active' => true,
                    'name' => 'Overdue Membership Class',
                    'description' => 'Test Description',
                    'pricing' => 'CUSTOM',
                    'default_price' => 20,
                    'date_start' => new MongoDate((new Carbon('06-05-2024','UTC'))->getTimestamp()),
                    'date_finish' => null,
                    'schedule' => [
                        [
                            'days_week' => 3,
                            'start_time' => '08:00',
                            'end_time' => '09:00',
                            'active' => true,
                            'facility' => self::FACILITY_ID,
                            'level' => 'advanced',
                            'trainers' => [self::TRAINER_ID],
                            'size' => 10,
                            'code' => '5833c03665fs4',
                        ],
                        [
                            'days_week' => 4,
                            'start_time' => '08:00',
                            'end_time' => '09:00',
                            'active' => true,
                            'facility' => self::FACILITY_ID,
                            'level' => 'advanced',
                            'trainers' => [self::TRAINER_ID],
                            'size' => 10,
                            'code' => '5833c03665fs5',
                        ],
                    ],
                    'schedule_default' => [
                        'size' => 10,
                    ],
                    'categories' => [
                        '56c1d609639cd18a87c0b854',
                    ],
                    'allowed_member_types' => [
                        [
                            'price' => 0.0,
                            'memberships' => [
                                [
                                    'label' => 'PAYG',
                                ],
                            ],
                            'type' => 'payg',
                        ],
                        [
                            'price' => 0,
                            'type' => 'member',
                            'membership_id' => '54107c1cd7b6ddc3a98b4577',
                        ],
                    ],
                    'metadata' => [
                        'gympass' => false,
                    ],
                    'private' => false
                ]
            ),
        ];
    }

    private function forNonDeleteEventsWithCanceledBookings(): array
    {
        return [
            $this->createBase(array_merge($this->getCommonBaseDetails(), [
                '_id' => new MongoId("67e508db1f2ea29d1b38cbc0"),
                'name' => 'Event with bookings, not deleted on program upsert',
                'branch_id' => self::BRANCH_ID,
                'schedule' => [
                    [
                        'days_week' => 6,
                        'start_time' => '18:00',
                        'end_time' => '19:00',
                        'active' => true,
                        'facility' => '80ebdb0e0000000000000000',
                        'level' => 'advanced',
                        'trainers' => ['80ebdb0e0000000000000001'],
                        'size' => 2,
                        'code' => '5211b01775aa2',
                    ]
                ],
            ])),
        ];
    }

    private function forCategoryAndModelValidations(): array
    {
        return [
            $this->createBase(array_merge($this->getCommonBaseDetails(), [
                '_id' => new MongoId('67e508db1f2ea29d1b38cbc1'),
                'name' => 'For category and model validation',
                'branch_id' => self::BRANCH_ID,
                'schedule' => [
                    [
                        'days_week' => 6,
                        'start_time' => '18:00',
                        'end_time' => '19:00',
                        'active' => true,
                        'facility' => '80ebdb0e0000000000000000',
                        'level' => 'advanced',
                        'trainers' => ['80ebdb0e0000000000000001'],
                        'size' => 2,
                        'code' => '5211b01775aa2',
                    ]
                ],
            ])),
        ];
    }
}
