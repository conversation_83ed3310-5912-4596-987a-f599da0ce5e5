<?php

declare(strict_types=1);

use Glofox\Domain\Bookings\Repositories\BookingsRepository;
use Glofox\Domain\Bookings\Status;
use Glofox\Domain\Cache\CacheClientInterface;
use Glofox\Infrastructure\Cache\CachePrefix;
use Glofox\Infrastructure\Cache\RedisCacheClientFactory;
use Psr\Log\LoggerInterface;

App::uses('Shell', 'Console');

class FixWrongEventCacheWaitlistShell extends Shell
{
    private BookingsRepository $bookingsRepository;
    private LoggerInterface $logger;
    private CacheClientInterface $cache;

    public function __construct()
    {
        parent::__construct();

        $this->logger = app()->make(LoggerInterface::class);
        $this->bookingsRepository = app()->make(BookingsRepository::class);

        $factory = app()->make(RedisCacheClientFactory::class);
        $this->cache = $factory->create(new CachePrefix(CachePrefix::BOOKING_EVENT_LOCKER));
    }

    public function getOptionParser(): ConsoleOptionParser
    {
        $parser = parent::getOptionParser();

        $parser->addOption('eventId', [
            'short' => 'e',
            'help' => 'Event ID',
            'required' => true
        ]);

        return $parser;
    }

    public function main(): void
    {
        $this->logger->info('[FixWrongEventCacheWaitlistShell] Started');

        try {
            $eventId = $this->param('eventId');
            $cacheKey = $eventId . '-' . Status::WAITING;

            $databaseWaitingBookings = $this->getWaitlistBookings($eventId);
            $cacheWaitingBookings = $this->cache->getListWithoutPrefix($cacheKey);

            $this->logger->info('[FixWrongEventCacheWaitlist] Initial report.', [
                'event_id' => $eventId,
                'members_in_database' => count($databaseWaitingBookings),
                'members_in_cache' => count($cacheWaitingBookings)
            ]);

            $membersRemovedFromCache = $this->fixWaitlist($cacheKey, $databaseWaitingBookings, $cacheWaitingBookings);

            $databaseWaitingBookings = $this->getWaitlistBookings($eventId);
            $cacheWaitingBookings = $this->cache->getListWithoutPrefix($cacheKey);

            $this->logger->info('[FixWrongEventCacheWaitlist] Final report.', [
                'event_id' => $eventId,
                'members_in_database' => count($databaseWaitingBookings),
                'members_in_cache' => count($cacheWaitingBookings),
                'members_removed_from_cache' => $membersRemovedFromCache
            ]);

            $this->logger->info('[FixWrongEventCacheWaitlist] Finished');
        } catch (Exception $e) {
            $this->logger->info('[FixWrongEventCacheWaitlist] ' . $e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    private function getWaitlistBookings(string $eventId): array
    {
        if (empty($eventId)) {
            throw new \Exception('Event ID cannot be empty.');
        }

        $bookedMembers = [];
        $bookings = $this->bookingsRepository->getWaitingBookingsByEventId($eventId);

        foreach ($bookings as $booking) {
            $bookedMembers[] = $booking->userId();
            for ($i = 0; $i < $booking->guestBookings() ?? 0; $i++) {
                $bookedMembers[] = $booking->userId() . '-guest-' . ($i+1);
            }
        }

        return $bookedMembers;
    }

    /**
     * @throws Exception
     */
    private function fixWaitlist(string $cacheKey, array $databaseWaitingBookings, array $cacheWaitingBookings): int
    {
        $dbTotal = count($databaseWaitingBookings);
        $cacheTotal = count($cacheWaitingBookings);

        if ($dbTotal === $cacheTotal) {
            throw new \Exception('Warning: The number of waiting bookings in the database is equal to the number of them in the cache.');
        }

        if ($dbTotal > $cacheTotal) {
            throw new \Exception('Error: The number of waiting bookings in the database is greater than the number of them in the cache.');
        }

        $removedMembers = 0;

        foreach ($cacheWaitingBookings as $cachedMember) {
            if (!in_array($cachedMember, $databaseWaitingBookings)) {
                $this->cache->deleteValueInListWithoutPrefix($cacheKey, $cachedMember);
                $removedMembers++;
            }
        }

        return $removedMembers;
    }
}
