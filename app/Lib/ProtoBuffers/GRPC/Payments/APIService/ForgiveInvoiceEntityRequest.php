<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: api_invoice.proto

namespace GRPC\Payments\APIService;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>grpc.ForgiveInvoiceEntityRequest</code>
 */
class ForgiveInvoiceEntityRequest extends \Google\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string InvoiceID = 1;</code>
     */
    protected $InvoiceID = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $InvoiceID
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\ApiInvoice::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string InvoiceID = 1;</code>
     * @return string
     */
    public function getInvoiceID()
    {
        return $this->InvoiceID;
    }

    /**
     * Generated from protobuf field <code>string InvoiceID = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setInvoiceID($var)
    {
        GPBUtil::checkString($var, True);
        $this->InvoiceID = $var;

        return $this;
    }

}

