<?php

namespace CakeTestCases\Glofox\Domain\Bookings\Requests;

use App;
use CakeTestCases\Glofox\Domain\Users\Traits\AuthenticateUsersTrait;
use Carbon\Carbon;
use Closure;
use Glofox\Domain\Bookings\Services\BookingsPublisher;
use Glofox\Domain\FeatureFlags\FeatureFlagInterface;
use GlofoxControllerTestCase;
use JsonException;
use Mockery;

App::import('Test/Case', 'GlofoxControllerTestCase');

class CancelBookingsByBatchIdRequestTest extends GlofoxControllerTestCase
{
    use AuthenticateUsersTrait;

    public $fixtures = [
        'app.branch',
        'app.user'
    ];

    private BookingsPublisher $bookingsPublisher;
    private FeatureFlagInterface $batchCancellationFlagger;

    public function setUp(): void
    {
        parent::setUp();

        $this->bookingsPublisher = Mockery::mock(BookingsPublisher::class);
        $this->batchCancellationFlagger = Mockery::mock(FeatureFlagInterface::class);
    }

    public function tearDown(): void
    {
        parent::tearDown();

        Mockery::close();
    }

    /**
     * @dataProvider rulesDataProvider
     * @param array $params
     * @param Closure $assertion
     * @return void
     * @throws JsonException
     */
    public function testRules(
        array $params,
        Closure $assertion
    ): void {
        $adminId = '59a7011a05c677bda916612a';
        $branchId = '49a7011a05c677b9a916612a';
        $batchId = 'coreapi-random-id';
        $this->authenticateUser($adminId);

        $this->bookingsPublisher
            ->shouldReceive('sendCancellationInBatchRequestedEvent')
            ->andReturnTrue();
        app()->instance(BookingsPublisher::class, $this->bookingsPublisher);

        $this->batchCancellationFlagger
            ->shouldReceive('withFlag')
            ->andReturnSelf()
            ->getMock()
            ->shouldReceive('hasByBranchId')
            ->andReturnTrue();
        app()->instance(FeatureFlagInterface::class, $this->batchCancellationFlagger);

        $url = sprintf(
            '2.2/branches/%s/bookings/batch/%s?utc-start-time=%s',
            $branchId,
            $batchId,
            $params['utcStartTime']
        );

        $response = json_decode(
            $this->testAction($url, ['method' => 'DELETE']),
            true,
            512,
            JSON_THROW_ON_ERROR
        );

        $assertion->call($this, $response, $this->response->statusCode());

        app()->forgetInstance(BookingsPublisher::class);
        app()->forgetInstance(FeatureFlagInterface::class);
    }

    public function rulesDataProvider(): array
    {
        return [
            '1. As a staff member, When I send a request to cancel bookings in batch with no timestamp, Then an
             invalid argument error is returned' =>
                [
                    'params' => [
                        'utcStartTime' => null,
                    ],
                    'assertion' => function ($response, $statusCode) {
                        $this->assertEquals(400, $statusCode);
                        $this->assertNotEmpty($response);
                        $this->assertFalse($response['success']);
                        $this->assertEquals('The utc-start-time field is required.', $response['message']);
                    }
                ],
            '2. As a staff member, When I send a request to cancel bookings in batch with timestamp in a wrong format,
              Then an invalid argument error is returned' =>
                [
                    'params' => [
                        'utcStartTime' => 'string',
                    ],
                    'assertion' => function ($response, $statusCode) {
                        $this->assertEquals(400, $statusCode);
                        $this->assertNotEmpty($response);
                        $this->assertFalse($response['success']);
                        $this->assertStringStartsWith('The utc-start-time must be a number.', $response['message']);
                    }
                ],
            '3. As a staff member, When I send a request to cancel bookings in batch with timestamp in the past,
              Then an invalid argument error is returned' =>
                [
                    'params' => [
                        'utcStartTime' => Carbon::yesterday()->timestamp,
                    ],
                    'assertion' => function ($response, $statusCode) {
                        $this->assertEquals(400, $statusCode);
                        $this->assertNotEmpty($response);
                        $this->assertFalse($response['success']);
                        $this->assertStringStartsWith('The utc-start-time must be at least', $response['message']);
                    }
                ],
            '4. As a staff member, When I send a request to cancel bookings in batch with correct timestamp,
              Then an empty(successful) response is returned' =>
                [
                    'params' => [
                        'utcStartTime' => Carbon::tomorrow()->timestamp
                    ],
                    'assertion' => function ($response, $statusCode) {
                        $this->assertEmpty($response);
                        $this->assertEquals(202, $statusCode);
                    }
                ],
        ];
    }

    /**
     * @dataProvider authorizeDataProvider
     * @param array $params
     * @param Closure $assertion
     * @return void
     * @throws JsonException
     */
    public function testAuthorize(
        array $params,
        Closure $assertion
    ): void {
        $this->authenticateUser($params['userId']);

        $this->bookingsPublisher
            ->shouldReceive('sendCancellationInBatchRequestedEvent')
            ->andReturnTrue();
        app()->instance(BookingsPublisher::class, $this->bookingsPublisher);

        $this->batchCancellationFlagger
            ->shouldReceive('withFlag')
            ->andReturnSelf()
            ->getMock()
            ->shouldReceive('hasByBranchId')
            ->andReturnTrue();
        app()->instance(FeatureFlagInterface::class, $this->batchCancellationFlagger);

        $url = sprintf(
            '2.2/branches/%s/bookings/batch/%s?utc-start-time=%s',
            $params['branchId'],
            $params['batchId'],
            $params['utcStartTime']
        );

        $response = json_decode(
            $this->testAction($url, ['method' => 'DELETE']),
            true,
            512,
            JSON_THROW_ON_ERROR
        );

        $assertion->call($this, $response, $this->response->statusCode());

        app()->forgetInstance(BookingsPublisher::class);
        app()->forgetInstance(FeatureFlagInterface::class);
    }


    public function authorizeDataProvider(): array
    {
        $branchId = '49a7011a05c677b9a916612a';
        $batchId = 'coreapi-random-id';
        return [
            '1. As a member, When I send a request to cancel bookings in batch, Then an unauthorized error
            is returned' =>
                [
                    'params' => [
                        'userId' => '59a3011a05c677bda916612d',
                        'branchId' => $branchId,
                        'batchId' => $batchId,
                        'utcStartTime' => Carbon::tomorrow()->timestamp
                    ],
                    'assertion' => function ($response, $statusCode) {
                        $this->assertEquals(400, $statusCode);
                        $this->assertNotEmpty($response);
                        $this->assertFalse($response['success']);
                        $this->assertEquals('This action is unauthorized.', $response['message_code']);
                    }
                ],
            '2. As a trainer, When I send a request to cancel bookings in batch,Then an empty response(successful)
             is returned' =>
                [
                    'params' => [
                        'userId' => '59a7011a05c677bda916612c',
                        'branchId' => $branchId,
                        'batchId' => $batchId,
                        'utcStartTime' => Carbon::tomorrow()->timestamp
                    ],
                    'assertion' => function ($response, $statusCode) {
                        $this->assertEquals(202, $statusCode);
                        $this->assertEmpty($response);
                    }
                ],
            '3. As a superadmin, I send a request to cancel bookings in batch, Then an empty response(successful)
             is returned' =>
                [
                    'params' => [
                        'userId' => '59a7011a05c677bda916619a',
                        'branchId' => $branchId,
                        'batchId' => $batchId,
                        'utcStartTime' => Carbon::tomorrow()->timestamp
                    ],
                    'assertion' => function ($response, $statusCode) {
                        $this->assertEquals(202, $statusCode);
                        $this->assertEmpty($response);
                    }
                ],
            '4. As an admin, When I send a request to cancel bookings in batch, Then an empty response(successful)
               is returned' =>
                [
                    'params' => [
                        'userId' => '59a7011a05c677bda916612a',
                        'branchId' => $branchId,
                        'batchId' => $batchId,
                        'utcStartTime' => Carbon::tomorrow()->timestamp
                    ],
                    'assertion' => function ($response, $statusCode) {
                        $this->assertEquals(202, $statusCode);
                        $this->assertEmpty($response);
                    }
                ],
            '5. As a receptionist, When I send a request to cancel bookings in batch, Then an empty response(successful)
               is returned' =>
                [
                    'params' => [
                        'userId' => '59a7011a05c677bda916619b',
                        'branchId' => $branchId,
                        'batchId' => $batchId,
                        'utcStartTime' => Carbon::tomorrow()->timestamp
                    ],
                    'assertion' => function ($response, $statusCode) {
                        $this->assertEquals(202, $statusCode);
                        $this->assertEmpty($response);
                    }
                ]
        ];
    }
}
