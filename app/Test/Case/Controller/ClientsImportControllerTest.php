<?php

App::import('Test/Case', 'GlofoxControllerTestCase');

/**
 * ClientsImportsController Test Case.
 */
class ClientsImportsControllerTest extends GlofoxControllerTestCase
{
    public function setUp()
    {
        parent::setUp();

        $this->authenticateAsAdmin();
    }

    /**
     * testUploadNoFile method
     * Test the upload when the payload does not contain a file.
     */
    public function testUploadNoCsvFile()
    {
        $data = ['delimiter' => ','];

        $result = $this->testAction('clientsImport/uploadCSV', ['data' => $data, 'method' => 'post']);
        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        $this->assertNotNull($result);
        $this->assertFalse($result->success);
        $this->assertEquals($result->error, 'Please enter a file');
    }

    /**
     * testUploadDataEmpty method
     * Test the response when no payload is present.
     */
    public function testUploadDataEmpty()
    {
        $data = [];
        $result = $this->testAction('clientsImport/uploadData', ['data' => $data, 'method' => 'post']);
        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        $this->assertNotNull($result);
        $this->assertTrue(false == $result->success);
        $this->assertEquals($result->message, 'Please submit data');
    }
}
