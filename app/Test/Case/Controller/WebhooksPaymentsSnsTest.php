<?php

use Aws\Sns\MessageValidator;
use CakeTestCases\Glofox\Domain\Authentication\Token\TokenGeneration;
use CakeTestCases\Glofox\Domain\Users\Traits\FetchUsersTrait;
use CakeTestCases\Helper\Sns\SnsPayloadFactory;
use CakeTestCases\Helper\Stream\MockPhpStream;
use Glofox\Domain\Authentication\Auth;
use Glofox\Domain\Locker\WebhookLockerInterface;
use Glofox\Payments\Entities\WebHook\Models\WebhookEvent;

App::import('Test/Case', 'GlofoxControllerTestCase');

class WebhooksPaymentsSnsTest extends GlofoxControllerTestCase
{
    use FetchUsersTrait;
    use TokenGeneration;

    public $fixtures = [
        'app.access_control_list',
        'app.booking',
        'app.user',
        'app.event',
        'app.branch',
        'app.program',
        'app.course',
        'app.membership',
        'app.stripe_charge',
        'app.user_credit',
        'app.facility',
        'app.client',
        'app.payment_method',
        'app.payment_method_user',
        'app.payment_provider',
    ];

    private ?\CakeTestCases\Helper\Sns\SnsPayloadFactory $snsPayloadFactory = null;

    public function setUp()
    {
        parent::setUp();
        self::mockHeaders();

        $this->snsPayloadFactory = new SnsPayloadFactory();

        \Mockery::mock(sprintf('overload:%s', MessageValidator::class))
            ->shouldReceive('validate')
            ->andReturn(null);
    }

    public function tearDown(): void
    {
        parent::tearDown();
        self::tearDownHeaders();
        \Mockery::close();
    }

    public function test_sns_webhook_handler_fails_with_non_event_payload(): void
    {
        $data = ['description' => 'test'];

        self::mockPhpInpuStream($data);

        $result = $this->testAction('/2.0/webhooks/payments-sns', ['method' => 'post']);
        $result = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        self::unmockPhpInputStream();

        self::assertNotEmpty($result);
        self::assertArrayHasKey('errors', $result);
        self::assertEquals('"Message" is required to verify the SNS Message.', $result['message']);
    }

    public function test_sns_webhook_handler_fails_with_broken_event_payload(): void
    {
        $invalidSubscriptionRenewalSnsPayloadPath = (APP . 'Test/Case/Helper/Sns/Payloads/invalid-subscription-renewal-sns-payload.json');
        $payload = $this->snsPayloadFactory->create($invalidSubscriptionRenewalSnsPayloadPath);

        self::mockPhpInpuStream($payload);

        $result = $this->testAction('/2.0/webhooks/payments-sns', ['method' => 'post']);
        $result = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        self::unmockPhpInputStream();

        self::assertNotEmpty($result);
        self::assertArrayHasKey('errors', $result);
        self::assertEquals('No customer provider id was found for account id: ', $result['message']);
    }

    public function test_sns_webhook_handler_succeeds_with_valid_subscription_renewal_payload(): void
    {
        self::markTestSkipped('This was a test that has been here for a long time, but it doesnt validate correctly due to the fact that it is impossible to test the real gateway and provider handlers at the moment, we need to create the capability to test them and update this test case. Previously, this test was returning success mistakenly');

        $validSubscriptionRenewalSnsPayloadPath = (APP . 'Test/Case/Helper/Sns/Payloads/valid-subscription-renewal-sns-payload.json');
        $payload = $this->snsPayloadFactory->create($validSubscriptionRenewalSnsPayloadPath);

        self::mockPhpInpuStream($payload);

        // correct payload
        $result = $this->testAction('/2.0/webhooks/payments-sns', ['method' => 'post']);
        $result = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        self::unmockPhpInputStream();

        self::assertNotEmpty($result);
        self::assertTrue($result['success']);
    }

    public function test_it_prevents_the_same_event_from_being_executed_in_concurrency(): void
    {
        $validSubscriptionRenewalSnsPayloadPath = (APP . 'Test/Case/Helper/Sns/Payloads/valid-subscription-renewal-sns-payload.json');
        $payload = $this->snsPayloadFactory->create($validSubscriptionRenewalSnsPayloadPath);

        self::mockPhpInpuStream($payload);

        $eventId = (string) json_decode($payload['Message'], true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR)['id'];

        /** @var WebhookLockerInterface $locker */
        $locker = app()->make(WebhookLockerInterface::class);

        // Making sure there's no locks before starting
        $locker->unlock(new WebhookEvent($eventId));

        // Locking the webhook to simulate a situation where the event had already been received.
        $locker->lock(new WebhookEvent($eventId));

        $result = $this->testAction('/2.0/webhooks/payments-sns', ['method' => 'post']);
        $result = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        app()->forgetInstance(Auth::class);

        self::unmockPhpInputStream();

        self::assertFalse($result['success']);
        self::assertEquals('Cannot lock the Webhook Event 7214467 because it is already locked', $result['message']);
    }

    public function test_it_unlocks_webhook_when_webhook_finishes_to_be_processed(): void
    {
        $this->markTestSkipped('This test case needs investigation once it is failing other tests');

        $mockedLockerInterface = \Mockery::mock(WebhookLockerInterface::class);
        $mockedLockerInterface
            ->expects('lock');

        $mockedLockerInterface
            ->expects('unlock');

        app()->forgetInstance(WebhookLockerInterface::class);
        app()->instance(WebhookLockerInterface::class, $mockedLockerInterface);

        $validSubscriptionRenewalSnsPayloadPath = (APP . 'Test/Case/Helper/Sns/Payloads/valid-subscription-renewal-sns-payload.json');
        $payload = $this->snsPayloadFactory->create($validSubscriptionRenewalSnsPayloadPath);

        self::mockPhpInpuStream($payload);

        $this->testAction('/2.0/webhooks/payments-sns', ['method' => 'post']);

        self::unmockPhpInputStream();

        app()->forgetInstance(WebhookLockerInterface::class);
    }

    private static function mockHeaders(): void
    {
        $_SERVER['HTTP_X_AMZ_SNS_MESSAGE_TYPE'] = 'Notification';
        $_SERVER['HTTP_X_AMZ_SNS_MESSAGE_ID'] = '22b80b92-fdea-4c2c-8f9d-bdfb0c7bmock';
        $_SERVER['HTTP_X_AMZ_SNS_TOPIC_ARN'] = 'arn:aws:sns:us-west-2:123456789012:glofox-payments-topic-mock';
        $_SERVER['HTTP_X_AMZ_SNS_SUBSCRIPTION_ARN'] = 'arn:aws:sns:us-west-2:123456789012:glofox-payments-topic-mock:c9135db0-26c4-47ec-8998-413945fbmock';
        $_SERVER['HTTP_USER_AGENT'] = 'Amazon Simple Notification Service Agent';
    }

    private static function tearDownHeaders(): void
    {
        unset($_SERVER['HTTP_X_AMZ_SNS_MESSAGE_TYPE']);
        unset($_SERVER['HTTP_X_AMZ_SNS_MESSAGE_ID']);
        unset($_SERVER['HTTP_X_AMZ_SNS_TOPIC_ARN']);
        unset($_SERVER['HTTP_X_AMZ_SNS_SUBSCRIPTION_ARN']);
        unset($_SERVER['HTTP_USER_AGENT']);
    }

    private static function mockPhpInpuStream(array $data): void
    {
        $data = json_encode($data, JSON_PARTIAL_OUTPUT_ON_ERROR);

        // Prep php://input with mocked data
        MockPhpStream::setStartingData($data);
        stream_wrapper_unregister('php');
        stream_wrapper_register('php', MockPhpStream::class);
    }

    private static function unmockPhpInputStream(): void
    {
        stream_wrapper_restore('php');
    }
}
