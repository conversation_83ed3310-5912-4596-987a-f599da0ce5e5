<?php

declare(strict_types=1);

namespace Glofox\Domain\Charges\Commands;

use Carbon\Carbon;
use Glofox\Datasource\Exceptions\InvalidMongoIdException;
use Glofox\Domain\Activities\Type;
use Glofox\Domain\Bookings\Search\Expressions\CreatedDate;
use Glofox\Domain\Charges\Models\Charge;
use Glofox\Domain\Charges\Repositories\ChargesRepository;
use Glofox\Domain\Charges\Search\Expressions\MetadataGlofoxEvent;
use Glofox\Domain\Charges\Search\Expressions\MetadataSubscriptionId;
use Glofox\Domain\Charges\Search\Expressions\MetadataUserId;
use Glofox\Domain\Charges\Status as ChargeStatus;
use Glofox\Domain\Charges\Type as ChargesType;
use Glofox\Domain\Memberships\Models\Membership;
use Glofox\Domain\Memberships\Models\Plan;
use Glofox\Domain\Memberships\Repositories\MembershipsRepository;
use Glofox\Domain\Users\Models\User;
use Glofox\Domain\Users\Repositories\UsersRepository;
use Glofox\Infrastructure\Flags\Flag;
use Glofox\Payments\Services\IsFirstChargeAfterPauseService;
use Glofox\Repositories\Search\Expressions\Shared\Id;
use Glofox\Repositories\Search\Expressions\Shared\IdNotEquals;
use Glofox\Repositories\Search\Expressions\Shared\InvoiceIdNotEquals;
use Psr\Log\LoggerInterface;

class SendReceiptValidator
{
    private MembershipsRepository $membershipsRepository;
    private ChargesRepository $chargesRepository;
    private UsersRepository $usersRepository;
    private IsFirstChargeAfterPauseService $isFirstChargeAfterPauseService;
    private LoggerInterface $logger;

    public function __construct(
        MembershipsRepository $membershipsRepository,
        ChargesRepository $chargesRepository,
        UsersRepository $usersRepository,
        IsFirstChargeAfterPauseService $isFirstChargeAfterPauseService,
        LoggerInterface $logger
    ) {
        $this->membershipsRepository = $membershipsRepository;
        $this->chargesRepository = $chargesRepository;
        $this->usersRepository = $usersRepository;
        $this->isFirstChargeAfterPauseService = $isFirstChargeAfterPauseService;
        $this->logger = $logger;
    }

    /**
     * @throws InvalidMongoIdException
     */
    public function validate(Charge $charge): bool
    {
        if ($this->isFreeCharge($charge)) {
            return false;
        }

        if ($this->transactionWithError($charge)) {
            return false;
        }

        if ($charge->isPendingAuthorization()) {
            return false;
        }

        if ($this->chargeFailed($charge)) {
            return false;
        }

        if ($this->isHistoricCharge($charge)) {
            return false;
        }

        /** @var User $user */
        $user = $this->usersRepository
            ->addCriteria(new Id($charge->userId()))
            ->firstOrFail();

        if ($this->isRecurrentFeeCharge($charge)) {
            if ($user->sendRenewalReceipts()) {
                return true;
            }
            return false;
        }

        if (!$charge->isMembershipRelated()) {
            return true;
        }

        if ($this->isSingleMembershipPayment($charge)) {
            return true;
        }

        if ($charge->isSubscriptionProrate()) {
            return true;
        }

        if ($charge->isUpfrontPayment()) {
            if ($this->subscriptionStartsInFuture($charge, $user)) {
                return true;
            }
            return true; // TODO this should be revert to false when the new receive feature is done
            // the upfront fee will be sent with the subscription amount in this case
        }

        $previousCharge = $this->getPreviousSubscriptionCharge($charge);
        if (!$previousCharge) {
            return true;
        }

        if ($this->isFirstChargeAfterPauseService->execute($charge->id(), $charge->userId())) {
            return true;
        }

        if ($this->shouldSendRenewalReceipts($charge, $user)) {
            return true;
        }

        $this->logger->info('SendReceiptMessage:: not sending receipt', [
            'chargeId' => $charge->id(),
        ]);

        return false;
    }

    private function isFreeCharge(Charge $charge): bool
    {
        return $charge->amount() <= 0;
    }

    private function transactionWithError(Charge $charge): bool
    {
        return $charge->transactionStatus() === ChargeStatus::ERROR;
    }

    private function chargeFailed(Charge $charge): bool
    {
        return strpos($charge->glofoxEvent(), 'fail') !== false;
    }

    private function isHistoricCharge(Charge $charge): bool
    {
        return strpos($charge->glofoxEvent(), 'historic') !== false;
    }

    private function isRecurrentFeeCharge(Charge $charge): bool
    {
        return $charge->glofoxEvent() === Type::RECURRENT_FEE;
    }

    /**
     * @throws InvalidMongoIdException
     */
    private function isSingleMembershipPayment(Charge $charge): bool
    {
        /** @var Membership $membership */
        $membership = $this->membershipsRepository
            ->addCriteria(new Id($charge->membershipId()))
            ->firstOrFail();
        /** @var Plan $plan */
        $plan = $membership->planByCode($charge->planCode());

        return $charge->isUpfrontPayment() && !$plan->isSubscription();
    }

    /**
     * @throws InvalidMongoIdException
     */
    private function subscriptionStartsInFuture(Charge $charge, User $user): bool
    {
        $timeZone = $user->branch()->timezone();

        return $user->membership()->startDate($timeZone)->isAfter(Carbon::today($timeZone));
    }

    /**
     * @throws InvalidMongoIdException
     */
    private function getPreviousSubscriptionCharge(Charge $currentCharge): ?Charge
    {
        if (empty($currentCharge->subscriptionId())) {
            return null;
        }

        /** @var Charge $previousCharge */
        $previousCharge = $this->chargesRepository
            ->addCriteria(new MetadataSubscriptionId($currentCharge->subscriptionId()))
            ->addCriteria(new MetadataGlofoxEvent(ChargesType::SUBSCRIPTION_PAYMENT))
            ->addCriteria(new MetadataUserId($currentCharge->userId()))
            ->addCriteria(new IdNotEquals($currentCharge->id()))
            ->addCriteria(new InvoiceIdNotEquals($currentCharge->invoiceId()))
            ->addCriteria(CreatedDate::lessThanOrEqual($currentCharge->created()->timestamp))
            ->order(['created' => -1])
            ->first();

        return $previousCharge ?: null;
    }

    private function shouldSendRenewalReceipts(Charge $charge, User $user): bool
    {
        if ($charge->glofoxEvent() !== ChargesType::SUBSCRIPTION_PAYMENT) {
            return false;
        }


        return $user->sendRenewalReceipts();
    }
}
