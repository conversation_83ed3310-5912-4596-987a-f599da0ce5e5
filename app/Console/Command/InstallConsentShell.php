<?php

declare(strict_types=1);

use Glofox\Domain\AsyncEvents\Events\IntegrationUpdatedEventMeta;
use Glofox\Domain\AsyncEvents\Events\IntegrationUpdatedEventPayload;
use Glofox\Domain\Branches\Repositories\BranchesRepository;
use Glofox\Domain\Branches\Search\Expressions\BranchId;
use Glofox\Domain\Integrations\Services\IntegrationUpdatedEventPublisher;
use Glofox\Domain\Terms\Type as WaiverType;
use Glofox\Domain\Users\Models\ConsentEmail;
use Glofox\Domain\Users\Models\ConsentPush;
use Glofox\Domain\Users\Models\ConsentSms;
use Glofox\Domain\Users\Models\User;
use Glofox\Domain\Users\Models\UserConsent;
use Glofox\Domain\Users\Repositories\UsersRepository;
use Glofox\Domain\Users\Search\Expressions\Type;
use Glofox\Domain\Users\UserTypesCollection;
use Glofox\Events\EventManager;
use Glofox\Repositories\Search\Expressions\Shared\Id;
use MongoDB\BSON\UTCDateTime;
use Psr\Log\LoggerInterface;
use TermsCondition as TermsModel;
use User as UserModel;

App::uses('Shell', 'Console');

class InstallConsentShell extends AppShell
{
    /** @var LoggerInterface */
    private $logger;

    /** @var BranchesRepository */
    private $branchesRepository;

    /** @var UsersRepository */
    private $usersRepository;

    /** @var IntegrationUpdatedEventPublisher */
    private $integrationUpdatedEventPublisher;

    /** @var UserModel */
    private $userModel;

    /** @var MessageReport */
    private $messageReportModel;

    private string $errorCode = '21610';

    /** @var EventManager */
    private $eventManager;

    /** @var TermsModel */
    private $termsAndConditionsModel;

    public function __construct($stdout = null, $stderr = null, $stdin = null)
    {
        parent::__construct($stdout, $stderr, $stdin);

        $this->logger = app()->make(LoggerInterface::class);
        $this->branchesRepository = app()->make(BranchesRepository::class);
        $this->usersRepository = app()->make(UsersRepository::class);
        $this->integrationUpdatedEventPublisher = app()->make(IntegrationUpdatedEventPublisher::class);
        $this->userModel = app()->make(UserModel::class);
        $this->messageReportModel = \ClassRegistry::init(MessageReport::class);
        $this->eventManager = app()->make(EventManager::class);
        $this->termsAndConditionsModel = app()->make(TermsModel::class);
    }

    public function getOptionParser(): ConsoleOptionParser
    {
        $parser = parent::getOptionParser();

        $parser->addOption('branch-ids', [
            'short' => 'b',
            'help' => 'The studio ids to install consent feature',
            'default' => '',
        ]);

        $parser->addOption('environment', [
            'short' => 'e',
            'help' => 'The environment to install feature',
            'default' => 'platform',
        ]);

        $parser->addOption('email-consent', [
            'short' => 'm',
            'help' => 'The email consent',
            'default' => false,
        ]);

        $parser->addOption('sms-consent', [
            'short' => 's',
            'help' => 'The sms consent',
            'default' => false,
        ]);

        return $parser;
    }

    public function main(): void
    {
        $branchIds = $this->param('branch-ids');
        $emailConsent = (bool)$this->param('email-consent');
        $smsConsent = (bool)$this->param('sms-consent');
        $installedBranches = [];

        if (empty($branchIds)) {
            throw new UnsuccessfulOperation('There is no branch id specified');
        }

        $branchIdArray = explode(PHP_EOL, $branchIds);

        foreach ($branchIdArray as $branchId) {
            $this->logger->info(sprintf('Starting to install Consent to studio %s', $branchId));

            try {
                $branch = $this->branchesRepository
                    ->addCriteria(new Id($branchId))
                    ->findOrFail(function () use ($branchId) {
                        throw new Exception(sprintf('Branch not found with id: %s', $branchId));
                    });

                $users = $this->getUsers($branchId);

                /** @var User $user */
                foreach ($users as $user) {
                    try {
                        if ($user->has('consent')) {
                            if ($user->has('receive_marketing')) {
                                // remove receive_marketing field from user
                                $this->usersRepository->removeField($user->id(), 'receive_marketing');
                            }
                            continue;
                        }

                        $consent = $this->getConsent($user, $emailConsent, $smsConsent);

                        // add consent field to user
                        $this->usersRepository->updateOneField($user->id(), 'consent', $consent->toArray());
                        // remove receive_marketing field from user
                        $this->usersRepository->removeField($user->id(), 'receive_marketing');
                    } catch (Exception $e) {
                        $this->logger->error(
                            sprintf(
                                'Cannot install consent for the user %s because %s',
                                $user->id(),
                                $e->getMessage()
                            )
                        );
                    }
                }
                $smsWaiverCount = $this->termsAndConditionsModel->find(
                    'count',
                    ['conditions' => ['branch_id' => $branchId, 'type' => WaiverType::SMS_WAIVER]]
                );
                if ($smsWaiverCount === 0) {
                    $smsWaiver = [
                        'branch_id' => $branchId,
                        'namespace' => $branch[0]['namespace'],
                        'type' => WaiverType::SMS_WAIVER,
                        'content' => ''
                    ];
                    $this->termsAndConditionsModel->save($smsWaiver);
                }

                $installedBranches[] = $branchId;
            } catch (Exception $exception) {
                $this->logger->error(
                    sprintf(
                        'Cannot install consent to the studio %s because %s',
                        $branchId,
                        $exception->getMessage()
                    )
                );
                continue;
            }

            $this->logger->info(sprintf('Consent installed successfully to studio %s', $branchId));
        }

        if (count($installedBranches) > 0) {
            $this->publishEvent($installedBranches);
        }
    }

    private function getUsers(string $branchId): array
    {
        return $this->usersRepository
            ->addCriteria(new BranchId($branchId))
            ->addCriteria(new Type(UserTypesCollection::make([UserType::MEMBER()])))
            ->skipCallbacks()
            ->find();
    }

    private function getConsent(User $user, bool $emailConsent, bool $smsConsent): UserConsent
    {
        $consent = new UserConsent();

        $date = new UTCDateTime();

        $userConsentEmail = new ConsentEmail([
            'active' => $emailConsent && $user->optedInForMarketingMessages(),
            'modified_at' => $date,
            'modified_by_user_id' => 'migrated',
            'modified_from_ip_address' => null,
            'message' => null,
        ]);
        $consent->put('email', $userConsentEmail);

        $userConsentSms = new ConsentSms([
            'active' => $smsConsent && $user->optedInForMarketingMessages() && !$this->isOptedOutBySms($user),
            'modified_at' => $date,
            'modified_by_user_id' => 'migrated',
            'modified_from_ip_address' => null,
            'message' => null,
        ]);
        $consent->put('sms', $userConsentSms);

        $userConsentPush = new ConsentPush([
            'active' => true,
            'modified_at' => $date,
            'modified_by_user_id' => 'migrated',
            'modified_from_ip_address' => null,
            'message' => null,
        ]);
        $consent->put('push', $userConsentPush);

        return $consent;
    }

    private function publishEvent(array $branches): void
    {
        foreach ($branches as $branch) {
            $meta = new IntegrationUpdatedEventMeta([
                'integration' => 'MAILCHIMP',
                'action' => 'SYNC_USERS',
            ]);

            $payload = new IntegrationUpdatedEventPayload([
                'branchId' => $branch,
            ]);

            $this->integrationUpdatedEventPublisher->sendIntegrationUpdatedEvent($meta, $payload);
        }
    }

    private function isOptedOutBySms(User $user): bool
    {
        $formattedPhoneNumber = data_get($user, 'metadata.twilio.phone_number');

        $params = [
            'conditions' => [
                'branch_id' => $user->originBranchId(),
                'response.To' => $formattedPhoneNumber,
                'response.ErrorCode' => $this->errorCode,
            ],
        ];

        $errorResultsCount = $this->messageReportModel->find('count', $params);

        return $errorResultsCount > 0;
    }
}
