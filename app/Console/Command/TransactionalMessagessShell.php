<?php

ini_set('max_execution_time', 600);

App::uses('Shell', 'Console');

use Glofox\Domain\Transactional\Repositories\TransactionalMessagesRepository;
use Glofox\Domain\Branches\Repositories\BranchesRepository;
use Glofox\Domain\Transactional\Messages\Factory as TransactionalMessageFactory;
use Glofox\Domain\Transactional\Triggers\Factory as TriggerFactory;
use Glofox\Domain\Transactional\Triggers\Trigger;
use Glofox\Domain\Transactional\Messages\Type as MessageType;
use Glofox\Domain\Transactional\Messages\Identifier as MessageIdentifier;

class TransactionalMessagessShell extends AppShell
{
    /**
     * @var TransactionalMessagesRepository
     */
    protected $transactionalMessagesRepository;

    /**
     * @var TransactionalMessageFactory
     */
    protected $transactionalMessagesFactory;

    /**
     * @var BranchesRepository
     */
    protected $branchesRepository;

    /**
     * @var TriggerFactory
     */
    protected $triggerFactory;

    /**
     * @var Branch
     */
    protected $branch;

    /**
     * TransactionalEmailsShell constructor.
     * @param null $stdout
     * @param null $stderr
     * @param null $stdin
     */
    public function __construct($stdout = null, $stderr = null, $stdin = null)
    {
        parent::__construct($stdout, $stderr, $stdin);

        $this->transactionalMessagesRepository = new TransactionalMessagesRepository();
        $this->transactionalMessagesFactory = new TransactionalMessageFactory();
        $this->branchesRepository = new BranchesRepository();
        $this->triggerFactory = new TriggerFactory();

        $this->branch = ClassRegistry::init('Branch');
    }

    public function main()
    {
        $this->log("~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~", LOG_INFO);
        $this->log("Cron Started: ". self::class, LOG_INFO);

        $defaults = $this->transactionalMessagesRepository->findByType(MessageType::TIME_BASED());

        foreach ($defaults as $default) {
            $default = $default['TransactionalMessagesDefault'];

            $identifier = MessageIdentifier::byName($default['identifier']);
            $branches = $this->branchesRepository->findBranchesByTransactionalMessageIdentifier($identifier);

            foreach ($branches as $branch) {
                try {
                    // Get branch's message setting data
                    $message = $this->transactionalMessagesRepository->findByBranchAndIdentifierWithDefaults($branch, $identifier);

                    $transactionalMessage = $this->transactionalMessagesFactory->create($message);

                    // We're already retrieving only the branches which have this enabled, but just in case some tweaks that rule...
                    if ($transactionalMessage->isEnabled()) {
                        $triggers = $transactionalMessage->triggers();

                        foreach ($triggers as $data) {
                            $identifier = $data['identifier'];

                            /** @var Trigger $trigger */
                            $trigger = $this->triggerFactory->create($identifier, $data);
                            $trigger->trigger($branch, $transactionalMessage);
                        }
                    }
                } catch (Exception $e) {
                    $this->log($e->getMessage(), LOG_WARNING);
                }
            }
        }

        $this->log("Cron Finished", LOG_INFO);
    }
}
