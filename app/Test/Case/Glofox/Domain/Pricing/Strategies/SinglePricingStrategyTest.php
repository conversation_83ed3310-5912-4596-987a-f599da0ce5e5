<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\Pricing\Strategies;

use Carbon\Carbon;
use Glofox\Domain\Branches\Models\Branch;
use Glofox\Domain\Memberships\Models\Addon;
use Glofox\Domain\Pricing\Strategies\SinglePricingStrategy;
use Glofox\Domain\Users\Models\User;

\App::import('Test/Case', 'GlofoxControllerTestCase');

class SinglePricingStrategyTest extends \GlofoxControllerTestCase
{
    public $fixtures = [];

    public function testAddonsMakePriceZero(): void
    {
        $branch = new Branch([]);
        $user = new User([]);
        $addon = new Addon(
            'service-id',
            'service-definition-id',
            'service-definition-plan-id',
            'service-definition-name',
            'service-definition-plan-name',
            'TRAINER',
            Carbon::yesterday()->getTimestamp(),
            false
        );
        $pricingStrategy = new SinglePricingStrategy($branch, $user, 1);
        $price = $pricingStrategy->calculatePriceForUser(10, 15, $addon);
        self::assertEquals(0, $price->price());
        self::assertEquals(0, $price->credits());
    }

    public function testNoAddonsNoMembershipWIthCredits(): void
    {
        $branch = new Branch([]);
        $user = new User([]);
        $pricingStrategy = new SinglePricingStrategy($branch, $user, 1);
        $price = $pricingStrategy->calculatePriceForUser(10, 15);
        self::assertEquals(0.0, $price->price());
        self::assertEquals(1, $price->credits());
    }

    public function testNoAddonsNoMembershipNoCredits(): void
    {
        $branch = new Branch([]);
        $user = new User([]);
        $pricingStrategy = new SinglePricingStrategy($branch, $user, 1);
        $price = $pricingStrategy->calculatePriceForUser(0, 15);
        self::assertEquals(15.0, $price->price());
        self::assertEquals(0, $price->credits());
    }
}
