<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Infrastructure\Exception;

use Glofox\Infrastructure\Exception\HttpClientResponseException;

\App::import('Test/Case', 'GlofoxTestCase');

class HttpClientResponseExceptionTest extends \GlofoxTestCase
{
    public function test_it_keeps_the_parent_instance(): void
    {
        $exception = new HttpClientResponseException(400, 'FOO');

        $this->assertInstanceOf(\UnsuccessfulOperation::class, $exception);
    }
    
    public function test_it_sets_the_correct_status_code_and_message(): void
    {
        $exception = new HttpClientResponseException(400, 'FOO');

        $this->assertSame(400, $exception->getCode());
        $this->assertSame('FOO', $exception->getMessage());
        $this->assertSame('FOO', $exception->getMessageCode());
    }
}
