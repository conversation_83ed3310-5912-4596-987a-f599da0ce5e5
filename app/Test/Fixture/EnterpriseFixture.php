<?php
declare(strict_types=1);

class EnterpriseFixture extends CakeTestFixture
{
    public $import = 'Enterprise';


    public $fields = [
        '_id' => ['type' => 'string', 'key' => 'primary'],
        'corporate_id' => ['type' => 'string', 'null' => false],
        'name' => ['type' => 'string', 'null' => false],
        'created' => ['type' => 'datetime', 'null' => false],
        'modified' => ['type' => 'datetime', 'null' => false],
        'active' => ['type' => 'boolean', 'null' => false, 'default' => true],
    ];

    public function init() : void
    {
        $this->records = [
            [
                '_id' => new MongoId('584ac642dd3c2fbfe13e9114'),
                'corporate_id' => 'corp_9RoundInternational',
                'name' => '9Round International',
                'created' => new MongoDate(strtotime('2018-01-01 15:00:00')),
                'modified' => new MongoDate(strtotime('2019-01-01 15:00:00')),
                'active' => true,
            ],
            [
                '_id' => new MongoId('984ac642dd3c2fbfe13e911c'),
                'corporate_id' => 'corp_AirLocker',
                'name' => 'AirLocker',
                'created' => new MongoDate(strtotime('2018-01-01 15:00:00')),
                'modified' => new MongoDate(strtotime('2019-01-01 15:00:00')),
                'active' => true,
            ],
            [
                '_id' => new MongoId('894ac642dd3c2fbfe13e911d'),
                'corporate_id' => 'corp_Bikerowski',
                'name' => 'Bikerowski',
                'created' => new MongoDate(strtotime('2018-01-01 15:00:00')),
                'modified' => new MongoDate(strtotime('2019-01-01 15:00:00')),
                'active' => true,
            ],
            [
                '_id' => new MongoId('736ac642dd3c2fbfe13e611e'),
                'corporate_id' => 'corp_BlackBox',
                'name' => 'Black Box',
                'created' => new MongoDate(strtotime('2018-01-01 15:00:00')),
                'modified' => new MongoDate(strtotime('2019-01-01 15:00:00')),
                'active' => true,
            ],
        ];
        parent::init();
    }
}
