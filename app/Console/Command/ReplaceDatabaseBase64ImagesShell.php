<?php

class ReplaceDatabaseBase64ImagesShell extends AppShell
{
    private const BASE64_PATTERN = '/^data:image.*/i';
    private const IMAGE_PERMISSION = 'public-read';
    private const DEFAULT_QUERY_LIMIT = 20;

    /** @var BranchConfigurationMobile */
    private $branchConfigurationMobileCakeModel;

    /** @var \Glofox\Storage\CloudStorageInterface */
    private $cloudStorage;

    /** @var \Intervention\Image\ImageManager */
    private $imageManager;

    public function __construct($stdout = null, $stderr = null, $stdin = null)
    {
        parent::__construct($stdout, $stderr, $stdin);

        $this->branchConfigurationMobileCakeModel = app()->make(\BranchConfigurationMobile::class);
        $this->cloudStorage = app()->make(\Glofox\Storage\CloudStorageInterface::class);
        $this->imageManager = app()->make(\Intervention\Image\ImageManager::class);
    }

    public function main()
    {
        $limit = $this->args[0] ?? static::DEFAULT_QUERY_LIMIT;

        foreach ($this->fetchRowsWithBase64Images($limit) as $row) {
            $row = $row['BranchConfigurationMobile'];
            $this->out(sprintf('---> Branch ID: %s (_id: %s)', $row['branch_id'], $row['_id']));
            $this->processConfiguration($row);
        }
    }

    private function processConfiguration(array $row): void
    {
        foreach ($row['images'] as $field => $image) {
            if ($this->shouldBeUploaded($image)) {
                $this->processUpload($field, $image, $row);
            }
        }

        $this->replaceInDatabase($row['images'], $row['_id']);
    }

    private function fetchRowsWithBase64Images(int $limit): array
    {
        return $this->branchConfigurationMobileCakeModel
            ->find('all', [
                'fields' => ['_id', 'images', 'branch_id', 'namespace'],
                'limit' => $limit,
                'conditions' => [
                    '$or' => [
                        ['images.primaryLogo' => new \MongoRegex(static::BASE64_PATTERN)],
                        ['images.secondaryLogo' => new \MongoRegex(static::BASE64_PATTERN)],
                        ['images.programsImage' => new \MongoRegex(static::BASE64_PATTERN)],
                        ['images.coursesImage' => new \MongoRegex(static::BASE64_PATTERN)],
                        ['images.trainersImage' => new \MongoRegex(static::BASE64_PATTERN)],
                        ['images.facilitiesImage' => new \MongoRegex(static::BASE64_PATTERN)],
                    ],
                ],
            ]);
    }

    private function shouldBeUploaded($image): bool
    {
        return 1 === preg_match(static::BASE64_PATTERN, $image);
    }

    private function processUpload($field, $base64Image, &$row)
    {
        $this->out(sprintf('Processing image %s: ', $field), $newLine = 0);

        try {
            $imageContent = (string) $this->imageManager->make($base64Image)->stream();
        } catch (\Intervention\Image\Exception\NotReadableException $e) {
            $this->out(sprintf('WARNING! Could not read %s image: %s', $field, substr($base64Image, 0, 30)));

            return;
        }

        $path = sprintf('%s/%s', $row['namespace'], $this->findImagePath($field, $row['branch_id']));

        $this->cloudStorage->put(
            $path, $imageContent, ['ACL' => static::IMAGE_PERMISSION]
        );

        $url = $this->cloudStorage->url($path);
        $row['images'][$field] = $url;

        $this->out(sprintf('OK! -> %s', $url));
    }

    private function findImagePath($field, $branchId)
    {
        switch ($field) {
            case 'primaryLogo':
                return 'logo.png';
            case 'secondaryLogo':
                return 'logo-secondary.png'; // TODO no secondary logo was found for now
            case 'programsImage':
                return sprintf('branches/%s/programs/default.png', $branchId);
            case 'coursesImage':
                return sprintf('branches/%s/courses/default.png', $branchId);
            case 'trainersImage':
                return sprintf('branches/%s/trainers/default.png', $branchId);
            case 'facilitiesImage':
                return sprintf('branches/%s/facilities/default.png', $branchId);
        }
    }

    private function replaceInDatabase($images, $id): void
    {
        $this->branchConfigurationMobileCakeModel->id = $id;
        $this->branchConfigurationMobileCakeModel->saveField('images', $images);

        $this->out('Database updated!');
    }
}
