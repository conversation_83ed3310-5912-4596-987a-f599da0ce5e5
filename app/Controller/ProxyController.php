<?php

declare(strict_types=1);

use Glofox\Http\Proxy\ProxyDispatcher;
use Glofox\Request;

\App::uses('AppController', 'Controller');

// TODO move this to a middleware. Can't to this atm because middlewares are not dealing with responses
class ProxyController extends \AppController
{
    public function handle(Request $request, ProxyDispatcher $proxy): CakeResponse
    {
        $service = $request->cakeRouteParams()->get('service');
        $method = $request->method();
        $uri = implode('/', $request->cakeRouteParams()->get('pass'));

        // call the internal service
        $response = $proxy->dispatch($service, $method, $uri);

        // convert to cake response
        $this->response->statusCode($response->getStatusCode());
        $this->response->body((string) $response->getBody());
        $this->response->header($response->getHeaders());
        $this->response->type($response->getHeaderLine('content-type'));

        return $this->response;
    }
}
