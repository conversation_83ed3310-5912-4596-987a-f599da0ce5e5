<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\MigrationDataPool\Search\Expressions;

use Glofox\Domain\MigrationDataPool\Models\DataPoolType;
use Glofox\Domain\MigrationDataPool\Search\Expressions\MappingType;
use Webmozart\Expression\Expression;

\App::import('Test/Case', 'GlofoxTestCase');

class MappingTypeTest extends \GlofoxTestCase
{
    public $fixtures = [];
    protected Expression $evaluable;

    public function setUp()
    {
        parent::setUp();

        $this->evaluable = new MappingType(DataPoolType::USER_ID);
    }

    public function testEvaluationPasses(): void
    {
        $data = [
            'mapping_type' => 'user_id',
        ];

        $this->assertTrue($this->evaluable->evaluate($data));
    }
}
