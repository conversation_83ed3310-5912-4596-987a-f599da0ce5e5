<?php

declare(strict_types=1);

use Glofox\Datasource\Exceptions\InvalidMongoIdException;
use Glofox\Domain\AsyncEvents\Events\MembershipBoughtEventMeta;
use Glofox\Domain\Authentication\Auth;
use Glofox\Domain\FeatureFlags\FeatureFlagInterface;
use Glofox\Domain\Memberships\Collections\MembershipsCollection;
use Glofox\Domain\Memberships\Events\Factories\MembershipBoughtEventPayloadFactory;
use Glofox\Domain\Memberships\Exceptions\ISO8601ValidationFailedException;
use Glofox\Domain\Memberships\Filters\FilterRemovedPlans;
use Glofox\Domain\Memberships\Http\MembershipsController as DomainMembershipsController;
use Glofox\Domain\Memberships\Http\Requests\UpsertMembershipRequest;
use Glofox\Domain\Memberships\Models\AcceptedPaymentMethod;
use Glofox\Domain\Memberships\Models\Plan;
use Glofox\Domain\Memberships\Requests\RemoveMembershipDefinitionRequest;
use Glofox\Domain\Memberships\Services\MembershipUpsertService;
use Glofox\Domain\Memberships\Type;
use Glofox\Domain\PaymentMethods\Exceptions\PaymentMethodNotAvailableForMembers;
use Glofox\Domain\PaymentMethods\Exceptions\PaymentMethodNotFoundException;
use Glofox\Domain\PaymentMethodUsers\Exceptions\PaymentMethodUserNotFoundException;
use Glofox\Domain\PaymentProviders\Exceptions\PaymentProviderNotFoundException;
use Glofox\Domain\Users\Models\User;
use Glofox\Infrastructure\Flags\Flag;
use Glofox\Infrastructure\Flags\Flaggers\IrisOnlyAllowStringForSubscriptionPlanIdFlagger;
use Glofox\Http\LegacyRedirectorContract;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;
use Psr\Log\LoggerInterface;

/**
 * Class MembershipsController.
 */
class MembershipsController extends DomainMembershipsController implements LegacyRedirectorContract
{
    /**
     * @var string
     */
    public $name = 'Memberships';
    /**
     * @var array
     */
    public $components = [
        'Utility',
        'JWT',
    ];

    private \Membership $Membership;
    private LoggerInterface $logger;
    private FeatureFlagInterface $featureFlagInterface;

    public function __construct($request = null, $response = null)
    {
        parent::__construct($request, $response);
        $this->Membership = app()->make(\Membership::class);
        $this->logger = app()->make(LoggerInterface::class);
        $this->featureFlagInterface = app()->make(FeatureFlagInterface::class);
    }

    /**
     * Dispatcher for Model.
     *
     * @param string $identifier [description]
     *
     * @return string
     */
    public function dispatcher($identifier = null)
    {
        return parent::dispatch($identifier, $this->Membership);
    }

    /**
     * {@inheritdoc}
     * @throws Exception
     */
    public function redirector(Glofox\Request $request)
    {
        $template = \Router::currentRoute()->template;

        $user = Auth::user();
        $branchId = $user->currentBranchId();

        if ('/:apiVersion/memberships' === $template) {
            $route = sprintf('/2.0/branches/%s/memberships', $branchId);
            $this->redirect($route, 307);

            return;
        }

        if ('/:apiVersion/memberships/:id' === $template) {
            $membershipId = $request->cakeRouteParams()->get('id');

            $route = sprintf('/2.0/branches/%s/memberships/%s', $branchId, $membershipId);
            $this->redirect($route, 307);

            return;
        }

        throw new Exception('Redirector couldnt not find a suitable route to dispatch this request');
    }

    /**
     * Dispatcher for purchase functionality.
     *
     * @param string $identifier [description]
     *
     * @return JsonResponse
     * @throws UnsuccessfulOperation
     * @throws InvalidMongoIdException
     * @throws PaymentMethodUserNotFoundException
     * @throws PaymentMethodNotFoundException
     * @throws PaymentProviderNotFoundException
     */
    public function purchase($identifier)
    {
        $logger = app()->make(LoggerInterface::class);

        $payload = $this->getPayload();
        $user = $this->getUser();
        $this->Membership->setUpInjector($payload, $identifier, $user);

        $isRevertingToPayg = (Type::PAYG === $identifier);

        /** @var MembershipBoughtEventPayloadFactory $membershipBoughtEventPayloadFactory */
        $membershipBoughtEventPayloadFactory = app()->make(MembershipBoughtEventPayloadFactory::class);

        if ($isRevertingToPayg) {
            $response = $this->Membership->backToPAYG();

            if ($response['success']) {
                $this->membershipsEventPublisher->sendMembershipBoughtEvent(
                    new MembershipBoughtEventMeta([
                        'userId' => $user['_id'],
                        'membershipId' => $identifier,
                        'planCode' => $payload['plan_code'],
                    ]),
                    $membershipBoughtEventPayloadFactory->execute(
                        $user['_id'],
                        $payload['payment_method'] ?? null,
                        Collection::make($payload ?? []),
                        null
                    )
                );
            }

            return response()->json($response);
        }

        $targetId = $payload['user_id'] ?? $user['_id'];
        $logger->info(
            sprintf(
                'User %s is purchasing membership %s for user %s with payload %s',
                $user['_id'],
                $identifier,
                $targetId,
                json_encode($payload, JSON_PARTIAL_OUTPUT_ON_ERROR)
            )
        );

        $branchId = $user['branch_id'];
        if (
            $this->isWebPortalWithMembershipStartDate($payload, $branchId) &&
            !$this->isValidIso8601Utc($payload['utc_start_date'])
        ) {
            throw new ISO8601ValidationFailedException();
        }

        if ($this->isWebPortalWithMembershipStartDate($payload, $branchId)) {
            $payload['start_date'] = $payload['utc_start_date'];
            $logger->info(sprintf('Purchase membership with start date=%s', $payload['start_date']));
        }

        $response = $this->Membership->purchase($identifier, $payload);

        return response()->json($response);
    }

    /**
     * Get courses by specifying all in branch, all in branch or by specific id.
     *
     * @param string $id Course id
     *
     * @return array List of courses that match the entry params
     *
     * @internal   This convention is a little boring for clients, but helps us keep clean backend
     *
     * @example    https://www.glofoxlogin/view/{id}
     */
    public function view($id)
    {
        $user = $this->JWT->parseToken();
        $params = ['branch_id' => $user['branch_id']];
        if (isset($id)) {
            $params['_id'] = $id;
        }

        return json_encode(
            $this->Membership->find('all', ['conditions' => array_merge($params, ['active' => true])]),
            JSON_PARTIAL_OUTPUT_ON_ERROR
        );
    }

    /**
     * @throws InvalidMongoIdException
     * @throws PaymentProviderNotFoundException
     * @throws PaymentMethodNotAvailableForMembers
     * @throws UnsuccessfulOperation
     */
    public function upsert(
        UpsertMembershipRequest $request,
        MembershipUpsertService $upsertService
    ): JsonResponse {
        $user = Auth::user();

        $data = $request->data()->toArray();

        $this->validateMembership(
            $data,
            $user
        );

        // Due to front-end misusage, we need to have this fallback in place, otherwise
        // subscription plans that are being newly created won't be identified as
        // "subscription" by the code. The dashboard must be fixed ASAP.

        // The first spec didn't account this and the following error was caused:
        // @see https://glofox.slack.com/archives/C8B375R42/p1558176577015400
        $membershipAsPayload = $request->data();
        $plansAsPayload = $membershipAsPayload->get('plans');

        $onlyAllowStringForSubscriptionPlanIdFlagger = app()->make(
            IrisOnlyAllowStringForSubscriptionPlanIdFlagger::class
        );
        $branchId = $membershipAsPayload->get('branch_id');
        $OnlyAllowStringForSubscriptionPlanId = $onlyAllowStringForSubscriptionPlanIdFlagger->hasByBranchId($branchId);
        if ($OnlyAllowStringForSubscriptionPlanId) {
            $this->logger->info(
                sprintf(
                    'Branch %s have the `is-only-allow-string-for-subscription-planId-enabled` flag enabled.',
                    $branchId
                )
            );
            foreach ($plansAsPayload as &$plan) {
                if (
                    (isset($plan['_subscription_plan_id']) && $plan['_subscription_plan_id']) &&
                    (!isset($plan['subscription_plan_id']) || !$plan['subscription_plan_id'])
                ) {
                    $plan['subscription_plan_id'] = 'INCOMPLETE';
                }
            }
        } else {
            $this->logger->info(
                sprintf(
                    'Branch %s have the `is-only-allow-string-for-subscription-planId-enabled` flag disabled.',
                    $branchId
                )
            );
            foreach ($plansAsPayload as &$plan) {
                if (
                    (isset($plan['_subscription_plan_id']) && $plan['_subscription_plan_id']) &&
                    (!isset($plan['subscription_plan_id']) || !$plan['subscription_plan_id'])
                ) {
                    $plan['subscription_plan_id'] = $plan['_subscription_plan_id'];
                }
            }
        }

        unset($plan);
        $membershipAsPayload->put('plans', $plansAsPayload);
        $membership = $upsertService->execute($membershipAsPayload, $OnlyAllowStringForSubscriptionPlanId);

        return response()->json($membership->toLegacy());
    }

    /*!
     * Search list with params and pagination
     * @param      [type]                   $start  [description]
     * @param      [type]                   $limit  [description]
     * @param      [type]                   $search [description]
     * @return     [type]                           [description]
     */

    /**
     * @param int $page
     * @param int $limit
     * @param null $search
     * @param string $active
     *
     * @return string
     */
    public function listview($page = 1, $limit = 30, $search = null, $active = '1')
    {
        $user = $this->JWT->parseToken();
        $active = '0' == $active ? false : true;
        $params = ['branch_id' => $user['branch_id'], 'active' => $active];
        $this->encodeSearchRegex($params, $search, $this->Membership); //<- add to params built Regex for global search
        $page = (int)$page;
        $limit = (int)$limit;
        $result = $this->Membership->find(
            'all',
            [
                'conditions' => $params,
                'limit' => $limit,
                'page' => $page,
                'order' => ['name' => 1],
            ]
        );

        $result = MembershipsCollection::fromLegacy($result)
            ->pipeline([
                FilterRemovedPlans::class,
            ])
            ->toLegacy();

        $result = $this->addFlexiblePaymentToMembership($result);

        return json_encode($result, JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    /*!
     * Count total records in this collection
     * @return     [type]                   [description]
     */

    /**
     * @param string $active
     *
     * @return string
     */
    public function count($active = '1')
    {
        $user = $this->JWT->parseToken();
        $active = '0' == $active ? false : true;
        $params = ['branch_id' => $user['branch_id'], 'active' => $active];
        $result = $this->Membership->find('count', ['conditions' => $params]);

        return json_encode($result, JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    /**
     * @throws Exception
     */
    public function remove(RemoveMembershipDefinitionRequest $request)
    {
        $user = Auth::user();

        // weird workaround, using ->first()[0] here as sometimes cake might not set 'id', but '0' as the
        // key for the incoming ID. ->get(0) doesn't work, so we use ->first()[0] to get it... it
        // needs investigation
        $membershipId = $request->cakeRouteParams()->get('id') ?? $request->cakeRouteParams()->first()[0];

        if (!is_string($membershipId)) {
            throw new \Exception('No ID provided');
        }

        $item = $this->Membership->findByBranchIdAndId(
            $user->currentBranchId(),
            $membershipId
        );

        if (!$item) {
            throw new \Exception('ID not found');
        }

        if ($item['Membership']['_id'] !== $membershipId) {
            throw new \Exception('ID mismatch');
        }

        $item['Membership']['active'] = false;

        return json_encode($this->Membership->save($item), JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    /************************************** NEW DASHBOARD UTILS ************************************/

    /****************************** REST WEBSERVICE ACTIONS  *******************************************/

    public function index()
    {
        $user = $this->JWT->getUser();
        $allowed_roles = ['admin', 'superadmin'];
        $this->JWT->validateOrFail($allowed_roles, $user['User']['namespace']);
        $this->layout = 'dashboard';
        $this->set('branch_id', $user['User']['branch_id']);
        $this->set('namespace', $user['User']['namespace']);
    }

    public function settings_memberships()
    {
        $user = $this->JWT->getUser();
        $allowed_roles = ['admin', 'superadmin'];
        $this->JWT->validateOrFail($allowed_roles, $user['User']['namespace']);
        $this->layout = 'dashboard';

        $this->loadModel('Branch');
        $this->set('currency', $this->Branch->getCurrency($user['User']['branch_id']));
        $this->set('branch_id', $user['User']['branch_id']);
        $this->set('namespace', $user['User']['namespace']);
    }

    /*********************** REST CALLS ********************************************
     *
     * @param $branch_id
     *
     * @return string
     */
    public function findMembershipTypesByBranchId($branch_id)
    {
        if (empty($branch_id)) {
            return json_encode(['success' => false, 'message' => __('The Branch Id is empty')], JSON_PARTIAL_OUTPUT_ON_ERROR);
        }

        $is_valid_mongoid = $this->Utility->is_valid_mongoid($branch_id);
        if (empty($is_valid_mongoid)) {
            return json_encode(['success' => false, 'message' => __('Invalid Branch Id')], JSON_PARTIAL_OUTPUT_ON_ERROR);
        }

        $as_array = true;
        $display_payg = false;
        $this->loadModel('Branch');
        $membership_types_list = $this->Branch->findActiveMembershipTypesList($branch_id, $as_array, $display_payg);

        return json_encode($membership_types_list, JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    /**
     * [findAllByBranchId description].
     *
     * @param $branch_id
     *
     * @return string [type]            [description]
     *
     * @internal param $ [type] $branch_id [description]
     */
    public function findAllByBranchId($branch_id)
    {
        // Validate Branch Id
        if (empty($branch_id)) {
            return json_encode(['success' => false, 'message' => __('The Branch Id is empty')], JSON_PARTIAL_OUTPUT_ON_ERROR);
        }
        $is_valid_mongoid = $this->Utility->is_valid_mongoid($branch_id);
        if (empty($is_valid_mongoid)) {
            return json_encode(['success' => false, 'message' => __('Invalid Branch Id')], JSON_PARTIAL_OUTPUT_ON_ERROR);
        }

        $find_memberships = $this->Membership->findAllByBranchId($branch_id);

        $memberships = [];

        foreach ($find_memberships as $membership) {
            $memberships[] = $membership['Membership'];
        }

        return json_encode(['success' => true, 'memberships' => $memberships], JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    /**
     * [findAllNotPrivateByBranchId description].
     *
     * @param $branch_id
     *
     * @return string [type]            [description]
     *
     * @internal param $ [type] $branch_id [description]
     */
    public function findAllNotPrivateByBranchId($branch_id)
    {
        // Validate Branch Id
        if (empty($branch_id)) {
            return json_encode(['success' => false, 'message' => __('The Branch Id is empty')], JSON_PARTIAL_OUTPUT_ON_ERROR);
        }
        $is_valid_mongoid = $this->Utility->is_valid_mongoid($branch_id);
        if (empty($is_valid_mongoid)) {
            return json_encode(['success' => false, 'message' => __('Invalid Branch Id')], JSON_PARTIAL_OUTPUT_ON_ERROR);
        }

        $find_memberships = $this->Membership->findAllNotPrivateByBranchId($branch_id);

        $memberships = [];

        foreach ($find_memberships as $membership) {
            $memberships[] = $membership['Membership'];
        }

        return json_encode(['success' => true, 'memberships' => $memberships], JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    /**
     * @param $branch_id
     * @param $membership_id
     *
     * @return string
     */
    public function findByBranchIdAndId($branch_id, $membership_id)
    {
        // Validate Branch Id
        if (empty($branch_id)) {
            return json_encode(['success' => false, 'message' => __('The Branch Id is empty')], JSON_PARTIAL_OUTPUT_ON_ERROR);
        }
        $is_valid_mongoid = $this->Utility->is_valid_mongoid($branch_id);
        if (empty($is_valid_mongoid)) {
            return json_encode(['success' => false, 'message' => __('Invalid Branch Id')], JSON_PARTIAL_OUTPUT_ON_ERROR);
        }

        // Validate Membership Id
        if (empty($membership_id)) {
            return json_encode(
                ['success' => false, 'message' => __('The Membership Id is empty')],
                JSON_PARTIAL_OUTPUT_ON_ERROR
            );
        }
        $is_valid_mongoid = $this->Utility->is_valid_mongoid($membership_id);
        if (empty($is_valid_mongoid)) {
            return json_encode(['success' => false, 'message' => __('Invalid Membership Id')], JSON_PARTIAL_OUTPUT_ON_ERROR);
        }

        $membership = $this->Membership->findByBranchIdAndId($branch_id, $membership_id);
        if (empty($membership)) {
            return json_encode(
                ['success' => false, 'message' => __('There is no membership with that id')],
                JSON_PARTIAL_OUTPUT_ON_ERROR
            );
        }

        return json_encode(['success' => true, 'membership' => $membership['Membership']], JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    /**
     * [get_time_units description].
     *
     * @return string [type] [description]
     */
    public function get_time_units()
    {
        $response = [];
        $response['time_units'] = $this->Membership->get_time_units();
        foreach ($response['time_units'] as &$time_unit) {
            $time_unit['label'] = ucfirst($time_unit['value']);
        }
        $response['default_time_unit'] = $this->Membership->get_time_unit('default');
        $response['default_time_unit']['label'] = ucfirst($response['default_time_unit']['value']);

        return json_encode($response, JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    /**
     * [deletePlan description].
     *
     * @param null $membership_id
     * @param null $plan_code
     *
     * @return string [type]                [description]
     *
     * @throws JsonException
     * @internal param $ [type] $plan_code     [description]
     * @internal param $ [type] $membership_id [description]
     */
    public function deletePlan($membership_id = null, $plan_code = null)
    {
        if (!$this->request->is('post')) {
            return json_encode(['success' => false, 'message' => 'Invalid POST request']);
        }

        if (empty($membership_id)) {
            return json_encode(['success' => false, 'message' => 'Empty membership id']);
        }

        if (empty($plan_code)) {
            return json_encode(['success' => false, 'message' => 'Empty plan code']);
        }

        $membership = $this->Membership->findById($membership_id);
        if (empty($membership)) {
            return json_encode(['success' => false, 'message' => 'There is no membership with that id']);
        }

        //$plan_found = false;
        $membership_plans = [];
        foreach ($membership['Membership']['plans'] as $key => $plan) {
            if ($plan['code'] != $plan_code) {
                $membership_plans[] = $plan;
            }
        }
        $membership['Membership']['plans'] = $membership_plans;

        $this->Membership->read(null, $membership_id);
        $save_membership = $this->Membership->save($membership);
        if ($save_membership) {
            return json_encode(
                [
                    'success' => true,
                    'Membership' => $save_membership['Membership'],
                    'message' => 'Plan successfully deleted',
                ],
                JSON_PARTIAL_OUTPUT_ON_ERROR
            );
        }

        return json_encode(
            ['success' => false, 'message' => 'An error ocurred while saving the membership plan'],
            JSON_THROW_ON_ERROR
        );
    }

    /**
     * [saveMembershipsOrder description].
     *
     * @param $branch_id
     *
     * @return string [type]            [description]
     *
     * @throws JsonException
     * @internal param $ [type] $branch_id [description]
     */
    public function saveMembershipsOrder($branch_id)
    {
        $memberships_id = json_decode(file_get_contents('php://input'), null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        if (empty($branch_id)) {
            return json_encode(['success' => false, 'message' => 'Invalid branch_id.'], JSON_THROW_ON_ERROR);
        }

        if (empty($memberships_id)) {
            return json_encode(
                ['success' => false, 'message' => 'Invalid Json sent or empty data.'],
                JSON_THROW_ON_ERROR
            );
        }

        $this->Membership->saveMembershipsOrder($branch_id, $memberships_id);

        return json_encode(['success' => true], JSON_THROW_ON_ERROR);
    }

    /**
     * ANGULARJS ENABLED SERVICES BELOW.
     */

    /**
     * Find all memberships for current branch.
     *
     * @return string [type] [description]
     */
    public function findAll()
    {
        //Validate user
        $user = $this->JWT->getUser();
        $allowed_roles = ['admin', 'superadmin'];
        $this->JWT->validateOrFail($allowed_roles, $user['User']['namespace']);
        $branch_id = $user['User']['branch_id'];

        //Get facilities
        $memberships = $this->Membership->findAllByBranchId($branch_id);

        return json_encode($memberships, JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    private function addFlexiblePaymentToMembership(array $result): array
    {
        $memberships = [];
        foreach ($result as $membership) {
            /** @var Plan $plan */
            $plans = [];
            foreach ($membership['Membership']['plans'] as $plan) {
                $plan['accepted_payment_methods'][] = new AcceptedPaymentMethod([
                    'active' => true,
                    'type_id' => \Glofox\Domain\PaymentMethods\Type::FLEXIBLE,
                ]);
                $plans[] = $plan;
            }
            $membership['Membership']['plans'] = $plans;
            $memberships[] = $membership;
        }
        return $memberships;
    }

    /**
     * @throws Exception
     */
    private function validateMembership(array $membership, User $user): void
    {
        if (empty($membership['name'])) {
            throw new \Exception('PLEASE_TYPE_A_NAME');
        }

        if (empty($membership['description'])) {
            throw new \Exception('PLEASE_TYPE_A_DESCRIPTION');
        }

        $validatePlans = $this->Membership->validatePlans($membership);
        if (!$validatePlans['success']) {
            throw new \Exception($validatePlans['message']);
        }
        $isAdmin = (in_array($user['type'], [UserType::ADMIN, UserType::SUPERADMIN]));

        if (!$isAdmin) {
            throw new UnauthorizedException('The action cannot be performed by the logged-in user');
        }

        // check if the membership belongs to the branch of the active user is roaming is disabled
        if (!isset($membership['roaming_enabled']) || (false === $membership['roaming_enabled'])) {
            $membershipBranchId = $membership['branch_id'];
            if ($user->currentBranchId() != $membershipBranchId) {
                throw new \Glofox\Exception('Active user branch is required to match the memberships branch');
            }
        }

        if (isset($membership['roaming_enabled']) && true === $membership['roaming_enabled']) {
            $noBranchesAreSelected = (!is_countable($membership['branches']) || count($membership['branches']) === 0);

            if ($noBranchesAreSelected) {
                throw new \Exception('Roaming memberships are required to have at least 1 roaming branch');
            }

            $currentAdminBranchIsSelected = in_array($user->currentBranchId(), $membership['branches']);

            if (!$currentAdminBranchIsSelected) {
                throw new \Exception('Please select your own branch as one of the roaming branches of this membership');
            }
        }
    }

    /**
     * Determines if the provided payload has the utc_start_date set and webportal-membership-start-date feature flag is enabled.
     * 
     * @param array $payload The data payload.
     * @param string $branchId The branch id.
     * @return bool True if the flag is enabled and utc_start_date is set, false otherwise.
     */
    private function isWebPortalWithMembershipStartDate(array $payload, string $branchId): bool
    {
        $isWebPortalMembershipStartDateEnabled = $this->featureFlagInterface
            ->withFlag(Flag::IS_WEBPORTAL_MEMBERSHIP_START_DATE())
            ->hasByBranchId($branchId);

        if (!$isWebPortalMembershipStartDateEnabled) {
            return false;
        }

        return null !== $payload['utc_start_date'];
    }
}
