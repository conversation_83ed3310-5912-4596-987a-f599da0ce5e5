<?php

declare(strict_types=1);

namespace Glofox\Repositories\Search\Expressions\Shared;

use Glofox\Repositories\Filters\ConditionsCollection;
use Glofox\Repositories\Filters\Evaluable;
use Glofox\Repositories\Repository;
use Glofox\Repositories\Search\Expressions\Conditionable;
use Glofox\Repositories\Search\Expressions\HasConditions;
use Glofox\Repositories\Search\Expressions\Key;
use Webmozart\Expression\Expr;

class FieldStartsWith extends Key implements Evaluable, Conditionable
{
    use HasConditions;

    private string $field;
    private string $value;

    public function __construct(string $field, string $value)
    {
        $this->field = $field;
        $this->value = $value;
        parent::__construct($field, Expr::startsWith($value));
    }

    public function field() : string
    {
        return $this->field;
    }

    public function applyToConditions(ConditionsCollection $conditions, Repository $repository = null): void
    {
        $conditions->push([
            $this->field => ['$regex' => new \MongoRegex('/^' . $this->value . '/i')],
        ]);
    }
}
