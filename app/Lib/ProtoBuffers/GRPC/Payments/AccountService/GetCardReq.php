<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: card.proto

namespace GRPC\Payments\AccountService;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>grpc.GetCardReq</code>
 */
class GetCardReq extends \Google\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>int32 ID = 1;</code>
     */
    protected $ID = 0;
    /**
     * Generated from protobuf field <code>string PSPUUID = 2;</code>
     */
    protected $PSPUUID = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int $ID
     *     @type string $PSPUUID
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Card::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>int32 ID = 1;</code>
     * @return int
     */
    public function getID()
    {
        return $this->ID;
    }

    /**
     * Generated from protobuf field <code>int32 ID = 1;</code>
     * @param int $var
     * @return $this
     */
    public function setID($var)
    {
        GPBUtil::checkInt32($var);
        $this->ID = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string PSPUUID = 2;</code>
     * @return string
     */
    public function getPSPUUID()
    {
        return $this->PSPUUID;
    }

    /**
     * Generated from protobuf field <code>string PSPUUID = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setPSPUUID($var)
    {
        GPBUtil::checkString($var, True);
        $this->PSPUUID = $var;

        return $this;
    }

}

