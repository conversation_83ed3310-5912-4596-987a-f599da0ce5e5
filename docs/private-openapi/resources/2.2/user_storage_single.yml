delete:
  tags:
    - UserStorage
  summary: ''
  operationId: delete-user-storage
  description: This endpoint deletes a given user storage by key
  x-internal: true
  security:
    - coreapi_auth: [ ]
  parameters:
    - schema:
        type: string
      name: branchId
      in: path
      required: true
    - schema:
        type: string
      name: userId
      in: path
      required: true
    - schema:
        type: string
      name: storageKey
      in: path
      required: true
  responses:
    '204':
      description: No Content
    '400':
      description: Bad request
      content:
        application/json:
          schema:
            $ref : "../../schemas/error.yml"