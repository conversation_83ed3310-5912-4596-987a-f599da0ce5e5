type: object
properties:
  private:
    type: boolean
    description: The visibility of the events generated in the program
  email_admin:
    type: boolean
    description: The capacity to receive notification on booking requests
  branch_id:
    type: string
    description: The unique identifier of the branch
  namespace:
    type: string
    description: The namespace of the studio
  active:
    type: boolean
    description: The current status of the program
  schedule:
    type: array
    items:
      type: object
      properties:
        days_week:
          type: integer
          description: The day of the week of the event
        start_time:
          type: string
          description: The UTC start time of the event
        end_time:
          type: string
          description: The UTC end time of the event
        active:
          type: boolean
          description: The status of the event
        facility:
          type: string
          description: The identifier of the facility
        level:
          type: string
          description: The required level for the event
        trainers:
          type: array
          description: The list of identifiers of the trainers added to the event
          items:
            type: string
        size:
          type: integer
          description: The capacity of the event
        displayAdd:
          type: boolean
          description: The current status of the campaign
        code:
          type: string
          description: The identifier of the schedule
  schedule_default:
    type: object
    properties:
      facility:
        type: string
        description: The identifier of the facility
      trainers:
        type: array
        description: The list of trainers added
        items:
          type: string
      size:
        type: integer
        description: The capacity of the event
      level:
        type: string
        description: The required level
  categories:
    type: array
    description: The list of the identifiers of the categories for the program
    items:
      type: string
  name:
    type: string
    description: The label of the program
  description:
    type: string
    description: The characteristics of the program
  image:
    type: string
    description: The identifier to retrieve the image
  allowed_member_types:
    description: The list of types of users that can book a event in the program
    type: array
    items:
      type: object
      properties:
        price:
          type: integer
          description: The price of the event
        type:
          type: string
          description: The identifier of the type
  date_start:
    type: string
    description: The local time the program starts
  date_finish:
    type: string
    description: The local time the program ends
  facility:
    type: string
    description: The identifier of the facility
  pricing:
    type: string
    description: The type of pricing of the program
  spot_booking_enabled:
    type: boolean
    description: The capacity to book spots in the events of the program
