parameters:
  - schema:
      type: string
    name: branchId
    in: path
    required: true
get:
  tags:
    - Appointments
  summary: ''
  operationId: get-appointments
  description: This endpoint returns filtered appointments of a given branch
  x-internal: true
  security:
    - coreapi_auth: [ ]
  parameters:
    - schema:
        type: string
        maxLength: 50
      in: query
      name: search-query
    - schema:
        type: string
      in: query
      name: sort-by
      description: field | -field
    - schema:
        type: integer
        minimum: 1
      in: query
      name: page
    - schema:
        type: integer
        minimum: 1
        maximum: 50
      in: query
      name: limit
    - schema:
        type: string
      in: query
      name: active
    - schema:
        type: string
      in: query
      name: include
      description: staff
    - schema:
        type: string
      in: query
      name: staff-id
    - schema:
        type: string
      in: query
      name: private
  responses:
    '200':
      description: OK
      content:
        application/json:
          schema:
            allOf:
              - $ref: "../../schemas/pagination.yml"
              - type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: "../../schemas/appointment.yml"
post:
  tags:
    - Appointments
  summary: ''
  operationId: create-appointment
  description: This endpoint creates a new appointment
  x-internal: true
  security:
    - coreapi_auth: [ ]
  requestBody:
    required: true
    content:
      application/json:
        schema:
          $ref: '../../requests/2.1/create_appointment.yml'
  responses:
    '200':
      description: OK
      content:
        application/json:
          schema:
            $ref: '../../schemas/appointment.yml'