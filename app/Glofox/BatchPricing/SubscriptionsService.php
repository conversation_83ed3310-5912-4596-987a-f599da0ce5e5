<?php

namespace Glofox\BatchPricing;

use Glofox\Domain\Bookings\Search\Expressions\BranchId;
use Glofox\Domain\SalesTaxes\Services\PriceCalculatorServiceInterface;
use Glofox\Domain\Users\Repositories\UsersRepository;
use Glofox\Payments\Entities\Subscription\Contracts\SubscriptionHandlerContract;
use Glofox\Payments\Entities\Subscription\Internal\UpdateItemPricesLineItem;
use Glofox\Payments\Entities\Subscription\Models\Subscription;
use Glofox\Repositories\Search\Expressions\Shared\FieldExists;
use Glofox\Domain\Memberships\Services\AddonServiceInterface;
use Glofox\Domain\Users\Models\User;

class SubscriptionsService
{
    /**
     * @var SubscriptionHandlerContract
     */
    protected $subscriptionService;

    /**
     * @var Glofox\Domain\Users\Repositories\UsersRepository
     */
    protected $usersRepository;

    /**
     * @var Glofox\Domain\SalesTaxes\Services\PriceCalculatorServiceInterface
     */
    protected $priceCalculator;

    /**
     * @var Glofox\Domain\Memberships\Services\AddonServiceInterface
     */
    protected $addonService;

    public function __construct(
        SubscriptionHandlerContract $subscriptionService,
        UsersRepository $usersRepository,
        PriceCalculatorServiceInterface $priceCalculator,
        AddonServiceInterface $addonService
    ) {
        $this->subscriptionService = $subscriptionService;
        $this->usersRepository = $usersRepository;
        $this->priceCalculator = $priceCalculator;
        $this->addonService = $addonService;
    }

    /**
     * Recalculates the prices of all member subscriptions to ensure that total price remains
     * the same, post a tax mode change.
     * 
     * Both memberships and addons are affected.
     *
     * @return array[] Results
     */
    public function recalculateSubscriptions(string $branchId, string $newTaxMode): array
    {
        $users = $this->getSubscriptionsByBranchId($branchId);
        $results = [];

        foreach ($users as $user) {
            try {
                $calculateProducts = [];
                array_push($calculateProducts, self::generateMembershipDetails($user));
                array_push($calculateProducts, ...$this->fetchMemberAddons($branchId, $user->id()));

                $calculateOptions = [
                    'discounts' => false,
                ];
                $calculateOptions['taxmode'] = $newTaxMode === 'inclusive' ? 'exclusive' : 'inclusive';
                $calculateResponse = $this->priceCalculator->calculateTaxes($branchId, json_encode([
                    'products' => $calculateProducts
                ], JSON_PARTIAL_OUTPUT_ON_ERROR), $calculateOptions);
                $calculateResponseData = $calculateResponse->getData();

                $lineItems = self::generateUpdatePricesLineItems(
                    $calculateResponseData->products, $newTaxMode,
                );
                $this->updateItemPrices(
                    $user->membership()->subscription()->id(),
                    ...$lineItems
                );

                array_push($results, ...self::generateUpdateSubscriptionResults($lineItems, $calculateProducts));
            } catch (\Throwable $th) {
                array_push($results, [
                    'result' => 'error',
                    'error' => "failed to update subscription for user {$user->id()}: $th",
                    'type' => 'subscription',
                ]);
            }
        }

        return $results;
    }

    /**
     * @return \Glofox\Payments\Entities\Subscription\Models\Subscription
     */
    public function updateItemPrices(
        int $subscriptionID,
        UpdateItemPricesLineItem ...$lineItems
    ): Subscription {
        return $this->subscriptionService->updateItemPrices($subscriptionID, ...$lineItems);
    }

    /**
     * @return \Glofox\Payments\Entities\Subscription\Internal\UpdateItemPricesLineItem[]
     */
    private static function generateUpdatePricesLineItems($productBreakdowns, $newTaxMode): array
    {
        $lineItems = [];

        foreach ($productBreakdowns as $product) {
            $newPrice = 0;

            if ($newTaxMode == 'exclusive') {
                $newPrice = $product->net_price;
            } else {
                $newPrice = $product->total_price;
            }

            array_push($lineItems, new UpdateItemPricesLineItem(
                $product->service_type,
                $product->service_id,
                $newPrice
            ));
        }

        return $lineItems;
    }

    /**
     * @return array
     */
    private static function generateMembershipDetails(User $user): array
    {
        $membership = $user->membership();
        $membershipExternalRef = "{$membership->userMembershipId()}:{$membership->id()}";

        return [
            'service_id' => $membershipExternalRef,
            'service_type' => 'memberships',
            'price' => Helpers::toCents($membership->subscription()->price()),
        ];
    }

    /**
     * @return array
     */
    private static function generateUpdateSubscriptionResults($lineItems, $products): array
    {
        $results = [];

        foreach ($lineItems as $key => $lineItem) {
            array_push($results, [
                'oldPrice' => $products[intval($key)]['price'],
                'newPrice' => $lineItem->price(),
                'id' => $lineItem->externalRef(),
                'type' => "{$lineItem->service()} (subscription)",
                'result' => 'success',
            ]);
        }

        return $results;
    }

    /**
     * @return array
     */
    private function fetchMemberAddons($branchId, $memberId): array
    {
        $addonsResponse = $this->addonService->getAddonsByMemberId($branchId, $memberId);
        $addonsResponseData = $addonsResponse->getData();
        $products = [];

        foreach ($addonsResponseData as $addon) {
            if (isset($addon->concludedAt) || !$addon->definition->plan->isRecurring) {
                continue;
            }

            $addonExternalRef = "{$addon->id}:{$addon->definition->id}";
            array_push($products, [
                'service_id' => $addonExternalRef,
                'service_type' => 'services',
                'price' => $addon->price,
            ]);
        }

        return $products;
    }

    /**
     * @return \Glofox\Domain\Users\Models\User[]
     */
    private function getSubscriptionsByBranchId(string $branchId): array
    {
        return $this->usersRepository->addCriteria(new BranchId($branchId))
            ->addCriteria(new FieldExists('membership.subscription'))
            ->fields(['_id', 'membership'])
            ->find();
    }
}
