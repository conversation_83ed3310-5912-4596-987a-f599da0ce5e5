<?php

namespace CakeTestCases\Glofox\Domain\Transactional\Events\Listeners;

use CakeTestCases\Glofox\Domain\Logger\Traits\MockedLoggerTrait;
use Carbon\Carbon;
use Glofox\Domain\Branches\Models\Branch;
use Glofox\Domain\Charges\Models\Charge;
use Glofox\Domain\Charges\Models\ChargesGroup;
use Glofox\Domain\FeatureFlags\FeatureFlagEnabler;
use Glofox\Domain\Memberships\Models\Membership;
use Glofox\Domain\Memberships\Repositories\MembershipsRepository;
use Glofox\Domain\Transactional\Events\Listeners\SendSubscriptionCyclePaymentHasFailedToAdmins;
use Glofox\Domain\Users\Models\User;
use Glofox\Payments\Entities\Subscription\Events\SubscriptionCyclePaymentHasFailed;
use Mockery\MockInterface;

\App::import('Test/Case', 'GlofoxTestCase');

class SendSubscriptionCyclePaymentHasFailedToAdminsTest extends \GlofoxTestCase
{
    use MockedLoggerTrait;

    /** @var \Mockery\MockInterface|MembershipsRepository */
    private $membershipsRepositoryMock;

    /** @var \Mockery\MockInterface|\User */
    private $userCakeModelMock;

    /** @var FeatureFlagEnabler */
    private $flagger;

    public function setUp()
    {
        parent::setUp();

        /** @var \Mockery\MockInterface $membershipsRepositoryMock */
        $membershipsRepositoryMock = \Mockery::mock(MembershipsRepository::class);

        /** @var \Mockery\MockInterface $userCakeModelMock */
        $userCakeModelMock = \Mockery::mock(\User::class);

        $membershipsRepositoryMock
            ->shouldReceive('addCriteria')
            ->andReturnSelf();

        $membershipsRepositoryMock
            ->shouldReceive('firstOrFail')
            ->andReturn(
                $this->getMembershipMock()
            );

        $this->membershipsRepositoryMock = $membershipsRepositoryMock;
        $this->userCakeModelMock = $userCakeModelMock;

        $this->flagger = \Mockery::mock(FeatureFlagEnabler::class, function (MockInterface $mock) {
            $mock
                ->shouldReceive('has')
                ->andReturnFalse();
        });
    }

    public function tearDown()
    {
        parent::tearDown();

        \Mockery::close();
    }

    /** @test */
    public function itSendsMessage()
    {
        $this->userCakeModelMock
            ->shouldReceive('getAdminEmailsByBranchId')
            ->andReturn(
                $this->getAdminEmailListStub()
            );

        $event = new SubscriptionCyclePaymentHasFailed(
            $this->getUserStub(),
            $this->getWebhookEventIdStub(),
            $this->getChargeGroupStub(),
            $this->getRequesterStub(),
        );

        $listener = new SendSubscriptionCyclePaymentHasFailedToAdmins(
            $this->userCakeModelMock,
            $this->membershipsRepositoryMock,
            $this->loggerMock,
            $this->flagger
        );

        $response = $listener->handle($event);

        $this->assertTrue($response);
    }

    /** @test */
    public function itReturnsErrorWhenThereAreNoAdmins()
    {
        $this->userCakeModelMock
            ->shouldReceive('getAdminEmailsByBranchId')
            ->andReturn(
                []
            );

        $event = new SubscriptionCyclePaymentHasFailed(
            $this->getUserStub(),
            $this->getWebhookEventIdStub(),
            $this->getChargeGroupStub(),
            $this->getRequesterStub(),
        );

        $listener = new SendSubscriptionCyclePaymentHasFailedToAdmins(
            $this->userCakeModelMock,
            $this->membershipsRepositoryMock,
            $this->loggerMock,
            $this->flagger
        );

        $response = $listener->handle($event);

        $this->assertFalse($response);
    }

    public function testSkipSendingEmailIfFeatureFlagEnabled(): void
    {
        $event = new SubscriptionCyclePaymentHasFailed(
            $this->getUserStub(),
            $this->getWebhookEventIdStub(),
            $this->getChargeGroupStub(),
            $this->getRequesterStub(),
        );

        $this->flagger = \Mockery::mock(FeatureFlagEnabler::class, function (MockInterface $mock) {
            $mock
                ->shouldReceive('has')
                ->with('49a7011a05c677b9a916612a')
                ->andReturnTrue()
                ->once();
        });

        $listener = new SendSubscriptionCyclePaymentHasFailedToAdmins(
            $this->userCakeModelMock,
            $this->membershipsRepositoryMock,
            $this->loggerMock,
            $this->flagger
        );

        $response = $listener->handle($event);

        $this->assertFalse($response);
    }

    private function getUserStub(): array
    {
        return [
            '_id' => new \MongoId(),
            'branch_id' => [
                '49a7011a05c677b9a916612a',
                '49a7011a05c677b9a916612b',
            ],
            'name' => 'userName',
            'phone' => 'userPhone',
            'email' => '<EMAIL>',
            'membership' => [
                '_id' => new \MongoId(),
                'branch_id' => '49a7011a05c677b9a916612a',
                'subscription' => [
                    'stripe_id' => '123'
                ]
            ],
        ];
    }

    private function getRequesterStub(): User
    {
        $requester = new User();
        $requester->set('_id', new \MongoId());
        return $requester;
    }

    private function getWebhookEventIdStub(): string
    {
        return 'stubEventId';
    }

    private function getChargeGroupStub(): ChargesGroup
    {
        return new ChargesGroup(
            'inv-1',
            'group-1',
            collect([
                new Charge([
                    'amount' => 1000,
                    'description' => 'foo',
                    'currency' => 'EUR',
                    'next_payment_attempt' => Carbon::now()->modify('+2 hours')->getTimestamp(),
                ]),
            ]),
        );
    }

    private function getAdminEmailListStub(): array
    {
        return [
            '<EMAIL>',
        ];
    }

    private function getMembershipMock(): Membership
    {
        $branchMock = \Mockery::mock(Branch::class)->makePartial();
        $branchMock->shouldReceive('id')->andReturn('49a7011a05c677b9a916612a');

        $membershipMock = \Mockery::mock(Membership::class);
        $membershipMock->shouldReceive('branch')
            ->andReturn($branchMock);

        return $membershipMock;
    }
}
