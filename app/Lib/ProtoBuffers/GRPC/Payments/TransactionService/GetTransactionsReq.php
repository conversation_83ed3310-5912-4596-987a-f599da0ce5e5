<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: action_request.proto

namespace GRPC\Payments\TransactionService;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>grpc.GetTransactionsReq</code>
 */
class GetTransactionsReq extends \Google\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>int32 DestinationAccountID = 1;</code>
     */
    protected $DestinationAccountID = 0;
    /**
     * Generated from protobuf field <code>int64 FromTimestamp = 2;</code>
     */
    protected $FromTimestamp = 0;
    /**
     * Generated from protobuf field <code>int64 UntilTimestamp = 3;</code>
     */
    protected $UntilTimestamp = 0;
    /**
     * Generated from protobuf field <code>string InvoiceID = 4;</code>
     */
    protected $InvoiceID = '';
    /**
     * Generated from protobuf field <code>string GroupID = 5;</code>
     */
    protected $GroupID = '';
    /**
     * Generated from protobuf field <code>int32 ParentID = 6;</code>
     */
    protected $ParentID = 0;
    /**
     * Generated from protobuf field <code>string Status = 7;</code>
     */
    protected $Status = '';
    /**
     * Generated from protobuf field <code>int32 CustomerAccountID = 8;</code>
     */
    protected $CustomerAccountID = 0;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int $DestinationAccountID
     *     @type int|string $FromTimestamp
     *     @type int|string $UntilTimestamp
     *     @type string $InvoiceID
     *     @type string $GroupID
     *     @type int $ParentID
     *     @type string $Status
     *     @type int $CustomerAccountID
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\ActionRequest::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>int32 DestinationAccountID = 1;</code>
     * @return int
     */
    public function getDestinationAccountID()
    {
        return $this->DestinationAccountID;
    }

    /**
     * Generated from protobuf field <code>int32 DestinationAccountID = 1;</code>
     * @param int $var
     * @return $this
     */
    public function setDestinationAccountID($var)
    {
        GPBUtil::checkInt32($var);
        $this->DestinationAccountID = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int64 FromTimestamp = 2;</code>
     * @return int|string
     */
    public function getFromTimestamp()
    {
        return $this->FromTimestamp;
    }

    /**
     * Generated from protobuf field <code>int64 FromTimestamp = 2;</code>
     * @param int|string $var
     * @return $this
     */
    public function setFromTimestamp($var)
    {
        GPBUtil::checkInt64($var);
        $this->FromTimestamp = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int64 UntilTimestamp = 3;</code>
     * @return int|string
     */
    public function getUntilTimestamp()
    {
        return $this->UntilTimestamp;
    }

    /**
     * Generated from protobuf field <code>int64 UntilTimestamp = 3;</code>
     * @param int|string $var
     * @return $this
     */
    public function setUntilTimestamp($var)
    {
        GPBUtil::checkInt64($var);
        $this->UntilTimestamp = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string InvoiceID = 4;</code>
     * @return string
     */
    public function getInvoiceID()
    {
        return $this->InvoiceID;
    }

    /**
     * Generated from protobuf field <code>string InvoiceID = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setInvoiceID($var)
    {
        GPBUtil::checkString($var, True);
        $this->InvoiceID = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string GroupID = 5;</code>
     * @return string
     */
    public function getGroupID()
    {
        return $this->GroupID;
    }

    /**
     * Generated from protobuf field <code>string GroupID = 5;</code>
     * @param string $var
     * @return $this
     */
    public function setGroupID($var)
    {
        GPBUtil::checkString($var, True);
        $this->GroupID = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 ParentID = 6;</code>
     * @return int
     */
    public function getParentID()
    {
        return $this->ParentID;
    }

    /**
     * Generated from protobuf field <code>int32 ParentID = 6;</code>
     * @param int $var
     * @return $this
     */
    public function setParentID($var)
    {
        GPBUtil::checkInt32($var);
        $this->ParentID = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string Status = 7;</code>
     * @return string
     */
    public function getStatus()
    {
        return $this->Status;
    }

    /**
     * Generated from protobuf field <code>string Status = 7;</code>
     * @param string $var
     * @return $this
     */
    public function setStatus($var)
    {
        GPBUtil::checkString($var, True);
        $this->Status = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 CustomerAccountID = 8;</code>
     * @return int
     */
    public function getCustomerAccountID()
    {
        return $this->CustomerAccountID;
    }

    /**
     * Generated from protobuf field <code>int32 CustomerAccountID = 8;</code>
     * @param int $var
     * @return $this
     */
    public function setCustomerAccountID($var)
    {
        GPBUtil::checkInt32($var);
        $this->CustomerAccountID = $var;

        return $this;
    }

}

