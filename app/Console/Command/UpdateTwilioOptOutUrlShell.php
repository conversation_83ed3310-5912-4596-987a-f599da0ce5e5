<?php

declare(strict_types=1);

class UpdateTwilioOptOutUrlShell extends AppShell
{
    public function __construct($stdout = null, $stderr = null, $stdin = null)
    {
        parent::__construct($stdout, $stderr, $stdin);

        $this->messageConfigModel = \ClassRegistry::init(MessageConfig::class);
    }

    public function main()
    {
        $messageConfigurations = $this->messageConfigModel->find('all',
            [
                'conditions' => [],
                'fields' => ['transactional', 'branch_id'],
            ]);

        foreach ($messageConfigurations as $index => $config) {
            if ($index % 50 === 0) {
                $this->log(sprintf('%s entries processed', $index), LOG_INFO);
            }
            sleep(1);
            try {
                $branchId = $config['MessageConfig']['branch_id'];
                $sid = $config['MessageConfig']['transactional']['account_sid'];
                $token = $config['MessageConfig']['transactional']['auth_token'];

                $services = $this->fetchServices($sid, $token);
                $this->updateServices($services, $sid, $token, $branchId);
            } catch (Exception $e) {
                $this->log(sprintf('Unexpected error: %s', $e));
                $this->log(sprintf('error in setting callback url for branch: %s',
                    $config['MessageConfig']['branch_id']), LOG_WARNING);
                continue;
            }
        }
    }

    private function fetchServices($sid, $token)
    {
        $ch = curl_init('https://messaging.twilio.com/v1/Services?PageSize=20');
        curl_setopt($ch, CURLOPT_USERPWD, sprintf('%s:%s', $sid, $token));
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'GET');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $services = json_decode(curl_exec($ch), null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        curl_close($ch);

        if (!property_exists($services, 'services')) {
            throw new Exception(sprintf('Twilio connection error:%s', $services->message));
        }

        return $services->services;
    }

    private function updateServices($services, $sid, $token, $branchId)
    {
        foreach ($services as $service) {
            $inboundRequestUrl = sprintf('%s/2.2/webhooks/%s/twilio?secret=%s', env('GLOFOX_ROOT_DOMAIN'),
                $branchId,
                env('GLOFOX_TWILIO_WEBHOOK_SECRET_KEY'));
            $payload = http_build_query([
                'InboundRequestUrl' => $inboundRequestUrl,
            ]);
            $ch = curl_init(sprintf('https://messaging.twilio.com/v1/Services/%s', $service->sid));
            curl_setopt($ch, CURLOPT_POSTFIELDS, $payload);
            curl_setopt($ch, CURLOPT_USERPWD, sprintf('%s:%s', $sid, $token));
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

            $result = json_decode(curl_exec($ch), null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
            if (!$result->inbound_request_url || $result->inbound_request_url !== $inboundRequestUrl) {
                throw new Exception(sprintf('Opt out url not set: %s',
                    $result->message ?? $result->inbound_request_url));
            }

            curl_close($ch);
        }
    }

}
