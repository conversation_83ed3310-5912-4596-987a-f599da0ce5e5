<?php

namespace Glofox\Domain\PaymentProviders\Http;

use Glofox\Domain\Branches\Models\Branch;
use Glofox\Domain\Branches\Repositories\BranchesRepository;
use Glofox\Domain\Clients\Models\Client;
use Glofox\Domain\Clients\Repositories\ClientsRepository;
use Glofox\Domain\Clients\Search\Expressions\ClientNamespace;
use Glofox\Domain\FeatureFlags\Flaggers\StripeDirectDebitFlagger;
use Glofox\Domain\FeatureFlags\Flaggers\StripePosIntegrationFlagger;
use Glofox\Domain\Integrators\Models\Integrator;
use Glofox\Domain\Integrators\Repositories\IntegratorsRepository;
use Glofox\Domain\Integrators\Search\Expressions\InPaymentProviderName;
use Glofox\Domain\Integrators\Search\Expressions\NamespacesContains;
use Glofox\Domain\PaymentMethods\Models\Provider;
use Glofox\Domain\PaymentMethods\Type;
use Glofox\Domain\PaymentProviders\Models\AvailableCountry;
use Glofox\Domain\PaymentProviders\Models\PaymentProvider;
use Glofox\Domain\PaymentProviders\Repositories\PaymentProvidersRepository;
use Glofox\Domain\PaymentProviders\Requests\ViewRequest;
use Glofox\Domain\PaymentProviders\Search\Expressions\AvailableInCountry;
use Glofox\Domain\PaymentProviders\Transformers\PaymentProviderTransformer;
use Glofox\Domain\ProductLedGrowth\Payment\ProductLedGrowthStripeDefaultChargePercentageAdjuster;
use Glofox\Http\Responses\Paginators\ResourcePaginator;
use Glofox\Repositories\Search\Expressions\Shared\Active;
use Glofox\Repositories\Search\Expressions\Shared\Id;
use Illuminate\Http\JsonResponse;
use \Illuminate\Support\Collection;
use League\Fractal\Resource;
use Psr\Log\LoggerInterface;

\App::uses('AppController', 'Controller');

/**
 * Class PaymentProvidersController.
 */
class PaymentProvidersController extends \AppController
{
    private BranchesRepository $branchesRepository;

    private PaymentProvidersRepository $paymentProvidersRepository;

    private IntegratorsRepository $integratorsRepository;

    private StripeDirectDebitFlagger $stripeDirectDebitFlagger;

    private StripePosIntegrationFlagger $stripePosIntegrationFlagger;

    private ClientsRepository $clientsRepository;

    private ProductLedGrowthStripeDefaultChargePercentageAdjuster $productLedGrowthStripeFeeAdjuster;

    public function __construct($request = null, $response = null)
    {
        parent::__construct($request, $response);

        $this->branchesRepository = app()->make(BranchesRepository::class);
        $this->paymentProvidersRepository = app()->make(PaymentProvidersRepository::class);
        $this->integratorsRepository = app()->make(IntegratorsRepository::class);
        $this->stripeDirectDebitFlagger = app()->make(StripeDirectDebitFlagger::class);
        $this->stripePosIntegrationFlagger = app()->make(StripePosIntegrationFlagger::class);
        $this->clientsRepository = app()->make(ClientsRepository::class);
        $this->productLedGrowthStripeFeeAdjuster = app()->make(ProductLedGrowthStripeDefaultChargePercentageAdjuster::class);
    }

    private function filterProvidersBySameGateway($providers): ?Collection
    {
        $cardGatewayId = null;
        foreach ($providers as $provider) {
            if ($provider->paymentMethodTypeId() == Type::CARD) {
                $cardGatewayId = $provider->gatewayId();
                break;
            }
        }

        if ($cardGatewayId !== null) {
            $providers = $providers->filter(fn(PaymentProvider $provider) =>
                /** @var PaymentProvider $provider */
                $provider->gatewayId() === $cardGatewayId);
        }

        return $providers;
    }

    public function view(ViewRequest $request): JsonResponse
    {
        $data = $request->data();
        $branchId = $request->cakeRouteParams()->get('branchId');
        $isStripeDirectDebitEnabled = $this->stripeDirectDebitFlagger->has($branchId);
        $isStripePOSEnabled = $this->stripePosIntegrationFlagger->has($branchId);
        $shouldNotUsePOS = false;

        /** @var Branch $branch */
        $branch = $this->branchesRepository
            ->addCriteria(new Id($branchId))
            ->firstOrFail();

        /** @var Client $client */
        //        $client = $this->clientsRepository
        //            ->addCriteria(new ClientNamespace($branch->namespace()))
        //            ->firstOrFail();

        $limit = $data->get('limit', 50);
        $page = $data->get('page', 1);

        $providers = $this->paymentProvidersRepository
            ->addCriteria(new Active(true))
            ->addCriteria(new AvailableInCountry($branch->address()->countryCode()))
            ->page($page)
            ->limit($limit)
            ->order([
                'created' => -1
            ])
            ->find();

        $providers = collect($providers);

        if ($isStripeDirectDebitEnabled) {
            $providers = $providers->reject(fn($provider) =>
                /** @var Provider $provider */
                !$provider->isStripeCustom() && $provider->paymentMethodTypeId() == Type::DIRECT_DEBIT);
        } else {
            $providers = $providers->reject(fn($provider) =>
                /** @var Provider $provider */
                $provider->isStripeCustom() && $provider->paymentMethodTypeId() == Type::DIRECT_DEBIT);
        }

        if (!$isStripePOSEnabled) {
            $providers = $providers->reject(fn($provider) =>
                /** @var Provider $provider */
                $provider->isStripeCustom() && $provider->paymentMethodTypeId() == Type::POS_TERMINAL);
        }

        $providersName = $providers->pluck('name');

        /** @var Integrator */
        $integrators = $this->integratorsRepository
            ->addCriteria(new Active(true))
            ->addCriteria(new InPaymentProviderName($providersName))
            ->addCriteria(new NamespacesContains($branch->namespace()))
            ->find();

        $integrators = collect($integrators);

        $paymentProviderNames = $integrators->pluck('payment_provider_names')->flatten()->toArray();

        $canUseRestrictedProviders = !empty($integrators);

        $groupedProviders = $providers->groupBy('payment_method_type_id');

        $availableProviders = [];

        foreach ($groupedProviders as $providerGroup) {
            $providerGroup = collect($providerGroup);

            $unrestricted = $providerGroup->where('is_restricted', false)->first();

            $restricted = $providerGroup
                ->where('is_restricted', true)
                ->filter(fn(PaymentProvider $provider) => \in_array($provider->name(), $paymentProviderNames))->first();

            $provider = $unrestricted;

            if ($canUseRestrictedProviders && !empty($restricted)) {
                if (\in_array($restricted->get('name'), $paymentProviderNames)) {
                    $provider = $restricted;
                }
            }

            if ($provider !== null && $this->shouldNotUsePOS($provider)) {
                $shouldNotUsePOS = true;
            }

            if (empty($provider)) {
                continue;
            }

            $availableProviders[] = $provider;
        }

        $providers = collect($availableProviders);

        if ($shouldNotUsePOS) {
            $providers = $providers->reject(fn($provider) =>
                /** @var Provider $provider */
                $provider->paymentMethodTypeId() == Type::POS_TERMINAL);
        }

        if ($isStripeDirectDebitEnabled) {
            $providers = $this->filterProvidersBySameGateway($providers);
            $paymentProviderName = '';
            /** @var PaymentProvider $ddProvider */
            $ddProvider = null;

            /** @var PaymentProvider $provider */
            foreach ($providers as $provider) {
                if ($provider->paymentMethodTypeId() == Type::DIRECT_DEBIT) {
                    $paymentProviderName = $provider->name();
                    $ddProvider = $provider;
                }
            }

            $parsedProvider = [];

            /** @var PaymentProvider $provider */
            foreach ($providers as $provider) {
                if ($provider->isStripeCustom()) {
                    $provider['sameCardAndDD'] = true;
                    if ($provider->paymentMethodTypeId() != Type::DIRECT_DEBIT && $ddProvider !== null) {
                        $provider['paymentProviderName'] = $paymentProviderName;
                        $provider = $this->mergeFeatures($provider, $ddProvider);
                    }
                }

                $parsedProvider[] = $provider;
            }
            $providers = collect($parsedProvider);
        }

        //        // @see https://glofox.atlassian.net/browse/PLG-6
//        // Disabled until this ticket is spec'ed fully
//
//        foreach ($providers as $provider) {
//            if ($this->productLedGrowthStripeFeeAdjuster->supports($client, $provider)) {
//                $this->productLedGrowthStripeFeeAdjuster->adjust($client, $branch, $provider);
//            }
//        }

        $resource = new Resource\Collection($providers, new PaymentProviderTransformer());
        $resource->setPaginator(new ResourcePaginator($request, collect($providers)));

        $response = fractal()->parseIncludes($request->includes())
            ->createData($resource)
            ->toArray();

        return response()->json($response);
    }

    private function mergeFeatures(PaymentProvider $provider, PaymentProvider $otherProvider): PaymentProvider
    {
        $provider->addFeatures($otherProvider->features());

        $paymentProviderCountries = $provider
            ->availableCountries()
            ->map(function (AvailableCountry $availableCountry) use ($otherProvider) {
                $otherCountryCode = $otherProvider->availableCountries()->findByCountryCode($availableCountry->countryCode());
                if ($otherCountryCode !== null) {
                    $availableCountry->addFeature($otherCountryCode->features());
                }
                return $availableCountry;
            });
        $provider->setAvailableCountries($paymentProviderCountries);
        return $provider;
    }

    private function shouldNotUsePOS($provider): bool
    {
        return strpos($provider->get('name'), 'EZI_DEBIT') !== false;
    }
}
