<?php

declare(strict_types=1);

namespace Glofox\Infrastructure\Flags\Flaggers;

use Glofox\Domain\Branches\Models\Branch;
use Glofox\Infrastructure\Flags\IrisFlags;
use Glofox\Infrastructure\Flags\IrisGenericFlagger;

class IrisOnlyAllowStringForSubscriptionPlanIdFlagger extends IrisGenericFlagger
{
    private const ENABLED_OPTION = 'ENABLED';

    public function shouldEnable(Branch $branch): bool
    {
        return true;
    }

    public function getSlug(): string
    {
        return IrisFlags::IS_ONLY_ALLOW_STRING_FOR_SUBSCRIPTION_PLAN_ID_ENABLED;
    }

    public function getTestEnabledOption(): string
    {
        return static::ENABLED_OPTION;
    }
}
