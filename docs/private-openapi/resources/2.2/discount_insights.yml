get:
  tags:
    - Reports
  summary: ''
  operationId: discount-insights
  description: This endpoint returns discount insights for a given discount
  x-internal: true
  security:
    - coreapi_auth: [ ]
  parameters:
    - schema:
        type: string
      name: branchId
      in: path
      required: true
    - schema:
        type: string
      name: discountId
      in: path
      required: true
  responses:
    '200':
      description: OK
      content:
        application/json:
          schema:
            $ref: "../../schemas/discount_insights.yml"