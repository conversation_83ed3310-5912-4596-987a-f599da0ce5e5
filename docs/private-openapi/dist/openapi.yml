openapi: 3.0.0
x-stoplight:
  id: mexpl8prmzuuu
info:
  title: Private CoreAPI
  version: 1.0.1
externalDocs:
  description: Find out more about CoreAPI
  url: 'https://github.com/glofoxinc/api'
servers:
  - url: 'http://localhost:8889'
  - url: 'https://hopper.glofox.com'
tags:
  - name: Accesses
  - name: Activities
  - name: Announcements
  - name: Assets
  - name: Auth
  - name: Appointments
  - name: Appointment Slots
  - name: Bookings
  - name: BookingSpots
  - name: Branches
  - name: Charge
  - name: Clients
  - name: Courses
  - name: Events
  - name: Experimental
  - name: Facilities
  - name: Leads
  - name: Payroll Groups
  - name: Payroll Rates
  - name: Price Calculator
  - name: ProductLedGrowth
  - name: Products
  - name: Program
  - name: ProgramSlots
  - name: PushNotifications
  - name: ReCaptcha
  - name: Reports
  - name: Staff
  - name: Users
  - name: UserStorage
paths:
  /announcements/upsert:
    post:
      summary: ''
      operationId: announcements-upsert
      tags:
        - Announcements
      description: Create new announcement and update an exisiting announcement.
      requestBody:
        content:
          application/json:
            schema:
              allOf:
                - type: object
                  properties:
                    _id:
                      type: string
                      description: Announcment ID
                    branch_id:
                      type: string
                    namespace:
                      type: string
                    active:
                      type: boolean
                    title:
                      type: string
                    content:
                      type: string
                    type:
                      type: string
                      enum:
                        - IMAGE
                        - VIDEO
                        - ARTICLE
                    author:
                      type: object
                      properties:
                        user_id:
                          type: string
                        first_name:
                          type: string
                        last_name:
                          type: string
                    image_url:
                      type: string
                    video_url:
                      type: string
                    scheduled:
                      type: object
                      properties:
                        is_scheduled:
                          type: boolean
                        start_date:
                          type: integer
                          description: Timestamp in UTC time zone.
                        end_date:
                          type: integer
                          description: Timestamp in UTC time zone.
                    tags:
                      type: array
                      description: An array of tags to mark the post.
                      items:
                        type: string
                        enum:
                          - News
                          - Fitness
                          - Trending
                    restrictions:
                      type: object
                      description: 'Restict, who can open the content.'
                      properties:
                        memberships:
                          type: array
                          items: {}
                    reasons:
                      type: array
                      description: |-
                        Reasons array is returned as a part of response,
                         but it doesn't exist in announcement Model in DB.
                         I would suggest to make it deprecated.
                      deprecated: true
                      items:
                        type: string
                    created:
                      type: integer
                      description: Timestamp of created date and time.
                    modified:
                      type: integer
                      description: Timestamp of modified date and time.
                    published:
                      type: integer
                      description: Timestamp of published date and time.
                  required:
                    - _id
                    - branch_id
                    - namespace
                    - title
                    - type
                - type: object
                  properties:
                    is_scheduled:
                      type: boolean
                    start_date:
                      type: string
                    end_date:
                      type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                oneOf:
                  - type: object
                    properties:
                      success:
                        type: boolean
                        default: true
                      Announcement:
                        $ref: '#/paths/~1announcements~1upsert/post/requestBody/content/application~1json/schema/allOf/0'
                  - $ref: '#/components/schemas/Error'
              examples:
                Success response:
                  value:
                    success: true
                    Announcement:
                      _id: string
                      branch_id: string
                      namespace: string
                      active: true
                      title: string
                      content: string
                      type: IMAGE
                      author:
                        user_id: string
                        first_name: string
                        last_name: string
                      image_url: string
                      video_url: string
                      scheduled:
                        is_scheduled: true
                        start_date: 0
                        end_date: 0
                      tags:
                        - News
                      restrictions:
                        memberships:
                          - null
                      reasons:
                        - string
                      created: 0
                      modified: 0
                      published: 0
                Malicious text was detected:
                  value:
                    success: false
                    message: We needed to edit your text as it contained forbidden symbols. Please review the content of the post before saving.
                    message_code: CONTENT_HTML_SANITISER_FAILED
                    message_data:
                      sanitized_content: <p>Some sanitized content</p>
                    errors:
                      - CONTENT_HTML_SANITISER_FAILED
                Video URLs must be from Youtube:
                  value:
                    success: false
                    message: Video URLs must be from Youtube
                    message_code: Video URLs must be from Youtube
                    message_data:
                      - string
                    errors:
                      - Video URLs must be from Youtube
                Wrong Youtube format:
                  value:
                    success: false
                    message: 'YouTube URLs are required to be in the "youtube.com/watch?v={videoId}" or "youtu.be/{videoId}" formats'
                    message_code: 'YouTube URLs are required to be in the "youtube.com/watch?v={videoId}" or "youtu.be/{videoId}" formats'
                    message_data:
                      - string
                    errors:
                      - 'YouTube URLs are required to be in the "youtube.com/watch?v={videoId}" or "youtu.be/{videoId}" formats'
  '/assets/upload/{model}/{id}/{type}':
    post:
      summary: Uploads a new asset
      operationId: asset-upload
      tags:
        - Assets
      description: Uploads a new asset
      security:
        - coreapi_auth: []
      parameters:
        - name: model
          in: path
          required: true
          schema:
            type: string
        - name: id
          in: path
          required: true
          schema:
            type: string
        - name: type
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                Image:
                  type: string
                  format: binary
                ImageName:
                  type: string
                  enum:
                    - default
                    - room_map
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    default: true
                  message:
                    type: string
                    default: Image successfully uploaded
  '/bookings/getAllByModelIdentifierAndModelId/{modelId}/{modelIdentifier}':
    parameters:
      - schema:
          type: string
        name: modelId
        in: path
        required: true
        description: The unique identifier of the requested event
      - schema:
          type: string
          enum:
            - class
            - course
            - program
            - appointment
        name: modelIdentifier
        in: path
        required: true
        description: The model of the requested event
    get:
      tags:
        - Booking
      summary: Get bookings
      operationId: list-bookings
      description: Use this call to list all the bookings for a given event. It will trigger a bad request response if the required parameters are not added to the request.
      security:
        - coreapi_auth: []
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/paths/~12.1~1bookings/post/responses/200/content/application~1json/schema'
                  - type: object
                    properties:
                      batch_id:
                        type: string
                        description: The batch this booking belongs to
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /programs/upsert:
    post:
      tags:
        - Program
      summary: Create a program
      operationId: upsert
      description: Use this call to create a program in the studio selected. If the creation does not go through it will retrieve an error stating the root cause that generate this error. It will trigger a bad request response if the required parameters are not added to the request.
      security:
        - coreapi_auth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                branch_id:
                  type: string
                  description: The unique identifier of the branch
                namespace:
                  type: string
                  description: The namespace of the studio
                active:
                  type: boolean
                  description: The current status of the program
                schedule:
                  type: array
                  items:
                    type: object
                    properties:
                      days_week:
                        type: integer
                        description: The day of the week of the event
                      start_time:
                        type: string
                        description: The start time in UTC of the event
                      end_time:
                        type: string
                        description: The end time in UTC of the event
                      active:
                        type: boolean
                        description: The current status of the event
                      facility:
                        type: string
                        description: The identifier of the facility
                      level:
                        type: string
                        description: The required level of the event
                      trainers:
                        type: array
                        items:
                          type: string
                          description: The unique identifier of the trainer
                      size:
                        type: integer
                        description: The maximum participants of the event
                      displayAdd:
                        type: boolean
                schedule_default:
                  type: object
                  properties:
                    facility:
                      type: string
                      description: The identifier of the facility
                    trainers:
                      type: string
                      description: The identifier of the trainers added
                    size:
                      type: integer
                      description: The maximum participants
                    level:
                      type: integer
                      description: The required level
                    first_name:
                      type: string
                      description: The name of the requester
                    last_name:
                      type: string
                      description: The last name of the requester
                    phone:
                      type: string
                      description: The phone number of the requester
                    description:
                      type: string
                      description: The description of the program
                    image:
                      type: string
                      description: The url of the image
                    email:
                      type: string
                      description: The email address of the requester
                    bookable:
                      type: boolean
                      description: The status of the program
                    schedule:
                      type: array
                      items:
                        type: object
                        properties:
                          branch_id:
                            type: string
                          namespace:
                            type: string
                          active:
                            type: boolean
                          week_day:
                            type: integer
                          start_time:
                            type: string
                          end_time:
                            type: string
                    time_slot_length:
                      type: integer
                      description: The duration of the slot
                    pricing:
                      type: integer
                      description: The price of the event
                categories:
                  type: array
                  items:
                    type: string
                    description: The identifier of the category for the program
                name:
                  type: string
                  description: The label of the program
                description:
                  type: string
                  description: The characteristics of the program
                image:
                  type: string
                  description: The url to retrieve the image for the program
                classpass:
                  type: string
                  description: The identifier of the class pass
                gympass:
                  type: string
                  description: The identifier of the gympass
                taxes:
                  type: array
                  items:
                    type: string
                    description: The identifier of the selected tax
                allowed_member_types:
                  type: array
                  description: The type of member that can book an event in the program
                  items:
                    type: object
                    properties:
                      price:
                        type: integer
                      type:
                        type: string
                date_start:
                  type: string
                  description: The local time that the program starts
                date_finish:
                  type: string
                  description: The local time that the program ends
                email_admin:
                  type: boolean
                  description: The capacity to receive emails after a booking goes through
                private:
                  type: boolean
                  description: The visibility of the events generated
                spot_booking_enable:
                  type: boolean
                  description: The capacity to book spots in the events generated
              required:
                - name
                - description
                - categories
                - date_start
                - schedule
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Program'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  '/programs/remove/{programId}':
    parameters:
      - schema:
          type: string
          pattern: '^[a-f\d]{24}$'
        name: programId
        in: path
        required: true
    get:
      tags:
        - Program
      summary: Remove a program
      operationId: remove
      description: 'Use this call to remove a program offered by a branch. If some event included in the program that someone is about to remove has any bookings with status ''booked'', this operation will not be completed. To complete this operation all bookings must be deleted prior hitting the endpoint.'
      security:
        - coreapi_auth: []
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Program'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /2.0/activities:
    get:
      tags:
        - Activities
      summary: ''
      operationId: get-activities
      description: This endpoint returns a filtered activities list from a given branch and user grouped by invoice
      x-internal: true
      security:
        - coreapi_auth: []
      parameters:
        - name: user_id
          in: query
          schema:
            type: string
            pattern: '^[0-9a-fA-F]{24}$'
          description: |
            The user id to filter the activities. If not provided, the user id will be taken from the JWT. Only admins and receptionists can access to other users activities.
        - name: exclude_free_bookings
          in: query
          schema:
            type: boolean
            default: false
          description: Exclude free bookings activities
        - name: event_identifiers
          in: query
          schema:
            type: string
            enum:
              - ''
              - CARD_ADDED
              - CARD_REMOVED
              - CARD_REPLACED
              - MANDATE_ADDED
              - MANDATE_REMOVED
              - MANDATE_REPLACED
              - MANDATE_STATUS_CHANGED
              - ENTITY_BOOKED
              - PRODUCT_PURCHASED
              - NON_SUB_MEMBERSHIP_PURCHASED
              - MEMBERSHIP_PRORATED
              - MEMBERSHIP_REMOVED
              - CUSTOM_CHARGE_EXECUTED
              - SUBSCRIPTION_PURCHASED
              - SUBSCRIPTION_CYCLE_PAID
              - SUBSCRIPTION_CYCLE_PAYMENT_FAILED
              - SUBSCRIPTION_PAUSED
              - SUBSCRIPTION_CANCELLED
              - HISTORIC_TRANSACTION_IMPORTED
              - NSF_FEE
              - EXTERNAL_ACCOUNT_UPDATED
            default: ''
          description: Filter activities by event identifier
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                properties:
                  object:
                    type: string
                    default: list
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        invoice_id:
                          type: string
                        user_id:
                          type: string
                          pattern: '^[0-9a-fA-F]{24}$'
                        earliest_date:
                          type: string
                          format: date-time
                        event_identifier:
                          type: string
                          enum:
                            - CARD_ADDED
                            - CARD_REMOVED
                            - CARD_REPLACED
                            - MANDATE_ADDED
                            - MANDATE_REMOVED
                            - MANDATE_REPLACED
                            - MANDATE_STATUS_CHANGED
                            - ENTITY_BOOKED
                            - PRODUCT_PURCHASED
                            - NON_SUB_MEMBERSHIP_PURCHASED
                            - MEMBERSHIP_PRORATED
                            - MEMBERSHIP_REMOVED
                            - CUSTOM_CHARGE_EXECUTED
                            - SUBSCRIPTION_PURCHASED
                            - SUBSCRIPTION_CYCLE_PAID
                            - SUBSCRIPTION_CYCLE_PAYMENT_FAILED
                            - SUBSCRIPTION_PAUSED
                            - SUBSCRIPTION_CANCELLED
                            - HISTORIC_TRANSACTION_IMPORTED
                            - NSF_FEE
                            - EXTERNAL_ACCOUNT_UPDATED
                        transactions:
                          type: array
                          description: Returned only if group_by_invoice is true
                          items:
                            $ref: '#/components/schemas/Activity'
                      required:
                        - user_id
                        - earliest_date
                        - event_identifier
                        - transactions
  /2.0/login:
    post:
      tags:
        - Auth
      summary: ''
      operationId: login
      x-internal: true
      description: 'If login fails, it returns a 200 status code with an error'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                branch_id:
                  type: string
                login:
                  type: string
                  format: email
                password:
                  type: string
                  format: password
              required:
                - branch_id
                - login
                - password
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  user:
                    $ref: '#/components/schemas/User'
                  token:
                    type: string
                  branch:
                    $ref: '#/components/schemas/Branch'
  /2.0/bookings/first:
    parameters:
      - in: query
        name: end
        required: true
        schema:
          type: integer
        description: The end date to look for the bookings collection in UTC Timestamp format
      - in: query
        name: start
        required: true
        schema:
          type: integer
        description: The start date to look for the bookings collection in UTC Timestamp format
    get:
      tags:
        - Bookings
      summary: List first booking
      operationId: list-bookings-first
      description: Use this call to list the first bookings that users make in  a given date range.
      security:
        - coreapi_auth: []
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  _id:
                    type: string
                    description: Unique identifier for the booking
                  first_name:
                    type: string
                    description: First name of the member booked into the event
                  last_name:
                    type: string
                    description: Last name of the member booked into the event
                  phone:
                    type: string
                    description: Phone number of the member booked into the event
                  email:
                    type: string
                    description: Email address of the member booked into the event
                  membership_type:
                    type: string
                    enum:
                      - payg
                      - time
                      - time_classes
                      - num_classes
                    description: |
                      There are 4 different types of membership:
                      - payg: This is a Drop In member with no membership. payg is short for "pay-as-you-go"
                      - time: This is a membership with no limit on the number of bookings made.
                      - num_classes: This is a credits based membership, the user will have credits which you can retrieve with Get Credit Packs by Member ID. It has no end dates for the credits. Note Get Credit Packs by Member ID could return no credits, meaning the members credits are expired
                      - time_classes: This is a subscription membership that limits the number of classes the user can go to. Eg; Max 12 Classes a month
                  name:
                    type: string
                    description: Full name of the member booked into the event
                  time_start:
                    type: integer
                    description: The UTC time that the booking starts
                  membership_name:
                    type: string
                    description: The name of the membership for the event
                  plan_name:
                    type: string
                    description: The name of the plan
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  '/2.0/branches/{branchId}/reports/member-usage':
    get:
      tags:
        - Experimental
      operationId: member-usage
      description: 'The end point calculates the number of booking and accesses a member made in a time range. Bbookings are filtered by status; booked, attended; true and type; events. The accesses are filtered by status; Granted'
      x-internal: true
      security:
        - coreapi_auth: []
      parameters:
        - schema:
            type: string
          name: branchId
          in: path
          required: true
          description: The query will limit results to bookings and access made in this location
        - schema:
            type: string
          in: query
          name: time_start
          description: Unix time stamp. The query will calculate bookings and accesses greater than this timestamp
        - schema:
            type: string
          in: query
          name: time_finish
          description: Unix time stamp. The query will calculate bookings and accesses lower than this timestamp
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  user:
                    $ref: '#/components/schemas/Analytics'
  '/2.0/branches/{branchId}/products':
    parameters:
      - schema:
          type: string
        name: branchId
        in: path
        required: true
    get:
      tags:
        - Products
      summary: ''
      operationId: get-products-by-branch
      description: This end point returns the store products of a given branch
      x-internal: true
      security:
        - coreapi_auth: []
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/Pagination'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          type: object
                          properties:
                            _id:
                              type: string
                              pattern: '^[a-f\d]{24}$'
                            branch_id:
                              type: string
                              pattern: '^[a-f\d]{24}$'
                            namespace:
                              type: string
                            name:
                              type: string
                            description:
                              type: string
                            featured:
                              type: boolean
                            presentations:
                              type: array
                              items:
                                type: object
                            type:
                              type: string
                              description: Type is product
                            image_url:
                              type: string
                              nullable: true
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: Not Found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /2.0/staff:
    get:
      tags:
        - Staff
      summary: ''
      operationId: get-staff
      description: This endpoint returns a filtered staff list from a given branch
      x-internal: true
      security:
        - coreapi_auth: []
      parameters:
        - schema:
            type: string
            enum:
              - ADMIN
              - MEMBER
              - RECEPTION
              - TRAINER
          in: query
          name: type
          description: type of the staff
        - schema:
            type: string
          in: query
          name: active
        - schema:
            type: string
          in: query
          name: sort-by
          description: field | -field
        - schema:
            type: integer
            minimum: 1
          in: query
          name: page
        - schema:
            type: integer
            minimum: 1
            maximum: 50
          in: query
          name: limit
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/Pagination'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          type: object
                          properties:
                            _id:
                              type: string
                              pattern: '^[a-f\d]{24}$'
                            branch_id:
                              type: string
                              pattern: '^[a-f\d]{24}$'
                            namespace:
                              type: string
                            active:
                              type: boolean
                            type:
                              type: string
                              enum:
                                - admin
                                - reception
                                - trainer
                            first_name:
                              type: string
                            last_name:
                              type: string
                            description:
                              type: string
                              nullable: true
                            name:
                              type: string
                            image_url:
                              type: string
                              nullable: true
  /2.0/courses:
    get:
      tags:
        - Courses
      summary: ''
      operationId: get-courses
      description: This endpoint returns a filtered courses list
      x-internal: true
      security:
        - coreapi_auth: []
      parameters:
        - name: page
          description: The page number used to filter the course listings
          in: query
          schema:
            type: integer
            default: 1
        - name: active
          description: The flag showing whether the course is active or not
          in: query
          schema:
            type: boolean
            default: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/Pagination'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          $ref: '#/components/schemas/Course'
  /2.1/bookings:
    post:
      tags:
        - Bookings
      summary: Create a booking
      operationId: create-booking
      description: |
        Use this call to book a user into a class. If a class is full the user will be added to a waitlist if there are spaces remaining on the waitlist.

        If the booking fails it returns and an error code and reason for failure.
      security:
        - coreapi_auth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                branch_id:
                  type: string
                  description: The branch_id of the studio.
                model:
                  type: string
                  description: For class bookings set to events.
                model_id:
                  type: string
                  description: The id of the event the booking is being made for.
                user_id:
                  type: string
                  description: The id of the user the booking is being made for.
                payment_method:
                  type: string
                  description: the payment method being used.
                price:
                  type: number
                  description: The id of the user the booking is being made for.
                  example: 123
                create_slot:
                  type: string
                  description: Set this string if you want to create a slot during the checkout.
                  example: appointment
                  enum:
                    - appointment
                time_start:
                  type: integer
                  description: The timestamp start time for the new slot in UTC.
                staff_id:
                  type: string
                  description: The id of the staff the booking is being made for.
                private:
                  type: boolean
                  description: If the slot is private or not
                size:
                  type: integer
                  maximum: 5
                  default: 1
                  description: 'This will set the capacity of the appointment to a maximum of five. If this field is not sent, the default value of one will be used.'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  _id:
                    type: string
                    description: Unique identifier for the booking.
                  branch_id:
                    type: string
                    description: The branch_id of the studio.
                  namespace:
                    type: string
                    description: The namespace of the studio.
                  user_id:
                    type: string
                    description: The id of the user booked into the event.
                  user_name:
                    type: string
                    description: The name of the user booked into the event.
                  program_id:
                    type: string
                    description: The id of the booked program.
                  event_id:
                    type: string
                    description: The id of the booked event.
                  event_name:
                    type: string
                    description: The name of the booked event.
                  membership_name:
                    type: string
                    description: The name of the membership used to book the class.
                  plan_name:
                    type: string
                    description: The name of the membership plan used to book the class.
                  status:
                    type: string
                    enum:
                      - BOOKED
                      - WAITING
                      - CANCELED
                      - RESERVED
                      - FAILED
                    description: |
                      A booking will have one of three statuses.
                      * Booked
                        * The user is booked into the class

                      * Waiting
                        * The user is on the waiting list for the class.When a space is available they will be notified or they will be automatically added to the class, depending on the studios settings.

                      * Canceled
                        * The user has no active booking for the class. If the user re-books in this status will changed from Canceled to Booked
                  type:
                    type: string
                    description: For class bookings type will be events.
                  time_start:
                    type: integer
                    description: The time in UTC when the class starts.
                  guest_bookings:
                    type: integer
                    description: |
                      Users can book in guests. The number of spaces booked is guest_bookings + 1
                      The booking object counts as 1.

                      Eg;
                      Class size = 10

                      guest_bookings = 3

                      Remaining spaces = Class size minus (guest_bookings +1)

                      Remaining spaces = 6
                  duration:
                    type: integer
                    description: The number of minutes the class is on for. End time of the class is found by adding this to the time start.
                  created:
                    type: integer
                    description: The time in UTC when the bookings was created.
                  modified:
                    type: integer
                    description: The time in UTC when the bookings was last updated.
  '/2.1/branches/{branchId}/store-sales':
    parameters:
      - schema:
          type: string
        name: branchId
        in: path
        required: true
    get:
      tags:
        - Branches
      summary: ''
      operationId: get-store-sales-by-branch
      description: This end point returns filtered store sales of a given branch
      x-internal: true
      security:
        - coreapi_auth: []
      parameters:
        - schema:
            type: string
          in: query
          name: status
          description: COLLECTED | UNCOLLECTED
        - schema:
            type: string
          in: query
          name: sort-by
          description: field | -field
        - schema:
            type: integer
          in: query
          name: page
        - schema:
            type: integer
            maximum: 50
          in: query
          name: limit
          description: <= 50
        - schema:
            type: string
          in: query
          name: user-id
          description: id of the member who purchased the product
        - schema:
            type: string
          in: query
          name: invoice-id
          description: invoice of the purchase
        - schema:
            type: string
          in: query
          name: search-query
          description: 'User, product or presentation name'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/Pagination'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          $ref: '#/components/schemas/StoreSales'
  '/2.1/branches/{branchId}/appointments':
    parameters:
      - schema:
          type: string
        name: branchId
        in: path
        required: true
    get:
      tags:
        - Appointments
      summary: ''
      operationId: get-appointments
      description: This endpoint returns filtered appointments of a given branch
      x-internal: true
      security:
        - coreapi_auth: []
      parameters:
        - schema:
            type: string
            maxLength: 50
          in: query
          name: search-query
        - schema:
            type: string
          in: query
          name: sort-by
          description: field | -field
        - schema:
            type: integer
            minimum: 1
          in: query
          name: page
        - schema:
            type: integer
            minimum: 1
            maximum: 50
          in: query
          name: limit
        - schema:
            type: string
          in: query
          name: active
        - schema:
            type: string
          in: query
          name: include
          description: staff
        - schema:
            type: string
          in: query
          name: staff-id
        - schema:
            type: string
          in: query
          name: private
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/Pagination'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          $ref: '#/components/schemas/Appointment'
    post:
      tags:
        - Appointments
      summary: ''
      operationId: create-appointment
      description: This endpoint creates a new appointment
      x-internal: true
      security:
        - coreapi_auth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                description:
                  type: string
                pricing:
                  type: string
                  enum:
                    - SINGLE
                    - FREE
                    - CUSTOM
                time_slot_length:
                  type: integer
                staff_ids:
                  type: array
                  items:
                    type: string
                    description: Staff id
                allowed_member_types:
                  type: array
                  items:
                    type: object
                    properties:
                      price:
                        type: number
                      type:
                        type: string
                        enum:
                          - payg
                          - member
                      membership_id:
                        type: string
                      memberships:
                        type: array
                        items:
                          type: object
                          properties:
                            id:
                              type: string
                            label:
                              type: string
                    required:
                      - price
                      - type
                categories:
                  type: array
                  items:
                    type: string
                    description: Category id
                private:
                  type: boolean
                  items:
                    type: boolean
                    description: Private toggle
                    nullable: true
                size:
                  type: integer
                  maximum: 5
                  default: 1
                  description: 'This will set the capacity of the appointment to a maximum of five. If this field is not sent, the default value of one will be used.'
              required:
                - name
                - description
                - pricing
                - time_slot_length
                - staff_ids
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Appointment'
  '/2.1/branches/{branchId}/appointments/{appointmentId}':
    parameters:
      - schema:
          type: string
        name: branchId
        in: path
        required: true
      - schema:
          type: string
        name: appointmentId
        in: path
        required: true
    get:
      tags:
        - Appointments
      summary: ''
      operationId: get-appointment-by-id
      description: |-
        This endpoint returns appointment data. If the id is not found, it returns a
        200 status code with an error.
      x-internal: true
      security:
        - coreapi_auth: []
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Appointment'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
    put:
      tags:
        - Appointments
      summary: ''
      operationId: update-appointment-by-id
      description: 'If the id is not found, it returns a 200 status code with an error'
      x-internal: true
      security:
        - coreapi_auth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/paths/~12.1~1branches~1%7BbranchId%7D~1appointments/post/requestBody/content/application~1json/schema'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Appointment'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
    delete:
      tags:
        - Appointments
      summary: ''
      operationId: delete-appointment-by-id
      description: 'If the id is not found, it still returns a 204 status code'
      x-internal: true
      security:
        - coreapi_auth: []
      responses:
        '204':
          description: No Content
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  '/2.1/branches/{branchId}/appointments/{appointmentId}/slots':
    parameters:
      - schema:
          type: string
        name: branchId
        in: path
        required: true
      - schema:
          type: string
        name: appointmentId
        in: path
        required: true
    post:
      tags:
        - Appointment Slots
      summary: ''
      operationId: create-appointment-slot
      description: This endpoint creates a new appointment slot
      x-internal: true
      security:
        - coreapi_auth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                time_start:
                  type: integer
                  description: Timestamp in UTC
                staff_id:
                  type: string
                private:
                  type: boolean
                booked:
                  type: boolean
                size:
                  type: integer
                  maximum: 5
                  default: 1
                  description: 'The maximum number of attendees that can book this slot. This value is a positive number that can be set from 1 to 5. If capacity is not sent on the payload, the default value is set to 1.'
              required:
                - time_start
                - staff_id
                - private
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AppointmentSlot'
    put:
      tags:
        - Appointment Slots
      summary: ''
      operationId: update-appointment-slot
      description: This endpoint updates an existing appointment slot
      x-internal: true
      security:
        - coreapi_auth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                staff_id:
                  type: string
                  description: The trainer assigned to this slot
                time_slot_pattern_id:
                  type: string
                  description: ID of the appointment pattern this slot belongs to
                time_start:
                  type: integer
                  description: Timestamp in UTC for when the slot starts
                date:
                  type: integer
                  description: Date timestamp for the slot
              required:
                - time_start
                - staff_id
                - time_slot_pattern_id
                - date
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AppointmentSlot'
  '/2.1/branches/{branchId}/appointments/{appointmentId}/slots/{slotId}':
    parameters:
      - schema:
          type: string
        name: branchId
        in: path
        required: true
      - schema:
          type: string
        name: appointmentId
        in: path
        required: true
      - schema:
          type: string
        name: slotId
        in: path
        required: true
    get:
      tags:
        - Appointment Slots
      summary: ''
      operationId: get-appointment-slot-by-id
      x-internal: true
      security:
        - coreapi_auth: []
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AppointmentSlot'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
    put:
      tags:
        - Appointment Slots
      summary: ''
      operationId: update-appointment-slot-by-id
      description: This endpoint updates an existing appointment slot
      x-internal: true
      security:
        - coreapi_auth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/paths/~12.1~1branches~1%7BbranchId%7D~1appointments~1%7BappointmentId%7D~1slots/put/requestBody/content/application~1json/schema'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AppointmentSlot'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
    delete:
      tags:
        - Appointment Slots
      summary: ''
      operationId: delete-appointment-slot-by-id
      x-internal: true
      security:
        - coreapi_auth: []
      responses:
        '204':
          description: No Content
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  '/2.1/branches/{branchId}/appointments/{appointmentId}/calculate-price':
    parameters:
      - schema:
          type: string
        name: branchId
        in: path
        required: true
      - schema:
          type: string
        name: appointmentId
        in: path
        required: true
    post:
      tags:
        - Appointments
      summary: ''
      operationId: calculate-price-of-appointment-by-id
      description: This endpoint calculates the price of an appointment for a specific member
      x-internal: true
      security:
        - coreapi_auth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                member_id:
                  type: string
                time_start:
                  type: integer
              required:
                - member_id
                - time_start
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  data:
                    type: object
                    properties:
                      credits:
                        type: integer
                      price:
                        type: integer
                      currency:
                        type: string
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  '/2.1/branches/{branchId}/users':
    parameters:
      - schema:
          type: string
          pattern: '^[a-f\d]{24}$'
        name: branchId
        in: path
        required: true
        description: The branch ID the users are searched in.
    get:
      tags:
        - Users
      summary: 'Search members by filter (email, search string, gympassId)'
      operationId: users-search
      description: |
        If you apply filter by email, you have to consider some points.
        Emails in Glofox are unique for each studio so you might need to see if an email exists in a studio.
        This search checks if a full email address exists. It can not be used for searching for part of an email.
      x-internal: true
      security:
        - coreapi_auth: []
      parameters:
        - in: query
          name: filters
          schema:
            type: object
            properties:
              email:
                type: string
                description: 'Filer by email. If used, then search is made by email.'
              search:
                type: string
                description: |
                  Filer by search string.
                  If used, then search string is used to find matches in user's name, email, phone, access barcode.
              gympassId:
                type: string
                description: 'Filer by gympassId. If used, then search is made by gympassId.'
          style: deepObject
          explode: true
          required: true
          description: |
            The filter applied to search for the users.
            If you need search by email, then you need to pass parameter like ?filters[email]=<EMAIL>.
            If you need search by search string, for example user name, then you need to pass parameter like ?filters[search]=UserName.
        - schema:
            type: array
            items:
              type: string
              enum:
                - membership
                - aggregated_credits
                - profile_rules
          name: includes
          in: query
          style: form
          explode: true
          required: false
          description: |
            If response should include additional information (about membership, credits, etc).
            Profile rules include the rules that are applied to the user in order to restrict their access to some resources.
            For instance it calculates restricted_profile showing if the user has restricted access to the details page of the profile.
            If you need to include profile_rules, then you need to pass parameter like ?includes[]=profile_rules:branch_id({branchId}),
            where {branchId} is the branch ID you are checking access to.
        - schema:
            type: integer
            minimum: 1
          name: page
          in: query
          required: true
        - schema:
            type: integer
            minimum: 1
            maximum: 50
            default: 50
          name: limit
          in: query
          required: false
        - schema:
            type: integer
            default: 0
            enum:
              - 0
              - 1
          name: namespace-search
          in: query
          required: false
          description: |
            If 1, then search is made within the namespace of the client. All users from all roaming branches are searched.
            If 0, then search is made within the branch only.
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    description: Indicates if the request was successful.
                  meta:
                    type: object
                    properties:
                      pagination:
                        type: object
                        properties:
                          count:
                            type: integer
                          perPage:
                            type: integer
                          currentPage:
                            type: integer
                          links:
                            type: object
                            properties:
                              previous:
                                type: string
                              next:
                                type: string
                          total:
                            type: integer
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        _id:
                          type: string
                        branch_id:
                          type: string
                        image:
                          type: string
                        first_name:
                          type: string
                        last_name:
                          type: string
                        phone:
                          type: string
                        email:
                          type: string
                        source:
                          $ref: '#/components/schemas/User/properties/source'
                        created:
                          type: integer
                          description: UTC timestamp of the user creation date
                        lead_status:
                          type: string
                          enum:
                            - MEMBER
                        origin_branch_id:
                          type: string
                        origin_branch_name:
                          type: string
                        access_barcode:
                          type: string
                        membership:
                          type: array
                          items:
                            type: object
                            properties:
                              _id:
                                type: string
                              branch_id:
                                type: string
                              branch_name:
                                type: string
                              type:
                                $ref: '#/paths/~12.0~1bookings~1first/get/responses/200/content/application~1json/schema/properties/membership_type'
                              booked_events:
                                type: integer
                              membership_group_id:
                                type: string
                              plan_code:
                                type: integer
                              plan_price:
                                type: integer
                              starts_on:
                                type: string
                                enum:
                                  - PURCHASE_DATE
                                  - FIRST_BOOKING_DATE
                              roaming_enabled:
                                type: boolean
                              user_membership_id:
                                type: string
                              trial:
                                type: boolean
                              plan_upfront_fee:
                                type: boolean
                              status:
                                $ref: '#/paths/~12.2~1branches~1%7BbranchId%7D~1users~1%7BuserId%7D~1memberships/get/responses/200/content/application~1json/schema/properties/status'
                              branches:
                                type: array
                                items:
                                  type: string
                              start_date:
                                type: number
                                description: The date the current membership cycle starts. This is returned in UTC epoch format.
                              expiry_date:
                                type: number
                                description: The date the current membership cycle ends. This is returned in UTC epoch format.
                              duration_time_unit:
                                type: string
                                enum:
                                  - month
                                  - week
                                  - day
                              duration_time_unit_count:
                                type: integer
                              name:
                                type: string
                        aggregated_credits:
                          type: integer
                        profile_rules:
                          type: object
                          properties:
                            restricted_profile:
                              type: boolean
                              description: |
                                True - if user has restricted access to the details page of the profile.
                                False - if user has full access to the details page of the profile.
                      required:
                        - _id
                        - branch_id
                        - first_name
                        - last_name
                        - phone
                        - email
                        - source
                        - created
                        - lead_status
                        - origin_branch_id
                        - origin_branch_name
                        - access_barcode
  '/2.1/branches/{branchId}/users/{userId}/switch':
    parameters:
      - schema:
          type: string
        name: branchId
        in: path
        required: true
        description: Id of the current branch
      - schema:
          type: string
        name: userId
        in: path
        required: true
    get:
      tags:
        - Auth
      summary: Switching a new branch
      operationId: switch-branch
      description: |-
        Authenticating the user for a new branch. Errors like "YOU_DO_NOT_HAVE_ACCESS_TO_THAT_BRANCH" are returned
        as a 200 status code.
      x-internal: true
      security:
        - coreapi_auth: []
      parameters:
        - schema:
            type: string
          in: query
          name: to
          description: Id of the branch to switch
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  message:
                    type: string
                  message_code:
                    type: string
                  data:
                    type: object
                    properties:
                      token:
                        type: string
                        format: jwt
                      refresh_token:
                        type: string
                        format: base64
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  '/2.1/branches/{branchId}/events/{eventId}/calculate-price':
    parameters:
      - schema:
          type: string
        name: branchId
        in: path
        required: true
      - schema:
          type: string
        name: eventId
        in: path
        required: true
    post:
      tags:
        - Events
      summary: ''
      operationId: calculate-price-of-event-by-id
      description: This endpoint calculates the price of an event for a specific member
      x-internal: true
      security:
        - coreapi_auth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                member_id:
                  type: string
                number_of_bookings:
                  type: integer
              required:
                - member_id
                - number_of_bookings
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                oneOf:
                  - type: object
                    description: Successful response
                    properties:
                      success:
                        type: boolean
                      data:
                        type: object
                        properties:
                          credits:
                            type: integer
                          price:
                            type: integer
                          currency:
                            type: string
                  - type: object
                    description: UserNotFound
                    properties:
                      success:
                        type: boolean
                        default: false
                      message:
                        type: string
                        default: 'User not found - id: {user_id}'
                      message_code:
                        type: string
                        default: 'User not found - id: {user_id}'
                      message_data:
                        type: array
                        items:
                          type: string
                      errors:
                        type: string
                        default: 'User not found - id: {user_id}'
                  - $ref: '#/paths/~12.3~1branches~1%7BbranchId%7D~1leads~1lead-statuses/get/responses/404/content/application~1json/schema'
                  - type: object
                    description: EventNotFound
                    properties:
                      success:
                        type: boolean
                        default: false
                      message:
                        type: string
                        default: 'Event not found - id: {event_id}'
                      message_code:
                        type: string
                        default: 'Event not found - id: {event_id}'
                      message_data:
                        type: array
                        items:
                          type: string
                      errors:
                        type: string
                        default: 'Event not found - id: {event_id}'
                  - type: object
                    description: ProgramNotFound
                    properties:
                      success:
                        type: boolean
                        default: false
                      message:
                        type: string
                        default: 'Program not found - id: {program_id}'
                      message_code:
                        type: string
                        default: 'Program not found - id: {program_id}'
                      message_data:
                        type: array
                        items:
                          type: string
                      errors:
                        type: string
                        default: 'Program not found - id: {program_id}'
                  - type: object
                    description: NotValidId
                    properties:
                      success:
                        type: boolean
                        default: false
                      message:
                        type: string
                        default: 'The following id is not valid: {_id}'
                      message_code:
                        type: string
                        default: 'The following id is not valid: {_id}'
                      message_data:
                        type: array
                        items:
                          type: string
                      errors:
                        type: string
                        default: 'The following id is not valid: {_id}'
                  - type: object
                    description: GuestNotEligibleForEvents
                    properties:
                      success:
                        type: boolean
                        default: false
                      message:
                        type: string
                        default: GUEST_NOT_ELIGIBLE_FOR_EVENTS
                      message_code:
                        type: string
                        default: GUEST_NOT_ELIGIBLE_FOR_EVENTS
                      message_data:
                        type: array
                        items:
                          type: string
                      errors:
                        type: string
                        default: GUEST_NOT_ELIGIBLE_FOR_EVENTS
                  - type: object
                    description: StaffNotEligibleForEvents
                    properties:
                      success:
                        type: boolean
                        default: false
                      message:
                        type: string
                        default: STAFF_NOT_ELIGIBLE_FOR_EVENTS
                      message_code:
                        type: string
                        default: STAFF_NOT_ELIGIBLE_FOR_EVENTS
                      message_data:
                        type: array
                        items:
                          type: string
                      errors:
                        type: string
                        default: STAFF_NOT_ELIGIBLE_FOR_EVENTS
                  - type: object
                    description: MembershipNotSupported
                    properties:
                      success:
                        type: boolean
                        default: false
                      message:
                        type: string
                        default: 'There is a problem with your subscription. Please, contact your studio for more info.'
                      message_code:
                        type: string
                        default: MEMBERSHIP_NOT_SUPPORTED
                  - type: object
                    description: MembershipIsLocked
                    properties:
                      success:
                        type: boolean
                        default: false
                      message:
                        type: string
                        default: 'You cannot book because your membership is locked. Please, contact your studio for more info.'
                      message_code:
                        type: string
                        default: CANNOT_BOOK_DUE_TO_MEMBERSHIP_BEING_LOCKED
                  - type: object
                    description: NotEligibleForEventAndNotEnoughCredits
                    properties:
                      success:
                        type: boolean
                        default: false
                      message:
                        type: string
                        default: NOT_ELIGIBLE_FOR_EVENT_AND_NOT_ENOUGH_CREDITS
                      message_code:
                        type: string
                        default: NOT_ELIGIBLE_FOR_EVENT_AND_NOT_ENOUGH_CREDITS
                      message_data:
                        type: array
                        items:
                          type: string
                      errors:
                        type: string
                        default: NOT_ELIGIBLE_FOR_EVENT_AND_NOT_ENOUGH_CREDITS
                  - type: object
                    description: NoCreditsLeft
                    properties:
                      success:
                        type: boolean
                        default: false
                      message:
                        type: string
                        default: YOU_HAVE_NO_CREDITS_LEFT
                      message_code:
                        type: string
                        default: YOU_HAVE_NO_CREDITS_LEFT
                      message_data:
                        type: array
                        items:
                          type: string
                      errors:
                        type: string
                        default: YOU_HAVE_NO_CREDITS_LEFT
                  - type: object
                    description: GuestBookingsLimitReached
                    properties:
                      success:
                        type: boolean
                        default: false
                      message:
                        type: string
                        default: GUEST_BOOKINGS_LIMIT_REACHED
                      message_code:
                        type: string
                        default: GUEST_BOOKINGS_LIMIT_REACHED
                      message_data:
                        type: array
                        items:
                          type: string
                      errors:
                        type: string
                        default: GUEST_BOOKINGS_LIMIT_REACHED
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  '/2.1/branches/{branchId}/courses/{courseId}/calculate-price':
    parameters:
      - schema:
          type: string
        name: branchId
        in: path
        required: true
      - schema:
          type: string
        name: courseId
        in: path
        required: true
    post:
      tags:
        - Courses
      summary: ''
      operationId: calculate-price-of-course-by-id
      description: This endpoint calculates the price of a course for a specific member
      x-internal: true
      security:
        - coreapi_auth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                member_id:
                  type: string
                number_of_bookings:
                  type: integer
              required:
                - member_id
                - number_of_bookings
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  data:
                    type: object
                    properties:
                      credits:
                        type: integer
                      price:
                        type: integer
                      currency:
                        type: string
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  '/2.1/branches/{branchId}/facilities/{facilityId}/calculate-price':
    parameters:
      - schema:
          type: string
        name: branchId
        in: path
        required: true
      - schema:
          type: string
        name: facilityId
        in: path
        required: true
    post:
      tags:
        - Facilities
      summary: ''
      operationId: calculate-price-of-facility-by-id
      description: This endpoint calculates the price of a facility for a specific member
      x-internal: true
      security:
        - coreapi_auth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                member_id:
                  type: string
                time_start:
                  type: integer
              required:
                - member_id
                - time_start
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  data:
                    type: object
                    properties:
                      credits:
                        type: integer
                      price:
                        type: integer
                      currency:
                        type: string
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  '/2.2/branches/{branchId}/accesses/':
    parameters:
      - schema:
          type: string
          pattern: '^[a-f\d]{24}$'
        name: branchId
        in: path
        required: true
        description: The branch ID the accesses list is fetched for.
    get:
      tags:
        - Accesses
      summary: ''
      operationId: accesses-list
      description: |
        This endpoint returns paginated list of the accesses for the specified studio.
        The maximum date range used to fetch data is 1 day.
        Accesses are sorted by entry date in descending mode. If the entry date is not defined, 
        than the date when access data was added into the system is considered.
      x-internal: true
      security:
        - coreapi_auth: []
      parameters:
        - schema:
            type: integer
          name: utc-start-time
          in: query
          required: true
          description: UTC timestamp of the starting point to fetch accesses.
        - schema:
            type: integer
          name: utc-end-time
          in: query
          required: true
          description: |
            UTC timestamp of the ending point to fetch accesses. 
            It must be at least +1 second more than utc-start-time value. 
            Maximum value is +1 day to the utc-start-time value.
        - schema:
            type: integer
            minimum: 1
            default: 1
          name: page
          in: query
          required: false
        - schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 50
          name: limit
          in: query
          required: false
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/Pagination'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          type: object
                          properties:
                            _id:
                              type: string
                              description: Unique identifier of the access log.
                            user_id:
                              type: string
                              description: Unique identifier of the user accessed the studio.
                            branch_id:
                              type: string
                              description: Unique identifier of the branch the user got access.
                            namespace:
                              type: string
                              description: The namespace the studio belongs to.
                            entry_at:
                              type: integer
                              description: The UTC Timestamp of the time when the user entered the studio.
                            valid_on_entry:
                              type: boolean
                              description: |
                                If the user's membership was valid when they entered the studio. 
                                Or if there where upcoming active bookings.
                            status:
                              type: string
                              enum:
                                - GRANTED
                                - FAILED
                              description: |
                                The status of the access request.
                                The access is GRANTED if valid_on_entry is true or if it was granted directly by integrators.
                                In all other cases the status would be FAILED.
                                Important to note even when the status is not granted by the system,
                                it does not prevent physical access to the studio.
                            origin_branch_id:
                              type: string
                              description: |
                                Unique identifier of the origin branch the user belongs to.
                                It works for roaming studios, when the member belongs to the concrete branch,
                                but is allowed to visit other branches within roaming.
                            origin_branch_name:
                              type: string
                              description: The name of the origin branch the user belongs to.
                            created:
                              type: integer
                              description: The UTC Timestamp when the access log was added.
                            modified:
                              type: integer
                              description: The UTC Timestamp when the access log was modified.
  '/2.2/branches/{branchId}/calculate-taxes':
    post:
      summary: Get tax and discounts breakdown for a given resource.
      description: |
        Calls price calculator service to determine the breakdown for a product(s) including discounts and taxes.

        Price calculator does not determine prorated amounts, or the validity of whether a discount/promo code can be used for a particular item. It is up to client to determine this.

        ## Discounts

        - `discount_ids`: This parameter can only be used by staff/integrators.
        - `promo_code`: This parameter can be used by any user.

        The `discount_ids` and `promo_code` parameters should only ever be used mutually exclusively.
      parameters:
        - in: path
          name: branchId
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                products:
                  items:
                    type: object
                    properties:
                      price:
                        type: number
                        example: 2
                      unit_price:
                        type: number
                        description: The price of a single unit of the item
                        example: 1
                      quantity:
                        type: number
                        description: The quantity of the item
                        example: 2
                      promo_code:
                        type: string
                        example: PROMO-123
                        description: The promo code.
                      discount_ids:
                        items:
                          type: string
                        type: array
                        description: The private ID of a discount.
                      service_type:
                        type: string
                        example: products
                      service_id:
                        type: string
                        example: product-id-123
                  type: array
      tags:
        - Price Calculator
      responses:
        '200':
          description: Response
          content:
            application/json:
              schema:
                type: object
                properties:
                  discounts:
                    type: array
                    items:
                      type: object
                      properties:
                        applied_discounts:
                          items:
                            type: object
                            properties:
                              id:
                                type: string
                                example: discount-id-1
                              name:
                                type: string
                                example: 10% Off Special
                              promo_code:
                                type: object
                                properties:
                                  id:
                                    type: string
                                  code:
                                    type: string
                              num_cycles:
                                type: number
                                description: The number of cycles the discount applies for (against a membership subscription).
                              rate_type:
                                type: string
                                description: The type of discount. This can be either percentage or fixed
                              rate_value:
                                type: number
                                description: |
                                  Based on the rate_type, this is either the percentage value or the fixed amount of the discount.
                                  - for percentage, this is the standard percentage value (0.001% - 100.000%) of the discount (e.g., 12.34 represents 12.34%)
                                  - for fixed, this is the fixed amount of the discount. Represented as a decimal value (e.g., 12.34 represents $12.34)
                                example: 10
                              discount_amount:
                                type: number
                                description: The amount that has been discounted.
                                example: 0.4
                            required:
                              - rate_type
                          type: array
                        discounted_price:
                          type: number
                          description: The price after applying all discounts.
                          example: 1.6
                        discounts_total:
                          type: number
                          description: The sum of the discounts.
                          example: 0.4
                        product_price:
                          type: number
                          description: The original product price that was supplied.
                          example: 2
                  taxes:
                    type: array
                    items:
                      type: object
                      properties:
                        applied_taxes:
                          items:
                            type: object
                            properties:
                              id:
                                type: string
                                description: ID of the tax.
                                example: id-123
                              name:
                                type: string
                                description: Name of the tax.
                                example: City tax
                              price:
                                type: number
                                description: The amount derived from applying the tax.
                                example: 0.19
                              rate:
                                type: number
                                description: The percentage rate of the tax (max 99.999).
                                example: 12
                          type: array
                        net_price:
                          type: number
                          description: The product price minus total taxes.
                          example: 1.6
                        product_price:
                          type: number
                          description: The original price of the product (after discounts).
                          example: 1.6
                        tax_total:
                          type: number
                          description: The sum of all the applied taxes.
                          example: 0.19
                        total_price:
                          type: number
                          description: The final price after applying the taxes (and discounts).
                          example: 1.79
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  '/2.2/branches/{branchId}/reports/discount-insights/{discountId}':
    get:
      tags:
        - Reports
      summary: ''
      operationId: discount-insights
      description: This endpoint returns discount insights for a given discount
      x-internal: true
      security:
        - coreapi_auth: []
      parameters:
        - schema:
            type: string
          name: branchId
          in: path
          required: true
        - schema:
            type: string
          name: discountId
          in: path
          required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  endDate:
                    description: EndDate states the lower range of the data.
                    type: string
                  sales:
                    description: DiscountInsightsSales states the sales related data for a discount.
                    properties:
                      discounts:
                        properties:
                          total:
                            description: Total is the monetary amount associated with pending and successful items.
                            type: number
                          totalForgiven:
                            description: TotalForgiven is the monetary amount associated with forgiven items.
                            type: number
                          totalPending:
                            description: TotalPending is the monetary amount associated with pending items.
                            type: number
                        type: object
                      gross:
                        description: DiscountInsightsSalesGross states the gross sales associated with items the discount is
                        properties:
                          total:
                            description: Total is the monetary amount associated with pending and successful items.
                            type: number
                          totalForgiven:
                            description: TotalForgiven is the monetary amount associated with forgiven items.
                            type: number
                          totalPending:
                            description: TotalPending is the monetary amount associated with pending items.
                            type: number
                        type: object
                    type: object
                  startDate:
                    description: StartDate states the upper range of the data.
                    type: string
                  usages:
                    description: DiscountInsightsUsages states the usage related data for a discount.
                    properties:
                      total:
                        description: Total is the total number of pending and successful applications of the discount
                        type: integer
                      totalDirectUses:
                        description: |-
                          TotalDirectUses is the total number of pending and successful applications exclusively for
                          manual applications of the discount.
                        type: integer
                      totalPromoCodeUses:
                        description: |-
                          TotalPromoCodeUses is the total number of pending and successful applications exclusively for
                          uses of a promo code.
                        type: integer
                      usagesByInterval:
                        description: UsagesByInterval defines the the breakdown of usages based on a specified interval.
                        properties:
                          intervalType:
                            description: 'IntervalType refers to the type of interval: day, week, month, year'
                            type: string
                          intervalValue:
                            description: IntervalValue refers to the value to apply to the interval.
                            type: integer
                          results:
                            description: Results is the usage results based on the interval.
                            items:
                              description: DiscountInsightsUsagesByIntervalItem is an item containing the usages for a specified time period.
                              properties:
                                date:
                                  description: Date is the specified time.
                                  type: string
                                total:
                                  description: Total is the total number of pending and successful applications of the discount
                                  type: integer
                                totalDirectUses:
                                  description: |-
                                    TotalDirectUses is the total number of pending and successful applications exclusively for
                                    manual applications of the discount.
                                  type: integer
                                totalPromoCodeUses:
                                  description: |-
                                    TotalPromoCodeUses is the total number of pending and successful applications exclusively for
                                    uses of a promo code.
                                  type: integer
                              type: object
                            type: array
                        type: object
                      usagesBySource:
                        description: UsagesBySources defines the the breakdown of usages by source.
                        properties:
                          results:
                            items:
                              description: DiscountInsightsUsageBySourceItem states the number of usages for a given source.
                              properties:
                                count:
                                  type: integer
                                source:
                                  type: string
                              type: object
                            type: array
                        type: object
                    type: object
  '/2.2/branches/{branchId}/reports/sales-metrics':
    get:
      tags:
        - Reports
      summary: ''
      operationId: sales-metrics
      description: This endpoint returns sales metrics of a given branch
      x-internal: true
      security:
        - coreapi_auth: []
      parameters:
        - schema:
            type: string
          name: branchId
          in: path
          required: true
        - schema:
            type: string
            format: date
          in: query
          required: true
          name: endPeriod
        - schema:
            type: string
            format: date
          in: query
          required: true
          name: startPeriod
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SalesMetrics'
  '/2.2/branches/{branchId}/reports/member-kpis/{userId}':
    get:
      tags:
        - Reports
      summary: ''
      operationId: member-kpis
      description: This endpoint returns member kpis of a given branch and user
      x-internal: true
      security:
        - coreapi_auth: []
      parameters:
        - schema:
            type: string
            pattern: '^[a-f\d]{24}$'
          name: branchId
          in: path
          required: true
        - schema:
            type: string
            pattern: '^[a-f\d]{24}$'
          name: userId
          in: path
          required: true
        - schema:
            type: string
            format: date
          in: query
          required: false
          name: endPeriod
        - schema:
            type: string
            format: date
          in: query
          required: false
          name: startPeriod
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MemberKPIs'
  '/2.2/branches/{branchId}/reports/visits-insights':
    get:
      tags:
        - Reports
      summary: ''
      operationId: visits-insights
      description: This endpoint returns branch level kpis relating to visits and visit details
      x-internal: true
      security:
        - coreapi_auth: []
      parameters:
        - schema:
            type: string
          name: branchId
          in: path
          required: true
        - schema:
            type: integer
          in: query
          required: false
          name: limit
        - schema:
            type: integer
          in: query
          required: false
          name: offset
        - schema:
            type: string
          in: query
          required: false
          name: sortBy
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VisitsInsights'
  '/2.2/branches/{branchId}/reports/{reportId}':
    get:
      tags:
        - Reports
      summary: ''
      operationId: reportId
      description: This endpoint returns metrics relating to reportId referenced
      x-internal: true
      security:
        - coreapi_auth: []
      parameters:
        - schema:
            type: string
          name: branchId
          in: path
          required: true
        - schema:
            type: string
            enum:
              - current-members/list
              - current-members/by-membership-name
              - current-members/by-membership-status
              - current-members/by-addon-name
          name: reportId
          in: path
          required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                oneOf:
                  - $ref: '#/components/schemas/CurrentMembers'
                  - $ref: '#/components/schemas/CurrentMembersByMembershipStatus'
                  - $ref: '#/components/schemas/CurrentMembersByMembershipName'
                  - $ref: '#/components/schemas/CurrentMembersByMembershipAddonName'
  /2.2/recaptcha-verify:
    post:
      tags:
        - ReCaptcha
      summary: ''
      operationId: validate-recaptcha
      description: This endpoint validates a ReCaptcha token generated by the front-end UI component
      x-internal: true
      security:
        - none: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                recaptchaToken:
                  type: string
                  description: 'The recaptcha token generated by the front-end UI component, as described in https://developers.google.com/recaptcha/docs/verify'
              required:
                - recaptchaToken
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RecaptchaValidationOutcome'
  /2.2/plg/create-client:
    post:
      tags:
        - ProductLedGrowth
      summary: ''
      operationId: plg-create-client
      description: This endpoint creates a client with information provided in the PLG flow
      x-internal: true
      security:
        - none: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                user:
                  type: object
                  properties:
                    firstName:
                      type: string
                    lastName:
                      type: string
                    phone:
                      type: string
                    email:
                      type: string
                    password:
                      type: string
                company:
                  type: object
                  properties:
                    businessName:
                      type: string
                    address:
                      type: object
                      properties:
                        continent:
                          type: string
                        city:
                          type: string
                        country:
                          type: string
                        countryCode:
                          type: string
                        currency:
                          type: string
                        latitude:
                          type: string
                        longitude:
                          type: string
                        state:
                          type: string
                        street:
                          type: string
                        timezoneId:
                          type: string
                mobileApp:
                  type: object
                  properties:
                    logo:
                      type: string
                    primaryColor:
                      type: string
                    secondaryColor:
                      type: string
              required:
                - user
                - company
                - mobileApp
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  branchId:
                    type: string
                  namespace:
                    type: string
  '/2.2/branches/{branchId}/events/{eventId}/spots':
    get:
      tags:
        - BookingSpots
      summary: ''
      operationId: list-booking-spots
      description: This endpoint lists booking spots for a given event
      x-internal: true
      security:
        - coreapi_auth: []
      parameters:
        - schema:
            type: string
          name: branchId
          in: path
          required: true
        - schema:
            type: string
          name: eventId
          in: path
          required: true
        - schema:
            type: string
          name: user_id
          in: query
          required: false
          description: 'When provided, it will return only spots reserved by the given user'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  has_more:
                    type: boolean
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        reserved:
                          type: boolean
                        spot_id:
                          type: string
                        booking_id:
                          type: string
                        user_id:
                          type: string
                        is_auto_assigned:
                          type: boolean
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
    post:
      tags:
        - BookingSpots
      summary: ''
      operationId: reserve-booking-spot
      description: This endpoint reserves a spot for a given event and booking
      x-internal: true
      security:
        - coreapi_auth: []
      parameters:
        - schema:
            type: string
          name: branchId
          in: path
          required: true
        - schema:
            type: string
          name: eventId
          in: path
          required: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                booking_id:
                  type: string
                spot_id:
                  type: string
              required:
                - booking_id
                - spot_id
      responses:
        '201':
          description: OK
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  '/2.2/branches/{branchId}/users/{userId}/storage':
    get:
      tags:
        - UserStorage
      summary: ''
      operationId: list-user-storage
      description: This endpoint lists storage keys for a given branch and user
      x-internal: true
      security:
        - coreapi_auth: []
      parameters:
        - schema:
            type: string
          name: branchId
          in: path
          required: true
        - schema:
            type: string
          name: userId
          in: path
          required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        _id:
                          type: string
                        branch_id:
                          type: string
                        user_id:
                          type: string
                        key:
                          type: string
                        value:
                          type: string
                        type:
                          type: string
                          enum:
                            - string
                        created:
                          type: integer
                        modified:
                          type: integer
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
    post:
      tags:
        - UserStorage
      summary: ''
      operationId: upsert-user-storage
      description: This endpoint upsert new and existing keys with the user storage
      x-internal: true
      security:
        - coreapi_auth: []
      parameters:
        - schema:
            type: string
          name: branchId
          in: path
          required: true
        - schema:
            type: string
          name: userId
          in: path
          required: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: array
              items:
                properties:
                  key:
                    type: string
                  value:
                    type: string
                  type:
                    type: string
                    enum:
                      - string
                required:
                  - key
                  - value
                  - type
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/paths/~12.2~1branches~1%7BbranchId%7D~1users~1%7BuserId%7D~1storage/get/responses/200/content/application~1json/schema/properties/data/items'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  '/2.2/branches/{branchId}/users/{userId}/storage/{storageKey}':
    delete:
      tags:
        - UserStorage
      summary: ''
      operationId: delete-user-storage
      description: This endpoint deletes a given user storage by key
      x-internal: true
      security:
        - coreapi_auth: []
      parameters:
        - schema:
            type: string
          name: branchId
          in: path
          required: true
        - schema:
            type: string
          name: userId
          in: path
          required: true
        - schema:
            type: string
          name: storageKey
          in: path
          required: true
      responses:
        '204':
          description: No Content
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  '/2.2/branches/{branchId}/users/{userId}/cards':
    post:
      tags:
        - UserCards
      summary: ''
      operationId: upsert-user-cards
      description: This endpoint upsert new card for a user
      x-internal: true
      security:
        - coreapi_auth: []
      parameters:
        - schema:
            type: string
          name: branchId
          in: path
          required: true
        - schema:
            type: string
          name: userId
          in: path
          required: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                token:
                  type: string
                options:
                  type: array
                  items:
                    type: string
              required:
                - token
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      type: array
                      items:
                        type: object
                        properties:
                          _id:
                            type: object
                            properties:
                              $oid:
                                type: string
                          user_id:
                            type: string
                          branch_id:
                            type: string
                          brand:
                            type: string
                          last_four:
                            type: integer
                          expiry_month:
                            type: integer
                          expiry_year:
                            type: integer
                          zip_code:
                            type: string
                          zip_code_valid:
                            type: boolean
                          zip_code_check_status:
                            type: string
                          card_provider_id:
                            type: string
                          payment_method_user_id:
                            type: string
                          payment_method_id:
                            type: string
                          created:
                            type: object
                            properties:
                              $date:
                                type: string
                                format: date-time
                          modified:
                            type: object
                            properties:
                              $date:
                                type: string
                                format: date-time
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  '/2.2/branches/{branchId}/users/{userId}/cards/{cardId}':
    delete:
      tags:
        - UserCards
      summary: ''
      operationId: delete-user-card
      description: This endpoint to delete card from a user
      x-internal: true
      security:
        - coreapi_auth: []
      parameters:
        - schema:
            type: string
          name: branchId
          in: path
          required: true
        - schema:
            type: string
          name: userId
          in: path
          required: true
        - schema:
            type: string
          name: cardId
          in: path
          required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/paths/~12.2~1branches~1%7BbranchId%7D~1users~1%7BuserId%7D~1cards/post/responses/200/content/application~1json/schema/properties/data/items'
        '404':
          description: Card not found
          content:
            application/json:
              schema:
                $ref: '#/paths/~12.2~1branches~1%7BbranchId%7D~1users~1%7BuserId%7D~1cards~1%7BcardId%7D/put/responses/404/content/application~1json/schema'
    put:
      tags:
        - UserCards
      summary: ''
      operationId: update-user-card
      description: This endpoint update an existing card for a user
      x-internal: true
      security:
        - coreapi_auth: []
      parameters:
        - schema:
            type: string
          name: branchId
          in: path
          required: true
        - schema:
            type: string
          name: userId
          in: path
          required: true
        - schema:
            type: string
          name: cardId
          in: path
          required: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                zip_code:
                  type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/paths/~12.2~1branches~1%7BbranchId%7D~1users~1%7BuserId%7D~1cards/post/responses/200/content/application~1json/schema/properties/data/items'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: Card not found
          content:
            application/json:
              schema:
                type: object
                description: CardNotFound
                properties:
                  success:
                    type: boolean
                    default: false
                  message:
                    type: string
                    default: 'Card not found - id: {card_id}'
                  message_code:
                    type: string
                    default: 'Card not found - id: {card_id}'
                  message_data:
                    type: array
                    items:
                      type: string
                  errors:
                    type: string
                    default: 'Card not found - id: {card_id}'
  '/2.2/charges/by-invoice-id/{invoiceId}':
    get:
      summary: Get Charge colletion for a given invoice id.
      description: This endpoint returns Charge collection of a given invoice id
      x-internal: true
      parameters:
        - in: path
          name: invoiceId
          required: true
          schema:
            type: string
      tags:
        - Charge
      responses:
        '200':
          description: Response
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  properties:
                    _id:
                      type: object
                      properties:
                        $oid:
                          type: string
                    id:
                      type: string
                    transaction_status:
                      type: string
                    transaction_provider_id:
                      type: string
                    metadata:
                      type: object
                      properties:
                        namespace:
                          type: string
                        branch_id:
                          type: string
                        glofox_event:
                          type: string
                        stripe_subscription_id:
                          type: string
                        user_id:
                          type: string
                        membership_id:
                          type: string
                        user_name:
                          type: string
                        environment:
                          type: string
                        payment_method:
                          type: string
                        plan_code:
                          type: string
                        resource_id:
                          type: string
                        already_paid:
                          type: boolean
                    amount:
                      type: integer
                    currency:
                      type: string
                    customer:
                      type: string
                    paid:
                      type: boolean
                    invoice_id:
                      type: string
                    created:
                      type: object
                      properties:
                        $date:
                          type: string
                          format: date-time
                    modified:
                      type: object
                      properties:
                        $date:
                          type: string
                          format: date-time
                    event_id:
                      type: string
                    description:
                      type: string
                    transaction_group_id:
                      type: string
                    amount_refunded:
                      type: integer
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  '/2.2/branches/{branchId}/payroll-groups':
    parameters:
      - schema:
          type: string
          pattern: '^[a-f\d]{24}$'
        name: branchId
        in: path
        required: true
    get:
      tags:
        - Payroll Groups
      summary: ''
      operationId: get-payroll-groups
      description: This endpoint returns filtered payroll groups of a given branch
      x-internal: true
      security:
        - coreapi_auth: []
      parameters:
        - schema:
            type: string
          in: query
          name: sort-by
          description: field | -field
        - schema:
            type: integer
            minimum: 1
            default: 1
          in: query
          name: page
        - schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 50
          in: query
          name: limit
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/Pagination'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          $ref: '#/components/schemas/PayrollGroups'
    post:
      tags:
        - Payroll Groups
      summary: ''
      operationId: create-payroll-group
      description: This endpoint will create a payroll group
      x-internal: true
      security:
        - coreapi_auth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  maximum: 255
                  minimum: 3
                  description: 'The name of the rate group. Allows only alpha-numeric characters, white spaces and .,_+-& characters.'
                staff_ids:
                  type: array
                  items:
                    type: string
                rates:
                  type: array
                  items:
                    $ref: '#/paths/~12.2~1branches~1%7BbranchId%7D~1payroll-rates/post/requestBody/content/application~1json/schema'
      responses:
        '201':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PayrollGroups'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    default: false
                  message:
                    type: string
                    enum:
                      - You must provide a group name
                      - You must provide a rate id
                      - You must provide a rate group id
                      - You must provide a rate price
                      - The selected event is invalid
                      - The must provide an event id
                      - You must select an event type
                      - The selected event type is invalid
                      - The rate price cannot be less than one
                      - The rate price is too large
                      - The rate price must be a number
                  message_code:
                    type: string
                    enum:
                      - PAYROLL_GROUP_MISSING_NAME_FIELD
                      - PAYROLL_RATE_MISSING_RATE_ID_FIELD
                      - PAYROLL_RATE_MISSING_RATE_GROUP_ID_FIELD
                      - PAYROLL_RATE_MISSING_RATE_PRICE_FIELD
                      - PAYROLL_RATE_EVENT_ID_MUST_BE_A_STRING
                      - PAYROLL_RATE_MISSING_EVENT_ID_FIELD
                      - PAYROLL_RATE_MISSING_EVENT_TYPE_FIELD
                      - PAYROLL_RATE_INVALID_EVENT_TYPE
                      - PAYROLL_RATE_PRICE_MIN_ERROR
                      - PAYROLL_RATE_PRICE_MAX_ERROR
                      - PAYROLL_RATE_PRICE_MUST_BE_AN_INTEGER
                      - PAYROLL_RATE_BONUS_SINGLE_THRESHOLD_THRESHOLD_MUST_BE_AN_INTEGER
                      - PAYROLL_RATE_BONUS_SINGLE_THRESHOLD_THRESHOLD_MIN_ERROR
                      - PAYROLL_RATE_BONUS_SINGLE_THRESHOLD_THRESHOLD_MAX_ERROR
                      - PAYROLL_RATE_BONUS_SINGLE_THRESHOLD_BONUS_MUST_BE_AN_INTEGER
                      - PAYROLL_RATE_BONUS_SINGLE_THRESHOLD_BONUS_MIN_ERROR
                      - PAYROLL_RATE_BONUS_SINGLE_THRESHOLD_BONUS_MAX_ERROR
                  message_data:
                    type: array
                    items:
                      type: string
                  errors:
                    type: array
                    items:
                      type: string
  '/2.2/branches/{branchId}/payroll-groups/{payrollGroupId}':
    parameters:
      - schema:
          type: string
          pattern: '^[a-f\d]{24}$'
        name: branchId
        in: path
        required: true
      - schema:
          type: string
          pattern: '^[a-f\d]{24}$'
        name: payrollGroupId
        in: path
        required: true
    get:
      tags:
        - Payroll Groups
      summary: ''
      operationId: get-payroll-group-by-id
      description: |-
        This endpoint returns payroll group data. If the ID is not found, it returns a
        404 status code with an error.
      x-internal: true
      security:
        - coreapi_auth: []
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  _id:
                    type: string
                    pattern: '^[a-f\d]{24}$'
                  branch_id:
                    type: string
                    pattern: '^[a-f\d]{24}$'
                  name:
                    type: string
                  staff_ids:
                    type: array
                    description: 'The staff IDs to which these group rates apply. If empty, it will apply to all.'
                    items:
                      type: string
                  rates:
                    type: array
                    items:
                      $ref: '#/components/schemas/PayrollRates'
                  created:
                    type: string
                    description: date-time or object with seconds and microseconds
                  created_by:
                    type: string
                    pattern: '^[a-f\d]{24}$'
                    description: user id of who created this record
                  modified:
                    type: string
                    description: date-time or object with seconds and microseconds
                  modified_by:
                    type: string
                    pattern: '^[a-f\d]{24}$'
                    description: user id of who updated this record
        '404':
          description: Bad request
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    default: false
                  message:
                    type: string
                    enum:
                      - You must provide a Payroll Group ID
                  message_code:
                    type: string
                    enum:
                      - EMPTY_PAYROLL_GROUP_ID
                  message_data:
                    type: array
                    items:
                      type: string
                  errors:
                    type: array
                    items:
                      type: string
    put:
      tags:
        - Payroll Groups
      summary: ''
      operationId: update-payroll-group-by-id
      description: 'If the ID is not found, it returns a 404 status code with an error'
      x-internal: true
      security:
        - coreapi_auth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/paths/~12.2~1branches~1%7BbranchId%7D~1payroll-groups/post/requestBody/content/application~1json/schema'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PayrollGroups'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/paths/~12.2~1branches~1%7BbranchId%7D~1payroll-groups/post/responses/400/content/application~1json/schema'
  '/2.2/branches/{branchId}/payroll-rates':
    parameters:
      - schema:
          type: string
          pattern: '^[a-f\d]{24}$'
        name: branchId
        in: path
        required: true
    get:
      tags:
        - Payroll Rates
      summary: ''
      operationId: get-payroll-rates
      description: This endpoint returns filtered payroll rates of a given branch
      x-internal: true
      security:
        - coreapi_auth: []
      parameters:
        - schema:
            type: string
          in: query
          name: sort-by
          description: field | -field
        - schema:
            type: integer
            minimum: 1
            default: 1
          in: query
          name: page
        - schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 50
          in: query
          name: limit
        - schema:
            type: integer
          name: utc-time-start
          in: query
          description: 'UTC time of the starting point to fetch pay rates. If provided, the rates active during utc-time-start and forward will be fetched.'
        - schema:
            type: integer
          name: utc-time-finish
          in: query
          description: 'UTC time of the ending point to fetch pay rates. If provided, the rates active during utc-time-finish and backward will be fetched.'
        - schema:
            type: string
          name: group_id
          in: query
          description: Fetch the rates for a given group ID.
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/Pagination'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          $ref: '#/components/schemas/PayrollRates'
    post:
      tags:
        - Payroll Rates
      summary: ''
      operationId: create-payroll-rate
      description: This endpoint will create a payroll rate
      x-internal: true
      security:
        - coreapi_auth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                rate_id:
                  type: string
                  pattern: '^[a-f\d]{24}$'
                event_type:
                  type: string
                  enum:
                    - class
                    - appointment
                event_id:
                  type: string
                  pattern: '^[a-f\d]{24}$'
                rate_type:
                  type: string
                  enum:
                    - base
                    - conditional
                rate_price:
                  type: integer
                  minimum: 1
                rate_group_id:
                  type: string
                  pattern: '^[a-f\d]{24}$'
                rate_bonus:
                  type: array
                  items:
                    type: object
                    properties:
                      bonus_id:
                        type: string
                        pattern: '^[a-f\d]{24}$'
                      bonus_type:
                        type: string
                        enum:
                          - SINGLE_THRESHOLD
                      bonus_settings:
                        type: array
                        items:
                          type: object
                          properties:
                            threshold:
                              type: number
                              description: The total attendees (greater than or equals to) that will trigger this bonus.
                            amount:
                              type: number
                              description: The total added to the rate value when the threshold is met.
                    required:
                      - bonus_id
                      - bonus_type
                      - bonus_settings
              required:
                - rate_id
                - event_type
                - event_id
                - rate_type
                - rate_price
      responses:
        '201':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PayrollRates'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    default: false
                  message:
                    type: string
                    enum:
                      - You must select an event
                      - The selected event type is invalid
                      - You must select a rate type
                      - You must provide a rate price
                      - The selected event type is invalid
                      - The rate price must be a number
                      - The rate price cannot be less than one
                      - The rate price is too large
                  message_code:
                    type: string
                    enum:
                      - PAYROLL_RATE_MISSING_EVENT_ID_FIELD
                      - PAYROLL_RATE_EVENT_ID_MUST_BE_A_STRING
                      - PAYROLL_RATE_MISSING_RATE_TYPE_FIELD
                      - PAYROLL_RATE_MISSING_RATE_PRICE_FIELD
                      - PAYROLL_RATE_INVALID_EVENT_TYPE
                      - PAYROLL_RATE_PRICE_MUST_BE_AN_INTEGER
                      - PAYROLL_RATE_PRICE_MIN_ERROR
                      - PAYROLL_RATE_PRICE_MAX_ERROR
                  message_data:
                    type: array
                    items:
                      type: string
                  errors:
                    type: array
                    items:
                      type: string
  '/2.2/branches/{branchId}/payroll-rates/{payrollRateId}':
    parameters:
      - schema:
          type: string
          pattern: '^[a-f\d]{24}$'
        name: branchId
        in: path
        required: true
      - schema:
          type: string
          pattern: '^[a-f\d]{24}$'
        name: payrollRateId
        in: path
        required: true
    get:
      tags:
        - Payroll Rates
      summary: ''
      operationId: get-payroll-rate-by-id
      description: |-
        This endpoint returns payroll rate data. If the id is not found, it returns a
        404 status code with an error.
      x-internal: true
      security:
        - coreapi_auth: []
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PayrollRates'
        '404':
          description: Bad request
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    default: false
                  message:
                    type: string
                    enum:
                      - You must provide a Payroll Rate ID
                  message_code:
                    type: string
                    enum:
                      - EMPTY_PAYROLL_RATE_ID
                  message_data:
                    type: array
                    items:
                      type: string
                  errors:
                    type: array
                    items:
                      type: string
    put:
      tags:
        - Payroll Rates
      summary: ''
      operationId: update-payroll-rate-by-id
      description: 'If the id is not found, it returns a 404 status code with an error'
      x-internal: true
      security:
        - coreapi_auth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/paths/~12.2~1branches~1%7BbranchId%7D~1payroll-rates/post/requestBody/content/application~1json/schema'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PayrollRates'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/paths/~12.2~1branches~1%7BbranchId%7D~1payroll-rates/post/responses/400/content/application~1json/schema'
  '/2.2/branches/{branchId}/payroll-reports':
    parameters:
      - schema:
          type: string
          pattern: '^[a-f\d]{24}$'
        name: branchId
        in: path
        required: true
    post:
      tags:
        - Payroll Rates
      summary: ''
      operationId: create-payroll-report
      description: This endpoint will create a payroll report
      x-internal: true
      security:
        - coreapi_auth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                utc_time_start:
                  type: integer
                  description: Timestamp in UTC
                utc_time_finish:
                  type: integer
                  description: Timestamp in UTC
                staff_ids:
                  type: array
                  description: The staff IDs to generate the report for. Send an empty list for all staff.
                  items:
                    type: string
                    pattern: '^[a-f\d]{24}$'
              required:
                - utc_time_start
                - utc_time_finish
                - staff_ids
      responses:
        '200':
          description: OK
          content:
            text/csv:
              schema:
                type: string
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/paths/~12.2~1branches~1%7BbranchId%7D~1payroll-rates/post/responses/400/content/application~1json/schema'
  '/2.2/programs/{programId}/recurring-bookings/slots':
    post:
      tags:
        - ProgramSlots
      summary: Get slots for multiple bookings
      operationId: get-slots
      description: Use this call to retrieve the slots for multiple bookings in a selected date range. If the slot bookable parameter is false it will state the errors  in the errors array in each slot retrieved.
      security:
        - coreapi_auth: []
      parameters:
        - schema:
            type: string
            pattern: '^[a-f\d]{24}$'
          name: programId
          in: path
          required: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                start_time:
                  type: integer
                  description: The local timestamp that the slot starts
                end_time:
                  type: integer
                  description: The local timestamp that the slot ends
                schedule_codes:
                  type: array
                  items:
                    type: string
                    description: refers to the code of the schedule slot retrieved
                user_id:
                  type: string
                  pattern: '^[a-f\d]{24}$'
                  description: The id of the user
              required:
                - start_time
                - end_time
                - schedule_codes
                - user_id
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProgramSlots'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  '/2.2/branches/{branchId}/users/{userId}/memberships':
    parameters:
      - schema:
          type: string
          pattern: '^[a-f\d]{24}$'
        name: branchId
        in: path
        required: true
        description: The branch ID the memberships list is fetched for
      - schema:
          type: string
          pattern: '^[a-f\d]{24}$'
        name: userId
        in: path
        required: true
        description: The user ID the memberships list is fetched for
    get:
      tags:
        - Memberships
      summary: Get memberships
      operationId: get-memberships
      description: 'Use this call to retrieve a list of memberships for an specified user. If the roaming global search is on, it will return all the memberships for the requested user even though this user belongs to a different location under the same namespace.'
      security:
        - coreapi_auth: []
      parameters:
        - schema:
            type: integer
            default: 0
            enum:
              - 0
              - 1
          name: namespace-search
          in: query
          required: false
          description: |
            If 1, then search is made within the namespace of the client. All users from all roaming branches are searched.
            If 0, then search is made within the branch only.
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: string
                    description: The unique identifier of the request
                  userId:
                    type: string
                    description: The unique identifier of the user
                  membershipId:
                    type: string
                    description: The unique identifier of the membership
                  subscriptionProviderId:
                    type: string
                    description: The unique identifier of the subscription provider
                  subscriptionPlanId:
                    type: string
                    description: The unique identifier of the subscription plan
                  planCode:
                    type: string
                    description: The code of the plan
                  branchId:
                    type: string
                    description: The unique identifier of the branch
                  groupId:
                    type: string
                    description: The unique identifier of the group
                  status:
                    type: string
                    enum:
                      - ACTIVE
                      - FUTURE
                      - LOCKED
                      - PAUSED
                      - CANCELLED
                      - EXPIRED
                    description: |
                      The membership lifecycle has 3 main stages: future, current, and ended. The membership status within those are:
                      - Future: FUTURE status, a membership that has not started yet
                      - Current: ACTIVE, LOCKED, PAUSED: transition between these states is unrestricted
                      - Ended: EXPIRED, CANCELLED
                  commencedDate:
                    type: integer
                    description: The local time that the membership started
                  duration:
                    type: object
                    properties:
                      startDate:
                        type: integer
                        description: The local time that the membership started
                      expiryDate:
                        type: integer
                        description: The local time that the membership expires
                  endDate:
                    type: integer
                    description: The local time that the membership ends
                  purchaseDate:
                    type: integer
                    description: The local time that the membership was purchased
                  nextPaymentDate:
                    type: integer
                    description: The local time that the next payment is due
                  concludedAt:
                    type: integer
                    description: The local time that the membership was concluded
                  membershipMetaData:
                    type: object
                    properties:
                      membershipName:
                        type: string
                        description: The name of the membership
                      planName:
                        type: string
                        description: The name of the plan
                      startsOn:
                        type: string
                        description: The date the membership starts
                      price:
                        type: integer
                        description: The price of the membership
                      minPrice:
                        type: integer
                        description: The minimum price of the membership
                      upfrontFee:
                        type: integer
                        description: The upfront fee of the membership
                      duration:
                        type: object
                        properties:
                          timeUnit:
                            type: string
                            description: The unit of time
                          value:
                            type: integer
                            description: The value of the time unit
                      contract:
                        type: object
                        properties:
                          timeUnit:
                            type: string
                            description: The unit of time
                          value:
                            type: integer
                            description: The value of the time unit
                      hasEndDate:
                        type: boolean
                        description: Whether the membership has an end date
                      planType:
                        type: string
                        description: The type of plan
                      isRoaming:
                        type: boolean
                        description: Whether the membership is roaming
                      isGroupMembership:
                        type: boolean
                        description: Whether the membership is a group membership
                      maxGroupMembershipSize:
                        type: integer
                        description: The maximum size of the group membership
                      isTrial:
                        type: boolean
                        description: Whether the membership is a trial
                      freeTimeUnitCount:
                        type: integer
                        description: The free time unit count
                      prorated:
                        type: boolean
                        description: Whether the membership is prorated
                      autoRenewal:
                        type: boolean
                        description: Whether the membership is set to auto-renew
                  schedulePause:
                    type: boolean
                    description: The status of the scheduled pause
                  pause:
                    type: boolean
                    description: The status of the pause
                  payment:
                    type: object
                    properties:
                      price:
                        type: integer
                        description: The price of the membership
                      upfrontFee:
                        type: integer
                        description: The upfront fee of the membership
                      paymentMethod:
                        type: string
                        description: The payment method of the membership
                      externalResourceId:
                        type: string
                        description: The external resource identifier of the membership
                      discounts:
                        type: array
                        description: The list of discounts applied to the membership
                        items:
                          type: string
                      discountsPreApplied:
                        type: boolean
                        description: The status of the discounts pre-applied to the membership
                  hasActiveSubscription:
                    type: boolean
                    description: The status of the subscription
                  allowedActions:
                    type: object
                    properties:
                      isScheduledPauseAllowed:
                        type: boolean
                        description: The status of the scheduled pause
                      isPauseAllowed:
                        type: boolean
                        description: The status of the pause
                      isEditPauseAllowed:
                        type: boolean
                        description: The status of the pause edit
                      isUnpauseAllowed:
                        type: boolean
                        description: The status of the unpause
                      isConsecutiveMembershipAllowed:
                        type: boolean
                        description: The status of the consecutive membership
                      isManageGroupMemberAllowed:
                        type: boolean
                        description: The status of the group member management
                      isRemoveScheduledCancellationAllowed:
                        type: boolean
                        description: The status of the scheduled cancellation removal
                  lock:
                    type: boolean
                    description: The status of the membership lock
                  payg:
                    type: boolean
                    description: The status of the payg
                  isPriceEdited:
                    type: boolean
                    description: The status of the price edit
                  isUpfrontFeeEdited:
                    type: boolean
                    description: The status of the upfront fee edit
                  isSubscription:
                    type: boolean
                    description: The status of the subscription
                  isScheduledPauseAllowed:
                    type: boolean
                    description: The status of the scheduled pause
                  isPauseAllowed:
                    type: boolean
                    description: The status of the pause
                  isPrimaryGroupMember:
                    type: boolean
                    description: The status of the primary group member
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  '/2.2/branches/{branchId}/bookings/batch/{batchId}':
    delete:
      tags:
        - Bookings
      summary: Cancel a list of bookings
      operationId: batch-bookings-cancel
      description: Use this call to cancel a list of bookings that were created in batch from a starting date onwards.
      security:
        - coreapi_auth: []
      parameters:
        - schema:
            type: string
            pattern: '^[a-f\d]{24}$'
          name: branchId
          in: path
          required: true
          description: The unique identifier of the branch where the bookings were created.
        - schema:
            type: string
            pattern: '^[a-f\d]{24}$'
          name: batchId
          in: path
          required: true
          description: The unique identifier to fetch bookings of the requested batch.
        - schema:
            type: integer
          name: utc-start-time
          in: query
          required: true
          description: UTC timestamp of the starting time to fetch bookings of the requested batch.
      responses:
        '202':
          description: Accepted
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: Not Found
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    default: false
                  message:
                    type: string
                    enum:
                      - Batch id not found
                  message_code:
                    type: string
                    enum:
                      - BATCH_ID_NOT_FOUND
  '/2.2/branches/{branchId}/bookings/recurrent/{batchId}':
    post:
      tags:
        - Bookings
      summary: Book a list of booking slot in batch
      operationId: batch-recurring-bookings
      description: 'Use this call to book or reserve a list of booking slots that were created in batch from checking availability endpoint. Use the batchId provided in checking availability endpoint (/2.2/programs/{programId}/recurring-bookings/slots). This endpoint will process all slots in background in the apiworker. Finally, this call will eventually update the slots with the proper status (`BOOKED`, `RESERVED` or `FAILED` with error message).'
      security:
        - coreapi_auth: []
      parameters:
        - schema:
            type: string
            pattern: '^[a-f\d]{24}$'
          name: branchId
          in: path
          required: true
          description: The unique identifier of the branch where the bookings were created.
        - schema:
            type: string
          name: batchId
          in: path
          required: true
          description: The unique identifier to fetch bookings of the requested batch. It is provided in checking availability slot.
      responses:
        '204':
          description: No Content
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
    get:
      tags:
        - Bookings
      summary: Get the list of booking slots by batch with their current result.
      operationId: get-batch-recurring-bookings
      description: 'Use this call to get all the booking slots result by batch. The async booking process could take some time, so we can use this endpoint to get the result for those booking slots. Slots can be `PENDING`, `BOOKED`, `RESERVED` or `FAILED`.'
      security:
        - coreapi_auth: []
      parameters:
        - schema:
            type: string
            pattern: '^[a-f\d]{24}$'
          name: branchId
          in: path
          required: true
          description: The unique identifier of the branch where the bookings were created.
        - schema:
            type: string
          name: batchId
          in: path
          required: true
          description: The unique identifier to fetch bookings of the requested batch. It is provided in checking availability slot.
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  batch_id:
                    type: string
                  total_instances:
                    type: integer
                    description: Total number of slots by batch.
                  bookings:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: string
                        status:
                          type: string
                          enum:
                            - BOOKED
                            - RESERVED
                            - FAILED
                            - PENDING
                        time_start:
                          type: integer
                          example: 1722431941
                          description: Booking start time in epoch time.
                        failure_reason:
                          type: string
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  '/2.2/branches/{branchId}/bookings/recurrent/import':
    post:
      tags:
        - Bookings
      summary: Import recurrent bookings
      operationId: import-recurrent-bookings
      description: 'This endpoint will first check bookings availability and then, will try to book or reserve available booking slots within the timeframe. Bookings will be based on the program schedule and it will attempt to book all possible slots. This endpoint is only used by Import service and all data are based on provided CSV file. To see how process is going you need to use `GET /2.2/branches/{branchId}/bookings/recurrent/{batchId}`'
      security:
        - coreapi_auth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                program_id:
                  type: string
                  description: The id of the booked program.
                  pattern: '^[a-f\d]{24}$'
                  example: 551be393de36da56ec8b45e1
                user_id:
                  type: string
                  description: The id of the member.
                  pattern: '^[a-f\d]{24}$'
                book_start_time:
                  type: string
                  nullable: true
                  example: '2024-12-31T00:00:00Z'
                  description: 'Date when recurring booking will start in `YYYY-MM-DDThh:mm:ssTZD` format. By default, will use the beginning today''s date.'
                book_until_time:
                  type: string
                  example: '2024-12-31T23:59:59Z'
                  description: 'Date when recurring booking will end in `YYYY-MM-DDThh:mm:ssTZD` format.'
                schedule_codes:
                  type: array
                  nullable: true
                  items:
                    type: string
                    example: 65f06ca6c1f24
                    description: Schedule code
      parameters:
        - schema:
            type: string
            pattern: '^[a-f\d]{24}$'
          name: branchId
          in: path
          required: true
          description: The unique identifier of the branch where the bookings are to be created.
      responses:
        '202':
          description: Ok
          content:
            application/json:
              schema:
                type: object
                properties:
                  batch_id:
                    type: string
                    example: coreapi-66e9a32cdaabd08978733
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  '/2.3/branches/{branchId}/users/{userId}/restore':
    post:
      summary: ''
      operationId: user-restore
      tags:
        - Users
      description: Restores a deleted user
      parameters:
        - schema:
            type: string
            pattern: '^[a-f\d]{24}$'
          name: branchId
          in: path
          required: true
        - schema:
            type: string
            pattern: '^[a-f\d]{24}$'
          name: userId
          in: path
          required: true
      responses:
        '204':
          description: No Content
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: Not Found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  '/2.3/branches/{branchId}/push-notifications/group-messages':
    post:
      summary: ''
      operationId: group-message
      tags:
        - PushNotifications
      description: To send a group message to a defined list of users
      parameters:
        - schema:
            type: string
            pattern: '^[a-f\d]{24}$'
          name: branchId
          in: path
          required: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                message:
                  type: string
                  description: Message content
                overwrite_marketing:
                  type: boolean
                  description: To overwrite marketing preferences
                  default: true
                user_ids:
                  type: array
                  items:
                    type: string
                    pattern: '^[a-f\d]{24}$'
                    description: user_id of user to notify
      responses:
        '200':
          description: OK
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: Not Found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  '/2.3/branches/{branchId}/leads/{userId}/interactions':
    post:
      summary: ''
      operationId: create-interaction
      tags:
        - Interactions
      description: To create a new interaction for a user
      parameters:
        - schema:
            type: string
            pattern: '^[a-f\d]{24}$'
          name: branchId
          in: path
          required: true
        - schema:
            type: string
            pattern: '^[a-f\d]{24}$'
          name: userId
          in: path
          required: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                type:
                  type: string
                  description: Type of interaction
                  enum:
                    - NOTE
                    - CALLED_AND_CONNECTED
                    - CALLED_AND_NO_ANSWER
                    - MANUAL_EMAIL
                    - MEMBER_SERVICE_INTERACTION
                    - AUTO_EMAIL
                    - AUTO_SMS
                    - AUTO_PUSH_NOTIFICATION
                    - AMPLIFY_EMAIL
                    - AMPLIFY_SMS
                    - AMPLIFY_PUSH
                description:
                  type: string
                  maxLength: 500
                  description: description of the interaction
                created:
                  type: integer
                  description: timestamp for the creation of the interaction
              required:
                - type
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                properties:
                  success:
                    type: string
                  data:
                    $ref: '#/components/schemas/Interactions'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: Not Found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  '/2.3/branches/{branchId}/leads/marketing-sources':
    post:
      summary: Create custom marketing sources.
      description: This call creates a custom marketing source for a location.
      parameters:
        - in: path
          name: branchId
          required: true
          schema:
            type: string
            pattern: '^[a-f\d]{24}$'
          description: The location id
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: Name of the marketing source.
              required:
                - name
      tags:
        - Leads
      responses:
        '204':
          description: No Content
        '404':
          description: Branch not found
        '500':
          description: Internal server error
  '/2.3/branches/{branchId}/leads/lead-statuses':
    get:
      summary: Retrieve lead statuses
      operationId: get-lead-status
      tags:
        - Leads
      security:
        - coreapi_auth: []
      description: Retrieve a list of available lead statuses.
      parameters:
        - schema:
            type: string
            pattern: '^[a-f\d]{24}$'
          name: branchId
          in: path
          required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  properties:
                    value:
                      type: string
                      description: A unique value representing the lead status for internal use.
                    key:
                      type: string
                      description: 'The translation key for the lead status, used in the frontend for displaying the status text.'
                  required:
                    - value
                    - key
        '400':
          description: Bad Request
        '401':
          description: Unauthorized
        '404':
          description: Not Found
          content:
            application/json:
              schema:
                type: object
                description: BranchNotFound
                properties:
                  success:
                    type: boolean
                    default: false
                  message:
                    type: string
                    default: 'Branch not found - id: {branch_id}'
                  message_code:
                    type: string
                    default: 'Branch not found - id: {branch_id}'
                  message_data:
                    type: array
                    items:
                      type: string
                  errors:
                    type: string
                    default: 'Branch not found - id: {branch_id}'
        '500':
          description: Internal Server Error
  /2.3/branches:
    get:
      summary: Get Branches
      operationId: filterBranches
      tags:
        - Branches
      description: |
        Finds branches with search parameters. It also includes color configurations from the branch_configuration_mobile collection.
      parameters:
        - in: query
          name: name
          schema:
            type: string
          required: true
          description: 'The name of the branch for a partial search, requiring a minimum of 3 characters.'
        - in: query
          name: bundle
          schema:
            type: string
            default: _glofox
          required: false
          description: Specifies the client bundle. Defaults to '_glofox' if not provided.
        - in: query
          name: role
          schema:
            type: string
            enum:
              - MEMBER
          required: false
          description: Used to select by role (MEMBER) to filter branches that are member_facing true or null - locations that are available/visible to members when logging in
      responses:
        '200':
          description: |
            A list of branches, each including name, namespace, client bundle information, and color configurations.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  entities:
                    type: array
                    items:
                      type: object
                      properties:
                        branch_id:
                          type: string
                          description: Unique identifier of the branch.
                        branch_name:
                          type: string
                          description: Name of the branch.
                        branch_namespace:
                          type: string
                          description: Namespace associated with the branch.
                        branch_colors:
                          type: object
                          properties:
                            background:
                              type: string
                              description: Background color.
                            accent:
                              type: string
                              description: Accent color.
                            text:
                              type: string
                              description: Text color.
                          description: Color configuration for the branch.
                        logo:
                          type: string
                          description: URL to the branch's logo.
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /2.4/enterprises:
    get:
      tags:
        - Enterprises
      summary: Retrieve enterprises
      operationId: get-enterprises
      description: This endpoint returns a list of enterprises.
      security:
        - coreapi_auth: []
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Enterprise'
        '400':
          description: Bad Request
        '500':
          description: Internal Server Error
    post:
      tags:
        - Enterprises
      summary: Create a new enterprise
      operationId: create-enterprise
      description: This endpoint allows the creation of a new enterprise by providing the required fields.
      security:
        - coreapi_auth: []
      requestBody:
        description: New enterprise object to be created
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: The name of the enterprise
                  example: AirLocker
                corporate_id:
                  type: string
                  description: The corporate identifier of the enterprise
                  example: corp_AirLocker
              required:
                - name
                - corporate_id
      responses:
        '204':
          description: Enterprise successfully created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Enterprise'
        '400':
          description: Bad Request
        '401':
          description: Unauthorized – Authentication is required and has failed or has not yet been provided.
        '403':
          description: Forbidden – The authenticated client does not have permission to perform this action.
        '500':
          description: Internal Server Error
  /2.4/enterprises/locations:
    get:
      summary: Get Enterprises Locations
      description: This endpoint retrieves a list of active locations associated with the corporate ID derived from the authenticated user.
      security:
        - coreapi_auth: []
      parameters:
        - in: query
          name: limit
          required: false
          schema:
            type: integer
            default: 50
            maximum: 1000
          description: 'The number of locations to return. Defaults to 50, with a maximum of 1000.'
        - in: query
          name: page
          required: false
          schema:
            type: integer
            default: 1
          description: The page number to return.
      tags:
        - Enterprises
      responses:
        '200':
          description: A list of locations objects
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/Pagination'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          type: object
                          properties:
                            _id:
                              type: string
                            name:
                              type: string
                            namespace:
                              type: string
                            active:
                              type: boolean
                            corporate_id:
                              type: string
                          required:
                            - _id
                            - name
                            - namespace
                            - active
                            - corporate_id
        '400':
          description: Bad Request
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    enum:
                      - 'Forbidden: No Corporate ID associated with this account'
        '500':
          description: Internal server error
  /3.0/clients:
    get:
      tags:
        - Clients
      summary: ''
      operationId: get-clients
      description: This endpoint returns filtered clients
      x-internal: true
      parameters:
        - schema:
            type: string
          in: query
          description: field | -field
          name: sort-by
        - schema:
            type: integer
            minimum: 1
          in: query
          name: page
        - schema:
            type: integer
            minimum: 1
            maximum: 50
          in: query
          name: limit
        - schema:
            type: string
            maxLength: 50
            pattern: '/^_[A-Za-z]+$/u'
          in: query
          description: It must starts with an underscore (_)
          name: bundle
        - schema:
            type: string
            maxLength: 50
          in: query
          name: name
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/Pagination'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          $ref: '#/components/schemas/Client'
  '/v3.0/locations/{locationId}/facilities':
    get:
      tags:
        - Facilities
      summary: ''
      operationId: facilities-list
      description: |
        This endpoint returns paginated list of the facilities for the specified location.
        Facilities are sorted by creation date in descending mode.
      x-internal: true
      security:
        - coreapi_auth: []
      parameters:
        - schema:
            type: integer
            minimum: 1
            default: 1
          in: query
          name: page
        - schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 50
          in: query
          name: limit
        - schema:
            type: string
          in: query
          description: 'field | -field (name, created_at)'
          name: sort-by
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/Pagination'
                  - type: object
                    properties:
                      model:
                        type: string
                        description: The name of the represented model
                        default: facility
                      data:
                        type: array
                        example: []
                        items:
                          type: object
                          properties:
                            _id:
                              type: string
                              description: The unique identifier for the facility
                              pattern: '^[a-f\d]{24}$'
                            location_id:
                              type: string
                              description: The unique identifier for the location
                              pattern: '^[a-f\d]{24}$'
                            description:
                              type: string
                              description: A brief summary of the facility
                            name:
                              type: string
                              description: The name of the facility
                            namespace:
                              type: string
                              description: The namespace the location is associated with
                            bookable:
                              type: boolean
                              description: Indicates whether the facility is bookable
                            is_online:
                              type: boolean
                              description: Indicates whether the facility is online
                            categories:
                              type: array
                              items:
                                type: string
                                description: Unique identifier associated with the category
                                pattern: '^[a-f\d]{24}$'
                            list_visible:
                              type: boolean
                              description: Indicates whether the facility is set to public or private
                            created_at:
                              type: string
                              format: date-time
                              description: The date and time when the facility was created
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
components:
  schemas:
    Error:
      type: object
      properties:
        success:
          type: boolean
          default: false
        message:
          type: string
        message_code:
          type: string
        message_data:
          type: array
          items:
            type: string
        errors:
          type: array
          items:
            type: string
    Pagination:
      type: object
      properties:
        object:
          type: string
        page:
          type: integer
        limit:
          type: integer
        hasMore:
          type: boolean
        totalCount:
          type: integer
        data:
          type: array
          items: {}
    Branch:
      type: object
      properties:
        _id:
          type: string
        categories:
          type: array
          items:
            type: string
        name:
          type: string
        language:
          type: string
        address:
          type: object
          properties:
            city:
              type: string
            continent:
              type: string
            country:
              type: string
            country_code:
              type: string
            currency:
              type: string
            district:
              type: string
            latitude:
              type: number
            longitude:
              type: number
            state:
              type: string
            street:
              type: string
            timezone_id:
              type: string
            timezone_name:
              type: string
            location:
              type: object
              properties:
                lat:
                  type: number
                lng:
                  type: number
        subscription:
          type: object
          properties:
            current_period_end:
              type: string
              format: date-time
            id:
              type: string
        namespace:
          type: string
        phone:
          type: string
        uses_iframe:
          type: boolean
        active:
          type: boolean
        features:
          type: object
          description: List of available features
        push_config:
          type: object
          description: List of push configurations
        tutorial:
          type: object
          description: Tutorial status
        configuration:
          type: object
          description: List of specific configurations
        modified:
          type: string
          format: date-time
        opening_times:
          type: array
          items:
            type: object
            properties:
              dow:
                type: string
              start:
                type: string
              end:
                type: string
              is_open:
                type: boolean
        questions:
          type: array
          items:
            type: string
    User:
      type: object
      properties:
        _id:
          type: string
        branch_id:
          type: string
        namespace:
          type: string
        type:
          type: string
          enum:
            - ADMIN
            - MEMBER
            - RECEPTION
            - SUPERADMIN
            - TRAINER
        first_name:
          type: string
        last_name:
          type: string
        phone:
          type: string
        description:
          type: string
          nullable: true
        image:
          type: string
          nullable: true
        email:
          type: string
          format: email
        login:
          type: string
          format: email
        modified:
          type: string
          description: 'Date time Y-m-d H:i:s'
        created:
          type: string
          description: 'Date time Y-m-d H:i:s'
        categories:
          type: array
          items:
            type: string
        joined_at:
          type: string
          format: date-time
          description: This field can be an object with seconds and microseconds
        lead_status:
          type: string
          enum:
            - CLIENT
            - COLD
            - LEAD
            - MEMBER
            - TRIAL
            - UNTOUCHED
            - WARM
        name:
          type: string
        source:
          type: string
          description: The source of the request asking the user being created.
          enum:
            - UNKNOWN
            - CLI
            - CLASSPASS
            - DASHBOARD
            - KIOSK
            - MEMBER_APP
            - ADMIN_APP
            - WEBPORTAL
            - IMPORTS
            - MANUAL_MEMBER_TRANSFER
        user_id:
          type: string
        WAIVER:
          type: boolean
    StoreSales:
      type: object
      properties:
        _id:
          type: string
          description: Unique identifier for the given store sales.
        namespace:
          type: string
        branch_id:
          type: string
        invoice_id:
          type: string
          format: email
        purchase_code:
          type: string
          format: date
          example: '1997-10-31'
        product_id:
          description: Set to true if the user's email has been verified.
          type: string
        user_id:
          type: string
          format: date
          description: The date that the user was created.
        presentation_id:
          type: string
        product_name:
          type: string
        presentation_name:
          type: string
        quantity:
          type: integer
        user_name:
          type: string
        collected_date:
          type: object
          properties:
            sec:
              type: integer
            usec:
              type: integer
        created:
          type: object
          properties:
            sec:
              type: integer
            usec:
              type: integer
    Appointment:
      type: object
      properties:
        _id:
          type: string
        namespace:
          type: string
        branch_id:
          type: string
        name:
          type: string
        active:
          type: boolean
        private:
          type: boolean
        categories:
          type: array
          items:
            type: string
            description: Category id
        description:
          type: string
        allowed_member_types:
          type: array
          items:
            type: object
            properties:
              price:
                type: number
              type:
                type: string
              membership_id:
                type: string
              memberships:
                type: array
                items:
                  type: object
                  properties:
                    id:
                      type: string
                    label:
                      type: string
        pricing:
          type: string
          nullable: true
          enum:
            - SINGLE
            - FREE
            - CUSTOM
        staff:
          type: string
        staff_ids:
          type: array
          items:
            type: string
            description: Staff id
        time_slot_length:
          type: integer
        date_start:
          type: string
          description: |-
            Date time Y-m-d. There are only a few cases with that field, we don't know if it's
            a deprecated field or not.
        size:
          type: integer
          maximum: 5
          default: 1
          description: The maximum number of attendees of the appointment. This value is a positive number that can be set from 1 to 5.
        created:
          type: string
          format: date-time
        modified:
          type: string
          format: date-time
    AppointmentSlot:
      type: object
      properties:
        _id:
          type: string
        branch_id:
          type: string
        namespace:
          type: string
        model:
          type: string
          enum:
            - appointments
            - facilities
            - users
        model_id:
          type: string
        active:
          type: boolean
        booked:
          type: boolean
        private:
          type: boolean
        staff_id:
          type: string
        time_slot_pattern_id:
          type: string
        time_start:
          type: string
          description: 'Date time Y-m-d H:i:s or timestamp'
        time_finish:
          type: string
          description: 'Date time Y-m-d H:i:s or timestamp'
        date:
          type: string
          description: Date or timestamp
        week_day:
          type: integer
        name:
          type: string
        modified:
          type: string
          description: date-time or object with seconds and microseconds
        created:
          type: string
          description: date-time or object with seconds and microseconds
        appointment:
          $ref: '#/components/schemas/Appointment'
        staff:
          $ref: '#/components/schemas/User'
    Client:
      type: object
      properties:
        _id:
          type: string
        name:
          type: string
        bundles:
          type: array
          items:
            type: string
    Analytics:
      type: object
      properties:
        total_bookings:
          type: integer
          description: The total number of bookings attended in the time period requested
        total_accesses:
          type: integer
          description: The total number of accesses granter in the time period requested
    RecaptchaValidationOutcome:
      type: object
      properties:
        success:
          type: boolean
    SalesMetrics:
      type: object
      properties:
        branch_id:
          type: string
        currency:
          type: string
        revenue_stream_type:
          type: string
        extract_date:
          type: string
          format: date-time
        extract_year:
          type: integer
          format: int32
        extract_month:
          type: integer
          format: int32
        extract_day:
          type: integer
          format: int32
        extract_quarter:
          type: integer
          format: int32
        extract_week:
          type: integer
          format: int32
        latest_created_at:
          type: string
          format: date-time
        sum_net_sales:
          type: integer
          format: int32
    MemberKPIs:
      type: object
      properties:
        branch_id:
          type: string
          pattern: '^[a-f\d]{24}$'
        user_id:
          type: string
          pattern: '^[a-f\d]{24}$'
        date_month:
          type: string
          format: date-time
        last_vist_at:
          type: string
          format: date-time
        local_last_visit_at:
          type: string
          format: date-time
        local_visits_executed_at:
          type: string
          format: date-time
        total_unique_visits_last_30_days:
          type: integer
    VisitsInsights:
      type: object
      properties:
        visits_insights_metrics:
          type: object
          properties:
            branch_id:
              type: string
              pattern: '^[a-f\\d]{24}$'
            total_members:
              type: integer
            total_members_previous_period:
              type: integer
            total_unique_users:
              type: integer
            total_unique_roaming_visits:
              type: integer
            total_unique_roaming_visits_previous_period:
              type: integer
            total_unique_visits:
              type: integer
            total_unique_visits_previous_period:
              type: integer
            total_visits:
              type: integer
            total_visits_previous_period:
              type: integer
            median_visits:
              type: number
            median_visits_previous_period:
              type: number
        visits_details:
          type: object
          properties:
            VisitsDetailsCollection:
              type: array
              items:
                properties:
                  member_id:
                    type: string
                  user_full_name:
                    type: string
                  user_email:
                    type: string
                  user_phone:
                    type: string
                  user_barcode:
                    type: string
                  membership_name:
                    type: string
                  membership_plan_name:
                    type: string
                  membership_status:
                    type: string
                  discount_name:
                    type: string
                  is_trial:
                    type: boolean
                  is_payg:
                    type: boolean
                  total_visits_in_period:
                    type: integer
                  total_unique_daily_visits_in_period:
                    type: integer
    CurrentMembersByMembershipStatus:
      type: object
      properties:
        total:
          type: integer
        status:
          type: string
    CurrentMembersByMembershipName:
      type: object
      properties:
        membership_name:
          type: string
        plan_name:
          type: string
        total:
          type: integer
    CurrentMembersByMembershipAddonName:
      type: object
      properties:
        addon_name:
          type: string
        addon_plan_name:
          type: string
        total:
          type: integer
    CurrentMembers:
      type: object
      properties:
        member_id:
          type: string
        full_name:
          type: string
        email:
          type: string
        phone:
          type: string
        membership_name:
          type: string
        plan_name:
          type: string
        status:
          type: string
        end_at:
          type: string
          format: date-time
    PayrollRates:
      type: object
      properties:
        _id:
          type: string
          pattern: '^[a-f\d]{24}$'
        event_type:
          type: string
          enum:
            - class
            - appointment
        event_id:
          type: string
          pattern: '^[a-f\d]{24}$'
        event_name:
          type: string
        rate_price:
          type: integer
          minimum: 1
          description: the price multiplied by 100. This is done to handle decimal.
        rate_group_id:
          type: string
          pattern: '^[a-f\d]{24}$'
        rate_bonus:
          type: array
          items:
            type: object
            properties:
              bonus_id:
                type: string
                pattern: '^[a-f\d]{24}$'
              bonus_type:
                type: string
                enum:
                  - SINGLE_THRESHOLD
              bonus_settings:
                anyOf:
                  - $ref: '#/paths/~12.2~1branches~1%7BbranchId%7D~1payroll-rates/post/requestBody/content/application~1json/schema/properties/rate_bonus/items/properties/bonus_settings/items'
    PayrollGroups:
      type: object
      properties:
        _id:
          type: string
          pattern: '^[a-f\d]{24}$'
        branch_id:
          type: string
          pattern: '^[a-f\d]{24}$'
        name:
          type: string
        staff_ids:
          type: array
          description: 'The staff IDs to which these group rates apply. If empty, it will apply to all.'
          items:
            type: string
        rate_classes_count:
          type: integer
          minimum: 0
          description: Total count of the class rates for this group.
        rate_appointments_count:
          type: integer
          minimum: 0
          description: Total count of the appointment rates for this group.
        created:
          type: string
          description: date-time or object with seconds and microseconds
        created_by:
          type: string
          pattern: '^[a-f\d]{24}$'
          description: user id of who created this record
        modified:
          type: string
          description: date-time or object with seconds and microseconds
        modified_by:
          type: string
          pattern: '^[a-f\d]{24}$'
          description: user id of who updated this record
    Program:
      type: object
      properties:
        private:
          type: boolean
          description: The visibility of the events generated in the program
        email_admin:
          type: boolean
          description: The capacity to receive notification on booking requests
        branch_id:
          type: string
          description: The unique identifier of the branch
        namespace:
          type: string
          description: The namespace of the studio
        active:
          type: boolean
          description: The current status of the program
        schedule:
          type: array
          items:
            type: object
            properties:
              days_week:
                type: integer
                description: The day of the week of the event
              start_time:
                type: string
                description: The UTC start time of the event
              end_time:
                type: string
                description: The UTC end time of the event
              active:
                type: boolean
                description: The status of the event
              facility:
                type: string
                description: The identifier of the facility
              level:
                type: string
                description: The required level for the event
              trainers:
                type: array
                description: The list of identifiers of the trainers added to the event
                items:
                  type: string
              size:
                type: integer
                description: The capacity of the event
              displayAdd:
                type: boolean
                description: The current status of the campaign
              code:
                type: string
                description: The identifier of the schedule
        schedule_default:
          type: object
          properties:
            facility:
              type: string
              description: The identifier of the facility
            trainers:
              type: array
              description: The list of trainers added
              items:
                type: string
            size:
              type: integer
              description: The capacity of the event
            level:
              type: string
              description: The required level
        categories:
          type: array
          description: The list of the identifiers of the categories for the program
          items:
            type: string
        name:
          type: string
          description: The label of the program
        description:
          type: string
          description: The characteristics of the program
        image:
          type: string
          description: The identifier to retrieve the image
        allowed_member_types:
          description: The list of types of users that can book a event in the program
          type: array
          items:
            type: object
            properties:
              price:
                type: integer
                description: The price of the event
              type:
                type: string
                description: The identifier of the type
        date_start:
          type: string
          description: The local time the program starts
        date_finish:
          type: string
          description: The local time the program ends
        facility:
          type: string
          description: The identifier of the facility
        pricing:
          type: string
          description: The type of pricing of the program
        spot_booking_enabled:
          type: boolean
          description: The capacity to book spots in the events of the program
    ProgramSlots:
      type: object
      properties:
        slots:
          type: array
          items:
            type: object
            properties:
              bookable:
                type: boolean
                description: If the slot can be booked
              reservable:
                type: boolean
                description: If the slot can be reserved
              startTime:
                type: integer
                description: The local timestamp that the slot starts
              endTime:
                type: integer
                description: The local timestamp that the slot ends
              errors:
                type: array
                items:
                  type: string
                  description: The variety of errors that can be returned
                  enum:
                    - MEMBER_HAS_ANOTHER_RESERVATION_AT_THE_TIME
                    - EVENT_DOES_NOT_EXIST
                    - SLOT_IS_IN_THE_PAST
                    - BOOKING_WINDOW_IS_CLOSED
                    - BOOKING_WINDOW_IS_NOT_OPEN_YET
                    - CLASS_IS_NOT_IN_PAYMENT_CYCLE
                    - MEMBER_IS_NOT_AVAILABLE_AT_THE_TIME
                    - MEMBERSHIP_IS_NOT_ALLOWED_TO_BOOK_IN_THE_CLASS
                    - MEMBER_IS_OUT_OF_CREDITS
                    - MEMBER_HAS_TO_PAY_FOR_THE_CLASS
                    - MEMBERSHIP_IS_EXPIRED
                    - MEMBERSHIP_IS_LOCKED
                    - MEMBERSHIP_IS_PAUSED
                    - MEMBERSHIP_IS_NOT_UNLIMITED_OR_RESTRICTED
                    - MEMBER_HAS_REACHED_MAX_NUMBER_OF_STRIKES
                    - CLASS_IS_FULL
              schedule_code:
                type: string
                description: Refers to the code of the schedule object retrieved
              batchId:
                type: string
                description: Unique identifier for the whole operation.
              memberId:
                type: string
                description: Refers to the booking's member.
              programId:
                type: string
                description: Refers to the program selected.
    Interactions:
      type: object
      properties:
        _id:
          type: string
        branch_id:
          type: string
        user_id:
          type: string
        description:
          type: string
        type:
          type: string
          enum:
            - NOTE
            - CALLED_AND_CONNECTED
            - CALLED_AND_NO_ANSWER
            - MANUAL_EMAIL
            - MEMBER_SERVICE_INTERACTION
            - AUTO_EMAIL
            - AUTO_SMS
            - AUTO_PUSH_NOTIFICATION
            - AMPLIFY_EMAIL
            - AMPLIFY_SMS
            - AMPLIFY_PUSH
        created:
          type: integer
    Activity:
      type: object
      properties:
        _id:
          type: string
          pattern: '^[0-9a-fA-F]{24}$'
        user_id:
          type: string
          pattern: '^[0-9a-fA-F]{24}$'
        requester_user_id:
          type: string
          pattern: '^[0-9a-fA-F]{24}$'
        event_identifier:
          type: string
          enum:
            - CARD_ADDED
            - CARD_REMOVED
            - CARD_REPLACED
            - MANDATE_ADDED
            - MANDATE_REMOVED
            - MANDATE_REPLACED
            - MANDATE_STATUS_CHANGED
            - ENTITY_BOOKED
            - PRODUCT_PURCHASED
            - NON_SUB_MEMBERSHIP_PURCHASED
            - MEMBERSHIP_PRORATED
            - MEMBERSHIP_REMOVED
            - CUSTOM_CHARGE_EXECUTED
            - SUBSCRIPTION_PURCHASED
            - SUBSCRIPTION_CYCLE_PAID
            - SUBSCRIPTION_CYCLE_PAYMENT_FAILED
            - SUBSCRIPTION_PAUSED
            - SUBSCRIPTION_CANCELLED
            - HISTORIC_TRANSACTION_IMPORTED
            - NSF_FEE
            - EXTERNAL_ACCOUNT_UPDATED
        event_context:
          type: object
          description: Dynamic object that contains the context of the event
        created:
          type: string
          format: date-time
        type:
          type: string
        image_url:
          type: string
          format: uri
        invoice_id:
          type: string
      required:
        - _id
        - user_id
        - event_identifier
        - created
    Enterprise:
      type: object
      properties:
        id:
          type: string
          pattern: '^[a-f\d]{24}$'
          description: Unique identifier for the enterprise
        corporate_id:
          type: string
          description: Corporate identifier for the enterprise
        name:
          type: string
          description: Name of the enterprise
      required:
        - id
        - corporate_id
        - name
    Course:
      type: object
      properties:
        _id:
          type: string
          description: The unique identifier of the course
        branch_id:
          type: string
          description: The unique identifier of the branch
        namespace:
          type: string
          description: The namespace of the branch
        name:
          type: string
          description: The name of the course
        description:
          type: string
          description: The description of the course
        active:
          type: boolean
          description: The status of the course
        private:
          type: boolean
          description: The privacy of the course
        facility:
          type: string
          description: The facility where the course takes place
        trainers:
          type: array
          description: The trainers that will deliver the course (ids of the trainers)
          items:
            type: string
        schedule:
          type: array
          description: The schedule of the course
          items:
            type: object
            properties:
              size:
                type: integer
                description: The maximum number of participants
              days:
                type: array
                description: The days of the week on which the course is conducted
                enum:
                  - MO
                  - TU
                  - WE
                  - TH
                  - FR
                  - SA
                  - SU
                items:
                  type: object
                  properties:
                    days:
                      type: string
                      description: The day of the week
                    start_time:
                      type: integer
                      description: The start time of that day
                    end_time:
                      type: integer
                      description: The end time of that day
                    active:
                      type: boolean
                      description: The flag showing whether the course is active or not
                    facility:
                      type: string
                      description: The facility where the course is conducted
                    trainers:
                      type: array
                      description: The list of trainers conducting the particular course's time slot
                      items:
                        type: string
                    size:
                      type: integer
                      description: The maximum number of participants
                    id:
                      type: integer
                      description: The unique identifier of the schedule
              trainers:
                type: array
                description: The list of trainers conducting the course
                items:
                  type: string
              facility:
                type: array
                description: The facility where the course takes place
                items:
                  type: string
              id:
                type: integer
                description: The unique identifier of the schedule
              start_date:
                type: integer
                description: The start date of the schedule
              end_date:
                type: integer
                description: The end date of the schedule
              label:
                type: string
                description: The label of the schedule
              total_bookings:
                type: integer
                description: The total number of bookings
              total_waitings:
                type: integer
                description: The total number of waitings
              course_id:
                type: string
                description: The unique identifier of the course
              booked:
                type: integer
                description: The number of booked participants
              has_booked:
                type: boolean
                description: The status of the booking
              booking_status:
                type: string
                description: The status of the booking
              booking_id:
                type: string
                description: The unique identifier of the booking
        pricing:
          type: array
          items:
            type: object
            properties:
              price:
                type: number
                description: The price of the course
              type:
                type: string
                description: 'The type of the price (PAYG - for pay as you go clients, MEMBER - for clients with active membership)'
                enum:
                  - PAYG
                  - MEMBER
              name:
                type: string
                description: The name of the price
        type:
          type: string
          description: The type of the event
        image_url:
          type: string
          description: The image URL of the course
  securitySchemes:
    coreapi_auth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: 'The API Key is a JWT in the form of "Bearer + {key}"'
    none:
      type: http
      scheme: none
      description: 'No API Key or JWT token is necessary, the endpoint is considered public'
