<?php

namespace Glofox\Validation\Rules\Shared;

use Carbon\Carbon;
use Glofox\Exception;
use Illuminate\Validation\Validator;

/**
 * <AUTHOR>
 */
class TimeValidator
{
    /**
     * @param string $start
     * @param string $end
     * @param int $hoursCheck
     * @param string $operator
     * @return void
     * @throws Exception
     */
    private function run(string $start, string $end, int $hoursCheck, string $operator): bool
    {
        // validate
        if (empty($start) || empty($end) || !is_numeric($hoursCheck)) {
            return false;
        }

        // get hours diff between the two dates
        try {
            $startDate = Carbon::parse($start);
            $hoursDiff = $startDate->diffInHours(Carbon::parse($end));
        } catch (\Exception $e) {
            // parse() can throw a default exception, handle this and pass false
            return false;
        }

        // perform some date validation
        // this is needed to stop events being saved with startDate ahead of endDate
        // ensure start date is less than end date
        if (in_array($operator, ['<', '<=']) && $startDate->greaterThan($end)) {
            throw new Exception("Start date is greater than end date");
        }
        // ensure start date is greater than end date
        if (in_array($operator, ['>', '>=']) && $startDate->lessThan($end)) {
            throw new Exception("Start date is less than end date");
        }

        // if we get then the check passed
        return $this->compare($hoursDiff, $hoursCheck, $operator);
    }

    /**
     * @param int $start
     * @param int $check
     * @param string $operator
     * @return bool
     * @throws Exception
     */
    private function compare(int $start, int $check, string $operator): bool
    {
        // now check the hours between dates
        switch ($operator) {
            default:
                // fall
            case '<':
                // we need to check the inverse requested operator because we will get incorrect results for
                // duplicate numbers
                if (!($start < $check)) {
                    throw new Exception("End date is less than {$check} hours from the start date");
                }
                break;
            case '>':
                if (!($start > $check)) {
                    throw new Exception("End date is greater than {$check} hours from the start date");
                }
                break;
            case '<=':
                if (!($start <= $check)) {
                    throw new Exception("End date is more than {$check} hours from the start date");
                }
                break;
            case '>=':
                if (!($start >= $check)) {
                    throw new Exception("End date is less than or equals to {$check} hours from start date");
                }
                break;
        }

        return true;
    }

    /**
     * @throws Exception
     */
    public function validateHoursDiffLessThanOrEqual(
        string $attribute,
        $value,
        array $parameters,
        Validator $validator
    ): bool {
        $start = $validator->getData()[$parameters[0]];
        if (is_int($start)) {
            $start = Carbon::createFromTimestamp($start)->toDateTimeString();
        }

        if (is_int($value)) {
            $value = Carbon::createFromTimestamp($value)->toDateTimeString();
        }

        return $this->run(
            $start,
            $value,
            (int)$parameters[1],
            '<='
        );
    }

    /**
     * @throws Exception
     */
    public function validateHoursDiffLessThan(string $attribute, $value, array $parameters, Validator $validator): bool
    {
        $start = $validator->getData()[$parameters[0]];
        if (is_int($start)) {
            $start = Carbon::createFromTimestamp($start)->toDateTimeString();
        }

        if (is_int($value)) {
            $value = Carbon::createFromTimestamp($value)->toDateTimeString();
        }

        return $this->run(
            $start,
            $value,
            (int)$parameters[1],
            '<'
        );
    }

    /**
     * @throws Exception
     */
    public function validateHoursDiffGreaterThanOrEqual(
        string $attribute,
        $value,
        array $parameters,
        Validator $validator
    ): bool {
        $start = $validator->getData()[$parameters[0]];
        if (is_int($start)) {
            $start = Carbon::createFromTimestamp($start)->toDateTimeString();
        }

        if (is_int($value)) {
            $value = Carbon::createFromTimestamp($value)->toDateTimeString();
        }

        return $this->run(
            $start,
            $value,
            (int)$parameters[1],
            '>='
        );
    }

    /**
     * @throws Exception
     */
    public function validateHoursDiffGreaterThan(
        string $attribute,
        $value,
        array $parameters,
        Validator $validator
    ): bool {
        $start = $validator->getData()[$parameters[0]];
        if (is_int($start)) {
            $start = Carbon::createFromTimestamp($start)->toDateTimeString();
        }

        if (is_int($value)) {
            $value = Carbon::createFromTimestamp($value)->toDateTimeString();
        }

        return $this->run(
            $start,
            $value,
            (int)$parameters[1],
            '>'
        );
    }
}
