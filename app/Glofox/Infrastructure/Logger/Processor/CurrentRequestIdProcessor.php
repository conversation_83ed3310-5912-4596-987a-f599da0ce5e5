<?php

namespace Glofox\Infrastructure\Logger\Processor;

use Glofox\Http\Requests\CurrentRequestIdServiceInterface;

class CurrentRequestIdProcessor
{
    private \Glofox\Http\Requests\CurrentRequestIdServiceInterface $service;

    public function __construct(
        CurrentRequestIdServiceInterface $service
    ) {
        $this->service = $service;
    }

    public function __invoke(array $record): array
    {
        $record['extra']['requestId'] = $this->service->getCurrentRequestId();

        return $record;
    }
}