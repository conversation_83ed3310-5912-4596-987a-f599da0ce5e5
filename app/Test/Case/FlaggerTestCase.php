<?php

use Glofox\Domain\FeatureFlags\Clients\LaunchDarklySDKClient;
use Glofox\Domain\FeatureFlags\FlagEnablers\LaunchDarklyEnabler;
use Glofox\Domain\FeatureFlags\Http\Clients\LaunchDarklyHttpClient;

App::import('Test/Case', 'GlofoxTestCase');

abstract class FlaggerTestCase extends GlofoxTestCase
{
    protected const FLAGGER_ENV = 'test-env';

    private LaunchDarklyHttpClient $mockedClient;
    private LaunchDarklySDKClient $mockedSdkClient;
    private LaunchDarklyEnabler $flagger;

    abstract protected function getFlaggerKey(): string;

    abstract protected function getFlaggerClass(): string;

    public function setUp(): void
    {
        parent::setUp();

        $mockedClient = Mockery::mock(LaunchDarklyHttpClient::class);
        $this->mockedClient = $mockedClient;
        $mockedSdkClient = Mockery::mock(LaunchDarklySDKClient::class);
        $this->mockedSdkClient = $mockedSdkClient;
        $flaggerClass = $this->getFlaggerClass();
        $this->flagger = new $flaggerClass($mockedClient, $mockedSdkClient);
    }

    public function tearDown(): void
    {
        parent::tearDown();
        Mockery::close();
    }

    public function test_it_enables_flag_for_branches(): void
    {
        $data = $this->doMockClient('addTargets');
        $this->flagger->setEnv($data['env']);
        $this->flagger->enable($data['branches']);
    }

    public function test_it_disables_flag_for_branches(): void
    {
        $data = $this->doMockClient('removeTargets');
        $this->flagger->setEnv($data['env']);
        $this->flagger->disable($data['branches']);
    }

    public function test_is_flag_enabled_for_branch(): void
    {
        $key = $this->getFlaggerKey();
        $branch = new MongoId();

        $this->mockedSdkClient
            ->shouldReceive('isEnabled')
            ->withArgs(function ($flagKey, $target) use ($key, $branch) {
                self::assertEquals($key, $flagKey);
                self::assertEquals($branch, $target);

                return true;
            });

        $this->flagger->has((string)$branch);
    }

    /**
     * @param string $shouldReceive
     * @return array {env: string, branches: array}
     */
    private function doMockClient(string $shouldReceive): array
    {
        $env = static::FLAGGER_ENV;
        $key = $this->getFlaggerKey();
        $branches = [
            (string)new MongoId(),
            (string)new MongoId(),
            (string)new MongoId(),
        ];

        $this->mockedClient
            ->shouldReceive($shouldReceive)
            ->withArgs(function ($environment, $flagKey, $targets) use ($env, $key, $branches) {
                self::assertEquals($env, $environment);
                self::assertEquals($key, $flagKey);
                self::assertEquals($branches, $targets);

                return true;
            });

        return ['env' => $env, 'branches' => $branches];
    }
}
