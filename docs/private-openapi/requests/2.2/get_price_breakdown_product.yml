type: object
properties:
  price:
    type: number
    example: 2
  unit_price:
    type: number
    description: The price of a single unit of the item
    example: 1
  quantity:
    type: number
    description: The quantity of the item
    example: 2
  promo_code:
    type: string
    example: PROMO-123
    description: The promo code.
  discount_ids:
    items:
      type: string
    type: array
    description: The private ID of a discount.
  service_type:
    type: string
    example: products
  service_id:
    type: string
    example: product-id-123