
## Group Payment Methods

### Get Payment Method [/branches/{branchId}/payment-methods]

#### Get Payment Methods [GET]

+ Parameters
    + includes: provider,provider.iframe (string, optional) - comma separated list of nested resources that should be included as part of the response

+ Response 200 (application/json)

    {
        "success": true,
        "meta": {
            "pagination": {
                "count": 8,
                "perPage": 50,
                "currentPage": 1,
                "links": {
                    "previous": null,
                    "next": null
                }
            }
        },
        "data": [
            {
                "_id": "605caaeae6bfb64e62463d1b",
                "branch_id": "605b7ab5d198a676d6264de3",
                "active": true,
                "staff_only": false,
                "type_id": "DIRECT_DEBIT",
                "account_management_link": null,
                "provider": {
                    "id": "5c470d0b2719ff1142b32180",
                    "name": "GOCARDLESS",
                    "charge_percentage": 0,
                    "is_charge_percentage_editable": false,
                    "fixed_charge": 0,
                    "is_fixed_charge_editable": false,
                    "publishable_key": "309623",
                    "account_id": "309623",
                    "tokenization_handler": "GOCARDLESS",
                }
            },
            {
                "_id": "605b7b6353f34c16533c4db8",
                "branch_id": "605b7ab5d198a676d6264de3",
                "active": true,
                "staff_only": false,
                "type_id": "WALLET",
                "account_management_link": null,
                "provider": {
                    "id": "5d8a3dc8bff4fd0898ced66b",
                    "name": "WALLET",
                    "charge_percentage": 0,
                    "is_charge_percentage_editable": false,
                    "fixed_charge": 0,
                    "is_fixed_charge_editable": false,
                    "publishable_key": null,
                    "account_id": "309615",
                    "tokenization_handler": null
                }
            },
            {
                "_id": "605b7b6153f34c16533c4db7",
                "branch_id": "605b7ab5d198a676d6264de3",
                "active": true,
                "staff_only": false,
                "type_id": "CARD",
                "account_management_link": null,
                "same_card_and_dd": true,
                "provider": {
                    "id": "5f4758e8af36710a928afc4b",
                    "name": "STRIPE_CUSTOM_EU",
                    "charge_percentage": 2.4,
                    "is_charge_percentage_editable": true,
                    "fixed_charge": 0.25,
                    "is_fixed_charge_editable": true,
                    "publishable_key": "pk_test_1loGihTKRmyb83crV9eKxhsv",
                    "account_id": "309615",
                    "tokenization_handler": "STRIPE"
                }
            },
            {
                "_id": "605b7ab6d198a676d6264de8",
                "branch_id": "605b7ab5d198a676d6264de3",
                "active": true,
                "staff_only": true,
                "type_id": "FLEXIBLE",
                "account_management_link": null,
                "provider": {
                    "id": "6013e76b6f3d128c6378c1be",
                    "name": "FLEXIBLE",
                    "charge_percentage": 0,
                    "is_charge_percentage_editable": false,
                    "fixed_charge": 0,
                    "is_fixed_charge_editable": false,
                    "publishable_key": null,
                    "account_id": "309614",
                    "tokenization_handler": null
                }
            },
            {
                "_id": "605b7ab6d198a676d6264de7",
                "branch_id": "605b7ab5d198a676d6264de3",
                "active": true,
                "staff_only": false,
                "type_id": "PAY_LATER",
                "account_management_link": null,
                "provider": {
                    "id": "5b2a3652c7805f005e37dcb5",
                    "name": "PAY_LATER",
                    "charge_percentage": 0,
                    "is_charge_percentage_editable": false,
                    "fixed_charge": 0,
                    "is_fixed_charge_editable": false,
                    "publishable_key": null,
                    "account_id": "309614",
                    "tokenization_handler": null
                }
            },
            {
                "_id": "605b7ab6d198a676d6264de6",
                "branch_id": "605b7ab5d198a676d6264de3",
                "active": true,
                "staff_only": true,
                "type_id": "BANK_TRANSFER",
                "account_management_link": null,
                "provider": {
                    "id": "5b2a3661c7805f005f2520b5",
                    "name": "BANK_TRANSFER",
                    "charge_percentage": 0,
                    "is_charge_percentage_editable": false,
                    "fixed_charge": 0,
                    "is_fixed_charge_editable": false,
                    "publishable_key": null,
                    "account_id": "309614",
                    "tokenization_handler": null
                }
            },
            {
                "_id": "605b7ab6d198a676d6264de5",
                "branch_id": "605b7ab5d198a676d6264de3",
                "active": true,
                "staff_only": true,
                "type_id": "COMPLIMENTARY",
                "account_management_link": null,
                "provider": {
                    "id": "5b2a3646c7805f005f2520b4",
                    "name": "COMPLIMENTARY",
                    "charge_percentage": 0,
                    "is_charge_percentage_editable": false,
                    "fixed_charge": 0,
                    "is_fixed_charge_editable": false,
                    "publishable_key": null,
                    "account_id": "309614",
                    "tokenization_handler": null
                }
            },
            {
                "_id": "605b7ab5d198a676d6264de4",
                "branch_id": "605b7ab5d198a676d6264de3",
                "active": true,
                "staff_only": true,
                "type_id": "CASH",
                "account_management_link": null,
                "provider": {
                    "id": "5b2a363bc7805f005e37dcb4",
                    "name": "CASH",
                    "charge_percentage": 0,
                    "is_charge_percentage_editable": false,
                    "fixed_charge": 0,
                    "is_fixed_charge_editable": false,
                    "publishable_key": null,
                    "account_id": "309614",
                    "tokenization_handler": null
                }
            }
        ]
    }
