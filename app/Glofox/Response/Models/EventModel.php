<?php

namespace Glofox\Response\Models;

use Glofox\Domain\Events\MemberEligibility;
use Glofox\Response\BaseModel;
use Glofox\Response\Models\Events\FacilityObjModel;
use Glofox\Response\Models\Events\ProgramObjModel;

/**
 * @SuppressWarnings(PHPMD)
 */
class EventModel extends BaseModel
{
    /**
     * @var string
     */
    public $_id = '';

    /**
     * @var string
     */
    public $namespace = '';

    /**
     * @var string
     */
    public $branch_id = '';

    /**
     * @var string
     */
    public $program_id = '';

    /**
     * @var bool
     */
    public $active = false;

    /**
     * @var string
     */
    public $name = '';

    /**
     * @var string
     */
    public $description = '';

    /**
     * @var int
     */
    public $time_start = 0;

    public $level;

    /**
     * @var int
     */
    public $size = '';

    /**
     * @var string
     */
    public $facility = '';

    /**
     * @var array
     */
    public $trainers = [];

    /**
     * @var bool
     */
    public $private = false;

    /**
     * @var int
     */
    public $booked = 0;

    /**
     * @var int
     */
    public $waiting = 0;

    /**
     * @var bool
     */
    public $has_booked = false;

    /**
     * @var null
     */
    public $booking_status = null;

    /**
     * @var int
     */
    public $duration = 0;

    /**
     * @var string
     */
    public $type = '';

    /**
     * @var ProgramObjModel
     */
    public $program_obj;

    /**
     * @var array
     */
    public $trainers_obj = [];

    /**
     * @var FacilityObjModel
     */
    public $facility_obj;

    /**
     * @var string
     */
    public $image_url = '';

    /**
     * @var int
     *          Position in the waiting list
     */
    public $position = null;

    /**
     * @var int
     *          Members are allowed to book after this time
     */
    public $open_booking_time = null;

    /**
     * @var int
     *          Members are allowed to book before this time
     */
    public $close_booking_time = null;

    /**
     * @var string
     *             Identifier of an instance of the course
     */
    public $session_id = null;

    /**
     * @var string
     *             Status of the event
     */
    public $status = null;

    /**
     * @var null
     */
    public $booking_id = null;

    /**
     * @var null
     */
    public $current_user_eligibility = MemberEligibility::UNKNOWN;

    protected array $allowedFields = [
        '_id',
        'namespace',
        'branch_id',
        'program_id',
        'active',
        'name',
        'description',
        'time_start',
        'level',
        'size',
        'facility',
        'trainers',
        'private',
        'booked',
        'waiting',
        'has_booked',
        'booking_status',
        'duration',
        'type',
        'program_obj',
        'trainers_obj',
        'facility_obj',
        'image_url',
        'position',
        'open_booking_time',
        'close_booking_time',
        'session_id', // Specific to courses
        'status',
        'booking_id',
        'model',
        'model_id',
        'current_user_eligibility',
        // Optional fields
        'parent',
        'is_online',
        'modified'
    ];

    public function __construct(array $data)
    {
        $this->setModelFields($data);
    }
}
