<?php

namespace CakeTestCases\Glofox\Domain\Integrations\ClassPass\Actions;

use Glofox\Domain\Bookings\Status as GlofoxBookingStatus;
use Glofox\Domain\Integrations\ClassPass\Actions\ListBookings;
use Glofox\Domain\Integrations\ClassPass\BookingStatus;

\App::import('Test/Case', 'GlofoxTestCase');

/**
 * Class ListBookingsTest.
 */
class ListBookingsTest extends \GlofoxTestCase
{
    /**
     * Fixtures.
     *
     * @var array
     */
    public $fixtures = [
        'app.booking', 'app.integration',
    ];

    /**
     * setUp method.
     */
    public function setUp()
    {
        parent::setUp();
        $this->ListBookings = new ListBookings();
    }

    /**
     * tearDown method.
     */
    public function tearDown()
    {
        parent::tearDown();
        unset($this->ListBookings);
    }

    public function testFormatBookings()
    {
        $bookings = [
            [
                '_id' => '5ade01592c896827ee993577',
                'namespace' => 'classpass_integrated',
                'branch_id' => '5addc25383266f65abf515c4',
                'user_id' => '5adde464b5cf4ad32eb7597e',
                'status' => GlofoxBookingStatus::BOOKED,
                'metadata' => [
                    'classpass' => [
                        '_id' => 'leClassPassBookingId',
                        'user_id' => 'leClassPassUserId',
                    ],
                ],
            ],
        ];

        $result = $this->ListBookings->formatBookings($bookings);
        $expectedContains = [
            'cp_user_id' => 'leClassPassUserId',
            'reservation_id' => 'leClassPassBookingId',
            'cp_status' => BookingStatus::get('ENROLLED')->getValue(),
        ];
        $this->assertArrayContainsArray($expectedContains, $result[0], 'Booked and attended not set');

        // Booked and attended set to false
        $bookingsCopy = $bookings;
        $bookingsCopy[0]['attended'] = false;
        $result = $this->ListBookings->formatBookings($bookingsCopy);
        $expectedContains = [
            'cp_user_id' => 'leClassPassUserId',
            'reservation_id' => 'leClassPassBookingId',
            'cp_status' => BookingStatus::get('MISSED')->getValue(),
        ];
        $this->assertArrayContainsArray($expectedContains, $result[0], 'Booked and attended set to false');

        // Booked and attended set to true
        $bookingsCopy = $bookings;
        $bookingsCopy[0]['attended'] = true;
        $result = $this->ListBookings->formatBookings($bookingsCopy);
        $expectedContains = [
            'cp_user_id' => 'leClassPassUserId',
            'reservation_id' => 'leClassPassBookingId',
            'cp_status' => BookingStatus::get('ATTENDED')->getValue(),
        ];
        $this->assertArrayContainsArray($expectedContains, $result[0], 'Booked and attended');

        // Canceled
        $bookingsCopy = $bookings;
        $bookingsCopy[0]['status'] = GlofoxBookingStatus::CANCELED;
        $result = $this->ListBookings->formatBookings($bookingsCopy);
        $expectedContains = [
            'cp_user_id' => 'leClassPassUserId',
            'reservation_id' => 'leClassPassBookingId',
            'cp_status' => BookingStatus::get('CANCELLED')->getValue(),
        ];
        $this->assertArrayContainsArray($expectedContains, $result[0], 'Canceled booking');
    }
}
