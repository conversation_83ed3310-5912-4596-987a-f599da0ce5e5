<?php

namespace Glofox\BatchPricing;

use Course;
use Glofox\Domain\Branches\Search\Expressions\BranchId;
use Glofox\Domain\Courses\Repositories\CoursesRepository;
use Glofox\Domain\Memberships\Repositories\MembershipsRepository;
use Glofox\Domain\Programs\Repositories\ProgramsRepository;
use Glofox\Domain\SalesTaxes\Services\PriceCalculatorServiceInterface;
use Glofox\Domain\Store\Products\Repositories\ProductsRepository;
use Glofox\Domain\TimeSlotPatterns\Repositories\TimeSlotPatternsRepository;
use Glofox\Domain\TimeSlotPatterns\Search\Expressions\Model;
use Glofox\Domain\TimeSlots\ModelList as TimeSlotsModelList;
use Glofox\Domain\Users\Repositories\UsersRepository;
use Glofox\Domain\Users\Search\Expressions\Type;
use Glofox\Domain\Users\UserTypesCollection;
use Glofox\Repositories\Search\Filters\Shared\IsActive;
use Illuminate\Support\Collection;
use Membership;
use Product;
use Program;
use TimeSlotPattern;
use User;
use Client;

class Calculator
{
    public function __construct(
        PriceCalculatorServiceInterface $priceCalculator,
        ProgramsRepository $programsRepository,
        MembershipsRepository $membershipsRepository,
        CoursesRepository $coursesRepository,
        UsersRepository $usersRepository,
        TimeSlotPatternsRepository $timeSlotPatternsRepository,
        ProductsRepository $productsRepository,
        Product $productModel,
        Course $courseModel,
        Membership $membershipModel,
        Program $programModel,
        TimeSlotPattern $timeSlotPatternModel,
        User $userModel,
        Client $clientsModel
    ) {
        $this->priceCalculator = $priceCalculator;
        $this->programsRepository = $programsRepository;
        $this->membershipsRepository = $membershipsRepository;
        $this->coursesRepository = $coursesRepository;
        $this->usersRepository = $usersRepository;
        $this->productsRepository = $productsRepository;
        $this->timeSlotPatternsRepository = $timeSlotPatternsRepository;
        $this->productModel = $productModel;
        $this->courseModel = $courseModel;
        $this->membershipModel = $membershipModel;
        $this->programModel = $programModel;
        $this->timeSlotPatternModel = $timeSlotPatternModel;
        $this->userModel = $userModel;
        $this->clientsModel = $clientsModel;
    }

    // TODO: if performance is very slow, leverage Swoole extension and make
    // sales-tax requests async.
    // Ref: https://glofox.slack.com/archives/C01H173SB1U/p1633344309436300
    public function recalculateStaticAssets(string $branchId, string $newTaxMode)
    {
        $assets = $this->fetchStaticAssetPrices($branchId);
        [$goodAssets, $badAssets] = Helpers::separateGoodAndBadAssets($assets);
        $indexedTaxBreakdowns = $this->fetchIndexedTaxBreakdowns($branchId, $goodAssets, $newTaxMode);

        $assetsWithNewPrice = array_map(fn($asset) => Helpers::mapNewPrice($asset, $indexedTaxBreakdowns, $newTaxMode), $goodAssets);

        $assetsToBePatched = array_filter($assetsWithNewPrice, fn(array $asset) =>
            // We don't want to update records whose prices don't change according to sales-tax
            $asset['result'] == 'success' && $asset['price'] != $asset['new_price']);

        $indexedAssetsByRecordId = Helpers::indexAssetsByRecordId($assetsToBePatched);

        $successfulOperations = array_reduce($indexedAssetsByRecordId, function ($acc, $assetsMap) {
            $result = $this->patchRecord($assetsMap);
            $responses = Helpers::mapPatchResponse($result, $assetsMap['assets']);
            return array_merge($acc, $responses);
        }, []);

        $allAssets = array_merge($badAssets, $assetsWithNewPrice);

        return array_reduce($allAssets, function ($operations, $asset) {
            if ($asset['result'] == 'error') {
                $operations[] = Helpers::mapToResponse($asset);
            }

            return $operations;
        }, $successfulOperations);
    }

    /**
     * Fetch tax breakdowns from sales-tax and index them in an associative
     * array by the service_id. This allows us to access all assets related to
     * the same service to avoid extra iterations upon processing.
     */
    public function fetchIndexedTaxBreakdowns(string $branchId, array $assets, string $newTaxMode): array
    {
        $options = [
            'discounts' => false,
        ];

        $options['taxmode'] = $newTaxMode === 'inclusive' ? 'exclusive' : 'inclusive';
        $body = json_encode(['products' => $assets], JSON_PARTIAL_OUTPUT_ON_ERROR);
        $body = $this->priceCalculator->calculateTaxes($branchId, $body, $options)->getData(true);
        $taxBreakdowns = $body['products'];


        // TODO: make another request to sales-tax service but with taxmode
        // flag, compare results and throw exception if they don't match.

        return Helpers::indexTaxBreakdowns($taxBreakdowns);
    }

    public function patchRecord(array $assetMap): array
    {
        $serviceType = $assetMap['service_type'];
        $patchedRecord = [];
        switch ($serviceType) {
            case 'classes':
                $patchedRecord = $this->patchProgram($assetMap);
                break;
            case 'trainers':
                $patchedRecord = $this->patchTrainer($assetMap);
                break;
            case 'memberships':
                $patchedRecord = $this->patchMembership($assetMap);
                break;
            case 'courses':
                $patchedRecord = $this->patchCourse($assetMap);
                break;
            case 'facilities':
                $patchedRecord = $this->patchFacility($assetMap);
                break;
            case 'products':
                $patchedRecord = $this->patchProduct($assetMap);
                break;
            default:
                throw new \Exception("Unknown service type: $serviceType", 1);
        }

        return $patchedRecord;
    }

    public function patchProduct(array $assetMap): array
    {
        $this->productModel->setUpInjector([], $assetMap['mongo_record_id'], $this->clientsModel->getUser());
        $product = $this->productModel->getById($assetMap['mongo_record_id']);
        $fields = Helpers::prepareProductPriceUpdateFields($assetMap['assets'], $product);
        $this->productModel->id = $assetMap['mongo_record_id'];
        return $this->productModel->saveField('presentations', $fields['presentations']);
    }

    public function patchCourse(array $assetMap): array
    {
        $this->courseModel->setUpInjector([], $assetMap['mongo_record_id'], $this->clientsModel->getUser());
        $course = $this->courseModel->find('first', ['conditions' => ['_id' => $assetMap['mongo_record_id']]])['Course'];
        $fields = Helpers::prepareAllowedMemberTypeFields($assetMap['assets'], $course);
        $this->courseModel->id = $assetMap['mongo_record_id'];
        $course['allowed_member_types'] = $fields['allowed_member_types'];
        return $this->courseModel->save($course);
    }

    public function patchMembership(array $assetMap): array
    {
        $this->membershipModel->setUpInjector([], $assetMap['mongo_record_id'], $this->clientsModel->getUser());
        $membership = $this->membershipModel->getById($assetMap['mongo_record_id']);
        $fields = Helpers::prepareMembershipPriceUpdateFields($assetMap['assets'], $membership);
        $this->membershipModel->id = $assetMap['mongo_record_id'];
        return  $this->membershipModel->saveField('plans', $fields['plans']);
    }

    public function patchProgram(array $assetMap): array
    {
        $program = $this->programModel->getByIdWithoutConditions($assetMap['mongo_record_id']);
        $fields = Helpers::prepareAllowedMemberTypeFields($assetMap['assets'], $program['Program']);
        $this->programModel->id = $assetMap['mongo_record_id'];
        $data = $fields['allowed_member_types'];
        return $this->programModel->saveField('allowed_member_types', $data);
    }

    public function patchFacility(array $assetMap): array
    {
        $this->timeSlotPatternModel->setUpInjector([], $assetMap['mongo_record_id'], $this->clientsModel->getUser());
        $timeslotPattern = $this->timeSlotPatternModel->find('first', ['conditions' => ['_id' => $assetMap['mongo_record_id']]]);
        $fields = Helpers::prepareAllowedMemberTypeFields($assetMap['assets'], $timeslotPattern['TimeSlotPattern']);
        $this->timeSlotPatternModel->id = $assetMap['mongo_record_id'];
        return $this->timeSlotPatternModel->saveField('allowed_member_types', $fields['allowed_member_types']);
    }

    public function patchTrainer(array $assetMap): array
    {
        $trainer = $this->userModel->getTrainerById($assetMap['mongo_record_id']);
        $fields = Helpers::prepareTrainerPriceUpdateFields($assetMap['assets'], $trainer['User']);
        $this->userModel->id = $assetMap['mongo_record_id'];
        $result = $this->userModel->saveField('_pattern', $fields['_pattern']);

        if ($result)
        {
            $timeslotPattern = $this->timeSlotPatternModel->find('first', ['conditions' => ['model' => "users", "model_id" => $assetMap['mongo_record_id']]]);
            $this->timeSlotPatternModel->id = $timeslotPattern['TimeSlotPattern']['_id'];
            $result = $this->timeSlotPatternModel->saveField('allowed_member_types', $fields['_pattern']['allowed_member_types']);
        }

        return $result;
    }

    /**
     * Fetches all static assets from Mongo and maps the data to a standardised
     * format that the algorithm to recalculate prices can understand.
     *
     * By doing the mapping here we're simplyfing the algorithm.
     *
     * TODO: fine-tune the queries so only assets containing ALL the data we require are fetched.
     */
    public function fetchStaticAssetPrices(string $branchId): array
    {
        $static_assets = [];

        $programs = ($this->programsRepository->addCriteria(new BranchId($branchId))
            ->addCriteria(new IsActive())
            ->order(['name' => 1])
            ->fields(['name', 'pricing', 'allowed_member_types'])
            ->find() ?? [new Collection()]);

        $static_assets[] = self::mapAssetFunction($programs, fn($arg) => Asset::fromProgram($arg));

        $courses = ($this->coursesRepository->addCriteria(new BranchId($branchId))
            ->addCriteria(new IsActive())
            ->order(['name' => 1])
            ->fields(['name', 'allowed_member_types'])
            ->find() ?? new Collection());

        $static_assets[] = self::mapAssetFunction($courses, fn($arg) => Asset::fromCourse($arg));

        $memberships = ($this->membershipsRepository->addCriteria(new BranchId($branchId))
            ->addCriteria(new IsActive())
            ->order(['name' => 1])
            ->fields(['name', 'plans'])
            ->find() ?? new Collection());

        $static_assets[] = self::mapAssetFunction($memberships, fn($arg) => Asset::fromMembership($arg));

        // Prices aren't found in facilities, but in 'time_slot_patterns'. Since
        // the actual timeslot pattern has a reference to the facility model, we
        // don't need to query facilites.
        $timeslotPatterns = ($this->timeSlotPatternsRepository
            ->addCriteria(new BranchId($branchId))
            ->addCriteria(new Model(TimeSlotsModelList::byValue(TimeSlotsModelList::FACILITIES)))
            ->order(['name' => 1])
            ->fields(['name', 'model_id', 'allowed_member_types'])
            ->find() ?? new Collection());

        $static_assets[] = self::mapAssetFunction($timeslotPatterns, fn($arg) => Asset::fromTimeslotPattern($arg));

        $trainers = ($this->usersRepository->addCriteria(new BranchId($branchId))
            ->addCriteria(new Type(UserTypesCollection::make([\UserType::TRAINER()])))
            ->order(['first_name' => 1])
            ->fields(['name', '_pattern'])
            ->find() ?? new Collection());

        $static_assets[] = self::mapAssetFunction($trainers, fn($arg) => Asset::fromTrainer($arg));

        $products = ($this->productsRepository->addCriteria(new BranchId($branchId))
            ->addCriteria(new IsActive())
            ->order(['name' => 1])
            ->fields(['name', 'presentations'])
            ->find() ?? new Collection());

        $static_assets[] = self::mapAssetFunction($products, fn($arg) => Asset::fromProduct($arg));

        return Helpers::recursiveFlatten($static_assets);
    }

    /**
     * mapAssetFunction simply maps a closure to all the elements of the list.
     * That list may be an array or it may an instance of
     * Illuminate\Support\Collection.
     */
    private static function mapAssetFunction($maybeArray, callable $fun): array
    {
        return array_map(function($el) use($fun) {
            if (!is_array($el)) {
                $el = $el->toArray();
            }

            return $fun($el);
        }, $maybeArray);
    }
}
