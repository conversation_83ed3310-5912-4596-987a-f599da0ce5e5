const branchesIdsOnMembershipsService = [];

db.getCollection('a_b_test_clients').find({
    active: true,
    type: 'memberships_service'
}).forEach((ab) => {
    branchesIdsOnMembershipsService.push(ab.branch_id);
});

const activitiesWithoutStartDate = db.getCollection('activities').find({
    'event_identifier': 'SUBSCRIPTION_PURCHASED',
    'event_context.starts_on': 'PURCHASE_DATE',
    'event_context.type': 'time',
    'event_context.start_date': { $exists: false },
});

let activitiesToBeUpdated = [];
let activitiesIdsCannotBeUpdated = [];
let progress = 0;

activitiesWithoutStartDate.forEach((activity) => {
    const member = db.getCollection('users').findOne({
        _id: ObjectId(activity.user_id),
        branch_id: { $in: branchesIdsOnMembershipsService }
    });

    // If the activity is from an user who is from a branch which is not in the memberships service
    if (!member) {
        return;
    }

    const stripeCharge = db.getCollection('stripe_charges').find({
        'metadata.glofox_event': 'subscription_payment',
        'metadata.user_id': activity.user_id,
        'metadata.membership_id': activity.event_context.membership_id,
        'metadata.plan_code': activity.event_context.plan_code.toString(),
    }).sort({created: 1}).limit(1);

    if (!stripeCharge || stripeCharge.length == 0 || !stripeCharge[0]) {
        activitiesIdsCannotBeUpdated.push(activity._id);

        return;
    }

    const createdDate = new Date(stripeCharge[0].created);
    const month = createdDate.getMonth() < 10 ? '0' + createdDate.getMonth() : createdDate.getMonth();
    const day = createdDate.getDate() < 10 ? '0' + createdDate.getDate() : createdDate.getDate();

    const act = {
        id: activity._id,
        newStartDate: createdDate.getFullYear() + '-' + month + '-' + day,
    };
    activitiesToBeUpdated.push(act);
});

print('Total activities to be updated => ', activitiesToBeUpdated.length);
print('Activities ids to be updated => ');
print(activitiesToBeUpdated);
print('Total activities cannot be updated => ', activitiesIdsCannotBeUpdated.length);
print('Activities ids cannot be updated => ');
print(activitiesIdsCannotBeUpdated);

activitiesToBeUpdated.forEach((activity) => {
    db.getCollection('activities').updateOne({ _id: activity.id }, {
        $set: {
            'event_context.start_date': activity.newStartDate
        }
    });

    progress++;

    print('Progress => ', progress, '/' , activitiesToBeUpdated.length);
});
