<?php

declare(strict_types=1);

namespace Glofox\Infrastructure\Services\Reports\Requests;

use Psr\Http\Message\RequestInterface;

class GetMemberKPIsRequest
{
    private string $branchId;
    private string $userId;

    private \Psr\Http\Message\RequestInterface $request;

    public function __construct(
        string $branchId,
        string $userId,
        RequestInterface $request
    ) {
        $this->branchId = $branchId;
        $this->userId = $userId;
        $this->request = $request;
    }

    public function branchId(): string
    {
        return $this->branchId;
    }

    public function userId(): string
    {
        return $this->userId;
    }

    public function getRequest(): RequestInterface
    {
        return $this->request;
    }
}
