type: object
properties:
  _id:
    type: string
    description: Unique identifier for the booking.
  branch_id:
    type: string
    description: The branch_id of the studio.
  namespace:
    type: string
    description: The namespace of the studio.
  user_id:
    type: string
    description: The id of the user booked into the event.
  user_name:
    type: string
    description: The name of the user booked into the event.
  program_id:
    type: string
    description: The id of the booked program.
  event_id:
    type: string
    description: The id of the booked event.
  event_name:
    type: string
    description: The name of the booked event.
  membership_name:
    type: string
    description: The name of the membership used to book the class.
  plan_name:
    type: string
    description: The name of the membership plan used to book the class.
  status:
    type: string
    enum:
      - BOOKED
      - WAITING
      - CANCELED
      - RESERVED
      - FAILED
    description: |
      A booking will have one of three statuses.
      * Booked
        * The user is booked into the class

      * Waiting
        * The user is on the waiting list for the class.When a space is available they will be notified or they will be automatically added to the class, depending on the studios settings.

      * Canceled
        * The user has no active booking for the class. If the user re-books in this status will changed from Canceled to Booked
  type:
    type: string
    description: For class bookings type will be events.
  time_start:
    type: integer
    description: The time in UTC when the class starts.
  guest_bookings:
    type: integer
    description: |
      Users can book in guests. The number of spaces booked is guest_bookings + 1
      The booking object counts as 1.

      Eg;
      Class size = 10

      guest_bookings = 3

      Remaining spaces = Class size minus (guest_bookings +1)

      Remaining spaces = 6
  duration:
    type: integer
    description: The number of minutes the class is on for. End time of the class is found by adding this to the time start.
  created:
    type: integer
    description: The time in UTC when the bookings was created.
  modified:
    type: integer
    description: The time in UTC when the bookings was last updated.
