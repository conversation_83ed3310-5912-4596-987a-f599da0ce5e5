<?php

declare(strict_types=1);

namespace Glofox\Traversable\Exceptions;

use Throwable;
use UnsuccessfulOperation;

class DomainException extends UnsuccessfulOperation
{
    public function __construct(string $message, string $messageCode, int $code = 400, $messageData = false, Throwable $previous = null)
    {
        parent::__construct($message, $messageData, $previous);

        $this
            ->setMessageCode($messageCode)
            ->setCode($code);
    }

    public function toArray(): array
    {
        return [
            'success' => false,
            'message' => $this->getMessage(),
            'message_code' => $this->getMessageCode(),
            'message_data' => $this->getMessageData(),
            'errors' => $this->getErrors()
        ];
    }
}
