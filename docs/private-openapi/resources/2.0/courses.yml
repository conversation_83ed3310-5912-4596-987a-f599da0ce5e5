get:
  tags:
    - Courses
  summary: ''
  operationId: get-courses
  description: This endpoint returns a filtered courses list
  x-internal: true
  security:
    - coreapi_auth: [ ]
  parameters:
    - name: page
      description: The page number used to filter the course listings
      in: query
      schema:
        type: integer
        default: 1
    - name: active
      description: The flag showing whether the course is active or not
      in: query
      schema:
        type: boolean
        default: true
  responses:
    '200':
      description: OK
      content:
        application/json:
          schema:
            allOf:
              - $ref: "../../schemas/pagination.yml"
              - type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: "../../schemas/course.yml"