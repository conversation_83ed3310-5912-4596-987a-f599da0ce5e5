<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\Charges\Services;

use Glofox\Domain\AsyncEvents\Events\TransactionFinishedEventMeta;
use Glofox\Domain\AsyncEvents\Events\TransactionFinishedEventPayload;
use Glofox\Domain\Charges\Services\ChargesPublisher;
use Glofox\Eventkit\Publisher\DomainEventPublisher;
use Psr\Log\LoggerInterface;

\App::import('Test/Case', 'GlofoxTestCase');

class ChargesPublisherTest extends \GlofoxTestCase
{
    public $fixtures = [];

    public function test_it_should_send_transaction_finished_events(): void
    {
        $domainEventPublisher = \Mockery::mock(DomainEventPublisher::class);
        $domainEventPublisher
            ->shouldReceive('publish')
            ->once();

        $logger = \Mockery::mock(LoggerInterface::class);
        $logger->shouldReceive('info');

        $meta = \Mockery::mock(TransactionFinishedEventMeta::class);
        $payload = \Mockery::mock(TransactionFinishedEventPayload::class);

        $publisher = new ChargesPublisher($domainEventPublisher, $logger);
        $publisher->sendTransactionFinishedEvent($meta, $payload);
    }
}
