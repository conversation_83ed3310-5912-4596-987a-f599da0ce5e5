<?php

namespace CakeTestCases\Controller;

use CakeTestCases\Glofox\Domain\Authentication\Token\TokenGeneration;
use CakeTestCases\Glofox\Domain\Users\Traits\AuthenticateUsersTrait;
use CakeTestCases\Glofox\Domain\Users\Traits\FetchUsersTrait;
use Glofox\Domain\Credits\Models\CreditPack;
use Glofox\Domain\Credits\Models\CreditPackSourceType;
use Glofox\Domain\Credits\Repositories\CreditsRepository;
use Glofox\Domain\FeatureFlags\FeatureFlagInterface;
use Glofox\Domain\Users\Search\Expressions\UserId;
use Glofox\Domain\Users\Validation\Guard\ReceptionistRestrictionRevenueGuard;
use Glofox\Infrastructure\Flags\Flag;
use Glofox\Infrastructure\Flags\Flagger;
use Glofox\Infrastructure\Flags\FlaggerInterface;
use Glofox\Infrastructure\Honeycomb\HoneycombTracker;
use Glofox\Repositories\Search\Expressions\Shared\Id;
use Mockery;

\App::import('Test/Case', 'GlofoxControllerTestCase');
\App::uses('UserCreditsController', 'Controller');

class UserCreditsControllerTest extends \GlofoxControllerTestCase
{
    use TokenGeneration;
    use FetchUsersTrait;
    use AuthenticateUsersTrait;

    public $fixtures = [
        'app.user',
        'app.user_credit',
        'app.booking',
        'app.program',
        'app.facility',
        'app.event',
        'app.time_slot',
        'app.time_slot_pattern',
        'app.branch',
        'app.client',
        'app.access_control_list',
        'app.integrator',
    ];

    public function setUp()
    {
        parent::setUp();

        $this->creditsRepository = app()->make(CreditsRepository::class);
    }

    public function testFindAllAvailableByBranchIdAndUserId(): void
    {
        $this->loginAsUser(
            $this->fetchUser('59a7011a05c677bda916612a')
        );

        $options = ['method' => 'get'];

        $result = $this->testAction('/branch/49a7011a05c677b9a916612a/user/59c92229c3fad88b481add96/credits', $options);
        $result = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        self::assertGreaterThan(0, $result);
    }

    /**
     * testFindAllByBranchIdAndUserId method.
     */
    public function testFindAllByBranchIdAndUserId(): void
    {
        $this->loginAsUser(
            $this->fetchUser('59a7011a05c677bda916612a')
        );

        $options = ['method' => 'get'];

        $result = $this->testAction(
            '/userCredits/findAllByBranchIdAndUserId/49a7011a05c677b9a916612a/59c92229c3fad88b481add96',
            $options
        );
        $result = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        self::assertGreaterThan(0, $result);
    }

    /**
     * testSaveBatch method.
     */
    public function testSaveBatch(): void
    {
        $this->loginAsUser(
            $this->fetchUser('59a7011a05c677bda916612a')
        );

        $userId = '59c92229c3fad88b481add96';

        $data = [
            [
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'model' => 'programs',
                'category_id' => null,
                'num_sessions' => 10,
                'model_ids' => null,
                'expiry' => null,
                'start_date' => (new \DateTime())->format('d/m/Y'),
                'bookings' => [],
                'user_id' => $userId,
                'end_date' => null,
            ],
            [
                '_id' => '59cbb21930c9433ae01c8c8b',
                'branch_id' => '49a7011a05c677b9a916612a',
                'model' => 'programs',
                'model_ids' => null,
                'category_id' => null,
                'user_id' => $userId,
                'num_sessions' => 10,
                'membership_id' => '54107c1cd7b6ddc3a98b4577',
                'start_date' => date('Y-m-d'),
                'end_date' => date('Y-m-d', strtotime('+1 month')),
            ],
        ];

        $options = [
            'method' => 'get',
            'data' => json_encode($data, JSON_THROW_ON_ERROR),
        ];

        $result = $this->testAction('/user_credits/saveBatch/' . $userId, $options);
        $result = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        self::assertTrue($result['success']);
        self::assertNotEmpty($result['credit_packs']);

        $creditPacks = $this->creditsRepository
            ->addCriteria(new UserId($userId))
            ->find();

        self::assertCount(3, $creditPacks);

        self::assertNull($creditPacks[0]->source());
        self::assertNull($creditPacks[1]->source());
        self::assertSame(CreditPackSourceType::MANUALLY_ADDED_BY_STAFF, $creditPacks[2]->source()->type()->getValue());
    }

    public function test_it_blocks_receptionist_from_saving_credits_in_batch(): void
    {
        $receptionistRestrictionRevenueFlagger = $this->createIrisMockFeatureFlagger(Flag::RECEPTIONIST_RESTRICTIONS_REVENUE());
        $receptionistRestrictionRevenueFlagger->shouldReceive('hasByBranchId')
                ->andReturnTrue();

        $receptionistRestrictionGuard = new ReceptionistRestrictionRevenueGuard($receptionistRestrictionRevenueFlagger);
        app()->instance(ReceptionistRestrictionRevenueGuard::class, $receptionistRestrictionGuard);

        $this->loginAsUser(
            $this->fetchUser('59a7011a05c677bda916619b')
        );

        $userId = '59c92229c3fad88b481add96';

        $data = [
            [
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'model' => 'programs',
                'category_id' => null,
                'num_sessions' => 10,
                'model_ids' => null,
                'expiry' => null,
                'start_date' => (new \DateTime())->format('d/m/Y'),
                'bookings' => [],
                'user_id' => $userId,
                'end_date' => null,
            ],
            [
                '_id' => '59cbb21930c9433ae01c8c8b',
                'branch_id' => '49a7011a05c677b9a916612a',
                'model' => 'programs',
                'model_ids' => null,
                'category_id' => null,
                'user_id' => $userId,
                'num_sessions' => 10,
                'membership_id' => '54107c1cd7b6ddc3a98b4577',
                'start_date' => date('Y-m-d'),
                'end_date' => date('Y-m-d', strtotime('+1 month')),
            ],
        ];

        $options = [
            'method' => 'post',
            'data' => $data,
        ];

        $result = $this->testAction('/user_credits/saveBatch/' . $userId, $options);
        $result = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        self::assertFalse($result['success']);
        self::assertSame(
            'Branch 49a7011a05c677b9a916612a has the receptionist restriction flag enabled, therefore user 59a7011a05c677bda916619b cannot perform this action',
            $result['message']
        );
    }

    public function testSaveBatchAsImporter(): void
    {
        $_SERVER['HTTP_X_GLOFOX_SOURCE'] = 'IMPORTS';
        $_SERVER['HTTP_X_GLOFOX_API_TOKEN'] = 'INTEGRATOR-TEST-TOKEN';
        $_SERVER['HTTP_X_GLOFOX_BRANCH_ID'] = '49a7011a05c677b9a916612a';

        $userId = '59c92229c3fad88b481add12';

        $data = [
            [
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'model' => 'programs',
                'category_id' => null,
                'num_sessions' => 10,
                'model_ids' => null,
                'expiry' => null,
                'start_date' => (new \DateTime())->format('d/m/Y'),
                'bookings' => [],
                'user_id' => $userId,
                'end_date' => null,
            ],
            [
                '_id' => '59cbb21930c9433ae01c8c8b',
                'branch_id' => '49a7011a05c677b9a916612a',
                'model' => 'programs',
                'model_ids' => null,
                'category_id' => null,
                'user_id' => $userId,
                'num_sessions' => 10,
                'membership_id' => '54107c1cd7b6ddc3a98b4577',
                'start_date' => date('Y-m-d'),
                'end_date' => date('Y-m-d', strtotime('+1 month')),
            ],
        ];

        $options = [
            'method' => 'get',
            'data' => json_encode($data, JSON_THROW_ON_ERROR),
        ];

        $result = $this->testAction('/user_credits/saveBatch/' . $userId, $options);
        $result = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        self::assertTrue($result['success'], $result['message'] ?? '');
        self::assertNotEmpty($result['credit_packs']);

        $creditPacks = $this->creditsRepository
            ->addCriteria(new UserId($userId))
            ->find();

        self::assertCount(2, $creditPacks);

        self::assertNull($creditPacks[0]->source());
        self::assertSame(CreditPackSourceType::IMPORTED, $creditPacks[1]->source()->type()->getValue());
    }

    public function testGetAll(): void
    {
        $this->loginAsUser(
            $this->fetchUser('59a7011a05c677bda916612a')
        );

        $result = $this->testAction('/2.0/credits?user_id=59ba576ef09e52064e23414d', ['method' => 'get']);
        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        self::assertNotNull($result->data);
        self::assertTrue(is_countable($result->data) && count($result->data) > 0);
    }

    public function testGetAllWithEventId(): void
    {
        $this->loginAsUser(
            $this->fetchUser('59a7011a05c677bda916612a')
        );

        $result = $this->testAction(
            '/2.0/credits?user_id=59ba576ef09e52064e23414d&event_id=29b7012a05c677c9a512503d',
            ['method' => 'get']
        );
        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        self::assertNotNull($result->data);
        self::assertTrue(is_countable($result->data) && count($result->data) > 0);

        foreach ($result->data as $record) {
            self::assertEquals('programs', $record->model);
        }
    }

    public function testGetAllWithTimeSlotId(): void
    {
        $this->loginAsUser(
            $this->fetchUser('59a7011a05c677bda916612a')
        );

        $result = $this->testAction(
            '/2.0/credits?user_id=59ba576ef09e52064e23414d&timeslot_id=59832e82e1608a08210041b9',
            ['method' => 'get']
        );
        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        self::assertNotNull($result->data);
        self::assertTrue(is_countable($result->data) && count($result->data) > 0);

        foreach ($result->data as $record) {
            self::assertEquals('facilities', $record->model);
        }
    }

    public function testGetAllForAppointmentTimeSlot(): void
    {
        $user = $this->fetchUser('59a7011a05c677bda916612a');
        $this->loginAsUser($user);

        $result = $this->testAction(
            '/2.0/credits?user_id=63074e6944eb47f8fd61a035&timeslot_id=630733bd9194da65257aab18',
            ['method' => 'get']
        );
        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        $expectedCreditIds = ['63073bf0616e2685f5d82dbd'];

        $this->assertCount(count($expectedCreditIds), $result->data);

        foreach ($result->data as $userCredit) {
            $this->assertContains($userCredit->_id, $expectedCreditIds);
        }
    }

    public function testGetById(): void
    {
        $this->loginAsUser(
            $this->fetchUser('59a7011a05c677bda916612a')
        );

        $result = $this->testAction('/2.0/credits/59ad6c1225f149b102000005', ['method' => 'get']);
        $result = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        self::assertNotEmpty($result);
    }

    public function testNonAdminUserCannotAccessCreditsHealingEndpoint(): void
    {
        $this->authenticateAsMember('77c2e5a8b5bda44ec0c9c0e2');

        $result = $this->testAction(
            '/2.2/branches/49a7011a05c677b9a916612a/users/77c2e5a8b5bda44ec0c9c0e2/credits/align-with-membership',
            [
                'method' => 'post',
            ]
        );

        $result = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        self::assertFalse($result['success']);
        self::assertEquals('ACTION_IS_NOT_ALLOWED', $result['message_code']);
    }

    public function testHealingCreditsDatesHappyPath(): void
    {
        $exampleCurrentCycleCreditPackToHeal = '66a2c85c1ea4bbcf6af58478';
        $expectedCurrentCycleStartDate = '2025-01-31 00:00:00';
        $expectedCurrentCycleEndDate = '2025-02-27 23:59:59';

        $exampleNextCycleCreditPackToHeal = '66a2c85c1ea4bbcf6af58477';
        $expectedNextCycleStartDate = '2025-02-28 00:00:00';
        $expectedNextCycleEndDate = '2025-03-30 23:59:59';

        $hcMock = Mockery::mock(HoneycombTracker::class);
        $hcMock->shouldReceive('track')->andReturnSelf();

        app()->instance(HoneycombTracker::class, $hcMock);
        app()->instance(FeatureFlagInterface::class, $this->givenFeatureFlagsMock());

        $this->authenticateAsGlofoxStaff('654b69c8abca805a744a9de7');

        $result = $this->testAction(
            '/2.2/branches/49a7011a05c677b9a916612a/users/77c2e5a8b5bda44ec0c9c0e2/credits/align-with-membership?is-dry-run=false',
            [
                'method' => 'post',
            ]
        );

        $result = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        self::assertTrue($result['success'], $result['message'] ?? '');

        $this->assertCreditDatesAreAligned(
            $expectedCurrentCycleStartDate,
            $expectedCurrentCycleEndDate,
            $exampleCurrentCycleCreditPackToHeal
        );
        $this->assertCreditDatesAreAligned(
            $expectedNextCycleStartDate,
            $expectedNextCycleEndDate,
            $exampleNextCycleCreditPackToHeal
        );

        app()->forgetInstance(FeatureFlagInterface::class);
        app()->forgetInstance(HoneycombTracker::class);
    }

    private function givenFeatureFlagsMock(): FeatureFlagInterface
    {
        $flagger = Mockery::mock(FeatureFlagInterface::class);
        $flagger
            ->shouldReceive('withFlag')->with(Flag::RECEPTIONIST_RESTRICTIONS_REVENUE())
            ->andReturnSelf()
            ->getMock()
            ->shouldReceive('withFlag')->with(Flag::IS_NO_MONTH_OVERFLOW_ENABLED())
            ->andReturnSelf()
            ->getMock();

        $flagger->shouldReceive('hasByBranchId')->andReturnValues([true, true]);

        return $flagger;
    }

    public function tearDown()
    {
        parent::tearDown();
        Mockery::close();

        app()->forgetInstance(ReceptionistRestrictionRevenueGuard::class);
    }
    private function createIrisMockFeatureFlagger(Flag $flag): FlaggerInterface
    {
        $flagger = Mockery::mock(Flagger::class);
        $flagger
            ->shouldReceive('withFlag')
            ->with($flag)
            ->andReturnSelf();

        return $flagger;
    }

    private function assertCreditDatesAreAligned(
        string $expectedStartDate,
        string $expectedEndDate,
        string $creditPackId
    ): void {
        /** @var CreditPack $creditPack */
        $creditPack = $this->creditsRepository
            ->addCriteria(new Id($creditPackId))
            ->first();

        self::assertNotEmpty($creditPack);
        self::assertEquals($expectedStartDate, $creditPack->startDate()->toDateTimeString());
        self::assertEquals($expectedEndDate, $creditPack->expiryDate()->toDateTimeString());
    }
}
