<IfModule mod_rewrite.c>
    RewriteEngine On
    RewriteCond %{HTTP_HOST}   ^development-zappy\.rhcloud\.com [NC]
    RewriteRule ^img/(.*)$ http://s3-eu-west-1.amazonaws.com/glofox/development/$1 [L,R=301]

    RewriteCond %{HTTP_HOST}   ^staging-zappy\.rhcloud\.com [NC]
    RewriteRule ^img/(.*)$ http://s3-eu-west-1.amazonaws.com/glofox/staging/$1 [L,R=301]

    RewriteCond %{HTTP_HOST}   ^www\.glofoxlogin\.com [NC]
    RewriteRule ^img/(.*)$ https://s3-eu-west-1.amazonaws.com/glofox/platform/$1 [L,R=301]

    RewriteCond %{HTTP_HOST}   ^platform-zappy\.rhcloud\.com [NC]
    RewriteRule ^img/(.*)$ https://s3-eu-west-1.amazonaws.com/glofox/platform/$1 [L,R=301]


    RewriteCond %{HTTP_HOST}   ^dev\.glofox\.com [NC]
    RewriteRule ^img/(.*)$ http://s3-eu-west-1.amazonaws.com/glofox/development/$1 [L,R=301]

    RewriteCond %{HTTP_HOST}   ^demo\.glofox\.com [NC]
    RewriteRule ^img/(.*)$ http://s3-eu-west-1.amazonaws.com/glofox/staging/$1 [L,R=301]

    RewriteCond %{HTTP_HOST}   ^staging\.glofox\.com [NC]
    RewriteRule ^img/(.*)$ https://s3-eu-west-1.amazonaws.com/glofox/staging/$1 [L,R=301]

    RewriteCond %{HTTP_HOST}   ^app\.glofox\.com [NC]
    RewriteRule ^img/(.*)$ https://s3-eu-west-1.amazonaws.com/glofox/platform/$1 [L,R=301]


    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^ index.php [L]
</IfModule>