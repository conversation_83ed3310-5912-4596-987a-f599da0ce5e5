<?php

namespace Glofox\Payments\Providers\Gateway\Capabilities;

use Glofox\Domain\PaymentMethods\Models\PaymentMethod;
use Glofox\Domain\PaymentProviders\Models\PaymentProvider;
use Glofox\Http\Requests\CurrentRequestIdService;
use Glofox\Payments\Entities\Capability\Contracts\CapabilityHandlerContract;
use Glofox\Payments\Entities\Capability\Exceptions\GetCapabilityException;
use Glofox\Payments\Entities\Capability\Exceptions\SetCapabilityException;
use Glofox\Payments\Entities\Capability\Models\Capability;
use Glofox\Payments\Providers\Gateway\GRPC\GRPC;
use Glofox\Payments\Providers\Gateway\GRPC\Metadata;
use Glofox\Payments\Providers\Gateway\ProtoParsers\CapabilityParser;
use GRPC\Payments\AccountService\AccountCapability;
use GRPC\Payments\AccountService\GetCapabilityReq;
use GRPC\Payments\AccountService\SetCapabilityReq;

class Capabilities implements CapabilityHandlerContract
{
    use GRPC;

    private \Glofox\Domain\PaymentMethods\Models\PaymentMethod $paymentMethod;

    private \Glofox\Domain\PaymentProviders\Models\PaymentProvider $paymentProvider;

    /**
     * @var CapabilityParser
     */
    private $capabilityParser;

    /** @var Metadata */
    private $metadata;

    /**
     * Capabilities constructor.
     */
    public function __construct(PaymentMethod $paymentMethod, PaymentProvider $paymentProvider)
    {
        $this->paymentMethod = $paymentMethod;
        $this->paymentProvider = $paymentProvider;
        $this->capabilityParser = app()->make(CapabilityParser::class);
        $this->metadata = app()->make(Metadata::class);
    }

    /**
     * @throws GetCapabilityException
     */
    public function getCapability(?string $accountId, string $capability): Capability
    {
        $request = new GetCapabilityReq();
        $request->setAccountID((int) $accountId);
        $request->setCapability($capability);

        /** @var $reply AccountCapability */
        [$reply, $status] = $this->GRCPClient()->accountService->GetCapability($request, $this->metadata->get())->wait();

        if ($status->code != 0 || !($reply instanceof AccountCapability)) {
            throw new GetCapabilityException($this->getErrorFromStatus($status));
        }

        return $this->capabilityParser->parseFromProtoMessage($reply);
    }

    /**
     * @return Capability|mixed
     *
     * @throws SetCapabilityException
     */
    public function setCapability(Capability $capability): Capability
    {
        $request = new SetCapabilityReq();
        $request->setAccountID((int) $capability->getAccountId());
        $request->setCapability($capability->getCapability());
        $request->setEnabled($capability->isEnabled());

        /** @var $reply AccountCapability */
        [$reply, $status] = $this->GRCPClient()->accountService->SetCapability($request, $this->metadata->get())->wait();

        if ($status->code != 0 || !($reply instanceof AccountCapability)) {
            throw new SetCapabilityException($this->getErrorFromStatus($status));
        }

        return $this->capabilityParser->parseFromProtoMessage($reply);
    }
}
