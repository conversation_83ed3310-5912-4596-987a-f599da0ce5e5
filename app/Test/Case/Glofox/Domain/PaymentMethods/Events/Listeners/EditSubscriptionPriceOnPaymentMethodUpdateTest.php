<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\PaymentMethods\Events\Listeners;

use Glofox\Domain\Branches\Models\Branch;
use Glofox\Domain\PaymentMethods\Events\Listeners\EditSubscriptionPriceOnPaymentMethodUpdate;
use Glofox\Domain\PaymentMethods\Events\PaymentMethodWasCreated;
use Glofox\Domain\PaymentMethods\Models\PaymentMethod;
use Glofox\Domain\PaymentMethods\Models\Provider;
use Glofox\Domain\PaymentMethods\Type;

\App::import('Test/Case', 'GlofoxTestCase');

class EditSubscriptionPriceOnPaymentMethodUpdateTest extends \GlofoxTestCase
{
    public $fixtures = [];

    public function test_branch_is_updated_with_correct_feature(): void
    {
        $branch = \Mockery::mock(Branch::class);
        $branch
            ->shouldReceive('set')
            ->withArgs(fn (string $key, bool $value) => $value === true && $key === 'features.subscriptions.edit_price.enabled');
        $paymentMethod = Paymentmethod::make([
            'type_id' => Type::CARD,
            'provider' => Provider::make([
                'account_id' => '12345',
                'name' => 'STRIPE_CUSTOM_US'
            ]),
        ]);

        $branch->shouldReceive('toArray')
            ->once();
        $listener = new EditSubscriptionPriceOnPaymentMethodUpdate();
        $listener->handle(new PaymentMethodWasCreated($branch, $paymentMethod));
    }
}
