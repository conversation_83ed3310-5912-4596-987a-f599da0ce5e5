<?php

namespace Glofox\BatchPricing;

class Helpers
{
    // Asset to update fields mappers

    public static function prepareTrainerPriceUpdateFields(array $assets, array $record): array
    {
        $pattern = $record['_pattern'] ?? ['allowed_member_types' => []];

        $newPattern = array_reduce($assets, function (array $pattern, array $asset) {
            $allowedMemberTypes = $pattern['allowed_member_types'] ?? [];

            if ($asset['price_type'] === 'default_price') {
                return array_replace($pattern, ['default_price' => self::toChange($asset['new_price'])]);
            }

            $newAllowedMemberTypes = array_map(function (array $memberType) use ($asset) {
                if (self::isMatchingAsset($asset, $memberType)) {
                    return array_replace($memberType, ['price' => self::toChange($asset['new_price'])]);
                }

                return $memberType;
            }, $allowedMemberTypes);

            return array_replace($pattern, ['allowed_member_types' => $newAllowedMemberTypes]);
        }, $pattern);

        return ['_pattern' => $newPattern];
    }

    // This one is reused across timeslot patterns, programs and coursed because the model is the same.
    public static function prepareAllowedMemberTypeFields(array $assets, array $record): array
    {
        $allowedMemberTypes = $record['allowed_member_types'] ?? [];

        $newAllowedMemberTypes = array_reduce($assets, fn(array $allowedMemberTypes, array $asset) => array_map(function (array $memberType) use ($asset) {
            if (self::isMatchingAsset($asset, $memberType)) {
                return array_replace($memberType, ['price' => self::toChange($asset['new_price'])]);
            }

            return $memberType;
        }, $allowedMemberTypes), $allowedMemberTypes);

        return ['allowed_member_types' => $newAllowedMemberTypes];
    }

    public static function prepareMembershipPriceUpdateFields(array $assets, array $record): array
    {
        $plans = $record['plans'] ?? [];

        $newPlans = array_reduce($assets, fn(array $plans, array $asset) => array_map(function (array $plan) use ($asset) {
            if (isset($plan['upfront_fee']) && $asset['price_type'] === 'upfront_fee' && self::toInt($asset['price']) === self::toCents($plan['upfront_fee'])) {
                return array_replace($plan, ['upfront_fee' => self::toChange($asset['new_price'])]);
            }

            if (isset($plan['price']) && $asset['price_type'] === 'price' && self::toInt($asset['price']) === self::toCents($plan['price'])) {
                return array_replace($plan, ['price' => $asset['new_price']/100]);
            }

            return $plan;
        }, $plans), $plans);

        return ['plans' => $newPlans];
    }

    public static function prepareProductPriceUpdateFields(array $assets, array $record): array
    {
        $presentations = $record['presentations'] ?? [];

        $newPresentations = array_reduce($assets, fn(array $presentations, array $asset) => array_map(function (array $singlePresentation) use ($asset) {
            if (self::toCents($singlePresentation['retail_price']) === self::toInt($asset['price'])) {
                return array_replace($singlePresentation, ['retail_price' => self::toChange($asset['new_price'])]);
            }
            return $singlePresentation;
        }, $presentations), $presentations);

        return ['presentations' => $newPresentations];
    }

    // Price mappers

    /**
     * Maps the new prices from the tax breakdowns provided (calculated in
     * sales-tax), to the static assets.
     */
    public static function mapNewPrices(array $assets, array $taxBreakdowns, string $newTaxMode): array
    {
        $indexedTaxBreakedowns = self::indexTaxBreakdowns($taxBreakdowns);

        return array_map(fn(array $asset) => self::mapNewPrice($asset, $indexedTaxBreakedowns, $newTaxMode), $assets);
    }

    /**
     * Formats the assets upon saving to the database to a response.
     */
    public static function mapPatchResponse(array $result, array $assets): array
    {
        return array_map(function (array $asset) use ($result) {
            if ($result) {
                return Helpers::mapToResponse($asset);
            } else {
                $asset['result'] = 'error';
                $asset['error'] = 'unable to update record';
                return Helpers::mapToResponse($asset);
            }
        }, $assets);
    }

    /**
     * Finds a matching price breakdown from $indexedTaxBreakedowns and maps the new
     * price to the asset based on the provided tax mode.
     */
    public static function mapNewPrice(array $asset, array $indexedTaxBreakedowns, string $newTaxMode): array
    {
        $breakdowns = $indexedTaxBreakedowns[$asset['service_id']] ?? [];
        $breakdown = array_filter($breakdowns, fn(array $br) =>
            // product_price is the price provided to the tax calculator in the request.
            $br['product_price'] == $asset['price']);

        if (empty($breakdown)) {
            return array_replace($asset, ['result' => 'error', 'error' => 'no tax breakdown found']);
        } else {
            $breakdown = array_values($breakdown)[0];
        }

        $newPrice = $newTaxMode == 'inclusive' ? $breakdown['total_price'] : $breakdown['net_price'];

        return array_replace($asset, ['result' => 'success', 'new_price' => $newPrice]);
    }

    // Utilities

    /**
     * Indexes the tax breakdowns by 'service_id' creating a map of buckets, where
     * each bucket contains all breakdowns sharing 'service_id'.
     *
     * This function can be used along with mapNewPrice to map the right asset to
     * it's price breakdown.
     */
    public static function indexTaxBreakdowns(array $taxBreakdowns): array
    {
        return array_reduce($taxBreakdowns ?? [], function ($acc, $taxBreakdown) {
            $acc[$taxBreakdown['service_id']][] = $taxBreakdown;

            return $acc;
        }, []);
    }

    public static function indexAssetsByRecordId(array $assets): array
    {
        return array_reduce($assets ?? [], function ($acc, $asset) {
            $acc[$asset['mongo_record_id']]['assets'][] = $asset;
            $acc[$asset['mongo_record_id']]['service_type'] = $asset["service_type"];
            $acc[$asset['mongo_record_id']]['mongo_record_id'] = $asset["mongo_record_id"];

            return $acc;
        }, []);
    }

    /**
     * Flattens the input array treating associative arrays as if they were not
     * arrays, hence not flattening them.
     */
    public static function recursiveFlatten(array $array, array $flat = []): array
    {
        return array_reduce($array, function ($flat, $el) {
            if ($el == []) {
                return $flat;
            }

            if (self::isAssociative($el)) {
                return array_merge($flat, [$el]);
            }

            return self::recursiveFlatten($el, $flat);
        }, $flat);
    }

    /**
     * Checks if the input array is associative or sequential, returning true if
     * it's associative and false if it's sequential.
     */
    public static function isAssociative(array $array): bool
    {
        if ([] === $array) {
            return true;
        }

        $n = \count($array);
        for ($i = 0; $i < $n; ++$i) {
            if (!\array_key_exists($i, $array)) {
                return true;
            }
        }

        return false;
    }

    public static function isPriceValid($price): bool {
        return is_numeric($price) && $price >= 0 && strlen(strval(intval($price))) < 16;
    }

    public static function formatPrice($price) {
        return Helpers::isPriceValid($price) ? Helpers::toCents($price) : $price;
    }

    public static function separateGoodAndBadAssets(array $assets): array {
        $goodAssets = [];
        $badAssets = [];

        foreach($assets as $asset) {
            if (Helpers::isPriceValid($asset['price'])) {
                $goodAssets[] = $asset;
            } else {
                $asset = array_replace($asset, ['result' => 'error', 'error' => 'invalid price format']);
                $badAssets[] = $asset;
            }
        }

        return [$goodAssets, $badAssets];
    }

    public static function mapToResponse(array $asset): array
    {
        $updatedAsset = [
            'oldPrice' => $asset['price'],
            'id' => $asset['service_id'],
            'type' => $asset['service_type'],
            'result' => $asset['result'],
        ];

        if (isset($asset['new_price'])) {
            $updatedAsset['newPrice'] = $asset['new_price'];
        }

        if (isset($asset['error'])) {
            $updatedAsset['error'] = $asset['error'];
        }

        return $updatedAsset;
    }

    public static function toCents($price): int
    {
        return self::toInt($price*100);
    }

    private static function toInt($price): int
    {
        return intval(strval($price));
    }

    private static function toChange($price)
    {
        return $price/100;
    }

    private static function isMatchingAsset(array $asset, array $recordField): int
    {
        return $recordField['type'] === $asset['price_type'] && self::toCents($recordField['price']) == self::toInt($asset['price']);
    }
}
