post:
  tags:
    - ProgramSlots
  summary: Get slots for multiple bookings
  operationId: get-slots
  description:
    Use this call to retrieve the slots for multiple bookings in a selected date range. If the slot bookable
    parameter is false it will state the errors  in the errors array in each slot retrieved.
  security:
    - coreapi_auth: [ ]
  parameters:
    - schema:
        type: string
        pattern: '^[a-f\d]{24}$'
      name: programId
      in: path
      required: true
  requestBody:
    required: true
    content:
      application/json:
        schema:
          $ref: '../../requests/2.2/get_program_slot.yml'
  responses:
    '200':
      description: OK
      content:
        application/json:
          schema:
            $ref: '../../schemas/program_slots.yml'
    '400':
      description: Bad request
      content:
        application/json:
          schema:
            $ref: "../../schemas/error.yml"
