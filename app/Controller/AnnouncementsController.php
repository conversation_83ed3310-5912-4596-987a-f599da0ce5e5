<?php

use Glofox\Domain\Announcements\Errors\UnauthorizedAnnouncementsOperationException;
use Glofox\Domain\Announcements\Http\Requests\UpsertAnnouncementRequest;
use Glofox\Domain\Announcements\Http\Responses\UpsertAnnouncementResponse;
use Glofox\Domain\Announcements\Models\Announcement as AnnouncementModel;
use Glofox\Domain\Announcements\Validation\Validators\UpsertAnnouncementValidator;
use Glofox\Domain\Announcements\UseCase\UpsertAnnouncementParams;
use Glofox\Domain\Authentication\Auth;
use Illuminate\Http\JsonResponse;

App::uses('AppController', 'Controller');

class AnnouncementsController extends \Glofox\Domain\Announcements\Http\AnnouncementsController
{
    public $uses = [
        'Branch',
        'Announcement',
        'User',
    ];

    public $helpers = [
        'Text',
    ];

    public $components = [
        'S3',
        'Paginator',
        'Utility',
        'Notification',
        'RequestHandler',
        'JWT',
    ];

    /**
     * Dispatcher for Model.
     *
     * @param string $identifier
     *
     * @return string
     */
    public function dispatcher($identifier = null)
    {
        return parent::dispatch($identifier, $this->Announcement);
    }

    public function beforeFilter()
    {
        parent::beforeFilter();
        $this->feature_name = 'announcements';
        $this->image_default_name = 'default.png';
    }

    /*******************************************************************************************
     *                                API 2.0 New Dashboard
     ******************************************************************************************/

    /**
     * Get announcements by specifying all in branch, all in branch or by specific id.
     *
     * @internal   This convention is a little boring for clients, but helps us keep clean backend
     *
     * @example    https://www.glofoxlogin/view/{id}
     *
     * @param string $id Announcement id
     *
     * @return array List of announcements that match the entry params
     */
    public function view($id)
    {
        $user = $this->JWT->parseToken();

        $params = [
            'branch_id' => $user['branch_id'],
            'active' => true,
        ];

        if (isset($id)) {
            $params['_id'] = $id;
        }

        return response()->json($this->Announcement->find('all', ['conditions' => $params]));
    }

    public function upsert(
        UpsertAnnouncementRequest $request,
        UpsertAnnouncementValidator $validator
    ): JsonResponse
    {
        $author = Auth::user();
        $item = $request->data()->toArray();

        $params = new UpsertAnnouncementParams(
            $author,
            $item,
            $request->getHtmlContent(),
            $request->getVideoUrl()
        );

        $validator->validate($params);

        if (!isset($item['author'])) {
            $item['author'] = [
                'user_id' => $author->id(),
                'first_name' => $author->firstName(),
                'last_name' => $author->lastName(),
            ];
        }

        if (isset($item['is_scheduled']) && isset($item['start_date']) && isset($item['end_date']) && $item['is_scheduled']) {
            $item['scheduled'] = [
                'is_scheduled' => $item['is_scheduled'],
                'start_date' => $item['start_date'],
                'end_date' => $item['end_date'],
            ];
        } else {
            $item['is_scheduled'] = false;
            unset($item['start_date']);
            unset($item['end_date']);
        }

        if ('IMAGE' === $item['type'] && !isset($item['image_url'])) {
            $item['image_url'] = 'default.png';
        }

        if (empty($item['video_url'])) {
            $item['video_url'] = '';
        }

        $record = $this->Announcement->save($item);

        if (!$record) {
            throw new Exception($this->Announcement->get_latest_error());
        }

        return (new UpsertAnnouncementResponse(AnnouncementModel::make($record['Announcement'])))
            ->toJsonResponse();
    }

    /**
     * Delete announcement given its _id.
     *
     * @param string $id Announcement _id
     *
     * @return object {Announcement:object, success:boolean}
     * @throws UnauthorizedAnnouncementsOperationException
     * @example    https://www.glofoxlogin/programs/remove/{id}
     *
     * @internal   Physical delete
     *
     */
    public function remove($id)
    {
        $user = Auth::user();

        $allowed = [
            UserType::ADMIN,
            UserType::SUPERADMIN
        ];

        $this->JWT->validateOrFail($allowed, $user->namespace());

        $announcement = $this->Announcement->findById($id)['Announcement'];
        if ($announcement['branch_id'] !== $user->branchId()) {
            UnauthorizedAnnouncementsOperationException::dueToRecordNotBelongingToBranch();
        }

        $announcement['active'] = false;

        $result = $this->Announcement->save($announcement);

        $response = response()->json($result);

        return $response;
    }

    /*!
     * Search list with params and pagination
     * @param      [type]                   $start  [description]
     * @param      [type]                   $limit  [description]
     * @param      [type]                   $search [description]
     * @param      String                   $active 1 for true, 0 for false, parsed as a string from get
     * @return     [type]                           [description]
     */

    /**
     * @param int    $page
     * @param int    $limit
     * @param null   $search
     * @param string $active
     *
     * @return string
     */
    public function listview($page = 1, $limit = 20, $search = null, $active = '1')
    {
        $user = $this->JWT->parseToken();
        $active = '0' == $active ? false : true;
        $params = ['branch_id' => $user['branch_id'], 'active' => $active];
        $this->encodeSearchRegex($params, $search); //<- add to params built Regex for global search
        $page = (int) $page;
        $limit = (int) $limit;
        $result = $this->Announcement->find('all', ['conditions' => $params]);

        return response()->json($result);
    }

    /*!
     * Count total records in this collection
     * @return     [type]                   [description]
     */

    /**
     * @param string $active
     *
     * @return string
     */
    public function count($active = '1')
    {
        $user = $this->JWT->parseToken();
        $active = '0' == $active ? false : true;
        $params = ['branch_id' => $user['branch_id']];
        $result = $this->Announcement->find('count', ['conditions' => array_merge($params, ['active' => $active])]);

        return response()->json($result);
    }

    /****************************** REST WEBSERVICE ACTIONS  *******************************************/

    /**
     * index method.
     */
    public function index()
    {
        $params = [
            'fields' => ['name'],
            'order' => ['_id' => -1],
            'limit' => 1000,
            'page' => 1,
        ];
        $results = $this->Announcement->find('all', $params);

        return response()->json($results);
    }

    /**
     * Get all announcements for a given branch /branch/<branch_id>/announcements/<start>/<end>/<filter>.
     *
     * @param type $id
     * @param null $start
     * @param null $end
     *
     * @return string
     */
    public function findByBranchId($id = null, $start = null, $end = null)
    {
        if (empty($id)) {
            return response()->json(['success' => false, 'message' => __('Empty Branch Id')]);
        }

        // Check if start date is valid
        $query_dates = false;
        if (!empty($start)) {
            $validate_start_date = $this->Utility->validate_date_format($start);
            if (!$validate_start_date['success']) {
                return response()->json(['success' => false, 'message' => $validate_start_date['message']]);
            }
            $query_dates = true;
            $date_condition = ['$gte' => new MongoDate(strtotime($start))];
        }
        if (!empty($end)) {
            $validate_end_date = $this->Utility->validate_date_format($end);
            if (!$validate_end_date['success']) {
                return response()->json(['success' => false, 'message' => $validate_end_date['message']]);
            }
            $date_condition = ['$gte' => new MongoDate(strtotime($start)), '$lte' => new MongoDate(strtotime($end))];
        }

        $params = [
            'conditions' => ['branch_id' => $id, 'active' => true],
            'order' => ['_id' => -1],
            'limit' => 1000,
            'page' => 1,
        ];
        if ($query_dates && $date_condition) {
            $params['conditions']['created'] = $date_condition;
        }
        $results = $this->Announcement->find('all', $params);
        $this->response->type('json');
        $this->autoRender = false;

        return response()->json($results);
    }

    /**
     * Get all announcements for a given branch user/<user_id>/branch/<branch_id>/announcements/<start>/<end>/<filter>.
     *
     * @param null $branch_id
     * @param null $user_id
     * @param null $start
     * @param null $end
     *
     * @return string
     *
     * @internal param type $id
     */
    public function findByBranchIdAndAuthor($branch_id = null, $user_id = null, $start = null, $end = null)
    {
        if (empty($user_id)) {
            return response()->json(['success' => false, 'message' => __('Empty User Id')]);
        }
        if (empty($branch_id)) {
            return response()->json(['success' => false, 'message' => __('Empty Branch Id')]);
        }

        // Check if start date is valid
        $query_dates = false;
        if (!empty($start)) {
            $validate_start_date = $this->Utility->validate_date_format($start);
            if (!$validate_start_date['success']) {
                return response()->json(['success' => false, 'message' => $validate_start_date['message']]);
            }
            $query_dates = true;
            $date_condition = ['$gte' => new MongoDate(strtotime($start))];
        }
        if (!empty($end)) {
            $validate_end_date = $this->Utility->validate_date_format($end);
            if (!$validate_end_date['success']) {
                return response()->json(['success' => false, 'message' => $validate_end_date['message']]);
            }
            $date_condition = ['$gte' => new MongoDate(strtotime($start)), '$lte' => new MongoDate(strtotime($this->Announcement->getEndOfDay($end)))];
        }

        $params = [
            'conditions' => ['author.user_id' => $user_id, 'branch_id' => $branch_id, 'active' => true],
            'order' => ['_id' => -1],
            'limit' => 1000,
            'page' => 1,
        ];

        if ($query_dates && $date_condition) {
            $params['conditions']['created'] = $date_condition;
        }
        $results = $this->Announcement->find('all', $params);

        return response()->json($results);
    }

    /**
     * Find one by announcement id and branch id /branch/<branch_id>/announcement/<announcement_id>.
     *
     * @param type $id
     * @param type $id_an
     *
     * @return type
     */
    public function findByBranchIdAndAnnouncementId($id = null, $id_an = null)
    {
        $params = [
            'conditions' => ['_id' => $id_an, 'branch_id' => $id, 'active' => true],
            'order' => ['_id' => -1],
            'limit' => 1,
            'page' => 1,
        ];
        $results = $this->Announcement->find('all', $params);

        return response()->json($results);
    }
}
