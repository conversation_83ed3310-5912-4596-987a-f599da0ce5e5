type: object
properties:
  invoice_id:
    type: string
  user_id:
    type: string
    pattern: '^[0-9a-fA-F]{24}$'
  earliest_date:
    type: string
    format: date-time
  event_identifier:
    type: string
    enum:
      - CARD_ADDED
      - CARD_REMOVED
      - CARD_REPLACED
      - MA<PERSON>ATE_ADDED
      - MANDATE_REMOVED
      - MANDATE_REPLACED
      - MANDATE_STATUS_CHANGED
      - ENTITY_BOOKED
      - PRODUCT_PURCHASED
      - NON_SUB_MEMBERSHIP_PURCHASED
      - MEMBERSHIP_PRORATED
      - MEMBERSHIP_REMOVED
      - CUSTOM_CHARGE_EXECUTED
      - SUBSCRIPTION_PURCHASED
      - SUBSCRIPTION_CYCLE_PAID
      - SUBSCRIPTION_CYCLE_PAYMENT_FAILED
      - SUBSCRIPTION_PAUSED
      - SUBSCRIPTION_CANCELLED
      - HISTORIC_TRANSACTION_IMPORTED
      - NSF_FEE
      - EXTERNAL_ACCOUNT_UPDATED
  transactions:
    type: array
    description: Returned only if group_by_invoice is true
    items:
      $ref: "./activity.yml"
required:
  - user_id
  - earliest_date
  - event_identifier
  - transactions
