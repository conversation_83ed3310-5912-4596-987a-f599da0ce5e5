<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\Leads\Events\Listeners;

use Glofox\Domain\Leads\Enums\LeadSchemaFields;
use Glofox\Domain\Leads\Events\LeadWasUpdated;
use Glofox\Domain\Leads\Events\Listeners\UpdateLeadStatus;
use Glofox\Domain\Leads\Status;
use Glofox\Domain\Users\Repositories\UsersRepository;
use MongoDB\BSON\UTCDateTime;

\App::import('Test/Case', 'GlofoxTestCase');

class UpdateLeadStatusTest extends \GlofoxTestCase
{

    public $fixtures = [
        'app.user',
    ];

    /**
     * @dataProvider leadStatusDataProvider
     */
    public function testUpdateLeadStatus($previousStatus, $newStatus, $expectedLeads): void
    {
        $id = '59a7011a05c677bda916624c';
        $usersRepository = app()->make(UsersRepository::class);
        $usersRepository->updateOneField( $id,'lead_status',$newStatus);
        $userPrevious = $usersRepository->getById($id);

        $event = new LeadWasUpdated($userPrevious, $previousStatus);
        (new UpdateLeadStatus())->handle($event);

        $user = $usersRepository->getById($id);

        $leadsObject = $user['leads'];
        $this->assertEquals($newStatus, $user['lead_status']);
        $this->assertNotNull($leadsObject);
        $this->assertEquals($expectedLeads[LeadSchemaFields::STATUS], $leadsObject[LeadSchemaFields::STATUS]);
        $this->assertNotNull($leadsObject[LeadSchemaFields::STATUS_MODIFIED]);
    }

    public function leadStatusDataProvider(): array
    {
        return [
            'When status changes from null to COLD, then it should  update leads object' => [
                'previousStatus' => null,
                'newStatus' => 'COLD',
                'expectedLeads' => [
                    LeadSchemaFields::STATUS_MODIFIED => new UTCDateTime(),
                    LeadSchemaFields::STATUS => Status::COLD,
                ],
            ],
            'When status changes from LEAD to COLD, then it should update leads object' => [
                'initialStatus' => 'LEAD',
                'newStatus' => 'COLD',
                'expectedLeads' => [
                    LeadSchemaFields::STATUS_MODIFIED => new UTCDateTime(),
                    LeadSchemaFields::STATUS => Status::COLD,
                ],
            ],
        ];
    }

}
