<?php

namespace CakeTestCases\Glofox\Domain\Authentication\Token;

use Glofox\Authentication\Jwt;
use Glofox\Domain\Authentication\Token\Parser;
use Glofox\Domain\Authentication\Token\TokenUpdater;
use Glofox\Domain\Authentication\TokenGenerator;
use Glofox\Infrastructure\Honeycomb\HoneycombTracker;

\App::import('Test/Case', 'GlofoxTestCase');

class TokenUpdaterTest extends \GlofoxTestCase
{
    use TokenGeneration;
    private string $newTokenExample = 'eyJhbGciOiJIUzI1NiJ9.eyJhdWQiOiJfIiwiZXhwIjoxNjE0MDA5MDEwLCJpYXQiOjE2MTEzMzA2MTAsImlzcyI6Il8iLCJ1c2VyIjp7Il9pZCI6IjVhZjQ2NzNjYTFiMDQ2MDIzODM5MGZlMiIsIm5hbWVzcGFjZSI6Imdsb2ZveCIsImJyYW5jaF9pZCI6IjUzZmIwYTIyZmUyNWYwYzg0YzhiNDU3NSIsImZpcnN0X25hbWUiOiJNYXJpdXMiLCJsYXN0X25hbWUiOiJUZW9kb3Jlc2N1IiwidHlwZSI6IkFETUlOIiwiaXNTdXBlckFkbWluIjpmYWxzZX19.B79QuijIOYoYheNh8apwI28_N80iPOZXisJqTYaD0I0';
    private string $oldTokenExample = 'eyJhbGciOiJIUzI1NiJ9.eyJhdWQiOiJfIiwiZXhwIjoxNjE0MDA5MDEwLCJpYXQiOjE2MTEzMzA2MTAsImlzcyI6Il8iLCJ1c2VyIjp7Il9pZCI6IjVhZjQ2NzNjYTFiMDQ2MDIzODM5MGZlMiIsIm5hbWVzcGFjZSI6Imdsb2ZveCIsImJyYW5jaF9pZCI6IjUzZmIwYTIyZmUyNWYwYzg0YzhiNDU3NSIsImZpcnN0X25hbWUiOiJNYXJpdXMiLCJsYXN0X25hbWUiOiJUZW9kb3Jlc2N1IiwidHlwZSI6IkFETUlOIiwiaXNTdXBlckFkbWluIjpmYWxzZX19.5BHSdgezcus_HbY18-Gku-6TqVQjvQMkw-xFatc8IoM';

    public function test_it_returns_null_for_up_to_date_token()
    {
        $parser = \Mockery::mock(Parser::class);
        $parser->shouldReceive('parse')
            ->andReturn($this->newTokenExample);

        $this->assertNull($this->tokenUpdater($parser)->updateIfOld());
    }

    public function test_it_returns_new_token_for_old_token()
    {
        $parser = \Mockery::mock(Parser::class);
        $parser->shouldReceive('parse')
            ->andReturn($this->oldTokenExample);

        $newToken = $this->tokenUpdater($parser)->updateIfOld();
        $this->assertNotNull($newToken);
    }

    private function tokenUpdater(Parser $parser): TokenUpdater
    {
        $jwt = app()->make(Jwt::class);

        $honeycombTracker = \Mockery::mock(HoneycombTracker::class);
        $honeycombTracker->shouldReceive('track');
        app()->instance(HoneycombTracker::class, $honeycombTracker);

        $tokenGenerator = \Mockery::mock(TokenGenerator::class);
        $tokenGenerator->shouldReceive('generate')
            ->andReturn($this->newTokenExample);

        return new TokenUpdater($jwt, $parser, $tokenGenerator,$honeycombTracker, 'old_salt');
    }
}
