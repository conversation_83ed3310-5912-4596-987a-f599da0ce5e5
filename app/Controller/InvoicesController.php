<?php

use Dompdf\Dompdf;
use Dompdf\Options;
use Glofox\CdnProvider;
use Glofox\Datasource\Exceptions\InvalidMongoIdException;
use Glofox\Domain\Authentication\Auth;
use Glofox\Domain\Branches\Repositories\BranchesRepository;
use Glofox\Domain\Invoices\Requests\InvoiceListRequest;
use Glofox\Domain\Invoices\Requests\ViewInvoiceRequest;
use Glofox\Domain\PaymentMethods\Type;
use Glofox\Infrastructure\Flags\Flagger;
use Glofox\Payments\Contracts\PaymentProviderContract;
use Glofox\Response\Transformers\Invoices\InvoicesTransformer;
use Illuminate\Http\JsonResponse;

\App::uses('AppController', 'Controller');

/**
 * Class InvoicesController.
 *
 * @property JWTComponent JWT
 */
class InvoicesController extends \AppController
{
    /**
     * @var string
     */
    public $name = 'Invoices';

    /**
     * @var array
     */
    public $components = ['JWT'];

    private PaymentProviderContract $paymentHandler;
    private BranchesRepository $branchesRepository;
    private InvoicesTransformer $invoiceTransformer;

    public function __construct($request = null, $response = null)
    {
        parent::__construct($request, $response);

        $this->paymentHandler = Auth::payments(Type::CARD);
        $this->branchesRepository = new BranchesRepository();
        $this->invoiceTransformer = new InvoicesTransformer();
        $this->flagger = app()->make(Flagger::class);
    }

    /**
     * @param InvoiceListRequest $request
     *
     * @return JsonResponse
     *
     * @throws UnsuccessfulOperation
     */
    public function list(InvoiceListRequest $request)
    {
        $user = $this->getUser();
        $branch = $this->branchesRepository->findById($user['branch_id']);

        if (empty($branch)) {
            throw new UnsuccessfulOperation('Invalid branch information');
        }

        $invoices = $this->paymentHandler->invoices()->getInvoicesList();

        $invoices = $this->invoiceTransformer->setFromPaymentProviderCollection($invoices);

        return response()->json((array) $invoices);
    }

    /**
     * @throws InvalidMongoIdException
     */
    public function view(
        ViewInvoiceRequest $request,
        \Branch $branchCakeModel
    ) {

        $branch = Auth::user()->branch();

        $year = $request->getYear();
        $month = $request->getMonth();


        /** @var Glofox\Payments\Entities\Invoice\Models\Invoice $invoice */
        $invoice = $this->paymentHandler->invoices()->getMonthlyInvoice($year, $month);

        if (!$invoice) {
            throw new NotFoundException(
                sprintf('Invoice for %d/%d not found', $month, $year)
            );
        }

        if ('application/pdf' !== $request->headers->get('Accept')) {
            $response = $this->invoiceTransformer->setFromPaymentProvider($invoice);

            return response()->json($response);
        }

        // Return the invoice HTML page
        $templateData = new stdClass();
        $templateData->invoice = $invoice;
        $templateData->branch = $branch;
        $templateData->logo = sprintf(
            '%s/%s/glofox/glofox-logo-horizontal.png',
            CdnProvider::getUrl(),
            GlofoxEnvironment::currentEnvironment()
        );

        $this->set('data', $templateData);

        $content = $this->render('download_invoice');
        header('Content-Type: text/html; charset=UTF-8');

        $options = new Options();
        $options->set('isHtml5ParserEnabled', true);
        $options->set('isFontSubsettingEnabled', true);
        $options->set('defaultFont', 'DejaVu Sans');

        $pdf = app()->make(Dompdf::class);
        $pdf->loadHtml($content);
        $pdf->render();
        $pdf->stream('invoice.pdf', ['Attachment' => 1]);

        return;
    }
}
