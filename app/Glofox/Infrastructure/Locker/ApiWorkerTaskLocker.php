<?php

declare(strict_types=1);

namespace Glofox\Infrastructure\Locker;

use Carbon\Carbon;
use Glofox\Domain\Cache\CacheClientInterface;
use Glofox\Domain\Locker\ApiWorkerTaskLockerInterface;
use Glofox\Eventkit\Task\TaskInterface;
use Psr\Log\LoggerInterface;

class ApiWorkerTaskLocker implements ApiWorkerTaskLockerInterface
{
    private \Glofox\Domain\Cache\CacheClientInterface $cache;

    private \Psr\Log\LoggerInterface $logger;

    private ?string $messageId = null;

    public function __construct(
        LoggerInterface $logger,
        CacheClientInterface $cache
    ) {
        $this->logger = $logger;
        $this->cache = $cache;
    }

    public function lock(TaskInterface $task): bool
    {
        $identifier = $this->identifier($task);
        $this->logger->info(sprintf('Attempting to lock Task %s', $identifier));

        $added = $this->cache->setIfNotExist($identifier, $identifier);
        if (!$added) {
            $this->logger->error(sprintf('Lock already exists for Task %s', $identifier));

            return false;
        }

        $expiresAt = Carbon::now()->addMinute();
        $this->cache->expiresAt($identifier, $expiresAt);

        $this->logger->info(
            sprintf('Lock successfully created for Task %s', $identifier),
            [
                'expiresAt' => $expiresAt->toISOString(),
            ]
        );

        return true;
    }

    public function unlock(TaskInterface $task): void
    {
        $identifier = $this->identifier($task);
        $this->cache->delete($identifier);

        $this->logger->info(
            sprintf('Lock deleted for Task %s', $identifier)
        );
    }

    public function setMessageId(string $messageId): void
    {
        $this->messageId = $messageId;
    }

    private function identifier(TaskInterface $task): string
    {
        return sprintf(
            '%s__%s',
            $this->messageId,
            \get_class($task)
        );
    }
}
