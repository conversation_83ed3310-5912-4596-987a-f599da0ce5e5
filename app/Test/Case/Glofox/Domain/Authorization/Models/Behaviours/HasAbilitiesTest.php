<?php

namespace CakeTestCases\Glofox\Domain\Authorization\Models\Behaviours;

use CakeTestCases\Glofox\Domain\Authentication\Token\TokenGeneration;
use Glofox\Domain\Authorization\Abilities\BranchAbility;
use Glofox\Domain\Users\Models\User;
use Glofox\Domain\Users\Repositories\UsersRepository;
use Glofox\Repositories\Search\Expressions\Shared\Id;

\App::import('Test/Case', 'GlofoxTestCase');

class HasAbilitiesTest extends \GlofoxTestCase
{
    use TokenGeneration;

    public $fixtures = [
        'app.user',
        'app.role',
    ];

    /** @test */
    public function it_adds_the_can_method_to_the_user_model(): void
    {
        $user = $this->fetchUser('5c7828a4d510f9635ad4a6b1');

        $this->assertTrue($user->can(BranchAbility::UPDATE));
    }

    /** @test */
    public function it_returns_all_users_abilities(): void
    {
        $user = $this->fetchUser('5c7828a4d510f9635ad4a6b1');

        $this->assertArrayContainsArray([
            BranchAbility::LIST_BY_NAMESPACE,
            BranchAbility::UPDATE,
            BranchAbility::LIST,
        ], $user->getAllAbilities());
    }

    private function fetchUser(string $identifier): User
    {
        /** @var UsersRepository $repository */
        $repository = app()->make(UsersRepository::class);

        return $repository
            ->addCriteria(new Id($identifier))
            ->firstOrFail();
    }

    /** @test */
    public function it_also_adds_a_cannot_method_to_the_user_model(): void
    {
        $user = $this->fetchUser('5c7828a4d510f9635ad4a6b1');

        $this->assertFalse($user->cannot(BranchAbility::UPDATE));
    }
}
