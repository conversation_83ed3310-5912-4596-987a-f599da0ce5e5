post:
  tags:
    - UserCards
  summary: ''
  operationId: upsert-user-cards
  description: This endpoint upsert new card for a user
  x-internal: true
  security:
    - coreapi_auth: [ ]
  parameters:
    - schema:
        type: string
      name: branchId
      in: path
      required: true
    - schema:
        type: string
      name: userId
      in: path
      required: true
  requestBody:
    required: true
    content:
      application/json:
        schema:
          type: object
          properties:
            token:
              type: string
            options:
              type: array
              items:
                type: string
          required:
            - token
  responses:
    '200':
      description: OK
      content:
        application/json:
          schema:
            type: object
            properties:
              data:
                type: array
                items:
                  $ref: "../../schemas/card.yml"
    '400':
      description: Bad request
      content:
        application/json:
          schema:
            $ref: "../../schemas/error.yml"
