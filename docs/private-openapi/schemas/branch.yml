type: object
properties:
  _id:
    type: string
  categories:
    type: array
    items:
      type: string
  name:
    type: string
  language:
    type: string
  address:
    type: object
    properties:
      city:
        type: string
      continent:
        type: string
      country:
        type: string
      country_code:
        type: string
      currency:
        type: string
      district:
        type: string
      latitude:
        type: number
      longitude:
        type: number
      state:
        type: string
      street:
        type: string
      timezone_id:
        type: string
      timezone_name:
        type: string
      location:
        type: object
        properties:
          lat:
            type: number
          lng:
            type: number
  subscription:
    type: object
    properties:
      current_period_end:
        type: string
        format: date-time
      id:
        type: string
  namespace:
    type: string
  phone:
    type: string
  uses_iframe:
    type: boolean
  active:
    type: boolean
  features:
    type: object
    description: List of available features
  push_config:
    type: object
    description: List of push configurations
  tutorial:
    type: object
    description: Tutorial status
  configuration:
    type: object
    description: List of specific configurations
  modified:
    type: string
    format: date-time
  opening_times:
    type: array
    items:
      type: object
      properties:
        dow:
          type: string
        start:
          type: string
        end:
          type: string
        is_open:
          type: boolean
  questions:
    type: array
    items:
      type: string