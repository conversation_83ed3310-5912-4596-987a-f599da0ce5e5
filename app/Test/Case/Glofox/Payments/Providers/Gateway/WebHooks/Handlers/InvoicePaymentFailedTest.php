<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Payments\Providers\Gateway\WebHooks\Handlers;

use Carbon\Carbon;
use Glofox\Domain\ABTestClients\Repositories\ABTestClientsRepository;
use Glofox\Domain\Charges\Events\ChargeWasUpdated;
use Glofox\Domain\Charges\Repositories\ChargesRepository;
use Glofox\Domain\FeatureFlags\FeatureFlagInterface;
use Glofox\Domain\Memberships\Services\MembershipsClient;
use Glofox\Domain\PaymentMethods\Models\PaymentMethod;
use Glofox\Domain\Users\Models\User;
use Glofox\Domain\Users\Repositories\UsersRepository;
use Glofox\Domain\Users\Search\Expressions\MembershipSubscriptionStripeId;
use Glofox\Domain\Users\Services\Membership\UserMembershipLocker;
use Glofox\Events\EventManager;
use Glofox\Infrastructure\Flags\Flag;
use Glofox\Payments\Contracts\PaymentProviderContract;
use Glofox\Payments\Entities\Invoice\Contracts\InvoiceHandlerContract;
use Glofox\Payments\Entities\Invoice\Models\InvoiceEntity;
use Glofox\Payments\Entities\Invoice\Models\InvoiceStatus;
use Glofox\Payments\Entities\Subscription\Contracts\SubscriptionHandlerContract;
use Glofox\Payments\Entities\Subscription\Events\SubscriptionCyclePaymentHasFailed;
use Glofox\Payments\Entities\Subscription\Models\Subscription;
use Glofox\Payments\Entities\Transaction\Contracts\ChargeHandlerContract;
use Glofox\Payments\Entities\Transaction\Models\Transaction;
use Glofox\Payments\Providers\Gateway\WebHooks\Handlers\InvoicePaymentFailed;
use Mockery;

\App::import('Test/Case', 'GlofoxTestCase');

class InvoicePaymentFailedTest extends \GlofoxTestCase
{
    public $fixtures = [
        'app.branch',
        'app.user',
        'app.membership',
        'app.stripe_charge',
    ];
    private UsersRepository $usersRepository;

    public function setUp()
    {
        parent::setUp();

        app()->forgetInstance(ChargesRepository::class);

        $userMembershipLocker = \Mockery::mock(UserMembershipLocker::class);
        $userMembershipLocker->shouldReceive('lock');

        app()->instance(UserMembershipLocker::class, $userMembershipLocker);

        $membershipClientMock = \Mockery::mock(MembershipsClient::class)
            ->shouldReceive('post')
            ->andReturn([])
            ->getMock();

        app()->instance(MembershipsClient::class, $membershipClientMock);

        $this->usersRepository = app()->make(UsersRepository::class);
        app()->forgetInstance(ABTestClientsRepository::class);
    }

    public function tearDown()
    {
        \Mockery::close();

        app()->forgetInstance(EventManager::class);
        app()->forgetInstance(UserMembershipLocker::class);
        app()->forgetInstance(MembershipsClient::class);
        app()->forgetInstance(FeatureFlagInterface::class);

        parent::tearDown();
    }

    public function test_handle_by_intent_id(): void
    {
        $invoiceId = (string) new \MongoId();
        $subscription = new Subscription();
        $subscription->setId('sub_00X');

        $invoice = new InvoiceEntity();
        $invoice->setId($invoiceId);
        $invoice->setStatus(InvoiceStatus::PENDING());

        $charge = new Transaction();
        $charge->setStatus('PENDING-INTENT')
            ->setAmount(100)
            ->setCurrency('CAD')
            ->setCustomerAccountId('5d6826348d8540851fe60662')
            ->setCreatedAt(1)
            ->setUpdatedAt(1)
            ->setServiceProviderIntentId('intent_123')
            ->setServiceProviderResponse(
                json_encode([
                    'client_secret' => 'secret_123',
                ], JSON_THROW_ON_ERROR)
            )
            ->setPaymentMethod('CARD')
            ->setInvoiceId($invoiceId);

        $paymentMethod = PaymentMethod::make([
            'branch_id' => '49a7011a05c677b9a916612a',
            'type_id' => 'CARD',
        ]);

        $chargesMock = \Mockery::mock(ChargeHandlerContract::class)
            ->allows(['getById' => $charge, 'getRefundsByChargeId' => []]);

        /** @var PaymentProviderContract $paymentHandler */
        $paymentHandler = \Mockery::mock(PaymentProviderContract::class)
            ->allows([
                'subscriptions' => \Mockery::mock(SubscriptionHandlerContract::class)
                    ->allows(['getById' => $subscription]),
                'invoices' => \Mockery::mock(InvoiceHandlerContract::class)
                    ->allows(['get' => $invoice]),
                'charges' => $chargesMock,
                'paymentMethod' => $paymentMethod,
            ]);

        $eventPayload = collect([
            'id' => 'evt_123',
            'psp_response' => '{"type":"invoice.payment_failed","subscription_id":"sub_00X","invoice":{"id":"' . $invoiceId . '","latest_transaction_group":{"id":"trx-group-1","transactions":[{"id":"trx-1"}]}}}',
        ]);

        $eventManager = \Mockery::mock(EventManager::class);
        $eventManager
            ->shouldReceive('emit')
            ->withArgs(function (string $listener, array $params) {
                $this->assertSame(SubscriptionCyclePaymentHasFailed::class, $listener);

                return true;
            })
            ->once()
            ->shouldReceive('emit')
            ->withArgs(function (string $listener, array $params) {
                $this->assertSame(ChargeWasUpdated::class, $listener);

                return true;
            })
            ->once();
        app()->instance(EventManager::class, $eventManager);

        $featureFlagMock = $this->givenFeatureFlagIsRestrictedRenewalThroughMembershipsEnabledMock('49a7011a05c677b9a916612a', false);
        app()->instance(FeatureFlagInterface::class, $featureFlagMock);

        $handler = app()->makeWith(InvoicePaymentFailed::class, compact('eventPayload', 'paymentHandler'));
        $status = $handler->handle();

        self::assertEquals(200, $status);

        app()->forgetInstance(EventManager::class);
        app()->forgetInstance(FeatureFlagInterface::class);
    }

    public function test_handle_by_charge_id(): void
    {
        $invoiceId = (string) new \MongoId();
        $subscription = new Subscription();
        $subscription->setId('sub_00X');

        $invoice = new InvoiceEntity();
        $invoice->setId($invoiceId);
        $invoice->setStatus(InvoiceStatus::PENDING());

        $charge = new Transaction();
        $charge->setStatus('PENDING-INTENT')
            ->setAmount(100)
            ->setCurrency('CAD')
            ->setCustomerAccountId('5d6826348d8540851fe60662')
            ->setCreatedAt(1)
            ->setUpdatedAt(1)
            ->setServiceProviderIntentId('intent_123')
            ->setServiceProviderResponse(
                json_encode([
                    'client_secret' => 'secret_123',
                ], JSON_THROW_ON_ERROR)
            )
            ->setPaymentMethod('CARD')
            ->setInvoiceId($invoiceId);

        $paymentMethod = PaymentMethod::make([
            'branch_id' => '49a7011a05c677b9a916612a',
            'type_id' => 'CARD',
        ]);

        $chargesMock = \Mockery::mock(ChargeHandlerContract::class)
            ->allows(['getById' => $charge, 'getRefundsByChargeId' => []]);
        $chargesMock->shouldReceive('getById')
            ->andThrow(new \Exception('payment intent not found'));

        /** @var PaymentProviderContract $paymentHandler */
        $paymentHandler = \Mockery::mock(PaymentProviderContract::class)
            ->allows([
                'subscriptions' => \Mockery::mock(SubscriptionHandlerContract::class)
                    ->allows(['getById' => $subscription]),
                'invoices' => \Mockery::mock(InvoiceHandlerContract::class)
                    ->allows(['get' => $invoice]),
                'charges' => $chargesMock,
                'paymentMethod' => $paymentMethod,
            ]);

        $eventManager = \Mockery::mock(EventManager::class);
        $eventManager
            ->shouldReceive('emit')
            ->withArgs(function (string $listener, array $params) {
                $this->assertSame(SubscriptionCyclePaymentHasFailed::class, $listener);

                return true;
            })
            ->once()
            ->shouldReceive('emit')
            ->withArgs(function (string $listener, array $params) {
                $this->assertSame(ChargeWasUpdated::class, $listener);

                return true;
            })
            ->once();
        app()->instance(EventManager::class, $eventManager);

        $eventPayload = collect([
            'id' => 'evt_123',
            'psp_response' => '{"type":"invoice.payment_failed","subscription_id":"sub_00X","invoice":{"id":"' . $invoiceId . '","latest_transaction_group":{"id":"trx-group-1","transactions":[{"id":"trx-1"}]}}}',
        ]);

        $featureFlagMock = $this->givenFeatureFlagIsRestrictedRenewalThroughMembershipsEnabledMock('49a7011a05c677b9a916612a', false);
        app()->instance(FeatureFlagInterface::class, $featureFlagMock);

        $handler = app()->makeWith(InvoicePaymentFailed::class, compact('eventPayload', 'paymentHandler'));
        $status = $handler->handle();

        self::assertEquals(200, $status);

        app()->forgetInstance(EventManager::class);
        app()->forgetInstance(FeatureFlagInterface::class);
    }

    public function test_it_should_shift_subscription_to_next_cycle_when_retry_style_is_parallel(): void
    {
        $invoiceId = (string) new \MongoId();
        $subscription = new Subscription();
        $subscription->setId('sub_00X');

        $invoice = new InvoiceEntity();
        $invoice->setId($invoiceId);
        $invoice->setStatus(InvoiceStatus::PAST_DUE());

        $charge = new Transaction();
        $charge->setStatus('PENDING-INTENT')
            ->setAmount(100)
            ->setCurrency('CAD')
            ->setCustomerAccountId('5d6826348d8540851fe60662')
            ->setCreatedAt(1)
            ->setUpdatedAt(1)
            ->setServiceProviderIntentId('intent_123')
            ->setServiceProviderResponse(
                json_encode([
                    'client_secret' => 'secret_123',
                ], JSON_THROW_ON_ERROR)
            )
            ->setPaymentMethod('CARD')
            ->setInvoiceId($invoiceId);

        $paymentMethod = PaymentMethod::make([
            'branch_id' => '49a7011a05c677b9a916612a',
            'type_id' => 'CARD',
        ]);

        $chargesMock = \Mockery::mock(ChargeHandlerContract::class)
            ->allows(['getById' => $charge, 'getRefundsByChargeId' => []]);
        $chargesMock->shouldReceive('getById')
            ->andThrow(new \Exception('payment intent not found'));

        /** @var PaymentProviderContract $paymentHandler */
        $paymentHandler = \Mockery::mock(PaymentProviderContract::class)
            ->allows([
                'subscriptions' => \Mockery::mock(SubscriptionHandlerContract::class)
                    ->allows(['getById' => $subscription]),
                'invoices' => \Mockery::mock(InvoiceHandlerContract::class)
                    ->allows(['get' => $invoice]),
                'charges' => $chargesMock,
                'paymentMethod' => $paymentMethod,
            ]);

        $expectedPeriodStart = Carbon::now();
        $expectedPeriodEnd = Carbon::now()->addWeek();
        $eventPayload = collect([
            'id' => 'evt_456',
            'psp_response' => '
                {
                  "type": "invoice.payment_failed",
                  "subscription_id": "sub_00X",
                  "data": {
                    "object": {
                      "period_start": ' . $expectedPeriodStart->getTimestamp() . ',
                      "period_end": ' . $expectedPeriodEnd->getTimestamp() . '
                    }
                  },
                  "subscription_cycle_style": "PARALLEL",
                  "invoice": {
                    "id": "' . $invoiceId . '",
                    "latest_transaction_group": {
                      "id": "trx-group-1",
                      "transactions": [
                        {
                          "id": "trx-1"
                        }
                      ]
                    }
                  },
                  "retry": {
                    "style": "PARALLEL",
                    "number": 1,
                    "total_automated_retries": 1,
                    "next_attempt": 1584116608
                  }
                }
            ',
        ]);

        $eventManager = \Mockery::mock(EventManager::class);
        $eventManager
            ->shouldReceive('emit')
            ->withArgs(function (string $listener, array $params) {
                $this->assertSame(SubscriptionCyclePaymentHasFailed::class, $listener);

                return true;
            })
            ->once()
            ->shouldReceive('emit')
            ->withArgs(function (string $listener, array $params) {
                $this->assertSame(ChargeWasUpdated::class, $listener);

                return true;
            })
            ->once();
        app()->instance(EventManager::class, $eventManager);

        $featureFlagMock = $this->givenFeatureFlagIsRestrictedRenewalThroughMembershipsEnabledMock('49a7011a05c677b9a916612a', false);
        app()->instance(FeatureFlagInterface::class, $featureFlagMock);

        $handler = app()->makeWith(InvoicePaymentFailed::class, compact('eventPayload', 'paymentHandler'));
        $status = $handler->handle();

        /** @var User $user */
        $user = $this->usersRepository
            ->addCriteria(new MembershipSubscriptionStripeId('sub_00X'))
            ->firstOrFail();

        self::assertEquals(200, $status);
        self::assertEquals(
            $expectedPeriodStart->copy()->addWeek()->startOfDay()->toDateString(),
            $user->membership()->startDate()->toDateString()
        );
        self::assertEquals(
            $expectedPeriodEnd->copy()->addWeek()->startOfDay()->toDateString(),
            $user->membership()->expiryDate()->toDateString()
        );

        app()->forgetInstance(EventManager::class);
        app()->forgetInstance(FeatureFlagInterface::class);
    }

    private function givenFeatureFlagIsRestrictedRenewalThroughMembershipsEnabledMock(
        string $branchId,
        bool $isEnabled): FeatureFlagInterface {
        $flagger = Mockery::mock(FeatureFlagInterface::class);
        $flagger->shouldReceive('hasByBranchId')->withArgs([$branchId])->andReturn($isEnabled);
        $featureFlag = Mockery::mock(FeatureFlagInterface::class);
        $featureFlag
            ->shouldReceive('withFlag')
            ->withArgs([Flag::IS_RESTRICTED_RENEWAL_THROUGH_MEMBERSHIPS_ENABLED()])
            ->andReturn($flagger);
        return $featureFlag;
    }
}
