<?php

namespace Glofox\Infrastructure\Flags\Flaggers;

use Glofox\Domain\Branches\Models\Branch;
use Glofox\Infrastructure\Flags\IrisFlags;
use Glofox\Infrastructure\Flags\IrisGenericFlagger;

class IrisGlobalSearchRoamingMembersFlagger extends IrisGenericFlagger
{
    private const ENABLED_OPTION = 'ENABLED';

    public function shouldEnable(Branch $branch): bool
    {
        return true;
    }

    public function getSlug(): string
    {
        return IrisFlags::GLOBAL_SEARCH_ROAMING_MEMBERS;
    }

    public function getTestEnabledOption(): string
    {
        return static::ENABLED_OPTION;
    }
}
