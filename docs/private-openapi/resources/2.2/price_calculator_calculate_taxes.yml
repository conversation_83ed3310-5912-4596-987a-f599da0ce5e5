post:
  summary: Get tax and discounts breakdown for a given resource.
  description: |
    Calls price calculator service to determine the breakdown for a product(s) including discounts and taxes.
    
    Price calculator does not determine prorated amounts, or the validity of whether a discount/promo code can be used for a particular item. It is up to client to determine this.

    ## Discounts

    - `discount_ids`: This parameter can only be used by staff/integrators.
    - `promo_code`: This parameter can be used by any user.
    
    The `discount_ids` and `promo_code` parameters should only ever be used mutually exclusively.
    
  parameters:
    - in: path
      name: branchId
      required: true
      schema:
        type: string
  requestBody:
    required: true
    content:
      application/json:
        schema:
          $ref: '../../requests/2.2/get_price_breakdown.yml'
  tags:
    - Price Calculator
  responses:
    '200':
      description: Response
      content:
        application/json:
          schema:
            $ref: '../../schemas/calculate_taxes_breakdown.yml'
    '400':
      description: Bad request
      content:
        application/json:
          schema:
            $ref : "../../schemas/error.yml"