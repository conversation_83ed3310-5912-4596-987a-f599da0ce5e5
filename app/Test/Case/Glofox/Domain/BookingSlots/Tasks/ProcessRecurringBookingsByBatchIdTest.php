<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\BookingSlots\Tasks;

use Glofox\Domain\AsyncEvents\Events\RecurrentBookingRequestedEvent;
use Glofox\Domain\AsyncEvents\Events\RecurrentBookingRequestedEventMeta;
use Glofox\Domain\AsyncEvents\Events\RecurrentBookingRequestedEventPayload;
use Glofox\Domain\AsyncEvents\Events\RecurringBookingsProcessFinishedEventMeta;
use Glofox\Domain\AsyncEvents\Events\RecurringBookingsProcessFinishedEventPayload;
use Glofox\Domain\Bookings\Commands\ProcessBookingHandler;
use Glofox\Domain\Bookings\Commands\ProcessReservationHandler;
use Glofox\Domain\Bookings\Commands\ProcessReservationOrBookingCommand;
use Glofox\Domain\Bookings\Models\Booking;
use Glofox\Domain\Bookings\Services\BookingsPublisher;
use Glofox\Domain\BookingSlots\BookingSlotStatus;
use Glofox\Domain\BookingSlots\Models\BookingSlot;
use Glofox\Domain\BookingSlots\Repositories\BookingSlotsRepository;
use Glofox\Domain\BookingSlots\Tasks\ProcessRecurringBookingsByBatchId;
use Glofox\Domain\BookingSlots\Tasks\RecurringBookingProcessStats;
use Glofox\Domain\Locker\RecurrentBookingLockerParams;
use Glofox\Domain\Programs\Models\Program;
use Glofox\Domain\Programs\Repositories\ProgramsRepository;
use Glofox\Domain\Users\Models\User;
use Glofox\Domain\Users\Repositories\UsersRepository;
use Glofox\Domain\Users\Services\BotGenerator;
use Glofox\Infrastructure\Honeycomb\HoneycombTracker;
use Glofox\Infrastructure\Locker\RecurrentBookingLockerInterface;
use Mockery;
use Mockery\MockInterface;
use Psr\Log\LoggerInterface;

\App::import('Test/Case', 'GlofoxTestCase');

class ProcessRecurringBookingsByBatchIdTest extends \GlofoxTestCase
{
    public $fixtures = [];
    private ProcessRecurringBookingsByBatchId $task;
    private MockInterface $bookingSlotsRepository;
    private MockInterface $processBookingFromRecurringRequestHandler;
    private MockInterface $processReservationFromRecurringRequestHandler;
    private MockInterface $usersRepository;
    private MockInterface $programsRepository;
    private MockInterface $honeycombTracker;
    private MockInterface $logger;
    private MockInterface $bookingsPublisher;
    private MockInterface $botGenerator;
    private MockInterface $locker;

    public function setUp(): void
    {
        parent::setUp();

        $this->bookingSlotsRepository = Mockery::mock(BookingSlotsRepository::class);
        $this->processBookingFromRecurringRequestHandler = Mockery::mock(ProcessBookingHandler::class);
        $this->processReservationFromRecurringRequestHandler = Mockery::mock(ProcessReservationHandler::class);
        $this->usersRepository = Mockery::mock(UsersRepository::class);
        $this->programsRepository = Mockery::mock(ProgramsRepository::class);
        $this->honeycombTracker = Mockery::mock(HoneycombTracker::class);
        $this->recurringBookingProcessStats = Mockery::mock(RecurringBookingProcessStats::class);
        $this->logger = Mockery::mock(LoggerInterface::class);
        $this->bookingsPublisher = Mockery::mock(BookingsPublisher::class);
        $this->botGenerator = Mockery::mock(BotGenerator::class);
        $this->locker = Mockery::mock(RecurrentBookingLockerInterface::class);

        $this->task = new ProcessRecurringBookingsByBatchId(
            $this->bookingSlotsRepository,
            $this->processBookingFromRecurringRequestHandler,
            $this->processReservationFromRecurringRequestHandler,
            $this->usersRepository,
            $this->programsRepository,
            $this->honeycombTracker,
            $this->recurringBookingProcessStats,
            $this->logger,
            $this->bookingsPublisher,
            $this->botGenerator,
            $this->locker
        );
    }

    public function tearDown(): void
    {
        parent::tearDown();

        Mockery::close();
    }

    public function testItShouldProcessBookableSlotSuccessfully(): void
    {
        $batchId = 'coreapi-668e8c50e4e8c63831043';
        $requestedByUserId = 'some-user-id';

        $requestedByUser = User::make([
            '_id' => $requestedByUserId,
        ]);

        $this->shouldFindTheRequesterUser($requestedByUserId, $requestedByUser);

        $event = new RecurrentBookingRequestedEvent(
            RecurrentBookingRequestedEventMeta::empty(),
            RecurrentBookingRequestedEventPayload::from(
                $batchId,
                'some-branch-id',
                $requestedByUserId,
                false
            ),
        );

        $memberId = '66910a801ac44b1d0fb69f90';
        $programId = '66910aa7d0e287594039684c';
        $scheduleCode = '123abc';
        $slotTimeStart = 1_636_912_800;
        $slotTimeEnd = 1_636_916_400;

        $slots = [
            [
                'batch_id' => $batchId,
                'schedule_code' => $scheduleCode,
                'start_time' => $slotTimeStart,
                'end_time' => $slotTimeEnd,
                'bookable' => true,
                'reservable' => true,
                'member_id' => $memberId,
                'program_id' => $programId,
                'created' => '2020-01-01T00:00:00Z',
            ],
        ];

        $this->bookingSlotsRepository
            ->expects('findPendingByBatchId')
            ->with($batchId)
            ->andReturns($slots)
            ->getMock()
            ->expects('findUnprocessedByBatchId')
            ->with($batchId)
            ->andReturn([]);

        $member = $this->itShouldReturnTheMemberFromTheDB($memberId);
        $program = $this->itShouldReturnTheProgramFromTheDB($programId);

        $command = new ProcessReservationOrBookingCommand(
            $member,
            $scheduleCode,
            $program,
            $slotTimeStart,
            $requestedByUser,
            $batchId
        );

        $successfulResult = ['success' => true];
        $this->processBookingFromRecurringRequestHandler
            ->expects('handle')
            ->withArgs(function (ProcessReservationOrBookingCommand $currentCommand) use ($command) {
                $this->assertEquals($currentCommand, $command);

                return true;
            })
            ->andReturn($successfulResult);

        $this->processReservationFromRecurringRequestHandler->shouldNotHaveBeenCalled();

        $expectedSavedSlot = BookingSlot::make(
            [
                'batch_id' => $batchId,
                'schedule_code' => $scheduleCode,
                'start_time' => $slotTimeStart,
                'end_time' => $slotTimeEnd,
                'bookable' => true,
                'reservable' => true,
                'member_id' => $memberId,
                'program_id' => $programId,
                'status' => BookingSlotStatus::BOOKED,
                'created' => '2020-01-01T00:00:00Z',
            ]
        );

        $this->itShouldUnlockSlot($expectedSavedSlot);
        $this->itShouldDispatchProcessFinishedEvent($batchId, false);
        $this->itShouldSaveTheUpdatedSlot($expectedSavedSlot);
        $this->itShouldTrackInHoneycomb();
        $this->itShouldProcessStatsForGivenSlot($expectedSavedSlot);

        $this->task->execute($event);
    }

    public function testItShouldProcessReservableSlotSuccessfully(): void
    {
        $batchId = 'coreapi-668e8c50e4e8c63831043';
        $requestedByUserId = 'some-user-id';

        $requestedByUser = User::make([
            '_id' => $requestedByUserId,
        ]);

        $this->shouldFindTheRequesterUser($requestedByUserId, $requestedByUser);

        $event = new RecurrentBookingRequestedEvent(
            RecurrentBookingRequestedEventMeta::empty(),
            RecurrentBookingRequestedEventPayload::from(
                $batchId,
                'some-branch-id',
                $requestedByUserId,
                false
            ),
        );

        $memberId = '66910a801ac44b1d0fb69f90';
        $programId = '66910aa7d0e287594039684c';
        $scheduleCode = '123abc';
        $slotTimeStart = 1_636_912_800;
        $slotTimeEnd = 1_636_916_400;

        $slots = [
            [
                'batch_id' => $batchId,
                'schedule_code' => $scheduleCode,
                'start_time' => $slotTimeStart,
                'end_time' => $slotTimeEnd,
                'bookable' => false,
                'reservable' => true,
                'member_id' => $memberId,
                'program_id' => $programId,
                'created' => '2020-01-01T00:00:00Z',
            ],
        ];

        $this->bookingSlotsRepository
            ->expects('findPendingByBatchId')
            ->with($batchId)
            ->andReturns($slots)
            ->getMock()
            ->expects('findUnprocessedByBatchId')
            ->with($batchId)
            ->andReturn([]);

        $member = $this->itShouldReturnTheMemberFromTheDB($memberId);
        $program = $this->itShouldReturnTheProgramFromTheDB($programId);

        $command = new ProcessReservationOrBookingCommand(
            $member,
            $scheduleCode,
            $program,
            $slotTimeStart,
            $requestedByUser,
            $batchId
        );

        $this->processBookingFromRecurringRequestHandler->shouldNotHaveBeenCalled();

        $someFakeBookingAsASuccessfulResult = Booking::make([]);
        $this->processReservationFromRecurringRequestHandler
            ->expects('handle')
            ->withArgs(function (ProcessReservationOrBookingCommand $currentCommand) use ($command) {
                $this->assertEquals($currentCommand, $command);

                return true;
            })
            ->andReturn($someFakeBookingAsASuccessfulResult);

        $expectedSavedSlot = BookingSlot::make(
            [
                'batch_id' => $batchId,
                'schedule_code' => $scheduleCode,
                'start_time' => $slotTimeStart,
                'end_time' => $slotTimeEnd,
                'bookable' => false,
                'reservable' => true,
                'member_id' => $memberId,
                'program_id' => $programId,
                'status' => BookingSlotStatus::RESERVED,
                'created' => '2020-01-01T00:00:00Z',
            ]
        );

        $this->itShouldUnlockSlot($expectedSavedSlot);
        $this->itShouldDispatchProcessFinishedEvent($batchId, false);
        $this->itShouldSaveTheUpdatedSlot($expectedSavedSlot);
        $this->itShouldTrackInHoneycomb();
        $this->itShouldProcessStatsForGivenSlot($expectedSavedSlot);

        $this->task->execute($event);
    }

    public function testItShouldProcessFailedSlotSuccessfully(): void
    {
        $batchId = 'coreapi-668e8c50e4e8c63831043';
        $requestedByUserId = 'some-user-id';

        $requestedByUser = User::make([
            '_id' => $requestedByUserId,
        ]);

        $this->shouldFindTheRequesterUser($requestedByUserId, $requestedByUser);

        $event = new RecurrentBookingRequestedEvent(
            RecurrentBookingRequestedEventMeta::empty(),
            RecurrentBookingRequestedEventPayload::from(
                $batchId,
                'some-branch-id',
                $requestedByUserId,
                false
            ),
        );

        $memberId = '66910a801ac44b1d0fb69f90';
        $programId = '66910aa7d0e287594039684c';
        $scheduleCode = '123abc';
        $slotTimeStart = 1_636_912_800;
        $slotTimeEnd = 1_636_916_400;

        $slots = [
            [
                'batch_id' => $batchId,
                'schedule_code' => $scheduleCode,
                'start_time' => $slotTimeStart,
                'end_time' => $slotTimeEnd,
                'bookable' => false,
                'reservable' => false,
                'member_id' => $memberId,
                'program_id' => $programId,
                'created' => '2020-01-01T00:00:00Z',
            ],
        ];

        $this->bookingSlotsRepository
            ->expects('findPendingByBatchId')
            ->with($batchId)
            ->andReturns($slots)
            ->getMock()
            ->expects('findUnprocessedByBatchId')
            ->with($batchId)
            ->andReturn([]);

        $this->processBookingFromRecurringRequestHandler->shouldNotHaveBeenCalled();

        $this->processReservationFromRecurringRequestHandler->shouldNotHaveBeenCalled();

        $expectedSavedSlot = BookingSlot::make(
            [
                'batch_id' => $batchId,
                'schedule_code' => $scheduleCode,
                'start_time' => $slotTimeStart,
                'end_time' => $slotTimeEnd,
                'bookable' => false,
                'reservable' => false,
                'member_id' => $memberId,
                'program_id' => $programId,
                'status' => BookingSlotStatus::FAILED,
                'error' => 'BOOKABLE_AND_RESERVABLE_ARE_NOT_TRUE',
                'created' => '2020-01-01T00:00:00Z',
            ]
        );

        $this->itShouldUnlockSlot($expectedSavedSlot);
        $this->itShouldDispatchProcessFinishedEvent($batchId, true);
        $this->itShouldSaveTheUpdatedSlot($expectedSavedSlot);
        $this->itShouldTrackInHoneycomb();
        $this->itShouldProcessStatsForGivenSlot($expectedSavedSlot);

        $this->task->execute($event);
    }

    public function testItShouldDoNothingWhenNoSlotsAreFound(): void
    {
        $batchId = 'coreapi-668e8c50e4e8c63831043';
        $requestedByUserId = 'some-user-id';
        $event = new RecurrentBookingRequestedEvent(
            RecurrentBookingRequestedEventMeta::empty(),
            RecurrentBookingRequestedEventPayload::from(
                $batchId,
                'some-branch-id',
                $requestedByUserId,
                false
            ),
        );

        $requestedByUser = User::make([
            '_id' => $requestedByUserId,
        ]);

        $this->shouldFindTheRequesterUser($requestedByUserId, $requestedByUser);

        $this->bookingSlotsRepository
            ->expects('findPendingByBatchId')
            ->with($batchId)
            ->andReturns([]);

        $this->processBookingFromRecurringRequestHandler
            ->shouldNotHaveBeenCalled();

        $this->processReservationFromRecurringRequestHandler
            ->shouldNotHaveBeenCalled();

        $this->bookingSlotsRepository
            ->shouldNotHaveBeenCalled();

        $this->honeycombTracker
            ->shouldNotHaveBeenCalled();

        $this->locker
            ->shouldNotHaveBeenCalled();

        $this->task->execute($event);
    }

    private function itShouldDispatchProcessFinishedEvent(string $batchId, bool $hasAnySlotFailed): void
    {
        $this->recurringBookingProcessStats
            ->expects('hasAnySlotFailed')
            ->andReturn($hasAnySlotFailed);

        $this->bookingsPublisher
            ->expects('sendRecurrentBookingProcessFinishedEvent')
            ->withArgs(
                function (
                    RecurringBookingsProcessFinishedEventMeta $meta,
                    RecurringBookingsProcessFinishedEventPayload $payload
                ) use ($batchId, $hasAnySlotFailed) {
                    $payloadData = $payload->jsonSerialize();
                    $this->assertEquals($batchId, $payloadData['batch_id']);
                    $this->assertEquals($hasAnySlotFailed, $payloadData['has_any_slot_failed']);

                    return true;
                }
            );
    }

    private function itShouldSaveTheUpdatedSlot(BookingSlot $expectedSavedSlot): void
    {
        $this->bookingSlotsRepository
            ->expects('update')
            ->withArgs(function (BookingSlot $currentSlot) use ($expectedSavedSlot) {
                $this->assertEquals($currentSlot, $expectedSavedSlot);

                return true;
            });
    }

    private function itShouldReturnTheMemberFromTheDB(string $memberId): User
    {
        $member = User::make(['_id' => $memberId]);
        $this->usersRepository
            ->expects('findById')
            ->with($memberId)
            ->andReturns(['User' => $member->toArray()]);

        return $member;
    }

    private function itShouldReturnTheProgramFromTheDB(string $programId): Program
    {
        $program = Program::make(['_id' => $programId]);
        $this->programsRepository
            ->expects('findById')
            ->with($programId)
            ->andReturns(['Program' => $program->toArray()]);

        return $program;
    }

    private function itShouldTrackInHoneycomb(): void
    {
        $this->honeycombTracker
            ->expects('track')
            ->withAnyArgs();
    }

    private function shouldFindTheRequesterUser(string $requestedByUserId, User $requestedByUser): void
    {
        $this->usersRepository
            ->shouldReceive('getById')
            ->once()
            ->with($requestedByUserId)
            ->andReturn($requestedByUser);
    }

    private function itShouldProcessStatsForGivenSlot(BookingSlot $expectedSlot): void
    {
        $this->recurringBookingProcessStats
            ->expects('calculate')
            ->withArgs(function (BookingSlot $currentSlot) use ($expectedSlot) {
                $this->assertEquals($currentSlot, $expectedSlot);

                return true;
            })
            ->getMock()
            ->expects('getBooked')
            ->getMock()
            ->expects('getFailed')
            ->getMock()
            ->expects('getReserved')
            ->getMock()
            ->expects('getProcessed')
            ->getMock()
            ->expects('getErrorCodes')
            ->getMock();
    }

    private function itShouldUnlockSlot(BookingSlot $expectedSavedSlot): void
    {
        $this->locker
            ->expects('unlock')
            ->withArgs(
                function (
                    RecurrentBookingLockerParams $params
                ) use ($expectedSavedSlot) {
                    $this->assertEquals($expectedSavedSlot->programId(), $params->programId());
                    $this->assertEquals($expectedSavedSlot->scheduleCode(), $params->scheduleCode());
                    $this->assertEquals($expectedSavedSlot->memberId(), $params->userId());
                    $this->assertEquals($expectedSavedSlot->startTime(), $params->bookingsStartTime()->timestamp);
                    $this->assertEquals($expectedSavedSlot->endTime(), $params->bookingsUntilTime()->timestamp);

                    return true;
                }
            );
    }
}
