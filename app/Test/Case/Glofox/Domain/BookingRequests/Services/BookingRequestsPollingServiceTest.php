<?php

namespace CakeTestCases\Glofox\Domain\BookingRequests\Services;

use Glofox\Domain\BookingRequests\Models\BookingRequest;
use Glofox\Domain\BookingRequests\Models\BookingRequestType;
use Glofox\Domain\BookingRequests\Repositories\BookingRequestsRepository;
use Glofox\Domain\BookingRequests\Services\BookingRequestsPollingService;
use Psr\Log\LoggerInterface;

\App::import('Test/Case', 'GlofoxTestCase');

class BookingRequestsPollingServiceTest extends \GlofoxTestCase
{
    public $fixtures = [];
    private $bookingRequestsRepository;
    private $logger;
    private int $interval = 0;

    private function createPollingService()
    {
        return new BookingRequestsPollingService(
            $this->bookingRequestsRepository,
            $this->logger,
            $this->interval
        );
    }

    public function setUp()
    {
        parent::setUp();

        $this->bookingRequestsRepository = \Mockery::mock(BookingRequestsRepository::class);
        $this->logger = \Mockery::mock(LoggerInterface::class);
        $this->logger->shouldReceive('info');
    }

    public function tearDown()
    {
        parent::tearDown();
        \Mockery::close();
    }

    public function test_it_should_poll_for_booking_request_is_processed_or_rejected()
    {
        $bookingRequest = \Mockery::spy(BookingRequest::class);
        $bookingRequest
            ->shouldReceive('isComplete')
            ->andReturnTrue();

        $this->bookingRequestsRepository
            ->shouldReceive('addCriteria')->andReturnSelf()
            ->shouldReceive('order')->andReturnSelf()
            ->shouldReceive('first')->andReturn($bookingRequest);

        $this->assertEquals(
            $bookingRequest,
            $this->createPollingService()->poll('123', '234', BookingRequestType::BOOKING())
        );
    }

    public function test_it_should_poll_until_booking_request_is_processed_or_rejected()
    {
        $bookingRequest = \Mockery::spy(BookingRequest::class);
        $bookingRequest
            ->shouldReceive('isComplete')
            ->once()
            ->andReturnFalse()
            ->shouldReceive('isComplete')
            ->once()
            ->andReturnTrue();

        $this->bookingRequestsRepository
            ->shouldReceive('addCriteria')->andReturnSelf()
            ->shouldReceive('order')->andReturnSelf()
            ->shouldReceive('first')->andReturn($bookingRequest);

        $this->assertEquals(
            $bookingRequest,
            $this->createPollingService()->poll('123', '234', BookingRequestType::BOOKING())
        );
    }

    public function test_it_should_keep_polling_for_booking_request_that_is_processed_or_rejected()
    {
        $bookingRequest = \Mockery::spy(BookingRequest::class);
        $bookingRequest
            ->shouldReceive('isComplete')
            ->andReturnTrue();

        $this->bookingRequestsRepository
            ->shouldReceive('addCriteria')->andReturnSelf()
            ->shouldReceive('order')->andReturnSelf()
            ->shouldReceive('first')
            ->once()
            ->andReturn(null)
            ->shouldReceive('first')
            ->once()
            ->andReturn($bookingRequest);

        $this->assertEquals(
            $bookingRequest,
            $this->createPollingService()->poll('123', '234', BookingRequestType::BOOKING())
        );
    }

    public function test_it_should_poll_until_safety_counter_is_reached()
    {
        $this->bookingRequestsRepository = \Mockery::spy(BookingRequestsRepository::class);
        $this->bookingRequestsRepository->shouldReceive('addCriteria')->andReturnSelf();
        $this->bookingRequestsRepository->shouldReceive('order')->andReturnSelf();
        $this->bookingRequestsRepository->shouldReceive('first')->andReturn(null);

        $this->assertEquals(
            null,
            $this->createPollingService()->poll('123', '234', BookingRequestType::BOOKING())
        );

        $this->bookingRequestsRepository->shouldHaveReceived('first')->times(10);
    }

    public function test_default_interval_is_500_milliseconds(): void
    {
        $instance = new BookingRequestsPollingService(
            $this->bookingRequestsRepository,
            $this->logger
        );

        $property = new \ReflectionProperty($instance, 'intervalMicroseconds');
        $property->setAccessible(true);

        $this->assertSame(500000, $property->getValue($instance));
    }
}
