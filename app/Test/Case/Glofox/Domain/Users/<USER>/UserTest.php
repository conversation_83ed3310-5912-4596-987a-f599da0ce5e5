<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\Users\Models;

use App;
use CakeTestCases\Glofox\Domain\Users\Traits\AuthenticateUsersTrait;
use Carbon\Carbon;
use Closure;
use Glofox\CdnProvider;
use Glofox\Datasource\Exceptions\InvalidMongoIdException;
use Glofox\Domain\Branches\Models\Branch;
use Glofox\Domain\Credits\ModelList as CreditsModelList;
use Glofox\Domain\FeatureFlags\FeatureFlagInterface;
use Glofox\Domain\PaymentMethods\Type as PaymentMethodType;
use Glofox\Domain\TimeSlots\Models\TimeSlot;
use Glofox\Domain\Users\Exceptions\UserOriginBranchIdException;
use Glofox\Domain\Users\Models\User;
use Glofox\Domain\Users\Repositories\UsersRepository;
use Glofox\Domain\Users\Services\Avatar\AvatarUrlFactory;
use Glofox\Infrastructure\Flags\Flag;
use Glofox\Repositories\Search\Expressions\Shared\Id;
use GlofoxTestCase;
use MembershipPlanStartsOn;
use Mockery;
use UserType;

App::import('Test/Case', 'GlofoxTestCase');

class UserTest extends GlofoxTestCase
{
    use AuthenticateUsersTrait;

    public $fixtures = [
        'app.branch',
        'app.user',
        'app.user_credit',
    ];

    private const TEST_PARENT_ID = '650c153cbe428f7ea80b1a1a';
    private const TEST_ID = 'test-id';
    protected UsersRepository $usersRepository;
    protected AvatarUrlFactory $avatarUrlFactory;

    public function setUp(): void
    {
        parent::setUp();

        $this->avatarUrlFactory = app()->make(AvatarUrlFactory::class);
        $this->usersRepository = app()->make(UsersRepository::class);
    }

    public function tearDown(): void
    {
        parent::tearDown();
        app()->forgetInstance(UsersRepository::class);
        Mockery::close();
    }

    public function test_it_returns_correct_url_for_trainer_images(): void
    {
        $userId = 'mock-user-id';
        $branchId = 'mock-branch-id';
        $namespace = 'mock-namespace';
        $originBranchId = $branchId;
        $modified = time();
        $environment = 'local';
        $extension = 'png';

        $user = User::make([
            '_id' => $userId,
            'namespace' => $namespace,
            'branch_id' => $branchId,
            'origin_branch_id' => $originBranchId,
            'modified' => $modified,
            'type' => UserType::TRAINER
        ]);

        $imageUrl = $user->image(
            $this->avatarUrlFactory
        );

        $expectedUrlTemplate = CdnProvider::getUrl() . '/%s/%s/branches/%s/trainers/%s/default.%s?v=%s';
        $expectedUrl = sprintf(
            $expectedUrlTemplate,
            $environment,
            $namespace,
            $originBranchId,
            $userId,
            $extension,
            $modified
        );

        self::assertEquals($expectedUrl, $imageUrl, $user->toJson());
    }

    public function test_it_returns_correct_url_for_member_images(): void
    {
        $userId = 'mock-user-id';
        $branchId = 'mock-branch-id';
        $namespace = 'mock-namespace';
        $originBranchId = $branchId;
        $modified = time();
        $environment = 'local';
        $extension = 'png';

        $user = User::make([
            '_id' => $userId,
            'namespace' => $namespace,
            'branch_id' => $branchId,
            'origin_branch_id' => $originBranchId,
            'modified' => $modified,
            'type' => UserType::MEMBER
        ]);

        $imageUrl = $user->image(
            $this->avatarUrlFactory
        );

        $expectedUrlTemplate = CdnProvider::getUrl() . '/%s/%s/branches/%s/users/%s.%s?v=%s';
        $expectedUrl = sprintf(
            $expectedUrlTemplate,
            $environment,
            $namespace,
            $originBranchId,
            $userId,
            $extension,
            $modified
        );

        self::assertEquals($expectedUrl, $imageUrl, $user->toJson());
    }

    public function test_it_returns_active(): void
    {
        $user = User::make([
            'active' => true
        ]);

        self::assertTrue($user->isActive());

        $user = User::make([
            'active' => false
        ]);

        self::assertFalse($user->isActive());
    }

    /**
     * @dataProvider provider_test_it_belongs_to
     */
    public function test_it_belongs_to(array $input, $expected): void
    {
        $this->authenticateAsGuest();

        $branch = Branch::make($input['branch']);
        $user = $this->usersRepository
            ->addCriteria($input['userId'])
            ->firstOrFail();

        $user = User::make($user);

        $response = $user->belongsTo($branch);

        self::assertEquals($expected, $response);
    }

    public function provider_test_it_belongs_to(): array
    {
        return [
            'User has an array in the branch_id and the Branch belongs to that list' => [
                [
                    'userId' => new Id('5b2a698c912db0f8e5f958b9'),
                    'branch' => [
                        '_id' => '5b2a69a23152c0744c2df3aa',
                    ],
                ],
                true,
            ],
            'User has an array in the branch_id and the Branch does not belong to that list' => [
                [
                    'userId' => new Id('5b2a698c912db0f8e5f958b9'),
                    'branch' => [
                        '_id' => '5b30fa289acb4455b1fc80f3',
                    ],
                ],
                false,
            ],
        ];
    }

    /**
     * @dataProvider provider_test_get_branches
     * @throws InvalidMongoIdException
     */
    public function test_get_branches(Id $userId, array $expectedBranches): void
    {
        $this->authenticateAsGuest();

        $user = $this->usersRepository
            ->addCriteria($userId)
            ->firstOrFail();

        $user = User::make($user);

        $response = $user->branches();

        self::assertEquals($expectedBranches, $response);
    }

    public function provider_test_get_branches(): array
    {
        return [
            'User has an array of 3 in the branch_id attribute' => [
                new Id('5b2a698c912db0f8e5f958b9'),
                [
                    '5b2a6999389704011b02d6bf',
                    '5b2a69a23152c0744c2df3aa',
                    '5b2a69ab7358de215ef5fe2c',
                ],
            ],
            'User has a string in the branch_id attribute' => [
                new Id('5b181291aadae0f18e8983a2'),
                [
                    '5addc25383266f65abf515c4',
                ],
            ],
        ];
    }

    /**
     * @dataProvider provider_test_has_first_booking_membership
     */
    public function test_has_first_booking_membership(array $userData, Closure $assertionClosure): void
    {
        $user = User::make($userData);

        $result = $user->hasFirstBookingMembership();

        $assertionClosure->call($this, $result);
    }

    public function provider_test_has_first_booking_membership(): array
    {
        return [
            'Member with an unlimited membership that starts on first booking' => [
                [
                    '_id' => '5b8c597271d970618cc5dcdf',
                    'branch_id' => '49a7011a05c677b9a916612a',
                    'membership' => [
                        '_id' => '5b8c5bc05e195e316e074ff4',
                        'plan_code' => '1521335368323',
                        'type' => 'time',
                        'starts_on' => MembershipPlanStartsOn::FIRST_BOOKING_DATE,
                        'duration_time_unit' => 'month',
                        'duration_time_unit_count' => 1,
                    ],
                ],
                function ($result) {
                    self::assertTrue($result);
                },
            ],
            'Member with a credit pack membership that starts on first booking but ran out of credits' => [
                [
                    '_id' => '5b8c597271d970618cc5dcdf',
                    'branch_id' => '49a7011a05c677b9a916612a',
                    'membership' => [
                        '_id' => '5b8c5bc05e195e316e074ff4',
                        'plan_code' => '1529406154886',
                        'type' => 'num_classes',
                        'starts_on' => MembershipPlanStartsOn::FIRST_BOOKING_DATE,
                    ],
                ],
                function ($result) {
                    self::assertFalse($result);
                },
            ],
            'Member with a credit pack membership that starts on first booking and still have active credits' => [
                [
                    '_id' => '5b8c6dd726dd9ebba8eed07b',
                    'branch_id' => '49a7011a05c677b9a916612a',
                    'membership' => [
                        '_id' => '5b8c5bc05e195e316e074ff4',
                        'plan_code' => '1529406154886',
                        'type' => 'num_classes',
                        'starts_on' => MembershipPlanStartsOn::FIRST_BOOKING_DATE,
                    ],
                ],
                function ($result) {
                    self::assertTrue($result);
                },
            ],
            'Drop in Member with active first booking credits' => [
                [
                    '_id' => '5b8c6dd726dd9ebba8eed07b',
                    'branch_id' => '49a7011a05c677b9a916612a',
                    'membership' => [
                        'type' => 'payg',
                    ],
                ],
                function ($result) {
                    self::assertTrue($result);
                },
            ],
            'Member with an unlimited membership that started on first booking but is expired' => [
                [
                    '_id' => '5b8c597271d970618cc5dcdf',
                    'branch_id' => '49a7011a05c677b9a916612a',
                    'membership' => [
                        '_id' => '5b8c5bc05e195e316e074ff4',
                        'plan_code' => '1521335368323',
                        'type' => 'time',
                        'starts_on' => MembershipPlanStartsOn::FIRST_BOOKING_DATE,
                        'duration_time_unit' => 'month',
                        'duration_time_unit_count' => 1,
                        'start_date' => Carbon::today()->subDay(31)->format('Y-m-d H:i:s'),
                        'expiry_date' => Carbon::today()->subDay()->format('Y-m-d H:i:s'),
                    ],
                ],
                function ($result) {
                    self::assertFalse($result);
                },
            ],
        ];
    }

    /**
     * @dataProvider testCreditsForProvider
     */
    public function testCreditsFor($data, Closure $assertionClosure): void
    {
        $user = User::make($data['member']);

        $timeslot = TimeSlot::make($data['creditPayable']);

        $result = $user->creditsFor($timeslot);

        $assertionClosure->call($this, $result);
    }

    public function testCreditsForProvider(): array
    {
        return [
            'Member with one pt credit left' => [
                [
                    'member' => [
                        '_id' => '5ba8cdfd6b24361fa2da2249',
                        'branch_id' => '49a7011a05c677b9a916612a',
                        'membership' => [
                            'type' => 'payg',
                        ],
                    ],
                    'creditPayable' => [
                        '_id' => '5ba8d934c4942047b524733a',
                        'branch_id' => '49a7011a05c677b9a916612a',
                        'namespace' => 'glofox',
                        'active' => true,
                        'date' => Carbon::today()->addDays(2)->getTimestamp(),
                        'time_start' => Carbon::today()->addDays(2)->addHours(7)->getTimestamp(),
                        'time_finish' => Carbon::today()->addDays(2)->addHours(7)->getTimestamp(),
                        'model' => CreditsModelList::USERS,
                        'model_id' => '59a7011a05c677bda916612c',
                        'name' => 'James Richin',
                        'booked' => false,
                    ],
                ],
                function ($crediPackCollection) {
                    self::assertEquals(1, $crediPackCollection->creditsLeft());
                },
            ],
        ];
    }

    /**
     * @throws UserOriginBranchIdException
     */
    public function testOriginBranchIdReturnsStaffBranchIdFromBranchIdWhenItIsSetAsString(): void
    {
        $expectedBranchId = 'expectedBranchId';
        $originBranchId = 'incorrect_branch_id';
        $testUser = User::make([
            'type' => 'ADMIN',
            'branch_id' => $expectedBranchId,
            'origin_branch_id' => $originBranchId
        ]);

        $result = $testUser->originBranchId();

        $this->assertEquals($expectedBranchId, $result);
    }

    /**
     * @throws UserOriginBranchIdException
     */
    public function testOriginBranchIdReturnsBranchIdWhenItIsSetAsString(): void
    {
        $originBranchId = 'as_string';
        $testUser = User::make(['origin_branch_id' => $originBranchId]);

        $result = $testUser->originBranchId();

        $this->assertEquals($originBranchId, $result);
    }

    /**
     * @throws UserOriginBranchIdException
     */
    public function testOriginBranchIdReturnsTheFirstIdFromArrayOfBranchIdsWhenOriginBranchIdIsNotSetup(): void
    {
        $expectedBranchId = 'expected_branch_id';
        $arrayOfBranchIds = [$expectedBranchId, 'another_branch_id'];
        $testUser = User::make(['branch_id' => $arrayOfBranchIds]);

        $result = $testUser->originBranchId();

        $this->assertEquals($expectedBranchId, $result);
    }

    /**
     * @throws UserOriginBranchIdException
     */
    public function testOriginBranchIdReturnsTheFirstIdFromArrayOfOriginBranchIdsWhenBranchIdIsNotSetup(): void
    {
        $expectedBranchId = 'expected_branch_id';
        $arrayOfOriginBranchIds = [$expectedBranchId, 'another_branch_id'];
        $testUser = User::make(['origin_branch_id' => $arrayOfOriginBranchIds]);

        $result = $testUser->originBranchId();

        $this->assertEquals($expectedBranchId, $result);
    }

    /**
     * @throws UserOriginBranchIdException
     */
    public function testOriginBranchIdThrowsUserOriginBranchIdExceptionWhenNoneOfTheBranchIdsIsProvided(): void
    {
        $testUser = User::make([]);
        $this->expectExceptionMessage('ORIGIN_BRANCH_ID_OF_USER_CANNOT_BE_RETRIEVED');

        $testUser->originBranchId();
    }

    /**
     * @dataProvider isParentDataProvider
     * @param string|null $parentId
     * @param string $id
     * @param bool $expected
     * @return void
     */
    public function testIsParent(?string $parentId, string $id, bool $expected): void
    {
        $user = User::make([
            'parent_id' => $parentId,
            '_id' => $id,
        ]);
        static::assertEquals($expected, $user->isParent());
    }

    public function isParentDataProvider(): array
    {
        return [
            'when parentId is null, then it returns true' => [
                'parentId' => null,
                'id' => static::TEST_ID,
                'expected' => true,
            ],
            'when parentId is empty, then it returns true' => [
                'parentId' => '',
                'id' => static::TEST_ID,
                'expected' => true,
            ],
            'when parentId is not empty and is equal than id, then it returns true' => [
                'parentId' => static::TEST_ID,
                'id' => static::TEST_ID,
                'expected' => true,
            ],
            'when parentId is not empty and is not equal than id, then it returns false' => [
                'parentId' => static::TEST_PARENT_ID,
                'id' => static::TEST_ID,
                'expected' => false,
            ],
        ];
    }

    /**
     * @dataProvider parentDataProvider
     * @param string|null $parentId
     * @param string $id
     * @param User|null $expected
     * @param bool $expectedException
     * @return void
     * @throws InvalidMongoIdException
     */
    public function testParent(
        ?string $parentId,
        string $id,
        ?User $expected,
        bool $expectedException
    ): void {
        $user = User::make([
            'parent_id' => $parentId,
            '_id' => $id,
        ]);

        if ($expectedException) {
            $this->expectExceptionMessage(
                sprintf(
                    'Parent Account not found - id: %s for Child - id: %s',
                    $parentId,
                    $id
                )
            );
        }

        if ($expected !== null || $expectedException) {
            $this->mockFetchParentModel($parentId, $expectedException);
        }

        $parent = $user->parent();
        static::assertEquals($expected, $parent);
    }

    public function parentDataProvider(): array
    {
        return [
            'when parentId is null, then it returns null' => [
                'parentId' => null,
                'id' => static::TEST_ID,
                'expected' => null,
                'expectedException' => false,
            ],
            'when parentId is empty, then it returns null' => [
                'parentId' => '',
                'id' => static::TEST_ID,
                'expected' => null,
                'expectedException' => false,
            ],
            'when parentId is not empty and is equal than id, then it returns null' => [
                'parentId' => static::TEST_ID,
                'id' => static::TEST_ID,
                'expected' => null,
                'expectedException' => false,
            ],
            'when parentId is not empty, is not equal than id and it exists, then it returns the User model' => [
                'parentId' => static::TEST_PARENT_ID,
                'id' => static::TEST_ID,
                'expected' => User::make([
                    '_id' => static::TEST_PARENT_ID,
                ]),
                'expectedException' => false,
            ],
            'when parentId is not empty, is not equal than id and it does not exists, then it throws an exception' => [
                'parentId' => static::TEST_PARENT_ID,
                'id' => static::TEST_ID,
                'expected' => null,
                'expectedException' => true,
            ],
        ];
    }

    /**
     * @dataProvider isChildDataProvider
     * @param string|null $parentId
     * @param string $id
     * @param bool $expected
     * @return void
     */
    public function testIsChild(?string $parentId, string $id, bool $expected): void
    {
        $user = User::make([
            'parent_id' => $parentId,
            '_id' => $id,
        ]);

        static::assertEquals($expected, $user->isChild());
    }

    public function isChildDataProvider(): array
    {
        return [
            'when parentId is null, then it returns false' => [
                'parentId' => null,
                'id' => static::TEST_ID,
                'expected' => false,
            ],
            'when parentId is empty, then it returns false' => [
                'parentId' => '',
                'id' => static::TEST_ID,
                'expected' => false,
            ],
            'when parentId is not empty and is equal than id, then it returns false' => [
                'parentId' => static::TEST_ID,
                'id' => static::TEST_ID,
                'expected' => false,
            ],
            'when parentId is not empty and is not equal than id, then it returns true' => [
                'parentId' => static::TEST_PARENT_ID,
                'id' => static::TEST_ID,
                'expected' => true,
            ],
        ];
    }

    /**
     * @dataProvider childIsUsingParentEmailDataProvider
     * @param string|null $parentId
     * @param string $id
     * @param bool|null $useParentEmail
     * @param string $email
     * @param bool $expected
     * @return void
     */
    public function testChildIsUsingParentEmail(
        ?string $parentId,
        string $id,
        ?bool $useParentEmail,
        string $email,
        bool $expected
    ): void {
        $user = User::make([
            'parent_id' => $parentId,
            '_id' => $id,
            'use_parent_email' => $useParentEmail,
            'email' => $email,
        ]);
        static::assertEquals($expected, $user->childIsUsingParentEmail());
    }

    public function childIsUsingParentEmailDataProvider(): array
    {
        return [
            'when is not child, then it returns false' => [
                'parentId' => null,
                'id' => static::TEST_ID,
                'useParentEmail' => false,
                'email' => '',
                'expected' => false,
            ],
            'when is child and use_parent_email is true, then it returns true' => [
                'parentId' => static::TEST_PARENT_ID,
                'id' => static::TEST_ID,
                'useParentEmail' => true,
                'email' => '',
                'expected' => true,
            ],
            'when is child and use_parent_email is false and email pattern not match, then it returns false' => [
                'parentId' => static::TEST_PARENT_ID,
                'id' => static::TEST_ID,
                'useParentEmail' => false,
                'email' => '',
                'expected' => false,
            ],
            'when is child and use_parent_email is null and email pattern not match, then it returns false' => [
                'parentId' => static::TEST_PARENT_ID,
                'id' => static::TEST_ID,
                'useParentEmail' => null,
                'email' => '<EMAIL>',
                'expected' => false,
            ],
            'when is child and use_parent_email is null and email pattern matches, then it returns true' => [
                'parentId' => static::TEST_PARENT_ID,
                'id' => static::TEST_ID,
                'useParentEmail' => null,
                'email' => '<EMAIL>',
                'expected' => true,
            ],
        ];
    }

    /**
     * @dataProvider childIsUsingParentPhoneDataProvider
     * @param string|null $parentId
     * @param string $id
     * @param bool|null $useParentPhone
     * @param string|null $phone
     * @param bool $expected
     * @return void
     */
    public function testChildIsUsingParentPhone(
        ?string $parentId,
        string $id,
        ?bool $useParentPhone,
        ?string $phone,
        bool $expected
    ): void {
        $user = User::make([
            'parent_id' => $parentId,
            '_id' => $id,
            'use_parent_phone' => $useParentPhone,
            'phone' => $phone,
        ]);

        static::assertEquals($expected, $user->childIsUsingParentPhone());
    }

    public function childIsUsingParentPhoneDataProvider(): array
    {
        return [
            'when is not child, then it returns false' => [
                'parentId' => null,
                'id' => static::TEST_ID,
                'useParentPhone' => false,
                'phone' => null,
                'expected' => false,
            ],
            'when is child and use_parent_phone is true, then it returns true' => [
                'parentId' => static::TEST_PARENT_ID,
                'id' => static::TEST_ID,
                'useParentPhone' => true,
                'phone' => null,
                'expected' => true,
            ],
            'when is child and use_parent_phone is false and phone is not null, then it returns false' => [
                'parentId' => static::TEST_PARENT_ID,
                'id' => static::TEST_ID,
                'useParentPhone' => false,
                'phone' => '+34666666666',
                'expected' => false,
            ],
            'when is child and use_parent_phone is null and phone is not null, then it returns false' => [
                'parentId' => static::TEST_PARENT_ID,
                'id' => static::TEST_ID,
                'useParentPhone' => null,
                'phone' => '+34666666666',
                'expected' => false,
            ],
            'when is child and use_parent_phone is null and phone is null, then it returns true' => [
                'parentId' => static::TEST_PARENT_ID,
                'id' => static::TEST_ID,
                'useParentPhone' => null,
                'phone' => null,
                'expected' => true,
            ],
            'when is child and use_parent_phone is null and phone is empty, then it returns true' => [
                'parentId' => static::TEST_PARENT_ID,
                'id' => static::TEST_ID,
                'useParentPhone' => null,
                'phone' => '',
                'expected' => true,
            ],
        ];
    }

    /**
     * @dataProvider childIsUsingParentCardDataProvider
     * @param string|null $parentId
     * @param string $id
     * @param bool $useParentCard
     * @param bool $expected
     * @return void
     */
    public function testChildIsUsingParentCard(?string $parentId, string $id, bool $useParentCard, bool $expected): void
    {
        $user = User::make([
            'parent_id' => $parentId,
            '_id' => $id,
            'use_parent_card' => $useParentCard,
        ]);

        static::assertEquals($expected, $user->childIsUsingParentCard());
    }

    public function childIsUsingParentCardDataProvider(): array
    {
        return [
            'when is not child, then it returns false' => [
                'parentId' => null,
                'id' => static::TEST_ID,
                'useParentCard' => false,
                'expected' => false,
            ],
            'when is child and use_parent_card is true, then it returns true' => [
                'parentId' => static::TEST_PARENT_ID,
                'id' => static::TEST_ID,
                'useParentCard' => true,
                'expected' => true,
            ],
            'when is child and use_parent_card is false, then it returns false' => [
                'parentId' => static::TEST_PARENT_ID,
                'id' => static::TEST_ID,
                'useParentCard' => false,
                'expected' => false,
            ],
        ];
    }

    /**
     * @dataProvider childIsUsingParentPaymentMethodTypeDataProvider
     * @param string|null $parentId
     * @param string $id
     * @param string $paymentMethodType
     * @param bool $useParentMandate
     * @param bool $useParentCard
     * @param bool $expected
     * @return void
     */
    public function testChildIsUsingParentPaymentMethodType(
        ?string $parentId,
        string $id,
        string $paymentMethodType,
        bool $useParentMandate,
        bool $useParentCard,
        bool $expected
    ): void {
        $user = User::make([
            'parent_id' => $parentId,
            '_id' => $id,
            'use_parent_mandate' => $useParentMandate,
            'use_parent_card' => $useParentCard,
        ]);

        static::assertEquals($expected, $user->childIsUsingParentPaymentMethodType($paymentMethodType));
    }

    public function childIsUsingParentPaymentMethodTypeDataProvider(): array
    {
        return [
            'when is not child, then it returns false' => [
                'parentId' => null,
                'id' => static::TEST_ID,
                'paymentMethodType' => '',
                'useParentMandate' => false,
                'useParentCard' => false,
                'expected' => false,
            ],
            'when is child and paymentMethodType is FLEXIBLE, then it returns false' => [
                'parentId' => static::TEST_PARENT_ID,
                'id' => static::TEST_ID,
                'paymentMethodType' => PaymentMethodType::FLEXIBLE,
                'useParentMandate' => false,
                'useParentCard' => false,
                'expected' => false,
            ],
            'when is child, paymentMethodType is DIRECT_DEBIT and useParentMandate is false, then it returns false' => [
                'parentId' => static::TEST_PARENT_ID,
                'id' => static::TEST_ID,
                'paymentMethodType' => PaymentMethodType::DIRECT_DEBIT,
                'useParentMandate' => false,
                'useParentCard' => false,
                'expected' => false,
            ],
            'when is child, paymentMethodType is DIRECT_DEBIT and useParentMandate is true, then it returns true' => [
                'parentId' => static::TEST_PARENT_ID,
                'id' => static::TEST_ID,
                'paymentMethodType' => PaymentMethodType::DIRECT_DEBIT,
                'useParentMandate' => true,
                'useParentCard' => false,
                'expected' => true,
            ],
            'when is child, paymentMethodType is PAY_LATER and useParentCard is false, then it returns false' => [
                'parentId' => static::TEST_PARENT_ID,
                'id' => static::TEST_ID,
                'paymentMethodType' => PaymentMethodType::PAY_LATER,
                'useParentMandate' => false,
                'useParentCard' => false,
                'expected' => false,
            ],
            'when is child, paymentMethodType is PAY_LATER and useParentCard is true, then it returns true' => [
                'parentId' => static::TEST_PARENT_ID,
                'id' => static::TEST_ID,
                'paymentMethodType' => PaymentMethodType::PAY_LATER,
                'useParentMandate' => false,
                'useParentCard' => true,
                'expected' => true,
            ],
            'when is child, paymentMethodType is BANK_TRANSFER and useParentCard is true, then it returns true' => [
                'parentId' => static::TEST_PARENT_ID,
                'id' => static::TEST_ID,
                'paymentMethodType' => PaymentMethodType::BANK_TRANSFER,
                'useParentMandate' => false,
                'useParentCard' => true,
                'expected' => true,
            ],
            'when is child, paymentMethodType is COMPLIMENTARY and useParentCard is true, then it returns true' => [
                'parentId' => static::TEST_PARENT_ID,
                'id' => static::TEST_ID,
                'paymentMethodType' => PaymentMethodType::COMPLIMENTARY,
                'useParentMandate' => false,
                'useParentCard' => true,
                'expected' => true,
            ],
            'when is child, paymentMethodType is CARD and useParentCard is true, then it returns true' => [
                'parentId' => static::TEST_PARENT_ID,
                'id' => static::TEST_ID,
                'paymentMethodType' => PaymentMethodType::CARD,
                'useParentMandate' => false,
                'useParentCard' => true,
                'expected' => true,
            ],
            'when is child, paymentMethodType is WALLET and useParentCard is true, then it returns true' => [
                'parentId' => static::TEST_PARENT_ID,
                'id' => static::TEST_ID,
                'paymentMethodType' => PaymentMethodType::WALLET,
                'useParentMandate' => false,
                'useParentCard' => true,
                'expected' => true,
            ],
            'when is child, paymentMethodType is POS_TERMINAL and useParentCard is true, then it returns true' => [
                'parentId' => static::TEST_PARENT_ID,
                'id' => static::TEST_ID,
                'paymentMethodType' => PaymentMethodType::POS_TERMINAL,
                'useParentMandate' => false,
                'useParentCard' => true,
                'expected' => true,
            ],
        ];
    }

    /**
     * @dataProvider childShouldUseParentsAccountBalanceDataProvider
     * @param bool $useParentCard
     * @param bool $expected
     * @return void
     */
    public function testChildShouldUseParentsAccountBalance(bool $useParentCard, bool $expected): void
    {
        $user = User::make([
            'use_parent_card' => $useParentCard,
        ]);

        static::assertEquals($expected, $user->childShouldUseParentsAccountBalance());
    }

    public function childShouldUseParentsAccountBalanceDataProvider(): array
    {
        return [
            'when useParentCard is false, then it returns false' => [
                'useParentCard' => false,
                'expected' => false,
            ],
            'when useParentCard is true, then it returns true' => [
                'useParentCard' => true,
                'expected' => true,
            ],
        ];
    }

    /**
     * @dataProvider hasAccessToBranchDataProvider
     */
    public function testHasAccessToBranch(string $userId, string $branchId, bool $expected): void
    {
        $user = $this->usersRepository->getById($userId);

        self::assertEquals($expected, $user->hasAccessToBranch($branchId));
    }

    public function hasAccessToBranchDataProvider(): array
    {
        return [
            '1. When the user has branch_id as a string and we are checking the access to the same branch,
            Then it returns true' => [
                'userId' => '65f1d4df96b13fe3c376e311',
                'branchId' => '65f1d4f17938c94639b652c6',
                'expected' => true,
            ],
            '2. When the user has branch_id as an array with 1 element and we are checking the same branch,
            Then it returns true' => [
                'userId' => '65f1d50ae9ab5f1e77eb641c',
                'branchId' => '65f1d4f17938c94639b652c6',
                'expected' => true,
            ],
            '3. When the user has branch_id as a string and we are checking the access to another branch,
            Then it returns false' => [
                'userId' => '65f1d5294b3f1377379e46fa',
                'branchId' => '65f1d531a689fb464251c9a7',
                'expected' => false,
            ],
            '4. When the user has branch_id as an array with 3 elements and we are checking the access to one of it,
            Then it returns true' => [
                'userId' => '65f1d547d9a4a2d914362b65',
                'branchId' => '65f1d531a689fb464251c9a7',
                'expected' => true,
            ],
            '5. When the user has branch_id as an array with 3 elements and we are checking the access to none of it,
            Then it returns false' => [
                'userId' => '65f1d547d9a4a2d914362b65',
                'branchId' => '65f332cd80616dbf5248e957',
                'expected' => false,
            ],
            '6. When the user is an Admin and has branch_id as a string and we are checking the access to another branch,
            Then it returns true' => [
                'userId' => '65f36e10aebcf2dfbc23ec14',
                'branchId' => '65f1d4f17938c94639b652c6',
                'expected' => false,
            ],
            '7. When the user is an Admin and has branch_id as an array with 3 elements and we are checking the access to one of it,
            Then it returns false' => [
                'userId' => '65f968a64e89ebe7f6a5d382',
                'branchId' => '65f1d531a689fb464251c9a7',
                'expected' => true,
            ],
        ];
    }

    public function userIdsDataProvider(): array
    {
        return [
            ['', true],
            [' ', true],
            [new \MongoId(), true],
            ['guest', false],
            ['GUEST', false],
            ['[worker]', false],
            ['[WORKER]', false],
        ];
    }

    /**
     * @dataProvider userIdsDataProvider
     */
    public function testIsRealUser(string $userId, bool $expected): void
    {
        $this->assertEquals($expected, User::isRealUserId($userId));
    }

    /**
     * @dataProvider setsRestrictedProfileRulesDataProvider
     */
    public function testSetMemberRestrictedProfileRules(string $type, bool $isFfEnabled, bool $belongsToBranch,
        string $originBranchId, bool $restrictedProfile): void
    {
        $branchId = '673f74100e194aa8e016e4a7';

        $isMember = $type === 'MEMBER';
        $user = Mockery::mock(User::class)->makePartial();
        $user->shouldReceive('isMember')
            ->andReturn($isMember);

        $user->shouldReceive('type')
            ->andReturn($type);

        $user->shouldReceive('hasAccessToBranch')
            ->andReturn($belongsToBranch);

        $user->shouldReceive('originBranchId')
            ->andReturn($originBranchId);

        $user->setMemberRestrictedProfileRules($branchId, $isFfEnabled);
        if($isMember){
            $this->assertEquals(['restricted_profile' => $restrictedProfile], $user->get('profile_rules'));
        } else {
            $this->assertNull($user->get('profile_rules'));
        }
    }

    public function setsRestrictedProfileRulesDataProvider(): array
    {
        return [
            'When the user type is not MEMBER then profile_rules isnt set' => [
                'type' => 'ADMIN',
                'isFfEnabled' => true,
                'belongsToBranch' => true,
                'origin_branch_id' => '673f74100e194aa8e016e4a7',
                'restrictedProfile' => false,
            ],
            'When the user is an empty type then profile_rules isnt set' => [
                'type' => '',
                'isFfEnabled' => true,
                'belongsToBranch' => true,
                'origin_branch_id' => '673f74100e194aa8e016e4a7',
                'restrictedProfile' => false,
            ],
            'When the restrict profile rules setting is enabled
            and we attempt to access a member that belongs to the branch then it returns
            restricted_profile:false under profile_rules for that user' => [
                'type' => 'MEMBER',
                'isFfEnabled' => true,
                'belongsToBranch' => true,
                'origin_branch_id' => '673f74100e194aa8e016e4a7',
                'restrictedProfile' => false,
            ],
            'When the restrict profile rules setting is enabled
            and we attempt access a member that is a roaming member on the branch then it returns
            restricted_profile:true under profile_rules for that user' => [
                'type' => 'MEMBER',
                'isFfEnabled' => true,
                'belongsToBranch' => true,
                'origin_branch_id' => '673f574a164ac23958540070',
                'restrictedProfile' => true,
            ],
            'When the restrict profile rules setting is enabled
            and we to attempt access a member that is within the namespace but belongs to another
            branch then it returns restricted_profile:true under profile_rules for that user' => [
                'type' => 'MEMBER',
                'isFfEnabled' => true,
                'belongsToBranch' => false,
                'origin_branch_id' => '673f574a164ac23958540070',
                'restrictedProfile' => true,
            ],
            'When the restrict profile rules setting is disabled
            and we attempt to access a member that belongs to the branch then it returns
            restricted_profile:false under profile_rules for that user' => [
                'type' => 'MEMBER',
                'isFfEnabled' => false,
                'belongsToBranch' => true,
                'origin_branch_id' => '673f74100e194aa8e016e4a7',
                'restrictedProfile' => false,
            ],
            'When the restrict profile rules setting is disabled
            and we attempt access a member that is a roaming member on the branch then it returns
            restricted_profile:false under profile_rules for that user' => [
                'type' => 'MEMBER',
                'isFFEnabled' => false,
                'belongsToBranch' => true,
                'origin_branch_id' => '673f574a164ac23958540070',
                'restrictedProfile' => false,
            ],
            'When the restrict profile rules setting is disabled
            and we to attempt access a member that is within the namespace but belongs to another
            branch then it returns restricted_profile:true under profile_rules for that user' => [
                'type' => 'MEMBER',
                'isFfEnabled' => false,
                'belongsToBranch' => false,
                'origin_branch_id' => '673f574a164ac23958540070',
                'restrictedProfile' => true,
            ],
        ];
    }

    private function mockFetchParentModel(string $parentId, bool $expectedException): void
    {
        $usersRepository = Mockery::mock(UsersRepository::class);
        $usersRepository->shouldReceive('addCriteria')->andReturnSelf();

        if ($expectedException) {
            $usersRepository->shouldReceive('firstOrFail')
                ->withArgs(function (Closure $exceptionCallback) {
                    $exceptionCallback->call($this);
                    return true;
                });
        } else {
            $usersRepository->shouldReceive('firstOrFail')
                ->andReturn(User::make(['_id' => $parentId]))
                ->once();
        }

        app()->forgetInstance(UsersRepository::class);
        app()->instance(UsersRepository::class, $usersRepository);
    }
}
