<?php

use Glofox\Authentication\Exceptions\AuthenticationException;
use Glofox\Domain\Assets\Exceptions\InvalidImageException;
use Glofox\Domain\Assets\Http\Requests\UploadRequest;
use Glofox\Domain\Assets\Services\Uploader as AssetUploaderService;
use Illuminate\Http\JsonResponse;

/**
 * AssetsController class.
 *
 * @uses          AppController
 *
 * @property Asset $Asset
 * @property PushNotification $PushNotification
 * @property Client $Client
 * @property S3Component $S3
 * @property JWTComponent $JWT
 * @property Branch $Branch
 */
class AssetsController extends AppController
{
    /**
     * Name property.
     *
     * @var string 'Clients'
     */
    public $name = 'Assets';

    public $components = [
        'S3',
        'Utility',
        'JWT',
    ];

    public $uses = [
        'Asset',
        'PushNotification',
        'Client',
        'User',
    ];

    /** @var AssetUploaderService|mixed */
    private AssetUploaderService $assetUploaderService;

    public function __construct($request = null, $response = null)
    {
        parent::__construct($request, $response);

        $this->assetUploaderService = app()->make(AssetUploaderService::class);
    }

    /*******************************************************************************************
     *                                API 2.0 New Dashboard
     *****************************************************************************************
     *
     * @param $identifier
     *
     * @return string
     */

    /*!
       * Dispatcher for Model
       * @param  [type] $identifier [description]
       * @return [type]             [description]
       */
    public function dispatcher($identifier)
    {
        return parent::dispatch($identifier, $this->Asset);
    }

    /**
     * Upload any asset to AWS, used for images for the different models. It expects a field of name "Image",
     * if an ID is specified, then it will treat it as a CRUD model operation and attempt to store in
     * s3->glofox/environment/model...
     * if no id is specified then it will use the model as the name image and attempt to upload it to the
     * s3->glofox/assets/namespace/drawables as png,
     * if no model is specified then the image will be stored as the logo.png of the namespace for which this token
     * belongs in the multipart. Type can be crud (default) will be stored as default.png or 'profile' in which case
     * will be stored.
     *
     * @example https://www.glofoxlogin/upload/courses/123456
     *
     * @param UploadRequest $request
     * @return string|JsonResponse
     * @throws JsonException
     * @throws AuthenticationException
     * @throws UnsuccessfulOperation
     */
    public function upload(UploadRequest $request)
    {
        $allowedRoles = [
            UserType::SUPERADMIN,
            UserType::ADMIN,
            UserType::RECEPTIONIST,
            UserType::TRAINER,
            UserType::MEMBER,
        ];
        $user = $this->JWT->parseToken();
        $this->JWT->validateOrFail($allowedRoles, $user['namespace'], $user['branch_id']);

        $this->assetUploaderService
            ->setImage($request->image())
            ->setImageName($request->imageName())
            ->setModel($request->model())
            ->setId($request->id())
            ->setType($request->type());

        try {
            $this->assetUploaderService->validateImage();
        } catch (InvalidImageException $e) {
            return json_encode([
                'message' => $e->getMessage(),
                'message_code' => $e->getMessage(),
                'success' => false,
            ], JSON_THROW_ON_ERROR);
        }

        $result = $this->assetUploaderService->upload();

        return response()->json($result->getData(), $result->getCode());
    }

    /**
     * @param $type
     * @param $index
     *
     * @return CakeResponse|null
     */
    public function template($type, $index)
    {
        $region = env('REGION');
        $src = \sprintf('https://s3-%s.amazonaws.com/glofox/templates/%s/%s', $region, $type, $index);

        $image = file_get_contents($src);
        $this->autoRender = false;
        $this->response->type('png');
        header('Content-Type: image/png');
        $this->response->body($image);

        return $this->response;
    }

    /*!
     * Remove asset image from AWS
     * @param      [type]                   $code [description]
     * @return     [type]                         [description]
     */

    /**
     * @param $code
     *
     * @return JsonResponse
     */
    public function remove($code)
    {
        $user = $this->JWT->parseToken();
        $path = 'assets/' . $user['namespace'] . '/drawables/' . $code . '.png';
        $delete = $this->S3->deleteFile($path);

        return response()->json($delete);
    }

    /**
     * When GET  ->
     * Retrieve the branch color configuration for the user requesting it, if none, return 404 for resource
     * When POST ->
     * Save the colors to the branch_id of the token owner
     * $element defines the type of color to configure, by default (element undefined) we configure the mobile app colors
     * by specifying additional parameters then we configure colors for different elements such as iframe, in the case of the iframe
     * by storing null colors, it disables the iframe.
     *
     * @example    https://www.glofoxlogin/colors
     *
     * @param null $element
     *
     * @return JsonResponse [type]                   [description]
     */
    public function colors($element = null)
    {
        $user = $this->JWT->parseToken();
        //Set colors
        if (!$element) {
            if ('GET' === $_SERVER['REQUEST_METHOD']) {
                $findColors = $this->Asset->findColorsByNamespaceAndBranchId($user['namespace'], $user['branch_id']);
                if ($findColors) {
                    return response()->json(['colors' => $findColors['Asset']['colors']]);
                } else {
                    throw new NotFoundException();
                }
            } elseif ('POST' === $_SERVER['REQUEST_METHOD']) {
                return $this->saveColorsByNamespaceAndBranchId($user['namespace'], $user['branch_id']);
            }
        } elseif ('iframe' == $element) {
            $this->loadModel('Branch');
            $branch = $this->Branch->findById($user['branch_id']);
            if ('GET' === $_SERVER['REQUEST_METHOD']) {
                $findColors = $branch['Branch']['configuration']['iframe'];
                if ($findColors) {
                    return response()->json($findColors);
                } else {
                    throw new NotFoundException();
                }
            } elseif ('POST' === $_SERVER['REQUEST_METHOD']) {
                $data = json_decode(file_get_contents('php://input'), null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
                $branch['Branch']['configuration']['iframe'] = $data;
                $branch['Branch']['uses_iframe'] = isset($data);

                return response()->json($this->Branch->save($branch['Branch']));
            }
        }
    }

    /*!
     * Send review request to bakery
     * @internal
     * @deprecated
     * @link
     * @example    https://www.glofoxlogin/
     * @return     [type]                   [description]
     */

    /**
     * @return JsonResponse
     */
    public function review()
    {
        $data = json_decode(file_get_contents('php://input'), true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        $curl = curl_init();
        curl_setopt_array($curl, [
            CURLOPT_URL => 'http://bakery.glofox.com/review',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => json_encode($data, JSON_PARTIAL_OUTPUT_ON_ERROR),
            CURLOPT_HTTPHEADER => [
                'cache-control: no-cache',
                'content-type: application/json',
            ],
        ]);

        $response = curl_exec($curl);
        $response = json_decode($response, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        $err = curl_error($curl);
        curl_close($curl);

        return response()->json(['response' => $response, 'error' => $err]);
    }

    /*!
     * Retrieve the current mobile URL for iOS and Android
     * @return     [type]                   [description]
     */

    /**
     * @return JsonResponse
     */
    public function mobile()
    {
        $user = $this->JWT->parseToken();
        //Get client and bundle info
        $client = $this->Client->getByNamespace($user['namespace'])['Client'];
        $bundle = $this->Client->getByNamespace(str_replace('_', '', $client['bundles']['0']))['Client'];
        //Get iOS bundle and custom info
        $cIos = json_decode(
            file_get_contents('https://itunes.apple.com/lookup?bundleId=ie.zappy.glofox.' . $client['namespace']), null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR
        );
        $bIos = json_decode(
            file_get_contents('https://itunes.apple.com/lookup?bundleId=ie.zappy.' . $bundle['namespace']), null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR
        );
        //Get Android bundle and custom info
        $cDroid = 'https://play.google.com/store/apps/details?id=ie.zappy.fennec.glofox.' . $client['namespace'];
        $bDroid = 'https://play.google.com/store/apps/details?id=ie.zappy.fennec.oneapp_' . $bundle['namespace'];

        return response()->json(
            ['ios' => ['custom' => $cIos, 'bundle' => $bIos], 'android' => ['custom' => $cDroid, 'bundle' => $bDroid]]
        );
    }

    /******************************** REST WEBSERVICE ACTIONS  *******************************************/

    /**
     * Index method /clients.
     */
    public function settings_change_assets()
    {
        $this->layout = 'dashboard';
        $user = $this->JWT->getUser();
        $allowed_roles = ['admin', 'superadmin'];
        $this->JWT->validateOrFail($allowed_roles, $user['User']['namespace']);
        $namespace = $user['User']['namespace'];
        $branch_id = $user['User']['branch_id'];
        $changed_assets = [];
        $assets_config = Configure::read('assets_conventions');

        if ($this->request->is('post')) {
            foreach ($this->request->data['Asset'] as $asset_key => $asset_image) {
                if (!empty($asset_image['name']) && (0 == $asset_image['error'])) {
                    $name = $assets_config[$asset_key]['custom']['name'];
                    $path = $this->parse_asset_path(
                        $namespace,
                        $branch_id,
                        $assets_config[$asset_key]['custom']['path']
                    );
                    $web_path = $this->S3->getImageUrl($path . $name);

                    $this->S3->saveImage($asset_image, $path, $name);
                    $changed_assets[] = ['url' => $web_path, 'img_id' => $asset_key];
                }
            }

            if (!empty($changed_assets)) {
                $this->PushNotification->saveAssetRefreshNotification($branch_id, $changed_assets);
            }
        }

        $assets_list = $this->get_assets_convention($namespace, $branch_id);
        $this->set('assets_list', $assets_list);
        $this->set('page_title', 'Update Assets');
        $this->set('page_action', 'Update');

        return response()->json(['success' => true]);
    }

    /**
     * [findByNamespaceAndBranchId description].
     *
     * @param $namespace
     * @param $branch_id
     *
     * @return JsonResponse [type]            [description]
     *
     * @internal param $ [type] $namespace [description]
     * @internal param $ [type] $branch_id [description]
     */
    public function findByNamespaceAndBranchId($namespace, $branch_id)
    {
        // Validating Namespace is not empty
        if (empty($namespace)) {
            return response()->json(['success' => false, 'message' => __('You must provide a namespace')]);
        }
        // Validating Branch ID is not empty
        if (empty($branch_id)) {
            return response()->json(['success' => false, 'message' => __('You must provide a branch id')]);
        }
        // Validating Branch ID is a valid mongodb id
        $is_valid_mongoid = $this->Utility->is_valid_mongoid($branch_id);
        if (empty($is_valid_mongoid)) {
            return response()->json(['success' => false, 'message' => __('Invalid Branch Id')]);
        }

        $assets_convention = $this->get_assets_convention($namespace, $branch_id);
        $assets = [];

        foreach ($assets_convention as $key => $asset) {
            $assets[] = [
                'img_id' => $key,
                'url' => $this->S3->getImageUrl($asset['path'] . $asset['name']),
                'tooltip' => $asset['tooltip'],
            ];
        }

        return response()->json($assets);
    }

    /**
     * [findColorsByNamespaceAndBranchId description].
     *
     * @param $namespace
     * @param $branch_id
     *
     * @return JsonResponse [type]            [description]
     *
     * @internal param $ [type] $namespace [description]
     * @internal param $ [type] $branch_id [description]
     */
    public function findColorsByNamespaceAndBranchId($namespace, $branch_id)
    {
        // Validating Namespace is not empty
        if (empty($namespace)) {
            return response()->json(['success' => false, 'message' => __('You must provide a namespace')]);
        }
        // Validating Branch ID is not empty
        if (empty($branch_id)) {
            return response()->json(['success' => false, 'message' => __('You must provide a branch id')]);
        }
        // Validating Branch ID is a valid mongodb id
        $is_valid_mongoid = $this->Utility->is_valid_mongoid($branch_id);
        if (empty($is_valid_mongoid)) {
            return response()->json(['success' => false, 'message' => __('Invalid Branch Id')]);
        }

        $findColors = $this->Asset->findColorsByNamespaceAndBranchId($namespace, $branch_id);

        if ($findColors) {
            return response()->json(['success' => true, 'colors' => $findColors['Asset']['colors']]);
        } else {
            return response()->json(
                ['success' => false, 'message' => 'There are no colors configured for the given branch.']
            );
        }
    }

    /**
     * [saveColorsByNamespaceAndBranchId description].
     *
     * @param $namespace
     * @param $branch_id
     *
     * @return JsonResponse [type]            [description]
     *
     * @internal param $ [type] $namespace [description]
     * @internal param $ [type] $branch_id [description]
     */
    public function saveColorsByNamespaceAndBranchId($namespace, $branch_id)
    {
        // Validating Namespace is not empty
        if (empty($namespace)) {
            return response()->json(['success' => false, 'message' => __('You must provide a namespace')]);
        }
        // Validating Branch ID is not empty
        if (empty($branch_id)) {
            return response()->json(['success' => false, 'message' => __('You must provide a branch id')]);
        }
        // Validating Branch ID is a valid mongodb id
        $is_valid_mongoid = $this->Utility->is_valid_mongoid($branch_id);
        if (empty($is_valid_mongoid)) {
            return response()->json(['success' => false, 'message' => __('Invalid Branch Id')]);
        }

        $findColors = $this->Asset->findColorsByNamespaceAndBranchId($namespace, $branch_id);

        $data = file_get_contents('php://input');
        if (empty($data)) {
            return response()->json(
                ['success' => false, 'message' => 'The color object is empty or is not a valid json.']
            );
        }

        $input_colors = json_decode(file_get_contents('php://input'), true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        if (empty($input_colors)) {
            return response()->json(
                ['success' => false, 'message' => 'The color object is empty or is not a valid json.']
            );
        }

        if ($findColors) {
            $this->Asset->read(null, $findColors['Asset']['_id']);
            $asset = $findColors['Asset'];
            $asset['colors'] = $input_colors['colors'];
        } else {
            $this->Asset->create();
            $asset = ['namespace' => $namespace, 'branch_id' => $branch_id, 'colors' => null];
        }

        $save_asset = $this->Asset->save($asset);

        if ($save_asset) {
            return response()->json(
                [
                    'success' => true,
                    'meesage' => 'The colors configuration has been successfully saved',
                    'colors' => $save_asset['Asset']['colors']
                ]
            );
        } else {
            return response()->json(
                ['success' => false, 'message' => 'An error has ocurred while saving the colors configuration.']
            );
        }
    }

    /**
     * @return JsonResponse
     */
    public function testQueueComponent()
    {
        $this->response->type('json');
        $this->autoRender = false;

        $this->Queue = $this->Components->load('Queue');

        return response()->json($this->Queue->sendMessage());
    }

    /**
     * [get_assets_convention description].
     *
     * @param $namespace
     * @param $branch_id
     *
     * @return array [type]            [description]
     *
     * @internal param $ [type] $namespace [description]
     * @internal param $ [type] $branch_id [description]
     */
    private function get_assets_convention($namespace, $branch_id)
    {
        $assets_config = Configure::read('assets_conventions');
        $assets_list = [];

        foreach ($assets_config as $key => &$asset_config) {
            $asset_config['default']['path'] = $this->parse_asset_path(
                $namespace,
                $branch_id,
                $asset_config['default']['path']
            );
            $asset_config['custom']['path'] = $this->parse_asset_path(
                $namespace,
                $branch_id,
                $asset_config['custom']['path']
            );

            $asset_custom_img_url = $asset_config['custom']['path'] . $asset_config['custom']['name'];

            $assets_list[$key]['label'] = $asset_config['label'];
            $assets_list[$key]['tooltip'] = $asset_config['tooltip'];

            $asset_custom_img_url = !empty($asset_custom_img_url) ? $this->S3->getImageUrl($asset_custom_img_url) : '';

            if ($this->S3->remoteFileExists($asset_custom_img_url) && !empty($asset_custom_img_url)) {
                $assets_list[$key]['path'] = $asset_config['custom']['path'];
                $assets_list[$key]['name'] = $asset_config['custom']['name'];
            } else {
                $assets_list[$key]['path'] = $asset_config['default']['path'];
                $assets_list[$key]['name'] = $asset_config['default']['name'];
            }
        }

        return $assets_list;
    }

    /**
     * [parse_asset_path description].
     *
     * @param $namespace
     * @param $branchId
     * @param $assetPath
     *
     * @return array|mixed [type]             [description]
     *
     * @internal param $ [type] $namespace  [description]
     * @internal param $ [type] $branchId  [description]
     * @internal param $ [type] $assetPath [description]
     */
    private function parse_asset_path($namespace, $branchId, $assetPath)
    {
        return str_replace([':namespace', ':branch_id'], [$namespace, $branchId], $assetPath);
    }
}
