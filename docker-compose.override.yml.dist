# This file is only necessary if you need any custom config, otherwise you don't need it
services:
  app:
    # Only necessary for Mac - m1 laptop
    platform: linux/amd64
    # Only necessary for Mac - m2 laptop
    platform: linux/arm64
    # Only necessary if you want the service to automatically start on startup
    restart: always

  db:
    # Only necessary for Mac - m1 laptop
    platform: linux/amd64
    # Only necessary for Mac - m2 laptop
    platform: linux/arm64
    # Only necessary if you want persistent databases. Is recommended to create the local folder in advance, otherwise
    # the folder will possibly be created after the service starts, so the container builder will fail
    volumes:
      - {{absolute local path}}:/data/db
    # Only necessary if you want the service to automatically start on startup
    restart: always
    # This is required to mount volumes with Docker Colima, more info https://www.notion.so/glofox/Runbook-Install-Docker-Desktop-Alternatives-9c5b23a599ab40949edeaaea9d8acfe9?pvs=4#7560260e3f44481292b16399e2fb1269
    user: "{userId}:{groupId}"

  cache:
    # Only necessary for Mac - m1/m2 laptop
    platform: linux/amd64
    # Only necessary if you want the service to automatically start on startup
    restart: always
    # This is required to mount volumes with Docker Colima, more info https://www.notion.so/glofox/Runbook-Install-Docker-Desktop-Alternatives-9c5b23a599ab40949edeaaea9d8acfe9?pvs=4#7560260e3f44481292b16399e2fb1269
    user: "{userId}:{groupId}"
