<?php

namespace Glofox\Infrastructure\ElectronicAgreements;

use Glofox\Domain\ElectronicAgreements\Models\Agreement;
use Glofox\Domain\ElectronicAgreements\Models\Document;
use Glofox\Domain\ElectronicAgreements\Models\DocumentVersion;
use Glofox\Domain\ElectronicAgreements\Requests\CreateAgreementRequest;
use Glofox\Domain\ElectronicAgreements\Requests\CreateDocumentVersionRequest;
use Glofox\Domain\ElectronicAgreements\Requests\DeleteAgreementRequest;
use Glofox\Domain\ElectronicAgreements\Requests\RedirectAgreementRequest;
use Glofox\Domain\ElectronicAgreements\Services\ElectronicAgreementsServiceInterface;
use Glofox\Domain\ElectronicAgreements\Transformers\FromCreateAgreementRequestToArray;
use Glofox\Domain\ElectronicAgreements\Transformers\FromCreateDocumentVersionRequestToArray;
use Glofox\Domain\ElectronicAgreements\Transformers\FromGenerateURLRequestToArray;
use Glofox\Domain\ElectronicAgreements\Transformers\FromRedirectAgreementRequestToArray;
use Glofox\Domain\ElectronicAgreements\Trigger;
use Glofox\Domain\Experiments\Services\ValidateExperimentAvailability;
use Glofox\Domain\Terms\Type;
use Glofox\Domain\Users\Models\User;
use Glofox\Infrastructure\Exception\HttpClientResponseException;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\BadResponseException;
use League\Fractal\Manager as FractalManager;
use League\Fractal\Resource\Collection as ResourceCollection;
use Psr\Log\LoggerInterface;

class ElectronicAgreementsHttpClient implements ElectronicAgreementsServiceInterface
{
    public const CREATE_DOCUMENT_URI = 'electronic-agreements/v1/studios/:studioID/documents';
    public const LIST_DOCUMENTS_URI = 'electronic-agreements/v1/studios/:studioID/documents';
    public const LIST_DOCUMENT_URI = 'electronic-agreements/v1/studios/:studioID/documents/:documentID';
    public const LIST_AGREEMENTS_URI = 'electronic-agreements/v1/studios/:studioID/members/:memberID/agreements';
    public const LIST_AGREEMENTS_FOR_MEMBERS_URI = 'electronic-agreements/v1/studios/:studioID/agreements';
    public const GET_LATEST_DOCUMENT_VERSION_URI = 'electronic-agreements/v1/studios/:studioID/documents/:documentID/version';
    public const GET_DOCUMENT_VERSION_URI = 'electronic-agreements/v1/studios/:studioID/documents/:documentID/version/:versionID';
    public const CREATE_DOCUMENT_VERSION_URI = 'electronic-agreements/v1/studios/:studioID/documents/:documentID/version';
    public const CREATE_AGREEMENT_URI = 'electronic-agreements/v1/studios/:studioID/members/:memberID/agreements';
    public const DELETE_AGREEMENT_URI = 'electronic-agreements/v1/studios/:studioID/members/:memberID/agreements/:agreementID';
    public const GENERATE_AGREEMENT_URL_URI = 'electronic-agreements/v1/studios/:studioID/members/:memberID/agreements/:agreementID/url';
    public const REDIRECT_AGREEMENT_URI = 'electronic-agreements/v1/redirect';
    public const GET_AGREEMENT_BY_METADATA_URI = 'electronic-agreements/v1/metadata/:resourceID/members/:memberID';
    public const HAS_OUTSTANDING_AGREEMENT_URI = 'electronic-agreements/v1/studios/:studioID/members/:memberID/outstanding';

    private LoggerInterface $logger;

    private ValidateExperimentAvailability $experimentService;

    private Client $client;

    private FractalManager $fractalManager;

    /**
     * ElectronicAgreementsHttpClient constructor.
     * @param LoggerInterface $logger
     * @param ValidateExperimentAvailability $experimentService
     * @param Client $client
     */
    public function __construct(
        LoggerInterface $logger,
        ValidateExperimentAvailability $experimentService,
        Client $client
    ) {
        $this->logger = $logger;
        $this->experimentService = $experimentService;
        $this->client = $client;
        $this->fractalManager = app()->make(FractalManager::class);
    }

    /**
     * @param string $branchId
     * @param User|null $user
     * @return bool
     */
    public function isElectronicAgreementsEnabled(string $branchId, User $user = null): bool
    {
        return $this->experimentService->validate($branchId, 'electronic-agreements', $user);
    }

    /**
     * @param string $studioId
     * @param array $includes
     * @return array
     * @throws HttpClientResponseException
     */
    public function findDocuments(string $studioId, array $includes = []): array
    {
        $uri = static::LIST_DOCUMENTS_URI;
        $uri = str_replace(':studioID', $studioId, $uri);
        $uri .= sprintf('?%s', http_build_query([
            'include' => implode(',', $includes),
        ]));

        $res = $this->getJSON($uri);
        $documents = [];
        if (isset($res['documents'])) {
            foreach ($res['documents'] as $document) {
                $documents[] = new Document($document, null);
            }
        }

        return $documents;
    }

    /**
     * @param string $studioId
     * @param Trigger $trigger
     * @param array $includes
     * @return Document|null
     * @throws HttpClientResponseException
     */
    public function findDocumentByTrigger(string $studioId, Trigger $trigger, array $includes = []): ?Document
    {
        $uri = static::LIST_DOCUMENTS_URI;
        $uri = str_replace(':studioID', $studioId, $uri);
        $uri .= sprintf('?%s', http_build_query([
            'trigger' => $trigger->getValue(),
            'include' => implode(',', $includes),
        ]));

        $res = $this->getJSON($uri);

        if (isset($res['documents'])) {
            foreach ($res['documents'] as $doc) {
                return new Document($doc);
            }
        }

        return null;
    }

    /**
     * @param string $studioId
     * @param string $documentId
     * @param array $includes
     * @return Document|null
     * @throws HttpClientResponseException
     */
    public function findDocument(string $studioId, string $documentId, array $includes = []): ?Document
    {
        $uri = static::LIST_DOCUMENT_URI;
        $uri = str_replace(':studioID', $studioId, $uri);
        $uri = str_replace(':documentID', $documentId, $uri);
        $uri .= sprintf('?%s', http_build_query([
            'include' => implode(',', $includes),
        ]));

        $res = $this->getJSON($uri);

        return new Document($res);
    }

    /**
     * @param CreateDocumentVersionRequest $data
     * @return Document
     * @throws HttpClientResponseException
     */
    public function createDocument(CreateDocumentVersionRequest $data): Document
    {
        $uri = static::CREATE_DOCUMENT_URI;
        $uri = str_replace(':studioID', $data->getStudioID(), $uri);

        $body = new ResourceCollection($data, new FromCreateDocumentVersionRequestToArray(false));

        $res = $this->postJSON($uri, $body);

        return new Document($res);
    }

    /**
     * @param string $studioId
     * @param string $documentId
     * @param string $versionId
     * @return DocumentVersion|null
     * @throws HttpClientResponseException
     */
    public function getDocumentVersion(string $studioId, string $documentId, string $versionId): ?DocumentVersion
    {
        $uri = static::GET_DOCUMENT_VERSION_URI;
        $uri = str_replace(':studioID', $studioId, $uri);
        $uri = str_replace(':documentID', $documentId, $uri);
        $uri = str_replace(':versionID', $versionId, $uri);
        $res = $this->getJSON($uri);

        return new DocumentVersion($res);
    }

    /**
     * @param string $studioId
     * @param string $documentId
     * @return DocumentVersion|null
     * @throws HttpClientResponseException
     */
    public function getLatestDocumentVersion(string $studioId, string $documentId): ?DocumentVersion
    {
        $uri = static::GET_LATEST_DOCUMENT_VERSION_URI;
        $uri = str_replace(':studioID', $studioId, $uri);
        $uri = str_replace(':documentID', $documentId, $uri);
        $res = $this->getJSON($uri);

        return new DocumentVersion($res);
    }

    /**
     * @param CreateDocumentVersionRequest $data
     * @return DocumentVersion
     * @throws HttpClientResponseException
     */
    public function createDocumentVersion(CreateDocumentVersionRequest $data): DocumentVersion
    {
        $uri = static::CREATE_DOCUMENT_VERSION_URI;
        $uri = str_replace(':studioID', $data->getStudioID(), $uri);
        $uri = str_replace(':documentID', $data->getDocumentID(), $uri);

        $body = new ResourceCollection($data, new FromCreateDocumentVersionRequestToArray(true));

        $res = $this->postJSON($uri, $body);

        return new DocumentVersion($res);
    }

    /**
     * @param CreateAgreementRequest $data
     * @return Agreement
     * @throws HttpClientResponseException
     */
    public function createAgreement(CreateAgreementRequest $data): Agreement
    {
        $uri = static::CREATE_AGREEMENT_URI;
        $uri = str_replace(':studioID', $data->getStudioID(), $uri);
        $uri = str_replace(':memberID', $data->getMemberId(), $uri);

        $body = new ResourceCollection($data, new FromCreateAgreementRequestToArray());

        $res = $this->postJSON($uri, $body);

        return new Agreement($res);
    }

    /**
     * @throws HttpClientResponseException
     */
    public function deleteAgreement(DeleteAgreementRequest $data): bool
    {
        $uri = static::DELETE_AGREEMENT_URI;
        $uri = str_replace(':studioID', $data->getStudioID(), $uri);
        $uri = str_replace(':memberID', $data->getMemberId(), $uri);
        $uri = str_replace(':agreementID', $data->getAgreementId(), $uri);

        $res = $this->deleteJSON($uri);

        return $res["success"];
    }

    /**
     * @param string $studioId
     * @param string $memberId
     * @param bool $hasMembership
     * @param Trigger|null $trigger
     * @return array
     * @throws HttpClientResponseException
     */
    public function listAgreements(
        string $studioId,
        string $memberId,
        bool $hasMembership = false,
        Trigger $trigger = null
    ): array {
        $uri = static::LIST_AGREEMENTS_URI;
        $uri = str_replace(':studioID', $studioId, $uri);
        $uri = str_replace(':memberID', $memberId, $uri);

        $query = [
            'hasActiveMembership' => $hasMembership ? '1' : '0',
        ];

        if (!empty($trigger)) {
            $query['trigger'] = $trigger->getValue();
        }

        $uri .= sprintf('?%s', http_build_query($query));

        $res = $this->getJSON($uri);
        $agreements = [];
        if (isset($res['agreements'])) {
            foreach ($res['agreements'] as $agreement) {
                $agreements[] = new Agreement($agreement);
            }
        }

        return $agreements;
    }

    /**
     * @param string $studioId
     * @param array $memberIds
     * @param array $memberHasActiveMemberships
     * @return array
     * @throws HttpClientResponseException
     */
    public function listAgreementsForMembers(
        string $studioId,
        array $memberIds,
        array $memberHasActiveMemberships
    ): array {
        $haveActiveMemberships = [];
        foreach ($memberHasActiveMemberships as $memberHasActiveMembership) {
            $haveActiveMemberships[] = $memberHasActiveMembership ? '1' : '0';
        }

        $uri = static::LIST_AGREEMENTS_FOR_MEMBERS_URI;
        $uri = str_replace(':studioID', $studioId, $uri);
        $uri .= sprintf('?%s', http_build_query([
            'members' => implode(',', $memberIds),
            'haveActiveMemberships' => implode(',', $haveActiveMemberships),
        ]));

        $res = $this->getJSON($uri);

        $groupedAgreements = [];
        if (!isset($res['agreements'])) {
            return $groupedAgreements;
        }

        foreach ($res['agreements'] as $memberId => $agreementList) {
            $agreements = [];
            foreach ($agreementList as $agreement) {
                $agreements[] = new Agreement($agreement);
            }
            $groupedAgreements[$memberId] = $agreements;
        }

        return $groupedAgreements;
    }

    /**
     * @param Agreement $agreement
     * @param string $email
     * @return Agreement
     * @throws HttpClientResponseException
     */
    public function generateUrl(Agreement $agreement, string $email): Agreement
    {
        $uri = static::GENERATE_AGREEMENT_URL_URI;
        $uri = str_replace(':studioID', $agreement->studioId(), $uri);
        $uri = str_replace(':memberID', $agreement->memberId(), $uri);
        $uri = str_replace(':agreementID', $agreement->id(), $uri);

        $body = new ResourceCollection($email, new FromGenerateURLRequestToArray());

        $res = $this->postJSON($uri, $body);

        return new Agreement($res);
    }

    /**
     * @param RedirectAgreementRequest $request
     * @return string
     * @throws HttpClientResponseException
     */
    public function agreementRedirectUrl(RedirectAgreementRequest $request): string
    {
        $body = new ResourceCollection($request, new FromRedirectAgreementRequestToArray());

        $res = $this->postJSON(self::REDIRECT_AGREEMENT_URI, $body);

        return $res['url'];
    }

    /**
     * @param string $resourceId
     * @param string $memberId
     * @return array
     * @throws HttpClientResponseException
     */
    public function findAgreementsByMetadata(string $resourceId, string $memberId): array
    {
        $uri = self::GET_AGREEMENT_BY_METADATA_URI;
        $uri = str_replace(':resourceID', $resourceId, $uri);
        $uri = str_replace(':memberID', $memberId, $uri);

        $res = $this->getJSON($uri);

        $agreements = [];
        if (isset($res['agreements'])) {
            foreach ($res['agreements'] as $agreement) {
                $agreements[] = new Agreement($agreement);
            }
        }

        return $agreements;
    }

    /**
     * @param string $studioId
     * @param string $memberId
     * @return bool
     * @throws HttpClientResponseException
     */
    public function hasOutstandingAgreements(string $studioId, string $memberId, string $memberType): bool
    {
        $uri = static::HAS_OUTSTANDING_AGREEMENT_URI;
        $uri = str_replace(':studioID', $studioId, $uri);
        $uri = str_replace(':memberID', $memberId, $uri);
        $uri .= sprintf('?%s', http_build_query(['memberType' => $memberType]));

        $res = $this->getJSON($uri);

        return $res["has_outstanding_agreements"];
    }

    /**
     * @param string $type
     * @return bool
     */
    public function isSupportedType(string $type): bool
    {
        return $type == Type::WAIVER || $type == Type::MEMBERPURCHASE;
    }

    /**
     * @param string $uri
     * @return mixed
     * @throws HttpClientResponseException
     */
    private function deleteJSON(string $uri)
    {
        $this->logger->info(sprintf('Electronic Agreements requesting %s...', $uri));
        $body = '{
            "success":false
        }';
        try {
            $this->client->delete($uri, [
                'verify' => false,
                'headers' => [
                    'Content-Type' => 'application/json',
                ],
            ]);
            $body = '{
                "success":true
            }';
        } catch (BadResponseException $exception) {
            $this->handleException($exception);
        }

        return json_decode($body, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    /**
     * @param string $uri
     * @return mixed
     * @throws HttpClientResponseException
     */
    private function getJSON(string $uri)
    {
        $this->logger->info(sprintf('Electronic Agreements requesting %s...', $uri));
        $body = [];

        try {
            $res = $this->client->get($uri, [
                'verify' => false,
                'headers' => [
                    'Content-Type' => 'application/json',
                ],
            ]);
            $body = $res->getBody()->getContents();
        } catch (BadResponseException $exception) {
            $this->handleException($exception);
        }


        return json_decode($body, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    /**
     * @param string $uri
     * @param ResourceCollection|null $body
     * @return mixed
     * @throws HttpClientResponseException
     */
    private function postJSON(string $uri, ?ResourceCollection $body)
    {
        $payload = '';
        if (!empty($body)) {
            $payload = json_encode($body->getTransformer()->transform($body->getData()), JSON_PARTIAL_OUTPUT_ON_ERROR);
        }

        $this->logger->info(sprintf('Electronic Agreements posting %s', $uri));

        try {
            $res = $this->client->post($uri, [
                'verify' => false,
                'headers' => [
                    'Content-Type' => 'application/json',
                ],
                'body' => $payload,
            ]);
            $body = $res->getBody()->getContents();
        } catch (BadResponseException $exception) {
            $this->handleException($exception);
        }

        return json_decode($body, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    /**
     * @param BadResponseException $exception
     * @throws HttpClientResponseException
     */
    private function handleException(BadResponseException $exception)
    {
        $body = json_decode(
            $exception->getResponse()->getBody()->getContents(),
            true,
            512,
            JSON_PARTIAL_OUTPUT_ON_ERROR
        );

        $this->logger->info(sprintf(
            'Electronic Agreements received error: %d %s',
            $exception->getCode(),
            $body['message'] ?? '(no body)'
        ));
        throw new HttpClientResponseException($exception->getCode(), $body['code'] ?? $body['message'] ?? '');
    }
}
