<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\Bookings\Events;

use Glofox\Domain\Bookings\Events\ReservationWasLinkedToEvent;
use Glofox\Domain\Bookings\Exceptions\BookingNotReservationException;
use Glofox\Domain\Bookings\Models\Booking;
use Glofox\Domain\Bookings\Status;
use Glofox\Domain\Events\Models\Event;

\App::import('Test/Case', 'GlofoxTestCase');

final class ReservationWasLinkedToEventTest extends \GlofoxTestCase
{
    public $fixtures = [];

    public function testNotReservation(): void
    {
        $this->setExpectedException(BookingNotReservationException::class);

        new ReservationWasLinkedToEvent(
            new Booking([
                '_id' => 'booking-123',
                'status' => Status::BOOKED,
            ]),
            new Event(),
        );
    }
}
