<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Http\Requests\Middlewares;

use Glofox\Domain\Users\Exceptions\MongoDbInjectionException;
use Glofox\Http\Requests\Middlewares\DataSanitizer;
use Glofox\Request;
use Psr\Log\LoggerInterface;
use ReflectionClass;

\App::import('Test/Case', 'GlofoxTestCase');

class DataSanitizerTest extends \GlofoxTestCase
{
    public $fixtures = [];
    private DataSanitizer $dataSanitizer;

    public function setUp(): void
    {
        parent::setUp();
        $this->logger = $this->createMock(LoggerInterface::class);
        $this->setDataSanitizer();
    }

    public function tearDown(): void
    {
        parent::tearDown();
        \Mockery::close();
    }

    public function requestDataProvider(): array
    {
        return [
            'GET with possibleMongoDbInjection. Should log in CW and throw an error' => [
                'method' => 'GET',
                'queryOrPayload' => ['$eq' => 'injection'],
                'expectedLogCount' => 1,
                'expectedException' => MongoDbInjectionException::class,
                'expectedExceptionMessage' => 'Invalid parameter exception',
            ],
            'GET without possibleMongoDbInjection. Should not log in CW and not throw an error' => [
                'method' => 'GET',
                'queryOrPayload' => ['foo' => 'bar'],
                'expectedLogCount' => 0,
                'expectedException' => null,
                'expectedExceptionMessage' => null,
            ],
            'POST with possibleMongoDbInjection. Should log in CW and throw an error' => [
                'method' => 'POST',
                'queryOrPayload' => ['$ne' => 'test'],
                'expectedLogCount' => 1,
                'expectedException' => MongoDbInjectionException::class,
                'expectedExceptionMessage' => 'Invalid parameter exception',
            ],
            'POST without possibleMongoDbInjection. Should not log in CW and not throw an error' => [
                'method' => 'POST',
                'queryOrPayload' => ['key' => 'value'],
                'expectedLogCount' => 0,
                'expectedException' => null,
                'expectedExceptionMessage' => null,
            ],
        ];
    }

    /**
     * @dataProvider requestDataProvider
     */
    public function testHandleRequest(
        string $method,
        array $queryOrPayload,
        int $expectedLogCount,
        ?string $expectedException,
        ?string $expectedExceptionMessage
    ): void {
        $request = Request::create(
            '/test',
            $method,
            $method === 'GET' ? $queryOrPayload : [],
            [],
            [],
            $method === 'POST' ? ['CONTENT_TYPE' => 'application/json'] : [],
            $method === 'POST' ? json_encode($queryOrPayload, JSON_THROW_ON_ERROR) : null
        );
        $next = fn($req) => $req;

        if ($expectedLogCount > 0) {
            $this->logger->expects($this->once())
                ->method('info')
                ->with('possible mongo operator injection detected');
        } else {
            $this->logger->expects($this->never())
                ->method('info');
        }

        if ($expectedException) {
            $this->expectExceptionMessage($expectedExceptionMessage);
        }

        $response = $this->dataSanitizer->handle($request, $next);

        if (!$expectedException) {
            $this->assertEquals($request, $response);
        }
    }

    private function setDataSanitizer(): void
    {
        $dataSanitizer = new DataSanitizer($this->logger);
        $reflection = new ReflectionClass($dataSanitizer);
        $property = $reflection->getProperty('mongoInjectionEnabled');
        $property->setAccessible(true);
        $property->setValue($dataSanitizer, true);
        $this->dataSanitizer = $dataSanitizer;
    }
}
