<?php

namespace Glofox\Payments\Providers\Gateway\Merchants\DataModellers;

use Carbon\Carbon;
use Glofox\Domain\Branches\Models\Branch;
use Glofox\Domain\PaymentProviders\Models\PaymentProvider;
use Glofox\Payments\Util;
use Illuminate\Support\Collection;

/**
 * Class StripeCustomDataModeller.
 */
class StripeCustomDataModeller implements DataModellerContract
{

    private ?\Glofox\Payments\Providers\Gateway\Merchants\DataModellers\StripeCustomLegacyOnboardingDataModeller $legacyDataModeller = null;
    private ?\Glofox\Payments\Providers\Gateway\Merchants\DataModellers\StripeCustomHostedOnboardingDataModeller $hostedOnboardingDataModeller = null;

    function __construct()
    {
        $this->legacyDataModeller = new StripeCustomLegacyOnboardingDataModeller();
        $this->hostedOnboardingDataModeller = new StripeCustomHostedOnboardingDataModeller();
    }

    public function modelForCreating(PaymentProvider $paymentProvider, Branch $branch, Collection $payload): Collection
    {
        return $this->getDataModeller($paymentProvider, $branch)
            ->modelForCreating($paymentProvider, $branch, $payload);
    }

    public function modelForUpdating(PaymentProvider $paymentProvider, Branch $branch, Collection $payload): Collection
    {
        return $this->getDataModeller($paymentProvider, $branch)
            ->modelForUpdating($paymentProvider, $branch, $payload);
    }

    public function modelForRegisterUrl(PaymentProvider $paymentProvider, Branch $branch): Collection
    {
        return $this->getDataModeller($paymentProvider, $branch)
            ->modelForRegisterUrl($paymentProvider, $branch);
    }

    private function getDataModeller(PaymentProvider $paymentProvider, Branch $branch): DataModellerContract
    {
        $provider = $paymentProvider->availableCountries()
            ->findByCountryCode($branch->address()->countryCode());

        if (empty($provider)) {
            return $this->hostedOnboardingDataModeller;
        }

        $isLegacy = $provider->isLegacyOnboarding();

        return $isLegacy ? $this->legacyDataModeller : $this->hostedOnboardingDataModeller;
    }
}
