<?php

class ProductFixture extends CakeTestFixture
{
    public $import = ['model' => 'Product'];

    public function init()
    {
        $this->records = [
            [
                '_id' => new MongoId('5b44ae18b13d95b5f633fd06'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'categories' => [],
                'name' => 'Edited Juice',
                'description' => 'There is no Cucumber in the juice',
                'private' => false,
                'presentations' => [
                    [
                        'id' => 1_506_964_157_657,
                        'retail_price' => 10,
                        'stock' => 10,
                        'wholesale_price' => 5,
                        'description' => 'Cucumber with Grapefruit',
                    ],
                ],
            ],
            [
                '_id' => new MongoId('5bacf781c8d7be0a03be0650'),
                'branch_id' => '5addc25383266f65abf515c4',
                'namespace' => 'classpass_integrated',
                'name' => 'Whey Protein',
                'presentations' => [
                    [
                        'id' => 1_506_964_157_658,
                        'stock' => 16,
                        'retail_price' => 17.49,
                        'description' => 'Chocolate 908g',
                    ],
                    [
                        'id' => 1_506_964_157_659,
                        'stock' => 20,
                        'retail_price' => 27.59,
                        'description' => 'Vanilla 1000g',
                    ],
                    [
                        'id' => 1_506_964_157_650,
                        'stock' => 4,
                        'retail_price' => 32.29,
                        'description' => 'Strawberry 908g',
                    ],
                ],
            ],
            [
                '_id' => new MongoId('6261051cbfee9e51e32e54a1'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'name' => 'Water',
                'presentations' => [
                    [
                        'id' => 1_506_964_157_789,
                        'stock' => 1,
                        'retail_price' => 1.50,
                        'description' => 'Just water',
                    ],
                ],
            ],
            [
                '_id' => new MongoId('62852a22a9a2bb677917b383'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'categories' => [],
                'name' => 'Water Bottle',
                'description' => 'Pure Water',
                'private' => false,
                'presentations' => [
                    [
                        'id' => 1_506_964_157_790,
                        'retail_price' => 0,
                        'stock' => 10,
                        'wholesale_price' => 0,
                        'description' => 'Pure Water makes you feel good',
                    ],
                ],
            ],
            ...$this->addProductsForViewTesting(),
        ];
        parent::init();
    }

    private function addProductsForViewTesting(): array
    {
        return [
            [
                '_id' => new MongoId('63e67b9616c7a30aebe7896d'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'categories' => ['62a9b7fd18d1d4d010e1110a', '62a9b7fd18d1d4d010e11108'],
                'name' => 'Sweet Water Bottle',
                'description' => 'Sweet Water',
                'private' => false,
                'presentations' => [
                    [
                        'id' => "1506964157790",
                        'retail_price' => "15.67",
                        'stock' => "10",
                        'wholesale_price' => "10",
                        'description' => 'Sweet Water makes you feel good 1',
                    ],
                    [
                        'id' => "1506964157791",
                        'retail_price' => "21",
                        'stock' => 100,
                        'wholesale_price' => 21,
                        'description' => 'Sweet Water makes you feel good 2',
                    ],
                ],
            ],
            [
                '_id' => new MongoId('63eba821d388b01fa1a39f25'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => false,
                'categories' => ['62a9b7fd18d1d4d010e1110a', '62a9b7fd18d1d4d010e11108'],
                'name' => 'Sparkling Water Bottle 1l',
                'description' => 'Sparkling Sweet Water 1l',
                'private' => false,
                'presentations' => [
                    [
                        'id' => 1_508_754_157_792,
                        'retail_price' => 13,
                        'stock' => 16,
                        'wholesale_price' => 12,
                        'description' => 'Sparkling Water makes you feel good every day',
                    ],
                ],
            ],
            [
                '_id' => new MongoId('63eb86d125ad6448d117ae5b'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => false,
                'categories' => ['62a9b7fd18d1d4d010e1110a', '62a9b7fd18d1d4d010e11108'],
                'name' => 'Sparkling Water Bottle 0.5l',
                'description' => 'Sparkling Sweet Water 0.5l',
                'private' => false,
                'presentations' => [
                    [
                        'id' => 1_508_754_157_795,
                        'retail_price' => 6,
                        'stock' => 20,
                        'wholesale_price' => 7,
                        'description' => 'Sparkling Water makes you feel good every day',
                    ],
                ],
            ],
        ];
    }
}
