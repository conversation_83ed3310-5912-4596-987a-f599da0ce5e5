<?php

declare(strict_types=1);

use Carbon\Carbon;
use Glofox\Domain\Bookings\Models\Booking;
use Glofox\Domain\Bookings\Repositories\BookingsRepository;
use Glofox\Domain\Bookings\UseCase\CancelBooking;
use Glofox\Domain\Bookings\UseCase\CancelBookingParams;
use Glofox\Domain\Branches\Exceptions\BranchNotFoundException;
use Glofox\Domain\Branches\Models\Branch;
use Glofox\Domain\Branches\Repositories\BranchesRepository;
use Glofox\Domain\Events\Models\Event;
use Glofox\Domain\Events\Repositories\EventsRepository;
use Glofox\Domain\EventTrackers\Exceptions\EventNotFoundException;
use Glofox\Domain\Users\Models\User;
use Psr\Log\LoggerInterface;

App::uses('Shell', 'Console');

class DeleteAllFutureDuplicatedEventsByBranchShell extends Shell
{
    private LoggerInterface $logger;
    private BranchesRepository $branchesRepository;
    private BookingsRepository $bookingsRepository;
    private EventsRepository $eventsRepository;
    private \Event $event;
    private CancelBooking $cancelBooking;

    public function __construct()
    {
        parent::__construct();

        $this->logger = app()->make(LoggerInterface::class);
        $this->branchesRepository = app()->make(BranchesRepository::class);
        $this->bookingsRepository = app()->make(BookingsRepository::class);
        $this->eventsRepository = app()->make(EventsRepository::class);
        $this->cancelBooking = app()->make(CancelBooking::class);
        $this->event = app()->make(\Event::class);
    }

    public function getOptionParser(): ConsoleOptionParser
    {
        $parser = parent::getOptionParser();

        $parser->addOption('branchId', [
            'short' => 'b',
            'help' => 'Branch ID',
            'required' => true
        ]);

        $parser->addOption('eventId', [
            'short' => 'e',
            'help' => 'Event ID',
            'required' => true
        ]);

        return $parser;
    }

    public function main(): void
    {
        $this->logger->info('[DeleteAllFutureDuplicatedEventsByBranch] Started');

        try {
            $branch = $this->resolveBranch($this->params['branchId']);
            $event = $this->resolveEvent($this->params['eventId']);
            $program = $event->program();
            $programScheduleCodes = $program->scheduleCodes();

            $duplicatedEvents = $this->eventsRepository->getDuplicatedEvents(
                $event->timeStart(),
                $program
            );

            if (empty($duplicatedEvents)) {
                $this->logger->info('[DeleteAllFutureDuplicatedEventsByBranch] No duplicated events found.');
                return;
            }

            $wrongSchedules = [];
            // Check if the duplicated events have a schedule code that is not in the program's schedule codes. The events related to these schedules will be deleted.
            foreach ($duplicatedEvents as $duplicatedEvent) {
                if (!in_array($duplicatedEvent['schedule_code'], $programScheduleCodes, true)) {
                    $wrongSchedules[] = $duplicatedEvent['schedule_code'];
                }
            }

            $timeStart = Carbon::parse($event->timeStart()->toDateTimeString())->getTimestamp();
            $futureEvents = $this->eventsRepository->getAllByScheduleCodesBetweenStartAndEndTime(
                array_unique($wrongSchedules),
                $this->params['branchId'],
                $timeStart,
                null
            );

            foreach ($futureEvents as $futureEvent) {
                $this->cancelBookings($futureEvent, $branch);
                $this->event->deleteById($futureEvent->id());
            }

            $this->logger->info('[DeleteAllFutureDuplicatedEventsByBranch] Finished');
        } catch (Exception $e) {
            $this->logger->error('[DeleteAllFutureDuplicatedEventsByBranch] Error: ' . $e->getMessage());
        }
    }

    private function cancelBookings(Event $event, Branch $branch): void
    {
        $bookings = $this->bookingsRepository->findByEventId($event->id()) ?? [];
        $bot = $this->resolveBot($branch);

        foreach ($bookings as $booking) {
            $bookingModel = Booking::make($booking['Booking']);
            $this->cancelBooking($bookingModel, $bot);
        }
    }

    private function cancelBooking(Booking $booking, User $bot): void
    {
        try {
            $this->cancelBooking->execute(
                new CancelBookingParams(
                    $bot,
                    $booking->id(),
                    $booking->userId(),
                    null
                )
            );

            $this->logger->info(
                '[DeleteAllFutureDuplicatedEventsByBranch] Booking cancelled: ' . $booking->userId(
                ) . ' - ' . $booking->userName() . ' - ' . $booking->startingTime()
            );
        } catch (Exception $e) {
            $this->logger->error(
                '[DeleteAllFutureDuplicatedEventsByBranch] Error cancelling booking: ' . $e->getMessage()
            );
        }
    }

    private function resolveBot(Branch $branch): User
    {
        return User::make([
            '_id' => User::WORKER_FAKE_ID,
            'namespace' => $branch->namespace(),
            'branch_id' => $branch->id(),
            'first_name' => 'Auto-cancelling',
            'last_name' => 'From DeleteAllFutureDuplicatedEventsByBranchShell',
            'type' => UserType::SUPERADMIN,
            'active' => true,
        ]);
    }

    /**
     * @throws BranchNotFoundException
     */
    private function resolveBranch(string $branchId): Branch
    {
        $branch = $this->branchesRepository->findById($branchId);

        if (empty($branch)) {
            throw new BranchNotFoundException('Branch not found');
        }

        return Branch::make($branch['Branch']);
    }

    /**
     * @throws EventNotFoundException
     */
    private function resolveEvent(string $eventId): Event
    {
        $event = $this->eventsRepository->findById($eventId);

        if (empty($event)) {
            throw new EventNotFoundException('Event not found');
        }

        return Event::make($event['Event']);
    }
}