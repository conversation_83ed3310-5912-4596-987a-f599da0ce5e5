<?php

declare(strict_types=1);

namespace Glofox\Validation\Rules\Shared;

use Illuminate\Validation\Validator;
use MongoException;
use MongoId;

class MongoIdValidator
{
    public function validate(string $attribute, $value, array $parameters, Validator $validator): bool
    {
        if (empty($value)) {
            return false;
        }

        try {
            new MongoId($value);
        } catch (MongoException $e) {
            return false;
        }

        return true;
    }
}
