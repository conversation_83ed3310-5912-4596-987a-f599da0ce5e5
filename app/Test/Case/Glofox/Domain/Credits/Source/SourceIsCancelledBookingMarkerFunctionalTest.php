<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\Credits\Source;

\App::import('Test/Case', 'GlofoxTestCase');

use Glofox\Domain\Bookings\Models\Booking;
use Glofox\Domain\Credits\Models\CreditPack;
use Glofox\Domain\Credits\Models\CreditPackSourceCancelledBooking;
use Glofox\Domain\Credits\Models\CreditPackSourceType;
use Glofox\Domain\Credits\Source\SourceIsCancelledBookingMarker;
use Glofox\Domain\Users\Models\User;
use Glofox\Payments\Exceptions\CreditPackAlreadyExistsException;

class SourceIsCancelledBookingMarkerFunctionalTest extends \GlofoxTestCase
{
    public $fixtures = [
        'app.branch',
        'app.booking',
        'app.user',
        'app.stripe_charge',
    ];
    private SourceIsCancelledBookingMarker $sourceIsCancelledBookingMarker;

    public function setUp(): void
    {
        parent::setUp();

        $this->sourceIsCancelledBookingMarker = app()->make(SourceIsCancelledBookingMarker::class);
    }

    public function test_it_fails_when_source_is_already_set(): void
    {
        $this->expectExceptionMessage('Credit Pack a123 already has an immutable source defined');

        $creditPack = CreditPack::make([
            '_id' => 'a123',
            'source' => [
                'type' => CreditPackSourceType::MANUALLY_ADDED_BY_STAFF,
                'staff_id' => '59a7011a05c677bda512212a',
            ],
        ]);

        $author = User::make([
            '_id' => '59a7011a05c677bda512212a', // admin fixture
        ]);

        $booking = Booking::make([
            '_id' => '5b043d752a1a3a1657e6db5e',
        ]);

        $this->sourceIsCancelledBookingMarker->mark($creditPack, $author, $booking);
    }

    public function test_it_marks_as_cancelled_booking_when_source_is_null(): void
    {
        $creditPack = CreditPack::make([
            '_id' => 'a123',
            'source' => null,
        ]);

        $author = User::make([
            '_id' => '59a7011a05c677bda512212a', // admin fixture
            'branch_id' => '5addc25383266f65abf515c4',
        ]);

        $booking = Booking::make([
            '_id' => '5b043d752a1a3a1657e6db5e',
            'user_id' => '5adde862426413b9e9b4be9e', // member fixture
        ]);

        $this->sourceIsCancelledBookingMarker->mark($creditPack, $author, $booking);

        $source = $creditPack->source();

        \assert($source instanceof CreditPackSourceCancelledBooking);

        self::assertSame(CreditPackSourceType::CANCELLED_BOOKING, $source->type()->getValue());
        self::assertSame('59a7011a05c677bda512212a', $source->authorId());
        self::assertSame('5b043d752a1a3a1657e6db5e', $source->cancelledBookingId());
        self::assertSame('INVOICE-MOCK', $source->invoiceId());
    }
}
