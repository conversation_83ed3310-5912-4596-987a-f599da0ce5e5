<?php

namespace Glofox\Payments\Providers\Gateway\Merchants;

use Glofox\Domain\Branches\Models\Branch;
use Glofox\Domain\PaymentMethods\Models\PaymentMethod;
use Glofox\Domain\PaymentProviders\Models\PaymentProvider;
use Glofox\Domain\PaymentProviders\Models\RegistrationFlow;
use Glofox\Domain\PaymentProviders\RegistrationFlowType;
use Glofox\Payments\Entities\Merchant\Contracts\MerchantHandlerContract;
use Glofox\Payments\Entities\Merchant\Exceptions\ChangeCurrencyException;
use Glofox\Payments\Entities\Merchant\Exceptions\MerchantCreationException;
use Glofox\Payments\Entities\Merchant\Exceptions\MerchantRetrievalException;
use Glofox\Payments\Entities\Merchant\Internal\ConnectedMerchant;
use Glofox\Payments\Entities\Merchant\Internal\CreateOrUpdateMerchant;
use Glofox\Payments\Entities\Merchant\Models\Account;
use Glofox\Payments\Providers\Gateway\GRPC\GRPC;
use Glofox\Payments\Providers\Gateway\GRPC\Metadata;
use Glofox\Payments\Providers\Gateway\Merchants\DataModellers\ApsDataModeller;
use Glofox\Payments\Providers\Gateway\Merchants\DataModellers\BasicDataModeller;
use Glofox\Payments\Providers\Gateway\Merchants\DataModellers\DataModellerContract;
use Glofox\Payments\Providers\Gateway\Merchants\DataModellers\DefaultDataModeller;
use Glofox\Payments\Providers\Gateway\Merchants\DataModellers\EziDebitDataModeller;
use Glofox\Payments\Providers\Gateway\Merchants\DataModellers\EzyPayDataModeller;
use Glofox\Payments\Providers\Gateway\Merchants\DataModellers\GoCardlessDataModeller;
use Glofox\Payments\Providers\Gateway\Merchants\DataModellers\PaytabsDataModeller;
use Glofox\Payments\Providers\Gateway\Merchants\DataModellers\SpreedlyDataModeller;
use Glofox\Payments\Providers\Gateway\Merchants\DataModellers\StripeCustomDataModeller;
use Glofox\Payments\Providers\Gateway\Merchants\DataModellers\StripeStandardGatewayDataModeller;
use Glofox\Payments\Providers\Gateway\Merchants\DataModellers\ZoozDataModeller;
use Glofox\Payments\Providers\Gateway\Merchants\DataModellers\NicePayDataModeller;
use Glofox\Payments\Providers\Gateway\ProtoParsers\AccountParser;
use Glofox\Payments\Providers\Gateway\Type;
use GRPC\Payments\AccountService\ChangeCurrencyReq;
use GRPC\Payments\AccountService\CreateAccountReq;
use GRPC\Payments\AccountService\EmptyAccountObject;
use GRPC\Payments\AccountService\GetAccountReq;
use GRPC\Payments\AccountService\UpdateAccountReq;
use Psr\Log\LoggerInterface;

/**
 * Class Merchants.
 */
class Merchants implements MerchantHandlerContract
{
    use GRPC;

    private array $dataModelers = [
        'STRIPE_CUSTOM_' => StripeCustomDataModeller::class,
        'PAYTABS' => PaytabsDataModeller::class,
        'SPREEDLY_' => SpreedlyDataModeller::class,
        'GOCARDLESS' => GoCardlessDataModeller::class,
        'ZOOZ' => ZoozDataModeller::class,
        'STRIPE_STANDARD_GATEWAY' => StripeStandardGatewayDataModeller::class,
        'BANK_TRANSFER' => BasicDataModeller::class,
        'PAY_LATER' => BasicDataModeller::class,
        'COMPLIMENTARY' => BasicDataModeller::class,
        'CASH' => BasicDataModeller::class,
        'AMAZON_PAYMENT_SERVICES' => ApsDataModeller::class,
        'EZI_DEBIT_' => EziDebitDataModeller::class,
        'EZYPAY_' => EzyPayDataModeller::class,
        'NICEPAY_' => NicePayDataModeller::class,
    ];

    private \Glofox\Payments\Providers\Gateway\ProtoParsers\AccountParser $accountParser;

    private \Glofox\Domain\PaymentMethods\Models\PaymentMethod $paymentMethod;

    private \Glofox\Domain\PaymentProviders\Models\PaymentProvider $paymentProvider;

    /**
     * @var LoggerInterface
     */
    private $log;

    /** @var Metadata */
    private $metadata;

    /**
     * {@inheritdoc}
     */
    public function __construct(PaymentMethod $paymentMethod, PaymentProvider $paymentProvider)
    {
        $this->accountParser = new AccountParser();
        $this->paymentProvider = $paymentProvider;
        $this->paymentMethod = $paymentMethod;
        $this->metadata = app()->make(Metadata::class);
        $this->log = app()->make(LoggerInterface::class);
    }

    /**
     * @param mixed $merchantId
     *
     * @return Account|null
     *
     * @throws MerchantRetrievalException
     */
    public function getById($merchantId): ?Account
    {
        $request = new GetAccountReq();
        $request->setID((int) $merchantId);

        /** @var $reply \GRPC\Payments\AccountService\Account */
        [$reply, $status] = $this->GRCPClient()->accountService->Get($request, $this->metadata->get())->wait();

        if (0 != $status->code || !($reply instanceof \GRPC\Payments\AccountService\Account)) {
            throw new MerchantRetrievalException($this->getErrorFromStatus($status));
        }

        return $this->accountParser->parseFromProtoMessage($reply);
    }

    /**
     * @param CreateOrUpdateMerchant $parameters
     *
     * @return ConnectedMerchant
     *
     * @throws MerchantCreationException
     */
    public function create(CreateOrUpdateMerchant $parameters): ConnectedMerchant
    {
        $dataModeler = $this->getProviderDataModeler();

        /** @var DataModellerContract $dataModeler */
        $dataModeler = new $dataModeler();

        $createMerchantPayload = $dataModeler->modelForCreating($this->paymentProvider, $parameters->branch(), $parameters->payload());

        $request = new CreateAccountReq();
        $request->setProviderID($this->paymentProvider->gatewayId());
        $request->setTypeID(Type::GATEWAY_MERCHANT_TYPE_ID);
        $request->setJsonModel($createMerchantPayload->toJson());
        $request->setExternalRef($parameters->externalRef());

        /** @var $reply \GRPC\Payments\AccountService\Account */
        [$reply, $status] = $this->GRCPClient()->accountService->Create($request, $this->metadata->get())->wait();

        if (0 != $status->code || !($reply instanceof \GRPC\Payments\AccountService\Account)) {
            throw new MerchantCreationException($this->getErrorFromStatus($status));
        }

        $merchantAccount = $this->accountParser->parseFromProtoMessage($reply);
        $merchantAuthObject = collect(json_decode($reply->getAuthObject(), true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR));

        $connectedAccount = (new ConnectedMerchant())
            ->setAccessToken(null)
            ->setRefreshableToken(null)
            ->setPublishableKey(data_get($merchantAuthObject, 'keys.publishable', $merchantAccount->id()))
            ->setAccount($merchantAccount);

        return $connectedAccount;
    }

    /**
     * @param CreateOrUpdateMerchant $parameters
     *
     * @return Account|null
     *
     * @throws MerchantCreationException
     * @throws MerchantRetrievalException
     */
    public function update(CreateOrUpdateMerchant $parameters): ?Account
    {
        $dataModeler = $this->getProviderDataModeler();

        /** @var DataModellerContract $dataModeler */
        $dataModeler = new $dataModeler();

        $updateMerchantPayload = $dataModeler->modelForUpdating($this->paymentProvider, $parameters->branch(), $parameters->payload());
        if ($updateMerchantPayload->isEmpty()) {
            return $this->getById($parameters->merchantId());
        }

        $request = new UpdateAccountReq();
        $request->setID($this->paymentMethod->provider()->accountId());
        $request->setJsonModel($updateMerchantPayload->toJson());
        $request->setExternalRef($parameters->externalRef());

        /** @var $reply \GRPC\Payments\AccountService\Account */
        [$reply, $status] = $this->GRCPClient()->accountService->Update($request, $this->metadata->get())->wait();

        if (0 != $status->code || !($reply instanceof \GRPC\Payments\AccountService\Account)) {
            throw new MerchantCreationException($this->getErrorFromStatus($status));
        }

        return $this->accountParser->parseFromProtoMessage($reply);
    }

    /**
     * @param Branch $branch
     *
     * @return RegistrationFlow
     */
    public function registrationFlow(Branch $branch): RegistrationFlow
    {
        if (RegistrationFlowType::REDIRECT != $this->paymentProvider->registrationFlow()->type()) {
            return $this->paymentProvider->registrationFlow();
        }

        $dataModeler = $this->getProviderDataModeler();

        $params = $dataModeler->modelForRegisterUrl($this->paymentProvider, $branch);

        $registrationFlow = $this->paymentProvider->registrationFlow();

        $redirectionUrl = $registrationFlow->baseUrl() . '?' . urldecode(http_build_query($params->toArray()));

        $this->paymentProvider->put('registration_flow', $registrationFlow->put('redirection_url', $redirectionUrl));

        return  $this->paymentProvider->registrationFlow();
    }

    /**
     * @throws ChangeCurrencyException
     */
    public function changeCurrency(string $currency)
    {
        $request = new ChangeCurrencyReq();
        $request->setAccountID((int) $this->paymentMethod->provider()->accountId());
        $request->setCurrency($currency);

        /** @var $reply EmptyAccountObject */
        [$reply, $status] = $this->GRCPClient()->accountService->ChangeCurrency($request, $this->metadata->get())->wait();

        if (0 != $status->code) {
            throw new ChangeCurrencyException($this->getErrorFromStatus($status));
        }
    }

    /**
     * @return DataModellerContract
     */
    private function getProviderDataModeler()
    {
        foreach ($this->dataModelers as $dataModelerPattern => $dataModelerClass) {
            if (0 === strncmp($this->paymentProvider->name(), $dataModelerPattern, \strlen($dataModelerPattern))) {
                return new $dataModelerClass();
            }
        }

        return new DefaultDataModeller();
    }
}
