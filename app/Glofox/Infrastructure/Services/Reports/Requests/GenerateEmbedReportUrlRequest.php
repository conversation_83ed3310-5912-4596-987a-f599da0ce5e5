<?php

declare(strict_types=1);

namespace Glofox\Infrastructure\Services\Reports\Requests;

use Glofox\Domain\Reports\Enum\EmbedReportId;
use Glofox\Domain\Users\Models\User;

class GenerateEmbedReportUrlRequest
{
    private \Glofox\Domain\Users\Models\User $user;

    private \Glofox\Domain\Reports\Enum\EmbedReportId $embedReportId;

    private string $domain;

    private string $locale;

    public function __construct(
        User $user,
        EmbedReportId $embedReportId,
        string $domain,
        string $locale
    ) {
        $this->user = $user;
        $this->embedReportId = $embedReportId;
        $this->domain = $domain;
        $this->locale = $locale;
    }

    public function user(): User
    {
        return $this->user;
    }

    public function embedReportId(): EmbedReportId
    {
        return $this->embedReportId;
    }

    public function domain(): string
    {
        return $this->domain;
    }

    public function toJson(): string
    {
        return json_encode($this->toArray(), JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    private function toArray(): array
    {
        return [
            'user_id' => $this->user->id(),
            'first_name' => $this->user->firstName(),
            'last_name' => $this->user->lastName(),
            'report_id' => $this->embedReportId->getValue(),
            'current_branch_id' => $this->user->currentBranchId(),
            'current_branch_timezone' => $this->user->branch()->timezone()->getName(),
            'domain' => $this->domain,
            'locale' => $this->locale,
        ];
    }
}
