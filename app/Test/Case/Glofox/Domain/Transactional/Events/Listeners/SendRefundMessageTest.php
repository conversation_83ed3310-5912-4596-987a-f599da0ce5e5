<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\Transactional\Events\Listeners;

use App;
use CakeTestCases\Glofox\Domain\Logger\Traits\MockedLoggerTrait;
use Glofox\Domain\Charges\Commands\SendRefundCommand;
use Glofox\Domain\Charges\Commands\SendRefundHandler;
use Glofox\Domain\Charges\Events\ChargeWasRefunded;
use Glofox\Domain\Charges\Models\Charge;
use Glofox\Domain\Transactional\Events\Listeners\SendRefundMessage;
use GlofoxTestCase;
use Mockery;
use MongoId;
use Psr\Log\LoggerInterface;


App::import('Test/Case', 'GlofoxTestCase');

class SendRefundMessageTest extends GlofoxTestCase
{
    use MockedLoggerTrait;

    public $fixtures = [];

    public function setUp()
    {
        parent::setUp();

        $this->mockLogger();
    }

    public function tearDown()
    {
        parent::tearDown();

        app()->forgetInstance(LoggerInterface::class);
        Mockery::close();
        $this->teardownLogger();
    }

    public function test_it_calls_the_refund_handler_with_the_proper_params(): void
    {
        $charge = Charge::make(['_id' => (string)new MongoId()]);

        $handler = Mockery::mock(SendRefundHandler::class);
        $handler
            ->shouldReceive('handle')
            ->withArgs(function (SendRefundCommand $command) use ($charge) {
                self::assertSame($command->getCharge()->id(), $charge->id());

                return true;
            })
            ->once();

        $event = new ChargeWasRefunded($charge);

        $listener = new SendRefundMessage($this->loggerMock, $handler);
        $listener->handle($event);
    }
}
