<?php

namespace Glofox\Payments\Console\Gateway;

use Glofox\Console\CommandHandlerInterface;
use Glofox\Console\CommandInputType;
use Glofox\Console\CommandParameter;
use Glofox\Console\CommandParametersCollection;
use Glofox\Domain\Activities\Events\Listeners\ChargeUpdated;
use Glofox\Domain\Activities\Events\Listeners\CustomChargeCreated;
use Glofox\Domain\Activities\Events\Listeners\EntityBooked;
use Glofox\Domain\Activities\Events\Listeners\NonSubMembershipPurchased;
use Glofox\Domain\Activities\Events\Listeners\ProductPurchased;
use Glofox\Domain\Activities\Events\Listeners\SubscriptionCyclePaid;
use Glofox\Domain\Activities\Events\Listeners\SubscriptionCyclePaymentFailed;
use Glofox\Domain\Activities\Events\Listeners\SubscriptionPurchased;
use Glofox\Domain\Activities\Models\Activity;
use Glofox\Domain\Activities\Repositories\ActivitiesRepository;
use Glofox\Domain\Activities\Search\Expressions\EventContextChargeId;
use Glofox\Domain\Activities\Search\Expressions\EventContextTransactionGroupId;
use Glofox\Domain\Authentication\Auth;
use Glofox\Domain\Bookings\Events\ClassBookingWasCreated;
use Glofox\Domain\Bookings\Events\TimeslotBookingWasCreated;
use Glofox\Domain\Bookings\Exceptions\BookingNotFoundException;
use Glofox\Domain\Bookings\Models\Booking;
use Glofox\Domain\Bookings\Repositories\BookingsRepository;
use Glofox\Domain\Bookings\Search\Expressions\EventId;
use Glofox\Domain\Bookings\Search\Expressions\UserId;
use Glofox\Domain\Branches\Models\Branch;
use Glofox\Domain\Branches\Repositories\BranchesRepository;
use Glofox\Domain\Charges\Events\ChargeWasCreated;
use Glofox\Domain\Charges\Events\ChargeWasUpdated;
use Glofox\Domain\Charges\Models\Charge;
use Glofox\Domain\Charges\Models\ChargesGroup;
use Glofox\Domain\Charges\Repositories\ChargesRepository;
use Glofox\Domain\Charges\Search\Expressions\TransactionGroupId;
use Glofox\Domain\Charges\Type;
use Glofox\Domain\Events\Repositories\EventsRepository;
use Glofox\Domain\Memberships\Events\MembershipWasPurchased;
use Glofox\Domain\Memberships\Models\Membership;
use Glofox\Domain\Memberships\Models\Plan;
use Glofox\Domain\Memberships\Repositories\MembershipsRepository;
use Glofox\Domain\Store\Products\Repositories\ProductsRepository;
use Glofox\Domain\Store\Sales\Events\ProductWasPurchased;
use Glofox\Domain\TimeSlotPatterns\Search\Expressions\Model;
use Glofox\Domain\TimeSlotPatterns\Search\Expressions\ModelId;
use Glofox\Domain\TimeSlots\Models\TimeSlot;
use Glofox\Domain\TimeSlots\Repositories\TimeSlotRepository;
use Glofox\Domain\Users\Exceptions\UserNotFoundException;
use Glofox\Domain\Users\Models\User;
use Glofox\Domain\Users\Repositories\UsersRepository;
use Glofox\NotFoundException;
use Glofox\Payments\Entities\Subscription\Events\SubscriptionCyclePaymentHasFailed;
use Glofox\Payments\Entities\Subscription\Events\SubscriptionCycleWasPaid;
use Glofox\Repositories\Search\Expressions\Shared\Id;
use Glofox\Repositories\Search\Expressions\Shared\InIds;
use Glofox\Storage\CloudStorageInterface;
use Illuminate\Support\Collection;
use League\Csv\Writer;
use Psr\Log\LoggerAwareTrait;
use Psr\Log\LoggerInterface;

class RecordMissingActivities implements CommandHandlerInterface
{
    use LoggerAwareTrait;

    private ?array $parameters = null;

    private \Glofox\Storage\CloudStorageInterface $cloudStorage;

    private ?string $reportFilePath = null;

    /** @var Writer */
    private $reportWriter;

    private \Glofox\Domain\Branches\Repositories\BranchesRepository $branchesRepository;

    private \Glofox\Domain\Users\Repositories\UsersRepository $usersRepository;

    private \Glofox\Domain\Charges\Repositories\ChargesRepository $chargesRepository;

    private \Glofox\Domain\Activities\Repositories\ActivitiesRepository $activitiesRepository;

    private \Glofox\Domain\Memberships\Repositories\MembershipsRepository $membershipsRepository;

    private \Glofox\Domain\Store\Products\Repositories\ProductsRepository $productsRepository;

    private \Glofox\Domain\Bookings\Repositories\BookingsRepository $bookingRepository;

    private \Glofox\Domain\Events\Repositories\EventsRepository $eventsRepository;

    private \Glofox\Domain\TimeSlots\Repositories\TimeSlotRepository $timeSlotRepository;

    private bool $isDryRun = true;

    /** @var User[] */
    private array $membersBuffer = [];

    /** @var User[] */
    private array $staffBuffer = [];

    /** @var Branch[] */
    private array $branchesBuffer = [];

    public function __construct(
        LoggerInterface $logger,
        CloudStorageInterface $cloudStorage,
        BranchesRepository $branchesRepository,
        ActivitiesRepository $activitiesRepository,
        UsersRepository $usersRepository,
        ChargesRepository $chargesRepository,
        ProductsRepository $productsRepository,
        BookingsRepository $bookingRepository,
        EventsRepository $eventsRepository,
        TimeSlotRepository $timeSlotRepository,
        MembershipsRepository $membershipsRepository
    ) {
        $this->cloudStorage = $cloudStorage;
        $this->branchesRepository = $branchesRepository;
        $this->activitiesRepository = $activitiesRepository;
        $this->usersRepository = $usersRepository;
        $this->chargesRepository = $chargesRepository;
        $this->productsRepository = $productsRepository;
        $this->bookingRepository = $bookingRepository;
        $this->eventsRepository = $eventsRepository;
        $this->timeSlotRepository = $timeSlotRepository;
        $this->membershipsRepository = $membershipsRepository;

        $this->setLogger($logger);
    }

    public function name(): string
    {
        return 'record-missing-activities';
    }

    public function parameters(): CommandParametersCollection
    {
        $parameters = new CommandParametersCollection();

        $parameters->push(
            (new CommandParameter())
                ->setName('method')
                ->setDescription('The method to execute')
                ->setDefault('dry-run')
        );

        $parameters->push(
            (new CommandParameter())
                ->setName('charge-ids')
                ->setDescription('Comma-separated list of charge IDs to record')
        );

        $parameters->push(
            (new CommandParameter())
                ->setName('upload-report')
                ->setDescription('Upload report to s3')
                ->setDefault(true)
                ->setType(CommandInputType::BOOLEAN())
        );

        return $parameters;
    }

    public function invoke(array $parameters): void
    {
        $this->parameters = $parameters;

        $this->init();

        $chargeIds = explode(',', $this->parameters['charge-ids']);
        $this->logger->info(sprintf('Fetching charges: %s', collect($chargeIds)->toJson()));
        $chargeIds = collect($chargeIds)->map(fn($id) => new \MongoId($id));

        $chargeGroups = $this->findChargeGroups($chargeIds);
        $total = \count($chargeGroups);

        $this->logger->info(sprintf('Processing %s charge groups', $total));

        $i = 0;
        foreach ($chargeGroups as $chargeGroup) {
            ++$i;

            $primaryCharge = $chargeGroup->getPrimaryCharge();

            $this->logger->info(
                sprintf('[%d/%d] Verifying charge %s from member %s', $i, $total, $primaryCharge->id(), $primaryCharge->userId())
            );

            try {
                $activity = $this->findActivityForChargeGroup($chargeGroup);

                if ($activity) {
                    $message = sprintf(
                        'Activity found for charge %s. Skipping the rest of the script for this charge.',
                        $primaryCharge->id(),
                    );

                    $this->logger->info($message);

                    $this->reportWriter->insertOne([
                        $primaryCharge->id(),
                        $primaryCharge->created()->toIso8601String(),
                        $primaryCharge->glofoxEvent(),
                        $primaryCharge->userId(),
                        $activity->id(),
                        $message,
                        'already-fixed',
                        false,
                    ]);

                    continue;
                }

                $this->logger->info(
                    sprintf('Activity missing for charge %s. Attempting to fix it.', $primaryCharge->id())
                );

                $activity = $this->attemptToFixMissingActivityOrFail($chargeGroup);

                $message = sprintf('Activity %s successfully created for charge %s.', $activity->id(), $primaryCharge->id());

                $this->logger->info($message, $activity->toArray());

                $this->reportWriter->insertOne([
                    $primaryCharge->id(),
                    $primaryCharge->created()->toIso8601String(),
                    $primaryCharge->glofoxEvent(),
                    $primaryCharge->userId(),
                    $activity->id(),
                    $message,
                    $this->isDryRun ? 'dry-run-fixed' : 'fixed',
                    false,
                ]);
            } catch (\Exception $e) {
                $this->logger->error($e->getMessage());

                $this->reportWriter->insertOne([
                    $primaryCharge->id(),
                    $primaryCharge->created()->toIso8601String(),
                    $primaryCharge->glofoxEvent(),
                    $primaryCharge->userId(),
                    'n/a',
                    $e->getMessage(),
                    'error',
                    true,
                ]);
            }
        }

        $this->logger->info('Finish recording missing activities');
        $this->uploadReport();
    }

    private function getActivityCreatorBasedOnCharge(ChargesGroup $chargesGroup): \Closure
    {
        $primaryCharge = $chargesGroup->getPrimaryCharge();
        $isPendingIntent = $primaryCharge->isPendingAuthorization();
        $chargeEventId = $primaryCharge->get('event_id', 'no-event-id');
        $event = $primaryCharge->glofoxEvent();
        $branch = $this->findBranchBasedOnCharge($primaryCharge);

        switch ($event) {
            case $event == Type::SUBSCRIPTION_PAYMENT || ($event == Type::SUBSCRIPTION_PAYMENT_FAILED && $isPendingIntent):
                $this->logger->info(sprintf('Resolved %s to activity creator for: %s', $primaryCharge->glofoxEvent(), Type::SUBSCRIPTION_PAYMENT));
                $this->logInAsGlofoxBot($primaryCharge);

                return function (User $user, ChargesGroup $chargesGroup) use ($chargeEventId) {
                    /** @var SubscriptionCyclePaid $event */
                    $event = app()->make(SubscriptionCyclePaid::class);
                    $event->handle(new SubscriptionCycleWasPaid($user->toArray(), $chargeEventId, $chargesGroup));
                };

            case $event == Type::SUBSCRIPTION_PAYMENT_FAILED:
                $this->logger->info(sprintf('Resolved %s to activity creator for: %s', $primaryCharge->glofoxEvent(), Type::SUBSCRIPTION_PAYMENT_FAILED));
                $this->logInAsGlofoxBot($primaryCharge);

                return function (User $user, ChargesGroup $chargesGroup) use ($chargeEventId) {
                    /** @var SubscriptionCyclePaymentFailed $event */
                    $event = app()->make(SubscriptionCyclePaymentFailed::class);
                    $event->handle(new SubscriptionCyclePaymentHasFailed($user->toArray(), $chargeEventId, $chargesGroup));
                };

            case $event == Type::UPFRONT_PAYMENT:
                $this->logger->info(sprintf('Resolved %s to activity creator for: %s', $primaryCharge->glofoxEvent(), Type::UPFRONT_PAYMENT));
                $this->logInAsBlankUser($primaryCharge);

                return function (User $user, ChargesGroup $chargesGroup) {
                    $primaryCharge = $chargesGroup->getPrimaryCharge();
                    $membershipId = data_get($primaryCharge->metadata(), 'membership_id');
                    $planCode = data_get($primaryCharge->metadata(), 'plan_code');

                    $membership = $this->findMembershipById($membershipId);
                    $plan = $this->findPlanByCode($membership, $planCode);

                    $chargeId = $primaryCharge->id();

                    /** @var SubscriptionCyclePaid $event */
                    $nonSubscriptionEvent = app()->make(NonSubMembershipPurchased::class);
                    $nonSubscriptionEvent->handle(new MembershipWasPurchased($user, $membership, $plan, null, null, $chargeId));

                    $subscriptionEvent = app()->make(SubscriptionPurchased::class);
                    $subscriptionEvent->handle(new MembershipWasPurchased($user, $membership, $plan, null, null, $chargeId));
                };

            case $event == Type::CUSTOM_CHARGE:
                $this->logger->info(sprintf('Resolved %s to activity creator for: %s', $primaryCharge->glofoxEvent(), Type::CUSTOM_CHARGE));
                $this->logInAsBlankUser($primaryCharge);

                return function (User $user, ChargesGroup $chargesGroup) {
                    /** @var CustomChargeCreated $event */
                    $event = app()->make(CustomChargeCreated::class);
                    $event->handle(new ChargeWasCreated($chargesGroup->getPrimaryCharge()->toArray()));
                };

            case $event == Type::BUY_PRODUCT:
                $this->logger->info(sprintf('Resolved %s to activity creator for: %s', $primaryCharge->glofoxEvent(), Type::BUY_PRODUCT));
                $this->logInAsBlankUser($primaryCharge);

                return function (User $user, ChargesGroup $chargesGroup) {
                    $primaryCharge = $chargesGroup->getPrimaryCharge();
                    $product = $this->productsRepository->findById($primaryCharge->productId());

                    /** @var ProductPurchased $event */
                    $event = app()->make(ProductPurchased::class);
                    $event->handle(new ProductWasPurchased(
                        $product->toLegacy(),
                        $user->toLegacy(),
                        $primaryCharge->metadata()['purchase_code'],
                        (int) $primaryCharge->metadata()['presentation_id'],
                        $primaryCharge->metadata()['quantity'],
                        $primaryCharge->id(),
                        $chargesGroup->getAmount(),
                        $primaryCharge->currency()
                    ));
                };
            case $event == Type::BOOK_CLASS:
                $this->logger->info(sprintf('Resolved %s to activity creator for: %s', $primaryCharge->glofoxEvent(), Type::BOOK_CLASS));
                $this->logInAsBlankUser($primaryCharge);

                return function (User $user, ChargesGroup $chargesGroup) use($branch) {
                    $primaryCharge = $chargesGroup->getPrimaryCharge();

                    $eventId = $primaryCharge->metadata()['event_id'];
                    if (empty($eventId)) {
                        $this->logger->error(sprintf('charge %s missing event_id', $primaryCharge->id()));
                        return;
                    }

                    $eventModel = $this->eventsRepository->findByIdOrFail($eventId);

                    /** @var Booking $booking */
                    $booking = $this->bookingRepository
                        ->addCriteria(new EventId($eventId))
                        ->addCriteria(new UserId($user->id()))
                        ->firstOrFail(function () {
                           throw new BookingNotFoundException();
                        });

                    /** @var EntityBooked $event */
                    $event = app()->make(EntityBooked::class);
                    $event->handle(new ClassBookingWasCreated(
                        $branch->toLegacy(),
                        $user->toLegacy(),
                        $booking->toLegacy(),
                        $primaryCharge->toLegacy(),
                        $eventModel->toLegacy()
                    ));
                };
            case $event == Type::BOOK_TIME_SLOT:
                $this->logger->info(sprintf('Resolved %s to activity creator for: %s', $primaryCharge->glofoxEvent(), Type::BOOK_TIME_SLOT));
                $this->logInAsBlankUser($primaryCharge);

                return function (User $user, ChargesGroup $chargesGroup) use($branch) {
                    $primaryCharge = $chargesGroup->getPrimaryCharge();

                    $modelId = $primaryCharge->metadata()['model_id'];
                    if (empty($modelId)) {
                        $this->logger->error(sprintf('charge %s missing model_id', $primaryCharge->id()));
                        return;
                    }

                    /** @var Booking $booking */
                    $booking = $this->bookingRepository
                        ->addCriteria(new UserId($user->id()))
                        ->addCriteria(new ModelId($modelId))
                        ->firstOrFail(function () {
                            throw new BookingNotFoundException();
                        });

                    /** @var TimeSlot $timeslot */
                    $timeslot = $this->timeSlotRepository
                        ->addCriteria(new Id($booking->timeSlotId()))
                        ->firstOrFail(function () use($booking) {
                            throw new NotFoundException(sprintf('timeslot %s not found', $booking->timeSlotId()));
                        });

                    /** @var EntityBooked $event */
                    $event = app()->make(EntityBooked::class);
                    $event->handle(new TimeslotBookingWasCreated(
                        $branch->toLegacy(),
                        $user->toLegacy(),
                        $booking->toLegacy(),
                        $primaryCharge->toLegacy(),
                        ['TimeSlot' => $timeslot]
                    ));
                };
            default:
                throw new \Exception(sprintf('The resolved `glofox event` %s from charge %s is not currently supported by this script and cannot be recovered automatically', $primaryCharge->glofoxEvent(), $primaryCharge->id()));
        }
    }

    /**
     * @param string[] $chargeIds
     *
     * @return ChargesGroup[]
     *
     * @throws \Exception
     */
    private function findChargeGroups(Collection $chargeIds): array
    {
        $chargeGroups = [];

        /** @var Charge[] $charges */
        $charges = $this->chargesRepository
            ->addCriteria(new InIds($chargeIds))
            ->find();

        foreach ($charges as $charge) {
            if (empty($charge->transactionGroupId())) {
                $chargeGroups[] = new ChargesGroup(
                    $charge->invoiceId(),
                    $charge->transactionGroupId(),
                    collect([$charge]),
                );
                continue;
            }

            $group = $this->chargesRepository
                ->addCriteria(new TransactionGroupId($charge->transactionGroupId()))
                ->find();

            $chargeGroups[] = new ChargesGroup(
                $charge->invoiceId(),
                $charge->transactionGroupId(),
                collect($group),
            );
        }

        return $chargeGroups;
    }

    /**
     * @return Activity
     *
     * @throws \Exception
     */
    private function findActivityForChargeGroup(ChargesGroup $chargesGroup): ?Activity
    {
        if (!empty($chargesGroup->getGroupId()) && !str_contains($chargesGroup->getGroupId(), 'core-api')) {
            $this->activitiesRepository->addCriteria(new EventContextTransactionGroupId($chargesGroup->getGroupId()));
        }
        else {
            $this->activitiesRepository->addCriteria(new EventContextChargeId($chargesGroup->getPrimaryCharge()->id()));
        }

        return $this->activitiesRepository->first();
    }

    private function findMemberBasedOnCharge(Charge $charge): User
    {
        if (!isset($this->membersBuffer[$charge->userId()])) {
            $user = $this->usersRepository
                ->addCriteria(new Id($charge->userId()))
                ->firstOrFail(function () use ($charge) {
                    throw new UserNotFoundException(sprintf('Could not find user %s of charge %s', $charge->userId(), $charge->id()));
                });

            $this->membersBuffer[$charge->userId()] = $user;
        }

        return $this->membersBuffer[$charge->userId()];
    }

    private function findStaffBasedOnCharge(Charge $charge): User
    {
        if (!isset($this->staffBuffer[$charge->soldByUserId()])) {
            $user = $this->usersRepository
                ->addCriteria(new Id($charge->soldByUserId()))
                ->firstOrFail(function () use ($charge) {
                    throw new UserNotFoundException(sprintf('Could not find seller %s of charge %s', $charge->soldByUserId(), $charge->id()));
                });

            $this->staffBuffer[$charge->soldByUserId()] = $user;
        }

        return $this->staffBuffer[$charge->soldByUserId()];
    }

    private function findBranchBasedOnCharge(Charge $charge): Branch
    {
        if (!isset($this->branchesBuffer[$charge->branchId()])) {
            $branch = $this->branchesRepository
                ->addCriteria(new Id($charge->branchId()))
                ->firstOrFail(function () use ($charge) {
                    throw new UserNotFoundException(sprintf('Could not find branch %s of charge %s', $charge->branchId(), $charge->id()));
                });

            $this->branchesBuffer[$charge->branchId()] = $branch;
        }

        return $this->branchesBuffer[$charge->branchId()];
    }

    private function attemptToFixMissingActivityOrFail(ChargesGroup $chargesGroup): Activity
    {
        $primaryCharge = $chargesGroup->getPrimaryCharge();
        $glofoxEvent = $primaryCharge->glofoxEvent();

        try {
            if (!$glofoxEvent) {
                throw new \Exception(sprintf("Charge %s doesn't have a Glofox Event ID", $primaryCharge->id()));
            }

            $activityCreator = $this->getActivityCreatorBasedOnCharge($chargesGroup);

            $user = $this->findMemberBasedOnCharge($primaryCharge);

            if (!$this->isDryRun) {
                $this->logger->info(sprintf('Running activity creator for charge %s', $primaryCharge->id()));
                $activityCreator($user, $chargesGroup);
            } else {
                $this->logger->info(sprintf('Since this is a run in dry-run mode, the activity creator will not be executed for charge %s', $primaryCharge->id()));
            }

            // Note: we don't need to do extra steps for checking whether the charge has been "refunded" because
            // we're also triggering the "charge was updated" event for every recovery, which means it should
            // add the refunded flag in the update of the activity.
        } catch (\Exception $e) {
            throw new \Exception(sprintf('Failed when attempting to fix activity for charge %s from member %s with error: %s', $primaryCharge->id(), $primaryCharge->userId(), $e->getMessage()));
        }

        try {
            $this->logger->info(sprintf('Validating if activity creator worked as expected for charge %s', $primaryCharge->id()));

            if (!$this->isDryRun) {
                usleep(100000); // replication delay
                $activity = $this->findActivityForChargeGroup($chargesGroup);
                if (!$activity) {
                    throw new \Exception(sprintf('Unable to verify activity creation for charge %s', $primaryCharge->id()));
                }

                /** @var ChargeUpdated $event */
                $event = app()->make(ChargeUpdated::class);
                $event->handle(new ChargeWasUpdated($primaryCharge->toArray()));
            } else {
                $this->logger->info(sprintf('Since this is a run in dry-run mode, the validator will not be executed for charge %s and will return an empty activity', $primaryCharge->id()));

                return Activity::make();
            }
        } catch (\Exception $e) {
            // in theory, this scenario should never exist, but leaving it here for reporting measures _if_ this ever happens.
            throw new \Exception(sprintf('INVESTIGATE IMMEDIATELY - Failed when validating if the activity was properly created for charge %s from member %s with error: %s', $primaryCharge->id(), $primaryCharge->userId(), $e->getMessage()));
        }

        return $activity;
    }

    private function init(): void
    {
        $this->initReportWriter();
        $this->logger->info(sprintf('TEMP file location: %s', $this->reportFilePath));

        $executionMethod = $this->parameters['method'];

        if ('dry-run' === $executionMethod) {
            $executionMethod = 'dry-run';
            $this->isDryRun = true;
        } else {
            $executionMethod = 'apply';
            $this->isDryRun = false;
        }

        $this->logger->info(sprintf('Execution method: %s', $executionMethod));
    }

    private function initReportWriter(): void
    {
        $this->reportFilePath = sprintf('%s/record_missing_activities_%d.csv', TMP, time());
        $this->reportWriter = Writer::createFromPath($this->reportFilePath, 'w+');

        $this->reportWriter->insertOne(['charge_id', 'charge_date', 'charge_glofox_event', 'user_id', 'activity_id', 'result', 'action', 'error']);
    }

    private function uploadReport(): void
    {
        $cloudFileName = sprintf(
            'scripts/%s',
            basename($this->reportFilePath)
        );

        $this->cloudStorage->put($cloudFileName, (string) $this->reportWriter->newReader());

        $this->logger->info(sprintf('Report uploaded to cloud on: %s', $cloudFileName));
    }

    private function print(): void
    {
        echo (string) $this->reportWriter->newReader();
    }

    private function logInAsGlofoxBot(Charge $charge): void
    {
        $botUser = User::make([
            'branch_id' => $this->findBranchBasedOnCharge($charge)->id(),
            'first_name' => 'Glofox',
            'last_name' => 'Bot',
        ]);

        Auth::loginAs($botUser);
    }

    private function logInAsBlankUser(Charge $charge): void
    {
        $user = User::make([
            'branch_id' => $this->findBranchBasedOnCharge($charge)->id(),
            'first_name' => '',
            'last_name' => '',
        ]);

        if ($charge->soldByUserId()) {
            $user = $this->findStaffBasedOnCharge($charge);
        }

        Auth::loginAs($user);
    }

    private function findMembershipById($id): Membership
    {
        return $this->membershipsRepository
            ->addCriteria(new Id($id))
            ->firstOrFail(function () use ($id) {
                throw new NotFoundException(sprintf('Membership id (%s) not found', $id));
            });
    }

    private function findPlanByCode(Membership $membership, $planCode): Plan
    {
        $plan = $membership->plans()->where('code', $planCode)->first();

        if (!$plan) {
            throw new NotFoundException(sprintf('Plan code not found (%s) for membership (%s)', $planCode, $membership->id()));
        }

        return $plan;
    }
}
