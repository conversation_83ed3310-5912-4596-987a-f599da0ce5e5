<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: action_request.proto

namespace GRPC\Payments\TransactionService;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>grpc.GetTransactionByIntentPSPUUIDReq</code>
 */
class GetTransactionByIntentPSPUUIDReq extends \Google\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string IntentPSPUUID = 1;</code>
     */
    protected $IntentPSPUUID = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $IntentPSPUUID
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\ActionRequest::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string IntentPSPUUID = 1;</code>
     * @return string
     */
    public function getIntentPSPUUID()
    {
        return $this->IntentPSPUUID;
    }

    /**
     * Generated from protobuf field <code>string IntentPSPUUID = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setIntentPSPUUID($var)
    {
        GPBUtil::checkString($var, True);
        $this->IntentPSPUUID = $var;

        return $this;
    }

}

