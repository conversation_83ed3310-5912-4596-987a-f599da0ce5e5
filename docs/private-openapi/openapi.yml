openapi: 3.0.0
x-stoplight:
  id: mexpl8prmzuuu
info:
  title: Private CoreAPI
  version: 1.0.1
externalDocs:
  description: Find out more about CoreAPI
  url: https://github.com/glofoxinc/api
servers:
  - url: http://localhost:8889
  - url: https://hopper.glofox.com
tags:
  - name: Accesses
  - name: Activities
  - name: Announcements
  - name: Assets
  - name: Auth
  - name: Appointments
  - name: Appointment Slots
  - name: Bookings
  - name: BookingSpots
  - name: Branches
  - name: Charge
  - name: Clients
  - name: Courses
  - name: Events
  - name: Experimental
  - name: Facilities
  - name: Leads
  - name: Payroll Groups
  - name: Payroll Rates
  - name: Price Calculator
  - name: ProductLedGrowth
  - name: Products
  - name: Program
  - name: ProgramSlots
  - name: PushNotifications
  - name: ReCaptcha
  - name: Reports
  - name: Staff
  - name: Users
  - name: UserStorage
paths:
  /announcements/upsert:
    $ref: "./resources/no-version/announcement_upsert.yml"
  /assets/upload/{model}/{id}/{type}:
    $ref: "./resources/no-version/asset_upload.yml"
  /bookings/getAllByModelIdentifierAndModelId/{modelId}/{modelIdentifier}:
    $ref: "./resources/1.0/bookings_by_model_identifier_and_model_id.yml"
  /programs/upsert:
    $ref: "./resources/no-version/program_upsert.yml"
  /programs/remove/{programId}:
    $ref: "./resources/no-version/program_remove.yml"
  /2.0/activities:
    $ref: "./resources/2.0/activities.yml"
  /2.0/login:
    $ref: "./resources/2.0/login.yml"
  /2.0/bookings/first:
    $ref: "./resources/2.0/bookings_first.yml"
  /2.0/branches/{branchId}/reports/member-usage:
    $ref: "./resources/2.0/member_usage.yml"
  /2.0/branches/{branchId}/products:
    $ref: "./resources/2.0/products.yml"
  /2.0/staff:
    $ref: "./resources/2.0/staff.yml"
  /2.0/courses:
    $ref: "./resources/2.0/courses.yml"
  /2.1/bookings:
    $ref: "./resources/2.1/bookings.yml"
  /2.1/branches/{branchId}/store-sales:
    $ref: "./resources/2.1/store_sales.yml"
  /2.1/branches/{branchId}/appointments:
    $ref: "./resources/2.1/appointments.yml"
  /2.1/branches/{branchId}/appointments/{appointmentId}:
    $ref: "./resources/2.1/appointment.yml"
  /2.1/branches/{branchId}/appointments/{appointmentId}/slots:
    $ref: "./resources/2.1/appointment_slots.yml"
  /2.1/branches/{branchId}/appointments/{appointmentId}/slots/{slotId}:
    $ref: "./resources/2.1/appointment_slot.yml"
  /2.1/branches/{branchId}/appointments/{appointmentId}/calculate-price:
    $ref: "./resources/2.1/appointment_price_calculator.yml"
  /2.1/branches/{branchId}/users:
    $ref: "./resources/2.1/users_search.yml"
  /2.1/branches/{branchId}/users/{userId}/switch:
    $ref: "./resources/2.1/user_switch.yml"
  /2.1/branches/{branchId}/events/{eventId}/calculate-price:
    $ref: "./resources/2.1/event_price_calculator.yml"
  /2.1/branches/{branchId}/courses/{courseId}/calculate-price:
    $ref: "./resources/2.1/course_price_calculator.yml"
  /2.1/branches/{branchId}/facilities/{facilityId}/calculate-price:
    $ref: "./resources/2.1/facility_price_calculator.yml"
  /2.2/branches/{branchId}/accesses/:
    $ref: "./resources/2.2/accesses.yml"
  /2.2/branches/{branchId}/calculate-taxes:
    $ref: "./resources/2.2/price_calculator_calculate_taxes.yml"
  /2.2/branches/{branchId}/reports/discount-insights/{discountId}:
    $ref: "./resources/2.2/discount_insights.yml"
  /2.2/branches/{branchId}/reports/sales-metrics:
    $ref: "./resources/2.2/sales_metrics.yml"
  /2.2/branches/{branchId}/reports/member-kpis/{userId}:
    $ref: "./resources/2.2/member_kpis.yml"
  /2.2/branches/{branchId}/reports/visits-insights:
    $ref: "./resources/2.2/visits_insights.yml"
  /2.2/branches/{branchId}/reports/{reportId}:
    $ref: "./resources/2.2/reports.yml"
  /2.2/recaptcha-verify:
    $ref: "./resources/2.2/recaptcha.yml"
  /2.2/plg/create-client:
    $ref: "./resources/2.2/plg_create_client.yml"
  /2.2/branches/{branchId}/events/{eventId}/spots:
    $ref: "./resources/2.2/booking_spots.yml"
  /2.2/branches/{branchId}/users/{userId}/storage:
    $ref: "./resources/2.2/user_storage.yml"
  /2.2/branches/{branchId}/users/{userId}/storage/{storageKey}:
    $ref: "./resources/2.2/user_storage_single.yml"
  /2.2/branches/{branchId}/users/{userId}/cards:
    $ref: "./resources/2.2/user_cards.yml"
  /2.2/branches/{branchId}/users/{userId}/cards/{cardId}:
    $ref: "./resources/2.2/user_cards_single.yml"
  /2.2/charges/by-invoice-id/{invoiceId}:
    $ref: "./resources/2.2/charge_by_invoice.yml"
  /2.2/branches/{branchId}/payroll-groups:
    $ref: "./resources/2.2/payroll_groups.yml"
  /2.2/branches/{branchId}/payroll-groups/{payrollGroupId}:
    $ref: "./resources/2.2/payroll_group.yml"
  /2.2/branches/{branchId}/payroll-rates:
    $ref: "./resources/2.2/payroll_rates.yml"
  /2.2/branches/{branchId}/payroll-rates/{payrollRateId}:
    $ref: "./resources/2.2/payroll_rate.yml"
  /2.2/branches/{branchId}/payroll-reports:
    $ref: "./resources/2.2/payroll_reports.yml"
  /2.2/programs/{programId}/recurring-bookings/slots:
    $ref: "./resources/2.2/program_slots.yml"
  /2.2/branches/{branchId}/users/{userId}/memberships:
    $ref: "./resources/2.2/memberships.yml"
  /2.2/branches/{branchId}/bookings/batch/{batchId}:
    $ref: "./resources/2.2/batch_bookings_cancel.yml"
  /2.2/branches/{branchId}/bookings/recurrent/{batchId}:
    $ref: "./resources/2.2/batch_recurring_bookings.yml"
  /2.2/branches/{branchId}/bookings/recurrent/import:
    $ref: "./resources/2.2/batch_recurring_bookings_import.yml"
  /2.3/branches/{branchId}/users/{userId}/restore:
    $ref: "./resources/2.3/update_user.yml"
  /2.3/branches/{branchId}/push-notifications/group-messages:
    $ref: "./resources/2.3/send_group_message.yml"
  /2.3/branches/{branchId}/leads/{userId}/interactions:
    $ref: "./resources/2.3/create-interaction.yml"
  /2.3/branches/{branchId}/leads/marketing-sources:
    $ref: "./resources/2.3/marketing-sources.yml"
  /2.3/branches/{branchId}/leads/lead-statuses:
    $ref: "./resources/2.3/lead_status.yml"
  /2.3/branches:
    $ref: "./resources/2.3/find_branches.yml"
  /2.4/enterprises:
    $ref: "./resources/2.4/enterprises.yml"
  /2.4/enterprises/locations:
    $ref: "./resources/2.4/enterprises_locations.yml"
  /3.0/clients:
    $ref: "./resources/3.0/clients.yml"
  /v3.0/locations/{locationId}/facilities:
    $ref: "./resources/3.0/facilities.yml"
components:
  schemas:
    $ref: "./schemas/_index.yml"
  securitySchemes:
    $ref: "./security/_index.yml"
