<?php

declare(strict_types=1);

namespace Glofox\Infrastructure\GoogleRecaptcha\Factory;

use Glofox\Domain\Recaptcha\Exception\EmptyRecaptchaToken;
use Glofox\Infrastructure\GoogleRecaptcha\VerifyRequest;

class VerifyRequestFactory
{
    private string $secret;

    public function __construct(string $secret)
    {
        $this->secret = $secret;
    }

    public function create(string $recaptchaToken): VerifyRequest
    {
        if (empty($recaptchaToken)) {
            throw EmptyRecaptchaToken::create();
        }

        return new VerifyRequest(
            $this->secret,
            $recaptchaToken
        );
    }
}