<?php

namespace CakeTestCases\Glofox\Domain\PaymentProviders\Http;

\App::import('Test/Case', "GlofoxControllerTestCase");

use Glofox\Domain\Authentication\Token\TokenGeneratorDto;
use Glofox\Domain\Authentication\TokenGenerator;
use Glofox\Domain\Clients\Enum\Origin;
use Glofox\Domain\Clients\Repositories\ClientsRepository;
use Glofox\Domain\Events\Search\Expressions\Id;
use Glofox\Domain\Experiments\Services\ValidateExperimentAvailability;
use Glofox\Domain\FeatureFlags\Flaggers\StripeDirectDebitFlagger;
use Glofox\Domain\PaymentMethods\Type;

class PaymentProvidersControllerTest extends \GlofoxControllertestCase
{
    public $fixtures = [
        'app.branch_configuration_mobile',
        'app.payment_method',
        'app.payment_provider',
        'app.access_control_list',
        'app.branch',
        'app.user',
        'app.client',
        'app.membership',
        'app.interaction',
        'app.event',
        'app.booking',
        'app.program',
        'app.facility',
        'app.role',
        'app.time_slot_pattern',
        'app.time_slot',
        'app.push_notification',
        'app.integrator',
    ];

    public static $token_unrestricted;
    public static $token_stripedd;
    public static $token_restricted;
    public static $token_stripe_ie_dd_sepa;
    public static $token_stripe_bg_card_only;
    public static $token_stripe_ie_dd_acss;

    public function setUp()
    {
        parent::setUp();

        if (self::$token_unrestricted && self::$token_restricted && self::$token_stripedd && self::$token_stripe_ie_dd_sepa && self::$token_stripe_ie_dd_acss && self::$token_stripe_bg_card_only) {
            return;
        }

        self::$token_unrestricted = app()->make(TokenGenerator::class)
            ->generate(new TokenGeneratorDto([
                '_id' => '59a7011a05c677bda916612a',
                'namespace' => 'glofox',
                'branch_id' => '49a7011a05c677b9a916612a',
                'first_name' => 'admin',
                'last_name' => 'admin',
                'type' => \UserType::ADMIN,
            ]));

        self::$token_restricted = app()->make(TokenGenerator::class)
            ->generate(new TokenGeneratorDto([
                '_id' => '59a7011a05c677bda916612b',
                'namespace' => 'restricted',
                'branch_id' => '5d9cdc06aad495538f810244',
                'first_name' => 'admin',
                'last_name' => 'admin',
                'type' => \UserType::ADMIN,
            ]));

        self::$token_stripedd = app()->make(TokenGenerator::class)
            ->generate(new TokenGeneratorDto([
                '_id' => '59a7011a02c677bda916612b',
                'namespace' => 'stripedd',
                'branch_id' => '49a7011a08c677b9a916612a',
                'first_name' => 'admin',
                'last_name' => 'admin',
                'type' => \UserType::ADMIN,
            ]));

        self::$token_stripe_ie_dd_sepa = app()->make(TokenGenerator::class)
            ->generate(new TokenGeneratorDto([
                '_id' => '59a7011a02c677bda9166578',
                'namespace' => 'stripeddsepa',
                'branch_id' => '49a7011a08c677111916613a',
                'first_name' => 'admin',
                'last_name' => 'admin',
                'type' => \UserType::ADMIN,
            ]));

        self::$token_stripe_ie_dd_acss = app()->make(TokenGenerator::class)
            ->generate(new TokenGeneratorDto([
                '_id' => '59a7011a02c677bda9166560',
                'namespace' => 'stripeddacss',
                'branch_id' => '49a7011a08c845111916614d',
                'first_name' => 'admin',
                'last_name' => 'admin',
                'type' => \UserType::ADMIN,
            ]));

        self::$token_stripe_bg_card_only = app()->make(TokenGenerator::class)
            ->generate(new TokenGeneratorDto([
                '_id' => '49a7011a08c845111916613c',
                'namespace' => 'stripecard',
                'branch_id' => '49a7011a08c845111916613c',
                'first_name' => 'admin',
                'last_name' => 'admin',
                'type' => \UserType::ADMIN,
            ]));
    }

    public function test_it_should_serve_all_total_payment_providers_without_any_restriction()
    {
        $_SERVER['HTTP_AUTHORIZATION'] = self::$token_unrestricted;

        $experimentService = \Mockery::mock(ValidateExperimentAvailability::class);
        $experimentService->shouldReceive('validate')->andReturnFalse();

        app()->instance(ValidateExperimentAvailability::class, $experimentService);

        $result = $this->testAction('/2.1/branches/49a7011a05c677b9a916612a/payment-providers?include=registration_flow', ['method' => 'get']);
        $result = json_decode($result, null, 512, JSON_THROW_ON_ERROR);

        $this->assertTrue($result->success);
        $this->assertCount(2, $result->data);

        foreach ($result->data as $data) {
            if ($data->payment_method_type_id === "CARD") {
                self::assertSame($data->name, "CARD NOOP 1");
            }

            if ($data->payment_method_type_id === "DIRECT_DEBIT") {
                self::assertSame($data->name, "CARD NOOP 3");
            }
        }

        app()->forgetInstance(ValidateExperimentAvailability::class);
    }

    public function test_it_should_serve_all_total_payment_providers_with_selective_fee_adjustment_for_plg(): void
    {
        $this->markTestSkipped('Disabled until this ticket is spec\'ed fully https://glofox.atlassian.net/browse/PLG-6');

        $_SERVER['HTTP_AUTHORIZATION'] = self::$token_stripedd;

        $stripeDirectDebitFlagger = \Mockery::mock(StripeDirectDebitFlagger::class);
        $stripeDirectDebitFlagger->shouldReceive('has')->andReturnTrue();

        app()->instance(StripeDirectDebitFlagger::class, $stripeDirectDebitFlagger);

        $result = $this->testAction('/2.1/branches/49a7011a08c677b9a916612a/payment-providers?includes=available_countries', ['method' => 'get']);
        $result = json_decode($result, null, 512, JSON_THROW_ON_ERROR);

        $this->assertTrue($result->success);
        $this->assertCount(2, $result->data);

        foreach ($result->data as $data) {
            if ($data->payment_method_type_id === Type::CARD) {
                $this->assertTextEquals($data->name, "STRIPE_CUSTOM_EU");
                $this->assertTrue($data->same_card_and_dd);
                $this->assertTextEquals($data->payment_provider_dd_name, "STRIPE_CUSTOM_EU_DD_BECS");

                foreach ($data->available_countries->data as $available_country) {
                    self::assertSame(7, $available_country->default_charge_percentage);
                }
            }
            if ($data->payment_method_type_id === Type::DIRECT_DEBIT) {
                $this->assertTextEquals($data->name, "STRIPE_CUSTOM_EU_DD_BECS");
                $this->assertTrue($data->same_card_and_dd);

                foreach ($data->available_countries->data as $available_country) {
                    self::assertSame(7, $available_country->default_charge_percentage);
                }
            }
        }

        app()->forgetInstance(StripeDirectDebitFlagger::class);
    }

    public function test_it_should_serve_all_total_payment_providers_with_restriction()
    {
        $_SERVER['HTTP_AUTHORIZATION'] = self::$token_restricted;
        $stripeDirectDebitFlagger = \Mockery::mock(StripeDirectDebitFlagger::class);
        $stripeDirectDebitFlagger->shouldReceive('has')->andReturnTrue();
        app()->instance(StripeDirectDebitFlagger::class, $stripeDirectDebitFlagger);

        $result = $this->testAction('/2.1/branches/5d9cdc06aad495538f810244/payment-providers', ['method' => 'get']);
        $result = json_decode($result, null, 512, JSON_THROW_ON_ERROR);

        $this->assertTrue($result->success);
        $this->assertCount(2, $result->data);

        foreach ($result->data as $data) {
            if ($data->payment_method_type_id === "CARD") {
                $this->assertEqual($data->name, "LIFT_BRANDS_STRIPE_CUSTOM_EU");
            }

            if ($data->payment_method_type_id === "DIRECT_DEBIT") {
                $this->assertEqual($data->name, "LIFT_BRANDS_STRIPE_CUSTOM_EU_DD_SEPA");
            }
        }
        app()->forgetInstance(StripeDirectDebitFlagger::class);
    }

    public function test_it_should_serve_all_total_payment_providers_with_direct_debit()
    {
        $_SERVER['HTTP_AUTHORIZATION'] = self::$token_stripedd;

        $stripeDirectDebitFlagger = \Mockery::mock(StripeDirectDebitFlagger::class);
        $stripeDirectDebitFlagger->shouldReceive('has')->andReturnTrue();

        app()->instance(StripeDirectDebitFlagger::class, $stripeDirectDebitFlagger);

        $result = $this->testAction('/2.1/branches/49a7011a08c677b9a916612a/payment-providers', ['method' => 'get']);
        $result = json_decode($result, null, 512, JSON_THROW_ON_ERROR);

        $this->assertTrue($result->success);
        $this->assertCount(2, $result->data);

        foreach ($result->data as $data) {
            if ($data->payment_method_type_id === Type::CARD) {
                $this->assertTextEquals($data->name, "STRIPE_CUSTOM_EU");
                $this->assertTrue($data->same_card_and_dd);
                $this->assertTextEquals($data->payment_provider_dd_name, "STRIPE_CUSTOM_EU_DD_BECS");
            }
            if ($data->payment_method_type_id === Type::DIRECT_DEBIT) {
                $this->assertTextEquals($data->name, "STRIPE_CUSTOM_EU_DD_BECS");
                $this->assertTrue($data->same_card_and_dd);
            }
        }

        app()->forgetInstance(StripeDirectDebitFlagger::class);
    }

    public function test_it_should_serve_all_total_payment_providers_with_direct_debit_with_registration_flow()
    {
        $_SERVER['HTTP_AUTHORIZATION'] = self::$token_stripe_ie_dd_sepa;

        $stripeDirectDebitFlagger = \Mockery::mock(StripeDirectDebitFlagger::class);
        $stripeDirectDebitFlagger->shouldReceive('has')->andReturnTrue();

        app()->instance(StripeDirectDebitFlagger::class, $stripeDirectDebitFlagger);

        $result = $this->testAction('/2.1/branches/49a7011a08c677111916613a/payment-providers?includes=registration_flow,available_countries', ['method' => 'get']);
        $result = json_decode($result, null, 512, JSON_THROW_ON_ERROR);

        $this->assertTrue($result->success);
        $this->assertCount(2, $result->data);

        foreach ($result->data as $data) {
            if ($data->payment_method_type_id === Type::CARD) {
                $this->assertTextEquals("STRIPE_CUSTOM_EU", $data->name);
                $this->assertTrue($data->same_card_and_dd);
                $this->assertTextEquals("STRIPE_CUSTOM_EU_DD_SEPA", $data->payment_provider_dd_name);
                $this->assertTextContains("sepa_debit_payments", $data->registration_flow->redirection_url);
                $this->assertEquals(["sepa_debit_payments"], $data->available_countries->data[1]->features);
            }
            if ($data->payment_method_type_id === Type::DIRECT_DEBIT) {
                $this->assertTextEquals($data->name, "STRIPE_CUSTOM_EU_DD_SEPA");
                $this->assertTrue($data->same_card_and_dd);
            }
        }

        app()->forgetInstance(StripeDirectDebitFlagger::class);
    }

    public function test_it_should_serve_all_providers_even_if_direct_debit_isnt_found()
    {
        $_SERVER['HTTP_AUTHORIZATION'] = self::$token_stripe_bg_card_only;

        $stripeDirectDebitFlagger = \Mockery::mock(StripeDirectDebitFlagger::class);
        $stripeDirectDebitFlagger->shouldReceive('has')->andReturnTrue();

        app()->instance(StripeDirectDebitFlagger::class, $stripeDirectDebitFlagger);

        $result = $this->testAction('/2.1/branches/49a7011a08c845111916613c/payment-providers?includes=registration_flow,available_countries', ['method' => 'get']);
        $result = json_decode($result, null, 512, JSON_THROW_ON_ERROR);

        $this->assertTrue($result->success);
        $this->assertCount(1, $result->data, "should be the card provider returned as part of the data");

        foreach ($result->data as $data) {
            if ($data->payment_method_type_id === Type::CARD) {
                $this->assertTextEquals("STRIPE_CUSTOM_EU", $data->name, "provider name should be present and correct");
                $this->assertTrue($data->same_card_and_dd, "same_card_and_dd variable should be true because direct debit feature flag is enabled");
                $this->assertTextEquals("", $data->payment_provider_dd_name, "should be empty provider name because no direct debit provider was found");
                $this->assertCount(0, $data->available_countries->data[0]->features, "should not contain any feature as none are present in STRIPE_CUSTOM_EU, BG country");
            }
        }

        app()->forgetInstance(StripeDirectDebitFlagger::class);
    }

    public function test_it_should_select_correct_direct_debit_with_same_gateway_as_card()
    {
        $_SERVER['HTTP_AUTHORIZATION'] = self::$token_stripe_ie_dd_acss;
        $stripeDirectDebitFlagger = \Mockery::mock(StripeDirectDebitFlagger::class);
        $stripeDirectDebitFlagger->shouldReceive('has')->andReturnTrue();

        app()->instance(StripeDirectDebitFlagger::class, $stripeDirectDebitFlagger);

        $result = $this->testAction('/2.1/branches/49a7011a08c845111916614d/payment-providers', ['method' => 'get']);
        $result = json_decode($result, null, 512, JSON_THROW_ON_ERROR);

        $this->assertTrue($result->success);
        $this->assertCount(2, $result->data, "There should be 1 CARD provider and 1 DIRECT_DEBIT provider");

        $cardProviderFound = false;
        $directDebitProviderFound = false;
        $gatewayId = null;

        foreach ($result->data as $data) {
            if ($data->payment_method_type_id === Type::CARD) {
            $this->assertTextEquals($data->name, "STRIPE_CUSTOM_EU");
            $this->assertTrue($data->same_card_and_dd);
            $this->assertTextEquals($data->payment_provider_dd_name, "STRIPE_CUSTOM_CA_DD_ACSS_EU");
            $gatewayId = $data->gateway_id;
            $cardProviderFound = true;
            }
            if ($data->payment_method_type_id === Type::DIRECT_DEBIT) {
            $this->assertTextEquals($data->name, "STRIPE_CUSTOM_CA_DD_ACSS_EU");
            $this->assertTrue($data->same_card_and_dd);
            $this->assertEquals($data->gateway_id, $gatewayId, "Gateway ID should match for both CARD and DIRECT_DEBIT providers");
            $directDebitProviderFound = true;
            }
        }

        $this->assertTrue($cardProviderFound, "CARD provider should be found");
        $this->assertTrue($directDebitProviderFound, "DIRECT_DEBIT provider should be found");

        app()->forgetInstance(StripeDirectDebitFlagger::class);
    }
}
