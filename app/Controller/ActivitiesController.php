<?php

/**
 * Class ActivitiesController.
 *
 * @property JWTComponent JWT
 */
class ActivitiesController extends \Glofox\Domain\Activities\Http\ActivitiesController
{
    /**
     * @var string
     */
    public $name = 'Activities';
    /**
     * @var array
     */
    public $components = [
        'JWT',
    ];

    /**
     * Dispatcher for Model.
     *
     * @param string $identifier
     *
     * @return string
     */
    public function dispatcher($identifier = null)
    {
        /** @var Activity $provider */
        $provider = ClassRegistry::init('Activity');

        foreach ($this->request->query as $param) {
            if (is_array($param)) {
                throw new \Glofox\Exception('Invalid query parameter exception');
            }
        }

        return parent::dispatch($identifier, $provider);
    }
}
