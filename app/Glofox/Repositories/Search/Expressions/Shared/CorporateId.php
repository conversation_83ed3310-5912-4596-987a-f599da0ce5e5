<?php

declare(strict_types=1);

namespace Glofox\Repositories\Search\Expressions\Shared;

use Glofox\Repositories\Filters\Evaluable;
use Glofox\Repositories\Search\Expressions\Conditionable;
use Glofox\Repositories\Search\Expressions\HasConditions;
use Webmozart\Expression\Expr;
use Glofox\Repositories\Search\Expressions\Key;

class CorporateId extends Key implements Evaluable, Conditionable
{
    use HasConditions;

    public function __construct(string $corporateId)
    {
        parent::__construct('corporate_id', Expr::equals($corporateId));
    }

    public function field() : string
    {
        return 'corporate_id';
    }
}
