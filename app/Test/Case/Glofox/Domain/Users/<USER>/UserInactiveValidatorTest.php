<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\Users\Validation;

use App;
use Glofox\Domain\Users\Exceptions\UserNotFoundException;
use Glofox\Domain\Users\Http\Requests\RestoreUserRequest;
use Glofox\Domain\Users\Repositories\UsersRepository;
use Glofox\Domain\Users\Validation\Validators\UserInactiveValidator;
use GlofoxTestCase;
use Mockery;

App::import('Test/Case', 'GlofoxTestCase');

class UserInactiveValidatorTest extends GlofoxTestCase
{
    public $fixtures = [];
    private UsersRepository $usersRepositoryMock;
    private RestoreUserRequest $restoreUserRequestMock;

    public function setUp(): void
    {
        parent::setUp();
        $this->usersRepositoryMock = Mockery::mock(UsersRepository::class);
        $this->restoreUserRequestMock = Mockery::mock(RestoreUserRequest::class);
    }

    public function tearDown(): void
    {
        parent::tearDown();
        Mockery::close();
    }

    public function testThrowsException(): void
    {
        $userId = 'test-user-id';
        $active = false;

        $this->restoreUserRequestMock->shouldReceive('getData')->andReturnSelf();
        $this->restoreUserRequestMock->shouldReceive('userId')->andReturn($userId);

        $this->usersRepositoryMock->shouldReceive('findActiveById')->withArgs(
            function (
                string $userIdParam,
                string $activeParam
            ) use ($userId, $active) {
                self::assertEquals(
                    $userId,
                    $userIdParam
                );
                self::assertEquals(
                    $active,
                    $activeParam
                );
                throw UserNotFoundException::withId($userId);
            }
        );
        $this->expectExceptionMessage(
            'User not found - id: ' . $userId
        );

        $validator = new UserInactiveValidator($this->usersRepositoryMock);
        $validator->validate($this->restoreUserRequestMock);
    }
}
