parameters:
  - schema:
      type: string
    name: branchId
    in: path
    required: true
  - schema:
      type: string
    name: appointmentId
    in: path
    required: true
post:
  tags:
    - Appointment Slots
  summary: ''
  operationId: create-appointment-slot
  description: This endpoint creates a new appointment slot
  x-internal: true
  security:
    - coreapi_auth: [ ]
  requestBody:
    required: true
    content:
      application/json:
        schema:
          $ref: '../../requests/2.1/create_appointment_slot.yml'
  responses:
    '200':
      description: OK
      content:
        application/json:
          schema:
            $ref: '../../schemas/appointment_slot.yml'
put:
  tags:
    - Appointment Slots
  summary: ''
  operationId: update-appointment-slot
  description: This endpoint updates an existing appointment slot
  x-internal: true
  security:
    - coreapi_auth: [ ]
  requestBody:
    required: true
    content:
      application/json:
        schema:
          $ref: '../../requests/2.1/update_appointment_slot.yml'
  responses:
    '200':
      description: OK
      content:
        application/json:
          schema:
            $ref: '../../schemas/appointment_slot.yml'