<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\Users\Formatters;

use App;
use Carbon\Carbon;
use Glofox\Domain\Branches\Models\Branch;
use Glofox\Domain\Users\Formatters\DateOfBirthFormatter;
use Glofox\Domain\Users\Models\User;
use Glofox\Domain\Users\Repositories\UsersRepository;
use GlofoxTestCase;
use Mockery;
use MongoDate;
use MongoId;

App::import('Test/Case', 'GlofoxTestCase');

final class DateOfBirthFormatterTest extends GlofoxTestCase
{
    public $fixtures = [
        'app.branch',
    ];
    private UsersRepository $usersRepository;

    public function setUp()
    {
        parent::setUp();

        $this->usersRepository = Mockery::mock(UsersRepository::class);
    }

    public function tearDown()
    {
        parent::tearDown();

        Mockery::close();
    }

    public function testNoDate(): void
    {
        $this->usersRepository
            ->shouldNotReceive('getById')
            ->never();

        $formatter = new DateOfBirthFormatter($this->usersRepository);
        $result = $formatter->format(
            User::make([])
        );

        $this->assertNull($result);
    }

    public function testEmptyStringDate(): void
    {
        $this->usersRepository
            ->shouldNotReceive('getById')
            ->never();

        $formatter = new DateOfBirthFormatter($this->usersRepository);
        $result = $formatter->format(
            User::make(['birth' => ''])
        );

        $this->assertNull($result);
    }

    public function testInstanceOfMongoDate(): void
    {
        $this->usersRepository
            ->shouldNotReceive('getById')
            ->never();

        $date = new MongoDate();

        $formatter = new DateOfBirthFormatter($this->usersRepository);
        $result = $formatter->format(
            User::make([
                'birth' => $date,
            ])
        );

        $this->assertSame($date, $result);
    }

    public function testInteger(): void
    {
        $this->usersRepository
            ->shouldNotReceive('getById')
            ->never();

        $date = time();

        $formatter = new DateOfBirthFormatter($this->usersRepository);
        $result = $formatter->format(
            User::make([
                'birth' => $date,
            ])
        );

        $this->assertInstanceOf(MongoDate::class, $result);
        $this->assertSame($date, $result->sec);
    }

    public function testStringWithLegacyFormat(): void
    {
        $this->usersRepository
            ->shouldNotReceive('getById')
            ->never();

        $date = '1980-05-22';

        $formatter = new DateOfBirthFormatter($this->usersRepository);
        $result = $formatter->format(
            User::make([
                'birth' => $date,
            ])
        );

        $this->assertInstanceOf(MongoDate::class, $result);
        $this->assertSame($date, $result->toDateTime()->format('Y-m-d'));
    }

    public function testStringWithBranch(): void
    {
        $this->usersRepository
            ->shouldNotReceive('getById')
            ->never();

        $date = '22/05/1980';

        $branch = Branch::make([
            'configuration' => [
                'formats' => [
                    'date' => 'DD/MM/YYYY',
                ],
            ],
        ]);

        $user = Mockery::mock(User::class);
        $user->shouldReceive('get')
            ->with('birth')
            ->twice()
            ->andReturn($date)
            ->getMock()
            ->shouldReceive('has')
            ->with('branch_id')
            ->once()
            ->andReturnTrue()
            ->getMock()
            ->shouldReceive('originBranch')
            ->andReturn($branch);

        $formatter = new DateOfBirthFormatter($this->usersRepository);
        $result = $formatter->format($user);

        $this->assertInstanceOf(MongoDate::class, $result);
        $this->assertSame($date, $result->toDateTime()->format('d/m/Y'));
    }

    public function testStringNoBranchUserHasIdAndFetchedUserHasBranchId(): void
    {
        $userId = (string)new MongoId();

        $this->usersRepository
            ->shouldReceive('getById')
            ->with($userId)
            ->andReturn(User::make([
                'branch_id' => (string)new MongoId('49a7011a05c677b9a916612a'),
                'origin_branch_id' => '49a7011a05c677b9a916612a',
            ]));

        $date = '1980/05/22';

        $user = Mockery::mock(User::class);
        $user
            ->shouldReceive('get')
            ->with('birth')
            ->twice()
            ->andReturn($date)
            ->getMock()
            ->shouldReceive('has')
            ->with('branch_id')
            ->once()
            ->andReturnFalse()
            ->getMock()
            ->shouldReceive('has')
            ->with('_id')
            ->once()
            ->andReturnTrue()
            ->getMock()
            ->shouldReceive('id')
            ->andReturn($userId);

        $formatter = new DateOfBirthFormatter($this->usersRepository);
        $result = $formatter->format($user);

        $this->assertInstanceOf(MongoDate::class, $result);
        $this->assertSame($date, $result->toDateTime()->format('Y/m/d'));
    }

    public function testStringNoBranchUserHasIdAndFetchedUserDoesNotHasBranchId(): void
    {
        $userId = (string)new MongoId();

        $this->usersRepository
            ->shouldReceive('getById')
            ->with($userId)
            ->andReturn(User::make([]));

        $date = '22/05/1980';

        $user = Mockery::mock(User::class);
        $user
            ->shouldReceive('get')
            ->with('birth')
            ->twice()
            ->andReturn($date)
            ->getMock()
            ->shouldReceive('has')
            ->with('branch_id')
            ->once()
            ->andReturnFalse()
            ->getMock()
            ->shouldReceive('has')
            ->with('_id')
            ->andReturnTrue()
            ->getMock()
            ->shouldReceive('id')
            ->andReturn($userId);

        $formatter = new DateOfBirthFormatter($this->usersRepository);
        $result = $formatter->format($user);

        $this->assertInstanceOf(MongoDate::class, $result);
        $this->assertSame($date, $result->toDateTime()->format('d/m/Y'));
    }

    public function testStringNoBranchUserDoesNotHaveId(): void
    {
        $userId = (string)new MongoId();

        $this->usersRepository
            ->shouldReceive('getById')
            ->with($userId)
            ->andReturn(User::make([]));

        $date = '22/05/1980';

        $user = Mockery::mock(User::class);
        $user
            ->shouldReceive('get')
            ->with('birth')
            ->twice()
            ->andReturn($date)
            ->getMock()
            ->shouldReceive('has')
            ->with('branch_id')
            ->once()
            ->andReturnFalse()
            ->getMock()
            ->shouldReceive('has')
            ->with('_id')
            ->andReturnFalse();

        $formatter = new DateOfBirthFormatter($this->usersRepository);
        $result = $formatter->format($user);

        $this->assertInstanceOf(MongoDate::class, $result);
        $this->assertSame($date, $result->toDateTime()->format('d/m/Y'));
    }

    public function testArrayWithSeconds(): void
    {
        $this->usersRepository
            ->shouldNotReceive('getById')
            ->never();

        $date = 1_632_237_250;

        $formatter = new DateOfBirthFormatter($this->usersRepository);
        $result = $formatter->format(
            User::make([
                'birth' => [
                    'sec' => $date,
                ],
            ])
        );

        $this->assertInstanceOf(MongoDate::class, $result);
        $this->assertSame($date, $result->sec);
    }

    public function testNull(): void
    {
        $this->usersRepository
            ->shouldNotReceive('getById')
            ->never();

        $formatter = new DateOfBirthFormatter($this->usersRepository);
        $result = $formatter->format(
            User::make([
                'birth' => 12.12, // float -> not valid
            ])
        );

        $this->assertNull($result);
    }

    public function testFutureDate(): void
    {
        $this->usersRepository
            ->shouldNotReceive('getById')
            ->never();

        $date = new MongoDate(Carbon::create()->addWeeks(3)->timestamp);

        $formatter = new DateOfBirthFormatter($this->usersRepository);
        $result = $formatter->format(
            User::make([
                'birth' => $date,
            ])
        );

        $this->assertNull($result);
    }
}
