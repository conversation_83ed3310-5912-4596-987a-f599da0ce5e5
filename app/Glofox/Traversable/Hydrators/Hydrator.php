<?php

namespace Glofox\Traversable\Hydrators;

abstract class Hydrator
{
    /**
     * @var string
     */
    protected $instance;

    /**
     * @var string
     */
    protected $expected;

    /**
     * @param string $className
     *
     * @return $this
     */
    public function expects(string $className)
    {
        $this->expected = $className;
        return $this;
    }

    public function instance($instance)
    {
        $this->instance = $instance;
        return $this;
    }
}