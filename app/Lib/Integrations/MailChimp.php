<?php
App::uses('IntegrationLayer', 'Lib/Integrations');

class MailChimp implements IntegrationLayer
{
    public function __construct($env)
    {
    }

    /**
     * Used to create a new integration in the branch
     * @param array $payload
     * @param array $integration
     * @return array
     */
    public function create($payload, $integration)
    {
        //TODO: implement
        return $payload;
    }

    /**
     * Used to update an existing integration in the branch
     * @param array $payload
     * @param array $integration
     * @return array
     */
    public function update($payload, $integration)
    {
        //TODO: implement
        return $integration;
    }

    /**
     * Used to confirm an integration that requires a second step
     * @param array $payload
     * @param array $integration
     * @return array
     */
    public function confirm($payload, $integration)
    {
        // No need to implement this call
        return ['success' => true];
    }


    /**
     * Used to delete an integration.
     * Some providers may require customised steps for turning off the integration
     * @param array $payload
     * @param array $integration
     * @return array
     */
    public function delete($payload, $integration)
    {
        // No need to implement this call
        return ['success' => true];
    }

    /**
     * Used internally to push new changes (create or update records)
     * An array of records should be passed, even if it is just one
     * Each record should have an identifier of the model that belongs to
     * @param array $integration
     * @param array $records
     * @return mixed
     */
    public function push($integration, $records)
    {
        //TODO: implement
        return true;
    }

    /**
     * Used for external services to notify changes in the data and then do updated when it is needed
     * @param array $integration
     * @param array $payload
     * @return mixed
     */
    public function sync($integration, $payload)
    {
        // No need to implement this call
        return true;
    }
}
