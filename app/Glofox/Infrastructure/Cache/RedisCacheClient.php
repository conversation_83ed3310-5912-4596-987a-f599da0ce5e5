<?php

namespace Glofox\Infrastructure\Cache;

use Carbon\Carbon;
use Glofox\Domain\Cache\CacheClientInterface;
use Predis\Client;

class RedisCacheClient implements CacheClientInterface
{
    private Client $client;

    private CachePrefix $prefix;

    public function __construct(Client $client, CachePrefix $prefix)
    {
        $this->client = $client;
        $this->prefix = $prefix;
    }

    public function count(string $key): int
    {
        $key = $this->prefix($key);

        return (int) $this->client->zcount($key, '-inf', '+inf');
    }

    public function delete(string $key): void
    {
        $key = $this->prefix($key);
        $this->client->del($key);
    }

    public function add(string $key, int $score, string $value): void
    {
        $key = $this->prefix($key);
        // For some reason, the interface defines 2 args only, but Redis zadd command needs 3
        $this->client->zadd($key, $score, $value);
    }

    public function set(string $key, string $value): void
    {
        $key = $this->prefix($key);
        $this->client->set($key, $value);
    }

    public function has(string $key): bool
    {
        $key = $this->prefix($key);

        return (bool) $this->client->get($key);
    }

    public function get(string $key)
    {
        $key = $this->prefix($key);

        return $this->client->get($key);
    }

    public function expiresAt(string $key, Carbon $expiresAt): void
    {
        $key = $this->prefix($key);
        $this->client->expireat($key, $expiresAt->getTimestamp());
    }

    public function setIfNotExist(string $key, string $value): bool
    {
        $key = $this->prefix($key);

        return (bool)$this->client->setnx($key, $value);
    }

    private function prefix(string $key): string
    {
        return sprintf('%s[%s]', $this->prefix->toString(), $key);
    }

    /**
     * This method has been included to allow us to manage cache keys without prefix
     * as they are used in other projects (such bookings typescript project)
     */
    public function addWithoutPrefix(string $key, int $score, string $value, ?Carbon $expiresAt): void
    {
        $this->client->zadd($key, $score, $value);

        if (!is_null($expiresAt)) {
            $this->client->expireat($key, $expiresAt->getTimestamp());
        }
    }

    /**
     * This method has been included to allow us to manage cache keys without prefix
     * as they are used in other projects (such bookings typescript project)
     */
    public function countWithoutPrefix(string $key): int
    {
        return (int) $this->client->zcount($key, '-inf', '+inf');
    }

    public function deleteWithoutPrefix(string $key): void
    {
        $this->client->del($key);
    }

    public function getListWithoutPrefix(string $key, int $start = 0, int $stop = -1): array
    {
        return $this->client->zrange($key, $start, $stop);
    }

    public function deleteValueInListWithoutPrefix(string $key, string $value): void
    {
        $this->client->zrem($key, $value);
    }

    public function incr(string $key): int
    {
        $key = $this->prefix($key);
        return $this->client->incr($key);
    }
}
