<?php

use Glofox\Report\TimeslotPerformance;

App::import('Test/Case', 'GlofoxTestCase');

/**
 * TimeslotPerformanceTest Test Case.
 */
class TimeslotPerformanceTest extends GlofoxTestCase
{
    public function testWithTimeslots()
    {
        $timeslotsData = [
            [
                'id' => '1',
            ],
        ];

        $timeslotPerformanceReport = ( new TimeslotPerformance() )->withTimeslots($timeslotsData);

        $this->assertEquals($timeslotPerformanceReport->timeslots(), $timeslotsData);
    }

    /**
     * testWithBookings method.
     */
    public function testWithBookings()
    {
        $bookingsData = [
            [
                'Booking' => [
                    'id' => '1',
                    'time_slot_id' => '1',
                ],
            ],
        ];

        $timeslotPerformanceReport = ( new TimeslotPerformance() )->withBookings($bookingsData);

        $this->assertEquals($timeslotPerformanceReport->bookingsMap()['1'], $bookingsData[0]['Booking']);
    }

    /**
     * testWithBookings method.
     */
    public function testGenerate()
    {
        $timeslots = [
            [
                'TimeSlot' => [
                    '_id' => '1',
                    'time_start' => 'Timeslot',
                ],
            ],
        ];

        $bookings = [
            [
                'Booking' => [
                    '_id' => '1',
                    'time_start' => 'Booking',
                    'status' => 'BOOKED',
                    'time_slot_id' => '1',
                    'attended' => true,
                ],
            ],
        ];

        $timeslotPerformanceReport = ( new TimeslotPerformance() )->withTimeslots($timeslots)
                                                                  ->withBookings($bookings)
                                                                  ->generate();

        $report = $timeslotPerformanceReport->toArray();

        $this->assertNotEmpty($report);

        $allAppointments = $report[0];

        $this->assertEquals($allAppointments->name, 'All appointments');
        $this->assertEquals($allAppointments->events, 1);
        $this->assertEquals($allAppointments->attendance, 1);
        $this->assertEquals($allAppointments->capacity, 1);
        $this->assertEquals($allAppointments->bookings, 1);

        $appointmentInstance = $allAppointments->instances[0];

        $this->assertNotEmpty($appointmentInstance);
        $this->assertEquals($appointmentInstance->name, 'Timeslot');
        $this->assertEquals($appointmentInstance->attendance, 1);
        $this->assertEquals($appointmentInstance->capacity, 1);
        $this->assertEquals($appointmentInstance->bookings, 1);
    }
}
