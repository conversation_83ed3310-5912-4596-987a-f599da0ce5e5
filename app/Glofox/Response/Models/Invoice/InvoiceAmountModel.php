<?php

namespace Glofox\Response\Models\Invoice;

use Glofox\Response\BaseModel;

/**
 * @SuppressWarnings(PHPMD)
 */
class InvoiceAmountModel extends BaseModel
{
    /**
     * @var float
     */
    public $processing_fees = 0;
    /**
     * @var float
     */
    public $processing_fees_vat = 0;
    /**
     * @var float
     */
    public $dispute_fees = 0;
    /**
     * @var float
     */
    public $dispute_fees_vat = 0;
    /**
     * @var float
     */
    public $total_vat = 0;
    /**
     * @var float
     */
    public $total = 0;
    /**
     * @var float
     */
    public $debited = 0;
    /**
     * @var float
     */
    public $total_due = 0;

    protected array $allowedFields = [
        'processing_fees',
        'processing_fees_vat',
        'dispute_fees',
        'dispute_fees_vat',
        'total_vat',
        'total',
        'debited',
        'total_due'
    ];

    public function __construct(array $data)
    {
        $this->setModelFields($data);
    }
}
