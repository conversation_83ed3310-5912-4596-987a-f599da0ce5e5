<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: subscription_service.proto

namespace GRPC\Payments\SubscriptionService;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>grpc.SynchronousPaymentSettings</code>
 */
class SynchronousPaymentSettings extends \Google\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>int32 CustomerID = 1;</code>
     */
    protected $CustomerID = 0;
    /**
     * Generated from protobuf field <code>int32 MerchantID = 2;</code>
     */
    protected $MerchantID = 0;
    /**
     * Generated from protobuf field <code>string PaymentMethod = 3;</code>
     */
    protected $PaymentMethod = '';
    /**
     * Generated from protobuf field <code>int32 PaymentMethodID = 4;</code>
     */
    protected $PaymentMethodID = 0;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int $CustomerID
     *     @type int $MerchantID
     *     @type string $PaymentMethod
     *     @type int $PaymentMethodID
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\SubscriptionService::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>int32 CustomerID = 1;</code>
     * @return int
     */
    public function getCustomerID()
    {
        return $this->CustomerID;
    }

    /**
     * Generated from protobuf field <code>int32 CustomerID = 1;</code>
     * @param int $var
     * @return $this
     */
    public function setCustomerID($var)
    {
        GPBUtil::checkInt32($var);
        $this->CustomerID = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 MerchantID = 2;</code>
     * @return int
     */
    public function getMerchantID()
    {
        return $this->MerchantID;
    }

    /**
     * Generated from protobuf field <code>int32 MerchantID = 2;</code>
     * @param int $var
     * @return $this
     */
    public function setMerchantID($var)
    {
        GPBUtil::checkInt32($var);
        $this->MerchantID = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string PaymentMethod = 3;</code>
     * @return string
     */
    public function getPaymentMethod()
    {
        return $this->PaymentMethod;
    }

    /**
     * Generated from protobuf field <code>string PaymentMethod = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setPaymentMethod($var)
    {
        GPBUtil::checkString($var, True);
        $this->PaymentMethod = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 PaymentMethodID = 4;</code>
     * @return int
     */
    public function getPaymentMethodID()
    {
        return $this->PaymentMethodID;
    }

    /**
     * Generated from protobuf field <code>int32 PaymentMethodID = 4;</code>
     * @param int $var
     * @return $this
     */
    public function setPaymentMethodID($var)
    {
        GPBUtil::checkInt32($var);
        $this->PaymentMethodID = $var;

        return $this;
    }

}

