<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Payments\Providers\Gateway\Merchants;

use Glofox\Domain\PaymentMethods\Exceptions\PaymentMethodNotNoopTypeException;
use Glofox\Domain\PaymentMethods\Models\PaymentMethod;
use Glofox\Domain\PaymentMethods\Type;
use Glofox\Domain\PaymentProviders\Models\PaymentProvider;
use Glofox\Payments\Contracts\PaymentProviderContract;
use Glofox\Payments\Entities\Merchant\Contracts\MerchantHandlerContract;
use Glofox\Payments\Entities\Merchant\Internal\ConnectedMerchant;
use Glofox\Payments\Entities\Merchant\Internal\CreateOrUpdateMerchant;
use Glofox\Payments\Entities\Merchant\Models\Account;
use Glofox\Payments\PaymentsHandler;
use Glofox\Payments\Providers\Gateway\Merchants\MerchantNoopAccountCreator;

\App::import('Test/Case', 'GlofoxTestCase');

class MerchantNoopAccountCreatorTest extends \GlofoxTestCase
{
    /** @var PaymentsHandler */
    private $paymentsHandler;

    public function setUp(): void
    {
        parent::setUp();
        $this->paymentsHandler = \Mockery::mock(PaymentsHandler::class);
    }

    public function tearDown()
    {
        parent::tearDown();

        \Mockery::close();
    }

    public function test_it_throws_exception_if_payment_method_is_not_noop_type(): void
    {
        $this->setExpectedException(PaymentMethodNotNoopTypeException::class);

        $this->createInstance()->create(
            PaymentMethod::make([
                'type_id' => Type::CARD,
            ]), 'eur'
        );
    }

    public function test_it_returns_the_account_id_according_to_the_gateway_response(): void
    {
        $paymentMethod = PaymentMethod::make([
            '_id' => 'foo-123',
            'type_id' => Type::CASH,
            'branch_id' => 'branch-123',
        ]);

        $paymentProvider = \Mockery::mock(PaymentProviderContract::class);
        $paymentProvider->shouldReceive('paymentProvider')
            ->andReturn(
                new PaymentProvider()
            );

        $paymentProvider->shouldReceive('merchants')
            ->andReturn(
                \Mockery::mock(MerchantHandlerContract::class)
                    ->shouldReceive('create')
                    ->withArgs(
                        function (CreateOrUpdateMerchant $parameters) use ($paymentMethod) {
                            $this->assertSame($paymentMethod->branchId(), $parameters->branch()->id());
                            $this->assertSame($paymentMethod->branchId(), $parameters->externalRef());
                            $this->assertSame($paymentMethod->branchId(), $parameters->merchantId());
                            $this->assertEmpty($parameters->payload()->all());
                            $this->assertTextEquals('eur', $parameters->branch()->currency());

                            return true;
                        }
                    )
                    ->andReturn(
                        (new ConnectedMerchant())->setAccount(
                            (new Account())->setId('*********')
                        )
                    )
                    ->getMock()
            );

        $this->paymentsHandler
            ->shouldReceive('providerByPaymentMethod')
            ->with($paymentMethod)
            ->andReturn($paymentProvider);

        $accountId = $this->createInstance()->create($paymentMethod, 'eur');

        $this->assertSame('*********', $accountId);
    }

    private function createInstance(): MerchantNoopAccountCreator
    {
        return new MerchantNoopAccountCreator(
            $this->paymentsHandler
        );
    }
}
