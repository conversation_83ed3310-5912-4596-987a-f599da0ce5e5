<?php

declare(strict_types=1);

namespace Glofox\Infrastructure\GoogleRecaptcha;

use Glofox\Infrastructure\GoogleRecaptcha\Exception\CouldNotVerifyRecaptcha;
use Glofox\Infrastructure\GoogleRecaptcha\Factory\VerifyResponseFactory;
use Guz<PERSON><PERSON>ttp\Client as GuzzleHttpClient;
use Psr\Log\LoggerInterface;

class RecaptchaVerificationClient
{
    private const ENDPOINT = '/siteverify';

    private GuzzleHttpClient $client;

    private VerifyResponseFactory $verifyResponseFactory;

    private LoggerInterface $logger;

    public function __construct(
        GuzzleHttpClient $client,
        VerifyResponseFactory $verifyResponseFactory,
        LoggerInterface $logger
    ) {
        $this->client = $client;
        $this->verifyResponseFactory = $verifyResponseFactory;
        $this->logger = $logger;
    }

    public function verify(VerifyRequest $verifyRequest): VerifyResponse
    {
        $this->logger->info('Verifying recaptcha using GoogleRecaptcha');

        try {
            $httpResponse = $this->client->request(
                'POST',
                self::ENDPOINT,
                [
                    'json' => $verifyRequest->getPayload()
                ]
            );

            $this->logger->info(
                \sprintf('Received response from GoogleRecaptcha: %s', $httpResponse->getBody()->getContents())
            );
        }
        catch (\Throwable $throwable) {
            throw CouldNotVerifyRecaptcha::wrap($throwable);
        }

        $response = $this->verifyResponseFactory->create($httpResponse);

        // According to Google reCAPTCHA's official documentation, captcha validation failures will return a
        // 200 OK response instead of a 4XX, which forces us to validate the contents of the response
        // to identify potential errors returned by their APIs.
        if (!$response->isSuccess()) {
            throw CouldNotVerifyRecaptcha::dueToErrors($response->getErrorCodes());
        }

        $this->logger->info('Recaptcha verification succeeded');

        return $response;
    }
}