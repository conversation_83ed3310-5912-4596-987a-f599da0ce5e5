<?php

namespace Glofox\Infrastructure\Cache;

use Glofox\Domain\Cache\CacheClientFactory;
use Glofox\Domain\Cache\CacheClientInterface;
use Predis\Client as PredisClient;

class RedisCacheClientFactory implements CacheClientFactory
{
    /** @var PredisClientFactory */
    private \Predis\Client $client;

    public function __construct(
        PredisClient $client
    ) {
        $this->client = $client;
    }

    public function create(CachePrefix $prefix): CacheClientInterface
    {
        return new RedisCacheClient($this->client, $prefix);
    }
}
