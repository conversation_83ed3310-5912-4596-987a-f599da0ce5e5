<?php

declare(strict_types=1);

namespace Glofox\Domain\AppointmentSlots\Http\Requests;

use Carbon\Carbon;
use Glofox\Request;

class UpdateAppointmentSlotRequest extends Request
{
    public function locationId(): string
    {
        return (string)$this->cakeRouteParams()->get('locationId');
    }

    public function appointmentId(): string
    {
        return (string)$this->cakeRouteParams()->get('appointmentId');
    }

    public function slotId(): string
    {
        return (string)$this->cakeRouteParams()->get('slotId');
    }


    public function staffId(): ?string
    {
        return $this->has('staff_id') ? (string)$this->get('staff_id') : null;
    }


    public function timeStart(): ?Carbon
    {
        if (!$this->has('time_start')) {
            return null;
        }

        $timeStart = $this->get('time_start');
        return Carbon::createFromTimestamp($timeStart)->utc();
    }

    public function date(): Carbon
    {
        return $this->timeStart()->startOfDay();
    }

}
