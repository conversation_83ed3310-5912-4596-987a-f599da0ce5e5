# LANGUAGE translation of CakePHP Application
# <AUTHOR> <EMAIL>
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PROJECT VERSION\n"
"POT-Creation-Date: 2014-08-12 08:18+0000\n"
"PO-Revision-Date: YYYY-mm-DD HH:MM+ZZZZ\n"
"Last-Translator: NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <EMAIL@ADDRESS>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=INTEGER; plural=EXPRESSION;\n"

#: Controller/AnnouncementsController.php:56;90
msgid "Empty Branch Id"
msgstr ""

#: Controller/AnnouncementsController.php:89
msgid "Empty User Id"
msgstr ""

#: Controller/AnnouncementsController.php:155
msgid "Announcement saved."
msgstr ""

#: Controller/AnnouncementsController.php:171;195
msgid "Invalid Announcement"
msgstr ""

#: Controller/AnnouncementsController.php:176
msgid "The Announcement has been saved."
msgstr ""

#: Controller/AnnouncementsController.php:199
msgid "The Announcement has been deleted."
msgstr ""

#: Controller/AnnouncementsController.php:357
#: Controller/FacilitiesController.php:235
msgid "Invalid facility"
msgstr ""

#: Controller/BookingsController.php:34
#: Controller/EventsController.php:78
msgid "Invalid Event Id"
msgstr ""

#: Controller/BookingsController.php:37;116;164
msgid "Invalid User Id"
msgstr ""

#: Controller/BookingsController.php:41;120
msgid "Member Not Found"
msgstr ""

#: Controller/BookingsController.php:42;121
msgid "Your account is not active."
msgstr ""

#: Controller/BookingsController.php:46
msgid "Event Not Found"
msgstr ""

#: Controller/BookingsController.php:113;171
msgid "Invalid Booking Id"
msgstr ""

#: Controller/BookingsController.php:125
msgid "Booking Not Found"
msgstr ""

#: Controller/BookingsController.php:131
msgid "Branch Not Found"
msgstr ""

#: Controller/BookingsController.php:133
msgid "This branch haven't set the booking cancel window parameter"
msgstr ""

#: Controller/BookingsController.php:141
msgid "You cannot cancel this booking in such a short notice. The cancel booking window is "
msgstr ""

#: Controller/BookingsController.php:161
#: Controller/BranchesController.php:121;138
#: Controller/EventsController.php:36;75
#: Controller/FacilitiesController.php:63;85
#: Controller/UsersController.php:198;220;270;398
msgid "Invalid Branch Id"
msgstr ""

#: Controller/BookingsController.php:181
msgid "No Booking"
msgstr ""

#: Controller/BranchesController.php:63;183
#: Controller/UsersController.php:98;185
msgid "Branch saved."
msgstr ""

#: Controller/BranchesController.php:79
#: Controller/UsersController.php:275;403
msgid "Invalid Branch"
msgstr ""

#: Controller/BranchesController.php:84
msgid "The Branch has been saved."
msgstr ""

#: Controller/BranchesController.php:103
#: Controller/ClientsController.php:94;118
msgid "Invalid Client"
msgstr ""

#: Controller/BranchesController.php:107
#: Controller/ClientsController.php:122
msgid "The Client has been deleted."
msgstr ""

#: Controller/ClientsController.php:78;186
msgid "Client saved."
msgstr ""

#: Controller/ClientsController.php:99
msgid "The Client has been saved."
msgstr ""

#: Controller/ClientsController.php:189
msgid "There was a problem saving the client"
msgstr ""

#: Controller/EventsController.php:96
msgid "No Event"
msgstr ""

#: Controller/FacilitiesController.php:88
msgid "Invalid Facility Id"
msgstr ""

#: Controller/FacilitiesController.php:102
msgid "No Facilty"
msgstr ""

#: Controller/FacilitiesController.php:125
msgid "Facility saved."
msgstr ""

#: Controller/FacilitiesController.php:141;165
msgid "Invalid Facility"
msgstr ""

#: Controller/FacilitiesController.php:146
msgid "The Facility has been saved."
msgstr ""

#: Controller/FacilitiesController.php:169
msgid "The Facility has been deleted."
msgstr ""

#: Controller/FeaturesController.php:53
msgid "Feature saved."
msgstr ""

#: Controller/FeaturesController.php:69;93
msgid "Invalid Feature"
msgstr ""

#: Controller/FeaturesController.php:73
msgid "The Feature has been saved."
msgstr ""

#: Controller/FeaturesController.php:96
msgid "Feature deleted"
msgstr ""

#: Controller/FeaturesController.php:98
msgid "Feature deleted Fail"
msgstr ""

#: Controller/FeaturesController.php:111
msgid "Feature deleteAll success"
msgstr ""

#: Controller/FeaturesController.php:113
msgid "Feature deleteAll Fail"
msgstr ""

#: Controller/FeaturesController.php:129
msgid "Feature updateAll success"
msgstr ""

#: Controller/FeaturesController.php:131
msgid "Feature updateAll Fail"
msgstr ""

#: Controller/ProgramsController.php:184;211
msgid "Invalid Program"
msgstr ""

#: Controller/ProgramsController.php:189
msgid "The Program has been saved."
msgstr ""

#: Controller/ProgramsController.php:215
msgid "The Program has been deleted."
msgstr ""

#: Controller/ProgramsController.php:320
msgid "Invalid program"
msgstr ""

#: Controller/ProgramsController.php:962
msgid "Invalid Program Id"
msgstr ""

#: Controller/ProgramsController.php:965
msgid "No Program With that Id"
msgstr ""

#: Controller/UsersController.php:130;149;804;1079
msgid "Invalid User"
msgstr ""

#: Controller/UsersController.php:134
msgid "The User has been deleted."
msgstr ""

#: Controller/UsersController.php:154
msgid "The User has been saved."
msgstr ""

#: Controller/UsersController.php:223
msgid "Invalid Trainer Id"
msgstr ""

#: Controller/UsersController.php:233
msgid "No Trainer"
msgstr ""

#: Controller/UsersController.php:263;386;535
msgid "Empty Namespace"
msgstr ""

#: Controller/UsersController.php:267;390
msgid "The user information is empty"
msgstr ""

#: Controller/UsersController.php:283
msgid "Invalid Credentials"
msgstr ""

#: Controller/UsersController.php:301;478
msgid "Invalid POST Request"
msgstr ""

#: Controller/UsersController.php:392
msgid "The password field is empty"
msgstr ""

#: Controller/UsersController.php:395
msgid "The phone field is empty"
msgstr ""

#: Controller/UsersController.php:430
msgid "This user already exists"
msgstr ""

#: Controller/UsersController.php:450
msgid "This branch does not allow pay as you go"
msgstr ""

#: Controller/UsersController.php:459
msgid "We couldn't validate your membership, please contact the gym to create your account."
msgstr ""

#: Controller/UsersController.php:526
msgid "Empty Login"
msgstr ""

#: Controller/UsersController.php:530
msgid "Empty Password"
msgstr ""

#: Controller/UsersController.php:581
msgid "Invalid Username or Password"
msgstr ""

#: Controller/UsersController.php:676
msgid "Invalid url. Please requst a new email."
msgstr ""

#: Controller/UsersController.php:690
msgid "The password was changes. Try to login now!"
msgstr ""

#: Controller/UsersController.php:697
msgid "The Password could not be saved. Please, try again."
msgstr ""

#: Controller/UsersController.php:701
msgid "The passwords fields must match."
msgstr ""

#: Controller/UsersController.php:997
msgid "Member Blocked"
msgstr ""

#: Controller/UsersController.php:1251
msgid "Invalid Staff Membe"
msgstr ""

#: Controller/WebsitesController.php:520
msgid "Registraition Failed. You're membership could not be found. Contact the gym to register"
msgstr ""

#: Controller/WebsitesController.php:528
msgid "Registration Successful. You can now sign in and book classes"
msgstr ""

#: Controller/WebsitesController.php:534
msgid "Registraition Failed. Try Again"
msgstr ""

#: Controller/Component/UserContextComponent.php:24
msgid "Your session has expired."
msgstr ""

#: Controller/Component/UserContextComponent.php:43
msgid "Forbidden Access."
msgstr ""

#: Plugin/Dompdf/View/PdfView.php:69
#: Cache/Engine/FileEngine.php:384
msgid "%s is not writable"
msgstr ""

#: Plugin/Mongodb/Model/Datasource/MongodbSource.php:590
#: Model/Datasource/DboSource.php:2966
msgid "Invalid schema object"
msgstr ""

#: View/Elements/branch_website_top_menu.ctp:30
#: View/Elements/common/iframe_header.ctp:60
msgid "<i class=\"entypo-logout\"></i> &nbsp Log Out"
msgstr ""

#: View/Elements/branch_website_top_menu.ctp:38
#: View/Elements/common/iframe_header.ctp:68
msgid "<i class=\"entypo-login\"></i> &nbsp Log In"
msgstr ""

#: View/Elements/branch_website_top_menu.ctp:42
#: View/Elements/common/iframe_header.ctp:72
msgid "<i class=\"entypo-users\"></i> &nbsp Register"
msgstr ""

#: View/Elements/top_menu.ctp:43
msgid "Log Out"
msgstr ""

#: View/Elements/top_menu.ctp:48
msgid "<i class=\"fa fa-power-off\"></i> &nbsp Log Out"
msgstr ""

#: View/Elements/common/iframe_header.ctp:41
msgid "View My Bookings"
msgstr ""

#: View/Elements/common/iframe_header.ctp:44;49
msgid "Classes"
msgstr ""

#: View/Programs/program_dates.ctp:48
#: View/Users/<USER>
#: View/Scaffolds/index.ctp:70
msgid "previous"
msgstr ""

#: View/Programs/program_dates.ctp:50
#: View/Users/<USER>
#: View/Scaffolds/index.ctp:72
msgid "next"
msgstr ""

#: View/Websites/bookings.ctp:93
msgid "Login to Book"
msgstr ""

#: View/Websites/login.ctp:3
#: View/Websites/register.ctp:3
msgid "Back to Bookings"
msgstr ""

#: View/ViewBlock.php:80
msgid "A view block with the name '%s' is already/still open."
msgstr ""

#: Model/Announcement.php:validation for field title
#: Model/User.php:validation for field first_name
msgid "Please insert your first name"
msgstr ""

#: Model/Announcement.php:validation for field title
#: Model/Facility.php:validation for field name
#: Model/User.php:validation for field first_name
msgid "Name must be no larger than 100 characters long."
msgstr ""

#: Model/Booking.php:validation for field namespace
msgid "Please insert a namespace"
msgstr ""

#: Model/Booking.php:validation for field branch_id
msgid "Please insert a branch id"
msgstr ""

#: Model/Booking.php:validation for field user_id
msgid "Please insert an user id"
msgstr ""

#: Model/Booking.php:validation for field event_id
msgid "Please insert an event id"
msgstr ""

#: Model/Booking.php:validation for field time_start
#: Model/Program.php:validation for field date_start
msgid "Enter a valid start date in YYYY-MM-DD format."
msgstr ""

#: Model/Booking.php:validation for field time_finish
#: Model/Program.php:validation for field date_finish
msgid "Enter a valid end date in YYYY-MM-DD format."
msgstr ""

#: Model/Booking.php:validation for field status
msgid "Please insert an status"
msgstr ""

#: Model/Booking.php:validation for field status
msgid "Booking status not valid. Allowed types are BOOKED, WAITING, CANCELED."
msgstr ""

#: Model/Client.php:validation for field namespace
msgid "That namespace is taken, please select a different one."
msgstr ""

#: Model/Client.php:validation for field namespace
msgid "The namespace can only contain letters and numbers"
msgstr ""

#: Model/Client.php:validation for field name
msgid "Please insert client name"
msgstr ""

#: Model/Facility.php:validation for field name
msgid "Please insert Facility name"
msgstr ""

#: Model/Facility.php:validation for field description
msgid "Please insert Facility description"
msgstr ""

#: Model/Facility.php:validation for field description
msgid "Description must be no larger than 700 characters long."
msgstr ""

#: Model/Facility.php:validation for field list_visible
msgid "Incorrect value for list_visible"
msgstr ""

#: Model/Program.php:validation for field name
msgid "Please insert the class name"
msgstr ""

#: Model/Program.php:validation for field description
msgid "Please insert the class description"
msgstr ""

#: Model/Program.php:validation for field date_start
msgid "Please insert the class start date"
msgstr ""

#: Model/Program.php:validation for field date_finish
msgid "Please insert the class end date"
msgstr ""

#: Model/User.php:validation for field last_name
msgid "Please insert your last name"
msgstr ""

#: Model/User.php:validation for field last_name
msgid "Surname must be no larger than 100 characters long."
msgstr ""

#: Model/User.php:validation for field login
msgid "Please insert your login"
msgstr ""

#: Model/User.php:validation for field login
msgid "A user is already using that login."
msgstr ""

#: Model/User.php:validation for field password
msgid "Please insert your password"
msgstr ""

#: Model/User.php:validation for field email
msgid "Please insert a valid email"
msgstr ""

#: Model/User.php:validation for field email
msgid "An user is already using that email."
msgstr ""

#: Model/User.php:validation for field birth
msgid "Enter a valid date in YYYY-MM-DD format."
msgstr ""

#: Model/User.php:validation for field type
msgid "Invalid User Type."
msgstr ""

#: Plugin/DebugKit/Console/Command/BenchmarkShell.php:41
msgid "-> Testing :url"
msgstr ""

#: Plugin/DebugKit/Console/Command/BenchmarkShell.php:67
msgid "Total Requests made: :requests"
msgstr ""

#: Plugin/DebugKit/Console/Command/BenchmarkShell.php:68
msgid "Total Time elapsed: :duration (seconds)"
msgstr ""

#: Plugin/DebugKit/Console/Command/BenchmarkShell.php:72
msgid "Requests/Second: :rps req/sec"
msgstr ""

#: Plugin/DebugKit/Console/Command/BenchmarkShell.php:76
msgid "Average request time: :average-time seconds"
msgstr ""

#: Plugin/DebugKit/Console/Command/BenchmarkShell.php:80
msgid "Standard deviation of average request time: :std-dev"
msgstr ""

#: Plugin/DebugKit/Console/Command/BenchmarkShell.php:84
msgid "Longest/shortest request: :longest sec/:shortest sec"
msgstr ""

#: Plugin/DebugKit/Console/Command/BenchmarkShell.php:134
msgid "Allows you to obtain some rough benchmarking statisticsabout a fully qualified URL."
msgstr ""

#: Plugin/DebugKit/Console/Command/BenchmarkShell.php:139
msgid "The URL to request."
msgstr ""

#: Plugin/DebugKit/Console/Command/BenchmarkShell.php:144
msgid "Number of iterations to perform."
msgstr ""

#: Plugin/DebugKit/Console/Command/BenchmarkShell.php:148
msgid "Maximum total time for all iterations, in seconds.If a single iteration takes more than the timeout, only one request will be made"
msgstr ""

#: Plugin/DebugKit/Console/Command/BenchmarkShell.php:152
msgid "Example Use: `cake benchmark --n 10 --t 100 http://localhost/testsite`. <info>Note:</info> this benchmark does not include browser render times."
msgstr ""

#: Plugin/DebugKit/Controller/Component/ToolbarComponent.php:159
msgid "Component initialization"
msgstr ""

#: Plugin/DebugKit/Controller/Component/ToolbarComponent.php:281
msgid "Controller action"
msgstr ""

#: Plugin/DebugKit/Controller/Component/ToolbarComponent.php:284
msgid "Controller action start"
msgstr ""

#: Plugin/DebugKit/Controller/Component/ToolbarComponent.php:304
msgid "Processing toolbar state"
msgstr ""

#: Plugin/DebugKit/Controller/Component/ToolbarComponent.php:327
msgid "Processing toolbar data"
msgstr ""

#: Plugin/DebugKit/Controller/Component/ToolbarComponent.php:362
msgid "Controller render start"
msgstr ""

#: Plugin/DebugKit/Controller/Component/ToolbarComponent.php:446
msgid "Could not load DebugToolbar panel %s"
msgstr ""

#: Plugin/DebugKit/Lib/DebugTimer.php:128
msgid "Core Processing (Derived from $_SERVER[\"REQUEST_TIME\"])"
msgstr ""

#: Plugin/DebugKit/Lib/FireCake.php:310
msgid "Headers already sent in %s on line %s. Cannot send log data to FirePHP."
msgstr ""

#: Plugin/DebugKit/Lib/FireCake.php:331
msgid "Incorrect parameter count for FireCake::fb()"
msgstr ""

#: Plugin/DebugKit/Lib/FireCake.php:410
msgid "Maximum number (99,999) of messages reached!"
msgstr ""

#: Plugin/DebugKit/View/Elements/debug_toolbar.ctp:23
msgid "There are no active panels. You must enable a panel to see its output."
msgstr ""

#: Plugin/DebugKit/View/Elements/environment_panel.ctp:20
msgid "App Constants"
msgstr ""

#: Plugin/DebugKit/View/Elements/environment_panel.ctp:36
msgid "CakePHP Constants"
msgstr ""

#: Plugin/DebugKit/View/Elements/environment_panel.ctp:52
msgid "PHP Environment"
msgstr ""

#: Plugin/DebugKit/View/Elements/environment_panel.ctp:70
msgid "Hidef Environment"
msgstr ""

#: Plugin/DebugKit/View/Elements/history_panel.ctp:19
msgid "Request History"
msgstr ""

#: Plugin/DebugKit/View/Elements/history_panel.ctp:21
msgid "No previous requests logged."
msgstr ""

#: Plugin/DebugKit/View/Elements/history_panel.ctp:23
msgid "previous requests available"
msgstr ""

#: Plugin/DebugKit/View/Elements/history_panel.ctp:25
msgid "Restore to current request"
msgstr ""

#: Plugin/DebugKit/View/Elements/include_panel.ctp:19
msgid "Included Files"
msgstr ""

#: Plugin/DebugKit/View/Elements/log_panel.ctp:19
msgid "Logs"
msgstr ""

#: Plugin/DebugKit/View/Elements/log_panel.ctp:27
msgid "Time"
msgstr ""

#: Plugin/DebugKit/View/Elements/log_panel.ctp:27
#: Plugin/DebugKit/View/Elements/timer_panel.ctp:42;66
msgid "Message"
msgstr ""

#: Plugin/DebugKit/View/Elements/log_panel.ctp:38
msgid "There were no log entries made this request"
msgstr ""

#: Plugin/DebugKit/View/Elements/request_panel.ctp:19
msgid "Request"
msgstr ""

#: Plugin/DebugKit/View/Elements/request_panel.ctp:27
msgid "No post data."
msgstr ""

#: Plugin/DebugKit/View/Elements/request_panel.ctp:36
msgid "No querystring data."
msgstr ""

#: Plugin/DebugKit/View/Elements/request_panel.ctp:49
msgid "Current Route"
msgstr ""

#: Plugin/DebugKit/View/Elements/session_panel.ctp:19
msgid "Session"
msgstr ""

#: Plugin/DebugKit/View/Elements/sql_log_panel.ctp:24
msgid "Sql Logs"
msgstr ""

#: Plugin/DebugKit/View/Elements/sql_log_panel.ctp:39
msgid "No query logs when debug < 2."
msgstr ""

#: Plugin/DebugKit/View/Elements/sql_log_panel.ctp:41
msgid "No query logs."
msgstr ""

#: Plugin/DebugKit/View/Elements/sql_log_panel.ctp:45
msgid "Total Time: %s ms <br />Total Queries: %s queries"
msgstr ""

#: Plugin/DebugKit/View/Elements/sql_log_panel.ctp:54
msgid "Query Explain:"
msgstr ""

#: Plugin/DebugKit/View/Elements/sql_log_panel.ctp:57
msgid "Click an \"Explain\" link above, to see the query explanation."
msgstr ""

#: Plugin/DebugKit/View/Elements/sql_log_panel.ctp:63
msgid "No active database connections"
msgstr ""

#: Plugin/DebugKit/View/Elements/timer_panel.ctp:35
msgid "Memory"
msgstr ""

#: Plugin/DebugKit/View/Elements/timer_panel.ctp:38
msgid "Peak Memory Use"
msgstr ""

#: Plugin/DebugKit/View/Elements/timer_panel.ctp:42
msgid "Memory use"
msgstr ""

#: Plugin/DebugKit/View/Elements/timer_panel.ctp:55
msgid "Timers"
msgstr ""

#: Plugin/DebugKit/View/Elements/timer_panel.ctp:57
msgid "%s (ms)"
msgstr ""

#: Plugin/DebugKit/View/Elements/timer_panel.ctp:58
msgid "Total Request Time:"
msgstr ""

#: Plugin/DebugKit/View/Elements/timer_panel.ctp:67
msgid "Time in ms"
msgstr ""

#: Plugin/DebugKit/View/Elements/timer_panel.ctp:68
msgid "Graph"
msgstr ""

#: Plugin/DebugKit/View/Elements/variables_panel.ctp:19
msgid "View Variables"
msgstr ""

#: Plugin/DebugKit/View/Helper/DebugTimerHelper.php:45
msgid "Rendering View"
msgstr ""

#: Plugin/DebugKit/View/Helper/DebugTimerHelper.php:60
msgid "Rendering %s"
msgstr ""

#: Plugin/DebugKit/View/Helper/DebugTimerHelper.php:86
msgid "View render complete"
msgstr ""

#: Plugin/DebugKit/View/Helper/HtmlToolbarHelper.php:226
msgid "Explain"
msgstr ""

#: Plugin/DebugKit/View/Helper/SimpleGraphHelper.php:80
msgid "Starting %sms into the request, taking %sms"
msgstr ""

#: Plugin/DebugKit/View/Helper/TidyHelper.php:99
msgid "No markup errors found"
msgstr ""

#: Plugin/DebugKit/View/Helper/ToolbarHelper.php:189
msgid "maybe slow"
msgstr ""

#: Plugin/DebugKit/Console/Command/WhitespaceShell.php:90
msgid "Absolute path or relative to APP."
msgstr ""

#: Console/ConsoleErrorHandler.php:57
msgid "<error>Error:</error> %s\n%s"
msgstr ""

#: Console/ConsoleErrorHandler.php:81
msgid "%s in [%s, line %s]"
msgstr ""

#: Console/ConsoleErrorHandler.php:82
msgid "<error>%s Error:</error> %s\n"
msgstr ""

#: Console/ConsoleInputArgument.php:98
msgid " <comment>(optional)</comment>"
msgstr ""

#: Console/ConsoleInputArgument.php:101
#: Console/ConsoleInputOption.php:130
msgid " <comment>(choices: %s)</comment>"
msgstr ""

#: Console/ConsoleInputArgument.php:145
msgid "\"%s\" is not a valid value for %s. Please use one of \"%s\""
msgstr ""

#: Console/ConsoleInputOption.php:95
msgid "Short option \"%s\" is invalid, short options must be one letter."
msgstr ""

#: Console/ConsoleInputOption.php:127
msgid " <comment>(default: %s)</comment>"
msgstr ""

#: Console/ConsoleInputOption.php:190
msgid "\"%s\" is not a valid value for --%s. Please use one of \"%s\""
msgstr ""

#: Console/ConsoleOptionParser.php:147
msgid "Display this help."
msgstr ""

#: Console/ConsoleOptionParser.php:154
msgid "Enable verbose output."
msgstr ""

#: Console/ConsoleOptionParser.php:158
msgid "Enable quiet output."
msgstr ""

#: Console/ConsoleOptionParser.php:482
msgid "Missing required arguments. %s is required."
msgstr ""

#: Console/ConsoleOptionParser.php:566
msgid "Unknown short option `%s`"
msgstr ""

#: Console/ConsoleOptionParser.php:582
msgid "Unknown option `%s`"
msgstr ""

#: Console/ConsoleOptionParser.php:634
msgid "Too many arguments."
msgstr ""

#: Console/HelpFormatter.php:70
msgid "<info>Usage:</info>"
msgstr ""

#: Console/HelpFormatter.php:75
msgid "<info>Subcommands:</info>"
msgstr ""

#: Console/HelpFormatter.php:86
msgid "To see help on a subcommand use <info>`cake %s [subcommand] --help`</info>"
msgstr ""

#: Console/HelpFormatter.php:93
msgid "<info>Options:</info>"
msgstr ""

#: Console/HelpFormatter.php:108
msgid "<info>Arguments:</info>"
msgstr ""

#: Console/Shell.php:232
#: Console/Command/ServerShell.php:112
msgid "<info>Welcome to CakePHP %s Console</info>"
msgstr ""

#: Console/Shell.php:234
#: Console/Command/ServerShell.php:114
msgid "App : %s"
msgstr ""

#: Console/Shell.php:235
#: Console/Command/ServerShell.php:115
#: Console/Command/Task/PluginTask.php:64
#: Console/Command/Task/TestTask.php:112
msgid "Path: %s"
msgstr ""

#: Console/Shell.php:664
msgid "<error>Error:</error> %s"
msgstr ""

#: Console/Shell.php:702
msgid "<warning>File `%s` exists</warning>"
msgstr ""

#: Console/Shell.php:703
msgid "Do you want to overwrite?"
msgstr ""

#: Console/Shell.php:706
msgid "<error>Quitting</error>."
msgstr ""

#: Console/Shell.php:709
msgid "Skip `%s`"
msgstr ""

#: Console/Shell.php:713
msgid "Creating file %s"
msgstr ""

#: Console/Shell.php:720
msgid "<success>Wrote</success> `%s`"
msgstr ""

#: Console/Shell.php:724
msgid "<error>Could not write to `%s`</error>."
msgstr ""

#: Console/Shell.php:744
msgid "PHPUnit is not installed. Do you want to bake unit test files anyway?"
msgstr ""

#: Console/Shell.php:750
msgid "You can download PHPUnit from %s"
msgstr ""

#: Console/Command/AclShell.php:77
msgid "Error: Your current CakePHP configuration is set to an ACL implementation other than DB."
msgstr ""

#: Console/Command/AclShell.php:78
msgid "Please change your core config to reflect your decision to use DbAcl before attempting to use this script"
msgstr ""

#: Console/Command/AclShell.php:80
msgid "Current ACL Classname: %s"
msgstr ""

#: Console/Command/AclShell.php:88
#: Console/Command/BakeShell.php:88
#: Console/Command/I18nShell.php:54
msgid "Your database configuration was not found. Take a moment to create one."
msgstr ""

#: Console/Command/AclShell.php:133
msgid "/ can not be used as an alias!"
msgstr ""

#: Console/Command/AclShell.php:133
msgid "\t/ is the root, please supply a sub alias"
msgstr ""

#: Console/Command/AclShell.php:139
msgid "<success>New %s</success> '%s' created."
msgstr ""

#: Console/Command/AclShell.php:141
msgid "There was a problem creating a new %s '%s'."
msgstr ""

#: Console/Command/AclShell.php:157
msgid "Node Not Deleted"
msgstr ""

#: Console/Command/AclShell.php:157
msgid "There was an error deleting the %s. Check that the node exists."
msgstr ""

#: Console/Command/AclShell.php:159
msgid "<success>%s deleted.</success>"
msgstr ""

#: Console/Command/AclShell.php:180
msgid "Error in setting new parent. Please make sure the parent node exists, and is not a descendant of the node specified."
msgstr ""

#: Console/Command/AclShell.php:182
msgid "Node parent set to %s"
msgstr ""

#: Console/Command/AclShell.php:200
msgid "Supplied Node '%s' not found"
msgstr ""

#: Console/Command/AclShell.php:201;317;319;528
msgid "No tree returned."
msgstr ""

#: Console/Command/AclShell.php:204
msgid "Path:"
msgstr ""

#: Console/Command/AclShell.php:238
msgid "%s is <success>allowed</success>."
msgstr ""

#: Console/Command/AclShell.php:240
msgid "%s is <error>not allowed</error>."
msgstr ""

#: Console/Command/AclShell.php:253
msgid "Permission <success>granted</success>."
msgstr ""

#: Console/Command/AclShell.php:255
msgid "Permission was <error>not granted</error>."
msgstr ""

#: Console/Command/AclShell.php:268
msgid "Permission denied."
msgstr ""

#: Console/Command/AclShell.php:270
msgid "Permission was not denied."
msgstr ""

#: Console/Command/AclShell.php:283
msgid "Permission inherited."
msgstr ""

#: Console/Command/AclShell.php:285
msgid "Permission was not inherited."
msgstr ""

#: Console/Command/AclShell.php:317;319;528
#: Console/Command/ApiShell.php:92
#: Console/Command/Task/DbConfigTask.php:254
#: Utility/Folder.php:653
msgid "%s not found"
msgstr ""

#: Console/Command/AclShell.php:369
msgid "Type of node to create."
msgstr ""

#: Console/Command/AclShell.php:373
msgid "A console tool for managing the DbAcl"
msgstr ""

#: Console/Command/AclShell.php:375
msgid "Create a new ACL node"
msgstr ""

#: Console/Command/AclShell.php:377
msgid "Creates a new ACL object <node> under the parent"
msgstr ""

#: Console/Command/AclShell.php:378
msgid "You can use `root` as the parent when creating nodes to create top level nodes."
msgstr ""

#: Console/Command/AclShell.php:382
msgid "The node selector for the parent."
msgstr ""

#: Console/Command/AclShell.php:386
msgid "The alias to use for the newly created node."
msgstr ""

#: Console/Command/AclShell.php:392
msgid "Deletes the ACL object with the given <node> reference"
msgstr ""

#: Console/Command/AclShell.php:394
msgid "Delete an ACL node."
msgstr ""

#: Console/Command/AclShell.php:398
msgid "The node identifier to delete."
msgstr ""

#: Console/Command/AclShell.php:404
msgid "Moves the ACL node under a new parent."
msgstr ""

#: Console/Command/AclShell.php:406
msgid "Moves the ACL object specified by <node> beneath <parent>"
msgstr ""

#: Console/Command/AclShell.php:410
msgid "The node to move"
msgstr ""

#: Console/Command/AclShell.php:414
msgid "The new parent for <node>."
msgstr ""

#: Console/Command/AclShell.php:420
msgid "Print out the path to an ACL node."
msgstr ""

#: Console/Command/AclShell.php:423
msgid "Returns the path to the ACL object specified by <node>."
msgstr ""

#: Console/Command/AclShell.php:424
msgid "This command is useful in determining the inheritance of permissions for a certain object in the tree."
msgstr ""

#: Console/Command/AclShell.php:429
msgid "The node to get the path of"
msgstr ""

#: Console/Command/AclShell.php:435
msgid "Check the permissions between an ACO and ARO."
msgstr ""

#: Console/Command/AclShell.php:438
msgid "Use this command to check ACL permissions."
msgstr ""

#: Console/Command/AclShell.php:441
msgid "ARO to check."
msgstr ""

#: Console/Command/AclShell.php:442
msgid "ACO to check."
msgstr ""

#: Console/Command/AclShell.php:443
msgid "Action to check"
msgstr ""

#: Console/Command/AclShell.php:447
msgid "Grant an ARO permissions to an ACO."
msgstr ""

#: Console/Command/AclShell.php:450
msgid "Use this command to grant ACL permissions. Once executed, the ARO specified (and its children, if any) will have ALLOW access to the specified ACO action (and the ACO's children, if any)."
msgstr ""

#: Console/Command/AclShell.php:453
msgid "ARO to grant permission to."
msgstr ""

#: Console/Command/AclShell.php:454
msgid "ACO to grant access to."
msgstr ""

#: Console/Command/AclShell.php:455
msgid "Action to grant"
msgstr ""

#: Console/Command/AclShell.php:459
msgid "Deny an ARO permissions to an ACO."
msgstr ""

#: Console/Command/AclShell.php:462
msgid "Use this command to deny ACL permissions. Once executed, the ARO specified (and its children, if any) will have DENY access to the specified ACO action (and the ACO's children, if any)."
msgstr ""

#: Console/Command/AclShell.php:465
msgid "ARO to deny."
msgstr ""

#: Console/Command/AclShell.php:466
msgid "ACO to deny."
msgstr ""

#: Console/Command/AclShell.php:467
msgid "Action to deny"
msgstr ""

#: Console/Command/AclShell.php:471
msgid "Inherit an ARO's parent permissions."
msgstr ""

#: Console/Command/AclShell.php:474
msgid "Use this command to force a child ARO object to inherit its permissions settings from its parent."
msgstr ""

#: Console/Command/AclShell.php:477
msgid "ARO to have permissions inherit."
msgstr ""

#: Console/Command/AclShell.php:478
msgid "ACO to inherit permissions on."
msgstr ""

#: Console/Command/AclShell.php:479
msgid "Action to inherit"
msgstr ""

#: Console/Command/AclShell.php:483
msgid "View a tree or a single node's subtree."
msgstr ""

#: Console/Command/AclShell.php:486
msgid "The view command will return the ARO or ACO tree."
msgstr ""

#: Console/Command/AclShell.php:487
msgid "The optional node parameter allows you to return"
msgstr ""

#: Console/Command/AclShell.php:488
msgid "only a portion of the requested tree."
msgstr ""

#: Console/Command/AclShell.php:492
msgid "The optional node to view the subtree of."
msgstr ""

#: Console/Command/AclShell.php:496
msgid "Initialize the DbAcl tables. Uses this command : cake schema create DbAcl"
msgstr ""

#: Console/Command/AclShell.php:564
msgid "Could not find node using reference \"%s\""
msgstr ""

#: Console/Command/ApiShell.php:100
msgid "%s::%s() could not be found"
msgstr ""

#: Console/Command/ApiShell.php:117
msgid "Select a number to see the more information about a specific method. q to quit. l to list."
msgstr ""

#: Console/Command/ApiShell.php:119
msgid "Done"
msgstr ""

#: Console/Command/ApiShell.php:147
msgid "Either a full path or type of class (model, behavior, controller, component, view, helper)"
msgstr ""

#: Console/Command/ApiShell.php:149
msgid "A CakePHP core class name (e.g: Component, HtmlHelper)."
msgstr ""

#: Console/Command/ApiShell.php:152
msgid "The specific method you want help on."
msgstr ""

#: Console/Command/ApiShell.php:153
msgid "Lookup doc block comments for classes in CakePHP."
msgstr ""

#: Console/Command/ApiShell.php:190
msgid "Command %s not found"
msgstr ""

#: Console/Command/ApiShell.php:207
#: View/Helper/NumberHelper.php:63
#: View/Helper/TextHelper.php:78
#: View/Helper/TimeHelper.php:61
msgid "%s could not be found"
msgstr ""

#: Console/Command/BakeShell.php:92
msgid "Interactive Bake Shell"
msgstr ""

#: Console/Command/BakeShell.php:94
msgid "[D]atabase Configuration"
msgstr ""

#: Console/Command/BakeShell.php:95
msgid "[M]odel"
msgstr ""

#: Console/Command/BakeShell.php:96
msgid "[V]iew"
msgstr ""

#: Console/Command/BakeShell.php:97
msgid "[C]ontroller"
msgstr ""

#: Console/Command/BakeShell.php:98
msgid "[P]roject"
msgstr ""

#: Console/Command/BakeShell.php:99
msgid "[F]ixture"
msgstr ""

#: Console/Command/BakeShell.php:100
msgid "[T]est case"
msgstr ""

#: Console/Command/BakeShell.php:101
#: Console/Command/I18nShell.php:71
msgid "[Q]uit"
msgstr ""

#: Console/Command/BakeShell.php:103
msgid "What would you like to Bake?"
msgstr ""

#: Console/Command/BakeShell.php:129
msgid "You have made an invalid selection. Please choose a type of class to Bake by entering D, M, V, F, T, or C."
msgstr ""

#: Console/Command/BakeShell.php:197
msgid "<success>Bake All complete</success>"
msgstr ""

#: Console/Command/BakeShell.php:200
msgid "Bake All could not continue without a valid model"
msgstr ""

#: Console/Command/BakeShell.php:212
msgid "The Bake script generates controllers, views and models for your application. If run with no command line arguments, Bake guides the user through the class creation process. You can customize the generation process by telling Bake where different parts of your application are using command line arguments."
msgstr ""

#: Console/Command/BakeShell.php:217
msgid "Bake a complete MVC. optional <name> of a Model"
msgstr ""

#: Console/Command/BakeShell.php:219
msgid "Bake a new app folder in the path supplied or in current directory if no path is specified"
msgstr ""

#: Console/Command/BakeShell.php:222
msgid "Bake a new plugin folder in the path supplied or in current directory if no path is specified."
msgstr ""

#: Console/Command/BakeShell.php:225
msgid "Bake a database.php file in config directory."
msgstr ""

#: Console/Command/BakeShell.php:228
msgid "Bake a model."
msgstr ""

#: Console/Command/BakeShell.php:231
msgid "Bake views for controllers."
msgstr ""

#: Console/Command/BakeShell.php:234
msgid "Bake a controller."
msgstr ""

#: Console/Command/BakeShell.php:237
msgid "Bake a fixture."
msgstr ""

#: Console/Command/BakeShell.php:240
msgid "Bake a unit test."
msgstr ""

#: Console/Command/BakeShell.php:243
msgid "Database connection to use in conjunction with `bake all`."
msgstr ""

#: Console/Command/BakeShell.php:248
#: Console/Command/Task/ControllerTask.php:493
#: Console/Command/Task/FixtureTask.php:90
#: Console/Command/Task/ModelTask.php:998
#: Console/Command/Task/ProjectTask.php:437
#: Console/Command/Task/TestTask.php:566
#: Console/Command/Task/ViewTask.php:437
msgid "Theme to use when baking code."
msgstr ""

#: Console/Command/CommandListShell.php:45
msgid "<info>Current Paths:</info>"
msgstr ""

#: Console/Command/CommandListShell.php:51
msgid "<info>Changing Paths:</info>"
msgstr ""

#: Console/Command/CommandListShell.php:52
msgid "Your working path should be the same as your application path. To change your path use the '-app' param."
msgstr ""

#: Console/Command/CommandListShell.php:53
msgid "Example: %s or %s"
msgstr ""

#: Console/Command/CommandListShell.php:55
msgid "<info>Available Shells:</info>"
msgstr ""

#: Console/Command/CommandListShell.php:125
msgid "To run an app or core command, type <info>cake shell_name [args]</info>"
msgstr ""

#: Console/Command/CommandListShell.php:126
msgid "To run a plugin command, type <info>cake Plugin.shell_name [args]</info>"
msgstr ""

#: Console/Command/CommandListShell.php:127
msgid "To get help on a specific command, type <info>cake shell_name --help</info>"
msgstr ""

#: Console/Command/CommandListShell.php:164
msgid "Get the list of available shells for this CakePHP application."
msgstr ""

#: Console/Command/CommandListShell.php:166
msgid "Does nothing (deprecated)"
msgstr ""

#: Console/Command/CommandListShell.php:170
msgid "Get the listing as XML."
msgstr ""

#: Console/Command/ConsoleShell.php:91;250
msgid "Model classes:"
msgstr ""

#: Console/Command/ConsoleShell.php:99
msgid "There was an error loading the routes config. Please check that the file exists and contains no errors."
msgstr ""

#: Console/Command/ConsoleShell.php:212
msgid "Invalid command"
msgstr ""

#: Console/Command/ConsoleShell.php:277
msgid "Created %s association between %s and %s"
msgstr ""

#: Console/Command/ConsoleShell.php:280
msgid "Please verify you are using valid models and association types"
msgstr ""

#: Console/Command/ConsoleShell.php:314
msgid "Removed %s association between %s and %s"
msgstr ""

#: Console/Command/ConsoleShell.php:317
msgid "Please verify you are using valid models, valid current association, and valid association types"
msgstr ""

#: Console/Command/ConsoleShell.php:376
msgid "No result set found"
msgstr ""

#: Console/Command/ConsoleShell.php:379
msgid "%s is not a valid model"
msgstr ""

#: Console/Command/ConsoleShell.php:403
msgid "Saved record for %s"
msgstr ""

#: Console/Command/ConsoleShell.php:431
msgid "Please verify that you selected a valid model"
msgstr ""

#: Console/Command/ConsoleShell.php:442
msgid "There was an error loading the routes config. Please check that the file exists and is free of parse errors."
msgstr ""

#: Console/Command/ConsoleShell.php:444
msgid "Routes configuration reloaded, %d routes connected"
msgstr ""

#: Console/Command/I18nShell.php:66
msgid "<info>I18n Shell</info>"
msgstr ""

#: Console/Command/I18nShell.php:68
msgid "[E]xtract POT file from sources"
msgstr ""

#: Console/Command/I18nShell.php:69
msgid "[I]nitialize i18n database table"
msgstr ""

#: Console/Command/I18nShell.php:70
msgid "[H]elp"
msgstr ""

#: Console/Command/I18nShell.php:73
msgid "What would you like to do?"
msgstr ""

#: Console/Command/I18nShell.php:87
msgid "You have made an invalid selection. Please choose a command to execute by entering E, I, H, or Q."
msgstr ""

#: Console/Command/I18nShell.php:110
msgid "I18n Shell initializes i18n database table for your application and generates .pot files(s) with translations."
msgstr ""

#: Console/Command/I18nShell.php:112
msgid "Initialize the i18n table."
msgstr ""

#: Console/Command/I18nShell.php:114
msgid "Extract the po translations from your application"
msgstr ""

#: Console/Command/SchemaShell.php:112
msgid "Schema file (%s) could not be found."
msgstr ""

#: Console/Command/SchemaShell.php:123
msgid "Generating Schema..."
msgstr ""

#: Console/Command/SchemaShell.php:138
msgid "Schema file exists.\n [O]verwrite\n [S]napshot\n [Q]uit\nWould you like to do?"
msgstr ""

#: Console/Command/SchemaShell.php:192;195
msgid "Schema file: %s generated"
msgstr ""

#: Console/Command/SchemaShell.php:212
msgid "Schema could not be loaded"
msgstr ""

#: Console/Command/SchemaShell.php:236
msgid "SQL dump file created in %s"
msgstr ""

#: Console/Command/SchemaShell.php:239
msgid "SQL dump could not be created"
msgstr ""

#: Console/Command/SchemaShell.php:282
msgid "Performing a dry run."
msgstr ""

#: Console/Command/SchemaShell.php:298
msgid "The chosen schema could not be loaded. Attempted to load:"
msgstr ""

#: Console/Command/SchemaShell.php:299
msgid "File: %s"
msgstr ""

#: Console/Command/SchemaShell.php:300
msgid "Name: %s"
msgstr ""

#: Console/Command/SchemaShell.php:333;393
msgid "Schema is up to date."
msgstr ""

#: Console/Command/SchemaShell.php:337
msgid "The following table(s) will be dropped."
msgstr ""

#: Console/Command/SchemaShell.php:340
msgid "Are you sure you want to drop the table(s)?"
msgstr ""

#: Console/Command/SchemaShell.php:341
msgid "Dropping table(s)."
msgstr ""

#: Console/Command/SchemaShell.php:345
msgid "The following table(s) will be created."
msgstr ""

#: Console/Command/SchemaShell.php:348
msgid "Are you sure you want to create the table(s)?"
msgstr ""

#: Console/Command/SchemaShell.php:349
msgid "Creating table(s)."
msgstr ""

#: Console/Command/SchemaShell.php:352
msgid "End create."
msgstr ""

#: Console/Command/SchemaShell.php:366
msgid "Comparing Database to Schema..."
msgstr ""

#: Console/Command/SchemaShell.php:397
msgid "The following statements will run."
msgstr ""

#: Console/Command/SchemaShell.php:399
msgid "Are you sure you want to alter the tables?"
msgstr ""

#: Console/Command/SchemaShell.php:401
msgid "Updating Database..."
msgstr ""

#: Console/Command/SchemaShell.php:405
msgid "End update."
msgstr ""

#: Console/Command/SchemaShell.php:418
msgid "Sql could not be run"
msgstr ""

#: Console/Command/SchemaShell.php:426
msgid "%s is up to date."
msgstr ""

#: Console/Command/SchemaShell.php:429
msgid "Dry run for %s :"
msgstr ""

#: Console/Command/SchemaShell.php:447
msgid "%s updated."
msgstr ""

#: Console/Command/SchemaShell.php:462
msgid "The plugin to use."
msgstr ""

#: Console/Command/SchemaShell.php:466
msgid "Set the db config to use."
msgstr ""

#: Console/Command/SchemaShell.php:470
msgid "Path to read and write schema.php"
msgstr ""

#: Console/Command/SchemaShell.php:474
msgid "File name to read and write."
msgstr ""

#: Console/Command/SchemaShell.php:478
msgid "Classname to use. If its Plugin.class, both name and plugin options will be set."
msgstr ""

#: Console/Command/SchemaShell.php:482
msgid "Snapshot number to use/make."
msgstr ""

#: Console/Command/SchemaShell.php:486
msgid "Specify models as comma separated list."
msgstr ""

#: Console/Command/SchemaShell.php:489
msgid "Perform a dry run on create and update commands. Queries will be output instead of run."
msgstr ""

#: Console/Command/SchemaShell.php:494
msgid "Force \"generate\" to create a new schema"
msgstr ""

#: Console/Command/SchemaShell.php:498
msgid "Write the dumped SQL to a file."
msgstr ""

#: Console/Command/SchemaShell.php:501
msgid "Tables to exclude as comma separated list."
msgstr ""

#: Console/Command/SchemaShell.php:506
msgid "The Schema Shell generates a schema object from the database and updates the database from the schema."
msgstr ""

#: Console/Command/SchemaShell.php:508
msgid "Read and output the contents of a schema file"
msgstr ""

#: Console/Command/SchemaShell.php:514
msgid "Reads from --connection and writes to --path. Generate snapshots with -s"
msgstr ""

#: Console/Command/SchemaShell.php:518
msgid "Generate a snapshot."
msgstr ""

#: Console/Command/SchemaShell.php:522
msgid "Dump database SQL based on a schema file to stdout."
msgstr ""

#: Console/Command/SchemaShell.php:528
msgid "Drop and create tables based on the schema file."
msgstr ""

#: Console/Command/SchemaShell.php:533;546
msgid "Name of schema to use."
msgstr ""

#: Console/Command/SchemaShell.php:536;549
msgid "Only create the specified table."
msgstr ""

#: Console/Command/SchemaShell.php:541
msgid "Alter the tables based on the schema file."
msgstr ""

#: Console/Command/ServerShell.php:116
msgid "DocumentRoot: %s"
msgstr ""

#: Console/Command/ServerShell.php:127
msgid "<warning>This command is available on %s or above</warning>"
msgstr ""

#: Console/Command/ServerShell.php:139
msgid "built-in server is running in http://%s%s/"
msgstr ""

#: Console/Command/ServerShell.php:153
msgid "ServerHost"
msgstr ""

#: Console/Command/ServerShell.php:157
msgid "ListenPort"
msgstr ""

#: Console/Command/ServerShell.php:161
msgid "DocumentRoot"
msgstr ""

#: Console/Command/ServerShell.php:165
msgid "PHP Built-in Server for CakePHP"
msgstr ""

#: Console/Command/ServerShell.php:166
msgid "<warning>[WARN] Don't use this at the production environment</warning>"
msgstr ""

#: Console/Command/TestShell.php:48
#: Console/Command/TestsuiteShell.php:42
msgid "The CakePHP Testsuite allows you to run test cases from the command line"
msgstr ""

#: Console/Command/TestShell.php:50
msgid "The category for the test, or test file, to test."
msgstr ""

#: Console/Command/TestShell.php:53
msgid "The path to the file, or test file, to test."
msgstr ""

#: Console/Command/TestShell.php:56
msgid "<file> Log test execution in JUnit XML format to file."
msgstr ""

#: Console/Command/TestShell.php:59
msgid "<file> Log test execution in JSON format to file."
msgstr ""

#: Console/Command/TestShell.php:62
msgid "<file> Log test execution in TAP format to file."
msgstr ""

#: Console/Command/TestShell.php:65
msgid "Log test execution to DBUS."
msgstr ""

#: Console/Command/TestShell.php:68
msgid "<dir> Generate code coverage report in HTML format."
msgstr ""

#: Console/Command/TestShell.php:71
msgid "<file> Write code coverage data in Clover XML format."
msgstr ""

#: Console/Command/TestShell.php:74
msgid "<file> Write agile documentation in HTML format to file."
msgstr ""

#: Console/Command/TestShell.php:77
msgid "<file> Write agile documentation in Text format to file."
msgstr ""

#: Console/Command/TestShell.php:80
msgid "<pattern> Filter which tests to run."
msgstr ""

#: Console/Command/TestShell.php:83
msgid "<name> Only runs tests from the specified group(s)."
msgstr ""

#: Console/Command/TestShell.php:86
msgid "<name> Exclude tests from the specified group(s)."
msgstr ""

#: Console/Command/TestShell.php:89
msgid "List available test groups."
msgstr ""

#: Console/Command/TestShell.php:92
msgid "TestSuiteLoader implementation to use."
msgstr ""

#: Console/Command/TestShell.php:95
msgid "<times> Runs the test(s) repeatedly."
msgstr ""

#: Console/Command/TestShell.php:98
msgid "Report test execution progress in TAP format."
msgstr ""

#: Console/Command/TestShell.php:101
msgid "Report test execution progress in TestDox format."
msgstr ""

#: Console/Command/TestShell.php:105
msgid "Do not use colors in output."
msgstr ""

#: Console/Command/TestShell.php:108
msgid "Write to STDERR instead of STDOUT."
msgstr ""

#: Console/Command/TestShell.php:111
msgid "Stop execution upon first error or failure."
msgstr ""

#: Console/Command/TestShell.php:114
msgid "Stop execution upon first failure."
msgstr ""

#: Console/Command/TestShell.php:117
msgid "Stop execution upon first skipped test."
msgstr ""

#: Console/Command/TestShell.php:120
msgid "Stop execution upon first incomplete test."
msgstr ""

#: Console/Command/TestShell.php:123
msgid "Mark a test as incomplete if no assertions are made."
msgstr ""

#: Console/Command/TestShell.php:126
msgid "Waits for a keystroke after each test."
msgstr ""

#: Console/Command/TestShell.php:129
msgid "Run each test in a separate PHP process."
msgstr ""

#: Console/Command/TestShell.php:132
msgid "Do not backup and restore $GLOBALS for each test."
msgstr ""

#: Console/Command/TestShell.php:135
msgid "Backup and restore static attributes for each test."
msgstr ""

#: Console/Command/TestShell.php:138
msgid "Try to check source files for syntax errors."
msgstr ""

#: Console/Command/TestShell.php:141
msgid "<file> A \"bootstrap\" PHP file that is run before the tests."
msgstr ""

#: Console/Command/TestShell.php:144
msgid "<file> Read configuration from XML file."
msgstr ""

#: Console/Command/TestShell.php:147
msgid "Ignore default configuration file (phpunit.xml)."
msgstr ""

#: Console/Command/TestShell.php:150
msgid "<path(s)> Prepend PHP include_path with given path(s)."
msgstr ""

#: Console/Command/TestShell.php:153
msgid "key[=value] Sets a php.ini value."
msgstr ""

#: Console/Command/TestShell.php:156
msgid "Choose a custom fixture manager."
msgstr ""

#: Console/Command/TestShell.php:158
msgid "More verbose output."
msgstr ""

#: Console/Command/TestShell.php:249
#: Console/Command/TestsuiteShell.php:87
msgid "CakePHP Test Shell"
msgstr ""

#: Console/Command/TestShell.php:298
msgid "No test cases available \n\n"
msgstr ""

#: Console/Command/TestShell.php:312
msgid "What test case would you like to run?"
msgstr ""

#: Console/Command/TestsuiteShell.php:43
msgid "<warning>This shell is for backwards-compatibility only</warning>\nuse the test shell instead"
msgstr ""

#: Console/Command/UpgradeShell.php:72
msgid "<warning>Dry-run mode enabled!</warning>"
msgstr ""

#: Console/Command/UpgradeShell.php:75
msgid "<warning>No git repository detected!</warning>"
msgstr ""

#: Console/Command/UpgradeShell.php:90
msgid "Running %s"
msgstr ""

#: Console/Command/UpgradeShell.php:139
msgid "Upgrading locations for plugin %s"
msgstr ""

#: Console/Command/UpgradeShell.php:144
msgid "Upgrading locations for app directory"
msgstr ""

#: Console/Command/UpgradeShell.php:161;588;615;707
msgid "Moving %s to %s"
msgstr ""

#: Console/Command/UpgradeShell.php:730
msgid "Updating %s..."
msgstr ""

#: Console/Command/UpgradeShell.php:771
msgid " * Updating %s"
msgstr ""

#: Console/Command/UpgradeShell.php:775
msgid "Done updating %s"
msgstr ""

#: Console/Command/UpgradeShell.php:791
msgid "The plugin to update. Only the specified plugin will be updated."
msgstr ""

#: Console/Command/UpgradeShell.php:795
msgid "The extension(s) to search. A pipe delimited list, or a preg_match compatible subpattern"
msgstr ""

#: Console/Command/UpgradeShell.php:800
msgid "Use git command for moving files around."
msgstr ""

#: Console/Command/UpgradeShell.php:805
msgid "Dry run the update, no files will actually be modified."
msgstr ""

#: Console/Command/UpgradeShell.php:812
msgid "A shell to help automate upgrading from CakePHP 1.3 to 2.0. \nBe sure to have a backup of your application before running these commands."
msgstr ""

#: Console/Command/UpgradeShell.php:815
msgid "Run all upgrade commands."
msgstr ""

#: Console/Command/UpgradeShell.php:819
msgid "Update tests class names to FooTest rather than FooTestCase."
msgstr ""

#: Console/Command/UpgradeShell.php:823
msgid "Move files and folders to their new homes."
msgstr ""

#: Console/Command/UpgradeShell.php:827
msgid "Update the i18n translation method calls."
msgstr ""

#: Console/Command/UpgradeShell.php:831
msgid "Update calls to helpers."
msgstr ""

#: Console/Command/UpgradeShell.php:835
msgid "Update removed basics functions to PHP native functions."
msgstr ""

#: Console/Command/UpgradeShell.php:839
msgid "Update removed request access, and replace with $this->request."
msgstr ""

#: Console/Command/UpgradeShell.php:843
msgid "Update Configure::read() to Configure::read('debug')"
msgstr ""

#: Console/Command/UpgradeShell.php:847
msgid "Replace Obsolete constants"
msgstr ""

#: Console/Command/UpgradeShell.php:851
msgid "Update components to extend Component class."
msgstr ""

#: Console/Command/UpgradeShell.php:855
msgid "Replace use of cakeError with exceptions."
msgstr ""

#: Console/Command/Task/ControllerTask.php:75
msgid "Baking basic crud methods for "
msgstr ""

#: Console/Command/Task/ControllerTask.php:81;121
msgid "Adding %s methods"
msgstr ""

#: Console/Command/Task/ControllerTask.php:132
msgid "No Controllers were baked, Models need to exist before Controllers can be baked."
msgstr ""

#: Console/Command/Task/ControllerTask.php:144
msgid "Bake Controller\nPath: %s"
msgstr ""

#: Console/Command/Task/ControllerTask.php:153
msgid "Baking %sController"
msgstr ""

#: Console/Command/Task/ControllerTask.php:163
msgid "Would you like to build your controller interactively?"
msgstr ""

#: Console/Command/Task/ControllerTask.php:165
msgid "Warning: Choosing no will overwrite the %sController."
msgstr ""

#: Console/Command/Task/ControllerTask.php:172
msgid "Would you like to use dynamic scaffolding?"
msgstr ""

#: Console/Command/Task/ControllerTask.php:185
msgid "Would you like to use Session flash messages?"
msgstr ""

#: Console/Command/Task/ControllerTask.php:207
#: Console/Command/Task/DbConfigTask.php:238
#: Console/Command/Task/ModelTask.php:274
#: Console/Command/Task/PluginTask.php:105
#: Console/Command/Task/ProjectTask.php:196
#: Console/Command/Task/ViewTask.php:332
msgid "Look okay?"
msgstr ""

#: Console/Command/Task/ControllerTask.php:236
msgid "The following controller will be created:"
msgstr ""

#: Console/Command/Task/ControllerTask.php:238
msgid "Controller Name:\n\t%s"
msgstr ""

#: Console/Command/Task/ControllerTask.php:245
msgid "Helpers:"
msgstr ""

#: Console/Command/Task/ControllerTask.php:246
msgid "Components:"
msgstr ""

#: Console/Command/Task/ControllerTask.php:273
msgid "Would you like to create some basic class methods \n(index(), add(), view(), edit())?"
msgstr ""

#: Console/Command/Task/ControllerTask.php:277
msgid "Would you like to create the basic class methods for admin routing?"
msgstr ""

#: Console/Command/Task/ControllerTask.php:299
msgid "You must have a model for this class to build basic methods. Please try again."
msgstr ""

#: Console/Command/Task/ControllerTask.php:331
msgid "Baking controller class for %s..."
msgstr ""

#: Console/Command/Task/ControllerTask.php:375
msgid "Would you like this controller to use other helpers\nbesides HtmlHelper and FormHelper?"
msgstr ""

#: Console/Command/Task/ControllerTask.php:376
msgid "Please provide a comma separated list of the other\nhelper names you'd like to use.\nExample: 'Text, Js, Time'"
msgstr ""

#: Console/Command/Task/ControllerTask.php:388
msgid "Would you like this controller to use other components\nbesides PaginatorComponent?"
msgstr ""

#: Console/Command/Task/ControllerTask.php:389
msgid "Please provide a comma separated list of the component names you'd like to use.\nExample: 'Acl, Security, RequestHandler'"
msgstr ""

#: Console/Command/Task/ControllerTask.php:424
msgid "Possible Controllers based on your current database:"
msgstr ""

#: Console/Command/Task/ControllerTask.php:448
msgid "Enter a number from the list above,\ntype in the name of another controller, or 'q' to exit"
msgstr ""

#: Console/Command/Task/ControllerTask.php:450
#: Console/Command/Task/ModelTask.php:963
msgid "Exit"
msgstr ""

#: Console/Command/Task/ControllerTask.php:455
msgid "The Controller name you supplied was empty,\nor the number you selected was not an option. Please try again."
msgstr ""

#: Console/Command/Task/ControllerTask.php:476
msgid "Bake a controller for a model. Using options you can bake public, admin or both."
msgstr ""

#: Console/Command/Task/ControllerTask.php:478
msgid "Name of the controller to bake. Can use Plugin.name to bake controllers into plugins."
msgstr ""

#: Console/Command/Task/ControllerTask.php:480
msgid "Bake a controller with basic crud actions (index, view, add, edit, delete)."
msgstr ""

#: Console/Command/Task/ControllerTask.php:483
msgid "Bake a controller with crud actions for one of the Routing.prefixes."
msgstr ""

#: Console/Command/Task/ControllerTask.php:487
msgid "Plugin to bake the controller into."
msgstr ""

#: Console/Command/Task/ControllerTask.php:490
msgid "The connection the controller's model is on."
msgstr ""

#: Console/Command/Task/ControllerTask.php:496
#: Console/Command/Task/FixtureTask.php:93
#: Console/Command/Task/ModelTask.php:1004
#: Console/Command/Task/TestTask.php:572
#: Console/Command/Task/ViewTask.php:443
msgid "Force overwriting existing files without prompting."
msgstr ""

#: Console/Command/Task/ControllerTask.php:498
msgid "Bake all controllers with CRUD methods."
msgstr ""

#: Console/Command/Task/ControllerTask.php:499
#: Console/Command/Task/FixtureTask.php:98
#: Console/Command/Task/ModelTask.php:1005
#: Console/Command/Task/TestTask.php:573
#: Console/Command/Task/ViewTask.php:446
msgid "Omitting all arguments and options will enter into an interactive mode."
msgstr ""

#: Console/Command/Task/DbConfigTask.php:89
msgid "Database Configuration:"
msgstr ""

#: Console/Command/Task/DbConfigTask.php:98
msgid "Name:"
msgstr ""

#: Console/Command/Task/DbConfigTask.php:101
msgid "The name may only contain unaccented latin characters, numbers or underscores"
msgstr ""

#: Console/Command/Task/DbConfigTask.php:104
msgid "The name must start with an unaccented latin character or an underscore"
msgstr ""

#: Console/Command/Task/DbConfigTask.php:108
msgid "Datasource:"
msgstr ""

#: Console/Command/Task/DbConfigTask.php:110
msgid "Persistent Connection?"
msgstr ""

#: Console/Command/Task/DbConfigTask.php:119
msgid "Database Host:"
msgstr ""

#: Console/Command/Task/DbConfigTask.php:124
msgid "Port?"
msgstr ""

#: Console/Command/Task/DbConfigTask.php:133
msgid "User:"
msgstr ""

#: Console/Command/Task/DbConfigTask.php:139
msgid "Password:"
msgstr ""

#: Console/Command/Task/DbConfigTask.php:142
msgid "The password you supplied was empty. Use an empty password?"
msgstr ""

#: Console/Command/Task/DbConfigTask.php:151
msgid "Database Name:"
msgstr ""

#: Console/Command/Task/DbConfigTask.php:156
msgid "Table Prefix?"
msgstr ""

#: Console/Command/Task/DbConfigTask.php:164
msgid "Table encoding?"
msgstr ""

#: Console/Command/Task/DbConfigTask.php:173
msgid "Table schema?"
msgstr ""

#: Console/Command/Task/DbConfigTask.php:187
msgid "Do you wish to add another database configuration?"
msgstr ""

#: Console/Command/Task/DbConfigTask.php:210
msgid "The following database configuration will be created:"
msgstr ""

#: Console/Command/Task/DbConfigTask.php:212
msgid "Name:         %s"
msgstr ""

#: Console/Command/Task/DbConfigTask.php:213
msgid "Datasource:   %s"
msgstr ""

#: Console/Command/Task/DbConfigTask.php:214
msgid "Persistent:   %s"
msgstr ""

#: Console/Command/Task/DbConfigTask.php:215
msgid "Host:         %s"
msgstr ""

#: Console/Command/Task/DbConfigTask.php:218
msgid "Port:         %s"
msgstr ""

#: Console/Command/Task/DbConfigTask.php:221
msgid "User:         %s"
msgstr ""

#: Console/Command/Task/DbConfigTask.php:222
msgid "Pass:         %s"
msgstr ""

#: Console/Command/Task/DbConfigTask.php:223
msgid "Database:     %s"
msgstr ""

#: Console/Command/Task/DbConfigTask.php:226
msgid "Table prefix: %s"
msgstr ""

#: Console/Command/Task/DbConfigTask.php:230
msgid "Schema:       %s"
msgstr ""

#: Console/Command/Task/DbConfigTask.php:234
msgid "Encoding:     %s"
msgstr ""

#: Console/Command/Task/DbConfigTask.php:365
msgid "Use Database Config"
msgstr ""

#: Console/Command/Task/DbConfigTask.php:378
msgid "Bake new database configuration settings."
msgstr ""

#: Console/Command/Task/ExtractTask.php:123
msgid "Current paths: %s\nWhat is the path you would like to extract?\n[Q]uit [D]one"
msgstr ""

#: Console/Command/Task/ExtractTask.php:130;207
msgid "Extract Aborted"
msgstr ""

#: Console/Command/Task/ExtractTask.php:136
msgid "<warning>No directories selected.</warning> Please choose a directory."
msgstr ""

#: Console/Command/Task/ExtractTask.php:141;213
msgid "The directory path you supplied was not found. Please try again."
msgstr ""

#: Console/Command/Task/ExtractTask.php:175
msgid "Would you like to extract the messages from the CakePHP core?"
msgstr ""

#: Console/Command/Task/ExtractTask.php:203
msgid "What is the path you would like to output?\n[Q]uit"
msgstr ""

#: Console/Command/Task/ExtractTask.php:223
msgid "Would you like to merge all domain and category strings into the default.pot file?"
msgstr ""

#: Console/Command/Task/ExtractTask.php:233
msgid "The output directory %s was not found or writable."
msgstr ""

#: Console/Command/Task/ExtractTask.php:279
msgid "Extracting..."
msgstr ""

#: Console/Command/Task/ExtractTask.php:281
msgid "Paths:"
msgstr ""

#: Console/Command/Task/ExtractTask.php:285
msgid "Output Directory: "
msgstr ""

#: Console/Command/Task/ExtractTask.php:295
msgid "Done."
msgstr ""

#: Console/Command/Task/ExtractTask.php:305
msgid "CakePHP Language String Extraction:"
msgstr ""

#: Console/Command/Task/ExtractTask.php:306
msgid "Directory where your application is located."
msgstr ""

#: Console/Command/Task/ExtractTask.php:307
msgid "Comma separated list of paths."
msgstr ""

#: Console/Command/Task/ExtractTask.php:309
msgid "Merge all domain and category strings into the default.po file."
msgstr ""

#: Console/Command/Task/ExtractTask.php:312
msgid "Full path to output directory."
msgstr ""

#: Console/Command/Task/ExtractTask.php:313
msgid "Comma separated list of files."
msgstr ""

#: Console/Command/Task/ExtractTask.php:317
msgid "Ignores all files in plugins if this command is run inside from the same app directory."
msgstr ""

#: Console/Command/Task/ExtractTask.php:320
msgid "Extracts tokens only from the plugin specified and puts the result in the plugin's Locale directory."
msgstr ""

#: Console/Command/Task/ExtractTask.php:325
msgid "Ignores validation messages in the $validate property. If this flag is not set and the command is run from the same app directory, all messages in model validation rules will be extracted as tokens."
msgstr ""

#: Console/Command/Task/ExtractTask.php:330
msgid "If set to a value, the localization domain to be used for model validation messages."
msgstr ""

#: Console/Command/Task/ExtractTask.php:333
msgid "Comma separated list of directories to exclude. Any path containing a path segment with the provided values will be skipped. E.g. test,vendors"
msgstr ""

#: Console/Command/Task/ExtractTask.php:339
msgid "Always overwrite existing .pot files."
msgstr ""

#: Console/Command/Task/ExtractTask.php:342
msgid "Extract messages from the CakePHP core libs."
msgstr ""

#: Console/Command/Task/ExtractTask.php:355
msgid "Processing %s..."
msgstr ""

#: Console/Command/Task/ExtractTask.php:630
msgid "Error: %s already exists in this location. Overwrite? [Y]es, [N]o, [A]ll"
msgstr ""

#: Console/Command/Task/ExtractTask.php:637
msgid "What would you like to name this file?"
msgstr ""

#: Console/Command/Task/ExtractTask.php:734
msgid "Invalid marker content in %s:%s\n* %s("
msgstr ""

#: Console/Command/Task/FixtureTask.php:70
msgid "Generate fixtures for use with the test suite. You can use `bake fixture all` to bake all fixtures."
msgstr ""

#: Console/Command/Task/FixtureTask.php:72
msgid "Name of the fixture to bake. Can use Plugin.name to bake plugin fixtures."
msgstr ""

#: Console/Command/Task/FixtureTask.php:74
msgid "When using generated data, the number of records to include in the fixture(s)."
msgstr ""

#: Console/Command/Task/FixtureTask.php:78
msgid "Which database configuration to use for baking."
msgstr ""

#: Console/Command/Task/FixtureTask.php:82
msgid "CamelCased name of the plugin to bake fixtures for."
msgstr ""

#: Console/Command/Task/FixtureTask.php:85
msgid "Importing schema for fixtures rather than hardcoding it."
msgstr ""

#: Console/Command/Task/FixtureTask.php:95
msgid "Used with --count and <name>/all commands to pull [n] records from the live tables, where [n] is either --count or the default of 10."
msgstr ""

#: Console/Command/Task/FixtureTask.php:154
msgid "Bake Fixture\nPath: %s"
msgstr ""

#: Console/Command/Task/FixtureTask.php:178
msgid "Would you like to import schema for this fixture?"
msgstr ""

#: Console/Command/Task/FixtureTask.php:186
msgid "Would you like to use record importing for this fixture?"
msgstr ""

#: Console/Command/Task/FixtureTask.php:192
msgid "Would you like to build this fixture with data from %s's table?"
msgstr ""

#: Console/Command/Task/FixtureTask.php:280
msgid "Baking test fixture for %s..."
msgstr ""

#: Console/Command/Task/FixtureTask.php:413
msgid "Please provide a SQL fragment to use as conditions\nExample: WHERE 1=1"
msgstr ""

#: Console/Command/Task/FixtureTask.php:417
msgid "How many records do you want to import?"
msgstr ""

#: Console/Command/Task/ModelTask.php:128
msgid "Baking %s"
msgstr ""

#: Console/Command/Task/ModelTask.php:176
msgid "Make a selection from the choices above"
msgstr ""

#: Console/Command/Task/ModelTask.php:193
msgid "Bake Model\nPath: %s"
msgstr ""

#: Console/Command/Task/ModelTask.php:208
msgid "The table %s doesn't exist or could not be automatically detected\ncontinue anyway?"
msgstr ""

#: Console/Command/Task/ModelTask.php:234
msgid "Would you like to supply validation criteria \nfor the fields in your model?"
msgstr ""

#: Console/Command/Task/ModelTask.php:240
msgid "Would you like to define model associations\n(hasMany, hasOne, belongsTo, etc.)?"
msgstr ""

#: Console/Command/Task/ModelTask.php:249
msgid "The following Model will be created:"
msgstr ""

#: Console/Command/Task/ModelTask.php:251
msgid "Name:       %s"
msgstr ""

#: Console/Command/Task/ModelTask.php:254
msgid "DB Config:  %s"
msgstr ""

#: Console/Command/Task/ModelTask.php:257
msgid "DB Table:   %s"
msgstr ""

#: Console/Command/Task/ModelTask.php:260
msgid "Primary Key: %s"
msgstr ""

#: Console/Command/Task/ModelTask.php:263
msgid "Validation: %s"
msgstr ""

#: Console/Command/Task/ModelTask.php:266
msgid "Associations:"
msgstr ""

#: Console/Command/Task/ModelTask.php:320
msgid "What is the primaryKey?"
msgstr ""

#: Console/Command/Task/ModelTask.php:331
msgid "A displayField could not be automatically detected\nwould you like to choose one?"
msgstr ""

#: Console/Command/Task/ModelTask.php:336
msgid "Choose a field from the options above:"
msgstr ""

#: Console/Command/Task/ModelTask.php:406
msgid "Field: <info>%s</info>"
msgstr ""

#: Console/Command/Task/ModelTask.php:407
msgid "Type: <info>%s</info>"
msgstr ""

#: Console/Command/Task/ModelTask.php:409
msgid "Please select one of the following validation options:"
msgstr ""

#: Console/Command/Task/ModelTask.php:421
msgid "%s - Do not do any validation on this field."
msgstr ""

#: Console/Command/Task/ModelTask.php:425
msgid "... or enter in a valid regex validation string.\n"
msgstr ""

#: Console/Command/Task/ModelTask.php:457
msgid "You have already chosen that validation rule,\nplease choose again"
msgstr ""

#: Console/Command/Task/ModelTask.php:461
msgid "Please make a valid selection."
msgstr ""

#: Console/Command/Task/ModelTask.php:483
msgid "Would you like to add another validation rule?"
msgstr ""

#: Console/Command/Task/ModelTask.php:500
msgid "One moment while the associations are detected."
msgstr ""

#: Console/Command/Task/ModelTask.php:530
msgid "None found."
msgstr ""

#: Console/Command/Task/ModelTask.php:532
msgid "Please confirm the following associations:"
msgstr ""

#: Console/Command/Task/ModelTask.php:704
msgid "Would you like to define some additional model associations?"
msgstr ""

#: Console/Command/Task/ModelTask.php:709
msgid "What is the association type?"
msgstr ""

#: Console/Command/Task/ModelTask.php:710
msgid "Enter a number"
msgstr ""

#: Console/Command/Task/ModelTask.php:712
msgid "For the following options be very careful to match your setup exactly.\nAny spelling mistakes will cause errors."
msgstr ""

#: Console/Command/Task/ModelTask.php:716
msgid "What is the alias for this association?"
msgstr ""

#: Console/Command/Task/ModelTask.php:717
msgid "What className will %s use?"
msgstr ""

#: Console/Command/Task/ModelTask.php:739
msgid "What is the table for this model?"
msgstr ""

#: Console/Command/Task/ModelTask.php:745
msgid "A helpful List of possible keys"
msgstr ""

#: Console/Command/Task/ModelTask.php:746
msgid "What is the foreignKey?"
msgstr ""

#: Console/Command/Task/ModelTask.php:750
msgid "What is the foreignKey? Specify your own."
msgstr ""

#: Console/Command/Task/ModelTask.php:753
msgid "What is the associationForeignKey?"
msgstr ""

#: Console/Command/Task/ModelTask.php:754
msgid "What is the joinTable?"
msgstr ""

#: Console/Command/Task/ModelTask.php:766
msgid "Define another association?"
msgstr ""

#: Console/Command/Task/ModelTask.php:838
msgid "Baking model class for %s..."
msgstr ""

#: Console/Command/Task/ModelTask.php:872
msgid "Possible Models based on your current database:"
msgstr ""

#: Console/Command/Task/ModelTask.php:904
msgid "Given your model named '%s',\nCake would expect a database table named '%s'"
msgstr ""

#: Console/Command/Task/ModelTask.php:905
msgid "Do you want to use this table?"
msgstr ""

#: Console/Command/Task/ModelTask.php:908
msgid "What is the name of the table?"
msgstr ""

#: Console/Command/Task/ModelTask.php:940
msgid "Your database does not have any tables."
msgstr ""

#: Console/Command/Task/ModelTask.php:959
msgid "Enter a number from the list above,\ntype in the name of another model, or 'q' to exit"
msgstr ""

#: Console/Command/Task/ModelTask.php:968
msgid "The model name you supplied was empty,\nor the number you selected was not an option. Please try again."
msgstr ""

#: Console/Command/Task/ModelTask.php:988
msgid "Bake models."
msgstr ""

#: Console/Command/Task/ModelTask.php:990
msgid "Name of the model to bake. Can use Plugin.name to bake plugin models."
msgstr ""

#: Console/Command/Task/ModelTask.php:992
msgid "Bake all model files with associations and validation."
msgstr ""

#: Console/Command/Task/ModelTask.php:995
msgid "Plugin to bake the model into."
msgstr ""

#: Console/Command/Task/ModelTask.php:1001
msgid "The connection the model table is on."
msgstr ""

#: Console/Command/Task/PluginTask.php:63
msgid "Plugin: %s already exists, no action taken"
msgstr ""

#: Console/Command/Task/PluginTask.php:81
msgid "Enter the name of the plugin in CamelCase format"
msgstr ""

#: Console/Command/Task/PluginTask.php:85
msgid "An error occurred trying to bake: %s in %s"
msgstr ""

#: Console/Command/Task/PluginTask.php:101
msgid "<info>Plugin Name:</info> %s"
msgstr ""

#: Console/Command/Task/PluginTask.php:102
msgid "<info>Plugin Directory:</info> %s"
msgstr ""

#: Console/Command/Task/PluginTask.php:162
#: Console/Command/Task/ProjectTask.php:207
msgid "<success>Created:</success> %s in %s"
msgstr ""

#: Console/Command/Task/PluginTask.php:204
msgid "Choose a plugin path from the paths above."
msgstr ""

#: Console/Command/Task/PluginTask.php:220
msgid "Create the directory structure, AppModel and AppController classes for a new plugin. Can create plugins in any of your bootstrapped plugin paths."
msgstr ""

#: Console/Command/Task/PluginTask.php:224
msgid "CamelCased name of the plugin to create."
msgstr ""

#: Console/Command/Task/ProjectTask.php:58
msgid "What is the path to the project you want to bake?"
msgstr ""

#: Console/Command/Task/ProjectTask.php:68
msgid "<warning>A project already exists in this location:</warning> %s Overwrite?"
msgstr ""

#: Console/Command/Task/ProjectTask.php:80
msgid " * Random hash key created for 'Security.salt'"
msgstr ""

#: Console/Command/Task/ProjectTask.php:82
msgid "Unable to generate random hash for 'Security.salt', you should change it in %s"
msgstr ""

#: Console/Command/Task/ProjectTask.php:87
msgid " * Random seed created for 'Security.cipherSeed'"
msgstr ""

#: Console/Command/Task/ProjectTask.php:89
msgid "Unable to generate random seed for 'Security.cipherSeed', you should change it in %s"
msgstr ""

#: Console/Command/Task/ProjectTask.php:94
msgid " * Cache prefix set"
msgstr ""

#: Console/Command/Task/ProjectTask.php:96
msgid "The cache prefix was <error>NOT</error> set"
msgstr ""

#: Console/Command/Task/ProjectTask.php:101
msgid " * app/Console/cake.php path set."
msgstr ""

#: Console/Command/Task/ProjectTask.php:103
msgid "Unable to set console path for app/Console."
msgstr ""

#: Console/Command/Task/ProjectTask.php:109
msgid "<info>CakePHP is on your `include_path`. CAKE_CORE_INCLUDE_PATH will be set, but commented out.</info>"
msgstr ""

#: Console/Command/Task/ProjectTask.php:111
msgid "<warning>CakePHP is not on your `include_path`, CAKE_CORE_INCLUDE_PATH will be hard coded.</warning>"
msgstr ""

#: Console/Command/Task/ProjectTask.php:112
msgid "You can fix this by adding CakePHP to your `include_path`."
msgstr ""

#: Console/Command/Task/ProjectTask.php:117;118
msgid " * CAKE_CORE_INCLUDE_PATH set to %s in %s"
msgstr ""

#: Console/Command/Task/ProjectTask.php:120
msgid "Unable to set CAKE_CORE_INCLUDE_PATH, you should change it in %s"
msgstr ""

#: Console/Command/Task/ProjectTask.php:124
msgid "   * <warning>Remember to check these values after moving to production server</warning>"
msgstr ""

#: Console/Command/Task/ProjectTask.php:129
msgid "Could not set permissions on %s"
msgstr ""

#: Console/Command/Task/ProjectTask.php:134
msgid "<success>Project baked successfully!</success>"
msgstr ""

#: Console/Command/Task/ProjectTask.php:136
msgid "Project baked but with <warning>some issues.</warning>."
msgstr ""

#: Console/Command/Task/ProjectTask.php:173
msgid "What is the path to the directory layout you wish to copy?"
msgstr ""

#: Console/Command/Task/ProjectTask.php:178
msgid "The directory path you supplied was empty. Please try again."
msgstr ""

#: Console/Command/Task/ProjectTask.php:182
msgid "Directory path does not exist please choose another:"
msgstr ""

#: Console/Command/Task/ProjectTask.php:192
msgid "<info>Skel Directory</info>: "
msgstr ""

#: Console/Command/Task/ProjectTask.php:193
msgid "<info>Will be copied to</info>: "
msgstr ""

#: Console/Command/Task/ProjectTask.php:210
msgid "<error>Could not create</error> '%s' properly."
msgstr ""

#: Console/Command/Task/ProjectTask.php:224
msgid "<error>Bake Aborted.</error>"
msgstr ""

#: Console/Command/Task/ProjectTask.php:387
msgid "You have more than one routing prefix configured"
msgstr ""

#: Console/Command/Task/ProjectTask.php:396
msgid "Please choose a prefix to bake with."
msgstr ""

#: Console/Command/Task/ProjectTask.php:401;411
msgid "You need to enable %s in %s to use prefix routing."
msgstr ""

#: Console/Command/Task/ProjectTask.php:404
msgid "What would you like the prefix route to be?"
msgstr ""

#: Console/Command/Task/ProjectTask.php:405
msgid "Example: %s"
msgstr ""

#: Console/Command/Task/ProjectTask.php:407
msgid "Enter a routing prefix:"
msgstr ""

#: Console/Command/Task/ProjectTask.php:410
msgid "<error>Unable to write to</error> %s."
msgstr ""

#: Console/Command/Task/ProjectTask.php:429
msgid "Generate a new CakePHP project skeleton."
msgstr ""

#: Console/Command/Task/ProjectTask.php:431
msgid "Application directory to make, if it starts with \"/\" the path is absolute."
msgstr ""

#: Console/Command/Task/ProjectTask.php:434
msgid "Create empty files in each of the directories. Good if you are using git"
msgstr ""

#: Console/Command/Task/ProjectTask.php:440
msgid "The directory layout to use for the new application skeleton. Defaults to cake/Console/Templates/skel of CakePHP used to create the project."
msgstr ""

#: Console/Command/Task/TemplateTask.php:176
msgid "You have more than one set of templates installed."
msgstr ""

#: Console/Command/Task/TemplateTask.php:177
msgid "Please choose the template set you wish to use:"
msgstr ""

#: Console/Command/Task/TemplateTask.php:187
msgid "Which bake theme would you like to use?"
msgstr ""

#: Console/Command/Task/TemplateTask.php:213
msgid "Could not find template for %s"
msgstr ""

#: Console/Command/Task/TestTask.php:111
msgid "Bake Tests"
msgstr ""

#: Console/Command/Task/TestTask.php:118
msgid "Incorrect type provided. Please choose one of %s"
msgstr ""

#: Console/Command/Task/TestTask.php:144
msgid "Bake is detecting possible fixtures..."
msgstr ""

#: Console/Command/Task/TestTask.php:162
msgid "Baking test case for %s %s ..."
msgstr ""

#: Console/Command/Task/TestTask.php:188
msgid "Select an object type:"
msgstr ""

#: Console/Command/Task/TestTask.php:198
msgid "Enter the type of object to bake a test for or (q)uit"
msgstr ""

#: Console/Command/Task/TestTask.php:222
msgid "Choose a %s class"
msgstr ""

#: Console/Command/Task/TestTask.php:229
msgid "Choose an existing class, or enter the name of a class that does not exist"
msgstr ""

#: Console/Command/Task/TestTask.php:453
msgid "Bake could not detect fixtures, would you like to add some?"
msgstr ""

#: Console/Command/Task/TestTask.php:456
msgid "Please provide a comma separated list of the fixtures names you'd like to use.\nExample: 'app.comment, app.post, plugin.forums.post'"
msgstr ""

#: Console/Command/Task/TestTask.php:552
msgid "Bake test case skeletons for classes."
msgstr ""

#: Console/Command/Task/TestTask.php:554
msgid "Type of class to bake, can be any of the following: controller, model, helper, component or behavior."
msgstr ""

#: Console/Command/Task/TestTask.php:563
msgid "An existing class to bake tests for."
msgstr ""

#: Console/Command/Task/TestTask.php:569
msgid "CamelCased name of the plugin to bake tests for."
msgstr ""

#: Console/Command/Task/ViewTask.php:209
msgid "Would you like bake to build your views interactively?\nWarning: Choosing no will overwrite %s views if it exist."
msgstr ""

#: Console/Command/Task/ViewTask.php:216
msgid "Would you like to create some CRUD views\n(index, add, view, edit) for this controller?\nNOTE: Before doing so, you'll need to create your controller\nand model classes (including associated models)."
msgstr ""

#: Console/Command/Task/ViewTask.php:219
msgid "Would you like to create the views for admin routing?"
msgstr ""

#: Console/Command/Task/ViewTask.php:238
msgid "View Scaffolding Complete.\n"
msgstr ""

#: Console/Command/Task/ViewTask.php:255
msgid "Controller not found"
msgstr ""

#: Console/Command/Task/ViewTask.php:267
msgid "The file '%s' could not be found.\nIn order to bake a view, you'll need to first create the controller."
msgstr ""

#: Console/Command/Task/ViewTask.php:319
msgid "Action Name? (use lowercase_underscored function name)"
msgstr ""

#: Console/Command/Task/ViewTask.php:321
msgid "The action name you supplied was empty. Please try again."
msgstr ""

#: Console/Command/Task/ViewTask.php:326
msgid "The following view will be created:"
msgstr ""

#: Console/Command/Task/ViewTask.php:328
msgid "Controller Name: %s"
msgstr ""

#: Console/Command/Task/ViewTask.php:329
msgid "Action Name:     %s"
msgstr ""

#: Console/Command/Task/ViewTask.php:330
msgid "Path:            %s"
msgstr ""

#: Console/Command/Task/ViewTask.php:337
msgid "Bake Aborted."
msgstr ""

#: Console/Command/Task/ViewTask.php:354
msgid "Baking `%s` view file..."
msgstr ""

#: Console/Command/Task/ViewTask.php:422
msgid "Bake views for a controller, using built-in or custom templates."
msgstr ""

#: Console/Command/Task/ViewTask.php:424
msgid "Name of the controller views to bake. Can be Plugin.name as a shortcut for plugin baking."
msgstr ""

#: Console/Command/Task/ViewTask.php:426
msgid "Will bake a single action's file. core templates are (index, add, edit, view)"
msgstr ""

#: Console/Command/Task/ViewTask.php:428
msgid "Will bake the template in <action> but create the filename after <alias>."
msgstr ""

#: Console/Command/Task/ViewTask.php:431
msgid "Plugin to bake the view into."
msgstr ""

#: Console/Command/Task/ViewTask.php:433
msgid "Set to only bake views for a prefix in Routing.prefixes"
msgstr ""

#: Console/Command/Task/ViewTask.php:440
msgid "The connection the connected model is on."
msgstr ""

#: Console/Command/Task/ViewTask.php:445
msgid "Bake all CRUD action views for all controllers. Requires models and controllers to exist."
msgstr ""

#: Plugin/Dropbox/Controller/Component/DropboxApiComponent.php:97
msgid "Please create your dropbox_token and dropbox_token_secret fields in your user model."
msgstr ""

#: Plugin/Dropbox/Model/Datasource/DropboxSource.php:193
msgid "Error decoding json, run json_last_error() after to see the error code."
msgstr ""

#: Plugin/Dropbox/Model/Datasource/DropboxSource.php:223
msgid "Sorry, that find method is not supported."
msgstr ""

#: View/Errors/error400.ctp:21
#: View/Errors/error500.ctp:21
#: View/Errors/fatal_error.ctp:22
#: View/Errors/missing_action.ctp:21;25
#: View/Errors/missing_behavior.ctp:23;27
#: View/Errors/missing_component.ctp:23;27
#: View/Errors/missing_connection.ctp:21;32
#: View/Errors/missing_controller.ctp:23;27
#: View/Errors/missing_database.ctp:21;25
#: View/Errors/missing_datasource.ctp:23
#: View/Errors/missing_datasource_config.ctp:21
#: View/Errors/missing_helper.ctp:23;27
#: View/Errors/missing_layout.ctp:21;25
#: View/Errors/missing_plugin.ctp:21;25
#: View/Errors/missing_table.ctp:21
#: View/Errors/missing_view.ctp:21;25
#: View/Errors/pdo_error.ctp:21
#: View/Errors/private_action.ctp:21
#: View/Errors/scaffold_error.ctp:21
msgid "Error"
msgstr ""

#: View/Errors/error400.ctp:23
msgid "The requested address %s was not found on this server."
msgstr ""

#: View/Errors/error500.ctp:22
#: Error/ExceptionRenderer.php:231
msgid "An Internal Error Has Occurred."
msgstr ""

#: Controller/Scaffold.php:128
msgid "Scaffold :: "
msgstr ""

#: Controller/Scaffold.php:167;234;303
msgid "Invalid %s"
msgstr ""

#: Controller/Scaffold.php:222
msgid "updated"
msgstr ""

#: Controller/Scaffold.php:225
msgid "saved"
msgstr ""

#: Controller/Scaffold.php:245
msgid "The %1$s has been %2$s"
msgstr ""

#: Controller/Scaffold.php:255
msgid "Please correct errors below."
msgstr ""

#: Controller/Scaffold.php:306
msgid "The %1$s with id: %2$s has been deleted."
msgstr ""

#: Controller/Scaffold.php:309
msgid "There was an error deleting the %1$s with id: %2$s"
msgstr ""

#: Controller/Component/AuthComponent.php:429
msgid "You are not authorized to access that location."
msgstr ""

#: Error/ExceptionRenderer.php:209
msgid "Not Found"
msgstr ""

#: Model/Validator/CakeValidationSet.php:286
msgid "This field cannot be left blank"
msgstr ""

#: Network/CakeResponse.php:1270
msgid "The requested file was not found"
msgstr ""

#: Utility/CakeNumber.php:119
msgid "%s KB"
msgstr ""

#: Utility/CakeNumber.php:121
msgid "%s MB"
msgstr ""

#: Utility/CakeNumber.php:123
msgid "%s GB"
msgstr ""

#: Utility/CakeNumber.php:125
msgid "%s TB"
msgstr ""

#: Utility/CakeNumber.php:117
msgid "%d Byte"
msgid_plural "%d Bytes"
msgstr[0] ""
msgstr[1] ""

#: Utility/CakeTime.php:398
msgid "Today, %s"
msgstr ""

#: Utility/CakeTime.php:401
msgid "Yesterday, %s"
msgstr ""

#: Utility/CakeTime.php:404
msgid "Tomorrow, %s"
msgstr ""

#: Utility/CakeTime.php:409
msgid "Sunday"
msgstr ""

#: Utility/CakeTime.php:410
msgid "Monday"
msgstr ""

#: Utility/CakeTime.php:411
msgid "Tuesday"
msgstr ""

#: Utility/CakeTime.php:412
msgid "Wednesday"
msgstr ""

#: Utility/CakeTime.php:413
msgid "Thursday"
msgstr ""

#: Utility/CakeTime.php:414
msgid "Friday"
msgstr ""

#: Utility/CakeTime.php:415
msgid "Saturday"
msgstr ""

#: Utility/CakeTime.php:421
msgid "On %s %s"
msgstr ""

#: Utility/CakeTime.php:742
msgid "%s ago"
msgstr ""

#: Utility/CakeTime.php:743
msgid "on %s"
msgstr ""

#: Utility/CakeTime.php:795
msgid "just now"
msgstr ""

#: Utility/CakeTime.php:911
msgid "about a second ago"
msgstr ""

#: Utility/CakeTime.php:912
msgid "about a minute ago"
msgstr ""

#: Utility/CakeTime.php:913
msgid "about an hour ago"
msgstr ""

#: Utility/CakeTime.php:914
msgid "about a day ago"
msgstr ""

#: Utility/CakeTime.php:915
msgid "about a week ago"
msgstr ""

#: Utility/CakeTime.php:916
msgid "about a year ago"
msgstr ""

#: Utility/CakeTime.php:925
msgid "in about a second"
msgstr ""

#: Utility/CakeTime.php:926
msgid "in about a minute"
msgstr ""

#: Utility/CakeTime.php:927
msgid "in about an hour"
msgstr ""

#: Utility/CakeTime.php:928
msgid "in about a day"
msgstr ""

#: Utility/CakeTime.php:929
msgid "in about a week"
msgstr ""

#: Utility/CakeTime.php:930
msgid "in about a year"
msgstr ""

#: Utility/CakeTime.php:952;974
msgid "days"
msgstr ""

#: Utility/CakeTime.php:884
msgid "%d year"
msgid_plural "%d years"
msgstr[0] ""
msgstr[1] ""

#: Utility/CakeTime.php:887
msgid "%d month"
msgid_plural "%d months"
msgstr[0] ""
msgstr[1] ""

#: Utility/CakeTime.php:890
msgid "%d week"
msgid_plural "%d weeks"
msgstr[0] ""
msgstr[1] ""

#: Utility/CakeTime.php:893
msgid "%d day"
msgid_plural "%d days"
msgstr[0] ""
msgstr[1] ""

#: Utility/CakeTime.php:896
msgid "%d hour"
msgid_plural "%d hours"
msgstr[0] ""
msgstr[1] ""

#: Utility/CakeTime.php:899
msgid "%d minute"
msgid_plural "%d minutes"
msgstr[0] ""
msgstr[1] ""

#: Utility/CakeTime.php:902
msgid "%d second"
msgid_plural "%d seconds"
msgstr[0] ""
msgstr[1] ""

#: View/Helper/FormHelper.php:702
msgid "Error in field %s"
msgstr ""

#: View/Helper/FormHelper.php:891
#: View/Scaffolds/form.ctp:48
#: View/Scaffolds/index.ctp:79;94
#: View/Scaffolds/view.ctp:65;80;190
msgid "New %s"
msgstr ""

#: View/Helper/FormHelper.php:897
#: View/Scaffolds/view.ctp:53;111
msgid "Edit %s"
msgstr ""

#: View/Helper/FormHelper.php:1462
msgid "empty"
msgstr ""

#: View/Helper/FormHelper.php:1834
#: View/Scaffolds/form.ctp:23
msgid "Submit"
msgstr ""

#: View/Helper/FormHelper.php:2820
msgid "January"
msgstr ""

#: View/Helper/FormHelper.php:2821
msgid "February"
msgstr ""

#: View/Helper/FormHelper.php:2822
msgid "March"
msgstr ""

#: View/Helper/FormHelper.php:2823
msgid "April"
msgstr ""

#: View/Helper/FormHelper.php:2824
msgid "May"
msgstr ""

#: View/Helper/FormHelper.php:2825
msgid "June"
msgstr ""

#: View/Helper/FormHelper.php:2826
msgid "July"
msgstr ""

#: View/Helper/FormHelper.php:2827
msgid "August"
msgstr ""

#: View/Helper/FormHelper.php:2828
msgid "September"
msgstr ""

#: View/Helper/FormHelper.php:2829
msgid "October"
msgstr ""

#: View/Helper/FormHelper.php:2830
msgid "November"
msgstr ""

#: View/Helper/FormHelper.php:2831
msgid "December"
msgstr ""

#: View/Helper/HtmlHelper.php:766
msgid "Home"
msgstr ""

#: View/Helper/PaginatorHelper.php:629
msgid " of "
msgstr ""

#: View/Scaffolds/form.ctp:27
#: View/Scaffolds/index.ctp:26;77
#: View/Scaffolds/view.ctp:49
msgid "Actions"
msgstr ""

#: View/Scaffolds/form.ctp:31
#: View/Scaffolds/index.ctp:51
#: View/Scaffolds/view.ctp:175
msgid "Delete"
msgstr ""

#: View/Scaffolds/form.ctp:34
#: View/Scaffolds/index.ctp:54
#: View/Scaffolds/view.ctp:57;178
msgid "Are you sure you want to delete # %s?"
msgstr ""

#: View/Scaffolds/form.ctp:37
msgid "List"
msgstr ""

#: View/Scaffolds/form.ctp:44
#: View/Scaffolds/index.ctp:87
#: View/Scaffolds/view.ctp:61;74
msgid "List %s"
msgstr ""

#: View/Scaffolds/index.ctp:48
#: View/Scaffolds/view.ctp:163
msgid "View"
msgstr ""

#: View/Scaffolds/index.ctp:49
#: View/Scaffolds/view.ctp:169
msgid "Edit"
msgstr ""

#: View/Scaffolds/index.ctp:65
msgid "Page {:page} of {:pages}, showing {:current} records out of {:count} total, starting on record {:start}, ending on {:end}"
msgstr ""

#: View/Scaffolds/view.ctp:20
msgid "View %s"
msgstr ""

#: View/Scaffolds/view.ctp:57
msgid "Delete %s"
msgstr ""

#: View/Scaffolds/view.ctp:95;135
msgid "Related %s"
msgstr ""

#: View/Layouts/default.ctp:19
#: View/Layouts/error.ctp:19
msgid "CakePHP: the rapid development php framework"
msgstr ""

#: View/Pages/home.ctp:16
msgid "Release Notes for CakePHP %s."
msgstr ""

#: View/Pages/home.ctp:18
msgid "Read the changelog"
msgstr ""

#: View/Pages/home.ctp:29
msgid "URL rewriting is not properly configured on your server."
msgstr ""

#: View/Pages/home.ctp:40
msgid "Your version of PHP is 5.2.8 or higher."
msgstr ""

#: View/Pages/home.ctp:44
msgid "Your version of PHP is too low. You need PHP 5.2.8 or higher to use CakePHP."
msgstr ""

#: View/Pages/home.ctp:53
msgid "Your tmp directory is writable."
msgstr ""

#: View/Pages/home.ctp:57
msgid "Your tmp directory is NOT writable."
msgstr ""

#: View/Pages/home.ctp:67
msgid "The %s is being used for core caching. To change the config edit %s"
msgstr ""

#: View/Pages/home.ctp:71
msgid "Your cache is NOT working. Please check the settings in %s"
msgstr ""

#: View/Pages/home.ctp:81
msgid "Your database configuration file is present."
msgstr ""

#: View/Pages/home.ctp:86
msgid "Your database configuration file is NOT present."
msgstr ""

#: View/Pages/home.ctp:88
msgid "Rename %s to %s"
msgstr ""

#: View/Pages/home.ctp:113
msgid "CakePHP is able to connect to the database."
msgstr ""

#: View/Pages/home.ctp:117
msgid "CakePHP is NOT able to connect to the database."
msgstr ""

#: View/Pages/home.ctp:129
msgid "PCRE has not been compiled with Unicode support."
msgstr ""

#: View/Pages/home.ctp:131
msgid "Recompile PCRE with Unicode support by adding <code>--enable-unicode-properties</code> when configuring"
msgstr ""

#: View/Pages/home.ctp:140
msgid "DebugKit plugin is present"
msgstr ""

#: View/Pages/home.ctp:144
msgid "DebugKit is not installed. It will help you inspect and debug different aspects of your application."
msgstr ""

#: View/Pages/home.ctp:146
msgid "You can install it from %s"
msgstr ""

#: View/Pages/home.ctp:152
msgid "Editing this Page"
msgstr ""

#: View/Pages/home.ctp:155
msgid "To change the content of this page, edit: %s.<br />\nTo change its layout, edit: %s.<br />\nYou can also add some CSS styles for your pages at: %s."
msgstr ""

#: View/Pages/home.ctp:162
msgid "Getting Started"
msgstr ""

#: View/Pages/home.ctp:166
msgid "New"
msgstr ""

#: View/Pages/home.ctp:166
msgid "CakePHP 2.0 Docs"
msgstr ""

#: View/Pages/home.ctp:175
msgid "The 15 min Blog Tutorial"
msgstr ""

#: View/Pages/home.ctp:182
msgid "Official Plugins"
msgstr ""

#: View/Pages/home.ctp:187
msgid "provides a debugging toolbar and enhanced debugging tools for CakePHP applications."
msgstr ""

#: View/Pages/home.ctp:191
msgid "contains various localized validation classes and translations for specific countries"
msgstr ""

#: View/Pages/home.ctp:196
msgid "More about CakePHP"
msgstr ""

#: View/Pages/home.ctp:198
msgid "CakePHP is a rapid development framework for PHP which uses commonly known design patterns like Active Record, Association Data Mapping, Front Controller and MVC."
msgstr ""

#: View/Pages/home.ctp:201
msgid "Our primary goal is to provide a structured framework that enables PHP users at all levels to rapidly develop robust web applications, without any loss to flexibility."
msgstr ""

#: View/Pages/home.ctp:206
msgid "The Rapid Development Framework"
msgstr ""

#: View/Pages/home.ctp:207
msgid "CakePHP Documentation"
msgstr ""

#: View/Pages/home.ctp:208
msgid "Your Rapid Development Cookbook"
msgstr ""

#: View/Pages/home.ctp:209
msgid "CakePHP API"
msgstr ""

#: View/Pages/home.ctp:210
msgid "Quick API Reference"
msgstr ""

#: View/Pages/home.ctp:211
msgid "The Bakery"
msgstr ""

#: View/Pages/home.ctp:212
msgid "Everything CakePHP"
msgstr ""

#: View/Pages/home.ctp:213
msgid "CakePHP Plugins"
msgstr ""

#: View/Pages/home.ctp:214
msgid "A comprehensive list of all CakePHP plugins created by the community"
msgstr ""

#: View/Pages/home.ctp:215
msgid "CakePHP Community Center"
msgstr ""

#: View/Pages/home.ctp:216
msgid "Everything related to the CakePHP community in one place"
msgstr ""

#: View/Pages/home.ctp:217
msgid "CakePHP Google Group"
msgstr ""

#: View/Pages/home.ctp:218
msgid "Community mailing list"
msgstr ""

#: View/Pages/home.ctp:220
msgid "Live chat about CakePHP"
msgstr ""

#: View/Pages/home.ctp:221
msgid "CakePHP Code"
msgstr ""

#: View/Pages/home.ctp:222
msgid "Find the CakePHP code on GitHub and contribute to the framework"
msgstr ""

#: View/Pages/home.ctp:223;224
msgid "CakePHP Issues"
msgstr ""

#: View/Pages/home.ctp:225;226
msgid "CakePHP Roadmaps"
msgstr ""

#: View/Pages/home.ctp:227
msgid "Training"
msgstr ""

#: View/Pages/home.ctp:228
msgid "Join a live session and get skilled with the framework"
msgstr ""

#: View/Pages/home.ctp:229
msgid "CakeFest"
msgstr ""

#: View/Pages/home.ctp:230
msgid "Don't miss our annual CakePHP conference"
msgstr ""

#: View/Pages/home.ctp:231
msgid "Cake Software Foundation"
msgstr ""

#: View/Pages/home.ctp:232
msgid "Promoting development related to CakePHP"
msgstr ""

#: webroot/test.php:93
msgid "Debug setting does not allow access to this url."
msgstr ""

#: Cache/Cache.php:173
msgid "Cache engine %s is not available."
msgstr ""

#: Cache/Cache.php:177
msgid "Cache engines must use %s as a base class."
msgstr ""

#: Cache/Cache.php:181
msgid "Cache engine %s is not properly configured."
msgstr ""

#: Cache/Cache.php:318
msgid "%s cache was unable to write '%s' to %s cache"
msgstr ""

#: Cache/Cache.php:542
msgid "Invalid cache group %s"
msgstr ""

#: Cache/Engine/FileEngine.php:312
msgid "Files cannot be atomically decremented."
msgstr ""

#: Cache/Engine/FileEngine.php:324
msgid "Files cannot be atomically incremented."
msgstr ""

#: Cache/Engine/FileEngine.php:361
msgid "Could not apply permission mask \"%s\" on cache file \"%s\""
msgstr ""

#: Cache/Engine/MemcacheEngine.php:165;182
msgid "Method %s not implemented for compressed cache in %s"
msgstr ""

#: Configure/IniReader.php:100
#: Configure/PhpReader.php:62
msgid "Cannot load configuration files with ../ in them."
msgstr ""

#: Configure/IniReader.php:105
#: Configure/PhpReader.php:67
msgid "Could not load configuration file: %s"
msgstr ""

#: Configure/PhpReader.php:72
msgid "No variable %s found in %s"
msgstr ""

#: Console/Command/TestShell.php:174
msgid "Please install PHPUnit framework <info>(http://www.phpunit.de)</info>"
msgstr ""

#: Console/Command/TestShell.php:370
msgid "Test case %s cannot be run via this shell"
msgstr ""

#: Console/Command/TestShell.php:383;400
msgid "Test case %s not found"
msgstr ""

#: Console/Command/Task/PluginTask.php:180
msgid "%s modified"
msgstr ""

#: Console/Command/Task/TestTask.php:322
msgid "Invalid object type."
msgstr ""

#: Console/Command/Task/TestTask.php:341
msgid "Invalid type name"
msgstr ""

#: Controller/Component/AclComponent.php:67
msgid "Could not find %s."
msgstr ""

#: Controller/Component/AclComponent.php:91
msgid "AclComponent adapters must implement AclInterface"
msgstr ""

#: Controller/Component/AclComponent.php:162;176
msgid "%s is deprecated, use %s instead"
msgstr ""

#: Controller/Component/AuthComponent.php:493
msgid "Authorization adapter \"%s\" was not found."
msgstr ""

#: Controller/Component/AuthComponent.php:496
msgid "Authorization objects must implement an %s method."
msgstr ""

#: Controller/Component/AuthComponent.php:782
msgid "Authentication adapter \"%s\" was not found."
msgstr ""

#: Controller/Component/AuthComponent.php:785
msgid "Authentication objects must implement an %s method."
msgstr ""

#: Controller/Component/CookieComponent.php:384
msgid "You must use cipher or rijndael for cookie encryption type"
msgstr ""

#: Controller/Component/RequestHandlerComponent.php:757
msgid "You must give a handler callback."
msgstr ""

#: Controller/Component/SecurityComponent.php:335;613
msgid "The request has been black-holed"
msgstr ""

#: Controller/Component/Acl/PhpAcl.php:104
msgid "\"roles\" section not found in configuration."
msgstr ""

#: Controller/Component/Acl/PhpAcl.php:108
msgid "Neither \"allow\" nor \"deny\" rules were provided in configuration."
msgstr ""

#: Controller/Component/Acl/PhpAcl.php:528
msgid "cycle detected when inheriting %s from %s. Path: %s"
msgstr ""

#: Controller/Component/Auth/BaseAuthenticate.php:154
msgid "Password hasher class \"%s\" was not found."
msgstr ""

#: Controller/Component/Auth/BaseAuthenticate.php:157
msgid "Password hasher must extend AbstractPasswordHasher class."
msgstr ""

#: Controller/Component/Auth/BaseAuthorize.php:97
msgid "$controller needs to be an instance of Controller"
msgstr ""

#: Controller/Component/Auth/ControllerAuthorize.php:51
msgid "$controller does not implement an %s method."
msgstr ""

#: Controller/Component/Auth/CrudAuthorize.php:85
msgid "CrudAuthorize::authorize() - Attempted access of un-mapped action \"%1$s\" in controller \"%2$s\""
msgstr ""

#: Core/Configure.php:73
msgid "Can't find application core file. Please create %s, and make sure it is readable by PHP."
msgstr ""

#: Core/Configure.php:93
msgid "Can't find application bootstrap file. Please create %s, and make sure it is readable by PHP."
msgstr ""

#: Core/Configure.php:340
msgid "There is no \"%s\" adapter."
msgstr ""

#: Core/Configure.php:343
msgid "The \"%s\" adapter, does not have a %s method."
msgstr ""

#: Event/CakeEventManager.php:104
msgid "The eventKey variable is required"
msgstr ""

#: I18n/I18n.php:164
msgid "You cannot use \"\" as a domain."
msgstr ""

#: I18n/I18n.php:202
msgid "Missing plural form translation for \"%s\" in \"%s\" domain, \"%s\" locale.  Check your po file for correct plurals and valid Plural-Forms header."
msgstr ""

#: Log/CakeLog.php:190
msgid "Invalid key name"
msgstr ""

#: Log/CakeLog.php:193
msgid "Missing logger class name"
msgstr ""

#: Log/CakeLog.php:314;332;351
msgid "Stream %s not found"
msgstr ""

#: Log/LogEngineCollection.php:44
msgid "logger class %s does not implement a %s method."
msgstr ""

#: Log/LogEngineCollection.php:69
msgid "Could not load class %s"
msgstr ""

#: Log/Engine/FileLog.php:150
msgid "Could not apply permission mask \"%s\" on log file \"%s\""
msgstr ""

#: Model/AclNode.php:176
msgid "AclNode::node() - Couldn't find %s node identified by \"%s\""
msgstr ""

#: Model/BehaviorCollection.php:226
msgid "%s - Method %s not found in any attached behavior"
msgstr ""

#: Model/CakeSchema.php:615
msgid "Schema generation error: invalid column type %s for %s.%s does not exist in DBO"
msgstr ""

#: Model/Model.php:1381
msgid "(Model::getColumnTypes) Unable to build model field data. If you are using a model without a database table, try implementing schema()"
msgstr ""

#: Model/Model.php:3583
msgid "Invalid join model settings in %s. The association parameter has the wrong type, expecting a string or array, but was passed type: %s"
msgstr ""

#: Model/Permission.php:86
msgid "%s - Failed ARO node lookup in permissions check. Node references:\nAro: %s\nAco: %s"
msgstr ""

#: Model/Permission.php:97
msgid "%s - Failed ACO node lookup in permissions check. Node references:\nAro: %s\nAco: %s"
msgstr ""

#: Model/Permission.php:108
msgid "ACO permissions key %s does not exist in %s"
msgstr ""

#: Model/Permission.php:179
msgid "%s - Invalid node"
msgstr ""

#: Model/Permission.php:197
msgid "Invalid permission key \"%s\""
msgstr ""

#: Model/Behavior/AclBehavior.php:66
msgid "Callback %s not defined in %s"
msgstr ""

#: Model/Behavior/AclBehavior.php:83
msgid "AclBehavior is setup with more then one type, please specify type parameter for node()"
msgstr ""

#: Model/Behavior/ContainableBehavior.php:342
msgid "Model \"%s\" is not associated with model \"%s\""
msgstr ""

#: Model/Behavior/TranslateBehavior.php:71
msgid "Datasource %s for TranslateBehavior of model %s is not connected"
msgstr ""

#: Model/Behavior/TranslateBehavior.php:601
msgid "You cannot bind a translation named \"name\"."
msgstr ""

#: Model/Behavior/TranslateBehavior.php:623
msgid "Association %s is already bound to model %s"
msgstr ""

#: Model/Datasource/CakeSession.php:501
msgid "Unable to configure the session, setting %s failed."
msgstr ""

#: Model/Datasource/CakeSession.php:535
msgid "Could not load %s to handle the session."
msgstr ""

#: Model/Datasource/CakeSession.php:541
msgid "Chosen SessionHandler does not implement CakeSessionHandlerInterface it cannot be used with an engine key."
msgstr ""

#: Model/Datasource/DboSource.php:255
msgid "Selected driver is not enabled"
msgstr ""

#: Model/Datasource/DboSource.php:1189
msgid "Error in Model %s"
msgstr ""

#: Model/Datasource/DboSource.php:3076
#: Model/Datasource/Database/Sqlite.php:400
msgid "Column name or type not defined in schema"
msgstr ""

#: Model/Datasource/DboSource.php:3081
#: Model/Datasource/Database/Sqlite.php:405
msgid "Column type %s does not exist"
msgstr ""

#: Model/Datasource/Database/Mysql.php:333
#: Model/Datasource/Database/Sqlserver.php:218
msgid "Could not describe table for %s"
msgstr ""

#: Model/Validator/CakeValidationRule.php:281
msgid "Could not find validation handler %s for %s"
msgstr ""

#: Network/CakeRequest.php:451
msgid "Method %s does not exist"
msgstr ""

#: Network/CakeResponse.php:624
msgid "Unknown status code"
msgstr ""

#: Network/CakeResponse.php:668
msgid "Invalid status code"
msgstr ""

#: Network/CakeResponse.php:1268
msgid "The requested file %s was not found or not readable"
msgstr ""

#: Network/CakeSocket.php:304
msgid "Connection timed out"
msgstr ""

#: Network/CakeSocket.php:371
msgid "Invalid encryption scheme chosen"
msgstr ""

#: Network/CakeSocket.php:384
msgid "Unable to perform enableCrypto operation on CakeSocket"
msgstr ""

#: Network/Email/CakeEmail.php:368
msgid "From requires only 1 email address."
msgstr ""

#: Network/Email/CakeEmail.php:383
msgid "Sender requires only 1 email address."
msgstr ""

#: Network/Email/CakeEmail.php:398
msgid "Reply-To requires only 1 email address."
msgstr ""

#: Network/Email/CakeEmail.php:413
msgid "Disposition-Notification-To requires only 1 email address."
msgstr ""

#: Network/Email/CakeEmail.php:428
msgid "Return-Path requires only 1 email address."
msgstr ""

#: Network/Email/CakeEmail.php:562;576;616;630
msgid "Invalid email: \"%s\""
msgstr ""

#: Network/Email/CakeEmail.php:661;676
msgid "$headers should be an array."
msgstr ""

#: Network/Email/CakeEmail.php:881
msgid "Format not available."
msgstr ""

#: Network/Email/CakeEmail.php:916
msgid "Class \"%s\" not found."
msgstr ""

#: Network/Email/CakeEmail.php:918
msgid "The \"%s\" does not have a %s method."
msgstr ""

#: Network/Email/CakeEmail.php:939
msgid "Invalid format for Message-ID. The text should be something like \"<<EMAIL>>\""
msgstr ""

#: Network/Email/CakeEmail.php:1018
msgid "No file or data specified."
msgstr ""

#: Network/Email/CakeEmail.php:1021
msgid "No filename specified."
msgstr ""

#: Network/Email/CakeEmail.php:1027
msgid "File not found: \"%s\""
msgstr ""

#: Network/Email/CakeEmail.php:1110
msgid "From is not specified."
msgstr ""

#: Network/Email/CakeEmail.php:1113
msgid "You need to specify at least one destination for to, cc or bcc."
msgstr ""

#: Network/Email/CakeEmail.php:1188
msgid "%s not found."
msgstr ""

#: Network/Email/CakeEmail.php:1192
msgid "Unknown email configuration \"%s\"."
msgstr ""

#: Network/Email/MailTransport.php:70;74
msgid "Could not send email."
msgstr ""

#: Network/Email/SmtpTransport.php:100
msgid "Unable to connect to SMTP server."
msgstr ""

#: Network/Email/SmtpTransport.php:121
msgid "SMTP server did not accept the connection or trying to connect to non TLS SMTP server using TLS."
msgstr ""

#: Network/Email/SmtpTransport.php:126
msgid "SMTP server did not accept the connection."
msgstr ""

#: Network/Email/SmtpTransport.php:142
msgid "SMTP server did not accept the username."
msgstr ""

#: Network/Email/SmtpTransport.php:145
msgid "SMTP server did not accept the password."
msgstr ""

#: Network/Email/SmtpTransport.php:148
msgid "SMTP authentication method not allowed, check if SMTP server requires TLS"
msgstr ""

#: Network/Email/SmtpTransport.php:150
msgid "SMTP does not require authentication."
msgstr ""

#: Network/Email/SmtpTransport.php:242
msgid "SMTP timeout."
msgstr ""

#: Network/Email/SmtpTransport.php:253
msgid "SMTP Error: %s"
msgstr ""

#: Network/Http/HttpResponse.php:21
msgid "HttpResponse is deprecated due to naming conflicts. Use HttpSocketResponse instead."
msgstr ""

#: Network/Http/HttpSocket.php:244
msgid "Invalid resource."
msgstr ""

#: Network/Http/HttpSocket.php:404
msgid "Class %s not found."
msgstr ""

#: Network/Http/HttpSocket.php:602
msgid "Unknown authentication method."
msgstr ""

#: Network/Http/HttpSocket.php:605
msgid "The %s does not support authentication."
msgstr ""

#: Network/Http/HttpSocket.php:631
msgid "Unknown authentication method for proxy."
msgstr ""

#: Network/Http/HttpSocket.php:634
msgid "The %s does not support proxy authentication."
msgstr ""

#: Network/Http/HttpSocket.php:886
msgid "HttpSocket::_buildRequestLine - Passed an invalid request line string. Activate quirks mode to do this."
msgstr ""

#: Network/Http/HttpSocket.php:904
msgid "HttpSocket::_buildRequestLine - The \"*\" asterisk character is only allowed for the following methods: %s. Activate quirks mode to work outside of HTTP/1.1 specs."
msgstr ""

#: Network/Http/HttpSocketResponse.php:151
msgid "Invalid response."
msgstr ""

#: Network/Http/HttpSocketResponse.php:155
msgid "Invalid HTTP response."
msgstr ""

#: Network/Http/HttpSocketResponse.php:223
msgid "HttpSocket::_decodeChunkedBody - Could not parse malformed chunk."
msgstr ""

#: Routing/Router.php:234
msgid "Route class not found, or route class is not a subclass of CakeRoute"
msgstr ""

#: Utility/CakeNumber.php:163
msgid "No unit type."
msgstr ""

#: Utility/ClassRegistry.php:116
msgid "(ClassRegistry::init() Attempted to create instance of a class with a numeric name"
msgstr ""

#: Utility/ClassRegistry.php:148
msgid "Cannot create instance of %s, as it is abstract or is an interface"
msgstr ""

#: Utility/ClassRegistry.php:189
msgid "(ClassRegistry::init() could not create instance of %s"
msgstr ""

#: Utility/Debugger.php:629
msgid "Invalid Debugger output format."
msgstr ""

#: Utility/Debugger.php:845
msgid "Please change the value of %s in %s to a salt value specific to your application."
msgstr ""

#: Utility/Debugger.php:849
msgid "Please change the value of %s in %s to a numeric (digits only) seed value specific to your application."
msgstr ""

#: Utility/Folder.php:385;408
msgid "%s changed to %s"
msgstr ""

#: Utility/Folder.php:389;410
msgid "%s NOT changed to %s"
msgstr ""

#: Utility/Folder.php:506
msgid "%s is a file"
msgstr ""

#: Utility/Folder.php:517;695
msgid "%s created"
msgstr ""

#: Utility/Folder.php:521
msgid "%s NOT created"
msgstr ""

#: Utility/Folder.php:593;601;613
msgid "%s removed"
msgstr ""

#: Utility/Folder.php:595;603;615
msgid "%s NOT removed"
msgstr ""

#: Utility/Folder.php:662
msgid "%s not writable"
msgstr ""

#: Utility/Folder.php:678
msgid "%s copied to %s"
msgstr ""

#: Utility/Folder.php:680
msgid "%s NOT copied to %s"
msgstr ""

#: Utility/Folder.php:699
msgid "%s not created"
msgstr ""

#: Utility/Hash.php:355
msgid "Hash::combine() needs an equal number of keys + values."
msgstr ""

#: Utility/ObjectCollection.php:128
msgid "Cannot use modParams with indexes that do not exist."
msgstr ""

#: Utility/Security.php:159
msgid "Invalid value, cost must be between %s and %s"
msgstr ""

#: Utility/Security.php:186;219
msgid "You cannot use an empty key for %s"
msgstr ""

#: Utility/Security.php:223
msgid "You must specify the operation for Security::rijndael(), either encrypt or decrypt"
msgstr ""

#: Utility/Security.php:227
msgid "You must use a key larger than 32 bytes for Security::rijndael()"
msgstr ""

#: Utility/Security.php:281
msgid "Invalid salt: %s for %s Please visit http://www.php.net/crypt and read the appropriate section for building %s salts."
msgstr ""

#: Utility/Validation.php:257
msgid "You must define the $operator parameter for %s"
msgstr ""

#: Utility/Validation.php:275
msgid "You must define a regular expression for %s"
msgstr ""

#: Utility/Validation.php:837
msgid "Could not find %s class, unable to complete validation."
msgstr ""

#: Utility/Validation.php:841
msgid "Method %s does not exist on %s unable to complete validation."
msgstr ""

#: Utility/Validation.php:935
msgid "Can not determine the mimetype."
msgstr ""

#: Utility/Xml.php:108;112;117
msgid "XML cannot be read."
msgstr ""

#: Utility/Xml.php:115;194
msgid "Invalid input."
msgstr ""

#: Utility/Xml.php:149
msgid "Xml cannot be read."
msgstr ""

#: Utility/Xml.php:198
msgid "The key of input must be alphanumeric"
msgstr ""

#: Utility/Xml.php:275;288
msgid "Invalid array"
msgstr ""

#: Utility/Xml.php:339
msgid "The input is not instance of SimpleXMLElement, DOMDocument or DOMNode."
msgstr ""

#: View/Helper.php:192
msgid "Method %1$s::%2$s does not exist"
msgstr ""

#: View/View.php:419
msgid "Element Not Found: %s"
msgstr ""

#: View/View.php:714
msgid "You cannot extend an element which does not exist (%s)."
msgstr ""

#: View/View.php:730
msgid "You cannot have views extend themselves."
msgstr ""

#: View/View.php:733
msgid "You cannot have views extend in a loop."
msgstr ""

#: View/View.php:916
msgid "The \"%s\" block was left open. Blocks are not allowed to cross files."
msgstr ""

#: View/Elements/sql_dump.ctp:81
msgid "Encountered unexpected %s. Cannot generate SQL log."
msgstr ""

#: View/Errors/fatal_error.ctp:20
msgid "Fatal Error"
msgstr ""

#: View/Errors/fatal_error.ctp:26
msgid "File"
msgstr ""

#: View/Errors/fatal_error.ctp:30
msgid "Line"
msgstr ""

#: View/Errors/fatal_error.ctp:34
#: View/Errors/missing_action.ctp:40
#: View/Errors/missing_behavior.ctp:37
#: View/Errors/missing_component.ctp:37
#: View/Errors/missing_connection.ctp:37
#: View/Errors/missing_controller.ctp:37
#: View/Errors/missing_database.ctp:29
#: View/Errors/missing_datasource.ctp:30
#: View/Errors/missing_datasource_config.ctp:25
#: View/Errors/missing_helper.ctp:37
#: View/Errors/missing_layout.ctp:29
#: View/Errors/missing_plugin.ctp:41
#: View/Errors/missing_table.ctp:25
#: View/Errors/missing_view.ctp:29
#: View/Errors/pdo_error.ctp:35
#: View/Errors/private_action.ctp:25
#: View/Errors/scaffold_error.ctp:25
msgid "Notice"
msgstr ""

#: View/Errors/fatal_error.ctp:35
#: View/Errors/missing_action.ctp:41
#: View/Errors/missing_behavior.ctp:38
#: View/Errors/missing_component.ctp:38
#: View/Errors/missing_connection.ctp:38
#: View/Errors/missing_controller.ctp:38
#: View/Errors/missing_database.ctp:30
#: View/Errors/missing_datasource.ctp:31
#: View/Errors/missing_datasource_config.ctp:26
#: View/Errors/missing_helper.ctp:38
#: View/Errors/missing_layout.ctp:30
#: View/Errors/missing_plugin.ctp:42
#: View/Errors/missing_table.ctp:26
#: View/Errors/missing_view.ctp:30
#: View/Errors/pdo_error.ctp:36
#: View/Errors/private_action.ctp:26
#: View/Errors/scaffold_error.ctp:26
msgid "If you want to customize this error message, create %s"
msgstr ""

#: View/Errors/missing_action.ctp:20
msgid "Missing Method in %s"
msgstr ""

#: View/Errors/missing_action.ctp:22
msgid "The action %1$s is not defined in controller %2$s"
msgstr ""

#: View/Errors/missing_action.ctp:26
msgid "Create %1$s%2$s in file: %3$s."
msgstr ""

#: View/Errors/missing_behavior.ctp:21
msgid "Missing Behavior"
msgstr ""

#: View/Errors/missing_behavior.ctp:24
#: View/Errors/missing_component.ctp:24
#: View/Errors/missing_controller.ctp:24
#: View/Errors/missing_helper.ctp:24
msgid "%s could not be found."
msgstr ""

#: View/Errors/missing_behavior.ctp:28
#: View/Errors/missing_component.ctp:28
#: View/Errors/missing_controller.ctp:28
#: View/Errors/missing_helper.ctp:28
msgid "Create the class %s below in file: %s"
msgstr ""

#: View/Errors/missing_component.ctp:21
msgid "Missing Component"
msgstr ""

#: View/Errors/missing_connection.ctp:19
#: View/Errors/missing_database.ctp:19
msgid "Missing Database Connection"
msgstr ""

#: View/Errors/missing_connection.ctp:22
msgid "A Database connection using \"%s\" was missing or unable to connect."
msgstr ""

#: View/Errors/missing_connection.ctp:26
msgid "The database server returned this error: %s"
msgstr ""

#: View/Errors/missing_connection.ctp:33
msgid "%s driver is NOT enabled"
msgstr ""

#: View/Errors/missing_controller.ctp:21
msgid "Missing Controller"
msgstr ""

#: View/Errors/missing_database.ctp:22
msgid "Scaffold requires a database connection"
msgstr ""

#: View/Errors/missing_database.ctp:26
#: View/Errors/missing_layout.ctp:26
#: View/Errors/missing_view.ctp:26
msgid "Confirm you have created the file: %s"
msgstr ""

#: View/Errors/missing_datasource.ctp:21
msgid "Missing Datasource"
msgstr ""

#: View/Errors/missing_datasource.ctp:24
msgid "Datasource class %s could not be found."
msgstr ""

#: View/Errors/missing_datasource_config.ctp:19
msgid "Missing Datasource Configuration"
msgstr ""

#: View/Errors/missing_datasource_config.ctp:22
msgid "The datasource configuration %1$s was not found in database.php."
msgstr ""

#: View/Errors/missing_helper.ctp:21
msgid "Missing Helper"
msgstr ""

#: View/Errors/missing_layout.ctp:19
msgid "Missing Layout"
msgstr ""

#: View/Errors/missing_layout.ctp:22
msgid "The layout file %s can not be found or does not exist."
msgstr ""

#: View/Errors/missing_plugin.ctp:19
msgid "Missing Plugin"
msgstr ""

#: View/Errors/missing_plugin.ctp:22
msgid "The application is trying to load a file from the %s plugin"
msgstr ""

#: View/Errors/missing_plugin.ctp:26
msgid "Make sure your plugin %s is in the %s directory and was loaded"
msgstr ""

#: View/Errors/missing_plugin.ctp:34
msgid "Loading all plugins"
msgstr ""

#: View/Errors/missing_plugin.ctp:35
msgid "If you wish to load all plugins at once, use the following line in your %s file"
msgstr ""

#: View/Errors/missing_table.ctp:19
msgid "Missing Database Table"
msgstr ""

#: View/Errors/missing_table.ctp:22
msgid "Table %1$s for model %2$s was not found in datasource %3$s."
msgstr ""

#: View/Errors/missing_view.ctp:19
msgid "Missing View"
msgstr ""

#: View/Errors/missing_view.ctp:22
msgid "The view for %1$s%2$s was not found."
msgstr ""

#: View/Errors/pdo_error.ctp:19
msgid "Database Error"
msgstr ""

#: View/Errors/pdo_error.ctp:26
msgid "SQL Query"
msgstr ""

#: View/Errors/pdo_error.ctp:31
msgid "SQL Query Params"
msgstr ""

#: View/Errors/private_action.ctp:19
msgid "Private Method in %s"
msgstr ""

#: View/Errors/private_action.ctp:22
msgid "%s%s cannot be accessed directly."
msgstr ""

#: View/Errors/scaffold_error.ctp:19
msgid "Scaffold Error"
msgstr ""

#: View/Errors/scaffold_error.ctp:22
msgid "Method _scaffoldError in was not found in the controller"
msgstr ""

#: View/Helper/CacheHelper.php:156
msgid "Unable to write view cache file: \"%s\" for \"%s\""
msgstr ""

#: View/Helper/FormHelper.php:1586
msgid "Missing field name for FormHelper::%s"
msgstr ""

#: View/Helper/HtmlHelper.php:1219
msgid "Cannot load the configuration file. Wrong \"configFile\" configuration."
msgstr ""

#: View/Helper/HtmlHelper.php:1225
msgid "Cannot load the configuration file. Unknown reader."
msgstr ""

#: View/Helper/JsHelper.php:152
msgid "JsHelper:: Missing Method %s is undefined"
msgstr ""

#: View/Helper/MootoolsEngineHelper.php:311
msgid "%s requires a \"drag\" option to properly function"
msgstr ""

#: View/Helper/PaginatorHelper.php:99
msgid "%s does not implement a %s method, it is incompatible with %s"
msgstr ""

#: Utility/CakeTime.php:152
msgid "abday"
msgstr ""

#: Utility/CakeTime.php:158
msgid "day"
msgstr ""

#: Utility/CakeTime.php:164
msgid "d_t_fmt"
msgstr ""

#: Utility/CakeTime.php:186
msgid "abmon"
msgstr ""

#: Utility/CakeTime.php:192
msgid "mon"
msgstr ""

#: Utility/CakeTime.php:203
msgid "am_pm"
msgstr ""

#: Utility/CakeTime.php:210
msgid "t_fmt_ampm"
msgstr ""

#: Utility/CakeTime.php:224
msgid "d_fmt"
msgstr ""

#: Utility/CakeTime.php:230
msgid "t_fmt"
msgstr ""

