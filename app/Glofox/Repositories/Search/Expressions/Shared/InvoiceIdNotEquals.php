<?php

namespace Glofox\Repositories\Search\Expressions\Shared;

use Glofox\Datasource\Exceptions\InvalidMongoIdException;
use Glofox\Repositories\Filters\Evaluable;
use Glofox\Repositories\Search\Expressions\Conditionable;
use Glofox\Repositories\Search\Expressions\HasConditions;
use Glofox\Repositories\Search\Expressions\Key;
use Webmozart\Expression\Expr;


class InvoiceIdNotEquals extends Key implements Evaluable, Conditionable
{
    use HasConditions;

    public function __construct(string $invoiceId)
    {
        parent::__construct('invoice_id', Expr::notEquals($invoiceId));
    }

    public function field() : string
    {
        return 'invoice_id';
    }
}
