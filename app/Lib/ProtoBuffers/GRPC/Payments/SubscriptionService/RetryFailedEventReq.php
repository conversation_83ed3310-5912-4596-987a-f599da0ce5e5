<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: subscription_service.proto

namespace GRPC\Payments\SubscriptionService;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>grpc.RetryFailedEventReq</code>
 */
class RetryFailedEventReq extends \Google\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>int32 SubscriptionID = 1;</code>
     */
    protected $SubscriptionID = 0;
    /**
     * Generated from protobuf field <code>string EventID = 2;</code>
     */
    protected $EventID = '';
    /**
     * Generated from protobuf field <code>string RequesterID = 3;</code>
     */
    protected $RequesterID = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int $SubscriptionID
     *     @type string $EventID
     *     @type string $RequesterID
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\SubscriptionService::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>int32 SubscriptionID = 1;</code>
     * @return int
     */
    public function getSubscriptionID()
    {
        return $this->SubscriptionID;
    }

    /**
     * Generated from protobuf field <code>int32 SubscriptionID = 1;</code>
     * @param int $var
     * @return $this
     */
    public function setSubscriptionID($var)
    {
        GPBUtil::checkInt32($var);
        $this->SubscriptionID = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string EventID = 2;</code>
     * @return string
     */
    public function getEventID()
    {
        return $this->EventID;
    }

    /**
     * Generated from protobuf field <code>string EventID = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setEventID($var)
    {
        GPBUtil::checkString($var, True);
        $this->EventID = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string RequesterID = 3;</code>
     * @return string
     */
    public function getRequesterID()
    {
        return $this->RequesterID;
    }

    /**
     * Generated from protobuf field <code>string RequesterID = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setRequesterID($var)
    {
        GPBUtil::checkString($var, True);
        $this->RequesterID = $var;

        return $this;
    }

}

