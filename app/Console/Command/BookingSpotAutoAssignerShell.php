<?php

use Glofox\Domain\BookingSpots\UseCase\BookingSpotAutoAssigner;
use Psr\Log\LoggerInterface;

App::uses('Shell', 'Console');

class BookingSpotAutoAssignerShell extends Shell
{
    private LoggerInterface $logger;
    private BookingSpotAutoAssigner $autoAssigner;

    public function __construct($stdout = null, $stderr = null, $stdin = null)
    {
        parent::__construct($stdout, $stderr, $stdin);
        $this->logger = app()->make(LoggerInterface::class);
        $this->autoAssigner = app()->make(BookingSpotAutoAssigner::class);
    }

    public function main(): void
    {
        $this->logger->info('[BookingSpotAutoAssignerShell] started');

        $this->autoAssigner->execute();

        $this->logger->info('[BookingSpotAutoAssignerShell] completed');
    }
}