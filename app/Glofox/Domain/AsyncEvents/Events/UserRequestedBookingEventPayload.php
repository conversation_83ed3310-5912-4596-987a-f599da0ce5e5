<?php

namespace Glofox\Domain\AsyncEvents\Events;

use Glofox\Eventkit\DomainEvent\AbstractDomainEventPayload;

class UserRequestedBookingEventPayload extends AbstractDomainEventPayload
{
    protected $schema = [
        'event',
        'member',
        'status',
        'payGym',
        'price',
        'paymentsEnabled',
        'guestBookings',
        'timezone',
        'paid',
        'paidWithCredits',
        'sessionUser',
        'metadata',
        'forceOverbook',
        'bookingRequestId',
    ];
}
