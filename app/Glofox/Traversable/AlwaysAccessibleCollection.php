<?php

namespace Glofox\Traversable;

use Glofox\Domain\Bookings\Collection;

class AlwaysAccessibleCollection extends Collection implements \ArrayAccess
{
    public function get($key, $default = null)
    {
        $value = parent::get($key, $default);

        if (is_array($value)) {
            return new self($value);
        }

        // Keep the chain of methods working in the case one of the items don't exist to avoid breaking the code.
        // e.g.: $item->get('existentProperty')->get('inexistentPropety')->has('myField')
        if (!$this->has($key)) {

            if ($default !== null) {
                return $default;
            }

            return new self([]);
        }

        if ($value  === null && $default !== null) {
            return $default;
        }

        return $value;
    }
}
