<?php

declare(strict_types=1);

namespace Glofox\Repositories\Search\Expressions\Shared;

use Glofox\Repositories\Filters\Evaluable;
use Glofox\Repositories\Search\Expressions\Conditionable;
use Glofox\Repositories\Search\Expressions\HasConditions;
use Glofox\Repositories\Search\Expressions\Key;
use Webmozart\Expression\Expr;

class FieldLessThan extends Key implements Evaluable, Conditionable
{
    use HasConditions;

    private string $field;

    public function __construct(string $field, $value)
    {
        $this->field = $field;
        parent::__construct($field, Expr::lessThan($value));
    }

    public function field() : string
    {
        return $this->field;
    }
}
