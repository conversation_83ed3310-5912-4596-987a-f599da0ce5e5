<?php

namespace Glofox\Response\Models\Branches\Features;

use Glofox\Response\BaseModel;

/**
 * @SuppressWarnings(PHPMD)
 */
class StripePaymentModel extends BaseModel
{
    /**
     * @var bool
     */
    public $enabled = false;

    /**
     * @var bool
     */
    public $livemode = false;

    /**
     * @var bool
     */
    public $pay_app = false;

    /**
     * @var bool
     */
    public $pay_gym = false;

    /**
     * @var string
     */
    public $publishable_key = '';

    /**
     * @var bool
     */
    public $cash_reciepts = false;

    /**
     * @var int
     */
    public $charge_percentage = 0;

    protected array $allowedFields = [
        'enabled',
        'livemode',
        'pay_app',
        'pay_gym',
        'publishable_key',
        'cash_reciepts',
        'charge_percentage',
    ];

    public function __construct(array $data)
    {
        $this->setModelFields($data);
    }
}
