## Interactions [/2.1/branches/{branchId}/leads/{userId}/interactions]

+ Parameters
    + branchId: 507f191e810c19729de860ea (string) - The identification of the branch
    + userId: 5b55e5742fc1bbef53293352 (string) - The identification of the user

+ Attributes
    + _id: 507f1f77bcf86cd799439011 (string)
    + branch_id: 507f191e810c19729de860ea (string)
    + user_id: 5b55e5742fc1bbef53293352 (string)
    + description: Notes sent to client (string, nullable) - A nullable string
    + type: NOTE (enum[string])
        + Members
            + NOTE - Note for Customer
            + AUTO_EMAIL - Automatic Transactional E-mail sent to Customer
            + MANUAL_EMAIL - Marketing / Contact E-mail sent to Customer
            + CALLED_AND_CONNECTED - Call to Customer
            + CALLED_AND_NO_ANSWER - Call without Answer to Customer
            + AUTO_PUSH_NOTIFICATION - Automatic Transactional Push Notification to Customer
    + created: 1532352489 (number)

### Retrieve Interactions of Customers [GET]

+ Request
    + Headers
          Authorization: Bearer {authToken}

+ Response 200 (application/json)
    + Attributes
        + success: true (boolean)
        + data (array, fixed)
            + (Interactions)
        + meta (object)
            + pagination (Pagination)

+ Response 400
    + Attributes (Generic Error)

### Create Interaction for Customer [POST]

+ Request
    + Headers
          Authorization: Bearer {authToken}
    + Attributes (Interactions)

+ Response 200 (application/json)
    + Attributes (Interactions)

+ Response 400
    + Attributes (Generic Error)