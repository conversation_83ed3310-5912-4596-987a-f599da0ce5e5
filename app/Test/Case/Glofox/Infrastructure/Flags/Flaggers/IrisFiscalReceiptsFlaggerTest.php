<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Infrastructure\Flags\Flaggers;

use Glofox\Domain\Branches\Models\Branch;
use Glofox\Domain\Experiments\Services\ExperimentsClient;
use Glofox\Infrastructure\Flags\Flaggers\IrisFiscalReceiptsFlagger;
use GuzzleHttp\Psr7\Response;

\App::import('Test/Case', 'GlofoxTestCase');

class IrisFiscalReceiptsFlaggerTest extends \GlofoxTestCase
{
    private $irisHttpClient;

    public function setUp()
    {
        parent::setUp();
        $this->irisHttpClient = \Mockery::mock(ExperimentsClient::class);
    }

    public function test_it_returns_false_for_has_if_response_is_403(): void
    {
        $this->irisHttpClient->shouldReceive('post')
            ->withArgs(
                function (string $endpoint, array $routeParams, array $body) {
                    $this->assertSame('GET_OUTCOMES', $endpoint);
                    $this->assertSame([
                        'testIdentifier' => 'fiscal-receipts',
                    ], $routeParams);
                    $this->assertSame([
                        'user' => [
                            'branch_id' => '49a7011a05c677b9a916612a',
                        ],
                    ], $body);

                    return true;
                }
            );

        $this->irisHttpClient->shouldReceive('getLastResponse')
            ->andReturn(
                new Response(403)
            );

        $branch = new Branch(['_id' => '49a7011a05c677b9a916612a']);
        $result = $this->createFlagger()->has($branch);

        $this->assertFalse($result);
    }

    public function test_it_returns_false_for_has_if_the_response_doesn_not_have_name_field(): void
    {
        $this->irisHttpClient->shouldReceive('post');
        $this->irisHttpClient->shouldReceive('getLastResponse')
            ->andReturn(
                new Response(200)
            );

        $branch = new Branch(['_id' => '49a7011a05c677b9a916612a']);
        $result = $this->createFlagger()->has($branch);

        $this->assertFalse($result);
    }

    public function test_it_returns_false_for_has_if_name_field_has_wrong_value(): void
    {
        $this->irisHttpClient->shouldReceive('post');
        $this->irisHttpClient->shouldReceive('getLastResponse')
            ->andReturn(
                new Response(200, [], json_encode([
                    'name' => 'foo',
                ]))
            );

        $branch = new Branch(['_id' => '49a7011a05c677b9a916612a']);
        $result = $this->createFlagger()->has($branch);

        $this->assertFalse($result);
    }

    public function test_it_returns_true_for_has_if_name_field_is_correct(): void
    {
        $this->irisHttpClient->shouldReceive('post');
        $this->irisHttpClient->shouldReceive('getLastResponse')
            ->andReturn(
                new Response(200, [], json_encode([
                    'name' => 'ENABLED',
                ]))
            );

        $branch = new Branch(['_id' => '49a7011a05c677b9a916612a']);
        $result = $this->createFlagger()->has($branch);

        $this->assertTrue($result);
    }

    private function createFlagger(): IrisFiscalReceiptsFlagger
    {
        return new IrisFiscalReceiptsFlagger(
            $this->irisHttpClient
        );
    }
}
