<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\Insurance\UseCase;

use App;
use Glofox\Domain\Insurance\Repositories\InsuranceCompanyRepository;
use Glofox\Domain\Insurance\UseCase\GetInsuranceCompanies;
use Glofox\Domain\Insurance\UseCase\GetInsuranceCompaniesParams;
use GlofoxTestCase;
use Mockery;
use MongoId;

App::import('Test/Case', 'GlofoxTestCase');

class GetInsuranceCompaniesTest extends GlofoxTestCase
{
    public $fixtures = [];
    private InsuranceCompanyRepository $mockedInsuranceCompaniesRepository;

    public function setUp(): void
    {
        parent::setUp();

        $this->mockedInsuranceCompaniesRepository = Mockery::mock(InsuranceCompanyRepository::class);
    }

    public function tearDown(): void
    {
        parent::tearDown();

        Mockery::close();
    }

    public function test_it_fetches_and_returns_proper_data_from_repository(): void
    {
        $branchId = (string)new MongoId();
        $insuranceCompanies = [
            [
                '_id' => (string)new MongoId(),
                'name' => 'test-name',
                'branch_id' => [$branchId],
                'number' => 'test-number',
            ],
            [
                '_id' => (string)new MongoId(),
                'name' => 'another-test-name',
                'branch_id' => [$branchId],
                'number' => 'another-test-number',
            ],
        ];

        $this->mockedInsuranceCompaniesRepository
            ->shouldReceive('getAllByBranchIdOrWithDefaultTrue')
            ->withArgs(function (string $param) use ($branchId) {
                $this->assertTextEquals($branchId, $param);
                return true;
            })
            ->andReturn($insuranceCompanies)
            ->once();

        $params = new GetInsuranceCompaniesParams(
            $branchId
        );

        $useCase = new GetInsuranceCompanies($this->mockedInsuranceCompaniesRepository);

        $result = $useCase->execute($params);

        self::assertEquals($insuranceCompanies, $result);
    }
}
