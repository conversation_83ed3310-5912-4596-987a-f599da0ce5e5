<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\Bookings\Validation\Validators\Request;

use App;
use CakeTestCases\Glofox\Domain\Users\Traits\AuthenticateUsersTrait;
use Glofox\Domain\Authentication\Auth;
use Glofox\Domain\Bookings\Requests\BookFacilityAsMemberRequest;
use Glofox\Domain\Bookings\Requests\BookFacilityAsStaffRequest;
use Glofox\Domain\Bookings\Validation\Validators\Request\BookEventBookRequestValidator;
use GlofoxTestCase;

App::import('Test/Case', 'GlofoxTestCase');

class BookFacilityBookRequestValidatorTest extends GlofoxTestCase
{
    use AuthenticateUsersTrait;

    public $fixtures = [
        'app.user',
        'app.branch'
    ];
    private BookEventBookRequestValidator $validator;

    public function setUp(): void
    {
        parent::setUp();

        $this->validator = new BookEventBookRequestValidator();
    }

    public function testValidateRequestAsAMemberSuccessfully(): void
    {
        $this->authenticateAsMember();

        $request = BookFacilityAsMemberRequest::create('', 'POST', [
            'model' => 'facility',
            'model_id' => '6759cec951232790f0382e85',
            'guest_bookings' => 2,
        ]);

        $this->validator->validate($request, Auth::user());
    }

    public function testValidateRequestAsAStaffSuccessfully(): void
    {
        $this->authenticateAsAdmin();

        $request = BookFacilityAsStaffRequest::create('', 'POST', [
            'model' => 'facility',
            'model_id' => '6759cec951232790f0382e85',
            'user_id' => '6759cec951232790f0382e85',
            'payment_method' => 'complimentary',
            'price' => 23.99,
            'guest_bookings' => 2,
            'charge' => false,
        ]);

        $this->validator->validate($request, Auth::user());
    }
}
