<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Infrastructure\Flags;

use Glofox\Domain\Branches\Models\Branch;
use Glofox\Domain\Experiments\Services\ExperimentsClient;
use Glofox\Infrastructure\Flags\Flag;
use Glofox\Infrastructure\Flags\Flagger;
use GlofoxTestCase;
use GuzzleHttp\Psr7\Response;
use Mockery;

\App::import('Test/Case', 'GlofoxTestCase');

class FlaggerTest extends GlofoxTestCase
{
    public const BRANCH_ID = '49a7011a05c677b9a916612a';
    private ExperimentsClient $flaggerClient;

    public function setUp(): void
    {
        parent::setUp();
        $this->flaggerClient = Mockery::mock(ExperimentsClient::class);
        app()->forgetInstance(ExperimentsClient::class);
    }

    public function tearDown(): void
    {
        parent::tearDown();
        app()->forgetInstance(ExperimentsClient::class);
        Mockery::close();
    }

    /**
     * @throws \JsonException
     */
    public function provideResponseScenarios(): array
    {
        return [
            'When response does not have name field , Then it should fail' => [
                'response' => new Response(200, [], json_encode([], JSON_THROW_ON_ERROR)),
                'expectedResult' => false,
            ],
            'When response has wrong name field value , then it should fail' => [
                'response' => new Response(200, [], json_encode(['name' => 'DISABLED'], JSON_THROW_ON_ERROR)),
                'expectedResult' => false,
            ],
            'When response has correct name field value , then it should pass' => [
                'response' => new Response(200, [], json_encode(['name' => 'ENABLED'], JSON_THROW_ON_ERROR)),
                'expectedResult' => true,
            ],
            'When response has name field value true , then it should pass' => [
                'response' => new Response(200, [], json_encode(['name' => true], JSON_THROW_ON_ERROR)),
                'expectedResult' => true,
            ],
            'When response has name field value false , then it should fail' => [
                'response' => new Response(200, [], json_encode(['name' => false], JSON_THROW_ON_ERROR)),
                'expectedResult' => false,
            ],
            'When response is 403, then it should fail' => [
                'response' => new Response(403),
                'expectedResult' => false,
                'endpoint' => 'GET_OUTCOMES',
                'routeParams' => [
                    'testIdentifier' => Flag::CONSENT,
                ],
                'body' => [
                    'user' => ['branch_id' => self::BRANCH_ID],
                ],
            ],
        ];
    }

    /**
     * @dataProvider provideResponseScenarios
     */
    public function test_flag_evaluation_based_on_response(
        Response $response,
        bool $expectedResult,
        string $endpoint = '',
        array $routeParams = [],
        array $body = []
    ): void {
        $this->flaggerClient = $this->getHttpClientMock($response, $endpoint, $routeParams, $body);
        $branch = new Branch(['_id' => self::BRANCH_ID]);
        $result = $this->createFlagger()->has($branch);

        $this->assertSame($expectedResult, $result);
    }

    private function createFlagger(): Flagger
    {
        app()->forgetInstance(ExperimentsClient::class);
        app()->instance(ExperimentsClient::class, $this->flaggerClient);

        $flagger = new Flagger();
        $flagger->withFlag(Flag::CONSENT());

        return $flagger;
    }

    private function getHttpClientMock(
        Response $response,
        string $endpoint = '',
        array $routeParams = [],
        array $body = []
    ) {
        $this->flaggerClient->shouldReceive('post')
            ->withArgs(
                function ($receivedEndpoint, $receivedRouteParams, $receivedBody) use ($endpoint, $routeParams, $body) {
                    if (!empty($endpoint)) {
                        $this->assertSame($endpoint, $receivedEndpoint);
                    }
                    if (!empty($routeParams)) {
                        $this->assertSame($routeParams, $receivedRouteParams);
                    }
                    if (!empty($body)) {
                        $this->assertSame($body, $receivedBody);
                    }
                    return true;
                }
            )
            ->andReturn($response);

        $this->flaggerClient->shouldReceive('getLastResponse')->andReturn($response);

        return $this->flaggerClient;
    }

    /**
     * @throws \JsonException
     */
    public function irisEndpointProvider(): array
    {
        return [
            'When calling iris service Enable endpoint with correct params then it passes' => [
                'action' => 'enable',
                'httpMethod' => 'post',
                'endpoint' => 'UPDATE_TEST_TARGETS',
                'endpointParams' => ['testIdentifier' => Flag::CONSENT],
                'body' => ['targetId' => self::BRANCH_ID],
                'expectedResult' => true,
                'responseStatus' => 200,
                'responseContent' => json_encode([Flag::CONSENT => 'ENABLED'], JSON_THROW_ON_ERROR),
            ],
            'When calling iris service Disable endpoint with correct params then it passes' => [
                'action' => 'disable',
                'httpMethod' => 'delete',
                'endpoint' => 'UPDATE_TEST_TARGETS',
                'endpointParams' => ['testIdentifier' => Flag::CONSENT],
                'body' => ['targetId' => self::BRANCH_ID],
                'expectedResult' => true,
                'responseStatus' => 200,
                'responseContent' => json_encode([Flag::CONSENT => 'DISABLED'], JSON_THROW_ON_ERROR),
            ],
        ];
    }

    /**
     * @dataProvider irisEndpointProvider
     * @throws \JsonException
     */
    public function test_iris_endpoint_calls_with_correct_params(
        string $action,
        string $httpMethod,
        string $endpoint,
        array $endpointParams,
        array $body,
        bool $expectedResult,
        int $responseStatus,
        string $responseContent
    ): void {
        $this->flaggerClient->shouldReceive($httpMethod)
            ->withArgs(
                fn(string $e, array $ep, array $b) => $e === $endpoint &&
                    $ep === $endpointParams &&
                    $b === $body
            )
            ->once()
            ->andReturnSelf();

        $this->flaggerClient->shouldReceive('getLastResponse')
            ->andReturn(new Response($responseStatus, [], $responseContent));

        $branch = new Branch(['_id' => self::BRANCH_ID]);
        $result = $this->createFlagger()->{$action}($branch);

        $this->assertEquals($expectedResult, $result);
    }
}
