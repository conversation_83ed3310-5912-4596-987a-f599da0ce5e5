type: string
enum:
  - payg
  - time
  - time_classes
  - num_classes
description: |
  There are 4 different types of membership:
  - payg: This is a Drop In member with no membership. payg is short for "pay-as-you-go"
  - time: This is a membership with no limit on the number of bookings made.
  - num_classes: This is a credits based membership, the user will have credits which you can retrieve with Get Credit Packs by Member ID. It has no end dates for the credits. Note Get Credit Packs by Member ID could return no credits, meaning the members credits are expired
  - time_classes: This is a subscription membership that limits the number of classes the user can go to. Eg; Max 12 Classes a month
