<?php

namespace Glofox\Payments\Providers\Gateway\ProtoParsers;

use Carbon\Carbon;
use Glofox\Payments\Entities\Card\Models\Card as ProviderCard;
use Google\Protobuf\Internal\RepeatedField;
use GRPC\Payments\AccountService\Card;

/**
 * Class CardParser.
 */
class CardParser
{
    private \Glofox\Payments\Providers\Gateway\ProtoParsers\SetupIntentParser $setupIntentParser;

    /**
     * CardParser constructor.
     */
    public function __construct()
    {
        $this->setupIntentParser = new SetupIntentParser();
    }

    /**
     * @param Card $card
     *
     * @return ProviderCard
     */
    public function parseFromProtoMessage($card): ProviderCard
    {
        $providerCard = new ProviderCard();
        $providerCard->setId($card->getID())
            ->setServiceProviderId($card->getProviderID())
            ->setMonth($card->getMonth())
            ->setYear($card->getYear())
            ->setLastFour($card->getLastFour())
            ->setBrand($card->getBrand())
            ->setPaymentServiceId($card->getPSPUUID())
            ->setPaymentServiceResponse($card->getPSPResponse())
            ->setCreatedAt(
                Carbon::parse($card->getCreatedAt())->getTimestamp()
            )
            ->setUpdatedAt(
                Carbon::parse($card->getUpdatedAt())->getTimestamp()
            );

        if (!empty($card->getSetupIntent())) {
            $setupIntent = $this->setupIntentParser->parseFromProtoMessage($card->getSetupIntent());
            $providerCard->setSetupIntent($setupIntent);
        }

        if (!empty($card->getChecks()) && !empty($card->getChecks()["zip_code_check_status"])) {
            $providerCard->setZipCodeCheckStatus($card->getChecks()["zip_code_check_status"]);
        }

        if (!empty($card->getBillingDetails()) && !empty($card->getBillingDetails()->getAddress())) {
            $providerCard->setZipCode($card->getBillingDetails()->getAddress()->getPostalCode());
        }

        return $providerCard;
    }

    /**
     * @param RepeatedField|Card[] $cards
     *
     * @return ProviderCard[]
     */
    public function parseFromProtoMessageCollection(RepeatedField $cards): ?array
    {
        $providerCards = null;

        if (empty($cards)) {
            return $providerCards;
        }

        foreach ($cards as $card) {
            $providerCards[] = $this->parseFromProtoMessage($card);
        }

        return $providerCards;
    }
}
