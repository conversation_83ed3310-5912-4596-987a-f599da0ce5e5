<?php

declare(strict_types=1);

namespace Glofox\Response\Models\Branches\Features\Message;

use Glofox\Response\BaseModel;

/**
 * @SuppressWarnings(PHPMD)
 */
class MessageModel extends BaseModel
{
    public int $order = 0;
    public bool $enabled = false;
    public SmsModel $sms;

    protected array $allowedFields = [
        'enabled',
        'order',
        'sms',
    ];

    public function __construct(array $data)
    {
        $this->setModelFields($data);
    }

}
