type: object
properties:
  _id:
    type: string
    pattern: '^[0-9a-fA-F]{24}$'
  user_id:
    type: string
    pattern: '^[0-9a-fA-F]{24}$'
  requester_user_id:
    type: string
    pattern: '^[0-9a-fA-F]{24}$'
  event_identifier:
    type: string
    enum:
      - CARD_ADDED
      - CARD_REMOVED
      - CARD_REPLACED
      - MANDATE_ADDED
      - MANDATE_REMOVED
      - MANDATE_REPLACED
      - MANDATE_STATUS_CHANGED
      - ENTITY_BOOKED
      - PRODUCT_PURCHASED
      - NON_SUB_MEMBERSHIP_PURCHASED
      - MEMBERSHIP_PRORATED
      - MEMBERSHIP_REMOVED
      - CUSTOM_CHARGE_EXECUTED
      - SUBSCRIPTION_PURCHASED
      - SUBSCRIPTION_CYCLE_PAID
      - SUBSCRIPTION_CYCLE_PAYMENT_FAILED
      - SUBSCRIPTION_PAUSED
      - SUBSCRIPTION_CANCELLED
      - HISTORIC_TRANSACTION_IMPORTED
      - NSF_FEE
      - EXTERNAL_ACCOUNT_UPDATED
  event_context:
    type: object
    description: Dynamic object that contains the context of the event
  created:
    type: string
    format: date-time
  type:
    type: string
  image_url:
    type: string
    format: uri
  invoice_id:
    type: string
required:
  - _id
  - user_id
  - event_identifier
  - created
