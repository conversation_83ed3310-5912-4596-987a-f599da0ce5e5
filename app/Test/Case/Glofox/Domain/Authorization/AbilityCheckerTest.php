<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\Authorization;

use CakeTestCases\Glofox\Domain\Authentication\Token\TokenGeneration;
use Glofox\Domain\Authorization\Abilities\BranchAbility;
use Glofox\Domain\Authorization\AbilityChecker;
use Glofox\Domain\Users\Models\User;
use Glofox\Domain\Users\Repositories\UsersRepository;
use Glofox\Repositories\Search\Expressions\Shared\Id;

\App::import('Test/Case', 'GlofoxTestCase');

final class AbilityCheckerTest extends \GlofoxTestCase
{
    use TokenGeneration;

    public $fixtures = [
        'app.user',
        'app.role',
    ];

    /** @test */
    public function it_checks_the_ability_for_a_specific_user(): void
    {
        $user = $this->getUser('5c7828a4d510f9635ad4a6b1');

        $result = $this->abilityChecker()
            ->setUser($user)
            ->canAccess(BranchAbility::UPDATE);

        $this->assertTrue($result);
    }

    private function getUser(string $identifier): User
    {
        /** @var UsersRepository $repository */
        $repository = app()->make(UsersRepository::class);

        return $repository->addCriteria(new Id($identifier))
            ->firstOrFail();
    }

    private function abilityChecker(): AbilityChecker
    {
        return app()->make(AbilityChecker::class);
    }

    /** @test */
    public function it_checks_the_ability_for_the_logged_user(): void
    {
        $token = $this->generateToken(['_id' => '5c7828a4d510f9635ad4a6b1']);
        $this->addTokenToRequest($token);

        $result = $this->abilityChecker()
            ->canAccess(BranchAbility::UPDATE);

        $this->assertTrue($result);
    }
}
