<?php

App::uses('Component', 'Controller');
App::uses('Folder', 'Utility');
App::uses('File', 'Utility');

/**
 * Class GetDoublePaymentsComponent.
 */
class GetDoublePaymentsComponent extends Component
{
    public $components = ['RequestHandler'];
    public $controller = null;
    protected $StripeCharge;

    /**
     * @param Controller $controller
     */
    public function initialize(Controller $controller)
    {
        $this->StripeCharge = ClassRegistry::init('StripeCharge');
        $this->Controller = $controller;
    }

    /**
     * @param $doublePaymentList
     *
     * @return bool
     */
    public function createDoublePayementReport($doublePaymentList)
    {
        $this->Controller->response->download('Double_Payments_Report.csv');
        $data = $doublePaymentList;
        $this->set(compact('data'));
        $this->layout = 'ajax';

        return true;
    }
}
