type: object
properties:
  _id:
    type: string
    description: Unique identifier for the booking
  first_name:
    type: string
    description: First name of the member booked into the event
  last_name:
    type: string
    description: Last name of the member booked into the event
  phone:
    type: string
    description: Phone number of the member booked into the event
  email:
    type: string
    description: Email address of the member booked into the event
  membership_type:
    $ref: "./membership_type.yml"
  name:
    type: string
    description: Full name of the member booked into the event
  time_start:
    type: integer
    description: The UTC time that the booking starts
  membership_name:
    type: string
    description: The name of the membership for the event
  plan_name:
    type: string
    description: The name of the plan
