<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Payments\Providers\Gateway\Merchants\DataModellers;

use Glofox\Domain\Branches\Models\Branch;
use Glofox\Domain\PaymentProviders\Models\PaymentProvider;
use Glofox\Payments\Providers\Gateway\Merchants\DataModellers\BasicDataModeller;
use Illuminate\Support\Collection;

\App::import('Test/Case', 'GlofoxTestCase');

final class BasicDataModellerTest extends \GlofoxTestCase
{
    public function test_it_formats_data(): void
    {
        $provider = new PaymentProvider();
        $branch = new Branch([
            '_id' => 'branch-123',
            'address' => [
                'currency' => 'USD',
            ],
        ]);
        $payload = new Collection();

        $modeller = new BasicDataModeller();

        $expected = [
            'Currency' => 'USD',
            'AuthObject' => [
                'BranchID' => 'branch-123',
            ],
        ];

        $this->assertSame($expected, $modeller->modelForCreating($provider, $branch, $payload)->toArray());
        $this->assertSame($expected, $modeller->modelForUpdating($provider, $branch, $payload)->toArray());
        $this->assertSame($expected, $modeller->modelForRegisterUrl($provider, $branch)->toArray());
    }
}
