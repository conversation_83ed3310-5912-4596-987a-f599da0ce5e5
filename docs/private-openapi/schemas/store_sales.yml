type: object
properties:
  _id:
    type: string
    description: Unique identifier for the given store sales.
  namespace:
    type: string
  branch_id:
    type: string
  invoice_id:
    type: string
    format: email
  purchase_code:
    type: string
    format: date
    example: '1997-10-31'
  product_id:
    description: Set to true if the user's email has been verified.
    type: string
  user_id:
    type: string
    format: date
    description: The date that the user was created.
  presentation_id:
    type: string
  product_name:
    type: string
  presentation_name:
    type: string
  quantity:
    type: integer
  user_name:
    type: string
  collected_date:
    type: object
    properties:
      sec:
        type: integer
      usec:
        type: integer
  created:
    type: object
    properties:
      sec:
        type: integer
      usec:
        type: integer
