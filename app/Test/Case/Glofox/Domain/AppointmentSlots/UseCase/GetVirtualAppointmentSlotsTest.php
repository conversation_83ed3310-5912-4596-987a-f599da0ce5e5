<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\AppointmentSlots\UseCase;

use App;
use Carbon\Carbon;
use Glofox\Domain\AppointmentSlots\Models\VirtualAppointmentSlot;
use Glofox\Domain\AppointmentSlots\UseCase\GetVirtualAppointmentSlots;
use Glofox\Domain\Bookings\Models\Booking;
use Glofox\Domain\Bookings\Repositories\BookingsRepository;
use Glofox\Domain\Events\Models\Event;
use Glofox\Domain\Events\Repositories\EventsRepository;
use Glofox\Domain\StaffAvailability\Http\Requests\GetAvailabilitiesRequest;
use Glofox\Domain\StaffAvailability\Http\Responses\StaffAvailability;
use Glofox\Domain\StaffAvailability\Http\Responses\StaffAvailabilityInfo;
use Glofox\Domain\StaffAvailability\Services\StaffAvailabilityHttpClient;
use Glofox\Domain\TimeSlotPatterns\Models\TimeSlotPattern;
use Glofox\Domain\TimeSlotPatterns\Repositories\TimeSlotPatternsRepository;
use Glofox\Domain\Users\Models\User;
use Glofox\Domain\Users\Repositories\UsersRepository;
use Glofox\Exception;
use Glofox\Infrastructure\Exception\HttpClientResponseException;
use GlofoxTestCase;
use Illuminate\Support\Collection;
use Mockery;
use MongoDate;
use ReflectionException;
use ReflectionMethod;

App::import('Test/Case', 'GlofoxTestCase');

class GetVirtualAppointmentSlotsTest extends GlofoxTestCase
{
    public $fixtures = [];
    private const TEST_BRANCH_ID = '6458e23901a7f3975430591d';
    private const TEST_STAFF_ID_1 = '644a78d510c576d3779e0d0e';
    /**
     * @var 2023-04-28 08:00:00
     */
    private const TEST_TIME_START_1 = 1_682_668_800;
    /**
     * @var 2023-04-28 14:00:00
     */
    private const TEST_TIME_FINISH_1 = 1_682_690_400;
    private const TEST_STAFF_ID_2 = '644a78e15b801bbe2cfb41a6';
    /**
     * @var 2023-04-28 12:00:00
     */
    private const TEST_TIME_START_2_1 = 1_682_683_200;
    /**
     * @var 2023-04-28 18:00:00
     */
    private const TEST_TIME_FINISH_2_1 = 1_682_704_800;
    /**
     * @var 2023-04-28 06:00:00
     */
    private const TEST_TIME_START_2_2 = 1_682_661_600;
    /**
     * @var 2023-04-28 10:00:00
     */
    private const TEST_TIME_FINISH_2_2 = 1_682_676_000;
    private const TEST_APPOINTMENT_ID_1 = 'test-appointment-id-1';
    private const TEST_APPOINTMENT_DURATION_1 = 60;
    private const TEST_APPOINTMENT_ID_2 = 'test-appointment-id-2';
    private const TEST_APPOINTMENT_DURATION_2 = 30;
    private TimeSlotPattern $appointment1;
    private TimeSlotPattern $appointment2;
    /**
     * @var TimeSlotPattern[]
     */
    private array $allAppointments;
    private User $staff1;
    /**
     * @var string[]
     */
    private array $allStaffIds;
    /**
     * @var User[]
     */
    private array $allUsers;
    /**
     * @var StaffAvailabilityInfo[]
     */
    private array $allAvailabilities;
    private TimeSlotPatternsRepository $timeSlotPatternsRepository;
    private UsersRepository $usersRepository;
    private StaffAvailabilityHttpClient $staffAvailabilityHttpClient;
    private BookingsRepository $bookingsRepository;
    private EventsRepository $eventsRepository;

    public function setUp(): void
    {
        parent::setUp();

        $this->mockLogger();

        $this->timeSlotPatternsRepository = Mockery::mock(TimeSlotPatternsRepository::class);
        $this->usersRepository = Mockery::mock(UsersRepository::class);
        $this->staffAvailabilityHttpClient = Mockery::mock(StaffAvailabilityHttpClient::class);
        $this->bookingsRepository = Mockery::mock(BookingsRepository::class);
        $this->eventsRepository = Mockery::mock(EventsRepository::class);

        $this->appointment1 = TimeSlotPattern::make([
            '_id' => self::TEST_APPOINTMENT_ID_1,
            'staff_ids' => [self::TEST_STAFF_ID_1, self::TEST_STAFF_ID_2],
            'time_slot_length' => self::TEST_APPOINTMENT_DURATION_1,
        ]);
        $this->appointment2 = TimeSlotPattern::make([
            '_id' => self::TEST_APPOINTMENT_ID_2,
            'staff_ids' => [self::TEST_STAFF_ID_1],
            'time_slot_length' => self::TEST_APPOINTMENT_DURATION_2,
            'private' => false,
        ]);
        $this->allAppointments = [$this->appointment1, $this->appointment2];

        $this->staff1 = User::make(['_id' => self::TEST_STAFF_ID_1]);
        $this->allStaffIds = [self::TEST_STAFF_ID_1, self::TEST_STAFF_ID_2];
        $this->allUsers = [
            $this->staff1,
            User::make(['_id' => self::TEST_STAFF_ID_2]),
        ];

        $this->allAvailabilities = [
            new StaffAvailabilityInfo(self::TEST_STAFF_ID_1, [
                new StaffAvailability(self::TEST_TIME_START_1, self::TEST_TIME_FINISH_1),
            ]),
            new StaffAvailabilityInfo(self::TEST_STAFF_ID_2, [
                new StaffAvailability(self::TEST_TIME_START_2_2, self::TEST_TIME_FINISH_2_2),
                new StaffAvailability(self::TEST_TIME_START_2_1, self::TEST_TIME_FINISH_2_1),
            ]),
        ];
    }

    public function tearDown(): void
    {
        parent::tearDown();
        $this->teardownLogger();
        Mockery::close();
    }

    /**
     * @throws Exception
     */
    public function testNoTimeStartTimeFinish(): void
    {
        $response = $this->executeUseCase(0, 0);
        $this->assertEmpty($response);
    }

    /**
     * @throws Exception
     */
    public function testItShouldReturnNothingWhenTimeStartIsGreaterThanTimeFinish(): void
    {
        $response = $this->executeUseCase(2, 1);
        $this->assertEmpty($response);
    }

    /**
     * @throws Exception
     */
    public function testNoActiveAppointments(): void
    {
        $return = [];
        $this->mockTimeSlotPatternsRepository('', $return);

        $response = $this->executeUseCase(1, 1);
        $this->assertEmpty($response);
    }

    /**
     * @throws Exception
     */
    public function testNoActiveTrainers(): void
    {
        $return = [$this->appointment1];
        $this->mockTimeSlotPatternsRepository('', $return);

        $userIds = $this->allStaffIds;
        $return = [];
        $this->mockUsersRepository($userIds, $return);

        $response = $this->executeUseCase(1, 1);
        $this->assertEmpty($response);
    }

    /**
     * @throws Exception
     */
    public function testNoAvailabilities(): void
    {
        $return = [$this->appointment1];
        $this->mockTimeSlotPatternsRepository('', $return);

        $userIds = $this->allStaffIds;
        $return = [$this->staff1];
        $this->mockUsersRepository($userIds, $return);

        $staffIds = [self::TEST_STAFF_ID_1];
        $timeStart = 1;
        $timeFinish = 1;
        $return = [];
        $this->mockGetAvailabilitiesService($staffIds, $timeStart, $timeFinish, $return);

        $response = $this->executeUseCase($timeStart, $timeFinish);
        $this->assertEmpty($response);
    }

    /**
     * @throws Exception
     */
    public function testAvailabilitiesServiceFails(): void
    {
        $return = [$this->appointment1];
        $this->mockTimeSlotPatternsRepository('', $return);

        $userIds = $this->allStaffIds;
        $return = [$this->staff1];
        $this->mockUsersRepository($userIds, $return);

        $staffIds = [self::TEST_STAFF_ID_1];
        $timeStart = 1;
        $timeFinish = 1;
        $return = [];
        $this->mockGetAvailabilitiesService($staffIds, $timeStart, $timeFinish, $return, true);

        $response = $this->executeUseCase($timeStart, $timeFinish);
        $this->assertEmpty($response);
    }

    /**
     * @throws Exception
     */
    public function testSlotsWithoutOverlappingEvents(): void
    {
        $return = [$this->appointment1];
        $this->mockTimeSlotPatternsRepository('', $return);

        $userIds = $this->allStaffIds;
        $return = $this->allUsers;
        $this->mockUsersRepository($userIds, $return);

        // 2023-04-28 13:00:00
        $timeStart = 1_682_686_800;
        // 2023-04-28 14:00:00
        $timeFinish = 1_682_690_400;
        $return = [
            new StaffAvailabilityInfo(self::TEST_STAFF_ID_1, [
                new StaffAvailability($timeStart, self::TEST_TIME_FINISH_1),
            ]),
            new StaffAvailabilityInfo(self::TEST_STAFF_ID_2, [
                new StaffAvailability($timeStart, $timeFinish),
            ]),
        ];
        $this->mockGetAvailabilitiesService($this->allStaffIds, $timeStart, $timeFinish, $return);

        $emptyReturn = [];
        $this->mockGetOverlappingBookingsByStaff(
            self::TEST_STAFF_ID_1,
            $timeStart,
            $timeFinish,
            $emptyReturn
        );
        $this->mockGetOverlappingBookingsByStaff(
            self::TEST_STAFF_ID_2,
            $timeStart,
            $timeFinish,
            $emptyReturn
        );

        $this->mockGetOverlappingEvents(
            self::TEST_STAFF_ID_1,
            $timeStart,
            $timeFinish,
            $emptyReturn
        );
        $this->mockGetOverlappingEvents(
            self::TEST_STAFF_ID_2,
            $timeStart,
            $timeFinish,
            $emptyReturn
        );

        $expectedResponse = [
            new VirtualAppointmentSlot(
                self::TEST_APPOINTMENT_ID_1,
                self::TEST_STAFF_ID_1,
                $timeStart,
                $timeFinish,
                $this->appointment1
            ),
            new VirtualAppointmentSlot(
                self::TEST_APPOINTMENT_ID_1,
                self::TEST_STAFF_ID_2,
                $timeStart,
                $timeFinish,
                $this->appointment1
            ),
        ];

        $response = $this->executeUseCase($timeStart, $timeFinish);
        $this->assertEquals($expectedResponse, $response);
    }

    /**
     * @throws Exception
     */
    public function testSlotsWithOverlappingEventsWithoutAvailableSpace(): void
    {
        $return = [$this->appointment1];
        $this->mockTimeSlotPatternsRepository('', $return);

        $userIds = $this->allStaffIds;
        $return = $this->allUsers;
        $this->mockUsersRepository($userIds, $return);

        // 2023-04-28 13:00:00
        $timeStart = 1_682_686_800;
        // 2023-04-28 14:00:00
        $timeFinish = 1_682_690_400;
        $return = [
            new StaffAvailabilityInfo(self::TEST_STAFF_ID_1, [
                new StaffAvailability($timeStart, self::TEST_TIME_FINISH_1),
            ]),
            new StaffAvailabilityInfo(self::TEST_STAFF_ID_2, [
                new StaffAvailability($timeStart, $timeFinish),
            ]),
        ];
        $this->mockGetAvailabilitiesService($this->allStaffIds, $timeStart, $timeFinish, $return);

        $return = [
            [
                'time_start' => $timeStart - (30 * 60),
                'time_finish' => $timeStart + (30 * 60),
            ],
            [
                'time_start' => $timeFinish - (30 * 60),
                'time_finish' => $timeFinish + (30 * 60),
            ],
        ];
        $this->mockGetOverlappingBookingsByStaff(
            self::TEST_STAFF_ID_1,
            $timeStart,
            $timeFinish,
            $return
        );
        $return = [
            [
                'time_start' => $timeStart + (15 * 60),
                'time_finish' => $timeFinish - (15 * 60),
            ],
        ];
        $this->mockGetOverlappingBookingsByStaff(
            self::TEST_STAFF_ID_2,
            $timeStart,
            $timeFinish,
            $return
        );
        $return = [];
        $this->mockGetOverlappingEvents(
            self::TEST_STAFF_ID_1,
            $timeStart,
            $timeFinish,
            $return
        );
        $return = [
            [
                'time_start' => $timeStart - (15 * 60),
                'time_finish' => $timeFinish + (15 * 60),
            ],
        ];
        $this->mockGetOverlappingEvents(
            self::TEST_STAFF_ID_2,
            $timeStart,
            $timeFinish,
            $return
        );

        $response = $this->executeUseCase($timeStart, $timeFinish);
        $this->assertEmpty($response);
    }

    /**
     * @throws Exception
     */
    public function testSlotsWithOverlappingEvents(): void
    {
        $return = $this->allAppointments;
        $this->mockTimeSlotPatternsRepository('', $return);

        $userIds = $this->allStaffIds;
        $return = $this->allUsers;
        $this->mockUsersRepository($userIds, $return);

        // 2023-04-28 00:00:00
        $timeStart = 1_682_640_000;
        // 2023-04-28 23:59:59
        $timeFinish = 1_682_726_399;
        $return = $this->allAvailabilities;
        $this->mockGetAvailabilitiesService($this->allStaffIds, $timeStart, $timeFinish, $return);

        $return = [
            [
                // 2023-04-28 08:30:00
                'time_start' => 1_682_670_600,
                // 2023-04-28 09:30:00
                'time_finish' => 1_682_674_200,
            ],
            [
                // 2023-04-28 13:30:00
                'time_start' => 1_682_688_600,
                // 2023-04-28 14:00:00
                'time_finish' => 1_682_690_400,
            ],
        ];
        $this->mockGetOverlappingBookingsByStaff(
            self::TEST_STAFF_ID_1,
            self::TEST_TIME_START_1,
            self::TEST_TIME_FINISH_1,
            $return
        );
        $return = [
            [
                // 2023-04-28 09:30:00
                'time_start' => 1_682_674_200,
                // 2023-04-28 10:30:00
                'time_finish' => 1_682_677_800,
            ],
        ];
        $this->mockGetOverlappingEvents(
            self::TEST_STAFF_ID_1,
            self::TEST_TIME_START_1,
            self::TEST_TIME_FINISH_1,
            $return
        );

        $return = [];
        $this->mockGetOverlappingBookingsByStaff(
            self::TEST_STAFF_ID_2,
            self::TEST_TIME_START_2_2,
            self::TEST_TIME_FINISH_2_2,
            $return
        );
        $return = [
            [
                // 2023-04-28 09:30:00
                'time_start' => 1_682_674_200,
                // 2023-04-28 12:30:00
                'time_finish' => 1_682_685_000,
            ],
        ];
        $this->mockGetOverlappingEvents(
            self::TEST_STAFF_ID_2,
            self::TEST_TIME_START_2_2,
            self::TEST_TIME_FINISH_2_2,
            $return
        );
        $return = [];
        $this->mockGetOverlappingBookingsByStaff(
            self::TEST_STAFF_ID_2,
            self::TEST_TIME_START_2_1,
            self::TEST_TIME_FINISH_2_1,
            $return
        );
        $return = [
            [
                // 2023-04-28 09:30:00
                'time_start' => 1_682_674_200,
                // 2023-04-28 12:30:00
                'time_finish' => 1_682_685_000,
            ],
        ];
        $this->mockGetOverlappingEvents(
            self::TEST_STAFF_ID_2,
            self::TEST_TIME_START_2_1,
            self::TEST_TIME_FINISH_2_1,
            $return
        );

        $expectedResponse = [
            new VirtualAppointmentSlot(
                self::TEST_APPOINTMENT_ID_1,
                self::TEST_STAFF_ID_2,
                // 2023-04-28 06:00:00
                self::TEST_TIME_START_2_2,
                // 2023-04-28 07:00:00
                1_682_665_200,
                $this->appointment1
            ),
            new VirtualAppointmentSlot(
                self::TEST_APPOINTMENT_ID_1,
                self::TEST_STAFF_ID_2,
                // 2023-04-28 07:00:00
                1_682_665_200,
                // 2023-04-28 08:00:00
                1_682_668_800,
                $this->appointment1
            ),
            new VirtualAppointmentSlot(
                self::TEST_APPOINTMENT_ID_1,
                self::TEST_STAFF_ID_2,
                // 2023-04-28 08:00:00
                1_682_668_800,
                // 2023-04-28 09:00:00
                1_682_672_400,
                $this->appointment1
            ),
            new VirtualAppointmentSlot(
                self::TEST_APPOINTMENT_ID_2,
                self::TEST_STAFF_ID_1,
                // 2023-04-28 08:00:00
                1_682_668_800,
                // 2023-04-28 08:30:00
                1_682_670_600,
                $this->appointment2
            ),
            new VirtualAppointmentSlot(
                self::TEST_APPOINTMENT_ID_1,
                self::TEST_STAFF_ID_1,
                // 2023-04-28 10:30:00
                1_682_677_800,
                // 2023-04-28 11:30:00
                1_682_681_400,
                $this->appointment1
            ),
            new VirtualAppointmentSlot(
                self::TEST_APPOINTMENT_ID_2,
                self::TEST_STAFF_ID_1,
                // 2023-04-28 10:30:00
                1_682_677_800,
                // 2023-04-28 11:00:00
                1_682_679_600,
                $this->appointment2
            ),
            new VirtualAppointmentSlot(
                self::TEST_APPOINTMENT_ID_2,
                self::TEST_STAFF_ID_1,
                // 2023-04-28 11:00:00
                1_682_679_600,
                // 2023-04-28 11:30:00
                1_682_681_400,
                $this->appointment2
            ),
            new VirtualAppointmentSlot(
                self::TEST_APPOINTMENT_ID_1,
                self::TEST_STAFF_ID_1,
                // 2023-04-28 11:30:00
                1_682_681_400,
                // 2023-04-28 12:30:00
                1_682_685_000,
                $this->appointment1
            ),
            new VirtualAppointmentSlot(
                self::TEST_APPOINTMENT_ID_2,
                self::TEST_STAFF_ID_1,
                // 2023-04-28 11:30:00
                1_682_681_400,
                // 2023-04-28 12:00:00
                1_682_683_200,
                $this->appointment2
            ),
            new VirtualAppointmentSlot(
                self::TEST_APPOINTMENT_ID_2,
                self::TEST_STAFF_ID_1,
                // 2023-04-28 12:00:00
                1_682_683_200,
                // 2023-04-28 12:30:00
                1_682_685_000,
                $this->appointment2
            ),
            new VirtualAppointmentSlot(
                self::TEST_APPOINTMENT_ID_1,
                self::TEST_STAFF_ID_1,
                // 2023-04-28 12:30:00
                1_682_685_000,
                // 2023-04-28 13:30:00
                1_682_688_600,
                $this->appointment1
            ),
            new VirtualAppointmentSlot(
                self::TEST_APPOINTMENT_ID_1,
                self::TEST_STAFF_ID_2,
                // 2023-04-28 12:30:00
                1_682_685_000,
                // 2023-04-28 13:30:00
                1_682_688_600,
                $this->appointment1
            ),
            new VirtualAppointmentSlot(
                self::TEST_APPOINTMENT_ID_2,
                self::TEST_STAFF_ID_1,
                // 2023-04-28 12:30:00
                1_682_685_000,
                // 2023-04-28 13:00:00
                1_682_686_800,
                $this->appointment2
            ),
            new VirtualAppointmentSlot(
                self::TEST_APPOINTMENT_ID_2,
                self::TEST_STAFF_ID_1,
                // 2023-04-28 13:00:00
                1_682_686_800,
                // 2023-04-28 13:30:00
                1_682_688_600,
                $this->appointment2
            ),
            new VirtualAppointmentSlot(
                self::TEST_APPOINTMENT_ID_1,
                self::TEST_STAFF_ID_2,
                // 2023-04-28 13:30:00
                1_682_688_600,
                // 2023-04-28 14:30:00
                1_682_692_200,
                $this->appointment1
            ),
            new VirtualAppointmentSlot(
                self::TEST_APPOINTMENT_ID_1,
                self::TEST_STAFF_ID_2,
                // 2023-04-28 14:30:00
                1_682_692_200,
                // 2023-04-28 15:30:00
                1_682_695_800,
                $this->appointment1
            ),
            new VirtualAppointmentSlot(
                self::TEST_APPOINTMENT_ID_1,
                self::TEST_STAFF_ID_2,
                // 2023-04-28 15:30:00
                1_682_695_800,
                // 2023-04-28 16:30:00
                1_682_699_400,
                $this->appointment1
            ),
            new VirtualAppointmentSlot(
                self::TEST_APPOINTMENT_ID_1,
                self::TEST_STAFF_ID_2,
                // 2023-04-28 16:30:00
                1_682_699_400,
                // 2023-04-28 17:30:00
                1_682_703_000,
                $this->appointment1
            ),
        ];

        $response = $this->executeUseCase($timeStart, $timeFinish);
        $this->assertEquals($expectedResponse, $response);
    }

    /**
     * @throws Exception
     */
    public function testSlotsByTrainer(): void
    {
        $return = [$this->appointment1];
        $this->mockTimeSlotPatternsRepository(self::TEST_STAFF_ID_1, $return);

        $userIds = [self::TEST_STAFF_ID_1];
        $return = [$this->staff1];
        $this->mockUsersRepository($userIds, $return);

        $staffIds = [self::TEST_STAFF_ID_1];
        // 2023-04-28 13:00:00
        $timeStart = 1_682_686_800;
        // 2023-04-28 14:00:00
        $timeFinish = 1_682_690_400;
        $return = [
            new StaffAvailabilityInfo(self::TEST_STAFF_ID_1, [
                new StaffAvailability($timeStart, $timeFinish),
            ]),
        ];
        $this->mockGetAvailabilitiesService($staffIds, $timeStart, $timeFinish, $return);

        $return = [];
        $this->mockGetOverlappingBookingsByStaff(
            self::TEST_STAFF_ID_1,
            $timeStart,
            $timeFinish,
            $return
        );
        $this->mockGetOverlappingEvents(
            self::TEST_STAFF_ID_1,
            $timeStart,
            $timeFinish,
            $return
        );

        $expectedResponse = [
            new VirtualAppointmentSlot(
                self::TEST_APPOINTMENT_ID_1,
                self::TEST_STAFF_ID_1,
                $timeStart,
                $timeFinish,
                $this->appointment1
            ),
        ];

        $response = $this->executeUseCase($timeStart, $timeFinish, self::TEST_STAFF_ID_1);
        $this->assertEquals($expectedResponse, $response);
    }

    /**
     * @dataProvider convertTimeStartToNextQuarterDataProvider
     * @param int $timeStart
     * @param int $expectedTime
     * @return void
     * @throws ReflectionException
     */
    public function testConvertTimeStartToNextQuarter(int $timeStart, int $expectedTime): void
    {
        $useCase = new GetVirtualAppointmentSlots(
            $this->timeSlotPatternsRepository,
            $this->usersRepository,
            $this->staffAvailabilityHttpClient,
            $this->bookingsRepository,
            $this->eventsRepository,
            $this->loggerMock
        );

        $convertTimeStartToNextQuarter = new ReflectionMethod($useCase, 'convertTimeStartToNextQuarter');
        $convertTimeStartToNextQuarter->setAccessible(true);

        $timeStart = $convertTimeStartToNextQuarter->invoke($useCase, $timeStart);
        $this->assertEquals($expectedTime, $timeStart);
    }

    public function convertTimeStartToNextQuarterDataProvider(): array
    {
        return [
            'When time is 00, then timeStart is not changed' => [
                // 2023-07-14 00:00:00
                'timeStart' => 1_689_292_800,
                // 2023-07-14 00:00:00
                'expectedTime' => 1_689_292_800,
            ],
            'When time is 15, then timeStart is not changed' => [
                // 2023-07-14 00:15:00
                'timeStart' => 1_689_293_700,
                // 2023-07-14 00:15:00
                'expectedTime' => 1_689_293_700,
            ],
            'When time is 30, then timeStart is not changed' => [
                // 2023-07-14 00:30:00
                'timeStart' => 1_689_294_600,
                // 2023-07-14 00:30:00
                'expectedTime' => 1_689_294_600,
            ],
            'When time is 45, then timeStart is not changed' => [
                // 2023-07-14 00:45:00
                'timeStart' => 1_689_295_500,
                // 2023-07-14 00:45:00
                'expectedTime' => 1_689_295_500,
            ],
            'When time is between Q1, then timeStart is changed to next 15 min' => [
                // 2023-07-14 00:03:00
                'timeStart' => 1_689_292_980,
                // 2023-07-14 00:15:00
                'expectedTime' => 1_689_293_700,
            ],
            'When time is between Q2, then timeStart is changed to next 30 min' => [
                // 2023-07-14 00:18:00
                'timeStart' => 1_689_293_880,
                // 2023-07-14 00:30:00
                'expectedTime' => 1_689_294_600,
            ],
            'When time is between Q3, then timeStart is changed to next 45 min' => [
                // 2023-07-14 00:35:00
                'timeStart' => 1_689_294_900,
                // 2023-07-14 00:45:00
                'expectedTime' => 1_689_295_500,
            ],
            'When time is between Q4, then timeStart is changed to next 00 min' => [
                // 2023-07-14 00:46:00
                'timeStart' => 1_689_295_560,
                // 2023-07-14 01:00:00
                'expectedTime' => 1_689_296_400,
            ],
            'When time is between Q4, hour is 23 and is leap year, then timeStart is changed to 00:00 of next day' => [
                // 2024-02-28 23:59:00
                'timeStart' => 1_709_164_740,
                // 2024-02-29 00:00:00
                'expectedTime' => 1_709_164_800,
            ],
        ];
    }

    /**
     * @throws Exception
     */
    private function executeUseCase(
        int $timeStart,
        int $timeFinish,
        ?string $staffId = null
    ): array {
        $useCase = new GetVirtualAppointmentSlots(
            $this->timeSlotPatternsRepository,
            $this->usersRepository,
            $this->staffAvailabilityHttpClient,
            $this->bookingsRepository,
            $this->eventsRepository,
            $this->loggerMock
        );
        return $useCase->execute(self::TEST_BRANCH_ID, $timeStart, $timeFinish, $staffId);
    }

    /**
     * @param string $staffId
     * @param TimeSlotPattern[] $return
     */
    private function mockTimeSlotPatternsRepository(string $staffId, array $return): void
    {
        $this->timeSlotPatternsRepository
            ->shouldReceive('getList')
            ->withArgs(function (
                string $branchIdParam,
                string $searchQuery,
                string $staffIdParam,
                bool $active,
                array $sort,
                int $page,
                int $limit,
                bool $private
            ) use ($staffId) {
                $this->assertEquals(self::TEST_BRANCH_ID, $branchIdParam);
                $this->assertEmpty($searchQuery);
                $this->assertEquals($staffId, $staffIdParam);
                $this->assertTrue($active);
                $this->assertEquals(['name' => 1], $sort);
                $this->assertEquals(1, $page);
                $this->assertEquals(0, $limit);
                $this->assertFalse($private);

                return true;
            })
            ->andReturn($return)
            ->once();
    }

    /**
     * @param string[] $userIds
     * @param User[] $return
     */
    private function mockUsersRepository(array $userIds, array $return): void
    {
        $this->usersRepository
            ->shouldReceive('findActiveByIds')
            ->withArgs(function (Collection $ids) use ($userIds) {
                $this->assertEquals(collect($userIds), $ids);
                return true;
            })
            ->andReturn($return)
            ->once();
    }

    /**
     * @param string[] $staffIds
     * @param int $timeStart
     * @param int $timeFinish
     * @param StaffAvailabilityInfo[] $return
     * @param bool $exception
     */
    private function mockGetAvailabilitiesService(
        array $staffIds,
        int $timeStart,
        int $timeFinish,
        array $return,
        bool $exception = false
    ): void {
        $mock = $this->staffAvailabilityHttpClient
            ->shouldReceive('getAvailabilities')
            ->withArgs(function (GetAvailabilitiesRequest $data) use ($staffIds, $timeStart, $timeFinish) {
                $this->assertEquals(self::TEST_BRANCH_ID, $data->branchId());
                $this->assertEquals($staffIds, $data->ids());
                $this->assertEquals($timeStart, $data->timeStart());
                $this->assertEquals($timeFinish, $data->timeFinish());
                return true;
            });

        if ($exception) {
            $mock->andThrow(new HttpClientResponseException(500, 'staff-availability-service-error'));
        } else {
            $mock->andReturn($return);
        }

        $mock->once();
    }

    private function mockGetOverlappingBookingsByStaff(
        string $staffId,
        int $timeStart,
        int $timeFinish,
        array $return
    ): void {
        $carbonTimeStart = Carbon::createFromTimestamp($timeStart);
        $carbonTimeFinish = Carbon::createFromTimestamp($timeFinish);
        $return = $this->timestampToMongoDate($return);

        $bookings = [];
        if (!empty($return)) {
            foreach ($return as $booking) {
                $bookings[] = Booking::make($booking);
            }
        }

        $this->bookingsRepository
            ->shouldReceive('getOverLappingBookingsByStaff')
            ->withArgs(function (
                string $staffIdParam,
                string $branchIdParam,
                Carbon $timeStartParam,
                Carbon $timeFinishParam,
                string $model
            ) use (
                $staffId,
                $carbonTimeStart,
                $carbonTimeFinish
            ) {
                $this->assertEquals(self::TEST_BRANCH_ID, $branchIdParam);
                $this->assertEquals($staffId, $staffIdParam);
                $this->assertEquals($carbonTimeStart, $timeStartParam);
                $this->assertEquals($carbonTimeFinish, $timeFinishParam);
                $this->assertEquals('appointments', $model);
                return true;
            })
            ->andReturn($bookings)
            ->once();
    }

    private function mockGetOverlappingEvents(
        string $staffId,
        int $timeStart,
        int $timeFinish,
        array $return
    ): void {
        $carbonTimeStart = Carbon::createFromTimestamp($timeStart);
        $carbonTimeFinish = Carbon::createFromTimestamp($timeFinish);
        $events = [];
        $return = $this->timestampToMongoDate($return);

        if (!empty($return)) {
            foreach ($return as $event) {
                $event['branch_id'] = self::TEST_BRANCH_ID;
                $event['is_online'] = false;
                $event['program_id'] = 'test-program-id';
                $event['program'] = [];
                $events[] = Event::make($event);
            }
        }

        $this->eventsRepository
            ->shouldReceive('getOverlappingEvents')
            ->withArgs(function (
                string $staffIdParam,
                Carbon $timeStartParam,
                Carbon $timeFinishParam,
                string $branchIdParam
            ) use (
                $staffId,
                $carbonTimeStart,
                $carbonTimeFinish
            ) {
                $this->assertEquals(self::TEST_BRANCH_ID, $branchIdParam);
                $this->assertEquals($staffId, $staffIdParam);
                $this->assertEquals($carbonTimeStart, $timeStartParam);
                $this->assertEquals($carbonTimeFinish, $timeFinishParam);
                return true;
            })
            ->andReturn($events)
            ->once();
    }

    private function timestampToMongoDate(array $items): array
    {
        if (empty($items)) {
            return [];
        }

        foreach ($items as &$item) {
            if (array_key_exists('time_start', $item)) {
                $item['time_start'] = new MongoDate($item['time_start']);
            }

            if (array_key_exists('time_finish', $item)) {
                $item['time_finish'] = new MongoDate($item['time_finish']);
            }
        }
        unset($item);

        return $items;
    }
}
