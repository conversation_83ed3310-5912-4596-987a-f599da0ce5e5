<?php

use CakeTestCases\Glofox\Domain\Authentication\Token\TokenGeneration;
use CakeTestCases\Glofox\Domain\Users\Traits\AuthenticateUsersTrait;
use CakeTestCases\Glofox\Domain\Users\Traits\FetchUsersTrait;
use Glofox\Domain\Dictionaries\Models\Dictionary;
use Glofox\Domain\Dictionaries\Repositories\DictionariesRepository;
use Glofox\Domain\Users\Services\Avatar\AvatarUploader;

App::import('Test/Case', 'GlofoxControllerTestCase');
App::uses('AppController', 'Controller');

/**
 * @deprecated this test suite will be replaced for {@see app/Test/Case/Controller/AssetsControllerTest.php}
 */
class OldAssetsControllerTest extends GlofoxControllerTestCase
{
    use FetchUsersTrait;
    use TokenGeneration;
    use AuthenticateUsersTrait;

    private const EMPTY_FILES = ['Image' => []];
    private const NOT_ALLOWED_TO_UPLOAD_IMAGES_FOR_THIS_USER = 'NOT_ALLOWED_TO_UPLOAD_IMAGES_FOR_THIS_USER';
    private const ERROR_UNAUTHORIZED = 'ERROR_UNAUTHORIZED';

    public function testItSavesNewUserProfileImage(): void
    {
        $_FILES = ['Image' => []];

        $avatarUploader = \Mockery::mock(AvatarUploader::class);
        $avatarUploader->shouldReceive('upload')->andReturn('my-new-avatar-url');
        $avatarUploader->shouldReceive('hasFile')->andReturn(true);
        app()->instance(AvatarUploader::class, $avatarUploader);

        // User authenticated as Admin, uploading image for Trainer is allowed.
        $this->loginAsUser($this->fetchUser('59a7011a05c677bda916612a'));
        $user = $this->fetchUser('59a7011a05c677bda916612c');
        $result = $this->testAction("/assets/upload/users/{$user->id()}/profile", ['method' => 'POST']);
        $result = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        $this->assertEquals(200, $this->response->statusCode());
        $this->assertTrue($result['success']);
        $this->assertEquals('my-new-avatar-url', $result['avatar']);

        $user = $this->fetchUser('59a7011a05c677bda916612c');
        $this->assertEquals('my-new-avatar-url', $user->avatar());

        app()->forgetInstance(AvatarUploader::class);
    }

    public function test_it_allows_upload_by_integrator(): void
    {
        $_FILES = ['Image' => []];

        $avatarUploader = \Mockery::mock(AvatarUploader::class);
        $avatarUploader->shouldReceive('upload')->andReturn('my-new-avatar-url');
        $avatarUploader->shouldReceive('hasFile')->andReturn(true);
        app()->instance(AvatarUploader::class, $avatarUploader);

        $_SERVER['HTTP_X_GLOFOX_SOURCE'] = 'IMPORTS';
        $_SERVER['HTTP_X_GLOFOX_API_TOKEN'] = 'INTEGRATOR-TEST-TOKEN';
        $_SERVER['HTTP_X_GLOFOX_BRANCH_ID'] = '49a7011a05c677b9a916612a';

        $user = $this->fetchUser('59a7011a05c677bda916612c');
        $result = $this->testAction("/assets/upload/users/{$user->id()}/profile", ['method' => 'POST']);
        $result = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        $this->assertEquals(200, $this->response->statusCode());
        $this->assertTrue($result['success'], $result['message'] ?? '');
        $this->assertEquals('my-new-avatar-url', $result['avatar']);

        $user = $this->fetchUser('59a7011a05c677bda916612c');
        $this->assertEquals('my-new-avatar-url', $user->avatar());

        app()->forgetInstance(AvatarUploader::class);
    }

    /**
     * This test isn't testing what is supposed from the title
     * @return void
     */
    public function testMemberUploadImageForHimself(): void
    {
        $_FILES = ['Image' => []];

        $avatarUploader = \Mockery::mock(AvatarUploader::class);
        $avatarUploader->shouldReceive('upload')->andReturn('my-new-avatar-url');
        $avatarUploader->shouldReceive('hasFile')->andReturn(true);
        app()->instance(AvatarUploader::class, $avatarUploader);

        // User authenticated as SuperAdmin, uploading image for Trainer is allowed.
        $this->loginAsUser($this->fetchUser('5e9ed06cd575e6003c38658e'));
        $user = $this->fetchUser('59a7011a05c677bda916612c');

        $result = $this->testAction("/assets/upload/users/{$user->id()}/profile", ['method' => 'POST']);
        $result = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        $this->assertEquals(200, $this->response->statusCode());
        $this->assertTrue($result['success']);
        $this->assertEquals('my-new-avatar-url', $result['avatar']);

        $user = $this->fetchUser('59a7011a05c677bda916612c');
        $this->assertEquals('my-new-avatar-url', $user->avatar());

        app()->forgetInstance(AvatarUploader::class);
    }

    public function testSuperAdminUploadImageForTrainer(): void
    {
        $_FILES = ['Image' => []];

        $avatarUploader = \Mockery::mock(AvatarUploader::class);
        $avatarUploader->shouldReceive('upload')->andReturn('my-new-avatar-url');
        $avatarUploader->shouldReceive('hasFile')->andReturn(true);

        app()->instance(AvatarUploader::class, $avatarUploader);

        $this->loginAsUser($this->fetchUser('59a3011a05c677bda916612d'));
        $user = $this->fetchUser('59a3011a05c677bda916612d');

        $result = $this->testAction("/assets/upload/users/{$user->id()}/profile", ['method' => 'POST']);
        $result = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        $this->assertEquals(200, $this->response->statusCode());
        $this->assertTrue($result['success']);
        $this->assertEquals('my-new-avatar-url', $result['avatar']);

        $user = $this->fetchUser('59a3011a05c677bda916612d');
        $this->assertEquals('my-new-avatar-url', $user->avatar());

        app()->forgetInstance(AvatarUploader::class);
    }

    public function testReceptionistUploadImageForMember(): void
    {
        $_FILES = ['Image' => []];

        $avatarUploader = \Mockery::mock(AvatarUploader::class);
        $avatarUploader->shouldReceive('upload')->andReturn('my-new-avatar-url');
        $avatarUploader->shouldReceive('hasFile')->andReturn(true);
        app()->instance(AvatarUploader::class, $avatarUploader);

        $this->loginAsUser($this->fetchUser('59a7011a05c677bda916619b'));
        $user = $this->fetchUser('59a3011a05c677bda916612d');

        $result = $this->testAction("/assets/upload/users/{$user->id()}/profile", ['method' => 'POST']);
        $result = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        $this->assertEquals(200, $this->response->statusCode());
        $this->assertTrue($result['success']);
        $this->assertEquals('my-new-avatar-url', $result['avatar']);

        app()->forgetInstance(AvatarUploader::class);
    }

    /**
     * @throws JsonException
     */
    public function testSuperAdminUploadImageForInactiveUser(): void
    {
        $_FILES = static::EMPTY_FILES;

        $this->authenticateAsSuperAdmin();
        $result = $this->uploadUserImage('889454f194a0d9a16433730e');

        $this->assertEquals(200, $this->response->statusCode());
        $this->assertFalse($result['success']);
        $this->assertEquals(static::NOT_ALLOWED_TO_UPLOAD_IMAGES_FOR_THIS_USER, $result['message']);
    }

    /**
     * @throws JsonException
     */
    public function testTrainerUploadImageForMember(): void
    {
        $_FILES = static::EMPTY_FILES;

        $this->authenticateAsTrainer();
        $result = $this->uploadUserImage('59a3011a05c677bda916612d');

        $this->assertEquals(200, $this->response->statusCode());
        $this->assertFalse($result['success']);
        $this->assertEquals(static::NOT_ALLOWED_TO_UPLOAD_IMAGES_FOR_THIS_USER, $result['message']);
    }

    public function testMemberUploadImageForInvalidUser(): void
    {
        $_FILES = ['Image' => []];

        $this->loginAsUser($this->fetchUser('59a3011a05c677bda916612d'));
        $result = $this->testAction(
            "/assets/upload/users/59a3011a05c677bda916612ddoesntexist/profile",
            ['method' => 'POST']
        );

        $result = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        $this->assertEquals(200, $this->response->statusCode());
        $this->assertFalse($result['success']);
        $this->assertEquals(
            'The following id is not valid: 59a3011a05c677bda916612ddoesntexist',
            $result['message']
        );
    }

    public function testImageIsNotUploaded(): void
    {
        $_FILES = ['Image' => []];

        $avatarUploader = \Mockery::mock(AvatarUploader::class);
        $avatarUploader->shouldReceive('upload')->andReturn('my-new-avatar-url-2');
        $avatarUploader->shouldReceive('hasFile')->andReturn(false);
        app()->instance(AvatarUploader::class, $avatarUploader);

        $this->loginAsUser($this->fetchUser('59a7011a05c677bda916612a'));
        $user = $this->fetchUser('59a7011a05c677bda916612c');

        $result = $this->testAction("/assets/upload/users/{$user->id()}/profile", ['method' => 'POST']);
        $result = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        $this->assertEquals(200, $this->response->statusCode());
        $this->assertFalse($result['success']);
        $this->assertEquals('UPLOAD_IMAGE_MISSING', $result['message']);

        app()->forgetInstance(AvatarUploader::class);
    }

    public function testImageRejectByModeration(): void
    {
        $user = $this->fetchUser('59a7011a05c677bda916612c');

        $this->loginAsUser(
            $this->fetchUser('59a7011a05c677bda916612a')
        );

        $_FILES = ['Image' => []];

        $avatarUploader = \Mockery::mock(AvatarUploader::class);
        $avatarUploader->shouldReceive('hasFile')->andReturn(true);
        $avatarUploader
            ->shouldReceive('upload')
            ->andThrow(new \BadRequestException('IMAGE_REJECTED_BY_MODERATION'));

        app()->instance(AvatarUploader::class, $avatarUploader);

        $dictionary = [
            '_id' => new MongoId('56fb0cff778f3b4db5000000'),
            'namespace' => 'glofox',
            'branch_id' => '49a7011a05c677b9a916612a',
            'dictionary' => [
                'IMAGE_REJECTED_BY_MODERATION' => 'There was an error uploading this image, please try another image or contact support if the isses persists',
            ],
            'language' => [
                'code' => 'en',
            ],
        ];

        $dictionaryMock = \Mockery::mock(DictionariesRepository::class);
        $dictionaryMock
            ->shouldReceive('findByBranchId')
            ->withArgs(function (string $branchId) {
                self::assertEquals('49a7011a05c677b9a916612a', $branchId);

                return true;
            })
            ->andReturn(Dictionary::make($dictionary))
            ->once()
            ->shouldReceive('translate')
            ->withArgs(function (string $message, string $code) {
                self::assertEquals('49a7011a05c677b9a916612a', $message);

                return true;
            })
            ->andReturn();
        app()->instance(DictionariesRepository::class, $dictionaryMock);

        $result = $this->testAction("/assets/upload/users/{$user->id()}/profile", [
            'method' => 'POST',
        ]);

        $result = json_decode($result, $assoc = true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        $this->assertEquals(403, $this->response->statusCode());
        $this->assertFalse($result['success']);
        $this->assertEquals($dictionary['dictionary']['IMAGE_REJECTED_BY_MODERATION'], $result['message']);

        app()->forgetInstance(AvatarUploader::class);
        app()->forgetInstance(DictionariesRepository::class);
    }

    /**
     * @throws JsonException
     */
    public function testGuestUploadImageForMember(): void
    {
        $_FILES = static::EMPTY_FILES;

        $this->authenticateAsGuest();
        $result = $this->uploadUserImage('59a3011a05c677bda916612d');

        $this->assertEquals(200, $this->response->statusCode());
        $this->assertFalse($result['success']);
        $this->assertEquals(static::ERROR_UNAUTHORIZED, $result['message_code']);
    }

    /**
     * @throws JsonException
     */
    private function uploadUserImage(string $userId): array
    {
        $result = $this->testAction("/assets/upload/users/" . $userId . "/profile", ['method' => 'POST']);
        return json_decode($result, true, 512, JSON_THROW_ON_ERROR);
    }
}
