<?php

namespace CakeTestCases\Glofox;

use Exception;
use Glofox\BatchPricing\SubscriptionsService;
use Glofox\Domain\Memberships\Services\AddonServiceInterface;
use Glofox\Domain\SalesTaxes\Services\PriceCalculatorServiceInterface;
use Glofox\Domain\Users\Models\User;
use Glofox\Domain\Users\Repositories\UsersRepository;
use Glofox\Payments\Entities\Subscription\Internal\UpdateItemPricesLineItem;
use Glofox\Payments\Entities\Subscription\Models\Subscription;
use Mockery;
use Glofox\Payments\Providers\Gateway\Subscriptions\Subscriptions;
use Illuminate\Http\JsonResponse;
use PHPUnit_Framework_TestCase;
use Throwable;

class SubscriptionsServiceTest extends PHPUnit_Framework_TestCase
{
    public function tearDown(): void
    {
        parent::tearDown();

        Mockery::close();
    }

    public function testUpdateItemPrices_Success()
    {
        $subscriptionContract = Mockery::mock(Subscriptions::class);
        $usersRepository = Mockery::mock(UsersRepository::class);
        $priceCalculator = Mockery::mock(PriceCalculatorServiceInterface::class);
        $addonService = Mockery::mock(AddonServiceInterface::class);

        $subscriptionId = 1;
        $service = 'memberships';
        $externalRef = 'abc:123';
        $price = 100;

        $subscriptionContract
            ->shouldReceive('updateItemPrices')
            ->withArgs(function (int $inSubscriptionId, UpdateItemPricesLineItem ...$lineItems) use ($subscriptionId, $service, $externalRef, $price) {
                self::assertEquals($subscriptionId, $inSubscriptionId);
                self::assertCount(1, $lineItems);
                self::assertEquals($service, $lineItems[0]->service());
                self::assertEquals($externalRef, $lineItems[0]->externalRef());
                self::assertEquals($price, $lineItems[0]->price());

                return true;
            })
            ->andReturn(new Subscription())
            ->once();

        $subscriptions = new SubscriptionsService($subscriptionContract, $usersRepository, $priceCalculator, $addonService);
        $subscriptions->updateItemPrices(1, new UpdateItemPricesLineItem($service, $externalRef, $price));
    }

    public function testUpdateItemPrices_Error()
    {
        $subscriptionContract = Mockery::mock(Subscriptions::class);
        $usersRepository = Mockery::mock(UsersRepository::class);
        $priceCalculator = Mockery::mock(PriceCalculatorServiceInterface::class);
        $addonService = Mockery::mock(AddonServiceInterface::class);

        $subscriptionId = 1;
        $service = 'memberships';
        $externalRef = 'abc:123';
        $price = 100;

        $subscriptionContract
            ->shouldReceive('updateItemPrices')
            ->withArgs(function (int $inSubscriptionId, UpdateItemPricesLineItem ...$lineItems) use ($subscriptionId, $service, $externalRef, $price) {
                self::assertEquals($subscriptionId, $inSubscriptionId);
                self::assertCount(1, $lineItems);
                self::assertEquals($service, $lineItems[0]->service());
                self::assertEquals($externalRef, $lineItems[0]->externalRef());
                self::assertEquals($price, $lineItems[0]->price());

                return true;
            })
            ->andThrow(new Exception("oh no"))
            ->once();

        $this->setExpectedException(Throwable::class, "oh no");

        $subscriptions = new SubscriptionsService($subscriptionContract, $usersRepository, $priceCalculator, $addonService);
        $subscriptions->updateItemPrices(1, new UpdateItemPricesLineItem($service, $externalRef, $price));
    }

    public function testRecalculateSubscription_Success()
    {
        $subscriptionContract = Mockery::mock(Subscriptions::class);
        $usersRepository = Mockery::mock(UsersRepository::class);
        $priceCalculator = Mockery::mock(PriceCalculatorServiceInterface::class);
        $addonService = Mockery::mock(AddonServiceInterface::class);

        $branchId = 'branch-123';
        $user = $this->generateGenericUser();

        $this->mockUsersRepositoryWithUser($usersRepository, $user);

        $addonService
            ->shouldReceive('getAddonsByMemberId')
            ->andReturn(new JsonResponse([[
                'id' => 'user-add-on-id-123',
                'definition' => [
                    'id' => 'add-on-id-123',
                    'plan' => [
                        'isRecurring' => true,
                    ]
                ],
                'price' => 5000
            ],
            [
                'id' => 'user-add-on-id-456',
                'definition' => [
                    'id' => 'add-on-id-789',
                    'plan' => [
                        'isRecurring' => false,
                    ]
                ],
                'price' => 7500
            ]]))
            ->once();

        $priceCalculator
            ->shouldReceive('calculateTaxes')
            ->andReturn(new JsonResponse([
                'products' => [
                    [
                        'service_id' => 'user-mem-id-123:membership-id-123',
                        'service_type' => 'memberships',
                        'net_price' => '5000',
                        'total_price' => '10000'
                    ],
                    [
                        'service_id' => 'user-add-on-id-123:add-on-id-123',
                        'service_type' => 'services',
                        'net_price' => '2500',
                        'total_price' => '5000'
                    ],
                ]
            ]))
            ->once();

        $subscriptionContract
            ->shouldReceive('updateItemPrices')
            ->andReturn(new Subscription())
            ->once();

        $subscriptions = new SubscriptionsService($subscriptionContract, $usersRepository, $priceCalculator, $addonService);
        $results = $subscriptions->recalculateSubscriptions($branchId, 'exclusive');
        self::assertEquals('success', $results[0]['result']);
        self::assertEquals('success', $results[1]['result']);
    }

    public function testRecalculateSubscription_ErrorFetchingUsers()
    {
        $subscriptionContract = Mockery::mock(Subscriptions::class);
        $usersRepository = Mockery::mock(UsersRepository::class);
        $priceCalculator = Mockery::mock(PriceCalculatorServiceInterface::class);
        $addonService = Mockery::mock(AddonServiceInterface::class);

        $branchId = 'branch-123';

        $usersRepository
            ->shouldReceive('addCriteria->addCriteria->fields->find')
            ->andThrow($this->generateGenericException())
            ->once();

        $this->setExpectedException(Throwable::class, "generic exception");

        $subscriptions = new SubscriptionsService($subscriptionContract, $usersRepository, $priceCalculator, $addonService);
        $subscriptions->recalculateSubscriptions($branchId, 'exclusive');
    }

    public function testRecalculateSubscription_ErrorFetchingAddons()
    {
        $subscriptionContract = Mockery::mock(Subscriptions::class);
        $usersRepository = Mockery::mock(UsersRepository::class);
        $priceCalculator = Mockery::mock(PriceCalculatorServiceInterface::class);
        $addonService = Mockery::mock(AddonServiceInterface::class);

        $branchId = 'branch-123';
        $user = $this->generateGenericUser();

        $this->mockUsersRepositoryWithUser($usersRepository, $user);

        $addonService
            ->shouldReceive('getAddonsByMemberId')
            ->andThrow($this->generateGenericException())
            ->once();

        $subscriptions = new SubscriptionsService($subscriptionContract, $usersRepository, $priceCalculator, $addonService);
        $results = $subscriptions->recalculateSubscriptions($branchId, 'exclusive');
        self::assertEquals('error', $results[0]['result']);
    }

    public function testRecalculateSubscription_ErrorCalculatingPrices()
    {
        $subscriptionContract = Mockery::mock(Subscriptions::class);
        $usersRepository = Mockery::mock(UsersRepository::class);
        $priceCalculator = Mockery::mock(PriceCalculatorServiceInterface::class);
        $addonService = Mockery::mock(AddonServiceInterface::class);

        $branchId = 'branch-123';
        $user = $this->generateGenericUser();

        $this->mockUsersRepositoryWithUser($usersRepository, $user);

        $addonService
            ->shouldReceive('getAddonsByMemberId')
            ->andReturn(new JsonResponse([[
                'id' => 'user-add-on-id-123',
                'definition' => [
                    'id' => 'add-on-id-123',
                    'plan' => [
                        'isRecurring' => true,
                    ]
                ],
                'price' => 5000
            ]]))
            ->once();

        $priceCalculator
            ->shouldReceive('calculateTaxes')
            ->andThrow($this->generateGenericException())
            ->once();

        $subscriptions = new SubscriptionsService($subscriptionContract, $usersRepository, $priceCalculator, $addonService);
        $results = $subscriptions->recalculateSubscriptions($branchId, 'exclusive');
        self::assertEquals('error', $results[0]['result']);
    }

    public function testRecalculateSubscription_ErrorUpdatingItemPrices()
    {
        $subscriptionContract = Mockery::mock(Subscriptions::class);
        $usersRepository = Mockery::mock(UsersRepository::class);
        $priceCalculator = Mockery::mock(PriceCalculatorServiceInterface::class);
        $addonService = Mockery::mock(AddonServiceInterface::class);

        $branchId = 'branch-123';
        $user = $this->generateGenericUser();

        $this->mockUsersRepositoryWithUser($usersRepository, $user);

        $addonService
            ->shouldReceive('getAddonsByMemberId')
            ->andReturn(new JsonResponse([[
                'id' => 'user-add-on-id-123',
                'definition' => [
                    'id' => 'add-on-id-123',
                    'plan' => [
                        'isRecurring' => true,
                    ]
                ],
                'price' => 5000
            ]]))
            ->once();

        $priceCalculator
            ->shouldReceive('calculateTaxes')
            ->andReturn(new JsonResponse([
                'products' => [
                    [
                        'service_id' => 'user-mem-id-123:membership-id-123',
                        'service_type' => 'memberships',
                        'net_price' => '5000',
                        'total_price' => '10000'
                    ],
                    [
                        'service_id' => 'user-add-on-id-123:add-on-id-123',
                        'service_type' => 'services',
                        'net_price' => '2500',
                        'total_price' => '5000'
                    ],
                ]
            ]))
            ->once();

        $subscriptionContract
            ->shouldReceive('updateItemPrices')
            ->andThrow($this->generateGenericException())
            ->once();

        $subscriptions = new SubscriptionsService($subscriptionContract, $usersRepository, $priceCalculator, $addonService);
        $results = $subscriptions->recalculateSubscriptions($branchId, 'exclusive');
        self::assertEquals('error', $results[0]['result']);
    }

    private function generateGenericUser(): User
    {
        $user = new User();
        $user['_id'] = '5450ca86d7b6dd21ccf97531';
        $user['membership'] = [
            '_id' => '5450ca87d7b6dd21ccf9757e',
            'user_membership_id' => 'user-mem-id-123',
            'subscription' => [
                'price' => 100,
                'stripe_id' => 12345
            ]
        ];
        return $user;
    }

    private function generateGenericException(): Exception
    {
        return new Exception("generic exception");
    }

    private function mockUsersRepositoryWithUser($usersRepository, $user)
    {
        $usersRepository
            ->shouldReceive('addCriteria->addCriteria->fields->find')
            ->andReturn([$user])
            ->once();
    }
}
