<?php


namespace CakeTestCases\Glofox\Payments\Providers\Gateway\ProtoParsers;

use Glofox\Payments\Providers\Gateway\ProtoParsers\MandateParser;
use GRPC\Payments\AccountService\Mandate;

\App::import('Test/Case', 'GlofoxTestCase');

class MandateParserTest extends \GlofoxTestCase
{

    public function test_it_handles_null_scheme_identifier_and_routing_number()
    {
        $in = new Mandate();
        $in->setID(1);
        $in->setProviderID(1);
        $in->setPSPUUID('psp-uuid');
        $in->setPSPResponse('');
        $in->setBankName('bank');
        $in->setStatus('status');
        $in->setSchema('scheme');
        $in->setAccountNumberEnding(123);
        $in->setReference('reference');
        $in->setAvailableFrom('2020-01-01');
        $in->setCreatedAt('2020-01-01');
        $in->setUpdatedAt('2020-01-01');

        $parser = new MandateParser();
        $mandate = $parser->parseFromProtoMessage($in);
        self::assertEmpty($mandate->schemeIdentifier());
        self::assertEmpty($mandate->routingNumber());
        self::assertEquals('reference', $mandate->reference());
    }

    public function test_it_handles_scheme_identifier_and_routing_number()
    {
        $in = new Mandate();
        $in->setID(1);
        $in->setProviderID(1);
        $in->setPSPUUID('psp-uuid');
        $in->setPSPResponse('');
        $in->setBankName('bank');
        $in->setStatus('status');
        $in->setSchema('scheme');
        $in->setAccountNumberEnding(123);
        $in->setReference('reference');
        $in->setAvailableFrom('2020-01-01');
        $in->setCreatedAt('2020-01-01');
        $in->setUpdatedAt('2020-01-01');

        $in->setSchemeIdentifier('scheme-identifier');
        $in->setRoutingNumber('**********');

        $parser = new MandateParser();
        $mandate = $parser->parseFromProtoMessage($in);
        self::assertEquals('scheme-identifier', $mandate->schemeIdentifier());
        self::assertEquals('**********', $mandate->routingNumber());
        self::assertEquals('reference', $mandate->reference());
    }
}