type: object
properties:
  membershipName:
    type: string
    description: The name of the membership
  planName:
    type: string
    description: The name of the plan
  startsOn:
    type: string
    description: The date the membership starts
  price:
    type: integer
    description: The price of the membership
  minPrice:
    type: integer
    description: The minimum price of the membership
  upfrontFee:
    type: integer
    description: The upfront fee of the membership
  duration:
    type: object
    properties:
      timeUnit:
        type: string
        description: The unit of time
      value:
        type: integer
        description: The value of the time unit
  contract:
    type: object
    properties:
      timeUnit:
        type: string
        description: The unit of time
      value:
        type: integer
        description: The value of the time unit
  hasEndDate:
    type: boolean
    description: Whether the membership has an end date
  planType:
    type: string
    description: The type of plan
  isRoaming:
    type: boolean
    description: Whether the membership is roaming
  isGroupMembership:
    type: boolean
    description: Whether the membership is a group membership
  maxGroupMembershipSize:
    type: integer
    description: The maximum size of the group membership
  isTrial:
    type: boolean
    description: Whether the membership is a trial
  freeTimeUnitCount:
    type: integer
    description: The free time unit count
  prorated:
    type: boolean
    description: Whether the membership is prorated
  autoRenewal:
    type: boolean
    description: Whether the membership is set to auto-renew




