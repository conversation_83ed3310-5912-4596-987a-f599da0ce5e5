<?php

declare(strict_types=1);

App::import('Test/Case', 'GlofoxTestCase');

use Glofox\Response\Models\Branches\Configuration\ColorsModel;

/**
 * Test cases
 * 1 Model contains all fields with data
 * 2 Missing fields and returns correct structure
 * 3 Additional fields and return correct structure
 * 4 Empty array fields and return correct structure.
 *
 * Class ColorsModelTest
 */
class ColorsModelTest extends GlofoxTestCase
{
    public $fixtures = [];

    /**
     * @dataProvider modelProvider
     */
    public function testModel($dataSent, $result): void
    {
        $model = new ColorsModel($dataSent);
        $this->assertEquals($model, $result);
    }

    public function modelProvider(): array
    {
        return [
            '1 Model contains all fields with data' => [
                [
                    'background' => 'FFFFFF',
                    'accent' => 'CA2728',
                    'text' => '182D97',
                ],
                new ColorsModel(
                    [
                        'background' => 'FFFFFF',
                        'accent' => 'CA2728',
                        'text' => '182D97',
                    ]
                ),
            ],
            '2 Missing fields and returns correct structure' => [
                [
                    // background missing
                    'accent' => 'CA2728',
                    'text' => '182D97',
                ],
                new ColorsModel(
                    [
                        'background' => '',
                        'accent' => 'CA2728',
                        'text' => '182D97',
                    ]
                ),
            ],
            '3 Additional fields and return correct structure' => [
                [
                    'extraColor' => '', // additional
                    'background' => 'FFFFFF',
                    'accent' => 'CA2728',
                    'text' => '182D97',
                ],
                new ColorsModel(
                    [
                        'background' => 'FFFFFF',
                        'accent' => 'CA2728',
                        'text' => '182D97',
                    ]
                ),
            ],
            '4 Empty array fields and return correct structure' => [
                [],
                new ColorsModel(
                    [
                        'background' => '',
                        'accent' => '',
                        'text' => '',
                    ]
                ),
            ],
        ];
    }
}
