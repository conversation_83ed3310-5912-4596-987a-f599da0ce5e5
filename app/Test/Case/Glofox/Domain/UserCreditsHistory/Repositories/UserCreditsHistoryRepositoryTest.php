<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\UserCreditsHistory\Repositories;

use App;
use Glofox\Domain\UserCreditsHistory\Models\UserCreditsHistory;
use Glofox\Domain\UserCreditsHistory\Models\UserCreditsHistoryAction;
use Glofox\Domain\UserCreditsHistory\Models\UserCreditsHistoryModification;
use Glofox\Domain\UserCreditsHistory\Repositories\UserCreditsHistoryRepository;
use GlofoxTestCase;

App::import('Test/Case', 'GlofoxTestCase');

class UserCreditsHistoryRepositoryTest extends GlofoxTestCase
{
    private const DEFAULT_USER_ID = '123abc';

    public $fixtures = [];

    private UserCreditsHistoryRepository $repository;
    private \UserCreditsHistory $model;

    public function setUp(): void
    {
        parent::setUp();
        $this->repository = app()->make(UserCreditsHistoryRepository::class);
        $this->model = app()->make(\UserCreditsHistory::class);
    }

    public function tearDown(): void
    {
        $this->model->deleteAll(['user_id' => self::DEFAULT_USER_ID]);
    }

    public function testCreate(): void
    {
        $exampleHistory = UserCreditsHistory::make([
            'user_id' => self::DEFAULT_USER_ID,
            'user_credit_id' => '456abc',
            'state' => ['test' => true],
            'performed_by_user_id' => '789abc',
            'action' => UserCreditsHistoryAction::UPDATED,
            'modifications' => [UserCreditsHistoryModification::MODEL_IDS, UserCreditsHistoryModification::MODEL],
        ]);

        $result = $this->repository->create($exampleHistory);
        $this->assertNotEmpty($result->id());
        $this->assertNotEmpty($result->created());

        unset($result['_id'], $result['created']);

        $this->assertEquals($exampleHistory->toArray(), $result->toArray());
    }
}
