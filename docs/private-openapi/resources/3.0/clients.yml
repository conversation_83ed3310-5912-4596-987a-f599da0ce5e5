get:
  tags:
    - Clients
  summary: ''
  operationId: get-clients
  description: This endpoint returns filtered clients
  x-internal: true
  parameters:
    - schema:
        type: string
      in: query
      description: field | -field
      name: sort-by
    - schema:
        type: integer
        minimum: 1
      in: query
      name: page
    - schema:
        type: integer
        minimum: 1
        maximum: 50
      in: query
      name: limit
    - schema:
        type: string
        maxLength: 50
        pattern: '/^_[A-Za-z]+$/u'
      in: query
      description: It must starts with an underscore (_)
      name: bundle
    - schema:
        type: string
        maxLength: 50
      in: query
      name: name
  responses:
    '200':
      description: OK
      content:
        application/json:
          schema:
            allOf:
              - $ref: "../../schemas/pagination.yml"
              - type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: "../../schemas/client.yml"
