<?php

namespace Glofox\Payments;

use Glofox\Datasource\Exceptions\InvalidMongoIdException;
use Glofox\Domain\Branches\Search\Expressions\BranchId;
use Glofox\Domain\PaymentMethods\Exceptions\PaymentMethodNotAvailableForMembers;
use Glofox\Domain\PaymentMethods\Exceptions\PaymentMethodNotFoundException;
use Glofox\Domain\PaymentMethods\Models\PaymentMethod;
use Glofox\Domain\PaymentMethods\Repositories\PaymentMethodsRepository;
use Glofox\Domain\PaymentMethods\Search\Expressions\TypeId;
use Glofox\Domain\PaymentMethods\Type;
use Glofox\Domain\PaymentProviders\Exceptions\PaymentProviderNotFoundException;
use Glofox\Domain\PaymentProviders\HandlerType;
use Glofox\Domain\PaymentProviders\Models\PaymentProvider;
use Glofox\Domain\PaymentProviders\Repositories\PaymentProvidersRepository;
use Glofox\Domain\Users\Models\User;
use Glofox\Payments\Contracts\PaymentProviderContract;
use Glofox\Payments\Providers\Gateway\Handler as GatewayHandler;
use Glofox\Payments\Providers\Noop\Handler as NoopHandler;
use Glofox\Repositories\Search\Expressions\Shared\Active;
use Glofox\Repositories\Search\Expressions\Shared\Id;

/**
 * Class PaymentsHandler.
 */
class PaymentsHandler
{
    /**
     * @var PaymentMethodsRepository
     */
    private $paymentMethodsRepository;

    /**
     * @var PaymentProvidersRepository
     */
    private $paymentProvidersRepository;

    private array $providersClassMap = [
        HandlerType::GATEWAY_HANDLER => GatewayHandler::class,
        HandlerType::NOOP_HANDLER => NoopHandler::class,
    ];

    /**
     * PaymentsHandler constructor.
     */
    public function __construct()
    {
        $this->paymentMethodsRepository = app()->make(PaymentMethodsRepository::class);
        $this->paymentProvidersRepository = app()->make(PaymentProvidersRepository::class);
    }

    /**
     * @param $branchId
     * @param $methodTypeId
     * @param bool $withUser
     *
     * @throws PaymentMethodNotAvailableForMembers
     * @throws PaymentProviderNotFoundException
     * @throws InvalidMongoIdException
     */
    public function provider($branchId, $methodTypeId, $withUser = false): PaymentProviderContract
    {
        /* @var PaymentMethod $paymentMethod */
        $this->paymentMethodsRepository->skipCallbacks()
            ->addCriteria(new BranchId($branchId))
            ->addCriteria(new TypeId($methodTypeId))
            ->addCriteria(new Active(true));

        /** @var PaymentMethod $paymentMethod */
        $paymentMethod = $this->paymentMethodsRepository->firstOrFail(function () use ($methodTypeId) {
            throw PaymentMethodNotFoundException::withType($methodTypeId);
        });

        // Complimentary is excluded from the check temporarily as the clients sends this payment method type with the amount is zero
        if ($withUser instanceof User && !$withUser->isStaff() && $paymentMethod->isStaffOnly() && $paymentMethod->typeId() != Type::COMPLIMENTARY) {
            throw PaymentMethodNotAvailableForMembers::withPaymentMethodName($paymentMethod->provider()->name());
        }

        return $this->providerByPaymentMethod($paymentMethod);
    }

    /**
     * @param PaymentMethod $paymentMethod
     * @return PaymentProviderContract
     *
     * @throws InvalidMongoIdException
     * @throws PaymentProviderNotFoundException
     */
    public function providerByPaymentMethod(PaymentMethod $paymentMethod): PaymentProviderContract
    {
        /* @var PaymentProvider $paymentProvider */
        $this->paymentProvidersRepository->skipCallbacks()
            ->addCriteria(new Id($paymentMethod->provider()->id()));

        $paymentProvider = $this->paymentProvidersRepository->firstOrFail(function () use ($paymentMethod) {
            throw PaymentProviderNotFoundException::withId($paymentMethod->provider()->id());
        });

        $handlerClass = $this->providersClassMap[$paymentProvider->handlerId()] ?? null;

        if (empty($handlerClass)) {
            throw PaymentProviderNotFoundException::withId($paymentMethod->provider()->id());
        }

        return app()->makeWith($handlerClass, [
            'paymentMethod' => $paymentMethod,
            'paymentProvider' => $paymentProvider,
        ]);
    }

    /**
     * @param PaymentProvider $paymentProvider
     * @param PaymentMethod $paymentMethod
     * @return PaymentProviderContract
     *
     * @throws PaymentProviderNotFoundException
     */
    public function buildPaymentHandlerContract(PaymentProvider $paymentProvider, PaymentMethod $paymentMethod): PaymentProviderContract
    {
        $handlerClass = $this->providersClassMap[$paymentProvider->handlerId()] ?? null;

        if (empty($handlerClass)) {
            throw PaymentProviderNotFoundException::withId($paymentProvider->id());
        }

        return app()->makeWith($handlerClass, [
            'paymentMethod' => $paymentMethod,
            'paymentProvider' => $paymentProvider,
        ]);
    }
}
