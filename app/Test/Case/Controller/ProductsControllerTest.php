<?php

use CakeTestCases\Glofox\Domain\Users\Traits\AuthenticateUsersTrait;
use Glofox\Domain\Activities\Repositories\ActivitiesRepository;
use Glofox\Domain\Activities\Search\Expressions\EventContextChargeId;
use Glofox\Domain\Authentication\Auth;
use Glofox\Domain\Store\Products\Repositories\ProductsRepository;
use Glofox\Domain\Store\Sales\Repositories\SalesRepository;
use Glofox\Domain\Users\Repositories\UsersRepository;
use Glofox\Domain\Wallets\Services\Balance\SendEmailBasedOnBalanceService;
use Glofox\Domain\Wallets\Services\Responses\BalanceResponse;
use Glofox\Domain\Wallets\Services\Responses\SettingsResponse;
use Glofox\Domain\Wallets\Services\WalletsClient;
use Glofox\Payments\Contracts\PaymentProviderContract;
use Glofox\Payments\Entities\Invoice\Contracts\InvoiceHandlerContract;
use Glofox\Payments\Entities\Invoice\Models\InvoiceEntity;
use Glofox\Payments\Entities\Invoice\Models\InvoiceEntityAutoCollectDetails;
use Glofox\Payments\Entities\Invoice\Models\InvoiceEntityLineItem;
use Glofox\Payments\Entities\Invoice\Models\InvoiceEntityLineItems;
use Glofox\Repositories\Search\Expressions\Shared\Id;
use Glofox\Request;
use StringTemplate\Engine;

App::import('Test/Case', 'GlofoxControllerTestCase');
App::uses('ProductsController', 'Controller');
App::uses('Component', 'Controller');

class ProductsControllerTest extends GlofoxControllerTestCase
{
    use AuthenticateUsersTrait;

    /** @var array */
    public $fixtures = [
        'app.branch',
        'app.user',
        'app.product',
        'app.stripe_charge',
        'app.payment_provider',
        'app.payment_method',
        'app.store_sale',
    ];

    /** @var string */
    public static $adminToken;

    /** @var string|null */
    public static $memberToken;

    /** @var Engine */
    protected $stringTemplate;

    /** @var StripeCharge */
    protected $stripeChargeCakeModel;

    /** @var ProductsRepository */
    protected $productsRepository;

    /** @var ActivitiesRepository */
    protected $activitiesRepository;

    protected SalesRepository $salesRepository;
    protected UsersRepository $usersRepository;

    /**
     * Authenticate as User, calls authenticate with password with new api.
     */
    public function setUp()
    {
        parent::setUp();

        $this->stringTemplate = new Engine('{{', '}}');
        $this->stripeChargeCakeModel = app(StripeCharge::class);
        $this->productsRepository = app(ProductsRepository::class);
        $this->activitiesRepository = app(ActivitiesRepository::class);
        $this->salesRepository = app(SalesRepository::class);
        $this->usersRepository = app(UsersRepository::class);
    }

    public function tearDown()
    {
        parent::tearDown();

        \Mockery::close();
    }

    public function test_it_lists_products_in_api_v2(): void
    {
        $productId = '5b44ae18b13d95b5f633fd06';
        $userId = '5ae1a75a8d7de985e383494d';
        $branchId = '49a7011a05c677b9a916612a';

        $user = $this->fetchUser($userId);
        $this->loginAsUser($user);

        $options = [
            'method' => 'GET',
        ];

        $url = \sprintf('/2.0/branches/' . $branchId . '/products');
        $result = $this->testAction($url, $options);

        $result = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        self::assertContains(
            'https://cdn.glofox.com/test/glofox/branches/' . $branchId . '/products/' . $productId .'/default.png?v=',
            $result['data'][0]['image_url']
        );
    }

    public function test_cannot_purchase_item_out_of_stock(): void
    {
        $productId = '5b44ae18b13d95b5f633fd06';
        $userId = '5ae1a75a8d7de985e383494d';
        $presentationId = 1_506_964_157_657;
        $quantity = 1;

        $user = $this->fetchUser($userId);
        $this->loginAsUser($user);

        /** @var \Glofox\Domain\Store\Products\Models\Product $product */
        $productPreviousState = $this->productsRepository
            ->addCriteria(new Id($productId))
            ->first();

        // Setting presentation Out of Stock
        $product = clone $productPreviousState;
        $product->setPresentationOutOfStock($presentationId);
        $this->productsRepository->save($product);

        $data = [
            'branchId' => '49a7011a05c677b9a916612a',
            'productId' => $productId,
            'presentationId' => $presentationId,
        ];

        $options = [
            'method' => 'POST',
            'data' => [
                'quantity' => $quantity,
                'by' => $userId,
            ],
        ];

        $url = '/2.0/branches/{{branchId}}/products/{{productId}}/presentations/{{presentationId}}/purchase';
        $url = $this->stringTemplate->render($url, $data);
        $result = $this->testAction($url, $options);

        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        self::assertFalse($result->success);
        self::assertEquals('INSUFFICIENT_STOCK_FOR_PRODUCT', $result->message);

        // Teardown
        $this->productsRepository->save($productPreviousState);
    }

    public function test_cannot_purchase_quantity_greater_than_stock(): void
    {
        $productId = '5b44ae18b13d95b5f633fd06';
        $userId = '5ae1a75a8d7de985e383494d';
        $presentationId = 1_506_964_157_657;
        $quantity = 2;

        $user = $this->fetchUser($userId);
        $this->loginAsUser($user);

        /** @var \Glofox\Domain\Store\Products\Models\Product $product */
        $productPreviousState = $this->productsRepository
            ->addCriteria(new Id($productId))
            ->first();

        // Setting presentation Out of Stock
        $product = clone $productPreviousState;
        $product->setPresentationStock($presentationId, 1);
        $this->productsRepository->save($product);

        $data = [
            'branchId' => '49a7011a05c677b9a916612a',
            'productId' => $productId,
            'presentationId' => $presentationId,
        ];

        $options = [
            'method' => 'POST',
            'data' => [
                'quantity' => $quantity,
                'by' => $userId,
            ],
        ];

        $url = '/2.0/branches/{{branchId}}/products/{{productId}}/presentations/{{presentationId}}/purchase';
        $url = $this->stringTemplate->render($url, $data);
        $result = $this->testAction($url, $options);

        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        self::assertFalse($result->success);
        self::assertEquals('INSUFFICIENT_STOCK_FOR_PRODUCT', $result->message);

        // Teardown
        $this->productsRepository->save($productPreviousState);
    }

    public function test_purchase_item_as_admin(): void
    {
        $walletClient = \Mockery::mock(WalletsClient::class);
        $walletClient
            ->shouldReceive('getWalletSettings')
            ->andReturn(SettingsResponse::fromArray(collect()));

        app()->instance(WalletsClient::class, $walletClient);

        $this->authenticateAsAdmin();

        $productId = '5b44ae18b13d95b5f633fd06';
        $presentationId = 1_506_964_157_657;

        /** @var \Glofox\Domain\Store\Products\Models\Product $product */
        $productPreviousState = $this->productsRepository
            ->addCriteria(new Id($productId))
            ->first();

        $data = [
            'productId' => $productId,
            'presentationId' => $presentationId,
            'userId' => '5ae1a75a8d7de985e383494d',
            'price' => 10,
            'paymentMethod' => 'cash',
            'quantity' => 1,
        ];

        $options = [
            'method' => 'post',
        ];

        $url = '/products/buyAdmin/{{productId}}/{{presentationId}}/{{userId}}/{{price}}/{{paymentMethod}}/{{quantity}}';
        $url = $this->stringTemplate->render($url, $data);
        $result = $this->testAction($url, $options);
        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        self::assertTrue($result->success);
        self::assertNotEmpty($result->purchase_code);

        // Making sure the payment was stored
        $charge = $this->stripeChargeCakeModel->find('first', [
            'conditions' => [
                'metadata.user_id' => $data['userId'],
                'metadata.purchase_code' => $result->purchase_code,
            ],
        ]);

        self::assertEquals($charge['StripeCharge']['amount'], 10);
        self::assertEquals($charge['StripeCharge']['metadata']['purchase_code'], $result->purchase_code);
        self::assertEquals($charge['StripeCharge']['metadata']['payment_method'], 'cash');

        // Making sure the activity was stored with charge id
        $activity = $this->activitiesRepository->addCriteria(new EventContextChargeId($result->charge_id))->first();

        self::assertNotEmpty($activity);

        /** @var \Glofox\Domain\Store\Products\Models\Product $product */
        $product = $this->productsRepository
            ->addCriteria(new Id($productId))
            ->first();

        $presentation = $product->presentation($presentationId);

        self::assertEquals(9, $presentation->stock());

        // Teardown
        $this->productsRepository->save($productPreviousState);
        app()->forgetInstance(WalletsClient::class);
    }

    public function test_purchase_item_as_member_using_legacy_endpoint(): void
    {
        $walletClient = \Mockery::mock(WalletsClient::class);
        $walletClient
            ->shouldReceive('getWalletSettings')
            ->andReturn(SettingsResponse::fromArray(collect()));

        app()->instance(WalletsClient::class, $walletClient);

        $userData = [
            'namespace' => 'glofox',
            'branch_id' => '49a7011a05c677b9a916612a',
            'email' => '<EMAIL>',
            'login' => '<EMAIL>',
            'password' => '123',
            '_id' => '5ae1a75a8d7de985e383494d',
        ];

        $user = $this->fetchUser($userData['_id']);
        $this->loginAsUser($user);

        $productId = '5b44ae18b13d95b5f633fd06';
        $presentationId = 1_506_964_157_657;

        /** @var \Glofox\Domain\Store\Products\Models\Product $product */
        $productPreviousState = $this->productsRepository
            ->addCriteria(new Id($productId))
            ->first();

        $data = [
            'productId' => $productId,
            'presentationId' => $presentationId,
            'userId' => '5ae1a75a8d7de985e383494d',
        ];

        $options = [
            'method' => 'post',
            'data' => ['password' => '123'],
        ];

        $url = '/products/buy/{{productId}}/{{presentationId}}/{{userId}}';
        $url = $this->stringTemplate->render($url, $data);
        $result = $this->testAction($url, $options);
        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        self::assertTrue($result->success);
        self::assertNotEmpty($result->purchase_code);

        /** @var \Glofox\Domain\Store\Products\Models\Product $product */
        $product = $this->productsRepository
            ->addCriteria(new Id($productId))
            ->first();

        $presentation = $product->presentation($presentationId);

        self::assertEquals(9, $presentation->stock());

        // Teardown
        $this->productsRepository->save($productPreviousState);
        app()->forgetInstance(WalletsClient::class);
    }

    public function test_purchase_decreases_stock(): void
    {
        $walletClient = \Mockery::mock(WalletsClient::class);
        $walletClient
            ->shouldReceive('getWalletSettings')
            ->andReturn(SettingsResponse::fromArray(collect()));

        app()->instance(WalletsClient::class, $walletClient);

        $productId = '5b44ae18b13d95b5f633fd06';
        $userId = '5ae1a75a8d7de985e383494d';
        $presentationId = 1_506_964_157_657;
        $quantity = 3;

        $user = $this->fetchUser($userId);
        $this->loginAsUser($user);

        /** @var \Glofox\Domain\Store\Products\Models\Product $product */
        $productPreviousState = $this->productsRepository
            ->addCriteria(new Id($productId))
            ->first();

        $data = [
            'branchId' => '49a7011a05c677b9a916612a',
            'productId' => $productId,
            'presentationId' => $presentationId,
        ];

        $options = [
            'method' => 'POST',
            'data' => [
                'quantity' => $quantity,
                'by' => $userId,
            ],
        ];

        $url = '/2.0/branches/{{branchId}}/products/{{productId}}/presentations/{{presentationId}}/purchase';
        $url = $this->stringTemplate->render($url, $data);
        $result = $this->testAction($url, $options);

        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        self::assertTrue($result->success);

        // ----------------------------------

        /** @var \Glofox\Domain\Store\Products\Models\Product $product */
        $product = $this->productsRepository
            ->addCriteria(new Id($productId))
            ->first();

        $presentation = $product->presentation($presentationId);

        self::assertEquals(7, $presentation->stock());

        // Teardown
        $this->productsRepository->save($productPreviousState);
        app()->forgetInstance(WalletsClient::class);
    }

    public function test_purchases_by_member_cannot_have_their_price_changed(): void
    {
        $userData = [
            'namespace' => 'glofox',
            'branch_id' => '49a7011a05c677b9a916612a',
            'email' => '<EMAIL>',
            'login' => '<EMAIL>',
            'password' => '123',
            '_id' => '5ae1a75a8d7de985e383494d',
        ];

        $user = $this->fetchUser($userData['_id']);
        $this->loginAsUser($user);

        $data = [
            'productId' => '5b44ae18b13d95b5f633fd06',
            'presentationId' => 1_506_964_157_657,
            'userId' => '5ae1a75a8d7de985e383494d',
            'price' => 0,
            'paymentMethod' => 'cash',
        ];

        $options = [
            'method' => 'post',
            'data' => ['password' => '123'],
        ];

        $url = '/products/buy/{{productId}}/{{presentationId}}/{{userId}}/{{price}}/{{paymentMethod}}';
        $url = $this->stringTemplate->render($url, $data);
        $result = $this->testAction($url, $options);
        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        self::assertFalse($result->success);
        self::assertEquals($result->message, 'UNAUTHORIZED_TO_CHANGE_PRICE');
    }

    public function test_adds_sold_by_id_field_if_present(): void
    {
        $walletClient = \Mockery::mock(WalletsClient::class);
        $walletClient
            ->shouldReceive('getWalletSettings')
            ->andReturn(SettingsResponse::fromArray(collect()));

        app()->instance(WalletsClient::class, $walletClient);

        $adminUser = $this->fetchUser('59a7011a05c677bda916616c');
        $this->loginAsUser($adminUser);
        $product = '5b44ae18b13d95b5f633fd06';
        $presentation = 1_506_964_157_657;

        $url = sprintf(
            '/2.0/branches/%s/products/%s/presentations/%d/purchase',
            $adminUser->currentBranchId(),
            $product,
            $presentation
        );

        $result = $this->testAction($url, [
            'method' => 'POST',
            'data' => [
                'by' => $adminUser->id(),
                'quantity' => 1,
                'payment_method' => 'credit_card',
                'sold_by_user_id' => 'a9a5521a05c687bda917785c',
            ],
        ]);

        $data = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        $this->assertTrue($data['success']);
        $this->assertNotEmpty($data['purchase_code']);

        $cakeStripeCharge = app()->make(StripeCharge::class);
        $charge = $cakeStripeCharge->findPurchaseByCode($data['purchase_code']);

        $this->assertSame('a9a5521a05c687bda917785c', $charge['StripeCharge']['sold_by_user_id']);
        app()->forgetInstance(WalletsClient::class);
    }

    /** @dataProvider provider_validate_purchase_required_fields */
    public function test_validate_purchase_required_fields(array $input, array $expected): void
    {
        $this->authenticateAsAdmin();

        $data = [
            'branchId' => '49a7011a05c677b9a916612a',
            'productId' => '5b44ae18b13d95b5f633fd06',
            'presentationId' => 1_506_964_157_657,
        ];

        $options = [
            'method' => 'post',
            'data' => $input,
        ];

        $url = '/2.0/branches/{{branchId}}/products/{{productId}}/presentations/{{presentationId}}/purchase';
        $url = $this->stringTemplate->render($url, $data);
        $result = $this->testAction($url, $options);

        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        self::assertFalse($result->success);
        self::assertEquals($result->message_code, $expected['message_code']);
    }

    public function provider_validate_purchase_required_fields(): array
    {
        return [
            'By field missing' => [
                [
                    'quantity' => 1,
                ],
                [
                    'message_code' => 'The by field is required.',
                ],
            ],
            'Quantity field missing' => [
                [
                    'by' => '123123',
                ],
                [
                    'message_code' => 'The quantity field is required.',
                ],
            ],
        ];
    }

    public function test_not_breaking_charge_user_for_product_flow_by_adding_send_email_based_on_balance_service_using_wallet(
    ) {
        $walletClient = \Mockery::mock(WalletsClient::class);
        $walletClient
            ->shouldReceive('getWalletSettings')
            ->andReturn(SettingsResponse::fromArray(collect()))
            ->shouldReceive('getUserBalance')
            ->andReturn(
                BalanceResponse::fromArray(collect([
                    'balance' => 20,
                ]))
            );

        $sendEmailBasedOnBalanceService = \Mockery::mock(SendEmailBasedOnBalanceService::class);
        $sendEmailBasedOnBalanceService
            ->shouldReceive('execute')
            ->once();

        app()->instance(WalletsClient::class, $walletClient);
        app()->instance(SendEmailBasedOnBalanceService::class, $sendEmailBasedOnBalanceService);

        $this->authenticateAsAdmin();

        $data = [
            'productId' => '5b44ae18b13d95b5f633fd06',
            'presentationId' => 1_506_964_157_657,
            'userId' => '5ae1a75a8d7de985e383494d',
            'price' => 10,
            'paymentMethod' => 'wallet',
            'quantity' => 1,
        ];

        $options = [
            'method' => 'post',
        ];

        $url = '/products/buyAdmin/{{productId}}/{{presentationId}}/{{userId}}/{{price}}/{{paymentMethod}}/{{quantity}}';
        $url = $this->stringTemplate->render($url, $data);
        $this->testAction($url, $options);

        app()->forgetInstance(WalletsClient::class);
        app()->forgetInstance(SendEmailBasedOnBalanceService::class);
    }

    public function testStoreSalesModelIsFilledAfterPurchase()
    {
        $walletClient = \Mockery::mock(WalletsClient::class);
        $walletClient
            ->shouldReceive('getWalletSettings')
            ->andReturn(SettingsResponse::fromArray(collect()));

        app()->instance(WalletsClient::class, $walletClient);

        $this->authenticateAsAdmin();

        $productId = '5b44ae18b13d95b5f633fd06';
        $presentationId = 1_506_964_157_657;
        $userId = '5ae1a75a8d7de985e383494d';
        $price = 10.34;
        $quantity = 1;

        $data = [
            'productId' => $productId,
            'presentationId' => $presentationId,
            'userId' => $userId,
            'price' => $price,
            'paymentMethod' => 'cash',
            'quantity' => $quantity,
        ];

        $url = '/products/buyAdmin/{{productId}}/{{presentationId}}/{{userId}}/{{price}}/{{paymentMethod}}/{{quantity}}';
        $url = $this->stringTemplate->render($url, $data);
        $result = $this->testAction($url, ['method' => 'post']);
        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        self::assertTrue($result->success);
        self::assertNotEmpty($result->purchase_code);

        $sale = $this->salesRepository->findByPurchaseCode($result->purchase_code);
        $product = $this->productsRepository->findById($productId);
        $presentation = $product->presentation($presentationId);
        $branch = $product->branch();
        $user = $this->usersRepository->getById($userId);

        self::assertEquals($productId, $sale->getProductId());
        self::assertEquals($product->name(), $sale->getProductName());

        self::assertEquals($presentationId, $sale->getPresentationId());
        self::assertEquals($presentation->description(), $sale->getPresentationName());

        self::assertEquals($quantity, $sale->getQuantity());

        self::assertEquals($branch->id(), $sale->branchId());
        self::assertEquals($branch->namespace(), $sale->namespace());
        self::assertEquals($userId, $sale->userId());
        self::assertEquals($user->fullName(), $sale->getUserName());

        self::assertNull($sale->getCollectedDate());

        app()->forgetInstance(WalletsClient::class);
    }

    /**
     * @dataProvider memberUnauthorizedPurchaseDataProvider
     * @param array $data
     * @return void
     */
    public function testMemberPurchaseUnauthorized(array $data, string $messageCode): void
    {
        $userId = '5ae1a75a8d7de985e383494d';
        $user = $this->fetchUser($userId);
        $this->loginAsUser($user);

        $data = array_merge([
            'branchId' => '49a7011a05c677b9a916612a',
            'by' => $userId,
        ], $data);

        $url = '/2.0/branches/{{branchId}}/products/{{productId}}/presentations/{{presentationId}}/purchase';
        $url = $this->stringTemplate->render($url, $data);
        $result = $this->testAction($url, ['method' => 'POST', 'data' => $data]);

        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        self::assertFalse($result->success);
        self::assertEquals($messageCode, $result->message_code);
    }

    public function memberUnauthorizedPurchaseDataProvider(): array
    {
        return [
            'Member unauthorized to change total price - different price' => [
                'data' => [
                    'productId' => '5b44ae18b13d95b5f633fd06',
                    'presentationId' => 1_506_964_157_657,
                    'quantity' => 1,
                    'total_price' => 20
                ],
                'message_code' => 'UNAUTHORIZED_TO_CHANGE_PRICE'
            ],
            'Member unauthorized to change total price - zero price' => [
                'data' => [
                    'productId' => '5b44ae18b13d95b5f633fd06',
                    'presentationId' => 1_506_964_157_657,
                    'quantity' => 1,
                    'total_price' => 0
                ],
                'message_code' => 'UNAUTHORIZED_TO_CHANGE_PRICE'
            ],
            'Member unauthorized to change payment method' => [
                'data' => [
                    'productId' => '5b44ae18b13d95b5f633fd06',
                    'presentationId' => 1_506_964_157_657,
                    'quantity' => 1,
                    'payment_method' => 'wallet'
                ],
                'message_code' => 'UNAUTHORIZED_TO_CHANGE_PAYMENT_METHOD'
            ],
            'Member unauthorized to change payment method 2' => [
                'data' => [
                    'productId' => '5b44ae18b13d95b5f633fd06',
                    'presentationId' => 1_506_964_157_657,
                    'quantity' => 1,
                    'total_price' => 10,
                    'payment_method' => 'complimentary'
                ],
                'message_code' => 'UNAUTHORIZED_TO_CHANGE_PAYMENT_METHOD'
            ],
            'Member unauthorized to change payment method 3' => [
                'data' => [
                    'productId' => '5b44ae18b13d95b5f633fd06',
                    'presentationId' => 1_506_964_157_657,
                    'quantity' => 1,
                    'total_price' => 10,
                    'payment_method' => 'complimentary'
                ],
                'message_code' => 'UNAUTHORIZED_TO_CHANGE_PAYMENT_METHOD'
            ],
        ];
    }

    /**
     * @dataProvider memberCanPurchaseComplimentaryProductDataProvider
     * @param array $data
     * @return void
     */
    public function testMemberCanPurchaseComplimentaryProduct(array $data): void
    {
        $walletClient = \Mockery::mock(WalletsClient::class);
        $walletClient
            ->shouldReceive('getWalletSettings')
            ->andReturn(SettingsResponse::fromArray(collect()));

        app()->instance(WalletsClient::class, $walletClient);

        $userId = '5ae1a75a8d7de985e383494d';
        $user = $this->fetchUser($userId);
        $this->loginAsUser($user);

        $data = array_merge([
            'branchId' => '49a7011a05c677b9a916612a',
            'by' => $userId,
            'productId' => '62852a22a9a2bb677917b383',
            'presentationId' => 1_506_964_157_790,
            'quantity' => 1,
            'payment_method' => 'complimentary'
        ], $data);

        $url = '/2.0/branches/{{branchId}}/products/{{productId}}/presentations/{{presentationId}}/purchase';
        $url = $this->stringTemplate->render($url, $data);
        $result = $this->testAction($url, ['method' => 'POST', 'data' => $data]);

        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        self::assertTrue($result->success);
        self::assertEquals('You have successfully bought the Product', $result->message);

        app()->forgetInstance(WalletsClient::class);
    }

    public function memberCanPurchaseComplimentaryProductDataProvider(): array
    {
        return [
            'Zero total_price' => ['data' => ['total_price' => 0]],
            'Total price is not provided' => ['data' => []],
        ];
    }

    /**
     * @dataProvider AdminPurchaseValidateIncomingRequestDataProvider
     * @param array $data
     * @param string $message
     * @return void
     */
    public function testAdminPurchaseValidateIncomingRequest(array $data, string $message): void
    {
        $this->authenticateAsAdmin();

        $data = array_merge([
            'branchId' => '49a7011a05c677b9a916612a',
            'productId' => '5b44ae18b13d95b5f633fd06',
            'presentationId' => 1_506_964_157_657
        ], $data);

        $url = '/2.0/branches/{{branchId}}/products/{{productId}}/presentations/{{presentationId}}/purchase';
        $url = $this->stringTemplate->render($url, $data);
        $result = $this->testAction($url, ['method' => 'POST', 'data' => $data]);

        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        self::assertFalse($result->success);
        self::assertEquals($message, $result->message);
    }

    public function AdminPurchaseValidateIncomingRequestDataProvider(): array
    {
        return [
            'All data are invalid' => [
                'data' => [
                    'by' => 0,
                    'quantity' => 0,
                    'payment_method' => 'invalid_method',
                    'total_price' => -1,
                ],
                'message' => 'The quantity must be at least 1., The by must be a string., The selected payment method is invalid., The total price must be at least 0.',
            ],
            'Invalid data types' => [
                'data' => [
                    'by' => 12_345_854,
                    'quantity' => 'quantity',
                    'total_price' => '20price',
                ],
                'message' => 'The quantity must be an integer., The by must be a string., The total price must be a number.',
            ],
        ];
    }

    /**
     * @dataProvider AdminBuyValidateIncomingRequestDataProvider
     * @param array $data
     * @param string $message
     * @return void
     */
    public function testAdminBuyValidateIncomingRequest(array $data, string $message): void
    {
        $this->authenticateAsAdmin();

        $data = array_merge([
            'userId' => '5ae1a75a8d7de985e383494d',
            'productId' => '5b44ae18b13d95b5f633fd06',
            'presentationId' => 1_506_964_157_657
        ], $data);

        $url = '/products/buyAdmin/{{productId}}/{{presentationId}}/{{userId}}/{{price}}/{{paymentMethod}}/{{quantity}}';
        $url = $this->stringTemplate->render($url, $data);
        $result = $this->testAction($url, ['method' => 'POST', 'data' => $data]);

        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        self::assertFalse($result->success);
        self::assertEquals($message, $result->message);
    }

    public function AdminBuyValidateIncomingRequestDataProvider(): array
    {
        return [
            'Quantity is empty' => [
                'data' => [
                    'quantity' => 0,
                    'payment_method' => 'credit_card',
                    'price' => 10,
                ],
                'message' => 'The quantity must be at least 1.',
            ],
            'Quantity is negative' => [
                'data' => [
                    'quantity' => -1,
                    'payment_method' => 'credit_card',
                    'price' => 10,
                ],
                'message' => 'The quantity must be at least 1.',
            ],
        ];
    }

    /**
     * @dataProvider memberCanBuyComplimentaryProductDataProvider
     * @param string $paymentMethod
     * @param float|null $price
     * @return void
     */
    public function testMemberCanBuyComplimentaryProduct(string $paymentMethod, ?float $price): void
    {
        $walletClient = \Mockery::mock(WalletsClient::class);
        $walletClient
            ->shouldReceive('getWalletSettings')
            ->andReturn(SettingsResponse::fromArray(collect()));

        app()->instance(WalletsClient::class, $walletClient);

        $userId = '5ae1a75a8d7de985e383494d';
        $user = $this->fetchUser($userId);
        $this->loginAsUser($user);

        $data = [
            'productId' => '62852a22a9a2bb677917b383',
            'presentationId' => 1_506_964_157_790,
            'userId' => $userId,
            'price' => $price,
            'paymentMethod' => $paymentMethod,
        ];

        $url = '/products/buy/{{productId}}/{{presentationId}}/{{userId}}/{{price}}/{{paymentMethod}}';
        $url = $this->stringTemplate->render($url, $data);
        $result = $this->testAction($url, ['method' => 'POST']);
        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        self::assertTrue($result->success);
        self::assertEquals($result->message, 'CUSTOM_CHARGE_SUCCESS');

        app()->forgetInstance(WalletsClient::class);
    }

    public function memberCanBuyComplimentaryProductDataProvider(): array
    {
        return [
            'Complimentary by credit card' => ['paymentMethod' => 'credit_card', 'price' => 0.0],
            'Complimentary by complimentary' => ['paymentMethod' => 'complimentary', 'price' => 0]
        ];
    }

    public function test_not_breaking_charge_user_for_product_flow_by_adding_send_email_based_on_balance_service_not_using_wallet(
    ) {
        $walletClient = \Mockery::mock(WalletsClient::class);
        $walletClient
            ->shouldReceive('getWalletSettings')
            ->andReturn(SettingsResponse::fromArray(collect()))
            ->shouldReceive('getUserBalance')
            ->andReturn(
                BalanceResponse::fromArray(collect([
                    'balance' => 20,
                ]))
            );

        $sendEmailBasedOnBalanceService = \Mockery::mock(SendEmailBasedOnBalanceService::class);
        $sendEmailBasedOnBalanceService
            ->shouldNotReceive('execute');

        app()->instance(WalletsClient::class, $walletClient);
        app()->instance(SendEmailBasedOnBalanceService::class, $sendEmailBasedOnBalanceService);

        $this->authenticateAsAdmin();

        $data = [
            'productId' => '5b44ae18b13d95b5f633fd06',
            'presentationId' => 1_506_964_157_657,
            'userId' => '5ae1a75a8d7de985e383494d',
            'price' => 10,
            'paymentMethod' => 'cash',
            'quantity' => 1,
        ];

        $options = [
            'method' => 'post',
        ];

        $url = '/products/buyAdmin/{{productId}}/{{presentationId}}/{{userId}}/{{price}}/{{paymentMethod}}/{{quantity}}';
        $url = $this->stringTemplate->render($url, $data);
        $result = $this->testAction($url, $options);

        app()->forgetInstance(WalletsClient::class);
        app()->forgetInstance(SendEmailBasedOnBalanceService::class);
    }

    public function test_discounts_are_passed()
    {
        $walletClient = \Mockery::mock(WalletsClient::class);
        $walletClient
            ->shouldReceive('getWalletSettings')
            ->andReturn(SettingsResponse::fromArray(collect()))
            ->shouldReceive('getUserBalance')
            ->andReturn(
                BalanceResponse::fromArray(collect([
                    'balance' => 20,
                ]))
            );

        $sendEmailBasedOnBalanceService = \Mockery::mock(SendEmailBasedOnBalanceService::class);
        $sendEmailBasedOnBalanceService
            ->shouldReceive('execute');

        app()->instance(WalletsClient::class, $walletClient);
        app()->instance(SendEmailBasedOnBalanceService::class, $sendEmailBasedOnBalanceService);

        $this->authenticateAsAdmin();

        $auth = new Auth();
        $paymentMethod = \Mockery::mock(PaymentProviderContract::class)
            ->allows([
                'paymentMethod' => \Glofox\Domain\PaymentMethods\Models\PaymentMethod::make([
                    '_id' => '5d68269a8d8540851fe60663',
                    'type_id' => 'WALLET',
                    'provider' => [
                        'charge_percentage' => 0,
                        'fixed_charge' => 2,
                        'account_id' => '123',
                    ],
                ]),
                'paymentProvider' => new \Glofox\Domain\PaymentProviders\Models\PaymentProvider(),
            ]);

        $mockedInvoiceHandler = Mockery::mock(InvoiceHandlerContract::class)
            ->shouldReceive('create')
            ->withArgs(function (
                InvoiceEntity $invoice,
                InvoiceEntityLineItems $lineItems,
                ?InvoiceEntityAutoCollectDetails $autoCollectDetails
            ) {
                /** @var InvoiceEntityLineItem $lineItem */
                $lineItem = $lineItems->getIterator()->offsetGet(0);

                $this->assertEquals('discount_1', $lineItem->getDiscountIDs()[0]);

                return true;
            })
            ->getMock();

        $paymentMethod
            ->shouldReceive('invoices')
            ->andReturn($mockedInvoiceHandler);

        $auth->paymentMethods[Glofox\Domain\PaymentMethods\Type::CARD] = $paymentMethod;
        app()->instance(Auth::class, $auth);
        $data = [
            'productId' => '5b44ae18b13d95b5f633fd06',
            'presentationId' => 1_506_964_157_657,
            'userId' => '5ae1a75a8d7de985e383494d',
            'price' => 15,
            'paymentMethod' => 'WALLET',
            'quantity' => 1,
        ];
        $payload = [
            'amount' => 5,
            'payment_method' => 'card',
            'description' => null,
            'discounts' => ['discount_1']
        ];

        $options = [
            'method' => 'post',
            'data' => $payload,
        ];

        $url = '/products/buyAdmin/{{productId}}/{{presentationId}}/{{userId}}/{{price}}/{{paymentMethod}}/{{quantity}}';
        $url = $this->stringTemplate->render($url, $data);
        $this->testAction($url, $options);

        app()->forgetInstance(Auth::class);
        app()->forgetInstance(WalletsClient::class);
        app()->forgetInstance(SendEmailBasedOnBalanceService::class);
    }

    public function testCollectStoreSale(): void
    {
        $this->authenticateAsAdmin();
        $storeSaleId = '6282544c2f77ae2e8b08a5fd';

        $url = sprintf(
            '/2.1/branches/49a7011a05c677b9a916612a/store-sales/%s/collect',
            $storeSaleId
        );

        $options = [
            'method' => Request::METHOD_PATCH,
        ];

        $this->testAction($url, $options);

        $this->assertEquals(204, $this->response->statusCode());

        $collectedProduct = app()->make(SalesRepository::class)->findById($storeSaleId);

        $this->assertNotEmpty($collectedProduct->getCollectedDate());
    }

    public function testItReturnErrorWhenStoreSaleIsAlreadyCollected(): void
    {
        $this->authenticateAsAdmin();

        $url = '/2.1/branches/49a7011a05c677b9a916612a/store-sales/6282588390a7db1420f9380c/collect';

        $options = [
            'method' => Request::METHOD_PATCH,
        ];

        $this->testAction($url, $options);

        $this->assertEquals(409, $this->response->statusCode());
    }

    public function testViewByIdShouldCastTypesProperly(): void
    {
        $this->authenticateAsAdmin();
        $productId = '63e67b9616c7a30aebe7896d';

        $result = $this->testAction(
            sprintf('/products/view/%s/', $productId),
            ['method' => Request::METHOD_GET]
        );
        $result = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        $expected = [
            [
                'Product' => [
                    '_id' => '63e67b9616c7a30aebe7896d',
                    'branch_id' => '49a7011a05c677b9a916612a',
                    'namespace' => 'glofox',
                    'active' => true,
                    'categories' => ['62a9b7fd18d1d4d010e1110a', '62a9b7fd18d1d4d010e11108'],
                    'name' => 'Sweet Water Bottle',
                    'description' => 'Sweet Water',
                    'private' => false,
                    'taxIds' => [],
                    'taxes' => null,
                    'image' => null,
                    'presentations' => [
                        [
                            'id' => 1_506_964_157_790,
                            'retail_price' => 15.67,
                            'stock' => 10,
                            'wholesale_price' => 10,
                            'description' => 'Sweet Water makes you feel good 1',
                        ],
                        [
                            'id' => 1_506_964_157_791,
                            'retail_price' => 21,
                            'stock' => 100,
                            'wholesale_price' => 21,
                            'description' => 'Sweet Water makes you feel good 2',
                        ],
                    ],
                ]
            ],
        ];

        $this->assertTrue($expected === $result);
    }

    public function testViewByCorruptedId(): void
    {
        $this->authenticateAsAdmin();
        $productId = 'a30aebe7896d';

        $result = $this->testAction(
            sprintf('/products/view/%s/', $productId),
            ['method' => Request::METHOD_GET]
        );
        $result = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        $this->assertEquals([], $result);
    }

    public function testViewItDoesNotFetchDataForOtherBranch(): void
    {
        // Authenticate as Admin from other branch.
        $this->authenticateUser('59a7011a05c677bda512212a');

        $result = $this->testAction(
            '/products/view/',
            ['method' => Request::METHOD_GET]
        );
        $result = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        $this->assertEquals([], $result);
    }

    public function testViewItDoesNotFetchDataForGuest(): void
    {
        $this->authenticateAsGuest();

        $result = $this->testAction(
            '/products/view/',
            ['method' => Request::METHOD_GET]
        );
        $result = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        $this->assertEquals([], $result);
    }

    /**
     * @param mixed $active
     * @dataProvider testViewFetchesAllInactiveProductsDataProvider
     * @return void
     */
    public function testViewFetchesAllInactiveProducts($active): void
    {
        $this->authenticateAsAdmin();

        $result = $this->testAction(
            "/products/view/?active=$active",
            ['method' => Request::METHOD_GET]
        );
        $result = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        $expected = [
            [
                'Product' => [
                    '_id' => '63eba821d388b01fa1a39f25',
                    'branch_id' => '49a7011a05c677b9a916612a',
                    'namespace' => 'glofox',
                    'active' => false,
                    'categories' => ['62a9b7fd18d1d4d010e1110a', '62a9b7fd18d1d4d010e11108'],
                    'name' => 'Sparkling Water Bottle 1l',
                    'description' => 'Sparkling Sweet Water 1l',
                    'private' => false,
                    'taxIds' => [],
                    'taxes' => null,
                    'image' => null,
                    'presentations' => [
                        [
                            'id' => 1_508_754_157_792,
                            'retail_price' => 13,
                            'stock' => 16,
                            'wholesale_price' => 12,
                            'description' => 'Sparkling Water makes you feel good every day',
                        ],
                    ],
                ]
            ],
            [
                'Product' => [
                    '_id' => '63eb86d125ad6448d117ae5b',
                    'branch_id' => '49a7011a05c677b9a916612a',
                    'namespace' => 'glofox',
                    'active' => false,
                    'categories' => ['62a9b7fd18d1d4d010e1110a', '62a9b7fd18d1d4d010e11108'],
                    'name' => 'Sparkling Water Bottle 0.5l',
                    'description' => 'Sparkling Sweet Water 0.5l',
                    'private' => false,
                    'taxIds' => [],
                    'taxes' => null,
                    'image' => null,
                    'presentations' => [
                        [
                            'id' => 1_508_754_157_795,
                            'retail_price' => 6,
                            'stock' => 20,
                            'wholesale_price' => 7,
                            'description' => 'Sparkling Water makes you feel good every day',
                        ],
                    ],
                ]
            ],
        ];

        $this->assertTrue($expected === $result);
    }

    public function testViewFetchesAllInactiveProductsDataProvider(): array
    {
        return [
            'Active 0 string' => ['0'],
            'Active false bool' => ['false'],
        ];
    }

    public function testTrainersShouldNotUpdateProducts(): void
    {
        $this->authenticateAsTrainer();
        $data = [
            '_id' => '5b44ae18b13d95b5f633fd06',
            'branch_id' => '49a7011a05c677b9a916612a',
            'namespace' => 'glofox',
            'active' => false,
            'categories' => ['62a9b7fd18d1d4d010e1110a', '62a9b7fd18d1d4d010e11108'],
            'name' => 'Sparkling Water Bottle 0.5l',
            'description' => 'Sparkling Sweet Water 0.5l',
            'private' => false,
            'taxIds' => [],
            'taxes' => null,
            'image' => null,
            'featured' => false,
            'presentations' => [
                [
                    'id' => 1_508_754_157_795,
                    'retail_price' => 6,
                    'stock' => 20,
                    'wholesale_price' => 7,
                    'description' => 'Sparkling Water makes you feel good every day',
                ],
            ],
        ];

        $result = $this->testAction('/products/upsert', [
            'method' => Request::METHOD_POST,
            'data' => $data,
        ]);
        $result = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        $this->assertFalse($result['success']);
        $this->assertEquals('UNAUTHORIZED_FOR_THIS_ACTION', $result['message']);
        $this->assertEquals('UNAUTHORIZED_FOR_THIS_ACTION', $result['message_code']);
    }
}
