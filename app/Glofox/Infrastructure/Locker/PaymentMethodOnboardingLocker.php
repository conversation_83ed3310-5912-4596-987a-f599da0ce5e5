<?php

declare(strict_types=1);

namespace Glofox\Infrastructure\Locker;

use Carbon\Carbon;
use Glofox\Domain\Cache\CacheClientInterface;
use Glofox\Domain\Locker\PaymentMethodOnboardingAlreadyLockedException;
use Glofox\Domain\Locker\PaymentMethodOnboardingLockerInterface;
use Psr\Log\LoggerInterface;

class PaymentMethodOnboardingLocker implements PaymentMethodOnboardingLockerInterface
{
    private CacheClientInterface $cache;

    private LoggerInterface $logger;

    public function __construct(
        LoggerInterface $logger,
        CacheClientInterface $cache
    ) {
        $this->logger = $logger;
        $this->cache = $cache;
    }

    public function lock(string $branchId, string $paymentMethodType): void
    {
        $lockKey = $this->computeLockKey($branchId, $paymentMethodType);
        $this->logger->info(sprintf('attempting to lock payment method onboarding with key %s', $lockKey));

        $added = $this->cache->setIfNotExist($lockKey, $lockKey);
        if (!$added) {
            throw PaymentMethodOnboardingAlreadyLockedException::withBranchIdAndPaymentMethodType($branchId, $paymentMethodType);
        }

        $expiresAt = Carbon::now()->addMinute();
        $this->cache->expiresAt($lockKey, $expiresAt);

        $this->logger->info(
            sprintf('lock successfully created payment method onboarding with key %s', $lockKey),
            [
                'expiresAt' => $expiresAt->toISOString(),
            ]
        );
    }

    public function unlock(string $branchId, string $paymentMethodType): void
    {
        $lockKey = $this->computeLockKey($branchId, $paymentMethodType);
        $this->cache->delete($lockKey);

        $this->logger->info(
            sprintf('lock deleted for payment method onboarding with key %s', $lockKey)
        );
    }

    private function computeLockKey(string $branchId, string $paymentMethodType): string
    {
        return sprintf(
            '%s-%s',
            $branchId,
            $paymentMethodType,
        );
    }
}
