<?php


namespace Glofox\Repositories\Search\Expressions;

use Glofox\Repositories\Filters\Evaluable;
use Glofox\Repositories\Search\Expressions\Conditionable;
use Glofox\Repositories\Search\Expressions\HasConditions;
use Glofox\Repositories\Search\Expressions\Key;
use Webmozart\Expression\Expr;

class PoolExternalId extends Key implements Evaluable, Conditionable
{
    use HasConditions;

    public function __construct(string $externalId)
    {
        parent::__construct('pool.external_id', Expr::equals($externalId));
    }

    public function field(): string
    {
        return 'pool.external_id';
    }
}
