<?php


class PriceBreakdownFixture extends CakeTestFixture
{
    public $import = 'PriceBreakdown';

    public function init()
    {
        $this->records = [

            [
                '_id' => new MongoId('56fb0cff778f3b4db5001200'),
                'branch_id' => '5db1b008f15a8f08c04f28ae',
                'charge_id' => '5db1b036f15a8f08c04f28af',
                'taxes' => [
                    'products' => [
                        0 => [
                            'tax_total' => 10,
                            'applied_taxes' => []
                        ]
                    ]
                ]
            ],
            [
                '_id' => new MongoId('56fb0cff778f3b4db5001201'),
                'branch_id' => '5db1b008f15a8f08c04f28ae',
                'charge_id' => '5db1b03df15a8f08c04f28b0',
                'taxes' => [
                    'products' => [
                        0 => [
                            'tax_total' => 20,
                            'applied_taxes' => []
                        ]
                    ]
                ]
            ],

            [
                '_id' => new MongoId('56fb0cff778f3b4db5001202'),
                'branch_id' => '5db1b008f15a8f08c04f28af',
                'charge_id' => '5dc5bcbe7d54963c7c846b5b',
                'taxes' => [
                    'products' => [
                        0 => [
                            'tax_total' => 15,
                            'applied_taxes' => []
                        ]
                    ]
                ]
            ],
            [
                '_id' => new MongoId('56fb0cff778f3b4db5001203'),
                'branch_id' => '5db1b008f15a8f08c04f28af',
                'charge_id' => '5dc5bd817d54963c7c846b5c',
                'taxes' => [
                    'products' => [
                        0 => [
                            'tax_total' => 0.2,
                            'applied_taxes' => []
                        ]
                    ]
                ]
            ],
            [
                '_id' => new MongoId('56fb0cff778f3b4db5001204'),
                'branch_id' => '5db1b008f15a8f08c04f28af',
                'charge_id' => '5afdc3da1129ba338f595132',
                'taxes' => [
                    'products' => [
                        0 => [
                            'tax_total' => 15,
                            'applied_taxes' => []
                        ]
                    ]
                ]
            ],
            [
                '_id' => new MongoId('56fb0cff778f3b4db5001205'),
                'branch_id' => '5db1b008f15a8f08c04f28af',
                'charge_id' => '5841700f7cad363dc88b4568',
                'taxes' => [
                    'products' => [
                        0 => [
                            'tax_total' => 3.3,
                            'applied_taxes' => []
                        ]
                    ]
                ]
            ],
            [
                '_id' => new MongoId('56fb0cff778f3b4db5001206'),
                'branch_id' => '5db1b008f15a8f08c04f28af',
                'charge_id' => '5a4a8e8a047673a418000004',
                'taxes' => [
                    'products' => [
                        0 => [
                            'tax_total' => 0.92,
                            'applied_taxes' => []
                        ]
                    ]
                ]
            ],
            [
                '_id' => new MongoId('56fb0cff778f3b4db5001207'),
                'branch_id' => '5db1b008f15a8f08c04f28af',
                'charge_id' => '5841700f7cad363dc88b4569',
                'taxes' => [
                    'products' => [
                        0 => [
                            'tax_total' => 2.3,
                            'applied_taxes' => []
                        ]
                    ]
                ]
            ],
            [
                '_id' => new MongoId('56fb0cff778f3b4db5001208'),
                'branch_id' => '5db1b008f15a8f08c04f28af',
                'charge_id' => '5841700f7cad363dc88b4570',
                'taxes' => [
                    'products' => [
                        0 => [
                            'tax_total' => 5,
                            'applied_taxes' => []
                        ]
                    ]
                ]
            ],
            [
                '_id' => new MongoId('56fb0cff778f3b4db5001209'),
                'branch_id' => '5db1b008f15a8f08c04f28af',
                'charge_id' => '5841700f7cad363dc88b4571',
                'taxes' => [
                    'products' => [
                        0 => [
                            'tax_total' => 5.46,
                            'applied_taxes' => []
                        ]
                    ]
                ]
            ],
            [
                '_id' => new MongoId('56fb0cff778f3b4db5001210'),
                'branch_id' => '5db1b008f15a8f08c04f28af',
                'charge_id' => '58e4cf398b8c354a0c000003',
                'taxes' => [
                    'products' => [
                        0 => [
                            'tax_total' => 7.3,
                            'applied_taxes' => []
                        ]
                    ]
                ]
            ],
            [
                '_id' => new MongoId('56fb0cff778f3b4db5001211'),
                'branch_id' => '5db1b008f15a8f08c04f28af',
                'charge_id' => '5a3a5054948dae0706000001',
                'taxes' => [
                    'products' => [
                        0 => [
                            'tax_total' => 12.45,
                            'applied_taxes' => [
                                0 => [
                                    'name' => 'test tax',
                                    'rate' => 12.5,
                                    'price' => 2.2
                                ]
                            ]
                        ]
                    ]
                ]
            ],
            [
                '_id' => new MongoId('56fb0cff778f3b4db5001212'),
                'branch_id' => '5db1b008f15a8f08c04f28af',
                'charge_id' => '5a3a5054948dae0706000009',
                'taxes' => [
                    'products' => [
                        0 => [
                            'tax_total' => 2.2,
                            'applied_taxes' => [
                                0 => [
                                    'id' => '123qweasd',
                                    'name' => 'test tax',
                                    'rate' => 12.5,
                                    'price' => 2.2
                                ]
                            ]
                        ]
                    ]
                ]
            ],
            [
                '_id' => new MongoId('56fb0cff778f3b4db5001213'),
                'branch_id' => '5db1b008f15a8f08c04f28af',
                'charge_id' => '5a3a5054948dae0706000019',
                'taxes' => [
                    'products' => [
                        0 => [
                            'tax_total' => 4.22,
                            'applied_taxes' => [
                                0 => [
                                    'id' => '123qweasd123',
                                    'name' => 'second tax',
                                    'rate' => 12.5,
                                    'price' => 4.22
                                ]
                            ]
                        ]
                    ]
                ]
            ],
            [
                '_id' => new MongoId('56fb0cff778f3b4db5001214'),
                'branch_id' => '5db1b008f15a8f08c04f28af',
                'charge_id' => '5a3a5054948dae0706000029',
                'taxes' => [
                    'products' => [
                        0 => [
                            'product_price'=> 9,
                            'net_price'=> 8,
                            'total_price'=> 9,
                            'tax_total' => 1,
                            'applied_taxes' => [
                                0 => [
                                    'id' => '123qweasd123',
                                    'name' => 'second tax',
                                    'rate' => 12.5,
                                    'price' => 1
                                ]
                            ]
                        ]
                    ]
                ],
                'discounts'=> [
                    'products'=> [
                        0 => [
                            'product_price'=> 10,
                            'discounts_total'=> 1,
                            'discounted_price'=> 9,
                            'applied_discounts'=> [
                                0 => [
                                    'id'=> 'discount-foo',
                                    'name'=> 'Disount 123',
                                    'rate_value'=> 10,
                                    'rate_type'=> 'percentage',
                                    'discount_amount'=> 1
                                ]
                          ]
                    ]
                    ]
                ]
            ],
            [
                '_id' => new MongoId('56fb0cff778f3b4db5001215'),
                'branch_id' => '5db1b008f15a8f08c04f28af',
                'charge_id' => '5a3a5054948dae0706000129',
                'taxes' => [
                    'products' => [
                        0 => [
                            'product_price'=> 17.5,
                            'net_price'=> 15.56,
                            'total_price'=> 17.5,
                            'tax_total' => 1.94,
                            'applied_taxes' => [
                                0 => [
                                    'id' => '123qweasd123',
                                    'name' => 'second tax',
                                    'rate' => 12.5,
                                    'price' => 1.94
                                ]
                            ]
                        ]
                    ]
                ],
                'discounts'=> [
                    'products'=> [
                        0 => [
                            'product_price'=> 20,
                            'discounts_total'=> 2.5,
                            'discounted_price'=> 17.5,
                            'applied_discounts'=> [
                                0 => [
                                    'id'=> 'discount-bar',
                                    'name'=> 'Disount abc',
                                    'rate_value'=> 12.5,
                                    'rate_type'=> 'percentage',
                                    'discount_amount'=> 2.5
                                ]
                            ]
                        ]
                    ]
                ]
            ],
            [
                '_id' => new MongoId('56fb0cff778f3b4db5001250'),
                'branch_id' => '5db1b008f15a8f08c04f28af',
                'charge_id' => '5a3a5054948dae0706000130',
                'taxes' => [
                    'products' => [
                        0 => [
                            'product_price'=> 9,
                            'net_price'=> 8,
                            'total_price'=> 9,
                            'tax_total' => 1,
                            'applied_taxes' => [
                                0 => [
                                    'id' => '123qweasd123',
                                    'name' => 'second tax',
                                    'rate' => 12.5,
                                    'price' => 1
                                ]
                            ]
                        ]
                    ]
                ],
                'discounts'=> [
                    'products'=> [
                        0 => [
                            'product_price'=> 100,
                            'discounts_total'=> 10,
                            'discounted_price'=> 90,
                            'applied_discounts'=> [
                                0 => [
                                    'id'=> 'discount-tom',
                                    'name'=> 'Disount 123',
                                    'rate_value'=> 10,
                                    'rate_type'=> 'fixed',
                                    'discount_amount'=> 10
                                ]
                          ]
                    ]
                    ]
                ]
            ],
        ];
        parent::init();
    }
}
