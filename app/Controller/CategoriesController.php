<?php

/**
 * ClientsController class.
 *
 * @uses          AppController
 */

use Glofox\Domain\Authentication\Auth;
use Glofox\Domain\Categories\UseCase\CategoriesFilter;

/**
 * Class CategoriesController.
 *
 * @property User $User
 * @property Program $Program
 * @property Facility $Facility
 * @property Category $Category
 */
class CategoriesController extends AppController
{
    /**
     * name property.
     *
     * @var string 'TermsConditions'
     */
    public $name = 'Categories';
    public $components = [
        'Utility',
        'JWT',
    ];
    public $uses = [
        'Category',
        'Facility',
        'User',
        'Program',
    ];
    /*******************************************************************************************
     *                                API 2.0 New Dashboard
     ******************************************************************************************/

    /**
     * Dispatcher for Model.
     *
     * @param string $identifier [description]
     *
     * @return string
     */
    public function dispatcher($identifier = null)
    {
        return parent::dispatch($identifier, $this->Category);
    }

    /**
     * Get categories all in branch or specific category by id,.
     *
     * @param $id
     *
     * @return array List of terms that match the entry params
     * @internal   This convention is a little boring for clients, but helps us keep clean backend
     *
     * @example    https://www.glofoxlogin/view
     *
     */
    public function view($id)
    {
        $this->JWT->parseToken();
        $categories = isset($id) ? $this->Category->findById($id) : $this->Category->find();

        return response()->json($categories);
    }

    /**
     * @param null $model
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function findByModel($model = null)
    {
        $model_categories_to_tick = null;
        $return_model = false;
        $categories = $this->Category->findByModel($model, $model_categories_to_tick, $return_model);

        $categories = app()
            ->make(CategoriesFilter::class)
            ->execute($categories);

        return response()->json(['success' => true, 'categories' => $categories]);
    }

    /**
     * @param $branch_id
     * @param $category_id
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function findEntitiesByBranchIdAndCategoryId($branch_id, $category_id)
    {
        $category = $this->Category->findById($category_id);

        if (!$category) {
            $result = [
                'success' => false,
                'message' => 'There is no category with the given id',
            ];

            return response()->json($result);
        }

        $models = [
            $this->Facility->useTable,
            $this->User->useTable,
            $this->Program->useTable,
        ];

        $returnModel = false;
        $bookable = true;

        if (!in_array($category['Category']['model'], $models)) {
            $result = [
                'success' => false,
                'message' => 'Invalid model',
            ];

            return response()->json($result);
        }

        $entities = [];
        if ($this->Facility->useTable === $category['Category']['model']) {
            $entities = $this->Facility->findListByBranchIdAndCategoryId(
                $branch_id,
                $category_id,
                $returnModel,
                $bookable
            );
        } elseif ($this->User->useTable === $category['Category']['model']) {
            $entities = $this->User->findListByBranchIdAndCategoryId($branch_id, $category_id, $returnModel, $bookable);
        } elseif ($this->Program->useTable === $category['Category']['model']) {
            $entities = $this->Program->findListByBranchIdAndCategoryId($branch_id, $category_id, $returnModel);
        }

        $result = [
            'success' => true,
            'entities' => $entities,
        ];

        return response()->json($result);
    }
}
