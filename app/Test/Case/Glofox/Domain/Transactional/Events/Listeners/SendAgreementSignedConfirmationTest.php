<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\Transactional\Events\Listeners;

use Glofox\Domain\Branches\Models\Branch;
use Glofox\Domain\Branches\Repositories\BranchesRepository;
use Glofox\Domain\ElectronicAgreements\Events\AgreementWasSigned;
use Glofox\Domain\ElectronicAgreements\Trigger;
use Glofox\Domain\Transactional\Events\Listeners\SendAgreementSignedConfirmation;
use Glofox\Domain\Transactional\MessageDispatcher;
use Glofox\Domain\Transactional\Messages\Factory;
use Glofox\Domain\Transactional\Messages\Identifier as MessageIdentifier;
use Glofox\Domain\Transactional\Messages\Message;
use Glofox\Domain\Transactional\Messages\ParsedMessage;
use Glofox\Domain\Transactional\Messages\Variable;
use Glofox\Domain\Transactional\Messages\Variables\Strategies\DocumentUrl;
use Glofox\Domain\Transactional\Repositories\TransactionalMessagesRepository;
use Glofox\Domain\Users\Models\User;
use Glofox\Domain\Users\Repositories\UsersRepository;
use League\Event\EventInterface;
use Mockery;

\App::import('Test/Case', 'GlofoxTestCase');

class SendAgreementSignedConfirmationTest extends \GlofoxTestCase
{
    public $fixtures = [];
    /** @var TransactionalMessagesRepository */
    private $transactionalMessagesRepositoryMock;

    /**
     * @var MessageDispatcher
     */
    protected $messageDispatcherMock;

    /** @var BranchesRepository */
    private $branchesRepositoryMock;

    /** @var UsersRepository */
    private $usersRepositoryMock;

    /** @var Factory */
    private $transactionalMessageFactoryMock;

    /** @var DocumentUrl */
    private $documentUrlMock;

    private ?\Glofox\Domain\ElectronicAgreements\Events\AgreementWasSigned $event = null;

    public function setUp()
    {
        parent::setUp();

        $this->transactionalMessagesRepositoryMock = Mockery::mock(TransactionalMessagesRepository::class);
        $this->branchesRepositoryMock = Mockery::mock(BranchesRepository::class);
        $this->usersRepositoryMock = Mockery::mock(UsersRepository::class);
        $this->transactionalMessageFactoryMock = Mockery::mock(Factory::class);
        $this->messageDispatcherMock = Mockery::mock(MessageDispatcher::class);
        $this->event = new AgreementWasSigned("5f2d3b0642a28e001974e563", "6091662fa67a7e4cec5f6ad2", "mock-agreement-id", Trigger::MEMBER_AUTHENTICATED());
    }

    public function tearDown()
    {
        parent::tearDown();

        Mockery::close();
    }

    public function test_it_should_not_send_message_when_transactional_message_is_not_enabled(): void
    {
        $message = ['enabled' => false];

        $branch = ['_id' => $this->event->branchId];
        $this->makeBranchRepository($branch);
        $this->makeUserRepository(['_id' => $this->event->userId]);

        $this->makeTransactionalMessagesRepository($message, ['Branch' => $branch], false);

        $this->injectDependencies();

        $listener = new SendAgreementSignedConfirmation();

        self::assertFalse($listener->handle($this->event));

        $this->removeDependencies();
    }

    public function test_it_should_successfully_send_message()
    {

        $message = ['enabled' => true];

        $branch = ['_id' => $this->event->branchId, 'namespace' => 'eamigrations', 'name' => 'mock-studio-name'];
        $this->makeBranchRepository($branch);

        $user = [
            '_id' => $this->event->userId,
            'first_name' => 'mock-name',
            'branch_id' => $this->event->branchId,
        ];
        $this->makeUserRepository($user);

        $this->makeTransactionalMessagesRepository($message, ['Branch' => $branch], true);

        $this->messageDispatcherMock
            ->shouldReceive('withBranch')
            ->withArgs([['Branch' => $branch]])
            ->andReturnSelf();

        $this->messageDispatcherMock
            ->shouldReceive('withMessageDefinition')
            ->andReturnSelf();

        $this->messageDispatcherMock
            ->shouldReceive('dispatchTo')
            ->withArgs($this->matchContent())
            ->andReturn(true);

        $this->documentUrlMock = Mockery::mock(DocumentUrl::class);
        $this->documentUrlMock
            ->shouldReceive('find')
            ->andReturn('mock-url');

        $this->injectDependencies();

        $listener = new SendAgreementSignedConfirmation();

        self::assertTrue($listener->handle($this->event));

        $this->removeDependencies();
    }

    private function matchContent(): \Closure
    {
        return function ($user, ParsedMessage $parsedMessage): bool {
            self::assertEquals(
                "<p>mock-name WAIVER mock-url mock-studio-name</p>",
                $parsedMessage->content()
            );
            return true;
        };
    }

    private function makeTransactionalMessagesRepository(array $message, array $branch, bool $enabled)
    {
        $transactionalMessageMock = Mockery::mock(Message::class);

        $this->transactionalMessagesRepositoryMock
            ->shouldReceive('findByBranchAndIdentifierWithDefaults')
            ->withArgs([$branch, MessageIdentifier::EA_AGREEMENT_SIGNED_CONFIRMATION()])
            ->andReturn($message);

        $this->transactionalMessageFactoryMock
            ->shouldReceive('create')
            ->andReturn($transactionalMessageMock);

        $transactionalMessageMock
            ->shouldReceive('isEnabled')
            ->andReturn($enabled);

        if (!$enabled) {
            return;
        }

        $variables = [
            Variable::MEMBER_FIRST_NAME(),
            Variable::DOCUMENT_NAME(),
            Variable::DOCUMENT_URL(),
            Variable::BRANCH_NAME(),
        ];

        $transactionalMessageMock
            ->shouldReceive('variables')
            ->andReturn($variables);

        $transactionalMessageMock
            ->shouldReceive('content')
            ->andReturn('<p>[member_first_name] [document_name] [document_url] [branch_name]</p>');

        $transactionalMessageMock
            ->shouldReceive('subject')
            ->andReturn('mock-subject');
    }

    private function makeBranchRepository(array $branch): void
    {
        $branchMock = Mockery::mock(Branch::class);
        $branchMock->shouldReceive('toArray')
            ->andReturn($branch);


        $this->branchesRepositoryMock
            ->shouldReceive('addCriteria')
            ->andReturnSelf();

        $this->branchesRepositoryMock
            ->shouldReceive('firstOrFail')
            ->andReturn($branchMock);

    }

    private function makeUserRepository(array $user): void
    {
        $this->usersRepositoryMock
            ->shouldReceive('addCriteria')
            ->andReturnSelf();

        $this->usersRepositoryMock
            ->shouldReceive('firstOrFail')
            ->andReturn(new User($user));
    }

    private function injectDependencies(): void
    {
        app()->instance(BranchesRepository::class, $this->branchesRepositoryMock);
        app()->instance(TransactionalMessagesRepository::class, $this->transactionalMessagesRepositoryMock);
        app()->instance(UsersRepository::class, $this->usersRepositoryMock);
        app()->instance(Factory::class, $this->transactionalMessageFactoryMock);
        app()->instance(MessageDispatcher::class, $this->messageDispatcherMock);
        app()->instance(DocumentUrl::class, $this->documentUrlMock);
    }

    private function removeDependencies(): void
    {
        app()->forgetInstance(BranchesRepository::class);
        app()->forgetInstance(TransactionalMessagesRepository::class);
        app()->forgetInstance(UsersRepository::class);
        app()->forgetInstance(Factory::class);
        app()->forgetInstance(MessageDispatcher::class);
        app()->forgetInstance(DocumentUrl::class);
    }
}
