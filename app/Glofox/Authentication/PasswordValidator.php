<?php

namespace Glofox\Authentication;

use Exception;
use Glofox\Authentication\PasswordRules\PasswordRuleInterface;
use Glofox\Domain\Users\Models\User;

class PasswordValidator implements PasswordValidatorInterface
{
    /** @var PasswordRuleInterface[] */
    private array $rules;

    public function __construct(array $rules)
    {
        $this->rules = $rules;
    }

    public function validate(User $target, string $password): void
    {
        foreach ($this->rules as $rule) {
            if (!$rule->check($target, $password)) {
                throw new Exception($rule->getMessageCode());
            }
        }
    }
}
