<?php

namespace Glofox\Providers;

use Glofox\Http\Requests\RequestIdGeneratorInterface;
use Glofox\Http\Requests\UuidRequestIdGenerator;
use Glofox\Http\Requests\TracingContextInterface;
use Glofox\Http\Requests\TracingContext;
use Illuminate\Routing\Redirector;
use Glofox\Request as GlofoxRequest;
use Glofox\Providers\Contracts\Provider as ProviderContract;
use Illuminate\Contracts\Routing\ResponseFactory as ResponseFactoryContract;
use Illuminate\Support\Facades\View;
use Illuminate\Support\ServiceProvider;
use Psr\Http\Message\ResponseInterface;
use Symfony\Component\HttpFoundation\Request;
use Illuminate\Contracts\Validation\ValidatesWhenResolved;

class RequestServiceProvider extends ServiceProvider implements ProviderContract
{
    /**
     * Register the service provider.
     *
     * @return void
     */
    public function register()
    {
        $this->registerRequestIdGenerator();
        $this->registerTracingContext();
        $this->registerPsrResponse();
        $this->registerResponseFactory();
        $this->registerCurrentRequest();
        $this->registerResolvers();
    }

    private function registerRequestIdGenerator(): void
    {
        $this->app->singleton(RequestIdGeneratorInterface::class, fn() => new UuidRequestIdGenerator());
    }

    private function registerTracingContext(): void
    {
        $this->app->singleton(TracingContextInterface::class, fn() => new TracingContext());
    }

    public function registerResolvers()
    {
        $this->app->afterResolving(ValidatesWhenResolved::class, function ($resolved) {
            $resolved->validate();
        });

        $this->app->resolving(GlofoxRequest::class, function ($request, $app) {
            $this->initializeRequest($request, $app['request']);
            $request->setContainer($app)->setRedirector($app->make(Redirector::class));
        });
    }

    public function registerCurrentRequest()
    {
        // $request = \Illuminate\Http\Request::capture();
        $request = \Glofox\Request::capture();
        $this->app->instance('request', $request);
    }
    /**
     * Register a binding for the PSR-7 response implementation.
     *
     * @return void
     */
    protected function registerPsrResponse()
    {
        $this->app->bind(ResponseInterface::class, fn($app) => new PsrResponse());
    }

    /**
     * Register the response factory implementation.
     *
     * @return void
     */
    protected function registerResponseFactory()
    {
        $this->app->singleton(ResponseFactoryContract::class, fn($app) => new ResponseFactory($app['view'], $app['redirect']));
    }

    /**
     * Initialize the form request with data from the given request.
     *
     * @param GlofoxRequest                              $form
     * @param  \Symfony\Component\HttpFoundation\Request $current
     *
     * @return void
     * @internal param GlofoxRequest $from
     */
    protected function initializeRequest(GlofoxRequest $form, Request $current)
    {
        $files = $current->files->all();

        $files = is_array($files) ? array_filter($files) : $files;

        $form->initialize(
            $current->query->all(),
            $current->request->all(),
            $current->attributes->all(),
            $current->cookies->all(),
            $files,
            $current->server->all(),
            $current->getContent()
        );

        $form->setJson($current->json());

        if ($session = $current->getSession()) {
            $form->setLaravelSession($session);
        }

        $form->setUserResolver($current->getUserResolver());

        $form->setRouteResolver($current->getRouteResolver());
    }
}
