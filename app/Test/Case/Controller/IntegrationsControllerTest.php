<?php

use CakeTestCases\Glofox\Domain\Authentication\Token\TokenGeneration;
use CakeTestCases\Glofox\Domain\Users\Traits\FetchUsersTrait;

App::import('Test/Case', 'GlofoxControllerTestCase');

class IntegrationsControllerTest extends GlofoxControllerTestCase
{
    use FetchUsersTrait;
    use TokenGeneration;

    /**
     * Fixtures.
     *
     * @var array
     */
    public $fixtures = [
        'app.branch',
        'app.user',
        'app.integration',
    ];

    public static $token;

    /*************************** API 2.0 **************************/

    /**
     * Authenticate as User, calls authenticate with password with new api.
     */
    public function testLoginRedirect()
    {
        $user = $this->fetchUser('59a7011a05c677bda916612a');
        $token = $this->loginAsUser($user);

        self::$token = $token;

        self::assertNotNull($token);
    }

    public function testGetAll()
    {
        $_SERVER['HTTP_AUTHORIZATION'] = self::$token;

        $result = $this->testAction('/2.0/integrations/', ['method' => 'get']);
        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        $this->assertNotNull($result->data);
        $this->assertTrue(is_countable($result->data) && count($result->data) > 0);
    }

    public function testGetById()
    {
        $_SERVER['HTTP_AUTHORIZATION'] = self::$token;
        $result = $this->testAction('/2.0/integrations/49b7012a05c677c9a512503c', ['method' => 'get']);
        $result = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        $this->assertNotEmpty($result['_id']);
    }

    public function testCreateIntegration()
    {
        $_SERVER['HTTP_AUTHORIZATION'] = self::$token;

        $insert = [
            'service' => 'classpass',
        ];
        $response = $this->testAction('/2.0/integrations', ['method' => 'post', 'data' => $insert]);
        $response = json_decode($response, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        $this->assertTrue($response->success);

        // bad request
        $response = $this->testAction('/2.0/integrations', ['method' => 'post', 'data' => []]);
        $response = json_decode($response, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        $this->assertFalse($response->success);
    }

    public function testUpdateIntegration()
    {
        $_SERVER['HTTP_AUTHORIZATION'] = self::$token;

        $update = [];
        $response = $this->testAction(
            '/2.0/integrations/49b7012a05c677c9a512503c',
            ['method' => 'put', 'data' => $update]
        );
        $response = json_decode($response, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        $this->assertTrue($response->success);

        // bad request
        $response = $this->testAction(
            '/2.0/integrations/49b7012a05c677c9a512503a',
            ['method' => 'put', 'data' => []]
        );
        $response = json_decode($response, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        $this->assertFalse($response->success);
    }

    public function testConfirmIntegration()
    {
        $_SERVER['HTTP_AUTHORIZATION'] = self::$token;

        // bad request
        $response = $this->testAction('/2.0/integrations/49b7012a05c677c9a512503a/confirm', ['method' => 'post']);
        $response = json_decode($response, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        $this->assertFalse($response->success);
    }

    public function testDeleteIntegration()
    {
        $_SERVER['HTTP_AUTHORIZATION'] = self::$token;

        $response = $this->testAction('/2.0/integrations/49b7012a05c677c9a512503c', ['method' => 'delete']);
        $response = json_decode($response, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        $this->assertTrue($response->success);

        // bad request
        $response = $this->testAction('/2.0/integrations/59ba9c5e60115d62e417a413', ['method' => 'delete']);
        $response = json_decode($response, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        $this->assertFalse($response->success);
    }
}
