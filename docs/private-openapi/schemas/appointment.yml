type: object
properties:
  _id:
    type: string
  namespace:
    type: string
  branch_id:
    type: string
  name:
    type: string
  active:
    type: boolean
  private:
    type: boolean
  categories:
    type: array
    items:
      type: string
      description: Category id
  description:
    type: string
  allowed_member_types:
    type: array
    items:
      type: object
      properties:
        price:
          type: number
        type:
          type: string
        membership_id:
          type: string
        memberships:
          type: array
          items:
            type: object
            properties:
              id:
                type: string
              label:
                type: string
  pricing:
    type: string
    nullable: true
    enum:
      - SINGLE
      - FREE
      - CUSTOM
  staff:
    type: string
  staff_ids:
    type: array
    items:
      type: string
      description: Staff id
  time_slot_length:
    type: integer
  date_start:
    type: string
    description: |-
      Date time Y-m-d. There are only a few cases with that field, we don't know if it's
      a deprecated field or not.
  size:
    type: integer
    maximum: 5
    default: 1
    description: The maximum number of attendees of the appointment. This value is a positive number that can be
      set from 1 to 5.
  created:
    type: string
    format: date-time
  modified:
    type: string
    format: date-time