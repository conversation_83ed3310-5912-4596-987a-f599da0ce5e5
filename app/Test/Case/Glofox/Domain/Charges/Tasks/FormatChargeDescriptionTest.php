<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\Charges\Tasks;

use App;
use CakeTestCases\Glofox\Domain\Logger\Traits\MockedLoggerTrait;
use Carbon\Carbon;
use Glofox\Domain\Bookings\Models\BookableEntityType;
use Glofox\Domain\Charges\Models\Charge;
use Glofox\Domain\Charges\Repositories\ChargesRepository;
use Glofox\Domain\Charges\Tasks\FormatChargeDescription;
use Glofox\Domain\Memberships\Models\Addon;
use Glofox\Domain\Memberships\Services\AddonServiceInterface;
use Glofox\Domain\Translation\Translator;
use Glofox\Eventkit\DomainEvent\DomainEventInterface;
use Glofox\Eventkit\DomainEvent\DomainEventPayloadInterface;
use Glofox\Repositories\Search\Expressions\Shared\Id;
use Mockery;

App::import('Test/Case', 'GlofoxTestCase');

class FormatChargeDescriptionTest extends \GlofoxTestCase
{
    use MockedLoggerTrait;

    /** @var ChargesRepository */
    private $chargesRepository;

    public function setUp()
    {
        parent::setUp();
        $this->chargesRepository = app()->make(ChargesRepository::class);
        $this->mockLogger();
    }

    public function tearDown()
    {
        parent::tearDown();
        $this->teardownLogger();
        \Mockery::close();
    }

    public function testDomainEventDoesNotChangeDescriptionWhenNoServicesAreIncluded(): void
    {
        $branchId = '49a7011a05c677b9a916612a';
        $chargeId = '5db1b036f15a8f08c04f28c3';
        $domainEvent = $this->mockDomainEvent($chargeId, $branchId);

        $serviceId = 'mock-serviceId';
        $serviceDefinitionId = 'mock-serviceDefinitionId';
        $addonServiceInterface = \Mockery::mock(AddonServiceInterface::class);

        $formatChargeDescriptionTask = new FormatChargeDescription(
            $this->chargesRepository,
            $addonServiceInterface,
            $this->loggerMock
        );

        $formatChargeDescriptionTask->execute($domainEvent);

        /** @var Charge $storedCharge */
        $storedCharge = $this->chargesRepository
            ->addCriteria(new Id($chargeId))
            ->first();

        self::assertEquals('Subscription with no add ons', $storedCharge->description());
    }

    public function testDomainEventUpdatesWhenOneServiceExistsOnCharge(): void
    {
        $branchId = '49a7011a05c677b9a916612a';
        $chargeId = '5db1b036f15a8f08c04f28b3';
        $domainEvent = $this->mockDomainEvent($chargeId, $branchId);

        $serviceId = 'mock-serviceId';
        $serviceDefinitionId = 'mock-serviceDefinitionId';
        $addonServiceInterface = \Mockery::mock(AddonServiceInterface::class);
        $addonServiceInterface
            ->shouldReceive('getAddon')
            ->withArgs(
                function (string $serviceIdSentToService, string $branchIdIdSentToService) use ($serviceId, $branchId) {
                    self::assertEquals($serviceId, $serviceIdSentToService);
                    self::assertEquals($branchId, $branchIdIdSentToService);

                    return true;
                })
            ->andReturn(new Addon(
                $serviceId,
                $serviceDefinitionId,
                'mock-serviceDefinitionPlanId',
                'Tanning',
                'Recurring',
                BookableEntityType::FACILITY,
                Carbon::yesterday()->getTimestamp(),
                false
            ))
            ->once();

        $translator = Mockery::mock(Translator::class);
        $translator->shouldReceive('translate')
            ->with('AND')
            ->once()
            ->andReturn('and');
        app()->instance(Translator::class, $translator);

        $formatChargeDescriptionTask = new FormatChargeDescription(
            $this->chargesRepository,
            $addonServiceInterface,
            $this->loggerMock
        );

        $formatChargeDescriptionTask->execute($domainEvent);

        /** @var Charge $storedCharge */
        $storedCharge = $this->chargesRepository
            ->addCriteria(new Id($chargeId))
            ->first();

        self::assertEquals('Subscription with one add on and Tanning', $storedCharge->description());

        app()->forgetInstance(Translator::class);
    }

    public function testDomainEventUpdatesWhenMultipleServicesExistsOnCharge(): void
    {
        $branchId = '49a7011a05c677b9a916612a';
        $chargeId = '5db1b036f15a8f08c04f28d3';
        $domainEvent = $this->mockDomainEvent($chargeId, $branchId);

        $serviceId = 'mock-serviceId';
        $serviceId2 = 'mock-serviceId2';
        $addonServiceInterface = \Mockery::mock(AddonServiceInterface::class);
        $addonServiceInterface
            ->shouldReceive('getAddon')
            ->withArgs(
                function (string $serviceIdSentToService, string $branchIdIdSentToService) use ($serviceId, $branchId) {
                    self::assertEquals($serviceId, $serviceIdSentToService);
                    self::assertEquals($branchId, $branchIdIdSentToService);

                    return true;
                })
            ->andReturn(new Addon(
                $serviceId,
                'mock-serviceDefinitionId',
                'mock-serviceDefinitionPlanId',
                'Tanning',
                'Recurring',
                BookableEntityType::FACILITY,
                Carbon::yesterday()->getTimestamp(),
                false
            ))
            ->once()
            ->shouldReceive('getAddon')
            ->withArgs(
                function (string $serviceIdSentToService, string $branchIdIdSentToService) use (
                    $serviceId2,
                    $branchId
                ) {
                    self::assertEquals($serviceId2, $serviceIdSentToService);
                    self::assertEquals($branchId, $branchIdIdSentToService);

                    return true;
                })
            ->andReturn(new Addon(
                $serviceId2,
                'mock-serviceDefinitionId2',
                'mock-serviceDefinitionPlanId2',
                'Locker',
                'Recurring',
                BookableEntityType::FACILITY,
                Carbon::yesterday()->getTimestamp(),
                false
            ))
            ->once();

        $translator = Mockery::mock(Translator::class);
        $translator->shouldReceive('translate')
            ->with('AND')
            ->once()
            ->andReturn('and');
        app()->instance(Translator::class, $translator);

        $formatChargeDescriptionTask = new FormatChargeDescription(
            $this->chargesRepository,
            $addonServiceInterface,
            $this->loggerMock
        );

        $formatChargeDescriptionTask->execute($domainEvent);

        /** @var Charge $storedCharge */
        $storedCharge = $this->chargesRepository
            ->addCriteria(new Id($chargeId))
            ->first();

        self::assertEquals(
            'Subscription with multiple add ons, Tanning and Locker',
            $storedCharge->description()
        );

        app()->forgetInstance(Translator::class);
    }

    public function testDomainEventUpdatesForPrepaidAddon(): void
    {
        $branchId = '49a7011a05c677b9a916612a';
        $chargeId = '5db1b036f15a8f08c04f28e3';
        $domainEvent = $this->mockDomainEvent($chargeId, $branchId);

        $serviceId = 'mock-serviceId';
        $serviceDefinitionId = 'mock-serviceDefinitionId';
        $addonServiceInterface = \Mockery::mock(AddonServiceInterface::class);
        $addonServiceInterface
            ->shouldReceive('getAddon')
            ->withArgs(
                function (string $serviceIdSentToService, string $branchIdIdSentToService) use ($serviceId, $branchId) {
                    self::assertEquals($serviceId, $serviceIdSentToService);
                    self::assertEquals($branchId, $branchIdIdSentToService);

                    return true;
                })
            ->andReturn(new Addon(
                $serviceId,
                $serviceDefinitionId,
                'mock-serviceDefinitionPlanId',
                'Tanning',
                'Recurring',
                BookableEntityType::FACILITY,
                Carbon::yesterday()->getTimestamp(),
                false
            ))
            ->once();

        $formatChargeDescriptionTask = new FormatChargeDescription(
            $this->chargesRepository,
            $addonServiceInterface,
            $this->loggerMock
        );

        $formatChargeDescriptionTask->execute($domainEvent);

        /** @var Charge $storedCharge */
        $storedCharge = $this->chargesRepository
            ->addCriteria(new Id($chargeId))
            ->first();

        self::assertEquals('Tanning', $storedCharge->description());
    }

    public function testDomainEventUpdatesForProratedPrepaidAddon(): void
    {
        $branchId = '49a7011a05c677b9a916612a';
        $chargeId = '5db1b036f15a8f08c04f28f3';
        $domainEvent = $this->mockDomainEvent($chargeId, $branchId);

        $serviceId = 'mock-serviceId';
        $serviceDefinitionId = 'mock-serviceDefinitionId';
        $addonServiceInterface = \Mockery::mock(AddonServiceInterface::class);
        $addonServiceInterface
            ->shouldReceive('getAddon')
            ->withArgs(
                function (string $serviceIdSentToService, string $branchIdIdSentToService) use ($serviceId, $branchId) {
                    self::assertEquals($serviceId, $serviceIdSentToService);
                    self::assertEquals($branchId, $branchIdIdSentToService);

                    return true;
                })
            ->andReturn(new Addon(
                $serviceId,
                $serviceDefinitionId,
                'mock-serviceDefinitionPlanId',
                'Locker',
                'Recurring',
                BookableEntityType::FACILITY,
                Carbon::yesterday()->getTimestamp(),
                false
            ))
            ->once();

        $formatChargeDescriptionTask = new FormatChargeDescription(
            $this->chargesRepository,
            $addonServiceInterface,
            $this->loggerMock
        );

        $formatChargeDescriptionTask->execute($domainEvent);

        /** @var Charge $storedCharge */
        $storedCharge = $this->chargesRepository
            ->addCriteria(new Id($chargeId))
            ->first();

        self::assertEquals('Locker (90%)', $storedCharge->description());
    }

    public function testDomainEventUpdatesForProratedPrepaidAddonWithTwoPercentagesInDescription(): void
    {
        $branchId = '49a7011a05c677b9a916612a';
        $chargeId = '5db1b036f15a8f08c04f28f0';
        $domainEvent = $this->mockDomainEvent($chargeId, $branchId);

        $serviceId = 'mock-serviceId';
        $serviceDefinitionId = 'mock-serviceDefinitionId';
        $addonServiceInterface = \Mockery::mock(AddonServiceInterface::class);
        $addonServiceInterface
            ->shouldReceive('getAddon')
            ->withArgs(
                function (string $serviceIdSentToService, string $branchIdIdSentToService) use ($serviceId, $branchId) {
                    self::assertEquals($serviceId, $serviceIdSentToService);
                    self::assertEquals($branchId, $branchIdIdSentToService);

                    return true;
                })
            ->andReturn(new Addon(
                $serviceId,
                $serviceDefinitionId,
                'mock-serviceDefinitionPlanId',
                'Locker',
                'Recurring',
                BookableEntityType::FACILITY,
                Carbon::yesterday()->getTimestamp(),
                false
            ))
            ->once();

        $formatChargeDescriptionTask = new FormatChargeDescription(
            $this->chargesRepository,
            $addonServiceInterface,
            $this->loggerMock
        );

        $formatChargeDescriptionTask->execute($domainEvent);

        /** @var Charge $storedCharge */
        $storedCharge = $this->chargesRepository
            ->addCriteria(new Id($chargeId))
            ->first();

        self::assertEquals('Locker (90%)', $storedCharge->description());
    }

    private function mockDomainEvent(string $chargeId, string $branchId): DomainEventInterface
    {
        $payload = [
            'branchId' => $branchId,
            'chargeId' => $chargeId,
        ];
        $domainEventPayload = Mockery::mock(DomainEventPayloadInterface::class);
        $domainEventPayload
            ->shouldReceive('jsonSerialize')
            ->andReturn($payload)
            ->once();

        $domainEvent = Mockery::mock(DomainEventInterface::class);
        $domainEvent
            ->shouldReceive('payload')
            ->andReturn($domainEventPayload)
            ->once()
            ->shouldReceive('type')
            ->andReturn('TRANSACTION_FINISHED');

        return $domainEvent;
    }
}
