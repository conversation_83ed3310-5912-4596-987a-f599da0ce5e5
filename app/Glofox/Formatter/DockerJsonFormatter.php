<?php

namespace Glofox\Formatter;

use Monolog\Formatter\JsonFormatter;

/**
 * Encodes whatever record data is passed to it as json.
 *
 * End of line added for Supervisor Docker compatibility
 */
class DockerJsonFormatter extends JsonFormatter
{
    /**
     * {@inheritdoc}
     */
    public function format(array $record): string
    {
        return parent::format($record) . PHP_EOL;
    }

    /**
     * {@inheritdoc}
     */
    public function formatBatch(array $records): string
    {
        return parent::formatBatch($records) . PHP_EOL;
    }
}
