<?php

declare(strict_types=1);

use Glofox\Payments\Entities\Invoice\Models\InvoiceAddress;
use Glofox\Response\Models\Invoice\InvoiceAddressModel;
use Glofox\Response\Transformers\Invoices\InvoiceAddressTransformer;

App::import('Test/Case', 'GlofoxTestCase');

class InvoiceAddressTransformerTest extends GlofoxTestCase
{
    public $fixtures = [];

    public function testSetFromPaymentProvider(): void
    {
        $address = $this->generateInvoiceAddress();
        $model = new InvoiceAddressModel($this->invoiceAddressToArray($address));

        $transformer = new InvoiceAddressTransformer();
        $transformedModel = $transformer->setFromPaymentProvider($address);

        $this->assertEquals($model, $transformedModel);
    }

    private function generateInvoiceAddress(): InvoiceAddress
    {
        $address = new InvoiceAddress();
        $address->setOwner('own');
        $address->setName('test');
        $address->setAddressLine1('add');
        $address->setAddressLine2('res');
        $address->setAddressLine3('s');

        return $address;
    }

    private function invoiceAddressToArray(InvoiceAddress $address): array
    {
        return [
            'owner' => $address->owner(),
            'name' => $address->name(),
            'address_first' => $address->addressLine1(),
            'address_second' => $address->addressLine2(),
            'address_third' => $address->addressLine3(),
        ];
    }
}
