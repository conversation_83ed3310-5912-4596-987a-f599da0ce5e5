<?php

declare(strict_types=1);

namespace Glofox\Repositories\Search\Expressions\Shared;

use Glofox\Repositories\Filters\Evaluable;
use Glofox\Repositories\Search\Expressions\Conditionable;
use Glofox\Repositories\Search\Expressions\HasConditions;
use Glofox\Repositories\Search\Expressions\Key;
use Illuminate\Support\Collection;
use Webmozart\Expression\Expr;

class InIds extends Key implements Evaluable, Conditionable
{
    use HasConditions;

    private const FIELD = '_id';

    public function __construct(Collection $ids)
    {
        parent::__construct(self::FIELD, Expr::in($ids->values()->toArray()));
    }

    public function field(): string
    {
        return self::FIELD;
    }
}
