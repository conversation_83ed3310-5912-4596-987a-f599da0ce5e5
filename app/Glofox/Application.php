<?php

namespace Glofox;

use Glofox\Bootstrap\Contracts\BootstraperContract;
use Glofox\Bootstrap\LoadConfiguration;
use Glofox\Bootstrap\RegisterProviders;
use Glofox\Domain\Authentication\Token\Verification\UserBranchRelationVerifier;
use Glofox\Providers\Contracts\Provider;
use Glofox\Providers\Traits\SupportsServiceProviders;
use Illuminate\Container\Container;
use Illuminate\Support\Facades\Facade;
use Sentry;

/**
 * Class Application
 *
 * @package Glofox
 */
class Application extends Container
{
    use SupportsServiceProviders;

    /**
     * @var Application
     */
    protected static $instance;

    protected static bool $sentryEnabled = false;

    /**
     * Application constructor.
     */
    public function __construct()
    {
        $this->reboot();

        self::$instance = $this;
    }

    public function configPath()
    {
        return APP . 'Config' . DS . 'bootstrap';
    }

    public function bootstrap()
    {
        $bootstrappers = [
            LoadConfiguration::class
        ];

        foreach ($bootstrappers as $bootstrapper) {
            /** @var BootstraperContract $bootstrapper */
            $bootstrapper = (new $bootstrapper());
            $bootstrapper->bootstrap($this);
        }
    }

    /**
     * Register all of the configured providers.
     *
     * @param array $providers
     *
     * @return void
     */
    public function registerProviders(array $providers)
    {
        foreach ($providers as $provider) {
            $provider = new \ReflectionClass($provider);

            /** @var Provider $instance */
            $instance = $provider->newInstanceArgs([$this]);
            $instance->register();
        }
    }


    public function bindPathsInContainer()
    {
        $this->instance('path.lang', $this->langPath());
    }

    /**
     * Get the path to the language files.
     *
     * @return string
     */
    public function langPath()
    {
        return APP . 'Config' . DS . 'resources/lang';
    }

    /**
     * @inheritdoc
     */
    public function call($callback, array $parameters = [], $defaultMethod = null)
    {
        return BoundMethod::call($this, $callback, $parameters, $defaultMethod);
    }

    /**
     * @template T
     * @param class-string<T> $abstract
     * @return T
     */
    public function make($abstract)
    {
        $abstract = $this->getAlias($abstract);

        return parent::make($abstract);
    }

    /**
     * Register the core class aliases in the container.
     *
     * @return void
     */
    public function registerContainerAliases()
    {
        $aliasList = [
            'redirect' => [\Illuminate\Routing\Redirector::class],
            'view' => [\Illuminate\View\Factory::class, \Illuminate\Contracts\View\Factory::class],
        ];

        foreach ($aliasList as $key => $aliases) {
            foreach ($aliases as $alias) {
                $this->alias($key, $alias);
            }
        }
    }

    public function setFacadeRoot()
    {
        Facade::setFacadeApplication($this);
    }

    public static function getDebugLevel(): int
    {
        $request = Request::capture();

        $debugLevel = $request->header('X-Glofox-Debug-Level', 0);

        if (!is_numeric($debugLevel)) {
            return 0;
        }

        return (int)($debugLevel);
    }

    public function enableSentry(): void
    {
        $sentryUrl = env('GLOFOX_SENTRY_URL');

        if (!$sentryUrl) {
            return;
        }

        Sentry\init([
            'dsn' => $sentryUrl,
            'environment' => env('OPENSHIFT_APP_NAME'),
            'attach_stacktrace' => true,
            'tags' => [
                'service_name' => env('SERVICE_NAME')
            ]
        ]);

        self::$sentryEnabled = true;
    }

    public function isSentryEnabled(): bool
    {
        return self::$sentryEnabled;
    }

    public function reboot(): void
    {
        $this->flush();

        $this->bootstrap();
        $this->bindPathsInContainer();
        $this->registerContainerAliases();
        $this->setFacadeRoot();
        $this->enableSentry();
    }

    public function flush(): void
    {
        parent::flush();

        $this->flushFacades();
        $this->flushProviders();
        $this->flushCallbacks();
        $this->flushStatics();
    }

    private function flushCallbacks(): void
    {
        $this->globalAfterResolvingCallbacks = [];
        $this->afterResolvingCallbacks = [];
        $this->resolvingCallbacks = [];
        $this->reboundCallbacks = [];
        $this->extenders = [];
    }

    private function flushFacades(): void
    {
        Facade::clearResolvedInstances();
    }

    private function flushStatics(): void
    {
        UserBranchRelationVerifier::$isValidToken = null;
    }
}
