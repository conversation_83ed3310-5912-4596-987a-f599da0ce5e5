<?php

declare(strict_types=1);

namespace Glofox\Infrastructure\Services\Reports\Requests;

use Psr\Http\Message\RequestInterface;

class GetDiscountInsightsRequest
{
    private string $branchId;

    private string $discountId;

    private \Psr\Http\Message\RequestInterface $request;

    public function __construct(
        string $branchId,
        string $discountId,
        RequestInterface $request
    ) {
        $this->branchId = $branchId;
        $this->discountId = $discountId;
        $this->request = $request;
    }

    public function branchId(): string
    {
        return $this->branchId;
    }

    public function discountId(): string
    {
        return $this->discountId;
    }

    public function getRequest(): RequestInterface
    {
        return $this->request;
    }
}
