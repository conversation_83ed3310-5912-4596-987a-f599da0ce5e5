<?php

use Glofox\Response\Models\Invoice\InvoiceModel;

class InvoiceModelTest extends GlofoxTestCase
{
    /**
     * @param $comparisonArray
     * @param $result
     * @dataProvider modelProvider
     */
    public function testModel($comparisonArray, $result)
    {
        $model = new InvoiceModel($comparisonArray);
        $this->assertEquals($model, $result);
    }

    public function modelProvider(): array
    {
        $time = strtotime('now');

        return [
            'Simple model, without address or amounts' => [
                [
                    'date' => $time,
                    'month' => 10,
                    'year' => 2017,
                    'number' => 10,
                    'account_number' => '42343fs34',
                    'vat_number' => '45234dasf',
                    'currency' => 'EUR',
                    'currency_symbol' => 'E',
                    'address' => null,
                    'amounts' => null,
                ],
                new InvoiceModel(
                    [
                        'date' => $time,
                        'month' => 10,
                        'year' => 2017,
                        'number' => 10,
                        'account_number' => '42343fs34',
                        'vat_number' => '45234dasf',
                        'currency' => 'EUR',
                        'currency_symbol' => 'E',
                        'address' => null,
                        'amounts' => null,
                    ]
                ),
            ],
        ];
    }
}
