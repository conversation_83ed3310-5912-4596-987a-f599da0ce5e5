<?php

namespace CakeTestCases\Glofox\Domain\Events\Events\Listeners;

use Carbon\Carbon;
use Glofox\Datasource\Exceptions\InvalidMongoIdException;
use Glofox\Domain\AsyncEvents\Events\EventDeletedEventMeta;
use Glofox\Domain\AsyncEvents\Events\EventDeletedEventPayload;
use Glofox\Domain\Branches\Exceptions\BranchNotFoundException;
use Glofox\Domain\Branches\Repositories\BranchesRepository;
use Glofox\Domain\Events\Events\EventWasDeleted;
use Glofox\Domain\Events\Events\Listeners\PublishEventWasDeletedListener;
use Glofox\Domain\Events\Exceptions\EventNotFoundException;
use Glofox\Domain\Events\Exceptions\InvalidEventException;
use Glofox\Domain\Events\Models\Event;
use Glofox\Domain\Events\Repositories\EventsRepository;
use Glofox\Domain\Events\Services\EventsPublisher;
use Glofox\Domain\Events\Status;
use Glofox\Domain\Events\Type;
use CakeTestCases\Glofox\Domain\Users\Traits\AuthenticateUsersTrait;

\App::import('Test/Case', 'GlofoxTestCase');

final class PublishEventWasDeletedListenerTest extends \GlofoxTestCase
{
    use AuthenticateUsersTrait;

    public $fixtures = ['app.event', 'app.branch', 'app.booking', 'app.user'];

    private EventsRepository $eventsRepository;
    private BranchesRepository $branchesRepository;

    public function setUp()
    {
        parent::setUp();

        $this->eventsRepository = app()->make(EventsRepository::class);
        $this->branchesRepository = app()->make(BranchesRepository::class);
    }

    public function tearDown(): void
    {
        parent::tearDown();

        \Mockery::close();
    }

    /**
     * @throws InvalidEventException
     * @throws BranchNotFoundException
     * @throws EventNotFoundException
     * @throws InvalidMongoIdException
     */
    public function test_domain_event_is_published(): void
    {
        $this->authenticateAsAdmin('111122223333444455556666');
        $eventsPublisher = \Mockery::mock(EventsPublisher::class);
        $eventsPublisher
            ->shouldReceive('sendEventDeletedEvent')
            ->withArgs(
                function (EventDeletedEventMeta $meta, EventDeletedEventPayload $payload) {
                    $correlation = $meta->correlation();
                    $data = $payload->jsonSerialize();

                    return
                        '666666777777888888000000' === $correlation['branchId'] &&
                        'test-branch-event-deleted' === $correlation['namespace'] &&
                        '386612' === $correlation['gympassProductId'] &&
                        '445014' === $correlation['gympassGymId'] &&
                        'gf-test-account' === $correlation['gympassClientId'] &&
                        'GYMPASS-CLIENT-SECRET' === $correlation['gympassClientSecret'] &&
                        '66aa128eb9f5579999990001' === $data['programId'] &&
                        '66aa128eb9f5579999990000' === $data['eventId'];
                }
            );

        $branch = $this->branchesRepository->getById('666666777777888888000000');
        $event = $this->eventsRepository->getById('66aa128eb9f5579999990000');

        $listener = new PublishEventWasDeletedListener($eventsPublisher);
        $listener->handle(new EventWasDeleted($event, $branch));
    }

    /**
     * @dataProvider handleDataProvider
     * @throws EventNotFoundException
     * @throws InvalidMongoIdException
     * @throws BranchNotFoundException
     * @throws InvalidEventException
     */
    public function testHandle(
        string $adminId,
        string $currentDate,
        string $branchId,
        ?Event $deletedEvent,
        string $eventId,
        array $expectedMeta,
        array $expectedPayload
    ): void
    {
        $this->authenticateAsAdmin($adminId);
        $eventsPublisher = \Mockery::mock(EventsPublisher::class);
        $eventsPublisher
            ->shouldReceive('sendEventDeletedEvent')
            ->withArgs(
                function (EventDeletedEventMeta $meta, EventDeletedEventPayload $payload) use ($expectedMeta, $expectedPayload) {
                    $this->assertEquals($expectedMeta, $meta->correlation());
                    $this->assertEquals($expectedPayload, $payload->jsonSerialize());
                    return true;
                }
            );

        Carbon::setTestNow($currentDate);
        $branch = $this->branchesRepository->getById($branchId);
        $event = is_null($deletedEvent) ? $this->eventsRepository->getById($eventId) : $deletedEvent;

        $listener = new PublishEventWasDeletedListener($eventsPublisher);
        $listener->handle(new EventWasDeleted($event, $branch));
    }

    public function handleDataProvider(): array
    {
        return [
            '1. The EVENT_DELETED payload is correct for an event that is still present in the database' => [
                'adminId' => '111122223333444455556666',
                'currentDate' => '2024-09-01T09:00:00.000000Z',
                'branchId' => '666666777777888888000000',
                'deletedEvent' => null,
                'eventId' => '66aa128eb9f5579999990000',
                'expectedMeta' => [
                    'branchId' => '666666777777888888000000',
                    'namespace' => 'test-branch-event-deleted',
                    'gympassProductId' => '386612',
                    'gympassGymId' => '445014',
                    'gympassClientId' => 'gf-test-account',
                    'gympassClientSecret' => 'GYMPASS-CLIENT-SECRET'
                ],
                'expectedPayload' => [
                    'eventId' => '66aa128eb9f5579999990000',
                    'programId' => '66aa128eb9f5579999990001',
                    'namespace' => 'test-branch-event-deleted',
                    'type' => Type::EVENT,
                    'active' => true,
                    'name' => 'Test Class',
                    'description' => 'Test Class Description',
                    'timeStart' => '2024-09-01T06:00:00.000000Z',
                    'timeFinish' => '2024-09-01T07:00:00.000000Z',
                    'isOnline' => false,
                    'size' => 10,
                    'private' => false,
                    'booked' => 2,
                    'waiting' => 0,
                    'level' => '',
                    'facility' => '',
                    'trainers' => [
                        '66aa128eb9f5579999990002'
                    ],
                    'created' => '2024-08-29T09:00:00.000000Z',
                    'modified' => '2024-08-29T09:00:00.000000Z',
                    'status' => Status::BOOKING_WINDOW_PASSED
                ]
            ],
            '2. The EVENT_DELETED payload is correct for an event that has been hard-removed from the database' => [
                'adminId' => '111122223333444455556666',
                'currentDate' => '2024-09-01T09:00:00.000000Z',
                'branchId' => '666666777777888888000000',
                'deletedEvent' => Event::make([
                    '_id' => '66aa128eb9f557999999ffff',
                    'branch_id' => '666666777777888888000000',
                    'program_id' => '66aa128eb9f5579999990001',
                    'namespace' => 'test-branch-event-deleted',
                    'type' => Type::EVENT,
                    'active' => true,
                    'name' => 'Test Class 2',
                    'description' => 'Test Class Description 2',
                    'time_start' => '2024-09-10T06:00:00.000000Z',
                    'time_finish' => '2024-09-10T07:00:00.000000Z',
                    'is_online' => false,
                    'size' => 10,
                    'private' => false,
                    'booked' => 0,
                    'waiting' => 0,
                    'level' => '',
                    'facility' => '',
                    'trainers' => [
                        '66aa128eb9f5579999990002'
                    ],
                    'created' => '2024-08-29T09:00:00.000000Z',
                    'modified' => '2024-08-29T09:00:00.000000Z',
                ]),
                'eventId' => '',
                'expectedMeta' => [
                    'branchId' => '666666777777888888000000',
                    'namespace' => 'test-branch-event-deleted',
                    'gympassProductId' => '386612',
                    'gympassGymId' => '445014',
                    'gympassClientId' => 'gf-test-account',
                    'gympassClientSecret' => 'GYMPASS-CLIENT-SECRET'
                ],
                'expectedPayload' => [
                    'eventId' => '66aa128eb9f557999999ffff',
                    'programId' => '66aa128eb9f5579999990001',
                    'namespace' => 'test-branch-event-deleted',
                    'type' => Type::EVENT,
                    'active' => true,
                    'name' => 'Test Class 2',
                    'description' => 'Test Class Description 2',
                    'timeStart' => '2024-09-10T06:00:00.000000Z',
                    'timeFinish' => '2024-09-10T07:00:00.000000Z',
                    'isOnline' => false,
                    'size' => 10,
                    'private' => false,
                    'booked' => 0,
                    'waiting' => 0,
                    'level' => '',
                    'facility' => '',
                    'trainers' => [
                        '66aa128eb9f5579999990002'
                    ],
                    'created' => '2024-08-29T09:00:00.000000Z',
                    'modified' => '2024-08-29T09:00:00.000000Z',
                    'status' => Status::AVAILABLE
                ]
            ]
        ];
    }
}
