<?php

use MongoDB\Database;

/**
 * <AUTHOR>
 */
class GlofoxTestFixture extends CakeTestFixture
{
    /**
     * @link https://www.mongodb.com/docs/manual/indexes/
     * @var array|int[index][field]sortOrder
     */
    protected $indexes = [];

    public function init()
    {
        parent::init();
        $this->createIndexes();
    }

    private function createIndexes(): void
    {
        if (!empty($this->indexes)) {
            $db = app()->make(Database::class);
            
            foreach ($this->indexes as $fields) {
                $db->selectCollection($this->table)
                    ->createIndex($fields, []);
            }
        }
    }
}
