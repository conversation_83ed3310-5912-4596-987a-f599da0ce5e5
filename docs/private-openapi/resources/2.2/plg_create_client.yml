post:
  tags:
    - ProductLedGrowth
  summary: ''
  operationId: plg-create-client
  description: 'This endpoint creates a client with information provided in the PLG flow'
  x-internal: true
  security:
    - none: [ ]
  requestBody:
    required: true
    content:
      application/json:
        schema:
          $ref: '../../requests/2.2/plg_create_client.yml'
  responses:
    '200':
      description: OK
      content:
        application/json:
          schema:
            $ref: '../../schemas/plg_create_client_outcome.yml'