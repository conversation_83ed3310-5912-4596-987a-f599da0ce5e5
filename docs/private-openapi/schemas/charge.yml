type: array
items:
  type: object
  properties:
    _id:
      type: object
      properties:
        $oid:
          type: string
    id:
      type: string
    transaction_status:
      type: string
    transaction_provider_id:
      type: string
    metadata:
      type: object
      properties:
        namespace:
          type: string
        branch_id:
          type: string
        glofox_event:
          type: string
        stripe_subscription_id:
          type: string
        user_id:
          type: string
        membership_id:
          type: string
        user_name:
          type: string
        environment:
          type: string
        payment_method:
          type: string
        plan_code:
          type: string
        resource_id:
          type: string
        already_paid:
          type: boolean
    amount:
      type: integer
    currency:
      type: string
    customer:
      type: string
    paid:
      type: boolean
    invoice_id:
      type: string
    created:
      type: object
      properties:
        $date:
          type: string
          format: date-time
    modified:
      type: object
      properties:
        $date:
          type: string
          format: date-time
    event_id:
      type: string
    description:
      type: string
    transaction_group_id:
      type: string
    amount_refunded:
      type: integer
