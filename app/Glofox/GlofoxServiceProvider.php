<?php

namespace Glofox;

\App::uses('ClassRegistry', 'Utility');
\App::uses('NotificationHelper', 'Lib');

use Aws\Rekognition\RekognitionClient;
use Aws\Sqs\SqsClient;
use Glofox\AuditLog\AuditLogBuilder;
use Glofox\AuditLog\Resolvers\OriginByRequestResolver;
use Glofox\Authentication\Jwt;
use Glofox\Authentication\PasswordHasher;
use Glofox\Calendar\CurrentYearConverter;
use Glofox\Datasource\MongoMaxQueryDurationThrottler;
use Glofox\Domain\Appointments\Locker\BookingAppointmentLocker;
use Glofox\Domain\AppointmentSlots\Locker\LockerPrefix;
use Glofox\Domain\AsyncEvents\Events\BookingCancelledEvent;
use Glofox\Domain\AsyncEvents\Events\BookingFinishedEvent;
use Glofox\Domain\AsyncEvents\Events\CancellationInBatchRequestedEvent;
use Glofox\Domain\AsyncEvents\Events\CourseBookingUpdateRequestProcessedEvent;
use Glofox\Domain\AsyncEvents\Events\EventBookingUpdateRequestProcessedEvent;
use Glofox\Domain\AsyncEvents\Events\EventCreatedEvent;
use Glofox\Domain\Bookings\Tasks\CancelCancelledMembershipBookings;
use Glofox\Domain\Bookings\Tasks\CancelPausedMembershipBookings;
use Glofox\Domain\Bookings\Tasks\DelayTask;
use Glofox\Domain\Communications\Services\CommunicationsClient;
use Glofox\Domain\Communications\UseCase\ModerateMessage;
use Glofox\Domain\Transactional\Tasks\SendNewBookingCreatedToMemberNotification;
use Glofox\Domain\AsyncEvents\Events\GrantCreditsEvent;
use Glofox\Domain\AsyncEvents\Events\MembershipActivatedEvent;
use Glofox\Domain\AsyncEvents\Events\MembershipLockedEvent;
use Glofox\Domain\AsyncEvents\Events\MembershipPaymentDatesMovedEvent;
use Glofox\Domain\AsyncEvents\Events\MembershipPaymentMethodUpdatedEvent;
use Glofox\Domain\AsyncEvents\Events\MembershipPaymentPriceUpdatedEvent;
use Glofox\Domain\AsyncEvents\Events\MembershipPurchasedEvent;
use Glofox\Domain\AsyncEvents\Events\MembershipRenewalFailureEvent;
use Glofox\Domain\AsyncEvents\Events\MembershipRenewalSuccessEvent;
use Glofox\Domain\AsyncEvents\Events\MembershipTransferEvent;
use Glofox\Domain\AsyncEvents\Events\MembershipUnlockedEvent;
use Glofox\Domain\AsyncEvents\Events\MembershipUnpausedEvent;
use Glofox\Domain\AsyncEvents\Events\ProgramUpdatedEvent;
use Glofox\Domain\AsyncEvents\Events\RecurrentBookingRequestedEvent;
use Glofox\Domain\AsyncEvents\Events\ReservationBookingRequestedEvent;
use Glofox\Domain\AsyncEvents\Events\ReservationCancellationRequestedEvent;
use Glofox\Domain\AsyncEvents\Events\ReservationConfirmationRequestedEvent;
use Glofox\Domain\AsyncEvents\Events\TimeSlotBookingUpdateRequestProcessedEvent;
use Glofox\Domain\AsyncEvents\Events\TrackingEvent;
use Glofox\Domain\AsyncEvents\Events\TransactionFinishedEvent;
use Glofox\Domain\AsyncEvents\Events\TransactionRefundedEvent;
use Glofox\Domain\AsyncEvents\Events\UserMembershipPausedEvent;
use Glofox\Domain\AsyncEvents\Events\UserMembershipPauseUpdatedEvent;
use Glofox\Domain\AsyncEvents\Events\UserMembershipUpdatedEvent;
use Glofox\Domain\AsyncEvents\Handlers\BookingFinishedEventHandler;
use Glofox\Domain\AsyncEvents\Handlers\CancellationInBatchRequestedEventHandler;
use Glofox\Domain\AsyncEvents\Handlers\CourseBookingUpdateRequestProcessedEventHandler;
use Glofox\Domain\AsyncEvents\Handlers\EventBookingUpdateRequestProcessedEventHandler;
use Glofox\Domain\AsyncEvents\Handlers\EventCreatedEventHandler;
use Glofox\Domain\AsyncEvents\Handlers\GrantCreditsEventHandler;
use Glofox\Domain\AsyncEvents\Handlers\ItemPurchaseUpdatedHandler;
use Glofox\Domain\AsyncEvents\Handlers\MembershipActivatedEventHandler;
use Glofox\Domain\AsyncEvents\Handlers\MembershipLockedEventHandler;
use Glofox\Domain\AsyncEvents\Handlers\MembershipPaymentDatesMovedEventHandler;
use Glofox\Domain\AsyncEvents\Handlers\MembershipPaymentMethodUpdatedEventHandler;
use Glofox\Domain\AsyncEvents\Handlers\MembershipPaymentPriceUpdatedEventHandler;
use Glofox\Domain\AsyncEvents\Handlers\MembershipPurchasedEventHandler;
use Glofox\Domain\AsyncEvents\Handlers\MembershipRenewalFailureEventHandler;
use Glofox\Domain\AsyncEvents\Handlers\MembershipRenewalSuccessEventHandler;
use Glofox\Domain\AsyncEvents\Handlers\MembershipTransferEventHandler;
use Glofox\Domain\AsyncEvents\Handlers\MembershipUnlockedEventHandler;
use Glofox\Domain\AsyncEvents\Handlers\MembershipUnpausedEventHandler;
use Glofox\Domain\AsyncEvents\Handlers\ProgramUpdatedEventHandler;
use Glofox\Domain\AsyncEvents\Handlers\RecurrentBookingRequestedEventHandler;
use Glofox\Domain\AsyncEvents\Handlers\ReservationBookingRequestedEventHandler;
use Glofox\Domain\AsyncEvents\Handlers\ReservationCancellationRequestedEventHandler;
use Glofox\Domain\AsyncEvents\Handlers\ReservationConfirmationRequestedEventHandler;
use Glofox\Domain\AsyncEvents\Handlers\TimeSlotBookingUpdateRequestProcessedEventHandler;
use Glofox\Domain\AsyncEvents\Handlers\TrackEventRequestedHandler;
use Glofox\Domain\AsyncEvents\Handlers\TransactionFinishedEventHandler;
use Glofox\Domain\AsyncEvents\Handlers\TransactionRefundedEventHandler;
use Glofox\Domain\AsyncEvents\Handlers\UserMembershipPausedEventHandler;
use Glofox\Domain\AsyncEvents\Handlers\UserMembershipPauseUpdatedEventHandler;
use Glofox\Domain\AsyncEvents\Handlers\UserMembershipUpdatedEventHandler;
use Glofox\Domain\AsyncEvents\Processors\BookingCancelledProcessor;
use Glofox\Domain\AsyncEvents\Processors\EventsErrorProcessor;
use Glofox\Domain\Authentication\Repositories\InvalidTokensRepository;
use Glofox\Domain\Authentication\Token\Parser;
use Glofox\Domain\Authentication\Token\TokenUpdater;
use Glofox\Domain\Authentication\Token\TokenValidator;
use Glofox\Domain\Authentication\Token\Verification\BlacklistVerifier;
use Glofox\Domain\Authentication\TokenGenerator;
use Glofox\Domain\BookingRequests\Repositories\BookingRequestsRepository;
use Glofox\Domain\BookingRequests\Services\BookingUpdateRequestEventPublisher;
use Glofox\Domain\BookingRequests\Services\BookingUpdateRequestEventPublisherInterface;
use Glofox\Domain\Bookings\Cancellation\BookingCancellatorInterface;
use Glofox\Domain\Bookings\Cancellation\LegacyInternalBookingCancellator;
use Glofox\Domain\Bookings\Events\Listeners\BookingCancellationMessageSending;
use Glofox\Domain\Bookings\Events\Listeners\UserRequestedBookingCancelling;
use Glofox\Domain\Bookings\Repositories\BookingsRepository;
use Glofox\Domain\Bookings\Services\BookingsPublisher;
use Glofox\Domain\Bookings\Tasks\CancelBookingsByBatchId;
use Glofox\Domain\Bookings\Tasks\CancelUnconfirmedBooking;
use Glofox\Domain\Bookings\Tasks\ConfirmUnconfirmedBooking;
use Glofox\Domain\Transactional\Tasks\SendNotificationBookingUpdated;
use Glofox\Domain\Bookings\Tasks\ValidateReservation;
use Glofox\Domain\BookingSlots\Tasks\ProcessRecurringBookingsByBatchId;
use Glofox\Domain\Branches\Repositories\BranchesRepository;
use Glofox\Domain\Cards\Managers\CardManager;
use Glofox\Domain\Cards\Repositories\CardsRepository;
use Glofox\Domain\Charges\Commands\SendReceiptHandler;
use Glofox\Domain\Charges\Repositories\ChargesRepository;
use Glofox\Domain\Charges\Tasks\FormatChargeDescription;
use Glofox\Domain\Clients\Legacy\LegacyCreateClientHandler;
use Glofox\Domain\Clients\Repositories\ClientsRepository;
use Glofox\Domain\Communications\Services\CommunicationsPublisher;
use Glofox\Domain\Credits\Factories\CreditPackSourceFactory;
use Glofox\Domain\Credits\Repositories\CreditsRepository;
use Glofox\Domain\Credits\Source\SourceIsCancelledBookingMarker;
use Glofox\Domain\Credits\Source\SourceIsImportedMarker;
use Glofox\Domain\Credits\Source\SourceIsManuallyAddedByStaffMarker;
use Glofox\Domain\Credits\Source\SourceIsMembershipPurchasedMarker;
use Glofox\Domain\Credits\Validation\Rules\FirstBookingCreditDates;
use Glofox\Domain\Credits\Validation\Schema\CompositeCreditPackSchemaValidator;
use Glofox\Domain\Credits\Validation\Schema\CreditPackSourceAdvancedWhenBookingSchemaValidator;
use Glofox\Domain\Credits\Validation\Schema\CreditPackSourceCancelledBookingSchemaValidator;
use Glofox\Domain\Credits\Validation\Schema\CreditPackSourceManuallyAddedByStaffSchemaValidator;
use Glofox\Domain\Credits\Validation\Schema\CreditPackSourceMembershipPurchasedSchemaValidator;
use Glofox\Domain\Credits\Validation\Schema\CreditPackSourceMembershipRenewedSchemaValidator;
use Glofox\Domain\Credits\Validation\Validator as CreditsValidator;
use Glofox\Domain\ElectronicAgreements\Services\ElectronicAgreementsServiceInterface;
use Glofox\Domain\Events\Tasks\CalculateEventBookingTotals;
use Glofox\Domain\Events\Tasks\UpdateEventsBasedOnProgramChange;
use Glofox\Domain\EventTrackers\Services\TrackEventPublisher;
use Glofox\Domain\EventTrackers\Tasks\AddEventIdToReservation;
use Glofox\Domain\EventTrackers\Tasks\TrackEvent;
use Glofox\Domain\EventTrackers\TrackerInterface;
use Glofox\Domain\Facilities\Repositories\FacilitiesRepository;
use Glofox\Domain\Interactions\Repositories\InteractionsRepository;
use Glofox\Domain\Interactions\Transformers\InteractionTypeTransformer;
use Glofox\Domain\Leads\Repositories\LeadsRepository;
use Glofox\Domain\Locker\ApiWorkerTaskLockerInterface;
use Glofox\Domain\Locker\BookingEventLockerInterface;
use Glofox\Domain\Locker\EventBookingLockerInterface;
use Glofox\Domain\Locker\PaymentMethodOnboardingLockerInterface;
use Glofox\Domain\Locker\SetUpIntentLockerInterface;
use Glofox\Domain\Locker\WebhookLockerInterface;
use Glofox\Domain\Memberships\Services\AddonServiceInterface;
use Glofox\Domain\Memberships\Tasks\UpdateMembershipBasedOnBooking;
use Glofox\Domain\Memberships\Validation\Validator as MembershipsValidator;
use Glofox\Domain\MigrationDataPool\Repositories\MigrationDataPoolRepository;
use Glofox\Domain\MigrationDataPool\Repositories\MigrationMappingRepository;
use Glofox\Domain\MigrationDataPool\Repositories\MigrationRepositoryInterface;
use Glofox\Domain\MigrationDataPool\Service\CreateMigrationDataPoolServiceInterface;
use Glofox\Domain\MigrationDataPool\Service\CreateMigrationDataPoolServiceV1;
use Glofox\Domain\MigrationDataPool\Service\CreateMigrationDataPoolServiceV2;
use Glofox\Domain\MigrationDataPool\Service\CreateMigrationDataPoolServiceVersionHandler;
use Glofox\Domain\MigrationDataPool\Service\DeleteMigrationDataPoolServiceInterface;
use Glofox\Domain\MigrationDataPool\Service\DeleteMigrationDataPoolServiceV1;
use Glofox\Domain\MigrationDataPool\Service\DeleteMigrationDataPoolServiceV2;
use Glofox\Domain\MigrationDataPool\Service\DeleteMigrationDataPoolServiceVersionHandler;
use Glofox\Domain\MigrationDataPool\Service\MappingFinderService;
use Glofox\Domain\MigrationDataPool\Service\RetrieveMigrationDataPoolServiceInterface;
use Glofox\Domain\MigrationDataPool\Service\RetrieveMigrationDataPoolServiceV1;
use Glofox\Domain\MigrationDataPool\Service\RetrieveMigrationDataPoolServiceV2;
use Glofox\Domain\MigrationDataPool\Service\RetrieveMigrationDataPoolServiceVersionHandler;
use Glofox\Domain\MigrationDataPool\Service\UpdateMigrationDataPoolServiceInterface;
use Glofox\Domain\MigrationDataPool\Service\UpdateMigrationDataPoolServiceV1;
use Glofox\Domain\MigrationDataPool\Service\UpdateMigrationDataPoolServiceV2;
use Glofox\Domain\MigrationDataPool\Service\UpdateMigrationDataPoolServiceVersionHandler;
use Glofox\Domain\MigrationDataPool\Service\UserForeignIdFinderServiceInterface;
use Glofox\Domain\MigrationDataPool\Service\UserForeignIdFinderServiceV1;
use Glofox\Domain\MigrationDataPool\Service\UserForeignIdFinderServiceV2;
use Glofox\Domain\MigrationDataPool\Service\UserForeignIdFinderServiceVersionHandler;
use Glofox\Domain\Notifications\Services\PushNotificationsServiceInterface;
use Glofox\Domain\Programs\Validation\Validator as ProgramsValidator;
use Glofox\Domain\Region\ContinentResolver;
use Glofox\Domain\SalesTaxes\Tasks\AddMemberTaxId;
use Glofox\Domain\SalesTaxes\Tasks\AddPriceBreakdownData;
use Glofox\Domain\Store\Sales\Repositories\SalesRepository as StoreSalesRepository;
use Glofox\Domain\Terms\Repositories\TermsRepository;
use Glofox\Domain\Transactional\Filters\OnlineBookingConfirmationTemplateFilter;
use Glofox\Domain\Transactional\MessageDispatcher;
use Glofox\Domain\Transactional\Repositories\TransactionalMessagesRepository;
use Glofox\Domain\Transactional\Sender;
use Glofox\Domain\Transactional\Tasks\SendBookingConfirmationTask;
use Glofox\Domain\Transactional\Tasks\SendNewBookingCreatedToStaffsNotification;
use Glofox\Domain\Transactional\Tasks\SendReceipt;
use Glofox\Domain\Transactional\Tasks\SendRefundEmail;
use Glofox\Domain\Transactional\Tasks\SendWaitingListSpotNoLongerAvailableNotification;
use Glofox\Domain\Transactional\Templates\Factory as TemplateFactory;
use Glofox\Domain\Transactional\Templates\TemplateManager;
use Glofox\Domain\Transactional\Validation\Validator as TransactionalMessageValidator;
use Glofox\Domain\Translation\Translator;
use Glofox\Domain\UserCreditsHistory\Repositories\UserCreditsHistoryRepository;
use Glofox\Domain\Users\MemberTransfer\Guard\CompositeIncomingMemberTransferDataOnUserCreationGuard;
use Glofox\Domain\Users\MemberTransfer\Guard\IncomingMemberTransferDataOnUserCreationGuard;
use Glofox\Domain\Users\MemberTransfer\Guard\MemberTransferDataAuthorizationGuard;
use Glofox\Domain\Users\MemberTransfer\Guard\MemberTransferDataContentGuard;
use Glofox\Domain\Users\Repositories\UsersRepository;
use Glofox\Domain\Users\Requests\SaveUser;
use Glofox\Domain\Users\Services\Avatar\AvatarUrlFactory;
use Glofox\Domain\Users\Services\Avatar\ImageModeratorClientInterface;
use Glofox\Domain\Users\Services\Avatar\RekognitionImageModeratorClient;
use Glofox\Domain\Users\Services\UsersPublisher;
use Glofox\Domain\Users\Tasks\UpdateUserMembership;
use Glofox\Domain\Users\Validation\Rules\GenderValidator;
use Glofox\Eventkit\Consumer\DomainEventConsumer;
use Glofox\Eventkit\Converter\SQSResultMessageToDomainEventConverter;
use Glofox\Eventkit\Publisher\DomainEventPublisher;
use Glofox\Eventkit\SQS\SQSConsumer;
use Glofox\Eventkit\SQS\SQSErrorHandler;
use Glofox\Eventkit\SQS\SQSMessageHandler;
use Glofox\Formatter\DockerJsonFormatter;
use Glofox\Http\InternalCakeRequestRouter;
use Glofox\Http\InternalCakeRequestRouterInterface;
use Glofox\Http\Requests\CurrentRequestIdService;
use Glofox\Http\Requests\CurrentRequestIdServiceInterface;
use Glofox\Http\Requests\ExistingRequestIdResolver;
use Glofox\Http\Responses\JsonPostEncodingErrorValidator;
use Glofox\Http\Responses\ResponseMiddlewarePipelineService;
use Glofox\Infrastructure\Cache\CachePrefix;
use Glofox\Infrastructure\Cache\PredisClientFactory;
use Glofox\Infrastructure\Cache\RedisCacheClientFactory;
use Glofox\Infrastructure\Email\CakeEmailSender;
use Glofox\Infrastructure\Email\EmailSenderInterface;
use Glofox\Infrastructure\Flags\Flagger;
use Glofox\Infrastructure\Flags\Flaggers\IrisGlofoxDigitalFlagger;
use Glofox\Infrastructure\Honeycomb\HoneycombTracker;
use Glofox\Infrastructure\Locker\ApiWorkerTaskLocker;
use Glofox\Infrastructure\Locker\BookingEventLocker;
use Glofox\Infrastructure\Locker\CacheWebhookLocker;
use Glofox\Infrastructure\Locker\PaymentMethodOnboardingLocker;
use Glofox\Infrastructure\Locker\RecurrentBookingLocker;
use Glofox\Infrastructure\Locker\RecurrentBookingLockerInterface;
use Glofox\Infrastructure\Locker\SetUpIntentLocker;
use Glofox\Infrastructure\Logger\Processor\CurrentRequestIdProcessor;
use Glofox\Infrastructure\PushNotifications\PushNotificationsHttpClientNew;
use Glofox\Infrastructure\Tracker\MixpanelTracker;
use Glofox\Log\DefaultObfuscator;
use Glofox\Log\ObfuscatorInterface;
use Glofox\MessageLibs\Consumer\Fulfilment\FulfilmentCoreapiConsumer;
use Glofox\MessageLibs\Infra\SQS\ConsumerConfiguration;
use Glofox\Models\Injector\EnumerableValueInjector;
use Glofox\Payments\Providers\Gateway\GRPC\Connection;
use Glofox\Repositories\Search\Expressions\MongoOperatorResolver;
use Glofox\Traversable\Hydrators\MethodHydrator;
use Glofox\Traversable\Hydrators\ModelHydrator;
use Glofox\Traversable\Hydrators\PropertyHydrator;
use Glofox\Validation\Rules\Shared\AnnouncementValidator;
use Glofox\Validation\Rules\Shared\FacilityValidator;
use Glofox\Validation\Rules\Shared\MembershipPlanValidator;
use Glofox\Validation\Rules\Shared\MongoIdValidator;
use Glofox\Validation\Rules\Shared\TimeStampValidator;
use Glofox\Validation\Rules\Shared\TimeValidator;
use Glofox\Validation\Rules\Shared\UserValidator;
use GuzzleHttp\Client as HttpClient;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Validator as IlluminateValidator;
use Illuminate\Support\ServiceProvider;
use League\Event\Emitter;
use Mixpanel;
use MongoDB\Database;
use MongoDB\Driver\ReadConcern;
use Monolog\Handler\StreamHandler;
use Monolog\Logger;
use Monolog\Processor\WebProcessor;
use Psr\Log\LoggerInterface;
use PushNotification;
use Ramsey\Uuid\Uuid;
use Ramsey\Uuid\UuidFactoryInterface;
use UserCreditsHistory;

class GlofoxServiceProvider extends ServiceProvider
{
    /**
     * Register bindings in the container.
     */
    public function register()
    {
        $this->app->singleton(MongoOperatorResolver::class);

        $this->registerMongoMaxQueryDurationThrottler();

        $this->app->bind(\TermsCondition::class, fn () => \ClassRegistry::init('TermsCondition'));

        $this->app->bind(LeadsRepository::class, function (Application $app) {
            $db = $app->make(Database::class);
            $collection = $db
                ->selectCollection('users')
                ->withOptions([
                    'readConcern' => new ReadConcern(ReadConcern::AVAILABLE),
                ]);
            return new LeadsRepository($collection);
        });

        $this->app->bind(InteractionsRepository::class, fn () => new InteractionsRepository());

        $this->app->bind(MethodHydrator::class, fn () => new MethodHydrator());

        $this->app->bind(ModelHydrator::class, fn () => new ModelHydrator());

        $this->app->bind(PropertyHydrator::class, fn () => new PropertyHydrator());

        $this->app->singleton(InteractionTypeTransformer::class, fn () => new InteractionTypeTransformer());

        $this->app->bind(TransactionalMessagesRepository::class, fn () => new TransactionalMessagesRepository());

        $this->app->bind(TransactionalMessageValidator::class, function (Application $app) {
            $validatorFactory = $app->make('validator');

            return new TransactionalMessageValidator($validatorFactory);
        });

        $this->app->bind(TemplateFactory::class, fn () => new TemplateFactory());

        $this->app->bind(TemplateManager::class, fn () => new TemplateManager());

        $this->app->bind(Sender::class, fn () => new Sender(
            app()->make(CommunicationsPublisher::class),
            app()->make(LoggerInterface::class)
        ));

        $this->app->bind(MessageDispatcher::class, fn (Application $app) => new MessageDispatcher(
            $app->make(TemplateManager::class),
            $app->make(TemplateFactory::class),
            $app->make(Sender::class),
            $app->make(Translator::class)
        ));

        // ActivityServiceProvider
        $this->app->bind(AuditLogBuilder::class);

        $this->app->singleton(OriginByRequestResolver::class);

        // ProgramsServiceProvider
        $this->app->bind(ProgramsValidator::class, function (Application $app) {
            $validatorFactory = $app->make('validator');

            return new ProgramsValidator($validatorFactory);
        });

        $this->app->bind(CardManager::class, function (Application $app) {
            $cardsRepository = $app->make(CardsRepository::class);
            $usersRepository = $app->make(UsersRepository::class);

            return new CardManager($cardsRepository, $usersRepository);
        });

        // AuthenticationServiceProvider
        $this->app->bind(TokenGenerator::class, function (Application $app) {
            $jwt = $app->make(Jwt::class);
            $salt = env('JWT_SALT_GENERATE');

            return new TokenGenerator($jwt, $salt);
        });

        $this->app->bind(BlacklistVerifier::class, function (Application $app) {
            $jwt = $app->make(Jwt::class);
            $invalidTokensRepository = $app->make(InvalidTokensRepository::class);
            $salts = explode(',', env('JWT_SALT_VERIFY'));

            return new BlacklistVerifier($jwt, $invalidTokensRepository, $salts);
        });

        $this->app->bind(TokenValidator::class, function (Application $app) {
            $jwt = $app->make(Jwt::class);
            $parser = $app->make(Parser::class);
            $salts = explode(',', env('JWT_SALT_VERIFY'));
            $honeycombTracker = $app->make(HoneycombTracker::class);

            return new TokenValidator($jwt, $parser, $salts, $honeycombTracker);
        });

        $this->app->bind(
            LegacyCreateClientHandler::class,
            fn (Application $app): LegacyCreateClientHandler => new LegacyCreateClientHandler(
                $app->make(ElectronicAgreementsServiceInterface::class),
                $app->make(ClientsRepository::class),
                $app->make(PasswordHasher::class),
                $app->make(UsersPublisher::class),
                $app->make(\TermsCondition::class),
                $app->make(\Facility::class),
                $app->make(\Client::class),
                $app->make(\Branch::class),
                $app->make(\User::class),
                $app->make(LoggerInterface::class)
            )
        );

        $this->app->bind(TokenUpdater::class, function (Application $app) {
            $jwt = $app->make(Jwt::class);
            $parser = $app->make(Parser::class);
            $tokenGenerator = $app->make(TokenGenerator::class);
            $oldSalt = explode(',', env('JWT_SALT_VERIFY'))[0];
            $honeycombTracker = $app->make(HoneycombTracker::class);

            return new TokenUpdater($jwt, $parser, $tokenGenerator, $honeycombTracker, $oldSalt);
        });

        // RegionServiceProvider
        $this->app->singleton(ContinentResolver::class);

        // RegionServiceProvider
        $this->app->singleton(ContinentResolver::class);

        // PushMessagesServiceProvider
        $this->app->singleton('push-message-bag', fn () => new Collection());

        // CakeModelsServiceProvider
        $this->app->bind(\Branch::class, fn () => \ClassRegistry::init('Branch'));

        $this->app->bind(\Membership::class, fn () => \ClassRegistry::init('Membership'));

        $this->app->bind(\StripeCharge::class, fn () => \ClassRegistry::init('StripeCharge'));

        $this->app->bind(\PriceBreakdown::class, fn () => \ClassRegistry::init('PriceBreakdown'));

        $this->app->bind(\UserCredit::class, fn () => \ClassRegistry::init('UserCredit'));

        $this->app->bind(\UserCreditsHistory::class, fn() => \ClassRegistry::init('UserCreditsHistory'));

        $this->app->bind(\TimeSlot::class, fn () => \ClassRegistry::init('TimeSlot'));

        $this->app->bind(\TimeSlotPattern::class, fn () => \ClassRegistry::init('TimeSlotPattern'));

        $this->app->bind(\Event::class, fn () => \ClassRegistry::init('Event'));

        $this->app->bind(\Booking::class, fn () => \ClassRegistry::init('Booking'));

        $this->app->bind(\Product::class, fn () => \ClassRegistry::init('Product'));

        $this->app->bind(\StoreSales::class, fn () => \ClassRegistry::init(\StoreSales::class));

        $this->app->bind(\Program::class, fn () => \ClassRegistry::init('Program'));

        $this->app->bind(\Course::class, fn () => \ClassRegistry::init('Course'));

        $this->app->bind(\Booking::class, fn () => \ClassRegistry::init('Booking'));

        $this->app->bind(\BookingSpot::class, fn () => \ClassRegistry::init('BookingSpot'));

        $this->app->bind(\BookingSlot::class, fn () => \ClassRegistry::init(\BookingSlot::class));

        $this->app->bind(\Access::class, fn () => \ClassRegistry::init('Access'));

        $this->app->bind(\InvalidToken::class, fn () => \ClassRegistry::init('InvalidToken'));

        $this->app->bind(\SubscriptionPlan::class, fn () => \ClassRegistry::init('SubscriptionPlan'));

        $this->app->bind(\BookingCancellation::class, fn () => \ClassRegistry::init('BookingCancellation'));

        $this->app->bind(\NotificationHelper::class, fn () => new \NotificationHelper());

        $this->app->bind(\S3Component::class, function () {
            $componentCollection = new \ComponentCollection();
            $component = $componentCollection->load('S3');
            $component->initialize(new \AppController());

            return $component;
        });

        $this->app->bind(CurrentYearConverter::class, fn () => new CurrentYearConverter());

        $this->app->singleton('executionLogsCollection', fn () => new Collection());

        $this->app->bind(CreditsRepository::class, fn (Application $app) => new CreditsRepository(
            $app->make(\UserCredit::class)
        ));

        $this->app->bind(UserCreditsHistoryRepository::class, fn(Application $app) => new UserCreditsHistoryRepository(
            $app->make(\UserCreditsHistory::class)
        ));

        // MembershipsServiceProvider
        $this->app->bind(MembershipsValidator::class, function (Application $app) {
            $validatorFactory = $app->make('validator');

            return new MembershipsValidator($validatorFactory);
        });

        // CreditsServiceProvider
        $this->app->bind(CreditsValidator::class, function (Application $app) {
            $validatorFactory = $app->make('validator');

            return new CreditsValidator($validatorFactory);
        });

        // TermsAndConditionsServiceProvider
        $this->app->bind(TermsRepository::class, fn (Application $app) => new TermsRepository());

        $this->app->bind(StoreSalesRepository::class, fn (Application $app) => new StoreSalesRepository());

        $this->app->singleton(
            BookingCancellatorInterface::class,
            fn (Application $app) => new LegacyInternalBookingCancellator($app->make(\Booking::class))
        );

        $this->app->singleton(Emitter::class, fn () => new Emitter());

        $this->app->singleton(
            SQSResultMessageToDomainEventConverter::class,
            fn () => new SQSResultMessageToDomainEventConverter()
        );

        $this->app->singleton(SQSErrorHandler::class, fn (Application $app) => new SQSErrorHandler(
            $app->make(Emitter::class)
        ));

        $this->app->singleton(
            SQSMessageHandler::class . '[' . BookingCancelledEvent::class . ']',
            fn (Application $app) => new SQSMessageHandler(
                $app->make(SQSResultMessageToDomainEventConverter::class),
                $app->make(Emitter::class),
                'BOOKING_CANCELLED',
                BookingCancelledEvent::class
            )
        );

        $this->app->singleton(
            SQSMessageHandler::class . '[' . ProgramUpdatedEvent::class . ']',
            fn (Application $app) => new SQSMessageHandler(
                $app->make(SQSResultMessageToDomainEventConverter::class),
                $app->make(Emitter::class),
                'PROGRAM_UPDATED',
                ProgramUpdatedEvent::class
            )
        );

        $this->app->singleton(
            SQSMessageHandler::class . '[' . CourseBookingUpdateRequestProcessedEvent::class . ']',
            fn (Application $app) => new SQSMessageHandler(
                $app->make(SQSResultMessageToDomainEventConverter::class),
                $app->make(Emitter::class),
                'COURSE_BOOKING_UPDATE_REQUEST_PROCESSED',
                CourseBookingUpdateRequestProcessedEvent::class
            )
        );

        $this->app->singleton(
            SQSMessageHandler::class . '[' . TimeSlotBookingUpdateRequestProcessedEvent::class . ']',
            fn (Application $app) => new SQSMessageHandler(
                $app->make(SQSResultMessageToDomainEventConverter::class),
                $app->make(Emitter::class),
                'TIME_SLOT_BOOKING_UPDATE_REQUEST_PROCESSED',
                TimeSlotBookingUpdateRequestProcessedEvent::class
            )
        );

        $this->app->singleton(
            SQSMessageHandler::class . '[' . EventBookingUpdateRequestProcessedEvent::class . ']',
            fn (Application $app) => new SQSMessageHandler(
                $app->make(SQSResultMessageToDomainEventConverter::class),
                $app->make(Emitter::class),
                'EVENT_BOOKING_UPDATE_REQUEST_PROCESSED',
                EventBookingUpdateRequestProcessedEvent::class
            )
        );

        $this->app->singleton(
            SQSMessageHandler::class . '[' . TrackingEvent::class . ']',
            fn (Application $app) => new SQSMessageHandler(
                $app->make(SQSResultMessageToDomainEventConverter::class),
                $app->make(Emitter::class),
                'TRACK_EVENT_REQUESTED',
                TrackingEvent::class
            )
        );

        $this->app->singleton(
            SQSMessageHandler::class . '[' . TransactionFinishedEvent::class . ']',
            fn (Application $app) => new SQSMessageHandler(
                $app->make(SQSResultMessageToDomainEventConverter::class),
                $app->make(Emitter::class),
                'TRANSACTION_FINISHED',
                TransactionFinishedEvent::class
            )
        );

        $this->app->singleton(
            SQSMessageHandler::class . '[' . ReservationCancellationRequestedEvent::class . ']',
            fn (Application $app) => new SQSMessageHandler(
                $app->make(SQSResultMessageToDomainEventConverter::class),
                $app->make(Emitter::class),
                ReservationCancellationRequestedEvent::NAME,
                ReservationCancellationRequestedEvent::class
            )
        );

        $this->app->singleton(
            SQSMessageHandler::class . '[' . ReservationConfirmationRequestedEvent::class . ']',
            fn (Application $app) => new SQSMessageHandler(
                $app->make(SQSResultMessageToDomainEventConverter::class),
                $app->make(Emitter::class),
                ReservationConfirmationRequestedEvent::NAME,
                ReservationConfirmationRequestedEvent::class
            )
        );

        $this->app->singleton(
            SQSMessageHandler::class . '[' . ReservationBookingRequestedEvent::class . ']',
            fn (Application $app) => new SQSMessageHandler(
                $app->make(SQSResultMessageToDomainEventConverter::class),
                $app->make(Emitter::class),
                ReservationBookingRequestedEvent::NAME,
                ReservationBookingRequestedEvent::class
            )
        );

        $this->app->singleton(
            SQSMessageHandler::class . '[' . TransactionRefundedEvent::class . ']',
            fn (Application $app) => new SQSMessageHandler(
                $app->make(SQSResultMessageToDomainEventConverter::class),
                $app->make(Emitter::class),
                'TRANSACTION_REFUNDED',
                TransactionRefundedEvent::class
            )
        );

        $this->app->singleton(
            SQSMessageHandler::class . '[' . UserMembershipPausedEvent::class . ']',
            fn (Application $app) => new SQSMessageHandler(
                $app->make(SQSResultMessageToDomainEventConverter::class),
                $app->make(Emitter::class),
                UserMembershipPausedEvent::EVENT_NAME,
                UserMembershipPausedEvent::class
            )
        );

        $this->app->singleton(
            SQSMessageHandler::class . '[' . UserMembershipPauseUpdatedEvent::class . ']',
            fn (Application $app) => new SQSMessageHandler(
                $app->make(SQSResultMessageToDomainEventConverter::class),
                $app->make(Emitter::class),
                UserMembershipPauseUpdatedEvent::EVENT_NAME,
                UserMembershipPauseUpdatedEvent::class
            )
        );

        $this->app->singleton(
            SQSMessageHandler::class . '[' . UserMembershipUpdatedEvent::class . ']',
            fn (Application $app) => new SQSMessageHandler(
                $app->make(SQSResultMessageToDomainEventConverter::class),
                $app->make(Emitter::class),
                UserMembershipUpdatedEvent::EVENT_NAME,
                UserMembershipUpdatedEvent::class
            )
        );

        $this->app->singleton(
            SQSMessageHandler::class . '[' . MembershipPaymentDatesMovedEvent::class . ']',
            fn (Application $app) => new SQSMessageHandler(
                $app->make(SQSResultMessageToDomainEventConverter::class),
                $app->make(Emitter::class),
                MembershipPaymentDatesMovedEvent::EVENT_NAME,
                MembershipPaymentDatesMovedEvent::class
            )
        );

        $this->app->singleton(
            SQSMessageHandler::class . '[' . MembershipPurchasedEvent::class . ']',
            fn (Application $app) => new SQSMessageHandler(
                $app->make(SQSResultMessageToDomainEventConverter::class),
                $app->make(Emitter::class),
                MembershipPurchasedEvent::EVENT_NAME,
                MembershipPurchasedEvent::class
            )
        );

        $this->app->singleton(
            SQSMessageHandler::class . '[' . GrantCreditsEvent::class . ']',
            fn (Application $app) => new SQSMessageHandler(
                $app->make(SQSResultMessageToDomainEventConverter::class),
                $app->make(Emitter::class),
                GrantCreditsEvent::EVENT_NAME,
                GrantCreditsEvent::class
            )
        );

        $this->app->singleton(
            SQSMessageHandler::class . '[' . MembershipActivatedEvent::class . ']',
            fn (Application $app) => new SQSMessageHandler(
                $app->make(SQSResultMessageToDomainEventConverter::class),
                $app->make(Emitter::class),
                MembershipActivatedEvent::EVENT_NAME,
                MembershipActivatedEvent::class
            )
        );

        $this->app->singleton(
            SQSMessageHandler::class . '[' . MembershipUnpausedEvent::class . ']',
            fn (Application $app) => new SQSMessageHandler(
                $app->make(SQSResultMessageToDomainEventConverter::class),
                $app->make(Emitter::class),
                MembershipUnpausedEvent::EVENT_NAME,
                MembershipUnpausedEvent::class
            )
        );

        $this->app->singleton(
            SQSMessageHandler::class . '[' . MembershipPaymentPriceUpdatedEvent::class . ']',
            fn (Application $app) => new SQSMessageHandler(
                $app->make(SQSResultMessageToDomainEventConverter::class),
                $app->make(Emitter::class),
                MembershipPaymentPriceUpdatedEvent::EVENT_NAME,
                MembershipPaymentPriceUpdatedEvent::class
            )
        );

        $this->app->singleton(
            SQSMessageHandler::class . '[' . MembershipPaymentMethodUpdatedEvent::class . ']',
            fn (Application $app) => new SQSMessageHandler(
                $app->make(SQSResultMessageToDomainEventConverter::class),
                $app->make(Emitter::class),
                MembershipPaymentMethodUpdatedEvent::EVENT_NAME,
                MembershipPaymentMethodUpdatedEvent::class
            )
        );

        $this->app->singleton(
            SQSMessageHandler::class . '[' . MembershipTransferEvent::class . ']',
            fn (Application $app) => new SQSMessageHandler(
                $app->make(SQSResultMessageToDomainEventConverter::class),
                $app->make(Emitter::class),
                MembershipTransferEvent::EVENT_NAME,
                MembershipTransferEvent::class
            )
        );

        $this->app->singleton(
            SQSMessageHandler::class . '[' . MembershipRenewalSuccessEvent::class . ']',
            fn (Application $app) => new SQSMessageHandler(
                $app->make(SQSResultMessageToDomainEventConverter::class),
                $app->make(Emitter::class),
                MembershipRenewalSuccessEvent::EVENT_NAME,
                MembershipRenewalSuccessEvent::class
            )
        );

        $this->app->singleton(
            SQSMessageHandler::class . '[' . MembershipRenewalFailureEvent::class . ']',
            fn (Application $app) => new SQSMessageHandler(
                $app->make(SQSResultMessageToDomainEventConverter::class),
                $app->make(Emitter::class),
                MembershipRenewalFailureEvent::EVENT_NAME,
                MembershipRenewalFailureEvent::class
            )
        );

        $this->app->singleton(
            SQSMessageHandler::class . '[' . MembershipLockedEvent::class . ']',
            fn (Application $app) => new SQSMessageHandler(
                $app->make(SQSResultMessageToDomainEventConverter::class),
                $app->make(Emitter::class),
                MembershipLockedEvent::EVENT_NAME,
                MembershipLockedEvent::class
            )
        );

        $this->app->singleton(
            SQSMessageHandler::class . '[' . MembershipUnlockedEvent::class . ']',
            fn (Application $app) => new SQSMessageHandler(
                $app->make(SQSResultMessageToDomainEventConverter::class),
                $app->make(Emitter::class),
                MembershipUnlockedEvent::EVENT_NAME,
                MembershipUnlockedEvent::class
            )
        );

        $this->app->singleton(
            SQSMessageHandler::class . '[' . EventCreatedEvent::class . ']',
            fn (Application $app) => new SQSMessageHandler(
                $app->make(SQSResultMessageToDomainEventConverter::class),
                $app->make(Emitter::class),
                EventCreatedEvent::EVENT_NAME,
                EventCreatedEvent::class
            )
        );

        $this->app->singleton(
            SQSMessageHandler::class . '[' . CancellationInBatchRequestedEvent::class . ']',
            fn (Application $app) => new SQSMessageHandler(
                $app->make(SQSResultMessageToDomainEventConverter::class),
                $app->make(Emitter::class),
                CancellationInBatchRequestedEvent::EVENT_NAME,
                CancellationInBatchRequestedEvent::class
            )
        );

        $this->app->singleton(
            SQSMessageHandler::class . '[' . RecurrentBookingRequestedEvent::class . ']',
            fn (Application $app) => new SQSMessageHandler(
                $app->make(SQSResultMessageToDomainEventConverter::class),
                $app->make(Emitter::class),
                RecurrentBookingRequestedEvent::EVENT_NAME,
                RecurrentBookingRequestedEvent::class
            )
        );

        $this->app->singleton(
            SQSMessageHandler::class . '[' . BookingFinishedEvent::class . ']',
            fn (Application $app) => new SQSMessageHandler(
                $app->make(SQSResultMessageToDomainEventConverter::class),
                $app->make(Emitter::class),
                BookingFinishedEvent::EVENT_NAME,
                BookingFinishedEvent::class
            )
        );

        $this->app->singleton(SqsClient::class, function () {
            $region = env('GLOFOX_SNS_REGION') ?: 'DEFAULT_REGION';

            return new SqsClient(
                [
                    'version' => 'latest',
                    'region' => $region,
                ]
            );
        });

        $this->app->singleton(ImageModeratorClientInterface::class, fn () => new RekognitionImageModeratorClient(
            app()->make(RekognitionClient::class),
            app()->make(LoggerInterface::class),
        ));

        $this->app->singleton(RekognitionClient::class, fn () => new RekognitionClient(
            [
                'version' => 'latest',
                'region' => 'eu-west-1',
            ]
        ));

        $this->app->singleton(SQSConsumer::class, function (Application $app) {
            $consumer = new SQSConsumer(
                $app->make(SqsClient::class),
                env('GLOFOX_SQS_API_QUEUE_URL'),
                $app->make(SQSErrorHandler::class)
            );

            $consumer->addHandler(
                $app->make(SQSMessageHandler::class . '[' . BookingCancelledEvent::class . ']')
            );
            $consumer->addHandler(
                $app->make(SQSMessageHandler::class . '[' . ProgramUpdatedEvent::class . ']')
            );
            $consumer->addHandler(
                $app->make(SQSMessageHandler::class . '[' . CourseBookingUpdateRequestProcessedEvent::class . ']')
            );
            $consumer->addHandler(
                $app->make(SQSMessageHandler::class . '[' . TimeSlotBookingUpdateRequestProcessedEvent::class . ']')
            );
            $consumer->addHandler(
                $app->make(SQSMessageHandler::class . '[' . EventBookingUpdateRequestProcessedEvent::class . ']')
            );
            $consumer->addHandler(
                $app->make(SQSMessageHandler::class . '[' . TrackingEvent::class . ']')
            );
            $consumer->addHandler(
                $app->make(SQSMessageHandler::class . '[' . TransactionFinishedEvent::class . ']')
            );
            $consumer->addHandler(
                $app->make(SQSMessageHandler::class . '[' . ReservationCancellationRequestedEvent::class . ']')
            );
            $consumer->addHandler(
                $app->make(SQSMessageHandler::class . '[' . ReservationConfirmationRequestedEvent::class . ']')
            );
            $consumer->addHandler(
                $app->make(SQSMessageHandler::class . '[' . ReservationBookingRequestedEvent::class . ']')
            );
            $consumer->addHandler(
                $app->make(SQSMessageHandler::class . '[' . TransactionRefundedEvent::class . ']')
            );
            $consumer->addHandler(
                $app->make(SQSMessageHandler::class . '[' . UserMembershipPausedEvent::class . ']')
            );
            $consumer->addHandler(
                $app->make(SQSMessageHandler::class . '[' . UserMembershipPauseUpdatedEvent::class . ']'),
            );
            $consumer->addHandler(
                $app->make(SQSMessageHandler::class . '[' . UserMembershipUpdatedEvent::class . ']')
            );
            $consumer->addHandler(
                $app->make(SQSMessageHandler::class . '[' . MembershipActivatedEvent::class . ']')
            );
            $consumer->addHandler(
                $app->make(SQSMessageHandler::class . '[' . MembershipPaymentDatesMovedEvent::class . ']')
            );
            $consumer->addHandler(
                $app->make(SQSMessageHandler::class . '[' . MembershipUnpausedEvent::class . ']')
            );
            $consumer->addHandler(
                $app->make(SQSMessageHandler::class . '[' . MembershipRenewalSuccessEvent::class . ']')
            );
            $consumer->addHandler(
                $app->make(SQSMessageHandler::class . '[' . MembershipRenewalFailureEvent::class . ']')
            );
            $consumer->addHandler(
                $app->make(SQSMessageHandler::class . '[' . EventCreatedEvent::class . ']')
            );
            $consumer->addHandler(
                $app->make(SQSMessageHandler::class . '[' . MembershipPaymentPriceUpdatedEvent::class . ']')
            );
            $consumer->addHandler(
                $app->make(SQSMessageHandler::class . '[' . MembershipPaymentMethodUpdatedEvent::class . ']')
            );
            $consumer->addHandler(
                $app->make(SQSMessageHandler::class . '[' . MembershipPurchasedEvent::class . ']')
            );
            $consumer->addHandler(
                $app->make(SQSMessageHandler::class . '[' . GrantCreditsEvent::class . ']')
            );
            $consumer->addHandler(
                $app->make(SQSMessageHandler::class . '[' . MembershipTransferEvent::class . ']')
            );
            $consumer->addHandler(
                $app->make(SQSMessageHandler::class . '[' . MembershipLockedEvent::class . ']')
            );
            $consumer->addHandler(
                $app->make(SQSMessageHandler::class . '[' . MembershipUnlockedEvent::class . ']')
            );
            $consumer->addHandler(
                $app->make(SQSMessageHandler::class . '[' . CancellationInBatchRequestedEvent::class . ']')
            );
            $consumer->addHandler(
                $app->make(SQSMessageHandler::class . '[' . RecurrentBookingRequestedEvent::class . ']')
            );
            $consumer->addHandler(
                $app->make(SQSMessageHandler::class . '[' . BookingFinishedEvent::class . ']')
            );

            return $consumer;
        });

        $this->app->singleton(FulfilmentCoreapiConsumer::class, function (Application $app) {
            $fulfilmentConsumer = new FulfilmentCoreapiConsumer(
                new ConsumerConfiguration(
                    env('ENVIRONMENT_SHORT_NAME'),
                    env('GLOFOX_SNS_REGION'),
                    1,
                    5
                )
            );

            $itemPurchaseUpdateHandler = app()->make(ItemPurchaseUpdatedHandler::class);
            $fulfilmentConsumer->registerItemPurchaseUpdatedV1JsonHandler($itemPurchaseUpdateHandler);

            return $fulfilmentConsumer;
        });

        $this->app->singleton(EventsErrorProcessor::class, fn () => new EventsErrorProcessor());

        $this->app->singleton(BookingCancelledProcessor::class, function (Application $app) {
            $eventHandler = new BookingCancelledProcessor(
                $app->make(BookingsRepository::class),
                $app->make(BookingRequestsRepository::class),
                $app->make(BookingsPublisher::class),
                $app->make(LoggerInterface::class)
            );

            $eventHandler->addTask($app->make(UserRequestedBookingCancelling::class));
            $eventHandler->addTask($app->make(BookingCancellationMessageSending::class));

            return $eventHandler;
        });

        $this->app->singleton(EventBookingUpdateRequestProcessedEventHandler::class, function (Application $app) {
            $eventHandler = new EventBookingUpdateRequestProcessedEventHandler();

            $eventHandler->addTask($app->make(UpdateMembershipBasedOnBooking::class));
            $eventHandler->addTask($app->make(SendNotificationBookingUpdated::class));
            $eventHandler->addTask($app->make(CalculateEventBookingTotals::class));
            $eventHandler->addTask($app->make(SendWaitingListSpotNoLongerAvailableNotification::class));

            return $eventHandler;
        });

        $this->app->singleton(CourseBookingUpdateRequestProcessedEventHandler::class, function (Application $app) {
            $eventHandler = new CourseBookingUpdateRequestProcessedEventHandler();

            $eventHandler->addTask($app->make(UpdateMembershipBasedOnBooking::class));

            return $eventHandler;
        });

        $this->app->singleton(TimeSlotBookingUpdateRequestProcessedEventHandler::class, function (Application $app) {
            $eventHandler = new TimeSlotBookingUpdateRequestProcessedEventHandler();

            $eventHandler->addTask($app->make(UpdateMembershipBasedOnBooking::class));

            return $eventHandler;
        });

        $this->app->singleton(ProgramUpdatedEventHandler::class, function (Application $app) {
            $eventHandler = new ProgramUpdatedEventHandler(
                $app->make(LoggerInterface::class)
            );

            $eventHandler->addTask($app->make(UpdateEventsBasedOnProgramChange::class));

            return $eventHandler;
        });

        $this->app->singleton(DomainEventConsumer::class, function (Application $app) {
            $consumer = new DomainEventConsumer(
                $app->make(Emitter::class),
                $app->make(SQSConsumer::class),
                $app->make(EventsErrorProcessor::class)
            );

            $consumer->addEventHandler($app->make(BookingCancelledProcessor::class));
            $consumer->addEventHandler($app->make(ProgramUpdatedEventHandler::class));
            $consumer->addEventHandler($app->make(EventBookingUpdateRequestProcessedEventHandler::class));
            $consumer->addEventHandler($app->make(CourseBookingUpdateRequestProcessedEventHandler::class));
            $consumer->addEventHandler($app->make(TimeSlotBookingUpdateRequestProcessedEventHandler::class));
            $consumer->addEventHandler($app->make(TrackEventRequestedHandler::class));
            $consumer->addEventHandler($app->make(TransactionFinishedEventHandler::class));
            $consumer->addEventHandler($app->make(ReservationCancellationRequestedEventHandler::class));
            $consumer->addEventHandler($app->make(ReservationConfirmationRequestedEventHandler::class));
            $consumer->addEventHandler($app->make(ReservationBookingRequestedEventHandler::class));
            $consumer->addEventHandler($app->make(TransactionRefundedEventHandler::class));
            $consumer->addEventHandler($app->make(UserMembershipPausedEventHandler::class));
            $consumer->addEventHandler($app->make(UserMembershipPauseUpdatedEventHandler::class));
            $consumer->addEventHandler($app->make(UserMembershipUpdatedEventHandler::class));
            $consumer->addEventHandler($app->make(MembershipActivatedEventHandler::class));
            $consumer->addEventHandler($app->make(MembershipPaymentDatesMovedEventHandler::class));
            $consumer->addEventHandler($app->make(MembershipUnpausedEventHandler::class));
            $consumer->addEventHandler($app->make(MembershipRenewalSuccessEventHandler::class));
            $consumer->addEventHandler($app->make(MembershipRenewalFailureEventHandler::class));
            $consumer->addEventHandler($app->make(EventCreatedEventHandler::class));
            $consumer->addEventHandler($app->make(MembershipPaymentPriceUpdatedEventHandler::class));
            $consumer->addEventHandler($app->make(MembershipPaymentMethodUpdatedEventHandler::class));
            $consumer->addEventHandler($app->make(MembershipPurchasedEventHandler::class));
            $consumer->addEventHandler($app->make(GrantCreditsEventHandler::class));
            $consumer->addEventHandler($app->make(MembershipTransferEventHandler::class));
            $consumer->addEventHandler($app->make(MembershipLockedEventHandler::class));
            $consumer->addEventHandler($app->make(MembershipUnlockedEventHandler::class));
            $consumer->addEventHandler($app->make(CancellationInBatchRequestedEventHandler::class));
            $consumer->addEventHandler($app->make(RecurrentBookingRequestedEventHandler::class));
            $consumer->addEventHandler($app->make(BookingFinishedEventHandler::class));

            return $consumer;
        });

        $this->app->singleton(
            BookingUpdateRequestEventPublisherInterface::class,
            fn (Application $app) => $app->make(BookingUpdateRequestEventPublisher::class)
        );

        $this->app->singleton(
            MigrationRepositoryInterface::class,
            fn (Application $app) => $app->make(MigrationDataPoolRepository::class)
        );

        $this->app->singleton(
            MigrationRepositoryInterface::class,
            fn (Application $app) => $app->make(MigrationMappingRepository::class)
        );

        $this->app->bind(
            MappingFinderService::class,
            fn (Application $app) => new MappingFinderService($app->make(MigrationMappingRepository::class))
        );

        $this->app->bind(PushNotification::class, fn () => \ClassRegistry::init('PushNotification'));

        $this->app->bind(\NotificationComponent::class, function () {
            $componentCollection = new \ComponentCollection();
            $component = new \NotificationComponent($componentCollection);
            $component->initialize(new \Controller());

            return $component;
        });

        $this->app->bind(\User::class, fn () => \ClassRegistry::init('User'));

        $this->app->bind(\Facility::class, fn () => \ClassRegistry::init('Facility'));

        $this->app->bind(\Client::class, fn () => \ClassRegistry::init('Client'));

        $this->app->bind(\InsuranceDetailsEvent::class, fn () => \ClassRegistry::init('InsuranceDetailsEvent'));

        $this->app->bind(
            PushNotificationsServiceInterface::class,
            fn (Application $app) => new PushNotificationsHttpClientNew(
                $app->make(LoggerInterface::class),
                new HttpClient(['base_uri' => env('GLOFOX_PUSHER_URL_NEW')])
            )
        );

        $this->app->bind(
            CreateMigrationDataPoolServiceInterface::class,
            fn (Application $app) => new CreateMigrationDataPoolServiceVersionHandler(
                new CreateMigrationDataPoolServiceV1(
                    $app->make(MigrationDataPoolRepository::class),
                    $app->make(UsersRepository::class)
                ),
                new CreateMigrationDataPoolServiceV2(
                    $app->make(MigrationMappingRepository::class),
                ),
                $app->make(BranchesRepository::class),
            )
        );

        $this->app->bind(
            DeleteMigrationDataPoolServiceInterface::class,
            fn (Application $app) => new DeleteMigrationDataPoolServiceVersionHandler(
                new DeleteMigrationDataPoolServiceV1(
                    $app->make(MigrationDataPoolRepository::class),
                ),
                new DeleteMigrationDataPoolServiceV2(
                    $app->make(MigrationMappingRepository::class),
                ),
            )
        );

        $this->app->bind(
            RetrieveMigrationDataPoolServiceInterface::class,
            fn (Application $app) => new RetrieveMigrationDataPoolServiceVersionHandler(
                new RetrieveMigrationDataPoolServiceV1(
                    $app->make(MigrationDataPoolRepository::class),
                ),
                new RetrieveMigrationDataPoolServiceV2(
                    $app->make(MigrationMappingRepository::class),
                ),
            )
        );

        $this->app->bind(
            UpdateMigrationDataPoolServiceInterface::class,
            fn (Application $app) => new UpdateMigrationDataPoolServiceVersionHandler(
                new UpdateMigrationDataPoolServiceV1(
                    $app->make(MigrationDataPoolRepository::class),
                ),
                new UpdateMigrationDataPoolServiceV2(
                    $app->make(MigrationMappingRepository::class),
                ),
            )
        );

        $this->app->bind(
            UserForeignIdFinderServiceInterface::class,
            fn (Application $app) => new UserForeignIdFinderServiceVersionHandler(
                new UserForeignIdFinderServiceV1(
                    $app->make(MigrationDataPoolRepository::class),
                ),
                new UserForeignIdFinderServiceV2(
                    $app->make(MigrationMappingRepository::class),
                )
            )
        );

        $this->app->singleton(
            JsonPostEncodingErrorValidator::class,
            fn (Application $app) => new JsonPostEncodingErrorValidator()
        );

        $this->app->singleton(Connection::class, fn (Application $app) => new Connection());

        $this->app->singleton(ExistingRequestIdResolver::class, fn (Application $app) => new ExistingRequestIdResolver(
            $app->make('request')
        ));

        $this->app->singleton(
            CurrentRequestIdServiceInterface::class,
            fn (Application $app) => $app->make(CurrentRequestIdService::class)
        );

        $this->app->singleton(ObfuscatorInterface::class, fn (Application $app) => new DefaultObfuscator(
            $app->make('config')->get('obfuscation')
        ));

        $this->app->singleton(UuidFactoryInterface::class, fn () => Uuid::getFactory());

        $this->app->singleton(
            ResponseMiddlewarePipelineService::class,
            fn (Application $app) => new ResponseMiddlewarePipelineService(
                $app,
                $app->make(LoggerInterface::class)
            )
        );

        $this->app->singleton(AvatarUrlFactory::class, fn (Application $app) => new AvatarUrlFactory(
            env('OPENSHIFT_APP_NAME')
        ));

        $this->app->singleton(PredisClientFactory::class, fn (Application $app) => new PredisClientFactory($app));

        $this->app->singleton(RedisCacheClientFactory::class, function (Application $app) {
            /** @var PredisClientFactory $factory */
            $factory = $app->make(PredisClientFactory::class);
            $predisClient = $factory->createFromEnvironment();

            return new RedisCacheClientFactory($predisClient);
        });

        $this->app->singleton(WebhookLockerInterface::class, function (Application $app) {
            $prefix = new CachePrefix(CachePrefix::WEBHOOK_LOCKER);

            /** @var RedisCacheClientFactory $factory */
            $factory = $app->make(RedisCacheClientFactory::class);
            $cacheClient = $factory->create($prefix);

            return new CacheWebhookLocker(
                $app->make(LoggerInterface::class),
                $cacheClient
            );
        });

        // this needs to be renamed to BookingAppointmentLockerInterface
        $this->app->singleton(EventBookingLockerInterface::class, function (Application $app) {
            $prefix = new CachePrefix(CachePrefix::BOOKING_APPOINTMENT_LOCKER);

            /** @var RedisCacheClientFactory $factory */
            $factory = $app->make(RedisCacheClientFactory::class);
            $cacheClient = $factory->create($prefix);

            return new BookingAppointmentLocker(
                $cacheClient,
                $app->make(LoggerInterface::class)
            );
        });

        $this->app->singleton(BookingEventLockerInterface::class, function (Application $app) {
            $prefix = new CachePrefix(CachePrefix::BOOKING_EVENT_LOCKER);

            /** @var RedisCacheClientFactory $factory */
            $factory = $app->make(RedisCacheClientFactory::class);
            $cacheClient = $factory->create($prefix);

            return new BookingEventLocker(
                $cacheClient,
                $app->make(LoggerInterface::class)
            );
        });

        $this->app->singleton(RecurrentBookingLockerInterface::class, function (Application $app) {
            $prefix = new LockerPrefix(LockerPrefix::RECURRENT_BOOKING_LOCKER);

            /** @var RedisCacheClientFactory $factory */
            $factory = $app->make(RedisCacheClientFactory::class);
            $cacheClient = $factory->create($prefix);

            return new RecurrentBookingLocker(
                $cacheClient,
                $app->make(LoggerInterface::class)
            );
        });

        $this->app->singleton(PaymentMethodOnboardingLockerInterface::class, function (Application $app) {
            $prefix = new CachePrefix(CachePrefix::PAYMENT_METHOD_ONBOARDING_LOCKER);

            /** @var RedisCacheClientFactory $factory */
            $factory = $app->make(RedisCacheClientFactory::class);
            $cacheClient = $factory->create($prefix);

            return new PaymentMethodOnboardingLocker(
                $app->make(LoggerInterface::class),
                $cacheClient
            );
        });

        $this->app->singleton(ApiWorkerTaskLockerInterface::class, function (Application $app) {
            $prefix = new CachePrefix(CachePrefix::API_WORKER_TASK_LOCKER);

            /** @var RedisCacheClientFactory $factory */
            $factory = $app->make(RedisCacheClientFactory::class);
            $cacheClient = $factory->create($prefix);

            return new ApiWorkerTaskLocker(
                $app->make(LoggerInterface::class),
                $cacheClient
            );
        });

        $this->app->singleton(SetUpIntentLockerInterface::class, function (Application $app) {
            $prefix = new CachePrefix(CachePrefix::SETUP_INTENT_LOCKER);

            /** @var RedisCacheClientFactory $factory */
            $factory = $app->make(RedisCacheClientFactory::class);
            $cacheClient = $factory->create($prefix);

            return new SetUpIntentLocker(
                $app->make(LoggerInterface::class),
                $cacheClient
            );
        });

        $this->app->singleton(
            InternalCakeRequestRouterInterface::class,
            fn (Application $app) => new InternalCakeRequestRouter($app->make(\User::class))
        );

        $this->app->singleton(FacilityValidator::class, fn (Application $app) => new FacilityValidator(
            $app->make(FacilitiesRepository::class),
            $app->make(LoggerInterface::class)
        ));

        $this->app->singleton(
            OnlineBookingConfirmationTemplateFilter::class,
            fn (Application $app) => new OnlineBookingConfirmationTemplateFilter(
                $app->make(IrisGlofoxDigitalFlagger::class),
                $app->make(BranchesRepository::class)
            )
        );

        $this->app->singleton(
            ModerateMessage::class,
            fn (Application $app) => new ModerateMessage(
                $app->make(Flagger::class),
                $app->make(CommunicationsClient::class),
                $app->make(LoggerInterface::class)
            )
        );

        $this->app->singleton(TrackEventRequestedHandler::class, function (Application $app) {
            $handler = new TrackEventRequestedHandler(
                $app->make(LoggerInterface::class)
            );

            $handler->addTask($app->make(TrackEvent::class));

            return $handler;
        });

        $this->app->singleton(TrackEventPublisher::class, function (Application $app) {
            $domainEventPublisher = $app->make(DomainEventPublisher::class);
            $logger = $app->make(LoggerInterface::class);

            return new TrackEventPublisher($domainEventPublisher, $logger);
        });

        $this->app->singleton(TransactionFinishedEventHandler::class, function (Application $app) {
            $handler = new TransactionFinishedEventHandler(
                $app->make(LoggerInterface::class),
                $app->make(ApiWorkerTaskLockerInterface::class)
            );

            $handler->addTask($app->make(AddPriceBreakdownData::class));
            $handler->addTask($app->make(AddMemberTaxId::class));
            $handler->addTask($app->make(FormatChargeDescription::class));
            $handler->addTask($app->make(SendReceipt::class));

            return $handler;
        });

        $this->app->singleton(ReservationCancellationRequestedEventHandler::class, function (Application $app) {
            $handler = new ReservationCancellationRequestedEventHandler(
                $app->make(LoggerInterface::class),
                $app->make(ApiWorkerTaskLockerInterface::class)
            );

            $handler->addTask($app->make(CancelUnconfirmedBooking::class));

            return $handler;
        });

        $this->app->singleton(ReservationConfirmationRequestedEventHandler::class, function (Application $app) {
            $handler = new ReservationConfirmationRequestedEventHandler(
                $app->make(LoggerInterface::class),
                $app->make(ApiWorkerTaskLockerInterface::class)
            );

            $handler->addTask($app->make(ValidateReservation::class));

            return $handler;
        });

        $this->app->singleton(ReservationBookingRequestedEventHandler::class, function (Application $app) {
            $handler = new ReservationBookingRequestedEventHandler(
                $app->make(LoggerInterface::class),
                $app->make(ApiWorkerTaskLockerInterface::class)
            );

            $handler->addTask($app->make(ConfirmUnconfirmedBooking::class));

            return $handler;
        });

        $this->app->singleton(AddMemberTaxId::class, function (Application $app) {
            $model = $app->make(\StripeCharge::class);
            $model->setReadConcern(new ReadConcern(ReadConcern::MAJORITY));

            return new AddMemberTaxId(
                $app->make(LoggerInterface::class),
                $app->make(UsersRepository::class),
                new ChargesRepository($model)
            );
        });

        $this->app->singleton(FormatChargeDescription::class, function (Application $app) {
            $model = $app->make(\StripeCharge::class);
            $model->setReadConcern(new ReadConcern(ReadConcern::MAJORITY));

            return new FormatChargeDescription(
                new ChargesRepository($model),
                $app->make(AddonServiceInterface::class),
                $app->make(LoggerInterface::class)
            );
        });

        $this->app->singleton(SendReceipt::class, function (Application $app) {
            $model = $app->make(\StripeCharge::class);
            $model->setReadConcern(new ReadConcern(ReadConcern::MAJORITY));

            return new SendReceipt(
                $app->make(SendReceiptHandler::class),
                new ChargesRepository($model),
                $app->make(LoggerInterface::class)
            );
        });

        $this->app->singleton(TransactionRefundedEventHandler::class, function (Application $app) {
            $handler = new TransactionRefundedEventHandler(
                $app->make(LoggerInterface::class),
                $app->make(ApiWorkerTaskLockerInterface::class)
            );

            $handler->addTask($app->make(SendRefundEmail::class));

            return $handler;
        });

        $this->app->singleton(UserMembershipPausedEventHandler::class, function (Application $app) {
            $handler = new UserMembershipPausedEventHandler(
                $app->make(LoggerInterface::class),
                $app->make(ApiWorkerTaskLockerInterface::class)
            );

            $handler->addTask($app->make(UpdateUserMembership::class));
            $handler->addTask($app->make(CancelPausedMembershipBookings::class));

            return $handler;
        });

        $this->app->singleton(UserMembershipPauseUpdatedEventHandler::class, function (Application $app) {
            $handler = new UserMembershipPauseUpdatedEventHandler(
                $app->make(LoggerInterface::class),
                $app->make(ApiWorkerTaskLockerInterface::class)
            );

            $handler->addTask($app->make(CancelPausedMembershipBookings::class));

            return $handler;
        });

        $this->app->singleton(UserMembershipUpdatedEventHandler::class, function (Application $app) {
            $handler = new UserMembershipUpdatedEventHandler(
                $app->make(LoggerInterface::class),
                $app->make(ApiWorkerTaskLockerInterface::class)
            );

            $handler->addTask($app->make(UpdateUserMembership::class));

            return $handler;
        });

        $this->app->singleton(MembershipActivatedEventHandler::class, function (Application $app) {
            $handler = new MembershipActivatedEventHandler(
                $app->make(LoggerInterface::class),
                $app->make(ApiWorkerTaskLockerInterface::class)
            );

            $handler->addTask($app->make(CancelCancelledMembershipBookings::class));
            $handler->addTask($app->make(UpdateUserMembership::class));

            return $handler;
        });

        $this->app->singleton(MembershipPaymentDatesMovedEventHandler::class, function (Application $app) {
            $handler = new MembershipPaymentDatesMovedEventHandler(
                $app->make(LoggerInterface::class),
                $app->make(ApiWorkerTaskLockerInterface::class)
            );

            $handler->addTask($app->make(UpdateUserMembership::class));

            return $handler;
        });

        $this->app->singleton(MembershipPurchasedEventHandler::class, function (Application $app) {
            $handler = new MembershipPurchasedEventHandler(
                $app->make(LoggerInterface::class),
                $app->make(ApiWorkerTaskLockerInterface::class)
            );

            $handler->addTask($app->make(UpdateUserMembership::class));

            return $handler;
        });

        $this->app->singleton(GrantCreditsEventHandler::class, function (Application $app) {
            $handler = new GrantCreditsEventHandler(
                $app->make(LoggerInterface::class),
                $app->make(ApiWorkerTaskLockerInterface::class)
            );

            $handler->addTask($app->make(UpdateUserMembership::class));

            return $handler;
        });

        $this->app->singleton(MembershipUnpausedEventHandler::class, function (Application $app) {
            $handler = new MembershipUnpausedEventHandler(
                $app->make(LoggerInterface::class),
                $app->make(ApiWorkerTaskLockerInterface::class)
            );

            $handler->addTask($app->make(UpdateUserMembership::class));

            return $handler;
        });

        $this->app->singleton(MembershipPaymentPriceUpdatedEventHandler::class, function (Application $app) {
            $handler = new MembershipPaymentPriceUpdatedEventHandler(
                $app->make(LoggerInterface::class),
                $app->make(ApiWorkerTaskLockerInterface::class)
            );

            $handler->addTask($app->make(UpdateUserMembership::class));

            return $handler;
        });

        $this->app->singleton(MembershipPaymentMethodUpdatedEventHandler::class, function (Application $app) {
            $handler = new MembershipPaymentMethodUpdatedEventHandler(
                $app->make(LoggerInterface::class),
                $app->make(ApiWorkerTaskLockerInterface::class)
            );

            $handler->addTask($app->make(UpdateUserMembership::class));

            return $handler;
        });

        $this->app->singleton(MembershipTransferEventHandler::class, function (Application $app) {
            $handler = new MembershipTransferEventHandler(
                $app->make(LoggerInterface::class),
                $app->make(ApiWorkerTaskLockerInterface::class)
            );

            $handler->addTask($app->make(UpdateUserMembership::class));

            return $handler;
        });

        $this->app->singleton(MembershipRenewalSuccessEventHandler::class, function (Application $app) {
            $handler = new MembershipRenewalSuccessEventHandler(
                $app->make(LoggerInterface::class),
                $app->make(ApiWorkerTaskLockerInterface::class)
            );

            $handler->addTask($app->make(UpdateUserMembership::class));

            return $handler;
        });

        $this->app->singleton(MembershipRenewalFailureEventHandler::class, function (Application $app) {
            $handler = new MembershipRenewalFailureEventHandler(
                $app->make(LoggerInterface::class),
                $app->make(ApiWorkerTaskLockerInterface::class)
            );

            $handler->addTask($app->make(UpdateUserMembership::class));

            return $handler;
        });

        $this->app->singleton(MembershipLockedEventHandler::class, function (Application $app) {
            $handler = new MembershipLockedEventHandler(
                $app->make(LoggerInterface::class),
                $app->make(ApiWorkerTaskLockerInterface::class)
            );

            $handler->addTask($app->make(UpdateUserMembership::class));

            return $handler;
        });

        $this->app->singleton(MembershipUnlockedEventHandler::class, function (Application $app) {
            $handler = new MembershipUnlockedEventHandler(
                $app->make(LoggerInterface::class),
                $app->make(ApiWorkerTaskLockerInterface::class)
            );

            $handler->addTask($app->make(UpdateUserMembership::class));

            return $handler;
        });

        $this->app->singleton(EventCreatedEventHandler::class, function (Application $app) {
            $handler = new EventCreatedEventHandler(
                $app->make(LoggerInterface::class),
                $app->make(ApiWorkerTaskLockerInterface::class)
            );

            $handler->addTask($app->make(AddEventIdToReservation::class));

            return $handler;
        });

        $this->app->singleton(CancellationInBatchRequestedEventHandler::class, function (Application $app) {
            $handler = new CancellationInBatchRequestedEventHandler(
                $app->make(LoggerInterface::class),
                $app->make(ApiWorkerTaskLockerInterface::class)
            );

            $handler->addTask($app->make(CancelBookingsByBatchId::class));

            return $handler;
        });

        $this->app->singleton(RecurrentBookingRequestedEventHandler::class, function (Application $app) {
            $eventHandler = new RecurrentBookingRequestedEventHandler(
                $app->make(LoggerInterface::class),
                $app->make(ApiWorkerTaskLockerInterface::class)
            );

            $eventHandler->addTask($app->make(ProcessRecurringBookingsByBatchId::class));

            return $eventHandler;
        });

        $this->app->singleton(BookingFinishedEventHandler::class, function (Application $app) {
            $eventHandler = new BookingFinishedEventHandler(
                $app->make(LoggerInterface::class),
                $app->make(ApiWorkerTaskLockerInterface::class)
            );

            $eventHandler->addTask($app->make(DelayTask::class));
            $eventHandler->addTask($app->make(SendWaitingListSpotNoLongerAvailableNotification::class));
            $eventHandler->addTask($app->make(SendNewBookingCreatedToStaffsNotification::class));
            $eventHandler->addTask($app->make(SendNewBookingCreatedToMemberNotification::class));
            $eventHandler->addTask($app->make(SendBookingConfirmationTask::class));

            return $eventHandler;
        });

        $this->registerCustomRules();
        $this->initiateCakeModels();
        $this->registerLogger();
        $this->registerEmailSender();
        $this->registerTrackerTool();
        $this->registerMemberTransferGuards();
        $this->registerCreditPackServices();
    }

    public function initiateCakeModels()
    {
        \ClassRegistry::init('User');
        \ClassRegistry::init('Membership');
        \ClassRegistry::init('StripeCharge');
        \ClassRegistry::init('ChargeTax');
        \ClassRegistry::init('UserCredit');
        \ClassRegistry::init('Timeslot');
        \ClassRegistry::init('Event');
        \ClassRegistry::init('Product');
        \ClassRegistry::init('Program');
        \ClassRegistry::init('Booking');
        \ClassRegistry::init('InvalidToken');
        \ClassRegistry::init('BookingCancellation');
    }

    /**
     * This function will extend the IlluminateValidator with
     * the custom rules that we created, so they can be called
     * from any of the validators.
     */
    public function registerCustomRules(): void
    {
        IlluminateValidator::extend('diffInHoursLessThan', TimeValidator::class . '@validateHoursDiffLessThan');
        IlluminateValidator::extend(
            'diffInHoursLessThanOrEqual',
            TimeValidator::class . '@validateHoursDiffLessThanOrEqual'
        );
        IlluminateValidator::extend(
            'diffInHoursGreaterThan',
            TimeValidator::class . '@validateHoursDiffGreaterThan'
        );
        IlluminateValidator::extend(
            'diffInHoursGreaterThanOrEqual',
            TimeValidator::class . '@validateHoursDiffGreaterThanOrEqual'
        );
        IlluminateValidator::extend('timestamp', TimeStampValidator::class . '@validate');
        IlluminateValidator::extend('timestamp_before', TimeStampValidator::class . '@validateBefore');
        IlluminateValidator::extend('timestamp_after', TimeStampValidator::class . '@validateAfter');
        IlluminateValidator::extend('userExists', UserValidator::class . '@validateExists');
        IlluminateValidator::extend(
            'restrictActiveMemberships',
            AnnouncementValidator::class . '@validateRestrictions'
        );
        IlluminateValidator::extend(
            'preventMultipleOnlineFacilities',
            FacilityValidator::class . '@validateOnlineFlag'
        );
        IlluminateValidator::extend('membershipMinimumPrice', MembershipPlanValidator::class . '@validateMinimumPrice');
        IlluminateValidator::extend('mongoId', MongoIdValidator::class . '@validate');

        IlluminateValidator::extend('FirstBookingCreditDates', FirstBookingCreditDates::class . '@validate');
        IlluminateValidator::replacer(
            'FirstBookingCreditDates',
            fn ($message, $attribute, $rule, $parameters) => 'UPDATING_DATES_OF_FIRST_BOOKING_CREDITS_IS_NOT_ALLOWED'
        );
        IlluminateValidator::extend('GenderValidator', GenderValidator::class . '@validate');
        IlluminateValidator::replacer(
            'GenderValidator',
            fn ($message, $attribute, $rule, $parameters) => GenderValidator::INVALID_FORMAT
        );
    }

    public function registerLogger(): void
    {
        $this->app->singleton(LoggerInterface::class, function (Application $app) {
            $logger = new Logger('app');

            $levels = [
                Logger::INFO,
                Logger::NOTICE,
                Logger::WARNING,
                Logger::ERROR,
                Logger::ALERT,
                Logger::EMERGENCY,
            ];

            foreach ($levels as $level) {
                $handler = new StreamHandler('php://stdout', $level);
                $handler->setFormatter(new DockerJsonFormatter());
                $logger->pushHandler($handler);
            }

            $processors = [
                new WebProcessor(),
                $app->make(CurrentRequestIdProcessor::class),
            ];

            foreach ($processors as $processor) {
                $logger->pushProcessor($processor);
            }

            return $logger;
        });
    }

    private function registerEmailSender(): void
    {
        $this->app->bind(EmailSenderInterface::class, fn (Application $app) => new CakeEmailSender());
    }

    private function registerTrackerTool(): void
    {
        $this->app->singleton(Mixpanel::class, function () {
            $token = env('GLOFOX_MIXPANEL_TOKEN');

            return Mixpanel::getInstance($token);
        });

        $this->app->bind(TrackerInterface::class, fn (Application $app) => new MixpanelTracker(
            $app->make(Mixpanel::class),
            $app->make(LoggerInterface::class)
        ));
    }

    private function registerMemberTransferGuards(): void
    {
        $this->app->singleton(
            IncomingMemberTransferDataOnUserCreationGuard::class,
            fn (Application $app) => new CompositeIncomingMemberTransferDataOnUserCreationGuard(
                $app->make(MemberTransferDataAuthorizationGuard::class),
                $app->make(MemberTransferDataContentGuard::class),
            )
        );

        $this->app->bind(SaveUser::class, fn () => SaveUser::createFromBase(
            app()->make(Request::class)
        ));
    }

    private function registerCreditPackServices(): void
    {
        $this->app->singleton(CreditPackSourceFactory::class);

        $this->app->singleton(CreditPackSourceManuallyAddedByStaffSchemaValidator::class);

        $this->app->singleton(CreditPackSourceCancelledBookingSchemaValidator::class);

        $this->app->singleton(CreditPackSourceMembershipPurchasedSchemaValidator::class);

        $this->app->singleton(CreditPackSourceMembershipRenewedSchemaValidator::class);

        $this->app->singleton(CreditPackSourceAdvancedWhenBookingSchemaValidator::class);

        $this->app->singleton(CreditPackSourceFactory::class);

        $this->app->singleton(SourceIsManuallyAddedByStaffMarker::class);

        $this->app->singleton(SourceIsCancelledBookingMarker::class);

        $this->app->singleton(SourceIsMembershipPurchasedMarker::class);

        $this->app->singleton(SourceIsImportedMarker::class);

        $this->app->singleton(EnumerableValueInjector::class);

        $this->app->singleton(
            CompositeCreditPackSchemaValidator::class,
            fn (Application $app) => new CompositeCreditPackSchemaValidator([
                $app->make(CreditPackSourceManuallyAddedByStaffSchemaValidator::class),
                $app->make(CreditPackSourceCancelledBookingSchemaValidator::class),
                $app->make(CreditPackSourceMembershipPurchasedSchemaValidator::class),
                $app->make(CreditPackSourceMembershipRenewedSchemaValidator::class),
                $app->make(CreditPackSourceAdvancedWhenBookingSchemaValidator::class),
            ])
        );
    }

    private function registerMongoMaxQueryDurationThrottler(): void
    {
        $this->app->singleton(MongoMaxQueryDurationThrottler::class, function () {
            $defaultMaxDuration = env('GLOFOX_DATABASE_DEFAULT_MAX_TIME_MS');
            if (!is_numeric($defaultMaxDuration)) {
                $defaultMaxDuration = 0;
            }
            return new MongoMaxQueryDurationThrottler((int) $defaultMaxDuration);
        });
    }
}
