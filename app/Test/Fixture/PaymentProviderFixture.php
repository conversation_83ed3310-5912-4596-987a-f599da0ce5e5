<?php

use Glofox\Domain\PaymentMethods\Type as PaymentMethodType;
use Glofox\Domain\PaymentProviders\HandlerType as PaymentProviderHandlerType;

class PaymentProviderFixture extends CakeTestFixture
{
    public $import = ['model' => 'PaymentProvider'];

    public function init()
    {
        $this->records = [
            [
                '_id' => new MongoId('5b2a363bc7805f005e37dcb4'),
                'active' => true,
                'gateway_id' => null,
                'payment_method_type_id' => PaymentMethodType::CASH,
                'registration_flow' => null,
                'handler_id' => PaymentProviderHandlerType::NOOP_HANDLER,
                'available_countries' => null,
                'name' => PaymentMethodType::CASH,
            ],
            [
                '_id' => new MongoId('5b2a3646c7805f005f2520b4'),
                'active' => true,
                'gateway_id' => null,
                'payment_method_type_id' => PaymentMethodType::COMPLIMENTARY,
                'registration_flow' => null,
                'handler_id' => PaymentProviderHandlerType::NOOP_HANDLER,
                'available_countries' => null,
                'name' => PaymentMethodType::COMPLIMENTARY,
            ],
            [
                '_id' => new MongoId('5b2a3652c7805f005e37dcb5'),
                'active' => true,
                'gateway_id' => null,
                'payment_method_type_id' => PaymentMethodType::PAY_LATER,
                'registration_flow' => null,
                'handler_id' => PaymentProviderHandlerType::NOOP_HANDLER,
                'available_countries' => null,
                'name' => PaymentMethodType::PAY_LATER,
            ],
            [
                '_id' => new MongoId('5b2a3661c7805f005f2520b5'),
                'active' => true,
                'gateway_id' => null,
                'payment_method_type_id' => PaymentMethodType::BANK_TRANSFER,
                'registration_flow' => null,
                'handler_id' => PaymentProviderHandlerType::NOOP_HANDLER,
                'available_countries' => null,
                'name' => PaymentMethodType::BANK_TRANSFER,
            ],
            [
                '_id' => new MongoId('5b2a3489c7805f005e37dcb4'),
                'active' => true,
                'gateway_id' => null,
                'payment_method_type_id' => PaymentMethodType::CARD,
                'registration_flow' => null,
                'handler_id' => PaymentProviderHandlerType::NOOP_HANDLER,
                'available_countries' => null,
                'name' => 'CARD NOOP',
            ],
            [
                '_id' => new MongoId('5b2a3489c7805f005e37dcb6'),
                'active' => true,
                'gateway_id' => null,
                'payment_method_type_id' => PaymentMethodType::CARD,
                'registration_flow' => null,
                'handler_id' => PaymentProviderHandlerType::NOOP_HANDLER,
                'available_countries' => [
                    [
                        'country_code' => "IE",
                        'default_charge_percentage' => 2.4,
                        'default_fixed_charge' => 0.25,
                    ],
                ],
                'name' => 'CARD NOOP 1',
            ],
            [
                '_id' => new MongoId('5b2a3489c7805f005e37dcd4'),
                'active' => true,
                'gateway_id' => null,
                'payment_method_type_id' => PaymentMethodType::CARD,
                'registration_flow' => null,
                'handler_id' => PaymentProviderHandlerType::NOOP_HANDLER,
                'available_countries' => [
                    [
                        'country_code' => "IE",
                        'default_charge_percentage' => 2.4,
                        'default_fixed_charge' => 0.25,
                    ],
                ],
                'name' => 'CARD NOOP 2',
                'is_restricted' => true,
            ],
            [
                '_id' => new MongoId('5b2a3489c7805f005e37dcb3'),
                'active' => true,
                'gateway_id' => null,
                'payment_method_type_id' => PaymentMethodType::CARD,
                'registration_flow' => null,
                'handler_id' => PaymentProviderHandlerType::NOOP_HANDLER,
                'available_countries' => [
                    [
                        'country_code' => "IE",
                        'default_charge_percentage' => 2.4,
                        'default_fixed_charge' => 0.25,
                        'features' => ['GLOBAL_PAYOUTS', 'LEGACY_ONBOARDING']
                    ],
                ],
                'name' => 'CARD NOOP 2',
                'is_restricted' => true,
            ],
            [
                '_id' => new MongoId('5b2a3489c7805f005e37dcd5'),
                'active' => true,
                'gateway_id' => null,
                'payment_method_type_id' => PaymentMethodType::DIRECT_DEBIT,
                'registration_flow' => null,
                'handler_id' => PaymentProviderHandlerType::NOOP_HANDLER,
                'available_countries' => [
                    [
                        'country_code' => "IE",
                        'default_charge_percentage' => 2.4,
                        'default_fixed_charge' => 0.25,
                    ],
                ],
                'name' => 'CARD NOOP 3',
            ],
            [
                '_id' => new MongoId('5b2a3489c7805f005e37dcc4'),
                'active' => true,
                'gateway_id' => null,
                'payment_method_type_id' => PaymentMethodType::DIRECT_DEBIT,
                'registration_flow' => null,
                'handler_id' => PaymentProviderHandlerType::NOOP_HANDLER,
                'available_countries' => null,
                'name' => 'DIRECT DEBIT NOOP',
            ],
            [
                '_id' => new MongoId('5b2a3489c7805f005e37dcb5'),
                'active' => true,
                'gateway_id' => null,
                'payment_method_type_id' => PaymentMethodType::WALLET,
                'registration_flow' => null,
                'handler_id' => PaymentProviderHandlerType::NOOP_HANDLER,
                'available_countries' => null,
                'name' => 'WALLET',
            ],
            [
                '_id' => new MongoId('5b2a3489c7805f005e37dcc6'),
                'active' => true,
                'gateway_id' => null,
                'payment_method_type_id' => PaymentMethodType::FLEXIBLE,
                'registration_flow' => null,
                'handler_id' => PaymentProviderHandlerType::NOOP_HANDLER,
                'available_countries' => null,
                'name' => 'FLEXIBLE ',
            ],
        ];

        $this->records = array_merge(
            $this->records,
            $this->forStripeDD(),
            $this->forStripePOS()
        );

        parent::init();
    }

    private function forStripeDD(): array
    {
        return [
            [
                '_id' => new MongoId('5b2a3489c7805f006e37dcb6'),
                'active' => true,
                'gateway_id' => 1,
                'payment_method_type_id' => PaymentMethodType::CARD,
                'registration_flow' => [
                    'type' => 'REDIRECT',
                    'base_url' => '${GLOFOX_ROOT_DOMAIN}/dashboard/#/settings/onboarding/glofox-payments'
                ],
                'handler_id' => PaymentProviderHandlerType::NOOP_HANDLER,
                'available_countries' => [
                    [
                        'country_code' => "PT",
                        'default_charge_percentage' => 2.4,
                        'default_fixed_charge' => 0.25
                    ],
                    [
                        'country_code' => "NL",
                        'default_charge_percentage' => 2.4,
                        'default_fixed_charge' => 0.25
                    ],
                    [
                        'country_code' => "BG",
                        'default_charge_percentage' => 2.4,
                        'default_fixed_charge' => 0.25
                    ],
                    [
                        'country_code' => "CA",
                        'default_charge_percentage' => 0,
                        'default_fixed_charge' => 0
                    ]
                ],
                'name' => 'STRIPE_CUSTOM_EU',
                'tokenization_handler' => 'STRIPE'
            ],
            [
                '_id' => new MongoId('5b2a3489c7805f006e37dcd4'),
                'active' => true,
                'gateway_id' => null,
                'payment_method_type_id' => PaymentMethodType::DIRECT_DEBIT,
                'registration_flow' => [
                    'type' => 'REDIRECT',
                    'base_url' => '${GLOFOX_ROOT_DOMAIN}/dashboard/#/settings/onboarding/glofox-payments'
                ],
                'handler_id' => PaymentProviderHandlerType::NOOP_HANDLER,
                'available_countries' => [
                    [
                        'country_code' => "PT",
                        'default_charge_percentage' => 2.4,
                        'default_fixed_charge' => 0.25,
                    ],
                ],
                'name' => 'GOCARDLESS',
                'tokenization_handler' => 'GOCARDLESS'
            ],
            [
                '_id' => new MongoId('5b2a3489c7805f006e37dcd5'),
                'active' => true,
                'gateway_id' => 1,
                'payment_method_type_id' => PaymentMethodType::DIRECT_DEBIT,
                'registration_flow' => null,
                'handler_id' => PaymentProviderHandlerType::NOOP_HANDLER,
                'available_countries' => [
                    [
                        'country_code' => "PT",
                        'default_charge_percentage' => 2.4,
                        'default_fixed_charge' => 0.25,
                    ],
                ],
                'name' => 'STRIPE_CUSTOM_EU_DD_BECS',
                'tokenization_handler' => 'STRIPE_DD_BECS'
            ],
            [
                '_id' => new MongoId('5b2a3489c7805f006e37dcd6'),
                'active' => true,
                'gateway_id' => 1,
                'payment_method_type_id' => PaymentMethodType::DIRECT_DEBIT,
                'registration_flow' => [
                    'type' => 'REDIRECT',
                    'base_url' => '${GLOFOX_ROOT_DOMAIN}/dashboard/#/settings/onboarding/glofox-payments'
                ],
                'handler_id' => PaymentProviderHandlerType::NOOP_HANDLER,
                'available_countries' => [
                    [
                        'country_code' => "NL",
                        'default_charge_percentage' => 2.4,
                        'default_fixed_charge' => 0.25,
                        'features' => ['sepa_debit_payments'],
                    ],
                ],
                'features' => ['something_else'],
                'name' => 'STRIPE_CUSTOM_EU_DD_SEPA',
                'tokenization_handler' => 'STRIPE_DD_SEPA'
            ],
            [
                '_id' => new MongoId('5b2a3489c7805f006e37dcd7'),
                'active' => true,
                'gateway_id' => 1,
                'payment_method_type_id' => PaymentMethodType::DIRECT_DEBIT,
                'registration_flow' => [
                    'type' => 'REDIRECT',
                    'base_url' => '${GLOFOX_ROOT_DOMAIN}/dashboard/#/settings/onboarding/glofox-payments'
                ],
                'handler_id' => PaymentProviderHandlerType::NOOP_HANDLER,
                'available_countries' => [
                    [
                        'country_code' => "CA",
                        'default_charge_percentage' => 0,
                        'default_fixed_charge' => 0,
                        'features' => ['acss_debit'],
                    ],
                ],
                'features' => ['something_else'],
                'name' => 'STRIPE_CUSTOM_CA_DD_ACSS_EU',
                'tokenization_handler' => 'STRIPE_DD_ACSS'
            ],
            [
                '_id' => new MongoId('5b2a3489c7805f006e37dc15'),
                'active' => true,
                'gateway_id' => 1,
                'payment_method_type_id' => PaymentMethodType::CARD,
                'registration_flow' => [
                    'type' => 'REDIRECT',
                    'base_url' => '${GLOFOX_ROOT_DOMAIN}/dashboard/#/settings/onboarding/glofox-payments'
                ],
                'handler_id' => PaymentProviderHandlerType::NOOP_HANDLER,
                'available_countries' => [
                    [
                        'country_code' => "IE",
                        'default_charge_percentage' => 2.4,
                        'default_fixed_charge' => 0.25
                    ]
                ],
                'name' => 'LIFT_BRANDS_STRIPE_CUSTOM_EU',
                'tokenization_handler' => 'STRIPE',
                'is_restricted' => true,
            ],
            [
                '_id' => new MongoId('5b2a3489c7805f006e37dcd1'),
                'active' => true,
                'gateway_id' => 1,
                'payment_method_type_id' => PaymentMethodType::DIRECT_DEBIT,
                'registration_flow' => [
                    'type' => 'REDIRECT',
                    'base_url' => '${GLOFOX_ROOT_DOMAIN}/dashboard/#/settings/onboarding/glofox-payments'
                ],
                'handler_id' => PaymentProviderHandlerType::NOOP_HANDLER,
                'available_countries' => [
                    [
                        'country_code' => "IE",
                        'default_charge_percentage' => 2.4,
                        'default_fixed_charge' => 0.25,
                        'features' => ['sepa_debit_payments'],
                    ],
                ],
                'features' => ['something_else'],
                'name' => 'LIFT_BRANDS_STRIPE_CUSTOM_EU_DD_SEPA',
                'tokenization_handler' => 'STRIPE_DD_SEPA',
                'is_restricted' => true,
            ],
        ];
    }

    private function forStripePOS(): array
    {
        return [
            [
                '_id' => new MongoId('5b2a3489c7805f005e37dcb0'),
                'active' => true,
                'gateway_id' => null,
                'payment_method_type_id' => PaymentMethodType::POS_TERMINAL,
                'registration_flow' => null,
                'handler_id' => PaymentProviderHandlerType::NOOP_HANDLER,
                'available_countries' => null,
                'name' => 'WALLET',
            ],
        ];
    }
}
