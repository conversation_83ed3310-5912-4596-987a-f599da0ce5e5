<?php

App::uses('App<PERSON>ontroller', 'Controller');

/**
 * Class ClientsImportController.
 */
class ClientsImportController extends AppController
{
    public $name = 'ClientsImport';
    public $uses = ['User', 'Membership', 'UserCredit'];
    protected $restActions = ['uploadCSV', 'uploadData'];
    /****************************** REST WEBSERVICE ACTIONS  *******************************************/

    /**
     * UploadCSV method /clientsImport/uploadCSV.
     *
     *
     * @return json returns success, error after a file sanity check and the delimeter supplied
     */
    public function uploadCSV()
    {
        // Grab reference to the uploaded files
        $files = $_FILES;
        // String for eerror message and check enables a quick check before moving on
        $str = '';
        $check = 0;
        // Loop through the user supplied uploaded files
        if ($files) {
            foreach ($files as $file) {
                // Grab the name
                $name = $file['name'];
                // Split the name on the '.' so that we can sanity check
                $splitName = explode('.', $name);
                // If the supplied file has 'filename' and the extension 'csv'
                if (2 == count($splitName)) {
                    // If 'csv' is the second element on the array
                    if ('csv' == $splitName[1]) {
                        // Increment check as this file is perfect
                        ++$check;
                    } else {
                        // File error it is not a csv
                        $str .= 'File type should be ".csv"';
                    }
                } else {
                    //
                    // Check for certain uploaded files irregularities.
                    // Allows you to check what type of malicous files are being uploaded.
                    // These can be pushed to a black box for later examination.
                    // Doubtful anything like this will be uploaded but best to check anyway.
                    // Known types are .PHP .exe .html .jpg .rar .xlsx .gif
                    //
                    $notAllowed = ['exe', 'gif', 'html', 'jpg', 'php', 'rar', 'xlsx'];
                    if (array_intersect($notAllowed, $splitName)) {
                        // Send file to a blackbox folder or just revoke its upload
                        // ...code to send file to folder
                        // Throw an error to the user without indicating anything about malicious activity
                        $str .= 'Error file names should not contain ".". Please upload a file that has a ".csv" extension and no "." in the filename.' . $name;
                    } else {
                        //Throw an error to the user as their file contains a double extension i.e. '.txt.csv'
                        $str .= 'Error file names should not contain ".". Please upload a file that has a ".csv" extension and no "." in the filename.' . $name;
                    }
                }
            }
        } else {
            $str .= 'Please enter a file';
        }

        // check to make sure all files adhere to the rules
        if ($check == count($files) && '' == $str) {
            // Return a success to the client side
            return response()->json(['success' => true, 'error' => false]);
        }
        // Return the error to the client side
        return response()->json(['success' => false, 'error' => $str]);
    }

    //
    // Still in development.
    // I have an object being created but Im not correct in my creation of the object so the user wont save.
    // This will then return a json string to the frontend showing a success or failure message.
    //

    /**
     * @return \Illuminate\Http\JsonResponse
     */
    public function uploadData()
    {
        $data = json_decode($this->request->input(), true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        if (!empty($data)) {
            $userConstructor = [
                'active' => true,
                'branch_id' => null,
                'namespace' => null,
                'first_name' => null,
                'last_name' => null,
                'phone' => null,
                'email' => null,
                'login' => null,
                'name' => null,
                'birth' => null,
                'gender' => null,
                'type' => 'MEMBER',
                'membership' => [],
            ];

            $membershipConstructor = [
                '_id' => null,
                'type' => 'payg',
                'start_date' => date('c'),
                'membership_group_id' => null,
                'plan_code' => null,
                'expiry_date' => date('c', strtotime('+1 month')),
            ];

            $userCreditConstructor = [
                'active' => true,
                'bookings' => [],
                'branch_id' => null,
                'end_date' => date('c', strtotime('+1 month')),
                'model' => 'programs',
                'namespace' => null,
                'num_sessions' => null,
                'start_date' => date('c'),
                'user_id' => null,
            ];

            $branch_id = $data['branch'];

            // Get the membership object the the user created
            $membership = $this->Membership->find(
                'first',
                [
                    'conditions' => [
                        'branch_id' => $branch_id,
                        'namespace' => $data['namespace'],
                        'active' => true,
                    ],
                ]
            );

            // Placeholder for our plan code
            $planCode = '';
            // If the user has more than one plan
            if (is_countable($membership['plans']) && count($membership['plans']) > 1) {
                //Loop through the plans
                foreach ($membership['Membership']['plans'] as $plans) {
                    // From the plans nested array loop through the credits object
                    $totalPlans = (is_countable($plans) ? count($plans) : 0);
                    for ($i = 0; $i <= $totalPlans; ++$i) {
                        $totalPlanCredits = (is_countable($plans['credits']) ? count($plans['credits']) : 0);
                        for ($j = 0; $j <= $totalPlanCredits; ++$j) {
                            // If the namespace and branch id are equal we have our plan code in this plan object
                            if (
                                $plans[$i]['credits'][$j]['namespace'] == $data['namespace']
                                && $plans[$i]['credits'][$j]['branch_id'] == $branch_id
                            ) {
                                $planCode = $plans[$i]['code'];
                            }
                        }
                    }
                }
            } else {
                $planCode = $membership['Membership']['plans'][0]['code'];
            }

            // Two arrays in scope to just keep a check on the users and the credits that did not get inserted.
            // This way even if a few elements dont get imported the rest will be and it wont make the user re-upload
            $notSavedUserArray = [];
            $notSavedUserCreditArray = [];

            if ($membership) {
                foreach ($data['content'] as $memberData) {
                    // Create and array from each row
                    $memberArray = explode($data['delimiter'], $memberData);

                    // Create our empty user objects
                    $user = $userConstructor;
                    $userMembership = $membershipConstructor;
                    $userCredit = $userCreditConstructor;

                    // Set all the fields we can for the object as we have the data from the admin user
                    $userMembership['_id'] = $membership['Membership']['_id'];
                    $userMembership['plan_code'] = $planCode;
                    $user['branch_id'] = $branch_id;
                    $user['namespace'] = $data['namespace'];

                    $userCredit['branch_id'] = $branch_id;
                    $userCredit['namespace'] = $data['namespace'];

                    // Loop through the users selected matches in step2 of the dashboard
                    foreach ($data['matches'] as $key => $value) {
                        // If the key is in the user object
                        if (array_key_exists($key, $user)) {
                            // If this is a 'missing' value filled in in step 3
                            if ('I dont have this' == $value || null == $value) {
                                $user[$key] = $data['missing'][$key];
                            } else {
                                $headerIndex = array_search($value, $data['headers']);
                                // If this is a default value filled in in step4
                                if ('' == $memberArray[$headerIndex]) {
                                    $user[$key] = $data['defaults'][$key];
                                } else {
                                    // Jus a quick re-formatting for gender. You will need to also specify the 'other' and not applicable.
                                    if ('gender' == $key) {
                                        if (strtolower($memberArray[$headerIndex]) == strtolower('Male')) {
                                            $user[$key] = 'm';
                                        } elseif (
                                            strtolower(trim($memberArray[$headerIndex])) == strtolower('Female')
                                        ) {
                                            $user[$key] = 'f';
                                        }
                                    } else {
                                        // Get the elements index and take it from the array
                                        $user[$key] = $memberArray[$headerIndex];
                                    }
                                }
                            }
                            // If the value was not supplied initally in the setup of step 2
                        } elseif (array_key_exists($key, $userMembership)) {
                            // look for the value in either the missing or default objects in 'data'
                            if ('I dont have this' == $value || null == $value) {
                                $userMembership[$key] = $data['missing'][$key];
                            } else {
                                $headerIndex = array_search($value, $data['headers']);
                                if ('' == $memberArray[$headerIndex]) {
                                    $userMembership[$key] = $data['defaults'][$key];
                                } else {
                                    $userMembership[$key] = $memberArray[$headerIndex];
                                }
                            }
                        }
                        // If this is the credits key
                        if ('credits' == $key) {
                            // Check if the value was set and set it to the userCredit object
                            if ('I dont have this' == $value || null == $value) {
                                $userCredit['num_sessions'] = (int)($data['missing'][$key]);
                            } else {
                                $headerIndex = array_search($value, $data['headers']);
                                if ('' == $memberArray[$headerIndex]) {
                                    $userCredit['num_sessions'] = (int)($data['defaults'][$key]);
                                } else {
                                    $userCredit['num_sessions'] = (int)($memberArray[$headerIndex]);
                                }
                            }
                        }
                    }

                    // Set the user login to their email
                    $user['login'] = $user['email'];

                    // Format the dates into ISODates and if they are not specified set them to start -> now, end -> now + 1 month
                    $userMembership['start_date'] = date('c', strtotime($userMembership['start_date']) ?: 'now');
                    $userMembership['expiry_date'] = date('c', strtotime($userMembership['expiry_date']) ?: '+1 month');

                    $userCredit['start_date'] = date('c', strtotime($userMembership['start_date']) ?: 'now');
                    $userCredit['end_date'] = date('c', strtotime($userMembership['expiry_date']) ?: '+1 month');

                    // Set the membership type
                    if ($userCredit['num_sessions'] > 0) {
                        $userMembership['type'] = 'time_classes';
                    } else {
                        $userMembership['type'] = 'time';
                    }
                    $user['membership'] = $userMembership;
                    // Create a new user object
                    $this->User->create();
                    // Save this user
                    $saveUser = $this->User->save($user);

                    // If the save was successful
                    if ($saveUser) {
                        // Set the user id to the user_id in user credits
                        $userCredit['user_id'] = $saveUser['User']['_id'];
                        // Create a new user credit object
                        $this->UserCredit->create();
                        // Save the user credit object
                        $saveUserCredit = $this->UserCredit->save($userCredit);

                        // If the userCredit object did not save push it to the holding array
                        if (!$saveUserCredit) {
                            array_push($notSavedUserCreditArray, $userCredit);
                        }
                    } else {
                        // If the user did not save push it to the holding array
                        array_push($notSavedUserArray, $user);
                    }
                }
            } else {
                // If the software failed to find a membership
                $result = [
                    'success' => false,
                    'message' => 'Membership has not been found. Please create a Membership first.',
                ];
            }

            if (!isset($result) || !$result) {
                // Checks to see if users were imported
                if (0 == count($notSavedUserArray)) {
                    // Checks to see if the users credits were imported
                    if (0 == count($notSavedUserCreditArray)) {
                        // A result object to return to the front end
                        $result = [
                            'success' => true,
                            'message' => 'All users have been imported',
                        ];
                    } else {
                        // Loop through the users and the credits to determine whos credits were not imported
                        $names = [];
                        for ($i = 0, $iMax = count($notSavedUserArray); $i < $iMax; ++$i) {
                            for ($j = 0, $jMax = count($notSavedUserCreditArray); $j < $jMax; ++$j) {
                                if ($notSavedUserArray[$i]['_id'] == $notSavedUserCreditArray[$j]['user_id']) {
                                    array_push(
                                        $names,
                                        $notSavedUserArray[$i]['first_name'] . ' ' . $notSavedUserArray[$i]['last_name']
                                    );
                                }
                            }
                        }
                        $slicedName = array_slice($names, 0, 3);
                        $elipses = '';
                        if (count($names) > count($slicedName)) {
                            $elipses = '...';
                        }
                        // A result object to return to the front end not sending back to much info as it can expand infinetly
                        $result = [
                            'success' => false,
                            'message' => count($notSavedUserArray)
                                . ' user(s) credit(s) could not be saved. They are '
                                . implode(', ', $names)
                                . $elipses,
                        ];
                    }
                } else {
                    // Loop through the users to determine whos credits were not imported
                    $names = [];
                    for ($i = 0, $iMax = count($notSavedUserArray); $i < $iMax; ++$i) {
                        array_push(
                            $names,
                            $notSavedUserArray[$i]['first_name'] . ' ' . $notSavedUserArray[$i]['last_name']
                        );
                    }
                    $slicedName = array_slice($names, 0, 3);
                    // A result object to return to the front end not sending back to much info as it can expand infinetly
                    $elipses = '';
                    if (count($names) > count($slicedName)) {
                        $elipses = '...';
                    }

                    foreach ($notSavedUserArray as $key => $value) {
                        $userCheck = $this->User->find(
                            'first',
                            [
                                'conditions' => [
                                    'branch_id' => $branch_id,
                                    'namespace' => $data['namespace'],
                                    'first_name' => $value['first_name'],
                                    'last_name' => $value['last_name'],
                                    'active' => true,
                                ],
                            ]
                        );
                        if ($userCheck) {
                            unset($notSavedUserArray[$key]);
                        }
                    }
                    // Get the membership object the the user created
                    if (0 == count($notSavedUserArray)) {
                        $result = [
                            'success' => false,
                            'message' => 'All users have already been added.',
                        ];
                    } else {
                        $result = [
                            'success' => false,
                            'message' => count($notSavedUserArray)
                                . ' user(s) could not be saved. This could be because they have already been added. '
                                . 'They are '
                                . implode(', ', $slicedName)
                                . $elipses,
                        ];
                    }
                }
            }
        } else {
            $result = [
                'success' => false,
                'message' => 'Please submit data',
            ];
        }

        //Return our object
        return response()->json($result);
    }
}
