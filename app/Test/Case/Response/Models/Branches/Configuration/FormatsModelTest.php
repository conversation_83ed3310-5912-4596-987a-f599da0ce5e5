<?php

App::import('Test/Case', 'GlofoxTestCase');

use Glofox\Response\Models\Branches\Configuration\FormatsModel;

/**
 * Test cases
 * 1 Model contains all fields with data
 * 2 Missing fields and returns correct structure
 * 3 Additional fields and return correct structure
 * 4 Empty array fields and return correct structure.
 *
 * Class FormatsModelTest
 */
class FormatsModelTest extends GlofoxTestCase
{
    public $fixtures = [];

    /**
     * @dataProvider modelProvider
     */
    public function testModel($dataSent, $result): void
    {
        $model = new FormatsModel($dataSent);
        $this->assertEquals($model, $result);
    }

    public function modelProvider(): array
    {
        return [
            '1 Model contains all fields with data' => [
                [
                    'date' => 'DD/MM/YYYY',
                    'time' => 'HH:mm',
                ],
                new FormatsModel(
                    [
                        'date' => 'DD/MM/YYYY',
                        'time' => 'HH:mm',
                    ]
                ),
            ],
            '2 Missing fields and returns correct structure' => [
                [
                    // date missing
                    'time' => 'HH:mm',
                ],
                new FormatsModel(
                    [
                        'date' => '',
                        'time' => 'HH:mm',
                    ]
                ),
            ],
            '3 Additional fields and return correct structure' => [
                [
                    'other' => '',
                    'date' => 'DD/MM/YYYY',
                    'time' => 'HH:mm',
                ],
                new FormatsModel(
                    [
                        'date' => 'DD/MM/YYYY',
                        'time' => 'HH:mm',
                    ]
                ),
            ],
            '4 Empty array fields and return correct structure' => [
                [],
                new FormatsModel(
                    [
                        'date' => '',
                        'time' => '',
                    ]
                ),
            ],
        ];
    }
}
