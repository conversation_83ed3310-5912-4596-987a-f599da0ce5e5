<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\BookingSpots\UseCase;

use App;
use Mockery;
use GlofoxTestCase;
use Carbon\Carbon;
use Glofox\Datasource\Exceptions\InvalidMongoIdException;
use Glofox\Domain\Bookings\Models\Booking;
use Glofox\Domain\Bookings\Repositories\BookingsRepository;
use Glofox\Domain\BookingSpots\Models\BookingSpot;
use Glofox\Domain\BookingSpots\UseCase\SendBookingSpotConfirmation;
use Glofox\Domain\Branches\Exceptions\InvalidBranchIdException;
use Glofox\Domain\Branches\Models\Branch;
use Glofox\Domain\Branches\Models\BranchConfiguration;
use Glofox\Domain\Branches\Models\WebPortalColorsConfiguration;
use Glofox\Domain\Branches\Models\WebPortalConfiguration;
use Glofox\Domain\Dictionaries\Models\Dictionary;
use Glofox\Domain\Dictionaries\Repositories\DictionariesRepository;
use Glofox\Domain\Events\Models\Event;
use Glofox\Domain\Programs\Models\Program;
use Glofox\Domain\Users\Models\User;

App::import('Test/Case', 'GlofoxTestCase');

class SendBookingSpotConfirmationTest extends GlofoxTestCase
{
    private const SPOT_BOOKING_CATEGORY = '644681a04c68b64891f7b1c7';
    private const SEND_SUCCESS = 'booking spot email confirmation has been sent';
    private const SEND_ERROR = 'booking spot email confirmation has been skipped ';
    private const SEND_ERROR_BECAUSE_IS_IN_PAST = self::SEND_ERROR . 'because the event is in the past';
    private const SEND_ERROR_BECAUSE_IS_NOT_SB = self::SEND_ERROR .
    'because the event does not have SpotBooking category';
    private BookingSpot $bookingSpotMock;
    private DictionariesRepository $dictionaryRepoMock;

    public function setUp(): void
    {
        parent::setUp();
        $this->mockLogger();
        $this->bookingSpotMock = $this->createBookingSpotMock();
        $this->dictionaryRepoMock = $this->createDictionaryRepoMock();
    }

    public function tearDown(): void
    {
        parent::tearDown();
        $this->teardownLogger();
        Mockery::close();
    }

    /**
     * @dataProvider sendEmailDataProvider
     * @throws InvalidMongoIdException
     * @throws InvalidBranchIdException
     */
    public function testSendEmail(bool $eventIsInPast, bool $eventIsNotSB, string $expectedResult): void
    {
        $bookingsRepoMock = $this->createBookingRepoMock($eventIsInPast, $eventIsNotSB);
        $useCase = new SendBookingSpotConfirmation($this->loggerMock, $bookingsRepoMock, $this->dictionaryRepoMock);
        $result = $useCase->execute($this->bookingSpotMock);
        self::assertEquals($expectedResult, $result);
    }

    public function sendEmailDataProvider(): array
    {
        return [
            'when event is a future event and has SB category, then it sends the email' =>
                [
                    'eventIsInPast' => false,
                    'eventIsNotSB' => true,
                    'expectedResult' => self::SEND_SUCCESS,
                ],
            'when event is in the past, then it does not send the email' =>
                [
                    'eventIsInPast' => true,
                    'eventIsNotSB' => false,
                    'expectedResult' => self::SEND_ERROR_BECAUSE_IS_IN_PAST,
                ],
            'when event does not have SB category, then it does not send the email' =>
                [
                    'eventIsInPast' => false,
                    'eventIsNotSB' => false,
                    'expectedResult' => self::SEND_ERROR_BECAUSE_IS_NOT_SB,
                ],
        ];
    }

    private function createBookingSpotMock(): BookingSpot
    {
        $bookingSpotMock = Mockery::mock(BookingSpot::class);
        $bookingSpotMock->shouldReceive('bookingId')
            ->andReturn('65141a3ecdd9be3c3e3763b9');
        $bookingSpotMock->shouldReceive('spotId')
            ->andReturn('test-id-1');
        return $bookingSpotMock;
    }

    private function createDictionaryRepoMock(): DictionariesRepository
    {
        $dictionaryRepoMock = Mockery::mock(DictionariesRepository::class);
        $dictionaryRepoMock->shouldReceive('findByBranchId')->andReturn($this->createDictionaryMock());
        return $dictionaryRepoMock;
    }

    private function createDictionaryMock(): Dictionary
    {
        $dictionaryMock = Mockery::mock(Dictionary::class);
        $dictionaryMock->shouldReceive('translate')->andReturn('a_string');
        $dictionaryMock->shouldReceive('hasKey')->andReturn(false);
        return $dictionaryMock;
    }

    private function createBookingRepoMock(bool $isPast, bool $hasSpotBooking): BookingsRepository
    {
        $bookingRepoMock = Mockery::mock(BookingsRepository::class);
        $bookingRepoMock->shouldReceive('addCriteria')->andReturnSelf();
        $bookingRepoMock->shouldReceive('firstOrFail')->andReturn(
            $this->createBookingMock($isPast, $hasSpotBooking)
        );
        return $bookingRepoMock;
    }

    private function createBookingMock(bool $isPast, bool $hasSpotBooking): Booking
    {
        $userMock = $this->createUserMock();

        $booking = [
            'id' => 'test-id-1',
            'event' => $this->createEventMock($isPast, $hasSpotBooking),
            'user' => $userMock,
            'branch' => Branch::make(),
        ];

        $bookingMock = Mockery::mock(Booking::class);
        $bookingMock->allows($booking);
        $bookingMock->shouldReceive('eventId')->andReturn('test-id-1');
        $bookingMock->shouldReceive('user')->andReturn($userMock);
        return $bookingMock;
    }

    private function createUserMock(): User
    {
        $userMock = Mockery::mock(User::class);
        $userMock->shouldReceive('email')->andReturn('<EMAIL>');
        $userMock->shouldReceive('name')->andReturn('a_name');
        return $userMock;
    }

    private function createEventMock(bool $isPast, bool $hasSpotBooking): Event
    {
        $eventMock = Mockery::mock(Event::class);
        $eventMock->shouldReceive('timeStart')->andReturn($this->createDateTimeMock($isPast));
        $eventMock->shouldReceive('program')->andReturn($this->createProgramMock($hasSpotBooking));
        $eventMock->shouldReceive('branch')->andReturn($this->createBranchMock());
        $eventMock->shouldReceive('name')->andReturn('a_name');
        $eventMock->shouldReceive('namespace')->andReturn('a_namespace');
        return $eventMock;
    }

    private function createDateTimeMock(bool $isPast): Carbon
    {
        $carbonMock = Mockery::mock(Carbon::class);
        $carbonMock->shouldReceive('isPast')->andReturn($isPast);
        $carbonMock->shouldReceive('toDateTimeString')->andReturn('2023/09/09');
        return $carbonMock;
    }

    private function createProgramMock(bool $hasSpotBooking): Program
    {
        $programMock = Mockery::mock(Program::class);
        $id = 'test-id-1';
        if ($hasSpotBooking) {
            $id = self::SPOT_BOOKING_CATEGORY;
        }
        $objectsArray = [$id, 'test-id-2'];
        $programMock->shouldReceive('categories')->andReturn($objectsArray);
        return $programMock;
    }

    private function createBranchMock(): Branch
    {
        $branchMock = Mockery::mock(Branch::class);
        $branchMock->shouldReceive('id')->andReturn('test-id-1');
        $branchMock->shouldReceive('name')->andReturn('a_name');
        $branchMock->shouldReceive('email')->andReturn('<EMAIL>');
        $branchMock->shouldReceive('configuration')->andReturn($this->createBranchConfigurationMock());
        return $branchMock;
    }

    private function createBranchConfigurationMock(): BranchConfiguration
    {
        $branchConfigurationMock = Mockery::mock(BranchConfiguration::class);
        $branchConfigurationMock->shouldReceive('webPortal')->andReturn($this->createWebPortalConfigurationMock());
        return $branchConfigurationMock;
    }

    private function createWebPortalConfigurationMock(): WebPortalConfiguration
    {
        $webPortalConfigurationMock = Mockery::mock(WebPortalConfiguration::class);
        $webPortalConfigurationMock->shouldReceive('colors')->andReturn(
            $this->createWebPortalColorsConfigurationMock()
        );
        return $webPortalConfigurationMock;
    }

    private function createWebPortalColorsConfigurationMock(): WebPortalColorsConfiguration
    {
        $branchMock = Mockery::mock(Branch::class);
        $branchMock->shouldReceive('isPlatinum')->andReturn(false);
        $branchMock->shouldReceive('all')->andReturn([]);
        $webPortalColorsConfigurationMock = Mockery::mock(WebPortalColorsConfiguration::class)->makePartial();
        $webPortalColorsConfigurationMock->setBranchModel($branchMock);
        return $webPortalColorsConfigurationMock;
    }
}
