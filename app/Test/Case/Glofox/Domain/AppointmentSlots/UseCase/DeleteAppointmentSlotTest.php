<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\AppointmentSlots\UseCase;

use App;
use Glofox\Domain\AppointmentSlots\UseCase\DeleteAppointmentSlot;
use Glofox\Domain\AppointmentSlots\UseCase\DeleteAppointmentSlotParams;
use Glofox\Domain\AppointmentSlots\Validation\DeleteAppointmentSlotValidator;
use Glofox\Domain\TimeSlots\Repositories\TimeSlotRepository;
use GlofoxTestCase;
use Mockery;

App::import('Test/Case', 'GlofoxTestCase');

class DeleteAppointmentSlotTest extends GlofoxTestCase
{
    public $fixtures = [];
    private TimeSlotRepository $timeSlotRepository;
    private DeleteAppointmentSlotValidator $validator;

    public function setUp()
    {
        parent::setUp();

        $this->timeSlotRepository = $this->createMock(TimeSlotRepository::class);
        $this->validator = $this->createMock(DeleteAppointmentSlotValidator::class);
    }

    public function tearDown()
    {
        parent::tearDown();

        Mockery::close();
    }

    public function testDeleteAppointmentSlot(): void
    {
        $id = 'test-slot-id';

        $this->timeSlotRepository
            ->expects($this->once())
            ->method('softDelete')
            ->with($id)
            ->willReturn(true);

        $params = new DeleteAppointmentSlotParams($id);
        $useCase = new DeleteAppointmentSlot($this->timeSlotRepository, $this->validator);
        $useCase->execute($params);
    }
}
