<?php

namespace CakeTestCases\Glofox\Http\Middlewares;

use Glofox\Application;
use Glofox\Http\Requests\Middlewares\AllowPublicRoutes;
use Glofox\Http\Requests\Middlewares\AuthorizeGantner;
use Glofox\Request;

\App::import('Test/Case', 'GlofoxTestCase');

class AuthorizeGantnerTest extends \GlofoxTestCase
{
    public $fixtures = [
        'app.branch',
        'app.user',
    ];

    /**
     * @var Application
     */
    protected $app;

    /**
     * @var AuthorizeGantner
     */
    protected $middleware;

    public function setUp()
    {
        parent::setUp();

        $this->app = Application::getInstance();
        $this->middleware = $this->app->make(AuthorizeGantner::class);
    }

    public function test_it_should_not_alter_the_request_flow_if_gantner_token_is_not_present()
    {
        $passed = false;

        $closure = function () use (&$passed) {
            $passed = true;
        };

        $request = new Request();

        $this->middleware->handle($request, $closure);

        self::assertTrue($passed);
    }

    public function test_it_should_block_routes_that_are_not_allowed_for_gantner()
    {
        $closure = function () {
        };

        $request = new Request();
        \Router::parse('/random/url');

        $request->query->set('gantner_token', 'gantner-token-123');

        $this->setExpectedException(\UnauthorizedException::class, 'This route is not allowed for Gantner');

        $this->middleware->handle($request, $closure);

        unset($_SERVER['HTTP_AUTHORIZATION']);
        app()->forgetInstance(Request::class);
    }

    public function test_it_should_grant_access_given_the_correct_gantner_token_through_url()
    {
        $gantnerRequestClosure = function () use (&$request) {
            return $request;
        };

        $request = new Request();

        $cakeRequest = new \CakeRequest();
        $cakeRequest->here = '/users/entry/49a7011a05c677b9a916612a/gantner-token-123';
        \Router::setRequestInfo($cakeRequest);
        \Router::parse($cakeRequest->here);

        /** @var Request $alteredRequest */
        $alteredRequest = $this->middleware->handle($request, $gantnerRequestClosure);

        self::assertNotEmpty($alteredRequest->headers->get('authorization'));

        // this request should now be good enough to pass thought the authenticate middleware without any error
        $authenticateMiddleware = $this->app->make(AllowPublicRoutes::class);

        $passedAuth = false;

        $authenticateRequestClosure = function () use (&$passedAuth) {
            $passedAuth = true;
        };

        $authenticateMiddleware->handle($alteredRequest, $authenticateRequestClosure);

        self::assertTrue($passedAuth);

        unset($_SERVER['HTTP_AUTHORIZATION']);
        app()->forgetInstance(Request::class);
    }

    public function test_it_should_skip_the_middleware_when_there_is_a_jwt_in_the_request()
    {
        $gantnerRequestClosure = function () use (&$request) {
            return $request;
        };

        $request = new Request();
        $request->headers->set('authorization', 'random-nice-token');

        $cakeRequest = new \CakeRequest();
        $cakeRequest->here = '/branches/upsert';
        \Router::setRequestInfo($cakeRequest);
        \Router::parse($cakeRequest->here);

        /** @var Request $alteredRequest */
        $alteredRequest = $this->middleware->handle($request, $gantnerRequestClosure);

        self::assertEquals('random-nice-token', $alteredRequest->headers->get('authorization'));

        unset($_SERVER['HTTP_AUTHORIZATION']);
        app()->forgetInstance(Request::class);
    }
}
