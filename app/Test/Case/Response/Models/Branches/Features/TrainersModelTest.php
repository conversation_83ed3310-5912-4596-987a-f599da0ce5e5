<?php

App::import('Test/Case', 'GlofoxTestCase');

use Glofox\Response\Models\Branches\Features\TrainersModel;

/**
 * Test cases
 * 1 Model contains all fields with data
 * 2 Missing fields and returns correct structure
 * 3 Additional fields and return correct structure
 * 4 Empty array fields and return correct structure.
 *
 * Class TrainersModelTest
 */
class TrainersModelTest extends GlofoxTestCase
{
    public $fixtures = [];

    /**
     * @dataProvider modelProvider
     */
    public function testModel($dataSent, $result): void
    {
        $model = new TrainersModel($dataSent);
        $this->assertEquals($model, $result);
    }

    public function modelProvider(): array
    {
        return [
            '1 Model contains all fields with data' => [
                [
                    'order' => 1,
                    'enabled' => true,
                    '_booking_open_window' => true,
                    '_booking_close_window' => true,
                    'booking_enabled' => true,
                    'booking_weeks_display' => 'string',
                    'booking_cancel_window' => 0,
                    'booking_open_window' => 0,
                    'booking_close_window' => 'test',
                    'booking_time_slot_length' => 0,
                ],
                new TrainersModel(
                    [
                        'order' => 1,
                        'enabled' => true,
                        '_booking_open_window' => true,
                        '_booking_close_window' => true,
                        'booking_enabled' => true,
                        'booking_weeks_display' => 'string',
                        'booking_cancel_window' => 0,
                        'booking_open_window' => 0,
                        'booking_close_window' => 'test',
                        'booking_time_slot_length' => 0,
                    ]
                ),
            ],
            '2 Missing fields and returns correct structure' => [
                [
                    'order' => 1,
                    'enabled' => true,
                    '_booking_open_window' => true,
                    '_booking_close_window' => true,
                    'booking_enabled' => true,
                    'booking_weeks_display' => 'string',
                    'booking_cancel_window' => 0,
                    //  booking_open_window, booking_close_window, booking_time_slot_length missing
                ],
                new TrainersModel(
                    [
                        'order' => 1,
                        'enabled' => true,
                        '_booking_open_window' => true,
                        '_booking_close_window' => true,
                        'booking_enabled' => true,
                        'booking_weeks_display' => 'string',
                        'booking_cancel_window' => 0,
                        'booking_open_window' => 0,
                        'booking_close_window' => '',
                        'booking_time_slot_length' => 0,
                    ]
                ),
            ],
            '3 Additional fields and return correct structure' => [
                [
                    'moreThingsToArray' => '123456789', // additional field
                    'order' => 1,
                    'enabled' => true,
                    '_booking_open_window' => true,
                    '_booking_close_window' => true,
                    'booking_enabled' => true,
                    'booking_weeks_display' => 'string',
                    'booking_cancel_window' => 0,
                    'booking_open_window' => 0,
                    'booking_close_window' => 'test',
                    'booking_time_slot_length' => 0,
                ],
                new TrainersModel(
                    [
                        'order' => 1,
                        'enabled' => true,
                        '_booking_open_window' => true,
                        '_booking_close_window' => true,
                        'booking_enabled' => true,
                        'booking_weeks_display' => 'string',
                        'booking_cancel_window' => 0,
                        'booking_open_window' => 0,
                        'booking_close_window' => 'test',
                        'booking_time_slot_length' => 0,
                    ]
                ),
            ],
            '4 Empty array fields and return correct structure' => [
                [],
                new TrainersModel(
                    [
                        'order' => 0,
                        'enabled' => false,
                        '_booking_open_window' => false,
                        '_booking_close_window' => false,
                        'booking_enabled' => false,
                        'booking_weeks_display' => '',
                        'booking_cancel_window' => 0,
                        'booking_open_window' => 0,
                        'booking_close_window' => '',
                        'booking_time_slot_length' => 0,
                    ]
                ),
            ],
        ];
    }
}
