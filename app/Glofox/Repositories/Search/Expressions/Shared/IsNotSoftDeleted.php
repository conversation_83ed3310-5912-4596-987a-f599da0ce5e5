<?php

namespace Glofox\Repositories\Search\Expressions\Shared;

use Glofox\Datasource\Exceptions\InvalidMongoIdException;
use Glofox\Repositories\Filters\Evaluable;
use Glofox\Repositories\Search\Expressions\Conditionable;
use Glofox\Repositories\Search\Expressions\HasConditions;
use Glofox\Repositories\Search\Expressions\Key;
use Webmozart\Expression\Expr;

class IsNotSoftDeleted extends Key implements Evaluable, Conditionable
{
    use HasConditions;

    public function __construct()
    {
        parent::__construct($this->field(), Expr::equals(null));
    }

    public function field() : string
    {
        return 'deleted';
    }
}
