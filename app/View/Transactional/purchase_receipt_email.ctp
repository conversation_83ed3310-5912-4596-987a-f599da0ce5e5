<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <link rel="preconnect" href="https://fonts.gstatic.com" />
    <meta name="x-apple-disable-message-reformatting" />
    <style>
    <?php include sprintf('%s/purchase_receipt_email.css', __DIR__);
    ?>
    </style>
    <title>Email</title>
</head>
<body class="main_bg" style="width:100%;">
    <div class="main_body" style="border-top-color: <?php echo $branch->mainColor(); ?>"> 
      <table
        align="middle"
        border="0"
        cellpadding="0"
        cellspacing="0"
        width="100%"
      >
        <tr valign="middle">
          <td></td>
          <td valign="middle" width="125">
            <div class="logo">
                <img class="img-logo" width="125" src="<?php echo $branch->logoUrl(); ?>" alt="studio logo" />
            </div>
          </td>
          <td></td>
        </tr>
      </table>
        <span class="welcome_message">
           [content]
        </span>
        <div class="item_container" style="border-left-color: <?php echo $branch->mainColor(); ?>">
            <div class='item_wrapper'>
                <div class='item'>
                    <table>
                        <colgroup>
                            <col class='name_col'>
                            <col span="1">
                        </colgroup>
                        <tbody>
                            <tr>
                                <td>
                                    <p>
                                        <?php echo $charge->name(); ?>
                                    </p>
                                </td>
                                <td>
                                    <p class='price_col'>
                                        <?php echo $charge->price(); ?>
                                    </p>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    <?php if ($charge->hasInterval() && !$charge->hasUpfrontFee()) { ?>
                    <table>
                        <colgroup>
                            <col class='name_col'>
                            <col span="1">
                        </colgroup>
                        <tbody>
                            <tr class="subtitle">
                                <td>
                                    <p>
                                        <?php echo sprintf(trans('PURCHASE_RECEIPT_EMAIL_EVERY_INTERVAL'), $charge->interval()); ?>
                                    </p>
                                </td>
                                <td>
                                    <p class='price_col'>
                                        <?php echo sprintf(trans('PURCHASE_RECEIPT_EMAIL_PER_INTERVAL'), $charge->interval()); ?>
                                    </p>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    <?php } ?>
                    <?php if ($charge->hasDiscounts()) {?>
                    <table>
                        <colgroup>
                            <col class='name_col'>
                            <col span="1">
                        </colgroup>
                        <tbody>
                            <tr class="discount">
                                <td>
                                </td>
                                <td>
                                    <p>
                                        <?php echo sprintf(trans('PURCHASE_RECEIPT_DISCOUNTED_FROM'), $charge->discountName()); ?>
                                    </p>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    <?php } ?>
                </div>
            </div>
        </div>
        <!-- This section is for when we send the same email for upfront fee and subscription charge -->
        <?php //if ($charge->hasUpfrontFee()) { ?>
        <!--    <div class="item_container" style="border-left-color: --><?php //echo $branch->mainColor(); ?>
        <!--">-->
        <!--        <div class='item_wrapper'>-->
        <!--            <div class='item'>-->
        <!--                <table>-->
        <!--                    <tbody>-->
        <!--                    <tr>-->
        <!--                        <td><p class="item_text">-->
        <?php //echo trans('PURCHASE_RECEIPT_EMAIL_JOINING_FEE'); ?>
        <!--</p></td>-->
        <!--                        <td><p class='price_col'>-->
        <?php //echo sprintf('%s %s', $branch->currency(), $charge->upfrontFee()); ?>
        <!--</p></td>-->
        <!--                    </tr>-->
        <!--                    </tbody>-->
        <!--                </table>-->
        <!--            </div>-->
        <!--        </div>-->
        <!--    </div>-->
        <?php //} ?>
        <div class="description_container">
            <p>
                <?php if ($charge->hasInterval() && !$charge->hasUpfrontFee()) { ?>
                <?php echo $description->subscriptionDescription(); ?>
                <?php echo trans('PURCHASE_RECEIPT_EMAIL_WE_WILL_NOTIFY_YOU_OF_ANY_CHANGE'); ?>
                <?php } else { ?>
                <?php echo $description->noneSubscriptionDescription(); ?>
                <?php } ?>
                <br><br>
                <?php echo sprintf(trans('PURCHASE_RECEIPT_EMAIL_ISSUE_WITH_PAYMENT'), $branch->email()); ?>
            </p>
        </div>
        <div class='footer_container'>
            <footer class="footer">
                <div class="footer_content_wrapper">
                    <p>
                        <img src="https://cdn.glofox.com/icons/attach_file_24px.png" alt="attach-clip">
                        <?php echo trans('PURCHASE_RECEIPT_EMAIL_SEE_ATTACHMENT_FOR_DETAILED_RECEIPT'); ?>
                    </p>
                </div>
            </footer>
        </div>
    </div>
    <div class="contact_container">
        <div class="contact_wrapper">
        <table align="center" >
                <tbody>
                    <tr>
                        <td class="powered">
                            <img class="glofox_logo" src="https://cdn.glofox.com/icons/glofox.png" alt="glofox-logo">
                            <p class="item_text">
                                <a style="color:#506388; text-decoration: none;" href="https://www.glofox.com">
                                    <?php echo trans('TRANSACTIONAL_VARIABLE_POWERED_BY'); ?>
                                </a>
                            </p>
                        </td>
                        <td>
                            <p class='price_col'>
                                <a style="color: <?php echo $branch->mainColor(); ?>; text-decoration: none">
                                    <?php echo trans('TRANSACTIONAL_VARIABLE_CONTACT_YOUR_STUDIO'); ?>
                                </a>
                            </p>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</body>
</html>