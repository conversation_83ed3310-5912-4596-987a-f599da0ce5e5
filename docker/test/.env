# General
GLOFOX_DEBUG_LEVEL=0
OPENSHIFT_APP_NAME=local
PHP_ENABLE_OPCACHE=1
PHP_XDEBUG_ENABLE=1
XDEBUG_SESSION=docker
SERVER=local
REGION=local

# Bind to docker host
GLOFOX_DATABASE_HOST=**********

# Force database name as test
GLOFOX_DATABASE_NAME=test

# Default mongodb port
GLOFOX_DATABASE_PORT=27017

# Pusher
GLOFOX_PUSHER_URL="http://localhost/"

# Test keys/endpoints
GLOFOX_STRIPE_CUSTOM_EU_PUBLISHABLE_KEY=pk_test_1loGihTKRmyb83crV9eKxhsv
GLOFOX_STRIPE_CUSTOM_US_PUBLISHABLE_KEY=pk_test_M4tZMQtambxuT70BtHzkyXAJ
GLOFOX_WEB_COMPONENT_HTTP_HOST=https://payments-web-service-dev.aws.glofox.com
GLOFOX_WEB_COMPONENT_SECRET=aJ3aZznpm3Mb2JdfNBgyK9pyLGfcqSc
GLOFOX_GRPC_ACCOUNT_CLIENT_HOST=payments-dev.aws.glofox.com:44441
GLOFOX_GRPC_TRANSACTION_CLIENT_HOST=payments-dev.aws.glofox.com:44442
GLOFOX_GRPC_SUBSCRIPTION_CLIENT_HOST=payments-dev.aws.glofox.com:44443
GLOFOX_AWS_S3_ACCESS_KEY=AKIAIOSFODNN7EXAMPLE
GLOFOX_AWS_S3_SECRET_KEY=wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY
GLOFOX_AWS_S3_BUCKET=glofox
GLOFOX_AWS_S3_HOST=s3.amazonaws.com
GLOFOX_ANDROID_GCM_KEY=
GLOFOX_IOS_APNS_KEY=athlone09
GLOFOX_ROOT_DOMAIN=https://development.glofox.com

# Codacy token to update coverage
CODACY_PROJECT_TOKEN=d44939c71bce4ab68746debc0244203a
GLOFOX_SENTRY_URL=

JWT_SALT_GENERATE=glofox_test_salt

JWT_SALT_VERIFY=glofox_salt1,glofox_salt2

PASSWORD_HASH_COST=10

EAGREEMENT_MUST_SIGN_DATE="2021-05-20 00:00:01"

URL_SIGNATURE_KEY=not-so-random-key

PASSWORD_RESET_TOKEN_VALIDITY=60
PASSWORD_RESET_TOKEN_SIZE=24

LAUNCH_DARKLY_SDK_KEY=mocked-key
LAUNCH_DARKLY_API_URL="https://app.launchdarkly.com/api/"
LAUNCH_DARKLY_API_URL=check-ssm

GOOGLE_RECAPTCHA_BASE_URL="http://localhost:3001/"
GOOGLE_RECAPTCHA_SECRET="very-secrety-secret"

AUTHENTICATION_SERVICE_URL="http://localhost:3000/"
