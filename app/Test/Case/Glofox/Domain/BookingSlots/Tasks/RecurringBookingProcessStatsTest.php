<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\BookingSlots\Tasks;

use App;
use Glofox\Domain\BookingSlots\Models\BookingSlot;
use Glofox\Domain\BookingSlots\Tasks\RecurringBookingProcessStats;
use GlofoxTestCase;

App::import('Test/Case', 'GlofoxTestCase');

class RecurringBookingProcessStatsTest extends GlofoxTestCase
{
    public $fixtures = [];

    public function testCalculateFailed(): void
    {
        $exampleError = 'Example error';
        $expectedErrorCodes = json_encode([$exampleError]);

        $bookingSlot = BookingSlot::make();
        $bookingSlot->markAsFailed($exampleError);

        $processStats = new RecurringBookingProcessStats();
        $processStats->calculate($bookingSlot);

        $this->assertEquals(1, $processStats->getFailed());
        $this->assertEquals($expectedErrorCodes, $processStats->getErrorCodes());
        $this->assertEquals(0, $processStats->getProcessed());
        $this->assertEquals(0, $processStats->getBooked());
        $this->assertEquals(0, $processStats->getReserved());
    }

    public function testCalculateBookableOnly(): void
    {
        $bookingSlot = BookingSlot::make(['bookable' => true]);
        $bookingSlot->markAsProcessed();

        $processStats = new RecurringBookingProcessStats();
        $processStats->calculate($bookingSlot);

        $this->assertEquals(0, $processStats->getFailed());
        $this->assertNull($processStats->getErrorCodes());
        $this->assertEquals(1, $processStats->getProcessed());
        $this->assertEquals(1, $processStats->getBooked());
        $this->assertEquals(0, $processStats->getReserved());
    }

    public function testCalculateReservedOnly(): void
    {
        $bookingSlot = BookingSlot::make(['reservable' => true]);
        $bookingSlot->markAsProcessed();

        $processStats = new RecurringBookingProcessStats();
        $processStats->calculate($bookingSlot);

        $this->assertEquals(0, $processStats->getFailed());
        $this->assertNull($processStats->getErrorCodes());
        $this->assertEquals(1, $processStats->getProcessed());
        $this->assertEquals(0, $processStats->getBooked());
        $this->assertEquals(1, $processStats->getReserved());
    }

    public function testCalculateReservableAndBookable(): void
    {
        $bookingSlot = BookingSlot::make(['reservable' => true, 'bookable' => true]);
        $bookingSlot->markAsProcessed();

        $processStats = new RecurringBookingProcessStats();
        $processStats->calculate($bookingSlot);

        $this->assertEquals(0, $processStats->getFailed());
        $this->assertNull($processStats->getErrorCodes());
        $this->assertEquals(1, $processStats->getProcessed());
        $this->assertEquals(1, $processStats->getBooked());
        $this->assertEquals(0, $processStats->getReserved());
    }

    public function testIfStatusIsNotSetStatsNotCalculated(): void
    {
        $bookingSlot = BookingSlot::make(['reservable' => true, 'bookable' => true]);

        $processStats = new RecurringBookingProcessStats();
        $processStats->calculate($bookingSlot);

        $this->assertEquals(0, $processStats->getFailed());
        $this->assertNull($processStats->getErrorCodes());
        $this->assertEquals(0, $processStats->getProcessed());
        $this->assertEquals(0, $processStats->getBooked());
        $this->assertEquals(0, $processStats->getReserved());
    }
}
