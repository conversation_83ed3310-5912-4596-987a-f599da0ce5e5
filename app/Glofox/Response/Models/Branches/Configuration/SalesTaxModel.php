<?php

declare(strict_types=1);

namespace Glofox\Response\Models\Branches\Configuration;

use Glofox\Response\BaseModel;

/**
 * @SuppressWarnings(PHPMD)
 */
class SalesTaxModel extends BaseModel
{

	public string $legal_name = '';

	public string $tax_number = '';

	public string $company_id = '';

	protected array $allowedFields = [
		'legal_name',
		'tax_number',
		'company_id'
	];

	public function __construct(array $data)
	{
		$this->setModelFields($data);
	}
}
