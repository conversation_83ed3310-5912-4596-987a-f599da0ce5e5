<?php

declare(strict_types=1);

use Glofox\Payments\Entities\Invoice\Models\Invoice;
use Glofox\Payments\Entities\Invoice\Models\InvoiceAddress;
use Glofox\Payments\Entities\Invoice\Models\InvoiceAmount;
use Glofox\Response\Models\Invoice\InvoiceAddressModel;
use Glofox\Response\Models\Invoice\InvoiceAmountModel;
use Glofox\Response\Models\Invoice\InvoiceModel;
use Glofox\Response\Transformers\Invoices\InvoicesTransformer;

App::import('Test/Case', 'GlofoxTestCase');

class InvoicesTransformerTest extends GlofoxTestCase
{
    public $fixtures = [];

    public function testSetFromPaymentProvider(): void
    {
        $invoice = $this->generateInvoice();
        $model = new InvoiceModel($this->invoiceToArray($invoice));

        $transformer = new InvoicesTransformer();
        $transformedModel = $transformer->setFromPaymentProvider($invoice);

        $this->assertEquals($model, $transformedModel);
    }

    private function generateInvoice(): Invoice
    {
        $invoice = new Invoice();
        $invoice->setDate(time());
        $invoice->setYear(2018);
        $invoice->setMonth(5);
        $invoice->setNumber('523gfsd');
        $invoice->setAccountNumber('reewr34345');
        $invoice->setVatNumber('ewr423');
        $invoice->setCurrency('EUR');

        $address = new InvoiceAddress();
        $address->setOwner('own');
        $address->setName('test');
        $address->setAddressLine1('add');
        $address->setAddressLine2('res');
        $address->setAddressLine3('s');
        $invoice->setAddress($address);

        $amounts = new InvoiceAmount();
        $amounts->setProcessingFees(1);
        $amounts->setProcessingFeesVat(2);
        $amounts->setDisputeFees(3);
        $amounts->setDisputeFeesVat(4);
        $amounts->setTotalVat(5);
        $amounts->setTotal(6);
        $amounts->setDebited(7);
        $amounts->setTotalDue(8);
        $invoice->setAmounts($amounts);

        return $invoice;
    }

    private function invoiceToArray(Invoice $invoice): array
    {
        $array = [
            'date' => $invoice->date(),
            'month' => $invoice->month(),
            'year' => $invoice->year(),
            'number' => $invoice->number(),
            'account_number' => $invoice->accountNumber(),
            'vat_number' => $invoice->vatNumber(),
            'currency' => $invoice->currency(),
        ];

        $address = $invoice->address();
        $array['address'] = new InvoiceAddressModel([
            'owner' => $address->owner(),
            'name' => $address->name(),
            'address_first' => $address->addressLine1(),
            'address_second' => $address->addressLine2(),
            'address_third' => $address->addressLine3(),
        ]);

        $amount = $invoice->amount();
        $array['amounts'] = new InvoiceAmountModel([
            'processing_fees' => $amount->processingFees(),
            'processing_fees_vat' => $amount->processingFeesVat(),
            'dispute_fees' => $amount->disputeFees(),
            'dispute_fees_vat' => $amount->disputeFeesVat(),
            'total_vat' => $amount->totalVat(),
            'total' => $amount->total(),
            'debited' => $amount->debited(),
            'total_due' => $amount->totalDue(),
        ]);

        return $array;
    }
}
