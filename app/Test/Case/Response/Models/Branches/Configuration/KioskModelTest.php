<?php

App::import('Test/Case', 'GlofoxTestCase');

use Glofox\Response\Models\Branches\Configuration\KioskModel;

/**
 * Test cases
 * 1 Model contains all fields with data
 * 2 Missing fields and returns correct structure
 * 3 Additional fields and return correct structure
 * 4 Empty array fields and return correct structure.
 *
 * Class KioskModelTest
 */
class KioskModelTest extends GlofoxTestCase
{
    public $fixtures = [];

    /**
     * @dataProvider modelProvider
     */
    public function testModel($dataSent, $result): void
    {
        $model = new KioskModel($dataSent);
        $this->assertEquals($model, $result);
    }

    public function modelProvider(): array
    {
        return [
            '1 Model contains all fields with data' => [
                [
                    'colors' => null,
                    'sign_in_view' => 'class',
                ],
                new KioskModel(
                    [
                        'colors' => null,
                        'sign_in_view' => 'class',
                    ]
                ),
            ],
            '2 Missing fields and returns correct structure' => [
                [
                    // colors missing
                    'sign_in_view' => 'class',
                ],
                new KioskModel(
                    [
                        'colors' => null,
                        'sign_in_view' => 'class',
                    ]
                ),
            ],
            '3 Additional fields and return correct structure' => [
                [
                    'other' => '', // additional
                    'colors' => null,
                    'sign_in_view' => 'class',
                ],
                new KioskModel(
                    [
                        'colors' => null,
                        'sign_in_view' => 'class',
                    ]
                ),
            ],
            '4 Empty array fields and return correct structure' => [
                [],
                new KioskModel(
                    [
                        'colors' => null,
                        'sign_in_view' => '',
                    ]
                ),
            ],
        ];
    }
}
