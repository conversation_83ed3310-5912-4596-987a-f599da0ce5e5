<?php

App::import('Test/Case', 'GlofoxTestCase');

use Glofox\Response\Models\Branches\Features\ClassesModel;

/**
 * Test cases
 * 1 Model contains all fields with data
 * 2 Missing fields and returns correct structure
 * 3 Additional fields and return correct structure
 * 4 Empty array fields and return correct structure.
 *
 * Class ClassesModelTest
 */
class ClassesModelTest extends GlofoxTestCase
{
    public $fixtures = [];

    /**
     * @dataProvider modelProvider
     */
    public function testModel($dataSent, $result): void
    {
        $model = new ClassesModel($dataSent);
        $this->assertEquals($model, $result);
    }

    public function modelProvider(): array
    {
        return [
            '1 Model contains all fields with data' => [
                [
                    'order' => 1,
                    'enabled' => true,
                    'weeks_display' => 'string',
                    'levels' => '5',
                    'total_display' => false,
                    'books_display' => false,
                    'books_detail_display' => true,
                ],
                new ClassesModel(
                    [
                        'order' => 1,
                        'enabled' => true,
                        'weeks_display' => 'string',
                        'levels' => '5',
                        'total_display' => false,
                        'books_display' => false,
                        'books_detail_display' => true,
                    ]
                ),
            ],
            '2 Missing fields and returns correct structure' => [
                [
                    // order missing
                    'enabled' => true,
                    'weeks_display' => 'string',
                    'levels' => '5',
                    'total_display' => false,
                    'books_display' => false,
                    'books_detail_display' => true,
                ],
                new ClassesModel(
                    [
                        'order' => 0,
                        'enabled' => true,
                        'weeks_display' => 'string',
                        'levels' => '5',
                        'total_display' => false,
                        'books_display' => false,
                        'books_detail_display' => true,
                    ]
                ),
            ],
            '3 Additional fields and return correct structure' => [
                [
                    'additional' => '456', // additional
                    'order' => 1,
                    'enabled' => true,
                    'weeks_display' => 'string',
                    'levels' => '5',
                    'total_display' => false,
                    'books_display' => false,
                    'books_detail_display' => true,
                ],
                new ClassesModel(
                    [
                        'order' => 1,
                        'enabled' => true,
                        'weeks_display' => 'string',
                        'levels' => '5',
                        'total_display' => false,
                        'books_display' => false,
                        'books_detail_display' => true,
                    ]
                ),
            ],
            '4 Empty array fields and return correct structure' => [
                [],
                new ClassesModel(
                    [
                        'order' => 0,
                        'enabled' => false,
                        'weeks_display' => '',
                        'levels' => '',
                        'total_display' => false,
                        'books_display' => false,
                        'books_detail_display' => false,
                    ]
                ),
            ],
        ];
    }
}
