<?php

declare(strict_types=1);

namespace Glofox\Domain\Payments\UseCase\Bookings;

use Exception;
use Glofox\Datasource\Exceptions\InvalidMongoIdException;
use Glofox\Domain\Activities\Exceptions\ActivityNotFoundException;
use Glofox\Domain\Activities\Models\Activity;
use Glofox\Domain\Activities\Repositories\ActivitiesRepository;
use Glofox\Domain\Bookings\Commands\UpdateBookingAfterProcessPaymentHandler;
use Glofox\Domain\Bookings\Commands\UpdateBookingAfterProcessPaymentParams;
use Glofox\Domain\Bookings\Events\ChargeWasCreatedForBooking;
use Glofox\Domain\Bookings\Models\Booking;
use Glofox\Domain\Bookings\ValueObjects\ProcessPaymentResult;
use Glofox\Domain\Branches\Exceptions\InvalidBranchIdException;
use Glofox\Domain\Branches\Models\Branch;
use Glofox\Domain\Charges\Events\ChargeWasUpdated;
use Glofox\Domain\Charges\Models\Charge;
use Glofox\Domain\Memberships\Services\AddonEligibilityServiceInterface;
use Glofox\Domain\Memberships\Validation\ValidateUserMembershipExpiryDateGivenEventStart;
use Glofox\Domain\PaymentMethods\Type as PaymentMethodType;
use Glofox\Domain\SalesTaxes\ServiceType;
use Glofox\Domain\TimeSlotPatterns\Exceptions\TimeSlotPatternNotFoundException;
use Glofox\Domain\TimeSlots\Exceptions\TimeSlotNotFoundException;
use Glofox\Domain\TimeSlots\Models\TimeSlot;
use Glofox\Domain\TimeSlots\Repositories\TimeSlotRepository;
use Glofox\Domain\Users\Exceptions\UserNotFoundException;
use Glofox\Domain\Users\Exceptions\UserOriginBranchIdException;
use Glofox\Domain\Users\Models\User;
use GlofoxEnvironment;
use Glofox\Events\EventManager;
use Psr\Log\LoggerInterface;
use User as UserCakeModel;
use UserCredit as UserCreditCakeModel;

class ProcessTimeSlotPayment
{
    private TimeSlotRepository $timeSlotRepo;
    private ActivitiesRepository $activitiesRepo;
    private AddonEligibilityServiceInterface $addonEligibilityService;
    private ExecuteBookingCharge $chargeExecutor;
    private UpdateBookingAfterProcessPaymentHandler $bookingHandler;
    private ValidateUserMembershipExpiryDateGivenEventStart $validateUserMembershipExpiryDateGivenEventStart;
    private EventManager $eventManager;
    private LoggerInterface $logger;
    private UserCakeModel $userCakeModel;
    private UserCreditCakeModel $userCreditCakeModel;

    public function __construct(
        TimeSlotRepository $timeSlotRepo,
        ActivitiesRepository $activitiesRepo,
        AddonEligibilityServiceInterface $addonEligibilityService,
        ExecuteBookingCharge $chargeExecutor,
        UpdateBookingAfterProcessPaymentHandler $bookingHandler,
        ValidateUserMembershipExpiryDateGivenEventStart $validateUserMembershipExpiryDateGivenEventStart,
        EventManager $eventManager,
        LoggerInterface $logger,
        UserCakeModel $userCakeModel,
        UserCreditCakeModel $userCreditCakeModel
    ) {
        $this->timeSlotRepo = $timeSlotRepo;
        $this->activitiesRepo = $activitiesRepo;
        $this->addonEligibilityService = $addonEligibilityService;
        $this->chargeExecutor = $chargeExecutor;
        $this->bookingHandler = $bookingHandler;
        $this->validateUserMembershipExpiryDateGivenEventStart = $validateUserMembershipExpiryDateGivenEventStart;
        $this->eventManager = $eventManager;
        $this->logger = $logger;
        $this->userCakeModel = $userCakeModel;
        $this->userCreditCakeModel = $userCreditCakeModel;
    }

    /**
     * @throws InvalidBranchIdException
     * @throws InvalidMongoIdException
     * @throws UserNotFoundException
     * @throws TimeSlotPatternNotFoundException
     * @throws UserOriginBranchIdException
     */
    public function handle(ProcessTimeSlotPaymentParams $params): ProcessPaymentResult
    {
        $booking = $params->booking();

        try {
            $timeSlot = $this->timeSlotRepo->getById($params->booking()->timeSlotId());
        } catch (TimeSlotNotFoundException $e) {
            return ProcessPaymentResult::createFromFailure($e->getMessage(), 'TIMESLOT_NOT_FOUND');
        } catch (InvalidMongoIdException $e) {
            return ProcessPaymentResult::createFromFailure($e->getMessage(), 'INVALID_MONGO_ID');
        }

        try {
            $activity = $this->activitiesRepo->getByBookingId($booking->id());
        } catch (ActivityNotFoundException $e) {
            return ProcessPaymentResult::createFromFailure($e->getMessage(), 'ACTIVITY_NOT_FOUND');
        }

        $userCredit = null;

        $addonValidationResult = $this->addonEligibilityService->validate($params->member(), $timeSlot);
        if (!$addonValidationResult) {
            $this->logger->info(
                sprintf(
                    'User %s has no eligible addons for timeslot %s, starting membership check',
                    $params->member()->id(),
                    $timeSlot->id()
                )
            );

            $membershipValidationResult = $this->validateMemberMembership(
                $params->branch(),
                $params->member(),
                $timeSlot
            );
            if (!$membershipValidationResult['success']) {
                return ProcessPaymentResult::createFromFailure(
                    (string)$membershipValidationResult['message'],
                    (string)$membershipValidationResult['message_code'],
                );
            }

            $userCredit = $membershipValidationResult['userCredits'];
        }

        try {
            $spendCredits = $this->payWithCredits($booking, $userCredit);
        } catch (Exception $e) {
            return ProcessPaymentResult::createFromFailure(
                $e->getMessage(),
                'OCCURRED_A_PROBLEM_WHILE_SPENDING_CREDITS'
            );
        }

        [$externalRef, $metadata, $description] = $this->resolveDetails(
            $params->booking(),
            $params->member(),
            $params->paymentMethod(),
            $timeSlot
        );

        $chargeExecutorParams = new ExecuteBookingChargeParams(
            $params->member(),
            $params->branch(),
            $params->paymentMethod(),
            $params->soldByUserId(),
            $metadata,
            $params->discounts(),
            ServiceType::map()[$booking->model()],
            $externalRef,
            $description,
            $params->price(),
            $booking->totalBookings(),
            $params->isOffSession(),
        );

        $bookingHandlerParams = new UpdateBookingAfterProcessPaymentParams(
            $booking,
            $params->member(),
            $params->paymentMethod()
        );

        $intents = [];
        if (!$addonValidationResult && empty($spendCredits)) {
            try {
                $chargeResult = $this->chargeExecutor->execute($chargeExecutorParams);
            } catch (Exception $e) {
                $this->logger->error('Error while process_payment in Bookings Controller', [
                    'booking_id' => $booking->id(),
                    'exception' => $e->getTrace(),
                ]);

                return ProcessPaymentResult::createFromFailure(
                    $e->getMessage(),
                    'OCCURRED_A_PROBLEM_WHILE_PROCESS_PAYMENT'
                );
            }

            try {
                $this->updateActivity($activity, $chargeResult->charge());
            } catch (Exception $e) {
                $this->logger->error(
                    sprintf(
                        'Cannot update activity %s with charge_id %s and invoice_id %s',
                        $activity->id(),
                        $chargeResult->charge()->id(),
                        $chargeResult->charge()->invoiceId()
                    )
                );

                return ProcessPaymentResult::createFromFailure(
                    $e->getMessage(),
                    'OCCURRED_A_PROBLEM_UPDATING_THE_ACTIVITY'
                );
            }

            $this->eventManager->emit(ChargeWasCreatedForBooking::class, [$chargeResult->charge(), $booking]);
            $this->eventManager->emit(ChargeWasUpdated::class, [$chargeResult->charge()]);

            if ($chargeResult->transaction() && !empty($chargeResult->transaction()->serviceProviderIntentId())) {
                $intents[] = $chargeResult->transaction()->serviceProviderIntentId();
            }

            $bookingHandlerParams->setCharge($chargeResult->charge());
        }

        if ($addonValidationResult !== null) {
            $bookingHandlerParams->setAddon($addonValidationResult);
        }

        if (!empty($spendCredits)) {
            $bookingHandlerParams->setCredits($spendCredits);
        }

        try {
            $booking = $this->bookingHandler->handle($bookingHandlerParams);
        } catch (Exception $e) {
            return ProcessPaymentResult::createFromFailure(
                $e->getMessage(),
                'OCCURRED_A_PROBLEM_UPDATING_THE_BOOKING'
            );
        }

        return ProcessPaymentResult::createFromSuccess($booking, $intents);
    }

    /**
     * @throws InvalidBranchIdException
     * @throws InvalidMongoIdException
     * @throws UserNotFoundException
     * @throws TimeSlotPatternNotFoundException
     */
    private function validateMemberMembership(Branch $branch, User $member, TimeSlot $timeSlot): array
    {
        $validateMembership = false;
        try {
            $this->validateUserMembershipExpiryDateGivenEventStart
                ->execute($member->membership(), $timeSlot->timeStart($branch->timezone()), $branch->timezone());
            $validateMembership = true;
        } catch (Exception $e) {
        }

        $asPayg = !$validateMembership;
        $timeSlotPattern = $timeSlot->pattern();
        $relatedModel = $timeSlot->relatedModel();
        $defaultPrice = $timeSlotPattern->defaultPrice();
        $allowedMemberTypes = $timeSlotPattern->allowedMemberTypes();

        if ($timeSlotPattern->allowedMemberTypes()->isEmpty()) {
            $defaultPrice = null;
            $allowedMemberTypes = $relatedModel->allowedMemberTypes();
        }

        $validateAllowedGroups = $this->userCakeModel->validateAllowedGroups(
            $member->toLegacy(),
            $branch->id(),
            $allowedMemberTypes->toArray(),
            $asPayg,
            $defaultPrice,
            $timeSlotPattern->modelId()
        );

        $userCredits = $this->userCreditCakeModel->findCreditForBooking(
            $branch->id(),
            $member->id(),
            $timeSlot->date($branch->timezone())->toDateTimeString(),
            $timeSlot->model()->getValue(),
            $timeSlot->modelId(),
            $relatedModel->categories()->toArray()
        );

        if (!$validateAllowedGroups['success'] && !$userCredits) {
            return $validateAllowedGroups;
        }

        return [
            'success' => true,
            'userCredits' => $userCredits,
        ];
    }

    /**
     * @throws Exception
     */
    private function payWithCredits(Booking $booking, ?array $userCredit): array
    {
        if (empty($userCredit)) {
            return [];
        }

        return $this->userCreditCakeModel->spendUserCredit($userCredit['UserCredit']['_id'], $booking->id());
    }

    /**
     * @throws UserOriginBranchIdException
     */
    private function resolveDetails(
        Booking $booking,
        User $member,
        PaymentMethodType $paymentMethod,
        TimeSlot $timeSlot
    ): array {
        $metadata = [
            'namespace' => $member->namespace(),
            'branch_id' => $member->originBranchId(),
            'glofox_event' => 'book_time_slot',
            'model' => $timeSlot->model()->getValue(),
            'model_id' => $timeSlot->modelId(),
            'user_id' => $member->id(),
            'user_name' => $member->fullName(),
            'environment' => GlofoxEnvironment::currentEnvironment(),
            'payment_method' => $paymentMethod->getValue(),
            'total_bookings' => $booking->totalBookings(),
        ];
        $description = sprintf('Booking for Timeslot (%s)', $timeSlot->name());

        return [$timeSlot->modelId(), $metadata, $description];
    }

    /**
     * @throws Exception
     */
    private function updateActivity(Activity $activity, Charge $charge): void
    {
        $activityAsArray = $activity->toArray();

        data_set($activityAsArray, 'event_context.charge_id', $charge->id());
        data_set($activityAsArray, 'event_context.invoice_id', $charge->invoiceId());

        $this->activitiesRepo->save($activityAsArray);
    }
}
