<?php

namespace Glofox\Response\Models\Branches\Features;

use Glofox\Response\BaseModel;

/**
 * @SuppressWarnings(PHPMD)
 */
class CoursesModel extends BaseModel
{
    /**
     * @var bool
     */
    public $enabled = false;

    /**
     * @var int
     */
    public $order = 0;

    /**
     * @var int
     */
    public $guest_bookings = 0;

    protected array $allowedFields = [
        'order',
        'enabled',
        'guest_bookings',
    ];

    public function __construct(array $data)
    {
        $this->setModelFields($data);
    }
}
