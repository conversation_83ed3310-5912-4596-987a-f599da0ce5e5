<?php

namespace Glofox\Payments\Providers\Gateway\ProtoParsers;

use Carbon\Carbon;
use Glofox\Calendar\ICalendar\Event;
use Glofox\Calendar\ICalendar\ICalendar;
use Glofox\Calendar\ICalendar\Reader;
use Glofox\Payments\Entities\Subscription\Models\Subscription as ProviderSubscription;
use Google\Protobuf\Internal\RepeatedField;
use GRPC\Payments\SubscriptionService\Subscription;

/**
 * Class SubscriptionParser.
 */
class SubscriptionParser
{
    /**
     * @param Subscription $subscription
     *
     * @return ProviderSubscription
     */
    public function parseFromProtoMessage($subscription): ProviderSubscription
    {
        $providerSubscription = new ProviderSubscription();

        $cancelledAt = null;
        if ($subscription->getDeletedAt()) {
            $cancelledAt = Carbon::parse($subscription->getDeletedAt(), 'UTC')->getTimestamp();
        }

        $calendar = $this->parseCalendar($subscription);
        $amount = $this->getAmount($calendar);
        $metadata = $this->getMetadata($calendar);

        return $providerSubscription->setId($subscription->getID())
            ->setPlanId(0)
            ->setCustomerId($subscription->getCustomerID())
            ->setMerchantId($subscription->getMerchantID())
            ->setFee(0)
            ->setAmount($amount)
            ->setStatus('')
            ->setCurrentRetries($subscription->getCurrentRetries())
            ->setStartAt(Carbon::parse($subscription->getStartAt(), 'UTC')->getTimestamp())
            ->setTrialStartAt(0)
            ->setTrialEndAt(0)
            ->setCanceledAt($cancelledAt)
            ->setEndedAt(Carbon::parse($subscription->getDeletedAt(), 'UTC')->getTimestamp())
            ->setMetadata($metadata)
            ->setPaymentServiceId($subscription->getID())
            ->setPaymentServiceResponse($subscription->getICalRule())
            ->setLastExecutedAt(Carbon::parse($subscription->getLastExecutedAt(), 'UTC')->getTimestamp())
            ->setCreatedAt(
                Carbon::parse($subscription->getCreatedAt())->getTimestamp()
            )
            ->setUpdatedAt(
                Carbon::parse($subscription->getUpdatedAt())->getTimestamp()
            )
            ->setExternalRef($subscription->getExternalRef());
    }

    /**
     * @param RepeatedField|Subscription[] $subscriptions
     *
     * @return ProviderSubscription[]
     */
    public function parseFromProtoMessageCollection(RepeatedField $subscriptions): ?array
    {
        $providerSubscriptions = null;

        if (empty($subscriptions)) {
            return $providerSubscriptions;
        }

        foreach ($subscriptions as $subscription) {
            $providerSubscriptions[] = $this->parseFromProtoMessage($subscription);
        }

        return $providerSubscriptions;
    }

    private function parseCalendar(Subscription $subscription): ICalendar
    {
        $calendar = Reader::read($subscription->getICalRule());

        return $calendar;
    }

    private function getMetadata(ICalendar $calendar): array
    {
        /** @var Event $event */
        $event = $calendar->events()->first();
        if (!$event) {
            return [];
        }

        return (array) json_decode($event->getDescription()->get('descriptions'), true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    private function getAmount(ICalendar $calendar): int
    {
        /** @var Event $event */
        $event = $calendar->events()->first();
        if (!$event) {
            return 0;
        }

        return (int) $event->getDescription()->get('amount', 0);
    }
}
