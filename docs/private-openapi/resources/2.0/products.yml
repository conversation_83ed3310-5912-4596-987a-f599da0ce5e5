parameters:
  - schema:
      type: string
    name: branchId
    in: path
    required: true
get:
  tags:
    - Products
  summary: ''
  operationId: get-products-by-branch
  description: This end point returns the store products of a given branch
  x-internal: true
  security:
    - coreapi_auth: [ ]
  responses:
    '200':
      description: OK
      content:
        application/json:
          schema:
            allOf:
              - $ref: "../../schemas/pagination.yml"
              - type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: "../../schemas/product.yml"
    '404':
      description: Not Found
      content:
        application/json:
          schema:
            $ref: "../../schemas/error.yml"
    '403':
      description: Forbidden
      content:
        application/json:
          schema:
            $ref: "../../schemas/error.yml"