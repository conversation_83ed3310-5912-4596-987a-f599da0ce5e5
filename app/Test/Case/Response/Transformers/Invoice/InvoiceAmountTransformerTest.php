<?php

declare(strict_types=1);

use Glofox\Payments\Entities\Invoice\Models\InvoiceAmount;
use Glofox\Response\Models\Invoice\InvoiceAmountModel;
use Glofox\Response\Transformers\Invoices\InvoiceAmountTransformer;

App::import('Test/Case', 'GlofoxTestCase');

/**
 * Class InvoiceAmountTransformerTest.
 */
class InvoiceAmountTransformerTest extends GlofoxTestCase
{
    public $fixtures = [];

    public function testSetFromPaymentProvider()
    {
        $amount = $this->generateInvoiceAmount();
        $model = new InvoiceAmountModel($this->invoiceAmountToArray($amount));

        $transformer = new InvoiceAmountTransformer();
        $transformedModel = $transformer->setFromPaymentProvider($amount);

        $this->assertEquals($model, $transformedModel);
    }

    /**
     * @return InvoiceAmount
     */
    private function generateInvoiceAmount(): InvoiceAmount
    {
        $amount = new InvoiceAmount();
        $amount->setProcessingFees(1);
        $amount->setProcessingFeesVat(2);
        $amount->setDisputeFees(3);
        $amount->setDisputeFeesVat(4);
        $amount->setTotal(6);
        $amount->setDebited(7);
        $amount->setTotalDue(8);

        return $amount;
    }

    private function invoiceAmountToArray(InvoiceAmount $amount): array
    {
        $array = [
            'processing_fees' => $amount->processingFees(),
            'processing_fees_vat' => $amount->processingFeesVat(),
            'dispute_fees' => $amount->disputeFees(),
            'dispute_fees_vat' => $amount->disputeFeesVat(),
            'total_vat' => $amount->totalVat(),
            'total' => $amount->total(),
            'debited' => $amount->debited(),
            'total_due' => $amount->totalDue(),
        ];

        return $array;
    }
}
