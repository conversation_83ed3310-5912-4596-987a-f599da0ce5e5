<?php

declare(strict_types=1);

namespace Glofox\Domain\AppointmentSlots\UseCase;

use Glofox\Domain\AppointmentSlots\Exceptions\AppointmentSlotNotFoundException;
use Glofox\Domain\TimeSlots\Exceptions\TimeSlotNotFoundException;
use Glofox\Domain\TimeSlots\Models\TimeSlot;
use Glofox\Domain\TimeSlots\Repositories\TimeSlotRepository;
use Glofox\Domain\TimeSlotPatterns\Repositories\TimeSlotPatternsRepository;
use MongoDB\BSON\UTCDateTime;

class UpdateAppointmentSlot
{
    private TimeSlotRepository $timeSlotRepository;
    private TimeSlotPatternsRepository $timeSlotPatternsRepository;

    public function __construct(
        TimeSlotRepository $timeSlotRepository,
        TimeSlotPatternsRepository $timeSlotPatternsRepository
    ) {
        $this->timeSlotRepository = $timeSlotRepository;
        $this->timeSlotPatternsRepository = $timeSlotPatternsRepository;
    }

    /**
     * @throws AppointmentSlotNotFoundException
     */
    public function execute(UpdateAppointmentSlotParams $params): TimeSlot
    {
        $existingSlot = $this->timeSlotRepository->getActiveSlotById($params->slotId());
        $updateData = $this->buildUpdateData($params, $existingSlot);

        $success = $this->timeSlotRepository->updateAppointmentSlot($params->slotId(), $updateData);

        if (!$success) {
            throw AppointmentSlotNotFoundException::withId($params->slotId());
        }

        try {
            return $this->timeSlotRepository->getById($params->slotId());
        } catch (TimeSlotNotFoundException $e) {
            throw AppointmentSlotNotFoundException::withId($params->slotId());
        }
    }

    private function buildUpdateData(UpdateAppointmentSlotParams $params, TimeSlot $existingSlot): array
    {
        $updateData = [];

        $this->addBasicFields($params, $updateData);
        $this->addTimeFields($params, $existingSlot, $updateData);
        $this->addDateFields($params, $updateData);

        return $updateData;
    }

    private function addBasicFields(UpdateAppointmentSlotParams $params, array &$updateData): void
    {
        if ($params->staffId() !== null) {
            $updateData['staff_id'] = $params->staffId();
        }

    }

    private function addTimeFields(UpdateAppointmentSlotParams $params, TimeSlot $existingSlot, array &$updateData): void
    {
        $updateData['time_start'] = new UTCDateTime($params->startTime()->getTimestamp() * 1000);

        $timeSlotPatternId = $existingSlot->get('time_slot_pattern_id');
        if ($timeSlotPatternId !== null) {
            $appointment = $this->timeSlotPatternsRepository->getById($timeSlotPatternId);
            $timeFinish = clone $params->startTime();
            $timeFinish->addMinutes($appointment->timeSlotLength());
            $updateData['time_finish'] = new UTCDateTime($timeFinish->getTimestamp() * 1000);
        }
    }

    private function addDateFields(UpdateAppointmentSlotParams $params, array &$updateData): void
    {
        $utcDate = $params->startTime()->startOfDay();
        $updateData['date'] = new UTCDateTime($utcDate->getTimestamp() * 1000);
        $updateData['week_day'] = $utcDate->dayOfWeek;
    }
}
