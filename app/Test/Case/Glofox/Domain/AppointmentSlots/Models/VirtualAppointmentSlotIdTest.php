<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\AppointmentSlots\Models;

use App;
use Glofox\Domain\AppointmentSlots\Models\VirtualAppointmentSlot;
use Glofox\Domain\AppointmentSlots\Models\VirtualAppointmentSlotId;
use Glofox\Domain\TimeSlotPatterns\Models\TimeSlotPattern;
use GlofoxTestCase;
use JsonException;

App::import('Test/Case', 'GlofoxTestCase');

class VirtualAppointmentSlotIdTest extends GlofoxTestCase
{
    public $fixtures = [];
    private const TEST_STAFF_ID_1 = '644a78d510c576d3779e0d0e';
    /**
     * @var 2023-04-28 08:00:00
     */
    private const TEST_TIME_START_1 = 1_682_668_800;
    private const TEST_APPOINTMENT_ID_1 = 'test-appointment-id-1';

    /**
     * @throws JsonException
     */
    public function testDecodeIdFromVirtualSlot(): void
    {
        $id = $this->generateVirtualId(
            self::TEST_APPOINTMENT_ID_1,
            self::TEST_STAFF_ID_1,
            self::TEST_TIME_START_1
        );

        $virtualId = new VirtualAppointmentSlotId($id);

        $this->assertTrue($virtualId->isVirtual());
        $this->assertEquals(self::TEST_APPOINTMENT_ID_1, $virtualId->appointmentId());
        $this->assertEquals(self::TEST_STAFF_ID_1, $virtualId->staffId());
        $this->assertEquals(self::TEST_TIME_START_1, $virtualId->timeStart());
    }

    /**
     * @throws JsonException
     */
    public function testDecodeIdFromMongoId(): void
    {
        $id = '645a6080566133a42915ac34';

        $virtualId = new VirtualAppointmentSlotId($id);

        $this->assertFalse($virtualId->isVirtual());
        $this->assertNull($virtualId->appointmentId());
        $this->assertNull($virtualId->staffId());
        $this->assertNull($virtualId->timeStart());
    }

    /**
     * @throws JsonException
     */
    public function testEncodeId(): void
    {
        $virtualSlot = new VirtualAppointmentSlot(
            self::TEST_APPOINTMENT_ID_1,
            self::TEST_STAFF_ID_1,
            self::TEST_TIME_START_1,
            0,
            TimeSlotPattern::make([])
        );
        $expectedId = $this->generateVirtualId(
            $virtualSlot->appointmentId(),
            $virtualSlot->staffId(),
            $virtualSlot->timeStart()
        );

        $id = VirtualAppointmentSlotId::encode($virtualSlot);

        $this->assertEquals($expectedId, $id);
    }

    /**
     * @throws JsonException
     */
    private function generateVirtualId(string $appointmentId, string $staffId, int $timeStart): string
    {
        $virtualSlotData = [
            'staff_id' => $staffId,
            'appointment_id' => $appointmentId,
            'time_start' => $timeStart,
        ];
        return base64_encode(json_encode($virtualSlotData, JSON_THROW_ON_ERROR));
    }
}
