<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: mandate.proto

namespace GRPC\Payments\AccountService;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>grpc.CreateMandateReq</code>
 */
class CreateMandateReq extends \Google\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>int32 AccountID = 1;</code>
     */
    protected $AccountID = 0;
    /**
     * Generated from protobuf field <code>string MandateToken = 2;</code>
     */
    protected $MandateToken = '';
    /**
     * Generated from protobuf field <code>map<string, string> Options = 3;</code>
     */
    private $Options;
    /**
     * Generated from protobuf field <code>string RequestUUID = 4;</code>
     */
    protected $RequestUUID = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int $AccountID
     *     @type string $MandateToken
     *     @type array|\Google\Protobuf\Internal\MapField $Options
     *     @type string $RequestUUID
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Mandate::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>int32 AccountID = 1;</code>
     * @return int
     */
    public function getAccountID()
    {
        return $this->AccountID;
    }

    /**
     * Generated from protobuf field <code>int32 AccountID = 1;</code>
     * @param int $var
     * @return $this
     */
    public function setAccountID($var)
    {
        GPBUtil::checkInt32($var);
        $this->AccountID = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string MandateToken = 2;</code>
     * @return string
     */
    public function getMandateToken()
    {
        return $this->MandateToken;
    }

    /**
     * Generated from protobuf field <code>string MandateToken = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setMandateToken($var)
    {
        GPBUtil::checkString($var, True);
        $this->MandateToken = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>map<string, string> Options = 3;</code>
     * @return \Google\Protobuf\Internal\MapField
     */
    public function getOptions()
    {
        return $this->Options;
    }

    /**
     * Generated from protobuf field <code>map<string, string> Options = 3;</code>
     * @param array|\Google\Protobuf\Internal\MapField $var
     * @return $this
     */
    public function setOptions($var)
    {
        $arr = GPBUtil::checkMapField($var, \Google\Protobuf\Internal\GPBType::STRING, \Google\Protobuf\Internal\GPBType::STRING);
        $this->Options = $arr;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string RequestUUID = 4;</code>
     * @return string
     */
    public function getRequestUUID()
    {
        return $this->RequestUUID;
    }

    /**
     * Generated from protobuf field <code>string RequestUUID = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setRequestUUID($var)
    {
        GPBUtil::checkString($var, True);
        $this->RequestUUID = $var;

        return $this;
    }

}

