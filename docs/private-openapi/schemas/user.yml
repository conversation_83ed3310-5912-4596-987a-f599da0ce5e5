type: object
properties:
  _id:
    type: string
  branch_id:
    type: string
  namespace:
    type: string
  type:
    type: string
    enum:
      - ADMIN
      - MEMBER
      - RECEPTION
      - SUPERADMIN
      - TRAINER
  first_name:
    type: string
  last_name:
    type: string
  phone:
    type: string
  description:
    type: string
    nullable: true
  image:
    type: string
    nullable: true
  email:
    type: string
    format: email
  login:
    type: string
    format: email
  modified:
    type: string
    description: Date time Y-m-d H:i:s
  created:
    type: string
    description: Date time Y-m-d H:i:s
  categories:
    type: array
    items:
      type: string
  joined_at:
    type: string
    format: date-time
    description: This field can be an object with seconds and microseconds
  lead_status:
    type: string
    enum:
      - CLIENT
      - COLD
      - LEAD
      - MEMBER
      - TRIAL
      - UNTOUCHED
      - WARM
  name:
    type: string
  source:
    $ref: './user_source.yml'
  user_id:
    type: string
  WAIVER:
    type: boolean
