<?php

declare(strict_types=1);

namespace Glofox\Infrastructure\Flags\Flaggers;

use Glofox\Domain\Branches\Models\Branch;
use Glofox\Domain\Experiments\Services\ExperimentsClient;
use Glofox\Infrastructure\Flags\FlaggerInterface;
use Glofox\Infrastructure\Flags\IrisFlags;
use Glofox\Infrastructure\Flags\IrisGenericFlagger;
use Illuminate\Http\Response;

class IrisGlofoxDigitalFlagger extends IrisGenericFlagger implements FlaggerInterface
{
    private const ENABLED_OPTION = 'ENABLED';

    public function getSlug(): string
    {
        return IrisFlags::GLOFOX_LIVE;
    }

    public function shouldEnable(Branch $branch): bool
    {
        return true;
    }

    public function getTestEnabledOption(): string
    {
        return static::ENABLED_OPTION;
    }
}
