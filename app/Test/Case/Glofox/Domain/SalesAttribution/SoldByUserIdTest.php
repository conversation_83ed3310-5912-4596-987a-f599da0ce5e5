<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\SalesAttribution;

use Glofox\Domain\SalesAttribution\SoldByUserId;
use Glofox\Domain\Users\Exceptions\UserNotFoundException;

\App::import('Test/Case', 'GlofoxTestCase');

final class SoldByUserIdTest extends \GlofoxTestCase
{
    public $fixtures = [
        'app.user',
    ];

    public function test_it_throws_exception_if_user_does_not_exist(): void
    {
        $this->setExpectedException(UserNotFoundException::class);
        new SoldByUserId('5d8a55cc532c54916cf6e594');
    }

    public function test_it_returns_the_id_if_user_exist(): void
    {
        $valueObject = new SoldByUserId('59a7011a05c677bda916616c');
        $this->assertSame('59a7011a05c677bda916616c', $valueObject->getValue());
    }

    public function test_it_returns_null_if_the_value_is_null(): void
    {
        $valueObject = new SoldByUserId(null);
        $this->assertNull($valueObject->getValue());
    }
}
