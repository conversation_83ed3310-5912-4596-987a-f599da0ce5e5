<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\EventTrackers\Listeners;

use App;
use CakeTestCases\Glofox\Domain\Users\Traits\AuthenticateUsersTrait;
use Glofox\AuditLog\Resolvers\OriginByRequestResolver;
use Glofox\Domain\AppointmentSlots\Events\AppointmentSlotWasCreated;
use Glofox\Domain\AsyncEvents\Events\TrackingEventMeta;
use Glofox\Domain\AsyncEvents\Events\TrackingEventPayload;
use Glofox\Domain\EventTrackers\Listeners\TrackAppointmentSlotCreated;
use Glofox\Domain\EventTrackers\Services\TrackEventPublisher;
use Glofox\Http\Source;
use Mockery;
use Psr\Log\LoggerInterface;

App::import('Test/Case', 'GlofoxTestCase');

class TrackAppointmentSlotCreatedTest extends \GlofoxTestCase
{
    use AuthenticateUsersTrait;

    private const DEFAULT_BRANCH_ID = '49a7011a05c677b9a916612a';
    private const DEFAULT_USER_ID = '59a7011a05c677bda916612a';
    private const DEFAULT_SLOT_ID = '111222a';

    public $fixtures = ['app.branch', 'app.user'];

    private TrackEventPublisher $publisher;
    private OriginByRequestResolver $resolver;
    private LoggerInterface $logger;

    public function setUp(): void
    {
        parent::setUp();

        $this->publisher = Mockery::mock(TrackEventPublisher::class);
        $this->resolver = Mockery::mock(OriginByRequestResolver::class);
        $this->logger = Mockery::mock(LoggerInterface::class);
    }

    public function tearDown(): void
    {
        parent::tearDown();

        Mockery::close();
    }

    public function testDoesNotHandleIfAuthUserIsNull(): void
    {
        $this->mockAuthenticatedUser(null);

        $this->publisher->shouldNotHaveBeenCalled();
        $this->resolver->shouldNotHaveBeenCalled();
        $this->logger->shouldReceive('info')->once();

        $handler = new TrackAppointmentSlotCreated($this->publisher, $this->resolver, $this->logger);
        $handler->handle($this->givenDefaultTrackingEvent());
    }

    public function testDoesNotHandleIfAuthUserIsGuest(): void
    {
        $this->authenticateAsGuest();

        $this->publisher->shouldNotHaveBeenCalled();
        $this->resolver->shouldNotHaveBeenCalled();
        $this->logger->shouldReceive('info')->once();

        $handler = new TrackAppointmentSlotCreated($this->publisher, $this->resolver, $this->logger);
        $handler->handle($this->givenDefaultTrackingEvent());
    }

    public function testSuccessfulTracking(): void
    {
        $this->authenticateUser(self::DEFAULT_USER_ID);

        $this->logger->shouldNotHaveBeenCalled();

        $domainEvent = new AppointmentSlotWasCreated(
            self::DEFAULT_SLOT_ID,
            self::DEFAULT_BRANCH_ID,
            true
        );

        $handler = $this->givenHandler($domainEvent);
        $handler->handle($domainEvent);
    }

    private function givenHandler(AppointmentSlotWasCreated $event): TrackAppointmentSlotCreated
    {
        $trackingData = [
            'eventName' => 'Appointment slot created with client',
            'branchId' => $event->getBranchId(),
            'userId' => self::DEFAULT_USER_ID,
            'platform' => strtolower(Source::UNKNOWN),
            'data' => [
                'slotId' => $event->getSlotId(),
            ],
        ];

        $this->publisher
            ->shouldReceive('sendEventToTrack')
            ->withArgs(
                function (TrackingEventMeta $meta, TrackingEventPayload $payload) use ($trackingData) {
                    $this->assertEmpty($meta->correlation());
                    $this->assertEmpty($meta->messageId());
                    $this->assertNotEmpty($meta->creationDate());

                    $serializedPayload = $payload->jsonSerialize();

                    $this->assertSame($trackingData, $serializedPayload);

                    return true;
                }
            )
            ->once();

        $this->resolver
            ->shouldReceive('resolve')
            ->andReturn(Source::UNKNOWN())
            ->once();

        return new TrackAppointmentSlotCreated($this->publisher, $this->resolver, $this->logger);
    }

    private function givenDefaultTrackingEvent(): AppointmentSlotWasCreated
    {
        return new AppointmentSlotWasCreated(
            self::DEFAULT_SLOT_ID,
            self::DEFAULT_BRANCH_ID,
            false
        );
    }
}
