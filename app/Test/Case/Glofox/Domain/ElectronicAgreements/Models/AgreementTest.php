<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\ElectronicAgreements\Models;

use Glofox\Domain\ElectronicAgreements\Models\Agreement;

\App::import('Test/Case', 'GlofoxTestCase');

class AgreementTest extends \GlofoxTestCase
{
    public $fixtures = [];

    public function test_requires_new_agreement(): void
    {
        $a = new Agreement(['status' => Agreement::AGREEMENT_STATUS_OUTSTANDING]);
        self::assertTrue($a->requiresNewAgreement());

        $a = new Agreement(['status' => Agreement::AGREEMENT_STATUS_ACCEPTED_OUTDATED_REQUIRED_NEW]);
        self::assertTrue($a->requiresNewAgreement());

        $a = new Agreement(['status' => Agreement::AGREEMENT_STATUS_ACCEPTED_OUTDATED]);
        self::assertFalse($a->requiresNewAgreement());

        $a = new Agreement(['status' => Agreement::AGREEMENT_STATUS_ACCEPTED]);
        self::assertFalse($a->requiresNewAgreement());
    }
}
