<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: card.proto

namespace GRPC\Payments\AccountService;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>grpc.SelfMigrateCardsReq</code>
 */
class SelfMigrateCardsReq extends \Google\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>int32 AccountID = 1;</code>
     */
    protected $AccountID = 0;
    /**
     * Generated from protobuf field <code>string PreferredCardID = 2;</code>
     */
    protected $PreferredCardID = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int $AccountID
     *     @type string $PreferredCardID
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Card::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>int32 AccountID = 1;</code>
     * @return int
     */
    public function getAccountID()
    {
        return $this->AccountID;
    }

    /**
     * Generated from protobuf field <code>int32 AccountID = 1;</code>
     * @param int $var
     * @return $this
     */
    public function setAccountID($var)
    {
        GPBUtil::checkInt32($var);
        $this->AccountID = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string PreferredCardID = 2;</code>
     * @return string
     */
    public function getPreferredCardID()
    {
        return $this->PreferredCardID;
    }

    /**
     * Generated from protobuf field <code>string PreferredCardID = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setPreferredCardID($var)
    {
        GPBUtil::checkString($var, True);
        $this->PreferredCardID = $var;

        return $this;
    }

}

