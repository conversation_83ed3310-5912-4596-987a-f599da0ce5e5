<?php

namespace Glofox\Response\Models\Branches\Configuration;

use Glofox\Response\BaseModel;

/**
 * @SuppressWarnings(PHPMD)
 */
class ConfigurationModel extends BaseModel
{
    /**
     * @var bool
     */
    public $only_payg = false;

    /**
     * @var FormatsModel
     */
    public $formats;

    /**
     * @var WebportalModel
     */
    public $webportal;

    /**
     * @var ControlAccessModel
     */
    public $control_access;

    /**
     * @var KioskModel
     */
    public $kiosk;

    /**
     * @var array
     */
    public $facebookPixel;

    /**
     * @var SalesTaxModel
     */
    public $sales_tax;

    protected array $allowedFields = [
        'only_payg',
        'formats',
        'webportal',
        'control_access',
        'kiosk',
        'facebookPixel',
        'sales_tax'
    ];

    public function __construct(array $data)
    {
        $this->setModelFields($data);
    }
}
