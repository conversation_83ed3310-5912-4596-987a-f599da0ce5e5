<?php

use Glofox\Console\ShellHelper;
use Glofox\Domain\ABTestClients\Repositories\ABTestClientsRepository;
use Glofox\Domain\Branches\Models\Branch;
use Glofox\Domain\Branches\Repositories\BranchesRepository;
use Glofox\Domain\Branches\Search\Expressions\BranchId;
use Glofox\Domain\Memberships\Exceptions\MembershipNotFoundException;
use Glofox\Domain\Memberships\Models\Membership;
use Glofox\Domain\Memberships\Models\Plan;
use Glofox\Domain\Memberships\Onboarding\Services\OnboardingService;
use Glofox\Domain\Memberships\Onboarding\UseCases\OnboardingUseCase;
use Glofox\Domain\Memberships\Repositories\MembershipsRepository;
use Glofox\Domain\PaymentMethods\Repositories\PaymentMethodsRepository;
use Glofox\Domain\PaymentMethods\Search\Expressions\TypeId;
use Glofox\Domain\PaymentMethods\Type;
use Glofox\Domain\Plans\Exceptions\PlanNotFoundException;
use Glofox\Domain\SubscriptionPlans\Models\SubscriptionPlan;
use Glofox\Domain\SubscriptionPlans\Repositories\SubscriptionPlanRepository;
use Glofox\Domain\Users\Exceptions\UserNotFoundException;
use Glofox\Domain\Users\Models\User;
use Glofox\Domain\Users\Repositories\UsersRepository;
use Glofox\Domain\Users\Search\Expressions\MindbodyId;
use Glofox\Domain\Users\Search\Expressions\UserId;
use Glofox\Payments\Contracts\PaymentProviderContract;
use Glofox\Repositories\Search\Expressions\Shared\Active;
use Glofox\Repositories\Search\Expressions\Shared\Id;
use Glofox\Validation\Exceptions\ValidationException;
use League\Csv\Reader;
use League\Csv\Writer;

App::uses('AmazonS3', 'AmazonS3.Lib');

/**
 * Class UserMembershipImportShell.
 */
class UserMembershipImportShell extends AppShell
{
    use ShellHelper;

    // ==================== Repositories ====================
    /** @var UsersRepository */
    private $usersRepository;
    /** @var BranchesRepository */
    private $branchesRepository;
    /** @var MembershipsRepository */
    private $membershipsRepository;
    /** @var ABTestClientsRepository */
    private $abTestClientsRepository;
    /** @var PaymentMethodsRepository */
    private $paymentMethodsRepository;
    /** @var SubscriptionPlanRepository */
    private $subscriptionPlansRepository;
    // ==================== Glofox Models ====================
    /** @var Branch */
    private $branch;
    private ?array $paymentHandlers = null;
    // ==================== Shell Variables ====================
    /** @var string */
    private $executionMethod;
    private ?string $reportFilePath = null;
    /** @var string */
    private $sourceFilePath;
    /** @var string */
    private $reportPath;
    /** @var string */
    private $branchId;
    /** @var bool */
    private $uploadReport;
    /** @var bool */
    private $isCloud;
    /** @var bool */
    private $forceSetup;
    private ?bool $isUsingMembershipsService = null;

    public function __construct($stdout = null, $stderr = null, $stdin = null)
    {
        parent::__construct($stdout, $stderr, $stdin);
    }

    public function getOptionParser()
    {
        $parser = parent::getOptionParser();
        $parser->addOption('method', [
            'short' => 'm',
            'help' => 'The method to execute',
            'default' => 'dry-run',
        ]);

        $parser->addOption('branch-id', [
            'short' => 'b',
            'help' => 'The branch to use in this execution',
            'default' => '',
        ]);

        $parser->addOption('source-file', [
            'short' => 's',
            'help' => 'The customers csv file',
            'default' => '',
        ]);

        $parser->addOption('is-cloud', [
            'short' => 'i',
            'help' => 'The source file location',
            'boolean' => true,
        ]);

        $parser->addOption('force-setup', [
            'short' => 'f',
            'help' => 'Force the setup of a membership',
            'boolean' => true,
        ]);

        $parser->addOption('report-path', [
            'short' => 'r',
            'help' => 'The path to the folder to output csv the report',
            'default' => null,
        ]);

        $parser->addOption('upload-report', [
            'short' => 'u',
            'help' => 'Upload report to s3',
            'boolean' => true,
        ]);

        return $parser;
    }

    public function main()
    {
        try {
            $this->parseParams();
            $this->init();

            $reportCsvWriter = Writer::createFromPath($this->reportFilePath, 'w+');
            $reportCsvWriter->insertOne([
                'email_or_mindbody_id',
                'user_id',
                'membership_id',
                'plan_code',
                'gateway_plan_id',
                'gateway_sub_id',
                'error',
            ]);

            $customersFilePath = $this->buildCsvFilePath();
            $sourceFileReader = Reader::createFromPath($customersFilePath, 'r');
            $records = $sourceFileReader->fetchAssoc(0);

            foreach ($records as $index => $row) {
                if (0 == $index) {
                    continue; //Skip header
                }

                if (!is_countable($row) || count($row) < 3) {
                    $this->log('Not enough columns in row', LOG_INFO);
                    continue;
                }

                $uniqueMemberId = $row['id'];
                $membershipId = $row['membership_id'];
                $planCode = $row['plan_code'];
                $paymentMethod = $row['payment_method'];
                $priceToPay = $row['overwrite_price'] ?? '';
                $endOfTrial = $row['trial_ends'];
                $endOfContract = $row['contract_ends'] ?? '';

                try {
                    if (empty($paymentMethod)) {
                        throw new ValidationException('missing payment_method');
                    }

                    $membership = $this->resolveMembership($membershipId);

                    $onboardingUseCase = app()->makeWith(OnboardingUseCase::class, [
                        'paymentHandlers' => $this->paymentHandlers,
                        'branch' => $this->branch,
                        'member' => $this->resolveMember($uniqueMemberId),
                        'membership' => $membership,
                        'plan' => $this->resolvePlan($membership, $planCode),
                        'subscriptionPlan' => $this->resolveSubscriptionPlan(
                            $this->resolvePlan($membership, $planCode)
                        ),
                        'paymentMethod' => $paymentMethod,
                        'priceToPay' => $priceToPay,
                        'endOfTrial' => $endOfTrial,
                        'endOfContract' => $endOfContract,
                        'isDryRun' => 'dry-run' === $this->executionMethod,
                        'shouldForceSetup' => $this->forceSetup,
                        'isUsingMembershipsService' => $this->isUsingMembershipsService,
                    ]);

                    /** @var OnboardingService $onboardingService */
                    $onboardingService = app()->make(OnboardingService::class);
                    $report = $onboardingService->execute($onboardingUseCase);

                    $reportCsvWriter->insertOne($report->toArray());
                } catch (\Exception $e) {
                    $this->log($e->getMessage(), LOG_ERR);
                    $this->log($e->getTraceAsString(), LOG_ERR);
                    $reportCsvWriter->insertOne([
                        $uniqueMemberId,
                        $membershipId,
                        $planCode,
                        'n/a',
                        'n/a',
                        $e->getMessage(),
                    ]);
                }
            }

            if ($this->uploadReport) {
                $s3Path = $this->getS3PathForBranchEnvironment($this->branchId);
                $this->uploadReportToS3($this->reportFilePath, $s3Path);
            }
        } catch (\Exception $e) {
            $this->log($e->getMessage(), LOG_ERR);
            $this->log($e->getTraceAsString(), LOG_ERR);
        }
    }

    private function buildCsvFilePath(): string
    {
        $sourceFilePath = $this->sourceFilePath;
        if ($this->isCloud) {
            $sourceFilePath = $this->getS3DataSourceFile($this->sourceFilePath);
        }

        return $sourceFilePath;
    }

    private function parseParams()
    {
        $this->executionMethod = $this->param('method');
        if ('dry-run' != $this->executionMethod && 'execute' != $this->executionMethod) {
            $this->executionMethod = 'dry-run';
        }

        $this->branchId = $this->param('branch-id');
        if ('' == $this->branchId) {
            throw new Exception('Branch id missing');
        }

        $this->sourceFilePath = $this->param('source-file');
        if ('' == $this->sourceFilePath) {
            throw new Exception('The import csv file is missing');
        }

        $this->isCloud = $this->params['is-cloud'];

        $this->reportPath = $this->param('report-path');
        if ('' == $this->reportPath) {
            $this->reportPath = LOGS;
        }

        $this->reportFilePath = sprintf('%s/membership_import_%d.csv', rtrim($this->reportPath, '/'), time());
        $this->log('Report path is: ' . $this->reportFilePath, LOG_INFO);

        $this->uploadReport = $this->params['upload-report'];
        $this->forceSetup = $this->params['force-setup'];
    }

    private function init(): void
    {
        $this->usersRepository = app()->make(UsersRepository::class);
        $this->branchesRepository = app()->make(BranchesRepository::class);
        $this->membershipsRepository = app()->make(MembershipsRepository::class);
        $this->abTestClientsRepository = app()->make(ABTestClientsRepository::class);
        $this->paymentMethodsRepository = app()->make(PaymentMethodsRepository::class);
        $this->subscriptionPlansRepository = app()->make(SubscriptionPlanRepository::class);

        $this->branch = $this->branchesRepository
            ->addCriteria(new Id($this->branchId))
            ->firstOrFail();

        /** @var \Glofox\Domain\ABTestClients\Models\ABTestClient $abTestClient */
        $abTestClient = $this->abTestClientsRepository
            ->addCriteria(new BranchId($this->branchId))
            ->first();
        $this->isUsingMembershipsService = !empty($abTestClient) && $abTestClient->isActive();

        // setup card payment handler
        $branchCardPaymentMethod = $this->paymentMethodsRepository
            ->skipCallbacks()
            ->addCriteria(new BranchId($this->branchId))
            ->addCriteria(new TypeId(Type::CARD))
            ->addCriteria(new Active(true))
            ->first();

        if (!empty($branchCardPaymentMethod) && $branchCardPaymentMethod instanceof \Glofox\Domain\PaymentMethods\Models\PaymentMethod) {
            $this->paymentHandlers[Type::CARD] = payments()->providerByPaymentMethod($branchCardPaymentMethod);
        }

        // setup direct debit payment handler
        $branchDDPaymentMethod = $this->paymentMethodsRepository
            ->skipCallbacks()
            ->addCriteria(new BranchId($this->branchId))
            ->addCriteria(new TypeId(Type::DIRECT_DEBIT))
            ->addCriteria(new Active(true))
            ->first();

        if (!empty($branchDDPaymentMethod) && $branchDDPaymentMethod instanceof \Glofox\Domain\PaymentMethods\Models\PaymentMethod) {
            $this->paymentHandlers[Type::DIRECT_DEBIT] = payments()->providerByPaymentMethod($branchDDPaymentMethod);
        }
    }

    private function resolveMember(string $memberUniqueId): User
    {
        if ($this->isValidMongoId($memberUniqueId)) {
            $member = $this->resolveMemberByUserId($memberUniqueId);
        } else {
            $member = $this->resolveMemberByMindbodyId($memberUniqueId);
        }

        if ($member->originBranchId() !== $this->branchId) {
            throw new Exception(
                sprintf(
                    'User [%s:%s] origin branch does not match [%s]',
                    $memberUniqueId,
                    $member->id(),
                    $this->branchId
                )
            );
        }

        return $member;
    }

    private function resolveMemberByUserId(string $userId): User
    {
        $this->usersRepository
            ->addCriteria(new BranchId($this->branchId))
            ->addCriteria(new UserId($userId));

        return $this->usersRepository->firstOrFail(function () use ($userId) {
            throw new UserNotFoundException('User not found with: ' . $userId);
        });
    }

    private function resolveMemberByMindbodyId($mindbodyId): User
    {
        $this->usersRepository
            ->addCriteria(new BranchId($this->branchId))
            ->addCriteria(new MindbodyId($mindbodyId));

        return $this->usersRepository->firstOrFail(function () use ($mindbodyId) {
            throw new UserNotFoundException('User not found with: ' . $mindbodyId);
        });
    }

    private function resolveMembership(string $membershipId): Membership
    {
        $this->membershipsRepository
            ->addCriteria(new BranchId($this->branchId))
            ->addCriteria(new Id($membershipId));

        return $this->membershipsRepository->firstOrFail(function () use ($membershipId) {
            throw new MembershipNotFoundException('Membership not found with: ' . $membershipId);
        });
    }

    private function resolvePlan(Membership $membership, string $planCode): Plan
    {
        if (!$membership->has('plans') || $membership->plans()->isEmpty()) {
            throw new Exception('Membership plan is not set');
        }

        /** @var Plan $plan */
        foreach ($membership->plans() as $plan) {
            if ($plan->code() == $planCode) {
                return $plan;
            }
        }

        throw new PlanNotFoundException('Plan is not found with: ' . $planCode);
    }

    private function resolveSubscriptionPlan(Plan $plan): SubscriptionPlan
    {
        $subscriptionPlanId = $plan->subscriptionPlanId();

        $this->subscriptionPlansRepository
            ->addCriteria(new Id($subscriptionPlanId));

        return $this->subscriptionPlansRepository->firstOrFail(function () use ($subscriptionPlanId) {
            throw new MembershipNotFoundException('Subscription Plan not found with: ' . $subscriptionPlanId);
        });
    }

    private function isValidMongoId(string $id): bool
    {
        try {
            return !empty(new \MongoId($id));
        } catch (\MongoException $ex) {
            return false;
        }
    }
}
