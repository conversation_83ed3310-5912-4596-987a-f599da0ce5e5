#!/bin/sh

PROJECT_DIR="/var/www"

cd $PROJECT_DIR/app && Console/cake Email --region=$1 2>&1 | tee /var/log/cron.log
cd $PROJECT_DIR/app && Console/cake EventGenerator --region=$1 2>&1 | tee /var/log/cron.log
cd $PROJECT_DIR/app && Console/cake TimeSlotGenerator --region=$1 2>&1 | tee /var/log/cron.log

dt=$(date '+%d/%m/%Y %H:%M:%S');
json='{"text":"Cronjobs have successfully run on '"$dt"' "}'
curl -s -d "payload=$json" "*****************************************************************************"
