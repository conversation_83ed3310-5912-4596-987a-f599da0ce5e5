<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Infrastructure\PushNotifications;

use App;
use CakeTestCases\Glofox\Domain\Logger\Traits\MockedLoggerTrait;
use Glofox\Domain\Branches\Models\Branch;
use Glofox\Domain\FeatureFlags\Flaggers\PushNotificationVersionFlagger;
use Glofox\Domain\Notifications\Services\PushNotificationsServiceInterface;
use Glofox\Infrastructure\Exception\HttpClientResponseException;
use Glofox\Infrastructure\PushNotifications\PushNotificationsHttpClientNew;
use Glofox\Infrastructure\Services\PriceCalculatorHttpService;
use GlofoxTestCase;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\ClientException;
use GuzzleHttp\Exception\ServerException;
use GuzzleHttp\Handler\MockHandler;
use GuzzleHttp\HandlerStack;
use GuzzleHttp\Psr7\Request;
use GuzzleHttp\Psr7\Response;
use Mockery;
use MongoId;
use Psr\Log\LoggerInterface;
use Ramsey\Uuid\Uuid;
use UnsuccessfulOperation;
use Glofox\Domain\SalesTaxes\Services\PriceCalculatorClient;
use Glofox\Http\Responses\ResponseInterface;
use function Sabre\Uri\build;

App::import('Test/Case', 'GlofoxTestCase');

class PushNotificationsHttpClientNewTest extends GlofoxTestCase
{
    use MockedLoggerTrait;

    private PushNotificationsHttpClientNew $PushNotificationsHttpNew;

    public function setUp(): void
    {
        parent::setUp();

        $this->mockLogger();

        app()->forgetInstance(PushNotificationVersionFlagger::class);
    }

    public function tearDown(): void
    {
        parent::tearDown();

        Mockery::close();
        $this->teardownLogger();
    }

    private function generateRandomString($length = 10) {
        return substr(str_shuffle(str_repeat($x='0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ', (int)ceil($length / strlen($x)))),1,$length);
    }

    public function test_get_200_OK_from_internal_endpoint_returns_valid_response(): void
    {
        $jsonKey = $this->generateRandomString(5);
        $jsonValue = $this->generateRandomString(10);
        $testJsonResponse = '{ "' . $jsonKey . '" : "' . $jsonValue . '" }';

        // Create a mock and queue two responses.
        $mock = new MockHandler([
            new Response(200, ['Content-Type' => 'application/json'], $testJsonResponse)
        ]);

        $mockHandler = HandlerStack::create($mock);

        $mockClient = new Client(['handler' => $mockHandler]);

        $this->PushNotificationsHttpNew = new PushNotificationsHttpClientNew(
            app()->make(LoggerInterface::class),
            $mockClient);

        $response = $this->PushNotificationsHttpNew->getMessagesStatusesInDateRange("namespace", "branchId", 0, 0 );

        $this->assertNotEmpty($response);
        $this->assertEquals($testJsonResponse, $response);
    }
}
