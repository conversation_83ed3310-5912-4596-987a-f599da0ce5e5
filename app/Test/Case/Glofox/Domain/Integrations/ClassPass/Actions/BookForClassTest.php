<?php

namespace CakeTestCases\Glofox\Domain\Integrations\ClassPass\Actions;

use Glofox\Domain\Integrations\ClassPass\Actions\BookForClass;
use Glofox\Domain\Integrations\Exceptions\ProgramNotConfiguredException;
use Glofox\Domain\Integrations\Origin;

\App::import('Test/Case', 'GlofoxTestCase');

/**
 * Class BookForClassTest.
 */
class BookForClassTest extends \GlofoxTestCase
{
    /**
     * Fixtures.
     *
     * @var array
     */
    public $fixtures = [
        'app.user',
        'app.event',
        'app.program',
        'app.booking',
    ];

    /**
     * setUp method.
     */
    public function setUp()
    {
        parent::setUp();
        $this->BookForClass = new BookForClass();
    }

    /**
     * tearDown method.
     */
    public function tearDown()
    {
        parent::tearDown();
        unset($this->BookForClass);
    }

    public function testGetBookingMetadata()
    {
        $origin = Origin::get('classpass');
        $userId = '5adde464b5cf4ad32eb7597e';
        $externalId = 'externalBookingId';
        $expected = ['classpass' => ['_id' => 'externalBookingId', 'user_id' => 'randomId123']];
        $result = $this->BookForClass->getBookingMetadata($origin, $userId, $externalId);
        $this->assertArrayContainsArray($expected, $result, 'userId and externalId provided and correct');

        // Member does not exists
        $userId = 'nonExistentUserId';
        $externalId = 'externalBookingId';
        $this->setExpectedException(\NotFoundException::class);
        $result = $this->BookForClass->getBookingMetadata($origin, $userId, $externalId);

        // Member exists but it doesn't have classpass id
        $userId = '5adde862426413b9e9b4be9e';
        $externalId = 'externalBookingId';
        $this->setExpectedException(\NotFoundException::class);
        $result = $this->BookForClass->getBookingMetadata($origin, $userId, $externalId);
    }

    public function testIsClassFull()
    {
        $origin = Origin::get('classpass');
        // Class is Full
        $eventId = '5ae1b7a0df7366855fd28702';
        $result = $this->BookForClass->isClassFull($origin, $eventId);
        $expected = true;
        $this->assertEquals($expected, $result, 'There is 1 classpass size in program and there is 1 classpass booking already');

        // Class has classpass spots available
        $eventId = '5adf43c0db6b6587ebcb2c5b';
        $result = $this->BookForClass->isClassFull($origin, $eventId);
        $expected = false;
        $this->assertEquals($expected, $result, 'There is 3 classpass size in program and there is 1 classpass booking already');

        // Event from program not set with classpass
        $eventId = '5adf5449f71d2fb4f4b9c5ea';
        $this->setExpectedException(ProgramNotConfiguredException::class, 'PROGRAM_NOT_SET_TO_USE_INTEGRATION');
        $result = $this->BookForClass->isClassFull($origin, $eventId);
    }
}
