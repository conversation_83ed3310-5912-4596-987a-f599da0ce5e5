<?php

namespace CakeTestCases\Glofox\Domain\BookingRequests\Http;

use CakeTestCases\Glofox\Domain\Authentication\Token\TokenGeneration;
use CakeTestCases\Glofox\Domain\Users\Traits\FetchUsersTrait;

\App::import('Test/Case', 'GlofoxControllerTestCase');

final class BookingRequestsControllerTest extends \GlofoxControllerTestCase
{
    use FetchUsersTrait;
    use TokenGeneration;

    public $fixtures = [
        'app.user',
        'app.branch',
        'app.booking_request',
    ];

    public function test_it_shows_booking_request_status_looking_up_by_event_id(): void
    {
        $this->authenticateAsAdmin();

        $userId = '59a7011a05c677bda916612a';
        $eventId = '49b7012a05c677c9a512503c';
        $result = $this->testAction(
            sprintf('/2.1/booking-requests/booking/%s/%s', $userId, $eventId),
            ['method' => 'GET']
        );
        $bookingRequest = json_decode($result, true, 512, JSON_THROW_ON_ERROR);

        self::assertEquals('PROCESSED', $bookingRequest['status'], json_encode($bookingRequest, JSON_THROW_ON_ERROR));
    }

    public function test_it_shows_booking_request_status_looking_up_by_correlation_id_and_message_id(): void
    {
        $this->authenticateAsAdmin();

        $userId = '59a7011a05c677bda916612a';
        $correlationId = '49b7012a05c677c9a512503d';
        $messageId = '1-2-3';
        $result = $this->testAction(
            sprintf('/2.1/booking-requests/booking/%s/%s?messageId=%s', $userId, $correlationId, $messageId),
            ['method' => 'GET']
        );
        $bookingRequest = json_decode($result, true, 512, JSON_THROW_ON_ERROR);

        self::assertEquals('PROCESSED', $bookingRequest['status'], json_encode($bookingRequest, JSON_THROW_ON_ERROR));
    }

    public function test_it_shows_booking_request_status_looking_up_by_correlation_id(): void
    {
        $this->authenticateAsAdmin();

        $userId = '59a7011a05c677bda916612a';
        $correlationId = '49b7012a05c677c9a512503d';
        $result = $this->testAction(
            sprintf('/2.1/booking-requests/booking/%s/%s', $userId, $correlationId),
            ['method' => 'GET']
        );
        $bookingRequest = json_decode($result, true, 512, JSON_THROW_ON_ERROR);

        self::assertEquals('PROCESSED', $bookingRequest['status'], json_encode($bookingRequest, JSON_THROW_ON_ERROR));
    }
}
