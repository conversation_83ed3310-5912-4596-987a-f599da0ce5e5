<?php

App::uses('CakeEmail', 'Network/Email');
App::uses('AppController', 'Controller');

use Carbon\Carbon;
use Glofox\AuditLog\Resolvers\OriginByRequestResolver;
use Glofox\Authentication\Exceptions\AuthenticationException;
use Glofox\CdnProvider;
use Glofox\Datasource\Exceptions\InvalidMongoIdException;
use Glofox\Domain\AsyncEvents\Events\BookingCreatedEventMeta;
use Glofox\Domain\AsyncEvents\Events\BookingCreatedEventPayload;
use Glofox\Domain\AsyncEvents\Events\BookingFinishedEventMeta;
use Glofox\Domain\AsyncEvents\Events\BookingFinishedEventPayload;
use Glofox\Domain\AsyncEvents\Events\CourseBookingFinishedEventMeta;
use Glofox\Domain\AsyncEvents\Events\CourseBookingFinishedEventPayload;
use Glofox\Domain\AsyncEvents\Events\TrackingEventMeta;
use Glofox\Domain\AsyncEvents\Events\TrackingEventPayload;
use Glofox\Domain\AsyncEvents\Events\WaitlistFinishedEventMeta;
use Glofox\Domain\AsyncEvents\Events\WaitlistFinishedEventPayload;
use Glofox\Domain\Authentication\Auth;
use Glofox\Domain\BookingRequests\Models\BookingRequest as BookingRequestModel;
use Glofox\Domain\BookingRequests\Models\BookingRequestStatus;
use Glofox\Domain\BookingRequests\Services\BookingRequestCreatorService;
use Glofox\Domain\BookingRequests\Services\BookingRequestsPollingService;
use Glofox\Domain\BookingRequests\Services\BookingRequestsPublisher;
use Glofox\Domain\BookingRequests\Services\BookingUpdateRequestEventPublisherInterface;
use Glofox\Domain\BookingRequests\Services\WaitlistBookingRequestCreatorService;
use Glofox\Domain\BookingRequests\ValueObjects\CreateBookingRequest;
use Glofox\Domain\BookingRequests\ValueObjects\CreateWaitlistBookingRequestParams;
use Glofox\Domain\Bookings\Events\BookingHasFailed;
use Glofox\Domain\Bookings\Exceptions\BookingNotFoundException;
use Glofox\Domain\Bookings\Exceptions\BookingValidatorException;
use Glofox\Domain\Bookings\Internal\Parameters\BookingRequestParameters;
use Glofox\Domain\Bookings\Internal\Parameters\CreateBookingParameters;
use Glofox\Domain\Bookings\Internal\Parameters\CreateTimeSlotBookingParameters;
use Glofox\Domain\Bookings\Internal\Parameters\UserMembershipValidParameters;
use Glofox\Domain\Bookings\Models\Booking;
use Glofox\Domain\Bookings\ModelType;
use Glofox\Domain\Bookings\Repositories\BookingsRepository;
use Glofox\Domain\Bookings\Requests\AddRequest;
use Glofox\Domain\Bookings\Requests\AddWebAdminRequest;
use Glofox\Domain\Bookings\Requests\BookRequest;
use Glofox\Domain\Bookings\Requests\GetBookingsByDateRangeRequest;
use Glofox\Domain\Bookings\Requests\GetBookingsByModelAndModelIdRequest;
use Glofox\Domain\Bookings\Requests\HasBookingsRequest;
use Glofox\Domain\Bookings\Requests\ListFirstBookingsRequest;
use Glofox\Domain\Bookings\Requests\ListLateCancellationsRequest;
use Glofox\Domain\Bookings\Requests\ListNoShowsRequest;
use Glofox\Domain\Bookings\Requests\ProcessPaymentRequestV1;
use Glofox\Domain\Bookings\Requests\ProcessPaymentRequestV2;
use Glofox\Domain\Bookings\Services\BookingsPublisher;
use Glofox\Domain\Bookings\Services\Payments\PaymentExecutorService;
use Glofox\Domain\Bookings\Services\Payments\PaymentProcessorService;
use Glofox\Domain\Events\EventId;
use Glofox\Domain\Bookings\Status;
use Glofox\Domain\Bookings\UseCase\CancelBooking;
use Glofox\Domain\Bookings\UseCase\CancelBookingParams;
use Glofox\Domain\Bookings\UseCase\GetBookingsByDateRange;
use Glofox\Domain\Bookings\UseCase\GetBookingsByDateRangeParams;
use Glofox\Domain\Bookings\UseCase\GetBookingsByModelAndModelId;
use Glofox\Domain\Bookings\UseCase\GetBookingsByModelAndModelIdParams;
use Glofox\Domain\Bookings\UseCase\ListFirstBookings;
use Glofox\Domain\Bookings\UseCase\ListFirstBookingsRequestParams;
use Glofox\Domain\Bookings\UseCase\ProcessBooking;
use Glofox\Domain\Bookings\UseCase\ProcessBookingParams;
use Glofox\Domain\Bookings\Validation\UserAuthorization\AddWebAdminValidator;
use Glofox\Domain\Bookings\Validation\Validators\ActiveLimitBookingValidator;
use Glofox\Domain\Bookings\Validation\Validators\CanBookWithAppointmentSlotValidator;
use Glofox\Domain\Bookings\Validation\Validators\EventHasAvailableSpotLegacyValidator;
use Glofox\Domain\Bookings\Validation\Validators\MemberMaximumBookings;
use Glofox\Domain\Bookings\ValueObjects\BookableModel;
use Glofox\Domain\Bookings\ValueObjects\ValidationParams;
use Glofox\Domain\Branches\Exceptions\BranchNotFoundException;
use Glofox\Domain\Branches\Exceptions\InvalidBranchIdException;
use Glofox\Domain\Branches\Models;
use Glofox\Domain\Branches\Models\Branch;
use Glofox\Domain\Branches\Repositories\BranchesRepository;
use Glofox\Domain\Charges\Events\ChargeWasUpdated;
use Glofox\Domain\Charges\Models\Charge;
use Glofox\Domain\Credits\CreditPayable;
use Glofox\Domain\Credits\Repositories\CreditsRepository;
use Glofox\Domain\Dictionaries\Repositories\DictionariesRepository;
use Glofox\Domain\Events\Exceptions\EventNotFoundException;
use Glofox\Domain\Events\Exceptions\InvalidEventException;
use Glofox\Domain\Events\Generators\OnlineEventUrlGenerator;
use Glofox\Domain\Events\Internal\Parameters\PricingForUserParameters;
use Glofox\Domain\Events\Models\Event;
use Glofox\Domain\Events\Repositories\EventsRepository;
use Glofox\Domain\EventTrackers\Services\TrackEventPublisher;
use Glofox\Domain\EventTrackers\Type as EventType;
use Glofox\Domain\Facilities\Exceptions\FacilityNotFoundException;
use Glofox\Domain\Facilities\Repositories\FacilitiesRepository;
use Glofox\Domain\FeatureFlags\FeatureFlagInterface;
use Glofox\Domain\FeatureFlags\Flaggers\UseRefactoredCodeForBookingFlagger;
use Glofox\Domain\Locker\BookingEventLockerInterface;
use Glofox\Domain\Locker\EventBookingLockerException;
use Glofox\Domain\Locker\EventBookingLockerInterface;
use Glofox\Domain\Memberships\Models\Addon;
use Glofox\Domain\Memberships\Services\AddonEligibilityServiceInterface;
use Glofox\Domain\Memberships\Services\AddonServiceInterface;
use Glofox\Domain\Memberships\Services\DateOfBooking\UpdateDatesBaseOnBookingsService;
use Glofox\Domain\Memberships\Services\Validate\ValidateUserHasActiveNonPAYGMembership;
use Glofox\Domain\Memberships\Type as MembershipType;
use Glofox\Domain\PaymentMethods\Exceptions\PaymentMethodNotAvailableForMembers;
use Glofox\Domain\PaymentMethods\Type as PaymentMethodType;
use Glofox\Domain\PaymentProviders\Exceptions\PaymentProviderNotFoundException;
use Glofox\Domain\Pricing\Exceptions\PricingStrategyNotFoundException;
use Glofox\Domain\Pricing\Strategies\Factory as PricingResolverFactory;
use Glofox\Domain\Programs\Models\Program;
use Glofox\Domain\SalesTaxes\ServiceType;
use Glofox\Domain\TimeSlotPatterns\Exceptions\TimeSlotPatternNotFoundException;
use Glofox\Domain\TimeSlotPatterns\Repositories\TimeSlotPatternsRepository;
use Glofox\Domain\TimeSlots\Exceptions\TimeSlotNotFoundException;
use Glofox\Domain\TimeSlots\Models\TimeSlot;
use Glofox\Domain\TimeSlots\Repositories\TimeSlotRepository;
use Glofox\Domain\TimeSlots\UseCase\BookTimeSlot;
use Glofox\Domain\TimeSlots\UseCase\BookTimeSlotParams;
use Glofox\Domain\TimeSlots\Validation\TimeSlotMembershipValidator;
use Glofox\Domain\Users\Exceptions\UserNotFoundException;
use Glofox\Domain\Users\Exceptions\UserOriginBranchIdException;
use Glofox\Domain\Users\Formatters\LegacyChildResponseFormatter;
use Glofox\Domain\Users\Lock\Context;
use Glofox\Domain\Users\Lock\Locker;
use Glofox\Domain\Users\Lock\Resource;
use Glofox\Domain\Users\Models\User;
use Glofox\Domain\Users\Repositories\UsersRepository;
use Glofox\Domain\Users\Services\StrikeValidationServiceInterface;
use Glofox\Domain\Users\ValueObjects\UserId;
use Glofox\Infrastructure\Flags\Flag;
use Glofox\Infrastructure\Flags\Flagger;
use Glofox\Infrastructure\Honeycomb\HoneycombTracker;
use Glofox\Repositories\Search\Expressions\Shared\Id;
use Glofox\Request;
use Glofox\Validation\Exceptions\ValidationException;
use Illuminate\Http\JsonResponse;
use Psr\Log\LoggerInterface;

/**
 *
 * @uses AppController
 *
 * @property \Booking $Booking
 * @property \User $User
 * @property \Event $Event
 * @property \Branch $Branch
 * @property TimeSlotPattern $TimeSlotPattern
 * @property \Program $Program
 * @property Course $Course
 * @property Membership $Membership
 * @property StripeCharge $StripeCharge
 * @property UserCredit $UserCredit
 * @property S3Component $S3
 * @property JWTComponent $JWT
 * @property paginatorComponent $paginator
 * @property UtilityComponent $Utility
 * @property NotificationComponent $Notification
 * @property EmailComponent $Email
 */
class BookingsController extends \Glofox\Domain\Bookings\Http\BookingsController
{
    private const MAX_BOOKINGS_LIMIT_BY_BRANCH_AND_USER_ID = 1000;

    public $name = 'Bookings';
    public $uses = [
        'Booking',
        'User',
        'Event',
        'Branch',
        'Program',
        'Course',
        'Membership',
        'StripeCharge',
        'UserCredit',
    ];
    public $components = [
        'S3',
        'JWT',
        'Paginator',
        'Utility',
        'Notification',
    ];
    private TimeSlotPatternsRepository $timeSlotPatternsRepository;
    private FacilitiesRepository $facilitiesRepository;
    private TimeSlotRepository $timeSlotsRepository;
    private BranchesRepository $branchesRepository;
    private BookingsRepository $bookingsRepository;
    private CreditsRepository $creditsRepository;
    private EventsRepository $eventsRepository;
    private UsersRepository $usersRepository;

    private BookingUpdateRequestEventPublisherInterface $bookingUpdateRequestEventPublisher;
    private ValidateUserHasActiveNonPAYGMembership $userHasMembershipValidator;
    private BookingRequestsPollingService $bookingRequestsPollingService;
    private AddonEligibilityServiceInterface $addonsEligibilityService;
    private StrikeValidationServiceInterface $strikeValidationService;
    private EventBookingLockerInterface $bookingAppointmentLocker;
    private BookingRequestsPublisher $bookingRequestsPublisher;
    private OnlineEventUrlGenerator $onlineEventUrlGenerator;
    private BookingEventLockerInterface $bookingEventLocker;
    private BookingsPublisher $bookingPublisher;
    private LoggerInterface $logger;
    private Locker $userLocker;
    private FeatureFlagInterface $featureFlagInterface;

    private const PLATFORM = 'CORE-API';

    public function __construct($request, $response)
    {
        parent::__construct($request, $response);
        $this->timeSlotPatternsRepository = app()->make(TimeSlotPatternsRepository::class);
        $this->facilitiesRepository = app()->make(FacilitiesRepository::class);
        $this->timeSlotsRepository = app()->make(TimeSlotRepository::class);
        $this->branchesRepository = app()->make(BranchesRepository::class);
        $this->bookingsRepository = app()->make(BookingsRepository::class);
        $this->creditsRepository = app()->make(CreditsRepository::class);
        $this->eventsRepository = app()->make(EventsRepository::class);
        $this->usersRepository = app()->make(UsersRepository::class);

        $this->bookingUpdateRequestEventPublisher = app()->make(BookingUpdateRequestEventPublisherInterface::class);
        $this->userHasMembershipValidator = app()->make(ValidateUserHasActiveNonPAYGMembership::class);
        $this->bookingRequestsPollingService = app()->make(BookingRequestsPollingService::class);
        $this->addonsEligibilityService = app()->make(AddonEligibilityServiceInterface::class);
        $this->strikeValidationService = app()->make(StrikeValidationServiceInterface::class);
        $this->bookingAppointmentLocker = app()->make(EventBookingLockerInterface::class);
        $this->bookingRequestsPublisher = app()->make(BookingRequestsPublisher::class);
        $this->onlineEventUrlGenerator = app()->make(OnlineEventUrlGenerator::class);
        $this->bookingEventLocker = app()->make(BookingEventLockerInterface::class);
        $this->bookingPublisher = app()->make(BookingsPublisher::class);
        $this->logger = app()->make(LoggerInterface::class);
        $this->userLocker = app()->make(Locker::class);
    }

    public function dispatcher($identifier = null)
    {
        $method = $this->getMethod();
        $provider = $this->Booking;

        if (in_array($method, ['POST', 'DELETE'])) {
            return $this->container()->call([$this, 'book'], ['identifier' => $identifier]);
        }

        return $this->dispatch($identifier, $provider);
    }

    /**
     * @throws JsonException
     * @throws UnsuccessfulOperation
     * @throws \Glofox\Exception
     */
    public function book(BookRequest $request, $identifier = null)
    {
        $this->injectAPIVersion($this->Booking, $this->request->url);
        $method = $this->getMethod();
        $payload = $this->getPayload();
        $payload['branch_id'] = $request->getBranchId();
        $user = $this->JWT->getUser();
        $bookingRequestParams = new BookingRequestParameters(
            $request->getCreateSlot(),
            $request->getUserId(),
            $request->getBranchId()
        );

        switch ($method) {
            case 'POST':
                try {
                    switch ($payload['model']) {
                        case BookableModel::COURSE:
                            $response = $this->bookCourseAgnostic($payload);
                            break;

                        case BookableModel::TIMESLOT:
                        case BookableModel::FACILITY:
                            $response = $this->bookTimeSlotAgnostic($payload, $bookingRequestParams);
                            break;

                        case BookableModel::EVENT:
                            $response = $this->bookEventAgnostic($payload);
                            break;

                        default:
                            throw new NotFoundException();
                    }
                    break;
                } catch (Exception $exception) {
                    \event()->emit(
                        BookingHasFailed::class,
                        [$request->getUserId(), $request->getBranchId(), $payload, $exception->getMessage()]
                    );
                    throw $exception;
                }
            case 'DELETE':
                $userID = null;
                if (!empty($payload['user_id']) && in_array($user['type'], UserType::ADMINISTRATORS, true)) {
                    $userID = $payload['user_id'];
                }

                $authorId = null;
                if (!empty($payload['author_id']) && Auth::integrator() !== null) {
                    $authorId = (string)$payload['author_id'];
                }

                $response = $this->cancel($identifier, $userID, $authorId);
                break;

            default:
                $response = [];
        }

        if ($response instanceof JsonResponse) {
            $response = $response->content();
        }

        $response = $this->normaliseBookingResponse(
            $request->getUserId(),
            $request->getBranchId(),
            $payload,
            $response,
        );

        return json_encode($response, JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    /**
     * @throws InvalidBranchIdException
     * @throws InvalidMongoIdException
     * @throws TimeSlotPatternNotFoundException
     * @throws UnsuccessfulOperation
     * @throws UserNotFoundException
     * @throws UserOriginBranchIdException
     */
    public function processPayment(ProcessPaymentRequestV2 $request): JsonResponse
    {
        $req = ProcessPaymentRequestV1::capture();

        $allowedRoles = [
            UserType::ADMIN,
            UserType::SUPERADMIN,
            UserType::RECEPTIONIST,
            UserType::TRAINER,
            UserType::MEMBER,
        ];
        $this->JWT->validateOrFail($allowedRoles, null, $req->branchId());

        return response()->json(
            app()->make(PaymentProcessorService::class)
                ->withBranchId($req->branchId())
                ->withMemberId($req->memberId())
                ->withBookingId($req->bookingId())
                ->withPaymentMethod($req->paymentMethod())
                ->withSoldByUserId($req->soldByUserId())
                ->withOffSession($req->isOffSession())
                ->withPrice($req->price())
                ->withDiscounts($req->discounts())
                ->process()
        );
    }

    /**
     * @throws EventNotFoundException
     * @throws InvalidMongoIdException
     * @throws BookingValidatorException
     * @throws JsonException
     * @throws InvalidEventException
     * @throws BranchNotFoundException
     * @throws \Glofox\Exception
     */
    public function add(AddRequest $req)
    {
        if (!$req->isMethod('POST')) {
            return response()->json(
                [
                    'success' => false,
                    'message' => 'Invalid Post Request',
                    'message_code' => 'INVALID_POST_REQUEST',
                ]
            );
        }

        $sessionUser = $this->JWT->getUser();

        $refactoredCodeFlagger = app()->make(UseRefactoredCodeForBookingFlagger::class);
        if ($refactoredCodeFlagger->has($req->branchId())) {
            $processBookingUseCase = app()->make(ProcessBooking::class);
            $params = new ProcessBookingParams(
                $req->branchId(),
                $req->memberId(),
                $sessionUser['_id'],
                $req->eventId(),
                $req->paymentMethod(),
                [],
                $req->metadata(),
                null,
                $req->guestBookings(),
                $req->isPayLater(),
                $req->isOffSession(),
                false,
                !empty($sessionUser['integrator']),
                false
            );

            return response()->json($processBookingUseCase->execute($params));
        }

        $memberId = $req->memberId();
        $branchId = $req->branchId();
        $eventId = $req->eventId();
        $guestBookings = $req->guestBookings();
        $payGym = $req->isPayLater();
        $paymentMethod = $req->paymentMethod();
        $offSession = $req->isOffSession();
        $metadata = $req->metadata();
        $batchId = $req->batchId();
        $isChargeable = $req->isChargeable();

        $hasAccess = isset($sessionUser) ? $this->JWT->validate(
            ['member'],
            $sessionUser['namespace'],
            $branchId
        ) : ['success' => true];

        $eventId = EventId::from($eventId);
        $memberId = UserId::from($memberId);

        if (!$hasAccess['success']) {
            return json_encode($hasAccess, JSON_PARTIAL_OUTPUT_ON_ERROR);
        }

        $event = $this->Event->findById($eventId->value());
        if (empty($event)) {
            return json_encode([
                'success' => false,
                'message' => 'Invalid Event',
                'message_code' => 'INVALID_EVENT',
            ], JSON_THROW_ON_ERROR);
        }

        $is_private = isset($event['Event']['private']) && filter_var(
            $event['Event']['private'],
            FILTER_VALIDATE_BOOLEAN
        );

        if ($is_private) {
            return json_encode([
                'success' => false,
                'message' => 'This event is private, only administrators can book you into this class',
                'message_code' => 'THIS_EVENT_IS_PRIVATE',
            ], JSON_THROW_ON_ERROR);
        }

        $branch = $this->Branch->findById($event['Event']['branch_id']);

        $member = $this->User->findById($memberId->value());
        if (empty($member)) {
            return json_encode([
                'success' => false,
                'message' => 'Invalid Password: Please log out and back in and you will be able to book',
                'message_code' => 'INVALID_MEMBER_OR_INVALID_CREDENTIALS',
            ], JSON_THROW_ON_ERROR);
        }

        $program = $this->Program->findById($event['Event']['program_id']);

        $isWebAdmin = false;
        $price = null;

        $spotLeftValidator = app()->make(EventHasAvailableSpotLegacyValidator::class);
        $validateHasSpotLeft = $spotLeftValidator->validate(
            $event['Event']['_id'],
            $guestBookings,
            false,
            $sessionUser ? User::make($sessionUser) : null
        );

        if (!$validateHasSpotLeft['success']) {
            return json_encode($validateHasSpotLeft, JSON_PARTIAL_OUTPUT_ON_ERROR);
        }

        $validationParams = new ValidationParams(
            Branch::make($branch['Branch']),
            User::make($member['User']),
            Auth::user(),
            Event::make($event['Event']),
            Program::make($program['Program']),
            $paymentMethod ?? PaymentMethods::CREDIT_CARD,
            [],
            $metadata,
            $price,
            $guestBookings,
            $payGym,
            $offSession,
            false,
            false,
            false
        );

        app()->make(ActiveLimitBookingValidator::class)->validate($validationParams);

        try {
            app()->make(MemberMaximumBookings::class)->validate($validationParams);
        } catch (Exception $e) {
            return json_encode([
                'success' => false,
                'message' => $e->getMessage(),
                'message_code' => 'LIMIT_BOOKING_REACHED',
            ], JSON_PARTIAL_OUTPUT_ON_ERROR);
        }

        return json_encode(
            $this->generateBookings(
                $event,
                $member,
                $program,
                $branch,
                $payGym,
                false,
                $sessionUser !== null ? User::make($sessionUser) : null,
                $paymentMethod,
                $price,
                $guestBookings,
                $offSession,
                [],
                false,
                false,
                $metadata,
                $batchId,
                $isChargeable
            ),
            JSON_PARTIAL_OUTPUT_ON_ERROR
        );
    }

    public function isValidBookingWindow($branch, $event, $timezone = 'UTC'): array
    {
        $event_start_time = $event['Event']['time_start'];
        $current_timestamp = $this->Booking->getCurrentTime($timezone);
        $booking_open_window = $branch['Branch']['features']['booking']['booking_open_window'] ?? 1;
        if (empty($booking_open_window)) {
            return ['success' => true];
        }
        $opening_time = $this->Booking->subtractNumberOfHours($event_start_time, $booking_open_window, 'datetime');
        $is_valid = (strtotime($opening_time) < $current_timestamp);
        $opening_time = $this->Booking->formatDate($opening_time, "l jS \of F Y \a\\t h:i A", $timezone);
        if ($is_valid) {
            return ['success' => true];
        }

        return [
            'success' => false,
            'message' => 'You cannot book this until ' . $opening_time,
            'message_code' => 'YOU_CANNOT_BOOK_UNTIL',
            'message_data' => $opening_time,
        ];
    }

    /**
     * @param $event
     * @param $member
     * @param $program
     * @param $branch
     * @param $payLater
     * @param $isWebAdmin
     * @param User|null $sessionUserModel
     * @param string $paymentMethod
     * @param null $price
     * @param int $guestBookings
     * @param bool $offSession
     * @param array $discounts
     * @param bool $isReservationProcessor
     * @param bool $forceOverbook
     * @param array $metadata
     * @param null $batchId
     * @param bool $isChargeable
     * @param bool $isChargeEnabledForIntegrator
     * @return array
     * @throws BranchNotFoundException
     * @throws InvalidMongoIdException
     * @throws \Glofox\NotFoundException
     * @throws Exception
     */
    public function generateBookings(
        $event,
        $member,
        $program,
        $branch,
        $payLater,
        $isWebAdmin,
        ?User $sessionUserModel,
        $paymentMethod = 'credit_card',
        $price = null,
        $guestBookings = 0,
        $offSession = true,
        array $discounts = [],
        bool $isReservationProcessor = false,
        $forceOverbook = false,
        $metadata = [],
        $batchId = null,
        $isChargeable = true,
        $isChargeEnabledForIntegrator = false
    ) {
        $startTime = microtime(true);
        $sessionUser = $sessionUserModel ? $sessionUserModel->toArray() : $this->JWT->getUser();
        if ($sessionUser === null) {
            $this->logger->info(
                '[BookingsController::generateBookings] no session user could be found.'
            );
        }

        /** @var \Event $eventCakeModel */
        $eventCakeModel = app()->make(\Event::class);
        $eventModel = Event::make($event['Event']);
        $branchModel = Branch::make($branch['Branch']);
        $eventBranchId = $event['Event']['branch_id'];
        $eventBranchModel = $this->branchesRepository->getById($eventBranchId);

        $isAnIntegration = !empty($sessionUser['integrator']);
        $isFromSameCorporation = $branchModel->isFromSameCorporation($eventBranchModel);
        if ($isAnIntegration && !$isFromSameCorporation) {
            return [
                'success' => false,
                'message' => 'The event does not belong to the branch corporation',
                'message_code' => 'EVENT_DOES_NOT_BELONG_TO_BRANCH_CORPORATION',
            ];
        }

        $branchId = (string)$branchModel->id();
        $timezone = $branchModel->timezone()->getName();
        $waitingListSize = $branchModel->waitingListSize();
        $totalBookings = $guestBookings + 1;
        $memberId = $member['User']['_id'];
        $eventId = (string)$eventModel->id();
        $charge = [];

        /** @var \Booking $bookingCakeModel */
        $bookingCakeModel = app()->make(\Booking::class);

        try {
            $this->lockEvent($branchId, $eventId);
        } catch (EventBookingLockerException $e) {
            $this->unlockEvent($branchId, $eventId);
            return [
                'success' => false,
                'message' => $e->getMessage(),
                'message_code' => 'A_TRANSACTION_IS_ALREADY_IN_PROGRESS',
            ];
        }

        $lock = new Resource($memberId, Context::forEvent($eventId));
        $isLocked = $this->userLocker->lock($lock);
        if (!$isLocked) {
            $this->unlockEvent($branchId, $eventId);
            return [
                'success' => false,
                'message' => 'A transaction is already in progress',
                'message_code' => 'A_TRANSACTION_IS_ALREADY_IN_PROGRESS',
            ];
        }

        try {
            if ($payLater && !$eventBranchModel->acceptsPayLaterAsPaymentMethod()) {
                $this->unlockEvent($branchId, $eventId);
                $this->userLocker->unlock();
                return [
                    'success' => false,
                    'message' => 'Pay Later is not an allowed payment method in this branch',
                    'message_code' => 'PAY_LATER_IS_NOT_AN_ALLOWED_PAYMENT_METHOD_IN_THIS_BRANCH',
                ];
            }

            $booking = $bookingCakeModel->findByEventIdAndUserId($eventId, $memberId);
            $currentBookingStatus = $booking['Booking']['status'] ?? null;
            $currentBookingConfirmed = $booking['Booking']['confirmed'] ?? false;
            $isUnconfirmedBooking = Status::BOOKED === $currentBookingStatus && !$currentBookingConfirmed;
            if (!empty($booking) && 'BOOKED' === $currentBookingStatus && $currentBookingConfirmed) {
                $this->unlockEvent($branchId, $eventId);
                $this->userLocker->unlock();

                return [
                    'success' => false,
                    'message' => __('You have already booked this'),
                    'message_code' => 'YOU_HAVE_BOOKED_FOR_THIS_EVENT',
                ];
            }

            if (!$member['User']['active']) {
                $this->unlockEvent($branchId, $eventId);
                $this->userLocker->unlock();

                return [
                    'success' => false,
                    'message' => 'CANNOT_BOOK_AS_MEMBER_IS_DELETED',
                    'message_code' => 'CANNOT_BOOK_AS_MEMBER_IS_DELETED',
                ];
            }

            if (!$eventModel->isActive()) {
                $this->unlockEvent($branchId, $eventId);
                $this->userLocker->unlock();

                return [
                    'success' => false,
                    'message' => 'The Event has been cancelled.',
                    'message_code' => 'EVENT_HAS_BEEN_CANCELLED',
                ];
            }

            $eventStartTime = $event['Event']['time_start'];
            $eventFinishTime = $event['Event']['time_finish'];

            $user = User::make($member['User']);

            $pricingForUserParameters = new PricingForUserParameters(
                $eventModel,
                $user,
                $guestBookings,
                $isAnIntegration,
                false,
                true,
                $sessionUser['origin'] ?? null,
                $isChargeable
            );
            $pricingDetail = $eventCakeModel->pricingForUser($pricingForUserParameters);
            $pricing = $pricingDetail->pricing();
            $creditsToUse = $pricing->credits();
            $addon = $pricingDetail->addon();

            if (!$isReservationProcessor) {
                $overlap = $this->Booking->getOverlapingBookings($memberId, $eventStartTime, $eventFinishTime);
                if (!empty($overlap)) {
                    $this->unlockEvent($branchId, $eventId);
                    $this->userLocker->unlock();
                    $msg = 'You have already booked a class at this time.';
                    $messageData = [];
                    if (isset($overlap['Booking']['model_name'])) {
                        $bookingModelName = $overlap['Booking']['model_name'];
                        $msg = sprintf('%s Booking "%s"', $msg, $bookingModelName);
                        $messageData[] = $bookingModelName;
                    }
                    if (isset($overlap['Booking']['time_start'])) {
                        $bookingTimeStart = $overlap['Booking']['time_start'];
                        $msg = sprintf('%s At "%s"', $msg, $bookingTimeStart);
                        $messageData[] = $bookingTimeStart;
                    }

                    $logMsg = ', Origin Event ID: ' . ($event['Event']['_id'] ?? '') .
                        ', Origin Event Name: ' . ($event['Event']['name'] ?? '') .
                        ', Origin Member ID: ' . $memberId .
                        ', Origin Event Start Time: ' . $eventStartTime .
                        ', Origin Event Finish Time: ' . $eventFinishTime .
                        ', Overlap Event ID: ' . ($overlap['Booking']['_id'] ?? '') .
                        ', Overlap Event Name: ' . ($overlap['Booking']['model_name'] ?? '') .
                        ', Overlap Event Start Time: ' . ($overlap['Booking']['time_start'] ?? '') .
                        ', Overlap Event Finish Time: ' . ($overlap['Booking']['time_finish'] ?? '');
                    $this->logger->info('[BookingOverlappingError] ' . $logMsg);

                    return [
                        'success' => false,
                        'message' => __($msg),
                        'message_code' => 'BOOK_ERROR_ANOTHER_CLASS',
                        'message_data' => $messageData,
                    ];
                }
            }

            $validateBookingWindowOpen = $this->isValidBookingWindow($branch, $event, $timezone);
            if (!$validateBookingWindowOpen['success'] && !$isAnIntegration) {
                $this->unlockEvent($branchId, $eventId);
                $this->userLocker->unlock();

                return $validateBookingWindowOpen;
            }

            $closeWindow = $this->Branch->findEventCloseBookingWindow($branch);
            $validateBookingWindowClose = $this->Booking->validateCloseBookingWindow(
                $eventStartTime,
                $closeWindow,
                $timezone
            );
            if (!$validateBookingWindowClose['success'] && !$isWebAdmin) {
                $this->unlockEvent($branchId, $eventId);
                $this->userLocker->unlock();

                return $validateBookingWindowClose;
            }

            if (!$isWebAdmin) {
                $validateBookingWindow = $this->validateEventHasPassed($eventStartTime, $timezone);
                if (!$validateBookingWindow['success']) {
                    $this->unlockEvent($branchId, $eventId);
                    $this->userLocker->unlock();

                    return $validateBookingWindow;
                }
            }

            $programId = $program['Program']['_id'];
            $userCredits = $pricingDetail->userCredits();
            $membershipType = $member['User']['membership']['type'];

            $price = ($isWebAdmin && isset($price)) ? $price : $pricingDetail->unitPrice();

            $numCredits = $userCredits['num_sessions'];

            if (!empty($pricing->credits())) {
                $addonCredits = ($addon && $addon->hasCreditsLeft()) ? $addon->serviceAvailableCredits() : null;
                $this->logger->info(
                    sprintf(
                        'User %s has %s legacy credits and %s addon credits to use, we are charging %s',
                        $memberId,
                        $numCredits,
                        $addonCredits,
                        $pricing->credits()
                    )
                );
            }

            $shouldProcessPayment = false;

            if ($pricing->credits() < $totalBookings) {
                $classRelated = $this->Membership->isClassRelatedMembership($membershipType);

                if ($classRelated && 0 == $price && $isChargeable) {
                    $code = $this->User->get_error_code('membership_expired');
                    $message = 'You have no credits left. Please choose Pay as you Go or go to Memberships to purchase more';
                    $messageCode = 'YOU_HAVE_NO_CREDITS_LEFT';
                    $this->unlockEvent($branchId, $eventId);
                    $this->userLocker->unlock();

                    return [
                        'success' => false,
                        'message' => $message,
                        'code' => $code,
                        'message_code' => $messageCode,
                    ];
                }

                // Validate payment for class
                // @see https://glofox.atlassian.net/browse/DASH2-3726
                if (!$payLater && $isChargeable && $pricing->price() > 0) {
                    $shouldProcessPayment = true;
                }
                $isChargeEnabledForIntegrator &&
                $this->logger->info('[IntegrationChargeRequest] NOTICE: Payment information.', [
                    'paymentShouldBeProcessed' => $shouldProcessPayment,
                    'payLater' => $payLater,
                    'isChargeable' => $isChargeable,
                    'pricing' => $pricing->price()
                ]);
            }

            $bookedStatus = $this->Booking->get_status('booked');
            $paidWithCredits = !$payLater && !empty($userCredits['credits']);

            if (
                $isChargeable
                && $isAnIntegration
                && $isChargeEnabledForIntegrator
                && !$paidWithCredits
                && !PaymentMethods::isValidForIntegrator($paymentMethod)
            ) {
                $this->logger->info(
                    '[IntegrationChargeRequest] ERROR: Provided payment method is not allowed for integrations.',
                    ['paymentMethod' => $paymentMethod]
                );
                $this->unlockEvent($branchId, $eventId);
                $this->userLocker->unlock();
                return [
                    'success' => false,
                    'message' => 'This payment method is not allowed for integrations',
                    'message_code' => 'PAYMENT_METHOD_NOT_ALLOWED',
                ];
            }

            try {
                Auth::payments(PaymentMethodType::CARD);
                $hasPaymentsEnabled = true;
            } catch (Exception $exception) {
                $hasPaymentsEnabled = false;
            }

            $onlineEventUrl = $this->onlineEventUrlGenerator->generate($eventModel, $user);

            if (!$isUnconfirmedBooking) {
                $bookingRequest = $this->getBookingRequest(
                    Event::make($event['Event']),
                    User::make($member['User']),
                    $guestBookings,
                    $price,
                    $payLater,
                    $hasPaymentsEnabled,
                    $paidWithCredits,
                    $forceOverbook,
                    $metadata,
                    $sessionUser
                );

                if (!$bookingRequest || $bookingRequest->status() === BookingRequestStatus::REJECTED()) {
                    $this->bookingPublisher->sendBookingFinishedEvent(
                        new BookingFinishedEventMeta([
                            'branchId' => $eventModel->branchId(),
                            'memberId' => $memberId,
                            'eventId' => $eventId,
                        ]),
                        new BookingFinishedEventPayload([
                            'externalProviderStreamUrl' => $eventModel->externalProviderStreamUrl(),
                            'glofoxLiveStreamUrl' => $onlineEventUrl,
                            // Parameter from the new standard
                            'namespace' => $eventModel->namespace(),
                            'branch_id' => $eventModel->branchId(),
                            'user_id' => $user->id(),
                            'status' => Status::FAILED,
                            'time_start' => $eventModel->timeStart()->toDateTimeString(),
                            'time_finish' => $eventModel->timeFinish()->toDateTimeString(),
                            'guest_bookings' => $guestBookings,
                            'model' => \Glofox\Domain\Bookings\Type::EVENTS,
                            'model_id' => $eventModel->id(),
                            'model_name' => $eventModel->name(),
                            'paid' => false,
                            'attended' => false,
                        ])
                    );

                    $this->unlockEvent($branchId, $eventId);
                    $this->userLocker->unlock();

                    return [
                        'success' => false,
                        'message' => 'Booking request has been rejected while processing.',
                        'message_code' => 'BOOKING_REQUEST_REJECTED',
                    ];
                }
            }

            if ($shouldProcessPayment && !$isUnconfirmedBooking) {
                $this->logger->info(
                    sprintf(
                        'Starting payment processing in [calculatedPrice=%s, inputPrice=%s, isBookedByStaff=%s]',
                        $pricing->price() ?? 'null',
                        $price ?? 'null',
                        $isWebAdmin
                    )
                );

                $totalPrice = $pricing->price();
                if ($isWebAdmin && isset($price)) {
                    $this->logger->info(
                        sprintf(
                            'Calculated booking price was override by staff\'s input price 
                            [calculatedPrice=%s, inputPrice=%s]',
                            $totalPrice,
                            $price,
                        )
                    );

                    $totalPrice = $price;
                }

                $programName = $program['Program']['name'];
                $totalChargeableBookings = $totalBookings - $creditsToUse;
                $paymentMethod ??= PaymentMethods::CREDIT_CARD;

                $startTime = microtime(true);

                $paymentExecution = app()->make(PaymentExecutorService::class)
                    ->withPaymentMethod(PaymentMethods::byValue($paymentMethod))
                    ->withMember(User::make($member['User']))
                    ->withDiscounts($discounts ?: ($this->getPayload()['discounts'] ?? []))
                    ->withSoldByUserId($this->getPayload()['sold_by_user_id'] ?? null)
                    ->withDescription($programName)
                    ->withExternalId($programId)
                    ->withModelName($this->Event->useTable)
                    ->withModelId($eventId)
                    ->withPrice($totalPrice)
                    ->withNumberOfBookingsToPay($totalChargeableBookings)
                    ->withGuestBookings($guestBookings)
                    ->withOffSession((bool) filter_var($offSession, FILTER_VALIDATE_BOOLEAN))
                    ->execute();

                $endTime = microtime(true);

                $paymentExecutionTime = round($endTime - $startTime, 4);
                $this->trackPaymentTimeIntoHoneyComb(
                    'execute-payment-events',
                    $batchId,
                    $memberId,
                    $programId,
                    $branchId,
                    $paymentMethod,
                    $eventId,
                    $totalPrice,
                    $paymentExecutionTime,
                    $paymentExecution
                );

                if (!$paymentExecution['success']) {
                    $this->unlockEvent($branchId, $eventId);
                    $this->userLocker->unlock();

                    $isAnIntegration && $this->logger->info(
                        '[IntegrationChargeRequest] ERROR: Unsuccessful payment validation.',
                        $paymentExecution
                    );

                    return $paymentExecution;
                }
                $charge = $paymentExecution['charge'] ?? [];
            }

            $paid = false === $payLater && 'cash' === strtolower($paymentMethod);

            $createBookingRequest = new CreateBookingParameters();
            $createBookingRequest->setEvent($event);
            $createBookingRequest->setMember($member);
            $createBookingRequest->setStatus($bookedStatus);
            $createBookingRequest->setPayGym($payLater);
            $createBookingRequest->setPrice($price);
            $createBookingRequest->setHasPaymentsEnabled($hasPaymentsEnabled);
            $createBookingRequest->setGuestBookings($guestBookings);
            $createBookingRequest->setTimezone($timezone);
            $createBookingRequest->setPaid($paid);
            $createBookingRequest->setPaidWithCredits($paidWithCredits);
            $createBookingRequest->setConfirmed(true);
            $createBookingRequest->setBatchId($batchId);
            $createBookingRequest->setIsChargeable($isChargeable);

            if ($paidWithCredits) {
                $firstUserCreditsUsed = reset($userCredits['credits']);
                $createBookingRequest->setFirstUserCreditUsed($firstUserCreditsUsed);
            }

            $createBookingRequest->setSessionUser($sessionUser);
            $createBookingRequest->setMetadata($metadata);

            if ($addon instanceof Addon) {
                $createBookingRequest->setAddon($addon);
            }

            $createBookingRequest->setForceBooked($forceOverbook === true);

            $createBooking = $this->Booking->createBooking($createBookingRequest, $charge);

            if (empty($createBooking)) {
                $this->unlockEvent($branchId, $eventId);
                $this->userLocker->unlock();

                return [
                    'success' => false,
                    'message' => $this->Booking->get_latest_error(),
                    'message_code' => $this->Booking->get_latest_error(),
                ];
            }

            $paygWaiver = $member['User']['PAYGPAYMENT'] ?? false;
            if (!$paygWaiver) {
                $member['User']['PAYGPAYMENT'] = true;
            }

            $overrideAddonsCredits = $pricing->price() === null &&
                $addon &&
                $addon->hasCreditsLeft() &&
                $addon->serviceAvailableCredits() < $totalBookings;

            if (
                !$payLater
                && $isChargeable
                && !$overrideAddonsCredits
                && $addon !== null
                && $pricing->credits() > 0
            ) {
                $addonService = app()->make(AddonServiceInterface::class);
                $addonService->consumeAddonCredits(
                    $addon->serviceId(),
                    $pricing->credits(),
                    $createBooking['Booking']['_id'],
                    $eventModel->branchId(),
                    $eventModel->timeStart()
                );
            }

            if (
                !$payLater
                && !empty($userCredits['credits'])
                && $isChargeable
                && (!$addon || $overrideAddonsCredits)
            ) {
                $bookingId = $createBooking['Booking']['_id'];
                $this->logger->info(
                    sprintf('spending credits for user[%s] on booking[%s]', $user->id(), $bookingId),
                    ['credits' => $userCredits]
                );
                $this->UserCredit->spendUserCredits($userCredits['credits'], $bookingId, $totalBookings);
            }

            $eventBookings = $this->Booking->find(
                'count',
                [
                    'conditions' => ['event_id' => $eventId, 'status' => $bookedStatus],
                ]
            );

            $saveUserNeeded = false;
            $isTimeRelatedMembership = $this->Membership->isTimeRelatedMembership($membershipType);
            if ($isTimeRelatedMembership) {
                $saveUserNeeded = $this->User->updateMembershipDatesBasedOnBooking(
                    $member,
                    $createBooking,
                    false
                );
            }

            if ($saveUserNeeded) {
                $userToSync = User::make($member['User']);
                app()
                    ->make(UpdateDatesBaseOnBookingsService::class)
                    ->execute($userToSync);

                $this->User->save($member);
            }

            if ($forceOverbook) {
                $this->eventsRepository->updateById($eventId, [
                    'size' => $eventBookings,
                ]);
            }

            $this->unlockEvent($branchId, $eventId);
            $this->userLocker->unlock();

            $this->bookingPublisher->sendBookingFinishedEvent(
                new BookingFinishedEventMeta([
                    'branchId' => $eventModel->branchId(),
                    'memberId' => $member['User']['_id'],
                    'eventId' => $eventModel->id(),
                ]),
                new BookingFinishedEventPayload([
                    'bookingId' => $createBooking['Booking']['_id'],
                    'externalProviderStreamUrl' => $eventModel->externalProviderStreamUrl(),
                    'glofoxLiveStreamUrl' => $onlineEventUrl,
                    // Parameter from the new standard
                    'id' => $createBooking['Booking']['_id'],
                    'namespace' => $createBooking['Booking']['namespace'],
                    'branch_id' => $createBooking['Booking']['branch_id'],
                    'user_id' => $createBooking['Booking']['user_id'],
                    'status' => $createBooking['Booking']['status'],
                    'time_start' => $eventModel->timeStart()->toDateTimeString(),
                    'time_finish' => $eventModel->timeFinish()->toDateTimeString(),
                    'guest_bookings' => $createBooking['Booking']['guest_bookings'],
                    'model' => $createBooking['Booking']['type'],
                    'model_id' => $createBooking['Booking']['event_id'],
                    'model_name' => $createBooking['Booking']['event_name'],
                    'paid' => $createBooking['Booking']['paid'] ?? null,
                    'attended' => false,
                ])
            );

            try {
                $mappedBooking = $this->Booking->mapObject($createBooking);
            } catch (Throwable $e) {
                $mappedBooking = $createBooking['Booking'];
            }

            $endTime = microtime(true);
            $bookingExecutionSecondsTime = round($endTime - $startTime, 4);
            $this->trackBookingTimeIntoHoneyComb(
                'execution-booking-time',
                $memberId,
                $branchId,
                $programId,
                $bookingExecutionSecondsTime,
            );

            return [
                'success' => true,
                'Booking' => $mappedBooking,
            ];
        } catch (Exception $ex) {
            $this->logger->error(
                sprintf('failed to complete booking for user[%s] - error[%s]', $memberId, $ex->getMessage()),
                ['trace' => $ex->getTrace()],
            );
            $this->unlockEvent($branchId, $eventId);
            $this->userLocker->unlock();

            $dictionary = app()->make(DictionariesRepository::class)->findByBranchId($branchId);

            return [
                'success' => false,
                'message' => $dictionary->translate($ex->getMessage()),
                'message_code' => $ex->getMessage(),
            ];
        }
    }

    /**
     * @throws InvalidMongoIdException
     */
    public function first(
        ListFirstBookingsRequest $request
    ): JsonResponse {
        $user = Auth::user();

        $params = new ListFirstBookingsRequestParams(
            Carbon::createFromTimestamp($request->startDate()),
            Carbon::createFromTimestamp($request->endDate()),
            $user
        );

        $useCase = app()->make(ListFirstBookings::class);
        $results = $useCase->execute($params);

        return response()->json($results);
    }

    /**
     * @throws InvalidMongoIdException
     */
    public function noShows(
        ListNoShowsRequest $request,
        \User $userCakeModel
    ): JsonResponse {
        $user = Auth::user();
        $branch = $user->branch();

        $start = Carbon::createFromTimestamp($request->get('start'));
        $end = Carbon::createFromTimestamp($request->get('end'));

        if ($end->greaterThan(Carbon::now())) {
            $end = Carbon::now();
        }

        $results = $this->Booking->generateNoShowsReport($branch, $start, $end);

        $userCakeModel->setUpInjector([], null, $user);

        foreach ($results as &$item) {
            $item['_id'] = (string)$item['_id'];
            $item = $userCakeModel->injector->formatTimestamps($item);
        }
        unset($item);

        $formatter = new LegacyChildResponseFormatter();
        $results = $formatter->format($results);

        return response()->json($results);
    }

    /**
     * @throws InvalidMongoIdException
     */
    public function lateCancellations(
        ListLateCancellationsRequest $request,
        \User $userCakeModel
    ): JsonResponse {
        $user = Auth::user();
        $branch = $user->branch();

        $start = Carbon::createFromTimestamp($request->get('start'));
        $end = Carbon::createFromTimestamp($request->get('end'));

        $results = $this->Booking->generateLateCancellationsReport($branch, $start, $end);

        $userCakeModel->setUpInjector([], null, $user);

        foreach ($results as &$item) {
            $item['_id'] = (string)$item['_id'];
            $item = $userCakeModel->injector->formatTimestamps($item);
        }
        unset($item);

        $formatter = new LegacyChildResponseFormatter();
        $results = $formatter->format($results);

        return response()->json($results);
    }

    /**
     * @throws EventNotFoundException
     * @throws BookingValidatorException
     * @throws InvalidMongoIdException
     * @throws InvalidEventException
     * @throws BranchNotFoundException
     * @throws \Glofox\NotFoundException
     * @throws \Glofox\Exception
     * @throws UnsuccessfulOperation
     */
    public function add_web_admin(AddWebAdminRequest $req)
    {
        if (!$req->isMethod('POST')) {
            throw (new UnsuccessfulOperation('Invalid POST Request'))
                ->setMessageCode('INVALID_POST_REQUEST');
        }

        $sessionUser = $this->JWT->getUser();

        $refactoredCodeFlagger = app()->make(UseRefactoredCodeForBookingFlagger::class);
        if ($refactoredCodeFlagger->has($req->branchId())) {
            $processBookingUseCase = app()->make(ProcessBooking::class);
            $params = new ProcessBookingParams(
                $req->branchId(),
                $req->memberId(),
                $sessionUser['_id'],
                $req->eventId(),
                $req->paymentMethod(),
                $req->discounts(),
                $req->metadata(),
                $req->price(),
                $req->guestBookings(),
                $req->isPayLater(),
                $req->isOffSession(),
                $req->isForcingOverbooking(),
                !empty($sessionUser['integrator']),
                false
            );

            return response()->json($processBookingUseCase->execute($params));
        }

        $branchId = $req->branchId();
        $eventId = EventId::from($req->eventId());
        $memberId = UserId::from($req->memberId());
        $price = $req->price();
        $payGym = $req->isPayLater();
        $paymentMethod = $req->paymentMethod();
        $guestBookings = $req->guestBookings();
        $offSession = $req->isOffSession();
        $forceOverbook = $req->isForcingOverbooking();
        $discounts = $req->discounts();
        $metadata = $req->metadata();
        $isAnIntegration = !empty($sessionUser['integrator']);
        $isChargeable = true;

        $this->loadModel('StripeCharge');

        $event = $this->Event->findById($eventId->value());
        $program = (!empty($event)) ? $this->Program->findById($event['Event']['program_id']) : null;
        $branch = $this->Branch->findById($branchId);
        $member = $this->User->findById($memberId->value());
        if (empty($member)) {
            return json_encode([
                'success' => false,
                'message' => __('Member Not Found'),
                'message_code' => 'MEMBER_NOT_FOUND',
            ], JSON_PARTIAL_OUTPUT_ON_ERROR);
        }

        $spotLeftValidator = app()->make(EventHasAvailableSpotLegacyValidator::class);
        $validateHasSpotLeft = $spotLeftValidator->validate(
            $eventId->value(),
            $guestBookings,
            $forceOverbook,
            $sessionUser ? User::make($sessionUser) : null
        );

        app()->make(AddWebAdminValidator::class)
            ->validate(Auth::user(), $branchId, $member['User']['branch_id'], $event['Event']['branch_id']);

        if (!$member['User']['active']) {
            return json_encode([
                'success' => false,
                'message' => __('This account is not active.'),
                'message_code' => 'MEMBER_ACCOUNT_NOT_ACTIVE',
            ], JSON_PARTIAL_OUTPUT_ON_ERROR);
        }

        if (!$validateHasSpotLeft['success']) {
            if (!$isAnIntegration && $this->Branch->hasWaitingListEnabled($branchId)) {
                $waitingList = json_decode(
                    $this->waitlist($eventId->value(), $memberId->value(), $guestBookings, $discounts, $branchId),
                    true,
                    512,
                    JSON_PARTIAL_OUTPUT_ON_ERROR
                );
                if ($waitingList['success']) {
                    $waitingList['message'] = 'You have successfully added the member to the waiting list';
                }

                return json_encode($waitingList, JSON_PARTIAL_OUTPUT_ON_ERROR);
            }

            return json_encode($validateHasSpotLeft, JSON_PARTIAL_OUTPUT_ON_ERROR);
        }

        $validationParams = new ValidationParams(
            Branch::make($branch['Branch']),
            User::make($member['User']),
            Auth::user(),
            Event::make($event['Event']),
            Program::make($program['Program']),
            $paymentMethod ?? PaymentMethods::CREDIT_CARD,
            $discounts,
            $metadata,
            $price,
            $guestBookings,
            $payGym,
            $offSession,
            $forceOverbook,
            $isAnIntegration,
            false
        );

        app()->make(MemberMaximumBookings::class)->validate($validationParams);

        $chargeIntegratorsFlagger = app()->make(Flagger::class);
        $isChargeEnabledForIntegrator = $chargeIntegratorsFlagger
            ->withFlag(Flag::INTEGRATOR_ENABLE_CHARGE_IRIS())
            ->hasByBranchId($branchId);

        if ($isAnIntegration && $isChargeEnabledForIntegrator) {
            $isChargeable = $req->charge() ?? true;
            $paymentMethod = $req->paymentMethod() ?? PaymentMethods::CREDIT_CARD;
        }

        if ($isAnIntegration) {
            $this->logger->info(
                sprintf(
                    '[IntegrationChargeRequest] NOTICE: The FF is [%s] for branch [%s] and charge is set to [%s].',
                    $isChargeEnabledForIntegrator ? 'ENABLED' : 'DISABLED',
                    $branchId,
                    $req->charge() ? 'TRUE' : 'FALSE'
                )
            );
        }

        $processBooking = null;
        try {
            $processBooking = $this->generateBookings(
                $event,
                $member,
                $program,
                $branch,
                $payGym,
                true,
                $sessionUser !== null ? User::make($sessionUser) : null,
                $paymentMethod,
                $price,
                $guestBookings,
                $offSession,
                $discounts,
                false,
                $forceOverbook,
                $metadata,
                null,
                $isChargeable,
                $isChargeEnabledForIntegrator
            );

            if (isset($processBooking['success']) && !$processBooking['success']) {
                event()->emit(
                    BookingHasFailed::class,
                    [$memberId->value(), $branchId, $req->all(), $processBooking['message_code']]
                );
            }
        } catch (Exception $exception) {
            event()->emit(BookingHasFailed::class, [$memberId->value(), $branchId, $req->all(), $exception->getMessage()]);
            throw $exception;
        } finally {
            if (
                $processBooking && $isAnIntegration && $isChargeEnabledForIntegrator
                && (!$paymentMethod || $paymentMethod === PaymentMethods::CREDIT_CARD)
            ) {
                $payload = [
                    'eventName' => ($processBooking['Booking']['metadata']['service']['type'] ?? null) === 'credit' ?
                        EventType::INTEGRATORS_BOOKINGS_CREDIT_CONSUMPTION :
                        EventType::INTEGRATORS_BOOKINGS_CREDIT_CARD_PAYMENT,
                    'branchId' => $branchId,
                    'userId' => $memberId->value(),
                    'platform' => static::PLATFORM,
                    'data' => [
                        'success' => $processBooking['success'] ?? false,
                        'message' => $processBooking['message_code'] ?? null,
                        'charge' => $isChargeable,
                        'bookingStatus' => $processBooking['Booking']['status'] ?? null,
                    ],
                ];
                (app()->make(TrackEventPublisher::class))->sendEventToTrack(
                    new TrackingEventMeta([]),
                    new TrackingEventPayload($payload)
                );
                $this->logger->info(
                    '[IntegrationChargeRequest] RESULT: Integrator booking attempt finished.',
                    ['eventDispatched' => $payload]
                );
            }
        }

        return json_encode($processBooking, JSON_PARTIAL_OUTPUT_ON_ERROR);
    }


    /**
     * @throws Exception
     */
    public function spendUserCredit($user_credit_id, $booking_id)
    {
        $this->loadModel('UserCredit');

        return json_encode(
            $this->UserCredit->spendUserCredit($user_credit_id, $booking_id),
            JSON_PARTIAL_OUTPUT_ON_ERROR
        );
    }

    /**
     * @param $eventId
     * @param $userId
     * @param int $guestBookings
     * @param array $discounts
     * @param string|null $branchId
     * @return false|string
     *
     * @throws InvalidMongoIdException
     * @throws PaymentMethodNotAvailableForMembers
     * @throws PaymentProviderNotFoundException
     * @throws Exception
     */
    public function waitlist(
        $eventId,
        $userId,
        $guestBookings = 0,
        array $discounts = [],
        ?string $branchId = null
    ) {
        /** @var UsersRepository $usersRepository */
        $usersRepository = app()->make(UsersRepository::class);

        /** @var \Booking $bookingCakeModel */
        $bookingCakeModel = app()->make(\Booking::class);

        $eventId = EventId::from($eventId);
        $userId = UserId::from($userId);

        $member = $this->User->findById($userId->value());
        if (empty($member)) {
            return json_encode([
                'success' => false,
                'message' => __('Member Not Found'),
            ], JSON_PARTIAL_OUTPUT_ON_ERROR);
        }
        if (!$member['User']['active']) {
            return json_encode([
                'success' => false,
                'message' => __('Your account is not active.'),
                'message_code' => 'MEMBER_ACCOUNT_NOT_ACTIVE',
            ], JSON_PARTIAL_OUTPUT_ON_ERROR);
        }

        $branchId ??= $member['User']['branch_id'];
        $branch = $this->Branch->findById($branchId);
        $timezone = $this->Branch->getTimeZone($branch);

        $event = $this->Event->getByIdAndBranchId($eventId->value(), $branchId);
        if (empty($event)) {
            return json_encode([
                'success' => false,
                'message' => __('Event Not Found'),
                'message_code' => 'EVENT_NOT_FOUND',
            ], JSON_PARTIAL_OUTPUT_ON_ERROR);
        }

        $this->loadModel('Program');
        $program = $this->Program->findById($event['Event']['program_id']);
        if (empty($program)) {
            return json_encode([
                'success' => false,
                'message' => __('Program Not Found'),
                'message_code' => 'PROGRAM_NOT_FOUND',
            ], JSON_PARTIAL_OUTPUT_ON_ERROR);
        }

        $membershipValidationResult = false;

        $eventAsModel = Event::make($event['Event']);
        $user = User::make($member['User']);
        $branchAsModel = Models\Branch::make($branch['Branch']);

        $strikeValidationResult = $this->strikeValidationService->validate($branchAsModel, $user);

        if (!$strikeValidationResult['success']) {
            return json_encode($strikeValidationResult, JSON_PARTIAL_OUTPUT_ON_ERROR);
        }

        $addonsValidationResult = $this->addonsEligibilityService->validate($user, $eventAsModel);

        if (!$addonsValidationResult) {
            $this->logger->info(
                sprintf(
                    'User %s has no eligible addons for event %s, starting membership check',
                    $user->id(),
                    $eventId->value()
                )
            );

            $userMembershipValidRequest = new UserMembershipValidParameters();
            $userMembershipValidRequest->setBranch($branch);
            $userMembershipValidRequest->setMember($member);
            $userMembershipValidRequest->setProgram($program);
            $userMembershipValidRequest->setEvent($event);
            $userMembershipValidRequest->setGuestBookings($guestBookings);

            $validateMembership = $bookingCakeModel->userMembershipValid($userMembershipValidRequest);

            $membershipValidationResult = (bool)$validateMembership['success'];
        }

        if (!$addonsValidationResult && !$membershipValidationResult) {
            return json_encode($validateMembership, JSON_PARTIAL_OUTPUT_ON_ERROR);
        }

        $validateHasNoSpotLeft = $this->validateEventHasNoSpotLeft($event, $guestBookings);

        if (!$validateHasNoSpotLeft['success']) {
            return json_encode($validateHasNoSpotLeft, JSON_PARTIAL_OUTPUT_ON_ERROR);
        }

        $validateEventIsActive = $this->validateEventIsActive($event);
        if (!$validateEventIsActive['success']) {
            return json_encode($validateEventIsActive, JSON_PARTIAL_OUTPUT_ON_ERROR);
        }

        $validateBookingWindow = $this->validateEventHasPassed($event['Event']['time_start'], $timezone);
        if (!$validateBookingWindow['success']) {
            $validateBookingWindow['message'] = 'You can not join waiting list of an event that already has passed.';

            return json_encode($validateBookingWindow, JSON_PARTIAL_OUTPUT_ON_ERROR);
        }

        $categories = $program['Program']['categories'] ?? [];

        $userCredits = $this->UserCredit->findCreditsForBooking(
            $program['Program']['branch_id'],
            $member['User']['_id'],
            $event['Event']['date'],
            $this->Program->useTable,
            $program['Program']['_id'],
            $categories
        );

        $numCredits = $userCredits['num_sessions'];

        // If no credits were found and the membership of the Booker is a credit-based membership,
        // we can't allow them to join the waitlist, as they might not have any credits in the
        // account when the waitlist starts to be processed.
        // @see https://glofox.atlassian.net/browse/DASH2-4879
        if (!$numCredits) {
            /** @var User $user */
            $user = $usersRepository->addCriteria(new Id($userId->value()))
                ->skipCallbacks()
                ->firstOrFail();

            if ($user->membership()->isCreditOnly()) {
                throw new Exception('NOT_ENOUGH_CREDITS_TO_JOIN_WAITLIST');
            }
        }

        $defaultPrice = $program['Program']['default_price'] ?? null;

        $validateAllowedGroups = $this->validateAllowedGroups(
            $member,
            $program['Program']['branch_id'],
            $program['Program']['allowed_member_types'],
            false,
            $defaultPrice,
            $program['Program']['_id']
        );

        if (!$validateAllowedGroups['success'] && empty($numCredits) && !$addonsValidationResult) {
            return json_encode($validateAllowedGroups, JSON_PARTIAL_OUTPUT_ON_ERROR);
        }

        $validateBookingWindowOpen = $this->validateBookingWindowOpen($event, $timezone);
        if (!$validateBookingWindowOpen['success']) {
            return json_encode($validateBookingWindowOpen, JSON_PARTIAL_OUTPUT_ON_ERROR);
        }

        $validateWaitlistSupport = $this->validateWaitingListSubFeature($member);
        if (!$validateWaitlistSupport['success']) {
            return json_encode($validateWaitlistSupport, JSON_PARTIAL_OUTPUT_ON_ERROR);
        }

        $validateMemberAlreadyBook = $this->validateMemberAlreadyBooked($member, $event);
        if (!$validateMemberAlreadyBook['success']) {
            return json_encode($validateMemberAlreadyBook, JSON_PARTIAL_OUTPUT_ON_ERROR);
        }

        $validateMemberBookStatus = $this->validateMemberBookStatus($member, $event);
        if (!$validateMemberBookStatus['success']) {
            return json_encode($validateMemberBookStatus, JSON_PARTIAL_OUTPUT_ON_ERROR);
        }

        $validateHasSpotWait = $this->validateEventWaitingList($branchId, $eventId, $guestBookings ?? 0);
        if (!$validateHasSpotWait['success']) {
            return json_encode($validateHasSpotWait, JSON_PARTIAL_OUTPUT_ON_ERROR);
        }

        $eventObject = Event::make($event['Event']);
        $bookingStatus = (string)$this->Booking->get_status('waiting');

        $waitlistBookingRequestCreator = app()->make(WaitlistBookingRequestCreatorService::class);
        $params = new CreateWaitlistBookingRequestParams($user, $eventObject, $guestBookings ?? 0, $bookingStatus);

        $bookingRequest = $waitlistBookingRequestCreator->execute($params);

        if (!$bookingRequest || $bookingRequest->status() === BookingRequestStatus::REJECTED()) {
            $this->bookingPublisher->sendWaitlistFinishedEvent(
                new WaitlistFinishedEventMeta([
                    'memberId' => $member['User']['_id'],
                    'eventId' => $event['Event']['_id'],
                ]),
                new WaitlistFinishedEventPayload([])
            );

            return json_encode([
                'success' => false,
                'message' => 'Waitlist request has been rejected while processing.',
                'message_code' => 'WAITLIST_REQUEST_REJECTED',
            ], JSON_THROW_ON_ERROR);
        }

        $bookingStatus = $this->Booking->get_status('waiting');

        $createBookingRequest = new CreateBookingParameters();
        $createBookingRequest->setEvent($event);
        $createBookingRequest->setMember($member);
        $createBookingRequest->setStatus($bookingStatus);
        $createBookingRequest->setMetadata(compact('discounts'));
        $createBookingRequest->setConfirmed(true);

        if ($addonsValidationResult instanceof Addon) {
            $createBookingRequest->setAddon($addonsValidationResult);
        }

        $createBooking = $this->Booking->createBooking($createBookingRequest, []);

        if ($createBooking) {
            $booking = current($this->Booking->afterFind([$createBooking]));
            $booking = current($this->Booking->setWaitingListPosition([$booking]));

            $this->bookingPublisher->sendWaitlistFinishedEvent(
                new WaitlistFinishedEventMeta([
                    'memberId' => $member['User']['_id'],
                    'eventId' => $event['Event']['_id'],
                ]),
                new WaitlistFinishedEventPayload([
                    'bookingId' => $createBooking['Booking']['_id'],
                ])
            );

            return json_encode([
                'success' => true,
                'message' => 'You have successfully added the member to the waiting list',
                'Booking' => $booking,
            ], JSON_PARTIAL_OUTPUT_ON_ERROR);
        }

        return json_encode([
            'success' => false,
            'message' => $this->Booking->get_latest_error(),
            'message_code' => $this->Booking->get_latest_error(),
        ], JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    /**
     * @throws \Glofox\Exception
     */
    public function cancel($bookingId, $userId = null, ?string $authorId = null): JsonResponse
    {
        $sessionUser = User::make($this->JWT->getUser());

        if (!empty($authorId) && $sessionUser->isIntegrator() && $authorId !== $sessionUser->id()) {
            $sessionUser = $this->usersRepository->getById($authorId);
        }

        $useCase = app()->make(CancelBooking::class);
        $params = new CancelBookingParams(
            $sessionUser,
            $bookingId,
            $userId,
            $authorId
        );

        $result = $useCase->execute($params);
        if (!$result['success']) {
            $this->logger->info(
                sprintf(
                    '[BookingsController::cancel] Cannot cancel booking %s due to %s',
                    $bookingId,
                    $result['message']
                )
            );
        }

        return response()->json($result);
    }

    /**
     * @throws InvalidBranchIdException
     * @throws InvalidMongoIdException
     * @throws PricingStrategyNotFoundException
     * @throws ReflectionException
     * @throws TimeSlotNotFoundException
     * @throws UserNotFoundException
     * @throws \Glofox\Exception
     * @throws \Glofox\NotFoundException
     */
    public function time_slot_add(string $timeSlotId): string
    {
        $requestData = $this->getPayload();

        $isValidMongoId = $this->Utility->is_valid_mongoid($timeSlotId);
        if (empty($isValidMongoId)) {
            return json_encode(
                ['success' => false, 'message' => __('Invalid Time Slot Id')],
                JSON_PARTIAL_OUTPUT_ON_ERROR
            );
        }

        $sessionUser = $this->JWT->getUser();
        $allowedRoles = ['member'];
        $namespace = $sessionUser['namespace'] ?? '';
        $branchId = $sessionUser['branch_id'] ?? '';
        $hasAccess = $this->JWT->validate($allowedRoles, $namespace, $branchId);
        if (!$hasAccess['success']) {
            return json_encode($hasAccess, JSON_PARTIAL_OUTPUT_ON_ERROR);
        }

        $usersRepository = app()->make(UsersRepository::class);
        $member = $usersRepository->getById($sessionUser['_id']);
        if (!$member->isActive()) {
            return json_encode(
                ['success' => false, 'message' => __('Your account is not active.')],
                JSON_PARTIAL_OUTPUT_ON_ERROR
            );
        }

        $payGym = (isset($requestData['pay_gym']))
            ? filter_var($requestData['pay_gym'], FILTER_VALIDATE_BOOLEAN)
            : false;
        $paymentMethod = PaymentMethods::CREDIT_CARD;
        $isChargeable = !isset($requestData['charge']) || filter_var(
            $requestData['charge'],
            FILTER_VALIDATE_BOOLEAN
        );

        return json_encode(
            $this->processTimeSlotBooking(
                $timeSlotId,
                $member,
                false,
                $payGym,
                false,
                $paymentMethod,
                true,
                null,
                null,
                $isChargeable,
            ),
            JSON_PARTIAL_OUTPUT_ON_ERROR
        );
    }

    /**
     * @throws InvalidBranchIdException
     * @throws InvalidMongoIdException
     * @throws PricingStrategyNotFoundException
     * @throws ReflectionException
     * @throws TimeSlotNotFoundException
     * @throws UserNotFoundException
     * @throws \Glofox\Exception
     * @throws \Glofox\NotFoundException
     */
    public function time_slot_add_web(
        $timeSlotId,
        $userId,
        $paymentMethod = null,
        $price = null,
        $batchId = null,
        bool $isChargeable = true
    ): string {
        $user = $this->JWT->getUser();
        $allowedRoles = ['admin', 'superadmin', 'receptionist', 'trainer'];
        $namespace = $user['namespace'] ?? '';
        $branchId = $user['branch_id'] ?? '';

        $hasAccess = $this->JWT->validate($allowedRoles, $namespace, $branchId);
        if (!$hasAccess['success']) {
            return json_encode($hasAccess, JSON_PARTIAL_OUTPUT_ON_ERROR);
        }

        $timeSlotId = $this->Utility->is_valid_mongoid($timeSlotId);
        if (empty($timeSlotId)) {
            return json_encode(
                ['success' => false, 'message' => __('Invalid Time Slot Id')],
                JSON_PARTIAL_OUTPUT_ON_ERROR
            );
        }

        $userId = $this->Utility->is_valid_mongoid($userId);
        if (empty($userId)) {
            return json_encode(['success' => false, 'message' => __('Invalid User Id')], JSON_PARTIAL_OUTPUT_ON_ERROR);
        }

        $usersRepository = app()->make(UsersRepository::class);
        $member = $usersRepository->getById($userId);
        if (!$member->isActive()) {
            return json_encode(
                ['success' => false, 'message' => __('Your account is not active.')],
                JSON_PARTIAL_OUTPUT_ON_ERROR
            );
        }

        $requestData = $this->getPayload() ?? [];
        $payGym = isset($requestData['pay_gym'])
            ? filter_var($requestData['pay_gym'], FILTER_VALIDATE_BOOLEAN)
            : false;
        $isChargeable = isset($requestData['charge'])
            ? (bool) filter_var($requestData['charge'], FILTER_VALIDATE_BOOLEAN)
            : $isChargeable;

        return json_encode(
            $this->processTimeSlotBooking(
                $timeSlotId,
                $member,
                false,
                $payGym,
                true,
                $paymentMethod,
                $price,
                true,
                $batchId,
                $isChargeable
            ),
            JSON_PARTIAL_OUTPUT_ON_ERROR
        );
    }

    public function findByBookingId($booking_id)
    {
        $is_valid_mongoid = $this->Utility->is_valid_mongoid($booking_id);
        if (empty($is_valid_mongoid)) {
            return json_encode(
                ['success' => false, 'message' => __('Invalid Booking Id')],
                JSON_PARTIAL_OUTPUT_ON_ERROR
            );
        }

        $params = ['conditions' => ['_id' => $booking_id]];

        $results = $this->Booking->find('first', $params);
        if (!empty($results)) {
            return json_encode($results, JSON_PARTIAL_OUTPUT_ON_ERROR);
        }

        return json_encode(['success' => false, 'message' => __('No Booking')], JSON_PARTIAL_OUTPUT_ON_ERROR);
    }


    /**
     * @throws InvalidBranchIdException
     * @throws InvalidMongoIdException
     * @throws UserOriginBranchIdException
     * @throws UnsuccessfulOperation
     * @throws UserNotFoundException
     * @throws TimeSlotPatternNotFoundException
     */
    public function process_payment(ProcessPaymentRequestV1 $req): JsonResponse
    {
        $allowedRoles = [
            UserType::ADMIN,
            UserType::SUPERADMIN,
            UserType::RECEPTIONIST,
            UserType::TRAINER,
            UserType::MEMBER,
        ];
        $this->JWT->validateOrFail($allowedRoles, null, $req->branchId());

        return response()->json(
            app()->make(PaymentProcessorService::class)
                ->withBranchId($req->branchId())
                ->withMemberId($req->memberId())
                ->withBookingId($req->bookingId())
                ->withPaymentMethod($req->paymentMethod())
                ->withSoldByUserId($req->soldByUserId())
                ->withOffSession($req->isOffSession())
                ->withPrice($req->price())
                ->withDiscounts($req->discounts())
                ->process()
        );
    }

    public function validateWaitingListSubFeature($member): array
    {
        $feature = $this->Branch->getBranchFeatures($member['User']['branch_id'], 'booking');

        return ($feature) ? ['success' => true] : [
            'success' => false,
            'message' => 'This branch does not support waiting list feature',
            'message_code' => 'BRANCH_DOES_NOT_SUPPORT_WAITING_LIST',
        ];
    }

    private function validateEventWaitingList(string $branchId, string $eventId, int $guestBookings): array
    {
        $feature = $this->Branch->getBranchFeatures($branchId, 'booking');
        $totalAllowed = $feature['Branch']['features']['booking']['waiting_list'] ?? 0;
        $totalWaiting = $this->bookingsRepository->countAllWaitingIncludingGuests($eventId);

        return ($totalAllowed > $totalWaiting + $guestBookings) ? ['success' => true] : [
            'success' => false,
            'message' => 'The Waiting list is full.',
            'message_code' => 'WAITING_LIST_IS_FULL',
        ];
    }

    public function validateMemberAlreadyBooked($member, $event): array
    {
        $current_booking = $this->Booking->findByEventIdAndUserId($event['Event']['_id'], $member['User']['_id']);

        return (empty($current_booking) || $current_booking['Booking']['status'] !== $this->Booking->get_status(
            'booked'
        )) ? ['success' => true] : [
            'success' => false,
            'message' => 'You are already booked for this class.',
            'message_code' => 'YOU_ALREADY_BOOKED_FOR_THIS_CLASS',
        ];
    }

    public function sendBookingCourseEmail($booking): bool
    {
        $course = $this->Course->findByIdAndBranchId($booking['course_id'], $booking['branch_id']);
        $member = $this->User->getMemberById($booking['user_id']);

        if (isset($member['User']['_id'], $course['Course']['_id'])) {
            $branch_name = $this->Branch->getNameById($course['Course']['branch_id']);

            $email_array = [];

            $default_trainers = $course['Course']['trainers'];
            $trainers = [];

            foreach ($course['Course']['schedule'] as $schedule) {
                if ($schedule['id'] == $booking['session_id']) {
                    $course['Course']['start_date'] = $this->Course->formatDate($schedule['start_date'], 'D jS \of M');
                    $course['Course']['end_date'] = $this->Course->formatDate($schedule['end_date'], 'D jS \of M');
                    $trainers = $schedule['trainers'];
                    break;
                }
            }

            if (!empty($trainers)) {
                foreach ($trainers as $trainer) {
                    $trainer = $this->User->getTrainerById($trainer['id']);
                    $email_array[] = $trainer['User']['email'];
                }
            } else {
                foreach ($default_trainers as $trainer) {
                    $trainerID = is_array($trainer) ? $trainer['id'] : $trainer;
                    $trainer = $this->User->getTrainerById($trainerID);
                    $email_array[] = $trainer['User']['email'];
                }
            }

            $admins = $this->User->find(
                'all',
                [
                    'conditions' => [
                        'type' => ['$in' => [UserType::ADMIN, UserType::SUPERADMIN]],
                        'active' => true,
                        'branch_id' => $course['Course']['branch_id'],
                    ],
                ]
            );
            $env = $this->User->get_current_environment();

            foreach ($admins as $admin) {
                $email_array[] = $admin['User']['email'];
            }

            try {
                /** @var CakeEmail $Email */
                $Email = app()->make(CakeEmail::class);
                $Email->config('email_for_booking');
                $Email->emailFormat('html');

                if ('BOOKED' === $booking['status']) {
                    $Email->subject('New Booking - ' . $branch_name);
                } elseif ('CANCELED' === $booking['status']) {
                    $Email->subject('Booking Cancelled - ' . $branch_name);
                }
                $Email->template('course_booking');
                $Email->cc($email_array);
                $Email->viewVars(
                    [
                        'booking' => $booking,
                        'course' => $course['Course'],
                        'member' => $member['User'],
                        'logoUrl' => sprintf('%s/%s/glofox/glofox-logo-horizontal.png', CdnProvider::getUrl(), $env),
                    ]
                );

                $Email->send();
            } catch (Exception $e) {
                CakeLog::write('error', $e->getMessage());
            }
        }

        return true;
    }

    /**
     * @throws UnsuccessfulOperation
     */
    public function bookCourse($courseId, $sessionId, $userId = null)
    {
        $isPost = $this->request->is('post');
        $validCourseId = $this->Utility->is_valid_mongoid($courseId);
        $requestData = isset($this->request->data['guest_bookings']) ? $this->request->data : json_decode(
            $this->request->input(),
            true,
            512,
            JSON_PARTIAL_OUTPUT_ON_ERROR
        );

        if ($userId) {
            $validUserId = $this->Utility->is_valid_mongoid($userId);

            if (empty($validUserId)) {
                return json_encode(
                    [
                        'success' => false,
                        'message' => __('Invalid User Id'),
                    ],
                    JSON_PARTIAL_OUTPUT_ON_ERROR
                );
            }
            $member = $this->User->findById($userId);
        } else {
            $sessionUser = $this->JWT->getUser();
            $allowedRoles = ['member'];
            $namespace = $sessionUser['namespace'] ?? '';
            $branchId = $sessionUser['branch_id'] ?? '';
            $hasAccess = $this->JWT->validate($allowedRoles, $namespace, $branchId);

            if (!$hasAccess['success']) {
                return json_encode($hasAccess, JSON_PARTIAL_OUTPUT_ON_ERROR);
            }

            $member = $this->User->findById($sessionUser['_id']);
        }
        $validUserStatus = $member['User']['active'];
        $guestBookings = $requestData['guest_bookings'] ?? 0;
        $offSession = (bool)($requestData['off_session'] ?? true);
        $payGym = (bool)($requestData['pay_gym'] ?? false);

        if (!$isPost) {
            return json_encode(
                [
                    'success' => false,
                    'message' => __('Invalid request, must be post'),
                ],
                JSON_PARTIAL_OUTPUT_ON_ERROR
            );
        }

        if (empty($validCourseId)) {
            return json_encode(
                [
                    'success' => false,
                    'message' => __('Invalid course id'),
                ],
                JSON_PARTIAL_OUTPUT_ON_ERROR
            );
        }

        if (empty($sessionId)) {
            return json_encode(
                [
                    'success' => false,
                    'message' => __('Invalid course session id'),
                ],
                JSON_PARTIAL_OUTPUT_ON_ERROR
            );
        }

        if (empty($member)) {
            return json_encode(
                [
                    'success' => false,
                    'message' => __('Member Not Found'),
                ],
                JSON_PARTIAL_OUTPUT_ON_ERROR
            );
        }

        if (!$validUserStatus) {
            return json_encode(
                [
                    'success' => false,
                    'message' => __('Your account is not active.'),
                ],
                JSON_PARTIAL_OUTPUT_ON_ERROR
            );
        }

        $price = null;
        $paymentMethod = PaymentMethods::CREDIT_CARD;

        return $this->processCourseBooking(
            $courseId,
            $sessionId,
            $member,
            $price,
            $paymentMethod,
            $guestBookings,
            $payGym,
            $isAdmin = false,
            $offSession
        );
    }

    /**
     * @throws UnsuccessfulOperation
     */
    public function bookCourseAdmin($course_id, $session_id, $user_id, $price = null, $payment_method = 'credit_card')
    {
        $user = $this->JWT->getUser();
        $allowed_roles = ['admin', 'superadmin', 'receptionist', 'trainer'];
        $namespace = $user['namespace'] ?? '';
        $branch_id = $user['branch_id'] ?? '';
        $has_access = $this->JWT->validate($allowed_roles, $namespace, $branch_id);

        if (!$has_access['success']) {
            return json_encode($has_access, JSON_PARTIAL_OUTPUT_ON_ERROR);
        }

        $is_post = $this->request->is('post');
        $member = $this->User->findById($user_id);
        $valid_course_id = $this->Utility->is_valid_mongoid($course_id);
        $valid_user_id = $this->Utility->is_valid_mongoid($user_id);
        $valid_user_status = $member['User']['active'];
        $request_data = (isset($this->request->data['guest_bookings'])) ?
            $this->request->data :
            json_decode($this->request->input(), true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        $guest_bookings = $request_data['guest_bookings'] ?? 0;
        $pay_at_gym = $request_data['pay_gym'] ?? false;
        $off_session = $request_data['off_session'] ?? true;

        if (!$is_post) {
            return json_encode(
                ['success' => false, 'message' => __('Invalid request, must be post')],
                JSON_PARTIAL_OUTPUT_ON_ERROR
            );
        }
        if (empty($valid_course_id)) {
            return json_encode(
                ['success' => false, 'message' => __('Invalid course id')],
                JSON_PARTIAL_OUTPUT_ON_ERROR
            );
        }
        if (empty($session_id)) {
            return json_encode(
                ['success' => false, 'message' => __('Invalid course session id')],
                JSON_PARTIAL_OUTPUT_ON_ERROR
            );
        }
        if (empty($valid_user_id)) {
            return json_encode(['success' => false, 'message' => __('Invalid user id')], JSON_PARTIAL_OUTPUT_ON_ERROR);
        }
        if (empty($member)) {
            return json_encode(['success' => false, 'message' => __('Member Not Found')], JSON_PARTIAL_OUTPUT_ON_ERROR);
        }
        if (!$valid_user_status) {
            return json_encode(
                ['success' => false, 'message' => __('Your account is not active.')],
                JSON_PARTIAL_OUTPUT_ON_ERROR
            );
        }

        return $this->processCourseBooking(
            $course_id,
            $session_id,
            $member,
            $price,
            $payment_method,
            $guest_bookings,
            $pay_at_gym,
            true,
            $off_session
        );
    }

    /**
     * @param $courseId
     * @param $sessionId
     * @param $member
     * @param null $price
     * @param string $paymentMethod
     * @param int $guestBookings
     * @param bool $payGym
     * @param bool $isAdmin
     * @param bool $offSession
     *
     * @return string
     *
     * @throws UnsuccessfulOperation
     * @throws Exception
     */
    public function processCourseBooking(
        $courseId,
        $sessionId,
        $member,
        $price = null,
        $paymentMethod = PaymentMethods::CREDIT_CARD,
        $guestBookings = 0,
        $payGym = false,
        $isAdmin = false,
        $offSession = true
    ) {
        $paymentMethod ??= PaymentMethods::CREDIT_CARD;

        $course = $this->Course->findById($courseId);
        if (empty($course)) {
            return json_encode(
                ['success' => false, 'message' => __('Course was not found')],
                JSON_PARTIAL_OUTPUT_ON_ERROR
            );
        }

        $schedule = $this->Course->findScheduleInCourseById($sessionId, $course['Course']);
        if (empty($schedule)) {
            return json_encode(
                ['success' => false, 'message' => __('Invalid course session id')],
                JSON_PARTIAL_OUTPUT_ON_ERROR
            );
        }

        $memberId = $member['User']['_id'];
        $lock = new Resource($memberId, Context::forCourse($sessionId ? $courseId . '-' . $sessionId : $courseId));
        $isLocked = $this->userLocker->lock($lock);
        if (!$isLocked) {
            return json_encode(
                [
                    'success' => false,
                    'message' => 'A transaction is already in progress',
                    'message_code' => 'A_TRANSACTION_IS_ALREADY_IN_PROGRESS',
                ],
                JSON_PARTIAL_OUTPUT_ON_ERROR
            );
        }

        try {
            $branchId = $course['Course']['branch_id'];
            $charge = [];

            /** @var Models\Branch $branch */
            $branch = $this->branchesRepository
                ->addCriteria(new Id($branchId))
                ->firstOrFail();

            if (!$branch->features()->areCoursesEnabled()) {
                throw new Exception('Course bookings are currently disabled');
            }

            if ($this->Booking->alreadyBooked($schedule, $member)) {
                return json_encode(
                    ['success' => false, 'message' => __('The user is already booked in this course')],
                    JSON_PARTIAL_OUTPUT_ON_ERROR
                );
            }

            $membership = $member['User']['membership'] ?? null;
            $hasActiveMembership = ($membership === null || $membership['type'] === MembershipType::NUM_CLASSES) ?
                false :
                $this->userHasMembershipValidator->execute($member);
            if (!$hasActiveMembership) {
                $membership = ['type' => MembershipType::PAYG];
            }

            $price = !isset($price) ? $this->Course->priceFor($membership, $course) : (float)$price;
            $course['Course']['default_price'] = $price;
            $bookings = $this->Booking->findByCourseAndSession($course['Course']['_id'], $schedule['id']);

            if ($this->Course->isFull($course, $schedule, $bookings, $guestBookings)) {
                return json_encode(
                    ['success' => false, 'message' => __('This course is already full')],
                    JSON_PARTIAL_OUTPUT_ON_ERROR
                );
            }

            $allowedTypes = $course['Course']['allowed_member_types'];

            if (is_array($allowedTypes) && !empty($allowedTypes) && 'FREE' !== (string)$course['Course']['pricing']) {
                $allowed = false;
                // under the course context, a member with just a credit pack is considered a PAYG member
                $isPaygOrCreditPack = $this->User->isPayg($member) ||
                    $member['User']['membership']['type'] === MembershipType::NUM_CLASSES;
                $membershipId = (string)($member['User']['membership']['_id'] ?? '');

                foreach ($allowedTypes as $rule) {
                    $allowedByPayg = $isPaygOrCreditPack
                        && isset($rule['type'])
                        && 'payg' === (string)$rule['type'];

                    $allowedByMembership = !$isPaygOrCreditPack
                        && isset($rule['membership_id'])
                        && (string)$rule['membership_id'] === $membershipId;

                    $allowedByType = !$isPaygOrCreditPack
                        && isset($rule['type'])
                        && 'member' === (string)$rule['type']
                        && !isset($rule['membership_id']);

                    if ($allowedByPayg || $allowedByMembership || $allowedByType) {
                        $allowed = true;
                        break;
                    }
                }

                if (!$allowed) {
                    if ('2.0' === $this->Booking->API) {
                        throw new UnsuccessfulOperation('Your membership is not valid for this course');
                    }

                    return json_encode([
                        'success' => false,
                        'message' => __('Your membership is not valid for this course'),
                    ], JSON_PARTIAL_OUTPUT_ON_ERROR);
                }
            }

            if (false === $payGym) {
                $totalBookings = $guestBookings + 1;
                $paymentMethod ??= PaymentMethods::CREDIT_CARD;
                $price = $course['Course']['default_price'];
                if (!empty($price) && $price > 0) {
                    $price = $this->Booking->calculateTotal($price, $totalBookings, 0, $isAdmin);
                }

                $startTime = microtime(true);

                $paymentExecution = app()->make(PaymentExecutorService::class)
                    ->withPaymentMethod(PaymentMethods::byValue($paymentMethod))
                    ->withMember(User::make($member['User']))
                    ->withDiscounts($this->getPayload()['discounts'] ?? [])
                    ->withSoldByUserId($this->getPayload()['sold_by_user_id'] ?? null)
                    ->withDescription('Book for Course ' . $course['Course']['name'])
                    ->withExternalId($course['Course']['_id'])
                    ->withModelName(ServiceType::COURSES)
                    ->withModelId($sessionId)
                    ->withPrice($price)
                    ->withNumberOfBookingsToPay($totalBookings)
                    ->withGuestBookings($guestBookings)
                    ->withOffSession((bool) filter_var($offSession, FILTER_VALIDATE_BOOLEAN))
                    ->execute();

                $endTime = microtime(true);
                $paymentExecutionTime = round($endTime - $startTime, 4);
                $this->trackPaymentTimeIntoHoneyComb(
                    'execute-payment-courses',
                    null,
                    $memberId,
                    null,
                    $branchId,
                    $paymentMethod,
                    $courseId,
                    $price,
                    $paymentExecutionTime,
                    $paymentExecution
                );

                if (!$paymentExecution['success']) {
                    return json_encode(
                        ['success' => false, 'message' => $paymentExecution['message']],
                        JSON_PARTIAL_OUTPUT_ON_ERROR
                    );
                }
                $charge = $paymentExecution['charge'] ?? [];

                if (!empty($charge) && !empty($charge['StripeCharge'])) {
                    event()->emit(ChargeWasUpdated::class, [Charge::make($charge['StripeCharge'])]);
                }
            }

            $booking = $this->Booking->createCourseBooking(
                $course,
                $schedule,
                $member,
                $charge,
                $guestBookings,
                $payGym
            );

            if (empty($booking)) {
                return json_encode([
                    'success' => false,
                    'message' => __('This course could not be booked. Please contact the club'),
                ], JSON_PARTIAL_OUTPUT_ON_ERROR);
            }
            $user = User::make($member['User']);

            if (!$user->membership()->isCreditOnly()) {
                $this->User->updateMembershipDatesBasedOnBooking($member, $booking);
            }

            $this->sendBookingCourseEmail($booking['Booking']);

            $bookingModel = Booking::make($booking['Booking']);
            $this->bookingPublisher->sendCourseBookingFinishedEvent(
                new CourseBookingFinishedEventMeta([
                    'location_id' =>  $booking['Booking']['branch_id'],
                ]),
                new CourseBookingFinishedEventPayload([
                    'id' => $bookingModel->id(),
                    'user_id' => $bookingModel->userId(),
                    'course_id' => $bookingModel->courseId(),
                    'status' => $bookingModel->status()->getValue(),
                    'date_start' => $bookingModel->startDate()->toDateTimeString(),
                    'date_finish' => $bookingModel->finishDate()->toDateTimeString(),
                    'guest_bookings' => $bookingModel->guestBookings(),
                    'paid' => $bookingModel->get('paid'),
                    'schedule' => $this->sanitiseScheduleForCourseBookingFinishedEvent($schedule),
                    'created' => $bookingModel->created()->toDateTimeString(),
                    'modified' => $bookingModel->modified()->toDateTimeString(),
                ])
            );

            return json_encode(
                ['success' => true, 'Booking' => $booking['Booking']],
                JSON_PARTIAL_OUTPUT_ON_ERROR
            );
        } finally {
            $this->userLocker->unlock();
        }
    }

    /**
     * @throws \Glofox\Exception
     * @throws UnsuccessfulOperation
     */
    public function cancelBookingForCourse($booking_id, $user_id)
    {
        $this->response->type('json');
        $this->autoRender = false;

        $user = $this->Session->read('User');
        $allowed_roles = ['admin', 'superadmin', 'receptionist'];
        $this->JWT->validateOrFail($allowed_roles, $user['User']['namespace']);

        $booking = $this->Booking->findByIdSetFields($booking_id, ['user_id'], false);

        if (!$booking) {
            throw new BookingNotFoundException();
        }

        return $this->cancel($booking_id, $booking['user_id']);
    }

    public function findByCourseAndSession($course_id, $session_id)
    {
        $this->response->type('json');
        $this->autoRender = false;
        $user_list = [];

        $user = $this->JWT->getUser();
        $allowed_roles = ['admin', 'superadmin', 'trainer'];
        $namespace = $user['namespace'] ?? '';
        $branch_id = $user['branch_id'] ?? '';
        $has_access = $this->JWT->validate($allowed_roles, $namespace, $branch_id);
        if (!$has_access['success']) {
            return json_encode($has_access, JSON_PARTIAL_OUTPUT_ON_ERROR);
        }

        $bookings = $this->Booking->findByCourseAndSession($course_id, $session_id);
        foreach ($bookings as $booking) {
            $user_list[] = $booking['Booking']['user_id'];
        }

        $users = $this->User->findBasicInfoFor($user_list);
        foreach ($bookings as &$booking) {
            $booking['Booking']['user'] = $users[$booking['Booking']['user_id']];
        }

        return json_encode($bookings, JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    public function getAllMembersBookedForACourse($branch_id, $course_id)
    {
        $is_valid_mongoid = $this->Utility->is_valid_mongoid($branch_id);

        if (empty($is_valid_mongoid)) {
            return json_encode(
                [
                    'success' => false,
                    'message' => __('Invalid Branch Id'),
                ],
                JSON_PARTIAL_OUTPUT_ON_ERROR
            );
        }

        $is_valid_mongoid = $this->Utility->is_valid_mongoid($course_id);

        if (empty($is_valid_mongoid)) {
            return json_encode(
                [
                    'success' => false,
                    'message' => __('Invalid Course Id'),
                ],
                JSON_PARTIAL_OUTPUT_ON_ERROR
            );
        }

        return json_encode(
            $this->Booking->findByBranchIdAndCourseId($branch_id, $course_id),
            JSON_PARTIAL_OUTPUT_ON_ERROR
        );
    }

    public function getTotalBookingsForAnEvent($branch_id, $event_id)
    {
        $event = $this->Event->findById($event_id);

        return $event['Event']['total_bookings'] ?? 0;
    }

    /**
     * @throws AuthenticationException
     */
    public function view(?string $id): JsonResponse
    {
        if (!empty($id)) {
            return response()->json($this->Booking->findById($id));
        }

        $user = $this->JWT->parseToken();
        $dateFormat = $this->Event->get_date_format('date');
        $timezone = $this->Branch->getTimeZone($user['branch_id']);
        $today = $this->Branch->getCurrentDate($dateFormat, $timezone);
        $weeksToDisplay = $this->Branch->findWeeksToDisplay($id, 'events');
        $endDate = $this->Branch->addNumberOfWeeks($today, $weeksToDisplay, $dateFormat);

        $params = new GetBookingsByDateRangeParams(
            $user['branch_id'],
            new Carbon($today),
            new Carbon($endDate)
        );

        $useCase = app()->make(GetBookingsByDateRange::class);
        $result = $useCase->execute($params);

        return response()->json($result);
    }

    /**
     * @throws AuthenticationException
     * @throws Exception
     */
    public function attendance($model = null, $id = null)
    {
        $user = $this->JWT->parseToken();

        if (isset($model) && 'sessions' === $model) { //For specific course session as member
            $identifier = is_numeric($id) ? (int)$id : $id;
            $conditions = [
                'namespace' => $user['namespace'],
                'model' => 'courses',
                'session_id' => $identifier,
            ];
        } elseif (isset($model) && 'events' === $model) { //For any other model as member
            $conditions = [
                'branch_id' => $user['branch_id'],
                'event_id' => $id,
            ];
        } elseif (isset($model)) { //For any other model as member
            $conditions = [
                'branch_id' => $user['branch_id'],
                'model' => $model,
                'model_id' => $id,
            ];
        } elseif (UserType::TRAINER === $user['type']) { //As trainer all bookings
            $conditions = [
                'branch_id' => $user['branch_id'],
                'model_id' => $user['_id'],
            ];
        } elseif (in_array($user['User']['type'], [UserType::ADMIN, UserType::SUPERADMIN], true)) {
            $conditions = ['branch_id' => $user['branch_id']];
        } else {
            throw new Exception('Could not resolve the attendance data that is being requested');
        }

        $bookings = $this->Booking->find('all', ['conditions' => $conditions]);

        $users = [];
        if (!empty($bookings)) {
            $ids = array_map([$this, 'mapUids'], $bookings);
            $ucond = ['branch_id' => $user['branch_id'], '_id' => ['$in' => $ids]];
            $users = $this->User->find('all', ['conditions' => $ucond]);
        }

        return json_encode(
            ['success' => true, 'users' => $users, 'bookings' => $bookings],
            JSON_PARTIAL_OUTPUT_ON_ERROR
        );
    }

    public function mapUids($booking)
    {
        return $booking['Booking']['user_id'];
    }

    public function upsert(Request $request): JsonResponse
    {
        $booking = Booking::make(
            $request->data()->toArray()
        );

        $snsMessageId = $this->bookingUpdateRequestEventPublisher->publish($booking);

        return response()->json([
            'success' => true,
            'message' => 'Booking update request has been accepted for processing.',
            'message_code' => 'BOOKING_UPDATE_REQUEST_ACCEPTED',
            'data' => ['messageId' => $snsMessageId],
        ]);
    }

    /**
     * @throws AuthenticationException
     * @throws Exception
     */
    public function remove($id)
    {
        $this->JWT->parseToken();
        $item = $this->Booking->findById($id);
        $item['Booking']['active'] = false;

        return json_encode($this->Booking->save($item), JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    /**
     * @throws AuthenticationException
     */
    public function funnel()
    {
        $user = $this->JWT->parseToken();
        $params = json_decode(@file_get_contents('php://input'), true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        if (!isset($params['$and'])) {
            $params['$and'] = [];
        }
        $params['$and'][] = ['branch_id' => $user['branch_id']];
        array_walk_recursive($params, [$this, 'encodeRegex']);
        $uids = $this->Booking->getMongoDb()->selectCollection('bookings')->distinct('user_id', $params);

        return json_encode($uids, JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    public function bookingsDateRange(GetBookingsByDateRangeRequest $req): JsonResponse
    {
        $params = new GetBookingsByDateRangeParams(
            $req->branchId(),
            $req->startDate(),
            $req->finishDate()
        );

        $useCase = app()->make(GetBookingsByDateRange::class);
        $result = $useCase->execute($params);

        return response()->json($result);
    }

    public function listBookingStatus()
    {
        return json_encode($this->Booking->listBookingStatus(), JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    /**
     * @throws UnsuccessfulOperation
     * @throws InvalidMongoIdException
     */
    public function getAllByBranchIdAndUserId($branch_id, $user_id, $limit = 100, $offset = 0, $load_title = false)
    {
        if ($limit > self::MAX_BOOKINGS_LIMIT_BY_BRANCH_AND_USER_ID) {
            $limit = self::MAX_BOOKINGS_LIMIT_BY_BRANCH_AND_USER_ID;
        }

        $filters = json_decode(@file_get_contents('php://input'), true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        $load_title = filter_var($load_title, FILTER_VALIDATE_BOOLEAN);
        $is_valid_mongoid = $this->Utility->is_valid_mongoid($branch_id);

        /** @var Models\Branch $branch */
        $branch = $this->branchesRepository
            ->addCriteria(new Id($branch_id))
            ->firstOrFail();

        $user = Auth::user();
        $tokenUserId = $user->id();
        $tokenBranchId = $user->currentBranchId();

        if ($tokenUserId !== $user_id || $tokenBranchId !== $branch_id) {
            $allowedRoles = [
                UserType::ADMIN,
                UserType::SUPERADMIN,
                UserType::RECEPTIONIST,
                UserType::TRAINER,
            ];

            $this->JWT->validateOrFail($allowedRoles, $branch->namespace(), $branch->id());
        }

        if (empty($is_valid_mongoid)) {
            return json_encode(
                ['success' => false, 'message' => __('Invalid Branch Id')],
                JSON_PARTIAL_OUTPUT_ON_ERROR
            );
        }

        $is_valid_mongoid = $this->Utility->is_valid_mongoid($user_id);
        if (empty($is_valid_mongoid)) {
            return json_encode(['success' => false, 'message' => __('Invalid User Id')], JSON_PARTIAL_OUTPUT_ON_ERROR);
        }

        $bookings = $this->Booking->findByBranchIdAndUserId(
            $branch_id,
            $user_id,
            $limit,
            $offset,
            $filters,
            $load_title
        );
        $hasMore = (is_countable($bookings) && count($bookings) === (int)$limit);

        $bookings = $this->Booking->setWaitingListPosition($bookings);

        foreach ($bookings as $k => &$booking) {
            if (isset($booking['Booking']['attended']) && !is_bool($booking['Booking']['attended'])) {
                $booking['Booking']['attended'] = (bool)$booking['Booking']['attended'];
            }

            try {
                if (isset($booking['Booking']['event_id'])) {
                    $eventId = $booking['Booking']['event_id'];
                    $event = $this->eventsRepository
                        ->addCriteria(new Id($eventId))
                        ->skipCallbacks()
                        ->firstOrFail();

                    /** @var DateTime $start */
                    $start = $event['time_start']->toDateTime();

                    /** @var DateTime $finish */
                    $finish = $event['time_finish']->toDateTime();

                    $booking['Booking']['time_start'] = Carbon::instance($start)->format('Y-m-d H:i:s');
                    $booking['Booking']['time_finish'] = Carbon::instance($finish)->format('Y-m-d H:i:s');
                    continue;
                }

                if (isset($booking['Booking']['time_slot_id'])) {
                    $timeslotId = $booking['Booking']['time_slot_id'];
                    $timeslot = $this->timeSlotsRepository
                        ->addCriteria(new Id($timeslotId))
                        ->skipCallbacks()
                        ->firstOrFail();

                    /** @var DateTime $start */
                    $start = $timeslot['time_start']->toDateTime();

                    /** @var DateTime $finish */
                    $finish = $timeslot['time_finish']->toDateTime();

                    $booking['Booking']['time_start'] = Carbon::instance($start)->format('Y-m-d H:i:s');
                    $booking['Booking']['time_finish'] = Carbon::instance($finish)->format('Y-m-d H:i:s');
                    continue;
                }
            } catch (Exception $e) {
                unset($bookings[$k]);
            }
        }
        unset($booking);

        $bookings = array_values($bookings);

        return response()->json([
            'success' => true,
            'bookings' => $bookings,
            'has_more' => $hasMore,
        ]);
    }

    public function hasBookings(HasBookingsRequest $request): bool
    {
        return $this->Booking->branchHasBookings($request->getBranchId());
    }

    public function getAllByModelIdentifierAndModelId(GetBookingsByModelAndModelIdRequest $req): JsonResponse
    {
        if (
            !$req->modelId() || !in_array(
                $req->modelIdentifier(),
                [ModelType::PROGRAM, ModelType::CLASSES, ModelType::COURSE, ModelType::APPOINTMENT],
                true
            )
        ) {
            return response()->json([
                'success' => false,
                'message' => 'MODEL_OR_MODEL_ID_CANNOT_BE_NULL',
            ]);
        }

        $user = Auth::user();

        $params = new GetBookingsByModelAndModelIdParams(
            $req->modelIdentifier(),
            $req->modelId(),
            $user->branchId(),
            $this->Branch,
            $this->Booking
        );

        $useCase = app()->make(GetBookingsByModelAndModelId::class);
        $result = $useCase->execute($params);

        return response()->json($result);
    }

    /**
     * @throws \Glofox\Exception
     * @throws InvalidBranchIdException
     * @throws InvalidMongoIdException
     * @throws PricingStrategyNotFoundException
     * @throws TimeSlotNotFoundException
     * @throws ReflectionException
     * @throws \Glofox\NotFoundException
     * @throws Exception
     */
    private function processTimeSlotBooking(
        $timeSlotId,
        User $member,
        $autoGenerated = false,
        $payGym = false,
        $isWebAdmin = false,
        $paymentMethod = PaymentMethods::CREDIT_CARD,
        $price = null,
        $offSession = true,
        $batchId = null,
        bool $isChargeable = true
    ) {
        $lock = new Resource($member->id(), Context::forAppointment($timeSlotId));
        if (!$this->userLocker->lock($lock)) {
            return [
                'success' => false,
                'message' => 'A transaction is already in progress for this user',
            ];
        }
        $isLocked = false;

        $branch = $member->branch();
        $branchId = $branch->id();
        $timezone = $branch->timezone();

        $this->loadModel('UserCredit');

        try {
            $timeSlot = $this->timeSlotsRepository->getById($timeSlotId);
        } catch (TimeSlotNotFoundException $e) {
            $this->userLocker->unlock();
            return ['success' => false, 'message' => __('Appointment Not Found')];
        }

        if (!$timeSlot->active() || $timeSlot->isClosed()) {
            $this->userLocker->unlock();
            return ['success' => false, 'message' => __('This Appointment is no longer available.')];
        }

        if ($timeSlot->isFullyBooked()) {
            $this->userLocker->unlock();
            return [
                'success' => false,
                'message' => __(
                    $timeSlot->size() > 1
                        ? 'This Appointment is fully booked.'
                        : 'This Appointment has already been booked.'
                )
            ];
        }

        try {
            if ($timeSlot->isAppointmentsModel()) {
                $timeSlotPattern = $this->timeSlotPatternsRepository->getById($timeSlot->modelId());
            } else {
                $timeSlotPattern = $this->timeSlotPatternsRepository->getByModelAndModelId(
                    $timeSlot->model(),
                    $timeSlot->modelId()
                );
            }
        } catch (TimeSlotPatternNotFoundException $e) {
            $this->userLocker->unlock();
            return ['success' => false, 'message' => __('Time Slot Pattern Not Found')];
        }

        $entity = [];
        if ($timeSlot->isFacilitiesModel()) {
            try {
                $facility = $this->facilitiesRepository->findFacilityById($timeSlot->modelId());
            } catch (FacilityNotFoundException $e) {
                $this->userLocker->unlock();
                return ['success' => false, 'message' => __('Facility Not Found')];
            }
            $entity['name'] = $facility->name();
            $entity['categories'] = $facility->categories();
            unset($facility);
        } elseif ($timeSlot->isAppointmentsModel()) {
            try {
                $appointment = $this->timeSlotPatternsRepository->getById($timeSlot->modelId());
            } catch (TimeSlotPatternNotFoundException $e) {
                $this->userLocker->unlock();
                return ['success' => false, 'message' => __('Appointment Not Found')];
            }
            $timeStart = Carbon::createFromTimeString($timeSlot->timeStartAsString());
            $lockEventId = $this->bookingAppointmentLocker->getEventIdFromAppointment(
                $appointment->id(),
                $timeSlot->staffId(),
                $timeStart->timestamp
            );
            $branchId = (string)$timeSlot->branchId();

            try {
                $this->bookingAppointmentLocker->lock($branchId, $lockEventId);
                $isLocked = true;
            } catch (EventBookingLockerException $e) {
                $this->userLocker->unlock();
                return ['success' => false, 'message' => $e->getMessage()];
            }

            try {
                $bookingAppointmentValidator = app()->make(CanBookWithAppointmentSlotValidator::class);
                $bookingAppointmentValidator->validate(
                    $member->id(),
                    $timeSlot->staffId(),
                    $timeSlot->branchId(),
                    $timeStart,
                    Carbon::createFromTimeString($timeSlot->timeFinishAsString()),
                    $timeSlot->id()
                );
            } catch (ValidationException $e) {
                $this->userLocker->unlock();
                $this->bookingAppointmentLocker->unlock($branchId, $lockEventId);
                return ['success' => false, 'message' => $e->validationErrors()];
            }

            $entity['name'] = $this->formatAppointmentBookingEventName($timeSlot->staffId(), $appointment->name());
            $entity['categories'] = $appointment->categories()->toArray();
            $entity['allowed_member_types'] = $appointment->allowedMemberTypes()->toArray();
        } else {
            try {
                $trainer = $this->usersRepository->getById($timeSlot->modelId());
            } catch (UserNotFoundException $e) {
                $this->userLocker->unlock();
                return ['success' => false, 'message' => __('Trainer Not Found')];
            }
            $entity['name'] = $trainer->fullName();
            $entity['categories'] = $trainer->categories();
            $entity['allowed_member_types'] = $trainer->allowedMemberTypes();
            unset($trainer);
        }

        $userMembership = $member->membership();

        if (false === $autoGenerated) {
            $validateEventHasPassed = $this->validateEventHasPassed(
                $timeSlot->timeStartAsString(),
                $timezone->getName()
            );
            if (!$validateEventHasPassed['success'] && !$isWebAdmin) {
                $this->userLocker->unlock();
                if ($isLocked) {
                    $this->bookingAppointmentLocker->unlock($branchId, $lockEventId);
                }
                return $validateEventHasPassed;
            }

            if (empty($batchId)) {
                $validateBookingWindowOpen = $this->validateTimeSlotBookingWindowOpen($timeSlot, $timezone->getName());
                if (!$validateBookingWindowOpen['success'] && !$isWebAdmin) {
                    $this->userLocker->unlock();
                    if ($isLocked) {
                        $this->bookingAppointmentLocker->unlock($branchId, $lockEventId);
                    }
                    return $validateBookingWindowOpen;
                }

                $validateBookingWindowClose = $this->validateTimeSlotBookingWindowClose(
                    $timeSlot,
                    $timezone->getName()
                );
                if (!$validateBookingWindowClose['success'] && !$isWebAdmin) {
                    $this->userLocker->unlock();
                    if ($isLocked) {
                        $this->bookingAppointmentLocker->unlock($branchId, $lockEventId);
                    }
                    return $validateBookingWindowClose;
                }
            }

            $validateStartDate = $this->Membership->validateStartDate(
                $member->toLegacy(),
                $this->Booking->formatDate($timeSlot->timeStartAsString(), 'date')
            );
            if (!$validateStartDate['success']) {
                $this->userLocker->unlock();
                if ($isLocked) {
                    $this->bookingAppointmentLocker->unlock($branchId, $lockEventId);
                }
                return $validateStartDate;
            }

            $membershipValidationResult = false;

            /** @var \Booking $bookingAsModel */
            $bookingAsModel = app()->make(\Booking::class);

            /**
             * Sometimes we receive acURL error 7: Failed to connect to services.plat.service.private port
             * 8083 after 7 ms: Connection refused (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) error
             * here, I added the try catch to unlock the key if required.
             */
            try {
                $strikeValidationResult = $this->strikeValidationService->validate($branch, $member);
            } catch (Exception $e) {
                $this->userLocker->unlock();
                if ($isLocked) {
                    $this->bookingAppointmentLocker->unlock($branchId, $lockEventId);
                }
                throw $e;
            }

            if (!$strikeValidationResult['success']) {
                $this->userLocker->unlock();
                if ($isLocked) {
                    $this->bookingAppointmentLocker->unlock($branchId, $lockEventId);
                }
                return $strikeValidationResult;
            }

            $addonsValidationResult = $this->addonsEligibilityService->validate($member, $timeSlot);

            if (!$addonsValidationResult) {
                $this->logger->info(
                    sprintf(
                        'User %s has no eligible addons for timeslot %s, starting membership check',
                        $member->id(),
                        $timeSlot->id()
                    )
                );

                $userMembershipValidRequest = new UserMembershipValidParameters();
                $userMembershipValidRequest->setBranch($branch->toLegacy());
                $userMembershipValidRequest->setMember($member->toLegacy());
                $userMembershipValidRequest->setGuestBookings(0);
                $userMembershipValidRequest->setTimeslot($timeSlot);
                $userMembershipValidRequest->setBatchId($batchId);

                try {
                    $validateMembership = $bookingAsModel->userMembershipValid($userMembershipValidRequest);
                } catch (Exception $e) {
                    $this->userLocker->unlock();
                    if ($isLocked) {
                        $this->bookingAppointmentLocker->unlock($branchId, $lockEventId);
                    }
                    throw $e;
                }

                $membershipValidationResult = (bool)$validateMembership['success'];
            }

            if (!$addonsValidationResult && !$membershipValidationResult) {
                $this->userLocker->unlock();
                if ($isLocked) {
                    $this->bookingAppointmentLocker->unlock($branchId, $lockEventId);
                }
                return $validateMembership;
            }

            $userCredit = $this->UserCredit->findCreditForBooking(
                $timeSlot->branchId(),
                $member->id(),
                $timeSlot->dateAsString(),
                $timeSlot->model()->getValue(),
                $timeSlot->modelId(),
                $entity['categories']
            );

            $isValidMembership = (new TimeSlotMembershipValidator())->validate(
                $timeSlot,
                $userMembership,
                $branch,
                $batchId
            );

            $hasAnActiveSubscription = $this->User->has_an_active_subscription($member->toLegacy());
            $asPayg = (!$isValidMembership && !$hasAnActiveSubscription);

            $defaultPrice = $timeSlotPattern->defaultPrice();

            // @see https://glofox.atlassian.net/browse/DASH2-2839
            // If there's not allowed_member_types in this timeslot pattern, it means the user is trying to
            // book into a Single Appointment, an appointment that wasn't created by the Schedule.
            if ($timeSlotPattern->allowedMemberTypes()->isEmpty()) {
                $defaultPrice = null;
                $timeSlotPattern->offsetSet('allowed_member_types', $entity['allowed_member_types']);
            }

            $validateAllowedGroups = $this->validateAllowedGroups(
                $member->toLegacy(),
                $timeSlotPattern->branchId(),
                $timeSlotPattern->allowedMemberTypes()->toArray(),
                $asPayg,
                $defaultPrice,
                $timeSlotPattern->modelId(),
                $batchId
            );

            if (!$validateAllowedGroups['success'] && !$userCredit && !$addonsValidationResult) {
                $this->userLocker->unlock();
                if ($isLocked) {
                    $this->bookingAppointmentLocker->unlock($branchId, $lockEventId);
                }
                return $validateAllowedGroups;
            }

            $pricingResolverFactory = new PricingResolverFactory();
            $pricingResolver = $pricingResolverFactory->create(
                $timeSlot->pricingType(),
                $member,
                $branch,
                1
            );

            if (empty($batchId)) {
                $pricing = $pricingResolver->resolve($timeSlot);
                $price = $isWebAdmin ? $price : $pricing->get('price');
            }

            $legacyCredits = ($timeSlot instanceof CreditPayable) ?
                $pricingResolver->availableCredits($timeSlot) :
                0;

            if (
                !$addonsValidationResult && empty($batchId) && (float)$price === 0.0 && $legacyCredits < 1 &&
                $userMembership->isTimeAndCreditBased()
            ) {
                $this->userLocker->unlock();
                if ($isLocked) {
                    $this->bookingAppointmentLocker->unlock($branchId, $lockEventId);
                }
                throw new Exception('YOU_HAVE_NO_CREDITS_LEFT');
            }

            // Only validate payment if the "Pay at the Gym" option is not set and stripe is enabled
            // Also check if the member has a credit for this time slot, in that case we won't charge
            // @see https://glofox.atlassian.net/browse/DASH2-3726
            $charge = [];
            if ($isChargeable && !$payGym && !$userCredit && !$addonsValidationResult) {
                $paymentMethod ??= PaymentMethods::CREDIT_CARD;

                $startTime = microtime(true);

                $paymentExecution = app()->make(PaymentExecutorService::class)
                    ->withPaymentMethod(PaymentMethods::byValue($paymentMethod))
                    ->withMember($member)
                    ->withDiscounts($this->getPayload()['discounts'] ?? [])
                    ->withSoldByUserId($this->getPayload()['sold_by_user_id'] ?? null)
                    ->withDescription($timeSlot->name())
                    ->withExternalId($timeSlot->modelId())
                    ->withModelName($timeSlot->model()->getValue())
                    ->withModelId($timeSlot->modelId())
                    ->withPrice($price)
                    ->withOffSession((bool) filter_var($offSession, FILTER_VALIDATE_BOOLEAN))
                    ->execute();

                $endTime = microtime(true);

                $paymentExecutionTime = round($endTime - $startTime, 4);
                $this->trackPaymentTimeIntoHoneyComb(
                    'execute-payment-timeslots',
                    $batchId,
                    $member->id(),
                    null,
                    $branchId,
                    $paymentMethod,
                    $timeSlot->modelId(),
                    $price,
                    $paymentExecutionTime,
                    $paymentExecution
                );

                if (!$paymentExecution['success']) {
                    $this->userLocker->unlock();
                    if ($isLocked) {
                        $this->bookingAppointmentLocker->unlock($branchId, $lockEventId);
                    }
                    return $paymentExecution;
                }

                $charge = $paymentExecution['charge'] ?? [];
            }
        }

        $bookingStatus = $this->Booking->get_status('booked');
        $timeSlotId = $timeSlot->id();

        $bookings = $this->bookingsRepository->getByTimeSlotIdAndStatus($timeSlotId, $bookingStatus);
        if (!empty($bookings) && $timeSlot->size() === 1) {
            $this->userLocker->unlock();
            if ($isLocked) {
                $this->bookingAppointmentLocker->unlock($branchId, $lockEventId);
            }
            return ['success' => false, 'message' => __('That time slot have been already booked')];
        }

        $requestResolver = app()->make(OriginByRequestResolver::class);
        $source = $requestResolver->resolveByPlatform(Router::getRequest());

        $createTimeSlotBookingParams = new CreateTimeSlotBookingParameters(
            $this->branchesRepository,
            $this->bookingsRepository,
            $timeSlot,
            $member->toLegacy(),
            $charge,
            $bookingStatus,
            $entity['name'],
            $payGym,
            (string)$source->getValue(),
            (string)$paymentMethod,
            $userMembership->type(),
            (int)$price,
            $isChargeable,
            $batchId
        );

        if ($isChargeable && empty($batchId) && !empty($addonsValidationResult)) {
            $createTimeSlotBookingParams->setAddon($addonsValidationResult);
        }

        if ($isChargeable && empty($batchId) && empty($addonsValidationResult) && !empty($userCredit)) {
            $createTimeSlotBookingParams->setCredit($userCredit);
        }

        $booking = $this->Booking->createTimeSlotBooking($createTimeSlotBookingParams);

        if ($booking) {
            $payWithAddons = isset($addonsValidationResult) && $addonsValidationResult;
            if ($isChargeable && empty($batchId) && $payWithAddons && isset($pricing) && $pricing->credits() > 0) {
                $addonService = app()->make(AddonServiceInterface::class);
                $addonService->consumeAddonCredits(
                    $addonsValidationResult->serviceId(),
                    $pricing->credits(),
                    $booking['Booking']['_id'],
                    $timeSlot->branchId(),
                    $timeSlot->timeStart()
                );
            }

            $payWithCredits = isset($userCredit) && $userCredit && !$payWithAddons;
            if ($isChargeable && empty($batchId) && $payWithCredits) {
                $this->UserCredit->spendUserCredit($userCredit['UserCredit']['_id'], $booking['Booking']['_id']);

                $bookingId = $booking['Booking']['_id'];
                $startsOn = MembershipPlanStartsOn::FIRST_BOOKING_DATE();

                $creditsUsed = $this->creditsRepository->getByBookingIdStartOnAndStartDate(
                    $bookingId,
                    $startsOn,
                    null
                );
                if (!empty($creditsUsed)) {
                    foreach ($creditsUsed as $credit) {
                        /** @var MongoDate $startDate */
                        $startDate = $booking['Booking']['time_start'];
                        $startDate = Carbon::createFromTimestampUTC($startDate->sec);
                        $this->UserCredit->updateCreditPackDates($credit, $startDate);
                    }
                }
            }

            $legacyMember = $member->toLegacy();
            $this->User->updateMembershipDatesBasedOnBooking($legacyMember, $booking);

            if ($timeSlot->isFacilitiesModel() || $timeSlot->isAppointmentsModel() || $timeSlot->isUsersModel()) {
                $timeSlotArray['TimeSlot'] = $timeSlot->toArray();
                if ($timeSlot->isAppointmentsModel()) {
                    $trainerId = $timeSlot->staffId();
                }

                if ($timeSlot->isUsersModel()) {
                    $trainerId = $timeSlot->modelId();
                }

                if ($timeSlot->isAppointmentsModel() || $timeSlot->isUsersModel()) {
                    try {
                        $trainer = $this->usersRepository->getById($trainerId);
                    } catch (UserNotFoundException $e) {
                        $this->userLocker->unlock();
                        if ($isLocked) {
                            $this->bookingAppointmentLocker->unlock($branchId, $lockEventId);
                        }
                        return ['success' => false, 'message' => __('Trainer Not Found')];
                    }
                }

                if ($timeSlot->isFacilitiesModel()) {
                    try {
                        $facility = $this->facilitiesRepository->findFacilityById($timeSlot->modelId());
                    } catch (FacilityNotFoundException $e) {
                        $this->userLocker->unlock();
                        if ($isLocked) {
                            $this->bookingAppointmentLocker->unlock($branchId, $lockEventId);
                        }
                        return ['success' => false, 'message' => __('Facility Not Found')];
                    }
                }

                $timeSlotArray['TimeSlot']['date'] = DateTime::createFromFormat('Y-m-d', $timeSlot->dateAsString())
                    ->format('l jS \of F');

                $timeSlotArray['TimeSlot']['time_start'] = $this->Booking->formatDate(
                    $timeSlot->timeStartAsString(),
                    'h:i:s A'
                );

                $timeSlotArray['TimeSlot']['time_finish'] = $this->Booking->formatDate(
                    $timeSlot->timeFinishAsString(),
                    'h:i:s A'
                );
                $adminEmail = $branch->email();

                try {
                    $emailTemplate = 'facility_booking';
                    $to = $adminEmail;
                    if ($timeSlot->isAppointmentsModel() || $timeSlot->isUsersModel()) {
                        $to = isset($trainer) ? $trainer->email() : $to;
                        $emailTemplate = 'trainer_booking';
                    }
                    if ($timeSlot->isFacilitiesModel()) {
                        $bookingType = isset($facility) ? $facility->name() : '';
                    } else {
                        $bookingType = isset($trainer) ? $trainer->fullName() : '';
                    }

                    /** @var CakeEmail $Email */
                    $Email = app()->make(CakeEmail::class);
                    $Email->config('sendgrid');
                    $Email->to($to);
                    $logoUrl = sprintf(
                        '%s/%s/glofox/glofox-logo-horizontal.png',
                        CdnProvider::getUrl(),
                        $this->User->get_current_environment()
                    );
                    $Email->viewVars(
                        [
                            'booking_type' => $bookingType,
                            'logoUrl' => $logoUrl,
                            'time_slot' => $timeSlotArray,
                            'member' => $this->User->getMemberById($booking['Booking']['user_id']),
                        ]
                    );
                    $Email->subject('New Booking in Glofox');
                    $Email->template($emailTemplate);
                    $Email->emailFormat('html');
                    $Email->send();
                } catch (Exception $e) {
                    CakeLog::write('error', $e->getMessage());
                }
            }

            $bookingModel = Booking::make($booking['Booking']);
            $this->bookingPublisher->sendBookingCreatedEvent(
                new BookingCreatedEventMeta([]),
                new BookingCreatedEventPayload([
                    'id' => $bookingModel->id(),
                    'namespace' => $bookingModel->namespace(),
                    'branch_id' => $bookingModel->branchId(),
                    'user_id' => $bookingModel->userId(),
                    'model' => $bookingModel->model(),
                    'model_id' => $bookingModel->timeSlotId(),
                    'model_name' => $bookingModel->eventName(),
                    'status' => $bookingModel->status()->getValue(),
                    'time_start' => $bookingModel->startingTime()->toDateTimeString(),
                    'time_finish' => $bookingModel->finishTime()->toDateTimeString(),
                    'guest_bookings' => 0,
                    'paid' => $booking['Booking']['paid'] ?? null,
                    'attended' => $bookingModel->attended(),
                    'created' => $bookingModel->created()->toDateTimeString(),
                    'modified' => $bookingModel->modified()->toDateTimeString(),
                ])
            );

            $this->userLocker->unlock();
            if ($isLocked) {
                $this->bookingAppointmentLocker->unlock($branchId, $lockEventId);
            }
            return ['success' => true, 'Booking' => $booking['Booking']];
        }

        $this->userLocker->unlock();
        if ($isLocked) {
            $this->bookingAppointmentLocker->unlock($branchId, $lockEventId);
        }

        return ['success' => false, 'message' => $this->Booking->get_latest_error()];
    }

    private function validateMemberBookStatus($member, $event): array
    {
        $current_booking = $this->Booking->findByEventIdAndUserId($event['Event']['_id'], $member['User']['_id']);

        return (empty($current_booking) ||
            $current_booking['Booking']['status'] !== $this->Booking->get_status('waiting')) ?
            ['success' => true] :
            [
                'success' => false,
                'message' => 'You are already in the waiting list for this class.',
                'message_code' => 'YOU_ARE_ALREADY_IN_THE_WAITING_LIST',
            ];
    }

    private function trackPaymentTimeIntoHoneyComb(
        string $trackName,
        ?string $batchId,
        string $memberId,
        ?string $programId = null,
        string $branchId,
        string $paymentMethod,
        string $modelId,
        ?float $price,
        float $validatePaymentTime,
        array $validatePayment
    ): void {
        $honeycombTracker = app()->make(HoneycombTracker::class);
        $data = [
            'service.name' => 'api',
            'name' => $trackName,
            'batchId' => $batchId,
            'memberId' => $memberId,
            'programId' => $programId,
            'branchId' => $branchId,
            'paymentMethod' => $paymentMethod,
            'modelId' => $modelId,
            'price' => $price,
            'roundedServerResponseTimeInSeconds' => $validatePaymentTime,
            'success' => $validatePayment['success'],
            'message' => $validatePayment['message'] ?? '',
        ];
        $honeycombTracker->track($data);
    }

    private function trackBookingTimeIntoHoneyComb(
        string $trackName,
        string $memberId,
        string $branchId,
        string $programId,
        float $bookingExecutionSecondsTime
    ): void {
        $honeycombTracker = app()->make(HoneycombTracker::class);
        $data = [
            'service.name' => 'api',
            'name' => $trackName,
            'memberId' => $memberId,
            'branchId' => $branchId,
            'programId' => $programId,
            'roundedServerResponseTimeInSeconds' => $bookingExecutionSecondsTime,
        ];
        $honeycombTracker->track($data);
    }

    private function validateEventHasNoSpotLeft($event, $guestBookings): array
    {
        $totalBookings = $this->bookingsRepository->countAllBookingsIncludingGuests($event['Event']['_id']);

        $spotsLeft = $event['Event']['size'] - $totalBookings - $guestBookings;

        return $spotsLeft <= 0 ? ['success' => true] : [
            'success' => false,
            'message' => 'The class still has ' . $spotsLeft . ' spots open for booking.',
            'message_code' => 'THE_CLASS_STILL_HAS_SPOTS_LEFT',
        ];
    }

    private function validateEventIsActive($event): array
    {
        return $event['Event']['active'] ? ['success' => true] : [
            'success' => false,
            'message' => 'The Event has been cancelled.',
            'message_code' => 'EVENT_HAS_BEEN_CANCELLED',
        ];
    }

    private function validateEventHasPassed($time_start, $timezone = 'UTC'): array
    {
        $event_start_time = $time_start;
        $current_timestamp = $this->Booking->getCurrentDate('datetime', $timezone);
        $is_valid = (strtotime($event_start_time) > strtotime($current_timestamp));

        if ($is_valid) {
            return ['success' => true];
        }

        return [
            'success' => false,
            'message' => 'The event has already passed.',
            'event_start_time' => strtotime($event_start_time),
            'current_time' => date('Y-m-d H:i:s', strtotime($current_timestamp)),
            'message_code' => 'EVENT_HAS_PASSED',
        ];
    }

    private function validateTimeSlotBookingWindowOpen(TimeSlot $time_slot, $timezone = 'UTC'): array
    {
        $time_slot_start_time = $time_slot->timeStartAsString();
        $current_timestamp = $this->Booking->getCurrentTime($timezone);

        $booking_open_window = $this->Branch->findTimeSlotOpenBookingWindow(
            $time_slot->branchId(),
            $time_slot->model()->getValue()
        );
        if (empty($booking_open_window)) {
            return ['success' => true];
        }

        $opening_time = $this->Booking->subtractNumberOfHours($time_slot_start_time, $booking_open_window, 'datetime');
        $is_valid = (strtotime($opening_time) < $current_timestamp);
        $opening_time = $this->Booking->formatDate($opening_time, "l jS \of F Y \a\\t h:i A");
        if ($is_valid) {
            return ['success' => true];
        }

        return ['success' => false, 'message' => 'You cannot book for this time slot until ' . $opening_time];
    }

    private function validateTimeSlotBookingWindowClose(TimeSlot $time_slot, $timezone = 'UTC'): array
    {
        $time_slot_start_time = $time_slot->timeStartAsString();
        $current_timestamp = $this->Booking->getCurrentTime($timezone);

        $booking_close_window = $this->Branch->findTimeSlotCloseBookingWindow(
            $time_slot->branchId(),
            $time_slot->model()->getValue()
        );
        if (empty($booking_close_window)) {
            return ['success' => true];
        }

        $closing_time = $this->Booking->subtractNumberOfHours($time_slot_start_time, $booking_close_window, 'datetime');
        $is_valid = ($current_timestamp < strtotime($closing_time));
        $closing_time = $this->Booking->formatDate($closing_time, "l jS \of F Y \a\\t h:i A", $timezone);
        if ($is_valid) {
            return ['success' => true];
        }

        return ['success' => false, 'message' => 'You cannot book for this time slot after ' . $closing_time];
    }

    private function validateBookingWindowOpen($event, $timezone = 'UTC'): array
    {
        $event_start_time = $event['Event']['time_start'];
        $current_timestamp = $this->Booking->getCurrentTime($timezone);

        $booking_open_window = $this->Branch->findEventBookingWindow($event['Event']['branch_id']);
        if (empty($booking_open_window)) {
            return ['success' => true];
        }

        $opening_time = $this->Booking->subtractNumberOfHours($event_start_time, $booking_open_window, 'datetime');

        $is_valid = (strtotime($opening_time) < $current_timestamp);
        $opening_time = $this->Booking->formatDate($opening_time, "l jS \of F Y \a\\t h:i A", $timezone);
        if ($is_valid) {
            return ['success' => true];
        }

        return [
            'success' => false,
            'message' => 'You cannot book for this event until ' . $opening_time,
            'message_code' => 'YOU_CANNOT_BOOK_UNTIL',
            'message_data' => $opening_time,
        ];
    }

    private function validateAllowedGroups(
        $member,
        $branchId,
        $allowed_member_types,
        $as_payg = false,
        $default_price = null,
        $modelId = null,
        ?string $batchId = null
    ): array {
        if (!empty($batchId)) {
            return ['success' => true];
        }

        return $this->User->validateAllowedGroups(
            $member,
            $branchId,
            $allowed_member_types,
            $as_payg,
            $default_price,
            $modelId
        );
    }

    /**
     * @throws UnsuccessfulOperation
     */
    private function normaliseBookingResponse(string $userId, string $branchId, array $payload, $response)
    {
        $response = is_string($response) ? json_decode($response, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR) : $response;
        if (isset($response['success']) && !$response['success']) {
            $messageCode = $response['message_code'] ?? $response['message'];
            $messageData = $response['message_data'] ?? [];

            if ('YOU_CANNOT_BOOK_UNTIL' === $messageCode && is_string($messageData)) {
                $messageData = ['date' => $messageData];
            }

            $exception = new UnsuccessfulOperation();
            $exception->setMessage($response['message']);
            $exception->setMessageCode($messageCode);
            $exception->setMessageData($messageData);

            event()->emit(BookingHasFailed::class, [$userId, $branchId, $payload, $messageCode]);
            throw $exception;
        }

        return $response;
    }

    /**
     * @throws EventNotFoundException
     * @throws InvalidMongoIdException
     * @throws InvalidEventException
     * @throws BranchNotFoundException
     * @throws \Glofox\Exception
     * @throws PaymentProviderNotFoundException
     * @throws BookingValidatorException
     * @throws PaymentMethodNotAvailableForMembers
     * @throws JsonException
     * @throws \Glofox\NotFoundException
     * @throws UnsuccessfulOperation
     */
    private function bookEventAgnostic(array $payload)
    {
        $sessionUser = $this->JWT->getUser();
        $reqUserId = !empty($payload['user_id']) ? $payload['user_id'] : $sessionUser['_id'];

        if (!empty($payload['join_waiting_list'])) {
            return $this->waitlist(
                $payload['model_id'],
                $reqUserId,
                $payload['guest_bookings'],
                [],
                $payload['branch_id']
            );
        }

        $isChargeable = !isset($payload['charge']) || filter_var($payload['charge'], FILTER_VALIDATE_BOOLEAN);

        if (in_array($sessionUser['type'], UserType::ADMINISTRATORS, true)) {
            if (!isset($payload['user_id'])) {
                return json_encode([
                    'success' => false,
                    'message' => 'Cannot create a booking without member id',
                    'message_code' => 'CANNOT_CREATE_A_BOOKING_WITHOUT_MEMBER_ID',
                ], JSON_THROW_ON_ERROR);
            }

            $this->request->data = array_merge($this->request->data, ['event_id' => $payload['model_id']]);

            $request = AddWebAdminRequest::create('', 'POST', [
                'user_id' => $payload['user_id'],
                'branch_id' => $payload['branch_id'],
                'event_id' => $payload['event_id'] ?? $payload['model_id'],
                'sold_by_user_id' => $payload['sold_by_user_id'] ?? '',
                'payment_method' => $payload['payment_method'] ?? null,
                'guest_bookings' => $payload['guest_bookings'] ?? 0,
                'price' => $payload['price'] ?? null,
                'pay_gym' => $payload['pay_gym'] ?? false,
                'force_overbook' => $payload['force_overbook'] ?? false,
                'is_free' => $payload['is_free'] ?? false,
                'off_session' => $payload['off_session'] ?? true,
                'discounts' => $payload['discounts'] ?? [],
                'metadata' => $payload['metadata'] ?? [],
                'batch_id' => $payload['batch_id'] ?? null,
                'charge' => $isChargeable,
            ]);

            return $this->add_web_admin($request);
        }

        $request = AddRequest::create('', 'POST', [
            'user_id' => $sessionUser['_id'],
            'branch_id' => $sessionUser['branch_id'],
            'namespace' => $sessionUser['namespace'],
            'event_id' => $payload['model_id'],
            'payment_method' => PaymentMethods::CREDIT_CARD,
            'guest_bookings' => $payload['guest_bookings'] ?? 0,
            'pay_gym' => $payload['pay_gym'] ?? false,
            'off_session' => $payload['off_session'] ?? true,
            'metadata' => $payload['metadata'] ?? [],
            'batch_id' => null,
            'charge' => $isChargeable,
        ]);

        return $this->add($request);
    }

    /**
     * @throws Exception
     */
    private function bookTimeSlotAgnostic(array $payload, BookingRequestParameters $bookingRequestParams): string
    {
        $bookTimeSlotParams = new BookTimeSlotParams(
            $payload,
            $bookingRequestParams,
            $this->JWT->getUser(),
            $this->Booking->API,
            $this
        );

        return (new BookTimeSlot($bookTimeSlotParams))->execute();
    }

    /**
     * @throws UnsuccessfulOperation
     */
    private function bookCourseAgnostic(array $payload)
    {
        $loggedUser = $this->JWT->getUser();
        $reqUser = !empty($payload['user_id']) ? $payload['user_id'] : $loggedUser['_id'];

        if (in_array($loggedUser['type'], UserType::ADMINISTRATORS, true)) {
            $price = $payload['price'] ?? null;
            $paymentMethod = $payload['payment_method'] ?? null;

            return $this->bookCourseAdmin(
                $payload['model_id'],
                $payload['schedule_id'],
                $reqUser,
                $price,
                $paymentMethod
            );
        }

        return $this->bookCourse($payload['model_id'], $payload['schedule_id']);
    }

    /**
     * @throws InvalidMongoIdException
     */
    private function formatAppointmentBookingEventName(string $staffId, string $appointmentName): string
    {
        $staff = $this->usersRepository
            ->addCriteria(new Id($staffId))
            ->firstOrFail();

        return $appointmentName . ' - ' . $staff->fullName();
    }

    /**
     * @throws InvalidBranchIdException
     */
    private function getBookingRequest(
        Event $event,
        User $member,
        int $guestBookings,
        float $price,
        bool $payLater,
        bool $hasPaymentsEnabled,
        string $paidWithCredits,
        bool $forceOverbook,
        array $metadata,
        array $sessionUser
    ): ?BookingRequestModel {
        $bookingRequestCreator = app()->make(BookingRequestCreatorService::class);
        $data = new CreateBookingRequest(
            $event->branch()->timezone(),
            $sessionUser,
            $metadata,
            $guestBookings,
            $price,
            $payLater,
            $hasPaymentsEnabled,
            $paidWithCredits,
            $forceOverbook,
            false
        );

        return $bookingRequestCreator->execute($member, $event, $data);
    }

    /**
     * @throws EventBookingLockerException
     */
    private function lockEvent(string $branchId, string $eventId): void
    {
        $this->bookingEventLocker->lock($branchId, $eventId);
    }

    private function unlockEvent(string $branchId, string $eventId): void
    {
        $this->bookingEventLocker->unlock($branchId, $eventId);
    }

    private function sanitiseScheduleForCourseBookingFinishedEvent(array $schedule): array
    {
        $scheduleDays = [];
        foreach ($schedule['days'] as $day) {
            $days = [];
            foreach ($day['days'] as $value) {
                $days[] = [
                    'id' => $value['id'],
                    'label' => $value['label'],
                ];
            }

            $scheduleDays[] = [
                'day' => $days[0],
                'time_start' => Carbon::parse($day['start_time'])->toTimeString(),
                'time_end' => Carbon::parse($day['end_time'])->toTimeString(),
            ];
        }

        return [
            'id' => (string)$schedule['id'],
            'name' => $schedule['label'],
            'days' => $scheduleDays,
        ];
    }
}
