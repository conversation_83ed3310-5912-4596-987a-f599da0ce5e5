<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Payments\Transformers;

use Carbon\Carbon;
use Glofox\Domain\Bookings\Models\BookableEntityType;
use Glofox\Domain\Charges\Models\ChargeMetadataService;
use Glofox\Domain\Memberships\Models\Addon;
use Glofox\Domain\Memberships\Services\AddonServiceInterface;
use Glofox\Payments\Entities\Transaction\Models\Transaction;
use Glofox\Payments\Transformers\TransactionToChargeTransformer;
use Illuminate\Support\Collection;

\App::import('Test/Case', 'GlofoxTestCase');

class TransactionToChargeTransformerTest extends \GlofoxTestCase
{
    public function testChargeWithNoLineItems(): void
    {
        $transaction = $this->buildTransaction();
        $metadata = $this->buildMetadata();
        $eventPayloadPSP = $this->buildEventPayloadPSPWithNoLineItems();

        $transformer = app()->make(TransactionToChargeTransformer::class);
        $charge = $transformer->execute(
            $transaction,
            $metadata,
            $eventPayloadPSP,
            'mock-description'
        );

        self::assertEquals($transaction->id(), $charge->get('id'));
        self::assertEquals('PAID', $charge->transactionStatus());
        self::assertEquals($metadata, $charge->metadata());
        self::assertEquals('60dad15f0ea516215d56ae48', $charge->invoiceId());
        self::assertEquals('mock-evt', $charge->eventId());
    }

    public function testChargeWithLineItems(): void
    {
        $transaction = $this->buildTransaction();
        $metadata = $this->buildMetadata();
        $eventPayloadPSP = $this->buildEventPayloadPSPWithLineItems();

        $serviceId = 'mock-serviceId';
        $serviceDefinitionId = 'mock-serviceDefinitionId';
        $branchId = '49a7011a05c677b9a916612a';

        $addonServiceInterface = \Mockery::mock(AddonServiceInterface::class);
        $addonServiceInterface
            ->shouldReceive('getAddon')
            ->withArgs(
                function (string $serviceIdSentToService, string $branchIdIdSentToService) use ($serviceId, $branchId) {
                    self::assertEquals($serviceId, $serviceIdSentToService);
                    self::assertEquals($branchId, $branchIdIdSentToService);

                    return true;
                })
            ->andReturn(new Addon(
                $serviceId,
                $serviceDefinitionId,
                'mock-serviceDefinitionPlanId',
                'Tanning',
                'Recurring',
                BookableEntityType::FACILITY,
                Carbon::yesterday()->getTimestamp(),
                false
            ))
            ->once();
        app()->instance(AddonServiceInterface::class, $addonServiceInterface);

        $transformer = app()->make(TransactionToChargeTransformer::class);
        $charge = $transformer->execute(
            $transaction,
            $metadata,
            $eventPayloadPSP,
            'mock-description'
        );

        $chargeMetadata = $charge->metadata();
        self::assertArrayHasKey('services', $chargeMetadata);

        /** @var ChargeMetadataService $chargeMetadataService */
        $chargeMetadataService = $chargeMetadata['services'][0];
        self::assertEquals($serviceId, $chargeMetadataService['service_id']);
        self::assertEquals($serviceDefinitionId, $chargeMetadataService['service_definition_id']);

        app()->forgetInstance(AddonServiceInterface::class);
    }

    private function buildTransaction(): Transaction
    {
        $transaction = new Transaction();
        $transaction->setId('mocked-psp-transaction-id')
            ->setStatus('SUCCESS')
            ->setType('CHARGE')
            ->setAmount(11)
            ->setCurrency('EUR')
            ->setCustomerAccountId('customer_123')
            ->setCreatedAt(Carbon::now()->getTimestamp());

        return $transaction;
    }

    private function buildMetadata(): array
    {
        return [
            'namespace' => 'glofox',
            'branch_id' => '49a7011a05c677b9a916612a',
            'glofox_event' => 'subscription_payment',
            'stripe_subscription_id' => 'sub_00X',
            'user_id' => '5d6826348d8540851fe60662',
            'membership_id' => '54107c1cd7b6ddc3a98b4577',
            'user_name' => 'Member To renew',
            'environment' => 'local',
            'payment_method' => 'credit_card',
            'plan_code' => '*************',
        ];
    }

    private function buildEventPayloadPSPWithNoLineItems(): Collection
    {
        $eventPayloadPSP = new Collection([
            'customer_account_id' => 'cusAcctID',
            'id' => 'mock-evt',
            'data' => [
                'object' => [
                    'lines' => [
                        'type' => 'invoice.payment_succeeded',
                        'data' => [
                            0 => [
                                'id' => 'sub_testSubscriptionPayment',
                                'type' => 'subscription',
                                'period' => [
                                    'start' => 1_624_953_183,
                                    'end' => 1_625_011_200,
                                ],
                            ],
                        ],
                    ],
                    'id' => '60dad15f0ea516215d56ae48',
                ],
            ],
        ]);

        return $eventPayloadPSP;
    }

    private function buildEventPayloadPSPWithLineItems(): Collection
    {
        $eventPayloadPSP = new Collection([
            'customer_account_id' => 'cusAcctID',
            'id' => 'mock-evt',
            'data' => [
                'object' => [
                    'lines' => [
                        'type' => 'invoice.payment_succeeded',
                        'data' => [
                            0 => [
                                'id' => 'sub_testSubscriptionPayment',
                                'type' => 'subscription',
                                'period' => [
                                    'start' => 1_624_953_183,
                                    'end' => 1_625_011_200,
                                ],
                            ],
                        ],
                    ],
                    'id' => '60dad15f0ea516215d56ae48',
                ],
            ],
            'line_items' => [
                [
                    'service' => 'services',
                    'amount' => 11,
                    'external_reference' => 'mock-serviceId:mock-serviceDefinitionId',
                ],
            ],
        ]);

        return $eventPayloadPSP;
    }
}
