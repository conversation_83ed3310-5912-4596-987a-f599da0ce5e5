<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\Users\Formatters;

use Glofox\Domain\Users\Formatters\ConsentFormatter;

\App::import('Test/Case', 'GlofoxTestCase');

class ConsentFormatterTest extends \GlofoxTestCase
{
    public $fixtures = [];
    private ?ConsentFormatter $formatter = null;

    public function setUp(): void
    {
        parent::setUp();

        $this->formatter = new ConsentFormatter();
    }

    public function tearDown(): void
    {
        parent::tearDown();
    }

    public function test_remove_consent_field_from_single_user(): void
    {
        $data = [
            'User' => [
                'consent' => [
                    'email' => [
                        'active' => true,
                        'modified_at' => '2021-07-27T12:29:53+0000',
                        'modified_by_user_id' => 'test-user-id',
                        'modified_from_ip_address' => '127.0.0.1',
                        'message' => 'test-message',
                    ],
                    'sms' => [
                        'active' => false,
                        'modified_at' => '2021-07-27T12:29:53+0000',
                        'modified_by_user_id' => 'test-user-id',
                        'modified_from_ip_address' => '127.0.0.1',
                        'message' => 'test-message',
                    ],
                ],
            ],
        ];

        $formattedData = $this->formatter->format($data);

        $this->assertFalse(isset($formattedData['User']['consent']['email']['modified_from_ip_address']));
        $this->assertFalse(isset($formattedData['User']['consent']['email']['message']));
        $this->assertFalse(isset($formattedData['User']['consent']['sms']['modified_from_ip_address']));
        $this->assertFalse(isset($formattedData['User']['consent']['sms']['message']));
    }

    public function test_remove_consent_field_from_user_list(): void
    {
        $data = [
            'data' => [
                [
                    'User' => [
                        'consent' => [
                            'email' => [
                                'active' => true,
                                'modified_at' => '2021-07-27T12:29:53+0000',
                                'modified_by_user_id' => 'test-user-id',
                                'modified_from_ip_address' => '127.0.0.1',
                                'message' => 'test-message',
                            ],
                            'sms' => [
                                'active' => false,
                                'modified_at' => '2021-07-27T12:29:53+0000',
                                'modified_by_user_id' => 'test-user-id',
                                'modified_from_ip_address' => '127.0.0.1',
                                'message' => 'test-message',
                            ],
                        ],
                    ],
                ],
                [
                    'User' => [
                        'consent' => [
                            'email' => [
                                'active' => true,
                                'modified_at' => '2021-07-27T12:29:53+0000',
                                'modified_by_user_id' => 'test-user-id',
                                'modified_from_ip_address' => '127.0.0.1',
                                'message' => 'test-message',
                            ],
                            'sms' => [
                                'active' => false,
                                'modified_at' => '2021-07-27T12:29:53+0000',
                                'modified_by_user_id' => 'test-user-id',
                                'modified_from_ip_address' => '127.0.0.1',
                                'message' => 'test-message',
                            ],
                        ],
                    ],
                ],
            ],
        ];

        $formattedData = $this->formatter->format($data);

        foreach ($formattedData['data'] as $user) {
            $this->assertFalse(isset($user['User']['consent']['email']['modified_from_ip_address']));
            $this->assertFalse(isset($user['User']['consent']['email']['message']));
            $this->assertFalse(isset($user['User']['consent']['sms']['modified_from_ip_address']));
            $this->assertFalse(isset($user['User']['consent']['sms']['message']));
        }
    }
}
