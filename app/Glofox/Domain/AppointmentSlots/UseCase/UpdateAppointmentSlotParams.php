<?php

declare(strict_types=1);

namespace Glofox\Domain\AppointmentSlots\UseCase;

use Carbon\Carbon;

class UpdateAppointmentSlotParams
{
    private string $slotId;
    private string $locationId;
    private ?string $staffId;
    private Carbon $startTime;
    private ?bool $active;

    public function __construct(
        string $slotId,
        string $locationId,
        Carbon $startTime,
        ?string $staffId = null,
        ?bool $active = null
    ) {
        $this->slotId = $slotId;
        $this->locationId = $locationId;
        $this->startTime = $startTime;
        $this->staffId = $staffId;
        $this->active = $active;
    }

    public function slotId(): string
    {
        return $this->slotId;
    }

    public function locationId(): string
    {
        return $this->locationId;
    }


    public function staffId(): ?string
    {
        return $this->staffId;
    }


    public function startTime(): Carbon
    {
        return $this->startTime;
    }
}
