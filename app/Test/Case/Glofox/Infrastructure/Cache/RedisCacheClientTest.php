<?php

namespace CakeTestCases\Glofox\Infrastructure\Cache;

use Carbon\Carbon;
use Glofox\Infrastructure\Cache\CachePrefix;
use Glofox\Infrastructure\Cache\PredisClientFactory;
use Glofox\Infrastructure\Cache\RedisCacheClient;

\App::import('Test/Case', 'GlofoxTestCase');

class RedisCacheClientTest extends \GlofoxTestCase
{
    /** @var \Predis\Client */
    private $predis;

    private ?\Glofox\Infrastructure\Cache\RedisCacheClient $client = null;

    private ?\Glofox\Infrastructure\Cache\CachePrefix $prefix = null;

    public function setUp()
    {
        parent::setUp();

        $this->prefix = new CachePrefix(sprintf('[test][%s]', uniqid('', false)));

        /** @var PredisClientFactory $factory */
        $factory = app()->make(PredisClientFactory::class);
        $this->predis = $factory->createFromEnvironment();

        $this->client = new RedisCacheClient(
            $this->predis,
            $this->prefix
        );
    }

    public function test_it_counts_by_key(): void
    {
        $this->client->add('a', 1, 'a');
        $this->client->add('a', 1, 'aa');
        $this->client->add('a', 1, 'aaa');

        $count = $this->client->count('a');

        self::assertEquals(3, $count);
    }

    public function test_it_deletes_key(): void
    {
        $this->client->set('b', 'mockedValue');

        $key = sprintf('%s[%s]', $this->prefix->toString(), 'b');
        self::assertEquals('mockedValue', $this->predis->get($key));

        $this->client->delete('b');

        self::assertNull($this->predis->get($key));
    }

    public function test_it_sets_expiration(): void
    {
        $this->client->set('c', 'mockedValue');
        $this->client->expiresAt('c', Carbon::now()->addSeconds(3));

        self::assertEquals('mockedValue', $this->client->get('c'));

        sleep(4);

        self::assertNull($this->client->get('c'));
    }

    public function test_it_adds_new_key(): void
    {
        $this->client->set('d', 'mockedValue');
        self::assertEquals('mockedValue', $this->client->get('d'));
    }

    public function test_it_checks_wheter_key_exists(): void
    {
        $this->client->set('e', 'mockedValue');
        self::assertTrue($this->client->has('e'));
        self::assertFalse($this->client->has('i-dont-exist'));
    }

    public function test_it_stores_prefixed_keys(): void
    {
        $this->client->set('mockedKey', 'mockedValue');

        $key = sprintf('%s[%s]', $this->prefix->toString(), 'mockedKey');
        self::assertEquals('mockedValue', $this->predis->get($key));
    }

    public function test_it_gets_prefixed_keys(): void
    {
        $key = sprintf('%s[%s]', $this->prefix->toString(), 'mockedKey');
        $this->predis->set($key, 'mockedValue');

        self::assertEquals('mockedValue', $this->client->get('mockedKey'));
    }

    public function test_it_deletes_prefixed_keys(): void
    {
        $key = sprintf('%s[%s]', $this->prefix->toString(), 'mockedKey');
        $this->predis->set($key, 'mockedValue');

        self::assertEquals('mockedValue', $this->client->get('mockedKey'));
    }

    public function test_it_counts_prefixed_keys(): void
    {
        $key = sprintf('%s[%s]', $this->prefix->toString(), 'mockedKeyX');
        $this->predis->zadd($key, 1, 'mockedValue');
        $this->predis->zadd($key, 2, 'mockedValue2');

        self::assertEquals(2, $this->client->count('mockedKeyX'));
    }

    public function test_it_replaces_existing_key(): void
    {
        $this->client->set('mockedKeyD', 'mockedValue1');
        $this->client->set('mockedKeyD', 'mockedValue2');

        self::assertEquals('mockedValue2', $this->client->get('mockedKeyD'));
    }

    public function test_it_stores_keys_without_prefix(): void
    {
        $key = 'mockedKeyWithoutPrefix';

        $value1 = 'mockedValueWithoutPrefix1';
        $value2 = 'mockedValueWithoutPrefix2';
        $value3 = 'mockedValueWithoutPrefix3';

        $this->client->addWithoutPrefix($key, 1, $value1, null);
        $this->client->addWithoutPrefix($key, 2, $value2, null);
        $this->client->addWithoutPrefix($key, 3, $value3, null);

        $this->assertEquals(
            [$value1, $value2, $value3],
            $this->predis->zrange($key, 0, -1)
        );
    }

    public function test_it_counts_by_key_without_prefix(): void
    {
        $key = 'mockedKeyWithoutPrefix';

        $count = $this->client->countWithoutPrefix($key);
        self::assertEquals(3, $count);
    }

    public function test_it_deletes_keys_without_prefix(): void
    {
        $key = 'mockedKeyWithoutPrefix';

        $this->client->deleteWithoutPrefix($key);
        $this->assertEquals([], $this->predis->zrange($key, 0, -1));

        $count = $this->client->countWithoutPrefix($key);
        self::assertEquals(0, $count);
    }

    public function test_it_set_expiration_when_adding_keys_without_prefix(): void
    {
        $key = 'mockedKeyWithoutPrefixWithExpiration';
        $value = 'mockedValueWithoutPrefixWithExpiration';

        $expiration = Carbon::now()->addSeconds(3);
        $this->client->addWithoutPrefix($key, 1, $value, $expiration);

        $this->assertEquals(1, $this->client->countWithoutPrefix($key));

        sleep(4);

        $this->assertEquals(0, $this->client->countWithoutPrefix($key));
    }

    public function test_it_returns_multiple_values_for_a_key_without_prefix(): void
    {
        $key = 'mockedKeyWithoutPrefix';
        $mockedValues = ['mockedValue1', 'mockedValue2', 'mockedValue3'];

        $this->predis->zadd($key, 1, $mockedValues[0]);
        $this->predis->zadd($key, 2, $mockedValues[1]);
        $this->predis->zadd($key, 3, $mockedValues[2]);

        $this->assertEquals(
            $mockedValues,
            $this->client->getListWithoutPrefix($key)
        );
    }

    public function test_it_removes_one_value_from_a_key_without_prefix(): void
    {
        $key = 'mockedKeyWithoutPrefix';

        $this->client->deleteValueInListWithoutPrefix($key, 'mockedValue2');

        $this->assertEquals(
            ['mockedValue1', 'mockedValue3'],
            $this->predis->zrange($key, 0, -1)
        );

        $this->client->deleteWithoutPrefix($key);

        $this->assertEquals(
            [],
            $this->predis->zrange($key, 0, -1)
        );
    }
}
