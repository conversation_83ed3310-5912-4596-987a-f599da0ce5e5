<?php

declare(strict_types=1);

namespace Glofox\Domain\Reports\Enum;

use Glofox\Enumerator;

/**
 * @method static TEST()
 * @method static EXPIRED_AND_CANCELLED_MEMBERSHIPS()
 */
class EmbedReportId extends Enumerator
{
    public const TEST = 'report.branch-names';
    public const NEW_MEMBERS = 'report.new-members';
    public const EXPIRED_AND_CANCELLED_MEMBERSHIPS = 'report.expired-and-cancelled-memberships';
    public const WAIVER_AND_TERMS = 'report.waiver-and-terms';
    public const LEADS_AND_MEMBERS = 'report.leads-and-members';
    public const ACCOUNT_BALANCE = 'report.account-balance';
    public const MEMBERS = 'report.members';
    public const SALES = 'report.sales';
    public const FEES_AND_PAYOUTS = 'report.fees-and-payouts';
    public const ACCESS_INSIGHTS = 'report.access-insights';
    public const ACCESS_INSIGHTS_ALL_CLIENTS = 'report.access-insights-all-clients';
    public const IMPORTED_CHARGES = 'report.imported-charges';
    public const MEMBER_TRANSFERS = 'report.member-transfers';
    public const TRAINER_INSIGHTS = 'report.trainer-insights';
    public const NEW_LEADS = 'report.new-leads';
    public const VISITS_INSIGHTS = 'report.visits-insights';
}
