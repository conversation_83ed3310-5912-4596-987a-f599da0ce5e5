<?php

namespace Glofox\Authentication\Traits;

use Glofox\Domain\Integrators\Exceptions\CoexistingJwtAndApiTokenException;
use Glofox\Request;

trait SetsAuthorizationHeaders {

    private function setAuthorizationHeaders(Request $request, string $jwt)
    {
        $bearerToken = sprintf('Bearer %s', $jwt);

        $_SERVER['HTTP_AUTHORIZATION'] = $bearerToken;

        $request->headers->set('authorization', $bearerToken);

        app()->instance(Request::class, $request);
    }
}