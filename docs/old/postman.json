{"info": {"name": "Glofox Internal API", "_postman_id": "44af4988-af3b-8d21-5012-e0035dc49669", "description": "", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "2.1", "item": [{"name": "Users", "item": [{"name": "Search Members by E-mail (strict)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "http://localhost:8889/2.1/branches/{{branchId}}/memberships?page=1&filters[email]=<EMAIL>", "protocol": "http", "host": ["localhost"], "port": "8889", "path": ["2.1", "branches", "{{branchId}}", "memberships"], "query": [{"key": "page", "value": "1", "equals": true}, {"key": "filters[email]", "value": "<EMAIL>", "equals": true}]}}, "response": [{"id": "1b6a60c1-d128-4ba2-b6f9-cd1e04c60f7b", "name": "200 - Succesful Response", "originalRequest": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "http://localhost:8889/2.1/branches/{{branchId}}/memberships?page=1&filters[email]=<EMAIL>", "protocol": "http", "host": ["localhost"], "port": "8889", "path": ["2.1", "branches", "{{branchId}}", "memberships"], "query": [{"key": "page", "value": "1", "equals": true}, {"key": "filters[email]", "value": "<EMAIL>", "equals": true}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "_postman_previewtype": "text", "header": [{"key": "Connection", "value": "keep-alive", "name": "Connection", "description": "Options that are desired for the connection"}, {"key": "Content-Encoding", "value": "gzip", "name": "Content-Encoding", "description": "The type of encoding used on the data."}, {"key": "Content-Type", "value": "application/json; charset=UTF-8", "name": "Content-Type", "description": "The mime type of this content"}, {"key": "Date", "value": "<PERSON><PERSON>, 17 Jul 2018 14:03:17 GMT", "name": "Date", "description": "The date and time that the message was sent"}, {"key": "Server", "value": "nginx", "name": "Server", "description": "A name for the server"}, {"key": "Set-<PERSON><PERSON>", "value": "XDEBUG_SESSION=docker; expires=Tue, 17-Jul-2018 15:03:17 GMT; Max-Age=3600; path=/", "name": "Set-<PERSON><PERSON>", "description": "an HTTP cookie"}, {"key": "Set-<PERSON><PERSON>", "value": "XDEBUG_SESSION=docker; expires=Tue, 17-Jul-2018 15:03:17 GMT; Max-Age=3600; path=/", "name": "Set-<PERSON><PERSON>", "description": "an HTTP cookie"}, {"key": "Set-<PERSON><PERSON>", "value": "XDEBUG_SESSION=docker; expires=Tue, 17-Jul-2018 15:03:17 GMT; Max-Age=3600; path=/", "name": "Set-<PERSON><PERSON>", "description": "an HTTP cookie"}, {"key": "Set-<PERSON><PERSON>", "value": "XDEBUG_SESSION=docker; expires=Tue, 17-Jul-2018 15:03:17 GMT; Max-Age=3600; path=/", "name": "Set-<PERSON><PERSON>", "description": "an HTTP cookie"}, {"key": "Set-<PERSON><PERSON>", "value": "XDEBUG_SESSION=docker; expires=Tue, 17-Jul-2018 15:03:17 GMT; Max-Age=3600; path=/", "name": "Set-<PERSON><PERSON>", "description": "an HTTP cookie"}, {"key": "Set-<PERSON><PERSON>", "value": "XDEBUG_SESSION=docker; expires=Tue, 17-Jul-2018 15:03:17 GMT; Max-Age=3600; path=/", "name": "Set-<PERSON><PERSON>", "description": "an HTTP cookie"}, {"key": "Transfer-Encoding", "value": "chunked", "name": "Transfer-Encoding", "description": "The form of encoding used to safely transfer the entity to the user. Currently defined methods are: chunked, compress, deflate, gzip, identity."}, {"key": "Vary", "value": "Accept-Encoding", "name": "Vary", "description": "Tells downstream proxies how to match future request headers to decide whether the cached response can be used rather than requesting a fresh one from the origin server."}, {"key": "X-HostName", "value": "8704295e9391", "name": "X-HostName", "description": "Custom header"}, {"key": "X-Region", "value": "local", "name": "X-Region", "description": "Custom header"}, {"key": "X-Version", "value": "37b638e63b6af0111a3f69b62ca63d548a090423", "name": "X-Version", "description": "Custom header"}], "cookie": [{"expires": "<PERSON><PERSON> Jul 17 2018 16:03:17 GMT+0100 (IST)", "httpOnly": false, "domain": "localhost", "path": "/", "secure": false, "value": "docker", "key": "XDEBUG_SESSION"}], "responseTime": 387, "body": "{\n    \"data\": [\n    \t{\n    \t\t\"_id\": \"59a3011a05c677bda916611c\",\n\t\t\t\"branch_id\": \"49a7011a05c677b9a916612a\",\n\t\t\t\"first_name\": \"<PERSON>\",\n\t\t\t\"last_name\": \"<PERSON><PERSON><PERSON><PERSON><PERSON>\",\n\t\t\t\"email\": \"<EMAIL>\",\n\t\t\t\"phone\": \"083-8599851\",\n\t\t\t\"source\": \"dashboard\",\n\t\t\t\"lead_status\": \"WARM\"\n    \t}\t\n\t],\n    \"success\": true,\n    \"meta\": {\n        \"pagination\": {\n            \"count\": 1,\n            \"perPage\": 50,\n            \"currentPage\": 1,\n            \"links\": {\n                \"previous\": null,\n                \"next\": null\n            }\n        }\n    }\n}"}]}], "_postman_isSubFolder": true}, {"name": "Leads", "item": [{"name": "Create New Lead", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"branch_id\":\"53fb0a22fe25f0c84c8b4575\",\"namespace\":\"glofox\", \"lead_status\":\"COLD\", \"active\":true,\"first_name\":\"test\",\"last_name\":\"test\",\"gender\":{\"name\":\"FEMALE\",\"label\":\"F\"},\"phone\":\"231dd23\",\"email\":\"<EMAIL>\",\"birth\":\"1994-12-07T00:00:00.000Z\",\"emergency_contact\":null,\"receive_marketing\":null,\"membership\":{\"type\":\"payg\"},\"device_os\":null,\"device_app_version\":null,\"expires_moment\":null,\"strike\":null,\"image\":null,\"status\":\"member\",\"type\":\"MEMBER\"}"}, "url": {"raw": "http://localhost:8889/2.1/branches/{{branchId}}/leads", "protocol": "http", "host": ["localhost"], "port": "8889", "path": ["2.1", "branches", "{{branchId}}", "leads"]}}, "response": []}, {"name": "Change Lead Status to Cold", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"lead_status\":\"COLD\"}"}, "url": {"raw": "http://localhost:8889/2.1/branches/{{branchId}}/leads/{{userId}}", "protocol": "http", "host": ["localhost"], "port": "8889", "path": ["2.1", "branches", "{{branchId}}", "leads", "{{userId}}"]}}, "response": []}, {"name": "Change Lead Status to Warm", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"lead_status\":\"WARM\"}"}, "url": {"raw": "http://localhost:8889/2.1/branches/{{branchId}}/leads/{{userId}}", "protocol": "http", "host": ["localhost"], "port": "8889", "path": ["2.1", "branches", "{{branchId}}", "leads", "{{userId}}"]}}, "response": []}, {"name": "Search Cold Leads", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "http://localhost:8889/2.1/branches/{{branchId}}/leads?filters[search]=&filters[status]=COLD&page=1", "protocol": "http", "host": ["localhost"], "port": "8889", "path": ["2.1", "branches", "{{branchId}}", "leads"], "query": [{"key": "filters[search]", "value": "", "equals": true}, {"key": "filters[status]", "value": "COLD", "equals": true}, {"key": "page", "value": "1", "equals": true}]}}, "response": []}, {"name": "Search Warm Leads", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "http://localhost:8889/2.1/branches/{{branchId}}/leads?filters[search]=&filters[status]=WARM&page=1", "protocol": "http", "host": ["localhost"], "port": "8889", "path": ["2.1", "branches", "{{branchId}}", "leads"], "query": [{"key": "filters[search]", "value": "", "equals": true}, {"key": "filters[status]", "value": "WARM", "equals": true}, {"key": "page", "value": "1", "equals": true}]}}, "response": []}, {"name": "Search Untouched Leads", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "http://localhost:8889/2.1/branches/{{branchId}}/leads?filters[search]=&filters[status]=UNTOUCHED&page=1", "protocol": "http", "host": ["localhost"], "port": "8889", "path": ["2.1", "branches", "{{branchId}}", "leads"], "query": [{"key": "filters[search]", "value": "", "equals": true}, {"key": "filters[status]", "value": "UNTOUCHED", "equals": true}, {"key": "page", "value": "1", "equals": true}]}}, "response": []}, {"name": "Search Trial Leads", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\"branch_id\":\"53fb0a22fe25f0c84c8b4575\",\"namespace\":\"glofox\",\"active\":true,\"first_name\":\"TEst\",\"last_name\":\"test\",\"gender\":{\"name\":\"MA<PERSON>\",\"label\":\"M\"},\"phone\":\"12311551s12311\",\"email\":\"<EMAIL>\",\"birth\":\"1994-12-07T00:00:00.000Z\",\"emergency_contact\":null,\"receive_marketing\":null,\"membership\":{\"type\":\"payg\"},\"device_os\":null,\"device_app_version\":null,\"expires_moment\":null,\"strike\":null,\"image\":null,\"status\":\"member\",\"type\":\"MEMBER\", \"lead_status\":\"WARM\"}"}, "url": {"raw": "http://localhost:8889/2.1/branches/{{branchId}}/leads?filters[search]=&filters[status]=TRIAL&page=1", "protocol": "http", "host": ["localhost"], "port": "8889", "path": ["2.1", "branches", "{{branchId}}", "leads"], "query": [{"key": "filters[search]", "value": "", "equals": true}, {"key": "filters[status]", "value": "TRIAL", "equals": true}, {"key": "page", "value": "1", "equals": true}]}}, "response": []}, {"name": "Search Leads by E-mail, First Name, Last Name and Phone", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\"branch_id\":\"53fb0a22fe25f0c84c8b4575\",\"namespace\":\"glofox\",\"active\":true,\"first_name\":\"TEst\",\"last_name\":\"test\",\"gender\":{\"name\":\"MA<PERSON>\",\"label\":\"M\"},\"phone\":\"12311551s12311\",\"email\":\"<EMAIL>\",\"birth\":\"1994-12-07T00:00:00.000Z\",\"emergency_contact\":null,\"receive_marketing\":null,\"membership\":{\"type\":\"payg\"},\"device_os\":null,\"device_app_version\":null,\"expires_moment\":null,\"strike\":null,\"image\":null,\"status\":\"member\",\"type\":\"MEMBER\", \"lead_status\":\"WARM\"}"}, "url": {"raw": "http://localhost:8889/2.1/branches/{{branchId}}/leads?filters[search]=Test&filters[status]=COLD&page=1", "protocol": "http", "host": ["localhost"], "port": "8889", "path": ["2.1", "branches", "{{branchId}}", "leads"], "query": [{"key": "filters[search]", "value": "Test", "equals": true}, {"key": "filters[status]", "value": "COLD", "equals": true}, {"key": "page", "value": "1", "equals": true}]}}, "response": []}], "_postman_isSubFolder": true}, {"name": "Interactions", "item": [{"name": "Create Interaction for a Lead/Member", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\"user_id\":\"5b080fee399be700f20d9bf4\",\"type\":\"NOTE\",\"description\":\"test test test\"}"}, "url": {"raw": "http://localhost:8889/2.1/branches/{{branchId}}/leads/{{userId}}/interactions", "protocol": "http", "host": ["localhost"], "port": "8889", "path": ["2.1", "branches", "{{branchId}}", "leads", "{{userId}}", "interactions"]}}, "response": []}, {"name": "Get Interactions of a Lead/Member", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\"user_id\":\"5b080fee399be700f20d9bf4\",\"type\":\"NOTE\",\"description\":\"testezinho basico\"}"}, "url": {"raw": "http://localhost:8889/2.1/branches/{{branchId}}/leads/{{userId}}/interactions", "protocol": "http", "host": ["localhost"], "port": "8889", "path": ["2.1", "branches", "{{branchId}}", "leads", "{{userId}}", "interactions"]}}, "response": []}, {"name": "Get Interactions Types for Branch", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\"user_id\":\"5b080fee399be700f20d9bf4\",\"type\":\"NOTE\",\"description\":\"testezinho basico\"}"}, "url": {"raw": "http://localhost:8889/2.1/branches/{{branchId}}/interactions/types", "protocol": "http", "host": ["localhost"], "port": "8889", "path": ["2.1", "branches", "{{branchId}}", "interactions", "types"]}}, "response": []}, {"name": "Get Manual Interactions Types for Branch", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\"user_id\":\"5b080fee399be700f20d9bf4\",\"type\":\"NOTE\",\"description\":\"testezinho basico\"}"}, "url": {"raw": "http://localhost:8889/2.1/branches/{{branchId}}/interactions/types?filters[manual]=1", "protocol": "http", "host": ["localhost"], "port": "8889", "path": ["2.1", "branches", "{{branchId}}", "interactions", "types"], "query": [{"key": "filters[manual]", "value": "1", "equals": true}]}}, "response": []}], "_postman_isSubFolder": true}, {"name": "Branches", "item": [{"name": "Switch Branch", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authenticationToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n\t\"branch_id\":\"58468cc1dd3c2f62e83e911b\"\n}"}, "url": {"raw": "http://{{host}}:{{port}}/2.1/branches/{{branchId}}/users/{{userId}}/switch?to={{targetBranchId}}", "protocol": "http", "host": ["{{host}}"], "port": "{{port}}", "path": ["2.1", "branches", "{{branchId}}", "users", "{{userId}}", "switch"], "query": [{"key": "to", "value": "{{targetBranchId}}", "equals": true}]}, "description": "If you are in a roaming membership you can use this endpoint to get a token from one of the branch that you have access to"}, "response": []}], "_postman_isSubFolder": true}, {"name": "Notifications", "item": [{"name": "List Notifications", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authenticationToken}}"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "http://{{host}}:{{port}}/2.1/branches/{{branchId}}/users/{{userId}}/notifications?page=1", "protocol": "http", "host": ["{{host}}"], "port": "{{port}}", "path": ["2.1", "branches", "{{branchId}}", "users", "{{userId}}", "notifications"], "query": [{"key": "page", "value": "1", "equals": true}]}, "description": "List Notifications"}, "response": [{"id": "8f555e1b-f65f-4420-8da1-668e24ab844e", "name": "Member without receive marketing enabled", "originalRequest": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authenticationToken}}"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "http://{{host}}:{{port}}/2.1/branches/{{branchId}}/users/{{userId}}/notifications?page=1", "protocol": "http", "host": ["{{host}}"], "port": "{{port}}", "path": ["2.1", "branches", "{{branchId}}", "users", "{{userId}}", "notifications"], "query": [{"key": "page", "value": "1", "equals": true}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "_postman_previewtype": "text", "header": [{"name": "connection", "key": "connection", "value": "Keep-Alive", "description": "Options that are desired for the connection"}, {"name": "content-encoding", "key": "content-encoding", "value": "gzip", "description": "The type of encoding used on the data."}, {"name": "content-type", "key": "content-type", "value": "application/json; charset=UTF-8", "description": "The mime type of this content"}, {"name": "date", "key": "date", "value": "<PERSON><PERSON>, 03 Jul 2018 14:15:56 GMT", "description": "The date and time that the message was sent"}, {"name": "keep-alive", "key": "keep-alive", "value": "timeout=5, max=100", "description": "Custom header"}, {"name": "server", "key": "server", "value": "Apache/2.4.10 (<PERSON><PERSON>)", "description": "A name for the server"}, {"name": "transfer-encoding", "key": "transfer-encoding", "value": "chunked", "description": "The form of encoding used to safely transfer the entity to the user. Currently defined methods are: chunked, compress, deflate, gzip, identity."}, {"name": "vary", "key": "vary", "value": "Accept-Encoding,Authorization", "description": "Tells downstream proxies how to match future request headers to decide whether the cached response can be used rather than requesting a fresh one from the origin server."}, {"name": "x-app-name", "key": "x-app-name", "value": "local", "description": "Custom header"}, {"name": "x-hostname", "key": "x-hostname", "value": "9cbcb6d5ff70", "description": "Custom header"}, {"name": "x-region", "key": "x-region", "value": "local", "description": "Custom header"}, {"name": "x-server", "key": "x-server", "value": "local", "description": "Custom header"}, {"name": "x-version", "key": "x-version", "value": "093f83ab5d05988b2c1a8d351a211e7ef0938a6a", "description": "Custom header"}], "cookie": [], "responseTime": 1994, "body": "{\"data\":[{\"_id\":\"5b3b5868925d4e01031b7d72\",\"branch_id\":\"57061ab7778f3bed478b4568\",\"type\":\"broadcast\",\"user_id\":\"5b0ebf15c0d8b300743fbd92\",\"created\":1530615912,\"is_marketing\":false,\"message\":\"Test Push Not Marketing\"},{\"_id\":\"5b3b563f925d4e00f02dd072\",\"branch_id\":\"57061ab7778f3bed478b4568\",\"type\":\"broadcast\",\"user_id\":null,\"created\":1530615359,\"is_marketing\":false,\"message\":\"Test broadcast to all 123\"}],\"success\":true}"}, {"id": "7dfd2e8a-f22d-415e-8c01-55b498c60506", "name": "Member with receive marketing enabled", "originalRequest": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authenticationToken}}", "warning": ""}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "http://{{host}}:{{port}}/2.1/branches/{{branchId}}/users/{{userId}}/notifications?page=1", "protocol": "http", "host": ["{{host}}"], "port": "{{port}}", "path": ["2.1", "branches", "{{branchId}}", "users", "{{userId}}", "notifications"], "query": [{"key": "page", "value": "1", "equals": true}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "_postman_previewtype": "text", "header": [{"name": "connection", "key": "connection", "value": "Keep-Alive", "description": "Options that are desired for the connection"}, {"name": "content-encoding", "key": "content-encoding", "value": "gzip", "description": "The type of encoding used on the data."}, {"name": "content-type", "key": "content-type", "value": "application/json; charset=UTF-8", "description": "The mime type of this content"}, {"name": "date", "key": "date", "value": "<PERSON><PERSON>, 03 Jul 2018 14:17:19 GMT", "description": "The date and time that the message was sent"}, {"name": "keep-alive", "key": "keep-alive", "value": "timeout=5, max=100", "description": "Custom header"}, {"name": "server", "key": "server", "value": "Apache/2.4.10 (<PERSON><PERSON>)", "description": "A name for the server"}, {"name": "transfer-encoding", "key": "transfer-encoding", "value": "chunked", "description": "The form of encoding used to safely transfer the entity to the user. Currently defined methods are: chunked, compress, deflate, gzip, identity."}, {"name": "vary", "key": "vary", "value": "Accept-Encoding,Authorization", "description": "Tells downstream proxies how to match future request headers to decide whether the cached response can be used rather than requesting a fresh one from the origin server."}, {"name": "x-app-name", "key": "x-app-name", "value": "local", "description": "Custom header"}, {"name": "x-hostname", "key": "x-hostname", "value": "9cbcb6d5ff70", "description": "Custom header"}, {"name": "x-region", "key": "x-region", "value": "local", "description": "Custom header"}, {"name": "x-server", "key": "x-server", "value": "local", "description": "Custom header"}, {"name": "x-version", "key": "x-version", "value": "093f83ab5d05988b2c1a8d351a211e7ef0938a6a", "description": "Custom header"}], "cookie": [], "responseTime": 2009, "body": "{\"data\":[{\"_id\":\"5b3b5868925d4e01031b7d72\",\"branch_id\":\"57061ab7778f3bed478b4568\",\"type\":\"broadcast\",\"user_id\":\"5b0ebf15c0d8b300743fbd92\",\"created\":1530615912,\"is_marketing\":false,\"message\":\"Test Push Not Marketing\"},{\"_id\":\"5b3b5660925d4e00f33d5cb2\",\"branch_id\":\"57061ab7778f3bed478b4568\",\"type\":\"broadcast\",\"user_id\":null,\"created\":1530615392,\"is_marketing\":true,\"message\":\"Test broadcast to all Marketing\"},{\"_id\":\"5b3b563f925d4e00f02dd072\",\"branch_id\":\"57061ab7778f3bed478b4568\",\"type\":\"broadcast\",\"user_id\":null,\"created\":1530615359,\"is_marketing\":false,\"message\":\"Test broadcast to all 123\"}],\"success\":true}"}, {"id": "45e3f618-5b9c-41be-9961-45f44a8e3b97", "name": "Invalid User Id", "originalRequest": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authenticationToken}}", "warning": ""}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "http://{{host}}:{{port}}/2.1/branches/{{branchId}}/users/{{wrongUserId}}/notifications?page=1", "protocol": "http", "host": ["{{host}}"], "port": "{{port}}", "path": ["2.1", "branches", "{{branchId}}", "users", "{{wrongUserId}}", "notifications"], "query": [{"key": "page", "value": "1", "equals": true}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "_postman_previewtype": "text", "header": [{"name": "connection", "key": "connection", "value": "Keep-Alive", "description": "Options that are desired for the connection"}, {"name": "content-encoding", "key": "content-encoding", "value": "gzip", "description": "The type of encoding used on the data."}, {"name": "content-type", "key": "content-type", "value": "application/json; charset=UTF-8", "description": "The mime type of this content"}, {"name": "date", "key": "date", "value": "<PERSON><PERSON>, 03 Jul 2018 14:32:04 GMT", "description": "The date and time that the message was sent"}, {"name": "keep-alive", "key": "keep-alive", "value": "timeout=5, max=100", "description": "Custom header"}, {"name": "server", "key": "server", "value": "Apache/2.4.10 (<PERSON><PERSON>)", "description": "A name for the server"}, {"name": "transfer-encoding", "key": "transfer-encoding", "value": "chunked", "description": "The form of encoding used to safely transfer the entity to the user. Currently defined methods are: chunked, compress, deflate, gzip, identity."}, {"name": "vary", "key": "vary", "value": "Accept-Encoding,Authorization", "description": "Tells downstream proxies how to match future request headers to decide whether the cached response can be used rather than requesting a fresh one from the origin server."}, {"name": "x-app-name", "key": "x-app-name", "value": "local", "description": "Custom header"}, {"name": "x-hostname", "key": "x-hostname", "value": "9cbcb6d5ff70", "description": "Custom header"}, {"name": "x-region", "key": "x-region", "value": "local", "description": "Custom header"}, {"name": "x-server", "key": "x-server", "value": "local", "description": "Custom header"}, {"name": "x-version", "key": "x-version", "value": "093f83ab5d05988b2c1a8d351a211e7ef0938a6a", "description": "Custom header"}], "cookie": [], "responseTime": 1535, "body": "{\"success\":false,\"message\":\"The following id is not valid: wrongUserId\",\"message_code\":\"The following id is not valid: wrongUserId\",\"message_data\":[],\"errors\":[\"The following id is not valid: wrongUserId\"]}"}]}], "_postman_isSubFolder": true}, {"name": "Memberships", "item": [{"name": "Search Trial Memberships", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "http://localhost:8889/2.1/branches/{{branchId}}/memberships?page=1&filters[trial]=1", "protocol": "http", "host": ["localhost"], "port": "8889", "path": ["2.1", "branches", "{{branchId}}", "memberships"], "query": [{"key": "page", "value": "1", "equals": true}, {"key": "filters[trial]", "value": "1", "equals": true}]}}, "response": []}, {"name": "Search Non-Trial Memberships ", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "http://localhost:8889/2.1/branches/{{branchId}}/memberships?page=1&filters[trial]=0", "protocol": "http", "host": ["localhost"], "port": "8889", "path": ["2.1", "branches", "{{branchId}}", "memberships"], "query": [{"key": "page", "value": "1", "equals": true}, {"key": "filters[trial]", "value": "0", "equals": true}]}}, "response": []}], "_postman_isSubFolder": true}]}, {"name": "2.0", "item": [{"name": "Events", "item": [{"name": "Create New Single Event", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"namespace\": null,\n    \"branch_id\": null,\n    \"program_id\": null,\n    \"active\": null,\n    \"private\": null,\n    \"name\": null,\n    \"description\": null,\n    \"time_start\": null,\n    \"level\": null,\n    \"size\": null,\n    \"facility\": null,\n    \"trainers\": null,\n    \"duration\": null,\n    \"attendance_submitted\": null,\n    \"manually_deleted\": null,\n    \"total_waitings\": null,\n    \"total_bookings\": null,\n    \"new\": null,\n    \"schedule_code\": null,\n    \"date\": null,\n    \"week_day\": null,\n    \"time_finish\": null,\n    \"status\": null\n}"}, "url": {"raw": "http://localhost:8889/2.0/branches/{{branchId}}/events", "protocol": "http", "host": ["localhost"], "port": "8889", "path": ["2.0", "branches", "{{branchId}}", "events"]}}, "response": []}, {"name": "Update Existing Single Event", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"namespace\": null,\n    \"branch_id\": null,\n    \"program_id\": null,\n    \"active\": null,\n    \"private\": null,\n    \"name\": null,\n    \"description\": null,\n    \"time_start\": null,\n    \"level\": null,\n    \"size\": null,\n    \"facility\": null,\n    \"trainers\": null,\n    \"duration\": null,\n    \"attendance_submitted\": null,\n    \"manually_deleted\": null,\n    \"total_waitings\": null,\n    \"total_bookings\": null,\n    \"new\": null,\n    \"schedule_code\": null,\n    \"date\": null,\n    \"week_day\": null,\n    \"time_finish\": null,\n    \"status\": null\n}"}, "url": {"raw": "http://localhost:8889/2.0/branches/{{branchId}}/events/{{eventId}}", "protocol": "http", "host": ["localhost"], "port": "8889", "path": ["2.0", "branches", "{{branchId}}", "events", "{{eventId}}"]}}, "response": []}, {"name": "Delete Existing Single Event", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "http://localhost:8889/2.0/branches/{{branchId}}/events/{{eventId}}", "protocol": "http", "host": ["localhost"], "port": "8889", "path": ["2.0", "branches", "{{branchId}}", "events", "{{eventId}}"]}}, "response": []}, {"name": "Get Events of a Branch", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "http://localhost:8889/2.0/branches/{{branchId}}/events", "protocol": "http", "host": ["localhost"], "port": "8889", "path": ["2.0", "branches", "{{branchId}}", "events"]}}, "response": []}, {"name": "Get Event data of a single event", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "http://localhost:8889/2.0/branches/{{branchId}}/events/{{eventId}}", "protocol": "http", "host": ["localhost"], "port": "8889", "path": ["2.0", "branches", "{{branchId}}", "events", "{{eventId}}"]}}, "response": []}, {"name": "Get Price for Single Event", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "http://localhost:8889/2.0/branches/{{branchId}}/events/{{eventId}}", "protocol": "http", "host": ["localhost"], "port": "8889", "path": ["2.0", "branches", "{{branchId}}", "events", "{{eventId}}"]}}, "response": [{"id": "936546ac-a9a3-4acb-9e0a-0d96bb4e9268", "name": "200 - Price Fetch Sucessful", "originalRequest": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "http://localhost:8889/2.0/branches/{{branchId}}/events/{{eventId}}/price?for={{userId}}", "protocol": "http", "host": ["localhost"], "port": "8889", "path": ["2.0", "branches", "{{branchId}}", "events", "{{eventId}}", "price"], "query": [{"key": "for", "value": "{{userId}}", "equals": true}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "_postman_previewtype": "parsed", "header": [], "cookie": [], "responseTime": 0, "body": "{\n    \"success\": true,\n    \"data\": {\n        \"credits\": 1,\n        \"price\": 15,\n        \"currency\": \"EUR\"\n    }\n}"}, {"id": "f4d8bcb1-1ef6-4129-88e5-ce3d0fca8944", "name": "400 - Membership is Expired", "originalRequest": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "http://localhost:8889/2.0/branches/{{branchId}}/events/{{eventId}}/price?for={{userId}}", "protocol": "http", "host": ["localhost"], "port": "8889", "path": ["2.0", "branches", "{{branchId}}", "events", "{{eventId}}", "price"], "query": [{"key": "for", "value": "{{userId}}", "equals": true}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "_postman_previewtype": "parsed", "header": [], "cookie": [], "responseTime": 0, "body": "{\n    \"success\": false,\n    \"message\": \"Membership is Expired\",\n    \"message_code\": \"MEMBERSHIP_EXPIRED\",\n    \"message_data\": [],\n    \"errors\": [\n        \"MEMBERSHIP_EXPIRED\"\n    ]\n}"}, {"id": "646359cf-04a9-4162-ab0e-a1ee1366cb47", "name": "400 - Membership is not Allowed (Not Eligible)", "originalRequest": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "http://localhost:8889/2.0/branches/{{branchId}}/events/{{eventId}}/price?for={{userId}}", "protocol": "http", "host": ["localhost"], "port": "8889", "path": ["2.0", "branches", "{{branchId}}", "events", "{{eventId}}", "price"], "query": [{"key": "for", "value": "{{userId}}", "equals": true}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "_postman_previewtype": "parsed", "header": [], "cookie": [], "responseTime": 0, "body": "{\n    \"success\": false,\n    \"message\": \"Membership is not Allowed\",\n    \"message_code\": \"MEMBERSHIP_NOT_ALLOWED\",\n    \"message_data\": [],\n    \"errors\": [\n        \"MEMBERSHIP_NOT_ALLOWED\"\n    ]\n}"}, {"id": "1d7f88fa-6bef-4053-9ea0-0144ec500a15", "name": "400 - Guest is not eligible for Event", "originalRequest": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "http://localhost:8889/2.0/branches/{{branchId}}/events/{{eventId}}", "protocol": "http", "host": ["localhost"], "port": "8889", "path": ["2.0", "branches", "{{branchId}}", "events", "{{eventId}}"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "_postman_previewtype": "parsed", "header": [], "cookie": [], "responseTime": 0, "body": "{\n    \"success\": false,\n    \"message\": \"Guest is not eligible for Event\",\n    \"message_code\": \"GUEST_NOT_ELIGIBLE_FOR_EVENT\",\n    \"message_data\": [],\n    \"errors\": [\n        \"GUEST_NOT_ELIGIBLE_FOR_EVENT\"\n    ]\n}"}, {"id": "f1c0c329-2f0f-4805-ac35-76f3292247e6", "name": "400 - Not Eligible for event and not enough credits", "originalRequest": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "http://localhost:8889/2.0/branches/{{branchId}}/events/{{eventId}}", "protocol": "http", "host": ["localhost"], "port": "8889", "path": ["2.0", "branches", "{{branchId}}", "events", "{{eventId}}"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "_postman_previewtype": "parsed", "header": [], "cookie": [], "responseTime": 0, "body": "{\n    \"success\": false,\n    \"message\": \"Not Eligible for event and not enough credits\",\n    \"message_code\": \"NOT_ELIGIBLE_FOR_EVENT_AND_NOT_ENOUGH_CREDITS\",\n    \"message_data\": [],\n    \"errors\": [\n        \"NOT_ELIGIBLE_FOR_EVENT_AND_NOT_ENOUGH_CREDITS\"\n    ]\n}"}]}], "_postman_isSubFolder": true}, {"name": "Memberships", "item": [{"name": "Create New Membership", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n}"}, "url": {"raw": "http://localhost:8889/2.0/branches/{{branchId}}/memberships", "protocol": "http", "host": ["localhost"], "port": "8889", "path": ["2.0", "branches", "{{branchId}}", "memberships"]}}, "response": []}, {"name": "Update Existing Membership", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n\t\n}"}, "url": {"raw": "http://localhost:8889/2.0/branches/{{branchId}}/memberships/{{membershipId}}", "protocol": "http", "host": ["localhost"], "port": "8889", "path": ["2.0", "branches", "{{branchId}}", "memberships", "{{membershipId}}"]}}, "response": []}, {"name": "Delete Existing Membership", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "http://localhost:8889/2.0/branches/{{branchId}}/memberships/{{membershipId}}", "protocol": "http", "host": ["localhost"], "port": "8889", "path": ["2.0", "branches", "{{branchId}}", "memberships", "{{membershipId}}"]}}, "response": []}, {"name": "Get Memberships of a Branch", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "http://localhost:8889/2.0/branches/{{branchId}}/memberships", "protocol": "http", "host": ["localhost"], "port": "8889", "path": ["2.0", "branches", "{{branchId}}", "memberships"]}}, "response": []}, {"name": "Get Membership data of a single membership", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "http://localhost:8889/2.0/branches/{{branchId}}/memberships/{{membershipId}}", "protocol": "http", "host": ["localhost"], "port": "8889", "path": ["2.0", "branches", "{{branchId}}", "memberships", "{{membershipId}}"]}}, "response": []}], "_postman_isSubFolder": true}, {"name": "Courses", "item": [{"name": "Create New Course", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n}"}, "url": {"raw": "http://localhost:8889/2.0/branches/{{branchId}}/courses", "protocol": "http", "host": ["localhost"], "port": "8889", "path": ["2.0", "branches", "{{branchId}}", "courses"]}}, "response": []}, {"name": "Update Existing Course", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n}"}, "url": {"raw": "http://localhost:8889/2.0/branches/{{branchId}}/courses/{{courseId}}", "protocol": "http", "host": ["localhost"], "port": "8889", "path": ["2.0", "branches", "{{branchId}}", "courses", "{{courseId}}"]}}, "response": []}, {"name": "Delete Existing Course", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "http://localhost:8889/2.0/branches/{{branchId}}/courses/{{courseId}}", "protocol": "http", "host": ["localhost"], "port": "8889", "path": ["2.0", "branches", "{{branchId}}", "courses", "{{courseId}}"]}}, "response": []}, {"name": "Get Memberships of a Course", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "http://localhost:8889/2.0/branches/{{branchId}}/courses", "protocol": "http", "host": ["localhost"], "port": "8889", "path": ["2.0", "branches", "{{branchId}}", "courses"]}}, "response": []}, {"name": "Get Course data of a single course", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "http://localhost:8889/2.0/branches/{{branchId}}/courses/{{courseId}}", "protocol": "http", "host": ["localhost"], "port": "8889", "path": ["2.0", "branches", "{{branchId}}", "courses", "{{courseId}}"]}}, "response": []}], "_postman_isSubFolder": true}]}, {"name": "1.0", "item": [{"name": "Timeslots", "item": [{"name": "Book member to Timeslot", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "formdata", "formdata": [{"key": "password", "value": "{{userPassword}}", "type": "text"}]}, "url": {"raw": "http://localhost:8889/time_slot/{{timeslotId}}/bookings/add/{{userId}}", "protocol": "http", "host": ["localhost"], "port": "8889", "path": ["time_slot", "{{timeslotId}}", "bookings", "add", "{{userId}}"]}}, "response": []}], "_postman_isSubFolder": true}, {"name": "Events", "item": [{"name": "Get Events of a Branch", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "http://localhost:8889/2.0/branches/{{branchId}}/events", "protocol": "http", "host": ["localhost"], "port": "8889", "path": ["2.0", "branches", "{{branchId}}", "events"]}}, "response": []}], "_postman_isSubFolder": true}]}]}