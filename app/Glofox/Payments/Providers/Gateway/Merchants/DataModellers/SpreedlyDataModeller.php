<?php

namespace Glofox\Payments\Providers\Gateway\Merchants\DataModellers;

use Glofox\Domain\Branches\Models\Branch;
use Glofox\Domain\PaymentProviders\Models\PaymentProvider;
use Illuminate\Support\Collection;

/**
 * Class SpreedlyDataModeler
 * @package Glofox\Payments\Providers\Gateway\Merchants\DataModelers
 */
class SpreedlyDataModeller extends DefaultDataModeller
{
    /**
     * @param PaymentProvider $paymentProvider
     * @param Branch $branch
     * @param Collection $payload
     * @return Collection
     */
    public function modelForCreating(PaymentProvider $paymentProvider, Branch $branch, Collection $payload): Collection
    {
        $payload = parent::modelForCreating($paymentProvider, $branch, $payload);
        if ($payload->isEmpty()) {
            return $payload;
        }

        return $this->format($branch, $payload);
    }

    /**
     * @param PaymentProvider $paymentProvider
     * @param Branch $branch
     * @param Collection $payload
     * @return Collection
     */
    public function modelForUpdating(PaymentProvider $paymentProvider, Branch $branch, Collection $payload): Collection
    {
        $payload = parent::modelForUpdating($paymentProvider, $branch, $payload);
        if ($payload->isEmpty()) {
            return $payload;
        }

        return $this->format($branch, $payload);
    }

    /**
     * @param Branch $branch
     * @param Collection $payload
     * @return Collection
     */
    private function format(Branch $branch, Collection $payload) : Collection
    {
        return collect([
            'gateway' => $payload,
            'merchant_details' => [
                'BusinessName' => $branch->name(),
                'Currency' => $branch->currency(),
                'Email' => $branch->email(),
                'Country' => $branch->address()->countryCode('alpha3'),
            ]
        ]);
    }
}
