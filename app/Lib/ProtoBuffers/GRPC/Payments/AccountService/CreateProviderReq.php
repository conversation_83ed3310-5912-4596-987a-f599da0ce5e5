<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: account.proto

namespace GRPC\Payments\AccountService;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>grpc.CreateProviderReq</code>
 */
class CreateProviderReq extends \Google\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string Value = 1;</code>
     */
    protected $Value = '';
    /**
     * Generated from protobuf field <code>string Client = 2;</code>
     */
    protected $Client = '';
    /**
     * Generated from protobuf field <code>string AuthObject = 3;</code>
     */
    protected $AuthObject = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $Value
     *     @type string $Client
     *     @type string $AuthObject
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Account::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string Value = 1;</code>
     * @return string
     */
    public function getValue()
    {
        return $this->Value;
    }

    /**
     * Generated from protobuf field <code>string Value = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setValue($var)
    {
        GPBUtil::checkString($var, True);
        $this->Value = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string Client = 2;</code>
     * @return string
     */
    public function getClient()
    {
        return $this->Client;
    }

    /**
     * Generated from protobuf field <code>string Client = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setClient($var)
    {
        GPBUtil::checkString($var, True);
        $this->Client = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string AuthObject = 3;</code>
     * @return string
     */
    public function getAuthObject()
    {
        return $this->AuthObject;
    }

    /**
     * Generated from protobuf field <code>string AuthObject = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setAuthObject($var)
    {
        GPBUtil::checkString($var, True);
        $this->AuthObject = $var;

        return $this;
    }

}

