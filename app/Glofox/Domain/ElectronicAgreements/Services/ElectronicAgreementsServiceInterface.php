<?php

namespace Glofox\Domain\ElectronicAgreements\Services;

use Glofox\Domain\ElectronicAgreements\Models\Agreement;
use Glofox\Domain\ElectronicAgreements\Models\Document;
use Glofox\Domain\ElectronicAgreements\Models\DocumentVersion;
use Glofox\Domain\ElectronicAgreements\Requests\CreateAgreementRequest;
use Glofox\Domain\ElectronicAgreements\Requests\DeleteAgreementRequest;
use Glofox\Domain\ElectronicAgreements\Requests\CreateDocumentVersionRequest;
use Glofox\Domain\ElectronicAgreements\Requests\RedirectAgreementRequest;
use Glofox\Domain\ElectronicAgreements\Trigger;
use Glofox\Domain\Users\Models\User;
use Glofox\Infrastructure\Exception\HttpClientResponseException;

interface ElectronicAgreementsServiceInterface
{
    /**
     * @param string $studioId
     * @param User|null $user
     * @return bool
     */
    public function isElectronicAgreementsEnabled(string $studioId, ?User $user = null): bool;

    /**
     * @param string $studioId
     * @param array $includes
     * @return array
     * @throws HttpClientResponseException
     */
    public function findDocuments(string $studioId, array $includes = []): array;

    /**
     * @param string $studioId
     * @param Trigger $trigger
     * @param array $includes
     * @return Document|null
     * @throws HttpClientResponseException
     */
    public function findDocumentByTrigger(string $studioId, Trigger $trigger, array $includes = []): ?Document;

    /**
     * @param string $studioId
     * @param string $documentId
     * @param array $includes
     * @return Document|null
     * @throws HttpClientResponseException
     */
    public function findDocument(string $studioId, string $documentId, array $includes = []): ?Document;

    /**
     * @param CreateDocumentVersionRequest $data
     * @return Document
     * @throws HttpClientResponseException
     */
    public function createDocument(CreateDocumentVersionRequest $data): Document;

    /**
     * @param string $studioId
     * @param string $documentId
     * @param string $versionId
     * @return DocumentVersion|null
     * @throws HttpClientResponseException
     */
    public function getDocumentVersion(string $studioId, string $documentId, string $versionId): ?DocumentVersion;

    /**
     * @param string $studioId
     * @param string $documentId
     * @return DocumentVersion|null
     * @throws HttpClientResponseException
     */
    public function getLatestDocumentVersion(string $studioId, string $documentId): ?DocumentVersion;

    /**
     * @param CreateDocumentVersionRequest $data
     * @return DocumentVersion
     * @throws HttpClientResponseException
     */
    public function createDocumentVersion(CreateDocumentVersionRequest $data): DocumentVersion;
    
    /**
     * @param string $type
     * @return bool
     */
    public function isSupportedType(string $type): bool;

    /**
     * @param CreateAgreementRequest $data
     * @return Agreement
     * @throws HttpClientResponseException
     */
    public function createAgreement(CreateAgreementRequest $data): Agreement;

    /**
     * @param DeleteAgreementRequest $data
     * @return bool
     * @throws HttpClientResponseException
     */
    public function deleteAgreement(DeleteAgreementRequest $data): bool;

    /**
     * @param string $studioId
     * @param string $memberId
     * @param bool $hasMembership
     * @param Trigger|null $trigger
     * @return array
     * @throws HttpClientResponseException
     */
    public function listAgreements(string $studioId, string $memberId, bool $hasMembership, Trigger $trigger = null): array;

    /**
     * @param string $studioId
     * @param array $memberIds
     * @param array $memberHasActiveMemberships
     * @return array
     * @throws HttpClientResponseException
     */
    public function listAgreementsForMembers(string $studioId, array $memberIds, array $memberHasActiveMemberships): array;

    /**
     * @param Agreement $agreement
     * @param string $email
     * @return Agreement
     * @throws HttpClientResponseException
     */
    public function generateUrl(Agreement $agreement, string $email): Agreement;

    /**
     * @param RedirectAgreementRequest $request
     * @return string
     * @throws HttpClientResponseException
     */
    public function agreementRedirectUrl(RedirectAgreementRequest $request): string;
   
    /**
     * @param string $resourceId
     * @param string $memberId
     * @return array
     * @throws HttpClientResponseException
     */
    public function findAgreementsByMetadata(string $resourceId, string $memberId): array;

    /**
     * @param string $studioId
     * @param string $memberId
     * @param string $memberType
     * @return bool
     * @throws HttpClientResponseException
     */
    public function hasOutstandingAgreements(string $studioId, string $memberId, string $memberType): bool;
}
