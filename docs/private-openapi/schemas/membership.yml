type: object
properties:
  id:
    type: string
    description: The unique identifier of the request
  userId:
    type: string
    description: The unique identifier of the user
  membershipId:
    type: string
    description: The unique identifier of the membership
  subscriptionProviderId:
    type: string
    description: The unique identifier of the subscription provider
  subscriptionPlanId:
    type: string
    description: The unique identifier of the subscription plan
  planCode:
    type: string
    description: The code of the plan
  branchId:
    type: string
    description: The unique identifier of the branch
  groupId:
    type: string
    description: The unique identifier of the group
  status:
    $ref: "./membership_status.yml"
  commencedDate:
    type: integer
    description: The local time that the membership started
  duration:
    type: object
    properties:
      startDate:
        type: integer
        description: The local time that the membership started
      expiryDate:
        type: integer
        description: The local time that the membership expires
  endDate:
    type: integer
    description: The local time that the membership ends
  purchaseDate:
    type: integer
    description: The local time that the membership was purchased
  nextPaymentDate:
    type: integer
    description: The local time that the next payment is due
  concludedAt:
    type: integer
    description: The local time that the membership was concluded
  membershipMetaData:
    $ref: "./membership_metadata.yml"
  schedulePause:
    type: boolean
    description: The status of the scheduled pause
  pause:
    type: boolean
    description: The status of the pause
  payment:
    type: object
    properties:
      price:
        type: integer
        description: The price of the membership
      upfrontFee:
        type: integer
        description: The upfront fee of the membership
      paymentMethod:
        type: string
        description: The payment method of the membership
      externalResourceId:
        type: string
        description: The external resource identifier of the membership
      discounts:
        type: array
        description: The list of discounts applied to the membership
        items:
          type: string
      discountsPreApplied:
        type: boolean
        description: The status of the discounts pre-applied to the membership
  hasActiveSubscription:
    type: boolean
    description: The status of the subscription
  allowedActions:
    type: object
    properties:
      isScheduledPauseAllowed:
        type: boolean
        description: The status of the scheduled pause
      isPauseAllowed:
        type: boolean
        description: The status of the pause
      isEditPauseAllowed:
        type: boolean
        description: The status of the pause edit
      isUnpauseAllowed:
        type: boolean
        description: The status of the unpause
      isConsecutiveMembershipAllowed:
        type: boolean
        description: The status of the consecutive membership
      isManageGroupMemberAllowed:
        type: boolean
        description: The status of the group member management
      isRemoveScheduledCancellationAllowed:
        type: boolean
        description: The status of the scheduled cancellation removal
  lock:
    type: boolean
    description: The status of the membership lock
  payg:
    type: boolean
    description: The status of the payg
  isPriceEdited:
    type: boolean
    description: The status of the price edit
  isUpfrontFeeEdited:
    type: boolean
    description: The status of the upfront fee edit
  isSubscription:
    type: boolean
    description: The status of the subscription
  isScheduledPauseAllowed:
    type: boolean
    description: The status of the scheduled pause
  isPauseAllowed:
    type: boolean
    description: The status of the pause
  isPrimaryGroupMember:
    type: boolean
    description: The status of the primary group member
