// UP

db.transactional_messages_defaults.insertOne({
    "identifier":"EA_AGREEMENT_SIGNED_CONFIRMATION",
    "name":"TRANSACTIONAL_MESSAGE_EA_SIGNATURE_SIGNED_CONFIRMATION_REQUEST_TITLE",
    "description":"TRANSACTIONAL_MESSAGE_EA_SIGNATURE_SIGNED_CONFIRMATION_REQUEST_DESCRIPTION",
    "template":"STANDARD",
    "enabled":true,
    "type":"EVENT_BASED",
    "subject":"Hi [member_first_name]",
    "content":"Hi [member_first_name],<br/><br/>You’re all set! Thanks for signing [document_name], You can find your signed copy here <a href='[document_url]'>[document_name]</a>.<br/><br/>If you have any questions, please contact [branch_name].<br/><br/>The [branch_name] team",
    "triggers":[],
    "variables": [
        "BRANCH_NAME",
        "MEMBER_FIRST_NAME",
        "DOCUMENT_NAME",
        "DOCUMENT_URL"
    ],
    "configurable":false
});

// DOWN

db.transactional_messages_defaults.deleteOne({
    "identifier":"EA_AGREEMENT_SIGNED_CONFIRMATION"
});