<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\Notifications\Search\Filters;

use Glofox\Domain\Notifications\Search\Filters\IsBroadcast;
use Glofox\Domain\Notifications\Type;

\App::import('Test/Case', 'GlofoxTestCase');

class IsBroadcastTest extends \GlofoxTestCase
{
    public $fixtures = [];
    /**
     * @var Expression
     */
    protected $evaluable;

    public function setUp()
    {
        parent::setUp();

        $this->evaluable = new IsBroadcast();
    }

    public function testEvaluationPasses(): void
    {
        $data = [
            'type' => Type::BROADCAST,
        ];

        $this->assertTrue($this->evaluable->evaluate($data));
    }

    public function testEvaluationFails(): void
    {
        $data = [
            'type' => 'invalid',
        ];

        $this->assertFalse($this->evaluable->evaluate($data));
    }
}
