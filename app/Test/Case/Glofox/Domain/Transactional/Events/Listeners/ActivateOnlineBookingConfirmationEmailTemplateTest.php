<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\Transactional\Events\Listeners;

use App;
use CakeTestCases\Glofox\Domain\Logger\Traits\MockedLoggerTrait;
use Glofox\Domain\Branches\Models\Branch;
use Glofox\Domain\Facilities\Models\Facility;
use Glofox\Domain\Transactional\Events\Listeners\ActivateOnlineBookingConfirmationEmailTemplate;
use Glofox\Domain\Transactional\Repositories\TransactionalMessagesRepository;
use Glofox\Domain\Transactional\Messages\Identifier;
use GlofoxTestCase;
use League\Event\EventInterface;
use Mockery;
use MongoId;

App::import('Test/Case', 'GlofoxTestCase');

class ActivateOnlineBookingConfirmationEmailTemplateTest extends GlofoxTestCase
{
    use MockedLoggerTrait;

    public $fixtures = [];
    private TransactionalMessagesRepository $transactionalMessagesRepository;
    private EventInterface $event;

    public function setUp()
    {
        parent::setUp();

        $facility = Mockery::mock(Facility::class);
        $facility->shouldReceive('branch')
            ->andReturn(Branch::make(['_id' => (string) new MongoId('5e907adf8f5c6a3808971e14')]));

        $this->transactionalMessagesRepository = Mockery::mock(TransactionalMessagesRepository::class);
        $this->event = Mockery::mock(EventInterface::class);
        $this->event->facility = $facility;

        $this->mockLogger();
    }

    public function tearDown()
    {
        parent::tearDown();

        $this->teardownLogger();
        Mockery::close();
    }

    public function test_when_online_booking_confirmation_email_is_enable_it_does_not_do_anything(): void
    {
        $this->transactionalMessagesRepository
            ->shouldReceive('findByBranchAndIdentifier')
            ->withArgs(function (array $branch, Identifier $identifier) {
                self::assertSame('5e907adf8f5c6a3808971e14', $branch['Branch']['_id']);
                self::assertSame('ONLINE_CLASS_BOOKING_CONFIRMATION', $identifier->getValue());

                return true;
            })
            ->andReturn([
                'TransactionalMessage' => [
                    'enabled' => true,
                ],
            ])
            ->once()
            ->getMock()
            ->shouldNotReceive('save');

        $listener = new ActivateOnlineBookingConfirmationEmailTemplate(
            $this->transactionalMessagesRepository,
            $this->loggerMock
        );

        $listener->handle($this->event);
    }

    public function test_when_online_booking_confirmation_email_is_not_enable_it_attempts_to_save_with_enable_true(): void
    {
        $this->transactionalMessagesRepository
            ->shouldReceive('findByBranchAndIdentifier')
            ->withArgs(function (array $branch, Identifier $identifier) {
                self::assertSame('5e907adf8f5c6a3808971e14', $branch['Branch']['_id']);
                self::assertSame('ONLINE_CLASS_BOOKING_CONFIRMATION', $identifier->getValue());

                return true;
            })
            ->andReturn([
                'TransactionalMessage' => [
                    'enabled' => false,
                ],
            ])
            ->once()
            ->getMock()
            ->shouldReceive('save')
            ->withArgs(function (array $data) {
                self::assertArrayHasKey('enabled', $data);
                self::assertTrue($data['enabled']);

                return true;
            })
            ->once();

        $listener = new ActivateOnlineBookingConfirmationEmailTemplate(
            $this->transactionalMessagesRepository,
            $this->loggerMock
        );

        $listener->handle($this->event);
    }

    public function test_when_custom_online_booking_confirmation_email_does_not_exists_it_should_attempt_to_create_one_and_save_it_with_enabled_true(): void
    {
        $this->transactionalMessagesRepository
            ->shouldReceive('findByBranchAndIdentifier')
            ->withArgs(function (array $branch, Identifier $identifier) {
                self::assertSame('5e907adf8f5c6a3808971e14', $branch['Branch']['_id']);
                self::assertSame('ONLINE_CLASS_BOOKING_CONFIRMATION', $identifier->getValue());

                return true;
            })
            ->andReturnNull()
            ->once()
            ->getMock()
            ->shouldReceive('findByIdentifier')
            ->andReturn([
                'TransactionalMessagesDefault' => [
                    'enabled' => false,
                    'triggers' => [],
                ],
            ])
            ->once()
            ->getMock()
            ->shouldReceive('save')
            ->withArgs(function (array $data) {
                self::assertArrayHasKey('enabled', $data);
                self::assertTrue($data['enabled']);

                return true;
            })
            ->once();

        $listener = new ActivateOnlineBookingConfirmationEmailTemplate(
            $this->transactionalMessagesRepository,
            $this->loggerMock
        );

        $listener->handle($this->event);
    }
}
