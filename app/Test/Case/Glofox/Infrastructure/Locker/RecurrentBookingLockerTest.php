<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Infrastructure\Locker;

use Carbon\Carbon;
use Glofox\Domain\Locker\RecurrentBookingAlreadyLockedException;
use Glofox\Domain\Locker\RecurrentBookingLockerParams;
use Glofox\Infrastructure\Locker\RecurrentBookingLockerInterface;

\App::import('Test/Case', 'GlofoxTestCase');

class RecurrentBookingLockerTest extends \GlofoxTestCase
{
    public $fixtures = [];
    private RecurrentBookingLockerInterface $locker;

    public function setUp(): void
    {
        parent::setUp();

        $this->locker = app()->make(RecurrentBookingLockerInterface::class);
    }

    public function tearDown(): void
    {
        parent::tearDown();

        $this->locker->unlock($this->getBookingLockerParams());
    }

    public function testItLoadDependencySuccessfully(): void
    {
        self::assertInstanceOf(RecurrentBookingLockerInterface::class, $this->locker);
    }

    public function testItShouldCreateLockKeyForRecurrentBooking(): void
    {
        $this->locker->lock($this->getBookingLockerParams());
    }

    public function testItShouldUnlockKeyForRecurrentBooking(): void
    {
        $this->locker->lock($this->getBookingLockerParams());
        $this->locker->unlock($this->getBookingLockerParams());
    }

    public function testItFailsToLockWhenLockKeyAlreadyExist(): void
    {
        $lockParams = $this->getBookingLockerParams();
        $this->locker->lock($lockParams);

        $this->expectException(RecurrentBookingAlreadyLockedException::class);
        $this->expectExceptionCode(400);
        $this->expectExceptionMessage('Recurrent booking is already locked');

        $this->locker->lock($lockParams);
    }

    private function getBookingLockerParams(): RecurrentBookingLockerParams
    {
        return new RecurrentBookingLockerParams(
            'programId',
            'scheduleCode',
            'userId',
            Carbon::parse('2024-10-01 10:00:00'),
            Carbon::parse('2024-10-01 10:15:00')
        );
    }
}
