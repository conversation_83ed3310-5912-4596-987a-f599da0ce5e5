parameters:
  - schema:
      type: string
      pattern: '^[a-f\d]{24}$'
    name: branchId
    in: path
    required: true
    description: The branch ID the accesses list is fetched for.
get:
  tags:
   - Accesses
  summary: ''
  operationId: accesses-list
  description: |
    This endpoint returns paginated list of the accesses for the specified studio.
    The maximum date range used to fetch data is 1 day.
    Accesses are sorted by entry date in descending mode. If the entry date is not defined, 
    than the date when access data was added into the system is considered.
  x-internal: true
  security:
    - coreapi_auth: [ ]
  parameters:
    - schema:
        type: integer
      name: utc-start-time
      in: query
      required: true
      description: UTC timestamp of the starting point to fetch accesses.
    - schema:
        type: integer
      name: utc-end-time
      in: query
      required: true
      description: |
        UTC timestamp of the ending point to fetch accesses. 
        It must be at least +1 second more than utc-start-time value. 
        Maximum value is +1 day to the utc-start-time value.
    - schema:
        type: integer
        minimum: 1
        default: 1
      name: page
      in: query
      required: false
    - schema:
        type: integer
        minimum: 1
        maximum: 100
        default: 50
      name: limit
      in: query
      required: false
  responses:
    '200':
      description: OK
      content:
        application/json:
          schema:
            allOf:
              - $ref: '../../schemas/pagination.yml'
              - type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '../../schemas/access.yml'
