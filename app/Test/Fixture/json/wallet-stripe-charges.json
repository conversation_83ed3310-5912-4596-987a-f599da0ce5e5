[{"StripeCharge": {"_id": "5d94af0c5c59560001960b10", "className": "com.glofox.payments.domain.entity.StripeCharge", "metadata": {"namespace": "paymentsyoga", "branch_id": "5bb1d2b437e2540327634a23", "glofox_event": "wallet_top_up", "user_id": "5d94abe0612d020039430319", "user_name": "Test 1", "description": "test card top up", "payment_method": "credit_card", "wallet_balance_after": 103.33, "quantity": 0}, "amount": 100.99, "currency": "USD", "customer": "132649", "paid": true, "amount_refunded": 0.0, "description": "Wallet top up: test card top up", "captured": true, "modified": "2019-10-02 14:07:08", "created": "2019-10-02 14:07:08"}}, {"StripeCharge": {"_id": "5d94a840e3918800014ef68c", "className": "com.glofox.payments.domain.entity.StripeCharge", "id": "616015", "transaction_provider_id": "616015", "metadata": {"namespace": "paymentsyoga", "branch_id": "5bb1d2b437e2540327634a23", "glofox_event": "subscription_payment", "user_id": "5d94a805dd08d1003d5649fd", "user_name": "Test 2", "description": "test card debit", "payment_method": "wallet", "wallet_balance_after": 25.59, "quantity": 0}, "amount": 22.34, "currency": "USD", "customer": "132573", "paid": true, "amount_refunded": 0.0, "description": "Wallet Debit: test card debit", "captured": true, "modified": "2019-10-02 13:38:08", "created": "2019-10-02 13:38:08"}}, {"StripeCharge": {"_id": "5d94a840e3918800014ef68c", "className": "com.glofox.payments.domain.entity.StripeCharge", "id": "616015", "transaction_provider_id": "616015", "metadata": {"namespace": "paymentsyoga", "branch_id": "5bb1d2b437e2540327634a23", "glofox_event": "subscription_payment", "user_id": "5d94a805dd08d1003d5649fd", "user_name": "Test 2", "description": "test card debit", "payment_method": "WALLET", "wallet_balance_after": 25.59, "quantity": 0}, "amount": 22.34, "currency": "USD", "customer": "132573", "paid": true, "amount_refunded": 0.0, "description": "Wallet Debit: test card debit", "captured": true, "modified": "2019-10-02 13:37:08", "created": "2019-10-02 13:37:08"}}]