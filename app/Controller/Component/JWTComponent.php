<?php

use Emarref\Jwt\Exception\InvalidSignatureException;
use Glofox\Application;
use Glofox\Authentication\Exceptions\EmptyTokenException;
use Glofox\Authentication\Exceptions\InvalidTokenDataException;
use Glofox\Authentication\Jwt;
use Glofox\Authorization\Exceptions\DeactivatedUserException;
use Glofox\Domain\Authentication\Token\TokenGeneratorDto;
use Glofox\Domain\Authentication\TokenGenerator;
use Glofox\Domain\Branches\Search\Expressions\BranchId;
use Glofox\Domain\Http\Exceptions\UnauthorizedOperation;
use Glofox\Domain\Integrations\Contracts\TokenizableIntegration;
use Glofox\Domain\Users\Models\User as UserModel;
use Glofox\Domain\Users\Repositories\UsersRepository;
use Glofox\Repositories\FetchType;
use Glofox\Repositories\Search\Expressions\Shared\Id;
use MongoDB\BSON\UTCDateTime;
use Emarref\Jwt\Token;

App::uses('Component', 'Controller');

/**
 * Class JWTComponent.
 */
class JWTComponent extends Component
{
    public $User;
    public $Client;

    protected $app;

    /**
     * @var TokenGenerator
     */
    protected $tokenGenerator;

    /*!
     * Init JWT  * @param      Controller               $controller [description]
     * @return     [type]                               [description]
     */

    public function initialize(Controller $controller)
    {
        $this->app = Application::getInstance();

        $this->controller = $controller;
        $this->User = ClassRegistry::init('User');
        $this->Client = ClassRegistry::init('Client');
        $this->tokenGenerator = $this->app->make(TokenGenerator::class);
    }

    /*!
     * Generate valid auth token for rest requests and file download
     * @param      [type]                   $data    [description]
     * @param      string                   $expires [description]
     * @return     [type]                            [description]
     */

    /**
     * @param        $data
     * @param string $expires
     *
     * @return string
     */
    public function generate($data, $expires = '1 month')
    {
        $data = $data['User'] ?? $data;
        $dto = new TokenGeneratorDto($data);

        return $this->tokenGenerator->generate($dto, $expires);
    }

    /**
     * Analyze headers and deserialize.
     *
     * @param JWTObject $jwt optional - An instance of Emarref\Jwt\Jwt
     *
     * @return \Emarref\Jwt\Token|string|null [type]      [description]
     */
    public function getToken($jwt = null)
    {
        try {
            $jwt = ($jwt) ?: new Jwt();
            $authHeader = $this->getAuthHeader();

            return !empty($authHeader) ? $jwt->deserialize($authHeader) : null;
        } catch (Exception $e) {
            return null;
        }
    }

    /**
     * [verifyToken description].
     *
     * @param null $token
     * @param null $jwt
     *
     * @return bool [type]        [description]
     *
     * @throws \Glofox\Authentication\Exceptions\EmptyTokenException
     *
     * @internal param $ [type] $token [description]
     * @internal param $ [type] $jwt   [description]
     */
    public function verifyToken($token = null, $jwt = null)
    {
        if (!$token) {
            throw new EmptyTokenException();
        }

        $jwt = ($jwt) ?: new Jwt();
        $salts = explode(',', env('JWT_SALT_VERIFY'));
        $authorised = false;

        foreach ($salts as $salt) {
            $algorithm = new Emarref\Jwt\Algorithm\Hs256($salt);
            $encryption = Emarref\Jwt\Encryption\Factory::create($algorithm);
            $context = new Emarref\Jwt\Verification\Context($encryption);

            try {
                if ($jwt->verify($token, $context)) {
                    $authorised = true;
                    break;
                }
            } catch (Exception $e) {
                $authorised = false;
            }
        }

        if (!$authorised) {
            $message = $e->getMessage();
            throw new InvalidSignatureException($message);
        }

        // Throws an exception upon failure
        $this->isTokenDataValidOrFail($token);

        return true;
    }

    /**
     * @param \Emarref\Jwt\Token $token
     *
     * @throws InvalidTokenDataException
     */
    private function isTokenDataValidOrFail(Emarref\Jwt\Token $token)
    {
        $properties = $token->getPayload()
            ->getClaims()
            ->getIterator();

        if (!isset($properties['user'])) {
            throw new InvalidTokenDataException();
        }

        $userToken = $properties['user']->getValue();
        $userToken = collect($userToken);

        $userId = $userToken->get('_id');
        $branchId = $userToken->get('branch_id');
        $email = $userToken->get('email');
        $isGlofoxStaff = $userToken->get('isGlofoxStaff');

        if (!UserModel::isRealUserId(strtolower($userId))) {
            return;
        }

        if ($userToken->has('integrator')) {
            return;
        }

        /** @var UsersRepository $usersRepository */
        $usersRepository = app()->make(UsersRepository::class);

        /** @var UserModel|null $user */
        $user = $usersRepository->addCriteria(new Id($userId))
            ->addCriteria(new BranchId($branchId))
            ->withFetchType(FetchType::FIRST())
            ->skipCallbacks()
            ->find();

        if (!$user) {
            throw new InvalidTokenDataException();
        }

        if ($isGlofoxStaff) {
            return;
        }

        if (!$user->isActive()) {
            throw DeactivatedUserException::withUser($user->id());
        }

        $enabled = filter_var(
            env('IS_REVOKE_SESSION_ENABLED'),
            FILTER_VALIDATE_BOOLEAN
        );

        if($enabled){
            $passwordChangeDate = $user->getChangePassword();

            if($passwordChangeDate !== null){
                $this->validateTokenIssuedAfterPasswordChange($passwordChangeDate, $token);
            }
        }
    }

    private function validateTokenIssuedAfterPasswordChange(MongoDate $passwordChangeDate, Token $token): void
    {
        $iatClaim = $token->getPayload()->findClaimByName('iat');

        $iat = $iatClaim ? $iatClaim->getValue() : null;

        if ($iat !== null) {
            $passwordChangeDateTimestamp = $passwordChangeDate->toDateTime()->getTimestamp();
            if ($passwordChangeDateTimestamp > $iat) {
                throw new UnauthorizedException("Token expired");
            }
        }
    }

        /**
     * This method will validate is the token given is valid to access a given request, for this reason receives
     * the allowed roles, namespace and branch_id.
     *
     * @param array $allowedRoles an array of the roles that have access
     * @param string $namespace optional
     * @param [type] $branch_id    optional
     *
     * @return array
     */
    public function validate($allowedRoles = [], $namespace = null, $branchId = null)
    {
        // Legacy compatibility
        try {
            $result = $this->validateOrFail($allowedRoles, $namespace, $branchId);

            return [
                'success' => $result,
            ];
        } catch (UnsuccessfulOperation $e) {
            return [
                'success' => false,
                'code' => $e->getCode(),
                'message' => $e->getMessage(),
                'message_code' => $e->getMessageCode(),
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'code' => $e->getCode(),
                'message' => $e->getMessage(),
                'message_code' => $e->getMessage(),
            ];
        }
    }

    /**
     * Return the current user with this token.
     */
    public function getUserOrFail()
    {
        return $this->parseToken();
    }

    /**
     * Return the current user with this token.
     */
    public function getUser()
    {
        try {
            return $this->getUserOrFail();
        } catch (Exception $e) {
            return null;
            // throw $e;
            // return null;
        }
    }

    /**
     * Will determine is the current token belongs
     * to a super admin.
     *
     * @param [string] $bundle [the bundle that we you want to access]
     *
     * @return bool [description]
     */
    public function isSuperAdmin($bundle = null)
    {
        try {
            $tokenData = $this->parseToken();
            if (!empty($tokenData['isSuperAdmin']) && ($bundle == $tokenData['bundle'])) {
                return true;
            } else {
                $conditions = ['namespace' => $tokenData['namespace']];
                $params = ['conditions' => $conditions];
                $client = $this->Client->find('first', $params);
                if (($client['Client']['namespace'] == $bundle) && in_array(
                        $tokenData['type'],
                        ['ADMIN', 'SUPERADMIN']
                    )) {
                    return true;
                } else {
                    return false;
                }
            }

            return true;
        } catch (Exception $e) {
            return false;
        }
    }

    /*!
     * Obtain Token by deserializing request headers
     * @return     [type]                   [description]
     */

    /**
     * @return mixed
     *
     * @throws \Glofox\Authentication\Exceptions\AuthenticationException
     */
    public function parseToken()
    {
        try {
            $token = $this->getToken();

            if (!$token) {
                throw new EmptyTokenException();
            }

            $this->verifyToken($token, new Jwt());

            if ($token instanceof \Emarref\Jwt\Token) {
                $properties = $token->getPayload()->getClaims()->jsonSerialize();
                newrelic_add_custom_parameter('tokenProperties', $properties);
            }

            //Is token in header as Authorization?
            if (empty($token)) {
                throw new Exception('The token is empty');
            }
            $payload = $token->getPayload();
            $claims = $payload->getClaims();
            $properties = $claims->getIterator();
            //Is token well assembled ?
            if (empty($properties['user'])) {
                throw new Exception('The token payload claim properties is empty');
            }

            $user = $properties['user']->getValue();

            if ($user) {
                $valueToBeLogged = $user;

                if (is_array($user)) {
                    $valueToBeLogged = json_encode($valueToBeLogged, JSON_PARTIAL_OUTPUT_ON_ERROR);
                }

                if (is_object($user)) {
                    $valueToBeLogged = serialize($valueToBeLogged);
                }

                newrelic_add_custom_parameter('authenticatedUser', $valueToBeLogged);
            }
            $payload = $token->getPayload();
            $claims = $payload->getClaims();
            $properties = $claims->getIterator();
            //Is token well assembled ?
            if (empty($properties['user'])) {
                throw new Exception('The token payload claim properties is empty');
            }

            return $user;
        } catch (\Glofox\Authentication\Exceptions\AuthenticationException $e) {
            throw $e;
        } catch (Exception $e) {
            $message = 'Unauthorized - ' . $e->getMessage();
            throw new UnauthorizedException($message);
        }
    }

    /*!
     * Attempt token deserialization and extract direct content data
     * @param      [type]                   $token [description]
     * @return     [type]                          [description]
     */

    /**
     * @param $token
     *
     * @return mixed
     */
    public function deserialize($token)
    {
        try {
            $jwt = new Jwt();
            $token = $jwt->deserialize($token);
            //Is token in header as Authorization?
            if (empty($token)) {
                throw new Exception('The token is empty');
            }
            $payload = $token->getPayload();
            $claims = $payload->getClaims();
            $properties = $claims->getIterator();
            //Is token well assembled ?
            if (empty($properties['user'])) {
                throw new Exception('The token payload claim properties is empty');
            }

            return $properties['user']->getValue();
        } catch (Exception $e) {
            throw new UnauthorizedException();
        }
    }

    /**
     * Get the token from the Authorization header in the request
     * removing the "Bearer " prefix.
     *
     * @return string [description]
     */
    public function getAuthHeader()
    {
        $token = \Glofox\Request::capture()->getAuthenticationToken();

        return $token;
    }

    /**
     * @param array $allowedRoles
     * @param null $namespace
     * @param null $branchId
     *
     * @return bool
     *
     * @throws UnsuccessfulOperation
     */
    public function validateOrFail($allowedRoles = [], $namespace = null, $branchId = null)
    {
        // Legacy code moved without refactor.
        try {
            $jwt = new Jwt();
            $token = $this->getToken($jwt);

            $this->verifyToken($token, $jwt);

            if (!$token) {
                $e = new UnsuccessfulOperation('Token couldnt be fetched from request.');
                $e->setCode(403);
                $e->setMessageCode('INVALID_TOKEN_FROM_REQUEST');
                throw $e;
            }

            $allowedRoles = array_map('strtolower', $allowedRoles);

            // The superadmin should be able to do anything that an admin can do
            if (is_array($allowedRoles) && in_array('admin', $allowedRoles) && !in_array('superadmin', $allowedRoles)) {
                $allowedRoles[] = 'superadmin';
            }

            if (is_array($allowedRoles)) {
                foreach ($allowedRoles as &$uppercaseRole) {
                    $uppercaseRole = strtolower($uppercaseRole);
                }
            }

            $user = $this->parseToken();

            $allowed = (
                ('zappy' == $user['namespace'])
                && ($user['type'] == strtolower(UserType::ADMIN))
            ) ? true : false;

            foreach ($allowedRoles as $allowedRole) {
                $role = $this->User->get_role($allowedRole);

                // if (($user['type'] == $role) && ($user['namespace'] == $namespace)) {
                if (($user['type'] == $role)) {
                    if (!empty($branchId)) {
                        if (
                            ($user['branch_id'] == $branchId)
                            || (strtolower($user['type']) == strtolower(UserType::SUPERADMIN))
                        ) {
                            $allowed = true;
                        }
                    } else {
                        $allowed = true;
                    }
                }
            }

            if (!$allowed) {
                throw UnauthorizedOperation::becauseUnauthorizedAction();
            }

            return true;
        } catch (Emarref\Jwt\Exception\ExpiredException $e) {
            $e = new UnsuccessfulOperation('Your session has expired. Please logout and login again.', null, $e);
            $e->setCode(403);
            $e->setMessageCode('TOKEN_EXPIRED');
            throw $e;
        } catch (UnsuccessfulOperation $e) {
            throw $e;
        } catch (Exception $e) {
            $e = new UnsuccessfulOperation($e->getMessage(), null, $e);
            $e->setMessageCode('UNEXPECTED_TOKEN_EXCEPTION');
            throw $e;
        }
    }

    /**
     * [generateFromIntegration description].
     *
     * @param TokenizableIntegration $integration [description]
     * @param string $namespace [description]
     * @param                        $branchId
     *
     * @return string [type]                   [description]
     *
     * @internal param string $branch_id [description]
     */
    public function generateFromIntegration(TokenizableIntegration $integration, $namespace, $branchId)
    {
        $adminData = $integration->getAdminData($namespace, $branchId);
        $token = $this->generate($adminData);
        // HACK to make it work when the request doesn't come from the Integrations Controller
        $_SERVER['HTTP_AUTHORIZATION'] = $token;

        return $token;
    }


    public function getTokenIssuedAt() : int
    {
        $token = $this->getToken();
        if (empty($token)) {
            return 0;
        }
        return $token->getPayload()->findClaimByName('iat')->getValue();
    }


    private function extractQueryParamFromRequest($key)
    {
        $request = $this->controller->request;
        if (is_object($request) && isset($request->query[$key])) {
            return $request->query[$key];
        }

        if (is_array($request) && isset($request['url'][$key])) {
            return $request['url'][$key];
        }
    }
}
