<?php

namespace Glofox\Payments\Providers\Gateway\ProtoParsers;

use Glofox\Payments\Entities\Card\Models\NextAction as ProviderNextAction;
use GRPC\Payments\AccountService\NextAction;

/**
 * Class NextActionParser.
 */
class NextActionParser
{
    /**
     * @param NextAction $nextAction
     *
     * @return ProviderNextAction
     */
    public function parseFromProtoMessage($nextAction): ProviderNextAction
    {
        $providerNextAction = new ProviderNextAction();
        $providerNextAction->setType($nextAction->getType())
            ->setRedirectURL($nextAction->getRedirectURL());

        return $providerNextAction;
    }
}
