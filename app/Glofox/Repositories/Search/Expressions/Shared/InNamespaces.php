<?php

namespace Glofox\Repositories\Search\Expressions\Shared;

use Illuminate\Support\Collection;
use Webmozart\Expression\Expr;
use Glofox\Repositories\Filters\Evaluable;
use Glofox\Repositories\Search\Expressions\Key;
use Glofox\Repositories\Search\Expressions\Conditionable;
use Glofox\Repositories\Search\Expressions\HasConditions;

/**
 * Class InNamespaces
 *
 * @package Glofox\Repositories\Search\Expressions\Shared
 */
class InNamespaces extends Key implements Evaluable, Conditionable
{
    use HasConditions;

    public function __construct(Collection $namespaces)
    {
        parent::__construct('namespace', Expr::in($namespaces->toArray()));
    }

    public function field() : string
    {
        return 'namespace';
    }
}
