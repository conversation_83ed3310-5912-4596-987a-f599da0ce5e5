<?php

declare(strict_types=1);

App::import('Test/Case', 'GlofoxTestCase');

use Glofox\Response\Models\Branches\Configuration\Webportal\Features\GeneralModel;

/**
 * Test cases
 * 1 Model contains all fields with data
 * 2 Missing fields and returns correct structure
 * 3 Additional fields and return correct structure
 * 4 Empty array fields and return correct structure.
 *
 * Class GeneralModelTest
 */
class GeneralModelTest extends GlofoxTestCase
{
    public $fixtures = [];

    /**
     * @dataProvider modelProvider
     */
    public function testModel($dataSent, $result): void
    {
        $model = new GeneralModel($dataSent);
        $this->assertEquals($model, $result);
    }

    public function modelProvider(): array
    {
        return [
            '1 Model contains all fields with data' => [
                [
                    'app_download_link' => false,
                    'classes' => false,
                    'courses' => true,
                    'facilities' => true,
                    'memberships' => false,
                    'show_past_events' => true,
                    'trainers' => true,
                ],
                new GeneralModel([
                    'app_download_link' => false,
                    'classes' => false,
                    'courses' => true,
                    'facilities' => true,
                    'memberships' => false,
                    'show_past_events' => true,
                    'trainers' => true,
                ]),
            ],
            '2 Missing fields and returns correct structure' => [
                [
                    //app_download_link, trainers missing
                    'classes' => false,
                    'courses' => true,
                    'facilities' => true,
                    'memberships' => false,
                    'show_past_events' => true,
                ],
                new GeneralModel([
                    'app_download_link' => false,
                    'classes' => false,
                    'courses' => true,
                    'facilities' => true,
                    'memberships' => false,
                    'show_past_events' => true,
                    'trainers' => false,
                ]),
            ],
            '3 Additional fields and return correct structure' => [
                [
                    'test' => true, // additional
                    'app_download_link' => false,
                    'classes' => false,
                    'courses' => true,
                    'facilities' => true,
                    'memberships' => false,
                    'show_past_events' => true,
                    'trainers' => true,
                ],
                new GeneralModel([
                    'app_download_link' => false,
                    'classes' => false,
                    'courses' => true,
                    'facilities' => true,
                    'memberships' => false,
                    'show_past_events' => true,
                    'trainers' => true,
                ]),
            ],
            '4 Empty array fields and return correct structure' => [
                [],
                new GeneralModel([
                    'app_download_link' => false,
                    'classes' => false,
                    'courses' => false,
                    'facilities' => false,
                    'memberships' => false,
                    'show_past_events' => false,
                    'trainers' => false,
                ]),
            ],
        ];
    }
}
