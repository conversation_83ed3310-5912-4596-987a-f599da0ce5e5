<?php

namespace Glofox\Payments\Exceptions;

class UnsupportedPaymentMethodTypeException extends \Exception
{
    protected $code = 400;

    protected $message = "Unsupported payment method type";

    public static function withPaymentMethod(string $paymentMethod)
    {
        return new self(
            sprintf(
                "[%s] does not match any of the supported payment methods",
                $paymentMethod,
            )
        );
    }
}
