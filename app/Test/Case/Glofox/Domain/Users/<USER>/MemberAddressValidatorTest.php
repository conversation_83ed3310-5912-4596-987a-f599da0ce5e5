<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\Users\Validation;

use App;
use Glofox\Domain\Users\Validation\MemberAddressValidator;
use GlofoxTestCase;

App::import('Test/Case', 'GlofoxTestCase');

class MemberAddressValidatorTest extends GlofoxTestCase
{
    public $fixtures = [];

    public function testNoErrorIfAddressDoesNotExist(): void
    {
        $payload = [];
        $result = app()->make(MemberAddressValidator::class)
            ->validate($payload);
        self::assertNull($result);
    }

    public function testIdShouldThrowErrorIfAddressIsNull(): void
    {
        $payload = [
            'address' => null
        ];
        $result = app()->make(MemberAddressValidator::class)
            ->validate($payload);
        self::assertEquals('ADDRESS_SHOULD_BE_OF_TYPE_ARRAY', $result);
    }

    public function testIdShouldThrowErrorIfAddressIsString()
    {
        $payload = [
            'address' => ''
        ];
        $result = app()->make(MemberAddressValidator::class)
            ->validate($payload);
        self::assertEquals('ADDRESS_SHOULD_BE_OF_TYPE_ARRAY', $result);
    }

    public function testIdShouldThrowErrorIfAddressIsInteger(): void
    {
        $payload = [
            'address' => 123131
        ];
        $result = app()->make(MemberAddressValidator::class)
            ->validate($payload);
        self::assertEquals('ADDRESS_SHOULD_BE_OF_TYPE_ARRAY', $result);
    }

    public function testIdShouldThrowErrorIfAddressIsBoolean(): void
    {
        $payload = [
            'address' => true
        ];
        $result = app()->make(MemberAddressValidator::class)
            ->validate($payload);
        self::assertEquals('ADDRESS_SHOULD_BE_OF_TYPE_ARRAY', $result);
    }

    public function testIdShouldThrowErrorIfAddressArrayDoesNotContainStreet(): void
    {
        $payload = [
            'address' => [
                'city' => 'test',
                'state' => '',
                'country' => 'test',
                'postal_code' => '',
                'country_code' => ''
            ]
        ];
        $result = app()->make(MemberAddressValidator::class)
            ->validate($payload);
        self::assertEquals('ADDRESS_SHOULD_CONTAIN_STREET_NAME_AS_STRING', $result);
    }

    public function testIdShouldThrowErrorIfAddressStreetIsNotString(): void
    {
        $payload = [
            'address' => [
                'street' => 123,
                'city' => 'test',
                'state' => '',
                'country' => 'test',
                'postal_code' => '',
                'country_code' => ''
            ]
        ];
        $result = app()->make(MemberAddressValidator::class)
            ->validate($payload);
        self::assertEquals('ADDRESS_SHOULD_CONTAIN_STREET_NAME_AS_STRING', $result);
    }

    public function testIdShouldThrowErrorIfAddressArrayDoesNotContainCity(): void
    {
        $payload = [
            'address' => [
                'street' => 'test',
                'state' => '',
                'country' => 'test',
                'postal_code' => '',
                'country_code' => ''
            ]
        ];
        $result = app()->make(MemberAddressValidator::class)
            ->validate($payload);
        self::assertEquals('ADDRESS_SHOULD_CONTAIN_CITY_NAME_AS_STRING', $result);
    }

    public function testIdShouldThrowErrorIfAddressCityIsNotString(): void
    {
        $payload = [
            'address' => [
                'street' => 'test',
                'city' => 123,
                'state' => '',
                'country' => 'test',
                'postal_code' => '',
                'country_code' => ''
            ]
        ];
        $result = app()->make(MemberAddressValidator::class)
            ->validate($payload);
        self::assertEquals('ADDRESS_SHOULD_CONTAIN_CITY_NAME_AS_STRING', $result);
    }

    public function testIdShouldThrowErrorIfAddressArrayDoesNotContainState(): void
    {
        $payload = [
            'address' => [
                'street' => 'test',
                'city' => 'test',
                'country' => 'test',
                'postal_code' => '',
                'country_code' => ''
            ]
        ];
        $result = app()->make(MemberAddressValidator::class)
            ->validate($payload);
        self::assertEquals('ADDRESS_SHOULD_CONTAIN_STATE_NAME_AS_STRING', $result);
    }

    public function testIdShouldThrowErrorIfAddressStateIsNotString(): void
    {
        $payload = [
            'address' => [
                'street' => 'test',
                'city' => '',
                'state' => 123,
                'country' => 'test',
                'postal_code' => '',
                'country_code' => ''
            ]
        ];
        $result = app()->make(MemberAddressValidator::class)
            ->validate($payload);
        self::assertEquals('ADDRESS_SHOULD_CONTAIN_STATE_NAME_AS_STRING', $result);
    }

    public function testIdShouldThrowErrorIfAddressArrayDoesNotContainCountry(): void
    {
        $payload = [
            'address' => [
                'street' => 'test',
                'city' => 'test',
                'state' => 'test',
                'postal_code' => '',
                'country_code' => ''
            ]
        ];
        $result = app()->make(MemberAddressValidator::class)
            ->validate($payload);
        self::assertEquals('ADDRESS_SHOULD_CONTAIN_COUNTRY_NAME_AS_STRING', $result);
    }

    public function testIdShouldThrowErrorIfAddressCountryIsNotString(): void
    {
        $payload = [
            'address' => [
                'street' => 'test',
                'city' => '',
                'state' => 'test',
                'country' => 123,
                'postal_code' => '',
                'country_code' => ''
            ]
        ];
        $result = app()->make(MemberAddressValidator::class)
            ->validate($payload);
        self::assertEquals('ADDRESS_SHOULD_CONTAIN_COUNTRY_NAME_AS_STRING', $result);
    }

    public function testIdShouldThrowErrorIfAddressArrayDoesNotContainPostalCode(): void
    {
        $payload = [
            'address' => [
                'street' => 'test',
                'city' => 'test',
                'state' => 'test',
                'country' => '',
                'country_code' => ''
            ]
        ];
        $result = app()->make(MemberAddressValidator::class)
            ->validate($payload);
        self::assertEquals('ADDRESS_SHOULD_CONTAIN_POSTAL_CODE_AS_STRING', $result);
    }

    public function testIdShouldThrowErrorIfAddressPostalCodeIsNotString(): void
    {
        $payload = [
            'address' => [
                'street' => 'test',
                'city' => '',
                'state' => 'test',
                'country' => '',
                'postal_code' => 123,
                'country_code' => ''
            ]
        ];
        $result = app()->make(MemberAddressValidator::class)
            ->validate($payload);
        self::assertEquals('ADDRESS_SHOULD_CONTAIN_POSTAL_CODE_AS_STRING', $result);
    }

    public function testIdShouldThrowErrorIfAddressArrayDoesNotContainCountryCode(): void
    {
        $payload = [
            'address' => [
                'street' => 'test',
                'city' => 'test',
                'state' => 'test',
                'country' => '',
                'postal_code' => '',
            ]
        ];
        $result = app()->make(MemberAddressValidator::class)
            ->validate($payload);
        self::assertEquals('ADDRESS_SHOULD_CONTAIN_COUNTRY_CODE_AS_STRING', $result);
    }

    public function testIdShouldThrowErrorIfAddressCountryCodeIsNotString(): void
    {
        $payload = [
            'address' => [
                'street' => 'test',
                'city' => '',
                'state' => 'test',
                'country' => '',
                'postal_code' => '',
                'country_code' => 123
            ]
        ];
        $result = app()->make(MemberAddressValidator::class)
            ->validate($payload);
        self::assertEquals('ADDRESS_SHOULD_CONTAIN_COUNTRY_CODE_AS_STRING', $result);
    }

    public function testIdShouldThrowErrorIfAddressCountryCodeIsNotTheCorrectLength(): void
    {
        $payload = [
            'address' => [
                'street' => 'test',
                'city' => '',
                'state' => 'test',
                'country' => '',
                'postal_code' => '',
                'country_code' => '22222'
            ]
        ];
        $result = app()->make(MemberAddressValidator::class)
            ->validate($payload);
        self::assertEquals('ADDRESS_SHOULD_CONTAIN_A_CORRECT_COUNTRY_CODE', $result);
    }

    public function testIdShouldNotThrowErrorIfAddressArrayIsComplete(): void
    {
        $payload = [
            'address' => [
                'street' => 'test',
                'city' => 'test',
                'state' => '',
                'country' => 'test',
                'postal_code' => '',
                'country_code' => ''
            ]
        ];
        $result = app()->make(MemberAddressValidator::class)
            ->validate($payload);
        self::assertNull($result);
    }
}
