<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\Programs\Transformers;

use App;
use Glofox\Domain\Facilities\Models\Facility;
use Glofox\Domain\Facilities\Repositories\FacilitiesRepository;
use Glofox\Domain\Programs\Models\Program;
use Glofox\Domain\Programs\Transformers\AddVirtualOnlineFieldTransformer;
use GlofoxTestCase;
use Mockery;

App::import('Test/Case', 'GlofoxTestCase');

class AddVirtualOnlineFieldTransformerTest extends GlofoxTestCase
{
    public $fixtures = [];
    /** @var FacilitiesRepository */
    private $facilitiesRepository;

    public function setUp()
    {
        parent::setUp();

        $this->facilitiesRepository = Mockery::mock(FacilitiesRepository::class);
    }

    public function tearDown()
    {
        parent::tearDown();

        Mockery::close();
    }

    public function test_it_adds_the_is_online_field_to_an_array_of_programs(): void
    {
        $this->facilitiesRepository
            ->shouldReceive('addCriteria')
            ->andReturnSelf()
            ->getMock()
            ->shouldReceive('find')
            ->andReturn([
                new Facility([
                    '_id' => new \MongoId('5e7cbc9b3d36311bf52c63fb'),
                    'is_online' => true,
                ]),
                new Facility([
                    '_id' => new \MongoId('5e7cbcbb3d36311bf52c63fc'),
                    'is_online' => false,
                ]),
            ]);

        $service = new AddVirtualOnlineFieldTransformer($this->facilitiesRepository);
        $result = $service->execute(collect([
            Program::make([
                '_id' => 'program1',
                'schedule_default' => [
                    'facility' => '5e7cbc9b3d36311bf52c63fb',
                ],
            ]),
            Program::make([
                '_id' => 'program2',
                'schedule_default' => [
                    'facility' => '5e7cbcbb3d36311bf52c63fc',
                ],
            ]),
        ]));

        self::assertSame('program1', $result[0]['_id']);
        self::assertSame('5e7cbc9b3d36311bf52c63fb', $result[0]['schedule_default']['facility']);
        self::assertTrue($result[0]['is_online']);

        self::assertSame('program2', $result[1]['_id']);
        self::assertSame('5e7cbcbb3d36311bf52c63fc', $result[1]['schedule_default']['facility']);
        self::assertFalse($result[1]['is_online']);
    }

    public function test_it_adds_a_false_virtual_field_if_the_program_has_no_facility_set(): void
    {
        $this->facilitiesRepository
            ->shouldReceive('addCriteria')
            ->andReturnSelf()
            ->getMock()
            ->shouldReceive('find')
            ->andReturn([]);

        $service = new AddVirtualOnlineFieldTransformer($this->facilitiesRepository);
        $result = $service->execute(collect([
            Program::make([
                '_id' => 'program1',
                'schedule_default' => [],
            ]),
            Program::make([
                '_id' => 'program2',
                'schedule_default' => [],
            ]),
        ]));

        self::assertSame('program1', $result[0]['_id']);
        self::assertFalse($result[0]['is_online']);

        self::assertSame('program2', $result[1]['_id']);
        self::assertFalse($result[1]['is_online']);
    }

    public function test_it_adds_a_false_virtual_field_if_the_facility_is_null_in_program_data(): void
    {
        $this->facilitiesRepository
            ->shouldReceive('addCriteria')
            ->andReturnSelf()
            ->getMock()
            ->shouldReceive('find')
            ->andReturn([]);

        $service = new AddVirtualOnlineFieldTransformer($this->facilitiesRepository);
        $result = $service->execute(collect([
            Program::make([
                '_id' => 'program1',
                'schedule_default' => [
                    'facility' => null,
                ],
            ]),
        ]));

        self::assertSame('program1', $result[0]['_id']);
        self::assertFalse($result[0]['is_online']);
    }

    public function test_it_works_if_the_facility_field_of_program_is_an_array(): void
    {
        $this->facilitiesRepository
            ->shouldReceive('addCriteria')
            ->andReturnSelf()
            ->getMock()
            ->shouldReceive('find')
            ->andReturn([
                new Facility([
                    '_id' => new \MongoId('5e7cbc9b3d36311bf52c63fb'),
                    'is_online' => true,
                ]),
            ]);

        $service = new AddVirtualOnlineFieldTransformer($this->facilitiesRepository);
        $result = $service->execute(collect([
            Program::make([
                '_id' => 'program1',
                'schedule_default' => [
                    'facility' => ['5e7cbc9b3d36311bf52c63fb'],
                ],
            ]),
        ]));

        self::assertSame('program1', $result[0]['_id']);
        self::assertSame(['5e7cbc9b3d36311bf52c63fb'], $result[0]['schedule_default']['facility']);
        self::assertTrue($result[0]['is_online']);
    }
}
