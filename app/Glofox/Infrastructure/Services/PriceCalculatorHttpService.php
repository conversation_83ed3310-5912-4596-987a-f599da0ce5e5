<?php

declare(strict_types=1);

namespace Glofox\Infrastructure\Services;

use Glofox\Domain\Branches\Models\Branch;
use Glofox\Domain\SalesTaxes\Services\PriceCalculatorServiceInterface;
use Glofox\Payments\Util;
use Glofox\Request;
use Illuminate\Http\JsonResponse;
use Psr\Log\LoggerInterface;
use Glofox\Domain\SalesTaxes\Services\PriceCalculatorClient;

class PriceCalculatorHttpService implements PriceCalculatorServiceInterface
{
    /** @var Client */
    private $httpClient;

    private \Psr\Log\LoggerInterface $logger;

    /** @var Request */
    private $request;

    public function __construct(PriceCalculatorClient $httpClient, LoggerInterface $logger)
    {
        $this->httpClient = $httpClient;
        $this->logger = $logger;
        $this->request = Request::createFromGlobals();
    }

    public static function convertTaxesResponse(array $response): array
    {
        $discounts = $response['discounts'] ?? [];

        return [
            'discounts' => array_map(static fn(array $product) => [
                'product_price' => Util::convertFromCents($product['product_price']),
                'discounts_total' => Util::convertFromCents($product['discounts_total']),
                'discounted_price' => Util::convertFromCents($product['discounted_price']),
                'applied_discounts' => array_map(static function (array $discount) {
                    $dividerPower = $discount['rate_type'] == 'percentage' ? 3 : 2;

                    return [
                        'id' => $discount['id'],
                        'name' => $discount['name'],
                        'promo_code' => $discount['promo_code'],
                        'num_cycles' => $discount['num_cycles'],
                        'rate_type' => $discount['rate_type'],
                        'rate_value' => Util::convertFromCents($discount['rate_value'], $dividerPower),
                        'discount_amount' => Util::convertFromCents($discount['discount_amount']),
                    ];
                }, $product['applied_discounts'] ?? []),
            ], $discounts['products'] ?? []),
            'products' => array_map(static fn(array $product) => [
                'product_price' => Util::convertFromCents($product['product_price']),
                'net_price' => Util::convertFromCents($product['net_price']),
                'total_price' => Util::convertFromCents($product['total_price']),
                'tax_total' => Util::convertFromCents($product['tax_total']),
                'applied_taxes' => array_map(static fn(array $tax) => [
                    'id' => $tax['id'],
                    'name' => $tax['name'],
                    'rate' => Util::convertFromCents($tax['rate'], 3),
                    'price' => Util::convertFromCents($tax['price']),
                ], $product['applied_taxes'] ?? []),
            ], $response['products'] ?? []),
        ];
    }

    public static function convertPriceBreakdownResponse(array $response): array
    {
        $discounts = $response['discounts'] ?? [];
        $taxes = $response['taxes'] ?? [];

        return [
            'discounts' => [
                'products' => array_map(static fn(array $product) => [
                    'product_price' => Util::convertFromCents($product['product_price']),
                    'discounts_total' => Util::convertFromCents($product['discounts_total']),
                    'discounted_price' => Util::convertFromCents($product['discounted_price']),
                    'applied_discounts' => array_map(static function (array $discount) {
                        $dividerPower = $discount['rate_type'] == 'percentage' ? 3 : 2;

                        return [
                            'id' => $discount['id'],
                            'name' => $discount['name'],
                            'promo_code' => $discount['promo_code'],
                            'num_cycles' => $discount['num_cycles'],
                            'rate_type' => $discount['rate_type'],
                            'rate_value' => Util::convertFromCents($discount['rate_value'], $dividerPower),
                            'discount_amount' => Util::convertFromCents($discount['discount_amount']),
                        ];
                    }, $product['applied_discounts'] ?? []),
                ], $discounts['products'] ?? []),
            ],
            'taxes' => [
                'products' => array_map(static fn(array $product) => [
                    'product_price' => Util::convertFromCents($product['product_price']),
                    'net_price' => Util::convertFromCents($product['net_price']),
                    'total_price' => Util::convertFromCents($product['total_price']),
                    'tax_total' => Util::convertFromCents($product['tax_total']),
                    'applied_taxes' => array_map(static fn(array $tax) => [
                        'id' => $tax['id'],
                        'name' => $tax['name'],
                        'rate' => Util::convertFromCents($tax['rate'], 3),
                        'price' => Util::convertFromCents($tax['price']),
                    ], $product['applied_taxes'] ?? []),
                ], $taxes['products'] ?? []),
            ],
        ];
    }

    public function install(Branch $branch, array $body): void
    {
        $this->logger->info(sprintf('Requesting Sales Tax installation for branch id %s', $branch->id()));

        $params = ['studioId' => $branch->id()];

        $response = $this->httpClient->post('INSTALL_URI', $params, $body);

        $this->logger->info(sprintf('PriceCalculatorService installed successfully for branch id %s', $branch->id()));
    }

    public function uninstall(Branch $branch): void
    {
        // TODO: Implement uninstall() method.
    }

    public function createTax(Branch $branch, string $body): JsonResponse
    {
        $this->logger->info(sprintf('Requesting tax creation for branch id %s', $branch->id()));

        $params = ['studioId' => $branch->id()];

        $response = $this->httpClient->post(
            'TAXES_URI',
            $params,
            $body
        );

        $this->logger->info(sprintf('Taxes created successfully for branch id %s', $branch->id()));

        return $response;
    }

    public function listTaxes(Branch $branch): JsonResponse
    {
        $this->logger->info(sprintf('Requesting Taxes list for branch id %s', $branch->id()));

        $params = ['studioId' => $branch->id()];

        $response = $this->httpClient->get('TAXES_URI', $params);

        $this->logger->info(sprintf('Taxes list requested successfully for branch id %s', $branch->id()));

        return $response;
    }

    public function updateTax(Branch $branch, string $taxId, string $body): JsonResponse
    {
        $this->logger->info(sprintf('Requesting update tax for branch id %s tax id %s', $branch->id(), $taxId));

        $params = ['studioId' => $branch->id(), 'taxId' => $taxId];

        $response = $this->httpClient->post(
            'UPDATE_TAX',
            $params,
            $body
        );

        $this->logger->info(sprintf('Tax successfully updated for branch id %s tax id %s', $branch->id(), $taxId));

        return $response;
    }

    public function createAssignedTaxes(Branch $branch, string $body): JsonResponse
    {
        $this->logger->info(sprintf('Requesting assigned tax creation for branch id %s', $branch->id()));
        
        $params = ['studioId' => $branch->id()];

        $response = $this->httpClient->post(
            'ASSIGNED_TAXES',
            $params,
            $body
        );

        $this->logger->info(sprintf('Assigned taxes created successfully for branch id %s', $branch->id()));

        return $response;
    }

    public function deleteTax(Branch $branch, string $taxId): JsonResponse
    {
        $this->logger->info(sprintf('Requesting delete tax for branch id %s tax id %s', $branch->id(), $taxId));

        $params = ['studioId' => $branch->id(), 'taxId' => $taxId];

        $body = json_encode([]);

        $response = $this->httpClient->delete(
            'DELETE_TAX',
            $params,
            $body
        );

        $this->logger->info(sprintf('Tax successfully updated for branch id %s tax id %s', $branch->id(), $taxId));

        return $response;
    }

    public function setAssignedTaxes(Branch $branch, string $taxId, string $body): JsonResponse
    {
        $this->logger->info(sprintf('Requesting assigned tax set for branch id %s tax id %s', $branch->id(), $taxId));

        $params = ['studioId' => $branch->id(), 'taxId' => $taxId];

        $response = $this->httpClient->post(
            'SET_ASSIGNED_TAXES',
            $params,
            $body
        );

        $this->logger->info(sprintf('Assigned taxes set successfully for branch id %s tax id %s', $branch->id(), $taxId));

        return $response;
    }

    public function setAssignedTaxesByItem(Branch $branch, string $serviceType, string $serviceIdentifier, string $body): JsonResponse
    {
        $this->logger->info(sprintf('Setting assigned tax set for branch id %s serviceType %s serviceIdentifier %s', $branch->id(), $serviceType, $serviceIdentifier));

        $params = ['studioId' => $branch->id(), 'serviceType' => $serviceType, 'serviceId' => $serviceIdentifier];

        $response = $this->httpClient->post(
            'SET_ASSIGNED_TAXES_BY_ITEM',
            $params,
            $body
        );

        $this->logger->info(sprintf('Assigned taxes set successfully for branch id %s serviceType %s serviceIdentifier %s', $branch->id(), $serviceType, $serviceIdentifier));

        return $response;
    }

    public function listAssignedTaxes(Branch $branch): JsonResponse
    {
        $this->logger->info(sprintf('Fetching assigned taxes for branch id %s', $branch->id()));

        $params = ['studioId' => $branch->id()];

        $response = $this->httpClient->get('ASSIGNED_TAXES', $params);

        $this->logger->info(sprintf('Successfully fetched assigned taxes for branch id %s', $branch->id()));

        return $response;
    }

    public function createTransaction(string $branchId, array $body): JsonResponse
    {
        $requestId = $this->request->getCurrentRequestUuid();
        $this->logger->info('request to create transaction in sales-tax', compact('branchId', 'requestId', 'body'));

        $params = ['studioId' => $branchId];

        $response = $this->httpClient->post(
            'CREATE_TRANSACTION',
            $params,
            $body,
            [
                'x-request-id' => $requestId
            ]
        );

        return $response;
    }

    public function updateTaxSettings(string $branchId, string $body): JsonResponse
    {
        $this->logger->info('request to update tax settings in sales-tax', compact('branchId', 'body'));

        $params = ['studioId' => $branchId];

        $response = $this->httpClient->post(
            'TAX_SETTINGS',
            $params,
            $body,
        );

        return $response;
    }

    public function getStudioTaxSettings(string $branchId): JsonResponse
    {
        $this->logger->info(sprintf('Requesting tax settings for branch id %s', $branchId));

        $params = ['studioId' => $branchId];

        $response = $this->httpClient->get('TAX_SETTINGS', $params);

        $this->logger->info(sprintf('Taxes settings requested successfully for branch id %s', $branchId));

        return $response;
    }

    public function calculateTaxes(string $branchId, string $body, array $options = []): JsonResponse
    {
        $this->logger->info('request to calculate taxes without saving them', compact('branchId', 'body'));

        $queryParams = [];

        if (isset($options['discounts'])) {
            $queryParams['discounts'] = $options['discounts'];
        }

        if (isset($options['taxmode'])) {
            $queryParams['taxmode'] = $options['taxmode'];
        }

        $params = ['studioId' => $branchId];

        $response = $this->httpClient->post(
            'CALCULATE_TAXES',
            $params,
            $body,
            $queryParams,
        );

        return $response;
    }

    public function getPriceBreakdown(string $branchId, string $body): JsonResponse
    {
        $this->logger->info('request to calculate taxes without saving them', compact('branchId', 'body'));

        $params = ['studioId' => $branchId];

        $response = $this->httpClient->post(
            'CALCULATE_TAXES',
            $params,
            $body
        );
        
        return $response;
    }

    public function getStudioDiscounts(string $branchId): JsonResponse
    {
        $this->logger->info(sprintf('Requesting discount settings for branch id %s', $branchId));

        $params = ['studioId' => $branchId];

        $response = $this->httpClient->get('DISCOUNT_SETTINGS', $params);

        $this->logger->info(sprintf('Discount settings requested successfully for branch id %s', $branchId));

        return $response;
    }

    public function listAssignedTaxesV3(string $branchId): JsonResponse
    {
        $this->logger->info(sprintf('Fetching assigned taxes for branch id %s', $branchId));

        $params = ['studioId' => $branchId];

        $response = $this->httpClient->get('GET_ASSIGNED_TAXES_V2', $params);

        $this->logger->info(sprintf('Successfully fetched assigned taxes for branch id %s', $branchId));

        return $response;
    }

    public function getAssignedTaxesByTaxId(string $branchId, string $taxId): JsonResponse
    {
        $this->logger->info(sprintf('Fetching assigned taxes for branch id %s tax id $s', $branchId, $taxId));

        $params = ['studioId' => $branchId, 'taxId' => $taxId];

        $response = $this->httpClient->get('GET_ASSIGNED_TAXES_BY_TAX_ID', $params);

        $this->logger->info(sprintf('Successfully fetched assigned taxes for branch id %s tax id %s', $branchId, $taxId));

        return $response;
    }
}
