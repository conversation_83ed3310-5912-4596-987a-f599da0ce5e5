get:
  tags:
    - Reports
  summary: ''
  operationId: sales-metrics
  description: This endpoint returns sales metrics of a given branch
  x-internal: true
  security:
    - coreapi_auth: [ ]
  parameters:
    - schema:
        type: string
      name: branchId
      in: path
      required: true
    - schema:
        type: string
        format: date
      in: query
      required: true
      name: endPeriod
    - schema:
        type: string
        format: date
      in: query
      required: true
      name: startPeriod
  responses:
    '200':
      description: OK
      content:
        application/json:
          schema:
            $ref: "../../schemas/sales_metrics.yml"