<?php

namespace Glofox\Payments\Providers\Gateway\Merchants\DataModellers;

use Glofox\Domain\Branches\Models\Branch;
use Glofox\Domain\PaymentProviders\Models\PaymentProvider;
use Illuminate\Support\Collection;

/**
 * Interface DataModelerContract.
 */
interface DataModellerContract
{
    /**
     * @param PaymentProvider $paymentProvider
     * @param Branch          $branch
     * @param Collection      $payload
     *
     * @return Collection
     */
    public function modelForCreating(PaymentProvider $paymentProvider, Branch $branch, Collection $payload): Collection;

    /**
     * @param PaymentProvider $paymentProvider
     * @param Branch          $branch
     * @param Collection      $payload
     *
     * @return Collection
     */
    public function modelForUpdating(PaymentProvider $paymentProvider, Branch $branch, Collection $payload): Collection;

    /**
     * @param PaymentProvider $paymentProvider
     * @param Branch          $branch
     *
     * @return Collection
     */
    public function modelForRegisterUrl(PaymentProvider $paymentProvider, Branch $branch): Collection;
}
