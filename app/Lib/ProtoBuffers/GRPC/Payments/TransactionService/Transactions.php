<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: action_request.proto

namespace GRPC\Payments\TransactionService;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>grpc.Transactions</code>
 */
class Transactions extends \Google\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>repeated .grpc.Transaction Transactions = 1;</code>
     */
    private $Transactions;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type array<\GRPC\Payments\TransactionService\Transaction>|\Google\Protobuf\Internal\RepeatedField $Transactions
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\ActionRequest::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>repeated .grpc.Transaction Transactions = 1;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getTransactions()
    {
        return $this->Transactions;
    }

    /**
     * Generated from protobuf field <code>repeated .grpc.Transaction Transactions = 1;</code>
     * @param array<\GRPC\Payments\TransactionService\Transaction>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setTransactions($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::MESSAGE, \GRPC\Payments\TransactionService\Transaction::class);
        $this->Transactions = $arr;

        return $this;
    }

}

