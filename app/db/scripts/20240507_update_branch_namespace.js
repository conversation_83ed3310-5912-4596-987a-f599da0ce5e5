//UPDATE - To update branch namespace see STAF-969 (https://glofox.atlassian.net/browse/STAF-969) for details

const branchIdsToUpdate = [
    "id",
];

const targetNamespace = 'namespace'

const modified = new Date();
let documentsUpdated = 0;

branchIdsToUpdate.forEach(branchIdString => {
    const branchId = ObjectId(branchIdString);
    const result = db.branches.updateOne(
        {
            _id: branchId,
        },
        {
            $set: { namespace: targetNamespace, modified: modified }
        }
    );

    if (result.modifiedCount > 0) {
        documentsUpdated++;
    }
});

print(`Total Documents Updated: ${documentsUpdated}`);
