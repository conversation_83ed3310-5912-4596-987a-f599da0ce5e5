openapi: 3.1.0
info:
  title: Glofox API Reference
  description: |
    Glofox's API is based on REST protocol. Our API enables external
    applications to access, create and edit users, memberships, credits,
    classes, bookings & payments. Each endpoint returns JSON responses.

    These headers must be included in every API call
    - x-glofox-branch-id
    - x-api-key
    - x-glofox-api-token

    The API key & token are only meant for backend integrations, not for browsers. If you need to make use of 
    this API key, the requests must be proxied through a backend, which in turn makes the authenticated call 
    to our API Gateway, thus keeping the key & token private.

    Regarding time stamps, most of the API is in UTC timezone. If the field is specified to be in "local" timezone,
    that's the timezone of the current location (branch) time.

    *Note:* To properly calculate the actual price of purchasing a resource (with discounts and taxes included), 
    you should pass the relevant info to the `/2.2/branches/{branchId}/price-breakdown` endpoint.
  version: 2.2.0
  contact:
    email: <EMAIL>
    name: Glofox Integrations
servers:
  - url: 'https://gf-api.aws.glofox.com/staging/'
    description: Staging server
  - url: 'https://gf-api.aws.glofox.com/prod/'
    description: Production server
tags:
  - name: Branches
    description: |
      A branch (a.k.a. studio or location) is a physical location. 
      They will have their own set of members and staff (even roaming ones can be shared).
      The ID it's the same that is send as a header in x-glofox-api-token
  - name: Users
    description: |
      A user is the entity representing one person. 
      These endpoints allow to get the Users data and user lists. 
      Specially focused on the ones of type Member.
  - name: Leads
    description: |
      Lead sourcing is a key pillar of CRM, providing precise data to optimize marketing spend and target communications more effectively. 
      These endpoints allow access to detailed information about lead sources, including their creation, retrieval, and management.
  - name: Memberships
    description: |
      The /memberships endpoint are the plans groups, what the user can purchase.
      The private ones can't be purchased by the member itself (usually used for staff memberships, special promotions, or similar).

      A membership is the main service that a user can have. 
      A user can have only one current membership at a given single time. 
      It can be for a single period (1 month) or in a subscription way (1 month for 12 months, or 1 month renewed till manually finished). 
      The membership usually means they have unrestricted access to the location. 
      The membership can be unlimited (type: time), so it allows any number of bookings, or restricted (time: time_classes) meaning it has door access for 1 month, but can book up to 10 times in that period. 
      A membership can be a roaming membership, meaning it grants access to more than 1 location.
      They can as well:
      • Purchase particular bookings
      • Purchase an add-on. An add-on is similar to a membership, but it’s over an existing membership. It has to have the same billing frequency.
      For details on membership lifecycle check the
  - name: Credits
    description: |
      A credit pack allows to book for different events. 
      These can be purchased independently or coming from a restricted membership.
  - name: Access
    description: |
      This allows management of access to the location, the access can be seen on locations and users, and there are several reports over them.
      From Glofox perspective, there are 2 things that grants access: an active membership, or a booking within the next hour
      If an access is created and there is a booking within the hour, attendance to the booking will be marked as well.

      Door access is something that's usually implemented on the partner side so it can be implemented with stricter rules if needed

      To get the members with door access, use the endpoints in the Users section.
  - name: Classes
    description: |
      A program has a set of schedules, those schedules are then instantiated as events for particular dates.
      In general, any partner works only with events as the relevant data from programs is replicated in that entity. 
      Eventually, using the program_id can be useful to link events that are different instances of the same program.
  - name: Bookings
    description: |
      A Booking is the result of a particular member booking in an event.
      After the booking has passed it will have the attendance for it inside.
  - name: Payments
    description: |
      A branch can have several payment methods, like CARD, DIRECT_DEBIT (ACH), CASH, etc.
      When doing a purchase, the payment method can be specified as well.
  - name: Reports
    description: |
      Several operations to grab aggregated data are listed in this section.
  - name: Electronic Agreements
    description: |
      A user can have the need to sign different electronic contracts. 
      Operations to get those and force the send email for those are exposed here.
  - name: Price Calculator
    description: |
      Operations from the purchase flow to know how much a user will be charged for a particular purchase.
  - name: Appointments Availability
    description: |
      Operations to get the virtual version of the appointments' availability.
  - name: Cart
    description: |
      Operations to manage a cart, a cart is a temporary storage of the items that a member wants to purchase.
paths:
  '/2.0/branches/{id}':
    get:
      summary: Get a Branch
      description: This call is used to get a single branch by it's id
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
            pattern: "^[a-f\\d]{24}$"
          description: The branch unique identifier
      tags:
        - Branches
      responses:
        '200':
          description: The branch object
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BranchBaseResponse'
  '/2.1/branches/{branchId}/leads':
    post:
      summary: Create a user (Lead or Client)
      description: This call is used to create a lead in a studio. Creating a lead will often put the new user into a sales funnel. If the user you are creating should not go through this flow set the lead status to member (more details below).
      parameters:
        - in: path
          name: branchId
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/User'
      tags:
        - Users
      responses:
        '200':
          description: The user object
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserResponseBirthAsString'
  '/2.1/branches/{branchId}/leads/{userId}/interactions':
    post:
      summary: Add a new interaction
      description: This endpoint allows adding a new interaction for a lead in a specific branch
      parameters:
        - name: branchId
          in: path
          required: true
          description: The ID of the branch
          schema:
            type: string
            pattern: '^[a-f\d]{24}$'
        - name: userId
          in: path
          required: true
          description: The ID of the lead
          schema:
            type: string
            pattern: '^[a-f\d]{24}$'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                user_id:
                  type: string
                  pattern: '^[a-f\d]{24}$'
                type:
                  type: string
                  enum: ['NOTE', 'MANUAL_EMAIL']
                description:
                  type: string
                  maxLength: 500
              required:
                - user_id
                - type
      tags:
        - Users
      responses:
        '200':
          description: Interaction added successfully
    get:
      summary: Gets user interactions
      description: This endpoint allows retrieving the interaction/notes list for a user in a specific branch
      parameters:
        - name: branchId
          in: path
          required: true
          description: The branch unique identifier
          schema:
            type: string
            pattern: '^[a-f\d]{24}$'
        - name: userId
          in: path
          required: true
          description: The user unique identifier
          schema:
            type: string
            pattern: '^[a-f\d]{24}$'
        - name: page
          in: query
          schema:
            type: integer
            minimum: 1
          required: true
          description: Page number.
      tags:
        - Users
      responses:
        '200':
          description: Object with data (array of Interaction objects) and request metadata.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InteractionsResponseContainer'
  '/2.0/members/{userId}':
    put:
      summary: Update a user (Lead or Client)
      description: 'This call is used to update a lead in a studio. You can change personal details about a user using this call. You cannot change membership or payment details using this call, for information reference the memberships and payments sections.'
      parameters:
        - in: path
          name: userId
          required: true
          schema:
            type: string
      requestBody:
        description: Only send the fields you want to change
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/User'
      tags:
        - Users
      responses:
        '200':
          description: The user object
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserResponseBirthAsString'
    get:
      summary: Get a user (Lead or Client)
      description: This call is used to get one user.
      parameters:
        - in: path
          name: userId
          required: true
          schema:
            type: string
      tags:
        - Users
      responses:
        '200':
          description: The user object
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserResponseBirthAsString'
  '/2.1/branches/{branchId}/leads/filter':
    post:
      summary: Get all leads
      description: >
        This call is used to get leads in a studio. It is a POST call as you can send filters in the body. These query
        parameters are optional, if you send no json body all leads in a studio are returned. The results are returned
        sorted by created date in descending mode.
      parameters:
        - in: path
          name: branchId
          required: true
          schema:
            type: string
      tags:
        - Users
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                filters:
                  $ref: '#/components/schemas/UserFilters'
                pagination:
                  type: object
                  properties:
                    skip:
                      type: integer
                      description: The number of pages to skip in oder to get to the concrete page.
                    limit:
                      type: integer
                      description: The count of returned values within the page.
            examples: {}
      responses:
        '200':
          description: Array of User objects.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserExtendedResponseBirthAsTimeStampContainer'
  '/2.0/members':
    get:
      summary: Get all clients
      description: This call is used to get clients in a studio
      tags:
        - Users
      responses:
        '200':
          description: A list of user objects
          content:
            application/json:
              schema:
                type: object
                x-examples:
                  example-1:
                    object: list
                    page: 1
                    limit: 2
                    has_more: true
                    total_count: 137
                    data:
                      - _id: 62d9e7d8e803e136a87b7367
                        first_name: Canada
                        last_name: Address
                        phone: '848484'
                        email: <EMAIL>
                        access_barcode: null
                        type: member
                        membership:
                          type: payg
                          start_date: 1658444400
                          user_membership_id: 62d9e7d921c5795bcfe49223
                          status: ACTIVE
                        branch_id: 6213afed9ead8f3e7b0e3574
                        namespace: locnamespace
                        active: true
                        origin_branch_id: 6213afed9ead8f3e7b0e3574
                        name: Canada Address
                        image_url: 'https://s3-eu-west-1.amazonaws.com/glofox/staging/locnamespace/branches/6213afed9ead8f3e7b0e3574/users/62d9e7d8e803e136a87b7367.png'
                        role: member
                      - _id: 62d9e74406dd365c127f6d46
                        first_name: Skywalktwo
                        last_name: Address
                        phone: '848484'
                        email: <EMAIL>
                        access_barcode: null
                        type: member
                        membership:
                          type: payg
                          start_date: 1658444400
                          user_membership_id: 62d9e74683ed70695e264e84
                          status: ACTIVE
                        branch_id: 6213afed9ead8f3e7b0e3574
                        namespace: locnamespace
                        active: true
                        origin_branch_id: 6213afed9ead8f3e7b0e3574
                        name: Skywalktwo Address
                        image_url: 'https://s3-eu-west-1.amazonaws.com/glofox/staging/locnamespace/branches/6213afed9ead8f3e7b0e3574/users/62d9e74406dd365c127f6d46.png'
                        role: member
                allOf:
                  - $ref: '#/components/schemas/BaseModelPagination'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          $ref: '#/components/schemas/ExtendedUser'
              examples:
                example:
                  value:
                    object: list
                    page: 1
                    limit: 2
                    has_more: true
                    total_count: 137
                    data:
                      - _id: 62d9e7d8e7b7367
                        first_name: Canada
                        last_name: Address
                        phone: '848484'
                        email: <EMAIL>
                        access_barcode: null
                        type: member
                        membership:
                          type: payg
                          start_date: 1658444400
                          user_membership_id: 62d9e7d9cfe49223
                          status: ACTIVE
                        branch_id: 6213afee7b0e3574
                        namespace: namespace
                        active: true
                        origin_branch_id: 6213afe3e7b0e3574
                        name: Canada Address
                        image_url: 'https://s3-eu-west-1.amazonaws.com/glofox/staging/namespace/branches/6213afed9ead8e3574/users/62d9e7d86a87b7367.png'
                        role: member
                      - _id: 62d9e7365c127f6d46
                        first_name: Skywalktwo
                        last_name: Address
                        phone: '848484'
                        email: <EMAIL>
                        access_barcode: null
                        type: member
                        membership:
                          type: payg
                          start_date: 1658444400
                          user_membership_id: 62d9695e264e84
                          status: ACTIVE
                        branch_id: 6213aff3e7b0e3574
                        namespace: namespace
                        active: true
                        origin_branch_id: 6213afed9eade3574
                        name: Skywalktwo Address
                        image_url: 'https://s3-eu-west-1.amazonaws.com/glofox/staging/namespace/branches/6213afef3e7b0e3574/users/62d9e74406d6d46.png'
                        role: member
                        use_parent_email: true
                        parent_id: '67178691e9494a88c91a405e'
                        use_parent_phone: true
                        birth: '1998-05-10'
                        consent:
                          email:
                            active: false
                            modified_at: '2023-11-06T17:57:39.696+00:00'
                            modified_by_user_id: 'guest'
                            modified_from_ip_address:
                              - '*************'
                            message: 'Yes, I consent to receive marketing emails about upcoming events and/or promotions.'
                          sms:
                            active: false
                            modified_at: '2023-11-06T17:57:39.697+00:00'
                            modified_by_user_id: 'guest'
                            modified_from_ip_address:
                              - '*************'
                            message: 'Yes, I consent to receive SMS texts to the mobile number that I provided.'
                          push:
                            active: true
                            modified_at: '2023-11-06T17:57:40.411+00:00'
                            modified_by_user_id: 'guest'
                            modified_from_ip_address:
                              - '*************'
                        created: 1723013931
                        emergency_contact: '5656565656'
                        lead_status: 'LEAD'
                        modified: 1723013930
                        source: 'DASHBOARD'

            application/xml:
              schema:
                type: object
                properties: {}
      parameters:
        - schema:
            type: integer
          in: query
          name: page
          description: The page which should be returned
        - schema:
            type: integer
          in: query
          name: limit
          description: The limit of the data within the page
        - schema:
            type: boolean
          in: query
          name: active
          description: Filter by active members, defaults to true
  '/2.1/branches/{branchId}/users':
    get:
      summary: Search Members by Email
      description: Emails in Glofox are unique for each studio so you might need to see if an email exists in a studio. This search checks if a full email address exists. It can not be used for searching for part of an email.
      parameters:
        - in: path
          name: branchId
          required: true
          schema:
            type: string
        - in: query
          name: 'filters[email]'
          required: true
          schema:
            type: string
        - in: query
          name: page
          required: false
          schema:
            type: integer
            default: 1
          description: The page number to retrieve.
        - in: query
          name: limit
          required: false
          schema:
            type: integer
            default: 50
          description: The number of results per page.
      tags:
        - Users
      responses:
        '200':
          description: The user object
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserResponseBirthAsString'
  /2.0/reset:
    post:
      summary: Request reset password link
      description: This will send a link to the <NAME_EMAIL>. This link contains a unique code they need to reset their email.
      tags:
        - Users
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PasswordReset'
      responses:
        '200':
          description: The message will state; 'Reset Email <NAME_EMAIL>'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PasswordResetResponse'
  '/2.0/memberships':
    get:
      summary: Get all memberships
      description: This call is used to get the memberships in a studio.
      parameters:
        - in: query
          name: private
          required: false
          schema:
            type: boolean
      tags:
        - Memberships
      responses:
        '200':
          description: Object with data (array of Memberships objects) and pagination as properties.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MembershipResponseContainer'
  '/2.0/memberships/{membershipId}':
    get:
      summary: Get a single membership
      description: This call is used to get the memberships in a studio.
      parameters:
        - in: path
          name: membershipId
          required: true
          schema:
            type: string
            pattern: "^[a-f\\d]{24}$"
      tags:
        - Memberships
      responses:
        '200':
          description: The membership object
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Membership'
  '/2.2/branches/{branchId}/users/{userId}/memberships/{membershipId}/plans/{planCode}/purchase':
    post:
      summary: Purchase Membership
      description: This call is used to purchase a membership for a user. If the user has an active membership the new members will be set to start the date after the active membership expires.
      parameters:
        - in: path
          name: branchId
          required: true
          schema:
            type: string
        - in: path
          name: userId
          required: true
          schema:
            type: string
        - in: path
          name: membershipId
          required: true
          schema:
            type: string
        - in: path
          name: planCode
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MembershipPurchaseRequest'
      tags:
        - Memberships
      responses:
        '200':
          description: Response when purchase is complete
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MembershipPurchaseSuccess'
  '/2.2/branches/{branchId}/charges/{chargeId}/finalize-flexible':
    post:
      summary: Purchase Membership with Flexible payment
      description: This call is used to forward to payments service the payment method we will use for a flexible payment membership
      parameters:
        - in: path
          name: branchId
          required: true
          schema:
            type: string
        - in: path
          name: chargeId
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FlexiblePaymentMethod'
      tags:
        - Memberships
      responses:
        '200':
          description: Response when purchase is complete
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MembershipPurchaseSuccess'
  '/v3.0/memberships/{membershipId}/cancel':
    post:
      summary: Cancel a user membership
      description: This method allows you to cancel an open-ended recurring membership on a specific date.
      parameters:
        - in: path
          name: membershipId
          description: The user membership ID we want to cancel
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MembershipCancellationRequest'
      tags:
        - Memberships
      responses:
        '200':
          description: Response when cancellation is successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MembershipCancellationResponse'

  '/2.0/credits':
    get:
      summary: Get a Users Credit Packs
      description: This call retrieves all credit packs for a user.
      parameters:
        - in: query
          name: user_id
          required: true
          schema:
            type: string
      tags:
        - Credits
      responses:
        '200':
          description: The credit object
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Credits'
  '/2.0/access':
    post:
      summary: Create Access
      description: |
        This endpoint is used to store accesses.

        It can also be used to check if a user has a valid membership. The response includes a boolean field called valid_on_entry. If this is false access should be denied.
      tags:
        - Access
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Access'
      responses:
        '200':
          description: The access object
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccessResponse'
  '/2.0/events/{id}':
    get:
      tags:
        - Classes
      summary: Get event by ID
      description: Returns Event object (Class|Appointment|Facility) by its unique identifier.
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
            pattern: '^[a-f\d]{24}$'
          description: The event's unique identifier.
      responses:
        '200':
          description: The event object
          content:
            application/json:
              schema:
                oneOf:
                  - $ref: '#/components/schemas/Class'
                  - $ref: '#/components/schemas/Course'
                  - $ref: '#/components/schemas/TimeSlot'
                discriminator:
                  propertyName: type
  '/2.0/events':
    get:
      summary: Classes - Events & Programs
      description: |-
        This call is used to retrieve classes in a timeframe. See the Bookings section below for information on booking a user into the classes

        If the response contains model_id as null, event._id field as value for model_id in subsequent calls and the model should be events
        If the response contains a model_id not null, it should be used in subsequent calls.
      parameters:
        - in: query
          name: start
          required: true
          description: Classes with a start time after this time will be retrieved. Send as UTC Timestamp in seconds. Default value is specified as current Timestamp.
          schema:
            type: string
        - in: query
          name: end
          required: true
          description: Classes with a start time before this time will be retrieved. Send as UTC Timestamp in seconds. Default value is specified as +1 day to the start value.
          schema:
            type: string
        - schema:
            type: string
            enum:
              - 'created'
              - '-created'
              - 'time_start'
              - '-time_start'
              - 'time_finish'
              - '-time_finish'
            default: '-created'
          in: query
          name: sort_by
          description: |-
            Sorting order of the returned data. When a "-" is prefixed to a parameter, it will be sorted in descending mode by that field.
            Note for courses, sorting by time_start and time_finish is not available, as courses have a lengthy duration.
        - schema:
            type: integer
            default: 50
            maximum: 100
            minimum: 0
          in: query
          name: limit
          description: Count of the limited data returned within 1 page.
        - schema:
            type: integer
            default: 1
          in: query
          name: page
          description: Page number. In case of the large amount of data exceeding the default limit, the data have to be requested by pages.
        - schema:
            type: boolean
            default: true
          in: query
          name: active
          description: 'Whether active (non deleted) events have to be returned. If active=any is sent, then both active and non active events are returned.'
        - schema:
            type: boolean
            default: false
          in: query
          name: private
          description: |-
            Whether private or public events have to be returned.
            Private events are not available for member side bookings, they could be booked by a Staff only on behalf of a Member.
            Public events are returned by default. If private=any is sent, then both public and private events are returned.
        - schema:
            type: string
            default: event
            enum:
              - event
              - timeslot
              - course
          in: query
          name: filter
          description: |-
            The filter by type of the event.
            To retrieve classes filter=event has to be passed.
            To retrieve courses filter=course has to be passed.
            To retrieve appointments and facilities filter=timeslot has to be passed.
            To retrieve all types filter=event,timeslot,course has to be passed.
            We recommend to fetch each type of the event in a separate request to narrow the amount of loaded events.
        - schema:
            type: string
          in: query
          name: programs
          description: |-
            Filer events by the list of program_ids divided by comma.
            For example you would like to fetch events associated with the concrete program/programs.
            In that case you have to pass programs=<program_id_1>,<program_id_2>.
        - schema:
            type: string
          in: query
          name: facilities
          description: |-
            Filer events by the list of facility_ids divided by comma.
            For example you would like to fetch events associated with the concrete facility/facilities.
            In that case you have to pass facilities=<facility_id_1>,<facility_id_2>.
        - schema:
            type: string
          in: query
          name: trainers
          description: |-
            Filer events by the list of trainer_ids divided by comma.
            For example you would like to fetch events associated with the concrete trainer/trainers.
            In that case you have to pass trainers=<trainer_id_1>,<trainer_id_2>.
        - schema:
            type: string
            enum:
              - appointments
              - facilities
          in: query
          name: model
          description: |-
            The model to specify the timeslot subtype
            To retrieve appointments only parameters filter=timeslot&model=appointments have to be passed.
            To retrieve facilities only parameters filter=timeslot&model=facilities have to be passed.
        - schema:
            type: string
          in: query
          name: model_id
          description: |-
            Specifies concrete model_id that need to be fetched.
            For example you would like to fetch only appointments slots for the concrete appointment.
            In that case you have to pass model_id=<appointment_id>.
        - in: query
          name: utc_modified_start_date
          required: false
          description: Filters events using a UTC timestamp based on their modified time. Where modified >= {value}
          schema:
            type: integer
        - in: query
          name: utc_modified_end_date
          required: false
          description: Filters events using a UTC timestamp based on their modified time. Where modified <= {value}
          schema:
            type: integer
      tags:
        - Classes
      responses:
        '200':
          description: The classes paginated object
          content:
            application/json:
              schema:
                type: object
                allOf:
                  - $ref: '#/components/schemas/BaseModelPagination'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          anyOf:
                            - $ref: '#/components/schemas/Class'
                            - $ref: '#/components/schemas/Course'
                            - $ref: '#/components/schemas/TimeSlot'
                          discriminator:
                            propertyName: type
  '/2.0/bookings':
    post:
      tags:
        - Bookings
      summary: Create a booking
      description: |
        Use this call to book a user into a class. If a class is full the user will be added to a wait-list if there are spaces remaining on the waitlist.
        If the booking fails it returns and an error code and reason for failure. You can check a list of possible errors in 'Responses' (use the 'Examples' drop-down list).
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BookingRequest'
      responses:
        '200':
          description: |
            The response will include a 'success' field, noting whether the response was properly created or there was an error during the process.
            If the booking was successfully created, the response will also include the booking object ('Booking' field). Otherwise, it will include the error information.
            A list of different sample responses can be checked using the 'Examples' drop-down selector.
          content:
            application/json:
              schema:
                oneOf:
                  - $ref: '#/components/schemas/CreatedBooking'
                  - $ref: '#/components/schemas/BookingRequestError'
              examples:
                OK - Successful booking:
                  value:
                    success: true
                    Booking:
                      namespace: 'namespace'
                      branch_id: 'branch_id'
                      user_id: 'user_id'
                      user_name: 'user_name'
                      program_id: 'program_id'
                      schedule_code: 'schedule_code'
                      event_id: 'event_id'
                      event_name: 'event_name'
                      model_name: 'Sweat1000'
                      membership_name: null
                      plan_name: null
                      plan_code: null
                      status: 'BOOKED'
                      confirmed: true
                      type: 'events'
                      time_start: 1723266000
                      guest_bookings: 0
                      timezone: 'Europe/Dublin'
                      modified: 1723013930
                      created: 1723013930
                      created_user_id: 'created_user_id'
                      _id: '_id'
                      duration: 60
                      image_url: 'image_url'
                ERROR - PAYMENT_METHOD_NOT_ALLOWED:
                  value:
                    success: false
                    message: Payment method is not allowed for integrations
                    message_code: PAYMENT_METHOD_NOT_ALLOWED
                ERROR - A_TRANSACTION_IS_ALREADY_IN_PROGRESS:
                  value:
                    success: false
                    message: A transaction is already in progress
                    message_code: A_TRANSACTION_IS_ALREADY_IN_PROGRESS
                ERROR - PAY_LATER_IS_NOT_AN_ALLOWED_PAYMENT_METHOD_IN_THIS_BRANCH:
                  value:
                    success: false
                    message: Pay later is not an allowed payment method in this branch
                    message_code: PAY_LATER_IS_NOT_AN_ALLOWED_PAYMENT_METHOD_IN_THIS_BRANCH
                ERROR - YOU_HAVE_BOOKED_FOR_THIS_EVENT:
                  value:
                    success: false
                    message: You have already booked this
                    message_code: YOU_HAVE_BOOKED_FOR_THIS_EVENT
                ERROR - EVENT_HAS_BEEN_CANCELLED:
                  value:
                    success: false
                    message: Event has been cancelled
                    message_code: EVENT_HAS_BEEN_CANCELLED
                ERROR - BOOK_ERROR_ANOTHER_CLASS:
                  value:
                    success: false
                    message: You have already booked a class at this time. Booking "(bookingClassName)" At "(bookingStartTime)"
                    message_code: BOOK_ERROR_ANOTHER_CLASS
                    message_data:
                      [
                        'bookingClassName (optional)',
                        'bookingStartTime (optional)',
                      ]
                ERROR - YOU_HAVE_NO_CREDITS_LEFT:
                  value:
                    success: false
                    message: You have no credits left. Please choose Pay as you Go or go to Memberships to purchase more
                    message_code: YOU_HAVE_NO_CREDITS_LEFT
                ERROR - BOOKING_REQUEST_REJECTED:
                  value:
                    success: false
                    message: Booking request has been rejected while processing.
                    message_code: BOOKING_REQUEST_REJECTED
                ERROR - USER_IN_THE_PAYLOAD_IS_DIFFERENT_THAN_THE_IMPERSONATED:
                  value:
                    success: false
                    message: The user id in the payload is different than the impersonated member id.
                    message_code: USER_IN_THE_PAYLOAD_IS_DIFFERENT_THAN_THE_IMPERSONATED
                ERROR - IMPERSONATED_MEMBER_IS_NOT_A_MEMBER:
                  value:
                    success: false
                    message: The impersonated member is not a member.
                    message_code: IMPERSONATED_MEMBER_IS_NOT_A_MEMBER
                ERROR - IMPERSONATED_MEMBER_IS_NOT_IN_THE_BRANCH_NAMESPACE:
                  value:
                    success: false
                    message: The impersonated member is not in the same namespace as the branch.
                    message_code: IMPERSONATED_MEMBER_IS_NOT_IN_THE_BRANCH_NAMESPACE
                ERROR - IMPERSONATION_NOT_ALLOWED_FOR_THIS_ROUTE:
                  value:
                    success: false
                    message: Impersonation is not allowed for this route.
                    message_code: IMPERSONATION_NOT_ALLOWED_FOR_THIS_ROUTE
                ERROR - ERROR_ACTIVE_LIMIT_BOOKING_EXCEEDED:
                  value:
                    success: false
                    message: You already have the maximum allowed number of upcoming bookings (n).
                    message_code: ERROR_ACTIVE_LIMIT_BOOKING_EXCEEDED
                ERROR - YOU_CANNOT_BOOK_UNTIL:
                  value:
                    success: false
                    message: You cannot book this until (date).
                    message_code: YOU_CANNOT_BOOK_UNTIL
                ERROR - YOU_CANNOT_BOOK_AFTER:
                  value:
                    success: false
                    message: You cannot book this after (date).
                    message_code: YOU_CANNOT_BOOK_AFTER
                ERROR - CLASS_IS_FULL:
                  value:
                    success: false
                    message: The class is full.
                    message_code: CLASS_IS_FULL
                ERROR - EVENT_HAS_PASSED:
                  value:
                    success: false
                    message: The event has already passed.
                    message_code: EVENT_HAS_PASSED
                ERROR - LIMIT_BOOKING_REACHED:
                  value:
                    success: false
                    message: You can not make more than (n) bookings per (day|week|month|year).
                    message_code: LIMIT_BOOKING_REACHED
                ERROR - WAITING_LIST_IS_FULL:
                  value:
                    success: false
                    message: The Waiting list is full.
                    message_code: WAITING_LIST_IS_FULL
                ERROR - EVENT_NOT_FOUND:
                  value:
                    success: false
                    message: Event Not Found.
                    message_code: EVENT_NOT_FOUND
                ERROR - MAX_STRIKES_EXCEEDED:
                  value:
                    success: false
                    message: You have not attended classes that you have booked. Please contact the facility directly for further information.
                    message_code: MAX_STRIKES_EXCEEDED
                ERROR - WAITLIST_REQUEST_REJECTED:
                  value:
                    success: false
                    message: Waitlist request has been rejected while processing.
                    message_code: WAITLIST_REQUEST_REJECTED
                ERROR - YOU_ARE_ALREADY_IN_THE_WAITING_LIST:
                  value:
                    success: false
                    message: You are already in the waiting list for this class.
                    message_code: YOU_ARE_ALREADY_IN_THE_WAITING_LIST
                ERROR - CANNOT_BOOK_DUE_TO_MEMBERSHIP_BEING_LOCKED:
                  value:
                    success: false
                    message: You cannot book because your membership is locked. Please, contact your studio for more info.
                    message_code: CANNOT_BOOK_DUE_TO_MEMBERSHIP_BEING_LOCKED
                ERROR - MEMBERSHIP_HAS_NOT_STARTED_YET:
                  value:
                    success: false
                    message: MEMBERSHIP_HAS_NOT_STARTED_YET.
                    message_code: MEMBERSHIP_HAS_NOT_STARTED_YET
                ERROR - MEMBERSHIP_EXPIRED:
                  value:
                    success: false
                    message: Your membership has expired
                    message_code: MEMBERSHIP_EXPIRED
                ERROR - THE_CLASS_STILL_HAS_SPOTS_LEFT:
                  value:
                    success: false
                    message: The class still has (n) spots open for booking.
                    message_code: THE_CLASS_STILL_HAS_SPOTS_LEFT
                ERROR - CANNOT_CREATE_A_BOOKING_WITHOUT_MEMBER_ID:
                  value:
                    success: false
                    message: Cannot create a booking without member id.
                    message_code: CANNOT_CREATE_A_BOOKING_WITHOUT_MEMBER_ID

    get:
      summary: Returns user's bookings
      description: This call returns the bookings for one user. The results are paginated with the limit set to 50 bookings.
      parameters:
        - in: query
          name: branchId
          required: true
          schema:
            type: string
          description: Unique branch ID the user belongs to.
        - in: query
          name: user_id
          required: true
          schema:
            type: string
          description: Unique user ID.
        - schema:
            type: string
            enum:
              - 'created'
              - '-created'
              - 'time_start'
              - '-time_start'
              - 'time_finish'
              - '-time_finish'
            default: '-created'
          in: query
          name: sort_by
          description: Sorting order of the returned data. If put "-" before parameter it will be sorted in descending mode.
        - schema:
            type: integer
            default: 50
            maximum: 100
            minimum: 0
          in: query
          name: limit
          description: Count of the limited data returned within 1 page.
        - schema:
            type: integer
            default: 1
          in: query
          name: page
          description: Page number. In case of the large amount of data exceeding the default limit, the data have to be requested by pages.
        - schema:
            type: integer
          in: query
          name: time_start
          description: 'Start time of bookings in UTC Timestamp in seconds format. Default value is specified as current Timestamp.'
        - schema:
            type: string
          in: query
          description: 'End time of bookings in UTC Timestamp in seconds format. Default value is specified as +1 day to the start value.'
          name: time_end
        - schema:
            type: boolean
            default: false
          in: query
          name: exclude_cancelled
          description: Should cancelled bookings be excluded from the result.
        - in: query
          name: event_id
          required: false
          schema:
            type: string
          description: |
            The id of the event you would like to get bookings for. event_id specifies a concrete class slot on the calendar.
        - in: query
          name: program_id
          required: false
          schema:
            type: string
          description: |
            The id of the program you would like to get bookings for. The program_id is the id of the class definition.
            If you provide it, you will get all bookings for the concrete program.
        - in: query
          name: model
          required: false
          schema:
            type: string
            enum:
              - appointments
              - facilities
              - courses
          description: |
            The type of the model you need to get bookings for.
            For instance, you need to fetch all bookings for the appointments only, then pass model=appointments.
        - in: query
          name: model_id
          required: false
          schema:
            type: string
          description: |
            The id of the booking model. If you need to fetch bookings fof the appointment, you have have to provide it's id.
            The same is for the facility and course.
        - in: query
          name: time_slot_id
          required: false
          schema:
            type: string
          description: |
            The id of the time slot you need to get booking for. time_slot_id works only for appointments and facilities bookings.
            Let's say, you need to get the booking for the appointment slot, then you have to send parameters in the request:
            model=appointments&time_slot_id=<appointment_time_slot_id>.
            If you need to get facility booking for the concrete time slot, you have to pass:
            model=facilities&time_slot_id=<facility_time_slot_id>
      tags:
        - Bookings
      responses:
        '200':
          description: The bookings paginated object
          content:
            application/json:
              schema:
                type: object
                allOf:
                  - $ref: '#/components/schemas/BaseModelPagination'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          anyOf:
                            - $ref: '#/components/schemas/ClassBooking'
                            - $ref: '#/components/schemas/CourseBooking'
                            - $ref: '#/components/schemas/TimeSlotBooking'
                          discriminator:
                            propertyName: model
  '/booking/{bookingId}/user/{userId}/cancel':
    post:
      summary: Cancel a Booking
      description: 'This call cancels a booking. In some cases, a booking cannot be cancelled due to the rules set up in the studio. If this happens you will receive a message in the response detailing why.'
      parameters:
        - in: path
          name: bookingId
          required: true
          schema:
            type: string
        - in: path
          name: userId
          required: true
          schema:
            type: string
      tags:
        - Bookings
      responses:
        '200':
          description: The booking object
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BookingCancelRequest'
  '/2.2/branches/{branchId}/bookings':
    get:
      summary: Get all Bookings in a studio
      description: This call returns a list of bookings in a studio.
      parameters:
        - in: path
          name: branchId
          required: true
          schema:
            type: string
        - schema:
            type: integer
            default: 50
            maximum: 100
            minimum: 0
          in: query
          name: limit
          description: Count of the limited data returned within 1 page.
        - schema:
            type: integer
            default: 1
          in: query
          name: page
          description: Page number. In case of the large amount of data exceeding the default limit, the data have to be requested by pages.
        - in: query
          name: start_date
          required: false
          description: Filters bookings using a UTC timestamp based on their created time. Where created >= {value}.
          schema:
            type: integer
        - in: query
          name: end_date
          required: false
          description: Filters bookings using a UTC timestamp based on their created time. Where created <= {value}
          schema:
            type: integer
        - in: query
          name: modified_start_date
          required: false
          description: Filters bookings using a UTC timestamp based on their modified time. Where modified >= {value}
          schema:
            type: integer
        - in: query
          name: modified_end_date
          required: false
          description: Filters bookings using a UTC timestamp based on their modified time. Where modified <= {value}
          schema:
            type: integer
        - in: query
          name: time_start_start_date
          required: false
          description: Filters bookings using a local timestamp based on their time_start. Where time_start >= {value}
          schema:
            type: integer
        - in: query
          name: time_start_end_date
          required: false
          description: Filters bookings using a local timestamp based on their time_start. Where time_start <= {value}
          schema:
            type: integer
        - in: query
          name: time_finish_start_date
          required: false
          description: Filters bookings using a local timestamp based on their time_finish. Where time_finish >= {value}
          schema:
            type: integer
        - in: query
          name: time_finish_end_date
          description: Filters bookings using a local timestamp based on their time_finish. Where time_finish <= {value}
          required: false
          schema:
            type: integer
        - in: query
          name: status
          required: false
          description: Filters bookings based on their status
          schema:
            type: string
            enum:
              - BOOKED
              - WAITING
              - CANCELED
              - RESERVED
              - FAILED
        - in: query
          name: event_type
          required: false
          description: Filters bookings based on their underline event type
          schema:
            type: string
            enum:
              - events
              - courses
              - facilities
              - users
              - appointments
      tags:
        - Bookings
      responses:
        '200':
          description: The booking object
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    description: Indicates if the request was successful.
                  meta:
                    type: object
                    properties:
                      totalCount:
                        type: integer
                        description: Total count of the fetched data.
                      page:
                        type: integer
                        default: 1
                        description: The number of the page returned when the response is paginated.
                      limit:
                        type: integer
                        default: 50
                        minimum: 0
                        maximum: 100
                        description: The limit of the returned data.
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/BranchBooking'
  '/2.3/branches/{branchId}/bookings':
    post:
      summary: Create a booking.
      description: |-
        Use this call to book a user into a class, appointment, course or facility.
        In case of booking a full class, and the join_waiting_list parameter is passed as true, the user will be added to the waiting list.
        If the booking fails it returns a 400 status code with details of the failure.
      parameters:
        - in: path
          name: branchId
          required: true
          schema:
            type: string
        - in: header
          name: x-glofox-impersonated-member-id
          description: |
            The ID of a member who is being impersonated.
            Impersonated requests are allowed for integrators only and are valuable when an integrator needs to make a booking on behalf of a member. 
            In such cases, the system abides by all booking settings and rejects requests if a member has exceeded set booking limitations.
          required: false
          schema:
            type: string
      tags:
        - Bookings
      requestBody:
        required: true
        content:
          application/json:
            schema:
              oneOf:
                - $ref: '#/components/schemas/BookingEventAsAMember'
                - $ref: '#/components/schemas/BookingEventAsAStaff'
                - $ref: '#/components/schemas/BookingTimeSlotAsAMember'
                - $ref: '#/components/schemas/BookingTimeSlotAsAStaff'
                - $ref: '#/components/schemas/BookingCourseAsAMember'
                - $ref: '#/components/schemas/BookingCourseAsAStaff'
                - $ref: '#/components/schemas/BookingFacilityAsAMember'
                - $ref: '#/components/schemas/BookingFacilityAsAStaff'
      responses:
        '200':
          description: The response will include a 'success' field along with the booking object ('Booking' field).
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreatedBooking'
              example:
                OK - Successful booking:
                  value:
                    success: true
                    Booking:
                      namespace: 'namespace'
                      branch_id: 'branch_id'
                      user_id: 'user_id'
                      user_name: 'user_name'
                      program_id: 'program_id'
                      schedule_code: 'schedule_code'
                      event_id: 'event_id'
                      event_name: 'event_name'
                      model_name: 'Sweat1000'
                      membership_name: null
                      plan_name: null
                      plan_code: null
                      status: 'BOOKED'
                      confirmed: true
                      type: 'events'
                      time_start: 1723266000
                      guest_bookings: 0
                      timezone: 'Europe/Dublin'
                      modified: 1723013930
                      created: 1723013930
                      created_user_id: 'created_user_id'
                      _id: '_id'
                      duration: 60
                      image_url: 'image_url'
        '400':
          description: |-
            When the booking fails, the response will include an error message.
            A list of different sample responses can be checked using the 'examples' drop-down selector.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericErrorResponse'
              examples:
                ERROR - PAYMENT_METHOD_NOT_ALLOWED:
                  value:
                    success: false
                    message: Payment method is not allowed for integrations
                    message_code: PAYMENT_METHOD_NOT_ALLOWED
                ERROR - A_TRANSACTION_IS_ALREADY_IN_PROGRESS:
                  value:
                    success: false
                    message: A transaction is already in progress
                    message_code: A_TRANSACTION_IS_ALREADY_IN_PROGRESS
                ERROR - PAY_LATER_IS_NOT_AN_ALLOWED_PAYMENT_METHOD_IN_THIS_BRANCH:
                  value:
                    success: false
                    message: Pay later is not an allowed payment method in this branch
                    message_code: PAY_LATER_IS_NOT_AN_ALLOWED_PAYMENT_METHOD_IN_THIS_BRANCH
                ERROR - YOU_HAVE_BOOKED_FOR_THIS_EVENT:
                  value:
                    success: false
                    message: You have already booked this
                    message_code: YOU_HAVE_BOOKED_FOR_THIS_EVENT
                ERROR - EVENT_HAS_BEEN_CANCELLED:
                  value:
                    success: false
                    message: Event has been cancelled
                    message_code: EVENT_HAS_BEEN_CANCELLED
                ERROR - BOOK_ERROR_ANOTHER_CLASS:
                  value:
                    success: false
                    message: You have already booked a class at this time. Booking "(bookingClassName)" At "(bookingStartTime)"
                    message_code: BOOK_ERROR_ANOTHER_CLASS
                    message_data:
                      [
                        'bookingClassName (optional)',
                        'bookingStartTime (optional)',
                      ]
                ERROR - YOU_HAVE_NO_CREDITS_LEFT:
                  value:
                    success: false
                    message: You have no credits left. Please choose Pay as you Go or go to Memberships to purchase more
                    message_code: YOU_HAVE_NO_CREDITS_LEFT
                ERROR - BOOKING_REQUEST_REJECTED:
                  value:
                    success: false
                    message: Booking request has been rejected while processing.
                    message_code: BOOKING_REQUEST_REJECTED
                ERROR - USER_IN_THE_PAYLOAD_IS_DIFFERENT_THAN_THE_IMPERSONATED:
                  value:
                    success: false
                    message: The user id in the payload is different than the impersonated member id.
                    message_code: USER_IN_THE_PAYLOAD_IS_DIFFERENT_THAN_THE_IMPERSONATED
                ERROR - IMPERSONATED_MEMBER_IS_NOT_A_MEMBER:
                  value:
                    success: false
                    message: The impersonated member is not a member.
                    message_code: IMPERSONATED_MEMBER_IS_NOT_A_MEMBER
                ERROR - IMPERSONATED_MEMBER_IS_NOT_IN_THE_BRANCH_NAMESPACE:
                  value:
                    success: false
                    message: The impersonated member is not in the same namespace as the branch.
                    message_code: IMPERSONATED_MEMBER_IS_NOT_IN_THE_BRANCH_NAMESPACE
                ERROR - IMPERSONATION_NOT_ALLOWED_FOR_THIS_ROUTE:
                  value:
                    success: false
                    message: Impersonation is not allowed for this route.
                    message_code: IMPERSONATION_NOT_ALLOWED_FOR_THIS_ROUTE
                ERROR - ERROR_ACTIVE_LIMIT_BOOKING_EXCEEDED:
                  value:
                    success: false
                    message: You already have the maximum allowed number of upcoming bookings (n).
                    message_code: ERROR_ACTIVE_LIMIT_BOOKING_EXCEEDED
                ERROR - YOU_CANNOT_BOOK_UNTIL:
                  value:
                    success: false
                    message: You cannot book this until (date).
                    message_code: YOU_CANNOT_BOOK_UNTIL
                ERROR - YOU_CANNOT_BOOK_AFTER:
                  value:
                    success: false
                    message: You cannot book this after (date).
                    message_code: YOU_CANNOT_BOOK_AFTER
                ERROR - CLASS_IS_FULL:
                  value:
                    success: false
                    message: The class is full.
                    message_code: CLASS_IS_FULL
                ERROR - EVENT_HAS_PASSED:
                  value:
                    success: false
                    message: The event has already passed.
                    message_code: EVENT_HAS_PASSED
                ERROR - LIMIT_BOOKING_REACHED:
                  value:
                    success: false
                    message: You can not make more than (n) bookings per (day|week|month|year).
                    message_code: LIMIT_BOOKING_REACHED
                ERROR - WAITING_LIST_IS_FULL:
                  value:
                    success: false
                    message: The Waiting list is full.
                    message_code: WAITING_LIST_IS_FULL
                ERROR - EVENT_NOT_FOUND:
                  value:
                    success: false
                    message: Event Not Found.
                    message_code: EVENT_NOT_FOUND
                ERROR - MAX_STRIKES_EXCEEDED:
                  value:
                    success: false
                    message: You have not attended classes that you have booked. Please contact the facility directly for further information.
                    message_code: MAX_STRIKES_EXCEEDED
                ERROR - WAITLIST_REQUEST_REJECTED:
                  value:
                    success: false
                    message: Waitlist request has been rejected while processing.
                    message_code: WAITLIST_REQUEST_REJECTED
                ERROR - YOU_ARE_ALREADY_IN_THE_WAITING_LIST:
                  value:
                    success: false
                    message: You are already in the waiting list for this class.
                    message_code: YOU_ARE_ALREADY_IN_THE_WAITING_LIST
                ERROR - CANNOT_BOOK_DUE_TO_MEMBERSHIP_BEING_LOCKED:
                  value:
                    success: false
                    message: You cannot book because your membership is locked. Please, contact your studio for more info.
                    message_code: CANNOT_BOOK_DUE_TO_MEMBERSHIP_BEING_LOCKED
                ERROR - MEMBERSHIP_HAS_NOT_STARTED_YET:
                  value:
                    success: false
                    message: MEMBERSHIP_HAS_NOT_STARTED_YET.
                    message_code: MEMBERSHIP_HAS_NOT_STARTED_YET
                ERROR - MEMBERSHIP_EXPIRED:
                  value:
                    success: false
                    message: Your membership has expired
                    message_code: MEMBERSHIP_EXPIRED
                ERROR - THE_CLASS_STILL_HAS_SPOTS_LEFT:
                  value:
                    success: false
                    message: The class still has (n) spots open for booking.
                    message_code: THE_CLASS_STILL_HAS_SPOTS_LEFT
                ERROR - CANNOT_CREATE_A_BOOKING_WITHOUT_MEMBER_ID:
                  value:
                    success: false
                    message: Cannot create a booking without member id.
                    message_code: CANNOT_CREATE_A_BOOKING_WITHOUT_MEMBER_ID
  '/2.3/branches/{branchId}/bookings/{bookingId}':
    delete:
      summary: Cancel a booking.
      description: This call cancels a booking.
      parameters:
        - in: path
          name: branchId
          required: true
          schema:
            type: string
        - in: path
          name: bookingId
          required: true
          schema:
            type: string
        - in: header
          name: x-glofox-impersonated-member-id
          description: |
            The ID of a member who is being impersonated.
            Impersonated requests are allowed for integrators only and are valuable when an integrator needs to make a booking on behalf of a member. 
            In such cases, the system abides by all booking settings and rejects requests if a member has exceeded set booking limitations.
          required: false
          schema:
            type: string
      tags:
        - Bookings
      responses:
        '204':
          description: Successful response.
        '400':
          description: Invalid request.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericErrorResponse'
  '/2.0/attendances':
    post:
      summary: Mark a booking as attended
      description: This call marks a booking (or bookings) as attended.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AttendanceRequest'
      tags:
        - Bookings
      responses:
        '200':
          description: The booking object
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AttendanceResponse'
  '/2.0/branches/{branchId}/events/{eventId}/price':
    get:
      summary: Get class availability booking price
      description: Use this call to find out if a class is available for a user and the price they will pay. Classes can be restricted to certain memberships and different memberships have different prices to pay for a booking.
      parameters:
        - in: path
          name: branchId
          required: true
          schema:
            type: string
        - in: path
          name: eventId
          required: true
          schema:
            type: string
        - in: query
          name: for
          required: true
          schema:
            type: string
            description: The user id who is intending to book the class
        - in: query
          name: guestBookings
          required: true
          schema:
            type: integer
            description: A user can book more than once space. This is used if they want to bring a friend to the class. Each extra booking multiplies the cost.
      tags:
        - Bookings
      responses:
        '200':
          description: The booking object
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Booking'
  '/2.1/branches/{branchId}/payment-methods':
    get:
      summary: Get Payment Methods Available for a Branch
      description: This call returns a list of all available payment methods for this branch.
      parameters:
        - in: path
          name: branchId
          required: true
          schema:
            type: string
        - in: query
          name: includes
          required: true
          schema:
            type: string
          description: "Set to 'includes=provider,iframe'"
      tags:
        - Payments
      responses:
        '200':
          description: The payment methods object
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaymentMethods'
  '/Analytics/report':
    post:
      summary: Get all Payments for a studio
      description: |
        Returns all transactions in a time frame for a studio. Set the start and end fields in the filter to set the timeframe.
        - Supports filtering by date ranges and comparing to a secondary date range.
        - If `filter.CompareToRanges` is set to `true`, the `secondStart` and `secondEnd` fields must be provided to define the secondary date range.
        - The `start` and `end` fields define the primary date range.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PaymentsReportRequest'
      tags:
        - Reports
      responses:
        '200':
          description: The payment methods object
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaymentsReportResponse'
  '/2.0/analytics/trainer-performance':
    get:
      summary: Trainers performance report
      description: This returns all trainers in a studio and the number of events they were assigned to in a time period.
      parameters:
        - in: query
          name: start
          required: true
          schema:
            type: string
          description: Events after this time will be reported on
        - in: query
          name: end
          required: true
          schema:
            type: string
          description: Events before this time will be reported on
      tags:
        - Reports
      responses:
        '200':
          description: The payment methods object
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TrainerReportResponse'
  '/2.0/staff':
    get:
      summary: Get staff members
      description: This call is used to get all staff members.
      parameters:
        - in: query
          name: type
          required: false
          schema:
            type: string
            enum:
              - ADMIN
              - MEMBER
              - RECEPTION
              - TRAINER
            example: TRAINER
          description: Type of staff members.
      tags:
        - Users
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StaffResponseContainer'
  '/2.0/staff/{staffId}':
    get:
      summary: Get a staff member
      description: This call is used to get one staff member.
      parameters:
        - in: path
          name: staffId
          required: true
          schema:
            type: string
            pattern: '^[a-f\d]{24}$'
          description: Unique identifier for the staff member.
      tags:
        - Users
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Staff'
  '/2.0/register':
    post:
      summary: Register a User
      description: >
        This endpoint is used to register a user account.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserRegisterRequest'
      tags:
        - Users
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserRegisterResponseContainer'

  '/TermsConditions/view':
    get:
      summary: Get the waivers (also called documents) for a studio
      description: This call returns the waivers for a studio. If you are integrating electronic agreements the first waiver to use is 'type = member.authenticated'
      tags:
        - Electronic Agreements
      responses:
        '200':
          description: Get a studios waivers
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TermsConditionsResponse'
  '/2.2/branches/{branchId}/users/{userId}/agreements/send':
    post:
      summary: Send a document to a user for signature
      description: This call emails a document to a user to sign.
      parameters:
        - in: path
          name: branchId
          required: true
          schema:
            type: string
            pattern: '^[a-f\d]{24}$'
        - in: path
          name: userId
          required: true
          schema:
            type: string
            pattern: '^[a-f\d]{24}$'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SendElectronicAgreementRequest'
      tags:
        - Electronic Agreements
      responses:
        '204':
          description: No content
  '/2.2/branches/{branchId}/users/{userId}/agreements/':
    get:
      summary: Retrieve user agreements
      description: Fetches a list of agreements for a specific user within a branch.
      parameters:
        - in: path
          name: branchId
          required: true
          schema:
            type: string
            pattern: '^[a-f\d]{24}$'
        - in: path
          name: userId
          required: true
          schema:
            type: string
            pattern: '^[a-f\d]{24}$'
      tags:
        - Electronic Agreements
      responses:
        '200':
          description: A list of user agreements
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserAgreementsResponse'

  '/2.3/branches/{branchId}/agreements-template/trigger/{trigger}':
    get:
      summary: Retrieve the latest agreement template based on trigger for a specific branch
      description: |
        Fetch the latest agreement template for a given branch based on the specified trigger.
        Member-authenticated refers to Waivers, while membership-purchased refers to Terms and Conditions.
      parameters:
        - name: branchId
          in: path
          required: true
          schema:
            type: string
            pattern: '^[a-zA-Z0-9]+$'
        - name: trigger
          in: path
          required: true
          description: The trigger event (member-authenticated or membership-purchased)
          schema:
            type: string
            enum:
              - member-authenticated
              - membership-purchased
        - name: userId
          in: query
          required: true
          description: The user ID related to the request
          schema:
            type: string
            pattern: '^[a-zA-Z0-9]+$'
        - name: membershipId
          in: query
          required: true
          description: The membership ID related to the request (must be empty for member-authenticated)
          schema:
            type: string
            pattern: '^[a-zA-Z0-9]+$'
        - name: planCode
          in: query
          required: true
          description: The plan code related to the request (must be empty for member-authenticated)
          schema:
            type: string
            pattern: '^[0-9]+$'
      tags:
        - Electronic Agreements
      responses:
        '200':
          description: Agreement template
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AgreementTemplateResponse'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericErrorResponse'

  '/2.2/branches/{branchId}/users/{userId}/agreements/{agreementId}/send':
    post:
      summary: Send agreement by ID
      description: This endpoint sends an agreement to a user by the agreement's ID. Use this to resend agreements that need to be signed again.
      parameters:
        - in: path
          name: branchId
          required: true
          schema:
            type: string
            pattern: "^[a-f\\d]{24}$"
        - in: path
          name: userId
          required: true
          schema:
            type: string
            pattern: "^[a-f\\d]{24}$"
        - in: path
          name: agreementId
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                agreementMetaData:
                  type: string
                  description: For memberships agreements it's the user membership ID
      tags:
        - Electronic Agreements
      responses:
        '200':
          description: Agreement sent successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  message:
                    type: string

  '/2.2/branches/{branchId}/price-breakdown':
    post:
      summary: Get price breakdown for a given resource.
      description: |
        Calls price calculator service to determine the breakdown for a product(s) including discounts and taxes.

        The example response is for a branch with exclusive tax set. If the branch is using inclusive tax you will note a few differences:

        - `taxes.products[n].net_price` This would be the discounted price minus the total tax (taxes are included in the product price).
        - `taxes.products[n].total_price` This would be the same as discounted price (again because taxes are included the price would remain the same).

        Price calculator does not determine prorated amounts, or the validity of whether a discount/promo code can be used for a particular item. It is up to client to determine this.

        ## Discounts

        - `discount_ids`: This parameter can only be used by staff/integrators.
        - `promo_code`: This parameter can be used by any user.

        The `discount_ids` and `promo_code` parameters should only ever be used mutually exclusively.

      parameters:
        - in: path
          name: branchId
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetPriceBreakdownRequest'
      tags:
        - Price Calculator
      responses:
        '200':
          description: Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetPriceBreakdownResponse'
        '400':
          description: Returned when an invalid promo code is used
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetPriceBreakdownError'
  '/2.1/branches/{branchId}/appointments/{appointmentId}/calculate-price':
    post:
      summary: Calculates appointment price
      description: This endpoint calculates the price of an appointment for a specific member.
      parameters:
        - in: path
          name: branchId
          required: true
          schema:
            type: string
        - in: path
          name: appointmentId
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CalculateAppointmentPriceRequest'
      tags:
        - Price Calculator
      responses:
        '200':
          description: Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CalculateAppointmentPriceResponse'
        '400':
          description: Returned when provided data is invalid
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CalculatePriceError'
  '/2.1/branches/{branchId}/facilities/{facilityId}/calculate-price':
    post:
      summary: Calculates facility price
      description: This endpoint calculates the price of a facility for a specific member.
      parameters:
        - in: path
          name: branchId
          required: true
          schema:
            type: string
        - in: path
          name: facilityId
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CalculateFacilityPriceRequest'
      tags:
        - Price Calculator
      responses:
        '200':
          description: Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CalculateFacilityPriceResponse'
        '400':
          description: Returned when provided data is invalid
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CalculatePriceError'
  '/2.1/branches/{branchId}/courses/{courseId}/calculate-price':
    post:
      summary: Calculates course price
      description: This endpoint calculates the price of a course for a specific member.
      parameters:
        - in: path
          name: branchId
          required: true
          schema:
            type: string
        - in: path
          name: courseId
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CalculateCoursePriceRequest'
      tags:
        - Price Calculator
      responses:
        '200':
          description: Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CalculateCoursePriceResponse'
        '400':
          description: Returned when provided data is invalid
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CalculatePriceError'
  '/2.1/branches/{branchId}/events/{eventId}/calculate-price':
    post:
      summary: Calculates event price
      description: This endpoint calculates the price of an event for a specific member.
      parameters:
        - in: path
          name: branchId
          required: true
          schema:
            type: string
        - in: path
          name: eventId
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CalculateEventPriceRequest'
      tags:
        - Price Calculator
      responses:
        '200':
          description: Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CalculateEventPriceResponse'
        '400':
          description: Returned when provided data is invalid
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CalculatePriceError'
  '/2.2/users/{parentId}/linked-accounts':
    get:
      summary: Get child accounts of a given user.
      description: This call returns all linked accounts (child accounts) of a given user (parent account).
      parameters:
        - in: path
          name: parentId
          required: true
          schema:
            type: string
            pattern: "^[a-f\\d]{24}$"
          description: 'The parent account id'
      tags:
        - Users
      responses:
        '200':
          description: Object with data (array of User objects) and success (boolean) as properties
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserBaseResponseContainer'
  '/2.3/branches/{branchId}/leads/contact-sources':
    get:
      summary: Get list of contact sources.
      description: This call returns list of available contact sources for a location.
      tags:
        - Leads
      parameters:
        - name: branchId
          in: path
          required: true
          description: 'The location id'
          schema:
            type: string
            pattern: '^[a-f\d]{24}$'
      responses:
        '200':
          description: Array of Lead Contact Sources.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LeadContactSources'
        '404':
          description: 'Branch not found'
        '500':
          description: 'Internal server error'
  '/2.3/branches/{branchId}/leads/marketing-sources':
    get:
      summary: Get list of marketing sources.
      description: This call returns list of available marketing sources for a location.
      parameters:
        - in: path
          name: branchId
          required: true
          schema:
            type: string
            pattern: "^[a-f\\d]{24}$"
          description: 'The location id'
      tags:
        - Leads
      responses:
        '200':
          description: Array of Lead Marketing Sources.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/BaseModelPagination'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          $ref: '#/components/schemas/LeadMarketingSources'
        '404':
          description: 'Branch not found'
        '500':
          description: 'Internal server error'
  '/2.1/branches/{branchId}/appointments-availability':
    get:
      tags:
        - Appointments Availability
      summary: Retrieve all the available appointments of the given branch.
      description:
        This endpoint is to virtually generate appointment slots based on the trainers availabilities.
        <br>
        Please note that filtering by `start-time` and `finish-time` may result in different values than if you do not filter by any time range,
        since this endpoint only returns availabilities that start and end in the requested range.
        <br>
        Time parameter validation rules - Both start-time and finish-time must be provided together when filtering by time.
        You can provide both parameters together, or omit both parameters (system defaults to today's availability window).
        Providing only one parameter will result in a validation error. When both are provided, finish-time must be after start-time.
      operationId: getAppointmentsAvailability
      parameters:
        - name: branchId
          in: path
          description: The id of the branch.
          required: true
          schema:
            type: string
        - name: trainer-id
          in: query
          description: The id of the trainer to filter availability.
          required: false
          example: 6744468fafce2a52b5d50aea
          schema:
            type: string
        - name: start-time
          in: query
          description:
            This parameter sets the initial point to generate the slots. <br>
            When neither start-time nor finish-time are provided, the system will default to today's availability window. <br>
            Required when finish-time is provided. Both parameters must be used together.
          required: false
          example: 2024-09-01T12:00:00Z
          schema:
            type: string
            format: datetime
        - name: finish-time
          in: query
          description:
            This parameter sets the final point to generate the slots. <br>
            When neither start-time nor finish-time are provided, the system will default to today's availability window. <br>
            Required when start-time is provided. Both parameters must be used together and this value must be after start-time.
          required: false
          example: 2024-09-01T20:00:00Z
          schema:
            type: string
            format: datetime
      responses:
        '200':
          description: Successful operation.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AvailableAppointments'
        '400':
          description:
            Bad request. This can occur due to invalid ID provided (malformed branch ID or trainer ID) or validation errors for time parameters.
            Possible validation error codes - START_TIME_IS_REQUIRED_WHEN_FINISH_TIME_IS_PROVIDED, FINISH_TIME_IS_REQUIRED_WHEN_START_TIME_IS_PROVIDED,
            FINISH_TIME_MUST_BE_AFTER_START_TIME, INVALID_START_TIME_FORMAT, INVALID_FINISH_TIME_FORMAT.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AvailableAppointmentsError'
        '404':
          description: Resource not found. It represents any empty return from the data source.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AvailableAppointmentsError'
  '/v2.0/carts/pre-checkout':
    post:
      summary: Pre-checkout
      description: >
        Validates the member can purchase the line items supplied and returns a
        detailed price breakdown, including both the immediate charge and any future
        recurring charges.


        The response is split into two items: due_today and future_charges:

        - due_today represents what the member will be charged today for the
        line_items supplied. For a membership line item it takes into account
        configurations like proration, upfront fees etc.

        - future_charges represents what they will be charged in the future as a
        result of purchasing these line items and groups them by their billing
        frequency. For example, if the request contains a line item which is a
        recurring membership, there will be an item with the future_charges array to
        represent the renewals for that membership. If there is a maintenance fee
        associated with that plan there will be a second item which represents that.

        For what is due_today and for all items in the future_charges array there
        will be a detailed price breakdown including any taxes or discounts
        aggregated across the items.


        The response includes:

        - The tax calculation mode (inclusive/exclusive)

        - Available payment methods for both immediate and recurring charges

        - Relevant metadata for items like memberships (duration, auto-renewal
        settings, etc.)


        Notes:

        - All monetary values are returned in the currency specified in the
        response.

        - This endpoint requires a valid JWT token for a user with MEMBER role. This
        will be who the purchase is for.
      tags:
        - Cart
      parameters:
        - name: x-glofox-impersonated-member-id
          in: header
          required: true
          description: The ID of a member who's pre-checkout cart values are being calculated.
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PreCheckoutRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PreCheckoutResponse'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericErrorResponse'
  '3.0/locations/{locationId}/facilities':
    get:
      summary: Get facilities for a location
      description: This call returns all facilities for a given location.
      parameters:
        - schema:
            type: integer
            minimum: 1
            default: 1
          in: query
          name: page
        - schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 50
          in: query
          name: limit
        - schema:
            type: string
          in: query
          description: field | -field (name, created_at)
          name: sort-by
      tags:
        - Facilities
      responses:
        '200':
          description: A list of facilities for the specified location.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BaseModelPagination'
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Facility'
        '400':
          description: Bad request.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericErrorResponse'
  '/3.0/locations/{locationId}/courses':
    get:
      summary: ''
      operationId: get-courses
      description: This endpoint returns a filtered courses list
      x-internal: true
      security:
        - coreapi_auth: [ ]
      parameters:
        - name: page
          in: query
          description: The page number used to paginate course listings
          schema:
            type: integer
            default: 1
        - name: limit
          in: query
          description: The number of results to return per page
          schema:
            type: integer
            default: 50
            minimum: 1
            maximum: 100
        - name: sort-by
          in: query
          description: Sort order for results (name, namespace, description, schedule_code, start_date, end_date)
          schema:
            type: string
      tags:
        - Courses
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BaseModelPagination'
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Course_V3'
webhooks:
  access:
    post:
      summary: Handle access events
      description: This describes the identifiers used by different access systems and their relation to the user. There are 2 types of events (MEMBER_ACCESS_INFO_CREATED, MEMBER_ACCESS_INFO_UPDATED).
      parameters:
        - name: signature
          in: header
          required: false
          description: A HMAC-SHA256 hash verifying the authenticity of Glofox webhook. The hash is a hexadecimal string. In short Signature = Hex( HMAC-SHA256( YourSecretKey,  StringToSign ))
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AccessEvent'
      responses:
        '200':
          description: Event received successfully.
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
  booking:
    post:
      summary: Handle booking events
      description: Booking is the result of a particular member booking an event. After the booking has passed it will contain the attendance. There are 2 types of events (BOOKING_CREATED, BOOKING_DELETED).
      parameters:
        - name: signature
          in: header
          required: false
          description: A HMAC-SHA256 hash verifying the authenticity of Glofox webhook. The hash is a hexadecimal string. In short Signature = Hex( HMAC-SHA256( YourSecretKey,  StringToSign ))
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BookingEvent'
      responses:
        '200':
          description: Event received successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
  course_booking:
    post:
      summary: Handle course booking events
      description: A course booking event represents the booking of a course by a user. There are 2 types of events (COURSE_BOOKING_CREATED, COURSE_BOOKING_DELETED).
      parameters:
        - name: signature
          in: header
          required: false
          description: A HMAC-SHA256 hash verifying the authenticity of Glofox webhook. The hash is a hexadecimal string. In short Signature = Hex( HMAC-SHA256( YourSecretKey,  StringToSign ))
          schema:
          type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CourseBookingEvent'
      responses:
        '200':
          description: Event received successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
  eagreement:
    post:
      summary: Handle eagreement events
      description: An eagreement event represents the acceptance by a user of a studio's waiver and/or terms & conditions for services purchased. There is 2 types of event (EAGREEMENT_CREATED, EAGREEMENT_UPDATED).
      parameters:
        - name: signature
          in: header
          required: false
          description: A HMAC-SHA256 hash verifying the authenticity of Glofox webhook. The hash is a hexadecimal string. In short Signature = Hex( HMAC-SHA256( YourSecretKey,  StringToSign ))
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EagreementEvent'
      responses:
        '200':
          description: Event received successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
  event:
    post:
      summary: Handle event events
      description: An event represents the occurrence of a class at a specific date and time. There are 3 types of events (EVENT_CREATED, EVENT_UPDATED, EVENT_DELETED).
      parameters:
        - name: signature
          in: header
          required: false
          description: A HMAC-SHA256 hash verifying the authenticity of Glofox webhook. The hash is a hexadecimal string. In short Signature = Hex( HMAC-SHA256( YourSecretKey,  StringToSign ))
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EventEvent'
      responses:
        '200':
          description: Event received successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
  invoice:
    post:
      summary: Handle invoice events
      description: An invoice represents an itemized commercial document that records the products or services delivered to the customer and the total amount due. There are 1 type of event (INVOICE_UPDATED).
      parameters:
        - name: signature
          in: header
          required: false
          description: A HMAC-SHA256 hash verifying the authenticity of Glofox webhook. The hash is a hexadecimal string. In short Signature = Hex( HMAC-SHA256( YourSecretKey,  StringToSign ))
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/InvoiceEvent'
      responses:
        '200':
          description: Event received successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
  member:
    post:
      summary: Handle member events
      description: A Member is the entity representing one person. There are 2 types of events (MEMBER_CREATED, MEMBER_UPDATED).
      parameters:
        - name: signature
          in: header
          required: false
          description: A HMAC-SHA256 hash verifying the authenticity of Glofox webhook. The hash is a hexadecimal string. In short Signature = Hex( HMAC-SHA256( YourSecretKey,  StringToSign ))
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MemberEvent'
      responses:
        '200':
          description: Event received successfully.
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
  membership:
    post:
      summary: Handle membership events
      description: A membership is the main service that a user can have. A user can only have one membership at a given time. There are 3 types of events (MEMBERSHIP_CREATED, MEMBERSHIP_UPDATED, MEMBERSHIP_DELETED).
      parameters:
        - name: signature
          in: header
          required: false
          description: A HMAC-SHA256 hash verifying the authenticity of Glofox webhook. The hash is a hexadecimal string. In short Signature = Hex( HMAC-SHA256( YourSecretKey,  StringToSign ))
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MembershipEvent'
      responses:
        '200':
          description: Event received successfully.
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
  service:
    post:
      summary: Handle service events
      description: A service is a product that a user can purchase. There are 3 types of service events (SERVICE_CREATED, SERVICE_UPDATED, SERVICE_DELETED).
      parameters:
        - name: signature
          in: header
          required: false
          description: A HMAC-SHA256 hash verifying the authenticity of Glofox webhook. The hash is a hexadecimal string. In short Signature = Hex( HMAC-SHA256( YourSecretKey,  StringToSign ))
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ServiceEvent'
      responses:
        '200':
          description: Event received successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
components:
  schemas:
    Pagination:
      type: object
      description: Information about pagination.
      properties:
        count:
          type: integer
          description: The total number of available items.
          example: 10
        perPage:
          type: integer
          description: The number of items per page.
          example: 50
        currentPage:
          type: integer
          description: The current page number.
          example: 1
    ResponseContainer:
      type: object
      properties:
        success:
          type: boolean
          description: Indicates if the request was successful.
        meta:
          type: object
          description: Metadata on the request.
          properties:
            pagination:
              $ref: '#/components/schemas/Pagination'
      required:
        - success
    SuccessResponseContainer:
      type: object
      properties:
        success:
          type: boolean
          description: Indicates if the request was successful.
      required:
        - success
    InteractionsResponseContainer:
      allOf:
        - $ref: '#/components/schemas/ResponseContainer'
        - type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/Interaction'
          description: Array of Interaction objects.
      required:
        - data
    Interaction:
      type: object
      properties:
        _id:
          type: string
          description: Unique identifier for the interaction.
          pattern: '^[a-f\d]{24}$'
        branch_id:
          type: string
          description: Unique identifier for the branch.
          pattern: '^[a-f\d]{24}$'
        user_id:
          type: string
          description: Unique identifier for the user.
          pattern: '^[a-f\d]{24}$'
        description:
          type: string
          description: Description of the interaction.
        type:
          type: string
          enum:
            - NOTE
            - CALLED_AND_CONNECTED
            - CALLED_AND_NO_ANSWER
            - MANUAL_EMAIL
          description: The type of interaction.
        created:
          type: integer
          format: int64
          description: The time in UTC Timestamp when the interaction was created.
      required:
        - _id
        - branch_id
        - user_id
        - type
    BranchBaseResponse:
      type: object
      properties:
        _id:
          type: string
          description: Unique identifier for the branch.
        name:
          type: string
          description: The name of the branch.
        namespace:
          type: string
          description: The namespace of the branch.
        address:
          $ref: '#/components/schemas/BranchAddress'
        phone:
          type: string
          description: The phone number of the branch.
        email:
          type: string
          description: The email address of the branch.
        facebook:
          type: string
          description: The Facebook page of the branch.
        instagram:
          type: string
          description: The Instagram handle of the branch.
        website:
          type: string
          description: The website URL of the branch.
        about:
          type: string
          description: The about or intro sentence of the branch.
        corporate_id:
          type: string
          description: The corporate id of the branch.
      required:
        - name
        - namespace
        - address
    BranchAddress:
      type: object
      description: The address of the branch.
      properties:
        street:
          type: string
          description: The street of the branch address.
        city:
          type: string
          description: The city of the branch address.
        state:
          type: string
          description: The state of the branch address.
        country_code:
          type: string
          description: The country code of the branch address.
        district:
          type: string
          description: The district of the branch address.
        latitude:
          type: number
          format: double
          description: The latitude of the branch address.
        longitude:
          type: number
          format: double
          description: The longitude of the branch address.
        currency:
          type: string
          description: The currency used at the branch location.
        timezone_id:
          type: string
          description: The timezone of the branch.
        postal_code:
          type: string
          description: The postal code of the branch address.
      required:
        - timezone_id
        - country
        - postal_code
        - latitude
        - longitude
    User:
      type: object
      properties:
        first_name:
          type: string
          description: The users first name.
        last_name:
          type: string
          description: The users last name or surname.
        phone:
          type: string
          description: The users phone number.
        password:
          type: string
          description: Required only for type MEMBER
        email:
          type: string
          description: The users email address.
        emergency_contact:
          type: string
          description: The users emergency contact information. This shows in the Details section of the Member profile. Staff can view this field.
        lead_status:
          type: string
          description: 'The status must be LEAD, MEMBER or COLD. Cold is used for leads with a low conversion prospect so in the majority of cases LEAD is the correct status. Using MEMBER will skip the sales funnel for the user. You can find more info on the status here'
        type:
          type: string
          description: Set as "MEMBER"
        access_barcode:
          type: string
          description: If the studio uses access control this field can be used to store an identifier for the access software.
        birth:
          type: string
          description: Send as "YYYY-MM-DD"
        image_url:
          type: string
          description:
            The URL of the user's profile picture. This field is always populated with an auto-generated URL, even if no image has been uploaded.
            If the image exists, it will be accessible through the URL. Otherwise, attempting to access it may result in a 403 (Forbidden) response, which is the existing behavior for missing files in S3.
            403 responses should be treated as an indication that the user has not uploaded a profile picture.
        consent:
          $ref: '#/components/schemas/UserConsent'
        membership:
          $ref: '#/components/schemas/UserMembership'
        leads:
          $ref: '#/components/schemas/Leads'
        joined_at:
          type: string
          format: date-time
          description: Represents the date when the user joined the location. Used primarily for imports, integrations, and member transfers to distinguish joining date (which can be set in the past) from created_at (date in which a document was created in Mongo set as today's date)
      required:
        - first_name
        - last_name
        - email
        - lead_status
        - type
    ExtendedUser:
      allOf:
        - $ref: '#/components/schemas/User'
        - type: object
          properties:
            created:
              type: integer
              format: int64
              description: The time in UTC Timestamp when the user was created.
            modified:
              type: integer
              format: int64
              description: The time in UTC that the user was last modified.
            use_parent_email:
              type: boolean
              description: Boolean Flag whether to use the parent's email for the user account. If use_parent_email is true then skip the property email on the payload. If use_parent_email exists and is false then the property email is required. Mandatory when registering a child.
            parent_id:
              type: string
              description: The user's parent account id
            use_parent_phone:
              type: boolean
              description: Boolean flag to indicate whether to use the parent's phone number for the child account. If it's present and false then the property phone is required.Mandatory when registering a child.
            source:
              type: array
              items:
                type: string
                enum:
                  - MEMBER_APP
                  - DASHBOARD
                  - LEGACY_MOBILE_APP
                  - KIOSK
                  - ADMIN_APP
                  - WEBPORTAL
                  - IMPORTS
                  - MANUAL_MEMBER_TRANSFER
                  - UNKNOWN
                description: The source of the user's creation.
    UserBaseResponse:
      type: object
      properties:
        _id:
          type: string
          description: Unique identifier for the user.
        first_name:
          type: string
          description: The users first name.
        last_name:
          type: string
          description: The users last name or surname.
        phone:
          type: string
          description: The users phone number.
        email:
          type: string
          description: The users email address.
        type:
          type: string
          description: Set as "MEMBER"
        parent_id:
          type: string
          description: The user's parent account id
      required:
        - first_name
        - last_name
        - email
    UserBaseResponseContainer:
      allOf:
        - $ref: '#/components/schemas/ResponseContainer'
        - type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/UserBaseResponse'
          description: Array of User objects.
      required:
        - data
    StaffResponseContainer:
      allOf:
        - $ref: '#/components/schemas/BaseModelPagination'
        - type: object
          properties:
            data:
              type: array
              items:
                $ref: '#/components/schemas/Staff'
              description: Array of Staff objects.
          required:
            - data
    Staff:
      type: object
      properties:
        _id:
          type: string
          description: Unique identifier for the staff member
          pattern: '^[a-f\d]{24}$'
        branch_id:
          type: string
          description: Unique identifier for staff's branch
          pattern: '^[a-f\d]{24}$'
        namespace:
          type: string
          description: The namespace of the Studio
        active:
          type: boolean
          description: Boolean flag to indicate whether the staff is active
        bookable:
          type: boolean
          description: Boolean flag to indicate whether the staff is bookable
        modified:
          type: integer
          format: in64
          description: The time in UTC the staff was last modified.
        type:
          type: string
          enum:
            - admin
            - superadmin
            - receptionist
            - trainer
        first_name:
          type: string
          description: The staffs first name
        last_name:
          type: string
          description: The staffs last name
        description:
          type: string
          description: The description of the staff member
        name:
          type: string
          description: The staffs first and last name
        image_url:
          type: string
          description:
            The URL of the user's profile picture. This field is always populated with an auto-generated URL, even if no image has been uploaded.
            If the image exists, it will be accessible through the URL. Otherwise, attempting to access it may result in a 403 (Forbidden) response, which is the existing behavior for missing files in S3.
            403 responses should be treated as an indication that the user has not uploaded a profile picture.
      required:
        - namespace
        - first_name
        - last_name
        - type
        - active
        - _id
        - branch_id
    UserRegisterRequest:
      type: object
      properties:
        first_name:
          type: string
          description: The first name of the user.
        last_name:
          type: string
          description: The last name of the user.
        gender:
          type: string
          description: The gender of the  user.
          enum:
            - M
            - F
            - O
            - P
        use_parent_phone:
          type: boolean
          description: Boolean flag to indicate whether to use the parent's phone number for the child account. If it's present and false then the property phone is required.Mandatory when registering a child.
        phone:
          type: string
          description: The phone number of the user.
        email:
          type: string
          description: The email address of the the user.
        use_parent_email:
          type: boolean
          description: Boolean Flag whether to use the parent's email for the user account. If use_parent_email is true then skip the property email on the payload. If use_parent_email exists and is false then the property email is required. Mandatory when registering a child.
        birth:
          type: string
          description: The user's birth date as a string (YYYY-MM-DD)
        emergency_contact:
          type: string
          description: The emergency contact for the user.
        parent_id:
          type: string
          description: Mandatory when registering child's account.Unique identifier for the parent's account.
        no_password:
          type: integer
          description: When set to 1, the account is created without a password.
        avatar:
          type: string
          example: UklGRmpmAgBXRUJQVlA4IF5mAgAQFwSdASo4BDgEAAAAJZ278
          description: Base64-encoded representation of the image
        avatarImg:
          type: string
          example: data:image/webp;base64,UklGRmpmAgBXRUJQVlA4IF5mAg
          description: Data URL containing a base64-encoded WebP format image.
        consent:
          $ref: '#/components/schemas/UserConsent'
        leads:
          $ref: '#/components/schemas/Leads'
        joined_at:
          type: string
          format: date-time
          description: Represents the date when the user joined the location. Used primarily for imports, integrations, and member transfers to distinguish joining date (which can be set in the past) from created_at (date in which a document was created in Mongo set as today's date)
      required:
        - first_name
        - last_name
        - birth
    UserResponse:
      allOf:
        - $ref: '#/components/schemas/UserBaseResponse'
        - type: object
          properties:
            emergency_contact:
              type: string
              description: >
                The user emergency contact information. This shows in the Details section of the Member profile. Staff
                can view this field.
            lead_status:
              type: string
              example: null
              description: >
                Cold is used for leads with a low conversion prospect, so in the majority of cases LEAD is the correct
                status. A TRIAL has a trial membership. If value is null, it means that the lead is a member. A member
                will skip the sales funnel for the user.NO_SALE_TRIAL is a manually updated  status created for trials that did not convert. TOUR is a manually updated  status created for tours. NO_SALE_TOUR is a manually updated  status created for tours that did not convert.
              enum:
                - LEAD
                - COLD
                - TRIAL
                - NO_SALE_TRIAL
                - TOUR
                - NO_SALE_TOUR
            access_barcode:
              type: string
              description: >
                If the studio uses access control this field can be used to store an identifier for the access software.
            image_url:
              type: string
              description:
                The URL of the user's profile picture. This field is always populated with an auto-generated URL, even if no image has been uploaded.
                If the image exists, it will be accessible through the URL. Otherwise, attempting to access it may result in a 403 (Forbidden) response, which is the existing behavior for missing files in S3.
                403 responses should be treated as an indication that the user has not uploaded a profile picture.
            account_email:
              type: string
              description: (Optional) The email address of the user document.
            contact_email:
              type: string
              description: (Optional) The email address used for communication purposes with the user.
            consent:
              $ref: '#/components/schemas/UserConsent'
            membership:
              $ref: '#/components/schemas/UserMembership'
            leads:
              $ref: '#/components/schemas/Leads'
            joined_at:
              type: string
              format: date-time
              description: Represents the date when the user joined the location. Used primarily for imports, integrations, and member transfers to distinguish joining date (which can be set in the past) from created_at (date in which a document was created in Mongo set as today's date)
    UserExtendedResponseBirthAsTimeStampContainer:
      allOf:
        - $ref: '#/components/schemas/SuccessResponseContainer'
        - type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/UserExtendedResponseBirthAsTimeStamp'
          description: Array of User objects.
      required:
        - data
    UserExtendedResponseBirthAsTimeStamp:
      allOf:
        - $ref: '#/components/schemas/UserResponseBirthAsTimeStamp'
      properties:
        name:
          type: string
          description: first and last name of the user
        use_parent_phone:
          type: boolean
          description: Boolean Flag whether to use the parent's phone number for the child account,if set to false then phone field is required.
        use_parent_email:
          type: boolean
          description: Boolean Flag whether to use the parent's email for the child account, if set to true then email field is required .
        branch_id:
          type: string
          description: Unique identifier for user's branch.
        active:
          type: boolean
          description: Boolean flag to indicate whether the user is active
        namespace:
          type: string
          description: The namespace of the studio.
        modified:
          type: integer
          format: int64
          description: The time in UTC that the user was last modified.
        source:
          type: array
          items:
            type: string
            enum:
              - MEMBER_APP
              - CLASSPASS
              - DASHBOARD
              - KIOSK
              - ADMIN_APP
              - WEBPORTAL
              - IMPORTS
              - MANUAL_MEMBER_TRANSFER
              - CLI
              - UNKNOWN
        created:
          type: integer
          format: int64
          description: The time in UTC Timestamp when the user was created.
    UserExtendedResponseBirthAsString:
      allOf:
        - $ref: '#/components/schemas/UserResponseBirthAsString'
      properties:
        name:
          type: string
          description: first and last name of the user
        use_parent_phone:
          type: boolean
          description: Boolean Flag whether to use the parent's phone number for the child account,if set to false then phone field is required.
        use_parent_email:
          type: boolean
          description: Boolean Flag whether to use the parent's email for the child account, if set to true then email field is required .
        branch_id:
          type: string
          description: Unique identifier for user's branch.
        active:
          type: boolean
          description: Boolean flag to indicate whether the user is active
        namespace:
          type: string
          description: The namespace of the studio.
        modified:
          type: integer
          format: int64
          description: The time in UTC that the user was last modified.
        source:
          type: array
          items:
            type: string
            enum:
              - MEMBER_APP
              - CLASSPASS
              - DASHBOARD
              - KIOSK
              - ADMIN_APP
              - WEBPORTAL
              - IMPORTS
              - MANUAL_MEMBER_TRANSFER
              - CLI
              - UNKNOWN
        created:
          type: integer
          format: int64
          description: The time in UTC Timestamp when the user was created.
    UserRegisterResponse:
      allOf:
        - $ref: '#/components/schemas/UserExtendedResponseBirthAsString'
      properties:
        gender:
          example:
            name: F
            label: FEMALE
          type: object
          description: Gender model for the User.
        user_id:
          type: string
          description: Unique identifier for user
        login:
          type: string
          format: email
          description: Email which is used for the user login
        no_password:
          type: boolean
          description: When set to true, the account is created without a password.
        joined_at:
          type: string
          format: date-time
          description: Represents the date when the user joined the location. Used primarily for imports, integrations, and member transfers to distinguish joining date (which can be set in the past) from created_at (date in which a document was created in Mongo set as today's date)
    Leads:
      type: object
      properties:
        contact_source:
          type: string
          description: The contact source of the lead. The list of possible values vary by location. Use GET /branches/{branchId}/leads/contact-sources to get the list of available contact sources.
        marketing_source:
          type: string
          description: The marketing source of the lead. The list of possible values vary by location. Use GET /branches/{branchId}/leads/marketing-sources to get the list of available marketing sources.
        marketing_source_details:
          type: string
          description: The details of the marketing source of the lead.
          maxLength: 200
          minLength: 3
    LeadMarketingSources:
      type: object
      properties:
        _id:
          type: string
          description: Unique identifier for the lead marketing source.
          pattern: '^[a-f\d]{24}$'
        name:
          type: string
          description: Name of the lead marketing source.
        code:
          type: string
          description: Autogenerated code for the lead marketing source.
        active:
          type: boolean
          description: Indicates if the lead marketing source is active.
        default:
          type: boolean
          description: Indicates if the lead source is pre-defined in Glofox's default list.
        corporate_id:
          type: string
          description: Identifier for the associated corporate. Indicates it's a custom marketing source defined by the HQ.
          nullable: true
        branch_id:
          type: string
          description: Identifier for the associated branch. Indicates it's a custom marketing source defined by the location.
          pattern: '^[a-f\d]{24}$'
          nullable: true
      required:
        - _id
        - name
        - code
        - active
    LeadContactSources:
      type: array
      items:
        type: string
        description: The contact source of the lead. The list of possible values vary by location. Use GET /branches/{branchId}/leads/contact-sources to get the list of available contact sources.
      description: List of contact sources
    UserRegisterResponseContainer:
      allOf:
        - $ref: '#/components/schemas/SuccessResponseContainer'
        - type: object
      properties:
        user:
          $ref: '#/components/schemas/UserRegisterResponse'
      required:
        - user
    UserResponseBirthAsString:
      allOf:
        - $ref: '#/components/schemas/UserResponse'
        - type: object
          properties:
            birth:
              type: string
              description: The user's birth date as a string (YYYY-MM-DD)
    UserResponseBirthAsTimeStamp:
      allOf:
        - $ref: '#/components/schemas/UserResponse'
        - type: object
          properties:
            birth:
              type: integer
              description: The user's birth date as a Unix timestamp
    UserMembership:
      type: object
      description: |
        A membership is the main service a user has.

        The membership lifecycle has 3 main stages: future, current, and ended. The membership status within those are:
        • Future: FUTURE status, a membership that has not started yet
        • Current: ACTIVE, LOCKED, PAUSED: transition between these states is unrestricted
        • Ended: EXPIRED, CANCELLED
      properties:
        type:
          type: string
          description: |
            There are 4 different types of membership:
            - payg: This is a Drop In member with no membership. payg is short for "pay-as-you-go"
            - time: This is a membership with no limit on the number of bookings made.
            - num_classes: This is a credits based membership, the user will have credits which you can retrieve with Get Credit Packs by Member ID. It has no end dates for the credits. Note Get Credit Packs by Member ID could return no credits, meaning the members credits are expired
            - time_classes: This is a subscription membership that limits the number of classes the user can go to. Eg; Max 12 Classes a month
        start_date:
          type: number
          description: The date the current membership cycle starts. This is returned in the location's local time in epoch format.
        expiry_date:
          type: number
          description: The date the current membership cycle ends. This is returned in the location's local time in epoch format.
        status:
          type: string
          description: The status of the membership, one of these strings 'FUTURE','ACTIVE','LOCKED','PAUSED','CANCELLED','EXPIRED'
        membership_name:
          type: string
          description: The membership name (group of plans)
        description:
          type: string
          description: The description of the membership.
        membership_plan_name:
          type: string
          description: The name of the plan.
    UserFilters:
      type: object
      description: Filters to filter users.
      properties:
        branch_id:
          type: string
          description: 'If provided, filter returned users from provided branch ID.'
        home_user:
          type: boolean
          description: >
            This is used for roaming studios. Set to true if you want leads from 1 studio. False if you want leads from
            all roaming studios. Should be provided with filter by branch_id.
        deleted:
          type: boolean
          description: Filter by deleted users. By default only active users are returned.
        lead_status:
          type: array
          enum:
            - LEAD
            - TRIAL
            - COLD
            - MEMBER
            - NO_SALE_TRIAL
            - TOUR
            - NO_SALE_TOUR
          items:
            type: string
            enum:
              - LEAD
              - TRIAL
              - COLD
              - MEMBER
              - NO_SALE_TRIAL
              - TOUR
              - NO_SALE_TOUR
        source:
          type: array
          items:
            type: string
            enum:
              - MEMBER_APP
              - CLASSPASS
              - DASHBOARD
              - KIOSK
              - ADMIN_APP
              - WEBPORTAL
              - IMPORTS
              - MANUAL_MEMBER_TRANSFER
              - CLI
              - UNKNOWN
        name:
          type: string
          description: >
            If is provided, users will be filtered by name. The search will be applied within fields: name, first name,
            last name, email, phone.
        created:
          type: object
          description: Created date.
          properties:
            start:
              type: integer
              description: Start date of created field in UnixTimestamp format. The timestamp should be sent in UTC.
            end:
              type: integer
              description: End date of created field in UnixTimestamp format. The timestamp should be sent in UTC.
        modified:
          type: object
          description: Modified date.
          properties:
            start:
              type: integer
              description: Start date of modified field in UnixTimestamp format. The timestamp should be sent in UTC.
            end:
              type: integer
              description: End date of modified field in UnixTimestamp format. The timestamp should be sent in UTC.
        checkin_num:
          type: object
          description: Filter by check-in/access count.
          properties:
            min:
              type: integer
            max:
              type: integer
        booking_num:
          type: object
          description: Filter by bookings count.
          properties:
            min:
              type: integer
            max:
              type: integer
        last_booking:
          type: object
          description: Filter by the date of last booking.
          properties:
            start:
              type: integer
              description: Start date of the booking period in UnixTimestamp format. The timestamp should be sent in UTC.
            end:
              type: integer
              description: End date of the booking period in UnixTimestamp format. The timestamp should be sent in UTC.
        last_interaction:
          type: object
          description: Filtering by last interactions with user.
          properties:
            is:
              type: boolean
              description: If there was contact with user or not.
            start:
              type: string
              description: Start of the filtered period. Should be provided in UTC format 'YYYY-MM-DD'.
            end:
              type: string
              description: End of the filtered period. Should be provided in UTC format 'YYYY-MM-DD'.
        expiry:
          type: object
          description: Filter by membership expiry date.
          properties:
            start:
              type: integer
              description: Start of expiry date in UnixTimestamp format. The timestamp should be sent in UTC.
            end:
              type: integer
              description: End of expiry date in UnixTimestamp format. The timestamp should be sent in UTC.
        status_history:
          type: object
          description: |-
            The history of the lead. If you want to filter users, which achieved their status during specified period of time, you need provide the list of desired statuses/status and specify the period. You should indicate both start and end dates. It's mandatory to specify period, if not, the filter will not be applied.
            If you pass 'status_history' filter, the response will contain leadEvent object with all historical data of the requested status. Otherwise leadStatus object is null.
          properties:
            status:
              type: string
              enum:
                - LEAD
                - TRIAL
                - CLIENT
                - NO_SALE_TRIAL
                - TOUR
                - NO_SALE_TOUR
              description: 'Pay attention, that there are only 4 valid statuses in history. Valid history status for member is CLIENT. If you provide invalid status, the filter will not be applied.'
            start:
              type: integer
              description: Start of search period in UnixTimestamp format. The timestamp should be sent in UTC.
            end:
              type: integer
              description: End of search period in UnixTimestamp format. The timestamp should be sent in UTC.
    PasswordReset:
      type: object
      properties:
        branch_id:
          type: string
          description: The branch_id of the studio.
        email:
          type: string
          description: The users email address.
    PasswordResetResponse:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
          description: Message stating reset email sent
    BaseModelPagination:
      type: object
      description: Information about pagination.
      properties:
        object:
          type: string
          default: list
        page:
          type: integer
          default: 1
          description: The current page number.
        limit:
          type: integer
          default: 50
          minimum: 0
          maximum: 100
          description: The number of items per page.
        has_more:
          type: boolean
          default: false
          description: |-
            Flag that indicates if there are more available pages.
            In case of the total count of returned objects exceeds the limit for the page, has_more returns true.
            It means you have to proceed to paginate to get the full amount of data.
        total_count:
          type: integer
          description: The total number of available items.
    MembershipResponseContainer:
      allOf:
        - $ref: '#/components/schemas/BaseModelPagination'
        - type: object
          properties:
            data:
              type: array
              items:
                $ref: '#/components/schemas/Membership'
              description: Array of Membership objects.
          required:
            - data
    Membership:
      type: object
      properties:
        _id:
          type: string
          description: Unique identifier for the membership.
        branch_id:
          type: string
          description: The branch_id of the studio.
        namespace:
          type: string
          description: The namespace of the studio.
        active:
          type: boolean
          description: If this is false the membership shows as deleted in Glofox. Memberships can be restored in Glofox by staff.
        name:
          type: string
          description: |
            The name of the membership.
            22 recommended character limit,  not a hard limit. Recommendation is based on how the memberships are presented to the end users in the apps.
        description:
          type: string
          description: |
            The description of the membership.
            160 recommended character limit, not a hard limit. Recommendation is based on how the memberships are presented to the end users in the apps.
        buy_just_once:
          type: boolean
          description: If this is true a user can only purchase this membership one time.
        plans:
          type: array
          items:
            $ref: '#/components/schemas/MembershipPlan'
          description: An array of MembershipPlan objects.
      required:
        - branch_id
        - namespace
        - active
        - name
        - description
        - plans
    MembershipPlan:
      type: object
      properties:
        code:
          type: integer
          description: Unique identifier for the plan. This is unique system wide.
        type:
          type: string
          description: |
            Plans can have three types

            time - This is an unlimited membership with no restriction on the number of classes that can be booked with the membership

            time_classes - This is a restricted membership used with subscriptions. This membership comes with credits. The number of credits is set in the credits array of the plan. This is the maximum number of classes the user can book.

            num_classes - This is a restricted membership that cannot be a subscription. It is the same as time_classes
        duration_time_unit:
          type: string
          description: 'Specifies billing frequency. One of; day, week, month or year.'
        duration_time_unit_count:
          type: number
          description: Used with duration_time_count to define the billing frequency.
        starts_on:
          type: string
          description: |
            This can have two values, PURCHASE_DATE or FIRST_BOOKING_DATE
            PURCHASE_DATE means the membership starts the date the user pays for it.
            FIRST_BOOKING_DATE means the start date is set when the user makes a booking. The start date will not be set until the user books a class with the membership.
        price:
          type: integer
          description: |
            For a subscription membership this is the recurring fee
            For a one time fee memberhsips this is the full price which is paid upfront
        upfront_fee:
          type: integer
          description: This is only set to subscription memberships. It is a one time fee paid when the membership is purchase, referred to as a Joining Fee
      required:
        - code
        - type
    MembershipPurchaseSuccess:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
          description: Message saying purchase complete or contains the reason why purchase could not be completed
    Credits:
      type: object
      properties:
        _id:
          type: string
          description: Unique identifier for the credits.
        branch_id:
          type: string
          description: The branch_id of the studio.
        user_id:
          type: string
          description: The id of the user who owns the credit pack.
        model:
          type: string
          description: |
            This can have three values which defines the type of event the credits can be used for.

            - programs = Classes

            - users = Trainer Appointments (deprecated)

            - appointments = Trainer Appointments

            - facilities = Facility Appointments
        num_sessions:
          type: integer
          description: 'The number classes that can be booked, ie total credits'
        active:
          type: boolean
          description: Credit packs can only be used when active is true. If set to false the staff cancelled the credit pack and/or membership in the admin dashboard.
        bookings:
          type: array
          items:
            type: string
          description: This array lists the booking_id of bookings made with the credit pack. It only includes an active booking. If a booking is cancelled the booking_id is removed from this array.
        start_date:
          type: string
          description: Bookings can be made if the class start time is greater than the start_date. The time is in UTC.
        end_date:
          type: string
          description: Bookings can be made if the class end time is less than the end_date. The time is in UTC.
        membership_id:
          type: string
          description: If the credit park was part of a membership this value will be set to the id of the membership.
        membership_name:
          type: string
          description: If the credit park was part of a membership this value will be set to the name of the membership.
    Access:
      type: object
      properties:
        user_id:
          type: string
          description: Unique identifier for the user.
          pattern: '^[a-f\d]{24}$'
        namespace:
          type: string
          description: The studio's namespace. By default it is taken from the current user token.
        branch_id:
          type: string
          description: The branch_id of the studio. By default it is taken from the current user token.
          pattern: '^[a-f\d]{24}$'
        entry_at:
          type: integer
          description: UTC Timestamp when the user entered the studio. The default value is the current time.
        status:
          type: string
          description: |-
            The status of the access. By default is evaluated based on the user's membership.
            If the user has valid active membership or upcoming bookings within the nearest hour, then the access is granted.
          enum:
            - GRANTED
            - FAILED
        door:
          type: string
          description: The name of the door that was accessed
        door_type:
          type: string
          description: The type of door that was accessed (if absent defaults to external)
          enum:
            - external
            - internal
      required:
        - user_id
    AccessResponse:
      type: object
      properties:
        success:
          type: boolean
          description: Returns true if the access was successfully created, or false in case of error
        access:
          type: object
          properties:
            _id:
              type: string
              description: ID of the created access
              pattern: '^[a-f\d]{24}$'
            user_id:
              type: string
              description: Unique identifier for the user.
              pattern: '^[a-f\d]{24}$'
            branch_id:
              type: string
              description: The branch_id of the studio accessed.
              pattern: '^[a-f\d]{24}$'
            namespace:
              type: string
              description: The namespace of the studio accessed.
            entry_at:
              type: integer
              description: The time in UTC when the user accessed the studio. In most cases this will be the same as created however an integrator can send a different time if needed.
            status:
              type: string
              description: |
                GRANTED, FAILED or PENDING

                Granted is used if the user was allowed into the studio

                Failed is used if the user was denied access

                Pending is used when the user requested an access. The studio will validate their access on arrival
            valid_on_entry:
              type: boolean
              description: When the user access they are valid if they have a membership and the start date is in the past and the expiry date is in the future
            door:
              type: string
              description: The name of the door that was accessed
            door_type:
              type: string
              description: The type of door that was accessed (if absent defaults to external)
              enum:
                - external
                - internal
            created:
              type: number
              description: The time in UTC Timestamp when the access was created.
            modified:
              type: number
              description: The time in UTC Timestamp when the access was updated.
    BasicEvent:
      type: object
      properties:
        _id:
          type: string
          description: Unique identifier for the event.
        namespace:
          type: string
          description: The namespace of the studio.
        branch_id:
          type: string
          description: The branch_id of the studio.
        type:
          type: string
          description: The type of the event.
        active:
          type: boolean
          description: If this is false the studio deleted the event and users will not be able to book it.
        name:
          type: string
          description: The name of the event.
        description:
          type: string
          description: The description of the event.
        time_start:
          type: integer
          description: The time in UTC that the event starts at.
        duration:
          type: integer
          description: The number of minutes the event is on for. End time of the class is found by adding this to the time start.
        is_online:
          type: boolean
          description: Is it online or offline event
        image_url:
          type: string
          description:
            The URL of the event image. This field is always populated with an auto-generated URL, even if no image has been uploaded.
            If the image exists, it will be accessible through the URL. Otherwise, attempting to access it may result in a 403 (Forbidden) response, which is the existing behavior for missing files in S3.
            403 responses should be treated as an indication that the event has not uploaded an image.
        size:
          type: integer
          description: The capacity of the event.
        private:
          type: boolean
          description: If this is true studios do not want users to book this class themselves. Staff can book users in using the admin app or dashboard.
        booked:
          type: integer
          description: The number of spaces booked for the event. Remaining spaces is worked out by subtracting this number from size.
        waiting:
          type: integer
          description: The number of users on the waiting list. This will only be set able 0 when the event is full.
        modified:
          type: integer
          description: The time in UTC that the event was last modified.
      required:
        - type
    Class:
      allOf:
        - $ref: '#/components/schemas/BasicEvent'
        - type: object
          description: Class/Event
          properties:
            type:
              type: string
              default: event
              description: The type specifying classes.
            program_id:
              type: string
              description: The id of the program.
            level:
              type: string
              description: This is a string studios use to add a Level beside the class listing. It is optional.
            facility:
              type: string
              description: The id of the facility (room) the class is on in.
            trainers:
              type: array
              items:
                type: string
                description: This array contain the user id's of the trainers assigned to the class.
            status:
              type: string
              description: |
                When you get a class it will have a status that indicates if it can be booked or not. The status indicates that the booking window is open and if there are spaces remaining.

                Statuses;
                * AVAILABLE
                  * This status indicates the class has spaces remaining and there are spaces remaining. A booking can still fail for a user if their membership is not eligible; the most common reason is their membership is not in date when the class is on

                * BOOKING_WINDOW_PASSED
                  * This status indicates the booking window is in the past. The most likely scenario is the class has already happened.
                Studios can set up booking window rules so it is possible a class is in the future and the window is closed. Use the close_booking_time field to see when this time was.

                * BOOKING_WINDOW_NOT_OPEN
                  * This status indicated the window has not opened yet.
                Use the open_booking_time field to see when the booking window opens.

                * JOIN_WAITING_LIST
                  * This status indicates the class is full and the waiting list has space. Any attempt at booking will add the user to the waiting list, they will not be charged for joining the waiting list.

                * FULLY_BOOKED
                  * This status indicates the class is full and the waiting list is full. Any attempt at booking will fail.
            open_booking_time:
              type: integer
              description: An optional setting studios can use to control when people can book the class. If this is set users cannot book until this time is passed. The value is returned in UTC Timestamp format.
            close_booking_time:
              type: integer
              description: An optional setting studios can use to control when people can book the class. This will be set to the start time of the class if no rules are setup. The value is returned in UTC Timestamp format.
    TimeSlot:
      allOf:
        - $ref: '#/components/schemas/BasicEvent'
        - type: object
          description: TimeSlot
          properties:
            type:
              type: string
              default: timeslot
              description: The type specifying facilities and appointments.
            model:
              type: string
              description: |-
                The type of the timeslot.
                The model=facilities specifies Facility event.
                The model=users specifies Appointment with a trainer. It's a deprecated old version of private training sessions.
                The model=appointments specifies Appointment with a trainer.
              enum:
                - facilities
                - users
                - appointments
            model_id:
              type: string
              description: |-
                The ID of the model points to the concrete model's entity.
                For the facilities it's a facility_id on base of which a facility event is created.
                For the appointments it's an appointment_id on base of which a appointment event is created.
                For the users it's a user_id of the trainer with whom an appointment is created.
            open_booking_time:
              type: integer
              description: An optional setting studios can use to control when people can book the timeslot. If this is set users cannot book until this time is passed. The value is returned in UTC Timestamp format.
            close_booking_time:
              type: integer
              description: An optional setting studios can use to control when people can book the timeslot. This will be set to the start time of the timeslot if no rules are setup. The value is returned in UTC Timestamp format.
    Course:
      allOf:
        - $ref: '#/components/schemas/BasicEvent'
        - type: object
          description: Course
          properties:
            type:
              type: string
              default: course
              description: The type specifying courses.
            facility:
              type: string
              description: The ID of the facility (room) the course is on in.
            trainers:
              type: array
              items:
                type: string
                description: This array contain the user id's of the trainers assigned to the course.
            session_id:
              type: integer
              description: The ID of the session in the course schedule.
    BookingRequest:
      type: object
      properties:
        branch_id:
          description: The ID of the branch the booking ID made for. Required only for Admin side booking.
          type: string
        staff_id:
          description: In case of booking an appointment, this field should be passed.
          type: string
        user_id:
          description: The ID of the user the booking is being made for.
          type: string
        model:
          type: string
          description: Model specifies the type of the booking. For classes model=event, for courses model=course, for appointments and facilities model=timeslot.
          enum:
            - event
            - timeslot
            - course
        model_id:
          description: The ID of the event the booking is being made for.
          type: string
        create_slot:
          description: This parameter has to be set as appointment when attempt to book an available appointment.
          type: string
        payment_method:
          description: |-
            The payment method used to pay booking. Required only for Admin side booking.
            pay_gym payment method is used for the late payments. The payment will be charged later by the Studio's staff.
            Integrators should use credit_card as the only valid payment method.
          type: string
          enum:
            - cash
            - credit_card
            - bank_transfer
            - wallet
            - pay_gym
        price:
          description: When the request is done by a staff, the price to be charged could be passed. If price is not provided, the credits and membership will be considered
          type: number
          format: float
        schedule_id:
          description: The ID of the session (schedule) for course booking. Required only for Admin side course booking. This could be found in [/2.0/events](#/Classes/get_2_0_events) as `session_id` when a course is returned.
          type: integer
        charge:
          description:
            This parameter is specifically for integrators. When it is set to true, the system will first try to charge
            the user using their available credits. If the user does not have enough credits, then the system will
            attempt to charge with the provided payment method. Only credit card is accepted, other payment methods
            will be rejected. If the payment method is not set in the payload, the system will default to credit card.
            If the user credits and the credit card attempts both fail, the booking will not be processed and an error
            will be returned. When set to false, the system will not attempt to charge the user thus making the booking
            free of charge. Charge will default to true if it is not included in the payload.
          type: boolean
          default: true
      required:
        - model
        - model_id
        - user_id
    BookingRequestError:
      type: object
      properties:
        success:
          type: boolean
          description: whether the request returned a 200
        message:
          type: string
          description: message describing the error
        message_code:
          type: string
          description: received error code
    BookingEventAsAMember:
      description: Booking a class as a member
      type: object
      properties:
        model:
          type: string
          description: The type of the booked event.
          enum:
            - event
        model_id:
          type: string
          description: The ID of the class.
        guest_bookings:
          type: integer
          description: The number of guests the user is booking for.
          default: 0
        join_waiting_list:
          type: boolean
          description: Whether the user should be added to the waiting list.
            Send this parameter to `true` only when the class is full.
            Send `false` or not send it when the class still has available spots.
          default: false
        charge:
          type: boolean
          description: Whether the booking payment should be delayed.
          default: true
      required:
        - model
        - model_id
    BookingEventAsAStaff:
      description: Booking a class as a staff
      type: object
      properties:
        model:
          type: string
          description: The type of the booked event.
          enum:
            - event
        model_id:
          type: string
          description: The ID of the class.
        user_id:
          type: string
          description: The ID of the user who the booking is made for.
        guest_bookings:
          type: integer
          description: The number of guests the user is booking for.
          default: 0
        join_waiting_list:
          type: boolean
          description: Whether the user should be added to the waiting list.
            Send this parameter to `true` only when the class is full.
            Send `false` or not send it when the class still has available spots.
          default: false
        payment_method:
          type: string
          description: The payment method used to pay booking.
          enum:
            - complimentary
            - credit_card
          default: credit_card
        price:
          type: number
          format: float
          description: The price of the booking. By default, the original event price will be applied.
          example: 19.99
        charge:
          type: boolean
          description: Whether the booking payment should be delayed.
          default: true
      required:
        - model
        - model_id
        - user_id
    BookingTimeSlotAsAMember:
      description: Booking an appointment as a member
      type: object
      properties:
        model:
          type: string
          description: The type of the booked event.
          enum:
            - timeslot
        model_id:
          type: string
          description: The ID of the appointment. This can be found in [/2.1/branches/{branchId}/appointments-availability](#/Appointments%20Availability/getAppointmentsAvailability) as the `id` when an availability is returned.
        staff_id:
          type: string
          description: The ID of the staff member who made the booking.
        time_start:
          type: integer
          description: The time in Timestamp when the class starts.
          example: 1672531200
        charge:
          type: boolean
          description: Whether the booking payment should be delayed.
          default: true
      required:
        - model
        - model_id
        - staff_id
        - time_start
    BookingTimeSlotAsAStaff:
      description: Booking an appointment as a staff
      type: object
      properties:
        model:
          type: string
          description: The type of the booked event.
          enum:
            - timeslot
        model_id:
          type: string
          description: The ID of the appointment. This can be found in [/2.1/branches/{branchId}/appointments-availability](#/Appointments%20Availability/getAppointmentsAvailability) as the `id` when an availability is returned.
        staff_id:
          type: string
          description: The ID of the staff member who made the booking.
        time_start:
          type: integer
          description: The time in Timestamp when the class starts.
          example: 1672531200
        user_id:
          type: string
          description: The ID of the user who the booking is made for.
        payment_method:
          type: string
          description: The payment method used to pay booking.
          enum:
            - complimentary
            - credit_card
          default: credit_card
        price:
          type: number
          format: float
          description: The price of the booking. By default, the original appointment price will be applied.
          example: 19.99
        charge:
          type: boolean
          description: Whether the booking payment should be delayed.
          default: true
      required:
        - model
        - model_id
        - user_id
        - staff_id
        - time_start
    BookingCourseAsAMember:
      description: Booking a course as a member
      type: object
      properties:
        model:
          type: string
          description: The type of the booked event.
          enum:
            - course
        model_id:
          type: string
          description: The ID of the course.
        schedule_id:
          type: string
          description: The ID of the booked course session. This could be found in [/2.0/events](#/Classes/get_2_0_events) as `session_id` when a course is returned.
        guest_bookings:
          type: integer
          description: The number of guests the user is booking for.
          default: 0
      required:
        - model
        - model_id
        - schedule_id
    BookingCourseAsAStaff:
      description: Booking a course as a staff
      type: object
      properties:
        model:
          type: string
          description: The type of the booked event.
          enum:
            - course
        model_id:
          type: string
          description: The ID of the course.
        schedule_id:
          type: string
          description: The ID of the booked course session (schedule). This could be found in [/2.0/events](#/Classes/get_2_0_events) as `session_id` when a course is returned.
        guest_bookings:
          type: integer
          description: The number of guests the user is booking for.
          default: 0
        user_id:
          type: string
          description: The ID of the user who the booking is made for.
        payment_method:
          type: string
          description: The payment method used to pay booking.
          enum:
            - complimentary
            - credit_card
          default: credit_card
        price:
          type: number
          format: float
          description: The price of the booking. By default, the original course price will be applied.
          example: 19.99
      required:
        - model
        - model_id
        - user_id
        - schedule_id
    BookingFacilityAsAMember:
      description: Booking a facility as a member
      type: object
      properties:
        model:
          type: string
          description: The type of the booked event.
          enum:
            - facility
        model_id:
          type: string
          description: The ID of the facility time slot.
        charge:
          type: boolean
          description: Whether the booking payment should be delayed.
          default: true
      required:
        - model
        - model_id
    BookingFacilityAsAStaff:
      description: Booking a facility as a staff
      type: object
      properties:
        model:
          type: string
          description: The type of the booked event.
          enum:
            - facility
        model_id:
          type: string
          description: The ID of the facility time slot.
        user_id:
          type: string
          description: The ID of the user who the booking is made for.
        payment_method:
          type: string
          description: The payment method used to pay booking.
          enum:
            - complimentary
            - credit_card
          default: credit_card
        price:
          type: number
          format: float
          description: The price of the booking. By default, the original facility price will be applied.
          example: 19.99
        charge:
          type: boolean
          description: Whether the booking payment should be delayed.
          default: true
      required:
        - model
        - model_id
        - user_id
    BasicBooking:
      type: object
      properties:
        _id:
          type: string
          description: Unique identifier for the booking.
        branch_id:
          type: string
          description: The branch_id of the studio.
        namespace:
          type: string
          description: The namespace of the studio.
        user_id:
          type: string
          description: The id of the user booked into the event.
        user_name:
          type: string
          description: The name of the user booked into the event.
        status:
          type: string
          description: |
            A booking will have one of three statuses:
            * Booked - The user is booked into the event.
            * Waiting - The user is on the waiting list for the event. When a space is available they will be notified or they will be automatically added to the event, depending on the studios settings.
            * Canceled - The user has no active booking for the event. If the user re-books in this status will changed from Canceled to Booked.
            * Reserved - A reserved booking in case someone makes an attempt to book at the same time.
            * Failed - Unsuccessful attempt to make booking.
          enum:
            - BOOKED
            - WAITING
            - CANCELED
            - RESERVED
            - FAILED
    CreatedBooking:
      type: object
      properties:
        success:
          type: boolean
          description: Returns true if the booking was successfully created, or false in case of error
        Booking:
          allOf:
            - $ref: '#/components/schemas/BasicBooking'
            - oneOf:
                - $ref: '#/components/schemas/CreatedClassBooking'
                - $ref: '#/components/schemas/CreatedCourseBooking'
                - $ref: '#/components/schemas/CreatedTimeSlotBooking'
    CreatedClassBooking:
      type: object
      properties:
        type:
          type: string
          description: The type of the booked event.
          default: events
        program_id:
          type: string
          description: The id of the booked program.
        event_id:
          type: string
          description: The id of the booked event.
        event_name:
          type: string
          description: The name of the booked class.
        membership_name:
          type: string
          description: The name of the membership used to book the class.
        plan_name:
          type: string
          description: The name of the membership plan used to book the class.
    CreatedCourseBooking:
      type: object
      properties:
        type:
          type: string
          description: The type of the booked event.
          default: courses
        course_id:
          type: string
          description: The ID of the booked course.
        course_name:
          type: string
          description: The name of the booked course.
        session_id:
          type: integer
          description: The ID of the booked course session (schedule).
    CreatedTimeSlotBooking:
      type: object
      properties:
        type:
          type: string
          description: The type of the booked event.
          default: time_slots
        time_slot_id:
          type: string
          description: the ID of the booked time slot.
        model:
          type: string
          description: The type of the booked time slot.
          enum:
            - facilities
            - appointments
            - users
        model_id:
          type: string
          description: The ID of the booked model.
        event_name:
          type: string
          description: The name of the booked event.
    Booking:
      allOf:
        - $ref: '#/components/schemas/BasicBooking'
        - type: object
          properties:
            attended:
              type: boolean
              description: The value representing the users booking attendance.
            time_start:
              type: integer
              description: The time in UTC Timestamp when the class starts.
            duration:
              type: integer
              description: The number of minutes the class is on for. End time of the class is found by adding this to the time start.
            paid:
              type: boolean
              description: If the booking is paid.
            guest_bookings:
              type: integer
              description: |
                Users can book in guests. The number of spaces booked is guest_bookings + 1
                The booking object counts as 1.
                Example:
                Class size = 10
                guest_bookings = 3
                Remaining spaces = Class size - (guest_bookings + 1) = 10 - (3 +1) = 6
            description:
              type: string
              description: The description of the booked event.
            image_url:
              type: string
              description:
                The URL of the booked event image. This field is always populated with an auto-generated URL, even if no image has been uploaded.
                If the image exists, it will be accessible through the URL. Otherwise, attempting to access it may result in a 403 (Forbidden) response, which is the existing behavior for missing files in S3.
                403 responses should be treated as an indication that the booked event has not uploaded an image.
            model:
              type: string
              description: The type of the booked event.
              enum:
                - events
                - appointments
                - courses
                - facilities
                - users
            model_id:
              type: string
              description: |-
                The model_id of the associated model.
                For events this is event_id.
                For appointments this is appointment_id.
                For courses this is course_id.
                For facilities this is facility_id.
                For users this is user_id (this is the ID of the trainer the private session is booked with).
            created:
              type: integer
              description: The time in UTC Timestamp when the bookings was created.
            modified:
              type: integer
              description: The time in UTC Timestamp when the bookings was last updated.
    ClassBooking:
      allOf:
        - $ref: '#/components/schemas/Booking'
        - type: object
          properties:
            model:
              type: string
              default: event
              description: The type specifying classes.
    CourseBooking:
      allOf:
        - $ref: '#/components/schemas/Booking'
        - type: object
          properties:
            model:
              type: string
              default: course
              description: The type specifying courses.
    TimeSlotBooking:
      allOf:
        - $ref: '#/components/schemas/Booking'
        - type: object
          properties:
            model:
              type: string
              description: The type specifying time slot based booking.
              enum:
                - facilities
                - appointments
                - users
    BranchBooking:
      allOf:
        - $ref: '#/components/schemas/BasicBooking'
        - type: object
          properties:
            type:
              type: string
              description: The type of the booked event
              enum:
                - events
                - courses
                - time_slots
            program_id:
              type: string
            event_id:
              type: string
            event_name:
              type: string
            time_slot_id:
              type: string
              description: The ID of the booked time slot. Is populated for the time slots based events only.
            model:
              type: string
              description: The model type of the booked event. Is populated for the time slot based bookings only.
              enum:
                - facilities
                - appointments
                - users
                - courses
            model_id:
              type: string
              description: The ID of the model.
            model_name:
              type: string
            course_id:
              type: string
              description: The ID of the booked course. Is populated for the courses only.
            session_id:
              type: string
              description: the ID of the booked course session. Is populated for the courses only.
            guest_bookings:
              type: integer
              description: The number of guest bookings made for the current booking.
            attended:
              type: boolean
              description: If the booked event was attended by the user.
            paid:
              type: boolean
              description: If the booking was paid.
            is_from_waiting_list:
              type: boolean
              description: If the booked user was moved to booking list from waiting list.
            is_late_cancellation:
              type: boolean
              description: If the booking was cancelled after the cancellation window was closed.
            payment_method:
              type: string
              description: The payment method used for the booking.
            time_start:
              type: string
              description: The local start date and time of the booking.
            time_finish:
              type: string
              description: The local finish date and time of the booking.
            is_first:
              type: boolean
              description: If this is the users first booking with the studio .
            canceled_at:
              type: string
              description: The local date and time when the booking was cancelled.
            modified:
              type: string
              description: The date and time of the modification in UTC.
            created:
              type: string
              description: The date and time of the creation in UTC.
            batch_id:
              type: string
              description: The batch this booking belongs to.
    BookingCancelRequest:
      type: object
      properties:
        data:
          type: string
          description: A message stating that the booking was cancelled or containing the reason why it could not be cancelled.
    AttendanceRequest:
      type: object
      properties:
        model:
          type: string
          enum:
            - 'bookings'
          description: The model specifies the type of the attendance need to be saved. You have to send model=bookings to save bookings attendance.
        model_ids:
          type: array
          items:
            type: string
            description: An array of bookings ID that have to be marked as attended.
    AttendanceResponse:
      type: object
      properties:
        success:
          type: boolean
        model_ids:
          type: array
          items:
            type: string
            description: An array of bookingIds
    MembershipPurchaseRequest:
      type: object
      properties:
        start_date:
          type: number
          example: 1725519600
          description: The date the membership starts in the location's local time in epoch format.
        promo_code:
          type: string
          example: PROMO-123
          description: The public facing promo code.
        payment_method:
          type: string
          example: card
          description: Specifies the payment method to be used. Must be one of the accepted payment methods defined by the membership configuration. If not provided, the member's default payment method will be used. The value must be provided in lowercase.
        is_renewal_terms_and_conditions_accepted:
          type: boolean
          example: true
          description: Indicates whether the user has opted in to be charged in the future per the terms of the membership they are purchasing.
      required:
        - start_date
    PaymentMethods:
      type: object
      properties:
        _id:
          type: string
          description: Unique identifier for the payment method.
        branch_id:
          type: string
          description: The branch_id of the studio.
        active:
          type: boolean
          description: Set to true if the payment method is available to use
        staff_only:
          type: boolean
          description: 'If set to true only staff can use. Eg; Cash is always set to true as clients cannot choose to pay in the app with cash, they need to pay with card or direct debit'
        type_id:
          $ref: '#/components/schemas/PaymentMethodType'
        iframe:
          $ref: '#/components/schemas/PaymentMethodIframe'
    PaymentMethodType:
      type: object
      properties:
        _id:
          type: string
        name:
          type: string
          description: The name of the payment method
        charge_percentage:
          type: integer
          description: For some payment gateways a % is taken from each payment.
        fixed_charge:
          type: integer
          description: For some payment gateways a fixed charge is taken from each payment.
        publishable_key:
          type: string
          description: The publishable key related to the method
        account_id:
          type: string
          description: The id of the the account
        tokenization_handler:
          type: string
          description: 'An enumerator referring the the payment method, eg "STRIPE" or "GOCARDLESS"'
    FlexiblePaymentMethod:
      type: object
      properties:
        payment_method_type:
          type: string
          description: The payment method type we want to use to create a charge
    PaymentMethodIframe:
      type: object
      properties:
        parameters:
          type: object
          properties:
            color_accent:
              type: string
              description: The color of the input borders and lines
            color_background:
              type: string
              description: The background color of the iframe
            color_text:
              type: string
              description: The color of the text
        domain:
          type: string
        full_path:
          type: string
          description: 'Combine these and you have the full URL; {domain} + {full_path}'
    PaymentsReportRequest:
      type: object
      properties:
        branch_id:
          type: string
          description: The branch_id of the studio.
        namespace:
          type: string
          description: The namespace of the studio.
        start:
          type: string
          description: UNIX timestamp.
        end:
          type: string
          description: UNIX timestamp.
        secondStart:
          type: integer
          format: int64
          description: UNIX timestamp for the secondary date range. Required if `filter.CompareToRanges` is `true`.
        secondEnd:
          type: integer
          format: int64
          description: UNIX timestamp for the secondary date range. Required if `filter.CompareToRanges` is `true`.
        model:
          type: string
          description: 'Set to "model":"TransactionsList"'
        filter:
          type: object
          properties:
            ReportByMembers:
              type: boolean
              description: Setting to true changes the result to be focused on members
            CompareToRanges:
              type: boolean
              description: Enables comparison to a secondary date range. Requires `secondStart` and `secondEnd`.
            PaymentMethods:
              type: array
              items:
                type: object
                properties:
                  id:
                    type: string
                    description: 'Options; "cash", "credit_card", "bank_transfer", "direct_debit" & "complimentary"'
    PaymentsReportResponse:
      type: object
      properties:
        TransactionsList:
          type: object
          properties:
            details:
              type: array
              items:
                $ref: '#/components/schemas/Transaction'
    Transaction:
      type: object
      properties:
        _id:
          type: string
          description: UUID
        id:
          type: string
          description: Int id. This id is incremental
        transaction_status:
          type: string
          description: |
            The status can be one of the following; PAID, REFUNDED, ERROR, PENDING, PARTIAL_REFUNDED & SUBSCRIPTION_CYCLE_PAYMENT_FAILED
        transaction_provider_id:
          type: string
          description: The id of the payment provider.
        amount:
          type: integer
          description: The payment amount.
        currency:
          type: string
          description: The currency the payment was taken in. A studio's payments will all be in the same currency
        paid:
          type: boolean
          description: true for successful payments that have not been refunded
        description:
          type: string
          description: A description of the payment.
        sold_by_user_id:
          type: string
          description: The id of the user who processed the transaction.
        created:
          type: string
          description: The time in UTC when the payment was created.
        modified:
          type: string
          description: The time in UTC when the payment was last updated.
        metadata:
          type: object
          properties:
            namespace:
              type: string
              description: The namespace of the studio.
            branch_id:
              type: string
              description: The branch_id of the studio.
            glofox_event:
              type: string
              description: 'The payment type can be one of the following; subscription_payment, upfront_payment, subscription_prorate, book_class, book_course, book_time_slot, buy_product, custom_charge'
            payment_method:
              type: string
              description: 'The payment methods can be one of the following; CARD, DIRECT_DEBIT, CASH, COMPLIMENTARY, BANK_TRANSFER & PAY_LATER'
    TrainerReportResponse:
      type: object
      properties:
        success:
          type: boolean
        report:
          type: object
          properties:
            trainer_id:
              type: object
              description: This is the user id of the trainer. Multiple trainer ids will be returned
              properties:
                _id:
                  type: boolean
                  description: Unique identifier for the trainer.
                name:
                  type: string
                  description: The trainers name.
                type:
                  type: string
                  description: This will always be TRAINER.
                active:
                  type: string
                  description: Deleted trainers will be returned in this call if the undertook a class in the time period sent. Delete is represented as active = false.
                programs:
                  $ref: '#/components/schemas/TrainerReportResult'
                appointments:
                  $ref: '#/components/schemas/TrainerReportResult'
    TrainerReportResult:
      type: object
      properties:
        events:
          type: integer
          description: The number of classes
        bookings:
          type: integer
          description: The number of bookings
        attendance:
          type: integer
          description: The number of bookings marked as attended
        capacity:
          type: integer
          description: The maximum possible bookings
    TermsConditionsResponse:
      type: array
      items:
        type: object
        properties:
          _id:
            type: string
            description: Unique identifier for the document.
          branch_id:
            type: string
            description: The branch_id of the studio.
          namespace:
            type: string
            description: The namespace of the studio.
          type:
            type: string
            description: The type of document. If you are integrating electronic agreements the first waiver to use is 'type = member.authenticated'
    SendElectronicAgreementRequest:
      type: object
      properties:
        trigger:
          type: string
          description: The type of document. If integrating electronic agreements the first waiver to use is 'trigger = member.authenticated'. For membership terms and conditions the waiver to use is 'trigger = membership.purchased'
          enum:
            - member.authenticated
            - membership.purchased
      required:
        - trigger
    PriceBreakdownRequestProduct:
      type: object
      properties:
        price:
          type: number
          description: The sum price of the item.
          example: 2
        unit_price:
          type: number
          description: The price of a single unit of the item
          example: 1
        quantity:
          type: number
          description: The quantity of the item
          example: 2
        promo_code:
          type: string
          example: PROMO-123
          description: The promo code.
        discount_ids:
          items:
            type: string
          type: array
          description: The private ID of a discount.
        service_type:
          type: string
          example: products
          description: The type of item. This determines how the associated `service_id` will be interpreted for tax calculation.
          enum:
            - appointments
            - classes
            - courses
            - facilities
            - fees
            - memberships
            - products
            - services
            - trainers
        service_id:
          type: string
          example: product-id-123
          description: >
            The ID of the item associated with the service_type. Used to determine applicable taxes during purchase.
            Expected values depend on the service_type:
              - `appointments`:  time_slot_pattern ID of appointment.
              - `services`: Service definition ID (e.g. `user-service-13:service-definition-7489`).
              - `classes`: Program ID.
              - `courses`: Course model ID.
              - `facilities`: Facility model ID.
              - `memberships`: Membership definition ID (e.g `user-membership-13:membership-definition-7489`).
              - `products`: Product model ID (not presentation ID).
              - `trainers`: User model ID.

              Note: The API does not validate the correctness of the `service_id`. 
              If an invalid or unknown ID is provided, it will be silently ignored and no error will be returned.
        sub_service_id:
          type: string
          example: product-presentation-id-123
          description: >
            The sub identifier of the item.
              - `memberships`: The membership plan's code.
              - `products`: The presentation ID of the product.

    GetPriceBreakdownRequest:
      type: object
      properties:
        products:
          items:
            $ref: '#/components/schemas/PriceBreakdownRequestProduct'
          type: array
    PriceBreakdownAppliedDiscount:
      type: object
      properties:
        discount_amount:
          type: number
          example: 0.4
        id:
          type: string
          example: discount-id-1
        name:
          type: string
          example: 10% Off Special
        promo_code:
          $ref: '#/components/schemas/PriceBreakdownAppliedPromoCode'
        rate_type:
          type: string
          example: percentage
        rate_value:
          type: number
          example: 10
      required:
        - rate_type
    PriceBreakdownAppliedPromoCode:
      type: object
      properties:
        id:
          type: string
        code:
          type: string
    PriceBreakdownDiscountsBreakdownItem:
      properties:
        applied_discounts:
          items:
            $ref: '#/components/schemas/PriceBreakdownAppliedDiscount'
          type: array
        discounted_price:
          type: number
          example: 1.6
        discounts_total:
          type: number
          example: 0.4
        product_price:
          type: number
          example: 2
      type: object
    PriceBreakdownDiscounts:
      type: object
      properties:
        products:
          items:
            $ref: '#/components/schemas/PriceBreakdownDiscountsBreakdownItem'
          type: array
    PriceBreakdownAppliedTax:
      type: object
      properties:
        id:
          type: string
          example: id-123
        name:
          type: string
          example: City tax
        price:
          type: number
          example: 0.19
        rate:
          type: number
          example: 12
    PriceBreakdownTaxesItem:
      type: object
      properties:
        applied_taxes:
          items:
            $ref: '#/components/schemas/PriceBreakdownAppliedTax'
          type: array
        net_price:
          type: number
          example: 1.6
        product_price:
          type: number
          example: 1.6
        tax_total:
          type: number
          example: 0.19
        total_price:
          type: number
          example: 1.79
    PriceBreakdownTaxes:
      type: object
      properties:
        products:
          items:
            $ref: '#/components/schemas/PriceBreakdownTaxesItem'
          type: array
    GetPriceBreakdownResponse:
      type: object
      properties:
        discounts:
          $ref: '#/components/schemas/PriceBreakdownDiscounts'
        taxes:
          $ref: '#/components/schemas/PriceBreakdownTaxes'
    GetPriceBreakdownError:
      type: object
      properties:
        success:
          type: boolean
          description: Whether the request returned a 200
        message:
          type: string
        message_code:
          type: string
        message_data:
          type: array
          items:
            type: string
        errors:
          type: array
          items:
            type: string
    CalculateAppointmentPriceRequest:
      type: object
      properties:
        member_id:
          type: string
        time_start:
          type: integer
      required:
        - member_id
        - time_start
    CalculateFacilityPriceRequest:
      type: object
      properties:
        member_id:
          type: string
        time_start:
          type: integer
      required:
        - member_id
        - time_start
    CalculateCoursePriceRequest:
      type: object
      properties:
        member_id:
          type: string
        number_of_bookings:
          type: integer
      required:
        - member_id
        - number_of_bookings
    CalculateEventPriceRequest:
      type: object
      properties:
        member_id:
          type: string
        number_of_bookings:
          type: integer
      required:
        - member_id
        - number_of_bookings
    CalculatePriceResponse:
      type: object
      properties:
        success:
          type: boolean
        data:
          type: array
          items:
            properties:
              credits:
                type: integer
              price:
                type: integer
              currency:
                type: string
    CalculateAppointmentPriceResponse:
      $ref: '#/components/schemas/CalculatePriceResponse'
    CalculateCoursePriceResponse:
      $ref: '#/components/schemas/CalculatePriceResponse'
    CalculateFacilityPriceResponse:
      $ref: '#/components/schemas/CalculatePriceResponse'
    CalculateEventPriceResponse:
      $ref: '#/components/schemas/CalculatePriceResponse'
    CalculatePriceError:
      $ref: '#/components/schemas/GenericErrorResponse'
    PreCheckoutRequest:
      type: object
      properties:
        line_items:
          type: array
          minItems: 1
          description: The items to be purchased
          items:
            $ref: '#/components/schemas/LineItems'
    LineItems:
      type: object
      properties:
        attributes:
          $ref: '#/components/schemas/MemberFacingCheckoutRequestLineItemAttributes'
        promo_code:
          type: string
          description: The promo code to be applied to the line item
        service_id:
          type: string
          description: The identifier of the item being purchased eg the membership id
        service_type:
          type: string
          description: The type of the item being purchased
          enum:
            - classes
            - custom_charges
            - courses
            - facilities
            - memberships
            - products
            - services
            - trainers
            - wallet-overdraft
            - wallet-top-up
        sub_service_id:
          type: string
          description: >-
            The identifier of the item variant being purchased eg the membership's
            plan code or the product's presentation id
      required:
        - service_id
        - service_type
        - sub_service_id
    MemberFacingCheckoutRequestLineItemAttributes:
      type: object
      properties:
        membership:
          $ref: '#/components/schemas/MemberFacingMembershipAttributes'
    MemberFacingMembershipAttributes:
      type: object
      properties:
        utc_start_date:
          type: string
          description: The ISO-8601 format start date of the membership in UTC
    PreCheckoutResponse:
      type: object
      properties:
        currency:
          type: string
          description: The currency of the transaction
        due_today:
          $ref: '#/components/schemas/PreCheckoutResponsePriceBreakdown'
        future_charges:
          $ref: '#/components/schemas/PreCheckoutResponseRecurringLineItem'
        location_id:
          type: string
          description: The location id of the studio
        member_id:
          type: string
          description: The member id
        tax_mode:
          type: string
          description: The tax mode of the studio
          enum:
            - exclusive
            - inclusive
    PreCheckoutResponsePriceBreakdown:
      type: object
      properties:
        accepted_payment_methods:
          type: array
          items:
            type: string
            description: Type is the type of payment method
        amount:
          type: number
          description: 'price after discounts are applied, including taxes'
        discounts:
          type: array
          items:
            $ref: '#/components/schemas/CartAggregateDiscount'
        line_items:
          type: array
          items:
            $ref: '#/components/schemas/PreCheckoutResponseLineItem'
        net_price:
          type: number
          description: 'price after discounts are applied, excluding taxes'
        taxes:
          type: array
          items:
            $ref: '#/components/schemas/CartAggregateTax'
        total_discount:
          type: number
          description: total discount amount
        total_taxes:
          type: number
          description: total tax amount
    PreCheckoutResponseRecurringLineItem:
      type: object
      properties:
        accepted_payment_methods:
          type: array
          items:
            type: string
            description: Type is the type of payment method
        amount:
          type: number
          description: 'price after discounts are applied, including taxes'
        category:
          type: string
          description: The category of the line item
          enum:
            - RECURRING_CHARGE_MEMBERSHIP
            - RECURRING_CHARGE_MAINTENANCE_FEE
        delay_charge_days:
          type: integer
          description: How many days after purchase that this recurring charge will begin
        discounts:
          type: array
          items:
            $ref: '#/components/schemas/CartAggregateDiscount'
        duration_time_unit:
          type: string
          description: The unit of time for the duration
          enum:
            - DAY
            - WEEK
            - MONTH
        duration_time_unit_count:
          type: integer
          description: How many of the duration time unit
        line_items:
          type: array
          items:
            $ref: '#/components/schemas/PreCheckoutResponseLineItem'
        net_price:
          type: number
          description: 'price after discounts are applied, excluding taxes'
        taxes:
          type: array
          items:
            $ref: '#/components/schemas/CartAggregateTax'
        total_discount:
          type: number
          description: total discount amount
        total_taxes:
          type: number
          description: total tax amount
    CartAggregateDiscount:
      type: object
      properties:
        id:
          type: string
          description: The id of the discount
        name:
          type: string
          description: The name of the discount
        num_cycles:
          type: integer
          description: >-
            NumCycles is the number of cycles the discount is applied for. 0 means
            it is unlimited
        rate:
          type: number
          description: |
            Based on the type, this is either the percentage value or the fixed amount of the discount.
            - for percentage, this is the percentage value (0.001% - 100.000%) of the discount. Represented as a decimal value (e.g., 0.1234 represents 12.34%)
            - for fixed, this is the fixed amount of the discount. Represented as a decimal value (e.g., 12.34 represents $12.34)
        total_applied:
          type: number
          description: TotalApplied is the total amount of the discount applied
        type:
          type: string
          description: 'Type is the type of discount, percentage or fixed'
    CartAggregateTax:
      type: object
      properties:
        id:
          type: string
          description: The id of the tax
        name:
          type: string
          description: The name of the tax
        rate:
          type: number
          description: Rate is the percentage rate in the form of a decimal i.e. 1% = 0.01
        total_applied:
          type: number
          description: TotalApplied is the total amount of the tax applied
    PreCheckoutResponseLineItem:
      type: object
      properties:
        amount:
          type: number
          description: The price of the item
        metadata:
          $ref: '#/components/schemas/LineItemMetadata'
        service_id:
          type: string
          description: The identifier of the item being purchased eg the membership id
        service_name:
          type: string
          description: The name of the item being purchased
        service_sub_type:
          type: string
          description: The sub type of the item being purchased
        service_type:
          type: string
          description: The type of the item being purchased
        sub_service_id:
          type: string
          description: sub service level info
        sub_service_name:
          type: string
          description: sub service level info
    LineItemMetadata:
      type: object
      properties:
        addon:
          $ref: '#/components/schemas/AddOnMetadata'
        membership:
          $ref: '#/components/schemas/MembershipMetadata'
        product:
          $ref: '#/components/schemas/ProductMetadata'
        recurring_fee:
          $ref: '#/components/schemas/RecurringFeeMetadata'
    AddOnMetadata:
      type: object
      properties:
        credit:
          $ref: '#/components/schemas/AddOnMetadataPlanCredit'
        duration:
          $ref: '#/components/schemas/AddOnMetadataPlanDuration'
        is_credit_based:
          type: boolean
          description: If the add-on is credit based
        is_recurring:
          type: boolean
          description: If the add-on is recurring
        plan_id:
          type: string
          description: The id of the plan
        plan_name:
          type: string
          description: The name of the plan
        type:
          type: string
          description: The type of the plan
    AddOnMetadataPlanCredit:
      type: object
      properties:
        count:
          type: integer
          description: The number of credits
        is_carry_over:
          type: boolean
          description: If the credits can be carried over
    AddOnMetadataPlanDuration:
      type: object
      properties:
        count:
          type: integer
          description: The number of days
        duration_unit:
          type: string
          description: The unit of time
        has_end_date:
          type: boolean
          description: If the plan has an end date
        total_num_cycles:
          type: integer
          description: The total number of cycles
    MembershipMetadata:
      type: object
      properties:
        auto_renewal:
          type: boolean
          description: If the membership is set to auto renew
        duration_time_unit:
          type: string
          description: The unit of time for the duration
        duration_time_unit_count:
          type: integer
          description: The number of the duration time unit
        free_time_unit_time:
          type: integer
          description: The free time unit time
        has_end:
          type: boolean
          description: If the membership has an end
        is_group_membership:
          type: boolean
          description: If the membership is a group membership
        is_prorated:
          type: boolean
          description: If the membership is prorated
        min_price:
          type: number
          description: The minimum price
        payment_methods:
          $ref: '#/components/schemas/MembershipPaymentMethod'
        plan_code:
          type: string
          description: The plan code
        plan_name:
          type: string
          description: The plan name
        starts_on:
          type: string
          description: The start date
        subscription_plan_duration:
          type: string
          description: The subscription plan duration
        subscription_plan_id:
          type: string
          description: The subscription plan id
        type:
          type: string
          description: The type
        upfront_fee:
          type: number
          description: The upfront fee
    MembershipPaymentMethod:
      type: object
      properties:
        active:
          type: boolean
          description: If the payment method is active
        type_id:
          type: string
          description: The type id
    MembershipCancellationRequest:
      type: object
      required:
        - when
        - reason
      properties:
        when:
          type: string
          enum: [ON_DATE, NOW, END_OF_CYCLE]
          description: |
            A string indicating when the membership cancellation should take effect. Possible values:  
            - ON_DATE: cancellation to happen on a specific date
            - NOW: immediate cancellation (not supported yet)
            - END_OF_CYCLE: cancellation at the end of the current payment cycle (not supported yet)
        local_date:
          type: string
          example: '2026-05-25'
          description: >
            When ON_DATE cancellation type is selected. Local date in YYYY-MM-DD format to indicate the date for the cancellation
        reason:
          type: string
          enum:
            [
              MEMBERSHIP_CANCELLATION_PRICE,
              MEMBERSHIP_CANCELLATION_MOVED,
              MEMBERSHIP_CANCELLATION_MOVED,
              MEMBERSHIP_CANCELLATION_NO_USAGE,
              MEMBERSHIP_CANCELLATION_CUSTOMER_SERVICE,
              MEMBERSHIP_CANCELLATION_EVENT_SCHEDULE,
              MEMBERSHIP_CANCELLATION_CHANGE_MEMBERSHIP,
            ]
          description: Cancellation reason, can be an empty string or any of the valid values.
    MembershipCancellationResponse:
      type: object
      properties:
        local_planned_end_date:
          type: string
          example: '2026-05-25'
          description: Local date, in YYYY-MM-DD format, when the membership cancellation will occur.
    ProductMetadata:
      type: object
      properties:
        presentation_id:
          type: string
          description: The presentation id
        presentation_name:
          type: string
          description: The presentation name
        product_name:
          type: string
          description: The product name
        stock:
          type: integer
          description: The stock
    RecurringFeeMetadata:
      type: object
      properties:
        fee_name:
          type: string
          description: The fee name
        price:
          type: number
          description: The price
    UserConsent:
      type: object
      properties:
        email:
          type: object
          properties:
            active:
              type: boolean
              description: If the user has opted to receive email marketing. Set to true if they have opted in.
        sms:
          type: object
          properties:
            active:
              type: boolean
              description: If the user has opted to receive sms marketing. Set to true if they have opted in.
        push:
          type: object
          properties:
            active:
              type: boolean
              description: If the user has opted to receive push notifications marketing. Set to true if they have opted in.
    UserAgreementsResponse:
      type: object
      properties:
        success:
          type: boolean
        agreements:
          type: array
          items:
            $ref: '#/components/schemas/Agreement'
    AgreementTemplateResponse:
      type: object
      properties:
        version:
          type: integer
          example: 1
        created_at:
          type: string
          format: date-time
          example: '2025-04-03T16:53:04.253Z'
        updated_at:
          type: string
          format: date-time
          example: '2025-04-03T16:53:04.253Z'
        template:
          type: string
          example: 'string'
    Agreement:
      type: object
      properties:
        id:
          type: string
        member_id:
          type: string
        studio_id:
          type: string
        document_id:
          type: string
        status:
          type: string
          description: |
            The status of the agreement, indicating its current state:
            - `outstanding`: The agreement is in an outstanding status.
            - `accepted_outdated_requires_new`: The agreement is accepted and outdated but requires a new agreement (needs to sign again).
            - `accepted_outdated`: The agreement is in an accepted but outdated status (valid but not the latest version of the agreement).
            - `accepted`: The agreement is in an accepted status.
          enum:
            - outstanding
            - accepted_outdated
            - accepted_outdated_requires_new
            - accepted
        external_reference:
          type: string
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
    AccessEvent:
      type: object
      required:
        - Type
        - Metadata
        - Timestamp
        - Payload
      properties:
        Type:
          type: string
          description: The payload event type.
          enum:
            - MEMBER_ACCESS_INFO_CREATED
            - MEMBER_ACCESS_INFO_UPDATED
        Metadata:
          type: object
          required:
            - trace_id
            - location_id
            - version
          properties:
            trace_id:
              type: string
              description: Trace Identifier.
            location_id:
              type: string
              description: Location Identifier.
              pattern: '^[a-f\d]{24}$'
            version:
              type: string
              description: The payload version.
        Timestamp:
          type: string
          format: date-time
          description: The timestamp when the event occurred.
        Payload:
          type: object
          required:
            - user_id
            - Identifiers
          properties:
            user_id:
              type: string
              description: User identifier.
              pattern: '^[a-f\d]{24}$'
            identifiers:
              type: object
              description: Access Identifiers.
              required:
                - barcode
                - fob_id
              properties:
                barcode:
                  type: string
                fob_id:
                  type: string
    CourseBookingEvent:
      type: object
      required:
        - Type
        - Metadata
        - Timestamp
        - Payload
      properties:
        Type:
          type: string
          description: The payload event type.
          enum:
            - COURSE_BOOKING_CREATED
            - COURSE_BOOKING_DELETED
        Metadata:
          type: object
          required:
            - trace_id
            - location_id
            - version
          properties:
            trace_id:
              type: string
              description: Trace Identifier
            location_id:
              type: string
              description: Location Identifier
              pattern: '^[a-f\d]{24}$'
            version:
              type: string
              description: The payload version
        Timestamp:
          type: string
          format: date-time
          description: The timestamp when the event occurred
        Payload:
          type: object
          required:
            - id
            - user_id
            - course_id
            - status
            - date_start
            - date_finish
            - guest_bookings
            - paid
            - schedule
            - created
            - modified
          properties:
            id:
              type: string
              description: Booking identifier.
              pattern: '^[a-f\d]{24}$'
            user_id:
              type: string
              description: User identifier.
              pattern: '^[a-f\d]{24}$'
            course_id:
              type: string
              description: Course identifier.
              pattern: '^[a-f\d]{24}$'
            status:
              type: string
              description: The status of the booking.
              enum:
                - BOOKED
                - CANCELED
            date_start:
              type: string
              format: date
              description: The time when the course starts.
              example: '2025-01-16'
            date_finish:
              type: string
              format: date
              description: The time when the course ends.
              example: '2025-01-16'
            guest_bookings:
              type: integer
              description: Users can book in guests. The number of spaces booked is guest_bookings + 1. The booking object counts as 1. Example, Class size = 10; guest_bookings = 3; Remaining spaces = Class size - (guest_bookings + 1) = 10 - (3 +1) = 6.
            paid:
              type: boolean
              description: If the booking is paid.
            schedule:
              type: array
              items:
                type: object
                properties:
                  day_of_week:
                    type: integer
                    description: The day of the week the course is on (0 for Sunday, 1 for Monday, etc.).
                    enum:
                      - 0
                      - 1
                      - 2
                      - 3
                      - 4
                      - 5
                      - 6
                  day_name:
                    type: string
                    description: The name of the day of the week.
                    enum:
                      - SUN
                      - MON
                      - TUE
                      - WED
                      - THU
                      - FRI
                      - SAT
                  slots:
                    type: array
                    items:
                      type: object
                      properties:
                        time_start:
                          type: string
                          description: The time when the course starts.
                        time_end:
                          type: string
                          description: The time when the course ends.
            created:
              type: string
              format: date-time
              description: The time when the booking was created.
              example: '2025-01-15T16:12:00Z'
            modified:
              type: string
              format: date-time
              description: The time when the booking was last updated.
              example: '2025-01-15T17:18:30Z'
    BookingEvent:
      type: object
      required:
        - Type
        - Metadata
        - Timestamp
        - Payload
      properties:
        Type:
          type: string
          description: The payload event type.
          enum:
            - BOOKING_CREATED
            - BOOKING_DELETED
        Metadata:
          type: object
          required:
            - trace_id
            - location_id
            - version
          properties:
            trace_id:
              type: string
              description: Trace Identifier.
            location_id:
              type: string
              description: Location Identifier.
              pattern: '^[a-f\d]{24}$'
            version:
              type: string
              description: The payload version
        Timestamp:
          type: string
          format: date-time
          description: The timestamp when the event occurred.
        Payload:
          type: object
          required:
            - id
            - user_id
            - status
            - time_start
            - time_finish
            - guest_bookings
            - model
            - model_id
            - paid
            - attended
            - course_session_id
            - created
            - modified
          properties:
            id:
              type: string
              description: Booking identifier.
              pattern: '^[a-f\d]{24}$'
            user_id:
              type: string
              description: User identifier.
              pattern: '^[a-f\d]{24}$'
            status:
              type: string
              description: The status of the booking.
              enum:
                - BOOKED
                - CANCELED
                - FAILED
                - RESERVED
                - WAITING
            time_start:
              type: string
              format: date-time
              description: The time when the class starts.
              example: '2025-01-16T15:00:00Z'
            time_finish:
              type: string
              format: date-time
              description: The time when the class ends.
              example: '2025-01-16T16:00:00Z'
            guest_bookings:
              type: integer
              description: Users can book in guests. The number of spaces booked is guest_bookings + 1. The booking object counts as 1. Example, Class size = 10; guest_bookings = 3; Remaining spaces = Class size - (guest_bookings + 1) = 10 - (3 +1) = 6.
            model:
              type: string
              description: Booked event type.
              enum:
                - appointments
                - events
                - facilities
            model_id:
              type: string
              description: Associated model Identifier. For appointments & facilities bookings, this will contain the time_slot_id.
              pattern: '^[a-f\d]{24}$'
            paid:
              type: boolean
              description: If the booking is paid.
            attended:
              type: boolean
              description: If the user attended the booking.
            course_session_id:
              type: string
              description: The session ID of a booking of type course.
              pattern: '^[a-f\d]{24}$'
            created:
              type: string
              format: date-time
              description: The time when the booking was created.
              example: '2025-01-15T16:12:00Z'
            modified:
              type: string
              format: date-time
              description: The time when the booking was last updated.
              example: '2025-01-15T17:18:30Z'
    EagreementEvent:
      type: object
      required:
        - Type
        - Metadata
        - Timestamp
        - Payload
      properties:
        Type:
          type: string
          description: The payload event type
          enum:
            - EAGREEMENT_CREATED,
            - EAGREEMENT_UPDATED,
        Metadata:
          type: object
          required:
            - trace_id
            - location_id
            - version
          properties:
            trace_id:
              type: string
              description: Trace Identifier
            location_id:
              type: string
              description: Location Identifier
              pattern: '^[a-f\d]{24}$'
            version:
              type: string
              description: The payload version
        Timestamp:
          type: string
          format: date-time
          description: The timestamp when the event occurred
        Payload:
          type: object
          required:
            - user_id
            - resource_id
            - trigger
            - location_id
            - status
            - created
            - modified
          properties:
            user_id:
              type: string
              description: Member Identifier.
              pattern: '^[a-f\d]{24}$'
            resource_id:
              type: string
              description: |
                The ID of the resource that the agreement is associated with.
                - If the agreement is associated with a membership, the resource_id will be the user_membership_id.
                - If the agreement is associated with a user, the resource_id will be the user_id.
            trigger:
              type: string
              description: |
                The type of document:
                - If integrating electronic agreements the first waiver to use is 'trigger = member_authenticated'. 
                - For membership terms and conditions the waiver to use is 'trigger = membership_purchased'
              enum:
                - member_authenticated
                - membership_purchased
            status:
              type: string
              description: The status of the agreement
              enum:
                - outstanding
                - accepted_outdated
                - accepted_outdated_requires_new
                - accepted
            created:
              type: string
              format: date-time
              description: The time when the agreement was created.
            modified:
              type: string
              format: date-time
              description: The time when the agreement was last updated.
    EventEvent:
      type: object
      required:
        - Type
        - Metadata
        - Timestamp
        - Payload
      properties:
        Type:
          type: string
          description: The payload event type
          enum:
            - EVENT_CREATED
            - EVENT_UPDATED
            - EVENT_DELETED
        Metadata:
          type: object
          required:
            - trace_id
            - location_id
            - version
          properties:
            trace_id:
              type: string
              description: Trace Identifier
            location_id:
              type: string
              description: Location Identifier
              pattern: '^[a-f\d]{24}$'
            version:
              type: string
              description: The payload version
        Timestamp:
          type: string
          format: date-time
          description: The timestamp when the event occurred
        Payload:
          type: object
          required:
            - id
            - namespace
            - type
            - active
            - name
            - description
            - time_start
            - time_finish
            - is_online
            - size
            - private
            - booked
            - waiting
            - program_id
            - level
            - facility
            - trainers
            - status
            - model
            - model_id
            - created
            - modified
          properties:
            id:
              type: string
              description: Event Identifier.
              pattern: '^[a-f\d]{24}$'
            namespace:
              type: string
              description: Namespace whose branch it belongs to.
            type:
              type: string
              description: Type of record, in this case, it is always "event".
            active:
              type: boolean
              description: Indicates if the event is currently active
            name:
              type: string
              description: Name of the gym class.
            description:
              type: string
              description: Detailed information about the gym class.
            time_start:
              type: string
              format: date-time
              description: The time when the class starts.
            time_finish:
              type: string
              format: date-time
              description: The time when the class ends.
            is_online:
              type: boolean
              description: Indicates if the event is online.
            size:
              type: integer
              description: The maximum number of attendees for the event.
            private:
              type: boolean
              description: Indicates if the event is private.
            booked:
              type: integer
              description: The number of attendees that have booked the event.
            waiting:
              type: integer
              description: The number of attendees that are on the waiting list.
            program_id:
              type: string
              description: Program Identifier.
            level:
              type: string
              description: The level of the gym class.
            facility:
              type: string
              description: The facility where the gym class is held.
            trainers:
              type: array
              description: The trainers that are hosting the gym class.
              items:
                type: string
            status:
              type: string
              description: The status of the gym class.
            model:
              type: string
              description: The model type of the event.
              enum:
                - facilities
                - users
                - appointments
            model_id:
              type: string
              description: Associated model Identifier.
              pattern: '^[a-f\d]{24}$'
            created:
              type: string
              description: The time when the event was created.
              format: date-time
            modified:
              type: string
              description: The time when the event was last updated.
              format: date-time
    InvoiceEvent:
      type: object
      required:
        - Type
        - Metadata
        - Timestamp
        - Payload
      properties:
        type:
          type: string
          description: The payload event type
          enum:
            - INVOICE_UPDATED
        Metadata:
          type: object
          required:
            - trace_id
            - location_id
            - version
          properties:
            trace_id:
              type: string
              description: Trace Identifier
            location_id:
              type: string
              description: Location Identifier
              pattern: '^[a-f\d]{24}$'
            version:
              type: string
              description: The payload version
        Timestamp:
          type: string
          format: date-time
          description: The timestamp when the event occurred
        Payload:
          type: object
          required:
            - id
            - user
            - date
            - line_items
            - total
            - currency
            - document_type
            - created
            - modified
          properties:
            id:
              type: string
              description: Invoice Identifier
            user:
              type: object
              required:
                - id
                - first_name
                - last_name
                - email
                - phone
                - tax_id
                - address
              properties:
                id:
                  type: string
                  description: User Identifier
                first_name:
                  type: string
                  description: The first name of the user
                last_name:
                  type: string
                  description: The last name of the user
                email:
                  type: string
                  format: email
                  description: The email address of the user
                phone:
                  type: string
                  description: The phone number of the user
                tax_id:
                  type: string
                  description: The tax identification number of the user
                address:
                  type: object
                  description: User address details
                  required:
                    - street
                    - city
                    - state
                    - country
                    - postal_code
                  properties:
                    street:
                      type: string
                      description: User street
                    city:
                      type: string
                      description: User city
                    state:
                      type: string
                      description: User state
                    country:
                      type: string
                      description: User country
                    postal_code:
                      type: string
                      description: User postal code
            date:
              type: string
              format: date-time
              description: The date and time when the invoice was created
            line_items:
              type: array
              items:
                type: object
                required:
                  - name
                  - unit_price
                  - quantity
                  - applied_taxes
                  - applied_discounts
                  - type
                  - sub_type
                properties:
                  name:
                    type: string
                    description: The name of the item
                  unit_price:
                    type: number
                    format: integer
                    description: The unit price of the item (in cents), after applying discounts but before adding taxes
                  quantity:
                    type: integer
                    description: The quantity of the item
                  applied_taxes:
                    type: array
                    items:
                      type: object
                      required:
                        - tax_amount
                        - rate
                        - tax_base
                      properties:
                        tax_amount:
                          type: number
                          format: integer
                          description: The tax amount (in cents) applied to the line item
                        rate:
                          type: number
                          format: integer
                          description: The tax rate applied to the line item. The tax rate is sent as an integer, representing the percentage value multiplied by 1000. For example:A 5.5% rate is sent as 5500
                        tax_base:
                          type: number
                          format: integer
                          description: The taxable base amount (in cents) for the line item, used as the reference value for calculating taxes.
                  applied_discounts:
                    type: array
                    items:
                      type: object
                      required:
                        - discount_amount
                        - rate_type
                        - rate_value
                      properties:
                        discount_amount:
                          type: number
                          format: integer
                          description: The discount amount (in cents) applied to the line item
                        rate_type:
                          type: string
                          description: The type of discount rate applied to the line item
                        rate_value:
                          type: number
                          format: integer
                          description: The value of the discount rate applied to the line item. The discount rate is sent as an integer, representing the percentage value multiplied by 1000. For example:A 5.5% discount is sent as 5500
                  type:
                    type: string
                    description: Item type
                    enum:
                      - APPOINTMENTS
                      - CLASSES
                      - COURSES
                      - CUSTOM_CHARGES
                      - FACILITIES
                      - FEES
                      - MEMBERSHIPS
                      - PRODUCTS
                      - SERVICES
                      - TRAINERS
                      - WALLET_TOP_UP
                  sub_type:
                    type: string
                    description: Item subtype
                    enum:
                      - BOOK_TIME_SLOT
                      - BOOK_CLASS
                      - BOOK_COURSE
                      - CUSTOM_CHARGE
                      - RECURRENT_FEE
                      - NON_SUFFICIENT_FUNDS
                      - UPFRONT_PAYMENT
                      - SUBSCRIPTION_PAYMENT
                      - SUBSCRIPTION_RENEWAL
                      - SUBSCRIPTION_PRORATE
                      - BUY_PRODUCT
                      - SERVICE_UPFRONT_PAYMENT
                      - SERVICE_PREPAID_PAYMENT
                      - WALLET_TOP_UP
            total:
              type: number
              format: integer
              description: The total amount of the invoice
            currency:
              type: string
              description: The currency used in the invoice
            document_type:
              type: string
              description: The type of document (e.g., invoice, receipt)
            payment_method:
              type: string
              description: >
                The payment type that was used:
                  * `CARD` - Payment made using a credit or debit card.
                  * `POS_TERMINAL` - Payment made via a glofox point-of-sale (POS) terminal, often using a card, mobile payment, or contactless method.
                  * `DIRECT_DEBIT` - An automatic withdrawal from a customer's bank account.
                  * `BANK_TRANSFER` - A manual or automated transfer of funds from one bank account to another, usually initiated by the payer.
                  * `COMPLIMENTARY` - A payment method indicating that the service or product was provided for free.
                  * `CASH` - Payment made with cash upfront.
                  * `FLEXIBLE` - A customizable payment method that allows customers to pay using multiple sources or split payments across different methods. Used frequently for subscriptions.
                  * `WALLET` - Payment made through the glofox account balance.
                  * `PAY_LATER` - A deferred payment option where the customer receives the service or product immediately but pays at a later date.
              enum:
                - CARD
                - POS_TERMINAL
                - DIRECT_DEBIT
                - BANK_TRANSFER
                - COMPLIMENTARY
                - CASH
                - FLEXIBLE
                - WALLET
                - PAY_LATER
            status:
              type: string
              description: >
                The invoice status:
                  * `PAID` - Indicates that the payment was successfully processed and the invoice has been fully settled. No further action is required for this invoice, and it is considered closed. Example: A customer successfully completes a credit card transaction for their membership fee.
                  * `PAST_DUE` - Indicates that the payment attempt failed, and the invoice remains unpaid. This status may occur due to various reasons, such as an invalid card, insufficient funds, or expired payment details. Example: A subscription renewal attempt fails because the customer’s card is declined.
                  * `PENDING` - Indicates that the payment is in progress but has not yet been confirmed. Example: A customer authorizes a payment via their bank, but the confirmation is pending.
                  * `FORGIVEN` - Indicates that the outstanding balance on the invoice has been written off and will no longer be pursued for collection. Example: A business decides to forgive an overdue invoice as part of a customer retention effort.
              enum:
                - PAID
                - PAST_DUE
                - PENDING
                - FORGIVEN
            created:
              type: string
              format: date-time
              description: The date and time when the invoice was created
            modified:
              type: string
              format: date-time
              description: The date and time when the invoice was last modified
    MemberEvent:
      type: object
      required:
        - Type
        - Metadata
        - Timestamp
        - Payload
      properties:
        type:
          type: string
          description: The payload event type
          enum:
            - MEMBER_CREATED
            - MEMBER_UPDATED
        Metadata:
          type: object
          required:
            - trace_id
            - location_id
            - version
          properties:
            trace_id:
              type: string
              description: Trace Identifier
            location_id:
              type: string
              description: Location Identifier
              pattern: '^[a-f\d]{24}$'
            version:
              type: string
              description: The payload version
        Timestamp:
          type: string
          format: date-time
          description: The timestamp when the event occurred
        Payload:
          type: object
          required:
            - id
            - first_name
            - last_name
            - email
            - home_location_id
            - gender
            - birth
            - phone
            - role
            - active
            - emergency_contact
            - namespace
            - parent_id
            - contact_email
            - image_url
            - created
            - modified
          properties:
            id:
              type: string
              description: Member Identifier.
              pattern: '^[a-f\d]{24}$'
            first_name:
              type: string
              description: Member's first name.
            last_name:
              type: string
              description: The member's last name.
            email:
              type: string
              description: The member's email.
            home_location_id:
              type: string
              description: Home Location Identifier. Home location is where their client profile details will be stored. This will also be their main studio location, however, if they have a roaming membership attached to their profile, they will be able to book at multiple locations from the Custom App
              pattern: '^[a-f\d]{24}$'
            gender:
              type: string
              description: The member's gender.
              enum:
                - MALE
                - FEMALE
                - OTHER
                - PREFER NOT TO SAY
            birth:
              type: string
              format: date
              description: The member's birth date.
            phone:
              type: string
              description: The member's phone.
            role:
              type: string
              description: The member's role.
              enum:
                - MEMBER
                - CHILD
            active:
              type: boolean
              description: If the member is active.
            emergency_contact:
              type: string
              description: The member's emergency contact information.
            namespace:
              type: string
              description: Namespace whose branch it belongs to
            parent_id:
              type: string
              description: The member's parent account id.
              pattern: '^[a-f\d]{24}$'
            contact_email:
              type: string
              description: The member's email for contact purpose.
            image_url:
              type: string
              description:
                The URL of the user's profile picture. This field is always populated with an auto-generated URL, even if no image has been uploaded.
                If the image exists, it will be accessible through the URL. Otherwise, attempting to access it may result in a 403 (Forbidden) response, which is the existing behavior for missing files in S3.
                403 responses should be treated as an indication that the user has not uploaded a profile picture.
            created:
              type: string
              format: date-time
              description: The time when the member was created.
            modified:
              type: string
              format: date-time
              description: The time when the member was updated.
    MembershipEvent:
      type: object
      required:
        - Type
        - Metadata
        - Timestamp
        - Payload
      properties:
        Type:
          type: string
          description: The payload event type
          enum:
            - MEMBERSHIP_CREATED
            - MEMBERSHIP_UPDATED
            - MEMBERSHIP_DELETED
        Metadata:
          type: object
          required:
            - trace_id
            - location_id
            - version
          properties:
            trace_id:
              type: string
              description: Trace Identifier
            location_id:
              type: string
              description: Location Identifier
              pattern: '^[a-f\d]{24}$'
            version:
              type: string
              description: The payload version
        Timestamp:
          type: string
          format: date-time
          description: The timestamp when the event occurred
        Payload:
          type: object
          required:
            - id
            - user_id
            - membership_definition
            - upfront_fee
            - contract
            - cycle
            - status
            - group
            - created
            - modified
          properties:
            id:
              type: string
              description: Membership Identifier.
              pattern: '^[a-f\d]{24}$'
            user_id:
              type: string
              description: User Identifier.
              pattern: '^[a-f\d]{24}$'
            membership_definition:
              type: object
              description: Defines the membership definition object.
              required:
                - id
                - name
                - plan_code
                - plan_name
                - roaming_enabled
                - trial
                - starts_on
                - type
                - roaming_branches
              properties:
                id:
                  type: string
                  description: Membership's definition Identifier.
                  pattern: '^[a-f\d]{24}$'
                name:
                  type: string
                  description: The membership's name.
                plan_code:
                  type: string
                  description: The membership's plan code.
                plan_name:
                  type: string
                  description: The membership's plan name.
                roaming_enabled:
                  type: boolean
                  description: If roaming is enable.
                trial:
                  type: boolean
                  description: If this is a trial membership.
                starts_on:
                  type: string
                  description: Moment when the membership starts.
                  enum:
                    - PURCHASE_DATE
                    - FIRST_BOOKING_DATE
                type:
                  type: string
                  description: The membership's type.
                  enum:
                    - time
                    - time_classes
                    - payg
                roaming_branches:
                  type: array
                  description: Membership's roaming branches.
                  items:
                    type: string
                    pattern: '^[a-f\d]{24}$'
            price:
              type: number
              description: The membership's price.
            upfront_fee:
              type: number
              description: The membership's upfront fee.
            contract:
              type: object
              required:
                - start_date
                - end_date
                - conclusion_date
              properties:
                start_date:
                  type: string
                  format: date-time
                  description: The contract's starting date.
                end_date:
                  type: string
                  format: date-time
                  description: The contract's end date.
                conclusion_date:
                  type: string
                  format: date-time
                  description: The contract's conclusion date.
            cycle:
              type: object
              description: Defines the membership cycle object.
              required:
                - start_date
                - end_date
                - next_payment_date
              properties:
                start_date:
                  type: string
                  format: date-time
                  description: The cycle's starting date.
                end_date:
                  type: string
                  format: date-time
                  description: The cycle's end date.
                next_payment_date:
                  type: string
                  format: date-time
                  description: The next payment date.
            status:
              type: string
              description: The membership's status.
              enum:
                - FUTURE
                - ACTIVE
                - LOCKED
                - PAUSED
                - CANCELLED
                - EXPIRED
            group:
              type: object
              description: Defines the membership group object.
              required:
                - id
                - is_primary
              properties:
                id:
                  type: string
                  description: Membership's group Identifier.
                  pattern: '^[a-f\d]{24}$'
                is_primary:
                  type: boolean
                  description: If it is a primary group.
            created:
              type: string
              format: date-time
              description: The time when the membership was created.
            modified:
              type: string
              format: date-time
              description: The time when the membership was updated.
    ServiceEvent:
      type: object
      required:
        - Type
        - Metadata
        - Timestamp
        - Payload
      properties:
        type:
          type: string
          description: The payload event type
          enum:
            - SERVICE_CREATED
            - SERVICE_UPDATED
            - SERVICE_DELETED
        metadata:
          type: object
          required:
            - trace_id
            - location_id
            - version
          properties:
            trace_id:
              type: string
              description: Trace Identifier
            location_id:
              type: string
              description: Location Identifier
              pattern: '^[a-f\d]{24}$'
            version:
              type: string
              description: The payload version
        timestamp:
          type: string
          format: date-time
          description: The timestamp when the event occurred
        payload:
          type: object
          required:
            - id
            - definition
            - planned_start_date
            - planned_end_date
            - next_payment_date
            - purchased
            - member_ids
            - price
            - concluded
            - commenced
            - cycles
            - membership_id
            - status
            - pause
            - available_credits
            - external_resource_id
            - membership_external_resource_id
            - created
            - modified
          properties:
            id:
              type: string
              description: Unique identifier for the service.
              pattern: '^[a-f\d]{24}$'
            definition:
              type: object
              description: Details about the service definition.
              required:
                - id
                - version
                - name
                - type
                - description
                - plan
              properties:
                id:
                  type: string
                  description: Service identifier.
                  pattern: '^[a-f\d]{24}$'
                version:
                  type: integer
                  description: Version number of the service definition.
                name:
                  type: string
                  description: Name of the service.
                type:
                  type: string
                  description: Type of service.
                description:
                  type: string
                  description: Detailed information about the service.
                plan:
                  type: array
                  description: Array of plan objects associated with the service.
                  items:
                    type: object
                    required:
                      - id
                      - name
                      - price
                      - credit
                      - duration
                      - is_recurring
                      - is_credit_based
                    properties:
                      id:
                        type: string
                        description: Plan's identifier.
                        pattern: '^[a-f\d]{24}$'
                      name:
                        type: string
                        description: Name of the plan.
                      price:
                        type: integer
                        description: Price of the plan.
                      credit:
                        type: object
                        description: Details about the credits for the plan.
                        required:
                          - count
                          - is_carry_over
                        properties:
                          count:
                            type: string
                            description: Number of credits.
                          is_carry_over:
                            type: string
                            description: Indicates if credits can be carried over.
                      duration:
                        type: object
                        description: Duration details of the plan.
                        required:
                          - count
                          - has_end_date
                          - total_no_of_cycles
                          - duration_unit
                        properties:
                          count:
                            type: integer
                            description: Number of units for the duration.
                          has_end_date:
                            type: boolean
                            description: Indicates if the plan has an end date.
                          total_no_of_cycles:
                            type: integer
                            description: Total number of cycles for the plan.
                          duration_unit:
                            type: string
                            description: Unit of duration.
                      is_recurring:
                        type: boolean
                        description: Indicates if the plan is recurring.
                      is_credit_based:
                        type: boolean
                        description: Indicates if the plan is credit-based.
            planned_start_date:
              type: string
              format: date-time
              description: Planned start date of the service.
            planned_end_date:
              type: string
              format: date-time
              description: Planned end date of the service.
            next_payment_date:
              type: string
              format: date-time
              description: Date of the next payment.
            purchased:
              type: string
              format: date-time
              description: Date when the service was purchased.
            member_ids:
              type: array
              description: List of member identifiers associated with the service.
              items:
                type: string
                pattern: '^[a-f\d]{24}$'
            price:
              type: integer
              description: Price of the service.
            concluded:
              type: string
              format: date-time
              description: Date when the service.
            commenced:
              type: string
              format: date-time
              description: Date when the service.
            cycles:
              type: array
              description: Array of cycle objects representing service cycles.
              items:
                type: object
                required:
                  - start_date
                  - end_date
                properties:
                  start_date:
                    type: string
                    format: date-time
                    description: Start date of the cycle.
                  end_date:
                    type: string
                    format: date-time
                    description: End date of the cycle.
            membership_id:
              type: string
              description: Identifier for the associated membership.
              pattern: '^[a-f\d]{24}$'
            status:
              type: string
              description: Current status of the service.
            pause:
              type: object
              description: Details about the pause status of the service. If this attribute is `nil`, the service is not paused.
              required:
                - start_date
                - duration_unit
                - duration_amount
                - resume_date
              properties:
                start_date:
                  type: string
                  format: date-time
                  description: Start date of the pause.
                duration_unit:
                  type: string
                  description: Unit of duration for the pause.
                  enum:
                    - DAY
                    - WEEK
                    - MONTH
                duration_amount:
                  type: integer
                  description: Amount of time for the pause.
                resume_date:
                  type: string
                  format: date-time
                  description: Date when the service will resume.
            available_credits:
              type: integer
              description: Number of available credits for the service.
            external_resource_id:
              type: string
              description: External resource identifier related to the service.
            membership_external_resource_id:
              type: string
              description: External resource identifier related to the membership.
            created:
              type: string
              format: date-time
              description: Timestamp when the service was created.
            modified:
              type: string
              format: date-time
              description: Timestamp when the service was last modified.
    AvailableAppointmentsError:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
    AvailableAppointments:
      type: object
      properties:
        data:
          description: This array contains all the available appointments and it will be empty when none are found
          type: array
          items:
            $ref: '#/components/schemas/AvailableAppointment'
    AvailableAppointment:
      type: object
      properties:
        id:
          type: string
        image_url:
          type: string
          format: url
        name:
          type: string
        description:
          type: string
        type:
          description: Usually this will be timeslot and it is needed to book it.
          type: string
        model_id:
          description: This is a parameter needed to make bookings
          type: string
        size:
          type: integer
          format: int32
        time_start:
          description: This is the actual timestamp the appointment is starting.
          type: integer
          format: int32
        time_start_iso:
          description: This is the appointment start time in the branch time (ISO format - YYYY-MM-DDThh:mm:ssTZD).
          type: string
          format: date-time
        duration:
          description: This parameter is represented in minutes.
          type: integer
          format: int32
        trainer:
          $ref: '#/components/schemas/Trainer'
    Trainer:
      type: object
      properties:
        id:
          type: string
        image_url:
          type: string
          format: url
          description:
            The URL of the user's profile picture. This field is always populated with an auto-generated URL, even if no image has been uploaded.
            If the image exists, it will be accessible through the URL. Otherwise, attempting to access it may result in a 403 (Forbidden) response, which is the existing behavior for missing files in S3.
            403 responses should be treated as an indication that the user has not uploaded a profile picture.
        first_name:
          type: string
        last_name:
          type: string
        description:
          type: string
    Facility:
      type: object
      properties:
        _id:
          type: string
          description: The unique identifier for the facility
          pattern: '^[a-f\d]{24}$'
        location_id:
          type: string
          description: The unique identifier for the location
          pattern: '^[a-f\d]{24}$'
        description:
          type: string
          description: A brief summary of the facility
        name:
          type: string
          description: The name of the facility
        namespace:
          type: string
          description: The namespace the location is associated with
        bookable:
          type: boolean
          description: Indicates whether the facility is bookable
        is_online:
          type: boolean
          description: Indicates whether the facility is online
        categories:
          type: array
          items:
            type: string
            description: Unique identifier associated with the category
            pattern: '^[a-f\d]{24}$'
        list_visible:
          type: boolean
          description: Indicates whether the facility is set to public or private
        created_at:
          type: string
          format: date-time
          description: The date and time when the facility was created
    GenericErrorResponse:
      type: object
      properties:
        success:
          type: boolean
          example: false
        message:
          type: string
        message_code:
          type: string
        message_data:
          type: array
          items:
            type: string
        errors:
          type: array
          items:
            type: string
    Course_V3:
      type: object
      properties:
        _id:
          type: string
          description: Unique identifier for the course
          pattern: '^[a-f\d]{24}$'
        location_id:
          type: string
          description: Unique identifier for the location
          pattern: '^[a-f\d]{24}$'
        namespace:
          type: string
          description: Namespace associated with the location
        name:
          type: string
          description: Name of the course
        description:
          type: string
          description: Description of the course
        active:
          type: boolean
          description: Indicates if the course is active
        private:
          type: boolean
          description: Indicates if the course is private
        facility:
          description: Facility ID associated with the course
          oneOf:
            - type: string
              pattern: '^[a-f\d]{24}$'
            - type: array
              items:
                type: string
                pattern: '^[a-f\d]{24}$'
        trainers:
          type: array
          description: List of trainer IDs assigned to the course
          items:
            type: string
            pattern: '^[a-f\d]{24}$'
        size:
          type: integer
          description: Maximum number of participants in the course
        type:
          type: string
          description: Type of the object (e.g., course)
          default: course
        image_url:
          type: string
          format: uri
          description: URL of the course image
        schedule:
          type: array
          description: Schedule details of the course
          items:
            type: object
            properties:
              id:
                type: integer
                description: Unique ID of the schedule entry
              start_date:
                type: string
                format: date
                description: Start date of the course (DD-MM-YYYY)
              end_date:
                type: string
                format: date
                description: End date of the course (DD-MM-YYYY)
              label:
                type: string
                description: Label for the schedule
              booked:
                type: integer
                description: Number of bookings for the course
              days:
                type: array
                description: Weekly recurring days and times
                items:
                  type: object
                  properties:
                    days:
                      type: string
                      description: Day abbreviation (e.g., MO, TU, FR)
                    start_time:
                      type: string
                      description: Start time (HH:MM)
                    end_time:
                      type: string
                      description: End time (HH:MM)
                    id:
                      type: integer
                      description: Unique ID for the day slot
        pricing:
          type: array
          description: Pricing tiers available for the course
          items:
            type: object
            properties:
              price:
                type: number
                format: float
                description: Price for the tier
              type:
                type: string
                description: Type of pricing (e.g., payg, member)
              name:
                type: string
                description: Name of the pricing tier
