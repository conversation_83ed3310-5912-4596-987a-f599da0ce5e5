# /etc/nginx/nginx.conf

user nginx;

worker_processes 4;

pcre_jit on;

error_log /var/log/nginx/error.log warn;
include /etc/nginx/modules/*.conf;

events {
        worker_connections 100;
        multi_accept on;
}

http {
        include /etc/nginx/mime.types;
        default_type application/octet-stream;

        server_tokens off;
        keepalive_timeout 350;

        sendfile on;
        tcp_nodelay on;
        tcp_nopush on;

        gzip on;
        gzip_vary on;
        gzip_static on;

        # Specifies the main log format.
        log_format main '{ "time": "$time_iso8601", '
                        '"service_name": "VAR_SERVICE_NAME", '
                        '"name": "api_request", '
                        '"http_x_forwarded_for": "$http_x_forwarded_for", '
                        '"remote_addr": "$remote_addr", '
                        '"remote_user": "$remote_user", '
                        '"body_bytes_sent": "$body_bytes_sent", '
                        '"request_time": "$request_time", '
                        '"status": "$status", '
                        '"request": "$request", '
                        '"request_method": "$request_method", '
                        '"http_referrer": "$http_referer", '
                        '"version": "VAR_VERSION", '
                        '"hostname": "VAR_HOSTNAME", '
                        '"region": "VAR_REGION", '
                        '"upstream_response_time": "$upstream_response_time", '
                        '"x-glofox-dashboard-version": "$http_x_glofox_dashboard_version", '
                        '"x-glofox-branch-id": "$http_x_glofox_branch_id", '
                        '"x-glofox-source": "$http_x_glofox_source", '
                        '"x-glofox-app-version": "$http_x_glofox_app_version", '
                        '"x-glofox-app-device-model": "$http_x_glofox_app_device_model", '
                        '"x-glofox-app-version": "$http_x_glofox_app_version", '
                        '"x-apigw-appid": "$http_x_integrator_api_id ", '
                        '"x-apigw-integrator-key-id": "$http_x_integrator_api_key_id  ", '
                        '"trace.trace_id": "$sent_http_x_traceid", '
                        '"trace.span_id": "$sent_http_x_spanid", '
                        '"trace.parent_id": "$sent_http_x_parentspanid", '
                        '"duration_ms": "$sent_http_x_requestdurationms", '
                        '"x-glofox-success": "$sent_http_x_glofox_success", '
                        '"x-glofox-message-code": "$sent_http_x_glofox_message_code", '
                        '"http_user_agent": "$http_user_agent" }';

        access_log /var/log/nginx/access.log main;

        client_max_body_size 50M;
        client_body_buffer_size 1m;
        client_body_timeout 15;
        client_header_timeout 15;
        send_timeout 300;

        open_file_cache          max=5000  inactive=20s;
        open_file_cache_valid    30s;
        open_file_cache_min_uses 2;
        open_file_cache_errors   on;

        fastcgi_buffers 256 16k;
        fastcgi_buffer_size 128k;
        fastcgi_connect_timeout 60s;
        fastcgi_send_timeout 300s;
        fastcgi_read_timeout 300s;
        fastcgi_busy_buffers_size 256k;
        fastcgi_temp_file_write_size 256k;
        reset_timedout_connection on;

        include /etc/nginx/conf.d/*.conf;
}
