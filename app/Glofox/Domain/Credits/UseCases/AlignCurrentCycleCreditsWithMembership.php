<?php

declare(strict_types=1);

namespace Glofox\Domain\Credits\UseCases;

use Carbon\Carbon;
use Glofox\Domain\Credits\Models\CreditPack;
use Glofox\Domain\Credits\Repositories\CreditsRepository;
use Glofox\Domain\Memberships\Status;
use Glofox\Domain\Users\Models\UserMembership;
use Glofox\Domain\Users\Models\UserMembershipSubscription;
use Glofox\Domain\Users\Repositories\UsersRepository;
use Glofox\Infrastructure\Honeycomb\HoneycombTracker;
use Psr\Log\LoggerInterface;
use UnsuccessfulOperation;

class AlignCurrentCycleCreditsWithMembership
{
    private const LOG_PREFIX = '[CREDITS_HEALING][CURRENT_CYCLE_CREDITS]';

    private CreditsRepository $creditsRepository;
    private UsersRepository $usersRepository;
    private LoggerInterface $logger;
    private HoneycombTracker $honeycombTracker;

    public function __construct(
        CreditsRepository $creditsRepository,
        UsersRepository $usersRepository,
        LoggerInterface $logger,
        HoneycombTracker $honeycombTracker
    ) {
        $this->creditsRepository = $creditsRepository;
        $this->usersRepository = $usersRepository;
        $this->logger = $logger;
        $this->honeycombTracker = $honeycombTracker;
    }

    public function alignCredits(string $userId, string $branchId, bool $isDryRun = false): void
    {
        $user = $this->usersRepository->getById($userId);
        $userMembership = $user->membership();

        if (!$this->hasValidMembership($userId, $userMembership)) {
            return;
        }

        $subscription = $userMembership->subscription();

        $this->logger->info(sprintf('%s Starting for user %s', self::LOG_PREFIX, $userId));

        $currentCycleCredits = $this->creditsRepository->getCurrentCycleCreditsForAlignment(
            $userId,
            $branchId,
            $userMembership
        );

        $this->logger->info(
            sprintf(
                '%s Found %s credits',
                self::LOG_PREFIX,
                is_countable($currentCycleCredits) ? count($currentCycleCredits) : 0
            )
        );

        /** @var CreditPack $credit */
        foreach ($currentCycleCredits as $credit) {
            $this->logger->info(
                sprintf('%s Started processing for credit id %s', self::LOG_PREFIX, $credit->id()),
                [
                    'user_id' => $userId,
                    'user_membership_id' => $userMembership->userMembershipId()
                ]
            );

            if (!$this->hasMatchingDuration($credit, $subscription)) {
                $this->logger->info(
                    sprintf('%s Intervals are different for credit id %s', self::LOG_PREFIX, $credit->id()),
                    [
                        'user_id' => $userId,
                        'subscription_interval' => $subscription->interval(),
                        'subscription_interval_count' => $subscription->intervalCount(),
                        'credit_interval' => $credit->hasDuration() ? $credit->intervalPeriod() : null,
                        'credit_interval_count' => $credit->hasDuration() ? $credit->intervalCount() : null,
                    ]
                );

                continue;
            }

            $expectedStartDate = $userMembership->startDate();
            $expectedEndDate = $userMembership->expiryDate();
            $creditBefore = clone $credit;

            $result = $this->updateUserCredit($credit, $expectedStartDate, $expectedEndDate, $isDryRun);

            if (!$result) {
                continue;
            }

            $this->logger->info(
                sprintf(
                    '%s Finished successfully for credit id: %s, new dates are %s and %s',
                    self::LOG_PREFIX,
                    $credit->id(),
                    $credit->startDate()->toDateTimeString(),
                    $credit->expiryDate()->toDateTimeString()
                ),
                [
                    'user_id' => $userId,
                    'user_membership_id' => $userMembership->userMembershipId()
                ]
            );

            if ($isDryRun) {
                continue;
            }

            $this->sendReport($branchId, $credit, $creditBefore, $userMembership);
        }
    }

    private function updateUserCredit(
        CreditPack $creditPack,
        Carbon $expectedStartDate,
        Carbon $expectedEndDate,
        bool $isDryRun = false
    ): bool {
        $isStartDateValid = $creditPack->startDate()->toDateTimeString() === $expectedStartDate->toDateTimeString();
        $isEndDateValid = $creditPack->expiryDate()->toDateTimeString() === $expectedEndDate->toDateTimeString();

        $this->logger->info(sprintf('%s Credit pack dates', self::LOG_PREFIX), [
            'isStartDateValid' => $isStartDateValid,
            'isEndDateValid' => $isEndDateValid,
            'expectedStartDate' => $expectedStartDate->toDateTimeString(),
            'expectedEndDate' => $expectedEndDate->toDateTimeString(),
            'creditStartDate' => $creditPack->startDate()->toDateTimeString(),
            'creditEndDate' => $creditPack->expiryDate()->toDateTimeString(),
        ]);

        if ($isStartDateValid && $isEndDateValid) {
            $this->logger->info(sprintf('%s Credit dates are good! Skipping...', self::LOG_PREFIX));

            return false;
        }

        if (!$isStartDateValid) {
            $creditPack->put('start_date', $expectedStartDate->toDateTimeString());
        }

        if (!$isEndDateValid) {
            $creditPack->put('end_date', $expectedEndDate->toDateTimeString());
        }

        if ($isDryRun) {
            return true;
        }

        try {
            $this->creditsRepository->saveOrFail($creditPack);
        } catch (UnsuccessfulOperation $exception) {
            $this->logger->info(sprintf('%s Error for %s', self::LOG_PREFIX, $creditPack->id()), [
                'exception' => $exception->getMessage(),
                'credit_pack' => $creditPack->toArray(),
                'user_id' => $creditPack->userId(),
            ]);

            return false;
        }

        return true;
    }

    private function hasMatchingDuration(CreditPack $credit, UserMembershipSubscription $subscription): bool
    {
        if (!$credit->hasDuration()) {
            return false;
        }

        return $subscription->intervalCount() === $credit->intervalCount() &&
            $subscription->interval() === $credit->intervalPeriod();
    }

    private function hasValidMembership(string $userId, UserMembership $userMembership): bool
    {
        if ($userMembership->status() !== Status::ACTIVE || !$userMembership->isRestricted()) {
            $this->logger->info(sprintf('%s user %s membership is not supported', self::LOG_PREFIX, $userId));

            return false;
        }

        if (!$userMembership->hasSubscription()) {
            $this->logger->info(sprintf('%s Membership for user %s has no subscription', self::LOG_PREFIX, $userId));

            return false;
        }

        return true;
    }

    private function sendReport(
        string $branchId,
        CreditPack $credit,
        CreditPack $creditBefore,
        UserMembership $userMembership
    ): void {
        $this->honeycombTracker->track([
            'service_name' => 'credits-healing',
            'sampling_type' => 'keep',
            'user_id' => $credit->userId(),
            'branch_id' => $branchId,
            'user_credit_id' => $credit->id(),
            'user_membership_id' => $userMembership->userMembershipId(),
            'user_membership_status' => $userMembership->status(),
            'user_membership_start_date' => $userMembership->startDate()->toDateTimeString(),
            'user_membership_expiry_date' => $userMembership->expiryDate()->toDateTimeString(),
            'subscription_plan_id' => $userMembership->subscription()->subscriptionPlanId(),
            'credit_start_date_before' => $creditBefore->startDate()->toDateTimeString(),
            'credit_end_date_before' => $creditBefore->expiryDate()->toDateTimeString(),
            'credit_start_date_after' => $credit->startDate()->toDateTimeString(),
            'credit_end_date_after' => $credit->expiryDate()->toDateTimeString(),
            'credit_expiry_interval' => $credit->intervalPeriod(),
            'credit_expiry_interval_count' => $credit->intervalCount(),
            'credits_left' => $credit->creditsLeft(),
            'is_current_cycle' => true
        ]);
    }
}
