<?php

use Glofox\Domain\Authentication\Auth;
use Glofox\Domain\TimeSlotPatterns\Requests\UpsertTimeslotPatternRequest;
use Glofox\Domain\TimeSlotPatterns\UseCase\CreateOrUpdateOne;
use Glofox\Domain\TimeSlotPatterns\UseCase\CreateOrUpdateOneParams;
use Glofox\Domain\TimeSlotPatterns\Validation\UserAuthorization\CreateOrUpdateValidator;
use Illuminate\Http\JsonResponse;
use Psr\Log\LoggerInterface;

/**
 * FacilitiesController class.
 *
 * @uses          AppController
 */
class TimeSlotPatternsController extends AppController
{
    /**
     * @var string
     */
    public $name = 'TimeSlotPattern';

    /**
     * @var array
     */
    public $uses = [
        'TimeSlotPattern',
        'TimeSlot',
        'User',
    ];

    /**
     * @var array
     */
    public $components = [
        'Utility',
        'JWT',
    ];

    /*!
     * Dispatcher for Model
     * @param  [type] $identifier [description]
     * @return [type]             [description]
     */

    /**
     * @param $identifier
     *
     * @return string
     */
    public function dispatcher($identifier)
    {
        return parent::dispatch($identifier, $this->TimeSlotPattern);
    }

    /**
     * @return string
     */
    public function clear_schedule()
    {
        $logger = app()->make(LoggerInterface::class);
        $logger->info('Log to track the use of this clear_schedule method from TimeSlotPattersController');

        $time_slot_pattern_data = json_decode(file_get_contents('php://input'), true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        $time_slot_pattern = $this->TimeSlotPattern->findById($time_slot_pattern_data['_id']);
        $this->loadModel('TimeSlot');
        // Delete all the time slots that have not been booked
        $this->TimeSlot->deleteAll(['time_slot_pattern_id' => $time_slot_pattern_data['_id'], 'booked' => false]);

        // Set Active false all the time slots that have been booked already
        $conditions = ['time_slot_pattern_id' => $time_slot_pattern_data['_id'], 'booked' => true];
        $fields = ['active' => false];
        $this->TimeSlot->updateAll($fields, $conditions);

        $time_slot_pattern['TimeSlotPattern']['schedule'] = [];
        $this->TimeSlotPattern->read(null, $time_slot_pattern_data['_id']);
        $save_time_slot_pattern = $this->TimeSlotPattern->save($time_slot_pattern);

        if ($save_time_slot_pattern) {
            return json_encode(
                [
                    'success' => true,
                    'time_slot_pattern' => $time_slot_pattern,
                    'message' => 'You have successfully cleared the schedule.'
                ], JSON_PARTIAL_OUTPUT_ON_ERROR
            );
        } else {
            return json_encode(['success' => false, 'message' => 'Ocurred an error clearing the schedule.']);
        }
    }

    /**
     * @param $branch_id
     * @param $model
     * @param $model_id
     *
     * @return string
     */
    public function findEntityByBranchIdModelAndModelId($branch_id, $model, $model_id)
    {
        $findEntity = $this->TimeSlotPattern->findEntityByBranchIdModelAndModelId($model, $model_id);
        if ($findEntity) {
            return json_encode(['success' => true, 'entity' => $findEntity], JSON_PARTIAL_OUTPUT_ON_ERROR);
        } else {
            return json_encode(['success' => false, 'message' => 'Invalid model or id sent.']);
        }
    }

    /**
     * @param $branch_id
     * @param $category_id
     *
     * @return string
     */
    public function findEntitiesByBranchIdCategoryId($branch_id, $category_id)
    {
        $category = $this->Category->findById($category_id);
        if (!$category) {
            return json_encode(['success' => false, 'message' => 'There is no category with the given id.']);
        }

        $this->loadModel('Facility');
        $this->loadModel('User');

        if ($this->Facility->useTable == $category['Category']['model']) {
            return json_encode(
                [
                    'success' => true,
                    'entities' => $this->Facility->findListByBranchIdAndCategoryId($branch_id, $category_id, false)
                ], JSON_PARTIAL_OUTPUT_ON_ERROR
            );
        } elseif ($this->User->useTable == $category['Category']['model']) {
            return json_encode(
                [
                    'success' => true,
                    'entities' => $this->User->findListByBranchIdAndCategoryId($branch_id, $category_id, false)
                ], JSON_PARTIAL_OUTPUT_ON_ERROR
            );
        } else {
            return json_encode(['success' => false, 'message' => 'Invalid model sent.']);
        }
    }

    /**
     * @return string
     */
    public function save()
    {
        $logger = app()->make(LoggerInterface::class);
        $logger->info('Log to track the use of this save method from TimeSlotPattersController');

        $time_slot_pattern = json_decode(file_get_contents('php://input'), true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        $has_a_schedule = false;
        $is_update = (empty($time_slot_pattern['_id'])) ? false : true;
        if ($is_update) {
            $time_slot_pattern_db = $this->TimeSlotPattern->findById($time_slot_pattern['_id']);
            if (!empty($time_slot_pattern_db['schedule'])) {
                $has_a_schedule = true;
            }
        }

        // Only create the schedule if they don't have one already
        if (!$has_a_schedule) {
            // Load Branch
            $this->loadModel('Branch');
            $current_date = $this->Branch->getCurrentDate('date');
            $branch = $this->Branch->getBranchFeatures($time_slot_pattern['branch_id']);
            $time_slot_length = $time_slot_pattern['time_slot_length'] ?? $this->Branch->findDefaultTimeSlotLength(
                $branch,
                $time_slot_pattern['model']
            );

            $weeks_to_display = $this->Branch->findWeeksToDisplay($branch, $time_slot_pattern['model']);
            $date_display_end = $this->Branch->addNumberOfWeeks(
                $this->Branch->getCurrentDate('date'),
                $weeks_to_display,
                $this->Branch->get_date_format('date')
            );
            $new_schedule = [];

            foreach ($time_slot_pattern['schedule'] as &$schedule) {
                $start_time = $schedule['start_time'];
                $end_time = $schedule['end_time'];
                $week_days = [];

                if (is_array($schedule['week_day'])) {
                    foreach ($schedule['week_day'] as $week_day) {
                        if ($week_day['ticked']) {
                            $week_days[] = $week_day['value'];
                        }
                    }
                } else {
                    // return json_encode( $schedule );
                    $week_days[] = $schedule['week_day'];
                }

                $time_slots_times = $this->TimeSlotPattern->calculateSlotsByLengthAndTimeRange(
                    $time_slot_length,
                    $start_time,
                    $end_time
                );

                foreach ($week_days as $week_day) {
                    foreach ($time_slots_times as $time_slot_time) {
                        $new_schedule[] = [
                            'week_day' => $week_day,
                            'start_time' => $time_slot_time['start_time'],
                            'end_time' => $time_slot_time['end_time'],
                        ];
                    }
                }
            }
            $time_slot_pattern['schedule'] = $new_schedule;
        }
        $time_slot_pattern['namespace'] = $this->Branch->findNamespaceById($time_slot_pattern['branch_id']);
        $save_time_slot_pattern = $this->TimeSlotPattern->save($time_slot_pattern);

        if ($save_time_slot_pattern) {
            $time_slot_patter_db = $this->TimeSlotPattern->findById($save_time_slot_pattern['TimeSlotPattern']['_id']);

            if (!empty($time_slot_patter_db['TimeSlotPattern']['schedule']) && !$has_a_schedule) {
                $this->loadModel('TimeSlot');
                $this->loadModel('Facility');
                $this->loadModel('User');

                if ($time_slot_patter_db['TimeSlotPattern']['model'] == $this->Facility->useTable) {
                    $facility = $this->Facility->findById($time_slot_patter_db['TimeSlotPattern']['model_id']);
                    $name = $facility['Facility']['name'];
                } elseif ($time_slot_patter_db['TimeSlotPattern']['model'] == $this->User->useTable) {
                    $trainer = $this->User->findById($time_slot_patter_db['TimeSlotPattern']['model_id']);
                    $name = $trainer['User']['first_name'] . ' ' . $trainer['User']['last_name'];
                }

                foreach ($time_slot_patter_db['TimeSlotPattern']['schedule'] as $schedule) {
                    $this->TimeSlot->generate_slots(
                        $time_slot_patter_db['TimeSlotPattern']['namespace'],
                        $time_slot_patter_db['TimeSlotPattern']['branch_id'],
                        $schedule,
                        $time_slot_patter_db['TimeSlotPattern']['_id'],
                        $time_slot_patter_db['TimeSlotPattern']['date_start'],
                        $time_slot_patter_db['TimeSlotPattern']['date_finish'],
                        $date_display_end,
                        $time_slot_patter_db['TimeSlotPattern']['model'],
                        $time_slot_patter_db['TimeSlotPattern']['model_id'],
                        $name
                    );
                }
            }

            return json_encode(
                [
                    'success' => true,
                    'time_slot_pattern' => $save_time_slot_pattern['TimeSlotPattern'],
                    'message' => 'You have successfully created the schedule.'
                ], JSON_PARTIAL_OUTPUT_ON_ERROR
            );
        } else {
            return json_encode(
                [
                    'success' => false,
                    'message' => 'Ocurred an error saving the time slot pattern. Please try again later.'
                ]
            );
        }
    }

    /**
     * @return string
     */
    public function get_new_code()
    {
        $new_code = $this->TimeSlotPattern->generateUniqueCode();
        $count = $this->TimeSlotPattern->find('count', [
            'conditions' => [
                'schedule.code',
            ],
        ]);
        if (0 == $count) {
            return $new_code;
        } else {
            return 'Nope';
        }
    }

    /**
     * @param null $branch_id
     * @param null $user_id
     * @param bool $fetch_credits
     *
     * @return string
     */
    public function findByBranchId($branch_id = null, $user_id = null, $fetch_credits = false)
    {
        $this->response->type('json');
        $this->autoRender = false;

        $params = [
            'conditions' => ['branch_id' => $branch_id],
            'limit' => 1000,
            'page' => 1,
        ];

        $results = $this->TimeSlotPattern->find('all', $params);

        if ($user_id) {
            foreach ($results as $key => &$result) {
                $model_id = $results[$key]['TimeSlotPattern']['model_id'];
                $results[$key] = $this->findTimeSlotPatternPrice($result, $user_id);

                // Fetch Credits
                $fetch_credits = filter_var($fetch_credits, FILTER_VALIDATE_BOOLEAN);
                if ($fetch_credits) {
                    $this->loadModel('UserCredit');
                    $model = $this->TimeSlotPattern->useTable;
                    $categories = $results[$key]['TimeSlotPattern']['categories'];
                    $return_model = false;
                    $credits = $this->UserCredit->findAllCredits(
                        $branch_id,
                        $user_id,
                        $model,
                        $model_id,
                        $categories,
                        $return_model
                    );
                    $results[$key]['TimeSlotPattern']['credits'] = $credits ?: [];
                }
            }
        }

        return json_encode($results, JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    // THIS IS A BIG TEST FOR NOW AND EVENTUALLY IS GOING TO REPLACE THE SAVE METHOD

    /**
     * @param UpsertTimeslotPatternRequest $req
     * @return JsonResponse
     */
    public function upsert(UpsertTimeslotPatternRequest $req): JsonResponse
    {
        app()->make(CreateOrUpdateValidator::class)
            ->validate(Auth::user(), $req->branchId(), $req->namespace(), $req->id());

        $params = new CreateOrUpdateOneParams(
            $req->id(),
            $req->branchId(),
            $req->namespace(),
            $req->model(),
            $req->modelId(),
            $req->timeSlotLength(),
            $req->isActive(),
            $req->pricing(),
            $req->allowedMemberTypes(),
            $req->schedule(),
            $req->startDate(),
            $req->finishDate()
        );

        $useCase = app()->make(CreateOrUpdateOne::class);
        $result = $useCase->execute($params);

        return response()->json($result);
    }

    /**
     * @param      $time_slot_pattern_id
     * @param null $user_id
     *
     * @return array
     */
    private function findTimeSlotPatternPrice($time_slot_pattern_id, $user_id = null)
    {
        $timeSlotPattern = is_array($time_slot_pattern_id) ? $time_slot_pattern_id : $this->TimeSlotPattern->findById(
            $time_slot_pattern_id
        );
        $user = is_array($user_id) ? $user_id : $this->User->findById($user_id);
        $membership_type = $user['User']['membership']['type'] ?? $this->User->get_membership_type(
            'payg'
        );
        $membership_id = $user['User']['membership']['_id'] ?? null;
        $membership_group_id = $user['User']['membership']['membership_group_id'] ?? null;

        $price_and_permission = $this->User->get_price_and_permission(
            $timeSlotPattern['TimeSlotPattern']['allowed_member_types'],
            $membership_type,
            $membership_id,
            $membership_group_id,
            $timeSlotPattern['TimeSlotPattern']['default_price']
        );

        $timeSlotPattern['TimeSlotPattern']['default_price'] = $price_and_permission['price'];
        $timeSlotPattern['TimeSlotPattern']['is_allowed'] = $price_and_permission['is_allowed'];

        if (!$price_and_permission['is_allowed']) {
            $this->loadModel('UserCredit');
            $credits = $this->UserCredit->findAllAvailableByBranchIdAndUserId(
                $timeSlotPattern['TimeSlotPattern']['branch_id'],
                $user_id
            );
            foreach ($credits as $credit) {
                if (isset($credit['UserCredit']['membership_id'])) {
                    $membership_type = 'member';
                    $membership_id = $credit['UserCredit']['membership_id'];
                    $membership_group_id = null;
                    $price_and_permission = $this->User->get_price_and_permission(
                        $timeSlotPattern['TimeSlotPattern']['allowed_member_types'],
                        $membership_type,
                        $membership_id,
                        $membership_group_id,
                        $timeSlotPattern['TimeSlotPattern']['default_price']
                    );

                    if (!empty($credit['UserCredit']['model_ids']) && !in_array(
                            $timeSlotPattern['TimeSlotPattern']['model_id'],
                            $credit['UserCredit']['model_ids']
                        )) {
                        continue;
                    }

                    if ($price_and_permission['is_allowed']) {
                        $timeSlotPattern['TimeSlotPattern']['default_price'] = $price_and_permission['price'];
                        $timeSlotPattern['TimeSlotPattern']['is_allowed'] = $price_and_permission['is_allowed'];
                        break;
                    }
                }
            }
        }

        return $timeSlotPattern;
    }

    // THIS IS A BIG TEST FOR NOW AND EVENTUALLY IS GOING TO REPLACE THE SAVE METHOD
}
