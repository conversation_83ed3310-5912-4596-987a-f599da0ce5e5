db.getCollection('cards').aggregate([{
    $group: {
        _id: {
            user_id: '$user_id',
            payment_method_id: '$payment_method_id'
        },
        count: {
            $sum: 1
        },
        cardIds: {
            $addToSet: "$_id"
        },
        uniquePaymentMethodUserIds: {
            $addToSet: '$payment_method_user_id'
        }
    }
 }, {
    $project: {
        count: 1,
        cardIds: 1,
        moreThanOnePayment: {
            $gt: [{
                $size: '$uniquePaymentMethodUserIds'
            }, 1]
        }
    }
 }, {
    $match: {
        count: {
            $gt: 1
        },
        moreThanOnePayment: true
    }
 }
 ], {allowDiskUse:true}).forEach(e => {
    let cardsToBeDeleted = [];
    let paymentMethodUsersToBeDeleted = [];
    let cardToLeave = null;
    e.cardIds.forEach((cardId) => {
        let card = db.getCollection('cards').findOne({_id: cardId});
        if (card && card.payment_method_user_id.length === 24) {
            if (!cardToLeave) {
                cardToLeave = card;
            } else {
                paymentMethodUsersToBeDeleted.push(card.payment_method_user_id);
            }
        }

        if (cardToLeave !== card) {
            cardsToBeDeleted.push(card);
        }
    })

    cardsToBeDeleted.forEach((card) => {
        card.user_id += '_deleted_' + Date.now();
        card.branch_id += '_deleted_' + Date.now();
        card.payment_method_id += '_deleted_' + Date.now();
        card.payment_method_user_id += '_deleted_' + Date.now();
        print(`Removing card ${card._id} as duplicate`);
        db.getCollection('cards').save(card);
    })

    paymentMethodUsersToBeDeleted.forEach((paymentMethodUserId) => {
        let paymentMethodUser = db.getCollection('payment_method_users').findOne({_id: ObjectId(paymentMethodUserId)});
        if (paymentMethodUser) {
            paymentMethodUser.user_id += '_deleted_' + Date.now();
            paymentMethodUser.branch_id += '_deleted_' + Date.now();
            paymentMethodUser.payment_method_id += '_deleted_' + Date.now();
            print(`Removing payment method user ${paymentMethodUserId} as duplicate`);
            db.getCollection('payment_method_users').save(paymentMethodUser);
        }
    })
 });
