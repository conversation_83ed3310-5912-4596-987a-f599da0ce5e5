#!/bin/sh
set -e

chmod 777 -R /var/www/app/tmp
rm -Rf /var/www/app/tmp/cache/persistent/myapp*

export HOSTNAME="$(hostname)"

echo "VERSION=${VERSION}" > /etc/environment
echo "HOSTNAME=${HOSTNAME}" >> /etc/environment

envsubst < /usr/local/etc/php/conf.d/newrelic.ini.dist > /usr/local/etc/php/conf.d/newrelic.ini
envsubst < /usr/local/etc/php/conf.d/opcache.ini.dist > /usr/local/etc/php/conf.d/opcache.ini
# envsubst < /etc/nginx/conf.d/default.conf.dist > /etc/nginx/conf.d/default.conf

sed -i \
    -e "s/VAR_VERSION/${VERSION}/" \
    -e "s/VAR_REGION/$REGION/" \
    -e "s/VAR_HOSTNAME/$HOSTNAME/" \
    /etc/nginx/conf.d/default.conf

sed -i \
    -e "s/VAR_SERVICE_NAME/${SERVICE_NAME}/" \
    -e "s/VAR_VERSION/${VERSION}/" \
    -e "s/VAR_REGION/$REGION/" \
    -e "s/VAR_HOSTNAME/$HOSTNAME/" \
    /etc/nginx/nginx.conf

if [ -f /usr/local/etc/php/conf.d/xdebug.ini.dist -a ${PHP_XDEBUG_ENABLE} -gt 0 ]; then
    if [ -z ${PHP_XDEBUG_MODE} ]; then
      export PHP_XDEBUG_MODE="develop,debug"
    elif [ ${PHP_XDEBUG_MODE} == "profile" ]; then
      export PHP_XDEBUG_ENABLE="yes"
    fi

    if [ -z ${PHP_XDEBUG_DISCOVER_CLIENT_HOST} ]; then
      export PHP_XDEBUG_DISCOVER_CLIENT_HOST="0"
    fi

    envsubst < /usr/local/etc/php/conf.d/xdebug.ini.dist > /usr/local/etc/php/conf.d/xdebug.ini
fi

# Display CPU information on startup
cat /proc/cpuinfo

# cd /opt/zpa/bin && sh configure.sh us_8fdfdc5396cf913a5ed6975274b91060a6a24d04

exec "$@"
