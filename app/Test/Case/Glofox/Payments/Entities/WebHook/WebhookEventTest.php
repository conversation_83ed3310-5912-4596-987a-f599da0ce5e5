<?php

namespace CakeTestCases\Glofox\Payments\Entities\WebHook;

use Glofox\Payments\Entities\WebHook\Models\WebhookEvent;

\App::import('Test/Case', 'GlofoxTestCase');

class WebhookEventTest extends \GlofoxTestCase
{
    public function test_it_fails_to_create_when_event_id_is_empty(): void
    {
        self::setExpectedException(\Exception::class, 'Cannot parse Webhook Event with empty event id');
        new WebhookEvent('');
    }

    public function test_it_creates_successfully_when_event_id_is_provided(): void
    {
        $event = new WebhookEvent('aaa');
        self::assertEquals('aaa', $event->getEventId());
    }
}
