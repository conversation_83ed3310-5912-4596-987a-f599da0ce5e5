<?php

App::import('Test/Case', 'GlofoxTestCase');

use Glofox\Response\Models\Waivers\WaiversModel;

/**
 * Test cases
 * 1 Model contains all fields with data
 * 2 Missing fields and returns correct structure
 * 3 Additional fields and return correct structure
 * 4 Empty array fields and return correct structure.
 *
 * Class WaiversModelTest
 */
class WaiversModelTest extends GlofoxTestCase
{
    public $fixtures = [];

    /**
     * @dataProvider modelProvider
     */
    public function testModel($dataSent, $result): void
    {
        $model = new WaiversModel($dataSent);
        $this->assertEquals($model, $result);
    }

    public function modelProvider(): array
    {
        return [
            '1 Model contains all fields with data' => [
                [
                    '_id' => '123456789',
                    'namespace' => 'test',
                    'branch_id' => '321654987',
                    'type' => 'onetime',
                    'content' => 'lot of things here',
                    'image_url' => 'somethingurl',
                ],
                new WaiversModel(
                    [
                        '_id' => '123456789',
                        'namespace' => 'test',
                        'branch_id' => '321654987',
                        'type' => 'onetime',
                        'content' => 'lot of things here',
                        'image_url' => 'somethingurl',
                    ]
                ),
            ],
            '2 Missing fields and returns correct structure' => [
                [
                    // _id missing
                    'namespace' => 'test',
                    'branch_id' => '321654987',
                    'type' => 'onetime',
                    'content' => 'lot of things here',
                    'image_url' => 'somethingurl',
                ],
                new WaiversModel(
                    [
                        '_id' => '',
                        'namespace' => 'test',
                        'branch_id' => '321654987',
                        'type' => 'onetime',
                        'content' => 'lot of things here',
                        'image_url' => 'somethingurl',
                    ]
                ),
            ],
            '3 Additional fields and return correct structure' => [
                [
                    'id2' => 123_456_789, // additional field
                    '_id' => '123456789',
                    'namespace' => 'test',
                    'branch_id' => '321654987',
                    'type' => 'onetime',
                    'content' => 'lot of things here',
                    'image_url' => 'somethingurl',
                ],
                new WaiversModel(
                    [
                        '_id' => '123456789',
                        'namespace' => 'test',
                        'branch_id' => '321654987',
                        'type' => 'onetime',
                        'content' => 'lot of things here',
                        'image_url' => 'somethingurl',
                    ]
                ),
            ],
            '4 Empty array fields and return correct structure' => [
                [],
                new WaiversModel(
                    [
                        '_id' => '',
                        'namespace' => '',
                        'branch_id' => '',
                        'type' => '',
                        'content' => '',
                        'image_url' => '',
                    ]
                ),
            ],
        ];
    }
}
