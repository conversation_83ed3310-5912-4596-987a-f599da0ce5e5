type: object
properties:
  applied_discounts:
    items:
      $ref: './calculate_taxes_applied_discount.yml'
    type: array
  discounted_price:
    type: number
    description: The price after applying all discounts.
    example: 1.6
  discounts_total:
    type: number
    description: The sum of the discounts.
    example: 0.4
  product_price:
    type: number
    description: The original product price that was supplied.
    example: 2
