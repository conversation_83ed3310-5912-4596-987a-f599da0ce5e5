# Make Documentation

In this document contains an explanation on how to manage the Makefile and how to use the different make commands that are available in this project.

Run `make` or `make help` to get a detailed list of all the available commands.

- [Make Documentation](#make-documentation)
  - [Docker related commands](#docker-related-commands)
  - [Composer related commands](#composer-related-commands)
  - [API documentation commands](#api-documentation-commands)
  - [Other commands](#other-commands)

## Docker related commands

Build and install service containers on the same terminal using:

```bash
make build
```

To rebuild a specific container:

```bash
make rebuild-container container=${containerServiceName}
```

Where `containerServiceName` is the name of the service. For example, we have the container named `glofox_db`, but its
service is called `db`, so `container=db`. This command will initially shut down all the containers.

Both build commands (`build` and `rebuild-container`) will trigger `make up`.

Run compose to bring up the containers and all dependencies:

```bash
make up
```

To stop the containers:

```bash
make stop
```

To shut down the containers:

```bash
make down
```

## Composer related commands

To install / update composer libraries:

```bash
make composer-install
```

## API documentation commands

To install swagger cli:

```bash
make install-swagger-cli
```

To validate docs:

```bash
make private-docs-lint
```

To generate docs:

```bash
make private-docs
```

The commands `private-docs-lint` and `private-docs` will automatically trigger `install-swagger-cli` at the beginning.

## Other commands

- [Code Version Compatibility](./README.md#code-version-compatibility)
- [Fixing Rector CI Warnings](./README.md#fixing-rector-ci-warnings)
- [Running Unit Tests](./README.md#running-unit-tests)
- [MongoDB Upgrade](./README.md#mongodb-upgrade)
