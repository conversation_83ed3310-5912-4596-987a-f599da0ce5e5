<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\AppointmentSlots\Events\Listeners;

use App;
use CakeTestCases\Glofox\Domain\Users\Traits\AuthenticateUsersTrait;
use Glofox\Domain\AppointmentSlots\Events\Listeners\TrackVirtualAppointmentSlotGenerated;
use Glofox\Domain\AppointmentSlots\Events\VirtualAppointmentSlotsGenerated;
use Glofox\Domain\AsyncEvents\Events\TrackingEventMeta;
use Glofox\Domain\AsyncEvents\Events\TrackingEventPayload;
use Glofox\Domain\EventTrackers\Services\TrackEventPublisher;
use Mockery;
use Psr\Log\LoggerInterface;

App::import('Test/Case', 'GlofoxTestCase');

class TrackVirtualAppointmentSlotGeneratedTest extends \GlofoxTestCase
{
    use AuthenticateUsersTrait;

    private const DEFAULT_BRANCH_ID = '49a7011a05c677b9a916612a';
    private const DEFAULT_USER_ID = '59a7011a05c677bda916612a';

    public $fixtures = ['app.branch', 'app.user'];

    private TrackEventPublisher $publisher;
    private LoggerInterface $logger;

    public function setUp(): void
    {
        parent::setUp();

        $this->publisher = Mockery::mock(TrackEventPublisher::class);
        $this->logger = Mockery::mock(LoggerInterface::class);
    }

    public function tearDown(): void
    {
        parent::tearDown();

        Mockery::close();
    }

    public function testDoesNotHandleIfAuthUserIsNull(): void
    {
        $this->mockAuthenticatedUser(null);

        $this->publisher->shouldNotHaveBeenCalled();
        $this->logger->shouldReceive('info')->once();

        $handler = new TrackVirtualAppointmentSlotGenerated($this->publisher, $this->logger);
        $handler->handle($this->givenDefaultTrackingEvent());
    }

    public function testDoesNotHandleIfAuthUserIsGuest(): void
    {
        $this->authenticateAsGuest();

        $this->publisher->shouldNotHaveBeenCalled();
        $this->logger->shouldReceive('info')->once();

        $handler = new TrackVirtualAppointmentSlotGenerated($this->publisher, $this->logger);
        $handler->handle($this->givenDefaultTrackingEvent());
    }

    public function testSuccessfulGenerationIsReported(): void
    {
        $this->authenticateUser(self::DEFAULT_USER_ID);

        $this->logger->shouldNotHaveBeenCalled();

        $domainEvent = new VirtualAppointmentSlotsGenerated(
            self::DEFAULT_BRANCH_ID,
            1,
            2,
            3,
            4,
            6.0
        );

        $handler = $this->givenHandler($domainEvent);

        $handler->handle($domainEvent);
    }

    private function givenHandler(VirtualAppointmentSlotsGenerated $event): TrackVirtualAppointmentSlotGenerated
    {
        $data = [
            'branchId' => $event->getBranchId(),
            'appointmentsCount' => $event->getAppointmentsCount(),
            'trainersCount' => $event->getTrainersCount(),
            'availabilitiesCount' => $event->getAvailabilitiesCount(),
            'virtualSlotsGeneratedCount' => $event->getVirtualSlotsGeneratedCount(),
            'generationTimeInSeconds' => round($event->getGenerationTimeInSeconds(), 5),
        ];

        $trackingData = [
            'eventName' => 'Virtual Appointment Slots Generated',
            'branchId' => self::DEFAULT_BRANCH_ID,
            'userId' => self::DEFAULT_USER_ID,
            'platform' => 'CORE-API',
            'data' => $data,
        ];

        $this->publisher
            ->shouldReceive('sendEventToTrack')
            ->withArgs(
                function (TrackingEventMeta $meta, TrackingEventPayload $payload) use ($trackingData) {
                    $this->assertEmpty($meta->correlation());
                    $this->assertEmpty($meta->messageId());
                    $this->assertNotEmpty($meta->creationDate());

                    $serializedPayload = $payload->jsonSerialize();

                    $this->assertSame($trackingData, $serializedPayload);

                    return true;
                }
            )
            ->once();

        return new TrackVirtualAppointmentSlotGenerated($this->publisher, $this->logger);
    }

    private function givenDefaultTrackingEvent(): VirtualAppointmentSlotsGenerated
    {
        return new VirtualAppointmentSlotsGenerated(
            self::DEFAULT_BRANCH_ID,
            0,
            0,
            0,
            0,
            1.0
        );
    }
}
