<?php
namespace Glofox\Datasource;

use Exception;

/**
 * [IMPORTANT]
 *
 * Special class to handle the UTC to timezone transformation hack. Use this transformer only to
 * handle the mistake of datetime with timezone is stored as a UTC in DB
 *
 * Ex:
 * Instead of '2014-06-06 18:00:00.GMT-04:00' wrongly '2014-06-06 18:00:00.000' is stored in DB.
 * For such special cases this class helps to get the correct datetime instance.
 *
 * Class WrongUTCMongoTransformer
 *
 * @package Glofox\Datasource
 */
class WrongUTCMongoTransformer
{
    
    /**
     * Method to convert the datetime in branch timezone to UTC timezone
     *
     * @param $data
     * @param $branchTimezone
     * @return int
     */
    public static function fromDBToResponse($date, $branchTimezone)
    {
        if (is_string($branchTimezone)) {
            $branchTimezone = new \DateTimeZone($branchTimezone);
        }
        if ($date instanceof \MongoDate) {
            $date = new \DateTime($date->toDateTime()->format('Y-m-d H:i:s'), $branchTimezone);
        } elseif (is_string($date)) {
            $date = new \DateTime((new \DateTime($date))->format('Y-m-d H:i:s'), $branchTimezone);
        } else {
            return $date;
        }
        return $date->setTimezone(new \DateTimeZone('UTC'))->getTimestamp();
    }

    /**
     * Method to convert the timestamp to datetime with branch timezone
     *
     * @param int $timestamp
     * @param $branchTimezone
     * @return int
     * @throws Exception
     */
    public static function fromRequestToDB(int $timestamp, $branchTimezone): int
    {
        if (is_string($branchTimezone)) {
            $branchTimezone = new \DateTimeZone($branchTimezone);
        }
        $dateTime = new \DateTime();
        $dateTime->setTimestamp($timestamp);
        $dateTime->setTimezone($branchTimezone);
        $dateTime = new \DateTime($dateTime->format('Y-m-d H:i:s'));
        return $dateTime->getTimestamp();
    }
}
