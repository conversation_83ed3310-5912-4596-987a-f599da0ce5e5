<?php

App::import('Test/Case', 'GlofoxTestCase');

/*
 * Custom test suite to execute all tests
 */

class AllTestsTest extends PHPUnit_Framework_TestSuite
{
    private static bool $doTestsBalance = false;
    private static array $isolatedTests = [];
    private static array $testsToBalanceByImpactScore = [
        1 => [],
        2 => [],
        3 => [],
        4 => [],
        5 => [],
    ];
    private static int $scoreToWorkloadFactor = 2;
    /**
     * @var array|string[] Test files that need to be run in a single node
     */
    private static array $testsToRunInSingleNodes = ['app/Test/Case/Controller/BookingsControllerTest.php'];
    /**
     * @var array|int[] Test files with big impact on the performance, impact score 1-5.
     *
     * 1- > 10 secs && < 30 secs
     * 2- < 1 min
     * 3- < 1.30 min
     * 4- < 2 min
     * 5- < 2.30 min
     */
    private static array $impactfulTestsWithImpactScore = [
        'app/Test/Case/Controller/UsersControllerTest.php' => 2,
        'app/Test/Case/Controller/EventsControllerTest.php' => 3,
        'app/Test/Case/Controller/ProductsControllerTest.php' => 2,
        'app/Test/Case/Glofox/Payments/Providers/Gateway/WebHooks/Handlers/InvoicePaymentFailedTest.php' => 1,
        'app/Test/Case/Glofox/Payments/Providers/Gateway/WebHooks/Handlers/InvoicePaymentSucceededTest.php' => 1,
        'app/Test/Case/Glofox/Domain/Bookings/Events/Listeners/UserRequestedBookingCancellingTest.php' => 1,
        'app/Test/Case/Glofox/Domain/AppointmentSlots/Http/AppointmentSlotsControllerTest.php' => 1,
        'app/Test/Case/Glofox/Payments/Providers/Gateway/WebHooks/Handlers/InvoiceAuthRequiredTest.php' => 1,
        'app/Test/Case/Controller/AnalyticsControllerTest.php' => 1,
    ];

    public static function suite()
    {
        GlofoxTestCase::setTestEnvironment();

        // We need to declare $suite at the beginning because of some class dependencies needed in following steps
        $suite = new CakeTestSuite('All tests');
        $testsToRun = self::getTestsToRun();
        $suite->addTestFiles($testsToRun);

        return $suite;
    }

    private static function getTestsToRun(): array
    {
        $totalNodes = (int)getenv('CIRCLE_NODE_TOTAL');
        $allTests = ($totalNodes === 1);
        $nodeIndex = $allTests ? 0 : (int)getenv('CIRCLE_NODE_INDEX');

        $testFiles = self::getTestFiles($allTests);

        if (!$allTests) {
            // split into even file numbers before sorting
            $totalNodes -= count(self::$isolatedTests);
        }

        $nodes = self::generateNodes($testFiles, $totalNodes);

        if (!$allTests) {
            $nodes = self::balanceTests($nodes);
            $nodes = self::addNodesForIsolatedTests($nodes);
        }

        $testsToRun = $nodes[$nodeIndex];
        return self::sortTests($testsToRun);
    }

    private static function getTestFiles(bool $allTests): array
    {
        $path = APP . 'Test' . DS . 'Case' . DS;
        $folder = new Folder($path);
        $testFiles = [];
        $baseDir = str_replace('app/Test/Case', '', __DIR__);

        foreach ($folder->tree(null, true, 'files') as $file) {
            if (substr($file, -4) !== '.php') {
                continue;
            }

            $fileRelativePath = str_replace($baseDir, '', $file);

            if (!$allTests && self::isTestToRunInSingleNode($fileRelativePath)) {
                self::$isolatedTests[] = $file;
            } elseif (!$allTests && self::isImpactfulTestToBalance($fileRelativePath)) {
                $impactScore = self::$impactfulTestsWithImpactScore[$fileRelativePath];

                self::$doTestsBalance = true;
                self::$testsToBalanceByImpactScore[$impactScore][] = $file;
            } else {
                $testFiles[] = $file;
            }
        }

        return $testFiles;
    }

    private static function isTestToRunInSingleNode(string $fileRelativePath): bool
    {
        return in_array($fileRelativePath, self::$testsToRunInSingleNodes, true);
    }

    private static function isImpactfulTestToBalance(string $fileRelativePath): bool
    {
        return array_key_exists($fileRelativePath, self::$impactfulTestsWithImpactScore);
    }

    private static function generateNodes(array $testFiles, int $totalNodes): array
    {
        $nodeSize = count($testFiles) / $totalNodes;
        return array_chunk($testFiles, ceil($nodeSize));
    }

    private static function balanceTests(array $nodes): array
    {
        if (!self::$doTestsBalance) {
            return $nodes;
        }

        $nodesWorkload = self::initNodesWorkload($nodes);

        /*
         * Loop tests to balance sorted by impact score DESC order
         */
        foreach (array_reverse(self::$testsToBalanceByImpactScore, true) as $score => $tests) {
            if (empty($tests)) {
                continue;
            }

            /*
             * The workload of the test will be the impact score configured * 2. Initially, all tests that aren't
             * inside these tests to balance have a testWorkload = 1, so we need to duplicate these tests score
             * in order to differentiate $testToBalance[1] from the non-balanced tests. Also, we consider that every
             * higher score has the double of impact on performance than the previous one.
             */
            $testWorkload = $score * self::$scoreToWorkloadFactor;

            foreach ($tests as $test) {
                $node = self::getNodeLessWorkload($nodesWorkload);
                $nodesWorkload[$node] += $testWorkload;
                $nodes[$node][] = $test;
            }
        }

        return $nodes;
    }

    /**
     * @param array<int, array> $nodes
     * @return array
     */
    private static function initNodesWorkload(array $nodes): array
    {
        $nodesWorkload = [];

        foreach ($nodes as $node => $tests) {
            $workload = count($tests);
            $nodesWorkload[$node] = $workload;
        }

        return $nodesWorkload;
    }

    private static function getNodeLessWorkload(array $nodesWorkload): int
    {
        $selectedNode = 0;
        $selectedNodeWorkload = null;

        foreach ($nodesWorkload as $node => $workload) {
            if ($selectedNodeWorkload === null || $workload < $selectedNodeWorkload) {
                $selectedNode = $node;
                $selectedNodeWorkload = $workload;
            }
        }

        return $selectedNode;
    }

    private static function addNodesForIsolatedTests(array $nodes): array
    {
        if (empty(self::$isolatedTests)) {
            return $nodes;
        }

        foreach (self::$isolatedTests as $test) {
            $nodes[] = [$test];
        }

        return $nodes;
    }

    private static function sortTests(array $tests): array
    {
        /*
         * Controller tests must always be run first, however the addTestDirectory implementation is OS dependent
         * and thus unpredictable. Instead, find all tests and sort them so controllers come first.
         */
        usort($tests, static function ($a, $b) {
            $aIsController = self::isControllerTest($a);
            $bIsController = self::isControllerTest($b);

            if ($aIsController === $bIsController) {
                return 0;
            }

            if ($aIsController) {
                return -1;
            }

            return 1;
        });

        return $tests;
    }

    private static function isControllerTest(string $test): bool
    {
        return false !== strpos($test, 'Controller');
    }
}
