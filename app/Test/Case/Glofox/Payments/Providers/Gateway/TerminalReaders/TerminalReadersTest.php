<?php

namespace CakeTestCases\Glofox\Payments\Providers\Gateway\TerminalReaders;

use Glofox\Domain\PaymentMethods\Models\PaymentMethod;
use Glofox\Domain\PaymentProviders\Models\PaymentProvider;
use Glofox\Payments\Entities\TerminalReader\Internal\CreateTerminalReader;
use Glofox\Payments\Providers\Gateway\GRPC\Connection;
use Glofox\Payments\Providers\Gateway\TerminalReaders\TerminalReaders;
use GRPC\Payments\AccountService\AccountServiceClient;
use GRPC\Payments\AccountService\CreateTerminalReaderReq;
use GRPC\Payments\AccountService\DeleteTerminalReaderReq;
use GRPC\Payments\AccountService\EmptyAccountObject;
use GRPC\Payments\AccountService\TerminalReader;
use Grpc\UnaryCall;

\App::import('Test/Case', 'GlofoxTestCase');

class TerminalReadersTest extends \GlofoxTestCase
{
    public function testItShouldCreateATerminalReader(): void
    {
        $terminalReader = new TerminalReader();
        $terminalReader->setID('123');
        $terminalReader->setDeviceType('terminal-reader-device-type');
        $terminalReader->setLabel('terminal-reader-label');
        $terminalReader->setSerialNumber('terminal-reader-serial-number');

        $connection = \Mockery::mock(Connection::class);
        $connection->accountService = \Mockery::mock(AccountServiceClient::class);
        $connection->accountService->shouldReceive('CreateTerminalReader')
            ->withArgs(function (CreateTerminalReaderReq $params) {
                $this->assertSame(123, $params->getMerchantID());
                $this->assertSame('registration-code', $params->getRegistrationCode());
                $this->assertSame('', $params->getLabel());

                return true;
            })
            ->andReturn(
                \Mockery::mock(UnaryCall::class)
                    ->shouldReceive('wait')
                    ->andReturn([$terminalReader, (object) ['code' => 0]])
                    ->getMock()
            );

        app()->instance(Connection::class, $connection);

        $terminalReaders = new TerminalReaders(new PaymentMethod(['provider' => ['account_id' => '123']]), new PaymentProvider());

        $createTerminalReaderParams = (new CreateTerminalReader())
            ->setRegistrationCode('registration-code');

        $result = $terminalReaders->create($createTerminalReaderParams);
        self::assertEquals('123', $result->getId());
        self::assertEquals('terminal-reader-device-type', $result->getDeviceType());
        self::assertEquals('terminal-reader-label', $result->getLabel());
        self::assertEquals('terminal-reader-serial-number', $result->getSerialNumber());

        app()->forgetInstance(Connection::class);
    }

    public function testItShouldDeleteATerminalReader(): void
    {
        $connection = \Mockery::mock(Connection::class);
        $connection->accountService = \Mockery::mock(AccountServiceClient::class);
        $connection->accountService->shouldReceive('DeleteTerminalReader')
            ->withArgs(function (DeleteTerminalReaderReq $params) {
                $this->assertSame(123, $params->getID());

                return true;
            })
            ->andReturn(
                \Mockery::mock(UnaryCall::class)
                    ->shouldReceive('wait')
                    ->andReturn([new EmptyAccountObject(), (object) ['code' => 0]])
                    ->getMock()
            );

        app()->instance(Connection::class, $connection);

        $terminalReaders = new TerminalReaders(new PaymentMethod(['provider' => ['account_id' => '123']]), new PaymentProvider());

        $terminalReaders->delete('123');

        app()->forgetInstance(Connection::class);
    }
}