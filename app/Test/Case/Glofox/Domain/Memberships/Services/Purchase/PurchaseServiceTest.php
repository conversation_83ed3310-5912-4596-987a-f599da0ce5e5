<?php

// The "PurchaseService" makes use of the `event()` global, the only way to mock
// it is to override the function at namespace level.

namespace Glofox\Domain\Memberships\Services\Purchase {

    function event()
    {
        return new class() {
            public function emit($a, $b): void
            {
                return;
            }

            public function getEmitter(): \Glofox\Events\Emitter
            {
                return new \CakeTestCases\Glofox\StubEmitter();
            }
        };
    }
}

namespace CakeTestCases\Glofox\Domain\Memberships\Services\Purchase {

    use CakeTestCases\Glofox\Domain\Users\Traits\FetchUsersTrait;
    use Carbon\Carbon;
    use Glofox\Domain\Authentication\Auth;
    use Glofox\Domain\Authentication\Token\TokenGeneratorDto;
    use Glofox\Domain\Authentication\TokenGenerator;
    use Glofox\Domain\Branches\Models\Branch;
    use Glofox\Domain\Branches\Repositories\BranchesRepository;
    use Glofox\Domain\Cart\Services\CartServiceInterface;
    use Glofox\Domain\ElectronicAgreements\Services\TermsConditionsAcceptedValidator;
    use Glofox\Domain\Memberships\Exceptions\MembershipPlanMinimumPriceValidationException;
    use Glofox\Domain\Memberships\Exceptions\MembershipResponseException;
    use Glofox\Domain\Memberships\Models\Membership;
    use Glofox\Domain\Memberships\Models\Plan;
    use Glofox\Domain\Memberships\Services\Purchase\PurchaseService;
    use Glofox\Domain\Memberships\Services\Purchase\SetMembershipService;
    use Glofox\Domain\Memberships\Type as MembershipsType;
    use Glofox\Domain\Memberships\Validation\PlanMinimumPriceValidator;
    use Glofox\Domain\Memberships\Validation\Purchase\CurrentMembershipAllowsNewMembershipPurchaseValidator;
    use Glofox\Domain\Memberships\Validation\Purchase\PurchaseMembershipWhileLockedValidator;
    use Glofox\Domain\Memberships\Validation\Purchase\PurchaseServiceAggregatedValidator;
    use Glofox\Domain\PaymentMethods\Models\PaymentMethod;
    use Glofox\Domain\PaymentMethods\Models\Provider;
    use Glofox\Domain\PaymentMethods\Repositories\PaymentMethodsRepository;
    use Glofox\Domain\PaymentMethods\Type;
    use Glofox\Domain\Users\Models\User;
    use Glofox\Domain\Users\Models\UserMembership;
    use Glofox\Domain\Users\Repositories\UsersRepository;
    use Glofox\Domain\Wallets\Services\Settings\OverdraftValidatorService;
    use Glofox\Infrastructure\Exception\HttpClientResponseException;
    use Glofox\Payments\Entities\Customer\Resolvers\IdResolver;
    use GuzzleHttp\Psr7\Response;
    use Illuminate\Support\Collection;
    use Mockery\MockInterface;
    use MongoId;
    use UnsuccessfulOperation;

    \App::import('Test/Case', 'GlofoxTestCase');

    class PurchaseServiceTest extends \GlofoxTestCase
    {
        use FetchUsersTrait;

        /** @var MockInterface */
        private $setMembershipServiceMock;

        /** @var MockInterface */
        private $usersRepositoryMock;

        /** @var MockInterface */
        private $validateMembershipCanBeBoughtFromCurrentMock;

        /** @var MockInterface */
        private $purchaseMembershipWhileLockedValidatorMock;

        /** @var MockInterface */
        private $aggregatedValidator;

        /** @var MockInterface */
        private $paymentMethodsRepository;

        /** @var UsersRepository */
        private $usersRepository;

        /** @var MockInterface */
        private $idResolverMock;

        /** @var MockInterface */
        private $cartService;

        /** @var MockInterface */
        private $termsValidator;

        /** @var MockInterface */
        private $planMinimumPricingValidator;

        public function setUp()
        {
            parent::setUp();

            $this->usersRepositoryMock = \Mockery::mock(UsersRepository::class);
            $this->setMembershipServiceMock = \Mockery::mock(SetMembershipService::class);
            $this->paymentMethodsRepository = \Mockery::mock(PaymentMethodsRepository::class);
            $this->usersRepository = \Mockery::mock(UsersRepository::class);
            $this->idResolverMock = \Mockery::mock(IdResolver::class);
            $this->cartService = \Mockery::mock(CartServiceInterface::class);
            $this->termsValidator = \Mockery::mock(TermsConditionsAcceptedValidator::class);
            $this->planMinimumPricingValidator = \Mockery::mock(PlanMinimumPriceValidator::class);

            $this->validateMembershipCanBeBoughtFromCurrentMock = \Mockery::mock(
                CurrentMembershipAllowsNewMembershipPurchaseValidator::class
            );
            $this->purchaseMembershipWhileLockedValidatorMock = \Mockery::mock(
                PurchaseMembershipWhileLockedValidator::class
            );

            $aggregatedValidator = new PurchaseServiceAggregatedValidator(
                $this->validateMembershipCanBeBoughtFromCurrentMock,
                $this->purchaseMembershipWhileLockedValidatorMock
            );

            $this->aggregatedValidator = \Mockery::mock($aggregatedValidator);

            $token = app()->make(TokenGenerator::class)
                ->generate(
                    new TokenGeneratorDto([
                        '_id' => '59a7011a05c677bda916612a',
                        'namespace' => 'glofox',
                        'branch_id' => '49a7011a05c677b9a916612a',
                        'first_name' => 'admin',
                        'last_name' => 'admin',
                        'type' => \UserType::ADMIN,
                    ])
                );

            $_SERVER['HTTP_AUTHORIZATION'] = $token;
        }

        public function tearDown()
        {
            \Mockery::close();
            parent::tearDown();

            unset($_SERVER['HTTP_AUTHORIZATION']);
        }

        /**
         * @dataProvider optionalParamProvider
         */
        public function test_optional_params($dataSet)
        {
            $branchMock = \Mockery::mock(Branch::class);
            $branchMock->shouldReceive('timezone')
                ->andReturn(new \DateTimeZone('GMT'));

            $userMembershipMock = \Mockery::mock(UserMembership::class);
            $userMembershipMock->shouldReceive('isPayg')
                ->andReturnTrue();

            $userMock = \Mockery::mock(User::class);
            $userMock->shouldReceive('toLegacy')
                ->andReturn([]);
            $userMock->shouldReceive('branch')
                ->andReturn($branchMock);
            $userMock->shouldReceive('originBranchId')
                ->andReturn((string)new \MongoId());
            $userMock->shouldReceive('membership')
                ->andReturn($userMembershipMock);
            $userMock->shouldReceive('currentBranchId')
                ->andReturn((string)new \MongoId());
            $userMock->shouldReceive('id')
                ->andReturn((string)new \MongoId());

            $membershipMock = \Mockery::mock(Membership::class);
            $membershipMock->shouldReceive('isActive')
                ->andReturnTrue();
            $membershipMock->shouldReceive('isRoaming')
                ->andReturnFalse();
            $membershipMock->shouldReceive('toLegacy')
                ->andReturn([]);

            $plan = Plan::make();
            $paymentMethod = Type::CASH();
            $optionalParams = Collection::make($dataSet['input']);

            app()->forgetInstance(BranchesRepository::class);
            $branchesRepositoryMock = \Mockery::mock(BranchesRepository::class);

            $branchesRepositoryMock->shouldReceive('first')
                ->andReturn($branchMock);

            app()->instance(BranchesRepository::class, $branchesRepositoryMock);

            $this->validateMembershipCanBeBoughtFromCurrentMock
                ->shouldReceive('execute')
                ->andReturn();

            $this->purchaseMembershipWhileLockedValidatorMock
                ->shouldReceive('execute')
                ->andReturn();

            $this->setMembershipServiceMock->shouldReceive('execute')
                ->andReturn(['id' => 'user-membership-id-123']);

            $this->paymentMethodsRepository
                ->shouldReceive('addCriteria')
                ->twice()
                ->andReturnSelf()
                ->getMock()
                ->shouldReceive('firstOrFail')
                ->once()
                ->andReturn(
                    new PaymentMethod([
                        '_id' => new \MongoId(),
                    ])
                );

            $this->usersRepository
                ->shouldReceive('addCriteria')
                ->andReturnSelf();
            $this->usersRepository
                ->shouldReceive('firstOrFail')
                ->andReturn($userMock);

            $this->idResolverMock
                ->shouldReceive('fromUserIdAndPaymentMethodId')
                ->once()
                ->andReturns();

            $this->termsValidator
                ->shouldReceive('validate')
                ->andReturn(true);

            $this->planMinimumPricingValidator
                ->shouldReceive('validate')
                ->andReturn(true);

            /** @noinspection PhpParamsInspection */
            $purchaseService = new PurchaseService(
                $this->setMembershipServiceMock,
                $this->aggregatedValidator,
                $this->paymentMethodsRepository,
                $this->usersRepository,
                $this->idResolverMock,
                $this->cartService,
                $this->termsValidator,
                $this->planMinimumPricingValidator
            );

            $data = $purchaseService->execute(
                $userMock,
                $membershipMock,
                $plan,
                $paymentMethod,
                null,
                $optionalParams
            );

            self::assertEquals($dataSet['expectedResult'], $optionalParams->get($dataSet['key']));
            self::assertSame('user-membership-id-123', $data['id']);

            app()->forgetInstance(BranchesRepository::class);
        }

        public function optionalParamProvider()
        {
            $today = Carbon::today(new \DateTimeZone('GMT'))
                ->setTimezone('UTC')
                ->getTimestamp();

            return [
                'start date' => [
                    [
                        'key' => 'start_date',
                        'expectedResult' => $today,
                        'input' => [],
                    ],
                ],
                'iso start date' => [
                    [
                        'key' => 'iso_start_date',
                        'expectedResult' => '2020-05-05T16:15:31+01:00',
                        'input' => ['iso_start_date' => '2020-05-05T16:15:31+01:00'],
                    ],
                ],
            ];
        }

        /**
         * @param int|null $startDate
         * @param Collection $params
         * @param Collection $expectedParams
         *
         * @throws \UnsuccessfulOperation
         * @dataProvider isoStartDateProvider
         */
        public function test_it_always_sends_iso_start_field_as_optional_parameters(
            ?int $startDate,
            Collection $params,
            Collection $expectedParams
        ): void {
            Carbon::setTestNow('2020-09-03 16:00:00.000Z'); // UTC

            $userId = '59a3011a05c677bda916611c';
            $user = $this->fetchUser($userId); // timezone is Dublin
            $user->set('membership', [
                'type' => 'payg',
            ]);

            $membership = new Membership(['active' => true]);
            $plan = new Plan(['price' => 10]);

            $this->setMembershipServiceMock
                ->shouldReceive('execute')
                ->withArgs(
                    function (
                        User $user,
                        Membership $membership,
                        Plan $plan,
                        Type $paymentMethodType,
                        Collection $params
                    ) use ($expectedParams) {
                        $this->assertSame('59a3011a05c677bda916611c', $user->id());
                        $this->assertSame('49a7011a05c677b9a916612a', $user->currentBranchId());
                        $this->assertTrue($membership->isActive());
                        $this->assertSame(10.0, $plan->amount());
                        $this->assertSame(Type::CARD, $paymentMethodType->getValue());
                        $this->assertSame($expectedParams->get('start_date'), $params->get('start_date'));
                        $this->assertSame($expectedParams->get('iso_start_date'), $params->get('iso_start_date'));

                        return true;
                    }
                )
                ->andReturn(['id' => 'id-123']);

            $this->aggregatedValidator
                ->shouldReceive('validate')
                ->with($user, $membership, $plan);

            $this->usersRepository
                ->shouldReceive('addCriteria')
                ->andReturnSelf();
            $this->usersRepository
                ->shouldReceive('firstOrFail')
                ->andReturn($user);

            $this->paymentMethodsRepository
                ->shouldReceive('addCriteria')
                ->twice()
                ->andReturnSelf()
                ->getMock()
                ->shouldReceive('firstOrFail')
                ->once()
                ->andReturn(
                    new PaymentMethod([
                        '_id' => new \MongoId(),
                    ])
                );

            $this->idResolverMock
                ->shouldReceive('fromUserIdAndPaymentMethodId')
                ->once()
                ->andReturn('456');

            $purchaseService = new PurchaseService(
                $this->setMembershipServiceMock,
                $this->aggregatedValidator,
                $this->paymentMethodsRepository,
                $this->usersRepository,
                $this->idResolverMock,
                $this->cartService,
                $this->termsValidator,
                $this->planMinimumPricingValidator
            );

            $purchaseService->execute(
                $user,
                $membership,
                $plan,
                Type::CARD(),
                $startDate, // timezone behind branch's timezone
                $params
            );

            Carbon::setTestNow();
        }

        public function isoStartDateProvider(): array
        {
            return [
                'start date is provided in request payload' => [
                    $startDate = Carbon::parse('2020-09-03 10:00:00', 'America/Sao_Paulo')->getTimestamp(),
                    $params = collect(),
                    $expectedParams = collect([
                        'start_date' => $startDate,
                        'iso_start_date' => '2020-09-03T14:00:00+01:00[Europe/Dublin]',
                    ]),
                ],
                'start date is not provided in request payload' => [
                    $startDate = null,
                    $params = collect(),
                    $expectedParams = collect([
                        'start_date' => Carbon::parse('2020-09-02 23:00:00', 'UTC')->getTimestamp(),
                        'iso_start_date' => '2020-09-03T00:00:00+01:00[Europe/Dublin]',
                    ]),
                ],
                'iso start date is already provided in request payload' => [
                    $startDate = Carbon::parse('2020-09-03 00:00:00', 'America/Sao_Paulo')->getTimestamp(),
                    $params = collect([
                        'iso_start_date' => '2020-09-03T20:00:00+01:00[Europe/Dublin]',
                    ]),
                    $expectedParams = collect([
                        'start_date' => $startDate,
                        'iso_start_date' => '2020-09-03T20:00:00+01:00[Europe/Dublin]',
                    ]),
                ],
            ];
        }

        public function test_exception_from_memberships_service(): void
        {
            $response = new Response(400, [], '{"key":"FOO_BAR"}');
            $exception = MembershipResponseException::createFromResponse($response);

            $this->setMembershipServiceMock
                ->shouldReceive('execute')
                ->andThrow($exception)
                ->once();

            $this->aggregatedValidator
                ->shouldReceive('validate');

            $this->paymentMethodsRepository
                ->shouldReceive('addCriteria')
                ->times(4)
                ->andReturnSelf()
                ->getMock()
                ->shouldReceive('firstOrFail')
                ->twice()
                ->andReturn(
                    new PaymentMethod([
                        '_id' => new \MongoId(),
                    ])
                );

            $this->idResolverMock
                ->shouldReceive('fromUserIdAndPaymentMethodId')
                ->twice()
                ->andReturn('456');

            $service = new PurchaseService(
                $this->setMembershipServiceMock,
                $this->aggregatedValidator,
                $this->paymentMethodsRepository,
                $this->usersRepository,
                $this->idResolverMock,
                $this->cartService,
                $this->termsValidator,
                $this->planMinimumPricingValidator
            );

            $userId = '59a7011a05c677bda916612a';
            $user = $this->fetchUser($userId);
            $membership = new Membership(['active' => true]);
            $plan = new Plan(['price' => 10]);

            // test with custom error
            try {
                $service->execute(
                    $user,
                    $membership,
                    $plan,
                    Type::CARD(),
                    null,
                    new Collection([
                        'iso_start_date' => Carbon::now()->toDateTimeString(),
                    ])
                );
            } catch (MembershipResponseException $e) {
                $this->assertTrue($e->hasCustomError());
            }

            // test with no custom error
            $response = new Response(400, [], '{"foo":"bar"}');
            $exception = MembershipResponseException::createFromResponse($response);

            $this->setMembershipServiceMock
                ->shouldReceive('execute')
                ->andThrow($exception)
                ->once();

            $service = new PurchaseService(
                $this->setMembershipServiceMock,
                $this->aggregatedValidator,
                $this->paymentMethodsRepository,
                $this->usersRepository,
                $this->idResolverMock,
                $this->cartService,
                $this->termsValidator,
                $this->planMinimumPricingValidator
            );

            try {
                $service->execute(
                    $user,
                    $membership,
                    $plan,
                    Type::CARD(),
                    null,
                    new Collection([
                        'iso_start_date' => Carbon::now()->toDateTimeString(),
                    ])
                );
            } catch (\UnsuccessfulOperation $e) {
                $this->assertSame('MS_ERROR_PURCHASING_MEMBERSHIP', $e->getMessage());
            }
        }

        public function test_exception_from_cart_service(): void
        {
            $exception = new HttpClientResponseException('500', 'card declined');

            $this->cartService
                ->shouldReceive('legacyPurchase')
                ->andThrow($exception)
                ->once();

            $this->aggregatedValidator
                ->shouldReceive('validate');

            $this->paymentMethodsRepository
                ->shouldReceive('addCriteria')
                ->twice()
                ->andReturnSelf()
                ->getMock()
                ->shouldReceive('firstOrFail')
                ->once()
                ->andReturn(
                    new PaymentMethod([
                        '_id' => new \MongoId(),
                    ])
                );

            $this->idResolverMock
                ->shouldReceive('fromUserIdAndPaymentMethodId')
                ->once()
                ->andReturn('456');

            $this->termsValidator
                ->shouldReceive('validate')
                ->andReturn(true);

            $this->planMinimumPricingValidator
                ->shouldReceive('validate')
                ->andReturn(true);

            $userId = '59a7011a05c677bda916612a';
            $user = $this->fetchUser($userId);
            $membership = new Membership(['active' => true, '_id' => 'membership-123']);

            $plan = new Plan(['price' => 10, 'type' => MembershipsType::TIME]);
            $branch = new Branch(['_id' => $user->branch()->id()]);

            $branchesRepository = \Mockery::mock(BranchesRepository::class);
            $branchesRepository
                ->shouldReceive('addCriteria')
                ->andReturnSelf()
                ->getMock()
                ->shouldReceive('firstOrFail')
                ->andReturn($branch);

            app()->instance(BranchesRepository::class, $branchesRepository);

            $paymentMethodType = Type::CARD;
            $paymentMethodId = new \MongoId();
            $paymentMethod = Paymentmethod::make([
                '_id' => $paymentMethodId,
                'type_id' => $paymentMethodType,
                'provider' => Provider::make([
                    'account_id' => '54321',
                ]),
            ]);

            $paymentProvider = payments()->provider($user->currentBranchId(), $paymentMethodType);
            $paymentProvider = \Mockery::mock($paymentProvider);
            $paymentProvider->shouldReceive('paymentMethod')->andReturn($paymentMethod);
            $paymentProvider->shouldReceive('id')->andReturn($paymentMethodId);

            Auth::loginAs($user);
            $auth = app()->make(Auth::class);
            $auth->paymentMethods[$paymentMethodType] = $paymentProvider;
            app()->instance(Auth::class, $auth);

            $overdraftValidator = \Mockery::mock(OverdraftValidatorService::class);
            $overdraftValidator->shouldReceive('validate')->andReturn(false)->once();
            app()->instance(OverdraftValidatorService::class, $overdraftValidator);

            $service = new PurchaseService(
                $this->setMembershipServiceMock,
                $this->aggregatedValidator,
                $this->paymentMethodsRepository,
                $this->usersRepository,
                $this->idResolverMock,
                $this->cartService,
                $this->termsValidator,
                $this->planMinimumPricingValidator
            );

            try {
                $service->executeViaCart(
                    $user,
                    $membership,
                    $plan,
                    Type::CARD(),
                    null,
                    new Collection([
                        'iso_start_date' => Carbon::now()->toDateTimeString(),
                    ])
                );
            } catch (UnsuccessfulOperation $e) {
                $this->assertEquals("PURCHASE_SERVICE_CART_ERROR", $e->getMessage());
            }

            app()->forgetInstance(BranchesRepository::class);
            app()->forgetInstance(Auth::class);
            app()->forgetInstance(OverdraftValidatorService::class);
        }

        public function test_execute_via_cart_success(): void
        {
            $cartBody = [
                'status' => 'SUCCESS',
                'invoice_id' => 'invoice-123',
                'failure_reason' => ''
            ];

            $membershipId = '88a7011a05c677bda91663c2';
            $userId = '59a7011a05c677bda916612a';
            $user = $this->fetchUser($userId);
            $membership = new Membership(
                ['active' => true, '_id' => new MongoId($membershipId), 'name' => 'Best Membership 2022']
            );
            $plan = new Plan(['price' => 10, 'code' => 112233, 'type' => MembershipsType::TIME, 'force_start' => true]);
            $branch = new Branch(['_id' => $user->branch()->id()]);
            $payer = [
                'email' => '<EMAIL>',
                'name' => 'Admin Istrator',
                'first_name' => 'Admin',
                'last_name' => 'Istrator',
                'phone' => '*********',
                'tax_id' => null
            ];

            $purchaseFields = [
                'membership_code' => $membershipId,
                'plan_code' => $plan->code(),
                'branch_id' => '49a7011a05c677b9a916612a',
                'user_id' => $userId,
                'payment_method' => 'CARD',
                'price' => 10,
                'upfront_fee' => 0,
                'is_consecutive_membership' => false,
                'is_terms_and_conditions_accepted' => true,
                'future_flexible_payments' => false,
                'discounts' => [],
                'merchant_account_id' => 54321,
                'customer_account_id' => 456,
                'membership_name' => 'Best Membership 2022',
                'currency' => 'EUR',
                'is_prorated' => true,
                'is_subscription' => false,
                'use_available_account_balance' => false,
                'allow_overdraft' => false,
                'payer' => $payer,
                'plan_name' => $plan->name()
            ];

            $cartResponse = \Mockery::mock(\Illuminate\Http\JsonResponse::class);
            $cartResponse
                ->shouldReceive('getData')
                ->andReturn($cartBody)
                ->twice()
                ->getMock();

            $this->cartService
                ->shouldReceive('legacyPurchase')
                ->withArgs(
                    function (string $body) use ($purchaseFields) {
                        $jsonBody = json_decode($body, true, 512, JSON_THROW_ON_ERROR);
                        $this->assertEquals($jsonBody['membership_code'], $purchaseFields['membership_code']);
                        $this->assertEquals($jsonBody['plan_code'], $purchaseFields['plan_code']);
                        $this->assertEquals($jsonBody['branch_id'], $purchaseFields['branch_id']);
                        $this->assertEquals($jsonBody['user_id'], $purchaseFields['user_id']);
                        $this->assertEquals($jsonBody['payment_method'], $purchaseFields['payment_method']);
                        $this->assertEquals($jsonBody['price'], $purchaseFields['price']);
                        $this->assertEquals($jsonBody['upfront_fee'], $purchaseFields['upfront_fee']);
                        $this->assertEquals(
                            $jsonBody['is_consecutive_membership'],
                            $purchaseFields['is_consecutive_membership']
                        );
                        $this->assertEquals(
                            $jsonBody['is_terms_and_conditions_accepted'],
                            $purchaseFields['is_terms_and_conditions_accepted']
                        );
                        $this->assertEquals(
                            $jsonBody['future_flexible_payments'],
                            $purchaseFields['future_flexible_payments']
                        );
                        $this->assertEquals($jsonBody['discounts'], $purchaseFields['discounts']);
                        $this->assertEquals($jsonBody['merchant_account_id'], $purchaseFields['merchant_account_id']);
                        $this->assertEquals($jsonBody['customer_account_id'], $purchaseFields['customer_account_id']);
                        $this->assertEquals($jsonBody['membership_name'], $purchaseFields['membership_name']);
                        $this->assertEquals($jsonBody['currency'], $purchaseFields['currency']);
                        $this->assertEquals($jsonBody['is_prorated'], $purchaseFields['is_prorated']);
                        $this->assertNotNull($jsonBody['prorated_percentage']);
                        $this->assertEquals($jsonBody['is_subscription'], $purchaseFields['is_subscription']);
                        $this->assertEquals(
                            $jsonBody['use_available_account_balance'],
                            $purchaseFields['use_available_account_balance']
                        );
                        $this->assertEquals($jsonBody['allow_overdraft'], $purchaseFields['allow_overdraft']);
                        $this->assertEquals($jsonBody['payer'], $purchaseFields['payer']);
                        $this->assertEquals($jsonBody['plan_name'], $purchaseFields['plan_name']);
                        $this->assertNotEmpty($jsonBody['start_date']);
                        $this->assertNotEmpty($jsonBody['iso_start_date']);

                        return true;
                    }
                )
                ->once()
                ->andReturn($cartResponse);

            $this->aggregatedValidator
                ->shouldReceive('validate');

            $this->paymentMethodsRepository
                ->shouldReceive('addCriteria')
                ->twice()
                ->andReturnSelf()
                ->getMock()
                ->shouldReceive('firstOrFail')
                ->once()
                ->andReturn(
                    new PaymentMethod([
                        '_id' => new \MongoId(),
                    ])
                );

            $this->idResolverMock
                ->shouldReceive('fromUserIdAndPaymentMethodId')
                ->once()
                ->andReturn('456');

            $this->termsValidator
                ->shouldReceive('validate')
                ->andReturn(true);

            $this->planMinimumPricingValidator
                ->shouldReceive('validate')
                ->andReturn(true);

            $branchesRepository = \Mockery::mock(BranchesRepository::class);
            $branchesRepository
                ->shouldReceive('addCriteria')
                ->andReturnSelf()
                ->getMock()
                ->shouldReceive('firstOrFail')
                ->andReturn($branch);

            app()->instance(BranchesRepository::class, $branchesRepository);

            $paymentMethodType = Type::CARD;
            $paymentMethodId = new \MongoId();
            $paymentMethod = Paymentmethod::make([
                '_id' => $paymentMethodId,
                'type_id' => $paymentMethodType,
                'provider' => Provider::make([
                    'account_id' => '54321',
                ]),
            ]);

            $paymentProvider = payments()->provider($user->currentBranchId(), $paymentMethodType);
            $paymentProvider = \Mockery::mock($paymentProvider);
            $paymentProvider->shouldReceive('paymentMethod')->andReturn($paymentMethod);
            $paymentProvider->shouldReceive('id')->andReturn($paymentMethodId);

            Auth::loginAs($user);
            $auth = app()->make(Auth::class);
            $auth->paymentMethods[$paymentMethodType] = $paymentProvider;
            app()->instance(Auth::class, $auth);

            $overdraftValidator = \Mockery::mock(OverdraftValidatorService::class);
            $overdraftValidator->shouldReceive('validate')->andReturn(false)->once();
            app()->instance(OverdraftValidatorService::class, $overdraftValidator);

            $service = new PurchaseService(
                $this->setMembershipServiceMock,
                $this->aggregatedValidator,
                $this->paymentMethodsRepository,
                $this->usersRepository,
                $this->idResolverMock,
                $this->cartService,
                $this->termsValidator,
                $this->planMinimumPricingValidator
            );

            $response = $service->executeViaCart(
                $user,
                $membership,
                $plan,
                Type::CARD(),
                null,
                new Collection([
                    'iso_start_date' => Carbon::now()->toDateTimeString(),
                ])
            );

            self::assertEquals('SUCCESS', $response['status']);

            app()->forgetInstance(BranchesRepository::class);
            app()->forgetInstance(Auth::class);
            app()->forgetInstance(OverdraftValidatorService::class);
        }

        public function test_execute_via_cart_minimum_price_failure(): void
        {
            $cartBody = [
                'status' => 'SUCCESS',
                'invoice_id' => 'invoice-123',
                'failure_reason' => ''
            ];

            $membershipId = '88a7011a05c677bda91663c2';
            $userId = '59a7011a05c677bda916612a';
            $user = $this->fetchUser($userId);
            $membership = new Membership(
                ['active' => true, '_id' => new MongoId($membershipId), 'name' => 'Best Membership 2022']
            );
            $plan = new Plan(['price' => 10, 'code' => 112233, 'type' => MembershipsType::TIME, 'force_start' => true]);
            $branch = new Branch(['_id' => $user->branch()->id()]);

            $this->aggregatedValidator
                ->shouldReceive('validate');

            $this->termsValidator
                ->shouldReceive('validate')
                ->andReturn(true);

            $this->planMinimumPricingValidator
                ->shouldReceive('validate')
                ->andThrow(new MembershipPlanMinimumPriceValidationException())
                ->once();

            $branchesRepository = \Mockery::mock(BranchesRepository::class);
            $branchesRepository
                ->shouldReceive('addCriteria')
                ->andReturnSelf()
                ->getMock()
                ->shouldReceive('firstOrFail')
                ->andReturn($branch);

            app()->instance(BranchesRepository::class, $branchesRepository);

            $service = new PurchaseService(
                $this->setMembershipServiceMock,
                $this->aggregatedValidator,
                $this->paymentMethodsRepository,
                $this->usersRepository,
                $this->idResolverMock,
                $this->cartService,
                $this->termsValidator,
                $this->planMinimumPricingValidator
            );

            try {
                $service->executeViaCart(
                    $user,
                    $membership,
                    $plan,
                    Type::CARD(),
                    null,
                    new Collection([
                        'iso_start_date' => Carbon::now()->toDateTimeString(),
                    ])
                );
            } catch (\UnsuccessfulOperation $ex) {
                self::assertEquals(['PLAN_MINIMUM_PRICE_VALIDATION_ERROR'], $ex->getMessageData());
            }

            app()->forgetInstance(BranchesRepository::class);
            app()->forgetInstance(Auth::class);
            app()->forgetInstance(OverdraftValidatorService::class);
        }

        public function testBuildLegacyPurchaseRequestZeroFullPrice(): void
        {
            $zeroFullPriceRequest = $this->getLegacyPurchaseRequest(
                new Collection([
                    'full_price' => 0,
                ]),
                10
            );
            self::assertEquals(0, $zeroFullPriceRequest['price']);
        }

        public function testBuildLegacyPurchaseRequestZeroSubscriptionAmount(): void
        {
            $zeroSubscriptionAmount = $this->getLegacyPurchaseRequest(
                new Collection([
                    'subscription_amount' => 0,
                ]),
                10
            );
            self::assertEquals(0, $zeroSubscriptionAmount['price']);
        }

        public function testBuildLegacyPurchaseRequestZeroDiscountedSubscriptionAmount(): void
        {
            $zeroDiscountedSubscriptionAmount = $this->getLegacyPurchaseRequest(
                new Collection([
                    'discounted_subscription_amount' => 0,
                ]),
                10
            );
            self::assertEquals(0, $zeroDiscountedSubscriptionAmount['price']);
        }

        public function testBuildLegacyPurchaseRequestNonZeroFullPrice(): void
        {
            $nonZeroFullPriceRequest = $this->getLegacyPurchaseRequest(
                new Collection([
                    'full_price' => 777,
                ]),
                10
            );
            self::assertEquals(777, $nonZeroFullPriceRequest['price']);
        }

        public function testBuildLegacyPurchaseRequestNonZeroSubscriptionAmount(): void
        {
            $nonZeroSubscriptionAmount = $this->getLegacyPurchaseRequest(
                new Collection([
                    'subscription_amount' => 777,
                ]),
                10
            );
            self::assertEquals(777, $nonZeroSubscriptionAmount['price']);
        }

        public function testBuildLegacyPurchaseRequestNonZeroDiscountedSubscriptionAmount(): void
        {
            $nonZeroDiscountedSubscriptionAmount = $this->getLegacyPurchaseRequest(
                new Collection([
                    'discounted_subscription_amount' => 777,
                ]),
                10
            );
            self::assertEquals(777, $nonZeroDiscountedSubscriptionAmount['price']);
        }

        public function testBuildLegacyPurchaseRequestGiftFirstPeriodFree(): void
        {
            $gfpf = $this->getLegacyPurchaseRequest(Collection::make(), 10);
            self::assertEquals(true, $gfpf['is_gift_first_period_free']);
        }

        public function testBuildLegacyPurchaseRequestPlanPrice(): void
        {
            $noFullPriceRequest = $this->getLegacyPurchaseRequest(Collection::make(), 10);
            self::assertEquals(10, $noFullPriceRequest['price']);
        }

        public function testBuildLegacyPurchaseRequestForCreditPackByMemberWithActiveMembership(): void
        {
            $membershipId = '88a7011a05c677bda91663c2';
            $userId = 'a9a3321a05c677bda916611c';  // user with an active membership
            $user = $this->fetchUser($userId);
            $membership = new Membership(
                ['active' => true, '_id' => new MongoId($membershipId), 'name' => 'Best Membership 2022']
            );
            $plan = new Plan(
                [
                    'price' => 30,
                    'code' => 112233,
                    'type' => MembershipsType::NUM_CLASSES,
                    'force_start' => true,
                    'duration_time_unit_count' => 1,
                    'duration_time_unit' => 'MONTH',
                    'free_time_unit_count' => 1
                ]
            );

            $this->termsValidator
                ->shouldReceive('validate')
                ->andReturn(true);

            $this->planMinimumPricingValidator
                ->shouldReceive('validate')
                ->andReturn(true);

            $service = new PurchaseService(
                $this->setMembershipServiceMock,
                $this->aggregatedValidator,
                $this->paymentMethodsRepository,
                $this->usersRepository,
                $this->idResolverMock,
                $this->cartService,
                $this->termsValidator,
                $this->planMinimumPricingValidator
            );

            $request = $service->buildLegacyPurchaseRequest(
                $user,
                $membership,
                $plan,
                Type::CARD(),
                new Collection()
            );

            self::assertEquals(false, $request['is_consecutive_membership']);
        }

        public function testBuildLegacyPurchaseRequestForUnlimitedMembershipForMemberWithActiveMembership(): void
        {
            $membershipId = '88a7011a05c677bda91663c2';
            $userId = 'a9a3321a05c677bda916611c';  // user with an active membership
            $user = $this->fetchUser($userId);
            $membership = new Membership(
                ['active' => true, '_id' => new MongoId($membershipId), 'name' => 'Best Membership 2022']
            );
            $plan = new Plan(
                [
                    'price' => 30,
                    'code' => 112233,
                    'type' => MembershipsType::TIME,
                    'force_start' => true,
                    'duration_time_unit_count' => 1,
                    'duration_time_unit' => 'MONTH',
                    'free_time_unit_count' => 1
                ]
            );

            $this->termsValidator
                ->shouldReceive('validate')
                ->andReturn(true);

            $this->planMinimumPricingValidator
                ->shouldReceive('validate')
                ->andReturn(true);

            $service = new PurchaseService(
                $this->setMembershipServiceMock,
                $this->aggregatedValidator,
                $this->paymentMethodsRepository,
                $this->usersRepository,
                $this->idResolverMock,
                $this->cartService,
                $this->termsValidator,
                $this->planMinimumPricingValidator
            );

            $request = $service->buildLegacyPurchaseRequest(
                $user,
                $membership,
                $plan,
                Type::CARD(),
                new Collection()
            );

            self::assertEquals(true, $request['is_consecutive_membership']);
        }

        public function testMembershipPlanFrequencyFields(): void
        {
            $req = $this->getLegacyPurchaseRequest(Collection::make(), 10);
            self::assertEquals(1, $req['duration_time_unit_count']);
            self::assertEquals('MONTH', $req['duration_time_unit']);
        }

        public function testBuildLegacyPurchaseRequestZIsRenewalTermsAndConditionsAccepted(): void
        {
            $isRenewalTermsAndConditionsAccepted = $this->getLegacyPurchaseRequest(
                new Collection([
                    'is_renewal_terms_and_conditions_accepted' => true,
                ]),
                10
            );
            self::assertTrue($isRenewalTermsAndConditionsAccepted['is_renewal_terms_and_conditions_accepted']);
        }

        private function getLegacyPurchaseRequest(Collection $optionalParams, int $planPrice): Collection
        {
            $membershipId = '88a7011a05c677bda91663c2';
            $userId = '59a7011a05c677bda916612a';
            $user = $this->fetchUser($userId);
            $membership = new Membership(
                ['active' => true, '_id' => new MongoId($membershipId), 'name' => 'Best Membership 2022']
            );
            $plan = new Plan(
                [
                    'price' => $planPrice,
                    'code' => 112233,
                    'type' => MembershipsType::TIME,
                    'force_start' => true,
                    'duration_time_unit_count' => 1,
                    'duration_time_unit' => 'MONTH',
                    'free_time_unit_count' => 1
                ]
            );

            $this->termsValidator
                ->shouldReceive('validate')
                ->andReturn(true);

            $this->planMinimumPricingValidator
                ->shouldReceive('validate')
                ->andReturn(true);

            $service = new PurchaseService(
                $this->setMembershipServiceMock,
                $this->aggregatedValidator,
                $this->paymentMethodsRepository,
                $this->usersRepository,
                $this->idResolverMock,
                $this->cartService,
                $this->termsValidator,
                $this->planMinimumPricingValidator
            );

            $request = $service->buildLegacyPurchaseRequest(
                $user,
                $membership,
                $plan,
                Type::CARD(),
                $optionalParams
            );

            return $request;
        }
    }
}
