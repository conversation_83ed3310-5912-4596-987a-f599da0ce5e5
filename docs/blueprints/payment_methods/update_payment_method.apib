
## Group Payment Methods

### Update Payment Method [/branches/{branchId}/payment-methods/{payment-methods-id}]

#### Update Payment Method [PATCH]

+ Parameters
    + includes: provider,provider.iframe (string, optional) - comma separated list of nested resources that should be included as part of the response

+ Request (application/json)

        {
            "active": true,
            "staff_only": false,
            "provider": {
                "charge_percentage": 0,
                "fixed_charge": 0
            }
        }

+ Response 200 (application/json)

        {
            "success": true,
            "data": {
                "_id": "605caaeae6bfb64e62463d1b",
                "branch_id": "605b7ab5d198a676d6264de3",
                "active": true,
                "staff_only": false,
                "type_id": "DIRECT_DEBIT",
                "account_management_link": ""
            }
        }
