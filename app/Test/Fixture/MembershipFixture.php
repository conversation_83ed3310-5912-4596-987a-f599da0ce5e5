<?php

class MembershipFixture extends CakeTestFixture
{
    public $import = ['model' => 'Membership'];

    private const BRANCH_ID = '49a7011a05c677b9a916612a';
    private const NAMESPACE = 'glofox';

    public function init()
    {
        $baseMemberships = [
            [
                '_id' => new MongoId('54107c1cd7b6ddc3a98b4577'),
                'branch_id' => self::BRANCH_ID,
                'namespace' => self::NAMESPACE,
                'active' => true,
                'created' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'description' => 'For premium members',
                'modified' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'name' => 'Gold Membership',
                'buy_just_once' => true,
                'plans' => [
                    [
                        'type' => 'time_classes',
                        'code' => '1506335805777',
                        'name' => 'Expiry Date * Number of Classes',
                        'price' => 10,
                        'duration_time_unit' => 'week',
                        'duration_time_unit_count' => 1,
                        'upfront_fee' => 0,
                        'credits' => [
                            'branch_id' => self::BRANCH_ID,
                            'namespace' => self::NAMESPACE,
                            'active' => true,
                            'model' => 'programs',
                            'category_id' => null,
                            'num_sessions' => 10,
                            'model_ids' => null,
                            'expiry' => null,
                        ],
                    ],
                    [
                        'type' => 'time_classes',
                        'code' => '54107aced7b6dd7aab8b4567',
                        'name' => 'Expiry Date * Number of Classes',
                        'price' => 30,
                        'membership_group_id' => '54107aced7b6dd7aab8b4567',
                        'num_classes' => 10,
                        'duration_time_unit' => 'month',
                        'duration_time_unit_count' => 3,
                    ],
                    [
                        'type' => 'time_classes',
                        'code' => '1506335805020',
                        'name' => 'Expiry Date * Number of Classes',
                        'price' => 10,
                        'duration_time_unit' => 'week',
                        'duration_time_unit_count' => 1,
                        'upfront_fee' => 0,
                        'credits' => [
                            'branch_id' => self::BRANCH_ID,
                            'namespace' => self::NAMESPACE,
                            'active' => true,
                            'model' => 'programs',
                            'category_id' => null,
                            'num_sessions' => 10,
                            'model_ids' => null,
                            'expiry' => [
                                'interval' => 'month',
                                'interval_count' => 1,
                            ],
                        ],
                    ],
                    [
                        'type' => 'time',
                        'code' => '1506335805840',
                        'name' => 'Expiry Date',
                        'price' => 10,
                        'duration_time_unit' => 'month',
                        'duration_time_unit_count' => 1,
                        'upfront_fee' => 0,
                    ],
                    [
                        'type' => 'time',
                        'code' => '1506335805841',
                        'name' => 'Monthly Subscription',
                        'price' => 10,
                        'duration_time_unit' => 'month',
                        'duration_time_unit_count' => 1,
                        'upfront_fee' => 0,
                        'starts_on' => 'PURCHASE_DATE',
                        '_subscription_plan_id' => true,
                        'subscription_plan_id' => '5be4551117844100fd1f0e84',
                        'amount' => 10,
                    ],
                ],
                'trial' => false,
            ],
            [
                '_id' => new MongoId('54107c1cd7b6ddc3a98b4543'),
                'branch_id' => self::BRANCH_ID,
                'namespace' => self::NAMESPACE,
                'active' => true,
                'created' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'description' => 'Subscription',
                'modified' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'name' => 'Gold Membership',
                'plans' => [
                    [
                        'code' => 'a1560347192843',
                        'price' => 10,
                        'type' => 'time',
                        'duration_time_unit' => 'day',
                        'duration_time_unit_count' => 1,
                        'upfront_fee' => 0,
                        'credits' => [],
                        'starts_on' => 'PURCHASE_DATE',
                        '_toggle' => true,
                        'name' => '1380 (Plan 1)',
                        'accepted_payment_methods' => [
                            [
                                'type_id' => 'CARD',
                                'active' => true,
                            ],
                        ],
                        '_has_end' => false,
                        'amount' => 10,
                        '_subscription_plan_id' => true,
                        'subscription_plan_id' => '5a53b98625f149480f000001',
                    ],
                    [
                        'code' => 'a1560347192844',
                        'price' => 10,
                        'type' => 'time',
                        'duration_time_unit' => 'day',
                        'duration_time_unit_count' => 1,
                        'upfront_fee' => 0,
                        'credits' => [],
                        'starts_on' => 'PURCHASE_DATE',
                        '_toggle' => true,
                        'name' => '1380 (Plan 1)',
                        'accepted_payment_methods' => [
                            [
                                'type_id' => 'CARD',
                                'active' => true,
                            ],
                        ],
                        '_has_end' => false,
                        'amount' => 10,
                        '_subscription_plan_id' => true,
                        'subscription_plan_id' => '5a53b98625f149480f000002',
                    ],
                ],
                'trial' => false,
            ],
            [
                '_id' => new MongoId('5a146888d721b079504aa0ee'),
                'branch_id' => self::BRANCH_ID,
                'namespace' => self::NAMESPACE,
                'active' => true,
                'created' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'description' => 'Start on First Booking Date Membership',
                'modified' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'name' => 'Start on First Booking Date Membership',
                'buy_just_once' => false,
                'plans' => [
                    [
                        'type' => 'time',
                        'code' => '5a1430309b264e6a99186003',
                        'name' => 'Starts on first booking date',
                        'price' => 10,
                        'duration_time_unit' => 'month',
                        'duration_time_unit_count' => 1,
                        'upfront_fee' => 0,
                        'starts_on' => 'FIRST_BOOKING_DATE',
                    ],
                ],
            ],
            [
                '_id' => new MongoId('5a146888d721b079504aa0ff'),
                'branch_id' => self::BRANCH_ID,
                'namespace' => self::NAMESPACE,
                'name' => 'Trial Membership',
                'active' => true,
                'created' => new MongoDate(\Carbon\Carbon::now()->getTimestamp()),
                'description' => 'Trial Membership',
                'modified' => new MongoDate(\Carbon\Carbon::now()->getTimestamp()),
                'buy_just_once' => false,
                'plans' => [
                    [
                        'type' => 'time',
                        'code' => '5a1430309b264e6a99186004',
                        'name' => 'Trial Membership',
                        'price' => 10,
                        'duration_time_unit' => 'month',
                        'duration_time_unit_count' => 1,
                        'upfront_fee' => 0,
                    ],
                ],
                'trial' => true,
            ],
            [
                '_id' => new MongoId('5b19486ac13acae3b31cf563'),
                'branch_id' => self::BRANCH_ID,
                'namespace' => self::NAMESPACE,
                'active' => true,
                'created' => new MongoDate(\Carbon\Carbon::now()->getTimestamp()),
                'description' => 'Simple Membership',
                'modified' => new MongoDate(\Carbon\Carbon::now()->getTimestamp()),
                'buy_just_once' => false,
                'plans' => [
                    [
                        'type' => 'time',
                        'code' => '1521554388960',
                        'name' => 'Simple Plan',
                        'price' => 10,
                        'duration_time_unit' => 'week',
                        'duration_time_unit_count' => 1,
                        'upfront_fee' => 0,
                    ],
                ],
                'trial' => false,
            ],
            [
                '_id' => new MongoId('5b7ad3cd01c9bb54498cac07'),
                'branch_id' => self::BRANCH_ID,
                'branches' => [
                    self::BRANCH_ID,
                    '5a9591bcdb07bce527400717',
                ],
                'namespace' => self::NAMESPACE,
                'active' => true,
                'created' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'description' => 'Roaming Membership',
                'modified' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'name' => 'Roaming Membership',
                'plans' => [
                    [
                        'type' => 'time_classes',
                        'code' => '1506335305324',
                        'name' => 'Expiry Date * Number of Classes',
                        'price' => 10,
                        'duration_time_unit' => 'week',
                        'duration_time_unit_count' => 1,
                        'upfront_fee' => 0,
                        'credits' => [
                            'model' => 'programs',
                            'category_id' => null,
                            'num_sessions' => 10,
                            'model_ids' => null,
                            'expiry' => [
                                'interval' => 'month',
                                'interval_count' => 1,
                            ],
                        ],
                    ],
                ],
                'trial' => false,
            ],
            [
                '_id' => new MongoId('5b8eb03b10e2c82a4d443f9c'),
                'branch_id' => '5addc25383266f65abf515c4',
                'namespace' => 'classpass_integrated',
                'active' => true,
                'created' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'description' => 'Starts on First Booking Membership',
                'modified' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'name' => 'Starts on First Booking Membership',
                'buy_just_once' => false,
                'plans' => [
                    [
                        'code' => 1_536_078_615_117,
                        'price' => 10,
                        'type' => 'num_classes',
                        'upfront_fee' => 0.0,
                        'credits' => [
                            [
                                'model' => 'programs',
                                'category_id' => null,
                                'num_sessions' => 10,
                                'model_ids' => [],
                                'expiry' => [
                                    'interval' => 'month',
                                    'interval_count' => 1,
                                ],
                                'end_date' => null,
                            ],
                        ],
                        'starts_on' => 'FIRST_BOOKING_DATE',
                        '_subscription_plan_id' => false,
                        'name' => 'Start on first booking Credit Pack',
                    ],
                ],
            ],
            [
                '_id' => new MongoId('5b8c5bc05e195e316e074ff4'),
                'branch_id' => self::BRANCH_ID,
                'namespace' => self::NAMESPACE,
                'active' => true,
                'created' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'description' => 'First Booking test membership',
                'modified' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'name' => 'First Booking test membership',
                'plans' => [
                    [
                        'type' => 'time',
                        'code' => '1521335368323',
                        'starts_on' => 'FIRST_BOOKING_DATE',
                        'name' => 'Time membership first booking date',
                        'price' => 10,
                        'duration_time_unit' => 'month',
                        'duration_time_unit_count' => 1,
                    ],
                    [
                        'type' => 'num_classes',
                        'code' => '1529406154886',
                        'name' => 'Credit Pack with first booking date',
                        'price' => 20,
                        'upfront_fee' => 0,
                        'credits' => [
                            [
                                'model' => 'programs',
                                'category_id' => null,
                                'num_sessions' => 10,
                                'model_ids' => null,
                                'expiry' => [
                                    'interval' => 'month',
                                    'interval_count' => 1,
                                ],
                                'end_date' => null,
                            ]
                        ],
                    ],
                    [
                        'type' => 'time_classes',
                        'code' => '1529406154887',
                        'name' => 'Restricted Recurring',
                        'price' => 10,
                        'force_start' => true,
                        'membership_group_id' => null,
                        'num_classes' => 10,
                        'duration_time_unit' => 'month',
                        'duration_time_unit_count' => 1,
                        'starts_on' => 'PURCHASE_DATE',
                        'credits' => [
                            [
                                'branch_id' => '49a7011a05c677b9a916612b',
                                'namespace' => self::NAMESPACE,
                                'active' => true,
                                'model' => 'users',
                                'category_id' => null,
                                'num_sessions' => 10,
                                'model_ids' => null,
                                'expiry' => [
                                    'interval' => 'month',
                                    'interval_count' => 1,
                                ],
                            ],
                        ],
                        'subscription_plan_id' => '622a11275822922a3460f232',
                        'subscription_plan_duration' => 2,
                    ],
                    [
                        'type' => 'num_classes',
                        'code' => '1529406154889',
                        'name' => 'Credit Pack with model ids',
                        'price' => 20,
                        'upfront_fee' => 0,
                        'credits' => [
                            [
                                'model' => 'programs',
                                'category_id' => null,
                                'num_sessions' => 10,
                                'model_ids' => [
                                    '13b7411a15c676bda824611b'
                                ],
                                'expiry' => [
                                    'interval' => 'month',
                                    'interval_count' => 1,
                                ],
                                'end_date' => null,
                            ]
                        ],
                    ],
                ],
                'trial' => false,
            ],
            [
                '_id' => new MongoId('54107c1cd7b6ddc3a98b4576'),
                'branch_id' => '49a7011a05c677b9a916612b',
                'namespace' => self::NAMESPACE,
                'active' => true,
                'created' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'description' => 'For premium members 1',
                'modified' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'name' => 'Gold Membership',
                'buy_just_once' => true,
                'plans' => [
                    [
                        'type' => 'time_classes',
                        'code' => '54107aced7b6dd7aab8b4568',
                        'name' => 'Expiry Date * Number of Classes',
                        'price' => 30,
                        'membership_group_id' => '54107aced7b6dd7aab8b4567',
                        'num_classes' => 10,
                        'duration_time_unit' => 'month',
                        'duration_time_unit_count' => 3,
                    ],
                ],
            ],
            [
                '_id' => new MongoId('56ceca057cad3653598b4582'),
                'branch_id' => '49a7011a05c677b9a916612b',
                'namespace' => self::NAMESPACE,
                'active' => true,
                'created' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'description' => 'Payment Intent Webhook Testing',
                'modified' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'name' => 'Gold Membership',
                'buy_just_once' => true,
                'plans' => [
                    [
                        'type' => 'time_classes',
                        'code' => '54107aced7b6dd7aab8b4568',
                        'name' => 'Expiry Date * Number of Classes',
                        'price' => 30,
                        'membership_group_id' => '54107aced7b6dd7aab8b4567',
                        'num_classes' => 10,
                        'duration_time_unit' => 'month',
                        'duration_time_unit_count' => 3,
                    ],
                ],
            ],
            [
                '_id' => new MongoId('6229f65fd5a0b9bfb6309afb'),
                'branch_id' => '49a7011a05c677b9a916612b',
                'namespace' => self::NAMESPACE,
                'active' => true,
                'created' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'description' => 'SCA Test Membership',
                'modified' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'name' => 'SCA Membership',
                'plans' => [
                    [
                        'type' => 'time_classes',
                        'code' => 'restricted-single-plan-code',
                        'name' => 'Restricted',
                        'price' => 30,
                        'membership_group_id' => null,
                        'num_classes' => 10,
                        'duration_time_unit' => 'month',
                        'duration_time_unit_count' => 1,
                        'starts_on' => 'PURCHASE_DATE',
                        'credits' => [
                            [
                                'branch_id' => '49a7011a05c677b9a916612b',
                                'namespace' => self::NAMESPACE,
                                'active' => true,
                                'model' => 'programs',
                                'category_id' => null,
                                'num_sessions' => 10,
                                'model_ids' => null,
                                'expiry' => [
                                    'interval' => 'month',
                                    'interval_count' => 1,
                                ],
                            ],
                        ],
                    ],
                    [
                        'type' => 'time_classes',
                        'code' => 'restricted-recurring-plan-code',
                        'name' => 'Restricted Recurring',
                        'price' => 10,
                        'membership_group_id' => null,
                        'num_classes' => 20,
                        'duration_time_unit' => 'month',
                        'duration_time_unit_count' => 1,
                        'starts_on' => 'PURCHASE_DATE',
                        'credits' => [
                            [
                                'branch_id' => '49a7011a05c677b9a916612b',
                                'namespace' => self::NAMESPACE,
                                'active' => true,
                                'model' => 'users',
                                'category_id' => null,
                                'num_sessions' => 20,
                                'model_ids' => null,
                                'expiry' => [
                                    'interval' => 'month',
                                    'interval_count' => 1,
                                ],
                            ],
                        ],
                        'subscription_plan_id' => '622a11275822922a3460f232',
                        'subscription_plan_duration' => 2,
                    ],
                ],
            ],
        ];

        $this->records = array_merge(
            $baseMemberships,
            $this->createCreditPackMemberships(),
            $this->createDateOfFirstBookingMemberships(),
            $this->membershipWithLongNames(),
            $this->membershipFloorPricing(),
            $this->membershipWithZeroPricing(),
            $this->createRestrictedSubForTimezoneTesting(),
            $this->forRestrictedRecurringClasses(),
            $this->forRestrictedMembershipNoCreditsNextCycle(),
            $this->forGrantCreditsOnImport(),
            $this->forAssignCreditsOnMembershipPurchasedEvent(),
        );
        parent::init();
    }

    private function createDateOfFirstBookingMemberships()
    {
        return [
            [
                '_id' => new MongoId('5b8c5bc05e195e316e074fe5'),
                'branch_id' => '5a9591bcdb07bce527400717',
                'namespace' => self::NAMESPACE,
                'active' => true,
                'description' => 'First Booking test membership',
                'name' => 'First Booking test membership',
                'trial' => false,
                'plans' => [
                    [
                        'type' => \Glofox\Domain\Memberships\Type::TIME,
                        'code' => '1521335368327',
                        'starts_on' => \MembershipPlanStartsOn::FIRST_BOOKING_DATE,
                        'name' => 'Time membership first booking date',
                        'price' => 10,
                        'duration_time_unit' => 'month',
                        'duration_time_unit_count' => 1,
                    ],
                ],
            ],
        ];
    }

    private function createCreditPackMemberships()
    {
        return [
            [
                '_id' => new MongoId('5b8c5bc05e196e316e076fe1'),
                'branch_id' => '5a9591bcdb07bce527400717',
                'namespace' => self::NAMESPACE,
                'active' => true,
                'description' => 'First Booking test membership',
                'name' => 'First Booking test membership',
                'trial' => false,
                'plans' => [
                    [
                        'code' => '15360786151111',
                        'price' => 10,
                        'type' => \Glofox\Domain\Memberships\Type::NUM_CLASSES,
                        'upfront_fee' => 0.0,
                        'credits' => [
                            [
                                'model' => 'programs',
                                'category_id' => null,
                                'num_sessions' => 10,
                                'model_ids' => [],
                                'expiry' => [
                                    'interval' => 'month',
                                    'interval_count' => 1,
                                ],
                                'end_date' => null,
                            ],
                        ],
                        'starts_on' => MembershipPlanStartsOn::PURCHASE_DATE,
                        'name' => 'Credit Pack',
                    ],
                ],
            ],
        ];
    }

    private function membershipWithLongNames(): array
    {
        return [
            [
                '_id' => new MongoId('54107c1cd7b6ddc3a98b4678'),
                'branch_id' => self::BRANCH_ID,
                'namespace' => self::NAMESPACE,
                'active' => true,
                'created' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'description' => 'For premium members',
                'modified' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'name' => 'F45 Membership - 12 month Commitment',
                'buy_just_once' => true,
                'plans' => [
                    [
                        'type' => 'time_classes',
                        'code' => '54107aced7b6dd7aab8b4567',
                        'name' => 'F45 Membership/12 month contract/Monthly billing',
                        'price' => 30,
                        'membership_group_id' => '54107aced7b6dd7aab8b4567',
                        'num_classes' => 10,
                        'duration_time_unit' => 'month',
                        'duration_time_unit_count' => 3,
                    ],
                ],
                'trial' => false,
            ],
        ];
    }

    private function membershipFloorPricing(): array
    {
        return [
            [
                '_id' => new MongoId('5f159e6962220714d0ee4ce2'),
                'branch_id' => self::BRANCH_ID,
                'namespace' => self::NAMESPACE,
                'active' => true,
                'created' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'description' => 'Membership with Floor Pricing',
                'modified' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'name' => 'Membership with Floor Pricing',
                'buy_just_once' => true,
                'trial' => false,
                'plans' => [
                    [
                        'type' => 'time',
                        'code' => 123_456_789,
                        'name' => 'Membership with Floor Pricing',
                        'price' => 30,
                        'min_price' => 30,
                        'membership_group_id' => '54107aced7b6dd7aab8b4567',
                        'num_classes' => 10,
                        'duration_time_unit' => 'month',
                        'duration_time_unit_count' => 1,
                    ],
                ],
            ],
            [
                '_id' => new MongoId('5f159e6962220714d0ee4ce3'),
                'branch_id' => self::BRANCH_ID,
                'namespace' => self::NAMESPACE,
                'active' => true,
                'created' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'description' => 'Membership with Floor Pricing 2',
                'modified' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'name' => 'Membership with Floor Pricing 2',
                'buy_just_once' => true,
                'trial' => false,
                'plans' => [
                    [
                        'type' => 'time',
                        'code' => 123_456_789,
                        'name' => 'Membership with Floor Pricing',
                        'price' => 29,
                        'min_price' => 30,
                        'membership_group_id' => '54107aced7b6dd7aab8b4567',
                        'num_classes' => 10,
                        'duration_time_unit' => 'month',
                        'duration_time_unit_count' => 1,
                    ],
                ],
            ],
        ];
    }

    private function membershipWithZeroPricing(): array
    {
        return [
            [
                '_id' => new MongoId('5f159e696222aaa4d0ee4cef'),
                'branch_id' => self::BRANCH_ID,
                'namespace' => self::NAMESPACE,
                'active' => true,
                'created' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'description' => 'Membership with Zero Pricing',
                'modified' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'name' => 'Membership with Zero Pricing',
                'buy_just_once' => true,
                'trial' => false,
                'plans' => [
                    [
                        'type' => 'time',
                        'code' => 123_456_789,
                        'name' => 'Membership with Zero Pricing',
                        'price' => 0,
                        'membership_group_id' => '54107aced7b6dd7aab8b4567',
                        'num_classes' => 10,
                        'duration_time_unit' => 'month',
                        'duration_time_unit_count' => 1,
                    ],
                ],
            ],
        ];
    }

    private function createRestrictedSubForTimezoneTesting(): array
    {
        return [
            [
                '_id' => new MongoId('5f159e696222aaa4d0ee4ce1'),
                'branch_id' => '5ce808750d35f24839739957',
                'namespace' => self::NAMESPACE,
                'active' => true,
                'created' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'description' => 'Timezone test LA branch',
                'modified' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'name' => 'Gold Membership',
                'buy_just_once' => true,
                'plans' => [
                    [
                        'type' => 'time_classes',
                        'code' => '1506335805977',
                        'name' => 'credits',
                        'price' => 10,
                        'duration_time_unit' => 'month',
                        'duration_time_unit_count' => 1,
                        'upfront_fee' => 0,
                        'credits' => [
                            'branch_id' => '5ce808750d35f24839739957',
                            'namespace' => self::NAMESPACE,
                            'active' => true,
                            'model' => 'programs',
                            'category_id' => null,
                            'num_sessions' => 10,
                            'model_ids' => null,
                            'expiry' => null,
                        ],
                    ]
                ],
                'trial' => false,
            ],
            [
                '_id' => new MongoId('5f159e696222aaa4d0ee4ce2'),
                'branch_id' => '5ce808750d35f24839739959',
                'namespace' => self::NAMESPACE,
                'active' => true,
                'created' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'description' => 'Timezone test Aussie branch',
                'modified' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'name' => 'Gold Membership',
                'buy_just_once' => true,
                'plans' => [
                    [
                        'type' => 'time_classes',
                        'code' => '1506335805979',
                        'name' => 'credits',
                        'price' => 10,
                        'duration_time_unit' => 'month',
                        'duration_time_unit_count' => 1,
                        'upfront_fee' => 0,
                        'credits' => [
                            'branch_id' => '5ce808750d35f24839739959',
                            'namespace' => self::NAMESPACE,
                            'active' => true,
                            'model' => 'programs',
                            'category_id' => null,
                            'num_sessions' => 10,
                            'model_ids' => null,
                            'expiry' => null,
                        ],
                    ]
                ],
                'trial' => false,
            ]
        ];
    }

    private function forRestrictedRecurringClasses(): array
    {
        return [
            [
                '_id' => new MongoId('5f159e696222aaa4d0ee4ce3'),
                'branch_id' => self::BRANCH_ID,
                'namespace' => self::NAMESPACE,
                'active' => true,
                'created' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'description' => 'Multiple Cycle Membership',
                'modified' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'name' => 'Multiple Cycle Membership',
                'buy_just_once' => true,
                'plans' => [
                    [
                        'type' => 'time_classes',
                        'code' => '54107aced7b6dd7aab8b4567',
                        'name' => 'Monthly Billing',
                        'price' => 30,
                        'membership_group_id' => '54107aced7b6dd7aab8b4567',
                        'num_classes' => 1,
                        'duration_time_unit' => 'week',
                        'duration_time_unit_count' => 1,
                    ],
                    [
                        'type' => 'time_classes',
                        'code' => 'abc123def456',
                        'name' => 'Monthly Billing',
                        'price' => 30,
                        'membership_group_id' => '54107aced7b6dd7aab8b4567',
                        'num_classes' => 2,
                        'duration_time_unit' => 'week',
                        'duration_time_unit_count' => 1,
                        'credits' => [
                            [
                                'num_sessions' => 2,
                            ]
                        ]
                    ],
                    [
                        'type' => 'time_classes',
                        'code' => '2cycle4bookings',
                        'name' => 'Monthly Billing',
                        'price' => 30,
                        'membership_group_id' => '54107aced7b6dd7aab8b4567',
                        'num_classes' => 2,
                        'duration_time_unit' => 'week',
                        'duration_time_unit_count' => 1,
                        'credits' => [
                            [
                                'num_sessions' => 2,
                                "expiry" => [
                                    'interval' => 'week',
                                    'interval_count' => 1,
                                ],
                            ]
                        ]
                    ],
                ],
                'trial' => false,
            ]
        ];
    }

    private function forRestrictedMembershipNoCreditsNextCycle(): array
    {
        return [
            [
                '_id' => new MongoId('5f159e696222aaa4d0ee4ce5'),
                'branch_id' => self::BRANCH_ID,
                'namespace' => self::NAMESPACE,
                'active' => true,
                'created' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'description' => 'Single Cycle Membership',
                'modified' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'name' => 'Single Cycle Membership',
                'buy_just_once' => true,
                'plans' => [
                    [
                        'type' => 'time_classes',
                        'code' => '2cycle4bookings',
                        'name' => 'Monthly Billing',
                        'price' => 30,
                        'membership_group_id' => '54107aced7b6dd7aab8b4899',
                        'num_classes' => 2,
                        'duration_time_unit' => 'month',
                        'duration_time_unit_count' => 1,
                        'credits' => [
                            [
                                'num_sessions' => 2,
                                "expiry" => [
                                    'interval' => 'month',
                                    'interval_count' => 1,
                                ],
                            ]
                        ]
                    ],
                ],
                'trial' => false,
            ]
        ];
    }

    private function forGrantCreditsOnImport(): array
    {
        return [
            [
                '_id' => new MongoId('5f159e696222aaa4d0ee4df5'),
                'branch_id' => self::BRANCH_ID,
                'namespace' => self::NAMESPACE,
                'active' => true,
                'created' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'description' => 'Single Cycle Membership',
                'modified' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'name' => 'Single Cycle Membership',
                'buy_just_once' => true,
                'plans' => [
                    [
                        'type' => 'time_classes',
                        'code' => '2cycle4bookings',
                        'name' => 'Monthly Billing',
                        'price' => 30,
                        'membership_group_id' => '54107aced7b6dd7aab8b4999',
                        'duration_time_unit' => 'month',
                        'duration_time_unit_count' => 1,
                        '_subscription_plan_id' => true,
                        'subscription_plan_id' => '5be4551117844100fd1f0e99',
                        'credits' => [
                            [
                                'num_sessions' => 15,
                                'model' => 'programs',
                                'expiry' => [
                                    'interval' => 'month',
                                    'interval_count' => 1,
                                ],
                            ]
                        ]
                    ],
                ],
                'trial' => false,
            ],
            [
                '_id' => new MongoId('5f159e696222aaa4d0ee4df6'),
                'branch_id' => self::BRANCH_ID,
                'namespace' => self::NAMESPACE,
                'active' => true,
                'created' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'description' => 'Single Restricted Prepaid Membership',
                'modified' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'name' => 'Single Restricted Prepaid Membership',
                'buy_just_once' => true,
                'plans' => [
                    [
                        'type' => 'time_classes',
                        'code' => 150_633_580_599,
                        'name' => 'Prepaid',
                        'price' => 30,
                        'membership_group_id' => '54107aced7b6dd7aab8b5999',
                        'num_classes' => 8,
                        'duration_time_unit' => 'month',
                        'duration_time_unit_count' => 1,
                        'credits' => [
                            [
                                'num_sessions' => 8,
                                'model' => 'programs',
                                'expiry' => [
                                    'interval' => 'month',
                                    'interval_count' => 1,
                                ],
                            ]
                        ]
                    ],
                ],
                'trial' => false,
            ]
        ];
    }

    private function forAssignCreditsOnMembershipPurchasedEvent(): array
    {
        return [
            [
                '_id' => new MongoId('8f259e696222aaa4d0ee4df5'),
                'branch_id' => self::BRANCH_ID,
                'namespace' => self::NAMESPACE,
                'active' => true,
                'created' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'description' => 'Single Cycle Membership',
                'modified' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'name' => 'Single Cycle Membership',
                'plans' => [
                    [
                        'type' => 'time_classes',
                        'code' => 250_633_580_599,
                        'name' => 'Plan code as a number',
                        'price' => 30,
                        'membership_group_id' => '54107aced7b6dd7aab8b4999',
                        'duration_time_unit' => 'month',
                        'duration_time_unit_count' => 1,
                        '_subscription_plan_id' => true,
                        'subscription_plan_id' => '5be4551117844100fd1f0e99',
                        'credits' => [
                            [
                                'num_sessions' => 5,
                                'model' => 'programs',
                                'expiry' => [
                                    'interval' => 'month',
                                    'interval_count' => 1,
                                ],
                            ]
                        ]
                    ],
                    [
                        'type' => 'time_classes',
                        'code' => '04949348474',
                        'name' => 'Plan code with leading zero',
                        'price' => 30,
                        'membership_group_id' => '54107aced7b6dd7aab8b4999',
                        'duration_time_unit' => 'month',
                        'duration_time_unit_count' => 1,
                        '_subscription_plan_id' => true,
                        'subscription_plan_id' => '5be4551117844100fd1f0e99',
                        'credits' => [
                            [
                                'num_sessions' => 5,
                                'model' => 'programs',
                                'expiry' => [
                                    'interval' => 'month',
                                    'interval_count' => 1,
                                ],
                            ]
                        ]
                    ],
                    [
                        'type' => 'time_classes',
                        'code' => '14949348474',
                        'name' => 'Plan code as a string',
                        'price' => 30,
                        'membership_group_id' => '54107aced7b6dd7aab8b4999',
                        'duration_time_unit' => 'month',
                        'duration_time_unit_count' => 1,
                        '_subscription_plan_id' => true,
                        'subscription_plan_id' => '5be4551117844100fd1f0e99',
                        'credits' => [
                            [
                                'num_sessions' => 5,
                                'model' => 'programs',
                                'expiry' => [
                                    'interval' => 'month',
                                    'interval_count' => 1,
                                ],
                            ]
                        ]
                    ],
                    [
                        'type' => 'time_classes',
                        'code' => '5a1430309b264e6a99186003',
                        'name' => 'Plan code as an alphanumeric string',
                        'price' => 30,
                        'membership_group_id' => '54107aced7b6dd7aab8b4999',
                        'duration_time_unit' => 'month',
                        'duration_time_unit_count' => 1,
                        '_subscription_plan_id' => true,
                        'subscription_plan_id' => '5be4551117844100fd1f0e99',
                        'credits' => [
                            [
                                'num_sessions' => 5,
                                'model' => 'programs',
                                'expiry' => [
                                    'interval' => 'month',
                                    'interval_count' => 1,
                                ],
                            ]
                        ]
                    ],
                ],
                'trial' => false,
            ],
        ];
    }
}
