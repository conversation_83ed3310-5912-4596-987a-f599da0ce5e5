<?php

declare(strict_types=1);

namespace Glofox;

use \GlofoxEnvironment;

class CdnProvider
{
    private const DEFAULT = 'https://cdn.glofox.com';

    private static array $cdns = [
        GlofoxEnvironment::BARTIK => 'https://cdn-bar.glofox.com',
        GlofoxEnvironment::HOPPER => 'https://cdn-hop.glofox.com',
        GlofoxEnvironment::HAMILTON => 'https://cdn-ham.glofox.com',
        GlofoxEnvironment::LOVELACE => 'https://cdn-lov.glofox.com',
        GlofoxEnvironment::LAMARR => 'https://cdn-lam.gfdevlab.com',
        GlofoxEnvironment::CLARKE => 'https://cdn-cla.gfdevlab.com',
        GlofoxEnvironment::TURING => 'https://cdn-tur.gfdevlab.com',
        GlofoxEnvironment::FRANKLIN => 'https://cdn-fra.gfdevlab.com',
        GlofoxEnvironment::PLATFORM => 'https://cdn.glofox.com',
    ];

    public static function hasUrl(?string $environment = null): bool
    {
        if($environment === null){
            $environment = GlofoxEnvironment::currentEnvironment();
        }
        return array_key_exists($environment, self::$cdns);
    }

    public static function getUrl(): string
    {
        $environment = GlofoxEnvironment::currentEnvironment();

        return self::$cdns[$environment] ?? self::DEFAULT;
    }
}
