<?php

App::import('Test/Case', 'GlofoxTestCase');

    use Glofox\Response\Models\Branches\Features\NewsModel;

    /**
 * Test cases
 * 1 Model contains all fields with data
 * 2 Missing fields and returns correct structure
 * 3 Additional fields and return correct structure
 * 4 Empty array fields and return correct structure.
 *
 * Class NewsModelTest
 */
class NewsModelTest extends GlofoxTestCase
{
    public $fixtures = [];

    /**
     * @dataProvider modelProvider
     */
    public function testModel($dataSent, $result): void
    {
        $model = new NewsModel($dataSent);
        $this->assertEquals($model, $result);
    }

    public function modelProvider(): array
    {
        return [
            '1 Model contains all fields with data' => [
                [
                    'order' => 1,
                    'enabled' => false,
                ],
                new NewsModel(
                    [
                        'order' => 1,
                        'enabled' => false,
                    ]
                ),
            ],
            '2 Missing fields and returns correct structure' => [
                [
                    // order missing
                    'enabled' => false,
                ],
                new NewsModel(
                    [
                        'order' => 0,
                        'enabled' => false,
                    ]
                ),
            ],
            '3 Additional fields and return correct structure' => [
                [
                    'extra' => 'test', // additional field
                    'order' => 1,
                    'enabled' => false,
                ],
                new NewsModel(
                    [
                        'order' => 1,
                        'enabled' => false,
                    ]
                ),
            ],
            '4 Empty array fields and return correct structure' => [
                [],
                new NewsModel(
                    [
                        'order' => 0,
                        'enabled' => false,
                    ]
                ),
            ],
        ];
    }
}
