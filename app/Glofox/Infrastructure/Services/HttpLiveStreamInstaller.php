<?php

declare(strict_types=1);

namespace Glofox\Infrastructure\Services;

use Glofox\Domain\Branches\Models\Branch;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\BadResponseException;
use Psr\Log\LoggerInterface;

class HttpLiveStreamInstaller implements LiveStreamInstallerInterface
{
    public const INSTALL_URI = 'v1/install';

    private \GuzzleHttp\Client $httpClient;

    private \Psr\Log\LoggerInterface $logger;

    public function __construct(Client $httpClient, LoggerInterface $logger)
    {
        $this->httpClient = $httpClient;
        $this->logger = $logger;
    }

    public function install(Branch $branch): void
    {
        $this->logger->info(sprintf('Installing Glofox Digital feature for branch id %s', $branch->id()));

        try {
            $this->httpClient->post(
                static::INSTALL_URI,
                [
                    'verify' => false,
                    'headers' => [
                        'Content-Type' => 'application/json',
                    ],
                    'body' => json_encode([
                        'studio' => [
                            'id' => (string) $branch->id(),
                            'namespace' => $branch->namespace(),
                            'name' => $branch->name(),
                        ],
                    ]),
                ]
            );
        } catch (BadResponseException $e) {
            $this->logger->error('Could not install Glofox Digital feature', [
                'status' => $e->getCode(),
                'response' => $e->getMessage(),
            ]);
            throw new \UnsuccessfulOperation('Could not install Glofox Digital feature');
        }
    }

    public function uninstall(Branch $branch): void
    {
        // TODO: Implement uninstall() method.
    }
}
