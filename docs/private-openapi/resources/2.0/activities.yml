get:
  tags:
    - Activities
  summary: ''
  operationId: get-activities
  description: This endpoint returns a filtered activities list from a given branch and user grouped by invoice
  x-internal: true
  security:
    - coreapi_auth: [ ]
  parameters:
    - name: user_id
      in: query
      schema:
        type: string
        pattern: '^[0-9a-fA-F]{24}$'
      description: > 
        The user id to filter the activities. If not provided, the user id will be taken from the JWT.
        Only admins and receptionists can access to other users activities.
    - name: exclude_free_bookings
      in: query
      schema:
        type: boolean
        default: false
      description: Exclude free bookings activities
    - name: event_identifiers
      in: query
      schema:
        type: string
        enum: [
          '',
          'CARD_ADDED',
          'CARD_REMOVED',
          'CARD_REPLACED',
          'MANDATE_ADDED',
          'MANDATE_REMOVED',
          'MANDATE_REPLACED',
          'MANDATE_STATUS_CHANGED',
          'ENTITY_BOOKED',
          'PRODUCT_PURCHASED',
          'NON_SUB_MEMBERSHIP_PURCHASED',
          'MEMBERSHIP_PRORATED',
          'MEMBERSHIP_REMOVED',
          'CUSTOM_CHARGE_EXECUTED',
          'SUBSCRIPTION_PURCHASED',
          'SUBSCRIPTION_CYCLE_PAID',
          'SUBSCRIPTION_CYCLE_PAYMENT_FAILED',
          'SUBSCRIPTION_PAUSED',
          'SUBSCRIPTION_CANCELLED',
          'HISTORIC_TRANSACTION_IMPORTED',
          'NSF_FEE',
          'EXTERNAL_ACCOUNT_UPDATED'
        ]
        default: ''
      description: Filter activities by event identifier
  responses:
    '200':
      description: OK
      content:
        application/json:
          schema:
            properties:
              object:
                type: string
                default: 'list'
              data:
                type: array
                items:
                  $ref: "../../schemas/activity_grouped_by_invoice.yml"
