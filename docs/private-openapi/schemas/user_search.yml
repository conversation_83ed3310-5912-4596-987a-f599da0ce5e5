type: object
properties:
  _id:
    type: string
  branch_id:
    type: string
  image:
    type: string
  first_name:
    type: string
  last_name:
    type: string
  phone:
    type: string
  email:
    type: string
  source:
    $ref: './user_source.yml'
  created:
    type: integer
    description: UTC timestamp of the user creation date
  lead_status:
    type: string
    enum:
      - MEMBER
  origin_branch_id:
    type: string
  origin_branch_name:
    type: string
  access_barcode:
    type: string
  membership:
    type: array
    items:
      $ref: './user_search_membership.yml'
  aggregated_credits:
    type: integer
  profile_rules:
    type: object
    properties:
        restricted_profile:
            type: boolean
            description: |
              True - if user has restricted access to the details page of the profile.
              False - if user has full access to the details page of the profile.
required:
  - _id
  - branch_id
  - first_name
  - last_name
  - phone
  - email
  - source
  - created
  - lead_status
  - origin_branch_id
  - origin_branch_name
  - access_barcode
