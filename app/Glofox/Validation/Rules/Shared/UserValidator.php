<?php

declare(strict_types=1);

namespace Glofox\Validation\Rules\Shared;

use Glofox\Domain\Events\Search\Expressions\Id;
use Glofox\Domain\Users\Repositories\UsersRepository;
use Illuminate\Validation\Validator;

final class UserValidator
{
    public function validateExists(string $attribute, string $value, array $parameters, Validator $validator): bool
    {
        if (empty($value)) {
            return true;
        }

        $validator->setCustomMessages([
            'user_exists' => 'There is no user associated with this id',
        ]);

        $count = app()->make(UsersRepository::class)
            ->addCriteria(new Id($value))
            ->count();

        return $count > 0;
    }
}
