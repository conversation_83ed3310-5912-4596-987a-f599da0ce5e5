<?php

namespace CakeTestCases\Glofox\Domain\ElectronicAgreements\Services;

use Glofox\Domain\ElectronicAgreements\Models\Document;
use Glofox\Domain\ElectronicAgreements\Services\AgreementRedirectUrlGenerator;
use Glofox\Domain\ElectronicAgreements\Services\ElectronicAgreementsServiceInterface;
use Glofox\Domain\ElectronicAgreements\Trigger;
use Glofox\Domain\Users\Models\User;

\App::import('Test/Case', 'GlofoxTestCase');

class AgreementRedirectUrlGeneratorTest extends \GlofoxTestCase
{
    public $fixtures = [];

    public function testItGeneratesRedirectUrl(): void
    {
        $eagreements = \Mockery::mock(ElectronicAgreementsServiceInterface::class);

        $eagreements
            ->shouldReceive('isElectronicAgreementsEnabled')
            ->withArgs(fn (string $studioId) => $studioId === 'studio-123')
            ->andReturn(true);

        $eagreements->shouldReceive('findDocumentByTrigger')
            ->withArgs(fn (string $studioId, Trigger $trigger) => $studioId === 'studio-123'
                && $trigger === Trigger::MEMBER_AUTHENTICATED())
            ->andReturn(new Document([
                'id' => 'document-id',
                'trigger' => Trigger::MEMBER_AUTHENTICATED(),
            ]))
            ->once();

        app()->instance(ElectronicAgreementsServiceInterface::class, $eagreements);

        /** @var AgreementRedirectUrlGenerator $generator */
        $generator = app()->make(AgreementRedirectUrlGenerator::class);

        $user = User::make([
            '_id' => 'user-123',
            'branch_id' => 'studio-123',
            'origin_branch_id' => 'studio-123',
        ]);

        $url = $generator->generate($user, Trigger::MEMBER_AUTHENTICATED());
        self::assertStringStartsWith('http', $url);
        self::assertContains('/2.2/electronic-agreements/redirect', $url);
        self::assertContains('branchId=studio-123', $url);
        self::assertContains('userId=user-123', $url);
        self::assertContains('documentId=document-id', $url);
        self::assertNotContains('agreementMetadata=', $url);

        app()->forgetInstance(ElectronicAgreementsServiceInterface::class);
    }

    public function test_it_generates_redirect_url_with_metadata(): void
    {
        $eagreements = \Mockery::mock(ElectronicAgreementsServiceInterface::class);

        $eagreements
            ->shouldReceive('isElectronicAgreementsEnabled')
            ->withArgs(fn (string $studioId) => $studioId === 'studio-123')
            ->andReturn(true);

        $eagreements->shouldReceive('findDocumentByTrigger')
            ->withArgs(fn (string $studioId, Trigger $trigger) => $studioId === 'studio-123'
                && $trigger === Trigger::MEMBERSHIP_PURCHASED())
            ->andReturn(new Document([
                'id' => 'document-id',
                'trigger' => Trigger::MEMBERSHIP_PURCHASED(),
            ]))
            ->once();

        app()->instance(ElectronicAgreementsServiceInterface::class, $eagreements);

        /** @var AgreementRedirectUrlGenerator $generator */
        $generator = app()->make(AgreementRedirectUrlGenerator::class);

        $user = User::make([
            '_id' => 'user-123',
            'branch_id' => 'studio-123',
            'origin_branch_id' => 'studio-123',
        ]);

        $url = $generator->generate($user, Trigger::MEMBERSHIP_PURCHASED(), "user-membership-123");
        self::assertStringStartsWith('http', $url);
        self::assertContains('/2.2/electronic-agreements/redirect', $url);
        self::assertContains('branchId=studio-123', $url);
        self::assertContains('userId=user-123', $url);
        self::assertContains('documentId=document-id', $url);
        self::assertContains('agreementMetadata=user-membership-123', $url);

        app()->forgetInstance(ElectronicAgreementsServiceInterface::class);
    }

    public function testItReturnsNullWithoutDocuments(): void
    {
        $eagreements = \Mockery::mock(ElectronicAgreementsServiceInterface::class);

        $eagreements
            ->shouldReceive('isElectronicAgreementsEnabled')
            ->withArgs(fn (string $studioId) => $studioId === 'studio-123')
            ->andReturn(true);

        $eagreements->shouldReceive('findDocumentByTrigger')
            ->withArgs(fn (string $studioId, Trigger $trigger) => $studioId === 'studio-123'
                && $trigger === Trigger::MEMBER_AUTHENTICATED())
            ->andReturn(null)
            ->once();

        app()->instance(ElectronicAgreementsServiceInterface::class, $eagreements);

        /** @var AgreementRedirectUrlGenerator $generator */
        $generator = app()->make(AgreementRedirectUrlGenerator::class);

        $user = User::make([
            '_id' => 'user-123',
            'branch_id' => 'studio-123',
            'origin_branch_id' => 'studio-123',
        ]);

        $url = $generator->generate($user, Trigger::MEMBER_AUTHENTICATED());
        self::assertNull($url);

        app()->forgetInstance(ElectronicAgreementsServiceInterface::class);
    }
}
