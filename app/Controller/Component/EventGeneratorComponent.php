<?php

use Glofox\Domain\Events\Repositories\EventsRepository;
use Psr\Log\LoggerInterface;

App::uses('Component', 'Controller');

/**
 * Class EventGeneratorComponent.
 */
class EventGeneratorComponent extends Component
{
    public $controller = null;

    /** @var Branch */
    protected $Branch;

    /** @var Program */
    protected $Program;

    /** @var Event */
    protected $Event;

    protected EventsRepository $eventsRepository;

    protected LoggerInterface $logger;

    public function initialize(Controller $controller): void
    {
        $this->loadModels();
    }

    public function loadModels(): void
    {
        $this->Branch = ClassRegistry::init('Branch');
        $this->Program = ClassRegistry::init('Program');
        $this->Event = ClassRegistry::init('Event');
        $this->eventsRepository = app()->make(EventsRepository::class);
        $this->logger = app()->make(LoggerInterface::class);
    }

    /*!
     * Iterate through all branch programs and generate future events based on its configuration
     * @param      [type]                   $branch_id [description]
     * @return     [type]                              [description]
     */

    /**
     * @param $branchId
     */
    public function generateByBranch($branchId): void
    {
        $branch = is_array($branchId) ? $branchId : $this->Branch->findById($branchId);
        $branchId = $branch['Branch']['_id'];
        $closingTimes = $branch['Branch']['closing_times'] ?? null;
        $weeksToDisplay = $this->Branch->findWeeksToDisplay($branch, 'events');
        $displayEndDate = $this->Event->addNumberOfWeeks(
            $this->Event->getCurrentDate('Y-m-d'),
            $weeksToDisplay,
            'Y-m-d'
        );
        $programs = $this->Program->findAllActiveByBranchId($branchId);
        foreach ($programs as $program) {
            try {
                $this->generateByProgram($program, $branch, $displayEndDate, $closingTimes);
            } catch (Throwable $e) {
                $this->logger->error(
                    sprintf('[EventGenerator] Error: %s', $e->getMessage()),
                    [
                        'programId' => $program['Program']['_id'],
                        'branchId' => $branchId,
                        'weeksToDisplay' => $weeksToDisplay,
                        'displayEndDate' => $displayEndDate,
                        'closingTimes' => $closingTimes,
                    ]
                );

                continue;
            }
        }
    }

    /*!
     * Generate Events automated
     * @param      [type]                   $program_id       Program or identifier
     * @param      [type]                   $display_end_date Final date to display
     * @param      [type]                   $closing_times    If holidays in branch are set (branch.closing_times) don't generate for this
     * @return     [type]                                     [description]
     */

    /**
     * @param $programId
     * @param $branch
     * @param null $displayEndDate
     * @param null $closingTimes
     */
    public function generateByProgram($programId, $branch, $displayEndDate = null, $closingTimes = null): void
    {
        $program = is_array($programId) ? $programId : $this->Program->findById($programId);
        if (empty($program['Program'])) {
            return;
        }

        $programId = (string) $program['Program']['_id'];
        $programEndDate = $program['Program']['date_finish'] ?? null;
        if ($programEndDate) {
            $isDisplayEndDateGreaterThanProgramEndDate = strtotime($displayEndDate) > strtotime($programEndDate);
            $endDate = $isDisplayEndDateGreaterThanProgramEndDate ? $programEndDate : $displayEndDate;
        } else {
            $endDate = $displayEndDate;
        }

        foreach ($program['Program']['schedule'] as $schedule) {
            $temporalProgram = $program;
            $temporalProgram['Program']['schedule'] = [$schedule];
            $scheduleCode = (string) ($schedule['code'] ?? '');

            $lastEvent = $this->eventsRepository->findLastEventByProgramId((string) $programId, $scheduleCode);

            $lastEventDate = isset($lastEvent['Event']['date'])
                ? $this->Event->getNextDay($lastEvent['Event']['date'])
                : $this->Event->getCurrentDate('Y-m-d');

            $programStartDate = $program['Program']['date_start'];
            $isLatestEventGreaterThanProgramStartDate = strtotime($lastEventDate) > strtotime($programStartDate);
            $startDate = $isLatestEventGreaterThanProgramStartDate
                ? $lastEventDate
                : $programStartDate;

            $isEndDateGreaterThanLastEventDate = strtotime($endDate) > strtotime($lastEventDate);
            if (!$isEndDateGreaterThanLastEventDate) {
                continue;
            }

            $this->Event->generateFrom(
                $temporalProgram,
                $branch,
                $startDate,
                $endDate,
                $closingTimes
            );
        }
    }
}
