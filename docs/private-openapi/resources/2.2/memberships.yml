parameters:
  - schema:
      type: string
      pattern: '^[a-f\d]{24}$'
    name: branchId
    in: path
    required: true
    description: The branch ID the memberships list is fetched for
  - schema:
      type: string
      pattern: '^[a-f\d]{24}$'
    name: userId
    in: path
    required: true
    description: The user ID the memberships list is fetched for
get:
  tags:
    - Memberships
  summary: Get memberships
  operationId: get-memberships
  description:
    Use this call to retrieve a list of memberships for an specified user. If the roaming global search is on, it will
    return all the memberships for the requested user even though this user belongs to a different location under the
    same namespace.
  security:
    - coreapi_auth: [ ]
  parameters:
    - schema:
        type: integer
        default: 0
        enum:
          - 0
          - 1
      name: namespace-search
      in: query
      required: false
      description: |
        If 1, then search is made within the namespace of the client. All users from all roaming branches are searched.
        If 0, then search is made within the branch only.
  responses:
    '200':
      description: OK
      content:
        application/json:
          schema:
            $ref: "../../schemas/membership.yml"
    '400':
      description: Bad request
      content:
        application/json:
          schema:
            $ref: "../../schemas/error.yml"
