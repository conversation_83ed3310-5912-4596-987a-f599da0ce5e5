<?php

namespace CakeTestCases\Glofox\Domain\Transactional\Templates;

use App;
use Glofox\Domain\Transactional\Templates\CustomTemplate;
use GlofoxTestCase;
use MongoId;

 App::import('Test/Case', 'GlofoxTestCase');

class CustomTemplateTest extends GlofoxTestCase
{
    public $fixtures = [];

    public function test_it_should_add_custom_fields_and_remove_unnecessary_ones(): void
    {
        $defaultTransactionalMessage = [
            '_id' => 'whatever',
            'identifier' => 'template-identifier',
            'name' => 'whatever',
            'description' => 'whatever',
            'template' => 'whatever',
            'enabled' => false,
            'type' => 'whatever',
            'subject' => 'template-subject',
            'content' => 'template-content',
            'triggers' => [],
            'variables' => 'whatever',
            'configurable' => 'whatever',
        ];
        $branchId = (string) new MongoId('5e908adf8f5c6a3808971e14');

        $result = (CustomTemplate::createFromDefaultWithBranchId($defaultTransactionalMessage, $branchId))->toArray();

        self::assertArrayHasKey('branch_id', $result);
        self::assertArrayHasKey('identifier', $result);
        self::assertArrayHasKey('enabled', $result);
        self::assertArrayHasKey('subject', $result);
        self::assertArrayHasKey('content', $result);
        self::assertArrayHasKey('triggers', $result);
        self::assertArrayHasKey('copy', $result);
        self::assertArrayHasKey('type', $result['copy']);
        self::assertArrayHasKey('emails', $result['copy']);

        self::assertSame('5e908adf8f5c6a3808971e14', $result['branch_id']);
        self::assertSame('template-identifier', $result['identifier']);
        self::assertFalse($result['enabled']);
        self::assertSame('template-subject', $result['subject']);
        self::assertSame('template-content', $result['content']);
        self::assertSame([], $result['triggers']);
        self::assertSame(['type' => 'cc', 'emails' => []], $result['copy']);

        self::assertArrayNotHasKey('_id', $result);
        self::assertArrayNotHasKey('name', $result);
        self::assertArrayNotHasKey('description', $result);
        self::assertArrayNotHasKey('template', $result);
        self::assertArrayNotHasKey('type', $result);
        self::assertArrayNotHasKey('variables', $result);
        self::assertArrayNotHasKey('configurable', $result);
    }
}
