<?php

declare(strict_types=1);

namespace Glofox\Infrastructure\GoogleRecaptcha;

class VerifyRequest
{
    private string $secret;

    private string $recaptchaToken;

    public function __construct(
        string $secret,
        string $recaptchaToken
    ) {
      $this->secret = $secret;
      $this->recaptchaToken = $recaptchaToken;
    }

    /**
     * @see https://developers.google.com/recaptcha/docs/verify
     */
    public function getPayload(): array
    {
        return [
            'secret' => $this->secret,
            'response' => $this->recaptchaToken
        ];
    }
}