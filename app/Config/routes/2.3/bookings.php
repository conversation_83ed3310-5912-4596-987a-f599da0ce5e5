<?php

use Glofox\Http\Router;
use Symfony\Component\HttpFoundation\Request;

Router::connect(
    '/:apiVersion/branches/:branchId/bookings',
    [
        'controller' => 'Bookings',
        'action' => 'bookAgnostic',
        '[method]' => Request::METHOD_POST,
    ],
    [
        'apiVersion' => '2.3',
    ]
)->withGateForParameter('branchId', 'gates.branch.view');

Router::connect(
    '/:apiVersion/branches/:branchId/bookings/:bookingId',
    [
        'controller' => 'Bookings',
        'action' => 'cancelAgnostic',
        '[method]' => Request::METHOD_DELETE,
    ],
    [
        'apiVersion' => '2.3',
    ]
)->withGateForParameter('branchId', 'gates.branch.view');

Router::connect(
    '/:apiVersion/locations/:locationID/timeslots/:timeslotID/calculate-price',
    [
        'controller' => 'BookingsV2',
        'action' => 'calculatePrice',
        '[method]' => Request::METHOD_POST,
    ],
    [
        'apiVersion' => '2.3',
    ]
)->withGateForParameter('locationID', 'gates.branch.view');
