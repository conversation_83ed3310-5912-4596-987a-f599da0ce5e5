<?php

namespace CakeTestCases\Glofox\Domain\TerminalReaders\Http;

use Glofox\Domain\TerminalReaders\Models\TerminalReader;
use Glofox\Domain\TerminalReaders\Repositories\TerminalReadersRepository;
use Glofox\Domain\TerminalReaders\UseCase\CreateTerminalReader;
use Glofox\Domain\TerminalReaders\UseCase\CreateTerminalReaderParams;
use Glofox\Domain\TerminalReaders\UseCase\DeleteTerminalReader;
use Glofox\Domain\TerminalReaders\UseCase\DeleteTerminalReaderParams;
use Glofox\Exception;
use Symfony\Component\HttpFoundation\Request;

\App::import('Test/Case', 'GlofoxControllerTestCase');

class TerminalReadersControllerTest extends \GlofoxControllerTestCase
{
    public const branchId = '5c783d4cd510f9635ad4a6b6';
    public const terminalReaderId = '5c783d4cd510f9635ad4a6b0';
    public const registrationCode = 'registration-code';

    public function tearDown()
    {
        parent::tearDown();
        \Mockery::close();
    }

    public function testItShouldFailWhenAnInvalidRequestIsProvided(): void
    {
        $admin = $this->fetchUser('58568b8fa875ab19530041b8');
        $this->loginAsUser($admin);
        $response = $this->testAction(sprintf('/2.1/branches/%s/terminal-readers', self::branchId), [
            'method' => Request::METHOD_POST,
            'data' => [
                'registration_code' => ''
            ]
        ]);
        $response = json_decode($response, null, 512, JSON_THROW_ON_ERROR);

        self::assertEquals(400, $this->response->statusCode());
        self::assertEquals('The registration code field is required.', $response->message);
    }

    public function testItShouldFailWhenUnableToCreateTerminalReader(): void
    {
        $useCaseMock = \Mockery::mock(CreateTerminalReader::class)
            ->shouldReceive('execute')
            ->andThrow(Exception::class, 'something went wrong', 400)
            ->once()
            ->getMock();

        app()->instance(CreateTerminalReader::class, $useCaseMock);

        $admin = $this->fetchUser('58568b8fa875ab19530041b8');
        $this->loginAsUser($admin);
        $response = $this->testAction(sprintf('/2.1/branches/%s/terminal-readers', self::branchId), [
            'method' => Request::METHOD_POST,
            'data' => [
                'registration_code' => self::registrationCode
            ]
        ]);
        $response = json_decode($response, null, 512, JSON_THROW_ON_ERROR);

        self::assertEquals(400, $this->response->statusCode());
        self::assertEquals('something went wrong', $response->message);

        app()->forgetInstance(CreateTerminalReader::class);
    }

    public function testItShouldCreateATerminalWhenParametersAreCorrect(): void
    {
        $expectedTerminal = TerminalReader::make([
            '_id' => self::terminalReaderId,
            'payment_method_id' => 'payment-method-id',
            'device_type' => 'device-type',
            'label' => 'label',
            'serial_number' => 'serial-number',
            'terminal_provider_id' => 'terminal-provider-id',
        ]);

        $useCaseMock = \Mockery::mock(CreateTerminalReader::class)
            ->shouldReceive('execute')
            ->withArgs(function (CreateTerminalReaderParams $params) {
                $this->assertEquals(self::branchId, $params->getBranchId());
                $this->assertEquals(self::registrationCode, $params->getRegistrationCode());
                return true;
            })
            ->andReturn($expectedTerminal)
            ->once()
            ->getMock();

        app()->instance(CreateTerminalReader::class, $useCaseMock);

        $admin = $this->fetchUser('58568b8fa875ab19530041b8');
        $this->loginAsUser($admin);
        $response = $this->testAction(sprintf('/2.1/branches/%s/terminal-readers', self::branchId), [
            'method' => Request::METHOD_POST,
            'data' => [
                'registration_code' => self::registrationCode
            ]
        ]);
        $response = json_decode($response, null, 512, JSON_THROW_ON_ERROR);

        self::assertEquals(200, $this->response->statusCode());
        self::assertTrue($response->success);
        self::assertEquals($expectedTerminal->id(), $response->data->_id);
        self::assertEquals($expectedTerminal->branchId(), $response->data->branch_id);
        self::assertEquals($expectedTerminal->serialNumber(), $response->data->serial_number);
        self::assertEquals($expectedTerminal->label(), $response->data->label);
        self::assertEquals($expectedTerminal->deviceType(), $response->data->device_type);
        self::assertEquals($expectedTerminal->terminalProviderId(), $response->data->terminal_provider_id);
        self::assertEquals($expectedTerminal->created()->timestamp, $response->data->created);
        self::assertEquals($expectedTerminal->modified()->timestamp, $response->data->modified);

        app()->forgetInstance(CreateTerminalReader::class);
    }

    public function testItShouldDeleteATerminalWhenParametersAreCorrect(): void
    {
        $useCaseMock = \Mockery::mock(DeleteTerminalReader::class)
            ->shouldReceive('execute')
            ->withArgs(function (DeleteTerminalReaderParams $params) {
                $this->assertEquals(self::branchId, $params->getBranchId());
                $this->assertEquals(self::terminalReaderId, $params->getTerminalReaderId());
                return true;
            })
            ->andReturn()
            ->once()
            ->getMock();

        app()->instance(DeleteTerminalReader::class, $useCaseMock);

        $admin = $this->fetchUser('58568b8fa875ab19530041b8');
        $this->loginAsUser($admin);
        $response = $this->testAction(sprintf('/2.1/branches/%s/terminal-readers/%s', self::branchId, self::terminalReaderId), [
            'method' => Request::METHOD_DELETE,
        ]);

        $response = json_decode($response, null, 512, JSON_THROW_ON_ERROR);

        self::assertEquals(200, $this->response->statusCode());
        self::assertTrue($response->success);

        app()->forgetInstance(DeleteTerminalReader::class);
    }
}