## Interaction Types [/2.1/branches/{branchId}/interactions/types?filters[manual]={manual}]

+ Parameters
    + branchId: 507f191e810c19729de860ea (string) - The identification of the branch
    + manual (enum[number], optional) - When not provided, lists everything
        + Members
            + 0 - Will show only automatic types
            + 1 - Will show only manual types

+ Attributes
    + name: Note (string)
    + type: NOTE (enum[string])
        + Members
            + NOTE - Note for Customer
            + AUTO_EMAIL - Automatic Transactional E-mail sent to Customer
            + MANUAL_EMAIL - Marketing / Contact E-mail sent to Customer
            + CALLED_AND_CONNECTED - Call to Customer
            + CALLED_AND_NO_ANSWER - Call without Answer to Customer
            + AUTO_PUSH_NOTIFICATION - Automatic Transactional Push Notification to Customer

### Retrieve Interaction Types of a Branch [GET]

+ Request
    + Headers
          Authorization: Bearer {authToken}

+ Response 200 (application/json)
    + Attributes
        + success: true (boolean)
        + data (array, fixed)
            + (Interaction Types)
        + meta (object)
            + pagination (Pagination)

+ Response 400
    + Attributes (Generic Error)