<?php

declare(strict_types=1);

namespace Glofox\Domain\Store\Sales\Repositories;

use ClassRegistry;
use Exception;
use Glofox\Datasource\Exceptions\InvalidMongoIdException;
use Glofox\Domain\Store\Sales\Exceptions\StoreSaleNotFountException;
use Glofox\Domain\Store\Sales\Expressions\InvoiceId;
use Glofox\Domain\Store\Sales\Expressions\PresentationId;
use Glofox\Domain\Store\Sales\Expressions\ProductId;
use Glofox\Domain\Store\Sales\Expressions\PurchaseCode;
use Glofox\Domain\Store\Sales\Models\StoreSales;
use Glofox\Repositories\Repository;
use Glofox\Repositories\Search\Expressions\Shared\Id;
use StoreSales as StoreSalesCakeModel;
use UnsuccessfulOperation;

/**
 * <AUTHOR>
 */
class SalesRepository extends Repository
{
    private StoreSalesCakeModel $storeSaleCakeModel;

    public function __construct()
    {
        parent::__construct();

        $this->storeSaleCakeModel = ClassRegistry::init(StoreSalesCakeModel::class);
    }

    public function model(): string
    {
        return StoreSales::class;
    }

    /**
     * @param StoreSales $sale
     * @return StoreSales
     * @throws UnsuccessfulOperation
     */
    public function saveOrFail(StoreSales $sale): StoreSales
    {
        $result = $this->dbCollection()->saveOrFail($sale->toArray());

        return new StoreSales($result['StoreSales']);
    }

    /**
     * @throws InvalidMongoIdException
     */
    public function findById(string $id): StoreSales
    {
        return StoreSales::make(
            $this->addCriteria(new Id($id))
                ->firstOrFail(function() use ($id) {
                    throw StoreSaleNotFountException::withId($id);
                })
        );
    }

    public function findByPurchaseCode(string $purchaseCode): ?StoreSales
    {
        /** @var StoreSales $sale */
        $sale = $this
            ->addCriteria(new PurchaseCode($purchaseCode))
            ->first();
        return $sale;
    }

    public function findByInvoiceId(string $invoiceId): ?StoreSales
    {
        /** @var StoreSales $sale */
        $sale = $this
            ->addCriteria(new InvoiceId($invoiceId))
            ->first();
        return $sale;
    }

    public function findByPurchaseCodeAndInvoiceId(string $purchaseCode, string $invoiceId): ?StoreSales
    {
        /** @var StoreSales $sale */
        $sale = $this
            ->addCriteria(new PurchaseCode($purchaseCode))
            ->addCriteria(new InvoiceId($invoiceId))
            ->first();
        return $sale;
    }

    public function findByPurchaseParams(
        string $productId,
        int $presentationId,
        string $purchaseCode,
        string $invoiceId
    ): ?StoreSales
    {
        /** @var StoreSales $sale */
        $sale = $this->getFindByPurchaseParamsCriteria($productId, $presentationId, $purchaseCode, $invoiceId)
            ->first();
        return $sale;
    }

    public function findByPurchaseParamsCount(
        string $productId,
        int $presentationId,
        string $purchaseCode,
        string $invoiceId
    ): int
    {
        return (int) $this->getFindByPurchaseParamsCriteria($productId, $presentationId, $purchaseCode, $invoiceId)
            ->count();
    }

    private function getFindByPurchaseParamsCriteria(
        string $productId,
        int $presentationId,
        string $purchaseCode,
        string $invoiceId
    ): self
    {
        return $this
            ->addCriteria(new InvoiceId($invoiceId))
            ->addCriteria(new ProductId($productId))
            ->addCriteria(new PresentationId($presentationId))
            ->addCriteria(new PurchaseCode($purchaseCode));
    }

    /**
     * @throws StoreSaleNotFountException
     * @throws Exception
     */
    public function update(string $id, $data): StoreSales
    {
        $cakeModel = $this->dbCollection();
        $cakeModel->clear();
        $cakeModel->read(null, $id);
        $cakeModel->set($data);

        $result = $cakeModel->save(
            null,
            [
                'validate' => !$this->skipValidations,
                'callbacks' => !$this->skipCallbacks,
            ]
        );

        if (!$result) {
            throw StoreSaleNotFountException::withId($id);
        }

        return StoreSales::make($result['StoreSales']);
    }

    protected function dbCollection(): StoreSalesCakeModel
    {
        return $this->storeSaleCakeModel;
    }
}
