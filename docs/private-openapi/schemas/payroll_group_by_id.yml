type: object
properties:
  _id:
    type: string
    pattern: '^[a-f\d]{24}$'
  branch_id:
    type: string
    pattern: '^[a-f\d]{24}$'
  name:
    type: string
  staff_ids:
    type: array
    description: The staff IDs to which these group rates apply. If empty, it will apply to all.
    items:
      type: string
  rates:
    type: array
    items:
      $ref: './payroll_rate.yml'
  created:
    type: string
    description: date-time or object with seconds and microseconds
  created_by:
    type: string
    pattern: '^[a-f\d]{24}$'
    description: user id of who created this record
  modified:
    type: string
    description: date-time or object with seconds and microseconds
  modified_by:
    type: string
    pattern: '^[a-f\d]{24}$'
    description: user id of who updated this record
