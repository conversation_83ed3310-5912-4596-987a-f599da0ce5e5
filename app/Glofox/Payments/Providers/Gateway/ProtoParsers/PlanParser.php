<?php

namespace Glofox\Payments\Providers\Gateway\ProtoParsers;

use Carbon\Carbon;
use Glofox\Payments\Entities\Plan\Models\Plan as ProviderPlan;
use GRPC\Payments\TransactionService\Plan;

/**
 * Class PlanParser
 * @package Glofox\Payments\Providers\Gateway\ProtoParsers
 */
class PlanParser
{
    /**
     * @param Plan $plan
     * @return ProviderPlan
     */
    public function parseFromProtoMessage($plan): ProviderPlan
    {
        $providerPlan = new ProviderPlan();

        return $providerPlan->setId($plan->getID())
                    ->setName($plan->getName())
                    ->setPrice($plan->getAmount())
                    ->setCurrency($plan->getCurrency())
                    ->setDurationUnit($plan->getInterval())
                    ->setDurationUnitCount($plan->getIntervalCount())
                    ->setServiceProviderId($plan->getProviderID())
                    ->setPaymentServiceId($plan->getPSPUUID())
                    ->setPaymentServiceResponse($plan->getPSPResponse())
                    ->setCreatedAt(
                        Carbon::parse($plan->getCreatedAt())->getTimestamp()
                    )
                    ->setUpdatedAt(
                        Carbon::parse($plan->getUpdatedAt())->getTimestamp()
                    );
    }
}
