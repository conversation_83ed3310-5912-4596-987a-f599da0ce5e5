<?php

namespace Glofox\Response\Models\Branches\Features;

use Glofox\Response\BaseModel;

/**
 * @SuppressWarnings(PHPMD)
 */
class MembershipsModel extends BaseModel
{
    /**
     * @var int
     */
    public $order = 0;

    /**
     * @var bool
     */
    public $enabled = false;

    public $payg;
    public $time;
    public $num_classes;
    public $time_classes;

    protected array $allowedFields = [
        'order',
        'enabled',
        'payg',
        'time',
        'num_classes',
        'time_classes',
    ];

    public function __construct(array $data)
    {
        $this->setModelFields($data);
    }
}
