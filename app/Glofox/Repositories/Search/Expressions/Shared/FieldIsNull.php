<?php

declare(strict_types=1);

namespace Glofox\Repositories\Search\Expressions\Shared;

use Glofox\Repositories\Filters\Evaluable;
use Glofox\Repositories\Search\Expressions\Conditionable;
use Glofox\Repositories\Search\Expressions\HasConditions;
use Glofox\Repositories\Search\Expressions\Key;
use Webmozart\Expression\Expr;

class FieldIsNull extends Key implements Evaluable, Conditionable
{
    use HasConditions;

    private string $field;

    public function __construct(string $field)
    {
        $this->field = $field;
        parent::__construct($field, Expr::equals(null));
    }

    public function field(): string
    {
        return $this->field;
    }
}
