<?php

use Glofox\Domain\Reports\Requests\HasReportsRequest;

/**
 * Class ReportsController.
 *
 * @property UtilityComponent $Utility
 * @property Branch           $Branch
 * @property Report           $Report
 * @property User             $User
 * @property Membership       $Membership
 */
class ReportsController extends \Glofox\Domain\Reports\Http\ReportsController
{
    public $name = 'Reports';

    public $uses = [
        'Branch',
        'Report',
        'Membership',
        'User',
    ];

    public $components = [
        'Utility',
    ];

    protected $restActions = [
        'save',
        'getAllByBranchId',
        'delete',
        'hasReports',
    ];

    /**
     * @return string
     */
    public function save()
    {
        $result = null;

        try {
            $data = file_get_contents('php://input');
            $data = json_decode($data, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
            $data ??= $this->request->data;

            if ('custom' === $data['range_date']) {
                $startDate = $data['start'];
                $data['start'] = (new DateTime("@$startDate"))->format('Y-m-d H:i:s');
                $endDate = $data['end'];
                $data['end'] = (new DateTime("@$endDate"))->format('Y-m-d H:i:s');
            }

            if (isset($data['filter']['CompareToRanges']) && $data['filter']['CompareToRanges']) {
                $secondStartDate = $data['secondStart'];
                $data['secondStart'] = (new DateTime("@$secondStartDate"))->format('Y-m-d H:i:s');
                $secondEndDate = $data['secondEnd'];
                $data['secondEnd'] = (new DateTime("@$secondEndDate"))->format('Y-m-d H:i:s');
            }

            $save_report = $this->Report->save($data);

            if (true == $save_report) {
                $result = [
                    'success' => true,
                    'message' => 'The report has been saved.',
                    'report' => $save_report['Report'],
                    ];
            } else {
                $result = [
                    'success' => false,
                    'message' => 'Ocurred a problem saving the report.',
                    ];
            }
        } catch (Exception $e) {
            $result = $e->getMessage();
        }

        return json_encode($result, JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    /**
     * @param null $branch_id
     * @param null $namespace
     *
     * @return string
     */
    public function getAllByBranchId($branch_id = null, $namespace = null)
    {
        $result = null;

        try {
            $is_valid_mongoid = $this->Utility->is_valid_mongoid($branch_id);

            if (!$is_valid_mongoid) {
                $result = [
                    'success' => false,
                    'message' => 'The branch ID is not a valid ID.',
                    ];
            }

            $branch_strike_system = $this->Branch->find(
                'first',
                [
                    'conditions' => [
                        '_id' => $branch_id,
                        ],
                    'fields' => ['features.booking.strike_system'],
                    ]
                );

            $all_reports = $this->Report->getAllByBranchId($branch_id, $namespace, $branch_strike_system);

            if (null != $all_reports && !empty($all_reports)) {
                $all_reports = $this->setDefaultReportsSettings($branch_id, $namespace, $all_reports);

                $result = [
                    'success' => true,
                    'message' => 'The reports have been found.',
                    'reports' => $all_reports,
                    ];
            } else {
                $result = [
                    'success' => false,
                    'message' => 'There are no reports for this branch.',
                    ];
            }
        } catch (Exception $e) {
            $result = $e->getMessage();
        }

        return json_encode($result, JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    /**
     * @return string
     */
    public function delete()
    {
        $result = null;

        try {
            $data = file_get_contents('php://input');
            $data = json_decode($data, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
            $data ??= $this->request->data;

            if ('DefaultMembership' != $data['model'] && 'DefaultMember' != $data['model'] && 'DefaultStrikes' != $data['model']) {
                $result = $this->Report->delete($data['_id']);
            } else {
                $data['do_not_show_branch_id'][] = $data['branch_id'];
                $data['branch_id'] = '';
                $data['namespace'] = '';
                unset($data['filter']);
                $result = $this->Report->save($data);
            }

            if (true == $result) {
                $result = [
                    'success' => true,
                    'message' => 'The report has been deleted.',
                    'report' => $result['Report'],
                    ];
            } else {
                $result = [
                    'success' => false,
                    'message' => 'Ocurred a problem deleting the report.',
                    ];
            }
        } catch (Exception $e) {
            $result = $e->getMessage();
        }

        return json_encode($result, JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    public function hasReports(HasReportsRequest $request)
    {
        return $this->Report->branchHasReports($request->getBranchId());
    }

    /**
     * @param null $branch_id
     * @param null $namespace
     * @param null $all_reports
     */
    private function setDefaultReportsSettings($branch_id = null, $namespace = null, $all_reports = null)
    {
        $today = $this->Utility->getCurrentDate('Y-m-d');

        $memberships_ids = $this->Membership->find(
            'all',
            [
                'conditions' => [
                    'branch_id' => $branch_id,
                    'namespace' => $namespace,
                    'active' => true,
                    ],
                'fields' => [
                    '_id',
                    ],
                ]
            );

        $members_ids = $this->User->find(
            'all',
            [
                'conditions' => [
                    'branch_id' => $branch_id,
                    'namespace' => $namespace,
                    'type' => 'MEMBER',
                    'active' => true,
                    'membership' => ['$exists' => true],
                    'membership.type' => ['$ne' => 'payg'],
                    ],
                'fields' => [
                    '_id',
                    'membership',
                    ],
                ]
            );

        foreach ($all_reports as &$report) {
            if ('' == $report['Report']['branch_id']) {
                $report['Report']['branch_id'] = $branch_id;
                $report['Report']['namespace'] = $namespace;
                $report['Report']['filter'] = [];

                if ('DefaultMembership' == $report['Report']['model']) {
                    $report['Report']['filter']['Memberships'] = [];

                    foreach ($memberships_ids as $membership_id) {
                        $report['Report']['filter']['Memberships'][] = [
                            'id' => $membership_id['Membership']['_id'],
                            ];
                    }
                }

                if ('DefaultMember' == $report['Report']['model']) {
                    $report['Report']['filter']['ActiveMembers'] = [];
                    $report['Report']['filter']['ExpiredMembers'] = [];
                    $report['Report']['filter']['ActiveRoamingMembers'] = [];

                    foreach ($members_ids as $member_id) {
                        // Calculate wheter the members are active or expired
                        if (strtotime($member_id['User']['membership']['expiry_date']) >= strtotime($today) || !isset($member_id['User']['membership']['expiry_date'])) {
                            // Determine if the members come from this branch or are roaming
                            if (empty($member_id['User']['membership']['branch_id']) || ($member_id['User']['membership']['branch_id'] === $branch_id)) {
                                $report['Report']['filter']['ActiveMembers'][] = ['id' => $member_id['User']['_id']];
                            } else {
                                $report['Report']['filter']['ActiveRoamingMembers'][] = ['id' => $member_id['User']['_id']];
                            }
                        } else {
                            $report['Report']['filter']['ExpiredMembers'][] = [
                                'id' => $member_id['User']['_id'],
                                ];
                        }
                    }
                }
            }
        }

        return $all_reports;
    }
}
