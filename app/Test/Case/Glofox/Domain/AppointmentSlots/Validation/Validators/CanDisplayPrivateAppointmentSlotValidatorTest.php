<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\AppointmentSlots\Validation\Validators;

use App;
use Glofox\Domain\AppointmentSlots\Exceptions\PrivateAppointmentSlotCanNotBeDisplayedException;
use Glofox\Domain\AppointmentSlots\Validation\Validators\CanDisplayPrivateAppointmentSlotValidator;
use Glofox\Domain\Users\Models\User;
use GlofoxTestCase;

App::import('Test/Case', 'GlofoxTestCase');

class CanDisplayPrivateAppointmentSlotValidatorTest extends GlofoxTestCase
{
    public $fixtures = [];

    /**
     * @dataProvider validateDataProvider
     * @param User $authorizedUser
     * @param bool $isValid
     * @return void
     * @throws PrivateAppointmentSlotCanNotBeDisplayedException
     */
    public function testValidate(User $authorizedUser, bool $isValid): void
    {
        $validator = new CanDisplayPrivateAppointmentSlotValidator();

        if (!$isValid) {
            $this->expectExceptionMessage('Private appointment slots can not be displayed');
        }

        $validator->validate($authorizedUser);
    }

    public function validateDataProvider(): array
    {
        return [
            'Given the user is logged in as an Admin, When he fetches appointments slots, then he should be allowed to see private slots' => [
                'authorizedUser' => User::make(['type' => 'ADMIN']),
                'isValid' => true,
            ],
            'Given the user is logged in as a SuperAdmin, When he fetches appointments slots, then he should be allowed to see private slots' => [
                'authorizedUser' => User::make(['type' => 'SUPERADMIN']),
                'isValid' => true,
            ],
            'Given the user is logged in as a Receptionist, When he fetches appointments slots, then he should be allowed to see private slots' => [
                'authorizedUser' => User::make(['type' => 'RECEPTION']),
                'isValid' => true,
            ],
            'Given the user is logged in as a Trainer, When he fetches appointments slots, then he should be allowed to see private slots' => [
                'authorizedUser' => User::make(['type' => 'TRAINER']),
                'isValid' => true,
            ],
            'Given the user is logged in as a Member, When he fetches appointments slots, then he should NOT be allowed to see private slots' => [
                'authorizedUser' => User::make(['type' => 'MEMBER']),
                'isValid' => false,
            ],
        ];
    }
}
