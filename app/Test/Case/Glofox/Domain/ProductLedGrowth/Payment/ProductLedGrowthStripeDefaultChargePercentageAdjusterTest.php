<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\ProductLedGrowth\Payment;

use Glofox\Domain\Branches\Models\Branch;
use Glofox\Domain\Clients\Enum\Origin;
use Glofox\Domain\Clients\Models\Client;
use Glofox\Domain\PaymentProviders\Models\PaymentProvider;
use Glofox\Domain\ProductLedGrowth\Payment\Exception\StripeDefaultChargePercentageCannotBeAdjustedException;
use Glofox\Domain\ProductLedGrowth\Payment\ProductLedGrowthStripeDefaultChargePercentageAdjuster;
use Glofox\Domain\ProductLedGrowth\Payment\ProductLedGrowthStripeDefaultChargePercentageRepository;
use Psr\Log\LoggerInterface;
use Psr\Log\NullLogger;

\App::import('Test/Case', 'GlofoxTestCase');

class ProductLedGrowthStripeDefaultChargePercentageAdjusterTest extends \GlofoxTestCase
{
    public $fixtures = [];
    /**
     * @var ProductLedGrowthStripeDefaultChargePercentageRepository&\PHPUnit_Framework_MockObject_MockObject
     */
    private ProductLedGrowthStripeDefaultChargePercentageRepository $productLedGrowthStripeDefaultChargePercentageRepository;

    private LoggerInterface $logger;

    private ProductLedGrowthStripeDefaultChargePercentageAdjuster $productLedGrowthStripeFeeAdjuster;

    public function setUp()
    {
        parent::setUp();

        $this->productLedGrowthStripeDefaultChargePercentageRepository = new ProductLedGrowthStripeDefaultChargePercentageRepository();
        $this->logger = new NullLogger();

        $this->productLedGrowthStripeFeeAdjuster = new ProductLedGrowthStripeDefaultChargePercentageAdjuster(
            $this->productLedGrowthStripeDefaultChargePercentageRepository,
            $this->logger
        );
    }


    public function test_it_doesnt_support_non_plg_clients(): void
    {
        $client = Client::make([
            'origin' => 'non-supported'
        ]);

        $paymentProvider = PaymentProvider::make([
            'handler_id' => 'STRIPE_CUSTOM_HANDLER'
        ]);

        self::assertFalse(
            $this->productLedGrowthStripeFeeAdjuster->supports($client, $paymentProvider)
        );
    }


    public function test_it_doesnt_support_non_stripe_custom_payment_providers(): void
    {
        $client = Client::make([
            'origin' => 'non-supported'
        ]);

        $paymentProvider = PaymentProvider::make([
            'handler_id' => 'STRIPE_CUSTOM_HANDLER'
        ]);

        self::assertFalse(
            $this->productLedGrowthStripeFeeAdjuster->supports($client, $paymentProvider)
        );
    }


    public function test_it_fails_when_an_unsupported_client_is_provided(): void
    {
        self::setExpectedException(StripeDefaultChargePercentageCannotBeAdjustedException::class);

        $client = Client::make([
            'origin' => 'non-supported'
        ]);

        $branch = Branch::make([
            '_id' => '5af45d6209f3282d1a3e2aa1'
        ]);

        $paymentProvider = PaymentProvider::make([
            'handler_id' => 'STRIPE_CUSTOM_HANDLER'
        ]);

        $this->productLedGrowthStripeFeeAdjuster->adjust($client, $branch, $paymentProvider);
    }


    public function test_it_fails_when_an_unsupported_payment_provider_is_provided(): void
    {
        self::setExpectedException(StripeDefaultChargePercentageCannotBeAdjustedException::class);

        $client = Client::make([
            'origin' => Origin::PLG
        ]);

        $branch = Branch::make([
            '_id' => '5af45d6209f3282d1a3e2aa1'
        ]);

        $paymentProvider = PaymentProvider::make([
            'handler_id' => 'UNSUPPORTED'
        ]);

        $this->productLedGrowthStripeFeeAdjuster->adjust($client, $branch, $paymentProvider);
    }

    public function test_it_changes_the_default_fee_for_every_country_of_the_provider(): void
    {
        $client = Client::make([
            'origin' => Origin::PLG
        ]);

        $branch = Branch::make([
            '_id' => '5af45d6209f3282d1a3e2aa1'
        ]);

        $paymentProvider = PaymentProvider::make([
            'active' => true,
            'gateway_id' => '1',
            'payment_method_type_id' => 'CARD',
            'verification_flow' => null,
            'handler_id' => 'STRIPE_CUSTOM_HANDLER',
            'available_countries' => [
                [
                    'country_code' => 'US',
                    'default_charge_percentage' => 0.0,
                    'default_fixed_charge' => 0.0
                ],
                [
                    'country_code' => 'UK',
                    'default_charge_percentage' => 4.0,
                    'default_fixed_charge' => 0.0
                ],
                [
                    'country_code' => 'BR',
                    'default_charge_percentage' => 7.1,
                    'default_fixed_charge' => 0.0
                ],
            ],
            'name' => 'STRIPE_CUSTOM_EU',
            'registration_fields' => null,
            'registration_flow' => [
                'type' => 'REDIRECT',
                'base_flow' => 'https://app.glofox.com/dashboard/#/settings/onboarding/glofox-payments'
            ],
            'tokenization_handler' => 'STRIPE'
        ]);

        $this->productLedGrowthStripeFeeAdjuster->adjust($client, $branch, $paymentProvider);

        $availableCountries = $paymentProvider->availableCountries();

        self::assertSame(7.0, $availableCountries->get(0)->defaultChargePercentage());
        self::assertSame(7.0, $availableCountries->get(1)->defaultChargePercentage());
        self::assertSame(7.1, $availableCountries->get(2)->defaultChargePercentage());
    }
}
