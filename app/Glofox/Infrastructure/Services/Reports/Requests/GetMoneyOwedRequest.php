<?php

declare(strict_types=1);

namespace Glofox\Infrastructure\Services\Reports\Requests;

use Psr\Http\Message\RequestInterface;

class GetMoneyOwedRequest
{
    private string $branchId;

    private \Psr\Http\Message\RequestInterface $request;

    public function __construct(
        string $branchId,
        RequestInterface $request
    ) {
        $this->branchId = $branchId;
        $this->request = $request;
    }

    public function branchId(): string
    {
        return $this->branchId;
    }

    public function getRequest(): RequestInterface
    {
        return $this->request;
    }
}
