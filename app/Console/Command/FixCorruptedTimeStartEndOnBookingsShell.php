<?php

declare(strict_types=1);

use Carbon\Carbon;
use Glofox\Console\ShellHelper;
use Glofox\Datasource\Exceptions\InvalidMongoIdException;
use Glofox\Domain\Bookings\Repositories\BookingsRepository;
use League\Csv\Reader;
use Psr\Log\LoggerInterface;

App::uses('AmazonS3', 'AmazonS3.Lib');

class FixCorruptedTimeStartEndOnBookingsShell extends Shell
{
    use ShellHelper;

    private \AmazonS3 $amazonS3;
    private BookingsRepository $bookingsRepository;
    private LoggerInterface $logger;

    private int $rowToUpdate = 0;

    public function __construct($stdout = null, $stderr = null, $stdin = null)
    {
        parent::__construct($stdout, $stderr, $stdin);

        $this->bookingsRepository = app()->make(BookingsRepository::class);
        $this->logger = app()->make(LoggerInterface::class);
    }

    public function getOptionParser(): ConsoleOptionParser
    {
        $parser = parent::getOptionParser();

        $parser->addOption('file-path', [
            'short' => 'f',
            'help' => 'The file path on S3',
            'default' => '',
        ]);

        return $parser;
    }

    public function main(): void
    {
        $filepath = $this->getCsvFileFromS3($this->param('file-path'));
        $file = Reader::createFromPath($filepath, 'r');

        foreach ($file as $row) {
            ++$this->rowToUpdate;

            [$bookingId, , , ,$eventTimeStart, $eventTimeFinish] = $row;

            if (empty($bookingId) || empty($eventTimeStart) || empty($eventTimeFinish)) {
                $this->logger->info("Corrupted data");
                continue;
            }

            $this->logger->info($this->rowToUpdate . '. Running for booking ID: ' . $bookingId);

            try {
                $this->fixCorruptedTimeStartAndTimeFinish($bookingId, $eventTimeStart, $eventTimeFinish);
            } catch (\Exception $e) {
                $this->logger->error('[ERROR] Booking id: ' . $bookingId . ' had error ' . $e->getMessage());
            }
        }
    }

    /**
     * @throws InvalidMongoIdException
     */
    private function fixCorruptedTimeStartAndTimeFinish(string $bookingId, string $timeStart, string $timeFinish): void
    {
        $bookingTimeStart = Carbon::parse($timeStart);
        $bookingTimeFinish = Carbon::parse($timeFinish);

        $booking = $this->bookingsRepository->getById($bookingId);

        if ($booking === null) {
            $this->logger->error('Booking not found: ' . $bookingId);
            return;
        }

        $this->bookingsRepository->updateTimeStartAndTimeFinish($bookingId, $bookingTimeStart, $bookingTimeFinish);

        $this->logger->info ( '... done');
    }

    private function getCsvFileFromS3(string $filePath): string
    {
        $config = Configure::read('amazon_s3');

        $this->amazonS3 = new AmazonS3(
            [
                $config['access_key'],
                $config['secret_key'],
                $config['bucket_name'],
                $config['region']
            ]
        );

        $this->amazonS3->get($filePath, TMP);

        return TMP . $filePath;
    }
}
