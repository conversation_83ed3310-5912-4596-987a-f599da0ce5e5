<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\Store\Sales\UseCase;

use App;
use Glofox\Domain\Bookings\Search\Expressions\BranchId;
use Glofox\Domain\Http\Request\BaseCriteria;
use Glofox\Domain\Store\Sales\Expressions\InvoiceId;
use Glofox\Domain\Store\Sales\Expressions\PresentationNameContains;
use Glofox\Domain\Store\Sales\Expressions\ProductNameContains;
use Glofox\Domain\Store\Sales\Expressions\Status;
use Glofox\Domain\Store\Sales\Expressions\UserNameContains;
use Glofox\Domain\Store\Sales\Models\StoreSales;
use Glofox\Domain\Store\Sales\Repositories\SalesRepository;
use Glofox\Domain\Store\Sales\UseCase\GetAllStoreSales;
use Glofox\Domain\Store\Sales\UseCase\GetAllStoreSalesParams;
use Glofox\Domain\Users\Search\Expressions\UserId;
use GlofoxTestCase;
use Mockery;

App::import('Test/Case', 'GlofoxTestCase');

class GetAllStoreSalesTest extends GlofoxTestCase
{
    private SalesRepository $salesRepoMock;
    private BaseCriteria $baseCriteria;

    public function setUp(): void
    {
        parent::setUp();

        $this->salesRepoMock = Mockery::mock(SalesRepository::class);
        $this->baseCriteria = new BaseCriteria(1, 10, ['sort' => 'by']);
    }

    public function tearDown(): void
    {
        parent::tearDown();

        Mockery::close();
    }

    public function testReturnsEmptyDataWhenNothingIsFoundInDB(): void
    {
        $params = new GetAllStoreSalesParams(
            'branch-id',
            'status',
            '',
            '',
            '',
            $this->baseCriteria
        );

        $this->salesRepoMock
            ->shouldReceive('addCriteria')
            ->andReturnSelf()
            ->twice()
            ->getMock()
            ->shouldReceive('count')
            ->andReturn(0)
            ->once();

        [$countResult, $dataResult] = (new GetAllStoreSales($this->salesRepoMock))->execute($params);

        self::assertEquals(0, $countResult);
        self::assertEmpty($dataResult);
    }

    public function testReturnsProperDataWhenFoundInDB(): void
    {
        $data = collect([
            StoreSales::make(['_id' => 'store-sales-1']),
            StoreSales::make(['_id' => 'store-sales-2']),
        ]);

        $params = new GetAllStoreSalesParams(
            'branch-id',
            'status',
            '',
            '',
            '',
            $this->baseCriteria
        );

        $this->salesRepoMock
            ->shouldReceive('addCriteria')
            ->andReturnSelf()
            ->twice()
            ->getMock()
            ->shouldReceive('count')
            ->andReturn(2)
            ->once()
            ->getMock()
            ->shouldReceive('order')
            ->andReturnSelf()
            ->once()
            ->getMock()
            ->shouldReceive('limit')
            ->andReturnSelf()
            ->once()
            ->getMock()
            ->shouldReceive('page')
            ->andReturnSelf()
            ->once()
            ->getMock()
            ->shouldReceive('find')
            ->andReturn($data)
            ->once()
            ->getMock();

        [$countResult, $dataResult] = (new GetAllStoreSales($this->salesRepoMock))->execute($params);

        self::assertEquals(2, $countResult);
        self::assertEquals($data, $dataResult);
    }

    public function testDBQueryIsPerformedAsExpected(): void
    {
        $data = collect([
            StoreSales::make(['_id' => 'store-sales-1']),
        ]);

        $params = new GetAllStoreSalesParams(
            'branch-id',
            'status',
            'query-search',
            'user-id',
            'invoice-id',
            $this->baseCriteria
        );

        $this->salesRepoMock
            ->shouldReceive('addCriteria')
            ->withArgs(function(BranchId $params) {
                self::assertEquals((new BranchId('branch-id')), $params);

                return true;
            })
            ->andReturnSelf()
            ->once()
            ->getMock()
            ->shouldReceive('addCriteria')
            ->withArgs(function(Status $params) {
                self::assertEquals((new Status('status')), $params);

                return true;
            })
            ->andReturnSelf()
            ->once()
            ->getMock()
            ->shouldReceive('addOrCriteria')
            ->withArgs(function(array $params) {
                self::assertEquals((new UserNameContains('query-search')), $params[0]);
                self::assertEquals((new ProductNameContains('query-search')), $params[1]);
                self::assertEquals((new PresentationNameContains('query-search')), $params[2]);

                return true;
            })
            ->andReturnSelf()
            ->once()
            ->getMock()
            ->shouldReceive('addCriteria')
            ->withArgs(function(UserId $params) {
                self::assertEquals((new UserId('user-id')), $params);

                return true;
            })
            ->andReturnSelf()
            ->once()
            ->getMock()
            ->shouldReceive('addCriteria')
            ->withArgs(function(InvoiceId $params) {
                self::assertEquals((new InvoiceId('invoice-id')), $params);

                return true;
            })
            ->andReturnSelf()
            ->once()
            ->getMock()
            ->shouldReceive('count')
            ->andReturn(1)
            ->once()
            ->getMock()
            ->shouldReceive('order')
            ->andReturnSelf()
            ->once()
            ->getMock()
            ->shouldReceive('limit')
            ->andReturnSelf()
            ->once()
            ->getMock()
            ->shouldReceive('page')
            ->andReturnSelf()
            ->once()
            ->getMock()
            ->shouldReceive('find')
            ->andReturn($data)
            ->once()
            ->getMock();

        [$countResult, $dataResult] = (new GetAllStoreSales($this->salesRepoMock))->execute($params);

        self::assertEquals(1, $countResult);
        self::assertEquals($data, $dataResult);
    }
}
