<?php

namespace Glofox\Payments\Contracts;

use Glofox\Domain\PaymentMethods\Models\PaymentMethod;
use Glofox\Domain\PaymentProviders\Models\PaymentProvider;
use Glofox\Payments\Entities\Capability\Contracts\CapabilityHandlerContract;
use Glofox\Payments\Entities\Card\Contracts\CardHandlerContract;
use Glofox\Payments\Entities\Customer\Contracts\CustomerHandlerContract;
use Glofox\Payments\Entities\Invoice\Contracts\InvoiceHandlerContract;
use Glofox\Payments\Entities\Mandate\Contracts\MandateHandlerContract;
use Glofox\Payments\Entities\Merchant\Contracts\MerchantHandlerContract;
use Glofox\Payments\Entities\Payout\Contracts\PayoutHandlerContract;
use Glofox\Payments\Entities\Plan\Contracts\PlanHandlerContract;
use Glofox\Payments\Entities\Subscription\Contracts\SubscriptionHandlerContract;
use Glofox\Payments\Entities\TerminalReader\Contracts\TerminalReaderHandlerContract;
use Glofox\Payments\Entities\Transaction\Contracts\ChargeHandlerContract;
use Glofox\Payments\Entities\Wallet\Contracts\WalletHandlerContract;
use Glofox\Payments\Entities\WebHook\Contracts\WebHookHandlerContract;

/**
 * Interface PaymentProviderContract.
 */
interface PaymentProviderContract
{
    public function __construct(PaymentMethod $paymentMethod, PaymentProvider $paymentProvider);

    public function paymentMethod(): PaymentMethod;

    public function paymentProvider(): PaymentProvider;

    public function merchants(): MerchantHandlerContract;

    public function customers(): CustomerHandlerContract;

    public function cards(): CardHandlerContract;

    public function mandates(): MandateHandlerContract;

    public function terminalReaders(): TerminalReaderHandlerContract;

    public function charges(): ChargeHandlerContract;

    public function plans(): PlanHandlerContract;

    public function subscriptions(): SubscriptionHandlerContract;

    public function invoices(): InvoiceHandlerContract;

    public function payouts(): PayoutHandlerContract;

    public function webHooks(): WebHookHandlerContract;

    public function capabilities(): CapabilityHandlerContract;
}
