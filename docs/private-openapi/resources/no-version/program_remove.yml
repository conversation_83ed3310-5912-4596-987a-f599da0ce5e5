parameters:
  - schema:
      type: string
      pattern: "^[a-f\\d]{24}$"
    name: programId
    in: path
    required: true
get:
  tags:
    - Program
  summary: Remove a program
  operationId: remove
  description:
    Use this call to remove a program offered by a branch. If some event included in the program that someone is about to
    remove has any bookings with status 'booked', this operation will not be completed. To complete this operation all bookings must be
    deleted prior hitting the endpoint.
  security:
    - coreapi_auth: [ ]
  responses:
    '200':
      description: OK
      content:
        application/json:
          schema:
            $ref: '../../schemas/program.yml'
    '400':
      description: Bad request
      content:
        application/json:
          schema:
            $ref: "../../schemas/error.yml"
