<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\Transactional\Tasks;

use App;
use CakeTestCases\Glofox\Domain\Logger\Traits\MockedLoggerTrait;
use Glofox\Domain\Charges\Commands\SendReceiptCommand;
use Glofox\Domain\Charges\Commands\SendReceiptHandler;
use Glofox\Domain\Charges\Exceptions\ChargeNotFoundException;
use Glofox\Domain\Charges\Models\Charge;
use Glofox\Domain\Charges\Repositories\ChargesRepository;
use Glofox\Domain\Transactional\Tasks\SendReceipt;
use Glofox\Eventkit\DomainEvent\DomainEventInterface;
use Glofox\Eventkit\DomainEvent\DomainEventPayloadInterface;
use GlofoxTestCase;
use Mockery;
use MongoId;

App::import('Test/Case', 'GlofoxTestCase');

class SendReceiptTest extends GlofoxTestCase
{
    use MockedLoggerTrait;

    public $fixtures = [];

    /** @var ChargesRepository */
    private $chargesRepository;

    /** @var SendReceiptHandler */
    private $sendReceiptHandler;

    /** @var DomainEventInterface */
    private $domainEvent;

    private ?string $branchId = null;

    private ?string $chargeId = null;

    public function setUp()
    {
        parent::setUp();

        $this->mockLogger();
        $this->chargesRepository = Mockery::mock(ChargesRepository::class);
        $this->sendReceiptHandler = Mockery::mock(SendReceiptHandler::class);

        $this->branchId = (string) new MongoId();
        $this->chargeId = (string) new MongoId();

        $payload = [
            'branchId' => $this->branchId,
            'chargeId' => $this->chargeId,
            'forceReceipt' => false,
        ];

        $domainEventPayload = Mockery::mock(DomainEventPayloadInterface::class);
        $domainEventPayload
            ->shouldReceive('jsonSerialize')
            ->andReturn($payload)
            ->once();

        $this->domainEvent = Mockery::mock(DomainEventInterface::class);
        $this->domainEvent
            ->shouldReceive('payload')
            ->andReturn($domainEventPayload)
            ->getMock()
            ->shouldReceive('type')
            ->andReturn('TRANSACTION_FINISHED');
    }

    public function tearDown()
    {
        parent::tearDown();

        Mockery::close();
        $this->teardownLogger();
    }

    public function testWhenRepositoryRetrievesTheHandlerIsExecutedWithProperParams(): void
    {
        $charge = Charge::make([
            '_id' => $this->chargeId,
            'branch_id' => $this->branchId,
            'foo' => 'bar',
        ]);

        $this->chargesRepository
            ->shouldReceive('addCriteria')
            ->andReturnSelf()
            ->once()
            ->getMock()
            ->shouldReceive('firstOrFail')
            ->andReturn($charge)
            ->once();

        $this->sendReceiptHandler
            ->shouldReceive('handle')
            ->withArgs(function (SendReceiptCommand $command) use ($charge) {
                $this->assertEquals($charge, $command->getChargesGroup()->getPrimaryCharge());

                return true;
            })
            ->once();

        $sendReceipt = new SendReceipt(
            $this->sendReceiptHandler,
            $this->chargesRepository,
            $this->loggerMock
        );
        $sendReceipt->execute($this->domainEvent);
    }

    public function testWhenRepositoryFailsHandlerIsNotExecuted(): void
    {
        $this->chargesRepository
            ->shouldReceive('addCriteria')
            ->andReturnSelf()
            ->once()
            ->getMock()
            ->shouldReceive('firstOrFail')
            ->andThrow(ChargeNotFoundException::class, sprintf('Charge not found with id %s', $this->chargeId))
            ->once();

        $this->sendReceiptHandler
            ->shouldNotReceive('handle');

        $this->setExpectedException(ChargeNotFoundException::class, sprintf('Charge not found with id %s', $this->chargeId));

        $sendReceipt = new SendReceipt(
            $this->sendReceiptHandler,
            $this->chargesRepository,
            $this->loggerMock
        );
        $sendReceipt->execute($this->domainEvent);
    }
}
