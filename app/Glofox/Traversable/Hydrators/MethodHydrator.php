<?php

namespace Glofox\Traversable\Hydrators;

use Glofox\Traversable\Hydrators\Contracts\HydratorContract;

class MethodHydrator extends Hydrator implements HydratorContract
{
    public function fill(array $items)
    {
        $instance = $this->instance ?? app()->make($this->expected);

        foreach ($items as $method => $value) {
            if (method_exists($instance, $method)) {
                // app()->call([$instance, $method], [$value]);
                call_user_func_array([$instance, $method], [$value]);
            }
        }

        return $instance;
    }
}