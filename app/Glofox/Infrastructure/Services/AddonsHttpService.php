<?php

namespace Glofox\Infrastructure\Services;

use Carbon\Carbon;
use Glofox\Domain\Memberships\Models\Addon;
use Glofox\Domain\Memberships\Services\AddonServiceInterface;
use Glofox\Infrastructure\Exception\HttpClientResponseException;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\BadResponseException;
use GuzzleHttp\Exception\ClientException;
use GuzzleHttp\Exception\ServerException;
use Illuminate\Http\JsonResponse;
use Psr\Http\Message\ResponseInterface;
use Psr\Log\LoggerInterface;

class AddonsHttpService implements AddonServiceInterface
{
    private const GET_ADDON = 'v3/services/:addonId';
    private const GET_ADDONS_LIST = 'v3/services';
    private const GET_ADDONS = 'v3/services';
    private const CONSUME_ADDON_CREDITS = 'v3/services/:addonId/credits/consume';
    private const REFUND_ADDON = 'v3/services/:addonId/credits/refund';
    private const PAST_BOOKING_ERROR_RAW = 'activeDate must not be in the past';
    private const PAST_BOOKING_ERROR = 'CANNOT_BOOK_IN_THE_PAST_WHEN_ADDON_SERVICE_IS_ACTIVE';
    private const ADDON_SERVICE_VALIDATION_ERROR = 'ADDON_SERVICE_VALIDATION_ERROR';
    private const ADDON_SERVICE_VALIDATION_ERROR_RAW = 'VALIDATION_FAILED';

    private Client $httpClient;

    private LoggerInterface $logger;

    public function __construct(Client $httpClient, LoggerInterface $logger)
    {
        $this->httpClient = $httpClient;
        $this->logger = $logger;
    }

    public function getAddon(string $addonId, string $branchId): Addon
    {
        $this->logger->info(sprintf('Requesting addon details for addon id: %s', $addonId));
        $url = str_replace(':addonId', $addonId, static::GET_ADDON);
        $response = null;
        try {
            $response = $this->httpClient->get($url, [
                'verify' => true,
                'headers' => [
                    'x-glofox-branch-id' => $branchId,
                ],
            ]);
        } catch (ServerException | ClientException $exception) {
            $this->handleException($exception);
        }

        $body = json_decode((string)$response->getBody(), true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        return $this->responseAddonToDomain($body);
    }

    /**
     * @return Addon[]
     * @see https://api-star-cf.aws.glofox.com/v3/services/swagger-ui/index.html#/services-controller/getServicesByMemberIdUsingGET
     */
    public function getUserAddonsActiveForUsageOnDate(string $branchId, string $userId, Carbon $activeOn): array
    {
        $url = self::GET_ADDONS_LIST;

        $activeOn = $activeOn->copy();
        $activeOn = $activeOn->setTimezone('UTC');

        $this->logger->info(sprintf('Requesting the list of active addons on date %s for user %s',
            $activeOn->toIso8601ZuluString(), $userId));

        $query = [
            'activeDate' => $activeOn->toIso8601ZuluString(),
            'memberId' => $userId
        ];

        $response = null;
        try {
            $response = $this->httpClient->get($url, [
                'query' => $query,
                'verify' => true,
                'headers' => [
                    'x-glofox-branch-id' => $branchId,
                ],
            ]);
        } catch (ServerException | ClientException $exception) {
            $this->handleException($exception);
        }

        $payload = (string)$response->getBody();
        $body = json_decode($payload, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        $this->logger->info(sprintf('Received response: %s', $payload));

        $addons = array_map(fn($item) => $this->responseAddonToDomain($item), $body);

        return $addons;
    }

    public function getAddonsByMemberId(string $branchId, string $memberId): JsonResponse
    {
        $this->logger->info(sprintf('Requesting addons for member id: %s', $memberId));

        $url = static::GET_ADDONS;
        $queryParams = [
            'memberId' => $memberId
        ];

        $response = null;
        try {
            $response = $this->httpClient->get($url, [
                'query' => $queryParams,
                'verify' => true,
                'headers' => [
                    'x-glofox-branch-id' => $branchId,
                ],
            ]);
        } catch (ServerException | ClientException $exception) {
            $this->handleException($exception);
        }

        return $this->normalizeResponseData($response);
    }

    public function consumeAddonCredits(
        string $addonId,
        int $consumedCredits,
        string $bookingId,
        string $branchId,
        Carbon $date
    ): JsonResponse {
        $this->logger->info(sprintf('Consuming credits from addon: %s', $addonId));

        $url = str_replace(':addonId', $addonId, static::CONSUME_ADDON_CREDITS);
        $body = json_encode([
            'consumedCredits' => $consumedCredits,
            'refId' => $bookingId,
            'eventTimestamp' => $date->toIso8601ZuluString()
        ], JSON_PARTIAL_OUTPUT_ON_ERROR);

        $response = null;
        try {
            $response = $this->httpClient->put($url, [
                'body' => $body,
                'verify' => true,
                'headers' => [
                    'x-glofox-branch-id' => $branchId,
                    'Content-Type' => 'application/json',
                ],
            ]);
        } catch (ServerException | ClientException $exception) {
            $this->handleException($exception);
        }

        return $this->normalizeResponseData($response);
    }

    public function refundCreditByAddOnIdAndBookingId(
        string $branchId,
        string $addOnId,
        string $bookingId
    ): JsonResponse {
        $this->logger->info(sprintf('Refunding the addon credit for booking id: %s', $bookingId));

        $url = str_replace(':addonId', $addOnId, static::REFUND_ADDON);
        $body = json_encode(['refId' => $bookingId], JSON_PARTIAL_OUTPUT_ON_ERROR);

        try {
            $response = $this->httpClient->put($url, [
                'verify' => true,
                'headers' => [
                    'x-glofox-branch-id' => $branchId,
                    'Content-Type' => 'application/json',
                ],
                'body' => $body
            ]);
        } catch (ServerException | ClientException $exception) {
            $this->handleException($exception);
        }

        return $this->normalizeResponseData($response);
    }

    protected function normalizeResponseData(ResponseInterface $data)
    {
        $this->lastResponse = $data;

        $response = new JsonResponse();
        $response->setJson((string)$data->getBody());
        $response->setStatusCode($data->getStatusCode());

        $this->logger->info('response received from addons-service', [
            'status' => $response->status(),
            'response' => $response->getData(true),
        ]);

        return $response;
    }

    private function handleException(BadResponseException $exception): void
    {
        $response = $exception->getResponse();
        $statusCode = $response ? $response->getStatusCode() : 'UNKNOWN';
        $body = $response ? (string)$response->getBody() : '';
        $responseData = json_decode($body, $assoc = true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        $this->logger->error('Exception requesting Addons service', [
            'status' => $statusCode,
            'response' => $responseData,
        ]);

        $error = $responseData['code'] ?? null;
        $error = $error ?: ($responseData['message'] ?? $exception->getMessage());

        if ($responseData !== null &&
            key_exists('key', $responseData) &&
            $responseData["key"] === static::ADDON_SERVICE_VALIDATION_ERROR_RAW) {
            $error = static::ADDON_SERVICE_VALIDATION_ERROR;
        }

        if ($responseData !== null && key_exists('errors', $responseData)) {
            foreach ($responseData['errors'] as $e) {
                if (key_exists('key', $e) && $e['key'] === static::PAST_BOOKING_ERROR_RAW) {
                    $error = static::PAST_BOOKING_ERROR;
                }
            }
        }

        throw new HttpClientResponseException($statusCode, $error);
    }

    private function responseAddonToDomain(array $data): Addon
    {
        $addon = new Addon(
            $data['id'],
            $data['definition']['id'],
            $data['definition']['plan']['id'],
            $data['definition']['name'],
            $data['definition']['plan']['name'],
            $data['definition']['type'],
            strtotime($data['purchasedAt']),
            (bool)$data['definition']['plan']['isCreditBased']
        );

        if ($data['definition']['plan']['duration']['hasEndDate']) {
            $addon->setServiceExpiryDate(strtotime($data['plannedEndDate']));
        }

        if ($addon->isCreditsBased()) {
            $addon->setServiceAvailableCredits($data['availableCredits']);
        }

        return $addon;
    }
}
