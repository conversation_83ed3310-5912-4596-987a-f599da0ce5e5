parameters:
  - schema:
      type: string
      pattern: '^[a-f\d]{24}$'
    name: branchId
    in: path
    required: true
  - schema:
      type: string
      pattern: '^[a-f\d]{24}$'
    name: payrollGroupId
    in: path
    required: true
get:
  tags:
    - Payroll Groups
  summary: ''
  operationId: get-payroll-group-by-id
  description: |-
    This endpoint returns payroll group data. If the ID is not found, it returns a
    404 status code with an error.
  x-internal: true
  security:
    - coreapi_auth: [ ]
  responses:
    '200':
      description: OK
      content:
        application/json:
          schema:
            $ref: '../../schemas/payroll_group_by_id.yml'
    '404':
      description: Bad request
      content:
        application/json:
          schema:
            type: object
            properties:
              success:
                type: boolean
                default: false
              message:
                type: string
                enum:
                  - You must provide a Payroll Group ID
              message_code:
                type: string
                enum:
                  - EMPTY_PAYROLL_GROUP_ID
              message_data:
                type: array
                items:
                  type: string
              errors:
                type: array
                items:
                  type: string
put:
  tags:
    - Payroll Groups
  summary: ''
  operationId: update-payroll-group-by-id
  description: If the ID is not found, it returns a 404 status code with an error
  x-internal: true
  security:
    - coreapi_auth: [ ]
  requestBody:
    required: true
    content:
      application/json:
        schema:
          $ref: '../../requests/2.2/create_payroll_group.yml'
  responses:
    '200':
      description: OK
      content:
        application/json:
          schema:
            $ref: '../../schemas/payroll_group.yml'
    '404':
      description: Not found
      content:
        application/json:
          schema:
            $ref : "../../schemas/error_payroll_group.yml"
