<?php

namespace Glofox\Response\Models\Branches\Configuration\Webportal\Features;

use Glofox\Response\BaseModel;

/**
 * @SuppressWarnings(PHPMD)
 */
class FiltersModel extends BaseModel
{
    /**
     * @var bool
     */
    public $classes = false;

    /**
     * @var bool
     */
    public $courses = false;

    /**
     * @var bool
     */
    public $facilities = false;

    /***
     * @var bool
     */
    public $trainers = false;

    protected array $allowedFields = [
        'classes',
        'courses',
        'facilities',
        'trainers',
    ];

    public function __construct(array $data)
    {
        $this->setModelFields($data);
    }
}
