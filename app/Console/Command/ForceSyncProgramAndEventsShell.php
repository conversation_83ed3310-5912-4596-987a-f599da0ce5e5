<?php

use Carbon\Carbon;
use Glofox\Domain\Programs\Repositories\ProgramsRepository;
use Glofox\Domain\Programs\Services\ForceSyncProgramsAndEvents;
use Glofox\Repositories\Search\Expressions\Shared\Id;
use Psr\Log\LoggerInterface;
use Glofox\Domain\Programs\Models\Program;

App::uses('Shell', 'Console');

class ForceSyncProgramAndEventsShell extends AppShell
{
    private LoggerInterface $logger;
    private ForceSyncProgramsAndEvents $forceSyncProgramsAndEventsService;
    private ProgramsRepository $programsRepository;

    public function __construct($stdout = null, $stderr = null, $stdin = null)
    {
        parent::__construct($stdout, $stderr, $stdin);
        $this->logger = app()->make(LoggerInterface::class);
        $this->forceSyncProgramsAndEventsService = app()->make(ForceSyncProgramsAndEvents::class);
        $this->programsRepository = app()->make(ProgramsRepository::class);
    }

    public function getOptionParser(): ConsoleOptionParser
    {
        $parser = parent::getOptionParser();

        $parser->addOption('program-ids', [
            'help' => 'Comma separated list of program ids to run',
            'default' => '',
        ]);

        $parser->addOption('from', [
            'help' => 'timestamp for the start date to run the job',
            'default' => '',
        ]);

        $parser->addOption('to', [
            'help' => 'timestamp for the end date to run the job',
            'default' => '',
        ]);

        return $parser;
    }

    public function main(): void
    {
        $programIds = $this->param('program-ids');
        if (empty($programIds)) {
            throw new UnsuccessfulOperation('There is no program ids specified');
        }
        $programIds = explode(PHP_EOL, $programIds);

        $from = (int)$this->param('from');
        $to = (int)$this->param('to');
        if (empty($from) || empty($to)) {
            throw new UnsuccessfulOperation('both $from and $to are required');
        }

        $from = Carbon::createFromTimestampUTC($from);
        $to = Carbon::createFromTimestampUTC($to);

        foreach ($programIds as $programId) {
            $this->logger->info(sprintf('ForceSyncProgramAndEventsShell for program [%s] started', $programId));

            /** @var \Glofox\Domain\Programs\Models\Program $program */
            $program = $this->programsRepository
                ->addCriteria(new Id($programId))
                ->firstOrFail(function () use ($programId) {
                    throw new NotFoundException(sprintf('program not found [%s]', $programId));
                });

            $this->forceSyncProgramsAndEventsService->execute(
                Program::make($program),
                $from,
                $to
            );

            $this->logger->info(sprintf('ForceSyncProgramAndEventsShell for program [%s] finished', $programId));
        }
    }
}