<?php

use Glofox\Domain\Appointments\Console\Migration\Migration;

class AppointmentsMigrationShell extends AppShell
{
    public function getOptionParser()
    {
        $parser = parent::getOptionParser();
        $parser->addOption('branch-ids', [
            'help' => 'List of branches to run the migration',
            'default' => '',
        ]);

        return $parser;
    }

    public function main(): void
    {
        $branchIds = explode(',', $this->param('branch-ids'));
        app()
            ->make(Migration::class)
            ->execute($branchIds);
    }
}
