<?php

declare(strict_types=1);

App::uses('Controller', 'Controller');
App::uses('AccessControlList', 'Model');

use Glofox\Application;
use Glofox\Http\Exceptions\DeprecatedException;
use Glofox\Http\Responses\JsonPostEncodingErrorValidator;
use Glofox\Http\Responses\JsonResponseInterface;
use Illuminate\Container\Container;
use Illuminate\Http\JsonResponse;

/**
 * Application Controller.
 *
 * Add your application-wide methods in the class below, your controllers
 * will inherit them.
 *
 *
 * @property CakeRequest $request
 *
 * @see        http://book.cakephp.org/2.0/en/controllers.html#the-app-controller
 */
class AppController extends Controller
{
    public $uses = [
        'Branch',
        'Client',
    ];

    public $components = [
        'JWT',
    ];
    protected AccessControlList $ACL;
    protected array $user;
    private JsonPostEncodingErrorValidator $jsonPostEncodingErrorValidator;

    /**
     * AppController constructor.
     *
     * @param null $request
     * @param null $response
     */
    public function __construct($request = null, $response = null)
    {
        parent::__construct($request, $response);

        $this->captureNewRelicParameters($request);

        // Custom setup for the main controller and all the sub controllers
        $this->ACL = ClassRegistry::init('AccessControlList');
        $this->jsonPostEncodingErrorValidator = app()->make(JsonPostEncodingErrorValidator::class);
    }

    /**
     * Transform all requests to json.
     */
    public function beforeFilter()
    {
        $this->layout = false;
        $this->response->type('json');
        $this->autoRender = false;
    }

    /**
     * Format dates, parse Regex and make it format friendly for PHP mongo from the JSON.
     *
     * @param string &$item [description]
     * @param string &$key [description]
     * @throws MongoException
     */
    public function encodeRegex(&$item, &$key): void
    {
        //Date parser
        if (!empty(date_parse($item)['day'])) {
            $item = new MongoDate(strtotime($item));
        }
        //Regex parser
        if ('regex' == $key) {
            $item = new MongoRegex($item);
        }
    }

    /**
     * Given a certain action, the method will get the authenticated user,
     * the resource trying to be accessed and will check if the operation can be performed
     * according to the user's role and the ACL settings.
     * If a record in passed, this will check that the user has the permissions
     * for performing the action on this record.
     *
     * @throws UnauthorizedException
     */
    public function validateAccess($action, $record = false): void
    {
        if (!$this->ACL->canIn($this->name, $action, $record)) {
            throw new UnauthorizedException('The action can not be performed by the logged user');
        }
    }

    /**
     * Obtain Payload Raw Data from Rest request.
     * alternatively
     * json_decode(@file_get_contents("php://input"),true);
     */
    public function getPayload(): ?array
    {
        $raw = $this->request->input('json_decode', true);

        return array_merge((array) $raw, $this->request->data, $this->request->query);
    }

    public function getHeaders(): array
    {
        return $this->request->headers;
    }

    /**
     * This method will split the string received by spaces,
     * and it will try to match every word in at least one
     * of the searchable attributes.
     */
    public function splitSearchRegex(array &$params, ?string $search, ?AppModel $model = null): void
    {
        if (!empty($search) && 'undefined' !== $search && 'null' !== $search) {
            $searchArray = explode(' ', $search);
            $params['$and'] = [];
            foreach ($searchArray as $key => $word) {
                $this->encodeSearchRegex($params['$and'][$key], $word, $model);
            }
        }
    }

    /**
     * Given a "parameters" array, generate a condition where the "search" field is matched.
     */
    public function encodeSearchRegex(?array &$params, ?string $search, ?AppModel $model = null): void
    {
        if (!empty($search) && 'undefined' !== $search && 'null' !== $search) {
            $escapedSearch = preg_quote($search, '/');
            $regex = ['$regex' => new MongoRegex("/$escapedSearch/i")];
            $searchable = ($model) ? $model->searchable() : null;

            if (!empty($searchable)) {
                foreach ($searchable as $field) {
                    $aggregation = [];
                    $aggregation[$field] = $regex;
                    $params['$or'][] = $aggregation; // Every searchable should be an or
                }
            }
        }
    }

    /**
     * Receive generic URL but depending the intention execute GET/POST/PUT/DELETE,
     * Get parameters are retrieved in this->request->query,
     * Get list included"object": "list",
     *  "url": "/v1/studios",
     *  "has_more": false,
     *  "data": [].
     *
     * @param string   $identifier
     * @param AppModel $provider
     *
     * @return string [description]
     */
    public function dispatch($identifier, $provider)
    {
        $this->layout = false;
        $this->response->type('json');
        $this->autoRender = false;
        $this->user = $this->getUser();
        $this->name = $provider->table;

        //Get request information $this->getHeaders(); $this->getPayload();
        $this->validateMethods($this->request);
        $this->injectAPIVersion($provider, $this->request->url);

        $params = $this->request->query;
        $payload = $this->getPayload();
        $method = $this->getMethod();
        unset($payload['_method']);

        // Set up the ACL
        $this->ACL->setUpInjector([], $identifier, $this->user);
        $this->ACL->fetchPermissions();

        //Validate this user is admin or superadmin
        $provider->namespace = $this->user['namespace'];

        // Set up the injector in the provider
        $provider->setUpInjector($params, $identifier, $this->user);

        if ('GET' == $method && !isset($identifier)) {
            //Limits && Generic properties
            $this->validateAccess('view');
            $response = $provider->getAll($params);
            if (is_array($response) && isset($response['data'])) {
                $response['data'] = $this->ACL->filterCollection($response['data'], $this->name);
            }
            if (is_object($response) && isset($response->data)) {
                $response->data = $this->ACL->filterCollection($response->data, $this->name);
            }
        } elseif ('GET' == $method && isset($identifier)) {
            //Find unique
            $response = $provider->getById($identifier, $params);
            $this->validateAccess('view', $response);
            $response = $this->ACL->filterResource($response, $this->name);
        } elseif ('POST' == $method) {
            //Create
            $this->validateAccess('create');
            $response = $provider->post($payload);
        } elseif ('PUT' == $method) {
            //Update
            $record = $provider->findOrFail($identifier);
            $this->validateAccess('update', $record);
            $response = $provider->patchById($identifier, $payload);
        } elseif ('DELETE' == $method) {
            //Delete
            $record = $provider->findOrFail($identifier);
            $this->validateAccess('delete', $record);

            $action = [
                $provider,
                'deleteById',
            ];

            $params = [
                'identifier' => $identifier,
            ];

            $response = app()->call($action, $params);
        } else {
            $response = null;
        }

        if ($response instanceof JsonResponseInterface) {
            return $response->toJsonResponse();
        }

        $this->validateNotEmpty($response);

        $result = json_encode($response, JSON_PARTIAL_OUTPUT_ON_ERROR);

        // Since the result is null, there's a chance json_encode failed to parse the
        // response. In this case, we check for errors.
        if (null === $result) {
            $this->jsonPostEncodingErrorValidator->check();
        }

        return $result;
    }

    /**
     * @deprecated use {@see \Glofox\Domain\Authentication\Auth::user} instead
     */
    public function getUser(): array
    {
        return $this->JWT->parseToken();
    }

    public function healthCheck(): JsonResponse
    {
        $this->loadModel('User');
        $database = (bool) $this->User->find('first');

        return response()->json([
            'application' => true,
            'database' => $database,
        ]);
    }

    public function apiDocs(): CakeResponse
    {
        $this->response->file(
            dirname(__DIR__, 2) . '/openapi.yml',
            [
                'download' => true,
                'name' => 'openapi.yml',
            ]
        );

        return $this->response;
    }

    public function notFound(): void
    {
        throw new NotFoundException('Route not found');
    }

    /**
     * @throws DeprecatedException
     */
    public function deprecated(): void
    {
        $route = \Router::currentRoute();

        $newRoute = $route->options['deprecated']['replacedBy'];

        throw DeprecatedException::withNewRoute($newRoute);
    }

    /**
     * @param array|string $url
     * @param null         $status
     * @param bool         $exit
     */
    public function redirect($url, $status = null, $exit = true)
    {
        $route = \Router::currentRoute();

        if (isset($route->options['deprecated'])) {
            $this->response->header('x-glofox-deprecated', 1);
            $this->response->header('x-glofox-replaced-by', $route->options['deprecated']['replacedBy']);
        }

        return parent::redirect($url, $status, $exit);
    }

    protected function captureNewRelicParameters($request): void
    {
        if ($request instanceof CakeRequest) {
            $params = $request->params;

            if (extension_loaded('newrelic')) {
                newrelic_name_transaction($this->name . '/' . $this->action);
            }

            if ($params) {
                if (is_array($params)) {
                    $params = json_encode($params, JSON_PARTIAL_OUTPUT_ON_ERROR);
                }

                newrelic_add_custom_parameter('requestParameters', $params);
            }

            $headers = getallheaders();

            if ($headers) {
                if (is_array($headers)) {
                    $headers = json_encode($headers, JSON_PARTIAL_OUTPUT_ON_ERROR);
                }

                newrelic_add_custom_parameter('requestHeaders', $headers);
            }

            $payload = $this->getPayload();

            if ($payload) {
                if (is_array($payload)) {
                    $payload = json_encode($payload, JSON_PARTIAL_OUTPUT_ON_ERROR);
                }

                newrelic_add_custom_parameter('requestPayload', $payload);
            }

            $ip = $this->request->clientIp();

            if ($ip) {
                newrelic_add_custom_parameter('clientIp', $ip);
            }
        }
    }

    /**
     * Returns the Request verb
     * This verb can be changed by passing _method param with wished verb to be perform
     * This would be useful for the cases where upload files is needed in a PUT/PATCH context.
     */
    protected function getMethod(): string
    {
        $payload = array_merge($this->request->query, (array) $this->getPayload());

        return isset($payload['_method']) && in_array($payload['_method'], ['GET', 'POST', 'PUT', 'DELETE']) ?
            $payload['_method'] :
            $this->request->method();
    }

    /**
     * Returns an specific key form the request payload
     * A default value can be passed in case the value does not exist.
     *
     * @param $key
     * @param $default
     *
     * @return mixed
     */
    protected function getParam($key, $default = null)
    {
        $value = $this->request->param($key);

        return empty($value) ? $default : $value;
    }

    /****************************** DISPATCHER HELPERS **********************************/

    /**
     * Inject version of the API found in the URL for beforesave/afterfind parsing, also define what is the name
     * of the class for easier access in AppModel.
     *
     * @param string   $url      [description]
     * @param AppModel $provider [description]
     */
    protected function injectAPIVersion(&$provider, $url): void
    {
        preg_match("/(\d+).(\d+)/", $url, $vers);
        if (!empty($vers) && !empty($vers[0])) {
            $provider->API = $vers[0];
        }
    }

    protected function container(): Container
    {
        return Application::getInstance();
    }

    protected function isUserLogged(): bool
    {
        try {
            $user = $this->getUser();
        } catch (Exception $exception) {
            $user = [];
        }

        return !empty($user);
    }

    /**
     * Validate only PUT POST GET DELETE.
     *
     * @throws MethodNotAllowedException $request
     */
    private function validateMethods(CakeRequest $request): void
    {
        if (!$request->is('post') && !$request->is('get') && !$request->is('put') && !$request->is('delete')) {
            throw new MethodNotAllowedException(__('Invalid Request, must be GET, POST, PUT or DELETE'));
        }
    }

    /**
     * If no data found for this request, throw a 404 error.
     *
     * @param array|bool|null $response [description]
     *
     * @throws NotFoundException
     */
    private function validateNotEmpty($response): void
    {
        if (empty($response)) {
            throw new NotFoundException(__('No Data Found For Access Key'));
        }
    }
}
