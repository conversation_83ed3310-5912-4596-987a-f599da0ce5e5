<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\BookingRequests\Services;

use Glofox\Domain\BookingRequests\Services\BookingUpdateRequestEventPublisher;
use Glofox\Domain\BookingRequests\Services\BookingUpdateRequestEventPayloadFactory;
use Glofox\Domain\BookingRequests\Services\BookingRequestsPublisher;
use Glofox\Domain\Bookings\Models\Booking;
use Glofox\Domain\Branches\Models\Branch;
use Glofox\Domain\Branches\Repositories\BranchesRepository;
use Mockery\MockInterface;

\App::import('Test/Case', 'GlofoxTestCase');

class BookingUpdateRequestEventPublisherTest extends \GlofoxTestCase
{
    public $fixtures = [];
    private BookingUpdateRequestEventPublisher $bookingUpdateRequestEventPublisher;
    private BookingRequestsPublisher $bookingRequestsPublisher;

    public function setUp(): void
    {
        parent::setUp();

        $this->bookingRequestsPublisher = \Mockery::spy(BookingRequestsPublisher::class);
        $bookingUpdatePayloadFactory = \Mockery::spy(
            app()->make(BookingUpdateRequestEventPayloadFactory::class)
        );
        $branchesRepository = \Mockery::mock(BranchesRepository::class);
        $branchesRepository
            ->shouldReceive('getById')
            ->with('5ce2810d227b1b034b1c6d21')
            ->andReturn(
                Branch::make([
                    'features' => [
                        'gympass' => [
                            'id' => '2',
                            'pass_type_number' => '3',
                            'client_id' => '2',
                            'client_secret' => '3',
                            'validation_api_auth_token' => '4',
                        ],
                    ],
                ]),
            );

        $bookingUpdateRequestEventPublisher = new BookingUpdateRequestEventPublisher(
            $this->bookingRequestsPublisher,
            $bookingUpdatePayloadFactory,
            $branchesRepository
        );

        $this->bookingUpdateRequestEventPublisher = \Mockery::spy($bookingUpdateRequestEventPublisher);
    }

    public function tearDown(): void
    {
        parent::tearDown();

        \Mockery::close();
    }

    public function testItShouldResolveAndPublishEventBookingUpdateRequest(): void
    {
        $booking = Booking::make([
            '_id' => '5d38681265b345003b2ec402',
            'type' => 'events',
            'active' => true,
            'branch_id' => '5ce2810d227b1b034b1c6d21',
            'event_id' => '5d3852c165b345002e423a95',
            'event_name' => 'Test 4',
            'force_overbook' => false,
            'guest_bookings' => 0,
            'paid' => false,
            'status' => 'BOOKED',
            'user_id' => '5d358dbe8bd9a30ec1075a95',
        ]);

        $this->bookingUpdateRequestEventPublisher->publish($booking);

        /** @var MockInterface $spy */
        $spy = $this->bookingRequestsPublisher;
        $spy->shouldHaveReceived('sendUserRequestedEventBookingUpdateEvent');
    }

    public function testItShouldResolveAndPublishTimeSlotBookingUpdateRequest(): void
    {
        $booking = Booking::make([
            '_id' => '5d38681265b345003b2ec402',
            'type' => 'time_slots',
            'active' => true,
            'branch_id' => '5ce2810d227b1b034b1c6d21',
            'time_slot_id' => '5d3852c165b345002e423a95',
            'event_name' => 'Test 4',
            'force_overbook' => false,
            'guest_bookings' => 0,
            'paid' => false,
            'status' => 'BOOKED',
            'user_id' => '5d358dbe8bd9a30ec1075a95',
        ]);

        $this->bookingUpdateRequestEventPublisher->publish($booking);

        /** @var MockInterface $spy */
        $spy = $this->bookingRequestsPublisher;
        $spy->shouldHaveReceived('sendUserRequestedTimeSlotBookingUpdateEvent');
    }

    public function testItShouldResolveAndPublishCourseBookingUpdateRequest(): void
    {
        $booking = Booking::make([
            '_id' => '5d38681265b345003b2ec402',
            'type' => 'courses',
            'active' => true,
            'branch_id' => '5ce2810d227b1b034b1c6d21',
            'event_id' => '5d3852c165b345002e423a95',
            'event_name' => 'Test 4',
            'force_overbook' => false,
            'guest_bookings' => 0,
            'paid' => false,
            'status' => 'BOOKED',
            'user_id' => '5d358dbe8bd9a30ec1075a95',
        ]);

        $this->bookingUpdateRequestEventPublisher->publish($booking);

        /** @var MockInterface $spy */
        $spy = $this->bookingRequestsPublisher;
        $spy->shouldHaveReceived('sendUserRequestedCourseBookingUpdateEvent');
    }
}
