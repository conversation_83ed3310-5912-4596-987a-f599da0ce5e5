<?php

declare(strict_types=1);

App::import('Test/Case', 'GlofoxTestCase');

use Glofox\Response\Models\BasicModels\MainModel;
use Glofox\Response\Transformers\BaseTransformer;

/**
 * Class BaseTransformerTest.
 */
class BaseTransformerTest extends GlofoxTestCase
{
    public $fixtures = [];

    /**
     * @dataProvider modelProvider
     */
    public function testSetMainModelFromArray($dataArray): void
    {
        $model = new MainModel($dataArray);
        $transformer = new BaseTransformer();
        $transformedModel = $transformer->setMainModelFromArray($dataArray, []);
        $this->assertEquals($model, $transformedModel);
    }

    public function modelProvider(): array
    {
        return [
            'Check model and transformers return with the same structure' => [
                [
                    'object' => 'string',
                    'page' => 1,
                    'limit' => 1,
                    'has_more' => false,
                    'total_count' => 5,
                    'data' => [],
                ],
            ],
        ];
    }
}
