post:
  summary: ''
  operationId: group-message
  tags:
    - PushNotifications
  description: To send a group message to a defined list of users
  parameters:
    - schema:
        type: string
        pattern: '^[a-f\d]{24}$'
      name: branchId
      in: path
      required: true
  requestBody:
    required: true
    content:
      application/json:
        schema:
          type: object
          properties:
            message:
              type: string
              description: Message content
            overwrite_marketing:
              type: boolean
              description: To overwrite marketing preferences
              default: true
            user_ids:
              type: array
              items:
                type: string
                pattern: '^[a-f\d]{24}$'
                description: user_id of user to notify
  responses:
    '200':
      description: OK
    '404':
      description: Not Found
      content:
        application/json:
          schema:
            $ref: "../../schemas/error.yml"
    '400':
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: "../../schemas/error.yml"
    '403':
      description: Forbidden
      content:
        application/json:
          schema:
            $ref: "../../schemas/error.yml"