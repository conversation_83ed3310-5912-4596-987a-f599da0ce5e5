<?php

namespace CakeTestCases\Glofox\Domain\TerminalReaders\UseCase;

use Glofox\Domain\Branches\Repositories\BranchesRepository;
use Glofox\Domain\FeatureFlags\Flaggers\StripePosIntegrationFlagger;
use Glofox\Domain\PaymentMethods\Models\PaymentMethod;
use Glofox\Domain\PaymentMethods\Repositories\PaymentMethodsRepository;
use Glofox\Domain\PaymentMethods\Type as PaymentMethodType;
use Glofox\Domain\PaymentProviders\Models\PaymentProvider;
use Glofox\Domain\PaymentProviders\Repositories\PaymentProvidersRepository;
use Glofox\Domain\TerminalReaders\Exceptions\FeatureFlagNotEnabledException;
use Glofox\Domain\TerminalReaders\Exceptions\StripeCustomPaymentMethodIsRequiredException;
use Glofox\Domain\TerminalReaders\Exceptions\TerminalReaderAlreadyExistsException;
use Glofox\Domain\TerminalReaders\Models\TerminalReader;
use Glofox\Domain\TerminalReaders\Repositories\TerminalReadersRepository;
use Glofox\Domain\TerminalReaders\UseCase\CreateTerminalReader;
use Glofox\Domain\TerminalReaders\UseCase\CreateTerminalReaderParams;
use Psr\Log\LoggerInterface;

\App::import('Test/Case', 'GlofoxTestCase');

class CreateTerminalReaderTest extends \GlofoxTestCase
{
    public const stripePosPaymentProviderId = '5b2a3489c7805f005e37dcb0';
    public const branchId = '5c783d4cd510f9635ad4a6b6';
    public const registrationCode = 'registration-code';

    public function tearDown()
    {
        parent::tearDown();
        \Mockery::close();
    }

    public function testItShouldThrowAnExceptionWhenAnExistingTerminalReaderIsLinked(): void
    {
        $terminalReadersRepositoryMock = \Mockery::mock(TerminalReadersRepository::class)
            ->shouldReceive('addCriteria')
            ->andReturnSelf()
            ->shouldReceive('count')
            ->andReturn(1)
            ->once()
            ->getMock();

        $useCase = new CreateTerminalReader(
            app()->make(LoggerInterface::class),
            \Mockery::mock(StripePosIntegrationFlagger::class),
            app()->make(BranchesRepository::class),
            $terminalReadersRepositoryMock,
            \Mockery::mock(PaymentMethodsRepository::class),
            \Mockery::mock(PaymentProvidersRepository::class)
        );

        $this->setExpectedException(TerminalReaderAlreadyExistsException::class);

        $useCase->execute(
            new CreateTerminalReaderParams(self::branchId, self::registrationCode)
        );
    }

    public function testItShouldThrowAnExceptionWhenTheFeatureFlagIsNotTurnedOn(): void
    {
        $flaggerMock = \Mockery::mock(StripePosIntegrationFlagger::class)
            ->shouldReceive('has')
            ->andReturnFalse()
            ->getMock();

        $terminalReadersRepositoryMock = \Mockery::mock(TerminalReadersRepository::class)
            ->shouldReceive('addCriteria')
            ->andReturnSelf()
            ->shouldReceive('count')
            ->andReturn(0)
            ->once()
            ->getMock();

        $useCase = new CreateTerminalReader(
            app()->make(LoggerInterface::class),
            $flaggerMock,
            app()->make(BranchesRepository::class),
            $terminalReadersRepositoryMock,
            \Mockery::mock(PaymentMethodsRepository::class),
            \Mockery::mock(PaymentProvidersRepository::class)
        );

        $this->setExpectedException(FeatureFlagNotEnabledException::class);

        $useCase->execute(
            new CreateTerminalReaderParams(self::branchId, self::registrationCode)
        );
    }

    public function testItShouldThrowAnExceptionWhenNoStripeCustomPaymentMethodIsAvailable()
    {
        $flaggerMock = \Mockery::mock(StripePosIntegrationFlagger::class)
            ->shouldReceive('has')
            ->andReturnTrue()
            ->getMock();

        $terminalReadersRepositoryMock = \Mockery::mock(TerminalReadersRepository::class)
            ->shouldReceive('addCriteria')
            ->andReturnSelf()
            ->shouldReceive('count')
            ->andReturn(0)
            ->getMock();

        $paymentMethodsRepository = \Mockery::mock(PaymentMethodsRepository::class)
            ->shouldReceive('addCriteria')
            ->times(2)
            ->andReturnSelf()
            ->shouldReceive('first')
            ->andReturn(null)
            ->once()
            ->shouldReceive('addCriteria')
            ->times(3)
            ->andReturnSelf()
            ->shouldReceive('first')
            ->andReturn(PaymentMethod::make())
            ->once()
            ->getMock();

        $useCase = new CreateTerminalReader(
            app()->make(LoggerInterface::class),
            $flaggerMock,
            app()->make(BranchesRepository::class),
            $terminalReadersRepositoryMock,
            $paymentMethodsRepository,
            \Mockery::mock(PaymentProvidersRepository::class)
        );

        $this->setExpectedException(StripeCustomPaymentMethodIsRequiredException::class);

        $useCase->execute(
            new CreateTerminalReaderParams(self::branchId, self::registrationCode)
        );
    }

    public function testItShouldCreateATerminalReader(): void
    {
        $exceptedTerminalReader = $this->getTerminalReaderMock();

        $flaggerMock = \Mockery::mock(StripePosIntegrationFlagger::class)
            ->shouldReceive('has')
            ->andReturnTrue()
            ->getMock();

        $terminalReadersRepositoryMock = \Mockery::mock(TerminalReadersRepository::class)
            ->shouldReceive('addCriteria')
            ->andReturnSelf()
            ->shouldReceive('count')
            ->andReturn(0)
            ->once()
            ->shouldReceive('legacySaveOrFail')
            ->andReturn($exceptedTerminalReader->toLegacy())
            ->getMock();

        $paymentMethodsRepository = \Mockery::mock(PaymentMethodsRepository::class)
            ->shouldReceive('addCriteria')
            ->times(2)
            ->andReturnSelf()
            ->shouldReceive('first')
            ->andReturn(null)
            ->once()
            ->shouldReceive('addCriteria')
            ->times(3)
            ->andReturnSelf()
            ->shouldReceive('first')
            ->andReturn($this->getStripeCustomCardPaymentMethodMock())
            ->once()
            ->shouldReceive('legacySaveOrFail')
            ->andReturn($this->getStripeCustomPOSPaymentMethodMock()->toLegacy())
            ->once()
            ->getMock();

        $paymentProvidersRepository = \Mockery::mock(PaymentProvidersRepository::class)
            ->shouldReceive('addCriteria')
            ->times(4)
            ->andReturnSelf()
            ->shouldReceive('firstOrFail')
            ->andReturn($this->getStripePOSPaymentProviderMock())
            ->getMock();


        $useCase = new CreateTerminalReader(
            app()->make(LoggerInterface::class),
            $flaggerMock,
            app()->make(BranchesRepository::class),
            $terminalReadersRepositoryMock,
            $paymentMethodsRepository,
            $paymentProvidersRepository
        );

        $terminalReader = $useCase->execute(
            new CreateTerminalReaderParams(self::branchId, self::registrationCode)
        );

        $this->assertEquals($exceptedTerminalReader->toArray(), $terminalReader->toArray());
    }

    public function testItShouldCreateATerminalReaderWithoutCreatingAPaymentMethodWhenItExistsAlready(): void
    {
        $exceptedTerminalReader = $this->getTerminalReaderMock();

        $flaggerMock = \Mockery::mock(StripePosIntegrationFlagger::class)
            ->shouldReceive('has')
            ->andReturnTrue()
            ->getMock();

        $terminalReadersRepositoryMock = \Mockery::mock(TerminalReadersRepository::class)
            ->shouldReceive('addCriteria')
            ->andReturnSelf()
            ->shouldReceive('count')
            ->andReturn(0)
            ->once()
            ->shouldReceive('legacySaveOrFail')
            ->andReturn($exceptedTerminalReader->toLegacy())
            ->getMock();

        $paymentMethodsRepository = \Mockery::mock(PaymentMethodsRepository::class)
            ->shouldReceive('addCriteria')
            ->times(2)
            ->andReturnSelf()
            ->shouldReceive('first')
            ->andReturn($this->getStripeCustomPOSPaymentMethodMock())
            ->once()
            ->getMock();

        $useCase = new CreateTerminalReader(
            app()->make(LoggerInterface::class),
            $flaggerMock,
            app()->make(BranchesRepository::class),
            $terminalReadersRepositoryMock,
            $paymentMethodsRepository,
            \Mockery::mock(PaymentProvidersRepository::class)
        );

        $terminalReader = $useCase->execute(
            new CreateTerminalReaderParams(self::branchId, self::registrationCode)
        );

        $this->assertEquals($exceptedTerminalReader->toArray(), $terminalReader->toArray());
    }

    private function getTerminalReaderMock(): TerminalReader
    {
        return TerminalReader::make([
            '_id' => 'terminal-reader-id',
            'label' => 'terminal-reader-label',
            'device_type' => 'terminal-reader-device-type',
            'serial_number' => 'terminal-reader-serial-number',
            'terminal_provider_id' => 'terminal-reader-provider-id',
        ]);
    }

    private function getStripeCustomCardPaymentMethodMock(): PaymentMethod
    {
        return PaymentMethod::make([
            '_id' => 'stripe-custom-card-id',
            'type_id' => PaymentMethodType::CARD,
            'provider' => ['name' => 'STRIPE_CUSTOM']
        ]);
    }

    private function getStripeCustomPOSPaymentMethodMock(): PaymentMethod
    {
        return PaymentMethod::make([
            '_id' => 'stripe-custom-pos-id',
            'type_id' => PaymentMethodType::POS_TERMINAL,
            'provider' => [
                'id' => self::stripePosPaymentProviderId,
                'name' => 'STRIPE_CUSTOM',
            ]
        ]);
    }

    private function getStripePOSPaymentProviderMock(): PaymentProvider
    {
        return PaymentProvider::make([
            '_id' => self::stripePosPaymentProviderId,
            'available_countries' => [
                [
                    'country_code' => 'IE',
                    'default_fixed_charge' => 1,
                    'default_charge_percentage' => 2,
                ]
            ]
        ]);
    }
}
