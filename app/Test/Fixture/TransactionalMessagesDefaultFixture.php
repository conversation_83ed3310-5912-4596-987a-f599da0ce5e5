<?php

class TransactionalMessagesDefaultFixture extends CakeTestFixture
{
    public $import = 'TransactionalMessagesDefault';

    public function init()
    {
        $this->records = [
            [
                '_id' => new MongoId('5a4e500c184ff253f893f5f1'),
                'configurable' => true,
                'content' => "Hi [member_first_name],<br/>Welcome to [branch_name]!<br/><br/><b>Sign in to your Account</b><br/>You can sign into your account on our website, or with our mobile app to book a session, buy a membership, or check out your schedule. Sign in with your email address and password.<br/><br/><b>No time like the present</b><br/>Why not get off to a great start by booking a session today <a href='[webportal_home_url]'><strong>Check out the Schedule</strong></a>!<br/><br/><b>Join the Family</b><br/>A Membership is a great way to get the best value for joining sessions in [branch_name]. Why not <a href='[webportal_memberships_url]'>check them out</a> to see what package is the best fit for you?<br/><br/>We can't wait for you to experience [branch_name] to help you achieve your goals.<br/><br/>The [branch_name] team",
                'description' => 'TRANSACTIONAL_MESSAGE_WELCOME_DESCRIPTION',
                'enabled' => false,
                'identifier' => \Glofox\Domain\Transactional\Messages\Identifier::WELCOME()->getName(),
                'name' => 'TRANSACTIONAL_MESSAGE_WELCOME_TITLE',
                'subject' => 'Welcome to [branch_name]!',
                'template' => \Glofox\Domain\Transactional\Templates\Identifier::STANDARD()->getName(),
                'triggers' => [],
                'type' => 'EVENT_BASED',
                'variables' => [
                    \Glofox\Domain\Transactional\Messages\Variable::BRANCH_NAME()->getName(),
                    \Glofox\Domain\Transactional\Messages\Variable::MEMBER_FIRST_NAME()->getName(),
                    \Glofox\Domain\Transactional\Messages\Variable::WEBPORTAL_MEMBERSHIPS_URL()->getName(),
                    \Glofox\Domain\Transactional\Messages\Variable::WEBPORTAL_HOME_URL()->getName(),
                ],
            ],
            [
                '_id' => new MongoId('5a4e500c184ff253f893f5f2'),
                'configurable' => true,
                'content' => "Hi [member_first_name],<br/>Welcome to [branch_name]!<br/><br/><b>Sign in to your Account</b><br/>You can sign into your account on our website, or with our mobile app to book a session, buy a membership, or check out your schedule. Sign in with your email address and password.<br/><br/><b>No time like the present</b><br/>Why not get off to a great start by booking a session today <a href='[webportal_home_url]'><strong>Check out the Schedule</strong></a>!<br/><br/><b>Join the Family</b><br/>A Membership is a great way to get the best value for joining sessions in [branch_name]. Why not <a href='[webportal_memberships_url]'>check them out</a> to see what package is the best fit for you?<br/><br/>We can't wait for you to experience [branch_name] to help you achieve your goals.<br/><br/>The [branch_name] team",
                'description' => 'TRANSACTIONAL_MESSAGE_WELCOME_DESCRIPTION',
                'enabled' => false,
                'identifier' => \Glofox\Domain\Transactional\Messages\Identifier::ONLINE_CLASS_BOOKING_CONFIRMATION()->getName(),
                'name' => 'TRANSACTIONAL_MESSAGE_WELCOME_TITLE',
                'subject' => 'Welcome to [branch_name]!',
                'template' => \Glofox\Domain\Transactional\Templates\Identifier::STANDARD()->getName(),
                'triggers' => [],
                'type' => 'EVENT_BASED',
                'variables' => [
                    \Glofox\Domain\Transactional\Messages\Variable::BRANCH_NAME()->getName(),
                    \Glofox\Domain\Transactional\Messages\Variable::MEMBER_FIRST_NAME()->getName(),
                    \Glofox\Domain\Transactional\Messages\Variable::WEBPORTAL_MEMBERSHIPS_URL()->getName(),
                    \Glofox\Domain\Transactional\Messages\Variable::WEBPORTAL_HOME_URL()->getName(),
                ],
            ],
        ];

        parent::init();
    }
}
