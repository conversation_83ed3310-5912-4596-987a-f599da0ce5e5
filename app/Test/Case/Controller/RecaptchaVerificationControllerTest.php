<?php

namespace CakeTestCases\Controller;

use Glofox\Infrastructure\GoogleRecaptcha\Enum\ErrorCode;
use Glofox\Infrastructure\GoogleRecaptcha\Factory\VerifyResponseFactory;
use Glofox\Infrastructure\GoogleRecaptcha\RecaptchaVerificationClient;
use Guz<PERSON><PERSON>ttp\Client as GuzzleHttpClient;
use GuzzleHttp\Psr7\Response;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Response as ResponseCode;

\App::import('Test/Case', 'GlofoxControllerTestCase');
\App::uses('AccessesController', 'Controller');

/**
 * @group functional
 */
class RecaptchaVerificationControllerTest extends \GlofoxControllerTestCase
{
    public function setUp(): void
    {
        parent::setUp();

        app()->forgetInstance(RecaptchaVerificationClient::class);
    }

    public function test_it_successfully_verifies_captcha(): void
    {
        $this->markTestSkipped('Test fails in circleci due to unknown reasons. Works well locally, however.');

        $this->stubRecaptchaClientWithSuccessResponse();

        $response = $this->testAction('/2.2/recaptcha-verify', [
            'method' => 'post',
            'data' => [
                'recaptchaToken' => 'recaptcha-token-stub'
            ]
        ]);

        $response = json_decode($response, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        self::assertSame('very-secrety-secret', env('GOOGLE_RECAPTCHA_SECRET'));
        self::assertTrue($response['success']);
    }

    public function test_it_fails_captcha_verification(): void
    {
        $this->markTestSkipped('Test fails in circleci due to unknown reasons. Works well locally, however.');

        $this->stubRecaptchaClientWithFailureResponse();

        $response = $this->testAction('/2.2/recaptcha-verify', [
            'method' => 'post',
            'data' => [
                'recaptchaToken' => 'recaptcha-token-stub'
            ]
        ]);

        $response = json_decode($response, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        self::assertSame('very-secrety-secret', env('GOOGLE_RECAPTCHA_SECRET'));
        self::assertFalse($response['success']);
        self::assertSame('Could not verify recaptcha due to error(s): invalid-input-response,invalid-input-secret', $response['message']);
    }

    private function stubRecaptchaClientWithFailureResponse(): void
    {
        $httpResponse = new Response(
            ResponseCode::HTTP_OK,
            [],
            json_encode([
                'success' => false,
                'error-codes' => [
                    ErrorCode::INVALID_INPUT_RESPONSE,
                    ErrorCode::INVALID_INPUT_SECRET
                ]
            ])
        );

        $guzzleClient = $this->createMock(GuzzleHttpClient::class);
        $guzzleClient->expects($this->once())
            ->method('request')
            ->with(
                'POST',
                '/siteverify',
                [
                    'json' => [
                        'secret' => 'very-secrety-secret', // from env
                        'response' => 'recaptcha-token-stub'
                    ]
                ]
            )
            ->willReturn($httpResponse);

        $client = new RecaptchaVerificationClient(
            $guzzleClient,
            app()->make(VerifyResponseFactory::class),
            app()->make(LoggerInterface::class),
        );

        app()->instance(RecaptchaVerificationClient::class, $client);
    }

    private function stubRecaptchaClientWithSuccessResponse(): void
    {
        $httpResponse = new Response(
            ResponseCode::HTTP_OK,
            [],
            json_encode([
                'success' => true,
                'error-codes' => []
            ])
        );

        $guzzleClient = $this->createMock(GuzzleHttpClient::class);
        $guzzleClient->expects($this->once())
            ->method('request')
            ->with(
                'POST',
                '/siteverify',
                [
                    'json' => [
                        'secret' => 'very-secrety-secret', // from env
                        'response' => 'recaptcha-token-stub'
                    ]
                ]
            )
            ->willReturn($httpResponse);

        $client = new RecaptchaVerificationClient(
            $guzzleClient,
            app()->make(VerifyResponseFactory::class),
            app()->make(LoggerInterface::class),
        );

        app()->instance(RecaptchaVerificationClient::class, $client);
    }
}
