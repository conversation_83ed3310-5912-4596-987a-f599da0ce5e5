<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\TimeSlots\Models;

use Glofox\Domain\Bookings\Models\BookableEntityType;
use Glofox\Domain\TimeSlotPatterns\Models\TimeSlotPattern;
use Glofox\Domain\TimeSlotPatterns\Repositories\TimeSlotPatternsRepository;
use Glofox\Domain\TimeSlotPatterns\Search\Expressions\Model;
use Glofox\Domain\TimeSlotPatterns\Search\Expressions\ModelId;
use Glofox\Domain\TimeSlots\ModelList;
use Glofox\Domain\TimeSlots\Models\TimeSlot;
use Glofox\Domain\TimeSlots\Repositories\TimeSlotRepository;
use Glofox\Repositories\Search\Expressions\Shared\Id;
use Mockery;

\App::import('Test/Case', 'GlofoxTestCase');

class TimeSlotTest extends \GlofoxTestCase
{
    public $fixtures = ['app.time_slot'];

    public function tearDown()
    {
        parent::tearDown();

        Mockery::close();
    }

    public function test_entity_type_user_returns_trainer(): void
    {
        $bookableEntity = new TimeSlot([
            'model' => ModelList::USERS,
        ]);
        $type = $bookableEntity->entityType();

        self::assertEquals(BookableEntityType::TRAINER, $type);
    }

    public function test_entity_type_appointment_returns_trainer(): void
    {
        $bookableEntity = new TimeSlot([
            'model' => ModelList::APPOINTMENTS,
        ]);
        $type = $bookableEntity->entityType();

        self::assertEquals(BookableEntityType::TRAINER, $type);
    }

    public function test_entity_type_facility_returns_facility(): void
    {
        $bookableEntity = new TimeSlot([
            'model' => ModelList::FACILITIES,
        ]);
        $type = $bookableEntity->entityType();

        self::assertEquals(BookableEntityType::FACILITY, $type);
    }

    public function test_search_params_for_pattern_repository_are_correct_for_appointments(): void
    {
        $bookableEntity = new TimeSlot([
            'model' => ModelList::APPOINTMENTS,
            'model_id' => new \MongoId(),
        ]);

        $timeSlotPatternRepoMock = $this->createMock(TimeSlotPatternsRepository::class);
        $timeSlotPatternRepoMock
            ->expects($this->once())
            ->method('addCriteria')
            ->with(
                $this->equalTo(new Id($bookableEntity->modelId()))
            )
            ->willReturnSelf();
        $timeSlotPatternRepoMock
            ->expects($this->once())
            ->method('firstOrFail')
            ->willReturn(new TimeSlotPattern());

        app()->instance(TimeSlotPatternsRepository::class, $timeSlotPatternRepoMock);

        $bookableEntity->pattern();

        app()->forgetInstance(TimeSlotPatternsRepository::class);
    }

    public function test_search_params_for_pattern_repository_are_correct_for_users(): void
    {
        $bookableEntity = new TimeSlot([
            'model' => ModelList::USERS,
            'model_id' => new \MongoId(),
        ]);

        $expectedModelSearch = new Model($bookableEntity->model());
        $expectedModelIdSearch = new ModelId($bookableEntity->modelId());

        $timeSlotPatternRepoMock = $this->createMock(TimeSlotPatternsRepository::class);
        $timeSlotPatternRepoMock
            ->expects($this->exactly(2))
            ->method('addCriteria')
            ->withConsecutive(
                [$this->equalTo($expectedModelSearch)],
                [$this->equalTo($expectedModelIdSearch)]
            )
            ->willReturnSelf();
        $timeSlotPatternRepoMock
            ->expects($this->once())
            ->method('firstOrFail')
            ->willReturn(new TimeSlotPattern());

        app()->instance(TimeSlotPatternsRepository::class, $timeSlotPatternRepoMock);

        $bookableEntity->pattern();

        app()->forgetInstance(TimeSlotPatternsRepository::class);
    }

    public function test_search_params_for_pattern_repository_are_correct_for_facilities(): void
    {
        $bookableEntity = new TimeSlot([
            'model' => ModelList::FACILITIES,
            'model_id' => new \MongoId(),
        ]);

        $expectedModelSearch = new Model($bookableEntity->model());
        $expectedModelIdSearch = new ModelId($bookableEntity->modelId());

        $timeSlotPatternRepoMock = $this->createMock(TimeSlotPatternsRepository::class);
        $timeSlotPatternRepoMock
            ->expects($this->exactly(2))
            ->method('addCriteria')
            ->withConsecutive(
                [$this->equalTo($expectedModelSearch)],
                [$this->equalTo($expectedModelIdSearch)]
            )
            ->willReturnSelf();
        $timeSlotPatternRepoMock
            ->expects($this->once())
            ->method('firstOrFail')
            ->willReturn(new TimeSlotPattern());

        app()->instance(TimeSlotPatternsRepository::class, $timeSlotPatternRepoMock);

        $bookableEntity->pattern();

        app()->forgetInstance(TimeSlotPatternsRepository::class);
    }

    public function testIsAppointmentsModel(): void
    {
        $timeSlot = TimeSlot::make([
            'model' => 'appointments',
        ]);
        self::assertTrue($timeSlot->isAppointmentsModel());

        $timeSlot = TimeSlot::make([
            'model' => 'users',
        ]);
        self::assertFalse($timeSlot->isAppointmentsModel());
    }

    public function testIsUsersModel(): void
    {
        $timeSlot = TimeSlot::make([
            'model' => 'users',
        ]);
        self::assertTrue($timeSlot->isUsersModel());

        $timeSlot = TimeSlot::make([
            'model' => 'facilities',
        ]);
        self::assertFalse($timeSlot->isUsersModel());
    }

    public function testIsFacilitiesModel(): void
    {
        $timeSlot = TimeSlot::make([
            'model' => 'facilities',
        ]);
        self::assertTrue($timeSlot->isFacilitiesModel());

        $timeSlot = TimeSlot::make([
            'model' => 'appointments',
        ]);
        self::assertFalse($timeSlot->isFacilitiesModel());
    }
}
