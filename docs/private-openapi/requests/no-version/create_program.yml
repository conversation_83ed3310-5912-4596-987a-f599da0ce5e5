type: object
properties:
  branch_id:
    type: string
    description: The unique identifier of the branch
  namespace:
    type: string
    description: The namespace of the studio
  active:
    type: boolean
    description: The current status of the program
  schedule:
    type: array
    items:
      type: object
      properties:
        days_week:
          type: integer
          description: The day of the week of the event
        start_time:
          type: string
          description: The start time in UTC of the event
        end_time:
          type: string
          description: The end time in UTC of the event
        active:
          type: boolean
          description: The current status of the event
        facility:
          type: string
          description: The identifier of the facility
        level:
          type: string
          description: The required level of the event
        trainers:
          type: array
          items:
            type: string
            description: The unique identifier of the trainer
        size:
          type: integer
          description: The maximum participants of the event
        displayAdd:
          type: boolean
  schedule_default:
    type: object
    properties:
      facility:
        type: string
        description: The identifier of the facility
      trainers:
        type: string
        description: The identifier of the trainers added
      size:
        type: integer
        description: The maximum participants
      level:
        type: integer
        description: The required level
      first_name:
        type: string
        description: The name of the requester
      last_name:
        type: string
        description: The last name of the requester
      phone:
        type: string
        description: The phone number of the requester
      description:
        type: string
        description: The description of the program
      image:
        type: string
        description: The url of the image
      email:
        type: string
        description: The email address of the requester
      bookable:
        type: boolean
        description: The status of the program
      schedule:
        type: array
        items:
          type: object
          properties:
            branch_id:
              type: string
            namespace:
              type: string
            active:
              type: boolean
            week_day:
              type: integer
            start_time:
              type: string
            end_time:
              type: string
      time_slot_length:
        type: integer
        description: The duration of the slot
      pricing:
        type: integer
        description: The price of the event
  categories:
    type: array
    items:
      type: string
      description: The identifier of the category for the program
  name:
    type: string
    description: The label of the program
  description:
    type: string
    description: The characteristics of the program
  image:
    type: string
    description: The url to retrieve the image for the program
  classpass:
    type: string
    description: The identifier of the class pass
  gympass:
    type: string
    description: The identifier of the gympass
  taxes:
    type: array
    items:
      type: string
      description: The identifier of the selected tax
  allowed_member_types:
    type: array
    description: The type of member that can book an event in the program
    items:
      type: object
      properties:
        price:
          type: integer
        type:
          type: string
  date_start:
    type: string
    description: The local time that the program starts
  date_finish:
    type: string
    description: The local time that the program ends
  email_admin:
    type: boolean
    description: The capacity to receive emails after a booking goes through
  private:
    type: boolean
    description: The visibility of the events generated
  spot_booking_enable:
    type: boolean
    description: The capacity to book spots in the events generated
required:
  - name
  - description
  - categories
  - date_start
  - schedule
