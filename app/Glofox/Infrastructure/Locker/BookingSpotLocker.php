<?php

namespace Glofox\Infrastructure\Locker;

use Carbon\Carbon;
use Glofox\Domain\BookingSpots\Exceptions\UnableToAcquireLockForModelIdException;
use Glofox\Domain\Cache\CacheClientInterface;
use Psr\Log\LoggerInterface;

class BookingSpotLocker
{
    public const DEFAULT_ACQUIRE_RETRIES_NUMBER = 3;
    public const DEFAULT_ACQUIRE_RETRIES_SLEEP_TIME_IN_SECS = 1;

    private CacheClientInterface $cache;

    private LoggerInterface $logger;

    public function __construct(
        LoggerInterface $logger,
        CacheClientInterface $cache
    ) {
        $this->logger = $logger;
        $this->cache = $cache;
    }

    public function lock(string $modelId): void
    {
        $this->logger->info(sprintf('attempting acquire spot booking lock with model id %s', $modelId));

        $added = $this->cache->setIfNotExist($modelId, $modelId);
        if (!$added) {
            throw new UnableToAcquireLockForModelIdException();
        }

        $expiresAt = Carbon::now()->addMinute();
        $this->cache->expiresAt($modelId, $expiresAt);

        $this->logger->info(
            sprintf('lock successfully acquired for spot booking with model id %s', $modelId),
            [
                'expiresAt' => $expiresAt->toISOString(),
            ]
        );
    }

    public function lockWithMultipleRetries(string $modelId): void
    {
        for ($retries = 0; $retries < self::DEFAULT_ACQUIRE_RETRIES_NUMBER; $retries++)
        {
            try {
                $this->lock($modelId);
                return;
            }
            catch (UnableToAcquireLockForModelIdException $exception) {
                sleep(self::DEFAULT_ACQUIRE_RETRIES_SLEEP_TIME_IN_SECS);
                continue;
            }
        }
        throw new UnableToAcquireLockForModelIdException();
    }

    public function unlock(string $modelId): void
    {
        $this->cache->delete($modelId);

        $this->logger->info(
            sprintf('lock successfully released for spot booking with model id %s', $modelId)
        );
    }
}