<?php


namespace CakeTestCases\Glofox\Payments\Services;

use Glofox\Domain\Users\Models\User;
use Glofox\Payments\Services\CustomChargeUseCase;

\App::import('Test/Case', 'GlofoxTestCase');

class CustomChargeUseCaseTest extends \GlofoxTestCase
{

    public function test_constructor_getters(): void
    {
        $useCase = new CustomChargeUseCase(
            'memberId',
            'branchId',
            'paymentMethod',
            'description',
            'soldByUserId',
            1.1,
            true,
            ['discount-1', 'discount-2'],
            ['super-tax-1'],
            User::make(['_id' => 'user-id-1'])
        );
    
        self::assertEquals('memberId', $useCase->getMemberId());
        self::assertEquals('branchId', $useCase->getBranchId());
        self::assertEquals('paymentMethod', $useCase->getPaymentMethod());
        self::assertEquals('description', $useCase->getDescription());
        self::assertEquals('soldByUserId', $useCase->getSoldByUserId());
        self::assertEquals(1.1, $useCase->getAmount());
        self::assertEquals(true, $useCase->isOffSession());
        self::assertEquals(2, \count($useCase->getDiscounts()));
        self::assertEquals(1, \count($useCase->getTaxIds()));
        self::assertEquals('user-id-1', $useCase->getSessionUser()->id());
    }
}