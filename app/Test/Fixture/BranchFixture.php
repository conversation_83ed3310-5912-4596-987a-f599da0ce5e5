<?php

use MongoDB\BSON\UTCDateTime;

class BranchFixture extends CakeTestFixture
{
    public $import = 'Branch';

    public function init()
    {
        $this->records = [
            [
                '_id' => new MongoId('49a7011a05c677b9a916612a'),
                'name' => 'Glofox',
                'namespace' => 'glofox',
                'active' => true,
                'gantner_token' => 'gantner-token-123',
                'stripe_plan_code' => 'platinum',
                'address' => [
                    'street' => '88 Ranelagh, Ireland',
                    'city' => 'Dublin',
                    'state' => 'Dublin',
                    'country' => 'Ireland',
                    'country_code' => 'IE',
                    'district' => 'Dublin',
                    'latitude' => 53.32456910000001,
                    'longitude' => -6.252675100000033,
                    'currency' => 'EUR',
                    'continent' => 'EU',
                    'timezone_id' => 'Europe/Dublin',
                    'timezone_name' => 'Greenwich Mean Time',
                    'location' => [
                        'lat' => 53.3245691,
                        'lng' => -6.2526751,
                    ],
                ],
                'tutorial' => [
                    'status' => 'completed', //Values skipped,completed,incomplete from OnboardingStatus Enum
                    'steps' => [
                        'memberships' => 'completed', //Values skipped,completed,incomplete from OnboardingStatus Enum
                        'programs' => 'completed', //Values skipped,completed,incomplete from OnboardingStatus Enum
                        'payments' => 'completed', //Values skipped,completed,incomplete from OnboardingStatus Enum
                        'live' => 'completed',  //Values skipped,completed,incomplete from OnboardingStatus Enum
                    ],
                ],
                'categories' => [],
                'class_policy' => 'A policy',
                'configuration' => [
                    'app' => [
                        'layout' => [
                            'header_color' => 'rgb(0, 0, 0)',
                            'body_color' => 'rgb(255, 255, 255)',
                            'footer_color' => 'rgb(0, 0, 0)',
                        ],
                    ],
                    'only_payg' => false,
                    'formats' => [
                        'date' => 'YYYY/MM/DD',
                        'time' => 'HH:mm',
                    ],
                    'webportal' => [
                        'features' => [
                            'general' => [
                                'classes' => true,
                            ],
                        ],
                    ],
                ],
                'email' => '<EMAIL>',
                'email_notifications' => [
                    'new_member' => true,
                    'weekly_expired' => true,
                    'members_expiring' => true,
                ],
                'facebook' => 'www.facebook.com/Weareglofox',
                'features' => [
                    'classes' => [
                        'levels' => 'advanced,all,beginner',
                        'weeks_display' => 4,
                        'order' => 0,
                        'enabled' => true,
                    ],
                    'trainers' => [
                        'enabled' => true,
                        'order' => 3,
                        'booking_weeks_display' => 0,
                        'booking_cancel_window' => 0,
                        'booking_open_window' => 0,
                        'booking_time_slot_length' => 0,
                        'booking_enabled' => false,
                        'booking_close_window' => 0,
                    ],
                    'news' => [
                        'enabled' => true,
                        'order' => 2,
                    ],
                    'club_info' => [
                        'enabled' => true,
                        'order' => 5,
                    ],
                    'facilities' => [
                        'enabled' => true,
                        'order' => 4,
                        'booking' => [
                            'enabled' => true,
                        ],
                        'booking_weeks_display' => 1,
                        'booking_cancel_window' => 1,
                        'booking_open_window' => 24,
                        'booking_time_slot_length' => 0,
                        'booking_enabled' => true,
                        'booking_close_window' => 3,
                    ],
                    'booking' => [
                        'enabled' => true,
                        'strike_system' => [
                            'enabled' => true,
                            'max_strikes' => '3',
                        ],
                        'booking_cancel_window' => 12,
                        'order' => 1,
                        'booking_open_window' => 0,
                        'waiting_list_enabled' => true,
                        'waiting_list' => 9,
                        'cancel_credit_option' => 'model_id',
                        'refunded_credit_expiry' => 0,
                        'guest_bookings' => 2,
                        'auto_booking_enabled' => false,
                    ],
                    'general' => [
                        'login_method' => 'email',
                        'private_access' => false,
                    ],
                    'memberships' => [
                        'payg' => [
                            'enabled' => true,
                        ],
                        'time' => [
                            'enabled' => true,
                        ],
                        'num_classes' => [
                            'enabled' => true,
                        ],
                        'time_classes' => [
                            'enabled' => true,
                        ],
                        'enabled' => true,
                        'order' => 6,
                    ],
                    'stripe_payment' => [
                        'enabled' => true,
                        'currency' => 'eur',
                        'charge_percentage' => 1.0,
                        'pay_gym' => false,
                        'pay_app' => false,
                        'cash_reciepts' => true,
                        'livemode' => false,
                        'access_token' => 'sk_test_8xZ2NfRPdV3NlB8iF7xavc2e',
                        'refresh_token' => 'rt_4upz4yFQ0RueEG2Oja1bB924NF8oP5kGQkmsmFp92lbM0ksF',
                        'publishable_key' => 'pk_test_wCO19vGTC8fbfB2CRJeDh95r',
                        'user_id' => 'acct_14gEdTB6RJo2Q3iN',
                    ],
                    'gympass' => [
                        'id' => '445014',
                        'product_id' => '386612',
                        'pass_type_number' => '',
                        'client_id' => 'gf-test-account',
                        'client_secret' => 'GYMPASS-CLIENT-SECRET',
                        'validation_api_auth_token' => 'GYMPASS-VALIDATION-API-AUTH-TOKEN',
                        'order' => 15,
                    ],
                    'store' => [
                        'enabled' => true,
                        'order' => 7,
                    ],
                    'courses' => [
                        'enabled' => true,
                        'order' => 8,
                        'guest_bookings' => 0,
                    ],
                ],
                'phone' => '1234567',
                'push_config' => [
                    'release' => [
                        'ios' => [
                            'password' => 'senstive-data',
                        ],
                    ],
                ],
                'stripe_customer_id' => 'cus_9I1hNZpUOs5F7L',
                'timezone' => 'Europe/Ireland',
                'twitter' => '@weareglofox',
                'uses_iframe' => false,
                'uses_profile_pictures' => false,
                'website' => 'www.glofox.com',
                'working_hours' => 'Our classes run from 6am to 10pm daily with something to suit everybody!',
                'mailchimp' => [
                    'apiKey' => 'test-api-key',
                    'defaultListId' => 'test-list-id',
                ],
                'opening_times' => [
                    [
                        'dow' => 'MO',
                        'start' => '06:00',
                        'end' => '22:00',
                        'is_open' => true,
                    ],
                    [
                        'dow' => 'TU',
                        'start' => '06:00',
                        'end' => '22:00',
                        'is_open' => true,
                    ],
                    [
                        'dow' => 'WE',
                        'start' => '06:00',
                        'end' => '22:00',
                        'is_open' => true,
                    ],
                    [
                        'dow' => 'TH',
                        'start' => '06:00',
                        'end' => '22:00',
                        'is_open' => true,
                    ],
                    [
                        'dow' => 'FR',
                        'start' => '06:00',
                        'end' => '22:00',
                        'is_open' => true,
                    ],
                    [
                        'dow' => 'SA',
                        'start' => '06:00',
                        'end' => '22:00',
                        'is_open' => true,
                    ],
                    [
                        'dow' => 'SU',
                        'start' => '06:00',
                        'end' => '22:00',
                        'is_open' => true,
                    ],
                ],
                'closing_times' => [
                    [
                        'dow' => [
                            ['label' => 'MON', 'id' => 1],
                            ['label' => 'TUE', 'id' => 2],
                            ['label' => 'WED', 'id' => 3],
                            ['label' => 'THU', 'id' => 4],
                            ['label' => 'FRI', 'id' => 5],
                            ['label' => 'SAT', 'id' => 6],
                            ['label' => 'SUN', 'id' => 0],
                        ],
                        'start' => date('Y-m-d', strtotime('+1 day')), //"2016-12-24T16:50:10.403Z",
                        'end' => date('Y-m-d', strtotime('+1 day')),
                        'all_day' => true,
                        'title' => '',
                        'has_bookings' => false,
                    ],
                    [
                        'dow' => [
                            ['label' => 'MON', 'id' => 1],
                            ['label' => 'TUE', 'id' => 2],
                            ['label' => 'WED', 'id' => 3],
                            ['label' => 'THU', 'id' => 4],
                            ['label' => 'FRI', 'id' => 5],
                            ['label' => 'SAT', 'id' => 6],
                            ['label' => 'SUN', 'id' => 0],
                        ],
                        'start' => date('Y-m-d', strtotime('+3 day')),
                        'end' => date('Y-m-d', strtotime('+5 day')),
                        'all_day' => true,
                        'title' => '',
                        'has_bookings' => false,
                    ],
                ],
                'corporate_id' => 'corp_Glofox',
            ],
            [
                '_id' => new MongoId('49a7011a05c677b9a916614d'),
                'name' => 'Glofox Singapore',
                'namespace' => 'glofoxsingapore',
                'active' => true,
                'stripe_plan_code' => 'platinum',
                'address' => [
                    'street' => 'Singapore',
                    'city' => 'Singapore',
                    'state' => 'Singapore',
                    'country' => 'Singapore',
                    'country_code' => 'SG',
                    'district' => 'Singapore',
                    'latitude' => 53.32456910000001,
                    'longitude' => -6.252675100000033,
                    'currency' => 'SGD',
                    'continent' => 'Asia',
                    'timezone_id' => 'Asia/Singapore',
                    'timezone_name' => 'Singapore',
                    'location' => [
                        'lat' => 53.3245691,
                        'lng' => -6.2526751,
                    ],
                ],
            ],
            [
                '_id' => new MongoId('49a7011a05c677b9a916612b'),
                'name' => 'Test Branch',
                'namespace' => 'test',
                'active' => true,
                'address' => [
                    'street' => '88 Ranelagh, Ireland',
                    'city' => 'Dublin',
                    'state' => 'Dublin',
                    'country' => 'Ireland',
                    'country_code' => 'IE',
                    'district' => 'Dublin',
                    'latitude' => 53.32456910000001,
                    'longitude' => -6.252675100000033,
                    'currency' => 'EUR',
                    'continent' => 'EU',
                    'timezone_id' => 'Europe/Dublin',
                    'timezone_name' => 'Greenwich Mean Time',
                ],
                'tutorial' => [
                    'status' => 'completed', //Values skipped,completed,incomplete from OnboardingStatus Enum
                    'steps' => [
                        'memberships' => 'completed', //Values skipped,completed,incomplete from OnboardingStatus Enum
                        'programs' => 'completed', //Values skipped,completed,incomplete from OnboardingStatus Enum
                        'payments' => 'completed', //Values skipped,completed,incomplete from OnboardingStatus Enum
                        'live' => 'completed',  //Values skipped,completed,incomplete from OnboardingStatus Enum
                    ],
                ],
                'categories' => [],
                'class_policy' => 'A policy',
                'configuration' => [
                    'app' => [
                        'layout' => [
                            'header_color' => 'rgb(0, 0, 0)',
                            'body_color' => 'rgb(255, 255, 255)',
                            'footer_color' => 'rgb(0, 0, 0)',
                        ],
                    ],
                    'only_payg' => false,
                    'formats' => [
                        'date' => 'YYYY/MM/DD',
                        'time' => 'HH:mm',
                    ],
                    'sales_tax' => [
                        'tax_number' => 'foo123',
                        'legal_name' => 'bar321',
                        'company_id' => 'foobar123321'
                    ],
                    'fiscal' => [
                        'member_tax_id_options' => [
                            'enabled' => true,
                            'required' => false,
                        ],
                    ],
                ],
                'email' => '<EMAIL>',
                'email_notifications' => [
                    'new_member' => true,
                    'weekly_expired' => true,
                    'members_expiring' => true,
                ],
                'facebook' => 'www.facebook.com/Weareglofox',
                'features' => [
                    'classes' => [
                        'levels' => 'advanced,all,beginner',
                        'weeks_display' => 4,
                        'order' => 0,
                        'enabled' => true,
                    ],
                    'trainers' => [
                        'enabled' => true,
                        'order' => 3,
                        'booking_weeks_display' => 0,
                        'booking_cancel_window' => 0,
                        'booking_open_window' => 0,
                        'booking_time_slot_length' => 0,
                        'booking_enabled' => false,
                        'booking_close_window' => 0,
                    ],
                    'news' => [
                        'enabled' => true,
                        'order' => 2,
                    ],
                    'club_info' => [
                        'enabled' => true,
                        'order' => 5,
                    ],
                    'facilities' => [
                        'enabled' => true,
                        'order' => 4,
                        'booking' => [
                            'enabled' => true,
                        ],
                        'booking_weeks_display' => 1,
                        'booking_cancel_window' => 1,
                        'booking_open_window' => 0,
                        'booking_time_slot_length' => 0,
                        'booking_enabled' => true,
                        'booking_close_window' => 0,
                    ],
                    'booking' => [
                        'enabled' => true,
                        'strike_system' => [
                            'enabled' => true,
                            'max_strikes' => '3',
                        ],
                        'booking_cancel_window' => 12,
                        'order' => 1,
                        'booking_open_window' => 1,
                        'waiting_list_enabled' => true,
                        'waiting_list' => 9,
                        'cancel_credit_option' => 'model_id',
                        'refunded_credit_expiry' => 0,
                        'guest_bookings' => 2,
                        'auto_booking_enabled' => false,
                    ],
                    'general' => [
                        'login_method' => 'email',
                        'private_access' => false,
                    ],
                    'memberships' => [
                        'payg' => [
                            'enabled' => true,
                        ],
                        'time' => [
                            'enabled' => true,
                        ],
                        'num_classes' => [
                            'enabled' => true,
                        ],
                        'time_classes' => [
                            'enabled' => true,
                        ],
                        'enabled' => true,
                        'order' => 6,
                    ],
                    'stripe_payment' => [
                        'enabled' => true,
                        'currency' => 'eur',
                        'charge_percentage' => 1.0,
                        'pay_gym' => false,
                        'pay_app' => false,
                        'cash_reciepts' => true,
                        'livemode' => false,
                        'access_token' => 'sk_test_8xZ2NfRPdV3NlB8iF7xavc2e',
                        'refresh_token' => 'rt_4upz4yFQ0RueEG2Oja1bB924NF8oP5kGQkmsmFp92lbM0ksF',
                        'publishable_key' => 'pk_test_wCO19vGTC8fbfB2CRJeDh95r',
                        'user_id' => 'acct_14gEdTB6RJo2Q3iN',
                    ],
                    'store' => [
                        'enabled' => true,
                        'order' => 7,
                    ],
                    'courses' => [
                        'enabled' => true,
                        'order' => 8,
                        'guest_bookings' => 0,
                    ],
                ],
                'phone' => '1234567',
                'stripe_customer_id' => 'cus_9I1hNZpUOs5F7L',
                'timezone' => 'Europe/Ireland',
                'twitter' => '@weareglofox',
                'uses_iframe' => false,
                'uses_profile_pictures' => false,
                'website' => 'www.glofox.com',
                'working_hours' => 'Our classes run from 6am to 10pm daily with something to suit everybody!',
                'opening_times' => [
                    [
                        [
                            'dow' => 'MO',
                            'start' => '06:00',
                            'end' => '22:00',
                            'is_open' => true,
                        ],
                        [
                            'dow' => 'TU',
                            'start' => '06:00',
                            'end' => '22:00',
                            'is_open' => true,
                        ],
                        [
                            'dow' => 'WE',
                            'start' => '06:00',
                            'end' => '22:00',
                            'is_open' => true,
                        ],
                        [
                            'dow' => 'TH',
                            'start' => '06:00',
                            'end' => '22:00',
                            'is_open' => true,
                        ],
                        [
                            'dow' => 'FR',
                            'start' => '06:00',
                            'end' => '22:00',
                            'is_open' => true,
                        ],
                        [
                            'dow' => 'SA',
                            'start' => '06:00',
                            'end' => '22:00',
                            'is_open' => true,
                        ],
                        [
                            'dow' => 'SU',
                            'start' => '06:00',
                            'end' => '22:00',
                            'is_open' => true,
                        ],
                    ],
                ],
                'closing_times' => [
                    [
                        'dow' => [
                            ['label' => 'MON', 'id' => 1],
                            ['label' => 'TUE', 'id' => 2],
                            ['label' => 'WED', 'id' => 3],
                            ['label' => 'THU', 'id' => 4],
                            ['label' => 'FRI', 'id' => 5],
                            ['label' => 'SAT', 'id' => 6],
                            ['label' => 'SUN', 'id' => 0],
                        ],
                        'start' => date('Y-m-d', strtotime('+1 day')), //"2016-12-24T16:50:10.403Z",
                        'end' => date('Y-m-d', strtotime('+1 day')),
                        'all_day' => true,
                        'title' => '',
                        'has_bookings' => false,
                    ],
                    [
                        'dow' => [
                            ['label' => 'MON', 'id' => 1],
                            ['label' => 'TUE', 'id' => 2],
                            ['label' => 'WED', 'id' => 3],
                            ['label' => 'THU', 'id' => 4],
                            ['label' => 'FRI', 'id' => 5],
                            ['label' => 'SAT', 'id' => 6],
                            ['label' => 'SUN', 'id' => 0],
                        ],
                        'start' => date('Y-m-d', strtotime('+3 day')),
                        'end' => date('Y-m-d', strtotime('+5 day')),
                        'all_day' => true,
                        'title' => '',
                        'has_bookings' => false,
                    ],
                ],
                'mailchimp' => [
                    'apiKey' => 'test-api-key',
                    'defaultListId' => 'test-list-id',
                ],
            ],
            [
                '_id' => new MongoId('5a9591bcdb07bce527400717'),
                'name' => 'Glofox2',
                'namespace' => 'glofox',
                'active' => true,
                'address' => [
                    'street' => '88 Ranelagh, Ireland',
                    'city' => 'Dublin',
                    'state' => 'Dublin',
                    'country' => 'Ireland',
                    'country_code' => 'IE',
                    'district' => 'Dublin',
                    'latitude' => 53.32456910000001,
                    'longitude' => -6.252675100000033,
                    'currency' => 'EUR',
                    'continent' => 'EU',
                    'timezone_id' => 'Europe/Dublin',
                    'timezone_name' => 'Greenwich Mean Time',
                    'location' => [
                        'lat' => 53.3245691,
                        'lng' => -6.2526751,
                    ],
                ],
                'tutorial' => [
                    'status' => 'completed', //Values skipped,completed,incomplete from OnboardingStatus Enum
                    'steps' => [
                        'memberships' => 'completed', //Values skipped,completed,incomplete from OnboardingStatus Enum
                        'programs' => 'completed', //Values skipped,completed,incomplete from OnboardingStatus Enum
                        'payments' => 'completed', //Values skipped,completed,incomplete from OnboardingStatus Enum
                        'live' => 'completed',  //Values skipped,completed,incomplete from OnboardingStatus Enum
                    ],
                ],
                'categories' => [],
                'class_policy' => 'A policy',
                'configuration' => [
                    'app' => [
                        'layout' => [
                            'header_color' => 'rgb(0, 0, 0)',
                            'body_color' => 'rgb(255, 255, 255)',
                            'footer_color' => 'rgb(0, 0, 0)',
                        ],
                    ],
                    'only_payg' => false,
                    'formats' => [
                        'date' => 'YYYY/MM/DD',
                        'time' => 'HH:mm',
                    ],
                ],
                'email' => '<EMAIL>',
                'email_notifications' => [
                    'new_member' => true,
                    'weekly_expired' => true,
                    'members_expiring' => true,
                ],
                'facebook' => 'www.facebook.com/Weareglofox',
                'features' => [
                    'classes' => [
                        'levels' => 'advanced,all,beginner',
                        'weeks_display' => 4,
                        'order' => 0,
                        'enabled' => true,
                    ],
                    'trainers' => [
                        'enabled' => true,
                        'order' => 3,
                        'booking_weeks_display' => 0,
                        'booking_cancel_window' => 0,
                        'booking_open_window' => 0,
                        'booking_time_slot_length' => 0,
                        'booking_enabled' => false,
                        'booking_close_window' => 0,
                    ],
                    'news' => [
                        'enabled' => true,
                        'order' => 2,
                    ],
                    'club_info' => [
                        'enabled' => true,
                        'order' => 5,
                    ],
                    'facilities' => [
                        'enabled' => true,
                        'order' => 4,
                        'booking' => [
                            'enabled' => true,
                        ],
                        'booking_weeks_display' => 1,
                        'booking_cancel_window' => 1,
                        'booking_open_window' => 0,
                        'booking_time_slot_length' => 0,
                        'booking_enabled' => true,
                        'booking_close_window' => 0,
                    ],
                    'booking' => [
                        'enabled' => true,
                        'strike_system' => [
                            'enabled' => true,
                            'max_strikes' => '3',
                        ],
                        'booking_cancel_window' => 12,
                        'order' => 1,
                        'booking_open_window' => 0,
                        'waiting_list_enabled' => true,
                        'waiting_list' => 9,
                        'cancel_credit_option' => 'model_id',
                        'refunded_credit_expiry' => 0,
                        'guest_bookings' => 2,
                        'auto_booking_enabled' => false,
                    ],
                    'general' => [
                        'login_method' => 'email',
                        'private_access' => false,
                    ],
                    'memberships' => [
                        'payg' => [
                            'enabled' => true,
                        ],
                        'time' => [
                            'enabled' => true,
                        ],
                        'num_classes' => [
                            'enabled' => true,
                        ],
                        'time_classes' => [
                            'enabled' => true,
                        ],
                        'enabled' => true,
                        'order' => 6,
                    ],
                    'stripe_payment' => [
                        'enabled' => true,
                        'currency' => 'eur',
                        'charge_percentage' => 1.0,
                        'pay_gym' => false,
                        'pay_app' => false,
                        'cash_reciepts' => true,
                        'livemode' => false,
                        'access_token' => 'sk_test_8xZ2NfRPdV3NlB8iF7xavc2e',
                        'refresh_token' => 'rt_4upz4yFQ0RueEG2Oja1bB924NF8oP5kGQkmsmFp92lbM0ksF',
                        'publishable_key' => 'pk_test_wCO19vGTC8fbfB2CRJeDh95r',
                        'user_id' => 'acct_14gEdTB6RJo2Q3iN',
                    ],
                    'store' => [
                        'enabled' => true,
                        'order' => 7,
                    ],
                    'courses' => [
                        'enabled' => true,
                        'order' => 8,
                        'guest_bookings' => 0,
                    ],
                ],
                'phone' => '1234567',
                'stripe_customer_id' => 'cus_9I1hNZpUOs5F7L',
                'timezone' => 'Europe/Ireland',
                'twitter' => '@weareglofox',
                'uses_iframe' => false,
                'uses_profile_pictures' => false,
                'website' => 'www.glofox.com',
                'working_hours' => 'Our classes run from 6am to 10pm daily with something to suit everybody!',
                'mailchimp' => [
                    'apiKey' => 'test-api-key',
                    'defaultListId' => 'test-list-id',
                ],
                'opening_times' => [
                    [
                        'dow' => 'MO',
                        'start' => '06:00',
                        'end' => '22:00',
                        'is_open' => true,
                    ],
                    [
                        'dow' => 'TU',
                        'start' => '06:00',
                        'end' => '22:00',
                        'is_open' => true,
                    ],
                    [
                        'dow' => 'WE',
                        'start' => '06:00',
                        'end' => '22:00',
                        'is_open' => true,
                    ],
                    [
                        'dow' => 'TH',
                        'start' => '06:00',
                        'end' => '22:00',
                        'is_open' => true,
                    ],
                    [
                        'dow' => 'FR',
                        'start' => '06:00',
                        'end' => '22:00',
                        'is_open' => true,
                    ],
                    [
                        'dow' => 'SA',
                        'start' => '06:00',
                        'end' => '22:00',
                        'is_open' => true,
                    ],
                    [
                        'dow' => 'SU',
                        'start' => '06:00',
                        'end' => '22:00',
                        'is_open' => true,
                    ],
                ],
                'closing_times' => [
                    [
                        'dow' => [
                            ['label' => 'MON', 'id' => 1],
                            ['label' => 'TUE', 'id' => 2],
                            ['label' => 'WED', 'id' => 3],
                            ['label' => 'THU', 'id' => 4],
                            ['label' => 'FRI', 'id' => 5],
                            ['label' => 'SAT', 'id' => 6],
                            ['label' => 'SUN', 'id' => 0],
                        ],
                        'start' => date('Y-m-d', strtotime('+1 day')), //"2016-12-24T16:50:10.403Z",
                        'end' => date('Y-m-d', strtotime('+1 day')),
                        'all_day' => true,
                        'title' => '',
                        'has_bookings' => false,
                    ],
                    [
                        'dow' => [
                            ['label' => 'MON', 'id' => 1],
                            ['label' => 'TUE', 'id' => 2],
                            ['label' => 'WED', 'id' => 3],
                            ['label' => 'THU', 'id' => 4],
                            ['label' => 'FRI', 'id' => 5],
                            ['label' => 'SAT', 'id' => 6],
                            ['label' => 'SUN', 'id' => 0],
                        ],
                        'start' => date('Y-m-d', strtotime('+3 day')),
                        'end' => date('Y-m-d', strtotime('+5 day')),
                        'all_day' => true,
                        'title' => '',
                        'has_bookings' => false,
                    ],
                ],
                'payments' => [
                    'stripe_custom' => null,
                ],
            ],

            // "incomplete branch" by alexis for membership validation tests
            [
                '_id' => new MongoId('59d3aa6591f32f90bc96d81e'),
                'features' => [
                    'booking' => [
                        'strike_system' => [
                            'enabled' => true,
                            'max_strikes' => 1,
                        ],
                    ],
                    'general' => [
                        'login_method' => 'email',
                    ],
                ],
            ],

            // Very short branch just for Authorization tests
            [
                '_id' => new MongoId('5c783d4cd510f9635ad4a6b3'),
                'name' => 'Authorization Branch',
                'namespace' => 'glofox',
                'active' => true,
                'features' => [
                    'general' => [
                        'login_method' => 'email',
                    ],
                ],
                'configuration' => [
                    'formats' => [
                        'date' => 'YYYY/MM/DD',
                        'time' => 'HH:mm',
                    ],
                ],
            ],

            [
                '_id' => new MongoId('5c783d4cd510f9635ad4a6b6'),
                'name' => 'Glofox',
                'namespace' => 'glofox',
                'active' => true,
                'gantner_token' => 'gantner-token-123',
                'address' => [
                    'street' => '88 Ranelagh, Ireland',
                    'city' => 'Dublin',
                    'state' => 'Dublin',
                    'country' => 'Ireland',
                    'country_code' => 'IE',
                    'district' => 'Dublin',
                    'latitude' => 53.32456910000001,
                    'longitude' => -6.252675100000033,
                    'currency' => 'EUR',
                    'continent' => 'EU',
                    'timezone_id' => 'Europe/Dublin',
                    'timezone_name' => 'Greenwich Mean Time',
                    'location' => [
                        'lat' => 53.3245691,
                        'lng' => -6.2526751,
                    ],
                ],
                'tutorial' => [
                    'status' => 'completed', //Values skipped,completed,incomplete from OnboardingStatus Enum
                    'steps' => [
                        'memberships' => 'completed', //Values skipped,completed,incomplete from OnboardingStatus Enum
                        'programs' => 'completed', //Values skipped,completed,incomplete from OnboardingStatus Enum
                        'payments' => 'completed', //Values skipped,completed,incomplete from OnboardingStatus Enum
                        'live' => 'completed',  //Values skipped,completed,incomplete from OnboardingStatus Enum
                    ],
                ],
                'categories' => [],
                'class_policy' => 'A policy',
                'configuration' => [
                    'app' => [
                        'layout' => [
                            'header_color' => 'rgb(0, 0, 0)',
                            'body_color' => 'rgb(255, 255, 255)',
                            'footer_color' => 'rgb(0, 0, 0)',
                        ],
                    ],
                    'only_payg' => false,
                    'formats' => [
                        'date' => 'YYYY/MM/DD',
                        'time' => 'HH:mm',
                    ],
                    'webportal' => [
                        'colors' => [
                            'background' => '#00000',
                            'accent' => '#00000',
                            'text' => '#00000',
                        ],
                        'features' => [
                            'general' => [
                                'classes' => true,
                            ],
                        ],
                    ],
                    'sales_tax' => [
                        'tax_number' => 'tax-number',
                        'legal_name' => 'legal-name',
                    ],
                    'fiscal' => [
                        'enabled' => false,
                        'required' => false,
                    ],
                ],
                'email' => '<EMAIL>',
                'email_notifications' => [
                    'new_member' => true,
                    'weekly_expired' => true,
                    'members_expiring' => true,
                ],
                'facebook' => 'www.facebook.com/Weareglofox',
                'features' => [
                    'classes' => [
                        'levels' => 'advanced,all,beginner',
                        'weeks_display' => 4,
                        'order' => 0,
                        'enabled' => true,
                    ],
                    'trainers' => [
                        'enabled' => true,
                        'order' => 3,
                        'booking_weeks_display' => 0,
                        'booking_cancel_window' => 0,
                        'booking_open_window' => 0,
                        'booking_time_slot_length' => 0,
                        'booking_enabled' => false,
                        'booking_close_window' => 0,
                    ],
                    'news' => [
                        'enabled' => true,
                        'order' => 2,
                    ],
                    'club_info' => [
                        'enabled' => true,
                        'order' => 5,
                    ],
                    'facilities' => [
                        'enabled' => true,
                        'order' => 4,
                        'booking' => [
                            'enabled' => true,
                        ],
                        'booking_weeks_display' => 1,
                        'booking_cancel_window' => 1,
                        'booking_open_window' => 24,
                        'booking_time_slot_length' => 0,
                        'booking_enabled' => true,
                        'booking_close_window' => 3,
                    ],
                    'booking' => [
                        'enabled' => true,
                        'strike_system' => [
                            'enabled' => true,
                            'max_strikes' => '3',
                        ],
                        'booking_cancel_window' => 12,
                        'order' => 1,
                        'booking_open_window' => 0,
                        'waiting_list_enabled' => true,
                        'waiting_list' => 9,
                        'cancel_credit_option' => 'model_id',
                        'refunded_credit_expiry' => 0,
                        'guest_bookings' => 2,
                        'auto_booking_enabled' => false,
                    ],
                    'general' => [
                        'login_method' => 'email',
                        'private_access' => false,
                    ],
                    'memberships' => [
                        'payg' => [
                            'enabled' => true,
                        ],
                        'time' => [
                            'enabled' => true,
                        ],
                        'num_classes' => [
                            'enabled' => true,
                        ],
                        'time_classes' => [
                            'enabled' => true,
                        ],
                        'enabled' => true,
                        'order' => 6,
                    ],
                    'stripe_payment' => [
                        'enabled' => true,
                        'currency' => 'eur',
                        'charge_percentage' => 1.0,
                        'pay_gym' => false,
                        'pay_app' => false,
                        'cash_reciepts' => true,
                        'livemode' => false,
                        'access_token' => 'sk_test_8xZ2NfRPdV3NlB8iF7xavc2e',
                        'refresh_token' => 'rt_4upz4yFQ0RueEG2Oja1bB924NF8oP5kGQkmsmFp92lbM0ksF',
                        'publishable_key' => 'pk_test_wCO19vGTC8fbfB2CRJeDh95r',
                        'user_id' => 'acct_14gEdTB6RJo2Q3iN',
                    ],
                    'store' => [
                        'enabled' => true,
                        'order' => 7,
                    ],
                    'courses' => [
                        'enabled' => true,
                        'order' => 8,
                        'guest_bookings' => 0,
                    ],
                    'gympass' => [
                        'id' => '445014',
                        'product_id' => '386612',
                        'pass_type_number' => '',
                        'client_id' => 'gf-test-account',
                        'client_secret' => 'GYMPASS-CLIENT-SECRET',
                        'validation_api_auth_token' => 'GYMPASS-VALIDATION-API-AUTH-TOKEN',
                        'order' => 15,
                    ],
                ],
                'phone' => '1234567',
                'push_config' => [
                    'release' => [
                        'ios' => [
                            'password' => 'ishouldnotbehere',
                        ],
                    ],
                ],
                'stripe_customer_id' => 'cus_9I1hNZpUOs5F7L',
                'timezone' => 'Europe/Ireland',
                'twitter' => '@weareglofox',
                'uses_iframe' => false,
                'uses_profile_pictures' => false,
                'website' => 'www.glofox.com',
                'working_hours' => 'Our classes run from 6am to 10pm daily with something to suit everybody!',
                'mailchimp' => [
                    'apiKey' => 'test-api-key',
                    'defaultListId' => 'test-list-id',
                ],
                'opening_times' => [
                    [
                        'dow' => 'MO',
                        'start' => '06:00',
                        'end' => '22:00',
                        'is_open' => true,
                    ],
                    [
                        'dow' => 'TU',
                        'start' => '06:00',
                        'end' => '22:00',
                        'is_open' => true,
                    ],
                    [
                        'dow' => 'WE',
                        'start' => '06:00',
                        'end' => '22:00',
                        'is_open' => true,
                    ],
                    [
                        'dow' => 'TH',
                        'start' => '06:00',
                        'end' => '22:00',
                        'is_open' => true,
                    ],
                    [
                        'dow' => 'FR',
                        'start' => '06:00',
                        'end' => '22:00',
                        'is_open' => true,
                    ],
                    [
                        'dow' => 'SA',
                        'start' => '06:00',
                        'end' => '22:00',
                        'is_open' => true,
                    ],
                    [
                        'dow' => 'SU',
                        'start' => '06:00',
                        'end' => '22:00',
                        'is_open' => true,
                    ],
                ],
                'closing_times' => [
                    [
                        'dow' => [
                            ['label' => 'MON', 'id' => 1],
                            ['label' => 'TUE', 'id' => 2],
                            ['label' => 'WED', 'id' => 3],
                            ['label' => 'THU', 'id' => 4],
                            ['label' => 'FRI', 'id' => 5],
                            ['label' => 'SAT', 'id' => 6],
                            ['label' => 'SUN', 'id' => 0],
                        ],
                        'start' => date('Y-m-d', strtotime('+1 day')), //"2016-12-24T16:50:10.403Z",
                        'end' => date('Y-m-d', strtotime('+1 day')),
                        'all_day' => true,
                        'title' => '',
                        'has_bookings' => false,
                    ],
                    [
                        'dow' => [
                            ['label' => 'MON', 'id' => 1],
                            ['label' => 'TUE', 'id' => 2],
                            ['label' => 'WED', 'id' => 3],
                            ['label' => 'THU', 'id' => 4],
                            ['label' => 'FRI', 'id' => 5],
                            ['label' => 'SAT', 'id' => 6],
                            ['label' => 'SUN', 'id' => 0],
                        ],
                        'start' => date('Y-m-d', strtotime('+3 day')),
                        'end' => date('Y-m-d', strtotime('+5 day')),
                        'all_day' => true,
                        'title' => '',
                        'has_bookings' => false,
                    ],
                ],
            ],

            // ClassPass Integration Test
            [
                '_id' => new MongoId('5addc25383266f65abf515c4'),
                'name' => 'ClassPass Integration Branch',
                'namespace' => 'classpass_integrated',
                'active' => true,
                'address' => [
                    'street' => '66 Ranelagh Road, Ireland',
                    'city' => 'Dublin',
                    'state' => 'Dublin',
                    'country' => 'Ireland',
                    'country_code' => 'IE',
                    'district' => 'Dublin',
                    'latitude' => 53.32456910000001,
                    'longitude' => -6.252675100000033,
                    'currency' => 'EUR',
                    'continent' => 'EU',
                    'timezone_id' => 'Europe/Dublin',
                    'timezone_name' => 'Greenwich Mean Time',
                    'location' => [
                        'lat' => 53.3245691,
                        'lng' => -6.2526751,
                    ],
                ],
                'push_config' => [
                    'release' => [
                        'ios' => [
                            'password' => 'senstive-data',
                        ],
                    ],
                ],
                'categories' => [],
                'class_policy' => 'A policy',
                'configuration' => [
                    'only_payg' => false,
                    'formats' => [
                        'date' => 'YYYY/MM/DD',
                        'time' => 'HH:mm',
                    ],
                ],
                'email' => '<EMAIL>',
                'email_notifications' => [
                    'new_member' => false,
                    'weekly_expired' => false,
                    'members_expiring' => false,
                ],
                'features' => [
                    'gympass' => [
                        'id' => '12345',
                        'pass_type_number' => '1',
                        'product_id' => '456',
                        'validation_api_auth_token' => '123',
                        'client_id' => '2',
                        'client_secret' => '3',
                    ],
                    'classes' => [
                        'levels' => 'advanced,all,beginner',
                        'weeks_display' => 4,
                        'order' => 0,
                        'enabled' => true,
                    ],
                    'trainers' => [
                        'enabled' => true,
                        'order' => 3,
                    ],
                    'facilities' => [
                        'enabled' => true,
                        'order' => 4,
                    ],
                    'booking' => [
                        'enabled' => true,
                        'strike_system' => [
                            'enabled' => true,
                            'max_strikes' => '3',
                        ],
                        'booking_cancel_window' => 12,
                        'order' => 1,
                        'booking_open_window' => 0,
                        'waiting_list_enabled' => true,
                        'waiting_list' => 9,
                        'cancel_credit_option' => 'model_id',
                        'refunded_credit_expiry' => 100,
                        'guest_bookings' => 2,
                        'auto_booking_enabled' => false,
                    ],
                    'general' => [
                        'login_method' => 'email',
                        'private_access' => false,
                    ],
                    'memberships' => [
                        'payg' => [
                            'enabled' => true,
                        ],
                        'time' => [
                            'enabled' => true,
                        ],
                        'num_classes' => [
                            'enabled' => true,
                        ],
                        'time_classes' => [
                            'enabled' => true,
                        ],
                        'enabled' => true,
                        'order' => 6,
                    ],
                    'stripe_payment' => [
                        'enabled' => true,
                        'currency' => 'eur',
                        'charge_percentage' => 1.0,
                        'pay_gym' => false,
                        'pay_app' => false,
                        'cash_reciepts' => true,
                        'livemode' => false,
                        'access_token' => 'sk_test_8xZ2NfRPdV3NlB8iF7xavc2e',
                        'refresh_token' => 'rt_4upz4yFQ0RueEG2Oja1bB924NF8oP5kGQkmsmFp92lbM0ksF',
                        'publishable_key' => 'pk_test_wCO19vGTC8fbfB2CRJeDh95r',
                        'user_id' => 'acct_14gEdTB6RJo2Q3iN',
                    ],
                    'courses' => [
                        'enabled' => true,
                        'order' => 8,
                        'guest_bookings' => 0,
                    ],
                ],
                'phone' => '1234567',
                'stripe_customer_id' => 'cus_9I1hNZpUOs5F7L',
                'timezone' => 'Europe/Ireland',

                'payments' => [
                    'stripe_custom' => null,
                ],
            ],
            // Branch Using Pounds
            [
                '_id' => new MongoId('5b5b265bce2cf6bdb9ad6d9a'),
                'name' => 'Branch Using Pounds',
                'namespace' => 'ukclient',
                'active' => true,
                'address' => [
                    'street' => '',
                    'city' => '',
                    'state' => '',
                    'country' => 'United Kingdom',
                    'country_code' => 'GB',
                    'district' => '',
                    'latitude' => 55.378051,
                    'longitude' => -3.43597299999999,
                    'currency' => 'GBP',
                    'continent' => 'EU',
                    'timezone_id' => 'Europe/London',
                    'timezone_name' => 'Europe/London',
                    'location' => [
                        'lat' => 53.3245691,
                        'lng' => -6.2526751,
                    ],
                ],
                'categories' => [],
                'class_policy' => 'A policy',
                'configuration' => [
                    'only_payg' => false,
                    'formats' => [
                        'date' => 'YYYY/MM/DD',
                        'time' => 'HH:mm',
                    ],
                ],
                'email' => '<EMAIL>',
                'email_notifications' => [
                    'new_member' => false,
                    'weekly_expired' => false,
                    'members_expiring' => false,
                ],
                'features' => [
                    'classes' => [
                        'levels' => 'advanced,all,beginner',
                        'weeks_display' => 4,
                        'order' => 0,
                        'enabled' => true,
                    ],
                    'trainers' => [
                        'enabled' => true,
                        'order' => 3,
                    ],
                    'facilities' => [
                        'enabled' => true,
                        'order' => 4,
                    ],
                    'booking' => [
                        'enabled' => true,
                        'strike_system' => [
                            'enabled' => true,
                            'max_strikes' => '3',
                        ],
                        'booking_cancel_window' => 12,
                        'order' => 1,
                        'booking_open_window' => 0,
                        'waiting_list_enabled' => true,
                        'waiting_list' => 9,
                        'cancel_credit_option' => 'model_id',
                        'refunded_credit_expiry' => 0,
                        'guest_bookings' => 2,
                        'auto_booking_enabled' => false,
                    ],
                    'general' => [
                        'login_method' => 'email',
                        'private_access' => false,
                    ],
                    'memberships' => [
                        'payg' => [
                            'enabled' => true,
                        ],
                        'time' => [
                            'enabled' => true,
                        ],
                        'num_classes' => [
                            'enabled' => true,
                        ],
                        'time_classes' => [
                            'enabled' => true,
                        ],
                        'enabled' => true,
                        'order' => 6,
                    ],
                    'stripe_payment' => [
                        'enabled' => true,
                        'currency' => 'eur',
                        'charge_percentage' => 1.0,
                        'pay_gym' => false,
                        'pay_app' => false,
                        'cash_reciepts' => true,
                        'livemode' => false,
                        'access_token' => 'sk_test_8xZ2NfRPdV3NlB8iF7xavc2e',
                        'refresh_token' => 'rt_4upz4yFQ0RueEG2Oja1bB924NF8oP5kGQkmsmFp92lbM0ksF',
                        'publishable_key' => 'pk_test_wCO19vGTC8fbfB2CRJeDh95r',
                        'user_id' => 'acct_14gEdTB6RJo2Q3iN',
                    ],
                    'courses' => [
                        'enabled' => true,
                        'order' => 8,
                        'guest_bookings' => 0,
                    ],
                ],
                'phone' => '1234567',
                'stripe_customer_id' => 'cus_9I1hNZpUOs5F7L',
                'timezone' => 'Europe/London',
                'payments' => [
                    'stripe_custom' => null,
                ],
            ],
            // Australian Branch
            [
                '_id' => new MongoId('5ce808750d35f24839739959'),
                'name' => 'Australian Branch',
                'namespace' => 'glofox',
                'active' => true,
                'address' => [
                    'timezone_id' => 'Australia/Brisbane',
                    'timezone_name' => 'Australia/Brisbane',
                ],
            ],
            // LA Time Branch
            [
                '_id' => new MongoId('5ce808750d35f24839739957'),
                'name' => 'LA Time Branch',
                'namespace' => 'glofox',
                'active' => true,
                'address' => [
                    'timezone_id' => 'America/Los_Angeles',
                    'timezone_name' => 'America/Los_Angeles',
                ],
            ],
            // Branch in Pacific/Rarotonga the longest timezone pre UTC
            [
                '_id' => new MongoId('5d56d214be51ef060e38408c'),
                'name' => 'Rarotonga Branch',
                'namespace' => 'glofox',
                'active' => true,
                'address' => [
                    'timezone_id' => 'Pacific/Rarotonga',
                    'timezone_name' => 'Pacific/Rarotonga',
                ],
            ],
            // Branch in Pacific/Apia the longest timezone post UTC
            [
                '_id' => new MongoId('5d56d214be51ef060f38408c'),
                'name' => 'Apia Branch',
                'namespace' => 'glofox',
                'active' => true,
                'address' => [
                    'timezone_id' => 'Pacific/Apia',
                    'timezone_name' => 'Pacific/Apia',
                ],
            ],
            [
                '_id' => new MongoId('5d56d214be51ef060e38409c'),
                'namespace' => 'failed-pmts',
                'name' => 'Failed payments',
                'active' => true,
            ],
            $this->createBranch([
                '_id' => new MongoId('5e9ed069e0a7da003254711d'),
            ]),
            $this->createBranch([
                '_id' => new MongoId('49a7011a05c677b9a916613b'),
            ]),
            [
                '_id' => new MongoId('6103fc36458c6e278e9863d3'),
                'name' => 'Personal Tranining Branch',
                'namespace' => 'glofox',
                'active' => true,
                'features' => [
                    'trainers' => [
                        'booking_enabled' => true,
                    ],
                    'timezone_name' => 'Australia/Brisbane',
                ],
            ],
            [
                '_id' => new MongoId('6103f87afb7e569994a13036'),
                'name' => 'Personal Tranining Branch',
                'namespace' => 'glofox',
                'active' => true,
                'features' => [
                    'trainers' => [
                        'booking_enabled' => false,
                    ],
                    'timezone_name' => 'Australia/Brisbane',
                ],
            ],
            [
                '_id' => new MongoId('61234de2be6a046c3186db0d'),
                'name' => 'Mailchimp Webhook Test Branch',
                'namespace' => 'glofox',
                'active' => true,
                'mailchimp' => [
                    'apiKey' => 'test-api-key',
                    'defaultListId' => 'test-list-id',
                ],
                'configuration' => [
                    'formats' => [
                        'date' => 'YYYY/MM/DD',
                    ],
                ],
            ],
            [
                '_id' => new MongoId('61c59e525f7ab689af46ccfb'),
                'name' => 'Timezone Brazilian Test Branch',
                'namespace' => 'glofox',
                'active' => true,
                'address' => [
                    'timezone_id' => 'America/Sao_Paulo',
                    'timezone_name' => 'Brasilia Standard Time',
                ],
            ],
            [
                '_id' => new MongoId('6279159a1d7cc351e50674bb'),
                'name' => 'Midnight Test Branch',
                'namespace' => 'midnightb',
                'active' => true,
                'address' => [
                    'timezone_id' => 'Africa/Cairo',
                    'timezone_name' => 'Africa/Cairo',
                ],
            ],
        ];

        $this->records = array_merge(
            $this->records,
            $this->forPaymentIntent(),
            $this->forSalesTax(),
            $this->forCsvTransactionReport(),
            $this->forIntegrators(),
            $this->forStripeDD(),
            $this->forVirtualIsOnlineFieldOnEvents(),
            $this->forBranchUpdateCertainFields(),
            $this->forDeactivatedBranch(),
            $this->forLeadsFilterTest(),
            $this->forTerminalPOS(),
            $this->forBookingsMetadata(),
            $this->forAccessesReport(),
            $this->forMinimumAgeTest(),
            $this->forListAllBookingsActionTest(),
            $this->forVirtualSlotsTest(),
            $this->forBookingWindow(),
            $this->forPayroll(),
            $this->forAccessesList(),
            $this->forRestoreUser(),
            $this->forAnalyticsReports(),
            $this->forExecuteBookingCharge(),
            $this->forFindBranchesBySearchCriteria(),
            $this->forHasAccessToBranch(),
            $this->forInteractions(),
            $this->forBranchesUpsert(),
            $this->forEventGeneratorEdgeTest(),
            $this->forGympassPaygDisabled(),
            $this->forEventDeletedEventHandle(),
            $this->forGrantCreditsOnImport(),
            $this->forBranchesWithCorporateId(),
            $this->forEventsUpdate(),
            $this->forGetAllBookings(),
            $this->forLeadSources(),
            $this->forGetMembers(),
            $this->forEventGeneratorComponent(),
            $this->forProgramUpsertService(),
            $this->forNicepayProvider(),
            $this->forGetFacilitiesByLocationId(),
            $this->forBranchWithNoFacilities()
        );

        parent::init();
    }

    private function forEventGeneratorEdgeTest(): array
    {
        return [
            [
                '_id' => new MongoId('4fa7011a05c677bfa9166121'),
                'name' => 'EventGeneratorEdgeTest',
                'namespace' => 'glofox',
                'active' => true,
                'features' => [
                    'classes' => [
                        'weeks_display' => 1,
                    ],
                ],
            ],
        ];
    }
    private function forPaymentIntent(): array
    {
        return [
            [
                '_id' => new MongoId('5d6868a48d8540851fe6066c'),
                'namespace' => 'payment-intent',
                'name' => 'Payment Intent Studio',
            ],
            [
                '_id' => new MongoId('5d6868a48d8540851fe6077c'),
                'namespace' => 'payment-intent',
                'name' => 'Payment Premium Studio',
                'stripe_plan_code' => 'platinum',
            ],
        ];
    }

    private function forSalesTax(): array
    {
        return [
            [
                '_id' => new MongoId('5d9cdc06aad495538f8102a4'),
                'namespace' => 'sales-tax',
                'name' => 'Sales Tax Studio',
                'active' => true,
            ],
        ];
    }

    private function forCsvTransactionReport(): array
    {
        return [
            [
                '_id' => new MongoId('5db1b008f15a8f08c04f28ae'),
                'namespace' => 'csv-transaction-report',
                'name' => 'CSV',
                'active' => true,
                'address' => [
                    'currency' => 'EUR',
                    'continent' => 'EU',
                    'timezone_id' => 'Europe/Dublin',
                    'timezone_name' => 'Greenwich Mean Time',
                ],
                'configuration' => [
                    'formats' => [
                        'date' => 'YYYY/MM/DD',
                        'time' => 'HH:mm',
                    ],
                    'fiscal' => [
                        'member_tax_id_options' => [
                            'enabled' => true,
                        ],
                    ],
                ],
            ],
        ];
    }

    private function forIntegrators(): array
    {
        return [
            [
                '_id' => new MongoId('5d9cdc06aad495538f810244'),
                'namespace' => 'restricted',
                'name' => 'Integrator restriction',
                'active' => true,
                'address' => [
                    'street' => '',
                    'city' => '',
                    'state' => '',
                    'country' => 'United Kingdom',
                    'country_code' => 'IE',
                    'district' => '',
                    'latitude' => 55.378051,
                    'longitude' => -3.43597299999999,
                    'currency' => 'EUR',
                    'continent' => 'EU',
                    'timezone_id' => 'Europe/London',
                    'timezone_name' => 'Europe/London',
                    'location' => [
                        'lat' => 53.3245691,
                        'lng' => -6.2526751,
                    ],
                ],
            ],
        ];
    }

    private function forStripeDD(): array
    {
        return [
            [
                '_id' => new MongoId('49a7011a08c677b9a916612a'),
                'name' => 'Stripe DD',
                'namespace' => 'stripedd',
                'active' => true,
                'gantner_token' => 'gantner-token-123',
                'address' => [
                    'street' => 'Avenida Eusebio da Silva Ferreira',
                    'city' => 'Lisboa',
                    'state' => 'Lisboa',
                    'country' => 'Portugal',
                    'country_code' => 'PT',
                    'district' => 'Lisboa',
                    'latitude' => 53.32456910000001,
                    'longitude' => -6.252675100000033,
                    'currency' => 'EUR',
                    'continent' => 'EU',
                    'timezone_id' => 'Europe/Dublin',
                    'timezone_name' => 'Greenwich Mean Time',
                    'location' => [
                        'lat' => 53.3245691,
                        'lng' => -6.2526751,
                    ],
                ],
            ],
            [
                '_id' => new MongoId('49a7011a08c677111916613a'),
                'name' => 'Stripe DD Sepa',
                'namespace' => 'stripeddsepa',
                'active' => true,
                'gantner_token' => 'gantner-token-123',
                'address' => [
                    'street' => '1 Patrick Street',
                    'city' => 'Amsterdam',
                    'state' => 'Amsterdam',
                    'country' => 'Netherlands',
                    'country_code' => 'NL',
                    'district' => 'Amsterdam',
                    'latitude' => 53.32456910000001,
                    'longitude' => -6.252675100000033,
                    'currency' => 'EUR',
                    'continent' => 'EU',
                    'timezone_id' => 'Europe/Amsterdam',
                    'timezone_name' => 'Greenwich Mean Time',
                    'location' => [
                        'lat' => 53.3245691,
                        'lng' => -6.2526751,
                    ],
                ],
            ],
            [
                '_id' => new MongoId('49a7011a08c845111916613c'),
                'name' => 'Stripe Card Only',
                'namespace' => 'stripecard',
                'active' => true,
                'gantner_token' => 'gantner-token-123',
                'address' => [
                    'street' => '1 Patrick Street',
                    'city' => 'Amsterdam',
                    'state' => 'Amsterdam',
                    'country' => 'Bulgaria',
                    'country_code' => 'BG',
                    'district' => 'Amsterdam',
                    'latitude' => 53.32456910000001,
                    'longitude' => -6.252675100000033,
                    'currency' => 'EUR',
                    'continent' => 'EU',
                    'timezone_id' => 'Europe/Amsterdam',
                    'timezone_name' => 'Greenwich Mean Time',
                    'location' => [
                        'lat' => 53.3245691,
                        'lng' => -6.2526751,
                    ],
                ],
            ],
            [
                '_id' => new MongoId('49a7011a08c845111916614d'),
                'name' => 'Stripe DD ACSS',
                'namespace' => 'stripeddacss',
                'active' => true,
                'gantner_token' => 'gantner-token-123',
                'address' => [
                    'street' => '1 Patrick Street',
                    'city' => 'Toronto',
                    'state' => '',
                    'country' => 'Canada',
                    'country_code' => 'CA',
                    'district' => 'Toronto',
                    'latitude' => 43.653226,
                    'longitude' => -79.3831843,
                    'currency' => 'CAD',
                    'continent' => 'NA',
                    'timezone_id' => 'America/Toronto',
                    'timezone_name' => 'America/Toronto',
                    'location' => [
                        'lat' => 43.653226,
                        'lng' =>  -79.3831843,
                    ],
                ],
            ], 
        ];
    }

    private function forTerminalPOS(): array
    {
        return [
            [
                '_id' => new MongoId('59a7011a02c677bda916612c'),
                'name' => 'Terminal POS',
                'namespace' => 'terminalpos',
                'active' => true,
            ],
        ];
    }

    private function forVirtualIsOnlineFieldOnEvents(): array
    {
        return [
            [
                '_id' => new MongoId('5e7cb6a53d36311bf52c63d8'),
                'namespace' => 'namespace-5e7cb6a53d36311bf52c63d8',
                'name' => 'Online Studio',
                'active' => true,
            ],
        ];
    }

    private function forBranchUpdateCertainFields(): array
    {
        return [
            [
                '_id' => new MongoId('5d6868a48d8540851fe6066e'),
                'namespace' => 'branch-update',
                'active' => true,
                'name' => 'BranchUpdateCertainFields',
                'address' => [
                    'latitude' => 53.32456910000001,
                    'longitude' => -6.252675100000033,
                ],
            ],
        ];
    }

    private function createBranch(array $override): array
    {
        $default = [
            '_id' => new MongoId(),
            'namespace' => 'testnamespace',
            'name' => 'Random Branch',
            'active' => true,
        ];

        return array_replace_recursive($default, $override);
    }

    private function forDeactivatedBranch(): array
    {
        return [
            [
                '_id' => new MongoId('5d6868a48d8540851fe6066f'),
                'namespace' => 'branch-deactivated',
                'active' => false,
                'name' => 'BranchDeactivated',
                'address' => [
                    'latitude' => 53.32456910000001,
                    'longitude' => -6.252675100000033,
                ],
            ],
        ];
    }

    private function forLeadsFilterTest(): array
    {
        return [
            [
                '_id' => new MongoId('62e13d1731a863c5cc75d14c'),
                'name' => 'Maryna Fitness',
                'namespace' => 'marynafitness',
                'active' => true,
                'gantner_token' => 'gantner-token-123',
                'stripe_plan_code' => 'platinum',
                'address' => [
                    'street' => '88 Ranelagh, Ireland',
                    'city' => 'Dublin',
                    'state' => 'Dublin',
                    'country' => 'Ireland',
                    'country_code' => 'IE',
                    'district' => 'Dublin',
                    'latitude' => 53.32456910000001,
                    'longitude' => -6.252675100000033,
                    'currency' => 'EUR',
                    'continent' => 'EU',
                    'timezone_id' => 'Europe/Dublin',
                    'timezone_name' => 'Greenwich Mean Time',
                    'location' => [
                        'lat' => 53.3245691,
                        'lng' => -6.2526751,
                    ],
                ],
                'tutorial' => [],
                'categories' => [],
                'class_policy' => 'A policy',
                'configuration' => [
                    'app' => [
                        'layout' => [
                            'header_color' => 'rgb(0, 0, 0)',
                            'body_color' => 'rgb(255, 255, 255)',
                            'footer_color' => 'rgb(0, 0, 0)',
                        ],
                    ],
                    'only_payg' => false,
                    'formats' => [
                        'date' => 'YYYY/MM/DD',
                        'time' => 'HH:mm',
                    ],
                    'webportal' => [
                        'features' => [
                            'general' => [
                                'classes' => true,
                            ],
                        ],
                    ],
                ],
                'email' => '<EMAIL>',
                'email_notifications' => [
                    'new_member' => true,
                    'weekly_expired' => true,
                    'members_expiring' => true,
                ],
                'facebook' => 'www.facebook.com/Weareglofox',
                'features' => [
                    'classes' => [
                        'levels' => 'advanced,all,beginner',
                        'weeks_display' => 4,
                        'order' => 0,
                        'enabled' => true,
                    ],
                    'trainers' => [
                        'enabled' => true,
                        'order' => 3,
                        'booking_weeks_display' => 0,
                        'booking_cancel_window' => 0,
                        'booking_open_window' => 0,
                        'booking_time_slot_length' => 0,
                        'booking_enabled' => false,
                        'booking_close_window' => 0,
                    ],
                    'news' => [
                        'enabled' => true,
                        'order' => 2,
                    ],
                    'club_info' => [
                        'enabled' => true,
                        'order' => 5,
                    ],
                    'facilities' => [
                        'enabled' => true,
                        'order' => 4,
                        'booking' => [
                            'enabled' => true,
                        ],
                        'booking_weeks_display' => 1,
                        'booking_cancel_window' => 1,
                        'booking_open_window' => 24,
                        'booking_time_slot_length' => 0,
                        'booking_enabled' => true,
                        'booking_close_window' => 3,
                    ],
                    'booking' => [
                        'enabled' => true,
                        'strike_system' => [
                            'enabled' => true,
                            'max_strikes' => '3',
                        ],
                        'booking_cancel_window' => 12,
                        'order' => 1,
                        'booking_open_window' => 0,
                        'waiting_list_enabled' => true,
                        'waiting_list' => 9,
                        'cancel_credit_option' => 'model_id',
                        'refunded_credit_expiry' => 0,
                        'guest_bookings' => 2,
                        'auto_booking_enabled' => false,
                    ],
                    'general' => [
                        'login_method' => 'email',
                        'private_access' => false,
                    ],
                    'memberships' => [
                        'payg' => [
                            'enabled' => true,
                        ],
                        'time' => [
                            'enabled' => true,
                        ],
                        'num_classes' => [
                            'enabled' => true,
                        ],
                        'time_classes' => [
                            'enabled' => true,
                        ],
                        'enabled' => true,
                        'order' => 6,
                    ],
                    'stripe_payment' => [
                        'enabled' => true,
                        'currency' => 'eur',
                        'charge_percentage' => 1.0,
                        'pay_gym' => false,
                        'pay_app' => false,
                        'cash_reciepts' => true,
                        'livemode' => false,
                        'access_token' => 'sk_test_8xZ2NfRPdV3NlB8iF7xavc2e',
                        'refresh_token' => 'rt_4upz4yFQ0RueEG2Oja1bB924NF8oP5kGQkmsmFp92lbM0ksF',
                        'publishable_key' => 'pk_test_wCO19vGTC8fbfB2CRJeDh95r',
                        'user_id' => 'acct_14gEdTB6RJo2Q3iN',
                    ],
                    'gympass' => [
                        'id' => '445014',
                        'product_id' => '386612',
                        'pass_type_number' => '',
                        'client_id' => 'gf-test-account',
                        'client_secret' => 'GYMPASS-CLIENT-SECRET',
                        'validation_api_auth_token' => 'GYMPASS-VALIDATION-API-AUTH-TOKEN',
                        'order' => 15,
                    ],
                    'store' => [
                        'enabled' => true,
                        'order' => 7,
                    ],
                    'courses' => [
                        'enabled' => true,
                        'order' => 8,
                        'guest_bookings' => 0,
                    ],
                ],
                'phone' => '1234567',
                'push_config' => [
                    'release' => [
                        'ios' => [
                            'password' => 'senstive-data',
                        ],
                    ],
                ],
                'stripe_customer_id' => 'cus_9I1hNZpUOs5F7L',
                'timezone' => 'Europe/Ireland',
                'twitter' => '@weareglofox',
                'uses_iframe' => false,
                'uses_profile_pictures' => false,
                'website' => 'www.glofox.com',
                'working_hours' => 'Our classes run from 6am to 10pm daily with something to suit everybody!',
                'mailchimp' => [
                    'apiKey' => 'test-api-key',
                    'defaultListId' => 'test-list-id',
                ],
                'opening_times' => [
                    [
                        'dow' => 'MO',
                        'start' => '06:00',
                        'end' => '22:00',
                        'is_open' => true,
                    ],
                    [
                        'dow' => 'TU',
                        'start' => '06:00',
                        'end' => '22:00',
                        'is_open' => true,
                    ],
                    [
                        'dow' => 'WE',
                        'start' => '06:00',
                        'end' => '22:00',
                        'is_open' => true,
                    ],
                    [
                        'dow' => 'TH',
                        'start' => '06:00',
                        'end' => '22:00',
                        'is_open' => true,
                    ],
                    [
                        'dow' => 'FR',
                        'start' => '06:00',
                        'end' => '22:00',
                        'is_open' => true,
                    ],
                    [
                        'dow' => 'SA',
                        'start' => '06:00',
                        'end' => '22:00',
                        'is_open' => true,
                    ],
                    [
                        'dow' => 'SU',
                        'start' => '06:00',
                        'end' => '22:00',
                        'is_open' => true,
                    ],
                ],
                'closing_times' => [
                    [
                        'dow' => [
                            ['label' => 'MON', 'id' => 1],
                            ['label' => 'TUE', 'id' => 2],
                            ['label' => 'WED', 'id' => 3],
                            ['label' => 'THU', 'id' => 4],
                            ['label' => 'FRI', 'id' => 5],
                            ['label' => 'SAT', 'id' => 6],
                            ['label' => 'SUN', 'id' => 0],
                        ],
                        'start' => date('Y-m-d', strtotime('+1 day')), //"2016-12-24T16:50:10.403Z",
                        'end' => date('Y-m-d', strtotime('+1 day')),
                        'all_day' => true,
                        'title' => '',
                        'has_bookings' => false,
                    ],
                    [
                        'dow' => [
                            ['label' => 'MON', 'id' => 1],
                            ['label' => 'TUE', 'id' => 2],
                            ['label' => 'WED', 'id' => 3],
                            ['label' => 'THU', 'id' => 4],
                            ['label' => 'FRI', 'id' => 5],
                            ['label' => 'SAT', 'id' => 6],
                            ['label' => 'SUN', 'id' => 0],
                        ],
                        'start' => date('Y-m-d', strtotime('+3 day')),
                        'end' => date('Y-m-d', strtotime('+5 day')),
                        'all_day' => true,
                        'title' => '',
                        'has_bookings' => false,
                    ],
                ],
            ],
        ];
    }

    private function forBookingsMetadata(): array
    {
        return [
            [
                '_id' => new MongoId('633ad68b69f7280b4564aee3'),
                'name' => 'Bookings metadata',
                'namespace' => 'bookingsmetadata',
                'active' => true,
            ],
        ];
    }

    private function forAccessesReport(): array
    {
        return [
            [
                '_id' => new MongoId('6364be7488385df39f3a70b0'),
                'name' => 'Accesses Report Branch',
                'namespace' => 'accessesreport',
                'active' => true,
            ],
        ];
    }

    private function forAccessesList(): array
    {
        return [
            [
                '_id' => new MongoId('64ee4ec06753d53819a72923'),
                'name' => 'Accesses List Branch',
                'namespace' => 'accesseslist',
                'active' => true,
            ],
            [
                '_id' => new MongoId('64ee4ec7261028d1afc99d0b'),
                'name' => 'Accesses List Branch Roaming',
                'namespace' => 'accesseslist',
                'active' => true,
            ],
        ];
    }

    private function forMinimumAgeTest(): array
    {
        return [
            [
                '_id' => new MongoId('6364be7488385df39f3a7015'),
                'name' => 'Minimum Age Branch',
                'namespace' => 'minimumagebranch',
                'active' => true,
                'features' => [
                    'general' => [
                        'minimum_age' => 15,
                    ],
                ],
            ],
        ];
    }

    private function forListAllBookingsActionTest(): array
    {
        return [
            [
                '_id' => new MongoId('6435563a6272b68401e85728'),
                'name' => 'List All Bookings Branch',
                'namespace' => 'listallbookings',
                'active' => true,
            ],
        ];
    }

    private function forVirtualSlotsTest(): array
    {
        return [
            [
                '_id' => new MongoId('6461ed6fdef3e8f4c6d4b00c'),
                'name' => 'Virtual slots test',
                'namespace' => 'virtualslotstest',
                'active' => true,
                'address' => [
                    'street' => 'Test, Spain',
                    'city' => 'Barcelona',
                    'state' => 'Barcelona',
                    'country' => 'Spain',
                    'country_code' => 'SP',
                    'currency' => 'EUR',
                    'continent' => 'EU',
                    'timezone_id' => 'Europe/Madrid',
                    'timezone_name' => 'Europe/Madrid',
                ],
            ],
        ];
    }

    private function forBookingWindow(): array
    {
        return [
            [
                '_id' => new MongoId('646de8d3c539cfc3cb649547'),
                'name' => 'Appointments Booking Window',
                'namespace' => 'appointmentsbookingwindow',
                'active' => true,
                'features' => [
                    'trainers' => [
                        'enabled' => true,
                        'order' => 3,
                        'booking_weeks_display' => 0,
                        'booking_cancel_window' => 0,
                        'booking_open_window' => 5,
                        'booking_time_slot_length' => 0,
                        'booking_enabled' => false,
                        'booking_close_window' => 1,
                    ],
                ],
            ],
            [
                '_id' => new MongoId('64784ace2c8a824784ab4cc4'),
                'name' => 'Class Booking Window',
                'namespace' => 'classbookingwindow',
                'active' => true,
                'address' => [
                    'timezone_id' => 'Europe/Dublin',
                    'timezone_name' => 'Europe/Dublin',
                ],
                'features' => [
                    'booking' => [
                        'enabled' => true,
                        'booking_open_window' => 168,
                        'booking_close_window' => 0,
                        'overbooking_enabled' => true,
                    ],
                ],
            ],
        ];
    }

    private function forPayroll(): array
    {
        return [
            [
                '_id' => new MongoId('64b803ccfc8ba4d6f8d99c9c'),
                'name' => 'Testing Payroll',
                'namespace' => 'payroll',
                'active' => true,
            ],
        ];
    }

    private function forRestoreUser(): array
    {
        return [
            [
                '_id' => new MongoId('652c3f13eb53aebfbb1266f9'),
                'name' => 'branch-for-restoring-user',
                'namespace' => 'a-test-namespace',
                'active' => true,
            ],
        ];
    }

    private function forAnalyticsReports(): array
    {
        return [
            [
                '_id' => new MongoId('5d794d5c7efab3813a2dfe58'),
                'name' => 'branch-for-analytics-reports',
                'namespace' => 'namespace-for-analytics-reports',
                'active' => true,
            ],
            [
                '_id' => new MongoId('5db1b008f15a8f08c04f28af'),
                'name' => 'branch-for-analytics-reports-2',
                'namespace' => 'namespace-for-analytics-reports',
                'active' => true,
            ],
            [
                '_id' => new MongoId('654b78d43d4b72f1aed44759'),
                'name' => 'branch-for-analytics-reports-3',
                'namespace' => 'namespace-for-analytics-reports',
                'active' => true,
            ],
        ];
    }

    private function forExecuteBookingCharge(): array
    {
        return [
            'Branch does not have payment by card allowed' => [
                '_id' => new MongoId('6565e7275e02799353dd1f0f'),
                'name' => 'branch-for-execute-booking-charge-1',
                'namespace' => 'namespace-for-execute-booking-charge',
                'active' => true,
            ],
            'Branch has payment by card allowed, but no card handler provided' => [
                '_id' => new MongoId('6565e781513193858ebb95bb'),
                'name' => 'branch-for-execute-booking-charge-2',
                'namespace' => 'namespace-for-execute-booking-charge',
                'active' => true,
            ],
        ];
    }

    private function forFindBranchesBySearchCriteria(): array
    {
        $date = new UTCDateTime(strtotime('2025-02-05T00:00:00Z') * 1000);
        return [
            [
                '_id' => new MongoId('6593ed5dd2b974bd736c3c58'),
                'name' => 'test-find-branches-1',
                'namespace' => 'test-find-branches-namespace-bundle-glofox',
                'active' => true,
                'created' => new MongoDate($date),
                'address' => $this->getBranchDefaultAddress(),
            ],
            [
                '_id' => new MongoId('6593ed6712533ebbe56b5afb'),
                'name' => 'test-find-branches-2',
                'namespace' => 'test-find-branches-namespace-bundle-glofox',
                'active' => true,
                'created' => new MongoDate($date),
                'address' => $this->getBranchDefaultAddress(),
            ],
            [
                '_id' => new MongoId('6594469527df91be161948bf'),
                'name' => 'test-find-branches-3',
                'namespace' => 'test-find-branches-namespace-bundle-not-glofox',
                'active' => true,
                'created' => new MongoDate($date),
                'address' => $this->getBranchDefaultAddress(),
            ],
            [
                '_id' => new MongoId('68060eb924ff28bb86c0ed8b'),
                'name' => 'test-find-branches-3',
                'namespace' => 'test-find-branches-namespace-bundle-not-glofox',
                'active' => true,
                'member_facing' => true,
                'created' => new MongoDate($date),
                'address' => $this->getBranchDefaultAddress(),
            ],
            [
                '_id' => new MongoId('680611e0c1061cf3bc719a7d'),
                'name' => 'test-find-branches-3',
                'namespace' => 'test-find-branches-namespace-bundle-not-glofox',
                'active' => true,
                'member_facing' => false,
                'created' => new MongoDate($date),
                'address' => $this->getBranchDefaultAddress(),
            ],
        ];
    }
    private function forInteractions()
    {
        return [
            [
                '_id' => new MongoId('661e8a9093731688897988c6'),
                'name' => 'test-branch-interactions',
                'namespace' => 'test-branch-interactions-namespace',
                'active' => true,
            ]
        ];
    }

    private function forHasAccessToBranch(): array
    {
        return [
            [
                '_id' => new MongoId('65f1d4f17938c94639b652c6'),
                'name' => 'has-access-to-branch-name',
                'namespace' => 'has-access-to-branch',
                'active' => true,
            ],
            [
                '_id' => new MongoId('65f1d531a689fb464251c9a7'),
                'name' => 'has-access-to-branch-name',
                'namespace' => 'has-access-to-branch',
                'active' => true,
            ],
            [
                '_id' => new MongoId('65f1d56e88a39a6b5f0a7a8e'),
                'name' => 'has-access-to-branch-name',
                'namespace' => 'has-access-to-branch-2',
                'active' => true,
            ],
        ];
    }

    private function forBranchesUpsert(): array
    {
        return [
            [
                '_id' => new MongoId('663a4c5c348b14dbfbd65e41'),
                'name' => 'test-branch-edit-namespace',
                'namespace' => 'test-branch-edit-namespace',
                'active' => true,
                'address' => [
                    'latitude' => 53.32456910000001,
                    'longitude' => -6.252675100000033,
                ],
            ]
        ];
    }

    private function forGympassPaygDisabled(): array
    {
        return [
            [
                '_id' => new MongoId('66c6f0a307ae75cf210410b9'),
                'name' => 'test-branch-gympass-payg',
                'namespace' => 'test-branch-gympass-payg-namespace',
                'active' => true,
                'address' => [
                    'latitude' => 53.32456910000001,
                    'longitude' => -6.252675100000033,
                ],
                'features' => [

                    'memberships' => [
                        'payg' => [
                            'enabled' => false,
                        ],
                    ],
                ],
            ],
        ];
    }

    private function forEventDeletedEventHandle(): array
    {
        return [
            [
                '_id' => new MongoId('666666777777888888000000'),
                'name' => 'test-branch-event-deleted',
                'namespace' => 'test-branch-event-deleted',
                'active' => true,
                'address' => [
                    'street' => 'Avenida de Andalucía, 100',
                    'city' => 'Malaga',
                    'state' => 'Malaga',
                    'country' => 'Spain',
                    'country_code' => 'SP',
                    'currency' => 'EUR',
                    'continent' => 'EU',
                    'timezone_id' => 'Europe/Madrid',
                    'timezone_name' => 'Europe/Madrid',
                ],
                'features' => [
                    'booking' => [
                        'enabled' => true,
                        'booking_open_window' => 168,
                        'booking_close_window' => 0,
                        'overbooking_enabled' => false,
                        'enabled' => true,
                        'strike_system' => [
                            'enabled' => true,
                            'max_strikes' => '3',
                        ],
                        'booking_cancel_window' => 12,
                        'order' => 1,
                        'booking_open_window' => 0,
                        'waiting_list_enabled' => true,
                        'waiting_list' => 9,
                        'cancel_credit_option' => 'model_id',
                        'refunded_credit_expiry' => 0,
                        'guest_bookings' => 2,
                        'auto_booking_enabled' => false,
                    ],
                    'memberships' => [
                        'payg' => [
                            'enabled' => false,
                        ],
                    ],
                    'gympass' => [
                        'id' => '445014',
                        'product_id' => '386612',
                        'pass_type_number' => '',
                        'client_id' => 'gf-test-account',
                        'client_secret' => 'GYMPASS-CLIENT-SECRET',
                        'validation_api_auth_token' => 'GYMPASS-VALIDATION-API-AUTH-TOKEN',
                        'order' => 15,
                    ],
                ],
            ],
        ];
    }

    private function forGrantCreditsOnImport(): array
    {
        return [
            [
                '_id' => new MongoId('99984ace2c8a824784ab4cc4'),
                'name' => 'Really remote studio',
                'namespace' => 'classbookingwindow',
                'active' => true,
                'address' => [
                    'timezone_id' => 'Pacific/Pago_Pago',
                    'timezone_name' => 'Pacific/Pago_Pago',
                ],
            ],
        ];
    }

    private function forBranchWithNoFacilities(): array
    {
        return [
            [
                '_id' => new MongoId('6836c18bf3c70c1e41a121ad'),
                'name' => 'test-branch-no-facilities',
                'namespace' => 'test-branch-no-facilities',
                'active' => true,
            ],
        ];
    }

    private function forBranchesWithCorporateId(): array
    {
        return [
            [
                '_id' => new MongoId('6594235cb31b79737d0b6c57'),
                'name' => 'test-name',
                'namespace' => 'test-find-branches-with-corporate-id',
                'active' => true,
                'corporate_id' => 'corporate_id_1'
            ],
            [
                '_id' => new MongoId('6594235cb31b79737d0b6c58'),
                'namespace' => 'test-find-branches-with-corporate-id-2',
                'active' => true,
                'corporate_id' => 'corporate_id_2'
            ],
            [
                '_id' => new MongoId('6594235cb31b79737d0b6c59'),
                'namespace' => 'test-find-branches-with-corporate-id-2',
                'active' => true,
                'corporate_id' => 'corporate_id_2'
            ],
        ];
    }

    private function forEventsUpdate(): array
    {
        return [
            [
                '_id' => new MongoId('67425b982a70f7afae14dc75'),
                'namespace' => 'events-update-plus-utc',
                'name' => 'plus-utc',
                'active' => true,
                'address' => [
                    'street' => '',
                    'city' => '',
                    'state' => '',
                    'country' => 'Finland',
                    'country_code' => 'FIN',
                    'district' => '',
                    'latitude' => 41.881832,
                    'longitude' => 24.945831,
                    'currency' => 'EUR',
                    'continent' => 'EU',
                    'timezone_id' => 'Europe/Helsinki',
                    'timezone_name' => 'Europe/Helsinki',
                    'location' => [
                        'lat' => 60.192059,
                        'lng' => 24.945831,
                    ],
                ],
            ],
            [
                '_id' => new MongoId('67425ec9a14b1b40651b4990'),
                'namespace' => 'events-update-minus-utc',
                'name' => 'minus-utc',
                'active' => true,
                'address' => [
                    'street' => '',
                    'city' => '',
                    'state' => '',
                    'country' => 'United States',
                    'country_code' => 'US',
                    'district' => '',
                    'latitude' => 41.881832,
                    'longitude' => -87.623177,
                    'currency' => 'USD',
                    'continent' => 'NA',
                    'timezone_id' => 'America/Chicago',
                    'timezone_name' => 'America/Chicago',
                    'location' => [
                        'lat' => 53.3245691,
                        'lng' => -87.623177,
                    ],
                ],
              ]
          ];
    }
  
    private function forGetAllBookings(): array
    {
        return [
            [
                '_id' => new MongoId('6745f7abd22f105c2aac5693'),
                'name' => 'get-all-bookings',
                'namespace' => 'get-all-bookings',
                'active' => true,
            ],
        ];
    }

    private function forLeadSources(): array
     {
         return [
             [
                 '_id' => new MongoId('67485c9c1b4ba3fcf196b337'),
                 'name' => 'test-lead-sources',
                 'namespace' => 'test-lead-sources',
                 'corporate_id' =>  'test-corporate-id',
                 'active' => true,
             ],
             [
                 '_id' => new MongoId('67485cbfffe518fbe2e18489'),
                 'name' => 'test-lead-sources-2',
                 'namespace' => 'test-lead-sources-2',
                 'active' => true,
             ]
         ];
    }

    private function forGetMembers(): array
    {
        return [
            [
                '_id' => new MongoId('675acdcdf1aef12a46e66bcc'),
                'name' => 'test-get-members',
                'namespace' => 'test-get-members',
                'active' => true,
            ],
        ];
    }

    private function forEventGeneratorComponent(): array
    {
        $commonDetails = $this->getBranchCommonDetails();

        return [
            array_merge($commonDetails, [
                '_id' => new MongoId('675acdcdf1aef12a46e66bcd'),
                'name' => 'EventGeneratorComponent DeletedEvents Studio 1',
                'namespace' => 'eventgeneratordeletedtest1',
            ]),
            array_merge($commonDetails, [
                '_id' => new MongoId('675acdcdf1aef12a46e66bce'),
                'name' => 'EventGeneratorComponent DeletedEvents Studio 2',
                'namespace' => 'eventgeneratordeletedtest2',
            ]),
            array_merge($commonDetails, [
                '_id' => new MongoId('675acdcdf1aef12a46e66bcf'),
                'name' => 'EventGeneratorComponent DeletedEvents Studio 3',
                'namespace' => 'eventgeneratordeletedtest3',
            ]),
            array_merge($commonDetails, [
                '_id' => new MongoId('675acdcdf1aef12a46e66bd0'),
                'name' => 'EventGeneratorComponent DeletedEvents Studio 4',
                'namespace' => 'eventgeneratordeletedtest4',
            ]),
            array_merge($commonDetails, [
                '_id' => new MongoId('675acdcdf1aef12a46e66bd1'),
                'name' => 'EventGeneratorComponent DeletedEvents Studio 5',
                'namespace' => 'eventgeneratordeletedtest5',
            ]),
        ];
    }

    private function forProgramUpsertService(): array
    {
        $commonDetails = $this->getBranchCommonDetails();

        return [
            array_merge($commonDetails, [
                '_id' => new MongoId('675acdcdf1aef12a46e66bd2'),
                'name' => 'Program Update Integral Test Branch',
                'namespace' => 'programupdateintegraltestbranch',
                'features' => [
                    'classes' => [
                        'weeks_display' => 4,
                    ],
                ],
            ]),
        ];
    }

    private function forNicepayProvider(): array
    {
        return [
            [
                '_id' => new MongoId('682b43bcfc88abbdcb0e867d'),
                'name' => 'Nicepay Test Branch',
                'namespace' => 'nicepaytestbranch',
                'active' => true,
                'address' => [
                    "city"=> "seoul,gwangjang-gu",
                    "continent"=> "AS",
                    "country"=> "Korea (the Republic of)",
                    "country_code"=> "KR",
                    "currency"=> "KRW",
                    "district"=> "seoul,gwangjang-gu",
                    "latitude"=> 37.5700398,
                    "longitude"=> 126.9996036,
                    "state"=> "korea",
                    "street"=> "123-45 apt303 building a",
                    "timezone_id"=> "Asia/Seoul",
                    "timezone_name"=> "Asia/Seoul",
                    "location"=> [
                        "lat"=> 37.5700398,
                        "lng"=> 126.9996036
                    ]
                ],
            ],
        ];
    }

    private function getBranchCommonDetails(): array
    {
        return [
            'active' => true,
            'features' => [
                'classes' => [
                    'weeks_display' => 8,
                ],
            ],
            'address' => $this->getBranchDefaultAddress(),
        ];
    }

    private function getBranchDefaultAddress(): array
    {
        return [
            'country' => 'Ireland',
            'country_code' => 'IE',
            'district' => 'Dublin',
            'latitude' => 53.32456910000001,
            'longitude' => -6.252675100000033,
            'currency' => 'EUR',
            'continent' => 'EU',
            'timezone_id' => 'Europe/Dublin',
            'timezone_name' => 'Greenwich Mean Time',
            'location' => [
                'lat' => 53.3245691,
                'lng' => -6.2526751,
            ],
        ];
    }

    private function forGetFacilitiesByLocationId(): array
    {
        return [
            [
                '_id' => new MongoId('683d79b87b6562a3a269d8b9'),
                'name' => 'for-get-facilities-by-location-id',
                'namespace' => 'for-get-facilities-by-location-id',
                'corporate_id' => 'corporate-id-for-get-facilities-by-location-id',
                'active' => true,
            ]
        ];
    }
}
