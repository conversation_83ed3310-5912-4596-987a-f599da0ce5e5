<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: subscription_service.proto

namespace GRPC\Payments\SubscriptionService;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>grpc.LineItemV2</code>
 */
class LineItemV2 extends \Google\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string ServiceType = 1;</code>
     */
    protected $ServiceType = '';
    /**
     * Generated from protobuf field <code>string Description = 2;</code>
     */
    protected $Description = '';
    /**
     * Generated from protobuf field <code>string ExternalReference = 3;</code>
     */
    protected $ExternalReference = '';
    /**
     * Generated from protobuf field <code>int32 Amount = 4;</code>
     */
    protected $Amount = 0;
    /**
     * Generated from protobuf field <code>uint32 Quantity = 5;</code>
     */
    protected $Quantity = 0;
    /**
     * Generated from protobuf field <code>uint32 StartCycle = 7;</code>
     */
    protected $StartCycle = 0;
    /**
     * Generated from protobuf field <code>uint32 DurationCycles = 8;</code>
     */
    protected $DurationCycles = 0;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $ServiceType
     *     @type string $Description
     *     @type string $ExternalReference
     *     @type int $Amount
     *     @type int $Quantity
     *     @type int $StartCycle
     *     @type int $DurationCycles
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\SubscriptionService::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string ServiceType = 1;</code>
     * @return string
     */
    public function getServiceType()
    {
        return $this->ServiceType;
    }

    /**
     * Generated from protobuf field <code>string ServiceType = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setServiceType($var)
    {
        GPBUtil::checkString($var, True);
        $this->ServiceType = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string Description = 2;</code>
     * @return string
     */
    public function getDescription()
    {
        return $this->Description;
    }

    /**
     * Generated from protobuf field <code>string Description = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setDescription($var)
    {
        GPBUtil::checkString($var, True);
        $this->Description = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string ExternalReference = 3;</code>
     * @return string
     */
    public function getExternalReference()
    {
        return $this->ExternalReference;
    }

    /**
     * Generated from protobuf field <code>string ExternalReference = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setExternalReference($var)
    {
        GPBUtil::checkString($var, True);
        $this->ExternalReference = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 Amount = 4;</code>
     * @return int
     */
    public function getAmount()
    {
        return $this->Amount;
    }

    /**
     * Generated from protobuf field <code>int32 Amount = 4;</code>
     * @param int $var
     * @return $this
     */
    public function setAmount($var)
    {
        GPBUtil::checkInt32($var);
        $this->Amount = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>uint32 Quantity = 5;</code>
     * @return int
     */
    public function getQuantity()
    {
        return $this->Quantity;
    }

    /**
     * Generated from protobuf field <code>uint32 Quantity = 5;</code>
     * @param int $var
     * @return $this
     */
    public function setQuantity($var)
    {
        GPBUtil::checkUint32($var);
        $this->Quantity = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>uint32 StartCycle = 7;</code>
     * @return int
     */
    public function getStartCycle()
    {
        return $this->StartCycle;
    }

    /**
     * Generated from protobuf field <code>uint32 StartCycle = 7;</code>
     * @param int $var
     * @return $this
     */
    public function setStartCycle($var)
    {
        GPBUtil::checkUint32($var);
        $this->StartCycle = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>uint32 DurationCycles = 8;</code>
     * @return int
     */
    public function getDurationCycles()
    {
        return $this->DurationCycles;
    }

    /**
     * Generated from protobuf field <code>uint32 DurationCycles = 8;</code>
     * @param int $var
     * @return $this
     */
    public function setDurationCycles($var)
    {
        GPBUtil::checkUint32($var);
        $this->DurationCycles = $var;

        return $this;
    }

}

