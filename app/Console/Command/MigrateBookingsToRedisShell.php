<?php

use Glofox\Datasource\Exceptions\InvalidMongoIdException;
use Glofox\Domain\BookingRequests\Models\BookingRequestType;
use Glofox\Domain\BookingRequests\Repositories\BookingRequestsCache;
use Glofox\Domain\Bookings\Repositories\BookingsRepository;
use Glofox\Domain\Bookings\Search\Expressions\BranchId;
use Glofox\Domain\Bookings\Search\Expressions\EventId;
use Glofox\Domain\Branches\Repositories\BranchesRepository;
use Glofox\Domain\Events\Models\Event;
use Glofox\Domain\Events\Repositories\EventsRepository;
use Glofox\Domain\Events\Search\Expressions\Id;

class MigrateBookingsToRedisShell extends AppShell
{

    /** @var BookingRequestsCache */
    private $bookingRequestsCache;

    /** @var BookingsRepository */
    private $bookingsRepository;

    /** @var BranchesRepository */
    private $branchesRepository;

    /** @var EventsRepository */
    private $eventsRepository;

    public function __construct($stdout = null, $stderr = null, $stdin = null)
    {
        parent::__construct($stdout, $stderr, $stdin);

        $this->bookingRequestsCache = app()->make(BookingRequestsCache::class);
        $this->bookingsRepository = app()->make(BookingsRepository::class);
        $this->branchesRepository = app()->make(BranchesRepository::class);
        $this->eventsRepository = app()->make(EventsRepository::class);
    }

    public function main()
    {
        $abTestId = $this->args[0] ?? null;
        $environment = $this->args[1] ?? null;
        $token = $this->args[2] ?? null;

        if (empty($abTestId)) {
            $this->log('Please specify the AB Test id in arguments', LOG_ERR);

            return;
        }

        if (empty($environment) || !in_array($environment, ['term', 'star', 'spd', 'dev', 'sand', 'stg', 'plat'])) {
            $this->log('Please specify a proper environment abbreviation in arguments', LOG_ERR);

            return;
        }

        if (empty($token)) {
            $this->log('Please specify the Authorization Token in arguments', LOG_ERR);

            return;
        }

        $this->branchesRepository
            ->fields(['_id'])
            ->order(['created' => 1]);

        $limit = $this->args[3] ?? null;
        if (null !== $limit) {
            $this->branchesRepository->limit($limit);
        }

        $page = $this->args[4] ?? null;
        if (null !== $page) {
            $this->branchesRepository->page($page);
        }

        $specificBranchId = $this->args[5] ?? null;
        if (null !== $specificBranchId) {
            $this->branchesRepository->addCriteria(new Id($specificBranchId));
        }

        $branches = $this->branchesRepository->find();

        foreach ($branches as $branch) {
            $this->log(sprintf('Starting Mongo to Redis migration script for bookings on branch %s', $branch->id()), LOG_INFO);

            $this->migrateBranch($branch->id());

            $this->log(sprintf('Finished Mongo to Redis migration script for bookings on branch %s', $branch->id()), LOG_INFO);
        }
    }

    private function migrateBranch(string $branchId)
    {
        $bookings = $this->bookingsRepository
            ->fields(['_id'])
            ->addCriteria(new BranchId($branchId))
            ->addCriteria(EventId::exists())
            ->order(['created' => 1])
            ->find();

        $this->log(sprintf('Found %s bookings', is_countable($bookings) ? count($bookings) : 0), LOG_INFO);

        $this->removeEventBookingsFromRedis($branchId);

        $this->log('Saving bookings', LOG_INFO);

        foreach ($bookings as $booking) {
            try {
                $this->addBookingCache($booking->id());
            } catch (InvalidMongoIdException $exception) {
                $this->log(
                    sprintf(
                        'Booking %s skipped. Invalid Mongo ID: %s',
                        $booking->id(),
                        $exception->getMessage()
                    ),
                    LOG_WARNING
                );
            }
        }

        $this->log('Saved bookings', LOG_INFO);

        $this->enableExperimentalBookingForBranch($branchId);
    }

    private function removeEventBookingsFromRedis(string $branchId): void
    {
        $events = $this->eventsRepository
            ->fields(['_id'])
            ->addCriteria(new BranchId($branchId))
            ->find();

        $this->log(sprintf('Wiping %s events from Redis', is_countable($events) ? count($events) : 0), LOG_INFO);

        foreach ($events as $event) {
            $this->bookingRequestsCache->removeByEventIds([
                sprintf('%s-%s', $event->id(), BookingRequestType::WAITING()),
                sprintf('%s-%s', $event->id(), BookingRequestType::CANCELLING()),
                sprintf('%s-%s', $event->id(), BookingRequestType::BOOKING()),
            ]);
        }

        $this->log('Events wiped from Redis', LOG_INFO);
    }

    private function getEvent(string $eventId): ?Event
    {
        return $this->eventsRepository
            ->fields(['_id', 'time_finish', 'branch_id'])
            ->addCriteria(new Id($eventId))
            ->first();
    }

    private function addBookingCache(string $bookingId): void
    {
        $booking = $this->bookingsRepository
            ->fields(['_id', 'event_id', 'user_id', 'created', 'status', 'guest_bookings'])
            ->addCriteria(new Id($bookingId))
            ->first();

        if (!$booking) {
            $this->log(sprintf('Could not find booking with id %s', $bookingId), LOG_INFO);

            return;
        }

        $event = $this->getEvent($booking->eventId());
        if (!$event) {
            $this->log(sprintf('Could not find event with id %s', $booking->eventId()), LOG_INFO);

            return;
        }

        $month = 2_592_000;
        $expireAt = $event->timeFinish()->getTimestamp() + $month;

        $this->bookingRequestsCache->save($booking, $expireAt);
    }

    private function enableExperimentalBookingForBranch(string $branchId): void
    {
        $payload = json_encode(['targetId' => $branchId], JSON_PARTIAL_OUTPUT_ON_ERROR);

        $abTestId = $this->args[0] ?? null;
        $environment = $this->args[1] ?? null;
        $token = $this->args[2] ?? null;

        $ch = curl_init(sprintf('https://iris-%s.aws.glofox.com/abtests/%s/testtargets', $environment, $abTestId));
        curl_setopt($ch, CURLOPT_POSTFIELDS, $payload);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type:application/json',
            'Authorization: Bearer ' . $token,
        ]);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $result = curl_exec($ch);

        curl_close($ch);

        $this->log(sprintf('Request for enabling experimental booking was sent: %s', $result), LOG_INFO);
    }
}
