const today = new Date();
let now = today.toISOString();
now = now.replace('T', ' ');
now = now.replace('Z', '000'); // 'YYYY-MM-DD HH:mm:ss.000000'

const updateStatus = function(userId, status) {
    db.getCollection('users').update({
        '_id': userId
    }, {
        $set: {
            'lead_status': status
        }
    });
};

const createLeadEvent = function(user, event) {
    const branchId = user.membership.branch_id ? user.membership.branch_id : user.branch_id;

    db.getCollection('lead_events').insertOne({
        'user_id': user._id.str,
        'branch_id': branchId,
        'namespace': user.namespace,
        'event': event,
        'time': {
            'date' : now,
            'timezone_type' : NumberInt(3),
            'timezone' : 'UTC'
        },
        'modified' : today,
        'created' : today
    });
};

let abBranchesIds = [];

db.getCollection('a_b_test_clients').find({
    'active': true,
    'type':'memberships_service'
}).forEach((abClient) => {
    abBranchesIds.push(abClient.branch_id)
});

const usersToUpdate = db.getCollection('users').find({
    'branch_id': {$in: abBranchesIds},
    'active': true,
    'type': 'MEMBER',
    'membership.type': {$ne: 'payg'},
    'membership._id': {$ne: null},
    '$and': [
        {'lead_status': {$exists: true}},
        {'lead_status': {$ne: 'MEMBER'}}
    ]
});

print('will update lees than ' + usersToUpdate.size() + ' users');

usersToUpdate.forEach((user) => {
    let membership = db.getCollection('memberships').findOne({
        '_id': ObjectId(user.membership._id)
    });

    if (membership) {
        const previousStatus = user.lead_status;
        const newStatus = membership.trial === true ? 'TRIAL' : 'MEMBER';

        if (previousStatus !== newStatus) {
            updateStatus(user._id, newStatus);

            print('user ' + user._id + ' updated from ' + previousStatus + ' to ' + newStatus);

            const event = newStatus === 'MEMBER' ? 'CLIENT' : newStatus;

            createLeadEvent(user, event);

            print('lead event created for user ' + user._id);
        }
    }
});
