<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\ElectronicAgreements\UseCase;

use App;
use Glofox\Domain\ElectronicAgreements\Models\Document;
use Glofox\Domain\ElectronicAgreements\Trigger;
use Glofox\Domain\ElectronicAgreements\UseCase\GetLatestVersionByTrigger;
use Glofox\Domain\ElectronicAgreements\UseCase\GetLatestVersionByTriggerParams;
use Mockery;
use GlofoxTestCase;
use Glofox\Domain\ElectronicAgreements\Services\ElectronicAgreementsServiceInterface;

App::import('Test/Case', 'GlofoxTestCase');

class GetLatestVersionByTriggerTest extends GlofoxTestCase
{
    public $fixtures = [];

    private const BRANCH_ID = 'test-branch-id';

    private const TRIGGER = 'member.authenticated';

    private ElectronicAgreementsServiceInterface $electronicAgreementsServiceMock;

    public function setUp(): void
    {
        parent::setUp();
        $this->electronicAgreementsServiceMock = Mockery::mock(ElectronicAgreementsServiceInterface::class);
    }

    public function tearDown(): void
    {
        parent::tearDown();
        Mockery::close();
    }

    /**
     * @dataProvider getLatestVersionByTriggerDataProvider
     */
    public function testGetLatestVersionByTrigger(
        GetLatestVersionByTriggerParams $params,
        string $exceptionMessage,
        bool $success
    ): void {
        $useCase = new GetLatestVersionByTrigger(
            $this->electronicAgreementsServiceMock
        );

        $version = [
            'id' => 'test-agreement-id',
            'version' => 1,
            'created_at' => '2023-01-01T00:00:00Z',
            'updated_at' => '2023-01-01T00:00:00Z',
            'document_id' => 'test-document-id',
            'template' => 'test-template',
            'requires_new_agreement' => true,
        ];

        $doc = new Document([
            'id' => 'test-document-id',
            'branch_id' => self::BRANCH_ID,
            'trigger' => self::TRIGGER,
            'latest_version' => $version,
        ]);

        if (!$success) {
            $this->electronicAgreementsServiceMock
                ->shouldReceive('findDocumentByTrigger')
                ->once()
                ->andThrow(new \Exception($exceptionMessage));

            $this->expectExceptionMessage($exceptionMessage);

            $useCase->execute($params);
        } else {
            $this->electronicAgreementsServiceMock
                ->shouldReceive('findDocumentByTrigger')
                ->once()
                ->withArgs(function (string $branchId, string $trigger, array $fields) use ($params) {
                    $this->assertEquals($params->branchId(), $branchId);
                    $this->assertEquals($params->trigger(), $trigger);
                    $this->assertEquals(['latest_version'], $fields);
                    return true;
                })
                ->andReturn($doc);

            $response = $useCase->execute($params);
            $this->assertEquals($doc->latestVersion(), $response);
        }
    }

    /**
     * @return array<string, array>
     */
    public function getLatestVersionByTriggerDataProvider(): array
    {
        $validParams = new GetLatestVersionByTriggerParams(
            self::BRANCH_ID,
            Trigger::fromEAString(self::TRIGGER)
        );

        $invalidTriggerParams = new GetLatestVersionByTriggerParams(
            "invalid-branch-id",
            Trigger::fromEAString(self::TRIGGER)
        );

        return [
            'when findDocumentByTrigger returns an error, then error is returned' => [
                'params' => $invalidTriggerParams,
                'exceptionMessage' => "error",
                'success' => false,
            ],
          'when get latest version by trigger usecase is actioned with all valid params, then the latest version of the document is returned' => [
              'params' => $validParams,
              'exceptionMessage' => "",
              'success' => true,
          ],
        ];
    }
}