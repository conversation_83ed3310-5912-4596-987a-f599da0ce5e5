<?php

use Glofox\Http\Requests\RequestIdGeneratorInterface;

App::import('Test/Case', 'GlofoxControllerTestCase');

class AppControllerTest extends GlofoxControllerTestCase
{
    public function tearDown(): void
    {
        parent::tearDown();
        Mockery::close();
    }

    public function test_it_should_expose_openapi_docs_file()
    {
        $this->testAction('/openapi.yml', ['method' => 'get']);

        $this->assertContains('attachment; filename="openapi.yml"', $this->headers);
    }

    public function test_it_should_contain_request_id_header()
    {
        $this->testAction('/', ['method' => 'get']);

        /** @var RequestIdGeneratorInterface $generator */
        $generator = app()->make(RequestIdGeneratorInterface::class);

        self::assertEquals($generator->getGeneratedUuid(), $this->response->header()['X-Request-Id']);
    }

    public function test_it_should_contain_duration_ms_header()
    {
        $this->testAction('/', ['method' => 'get']);

        /** @var RequestIdGeneratorInterface $generator */
        $generator = app()->make(RequestIdGeneratorInterface::class);

        self::assertRegExp('/[0-9]+(\.[0-9]+)?/', $this->response->header()['X-RequestDurationMs']);
    }

    public function test_it_should_contain_trace_and_span_headers_if_new_trace()
    {
        $this->testAction('/', ['method' => 'get']);

        /** @var RequestIdGeneratorInterface $generator */
        $generator = app()->make(RequestIdGeneratorInterface::class);

        self::assertRegExp('/[0-9a-f]{16}/', $this->response->header()['X-TraceId']);
        self::assertRegExp('/[0-9a-f]{8}/', $this->response->header()['X-SpanId']);

        // New trace has no parent
        self::assertEquals('', $this->response->header()['X-ParentSpanId']);
    }

    public function test_it_should_contain_captured_honeycomb_tracing_headers()
    {

        $_SERVER['HTTP_X_HONEYCOMB_TRACE'] = '1;trace_id=********************************,parent_id=7248651b52e67446';
        $this->testAction('/', ['method' => 'get']);

        /** @var RequestIdGeneratorInterface $generator */
        $generator = app()->make(RequestIdGeneratorInterface::class);

        // Captured from header
        self::assertEquals('********************************', $this->response->header()['X-TraceId']);
        self::assertEquals('7248651b52e67446', $this->response->header()['X-ParentSpanId']);

        // Automatically generated new span
        self::assertRegExp('/[0-9a-f]{8}/', $this->response->header()['X-SpanId']);
    }

    public function test_it_should_contain_captured_honeycomb_tracing_headers_with_extra_data()
    {

        $_SERVER['HTTP_X_HONEYCOMB_TRACE'] = '1;trace_id=********************************,parent_id=7248651b52e67447,dataset=glofox-dev,context=abc';
        $this->testAction('/', ['method' => 'get']);

        /** @var RequestIdGeneratorInterface $generator */
        $generator = app()->make(RequestIdGeneratorInterface::class);

        // Captured from header
        self::assertEquals('********************************', $this->response->header()['X-TraceId']);
        self::assertEquals('7248651b52e67447', $this->response->header()['X-ParentSpanId']);

        // Automatically generated new span
        self::assertRegExp('/[0-9a-f]{8}/', $this->response->header()['X-SpanId']);
    }

    /**
     * @throws MongoException
     */
    public function testEncodeSearchRegexWithValidSearchString(): void
    {
        $params = [];
        $search = '?$.*^+test-string';
        $modelMock = Mockery::mock(AppModel::class);
        $modelMock->shouldReceive('searchable')
            ->once()
            ->andReturn(['test-field-1', 'test-field-2']);
        $appController = new AppController();
        $appController->encodeSearchRegex($params, $search, $modelMock);

        $expectedRegex = ['$regex' => new MongoRegex("/" . preg_quote($search, '/') . "/i")];
        $expected = [
            '$or' => [
                ['test-field-1' => $expectedRegex],
                ['test-field-2' => $expectedRegex]
            ]
        ];

        $this->assertEquals($expected, $params);
    }

    public function testSplitSearchRegexWithValidSearchString(): void
    {
        $params = [];
        $search = 'test split';
        $appControllerMock = Mockery::mock(AppController::class)->makePartial();
        $appControllerMock->shouldReceive('encodeSearchRegex')
            ->twice()
            ->withArgs(function (&$params, $word) {
                $params[] = "mocked: $word";
                return true;
            });

        $appControllerMock->splitSearchRegex($params, $search);
        $expected = [
            '$and' => [
                ['mocked: test'],
                ['mocked: split']
            ]
        ];

        $this->assertEquals($expected, $params);
    }
}
