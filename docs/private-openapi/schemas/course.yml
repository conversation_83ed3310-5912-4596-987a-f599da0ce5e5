type: object
properties:
  _id:
    type: string
    description: The unique identifier of the course
  branch_id:
    type: string
    description: The unique identifier of the branch
  namespace:
    type: string
    description: The namespace of the branch
  name:
    type: string
    description: The name of the course
  description:
    type: string
    description: The description of the course
  active:
    type: boolean
    description: The status of the course
  private:
    type: boolean
    description: The privacy of the course
  facility:
    type: string
    description: The facility where the course takes place
  trainers:
    type: array
    description: The trainers that will deliver the course (ids of the trainers)
    items:
      type: string
  schedule:
    type: array
    description: The schedule of the course
    items:
      type: object
      properties:
        size:
          type: integer
          description: The maximum number of participants
        days:
          type: array
          description: The days of the week on which the course is conducted
          enum:
            - MO
            - TU
            - WE
            - TH
            - FR
            - SA
            - SU
          items:
            type: object
            properties:
              days:
                type: string
                description: The day of the week
              start_time:
                type: integer
                description: The start time of that day
              end_time:
                type: integer
                description: The end time of that day
              active:
                type: boolean
                description: The flag showing whether the course is active or not
              facility:
                type: string
                description: The facility where the course is conducted
              trainers:
                type: array
                description: The list of trainers conducting the particular course's time slot
                items:
                  type: string
              size:
                type: integer
                description: The maximum number of participants
              id:
                type: integer
                description: The unique identifier of the schedule
        trainers:
          type: array
          description: The list of trainers conducting the course
          items:
            type: string
        facility:
          type: array
          description: The facility where the course takes place
          items:
            type: string
        id:
          type: integer
          description: The unique identifier of the schedule
        start_date:
          type: integer
          description: The start date of the schedule
        end_date:
          type: integer
          description: The end date of the schedule
        label:
          type: string
          description: The label of the schedule
        total_bookings:
          type: integer
          description: The total number of bookings
        total_waitings:
          type: integer
          description: The total number of waitings
        course_id:
          type: string
          description: The unique identifier of the course
        booked:
          type: integer
          description: The number of booked participants
        has_booked:
          type: boolean
          description: The status of the booking
        booking_status:
          type: string
          description: The status of the booking
        booking_id:
          type: string
          description: The unique identifier of the booking
  pricing:
    type: array
    items:
      $ref: "../schemas/pricing.yml"
  type:
    type: string
    description: The type of the event
  image_url:
    type: string
    description: The image URL of the course