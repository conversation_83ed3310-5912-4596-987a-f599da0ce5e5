<?php

namespace CakeTestCases\Glofox\Domain\BookingSpots\UseCase;

use Glofox\Domain\BookingSpots\Models\BookingSpot;
use Glofox\Domain\BookingSpots\Models\BookingSpotsCollection;
use Glofox\Domain\BookingSpots\Repositories\BookingSpotsRepository;
use Glofox\Domain\BookingSpots\UseCase\ListBookingSpotsByModelId;
use Glofox\Domain\BookingSpots\UseCase\ListBookingSpotsByModelIdParams;
use Glofox\Domain\Events\Models\Event;
use Glofox\Domain\Events\Repositories\EventsRepository;
use Glofox\Domain\Http\Request\BaseCriteria;

\App::import('Test/Case', 'GlofoxTestCase');

class ListBookingSpotsByModelIdTest extends \GlofoxTestCase
{
    public $fixtures = [];

    public function tearDown(): void
    {
        parent::tearDown();

        \Mockery::close();
    }

    public function test_it_should_return_both_available_and_reserved_spots_for_a_given_user(): void
    {
        $reserved = BookingSpotsCollection::make([
            BookingSpot::make(['booking_id' => 'booking_id_1', 'spot_id' => '2', 'user_id' => 'user-id']),
        ]);

        $bookingSpotsRepo = \Mockery::mock(BookingSpotsRepository::class)
            ->shouldReceive('findByBranchAndModelId')
            ->withArgs(function (string $branchId, string $modelId, ?string $userId) {
                self::assertEquals('branch-id', $branchId);
                self::assertEquals('event-id', $modelId);
                self::assertEquals('user-id', $userId);
                return true;
            })
            ->andReturn($reserved)
            ->getMock();

        $eventsRepo = \Mockery::mock(EventsRepository::class)
            ->shouldReceive('findByIdOrFail')
            ->andReturn(Event::make(['size' => 4]))
            ->getMock();

        $useCase = new ListBookingSpotsByModelId($bookingSpotsRepo, $eventsRepo);

        $result = $useCase->execute(
            new ListBookingSpotsByModelIdParams(
                new BaseCriteria(1, 100, []),
                'branch-id',
                'event-id',
                'user-id',
            )
        );

        self::assertEquals(1, count($result->getReservedSpots()));
        self::assertEquals(0, count($result->getNonReservedSpotIds()));

        self::assertEquals($reserved, $result->getReservedSpots());
        self::assertEquals([], $result->getNonReservedSpotIds());
    }

    public function test_it_should_return_both_available_and_reserved_spots(): void
    {
        $reserved = BookingSpotsCollection::make([
            BookingSpot::make(['booking_id' => 'booking_id_1', 'spot_id' => '2']),
            BookingSpot::make(['booking_id' => 'booking_id_2', 'spot_id' => '4']),
        ]);

        $bookingSpotsRepo = \Mockery::mock(BookingSpotsRepository::class)
            ->shouldReceive('findByBranchAndModelId')
            ->withArgs(function (string $branchId, string $modelId, ?string $userId) {
                self::assertEquals('branch-id', $branchId);
                self::assertEquals('event-id', $modelId);
                self::assertEquals(null, $userId);
                return true;
            })
            ->andReturn($reserved)
            ->getMock();

        $eventsRepo = \Mockery::mock(EventsRepository::class)
            ->shouldReceive('findByIdOrFail')
            ->andReturn(Event::make(['size' => 4]))
            ->getMock();

        $useCase = new ListBookingSpotsByModelId($bookingSpotsRepo, $eventsRepo);

        $result = $useCase->execute(
            new ListBookingSpotsByModelIdParams(
                new BaseCriteria(1, 100, []),
                'branch-id',
                'event-id',
                null,
            )
        );

        self::assertEquals(2, count($result->getReservedSpots()));
        self::assertEquals(2, count($result->getNonReservedSpotIds()));

        self::assertEquals($reserved, $result->getReservedSpots());
        self::assertEquals([1, 3], $result->getNonReservedSpotIds());
    }
}
