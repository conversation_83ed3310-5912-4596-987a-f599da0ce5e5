<?php

use Glofox\CdnProvider;
use Glofox\Domain\Authentication\Auth;
use Glofox\Domain\Facilities\Http\Requests\UpsertFacilityRequest;
use Glofox\Domain\Facilities\Requests\DeleteByIdRequest;
use Glofox\Domain\Facilities\UseCase\CreateOrUpdateOne;
use Glofox\Domain\Facilities\UseCase\CreateOrUpdateOneParams;
use Glofox\Domain\Facilities\UseCase\DeleteOne;
use Glofox\Domain\Facilities\Validation\ScheduleSizeValidator;
use Glofox\Domain\Facilities\Validation\UserAuthorization\CreateOrUpdateValidator;
use Glofox\Domain\Facilities\Validation\UserAuthorization\DeleteValidator;
use Illuminate\Http\JsonResponse;
use Psr\Log\LoggerInterface;

/**
 * FacilitiesController class.
 *
 * @property Facility $Facility
 */
class FacilitiesController extends \Glofox\Domain\Facilities\Http\FacilitiesController
{
    public $feature_name;

    public $image_default_name;

    /**
     * @var string
     */
    public $name = 'Facilities';

    /**
     * @var array
     */
    public $uses = [
        'Branch',
        'Facility',
        'User',
        'TimeSlotPattern',
        'Event',
    ];

    /**
     * @var array
     */
    public $components = [
        'Paginator',
        'Utility',
        'Notification',
        'EventGenerator',
        'S3',
        'JWT',
    ];

    /*******************************************************************************************
     *                                API 2.0 New Dashboard
     ******************************************************************************************/

    public function beforeFilter()
    {
        parent::beforeFilter();
        $this->feature_name = 'facilities';
        $this->image_default_name = 'default.png';
    }

    /**
     * Dispatcher for Model.
     *
     * @param string|null $identifier [description]
     *
     * @return mixed
     */
    public function dispatcher($identifier = null)
    {
        return parent::dispatch($identifier, $this->Facility);
    }

    /**
     * Get facilities by specifying all in branch, all in branch or by specific id.
     *
     * @param string $id Facility id
     *
     * @return array List of facilities that match the entry params
     *
     * @internal   This convention is a little boring for clients, but helps us keep clean backend
     *
     * @example    https://www.glofoxlogin/view/{id}
     */
    public function view($id)
    {
        $user = $this->JWT->parseToken();
        $params = ['branch_id' => $user['branch_id']];
        if (isset($id)) {
            $params['_id'] = $id;
        }

        return json_encode($this->Facility->find('all', ['conditions' => array_merge($params, ['active' => true])]), JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    /**
     * @param UpsertFacilityRequest $req
     * @return JsonResponse
     * @throws UnsuccessfulOperation
     * @throws \Glofox\Exception
     */
    public function upsert(UpsertFacilityRequest $req): JsonResponse
    {
        app()->make(CreateOrUpdateValidator::class)
            ->validate(Auth::user(), $req->branchId(), $req->namespace(), $req->id());

        app()->make(ScheduleSizeValidator::class)
            ->validate($req->pattern(), $req->branchId());

        $params = new CreateOrUpdateOneParams(
            $req->id(),
            $req->branchId(),
            $req->namespace(),
            $req->name(),
            $req->description(),
            $req->image(),
            $req->isActive(),
            $req->isBookable(),
            $req->isPublic(),
            $req->isOnline(),
            $req->categories(),
            $req->taxIds(),
            $req->pattern()
        );

        $useCase = app()->make(CreateOrUpdateOne::class);
        $result = $useCase->execute($params);

        return response()->json($result);
    }

    /*!
     * Search list with params and pagination
     * @param      [type]                   $start  [description]
     * @param      [type]                   $limit  [description]
     * @param      [type]                   $search [description]
     * @return     [type]                           [description]
     */

    /**
     * @param int $page
     * @param int $limit
     * @param null $search
     * @param string $active
     *
     * @return string
     */
    public function listview($page = 1, $limit = 30, $search = null, $active = '1')
    {
        $user = $this->JWT->parseToken();
        $active = '0' == $active ? false : true;
        $params = ['branch_id' => $user['branch_id'], 'active' => $active];
        $this->encodeSearchRegex($params, $search, $this->Facility); //<- add to params built Regex for global search
        $page = (int)$page;
        $limit = (int)$limit;
        $result = $this->Facility->find(
            'all',
            [
                'conditions' => $params,
                'limit' => $limit,
                'page' => $page,
                'order' => ['name' => 1],
            ]
        );

        return json_encode($result, JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    /*!
     * Count total records in this collection
     * @return     [type]                   [description]
     */

    /**
     * @param string $active
     *
     * @return string
     */
    public function count($active = '1')
    {
        $user = $this->JWT->parseToken();
        $active = '0' == $active ? false : true;
        $params = ['branch_id' => $user['branch_id'], 'active' => $active];
        $result = $this->Facility->find('count', ['conditions' => $params]);

        return json_encode($result, JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    /**
     * @param DeleteByIdRequest $req
     * @return JsonResponse
     * @throws UnsuccessfulOperation
     */
    public function remove(DeleteByIdRequest $req): JsonResponse
    {
        app()->make(DeleteValidator::class)->validate(Auth::user(), $req->facilityId());

        $useCase = app()->make(DeleteOne::class);
        $result = $useCase->execute($req->facilityId());

        return response()->json($result);
    }

    /****************************** REST WEBSERVICE ACTIONS  *******************************************/

    /**
     * Index method.
     */
    public function index()
    {
        $params = [
            'order' => ['_id' => -1],
            'limit' => 35,
            'page' => 1,
        ];
        $results = $this->Facility->find('all', $params);

        return json_encode($results, JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    /**
     * Get all facilities for a given branch /branch/<branch_id>/facilities.
     *
     * @param type $id
     * @param bool $fetch_images
     * @param null $user_id
     * @param bool $fetch_credits
     *
     * @return string
     */
    public function findByBranchId($id = null, $fetch_images = false, $user_id = null, $fetch_credits = false)
    {
        $is_valid_mongoid = $this->Utility->is_valid_mongoid($id);
        if (empty($is_valid_mongoid)) {
            return json_encode(['success' => false, 'message' => __('Invalid Branch Id')], JSON_PARTIAL_OUTPUT_ON_ERROR);
        }

        $params = [
            'conditions' => ['branch_id' => $id, 'active' => true],
            'order' => ['name' => 1],
            'limit' => 1000,
            'page' => 1,
        ];
        $results = $this->Facility->find('all', $params);
        foreach ($results as $key => $result) {
            if (!isset($results[$key]['Facility']['categories']['0'])) {
                $results[$key]['Facility']['categories'] = [];
            }

            $branch_id = $result['Facility']['branch_id'];

            // Fetch Images
            $fetch_images = filter_var($fetch_images, FILTER_VALIDATE_BOOLEAN);
            if ($fetch_images) {
                $namespace = $result['Facility']['namespace'];
                $feature = 'facilities';
                if ($this->S3->crudImageExists($namespace, $branch_id, $feature, $result['Facility']['_id'])) {
                    $results[$key]['Facility']['image_url'] = $this->S3->getCrudImageUrl(
                        $namespace,
                        $branch_id,
                        $feature,
                        $result['Facility']['_id']
                    );
                }
            }
            // Validate user_id is a valid MongoId
            if ($user_id) {
                // Fetch the price
                $this->loadModel('TimeSlotPattern');
                $time_slot_pattern = $this->TimeSlotPattern->findByModelAndModelId(
                    $this->Facility->useTable,
                    $results[$key]['Facility']['_id']
                );

                $member = ($this->Utility->is_valid_mongoid($user_id)) ? $this->User->findByIdSetFields(
                    $user_id,
                    ['membership']
                ) : null;
                $allowed_member_types = $time_slot_pattern['TimeSlotPattern']['allowed_member_types'];
                $as_payg = ($member) ? false : true;
                $default_price = $time_slot_pattern['TimeSlotPattern']['default_price'];
                $price_and_permission = $this->User->validateAllowedGroups(
                    $user_id,
                    $branch_id,
                    $allowed_member_types,
                    $as_payg,
                    $default_price,
                    $time_slot_pattern['TimeSlotPattern']['model_id']
                );
                $results[$key]['Facility']['is_allowed'] = $price_and_permission['success'];
                $results[$key]['Facility']['default_price'] = $price_and_permission['price'] ?? null;

                // Fetch Credits
                $fetch_credits = filter_var($fetch_credits, FILTER_VALIDATE_BOOLEAN);
                if ($fetch_credits) {
                    $this->loadModel('UserCredit');
                    $branch_id = $id;
                    $model = $this->Facility->useTable;
                    $model_id = $results[$key]['Facility']['_id'];
                    $categories = $results[$key]['Facility']['categories'];
                    $return_model = false;

                    $credits = $this->UserCredit->findAllCredits(
                        $branch_id,
                        $user_id,
                        $model,
                        $model_id,
                        $categories,
                        $return_model
                    );

                    $results[$key]['Facility']['credits'] = $credits ?: [];
                }
            }
        }

        return json_encode($results, JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    /**
     * Find one by facility id and branch id /branch/<branch_id>/facility/<facility_id>.
     *
     * @param type $id
     * @param type $id_fac
     *
     * @return type
     */
    public function findByBranchIdAndFacilityId($id = null, $id_fac = null)
    {
        // Check if Branch Id is empty
        $is_valid_mongoid = $this->Utility->is_valid_mongoid($id);
        if (empty($is_valid_mongoid)) {
            return json_encode(['success' => false, 'message' => __('Invalid Branch Id')], JSON_PARTIAL_OUTPUT_ON_ERROR);
        }
        // Check if Event Id is empty
        $is_valid_mongoid = $this->Utility->is_valid_mongoid($id_fac);
        if (empty($is_valid_mongoid)) {
            return json_encode(['success' => false, 'message' => __('Invalid Facility Id')], JSON_PARTIAL_OUTPUT_ON_ERROR);
        }

        $params = [
            'conditions' => ['_id' => $id_fac, 'branch_id' => $id, 'active' => true],
            'order' => ['_id' => -1],
            'limit' => 1,
            'page' => 1,
        ];
        $results = $this->Facility->find('all', $params);

        if (!empty($results)) {
            return json_encode($results, JSON_PARTIAL_OUTPUT_ON_ERROR);
        } else {
            return json_encode(['success' => false, 'message' => __('No Facilty')], JSON_PARTIAL_OUTPUT_ON_ERROR);
        }
    }

    /**
     * Find all facilities for current branch.
     *
     * @return string [type] [description]
     */
    public function findAll()
    {
        //Validate user
        $user = $this->JWT->getUser();
        $allowed_roles = ['admin', 'superadmin'];
        $this->JWT->validateOrFail($allowed_roles, $user['User']['namespace']);
        $namespace = $user['User']['namespace'];
        $branch_id = $user['User']['branch_id'];

        //Get facilities
        $facilities = $this->Facility->find('all', [
            'conditions' => ['namespace' => $namespace, 'branch_id' => $branch_id, 'active' => true],
            'order' => ['name' => 'ASC'],
        ]);

        return json_encode($facilities, JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    /**
     * This function will return all the facilities that have time slots.
     *
     * @param [type] $id [description]
     *
     * @return string [type]     [description]
     */
    public function findBookableFacilitiesByBranchId($id = null)
    {
        $is_valid_mongoid = $this->Utility->is_valid_mongoid($id);
        if (empty($is_valid_mongoid)) {
            return json_encode(['success' => false, 'message' => __('Invalid Branch Id')], JSON_PARTIAL_OUTPUT_ON_ERROR);
        }

        $fields = ['namespace', 'branch_id', 'name', 'categories', 'description'];
        $return_model = false;

        return json_encode($this->Facility->findBookableFacilitiesByBranchId($id, $fields, $return_model), JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    /**
     * @return string
     *
     * @throws Exception
     */
    public function uploads(
        Glofox\Request $request
    ) {
        $logger = app()->make(LoggerInterface::class);
        $logger->info('Log to track the use of this uploads method from FacilitiesController');

        $user = Auth::user();
        $fileKey = 'facilitiesImage';
        $feature = 'facilities';

        if (!$request->hasFile($fileKey)) {
            throw new UnsuccessfulOperation('THERE_IS_NO_FILE_TO_UPLOAD');
        }

        $saveImage = $this->S3->saveCrudImage(
            $_FILES[$fileKey],
            $user->namespace(),
            $user->currentBranchId(),
            $feature
        );

        if (!$saveImage['success']) {
            throw new UnsuccessfulOperation($saveImage['message']);
        }

        $fileUrl = $this->S3->getCrudImageUrl($user->namespace(), $user->currentBranchId(), $feature);
        $cdnUrl = str_replace('https://glofox.s3.amazonaws.com/', \sprintf('%s/', CdnProvider::getUrl()), $fileUrl);

        return response()->json([
            'success' => true,
            'url' => $cdnUrl,
        ]);
    }
}
