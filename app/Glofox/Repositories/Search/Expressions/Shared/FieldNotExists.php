<?php

namespace Glofox\Repositories\Search\Expressions\Shared;

use Glofox\Repositories\Filters\ConditionsCollection;
use Glofox\Repositories\Filters\Evaluable;
use Glofox\Repositories\Repository;
use Glofox\Repositories\Search\Expressions\Conditionable;
use Glofox\Repositories\Search\Expressions\HasConditions;
use Glofox\Repositories\Search\Expressions\Key;
use Webmozart\Expression\Expr;

class FieldNotExists extends Key implements Evaluable, Conditionable
{
    use HasConditions;

    private string $field;

    public function __construct(string $field)
    {
        $this->field = $field;

        parent::__construct($field, Expr::keyNotExists($field));
    }

    public function field(): string
    {
        return $this->field;
    }

    public function applyToConditions(ConditionsCollection $conditions, Repository $repository = null)
    {
        $conditions->push([
            $this->field() => ['$exists' => false]
        ]);
    }
}
