<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\TimeSlotPatterns\Services;

use App;
use Glofox\Domain\TimeSlotPatterns\Models\TimeSlotPattern;
use Glofox\Domain\TimeSlotPatterns\Services\AllowedMemberTypesSanitiser;
use GlofoxTestCase;

App::import('Test/Case', 'GlofoxTestCase');

class AllowedMemberTypesSanitiserTest extends GlofoxTestCase
{
    public $fixtures = [];

    public function testCustomPricesGetProperlyResolved(): void
    {
        $timeSlotPattern = TimeSlotPattern::make([
            'pricing' => 'CUSTOM',
            'allowed_member_types' => [
                [
                    'price' => '111',
                    'type' => 'payg',
                    'memberships' => [['id' => 0, 'label' => 'PAYG']],
                ],
                [
                    'price' => 222,
                    'type' => 'member',
                    'memberships' => [['id' => 'random-membership-id', 'label' => 'Random Membership']],
                    'membership_id' => 'random-membership-id',
                ],
                [
                    'price' => '333',
                    'type' => 'member',
                    'memberships' => [['id' => 'random-membership-id', 'label' => 'Random Membership']],
                    'membership_id' => 'random-membership-id',
                ],
            ],
        ]);

        $sanitiser = new AllowedMemberTypesSanitiser();
        $result = $sanitiser->sanitise($timeSlotPattern);

        foreach ($result as $item) {
            self::assertInternalType('float', $item['price']);
        }
    }

    public function testSinglePricesGetProperlyResolved(): void
    {
        $timeSlotPattern = TimeSlotPattern::make([
            'pricing' => 'SINGLE',
            'allowed_member_types' => [
                [
                    'price' => '111',
                    'type' => 'payg',
                ],
                [
                    'price' => '222',
                    'type' => 'member',
                ],
            ],
        ]);

        $sanitiser = new AllowedMemberTypesSanitiser();
        $result = $sanitiser->sanitise($timeSlotPattern);

        foreach ($result as $item) {
            self::assertInternalType('float', $item['price']);
        }
    }
}
