<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\Users\Models;

use Carbon\Carbon;
use Glofox\Domain\Memberships\Status;
use Glofox\Domain\Memberships\Type;
use Glofox\Domain\Users\Models\UserMembership;

\App::import('Test/Case', 'GlofoxTestCase');

class UserMembershipTest extends \GlofoxTestCase
{
    public $fixtures = [];

    public function test_it_gets_locked(): void
    {
        $membership = new UserMembership(Status::ACTIVE);

        $membership->lock();

        $this->assertSame(Status::LOCKED, $membership->status());
        $this->assertTrue($membership->isLocked());
    }

    public function test_it_gets_unlocked(): void
    {
        $membership = new UserMembership(['status' => Status::LOCKED]);

        $membership->unlock();

        $this->assertSame(Status::ACTIVE, $membership->status());
        $this->assertFalse($membership->isLocked());
    }

    public function test_plan_price(): void
    {
        $membership = new UserMembership(['plan_price' => 10]);
        self::assertEquals(10, $membership->planPrice());

        $membership = new UserMembership(['plan_price' => '10']);
        self::assertEquals(10, $membership->planPrice());

        $membership = new UserMembership(['type' => 'PAYG']);
        self::assertEquals(0, $membership->planPrice());

        $membership = new UserMembership([]);
        self::assertEquals(0, $membership->planPrice());
    }

    public function test_plan_upfront_fee(): void
    {
        $membership = new UserMembership(['plan_upfront_fee' => 10]);
        self::assertEquals(10, $membership->planUpfrontFee());

        $membership = new UserMembership(['plan_upfront_fee' => '10']);
        self::assertEquals(10, $membership->planUpfrontFee());

        $membership = new UserMembership(['type' => 'PAYG']);
        self::assertEquals(0, $membership->planUpfrontFee());

        $membership = new UserMembership([]);
        self::assertEquals(0, $membership->planUpfrontFee());
    }

    public function test_it_checks_restricted_type(): void
    {
        $membership = new UserMembership(['type' => Type::TIME_CLASSES]);
        $this->assertTrue($membership->isRestricted());

        $membership = new UserMembership(['type' => Type::TIME]);
        $this->assertFalse($membership->isRestricted());
    }

    public function test_it_checks_lock_details(): void
    {
        $now = Carbon::now();
        $startDate = new \MongoDate($now->getTimestamp());
        $membership = new UserMembership(['lock' => [
            'start_date' => $startDate,
            'is_retrying' => true,
        ]]);
        $this->assertTrue($membership->lockDetails()->isRetrying());
        $this->assertEquals($membership->lockDetails()->startDate()->getTimestamp(), $now->getTimestamp());

        $membership = new UserMembership([]);
        $this->assertNull($membership->lockDetails());
    }

    public function test_it_checks_group_details(): void
    {
        $id = '5f7aefea72fec500245f76e2';

        $membership = new UserMembership(['group' => [
            '_id' => $id,
            'is_primary' => true,
        ]]);
        $this->assertTrue($membership->group()->isPrimary());
        $this->assertEquals($membership->group()->id(), $id);

        $membership = new UserMembership([]);
        $this->assertNull($membership->group());
    }
}
