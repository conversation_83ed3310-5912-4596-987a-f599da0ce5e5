<?php

namespace CakeTestCases\Glofox\Domain\Branches\Http\Actions;

use CakeTestCases\Glofox\Domain\Authentication\Token\TokenGeneration;
use CakeTestCases\Glofox\Domain\Users\Traits\FetchUsersTrait;
use Carbon\Carbon;
use Glofox\Domain\Bookings\Models\Booking;
use Glofox\Domain\Bookings\Repositories\BookingsRepository;
use Glofox\Repositories\Search\Expressions\Shared\Id;

\App::import('Test/Case', 'GlofoxControllerTestCase');

final class ListAllBookingsActionTest extends \GlofoxControllerTestCase
{
    use FetchUsersTrait;
    use TokenGeneration;

    public $fixtures = [
        'app.branch',
        'app.user',
        'app.booking',
    ];

    public function test_it_lists_bookings(): void
    {
        $admin = $this->fetchUser('59a7011a05c677bda512212a');
        $booking = $this->getBookingById('5b3e01904a9b7505d9d3fb79');

        $this->loginAsUser($admin);

        $result = $this->testAction("/2.2/branches/{$booking->branchId()}/bookings", [
            'method' => 'GET',
            'data' => [
                'start_date' => strtotime('+6 days'),
                'end_date' => strtotime('+8 days'),
            ],
        ]);

        $data = json_decode($result, $assoc = true, 512, JSON_THROW_ON_ERROR);

        $this->assertCount(1, $data['data']);
        $this->assertEquals($booking->id(), $data['data'][0]['_id']);
        $this->assertEquals(1, $data['meta']['totalCount']);
        $this->assertEquals(1, $data['meta']['page']);
        $this->assertEquals(50, $data['meta']['limit']);
    }

    public function test_it_lists_bookings_by_modified_date(): void
    {
        $admin = $this->fetchUser('59a7011a05c677bda512212a');
        $booking = $this->getBookingById('5b3e01904a9b7505d9d3fb79');

        $this->loginAsUser($admin);

        $result = $this->testAction("/2.2/branches/{$booking->branchId()}/bookings", [
            'method' => 'GET',
            'data' => [
                'modified_start_date' => strtotime('+6 days'),
                'modified_end_date' => strtotime('+8 days'),
                'page' => -1,
                'limit' => 250,
            ],
        ]);

        $data = json_decode($result, $assoc = true, 512, JSON_THROW_ON_ERROR);

        $this->assertCount(1, $data['data']);
        $this->assertEquals($booking->id(), $data['data'][0]['_id']);
        $this->assertEquals(1, $data['meta']['totalCount']);
        $this->assertEquals(1, $data['meta']['page']);
        $this->assertEquals(200, $data['meta']['limit']);
    }

    public function test_it_lists_bookings_by_time_start_dates(): void
    {
        $admin = $this->fetchUser('59a7011a05c677bda512212a');
        $booking1 = $this->getBookingById('6422c7f680a9208d01d3e86c');
        $booking2 = $this->getBookingById('6424648bc2de42ef7a8e2b31');

        $this->loginAsUser($admin);

        $result = $this->testAction("/2.2/branches/{$booking1->branchId()}/bookings", [
            'method' => 'GET',
            'data' => [
                'time_start_start_date' => strtotime('+11 days 7 hours'),
                'time_start_end_date' => strtotime('+11 days 9 hours'),
                'page' => -1,
                'limit' => 250,
            ],
        ]);

        $data = json_decode($result, $assoc = true, 512, JSON_THROW_ON_ERROR);

        $this->assertCount(2, $data['data']);
        $this->assertEquals($booking1->id(), $data['data'][0]['_id']);
        $this->assertEquals($booking2->id(), $data['data'][1]['_id']);
        $this->assertEquals(2, $data['meta']['totalCount']);
        $this->assertEquals(1, $data['meta']['page']);
        $this->assertEquals(200, $data['meta']['limit']);
    }

    public function test_it_lists_bookings_by_time_finish_dates(): void
    {
        $admin = $this->fetchUser('59a7011a05c677bda512212a');
        $booking = $this->getBookingById('6425b15ae0531a7dba1d1c4a');

        $this->loginAsUser($admin);

        $result = $this->testAction("/2.2/branches/{$booking->branchId()}/bookings", [
            'method' => 'GET',
            'data' => [
                'time_finish_start_date' => strtotime('+13 days 7 hours'),
                'time_finish_end_date' => strtotime('+13 days 9 hours'),
                'page' => -1,
                'limit' => 250,
            ],
        ]);

        $data = json_decode($result, $assoc = true, 512, JSON_THROW_ON_ERROR);

        $this->assertCount(1, $data['data']);
        $this->assertEquals($booking->id(), $data['data'][0]['_id']);
        $this->assertEquals(1, $data['meta']['totalCount']);
        $this->assertEquals(1, $data['meta']['page']);
        $this->assertEquals(200, $data['meta']['limit']);
    }

    public function test_it_lists_bookings_by_status(): void
    {
        $admin = $this->fetchUser('59a7011a05c677bda512212a');
        $booking = $this->getBookingById('6424648bc2de42ef7a8e2b31');

        $this->loginAsUser($admin);

        $result = $this->testAction("/2.2/branches/{$booking->branchId()}/bookings", [
            'method' => 'GET',
            'data' => [
                'time_start_start_date' => strtotime('+11 days 7 hours'),
                'time_start_end_date' => strtotime('+11 days 9 hours'),
                'status' => 'WAITING',
                'page' => -1,
                'limit' => 250,
            ],
        ]);

        $data = json_decode($result, $assoc = true, 512, JSON_THROW_ON_ERROR);

        $this->assertCount(1, $data['data']);
        $this->assertEquals($booking->id(), $data['data'][0]['_id']);
        $this->assertEquals(1, $data['meta']['totalCount']);
        $this->assertEquals(1, $data['meta']['page']);
        $this->assertEquals(200, $data['meta']['limit']);
    }

    public function test_it_blocks_request_for_integrations_in_different_branches(): void
    {
        $user = $this->fetchUser('5c79e58d7fb4afd6e2c806f6');
        $booking = $this->getBookingById('5b3e01904a9b7505d9d3fb79');

        $this->loginAsUser($user);

        $this->testAction("/2.2/branches/{$booking->branchId()}/bookings", [
            'method' => 'GET',
            'data' => [
                'start_date' => strtotime('+6 days'),
                'end_date' => strtotime('+8 days'),
            ],
        ]);

        $this->assertEquals(403, $this->response->statusCode());
    }

    /**
     * @dataProvider bookingsByEventDataProvider
     * @param array $filter
     * @param array $filteredData
     * @return void
     */
    public function test_it_fetches_bookings_by_event_type(array $filter, array $filteredData): void
    {
        $user = $this->fetchUser('6435576e436939a4467bf624');
        $this->loginAsUser($user);
        $branchId = '6435563a6272b68401e85728';

        $result = $this->testAction("/2.2/branches/$branchId/bookings", [
            'method' => 'GET',
            'data' => $filter,
        ]);

        $data = json_decode($result, $assoc = true, 512, JSON_THROW_ON_ERROR);

        $this->assertCount(count($filteredData), $data['data']);
        foreach ($data['data'] as $booking) {
            $this->assertContains($booking['_id'], $filteredData);
        }
        $this->assertEquals(count($filteredData), $data['meta']['totalCount']);
        $this->assertEquals(1, $data['meta']['page']);
        $this->assertEquals(50, $data['meta']['limit']);
    }

    public function bookingsByEventDataProvider(): array
    {
        return [
            'Check if it fetches classes' => [
                'filter' => ['event_type' => 'events'],
                'filteredData' => ['6435581e1b4f203a48397624'],
            ],
            'Check if it fetches appointments' => [
                'filter' => [
                    'event_type' => 'appointments',
                    'time_start_start_date' => strtotime('2022-09-01 15:00:00'),
                    'time_start_end_date' => strtotime('2022-09-01 17:00:00'),
                    'status' => 'BOOKED',
                ],
                'filteredData' => ['643569da5656d16b30337f32'],
            ],
            'Check if it fetches facilities' => [
                'filter' => ['event_type' => 'facilities'],
                'filteredData' => ['64356aa7a605e4a2ab4e613d'],
            ],
            'Check if it fetches courses' => [
                'filter' => ['event_type' => 'courses'],
                'filteredData' => ['64356ab5500a5c7372ba5514'],
            ],
            'check if it fetches courses with time_start filter' => [
                'filter' => [
                    'event_type' => 'courses',
                    'time_start_start_date' => Carbon::parse('2022-08-01 00:00:00')->timestamp,
                    'time_start_end_date' => Carbon::parse('2022-10-01 23:59:59')->timestamp,
                ],
                'filteredData' => ['64356ab5500a5c7372ba5514'],
            ],
            'check it fetches courses with time_finish filter' => [
                'filter' => [
                    'event_type' => 'courses',
                    'time_finish_start_date' => strtotime('2022-08-01 00:00:00'),
                    'time_finish_end_date' => strtotime('2022-10-01 23:59:59'),
                ],
                'filteredData' => ['64356ab5500a5c7372ba5514'],
            ],
            'Check if it fetches users' => [
                'filter' => ['event_type' => 'users'],
                'filteredData' => ['64356b344911dbc18e0a081d'],
            ],
        ];
    }

    private function getBookingById(string $identifier): Booking
    {
        return app()->make(BookingsRepository::class)
            ->addCriteria(new Id($identifier))
            ->firstOrFail();
    }
}
