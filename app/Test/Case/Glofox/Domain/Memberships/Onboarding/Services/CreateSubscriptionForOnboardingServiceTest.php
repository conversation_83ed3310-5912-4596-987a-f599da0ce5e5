<?php

namespace CakeTestCases\Glofox\Domain\Memberships\Onboarding\Services;

use Carbon\Carbon;
use Glofox\Domain\Branches\Models\Branch;
use Glofox\Domain\Memberships\Models\Membership;
use Glofox\Domain\Memberships\Models\Plan;
use Glofox\Domain\Memberships\Onboarding\Services\CreateSubscriptionForOnboardingService;
use Glofox\Domain\PaymentMethods\Models\PaymentMethod;
use Glofox\Domain\PaymentMethods\Models\Provider;
use Glofox\Domain\PaymentMethods\Repositories\PaymentMethodsRepository;
use Glofox\Domain\PaymentMethods\Type;
use Glofox\Domain\PaymentMethodUsers\Models\PaymentMethodUser;
use Glofox\Domain\PaymentMethodUsers\Repository\PaymentMethodUsersRepository;
use Glofox\Domain\SubscriptionPlans\Models\SubscriptionPlan;
use Glofox\Domain\Users\Models\User;
use Glofox\Domain\Users\Repositories\UsersRepository;
use Glofox\Payments\Entities\Subscription\Models\Subscription;
use Glofox\Payments\Providers\Gateway\Handler as GatewayHandler;
use Glofox\Payments\Providers\Gateway\Subscriptions\Subscriptions;
use Ramsey\Uuid\Uuid;

\App::import('Test/Case', 'GlofoxTestCase');

class CreateSubscriptionForOnboardingServiceTest extends \GlofoxTestCase
{
    public $fixtures = [];

    /** @var UsersRepository */
    private $usersRepository;

    /** @var \User */
    private $userModel;

    /** @var \StripeCharge */
    private $stripeChargeModel;

    /** @var PaymentMethodsRepository */
    private $paymentMethodsRepository;

    /** @var PaymentMethodUsersRepository */
    private $paymentMethodUsersRepository;

    /** @var Branch */
    private $branch;

    /** @var User */
    private $member;

    /** @var Membership */
    private $membership;

    /** @var Plan */
    private $plan;

    /** @var SubscriptionPlan */
    private $subscriptionPlan;

    private ?int $priceToPay = null;

    /** @var Carbon */
    private $endOfTrial;

    /** @var Carbon */
    private $endOfContract;

    public function setUp()
    {
        parent::setUp();

        $this->usersRepository = \Mockery::mock(UsersRepository::class);
        $this->userModel = \Mockery::mock(\User::class);
        $this->stripeChargeModel = \Mockery::mock(\StripeCharge::class);
        $this->paymentMethodsRepository = \Mockery::mock(PaymentMethodsRepository::class);
        $this->paymentMethodUsersRepository = \Mockery::mock(PaymentMethodUsersRepository::class);

        $this->branch = Branch::make([
            '_id' => new \MongoId('49a7011a05c677b9a916613c'),
            'name' => 'testing',
            'address' => [
                'timezone_id' => 'UTC',
            ],
        ]);
        $this->member = User::make([
            '_id' => new \MongoId('49a7011a05c677b9a916613d'),
        ]);
        $this->membership = Membership::make([
            '_id' => new \MongoId('49a7011a05c677b9a916613e'),
        ]);
        $this->plan = Plan::make([
            'code' => '18237123',
        ]);
        $this->subscriptionPlan = SubscriptionPlan::make([
            'amount' => 10.01,
            'payment_gateway_id' => 123123,
        ]);
        $this->priceToPay = 10;
        $this->endOfTrial = Carbon::today()->addWeek();
        $this->endOfContract = Carbon::today()->addMonth();
    }

    public function tearDown()
    {
        parent::tearDown();

        \Mockery::close();
    }

    public function test_when_successfully_executed_it_attempts_to_updated_the_member_membership(): void
    {
        $provider = \Mockery::mock(Provider::class);
        $provider
            ->shouldReceive('chargePercentage')
            ->andReturn(1.1)
            ->once()
            ->getMock()
            ->shouldReceive('fixedCharge')
            ->andReturn(1.1)
            ->once()
            ->getMock()
            ->shouldReceive('accountId')
            ->andReturn('********')
            ->once()
            ->getMock();

        $paymentMethod = \Mockery::mock(PaymentMethod::class);
        $paymentMethod
            ->shouldReceive('id')
            ->andReturn(new \MongoId('49a7011a05c677b9a916614c'))
            ->once()
            ->getMock()
            ->shouldReceive('provider')
            ->andReturn($provider)
            ->times(3)
            ->getMock()
            ->shouldReceive('typeId')
            ->andReturn(Type::CARD)
            ->once()
            ->getMock();

        $subscription = new Subscription();
        $subscription->setId(Uuid::uuid4()->toString());
        $subscriptions = \Mockery::mock(Subscriptions::class);
        $subscriptions
            ->shouldReceive('subscribe')
            ->andReturn($subscription)
            ->once()
            ->getMock();

        $paymentHandler = \Mockery::mock(GatewayHandler::class);
        $paymentHandler
            ->shouldReceive('paymentMethod')
            ->andReturn($paymentMethod)
            ->times(5)
            ->getMock()
            ->shouldReceive('subscriptions')
            ->andReturn($subscriptions)
            ->once()
            ->getMock();

        $this->userModel
            ->shouldReceive('generate_membership_array')
            ->andReturn([])
            ->once()
            ->getMock();

        $this->stripeChargeModel
            ->shouldReceive('buildSubscriptionCreateMetadata')
            ->andReturn([])
            ->once()
            ->getMock();

        $this->setupRepositories();

        $this->injectDependencies();

        /** @var CreateSubscriptionForOnboardingService $service */
        $service = app()->make(CreateSubscriptionForOnboardingService::class);
        $service->execute(
            $paymentHandler,
            $this->branch,
            $this->member,
            $this->membership,
            $this->plan,
            $this->subscriptionPlan,
            $this->priceToPay,
            $this->endOfTrial,
            $this->endOfContract
        );

        $this->removeDependencies();
    }

    private function setupRepositories(): void
    {
        $this->usersRepository
            ->shouldReceive('setReadConcern')
            ->andReturnSelf()
            ->shouldReceive('addCriteria')
            ->andReturnSelf()
            ->getMock()
            ->shouldReceive('firstOrFail')
            ->andReturn($this->member)
            ->getMock()
            ->shouldReceive('legacySaveOrFail')
            ->andReturn(['User' => []])
            ->once()
            ->getMock();

        $this->paymentMethodsRepository
            ->shouldReceive('setReadConcern')
            ->andReturnSelf()
            ->shouldReceive('addCriteria')
            ->andReturnSelf()
            ->getMock()
            ->shouldReceive('firstOrFail')
            ->andReturn(PaymentMethod::make(['type_id' => Type::CARD]))
            ->getMock()
            ->shouldReceive('first')
            ->andReturn(PaymentMethod::make(['type_id' => Type::CARD]))
            ->getMock();

        $this->paymentMethodUsersRepository
            ->shouldReceive('addCriteria')
            ->andReturnSelf()
            ->getMock()
            ->shouldReceive('skipCallBacks')
            ->andReturnSelf()
            ->getMock()
            ->shouldReceive('withFetchType')
            ->andReturnSelf()
            ->getMock()
            ->shouldReceive('find')
            ->andReturn(PaymentMethodUser::make(['customer_provider_id' => '73487384']))
            ->getMock();
    }

    private function injectDependencies(): void
    {
        app()->instance(UsersRepository::class, $this->usersRepository);
        app()->instance(\User::class, $this->userModel);
        app()->instance(\StripeCharge::class, $this->stripeChargeModel);
        app()->instance(PaymentMethodsRepository::class, $this->paymentMethodsRepository);
        app()->instance(PaymentMethodUsersRepository::class, $this->paymentMethodUsersRepository);
    }

    private function removeDependencies(): void
    {
        app()->forgetInstance(UsersRepository::class);
        app()->forgetInstance(\User::class);
        app()->forgetInstance(\StripeCharge::class);
        app()->forgetInstance(PaymentMethodsRepository::class);
        app()->forgetInstance(PaymentMethodUsersRepository::class);
    }
}
