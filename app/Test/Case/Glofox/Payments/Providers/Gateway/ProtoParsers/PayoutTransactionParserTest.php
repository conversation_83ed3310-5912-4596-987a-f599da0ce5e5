<?php

namespace CakeTestCases\Glofox\Payments\Providers\Gateway\ProtoParsers;

use Carbon\Carbon;
use GRPC\Payments\TransactionService\Transaction;
use GRPC\Payments\TransactionService\PayoutTransaction;
use Glofox\Payments\Providers\Gateway\ProtoParsers\PayoutTransactionParser;


\App::import('Test/Case', 'GlofoxTestCase');

class PayoutTransactionParserTest extends \GlofoxTestCase
{
    public function testParseFromProtoMessage()
    {
        $payoutTransactions = [];

        $date = Carbon::parse('2019-01-03 00:00:00')->getTimestamp();

        $transaction = new Transaction();
        $transaction->setId(123);
        $transaction->setCurrency('EUR');
        $transaction->setType('Type');
        $transaction->setAmount(4000);
        $transaction->setFee(5000);
        $transaction->setCreatedAt($date);

        $payoutTransaction = new PayoutTransaction();
        $payoutTransaction->setID(123);
        $payoutTransaction->setGrossAmount(1001);
        $payoutTransaction->setFee(2001);
        $payoutTransaction->setNetAmount(3001);
        $payoutTransaction->setTransaction($transaction);
        $payoutTransactions[] = $payoutTransaction;

        $parser = new PayoutTransactionParser();

        $parsed = $parser->parseFromProtoMessage($payoutTransaction);

        self::assertEquals(10.01, $parsed->grossAmount());
        self::assertEquals(20.01, $parsed->fee());
        self::assertEquals(30.01, $parsed->netAmount());
    }

    public function testParseFromProtoMessageNonFractionalCurrency()
    {
        $payoutTransactions = [];

        $date = Carbon::parse('2019-01-03 00:00:00')->getTimestamp();

        $transaction = new Transaction();
        $transaction->setId(123);
        $transaction->setCurrency('JPY');
        $transaction->setType('Type');
        $transaction->setAmount(4000);
        $transaction->setFee(5000);
        $transaction->setCreatedAt($date);

        $payoutTransaction = new PayoutTransaction();
        $payoutTransaction->setID(123);
        $payoutTransaction->setGrossAmount(1001);
        $payoutTransaction->setFee(2001);
        $payoutTransaction->setNetAmount(3001);
        $payoutTransaction->setTransaction($transaction);
        $payoutTransactions[] = $payoutTransaction;

        $parser = new PayoutTransactionParser();

        $parsed = $parser->parseFromProtoMessage($payoutTransaction);

        self::assertEquals(1001, $parsed->grossAmount());
        self::assertEquals(2001, $parsed->fee());
        self::assertEquals(3001, $parsed->netAmount());
    }
}
