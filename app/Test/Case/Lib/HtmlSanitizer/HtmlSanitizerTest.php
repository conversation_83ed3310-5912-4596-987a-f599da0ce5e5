<?php

declare(strict_types=1);

namespace CakeTestCases\Lib\HtmlSanitizer;

\App::import('Test/Case', 'GlofoxTestCase');
\App::import('Lib/HtmlSanitizer', 'HtmlSanitizerFactory');
\App::import('Lib/HtmlSanitizer', 'HtmlSanitizer');

/**
 * <AUTHOR>
 */
class HtmlSanitizerTest extends \GlofoxTestCase
{
    public $fixtures = [];

    /**
     * @dataProvider shouldNotBeSanitizedDataProvider
     * @param string $value
     * @return void
     */
    public function testShouldNotBeSanitized(string $value): void
    {
        $sanitizer = \HtmlSanitizerFactory::getSanitizer(
            $value,
            \HtmlSanitizerFactory::HTML_SANITIZER_TYPE
        );
        $sanitizer->execute();

        $this->assertFalse($sanitizer->isSanitized());
    }

    public function shouldNotBeSanitizedDataProvider(): array
    {
        return [
            'Case 1' => [
                'value' => 'There is some v@lid string & several allowed * symb0ls there'
            ],
            'Case 2' => [
                'value' => '<p>some text here</p>    <p>then image <img    src="https://media.giphy.com/media/StKiS6x698JAl9d6cx/giphy.gif"  /></p><p><br/></p><p>the</p>'
            ],
            'Case 3' => [
                'value' => '5 > 4 is true'
            ],
            'Case 4' => [
                'value' => <<<HTML
<p></p><p style="font-size: 12px;"><span style="font-size: 18px;"><strong>Madisons Christmas Party</strong></span></p>
<p style="font-size: 12px;">Date: Friday 9th December</p><p style="font-size: 12px;">Venue: Haywards Heath Golf Club </p><p style="font-size: 12px;">Time: 6.30pm until late!</p>
<p><img src="https://embed.filekitcdn.com/e/4YyCYBLTfHFuZM6PqvgPt4/7KJW3fM1deabgeutozzHiz/email" width="567" style="width: 567px;"/><br/>
</p><p></p><p style="font-size: 12px;">One for a little later in the year … our Christmas party … put the date in your diary as this one is going to go off with a bang.</p>
<p style="font-size: 12px;">Set to be the biggest party of the year, think glitz and glamour as you get ready to finish a strong year with your favourite coaches, friends and family!</p>
<p style="font-size: 12px;">We would love to have as many of you there as possible so save the date and spread the word.</p>
<p style="font-size: 12px;">Much love, </p><p style="font-size: 12px;">The Madisons team</p><p><br/></p>
HTML,
            ],
            'Case 5' => [
                'value' => <<<HTML
<h1>some text here</h1><p><i>then</i> image </p><p>sfsdfsfsdf dfdv yty <u>trtr</u> rte f.</p><p><b>the</b></p><ul><li>dfgdg</li>
<li>dfgdfg</li></ul><p>dfgd</p><p>dfg</p><p>e</p><p>er</p><p><a href="https://clarke.gfdevlab.com/dashboard/#/announcements" target="">Announcements</a>
</p><p>sad</p><p>asas</p><ol><li>asdada</li><li>asdasd</li></ol><p><br/></p><p><br/></p><p>
<span id="selectionBoundary_1665567707299_8152591201233024" class="rangySelectionBoundary">Some text here</span>
<br/></p><p>
<br/></p><p><br/></p><p><br/></p><p><br/></p>
HTML,
            ],
            'Case 6' => [
                'value' => <<<HTML
<h2>Join us for soulfoul, joyful and madly divine weekend of chanting & flowing with Colette & Nikki!<br/></h2><p>Saturday 11.15-11.45 Magical Mantra  with Colette <br/>
12-13.30 Mad Lotus Flow with Colette<br/>19-20.30 Mad Dulce XL with Nikki (Restorative & Yoga Nidra)<br/>Sunday  11.15-11.45 Magical Mantra  with Colette <br/>
12-13.30 Mad Lotus Flow with Colette<br/><br/>* All classes in English<br/>
** Mad Lotus students can use a credit for each class (Magical Mantra is free but please remember to sign up) !!!<br/></p>
HTML,
            ],
            'Case 7' => [
                'value' => <<<HTML
<p class="p1" style="font-size: 17.2px;"><span class="s1" style="font-size: 17.16px;">SCHEDULE UPDATE </span></p><p class="p2" style="font-size: 17.2px;">
<span class="s1" style="font-size: 17.16px;"></span><br/></p><p class="p1" style="font-size: 17.2px;">
<span class="s1" style="font-size: 17.16px;">Please note that starting <b>Sunday 16th of October </b>our evening Schedule will change.</span></p>
<p class="p2" style="font-size: 17.2px;"><span class="s1" style="font-size: 17.16px;"></span><br/></p><p class="p1" style="font-size: 17.2px;">
<span class="s1" style="font-size: 17.16px;">16 : 30 MIXED </span></p><p class="p1" style="font-size: 17.2px;"><span class="s1" style="font-size: 17.16px;">17 : 30 <i>LADIES ONLY</i> </span></p>
<p class="p1" style="font-size: 17.2px;"><span class="s1" style="font-size: 17.16px;">18 : 30 MIXED</span></p>
<p class="p2" style="font-size: 17.2px;"><span class="s1" style="font-size: 17.16px;"></span><br/></p>
<p class="p1" style="font-size: 17.2px;"><span class="s1" style="font-size: 17.16px;">Don’t forget to book your spots, happy training. </span></p>
HTML,
            ],
            'Case 8' => [
                'value' => <<<HTML
<p>Have you tried Nutrition Kitchen yet? Choose from a variety of meal plans to find the ideal dietitian-approved meals to fuel your body with healthy, nutrient-dense meals,
 ready to smash your goals. <br/><br/>Enjoy 20% off all meal plan packages for Unlimited members with code <b>F45TS20. </b><br/>
 Redeem only 15% off all meal plan packages for Class Pack members with code <b>F45TS15.</b><br/></p><p><b><br/></b></p>
 <p><b><a href="https://nutritionkitchenuae.com/" target="">https://nutritionkitchenuae.com/</a><br/></b></p>
HTML,
            ],
            'Case 9' => [
                'value' => <<<HTML
<p style="font-size: 15px;">Get yourself pampered at Refresh Hair & Beauty Bar with the following deals for F45 members:</p><p style="font-size: 15px;"><br/></p>
<p style="font-size: 15px;"><b>Post-gym Blow Dry:</b> 99AED.</p><p style="font-size: 15px;"><b>Post-gym Facial:</b> 199AED.</p>
<p style="font-size: 15px;"><b>Post-gym Pedi: </b>Get a free 10 minute foot massage when you purchase any pedicure treatment.</p>
<p style="font-size: 15px;"><br/></p><p style="font-size: 15px;">Available Monday - Thursday.</p><p style="font-size: 15px;"><br/></p>
<p style="font-size: 15px;">Call or Whatsapp +971585224616 to make your booking. A valid membership will need to be shown on arrival at the salon.</p>
HTML,
            ],
            'Case 10' => [
                'value' => <<<HTML
<p><br/></p><p><br/></p><!--StartFragment--><p style="font-size: 18px;text-align: left;"><strong>Norman is an active member at Club Pilates Oceanside in Long Island,
 New York, as well as a Teacher Training graduate and now certified Instructor! Read his story here.</strong></p><h3 style="font-size: 36px;">What made you want to try Pilates?</h3>
 <p style="font-size: 18px;text-align: left;">When my wife started going to<a href="http://clubpilates.com/oceanside?amp_device_id=-qElxnRDO3zD1rq8Zv2E3G" rel="noopener" target="_blank">
 Club Pilates Oceanside,</a>I noticed a big difference in her body and saw that she was gaining some tremendous benefits from it, so I thought I'd give it a try as well.</p>
 <h2 style="font-size: 44px;">How has Pilates enhanced your life?</h2><p style="font-size: 18px;text-align: left;">
 I have better balance, I feel more grounded, I have a clearer mind and more self-confidence. Practicing Pilates has also improved by Qi-Gong practice, which I’ve been practicing for 7 years.
  I find my energy flow builds up quicker, my movements are smoother and I don't fall as often.</p><h4 style="font-size: 28px;">
  I also like the fact that I was seeing similar benefits as weight training but without the wear and tear and damage to my joints.</h4>
  <p style="font-size: 18px;text-align: left;">I love that Club Pilates classes combine cardio, flexibility and strength.</p>
  <h4 style="font-size: 28px;">I found things from my Pilates practice that I wasn't getting from my other workout modalities, and this has helped me in my overall life.</h4>
  <p style="font-size: 18px;text-align: left;">I work in the construction department for a major corporation and Pilates has allowed me to do physical work that my colleagues aren't able to do.
   I can maneuver around areas and lift things that my coworkers aren’t able to.</p><h4 style="font-size: 28px;">
   My endurance had greatly improved - I can work for longer periods of time than I could in the past. Pilates keeps me young at heart.</h4>
   <h2 style="font-size: 44px;"><br/>What made you decide to take your love for Pilates to the next level and become a Club Pilates Instructor?</h2>
   <p style="font-size: 18px;text-align: left;">I love to teach and I was drawn to<a href="https://www.clubpilates.com/education?amp_device_id=-qElxnRDO3zD1rq8Zv2E3G" rel="noopener" target="_blank">
   Teacher Training</a>as I knew it would help me better understand the exercises and be more mindful of correct form while in class.
   I felt blessed to have Pilates presented to me and I wanted to pay it forward by teaching others so they could experience the benefits that I have.</p>
   <p style="font-size: 18px;text-align: left;"></p><h2 style="font-size: 44px;">What did you learn about yourself going through the Teacher Training program?</h2>
   <p style="font-size: 18px;text-align: left;">Going through the<a href="https://www.clubpilates.com/education?amp_device_id=-qElxnRDO3zD1rq8Zv2E3G" rel="noopener" target="_blank">
   Teacher Training</a>program, I learned that I could finish something that I started and that was a very motivating and uplifting feeling.
   The support I got from the entire Club Pilates Oceanside family was like nothing I ever experienced.</p><h4 style="font-size: 28px;">
   I never felt at any point that I was left on my own. There were always people to help answer my questions and cheer me on.</h4><p style="font-size: 18px;text-align: left;"></p>
   <h2 style="font-size: 44px;">How might your life be different if you never tried Pilates?</h2><p style="font-size: 18px;text-align: left;">
   I don't think I would have found anything that motivated me and gave me the results I wanted had I not discovered Pilates.
   I’d be a couch potato and I'd be lonely without the great people and energy of my studio.</p><h4 style="font-size: 28px;">Pilates has shifted my mindset. I feel like I’ve reinvented myself.</h4>
   <p style="font-size: 18px;text-align: left;">Club Pilates has given me an opportunity to find my own voice and my own identity, and Teacher Training has given me something to be confident about.</p>
   <p style="font-size: 18px;text-align: left;"></p><h2 style="font-size: 44px;">What’s something you would want others to know about Pilates?</h2>
   <p style="font-size: 18px;text-align: left;">I’d want them to know about the health benefits. How quickly you find yourself getting stronger,
   how you can get into certain positions other modalities don't teach and the holistic mind-body connection that’s hard to explain, but needs to be experienced.</p>
   <p style="font-size: 18px;text-align: left;"></p><h2 style="font-size: 44px;">What would you say specifically to men who have never tried Pilates?</h2>
   <h4 style="font-size: 28px;">If you think weightlifting is challenging, try Pilates!</h4><p style="font-size: 18px;text-align: left;">
   You'll gain such respect for the art of Pilates. It strengthens muscles that other exercises don't address. I was amazed at how quickly I got results at CPO than I did when I was weightlifting.</p>
   <h2 style="font-size: 44px;">What keeps you coming back to CPO?</h2><p style="font-size: 18px;text-align: left;">The friendships I made here, the family atmosphere,
   and the positive and upbeat energy. And through teaching, I feel like I'm giving back something that I was blessed with being introduced to.
   It feels like you're part of something bigger than yourself.</p><h2 style="font-size: 44px;">Tell us a fun fact about you:</h2>
   <p style="font-size: 18px;text-align: left;">I’m in a band called Beyond Norm, and being a musician helps me be able to adjust and change on a dime while teaching classes.
   I also like to add my Rock & Roll flair to the classes I teach - I created a "Ring of Fire" themed class centered around the Magic Circle and I always use Rock & Roll playlists in my classes.</p>
   <!--EndFragment--><p><br/></p><p><br/></p>
HTML,
            ],
            'Case 11' => [
                'value' => <<<HTML
<p><a href="https://thecsicompanies-my.sharepoint.com/:i:/g/personal/rmurphy_csicompanies_com/EYHtrSUEV8ZAsyalryZUz7kBBJEc6mU6Rg5fXG86xH8iDA?e=hYZOEa" target="">
https://thecsicompanies-my.sharepoint.com/:i:/g/personal/rmurphy_csicompanies_com/EYHtrSUEV8ZAsyalryZUz7kBBJEc6mU6Rg5fXG86xH8iDA?e=hYZOEa</a></p>
HTML,
            ],
            'Case 12' => [
                'value' => <<<HTML
<p><br/><br/><!--StartFragment--><a href="https://vimeo.com/757701071/a572e31564" target="_blank" style="font-size: small;">https://vimeo.com/757701071/<wbr/>a572e31564</a>
<!--EndFragment--><br/><br/><br/></p>
HTML,
            ],
            'Case 13' => [
                'value' => <<<HTML
<p><a href="https://thecsicompanies-my.sharepoint.com/:i:/g/personal/rmurphy_csicompanies_com/EYHtrSUEV8ZAsyalryZUz7kBBJEc6mU6Rg5fXG86xH8iDA?e=hYZOEa" target="">
https://thecsicompanies-my.sharepoint.com/:i:/g/personal/rmurphy_csicompanies_com/EYHtrSUEV8ZAsyalryZUz7kBBJEc6mU6Rg5fXG86xH8iDA?e=hYZOEa</a></p>
HTML,
            ],
            'Case 14 Emoji' => [
                'value' => <<<HTML
<p><span>Monday 7th &amp; Wednesday 9th November &#64;7pm </span><span>💪🏻</span><span></span></p><p><span></span></p>
<p><span>Come in &amp; find out what HYROX is all about. </span></p><p><span></span></p>
<p><span>We’ll take you through a taster session, explain all about the concept of Hyrox &amp; how sessions will work at TT. </span></p>
<p><span></span></p><p><span>We’re so excited for this new era of the gym &amp; how we can continue to build this community! </span></p>
<p><span></span></p><p><span>Sign up now on the sign up sheet in the gym or send us a DM to get your space! </span></p>
<p><span></span></p><p><span>FREE taster for members &amp; non-members. </span></p><p><span></span></p>
<p><span>(You won’t have to be a member to do the Hyrox classes either - we’ll have hyrox only / class only packages 
/ payg options available so that it’s for everyone </span><span>✨</span><span>) </span></p>
HTML,
            ],
        ];
    }

    /**
     * @dataProvider shouldPassValidationDataProvider
     * @param string $value
     * @return void
     */
    public function testShouldPassValidation(string $value): void
    {
        $sanitizer = \HtmlSanitizerFactory::getSanitizer(
            $value,
            \HtmlSanitizerFactory::HTML_SANITIZER_TYPE
        );
        $sanitizer->execute();

        $this->assertTrue($sanitizer->isSanitized());
    }

    public function shouldPassValidationDataProvider(): array
    {
        return [
            'Case 1' => [
                'value' => '><img/src="x"onmouseover=alert(555)>'
            ],
            'Case 2' => [
                'value' => '<div style=content:url(%(svg)s)></div>'
            ],
            'Case 3' => [
                'value' => '<IMG SRC="javascript:alert(\'XSS\');">'
            ],
            'Case 4' => [
                'value' => '&lt;STYLE&gt;BODY{-moz-binding&#58;url(\"http&#58;//ha&#46;ckers&#46;org/xssmoz&#46;xml#xss\")}&lt;/STYLE&gt;'
            ],
            'Case 5' => [
                'value' => '<IMG SRC=x onpageshow="alert(String.fromCharCode(88,83,83))">'
            ],
            'Case 6' => [
                'value' => '<body onMouseMove body onMouseMove="javascript:javascript:alert(1)"></body onMouseMove>'
            ],
            'Case 7' => [
                'value' => "<svg><script xlink:href=data&colon;,window.open('https://www.google.com/')></script"
            ],
            'Case 8' => [
                'value' => '<div id="x">XXX</div> <style>  #x{font-family:foo[bar;color:green;}  #y];color:red;{}  </style>'
            ],
            'Case 9' => [
                'value' => '<STYLE TYPE="text/javascript">javascript:alert(1);</STYLE>'
            ],
            'Case 10' => [
                'value' => '<OBJECT TYPE="text/x-scriptlet" DATA="%(scriptlet)s"></OBJECT>'
            ],
            'Case 11' => [
                'value' => '<HEAD><META HTTP-EQUIV="CONTENT-TYPE" CONTENT="text/html; charset=UTF-7"> </HEAD>+ADw-SCRIPT+AD4-%(payload)s;+ADw-/SCRIPT+AD4-'
            ],
            'Case 12' => [
                'value' => '<embed type="image" src=%(scriptlet)s></embed>'
            ],
            'Case 13' => [
                'value' => '<IMG SRC="javascript:alert(\'XSS\');">'
            ],
            'Case 14' => [
                'value' => "<!--#exec cmd=\"/bin/echo '<SCR'\"--><!--#exec cmd=\"/bin/echo 'IPT SRC=http://ha.ckers.org/xss.js></SCRIPT>'\"-->"
            ],
            'Case 15' => [
                'value' => "<? echo('<SCR)';echo('IPT>alert(\"XSS\")</SCRIPT>'); ?>"
            ],
            'Case 16' => [
                'value' => '<img/&#09;&#10;&#11; src=`~` onerror=prompt(1)>'
            ],
            'Case 17' => [
                'value' => '<--`<img/src=` onerror=alert(1)> --!>'
            ],
            'Case 18' => [
                'value' => "<%<!--'%><script>alert(1);</script -->"
            ],
            'Case 19' => [
                'value' => <<<HTML
<IMG SRC=&#0000106&#0000097&#0000118&#0000097&#0000115&#0000099&#0000114&#0000105&#0000112&#0000116&#0000058&#0000097&
#0000108&#0000101&#0000114&#0000116&#0000040&#0000039&#0000088&#0000083&#0000083&#0000039&#0000041>'
HTML,
            ],
            'Case 20' => [
                'value' => '<input type="image" dynsrc="javascript:document.vulnerable=true;">'
            ],
            'Case 21. Invalid html, should be cleaned up.' => [
                'value' => <<<HTML
<p style="font-size: medium;"><b><u><span style="font-size: 13pt;color: black;">MADISONS HALLOWEEN PARTY!</span></u></b></p><p style="font-size: medium;">
<span style="font-size: 13pt;">You’re cordially invited to join us for...</span></p><p style="font-size: medium;">
<span style="font-size: 13pt;">A BLOODY GOOD TIME as we eat, drink and be scary.</span></p><p style="font-size: medium;">
<span style="font-size: 13pt;color: black;">Please do not be &#34;afraid&#34; to bring your own drinks, your spookiest looks and your scariest dance moves!</span>
</p><ul><li><b style="font-size: 15px;"><span style="font-size: 13pt;">Date:</span></b><span style="font-size: 13pt;">Friday 28<sup>th</sup>October</span></li>
<li><b style="font-size: 15px;">Venue: </b><span style="font-size: 15px;">MINCKA</span></li><li><b><span style="font-size: 13pt;">Dress code:</span>
</b><span style="font-size: 13pt;">Scary! We want everyone to dress up</span></li><li><b style="font-size: 15px;">What time? </b>
<span style="font-size: 15px;">18:30-22:30</span></li></ul><p></p><p style="font-size: medium;"><b><i><u><span style="font-size: 13pt;color: black;">Bring your own booze!</span>
</u></i></b></p><p style="font-size: medium;"><i><span style="font-size: 13pt;color: black;">There will also be: </span></i></p><ul><li>
<span style="font-size: 13pt;">Prizes for the best dress male and female</span></li><li><span style="font-size: 13pt;">Some nibbles and drinks</span></li>
<li><span style="font-size: 13pt;">An enjoyable evening with the Madisons Family and the opportunity to bring your own booze (or soft drinks hehe)!</span></li></ul>
<p><span style="font-size: 13pt;">The Madisons Team</span><br/></p><p style="font-size: medium;"><span style="font-size: 13pt;"></span></p><p><br/></p>
HTML,
            ],
            'Case 22' => [
                'value' => '<a href="javas\x09cript:javascript:alert(1)" id="fuzzelement1">test</a>'
            ],
            'Case 23' => [
                'value' => '<p>asdas</p><p>&lt;a href=&#34;javas\x09cript:javascript:alert(1)&#34; id=&#34;fuzzelement1&#34;&gt;test&lt;/a&gt;<br/></p>'
            ],
            'Case 24' => [
                'value' => '"onclick=prompt(8)><svg/onload=prompt(8)>"@x.y'
            ],
            'Case 25' => [
                'value' => '<image/src/onerror=prompt(8)>'
            ],
        ];
    }
}
