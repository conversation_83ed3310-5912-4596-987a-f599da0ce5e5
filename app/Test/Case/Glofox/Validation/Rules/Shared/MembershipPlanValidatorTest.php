<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Validation\Rules\Shared;

use Glofox\Domain\Memberships\Events\MembershipFloorPricingPurchaseAttempt;
use Glofox\Domain\Memberships\Exceptions\MembershipPlanMinimumPriceValidationException;
use Glofox\Domain\Memberships\Models\Membership;
use Glofox\Domain\Memberships\Models\Plan;
use Glofox\Events\EventManager;
use Glofox\NotFoundException;
use Illuminate\Validation\Factory;

\App::import('Test/Case', 'GlofoxTestCase');

class MembershipPlanValidatorTest extends \GlofoxTestCase
{
    /** @var Factory */
    private $validatorFactory;

    public function setUp()
    {
        parent::setUp();
        $this->validatorFactory = app()->make('validator');
    }

    public function tearDown()
    {
        app()->forgetInstance(EventManager::class);

        \Mockery::close();

        parent::tearDown();
    }

    public function test_it_fails_if_membership_does_not_exist(): void
    {
        $this->setExpectedException(\NotFoundException::class);

        $validator = $this->validatorFactory->make(
            ['discounted_subscription_amount' => 45.67],
            ['discounted_subscription_amount' => 'membershipMinimumPrice:5f159cba62220714d0ee4ce1,123']
        );

        $validator->passes();
    }

    public function test_it_fails_if_the_plan_does_not_exist(): void
    {
        $this->setExpectedException(NotFoundException::class, 'MEMBERSHIP_PLAN_CODE_NOT_FOUND');

        $validator = $this->validatorFactory->make(
            ['discounted_subscription_amount' => 45.67],
            ['discounted_subscription_amount' => 'membershipMinimumPrice:5f159e6962220714d0ee4ce2,23456789']
        );

        $validator->passes();
    }

    public function test_it_fails_if_price_is_lower_than_minimum_price(): void
    {
        $this->setExpectedException(MembershipPlanMinimumPriceValidationException::class);

        $eventManager = \Mockery::mock(EventManager::class);
        $eventManager->shouldReceive('emit')
            ->withArgs(
                function (string $eventName, array $parameters) {
                    $this->assertSame(MembershipFloorPricingPurchaseAttempt::class, $eventName);
                    $this->assertInstanceOf(Membership::class, $parameters[0]);
                    $this->assertInstanceOf(Plan::class, $parameters[1]);
                    $this->assertSame(123_456_789, $parameters[1]->code());
                    $this->assertSame(29.99, $parameters[2]);

                    return true;
                }
            );

        app()->instance(EventManager::class, $eventManager);

        $validator = $this->validatorFactory->make(
            ['discounted_subscription_amount' => 29.99], // min price is 30
            ['discounted_subscription_amount' => 'membershipMinimumPrice:5f159e6962220714d0ee4ce2,123456789']
        );

        $validator->passes();

        app()->forgetInstance(EventManager::class);
    }

    public function test_it_returns_true_if_the_price_passed_validation(): void
    {
        $validator = $this->validatorFactory->make(
            ['discounted_subscription_amount' => 30], // min price is 30
            ['discounted_subscription_amount' => 'membershipMinimumPrice:5f159e6962220714d0ee4ce2,123456789']
        );

        $this->assertTrue($validator->passes());
    }

    public function test_it_returns_true_if_the_price_passed_is_null(): void
    {
        $validator = $this->validatorFactory->make(
            ['discounted_subscription_amount' => null], // min price is 30
            ['discounted_subscription_amount' => 'membershipMinimumPrice:5f159e6962220714d0ee4ce2,123456789']
        );

        $this->assertTrue($validator->passes());
    }
}
