{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "c85f10def9a7a747fed69e9aece23dc0", "packages": [{"name": "alcaeus/mongo-php-adapter", "version": "1.2.4", "source": {"type": "git", "url": "https://github.com/alcaeus/mongo-php-adapter.git", "reference": "7d488cc5b3ba6802d9237a71e5ed8d72391f15e7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/alcaeus/mongo-php-adapter/zipball/7d488cc5b3ba6802d9237a71e5ed8d72391f15e7", "reference": "7d488cc5b3ba6802d9237a71e5ed8d72391f15e7", "shasum": ""}, "require": {"ext-ctype": "*", "ext-hash": "*", "ext-mongodb": "^1.2.0", "mongodb/mongodb": "^1.0.1", "php": "^5.6 || ^7.0 || ^8.0"}, "provide": {"ext-mongo": "1.6.14"}, "require-dev": {"squizlabs/php_codesniffer": "^3.2", "symfony/phpunit-bridge": "^4.4.16 || ^5.2"}, "type": "library", "extra": {"branch-version": "1.x"}, "autoload": {"files": ["lib/Mongo/functions.php"], "psr-0": {"Mongo": "lib/Mongo"}, "psr-4": {"Alcaeus\\MongoDbAdapter\\": "lib/Alcaeus/MongoDbAdapter"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "alcaeus", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Adapter to provide ext-mongo interface on top of mongo-php-library", "keywords": ["database", "mongodb"], "support": {"issues": "https://github.com/alcaeus/mongo-php-adapter/issues", "source": "https://github.com/alcaeus/mongo-php-adapter/tree/1.2.4"}, "time": "2023-09-29T15:00:28+00:00"}, {"name": "aws/aws-crt-php", "version": "v1.2.1", "source": {"type": "git", "url": "https://github.com/awslabs/aws-crt-php.git", "reference": "1926277fc71d253dfa820271ac5987bdb193ccf5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/awslabs/aws-crt-php/zipball/1926277fc71d253dfa820271ac5987bdb193ccf5", "reference": "1926277fc71d253dfa820271ac5987bdb193ccf5", "shasum": ""}, "require": {"php": ">=5.5"}, "require-dev": {"phpunit/phpunit": "^4.8.35||^5.6.3||^9.5", "yoast/phpunit-polyfills": "^1.0"}, "suggest": {"ext-awscrt": "Make sure you install awscrt native extension to use any of the functionality."}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "AWS SDK Common Runtime Team", "email": "<EMAIL>"}], "description": "AWS Common Runtime for PHP", "homepage": "https://github.com/awslabs/aws-crt-php", "keywords": ["amazon", "aws", "crt", "sdk"], "support": {"issues": "https://github.com/awslabs/aws-crt-php/issues", "source": "https://github.com/awslabs/aws-crt-php/tree/v1.2.1"}, "time": "2023-03-24T20:22:19+00:00"}, {"name": "aws/aws-php-sns-message-validator", "version": "1.8.0", "source": {"type": "git", "url": "https://github.com/aws/aws-php-sns-message-validator.git", "reference": "ea20e69fe15139540c6f20ff7c61ed48b9804bfd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/aws/aws-php-sns-message-validator/zipball/ea20e69fe15139540c6f20ff7c61ed48b9804bfd", "reference": "ea20e69fe15139540c6f20ff7c61ed48b9804bfd", "shasum": ""}, "require": {"ext-openssl": "*", "php": ">=5.4", "psr/http-message": "^1.0"}, "require-dev": {"guzzlehttp/psr7": "^1.4", "phpunit/phpunit": "^4.0", "squizlabs/php_codesniffer": "^2.3"}, "type": "library", "autoload": {"psr-4": {"Aws\\Sns\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Amazon Web Services", "homepage": "http://aws.amazon.com"}], "description": "Amazon SNS message validation for PHP", "homepage": "http://aws.amazon.com/sdkforphp", "keywords": ["SNS", "amazon", "aws", "cloud", "message", "sdk", "webhooks"], "support": {"forum": "https://forums.aws.amazon.com/forum.jspa?forumID=80", "issues": "https://github.com/aws/aws-sns-message-validator/issues", "source": "https://github.com/aws/aws-php-sns-message-validator/tree/1.8.0"}, "time": "2022-08-22T19:38:21+00:00"}, {"name": "aws/aws-sdk-php", "version": "3.263.4", "source": {"type": "git", "url": "https://github.com/aws/aws-sdk-php.git", "reference": "08e2f243243a9fcdd8909d596e46f81d8c72cb60"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/aws/aws-sdk-php/zipball/08e2f243243a9fcdd8909d596e46f81d8c72cb60", "reference": "08e2f243243a9fcdd8909d596e46f81d8c72cb60", "shasum": ""}, "require": {"aws/aws-crt-php": "^1.0.4", "ext-json": "*", "ext-pcre": "*", "ext-simplexml": "*", "guzzlehttp/guzzle": "^6.5.8 || ^7.4.5", "guzzlehttp/promises": "^1.4.0", "guzzlehttp/psr7": "^1.8.5 || ^2.3", "mtdowling/jmespath.php": "^2.6", "php": ">=5.5"}, "require-dev": {"andrewsville/php-token-reflection": "^1.4", "aws/aws-php-sns-message-validator": "~1.0", "behat/behat": "~3.0", "composer/composer": "^1.10.22", "dms/phpunit-arraysubset-asserts": "^0.4.0", "doctrine/cache": "~1.4", "ext-dom": "*", "ext-openssl": "*", "ext-pcntl": "*", "ext-sockets": "*", "nette/neon": "^2.3", "paragonie/random_compat": ">= 2", "phpunit/phpunit": "^4.8.35 || ^5.6.3 || ^9.5", "psr/cache": "^1.0", "psr/simple-cache": "^1.0", "sebastian/comparator": "^1.2.3 || ^4.0", "yoast/phpunit-polyfills": "^1.0"}, "suggest": {"aws/aws-php-sns-message-validator": "To validate incoming SNS notifications", "doctrine/cache": "To use the DoctrineCacheAdapter", "ext-curl": "To send requests using cURL", "ext-openssl": "Allows working with CloudFront private distributions and verifying received SNS messages", "ext-sockets": "To use client-side monitoring"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"files": ["src/functions.php"], "psr-4": {"Aws\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Amazon Web Services", "homepage": "http://aws.amazon.com"}], "description": "AWS SDK for PHP - Use Amazon Web Services in your PHP project", "homepage": "http://aws.amazon.com/sdkforphp", "keywords": ["amazon", "aws", "cloud", "dynamodb", "ec2", "glacier", "s3", "sdk"], "support": {"forum": "https://forums.aws.amazon.com/forum.jspa?forumID=80", "issues": "https://github.com/aws/aws-sdk-php/issues", "source": "https://github.com/aws/aws-sdk-php/tree/3.263.4"}, "time": "2023-04-05T18:21:54+00:00"}, {"name": "cakephp/cakephp", "version": "2.10.24", "source": {"type": "git", "url": "https://github.com/cakephp/cakephp.git", "reference": "cf14e6546ec44e3369e3531add11fdb946656280"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/cakephp/cakephp/zipball/cf14e6546ec44e3369e3531add11fdb946656280", "reference": "cf14e6546ec44e3369e3531add11fdb946656280", "shasum": ""}, "require": {"php": ">=5.3.0,<8.0.0"}, "require-dev": {"cakephp/cakephp-codesniffer": "^1.0.0", "phpunit/phpunit": "^3.7"}, "suggest": {"ext-mcrypt": "You need to install ext-openssl or ext-mcrypt to use AES-256 encryption", "ext-openssl": "You need to install ext-openssl or ext-mcrypt to use AES-256 encryption"}, "bin": ["lib/Cake/Console/cake"], "type": "library", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "CakePHP Community", "homepage": "https://github.com/cakephp/cakephp/graphs/contributors"}], "description": "The CakePHP framework", "homepage": "https://cakephp.org", "keywords": ["framework"], "support": {"forum": "https://stackoverflow.com/tags/cakephp", "irc": "irc://irc.freenode.org/cakephp", "issues": "https://github.com/cakephp/cakephp/issues", "source": "https://github.com/cakephp/cakephp"}, "time": "2020-12-16T02:47:53+00:00"}, {"name": "cakephp/monolog", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/jadb/cakephp-monolog.git", "reference": "73bfc438aeafa43cc44027e7444b4f82528b3df7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jadb/cakephp-monolog/zipball/73bfc438aeafa43cc44027e7444b4f82528b3df7", "reference": "73bfc438aeafa43cc44027e7444b4f82528b3df7", "shasum": ""}, "require": {"composer/installers": "*", "monolog/monolog": "1.7.*", "php": ">=5.3.0"}, "type": "cakephp-plugin", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Others", "homepage": "https://github.com/jadb/cakephp-monolog/graphs/contributors"}, {"name": "<PERSON><PERSON>", "homepage": "http://jadb.io", "role": "Author"}], "description": "CakePHP Monolog Plugin", "homepage": "https://github.com/jadb/cakephp-monolog", "keywords": ["cakephp", "monolog", "plugin"], "support": {"issues": "https://github.com/jadb/cakephp-monolog/issues", "source": "https://github.com/jadb/cakephp-monolog"}, "abandoned": "jadb/cakephp-monolog", "time": "2014-08-04T14:44:12+00:00"}, {"name": "clue/stream-filter", "version": "v1.6.0", "source": {"type": "git", "url": "https://github.com/clue/stream-filter.git", "reference": "d6169430c7731d8509da7aecd0af756a5747b78e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/clue/stream-filter/zipball/d6169430c7731d8509da7aecd0af756a5747b78e", "reference": "d6169430c7731d8509da7aecd0af756a5747b78e", "shasum": ""}, "require": {"php": ">=5.3"}, "require-dev": {"phpunit/phpunit": "^9.3 || ^5.7 || ^4.8.36"}, "type": "library", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"Clue\\StreamFilter\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A simple and modern approach to stream filtering in PHP", "homepage": "https://github.com/clue/php-stream-filter", "keywords": ["bucket brigade", "callback", "filter", "php_user_filter", "stream", "stream_filter_append", "stream_filter_register"], "support": {"issues": "https://github.com/clue/stream-filter/issues", "source": "https://github.com/clue/stream-filter/tree/v1.6.0"}, "funding": [{"url": "https://clue.engineering/support", "type": "custom"}, {"url": "https://github.com/clue", "type": "github"}], "time": "2022-02-21T13:15:14+00:00"}, {"name": "composer/installers", "version": "v2.2.0", "source": {"type": "git", "url": "https://github.com/composer/installers.git", "reference": "c29dc4b93137acb82734f672c37e029dfbd95b35"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/installers/zipball/c29dc4b93137acb82734f672c37e029dfbd95b35", "reference": "c29dc4b93137acb82734f672c37e029dfbd95b35", "shasum": ""}, "require": {"composer-plugin-api": "^1.0 || ^2.0", "php": "^7.2 || ^8.0"}, "require-dev": {"composer/composer": "1.6.* || ^2.0", "composer/semver": "^1 || ^3", "phpstan/phpstan": "^0.12.55", "phpstan/phpstan-phpunit": "^0.12.16", "symfony/phpunit-bridge": "^5.3", "symfony/process": "^5"}, "type": "composer-plugin", "extra": {"class": "Composer\\Installers\\Plugin", "branch-alias": {"dev-main": "2.x-dev"}, "plugin-modifies-install-path": true}, "autoload": {"psr-4": {"Composer\\Installers\\": "src/Composer/Installers"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/shama"}], "description": "A multi-framework Composer library installer", "homepage": "https://composer.github.io/installers/", "keywords": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "ImageCMS", "Kanboard", "Lan Management System", "MODX Evo", "MantisBT", "Mautic", "Maya", "OXID", "Plentymarkets", "Porto", "RadPHP", "SMF", "Starbug", "Thelia", "Whmcs", "WolfCMS", "agl", "annotatecms", "attogram", "bitrix", "cakephp", "chef", "cockpit", "codeigniter", "concrete5", "croogo", "<PERSON><PERSON><PERSON><PERSON>", "drupal", "eZ Platform", "elgg", "expressionengine", "fuelphp", "grav", "installer", "itop", "known", "kohana", "laravel", "lavalite", "lithium", "magento", "majima", "mako", "matomo", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "modulework", "modx", "moodle", "osclass", "pantheon", "phpbb", "piwik", "ppi", "processwire", "puppet", "pxcms", "reindex", "roundcube", "shopware", "silverstripe", "sydes", "sylius", "tastyigniter", "wordpress", "yawik", "zend", "zikula"], "support": {"issues": "https://github.com/composer/installers/issues", "source": "https://github.com/composer/installers/tree/v2.2.0"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2022-08-20T06:45:11+00:00"}, {"name": "doctrine/annotations", "version": "1.14.3", "source": {"type": "git", "url": "https://github.com/doctrine/annotations.git", "reference": "fb0d71a7393298a7b232cbf4c8b1f73f3ec3d5af"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/annotations/zipball/fb0d71a7393298a7b232cbf4c8b1f73f3ec3d5af", "reference": "fb0d71a7393298a7b232cbf4c8b1f73f3ec3d5af", "shasum": ""}, "require": {"doctrine/lexer": "^1 || ^2", "ext-tokenizer": "*", "php": "^7.1 || ^8.0", "psr/cache": "^1 || ^2 || ^3"}, "require-dev": {"doctrine/cache": "^1.11 || ^2.0", "doctrine/coding-standard": "^9 || ^10", "phpstan/phpstan": "~1.4.10 || ^1.8.0", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "symfony/cache": "^4.4 || ^5.4 || ^6", "vimeo/psalm": "^4.10"}, "suggest": {"php": "PHP 8.0 or higher comes with attributes, a native replacement for annotations"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Annotations\\": "lib/Doctrine/Common/Annotations"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "Docblock Annotations Parser", "homepage": "https://www.doctrine-project.org/projects/annotations.html", "keywords": ["annotations", "doc<PERSON>", "parser"], "support": {"issues": "https://github.com/doctrine/annotations/issues", "source": "https://github.com/doctrine/annotations/tree/1.14.3"}, "time": "2023-02-01T09:20:38+00:00"}, {"name": "doctrine/cache", "version": "1.13.0", "source": {"type": "git", "url": "https://github.com/doctrine/cache.git", "reference": "56cd022adb5514472cb144c087393c1821911d09"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/cache/zipball/56cd022adb5514472cb144c087393c1821911d09", "reference": "56cd022adb5514472cb144c087393c1821911d09", "shasum": ""}, "require": {"php": "~7.1 || ^8.0"}, "conflict": {"doctrine/common": ">2.2,<2.4"}, "require-dev": {"alcaeus/mongo-php-adapter": "^1.1", "cache/integration-tests": "dev-master", "doctrine/coding-standard": "^9", "mongodb/mongodb": "^1.1", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "predis/predis": "~1.0", "psr/cache": "^1.0 || ^2.0 || ^3.0", "symfony/cache": "^4.4 || ^5.4 || ^6", "symfony/var-exporter": "^4.4 || ^5.4 || ^6"}, "suggest": {"alcaeus/mongo-php-adapter": "Required to use legacy MongoDB driver"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Cache\\": "lib/Doctrine/Common/Cache"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Cache library is a popular cache implementation that supports many different drivers such as redis, memcache, apc, mongodb and others.", "homepage": "https://www.doctrine-project.org/projects/cache.html", "keywords": ["abstraction", "apcu", "cache", "caching", "couchdb", "memcached", "php", "redis", "xcache"], "support": {"issues": "https://github.com/doctrine/cache/issues", "source": "https://github.com/doctrine/cache/tree/1.13.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fcache", "type": "tidelift"}], "time": "2022-05-20T20:06:54+00:00"}, {"name": "doctrine/deprecations", "version": "v1.0.0", "source": {"type": "git", "url": "https://github.com/doctrine/deprecations.git", "reference": "0e2a4f1f8cdfc7a92ec3b01c9334898c806b30de"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/deprecations/zipball/0e2a4f1f8cdfc7a92ec3b01c9334898c806b30de", "reference": "0e2a4f1f8cdfc7a92ec3b01c9334898c806b30de", "shasum": ""}, "require": {"php": "^7.1|^8.0"}, "require-dev": {"doctrine/coding-standard": "^9", "phpunit/phpunit": "^7.5|^8.5|^9.5", "psr/log": "^1|^2|^3"}, "suggest": {"psr/log": "Allows logging deprecations via PSR-3 logger implementation"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Deprecations\\": "lib/Doctrine/Deprecations"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A small layer on top of trigger_error(E_USER_DEPRECATED) or PSR-3 logging with options to disable all deprecations or selectively for packages.", "homepage": "https://www.doctrine-project.org/", "support": {"issues": "https://github.com/doctrine/deprecations/issues", "source": "https://github.com/doctrine/deprecations/tree/v1.0.0"}, "time": "2022-05-02T15:47:09+00:00"}, {"name": "doctrine/inflector", "version": "1.4.4", "source": {"type": "git", "url": "https://github.com/doctrine/inflector.git", "reference": "4bd5c1cdfcd00e9e2d8c484f79150f67e5d355d9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/inflector/zipball/4bd5c1cdfcd00e9e2d8c484f79150f67e5d355d9", "reference": "4bd5c1cdfcd00e9e2d8c484f79150f67e5d355d9", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^8.0", "phpstan/phpstan": "^0.12", "phpstan/phpstan-phpunit": "^0.12", "phpstan/phpstan-strict-rules": "^0.12", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Inflector\\": "lib/Doctrine/Inflector", "Doctrine\\Common\\Inflector\\": "lib/Doctrine/Common/Inflector"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Inflector is a small library that can perform string manipulations with regard to upper/lowercase and singular/plural forms of words.", "homepage": "https://www.doctrine-project.org/projects/inflector.html", "keywords": ["inflection", "inflector", "lowercase", "manipulation", "php", "plural", "singular", "strings", "uppercase", "words"], "support": {"issues": "https://github.com/doctrine/inflector/issues", "source": "https://github.com/doctrine/inflector/tree/1.4.4"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Finflector", "type": "tidelift"}], "time": "2021-04-16T17:34:40+00:00"}, {"name": "doctrine/lexer", "version": "2.1.0", "source": {"type": "git", "url": "https://github.com/doctrine/lexer.git", "reference": "39ab8fcf5a51ce4b85ca97c7a7d033eb12831124"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/lexer/zipball/39ab8fcf5a51ce4b85ca97c7a7d033eb12831124", "reference": "39ab8fcf5a51ce4b85ca97c7a7d033eb12831124", "shasum": ""}, "require": {"doctrine/deprecations": "^1.0", "php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9 || ^10", "phpstan/phpstan": "^1.3", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "psalm/plugin-phpunit": "^0.18.3", "vimeo/psalm": "^4.11 || ^5.0"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Lexer\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Lexer parser library that can be used in Top-Down, Recursive Descent Parsers.", "homepage": "https://www.doctrine-project.org/projects/lexer.html", "keywords": ["annotations", "doc<PERSON>", "lexer", "parser", "php"], "support": {"issues": "https://github.com/doctrine/lexer/issues", "source": "https://github.com/doctrine/lexer/tree/2.1.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Flexer", "type": "tidelift"}], "time": "2022-12-14T08:49:07+00:00"}, {"name": "dompdf/dompdf", "version": "v3.1.0", "source": {"type": "git", "url": "https://github.com/dompdf/dompdf.git", "reference": "a51bd7a063a65499446919286fb18b518177155a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dompdf/dompdf/zipball/a51bd7a063a65499446919286fb18b518177155a", "reference": "a51bd7a063a65499446919286fb18b518177155a", "shasum": ""}, "require": {"dompdf/php-font-lib": "^1.0.0", "dompdf/php-svg-lib": "^1.0.0", "ext-dom": "*", "ext-mbstring": "*", "masterminds/html5": "^2.0", "php": "^7.1 || ^8.0"}, "require-dev": {"ext-gd": "*", "ext-json": "*", "ext-zip": "*", "mockery/mockery": "^1.3", "phpunit/phpunit": "^7.5 || ^8 || ^9 || ^10 || ^11", "squizlabs/php_codesniffer": "^3.5", "symfony/process": "^4.4 || ^5.4 || ^6.2 || ^7.0"}, "suggest": {"ext-gd": "Needed to process images", "ext-gmagick": "Improves image processing performance", "ext-imagick": "Improves image processing performance", "ext-zlib": "Needed for pdf stream compression"}, "type": "library", "autoload": {"psr-4": {"Dompdf\\": "src/"}, "classmap": ["lib/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-2.1"], "authors": [{"name": "The Dompdf Community", "homepage": "https://github.com/dompdf/dompdf/blob/master/AUTHORS.md"}], "description": "DOMPDF is a CSS 2.1 compliant HTML to PDF converter", "homepage": "https://github.com/dompdf/dompdf", "support": {"issues": "https://github.com/dompdf/dompdf/issues", "source": "https://github.com/dompdf/dompdf/tree/v3.1.0"}, "time": "2025-01-15T14:09:04+00:00"}, {"name": "dompdf/php-font-lib", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/dompdf/php-font-lib.git", "reference": "6137b7d4232b7f16c882c75e4ca3991dbcf6fe2d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dompdf/php-font-lib/zipball/6137b7d4232b7f16c882c75e4ca3991dbcf6fe2d", "reference": "6137b7d4232b7f16c882c75e4ca3991dbcf6fe2d", "shasum": ""}, "require": {"ext-mbstring": "*", "php": "^7.1 || ^8.0"}, "require-dev": {"symfony/phpunit-bridge": "^3 || ^4 || ^5 || ^6"}, "type": "library", "autoload": {"psr-4": {"FontLib\\": "src/FontLib"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-2.1-or-later"], "authors": [{"name": "The FontLib Community", "homepage": "https://github.com/dompdf/php-font-lib/blob/master/AUTHORS.md"}], "description": "A library to read, parse, export and make subsets of different types of font files.", "homepage": "https://github.com/dompdf/php-font-lib", "support": {"issues": "https://github.com/dompdf/php-font-lib/issues", "source": "https://github.com/dompdf/php-font-lib/tree/1.0.1"}, "time": "2024-12-02T14:37:59+00:00"}, {"name": "dompdf/php-svg-lib", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/dompdf/php-svg-lib.git", "reference": "eb045e518185298eb6ff8d80d0d0c6b17aecd9af"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dompdf/php-svg-lib/zipball/eb045e518185298eb6ff8d80d0d0c6b17aecd9af", "reference": "eb045e518185298eb6ff8d80d0d0c6b17aecd9af", "shasum": ""}, "require": {"ext-mbstring": "*", "php": "^7.1 || ^8.0", "sabberworm/php-css-parser": "^8.4"}, "require-dev": {"phpunit/phpunit": "^7.5 || ^8.5 || ^9.5"}, "type": "library", "autoload": {"psr-4": {"Svg\\": "src/Svg"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "The SvgLib Community", "homepage": "https://github.com/dompdf/php-svg-lib/blob/master/AUTHORS.md"}], "description": "A library to read, parse and export to PDF SVG files.", "homepage": "https://github.com/dompdf/php-svg-lib", "support": {"issues": "https://github.com/dompdf/php-svg-lib/issues", "source": "https://github.com/dompdf/php-svg-lib/tree/1.0.0"}, "time": "2024-04-29T13:26:35+00:00"}, {"name": "eluceo/ical", "version": "0.14.0", "source": {"type": "git", "url": "https://github.com/markuspoerschke/iCal.git", "reference": "5db245d5521a060129d0f14d154d65f32b804883"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/markuspoerschke/iCal/zipball/5db245d5521a060129d0f14d154d65f32b804883", "reference": "5db245d5521a060129d0f14d154d65f32b804883", "shasum": ""}, "require": {"php": ">=7.0"}, "require-dev": {"phpunit/phpunit": "^6.0"}, "suggest": {"ext-mbstring": "Massive performance enhancement of line folding"}, "type": "library", "autoload": {"psr-4": {"Eluceo\\iCal\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "The eluceo/iCal package offers a abstraction layer for creating iCalendars. You can easily create iCal files by using PHP object instead of typing your *.ics file by hand. The output will follow RFC 5545 as best as possible.", "homepage": "https://github.com/markuspoerschke/iCal", "keywords": ["calendar", "iCalendar", "ical", "ics", "php calendar"], "support": {"issues": "https://github.com/markuspoerschke/iCal/issues", "source": "https://github.com/markuspoerschke/iCal"}, "time": "2018-03-13T19:39:55+00:00"}, {"name": "emarref/jwt", "version": "dev-unicode", "source": {"type": "git", "url": "https://github.com/glofoxinc/jwt.git", "reference": "f30a8315fdc23af9beb90778ffd488a68a0e7b32"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/glofoxinc/jwt/zipball/f30a8315fdc23af9beb90778ffd488a68a0e7b32", "reference": "f30a8315fdc23af9beb90778ffd488a68a0e7b32", "shasum": ""}, "require": {"php": ">=5.4"}, "require-dev": {"phpunit/phpunit": "*"}, "suggest": {"ext-openssl": "Enables more token encryption options"}, "type": "library", "autoload": {"psr-4": {"Emarref\\Jwt\\": "src/"}}, "autoload-dev": {"psr-4": {"Emarref\\Jwt\\": "test/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A JWT implementation", "support": {"source": "https://github.com/glofoxinc/jwt/tree/unicode"}, "time": "2019-05-03T15:05:11+00:00"}, {"name": "gianksp/cakephp-amazon-s3", "version": "dev-master", "source": {"type": "git", "url": "https://github.com/gianksp/cakephp-amazon-s3.git", "reference": "d7f62f2dfad46420bc5cdc2287a78f45f8d5c5d1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/gianksp/cakephp-amazon-s3/zipball/d7f62f2dfad46420bc5cdc2287a78f45f8d5c5d1", "reference": "d7f62f2dfad46420bc5cdc2287a78f45f8d5c5d1", "shasum": ""}, "require": {"aws/aws-php-sns-message-validator": "^1.1", "aws/aws-sdk-php": "^3.23", "doctrine/cache": "^1.6", "php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "3.7.*"}, "default-branch": true, "type": "cakephp-plugin", "extra": {"installer-name": "AmazonS3"}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "homepage": "http://twitter.com/gianksp", "role": "Author"}], "description": "Amazon S3 Plugin for CakePHP", "homepage": "https://github.com/gianksp/cakephp-amazon-s3", "keywords": ["aws", "cakephp", "s3"], "support": {"issues": "https://github.com/gianksp/cakephp-amazon-s3/issues", "source": "https://github.com/gianksp/cakephp-amazon-s3"}, "time": "2017-06-05T16:55:37+00:00"}, {"name": "gianksp/cakephp-swagger", "version": "dev-master", "source": {"type": "git", "url": "https://github.com/gianksp/cakephp-swagger.git", "reference": "f17b959b2182166b13bc47f5d753f4f476f00c1f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/gianksp/cakephp-swagger/zipball/f17b959b2182166b13bc47f5d753f4f476f00c1f", "reference": "f17b959b2182166b13bc47f5d753f4f476f00c1f", "shasum": ""}, "require": {"php": ">=5.3.0", "zircote/swagger-php": "^2.0"}, "require-dev": {"phpunit/phpunit": "3.7.*"}, "default-branch": true, "type": "cakephp-plugin", "extra": {"installer-name": "Swagger"}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "homepage": "http://twitter.com/gianksp", "role": "Author"}], "description": "Swagger for CakePHP", "homepage": "https://github.com/gianksp/cakephp-swagger", "keywords": ["cakephp", "swagger"], "support": {"issues": "https://github.com/gianksp/cakephp-swagger/issues", "source": "https://github.com/gianksp/cakephp-swagger"}, "time": "2017-06-22T16:34:34+00:00"}, {"name": "giggsey/libphonenumber-for-php", "version": "8.13.8", "source": {"type": "git", "url": "https://github.com/giggsey/libphonenumber-for-php.git", "reference": "2d3f07bb56daa44b9a2f5ff72e41db762f9573d1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/giggsey/libphonenumber-for-php/zipball/2d3f07bb56daa44b9a2f5ff72e41db762f9573d1", "reference": "2d3f07bb56daa44b9a2f5ff72e41db762f9573d1", "shasum": ""}, "require": {"giggsey/locale": "^1.7|^2.0", "php": ">=5.3.2", "symfony/polyfill-mbstring": "^1.17"}, "require-dev": {"pear/pear-core-minimal": "^1.9", "pear/pear_exception": "^1.0", "pear/versioncontrol_git": "^0.5", "phing/phing": "^2.7", "php-coveralls/php-coveralls": "^1.0|^2.0", "symfony/console": "^2.8|^3.0|^v4.4|^v5.2", "symfony/phpunit-bridge": "^4.2 || ^5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "8.x-dev"}}, "autoload": {"psr-4": {"libphonenumber\\": "src/"}, "exclude-from-classmap": ["/src/data/", "/src/carrier/data/", "/src/geocoding/data/", "/src/timezone/data/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://giggsey.com/"}], "description": "PHP Port of Google's libphonenumber", "homepage": "https://github.com/giggsey/libphonenumber-for-php", "keywords": ["geocoding", "geolocation", "libphonenumber", "mobile", "phonenumber", "validation"], "support": {"issues": "https://github.com/giggsey/libphonenumber-for-php/issues", "source": "https://github.com/giggsey/libphonenumber-for-php"}, "time": "2023-03-27T15:36:27+00:00"}, {"name": "giggsey/locale", "version": "2.3", "source": {"type": "git", "url": "https://github.com/giggsey/Locale.git", "reference": "5f035523740be40d40ac768a123c9bcc1ae12f56"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/giggsey/Locale/zipball/5f035523740be40d40ac768a123c9bcc1ae12f56", "reference": "5f035523740be40d40ac768a123c9bcc1ae12f56", "shasum": ""}, "require": {"php": ">=7.2"}, "require-dev": {"ext-json": "*", "pear/pear-core-minimal": "^1.9", "pear/pear_exception": "^1.0", "pear/versioncontrol_git": "^0.5", "phing/phing": "^2.7", "php-coveralls/php-coveralls": "^2.0", "phpunit/phpunit": "^8.5|^9.5", "symfony/console": "^5.0|^6.0", "symfony/filesystem": "^5.0|^6.0", "symfony/finder": "^5.0|^6.0", "symfony/process": "^5.0|^6.0"}, "type": "library", "autoload": {"psr-4": {"Giggsey\\Locale\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://giggsey.com/"}], "description": "Locale functions required by libphonenumber-for-php", "support": {"issues": "https://github.com/giggsey/Locale/issues", "source": "https://github.com/giggsey/Locale/tree/2.3"}, "time": "2022-10-19T20:03:30+00:00"}, {"name": "glofox/cakephp-mongodb", "version": "1.1.3", "source": {"type": "git", "url": "https://github.com/glofoxinc/cakephp-mongodb.git", "reference": "688d61aca880f5613927d98e7b8cd6c55d606768"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/glofoxinc/cakephp-mongodb/zipball/688d61aca880f5613927d98e7b8cd6c55d606768", "reference": "688d61aca880f5613927d98e7b8cd6c55d606768", "shasum": ""}, "require": {"composer/installers": "*", "php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "3.7.*"}, "type": "cakephp-plugin", "extra": {"installer-name": "Mongodb"}, "license": ["MIT"], "authors": [{"name": "Glofox Team", "homepage": "https://www.glofox.com", "role": "Author"}, {"name": "<PERSON>", "homepage": "https://github.com/alexrom7", "role": "Author"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "homepage": "https://github.com/gianksp", "role": "Author"}, {"name": "Various Others", "homepage": "https://github.com/alexrom7/cakephp-mongodb/graphs/contributors"}], "description": "MongoDB Datasource for CakePHP", "homepage": "https://github.com/alexrom7/cakephp-mongodb", "keywords": ["cakephp", "datasource", "mongo"], "support": {"issues": "https://github.com/alexrom7/cakephp-mongodb/issues", "source": "https://github.com/alexrom7/cakephp-mongodb"}, "time": "2023-03-14T12:04:37+00:00"}, {"name": "glofoxinc/eventkit-php", "version": "1.2.19", "source": {"type": "git", "url": "**************:glofoxinc/eventkit-php.git", "reference": "b95596b44329856627bdefb0f9357cc4aef8dda9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/glofoxinc/eventkit-php/zipball/b95596b44329856627bdefb0f9357cc4aef8dda9", "reference": "b95596b44329856627bdefb0f9357cc4aef8dda9", "shasum": ""}, "require": {"aws/aws-sdk-php": "^3.91", "league/event": "2.1.2", "psr/log": "1.0.2"}, "require-dev": {"phpunit/phpunit": "^8.1"}, "type": "library", "autoload": {"psr-4": {"Glofox\\Eventkit\\": "src/"}}, "authors": [{"name": "<PERSON><PERSON>", "email": "ka<PERSON><PERSON><PERSON><PERSON>@gmail.com"}], "description": "PHP SDK for using AWS SNS & SQS as a domain event bus", "support": {"source": "https://github.com/glofoxinc/eventkit-php/tree/1.2.19", "issues": "https://github.com/glofoxinc/eventkit-php/issues"}, "time": "2025-02-26T11:39:52+00:00"}, {"name": "glofoxinc/message-libs-php", "version": "v0.0.89", "source": {"type": "git", "url": "**************:glofoxinc/message-libs-php.git", "reference": "9d74bab657bdb474b75f6ec734235e5f80ee8853"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/glofoxinc/message-libs-php/zipball/9d74bab657bdb474b75f6ec734235e5f80ee8853", "reference": "9d74bab657bdb474b75f6ec734235e5f80ee8853", "shasum": ""}, "require": {"aws/aws-sdk-php": "^3.209.21", "ext-json": "*", "jane-php/json-schema": "5.3.3", "php": "7.4.* || 8.1.*", "psr/log": "1.0.2", "symfony/serializer": "^5.4"}, "type": "library", "autoload": {"psr-4": {"Glofox\\MessageLibs\\": "src/"}}, "description": "PHP libs for message-inventory", "support": {"source": "https://github.com/glofoxinc/message-libs-php/tree/v0.0.89", "issues": "https://github.com/glofoxinc/message-libs-php/issues"}, "time": "2023-10-04T11:18:17+00:00"}, {"name": "google/protobuf", "version": "v3.6.1.3", "source": {"type": "git", "url": "https://github.com/protocolbuffers/protobuf-php.git", "reference": "c39346815303f63ad295d6499e11d914c40040e6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/protocolbuffers/protobuf-php/zipball/c39346815303f63ad295d6499e11d914c40040e6", "reference": "c39346815303f63ad295d6499e11d914c40040e6", "shasum": ""}, "require": {"php": ">=5.5.0"}, "require-dev": {"phpunit/phpunit": ">=4.8.0"}, "suggest": {"ext-bcmath": "Need to support JSON deserialization"}, "type": "library", "autoload": {"psr-4": {"Google\\Protobuf\\": "src/Google/Protobuf", "GPBMetadata\\Google\\Protobuf\\": "src/GPBMetadata/Google/Protobuf"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "proto library for PHP", "homepage": "https://developers.google.com/protocol-buffers/", "keywords": ["proto"], "support": {"issues": "https://github.com/protocolbuffers/protobuf-php/issues", "source": "https://github.com/protocolbuffers/protobuf-php/tree/v3.6.1.3"}, "time": "2019-02-25T22:48:21+00:00"}, {"name": "grpc/grpc", "version": "1.6.0", "source": {"type": "git", "url": "https://github.com/grpc/grpc-php.git", "reference": "8d190d91ddb9d980f685d9caf79bca62d7edc1e6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/grpc/grpc-php/zipball/8d190d91ddb9d980f685d9caf79bca62d7edc1e6", "reference": "8d190d91ddb9d980f685d9caf79bca62d7edc1e6", "shasum": ""}, "require": {"php": ">=5.5.0"}, "require-dev": {"google/auth": "v0.9"}, "suggest": {"ext-protobuf": "For better performance, install the protobuf C extension.", "google/protobuf": "To get started using grpc quickly, install the native protobuf library."}, "type": "library", "autoload": {"psr-4": {"Grpc\\": "src/lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "gRPC library for PHP", "homepage": "https://grpc.io", "keywords": ["rpc"], "support": {"source": "https://github.com/grpc/grpc-php/tree/v1.6.0"}, "time": "2017-09-11T20:50:39+00:00"}, {"name": "guzzlehttp/guzzle", "version": "6.5.8", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "a52f0440530b54fa079ce76e8c5d196a42cad981"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/a52f0440530b54fa079ce76e8c5d196a42cad981", "reference": "a52f0440530b54fa079ce76e8c5d196a42cad981", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/promises": "^1.0", "guzzlehttp/psr7": "^1.9", "php": ">=5.5", "symfony/polyfill-intl-idn": "^1.17"}, "require-dev": {"ext-curl": "*", "phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.4 || ^7.0", "psr/log": "^1.1"}, "suggest": {"psr/log": "Required for using the Log middleware"}, "type": "library", "extra": {"branch-alias": {"dev-master": "6.5-dev"}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "jereme<PERSON>@gmail.com", "homepage": "https://github.com/jeremeamia"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle is a PHP HTTP client library", "homepage": "http://guzzlephp.org/", "keywords": ["client", "curl", "framework", "http", "http client", "rest", "web service"], "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/6.5.8"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/guzzle", "type": "tidelift"}], "time": "2022-06-20T22:16:07+00:00"}, {"name": "guzzlehttp/promises", "version": "1.5.3", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "67ab6e18aaa14d753cc148911d273f6e6cb6721e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/67ab6e18aaa14d753cc148911d273f6e6cb6721e", "reference": "67ab6e18aaa14d753cc148911d273f6e6cb6721e", "shasum": ""}, "require": {"php": ">=5.5"}, "require-dev": {"symfony/phpunit-bridge": "^4.4 || ^5.1"}, "type": "library", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle promises library", "keywords": ["promise"], "support": {"issues": "https://github.com/guzzle/promises/issues", "source": "https://github.com/guzzle/promises/tree/1.5.3"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/promises", "type": "tidelift"}], "time": "2023-05-21T12:31:43+00:00"}, {"name": "guzzlehttp/psr7", "version": "1.9.1", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "e4490cabc77465aaee90b20cfc9a770f8c04be6b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/e4490cabc77465aaee90b20cfc9a770f8c04be6b", "reference": "e4490cabc77465aaee90b20cfc9a770f8c04be6b", "shasum": ""}, "require": {"php": ">=5.4.0", "psr/http-message": "~1.0", "ralouphie/getallheaders": "^2.0.5 || ^3.0.0"}, "provide": {"psr/http-message-implementation": "1.0"}, "require-dev": {"ext-zlib": "*", "phpunit/phpunit": "~4.8.36 || ^5.7.27 || ^6.5.14 || ^7.5.20 || ^8.5.8 || ^9.3.10"}, "suggest": {"laminas/laminas-httphandlerrunner": "Emit PSR-7 responses"}, "type": "library", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\Psr7\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "support": {"issues": "https://github.com/guzzle/psr7/issues", "source": "https://github.com/guzzle/psr7/tree/1.9.1"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/psr7", "type": "tidelift"}], "time": "2023-04-17T16:00:37+00:00"}, {"name": "http-interop/http-factory-guzzle", "version": "1.2.0", "source": {"type": "git", "url": "https://github.com/http-interop/http-factory-guzzle.git", "reference": "8f06e92b95405216b237521cc64c804dd44c4a81"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/http-interop/http-factory-guzzle/zipball/8f06e92b95405216b237521cc64c804dd44c4a81", "reference": "8f06e92b95405216b237521cc64c804dd44c4a81", "shasum": ""}, "require": {"guzzlehttp/psr7": "^1.7||^2.0", "php": ">=7.3", "psr/http-factory": "^1.0"}, "provide": {"psr/http-factory-implementation": "^1.0"}, "require-dev": {"http-interop/http-factory-tests": "^0.9", "phpunit/phpunit": "^9.5"}, "suggest": {"guzzlehttp/psr7": "Includes an HTTP factory starting in version 2.0"}, "type": "library", "autoload": {"psr-4": {"Http\\Factory\\Guzzle\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "An HTTP Factory using Guzzle PSR7", "keywords": ["factory", "http", "psr-17", "psr-7"], "support": {"issues": "https://github.com/http-interop/http-factory-guzzle/issues", "source": "https://github.com/http-interop/http-factory-guzzle/tree/1.2.0"}, "time": "2021-07-21T13:50:14+00:00"}, {"name": "illuminate/auth", "version": "v5.4.36", "source": {"type": "git", "url": "https://github.com/illuminate/auth.git", "reference": "f11e142d4e60ed84272f988dddeb73f87c132db6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/auth/zipball/f11e142d4e60ed84272f988dddeb73f87c132db6", "reference": "f11e142d4e60ed84272f988dddeb73f87c132db6", "shasum": ""}, "require": {"illuminate/contracts": "5.4.*", "illuminate/http": "5.4.*", "illuminate/queue": "5.4.*", "illuminate/support": "5.4.*", "nesbot/carbon": "~1.20", "php": ">=5.6.4"}, "suggest": {"illuminate/console": "Required to use the auth:clear-resets command (5.4.*).", "illuminate/queue": "Required to fire login / logout events (5.4.*).", "illuminate/session": "Required to use the session based guard (5.4.*)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.4-dev"}}, "autoload": {"psr-4": {"Illuminate\\Auth\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Auth package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "time": "2017-08-23T20:25:41+00:00"}, {"name": "illuminate/config", "version": "v5.4.0", "source": {"type": "git", "url": "https://github.com/illuminate/config.git", "reference": "6a1a4f2c8b0732a419f5df018b8a2f59cebbed8f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/config/zipball/6a1a4f2c8b0732a419f5df018b8a2f59cebbed8f", "reference": "6a1a4f2c8b0732a419f5df018b8a2f59cebbed8f", "shasum": ""}, "require": {"illuminate/contracts": "5.4.*", "illuminate/filesystem": "5.4.*", "illuminate/support": "5.4.*", "php": ">=5.6.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.4-dev"}}, "autoload": {"psr-4": {"Illuminate\\Config\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Config package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "time": "2016-12-23T13:31:56+00:00"}, {"name": "illuminate/console", "version": "v5.4.36", "source": {"type": "git", "url": "https://github.com/illuminate/console.git", "reference": "4f0413ffd240d2004c3e9e4cd8f63df249939a15"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/console/zipball/4f0413ffd240d2004c3e9e4cd8f63df249939a15", "reference": "4f0413ffd240d2004c3e9e4cd8f63df249939a15", "shasum": ""}, "require": {"illuminate/contracts": "5.4.*", "illuminate/support": "5.4.*", "nesbot/carbon": "~1.20", "php": ">=5.6.4", "symfony/console": "~3.2"}, "suggest": {"guzzlehttp/guzzle": "Required to use the ping methods on schedules (~6.0).", "mtdowling/cron-expression": "Required to use scheduling component (~1.0).", "symfony/process": "Required to use scheduling component (~3.2)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.4-dev"}}, "autoload": {"psr-4": {"Illuminate\\Console\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Console package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "time": "2017-08-24T22:31:32+00:00"}, {"name": "illuminate/container", "version": "v5.4.36", "source": {"type": "git", "url": "https://github.com/illuminate/container.git", "reference": "c5b8a02a34a52c307f16922334c355c5eef725a6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/container/zipball/c5b8a02a34a52c307f16922334c355c5eef725a6", "reference": "c5b8a02a34a52c307f16922334c355c5eef725a6", "shasum": ""}, "require": {"illuminate/contracts": "5.4.*", "php": ">=5.6.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.4-dev"}}, "autoload": {"psr-4": {"Illuminate\\Container\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Container package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "time": "2017-05-24T14:15:53+00:00"}, {"name": "illuminate/contracts", "version": "v5.4.36", "source": {"type": "git", "url": "https://github.com/illuminate/contracts.git", "reference": "67f642e018f3e95fb0b2ebffc206c3200391b1ab"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/contracts/zipball/67f642e018f3e95fb0b2ebffc206c3200391b1ab", "reference": "67f642e018f3e95fb0b2ebffc206c3200391b1ab", "shasum": ""}, "require": {"php": ">=5.6.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.4-dev"}}, "autoload": {"psr-4": {"Illuminate\\Contracts\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Contracts package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "time": "2017-08-26T23:56:53+00:00"}, {"name": "illuminate/database", "version": "v5.4.36", "source": {"type": "git", "url": "https://github.com/illuminate/database.git", "reference": "405aa061a5bc8588cbf3a78fba383541a568e3fe"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/database/zipball/405aa061a5bc8588cbf3a78fba383541a568e3fe", "reference": "405aa061a5bc8588cbf3a78fba383541a568e3fe", "shasum": ""}, "require": {"illuminate/container": "5.4.*", "illuminate/contracts": "5.4.*", "illuminate/support": "5.4.*", "nesbot/carbon": "~1.20", "php": ">=5.6.4"}, "suggest": {"doctrine/dbal": "Required to rename columns and drop SQLite columns (~2.5).", "fzaninotto/faker": "Required to use the eloquent factory builder (~1.4).", "illuminate/console": "Required to use the database commands (5.4.*).", "illuminate/events": "Required to use the observers with Eloquent (5.4.*).", "illuminate/filesystem": "Required to use the migrations (5.4.*).", "illuminate/pagination": "Required to paginate the result set (5.4.*)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.4-dev"}}, "autoload": {"psr-4": {"Illuminate\\Database\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Database package.", "homepage": "https://laravel.com", "keywords": ["database", "laravel", "orm", "sql"], "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "time": "2017-08-24T12:07:53+00:00"}, {"name": "illuminate/events", "version": "v5.4.36", "source": {"type": "git", "url": "https://github.com/illuminate/events.git", "reference": "ebdca3b0305e9fc954afb9e422c4559482cd11e6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/events/zipball/ebdca3b0305e9fc954afb9e422c4559482cd11e6", "reference": "ebdca3b0305e9fc954afb9e422c4559482cd11e6", "shasum": ""}, "require": {"illuminate/container": "5.4.*", "illuminate/contracts": "5.4.*", "illuminate/support": "5.4.*", "php": ">=5.6.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.4-dev"}}, "autoload": {"psr-4": {"Illuminate\\Events\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Events package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "time": "2017-05-02T12:57:00+00:00"}, {"name": "illuminate/filesystem", "version": "v5.4.36", "source": {"type": "git", "url": "https://github.com/illuminate/filesystem.git", "reference": "b800a1423d06869ee5c2768eee123917f12b693e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/filesystem/zipball/b800a1423d06869ee5c2768eee123917f12b693e", "reference": "b800a1423d06869ee5c2768eee123917f12b693e", "shasum": ""}, "require": {"illuminate/contracts": "5.4.*", "illuminate/support": "5.4.*", "php": ">=5.6.4", "symfony/finder": "~3.2"}, "suggest": {"league/flysystem": "Required to use the Flysystem local and FTP drivers (~1.0).", "league/flysystem-aws-s3-v3": "Required to use the Flysystem S3 driver (~1.0).", "league/flysystem-rackspace": "Required to use the Flysystem Rackspace driver (~1.0)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.4-dev"}}, "autoload": {"psr-4": {"Illuminate\\Filesystem\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Filesystem package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "time": "2017-08-02T21:58:00+00:00"}, {"name": "illuminate/http", "version": "v5.4.36", "source": {"type": "git", "url": "https://github.com/illuminate/http.git", "reference": "48b951df8c9f90ed42681c012bd336a16d54adf5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/http/zipball/48b951df8c9f90ed42681c012bd336a16d54adf5", "reference": "48b951df8c9f90ed42681c012bd336a16d54adf5", "shasum": ""}, "require": {"illuminate/session": "5.4.*", "illuminate/support": "5.4.*", "php": ">=5.6.4", "symfony/http-foundation": "~3.2", "symfony/http-kernel": "~3.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.4-dev"}}, "autoload": {"psr-4": {"Illuminate\\Http\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Http package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "time": "2017-08-25T01:40:01+00:00"}, {"name": "illuminate/pipeline", "version": "v5.4.36", "source": {"type": "git", "url": "https://github.com/illuminate/pipeline.git", "reference": "ada6dc2435afa57a74e3dcd4570c31a1a1244e65"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/pipeline/zipball/ada6dc2435afa57a74e3dcd4570c31a1a1244e65", "reference": "ada6dc2435afa57a74e3dcd4570c31a1a1244e65", "shasum": ""}, "require": {"illuminate/contracts": "5.4.*", "illuminate/support": "5.4.*", "php": ">=5.6.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.4-dev"}}, "autoload": {"psr-4": {"Illuminate\\Pipeline\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Pipeline package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "time": "2017-01-17T14:21:32+00:00"}, {"name": "illuminate/queue", "version": "v5.4.36", "source": {"type": "git", "url": "https://github.com/illuminate/queue.git", "reference": "874144ac76df651572fc884951f80f3afd9d7057"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/queue/zipball/874144ac76df651572fc884951f80f3afd9d7057", "reference": "874144ac76df651572fc884951f80f3afd9d7057", "shasum": ""}, "require": {"illuminate/console": "5.4.*", "illuminate/container": "5.4.*", "illuminate/contracts": "5.4.*", "illuminate/database": "5.4.*", "illuminate/filesystem": "5.4.*", "illuminate/support": "5.4.*", "nesbot/carbon": "~1.20", "php": ">=5.6.4", "symfony/debug": "~3.2", "symfony/process": "~3.2"}, "suggest": {"aws/aws-sdk-php": "Required to use the SQS queue driver (~3.0).", "illuminate/redis": "Required to use the Redis queue driver (5.4.*).", "pda/pheanstalk": "Required to use the Beanstalk queue driver (~3.0)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.4-dev"}}, "autoload": {"psr-4": {"Illuminate\\Queue\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Queue package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "time": "2017-08-28T21:32:02+00:00"}, {"name": "illuminate/routing", "version": "v5.4.36", "source": {"type": "git", "url": "https://github.com/illuminate/routing.git", "reference": "c9205bc863d243e04494d409b47485631f2a7060"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/routing/zipball/c9205bc863d243e04494d409b47485631f2a7060", "reference": "c9205bc863d243e04494d409b47485631f2a7060", "shasum": ""}, "require": {"illuminate/container": "5.4.*", "illuminate/contracts": "5.4.*", "illuminate/http": "5.4.*", "illuminate/pipeline": "5.4.*", "illuminate/session": "5.4.*", "illuminate/support": "5.4.*", "php": ">=5.6.4", "symfony/debug": "~3.2", "symfony/http-foundation": "~3.2", "symfony/http-kernel": "~3.2", "symfony/routing": "~3.2"}, "suggest": {"illuminate/console": "Required to use the make commands (5.4.*).", "symfony/psr-http-message-bridge": "Required to psr7 bridging features (0.2.*)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.4-dev"}}, "autoload": {"psr-4": {"Illuminate\\Routing\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Routing package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "time": "2017-08-23T12:58:44+00:00"}, {"name": "illuminate/session", "version": "v5.4.36", "source": {"type": "git", "url": "https://github.com/illuminate/session.git", "reference": "59f994b299318ba5a91b390fe482e24fdaa08ec5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/session/zipball/59f994b299318ba5a91b390fe482e24fdaa08ec5", "reference": "59f994b299318ba5a91b390fe482e24fdaa08ec5", "shasum": ""}, "require": {"illuminate/contracts": "5.4.*", "illuminate/filesystem": "5.4.*", "illuminate/support": "5.4.*", "nesbot/carbon": "~1.20", "php": ">=5.6.4", "symfony/finder": "~3.2", "symfony/http-foundation": "~3.2"}, "suggest": {"illuminate/console": "Required to use the session:table command (5.4.*)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.4-dev"}}, "autoload": {"psr-4": {"Illuminate\\Session\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Session package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "time": "2017-03-30T14:26:45+00:00"}, {"name": "illuminate/support", "version": "v5.4.36", "source": {"type": "git", "url": "https://github.com/illuminate/support.git", "reference": "feab1d1495fd6d38970bd6c83586ba2ace8f299a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/support/zipball/feab1d1495fd6d38970bd6c83586ba2ace8f299a", "reference": "feab1d1495fd6d38970bd6c83586ba2ace8f299a", "shasum": ""}, "require": {"doctrine/inflector": "~1.1", "ext-mbstring": "*", "illuminate/contracts": "5.4.*", "paragonie/random_compat": "~1.4|~2.0", "php": ">=5.6.4"}, "replace": {"tightenco/collect": "self.version"}, "suggest": {"illuminate/filesystem": "Required to use the composer class (5.2.*).", "symfony/process": "Required to use the composer class (~3.2).", "symfony/var-dumper": "Required to use the dd function (~3.2)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.4-dev"}}, "autoload": {"files": ["helpers.php"], "psr-4": {"Illuminate\\Support\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Support package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "time": "2017-08-15T13:25:41+00:00"}, {"name": "illuminate/translation", "version": "v5.4.36", "source": {"type": "git", "url": "https://github.com/illuminate/translation.git", "reference": "b671ddf78cbee60b0b357ad5745eceda2df26082"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/translation/zipball/b671ddf78cbee60b0b357ad5745eceda2df26082", "reference": "b671ddf78cbee60b0b357ad5745eceda2df26082", "shasum": ""}, "require": {"illuminate/contracts": "5.4.*", "illuminate/filesystem": "5.4.*", "illuminate/support": "5.4.*", "php": ">=5.6.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.4-dev"}}, "autoload": {"psr-4": {"Illuminate\\Translation\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Translation package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "time": "2017-06-11T21:19:31+00:00"}, {"name": "illuminate/validation", "version": "v5.4.0", "source": {"type": "git", "url": "https://github.com/illuminate/validation.git", "reference": "ef0938eacb3b418190c4ec5f1091d8d41cace2cc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/validation/zipball/ef0938eacb3b418190c4ec5f1091d8d41cace2cc", "reference": "ef0938eacb3b418190c4ec5f1091d8d41cace2cc", "shasum": ""}, "require": {"illuminate/container": "5.4.*", "illuminate/contracts": "5.4.*", "illuminate/support": "5.4.*", "php": ">=5.6.4", "symfony/http-foundation": "~3.2"}, "suggest": {"illuminate/database": "Required to use the database presence verifier (5.4.*)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.4-dev"}}, "autoload": {"psr-4": {"Illuminate\\Validation\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Validation package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "time": "2017-01-24T13:10:15+00:00"}, {"name": "illuminate/view", "version": "v5.4.36", "source": {"type": "git", "url": "https://github.com/illuminate/view.git", "reference": "785f41f8f01653dc830c5c89eb0f25c311af7615"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/view/zipball/785f41f8f01653dc830c5c89eb0f25c311af7615", "reference": "785f41f8f01653dc830c5c89eb0f25c311af7615", "shasum": ""}, "require": {"illuminate/container": "5.4.*", "illuminate/contracts": "5.4.*", "illuminate/events": "5.4.*", "illuminate/filesystem": "5.4.*", "illuminate/support": "5.4.*", "php": ">=5.6.4", "symfony/debug": "~3.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.4-dev"}}, "autoload": {"psr-4": {"Illuminate\\View\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate View package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "time": "2017-08-05T12:41:58+00:00"}, {"name": "intervention/image", "version": "2.7.2", "source": {"type": "git", "url": "https://github.com/Intervention/image.git", "reference": "04be355f8d6734c826045d02a1079ad658322dad"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Intervention/image/zipball/04be355f8d6734c826045d02a1079ad658322dad", "reference": "04be355f8d6734c826045d02a1079ad658322dad", "shasum": ""}, "require": {"ext-fileinfo": "*", "guzzlehttp/psr7": "~1.1 || ^2.0", "php": ">=5.4.0"}, "require-dev": {"mockery/mockery": "~0.9.2", "phpunit/phpunit": "^4.8 || ^5.7 || ^7.5.15"}, "suggest": {"ext-gd": "to use GD library based image processing.", "ext-imagick": "to use Imagick based image processing.", "intervention/imagecache": "Caching extension for the Intervention Image library"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.4-dev"}, "laravel": {"providers": ["Intervention\\Image\\ImageServiceProvider"], "aliases": {"Image": "Intervention\\Image\\Facades\\Image"}}}, "autoload": {"psr-4": {"Intervention\\Image\\": "src/Intervention/Image"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://intervention.io/"}], "description": "Image handling and manipulation library with support for Laravel integration", "homepage": "http://image.intervention.io/", "keywords": ["gd", "image", "imagick", "laravel", "thumbnail", "watermark"], "support": {"issues": "https://github.com/Intervention/image/issues", "source": "https://github.com/Intervention/image/tree/2.7.2"}, "funding": [{"url": "https://paypal.me/interventionio", "type": "custom"}, {"url": "https://github.com/Intervention", "type": "github"}], "time": "2022-05-21T17:30:32+00:00"}, {"name": "jane-php/json-schema", "version": "v5.3.3", "source": {"type": "git", "url": "https://github.com/janephp/json-schema.git", "reference": "5ecca7ac8a11f819624ad77156e5311e61fbdc4b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/janephp/json-schema/zipball/5ecca7ac8a11f819624ad77156e5311e61fbdc4b", "reference": "5ecca7ac8a11f819624ad77156e5311e61fbdc4b", "shasum": ""}, "require": {"doctrine/inflector": "^1.0", "ext-json": "*", "jane-php/json-schema-runtime": "^5.0", "nikic/php-parser": "^4.0", "php": ">=7.2", "symfony/console": "^3.1 || ^4.0 || ^5.0", "symfony/filesystem": "^3.1 || ^4.0 || ^5.0", "symfony/options-resolver": "^3.1 || ^4.0 || ^5.0", "symfony/serializer": "^4.2 || ^5.0", "symfony/yaml": "^3.1 || ^4.0 || ^5.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "2.15.3", "phpunit/phpunit": "^8.0"}, "suggest": {"friendsofphp/php-cs-fixer": "Allow to automatically fix cs on generated code for better visualisation"}, "bin": ["bin/jane"], "type": "library", "extra": {"branch-alias": {"dev-master": "5-dev"}}, "autoload": {"psr-4": {"Jane\\JsonSchema\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Generate a serializable / deserializable object model given a json schema", "support": {"source": "https://github.com/janephp/json-schema/tree/v5.3.3"}, "time": "2020-03-11T16:17:39+00:00"}, {"name": "jane-php/json-schema-runtime", "version": "v5.3.3", "source": {"type": "git", "url": "https://github.com/janephp/json-schema-runtime.git", "reference": "1d0a5200fe8b1382341209beae0c9aa021a8b86f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/janephp/json-schema-runtime/zipball/1d0a5200fe8b1382341209beae0c9aa021a8b86f", "reference": "1d0a5200fe8b1382341209beae0c9aa021a8b86f", "shasum": ""}, "require": {"ext-json": "*", "league/uri": "^6.0", "php": ">=7.2", "php-jsonpointer/php-jsonpointer": "^3.0", "symfony/serializer": "^3.1 || ^4.0 || ^5.0", "symfony/yaml": "^3.1 || ^4.0 || ^5.0"}, "require-dev": {"phpunit/phpunit": "^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5-dev"}}, "autoload": {"psr-4": {"Jane\\JsonSchemaRuntime\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Jane runtime Library", "support": {"source": "https://github.com/janephp/json-schema-runtime/tree/v5.3.3"}, "time": "2020-04-23T11:32:25+00:00"}, {"name": "jean85/pretty-package-versions", "version": "2.0.5", "source": {"type": "git", "url": "https://github.com/Jean85/pretty-package-versions.git", "reference": "ae547e455a3d8babd07b96966b17d7fd21d9c6af"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Jean85/pretty-package-versions/zipball/ae547e455a3d8babd07b96966b17d7fd21d9c6af", "reference": "ae547e455a3d8babd07b96966b17d7fd21d9c6af", "shasum": ""}, "require": {"composer-runtime-api": "^2.0.0", "php": "^7.1|^8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.17", "jean85/composer-provided-replaced-stub-package": "^1.0", "phpstan/phpstan": "^0.12.66", "phpunit/phpunit": "^7.5|^8.5|^9.4", "vimeo/psalm": "^4.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Jean85\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A library to get pretty versions strings of installed dependencies", "keywords": ["composer", "package", "release", "versions"], "support": {"issues": "https://github.com/Jean85/pretty-package-versions/issues", "source": "https://github.com/Jean85/pretty-package-versions/tree/2.0.5"}, "time": "2021-10-08T21:21:46+00:00"}, {"name": "julien-c/iso3166", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/julien-c/iso3166.git", "reference": "d7bd56333a1ed901fbc41d5a5c92d88a2a6cb88c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/julien-c/iso3166/zipball/d7bd56333a1ed901fbc41d5a5c92d88a2a6cb88c", "reference": "d7bd56333a1ed901fbc41d5a5c92d88a2a6cb88c", "shasum": ""}, "type": "library", "autoload": {"psr-4": {"Iso3166\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "ISO 3166-1 alpha-2 mapping", "support": {"issues": "https://github.com/julien-c/iso3166/issues", "source": "https://github.com/julien-c/iso3166/tree/master"}, "time": "2015-12-15T15:40:03+00:00"}, {"name": "kevinrob/guzzle-cache-middleware", "version": "v1.5.2", "source": {"type": "git", "url": "https://github.com/Kevinrob/guzzle-cache-middleware.git", "reference": "2893fff87ef9f7f2c669957f5e446beea48d7a1d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Kevinrob/guzzle-cache-middleware/zipball/2893fff87ef9f7f2c669957f5e446beea48d7a1d", "reference": "2893fff87ef9f7f2c669957f5e446beea48d7a1d", "shasum": ""}, "require": {"php": ">=5.5.0"}, "require-dev": {"cache/array-adapter": "^0.4", "doctrine/cache": "^1.0", "guzzlehttp/guzzle": "^6.0", "illuminate/cache": "^5.0", "league/flysystem": "^1.0", "phpunit/phpunit": "^4.0 || ^5.0", "psr/cache": "^1.0"}, "suggest": {"doctrine/cache": "This library have a lot of ready-to-use cache storage (to be use with Kevinrob\\GuzzleCache\\Storage\\DoctrineCacheStorage)", "guzzlehttp/guzzle": "For using this library. It was created for Guzzle6. (but you can use it with any PSR-7 HTTP Client)", "laravel/framework": "To be use with Kevinrob\\GuzzleCache\\Storage\\LaravelCacheStorage", "league/flysystem": "To be use with Kevinrob\\GuzzleCache\\Storage\\FlysystemStorage", "psr/cache": "To be use with Kevinrob\\GuzzleCache\\Storage\\Psr6CacheStorage"}, "type": "library", "autoload": {"psr-4": {"Kevinrob\\GuzzleCache\\": ["src/", "tests/"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Kevinrob"}], "description": "A HTTP/1.1 Cache for Guzzle 6. It's a simple Middleware to be added in the HandlerStack. (RFC 7234)", "homepage": "https://github.com/Kevinrob/guzzle-cache-middleware", "keywords": ["Etag", "Flysystem", "Guzzle", "cache", "cache-control", "doctrine", "expiration", "guzzle6", "handler", "http", "http 1.1", "middleware", "performance", "php", "promise", "psr6", "psr7", "rfc7234", "validation"], "support": {"issues": "https://github.com/Kevinrob/guzzle-cache-middleware/issues", "source": "https://github.com/Kevinrob/guzzle-cache-middleware/tree/master"}, "time": "2017-01-16T07:02:13+00:00"}, {"name": "kylekatarnls/update-helper", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/kylekatarnls/update-helper.git", "reference": "429be50660ed8a196e0798e5939760f168ec8ce9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kylekatarnls/update-helper/zipball/429be50660ed8a196e0798e5939760f168ec8ce9", "reference": "429be50660ed8a196e0798e5939760f168ec8ce9", "shasum": ""}, "require": {"composer-plugin-api": "^1.1.0 || ^2.0.0", "php": ">=5.3.0"}, "require-dev": {"codeclimate/php-test-reporter": "dev-master", "composer/composer": "2.0.x-dev || ^2.0.0-dev", "phpunit/phpunit": ">=4.8.35 <6.0"}, "type": "composer-plugin", "extra": {"class": "UpdateHelper\\ComposerPlugin"}, "autoload": {"psr-0": {"UpdateHelper\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Update helper", "support": {"issues": "https://github.com/kylekatarnls/update-helper/issues", "source": "https://github.com/kylekatarnls/update-helper/tree/1.2.1"}, "funding": [{"url": "https://github.com/kylekatarnls", "type": "github"}, {"url": "https://opencollective.com/Carbon", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/nesbot/carbon", "type": "tidelift"}], "time": "2020-04-07T20:44:10+00:00"}, {"name": "launchdarkly/server-sdk", "version": "4.3.0", "source": {"type": "git", "url": "https://github.com/launchdarkly/php-server-sdk.git", "reference": "7c5fb3ee14a443d0768da594911d7d63350ad8fa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/launchdarkly/php-server-sdk/zipball/7c5fb3ee14a443d0768da594911d7d63350ad8fa", "reference": "7c5fb3ee14a443d0768da594911d7d63350ad8fa", "shasum": ""}, "require": {"monolog/monolog": "^1.6|^2.0|^3.0", "php": ">=7.3", "psr/log": "^1.0|^2.0|^3.0"}, "require-dev": {"friendsofphp/php-cs-fixer": ">=2.18.0 <3.0", "guzzlehttp/guzzle": "^6.3 | ^7", "kevinrob/guzzle-cache-middleware": "^4.0", "phpunit/php-code-coverage": "^9", "phpunit/phpunit": "^9", "vimeo/psalm": "^4.9"}, "suggest": {"guzzlehttp/guzzle": "(^6.3 | ^7) Required when using GuzzleEventPublisher or the default FeatureRequester", "kevinrob/guzzle-cache-middleware": "(^3) Recommended for performance when using the default FeatureRequester"}, "type": "library", "autoload": {"psr-4": {"LaunchDarkly\\": "src/LaunchDarkly/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "LaunchDarkly <<EMAIL>>", "homepage": "http://launchdarkly.com/"}], "description": "Official LaunchDarkly SDK for PHP", "homepage": "https://github.com/launchdarkly/php-server-sdk", "keywords": ["launchdarkly", "launchdarkly php"], "support": {"issues": "https://github.com/launchdarkly/php-server-sdk/issues", "source": "https://github.com/launchdarkly/php-server-sdk/tree/4.3.0"}, "time": "2023-01-31T19:18:32+00:00"}, {"name": "league/csv", "version": "8.2.3", "source": {"type": "git", "url": "https://github.com/thephpleague/csv.git", "reference": "d2aab1e7bde802582c3879acf03d92716577c76d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/csv/zipball/d2aab1e7bde802582c3879acf03d92716577c76d", "reference": "d2aab1e7bde802582c3879acf03d92716577c76d", "shasum": ""}, "require": {"ext-mbstring": "*", "php": ">=5.5.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^1.9", "phpunit/phpunit": "^4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "8.2-dev"}}, "autoload": {"psr-4": {"League\\Csv\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/nyamsprod/", "role": "Developer"}], "description": "Csv data manipulation made easy in PHP", "homepage": "http://csv.thephpleague.com", "keywords": ["csv", "export", "filter", "import", "read", "write"], "support": {"forum": "https://groups.google.com/forum/#!forum/thephpleague", "issues": "https://github.com/thephpleague/csv/issues", "source": "https://github.com/thephpleague/csv/tree/8.x"}, "time": "2018-02-06T08:27:03+00:00"}, {"name": "league/event", "version": "2.1.2", "source": {"type": "git", "url": "https://github.com/thephpleague/event.git", "reference": "e4bfc88dbcb60c8d8a2939a71f9813e141bbe4cd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/event/zipball/e4bfc88dbcb60c8d8a2939a71f9813e141bbe4cd", "reference": "e4bfc88dbcb60c8d8a2939a71f9813e141bbe4cd", "shasum": ""}, "require": {"php": ">=5.4.0"}, "require-dev": {"henrikbjorn/phpspec-code-coverage": "~1.0.1", "phpspec/phpspec": "~2.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2-dev"}}, "autoload": {"psr-4": {"League\\Event\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Event package", "keywords": ["emitter", "event", "listener"], "support": {"issues": "https://github.com/thephpleague/event/issues", "source": "https://github.com/thephpleague/event/tree/master"}, "time": "2015-05-21T12:24:47+00:00"}, {"name": "league/flysystem", "version": "1.1.10", "source": {"type": "git", "url": "https://github.com/thephpleague/flysystem.git", "reference": "3239285c825c152bcc315fe0e87d6b55f5972ed1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/flysystem/zipball/3239285c825c152bcc315fe0e87d6b55f5972ed1", "reference": "3239285c825c152bcc315fe0e87d6b55f5972ed1", "shasum": ""}, "require": {"ext-fileinfo": "*", "league/mime-type-detection": "^1.3", "php": "^7.2.5 || ^8.0"}, "conflict": {"league/flysystem-sftp": "<1.0.6"}, "require-dev": {"phpspec/prophecy": "^1.11.1", "phpunit/phpunit": "^8.5.8"}, "suggest": {"ext-ftp": "Allows you to use FTP server storage", "ext-openssl": "Allows you to use FTPS server storage", "league/flysystem-aws-s3-v2": "Allows you to use S3 storage with AWS SDK v2", "league/flysystem-aws-s3-v3": "Allows you to use S3 storage with AWS SDK v3", "league/flysystem-azure": "Allows you to use Windows Azure Blob storage", "league/flysystem-cached-adapter": "Flysystem adapter decorator for metadata caching", "league/flysystem-eventable-filesystem": "Allows you to use EventableFilesystem", "league/flysystem-rackspace": "Allows you to use Rackspace Cloud Files", "league/flysystem-sftp": "Allows you to use SFTP server storage via phpseclib", "league/flysystem-webdav": "Allows you to use WebDAV storage", "league/flysystem-ziparchive": "Allows you to use ZipArchive adapter", "spatie/flysystem-dropbox": "Allows you to use Dropbox storage", "srmklive/flysystem-dropbox-v2": "Allows you to use Dropbox storage for PHP 5 applications"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"psr-4": {"League\\Flysystem\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Filesystem abstraction: Many filesystems, one API.", "keywords": ["Cloud Files", "WebDAV", "abstraction", "aws", "cloud", "copy.com", "dropbox", "file systems", "files", "filesystem", "filesystems", "ftp", "rackspace", "remote", "s3", "sftp", "storage"], "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/1.1.10"}, "funding": [{"url": "https://offset.earth/frankdejonge", "type": "other"}], "time": "2022-10-04T09:16:37+00:00"}, {"name": "league/flysystem-aws-s3-v3", "version": "1.0.30", "source": {"type": "git", "url": "https://github.com/thephpleague/flysystem-aws-s3-v3.git", "reference": "af286f291ebab6877bac0c359c6c2cb017eb061d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/flysystem-aws-s3-v3/zipball/af286f291ebab6877bac0c359c6c2cb017eb061d", "reference": "af286f291ebab6877bac0c359c6c2cb017eb061d", "shasum": ""}, "require": {"aws/aws-sdk-php": "^3.20.0", "league/flysystem": "^1.0.40", "php": ">=5.5.0"}, "require-dev": {"henrikbjorn/phpspec-code-coverage": "~1.0.1", "phpspec/phpspec": "^2.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"psr-4": {"League\\Flysystem\\AwsS3v3\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Flysystem adapter for the AWS S3 SDK v3.x", "support": {"issues": "https://github.com/thephpleague/flysystem-aws-s3-v3/issues", "source": "https://github.com/thephpleague/flysystem-aws-s3-v3/tree/1.0.30"}, "funding": [{"url": "https://offset.earth/frankdejonge", "type": "custom"}, {"url": "https://github.com/frankdejonge", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/league/flysystem", "type": "tidelift"}], "time": "2022-07-02T13:51:38+00:00"}, {"name": "league/fractal", "version": "0.17.0", "source": {"type": "git", "url": "https://github.com/thephpleague/fractal.git", "reference": "a0b350824f22fc2fdde2500ce9d6851a3f275b0e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/fractal/zipball/a0b350824f22fc2fdde2500ce9d6851a3f275b0e", "reference": "a0b350824f22fc2fdde2500ce9d6851a3f275b0e", "shasum": ""}, "require": {"php": ">=5.4"}, "require-dev": {"doctrine/orm": "^2.5", "illuminate/contracts": "~5.0", "mockery/mockery": "~0.9", "pagerfanta/pagerfanta": "~1.0.0", "phpunit/phpunit": "~4.0", "squizlabs/php_codesniffer": "~1.5", "zendframework/zend-paginator": "~2.3"}, "suggest": {"illuminate/pagination": "The Illuminate Pagination component.", "pagerfanta/pagerfanta": "Pagerfant<PERSON>", "zendframework/zend-paginator": "Zend Framework Paginator"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.13-dev"}}, "autoload": {"psr-4": {"League\\Fractal\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://philsturgeon.uk/", "role": "Developer"}], "description": "Handle the output of complex data structures ready for API output.", "homepage": "http://fractal.thephpleague.com/", "keywords": ["api", "json", "league", "rest"], "support": {"issues": "https://github.com/thephpleague/fractal/issues", "source": "https://github.com/thephpleague/fractal/tree/master"}, "time": "2017-06-12T11:04:56+00:00"}, {"name": "league/iso3166", "version": "4.3.1", "source": {"type": "git", "url": "https://github.com/thephpleague/iso3166.git", "reference": "11703e0313f34920add11c0228f0dd43ebd10f9a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/iso3166/zipball/11703e0313f34920add11c0228f0dd43ebd10f9a", "reference": "11703e0313f34920add11c0228f0dd43ebd10f9a", "shasum": ""}, "require": {"ext-mbstring": "*", "php": "^7.3|^8.0"}, "require-dev": {"phpunit/phpunit": "^9.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"League\\ISO3166\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "ISO 3166-1 PHP Library", "homepage": "https://github.com/thephpleague/iso3166", "keywords": ["3166", "3166-1", "ISO 3166", "countries", "iso", "library"], "support": {"issues": "https://github.com/thephpleague/iso3166/issues", "source": "https://github.com/thephpleague/iso3166"}, "time": "2023-09-11T07:59:36+00:00"}, {"name": "league/mime-type-detection", "version": "1.11.0", "source": {"type": "git", "url": "https://github.com/thephpleague/mime-type-detection.git", "reference": "ff6248ea87a9f116e78edd6002e39e5128a0d4dd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/mime-type-detection/zipball/ff6248ea87a9f116e78edd6002e39e5128a0d4dd", "reference": "ff6248ea87a9f116e78edd6002e39e5128a0d4dd", "shasum": ""}, "require": {"ext-fileinfo": "*", "php": "^7.2 || ^8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.2", "phpstan/phpstan": "^0.12.68", "phpunit/phpunit": "^8.5.8 || ^9.3"}, "type": "library", "autoload": {"psr-4": {"League\\MimeTypeDetection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Mime-type detection for Flysystem", "support": {"issues": "https://github.com/thephpleague/mime-type-detection/issues", "source": "https://github.com/thephpleague/mime-type-detection/tree/1.11.0"}, "funding": [{"url": "https://github.com/frankdejonge", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/league/flysystem", "type": "tidelift"}], "time": "2022-04-17T13:12:02+00:00"}, {"name": "league/uri", "version": "6.7.2", "source": {"type": "git", "url": "https://github.com/thephpleague/uri.git", "reference": "d3b50812dd51f3fbf176344cc2981db03d10fe06"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/uri/zipball/d3b50812dd51f3fbf176344cc2981db03d10fe06", "reference": "d3b50812dd51f3fbf176344cc2981db03d10fe06", "shasum": ""}, "require": {"ext-json": "*", "league/uri-interfaces": "^2.3", "php": "^7.4 || ^8.0", "psr/http-message": "^1.0"}, "conflict": {"league/uri-schemes": "^1.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^v3.3.2", "nyholm/psr7": "^1.5", "php-http/psr7-integration-tests": "^1.1", "phpstan/phpstan": "^1.2.0", "phpstan/phpstan-deprecation-rules": "^1.0", "phpstan/phpstan-phpunit": "^1.0.0", "phpstan/phpstan-strict-rules": "^1.1.0", "phpunit/phpunit": "^9.5.10", "psr/http-factory": "^1.0"}, "suggest": {"ext-fileinfo": "Needed to create Data URI from a filepath", "ext-intl": "Needed to improve host validation", "league/uri-components": "Needed to easily manipulate URI objects", "psr/http-factory": "Needed to use the URI factory"}, "type": "library", "extra": {"branch-alias": {"dev-master": "6.x-dev"}}, "autoload": {"psr-4": {"League\\Uri\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://nyamsprod.com"}], "description": "URI manipulation library", "homepage": "https://uri.thephpleague.com", "keywords": ["data-uri", "file-uri", "ftp", "hostname", "http", "https", "middleware", "parse_str", "parse_url", "psr-7", "query-string", "querystring", "rfc3986", "rfc3987", "rfc6570", "uri", "uri-template", "url", "ws"], "support": {"docs": "https://uri.thephpleague.com", "forum": "https://thephpleague.slack.com", "issues": "https://github.com/thephpleague/uri/issues", "source": "https://github.com/thephpleague/uri/tree/6.7.2"}, "funding": [{"url": "https://github.com/sponsors/nyamsprod", "type": "github"}], "time": "2022-09-13T19:50:42+00:00"}, {"name": "league/uri-components", "version": "2.4.2", "source": {"type": "git", "url": "https://github.com/thephpleague/uri-components.git", "reference": "c93837294fe9021d518fd3ea4c5f3fbba8b8ddeb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/uri-components/zipball/c93837294fe9021d518fd3ea4c5f3fbba8b8ddeb", "reference": "c93837294fe9021d518fd3ea4c5f3fbba8b8ddeb", "shasum": ""}, "require": {"ext-json": "*", "league/uri-interfaces": "^2.3", "php": "^7.4 || ^8.0", "psr/http-message": "^1.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^v3.22.0", "guzzlehttp/psr7": "^2.2", "laminas/laminas-diactoros": "^2.11", "league/uri": "^6.0", "phpstan/phpstan": "^1.10.28", "phpstan/phpstan-deprecation-rules": "^1.1.4", "phpstan/phpstan-phpunit": "^1.3.13", "phpstan/phpstan-strict-rules": "^1.5.1", "phpunit/phpunit": "^9.6.10"}, "suggest": {"ext-fileinfo": "Needed to create Data URI from a filepath", "ext-gmp": "to improve handle IPV4 parsing", "ext-intl": "to handle IDN host", "jeremykendall/php-domain-parser": "Public Suffix and Top Level Domain parsing implemented in PHP", "league/uri": "to allow manipulating URI objects", "php-64bit": "to improve handle IPV4 parsing", "psr/http-message-implementation": "to allow manipulating PSR-7 Uri objects"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.x-dev"}}, "autoload": {"psr-4": {"League\\Uri\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://nyamsprod.com"}], "description": "URI components manipulation library", "homepage": "http://uri.thephpleague.com", "keywords": ["authority", "components", "fragment", "host", "path", "port", "query", "rfc3986", "scheme", "uri", "url", "userinfo"], "support": {"source": "https://github.com/thephpleague/uri-components/tree/2.4.2"}, "funding": [{"url": "https://github.com/nyamsprod", "type": "github"}], "time": "2023-08-13T19:53:57+00:00"}, {"name": "league/uri-interfaces", "version": "2.3.0", "source": {"type": "git", "url": "https://github.com/thephpleague/uri-interfaces.git", "reference": "00e7e2943f76d8cb50c7dfdc2f6dee356e15e383"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/uri-interfaces/zipball/00e7e2943f76d8cb50c7dfdc2f6dee356e15e383", "reference": "00e7e2943f76d8cb50c7dfdc2f6dee356e15e383", "shasum": ""}, "require": {"ext-json": "*", "php": "^7.2 || ^8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.19", "phpstan/phpstan": "^0.12.90", "phpstan/phpstan-phpunit": "^0.12.19", "phpstan/phpstan-strict-rules": "^0.12.9", "phpunit/phpunit": "^8.5.15 || ^9.5"}, "suggest": {"ext-intl": "to use the IDNA feature", "symfony/intl": "to use the IDNA feature via Symfony Polyfill"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.x-dev"}}, "autoload": {"psr-4": {"League\\Uri\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://nyamsprod.com"}], "description": "Common interface for URI representation", "homepage": "http://github.com/thephpleague/uri-interfaces", "keywords": ["rfc3986", "rfc3987", "uri", "url"], "support": {"issues": "https://github.com/thephpleague/uri-interfaces/issues", "source": "https://github.com/thephpleague/uri-interfaces/tree/2.3.0"}, "funding": [{"url": "https://github.com/sponsors/nyamsprod", "type": "github"}], "time": "2021-06-28T04:27:21+00:00"}, {"name": "league/uri-parser", "version": "1.4.1", "source": {"type": "git", "url": "https://github.com/thephpleague/uri-parser.git", "reference": "671548427e4c932352d9b9279fdfa345bf63fa00"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/uri-parser/zipball/671548427e4c932352d9b9279fdfa345bf63fa00", "reference": "671548427e4c932352d9b9279fdfa345bf63fa00", "shasum": ""}, "require": {"php": ">=7.0.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.0", "phpstan/phpstan": "^0.9.2", "phpstan/phpstan-phpunit": "^0.9.4", "phpstan/phpstan-strict-rules": "^0.9.0", "phpunit/phpunit": "^6.0"}, "suggest": {"ext-intl": "Allow parsing RFC3987 compliant hosts", "league/uri-schemes": "Allow validating and normalizing URI parsing results"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"League\\Uri\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://nyamsprod.com"}], "description": "userland URI parser RFC 3986 compliant", "homepage": "https://github.com/thephpleague/uri-parser", "keywords": ["parse_url", "parser", "rfc3986", "rfc3987", "uri", "url"], "support": {"issues": "https://github.com/thephpleague/uri-parser/issues", "source": "https://github.com/thephpleague/uri-parser/tree/master"}, "abandoned": true, "time": "2018-11-22T07:55:51+00:00"}, {"name": "marc-mabe/php-enum", "version": "v3.2.0", "source": {"type": "git", "url": "https://github.com/marc-mabe/php-enum.git", "reference": "021dae8d9ddb9e726554ad636081496345cf12e1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/marc-mabe/php-enum/zipball/021dae8d9ddb9e726554ad636081496345cf12e1", "reference": "021dae8d9ddb9e726554ad636081496345cf12e1", "shasum": ""}, "require": {"ext-reflection": "*", "php": ">=5.6"}, "require-dev": {"lstrojny/functional-php": "1.2.* || 1.0.*", "phpbench/phpbench": "@dev", "phpunit/phpunit": "^5.7 || ^6.0 || ^9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev", "dev-3.x": "3.1-dev", "dev-2.x": "2.3-dev", "dev-1.x": "1.3-dev"}}, "autoload": {"psr-4": {"MabeEnum\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://mabe.berlin/", "role": "Lead"}], "description": "Simple and fast implementation of enumerations with native PHP>=5.6", "homepage": "https://github.com/marc-mabe/php-enum", "keywords": ["enum", "enum-map", "enum-set", "enumeration", "enumerator", "enummap", "enumset", "map", "set", "type", "type-hint", "<PERSON><PERSON>t"], "support": {"issues": "https://github.com/marc-mabe/php-enum/issues", "source": "https://github.com/marc-mabe/php-enum/tree/v3.2.0"}, "time": "2022-02-13T05:42:09+00:00"}, {"name": "masterminds/html5", "version": "2.9.0", "source": {"type": "git", "url": "https://github.com/Masterminds/html5-php.git", "reference": "f5ac2c0b0a2eefca70b2ce32a5809992227e75a6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Masterminds/html5-php/zipball/f5ac2c0b0a2eefca70b2ce32a5809992227e75a6", "reference": "f5ac2c0b0a2eefca70b2ce32a5809992227e75a6", "shasum": ""}, "require": {"ext-dom": "*", "php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7.21 || ^6 || ^7 || ^8 || ^9"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.7-dev"}}, "autoload": {"psr-4": {"Masterminds\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "An HTML5 parser and serializer.", "homepage": "http://masterminds.github.io/html5-php", "keywords": ["HTML5", "dom", "html", "parser", "querypath", "serializer", "xml"], "support": {"issues": "https://github.com/Masterminds/html5-php/issues", "source": "https://github.com/Masterminds/html5-php/tree/2.9.0"}, "time": "2024-03-31T07:05:07+00:00"}, {"name": "mixpanel/mixpanel-php", "version": "2.10.0", "source": {"type": "git", "url": "**************:mixpanel/mixpanel-php.git", "reference": "c4b6245713dad06f0527c7b98ee7275116b9e163"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mixpanel/mixpanel-php/zipball/c4b6245713dad06f0527c7b98ee7275116b9e163", "reference": "c4b6245713dad06f0527c7b98ee7275116b9e163", "shasum": ""}, "require": {"php": ">=5.0"}, "require-dev": {"phpdocumentor/phpdocumentor": "2.9.*", "phpunit/phpunit": "5.6.*"}, "type": "library", "autoload": {"files": ["lib/Mixpanel.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Mixpanel <<EMAIL>>", "homepage": "https://mixpanel.com/"}], "description": "The Official PHP library for Mixpanel", "homepage": "https://mixpanel.com/help/reference/php", "keywords": ["mixpanel", "mixpanel php"], "time": "2022-08-10T22:03:19+00:00"}, {"name": "mmucklo/inflect", "version": "v0.3.0", "source": {"type": "git", "url": "https://github.com/mmucklo/inflect.git", "reference": "b665bcd3d4c23b6aa1990b6405ff96dd437689e9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mmucklo/inflect/zipball/b665bcd3d4c23b6aa1990b6405ff96dd437689e9", "reference": "b665bcd3d4c23b6aa1990b6405ff96dd437689e9", "shasum": ""}, "require": {"php": ">=5.3.17"}, "require-dev": {"phpunit/phpunit": "3.7.*"}, "type": "library", "autoload": {"psr-0": {"Inflect": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "inflect - a memoizing inflector for php", "keywords": ["inflect", "inflector", "pluralize", "singularize", "urlify"], "support": {"issues": "https://github.com/mmucklo/inflect/issues", "source": "https://github.com/mmucklo/inflect/tree/v0.3.0"}, "time": "2015-05-16T04:16:08+00:00"}, {"name": "moneyphp/money", "version": "v3.3.3", "source": {"type": "git", "url": "https://github.com/moneyphp/money.git", "reference": "0dc40e3791c67e8793e3aa13fead8cf4661ec9cd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/moneyphp/money/zipball/0dc40e3791c67e8793e3aa13fead8cf4661ec9cd", "reference": "0dc40e3791c67e8793e3aa13fead8cf4661ec9cd", "shasum": ""}, "require": {"ext-json": "*", "php": ">=5.6"}, "require-dev": {"cache/taggable-cache": "^0.4.0", "doctrine/instantiator": "^1.0.5", "ext-bcmath": "*", "ext-gmp": "*", "ext-intl": "*", "florianv/exchanger": "^1.0", "florianv/swap": "^3.0", "friends-of-phpspec/phpspec-code-coverage": "^3.1.1 || ^4.3", "moneyphp/iso-currencies": "^3.2.1", "php-http/message": "^1.4", "php-http/mock-client": "^1.0.0", "phpspec/phpspec": "^3.4.3", "phpunit/phpunit": "^5.7.27 || ^6.5.14 || ^7.5.18 || ^8.5", "psr/cache": "^1.0", "symfony/phpunit-bridge": "^4"}, "suggest": {"ext-bcmath": "Calculate without integer limits", "ext-gmp": "Calculate without integer limits", "ext-intl": "Format Money objects with intl", "florianv/exchanger": "Exchange rates library for PHP", "florianv/swap": "Exchange rates library for PHP", "psr/cache-implementation": "Used for Currency caching"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Money\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://verraes.net"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "PHP implementation of Fowler's Money pattern", "homepage": "http://moneyphp.org", "keywords": ["Value Object", "money", "vo"], "support": {"issues": "https://github.com/moneyphp/money/issues", "source": "https://github.com/moneyphp/money/tree/v3.3.3"}, "time": "2022-09-21T07:43:36+00:00"}, {"name": "mongodb/mongodb", "version": "1.9.0", "source": {"type": "git", "url": "https://github.com/mongodb/mongo-php-library.git", "reference": "4afe9254e87252879bfbb625858e78fbe072daa0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mongodb/mongo-php-library/zipball/4afe9254e87252879bfbb625858e78fbe072daa0", "reference": "4afe9254e87252879bfbb625858e78fbe072daa0", "shasum": ""}, "require": {"ext-hash": "*", "ext-json": "*", "ext-mongodb": "^1.10.0", "jean85/pretty-package-versions": "^1.2 || ^2.0.1", "php": "^7.1 || ^8.0", "symfony/polyfill-php80": "^1.19"}, "require-dev": {"doctrine/coding-standard": "^9.0", "squizlabs/php_codesniffer": "^3.6", "symfony/phpunit-bridge": "^5.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.9.x-dev"}}, "autoload": {"files": ["src/functions.php"], "psr-4": {"MongoDB\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "MongoDB driver library", "homepage": "https://jira.mongodb.org/browse/PHPLIB", "keywords": ["database", "driver", "mongodb", "persistence"], "support": {"issues": "https://github.com/mongodb/mongo-php-library/issues", "source": "https://github.com/mongodb/mongo-php-library/tree/1.9.0"}, "time": "2021-07-13T20:03:24+00:00"}, {"name": "monolog/monolog", "version": "1.7.0", "source": {"type": "git", "url": "https://github.com/Seldaek/monolog.git", "reference": "6225b22de9dcf36546be3a0b2fa8e3d986153f57"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/monolog/zipball/6225b22de9dcf36546be3a0b2fa8e3d986153f57", "reference": "6225b22de9dcf36546be3a0b2fa8e3d986153f57", "shasum": ""}, "require": {"php": ">=5.3.0", "psr/log": "~1.0"}, "require-dev": {"aws/aws-sdk-php": "~2.4.8", "doctrine/couchdb": "dev-master", "mlehner/gelf-php": "1.0.*", "phpunit/phpunit": "~3.7.0", "raven/raven": "0.5.*", "ruflin/elastica": "0.90.*"}, "suggest": {"aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-mongo": "Allow sending log messages to a MongoDB server", "mlehner/gelf-php": "Allow sending log messages to a GrayLog2 server", "raven/raven": "Allow sending log messages to a Sentry server", "ruflin/elastica": "Allow sending log messages to an Elastic Search server"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.7.x-dev"}}, "autoload": {"psr-0": {"Monolog": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be", "role": "Developer"}], "description": "Sends your logs to files, sockets, inboxes, databases and various web services", "homepage": "http://github.com/Seldaek/monolog", "keywords": ["log", "logging", "psr-3"], "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/master"}, "time": "2013-11-14T19:48:31+00:00"}, {"name": "mtdowling/jmespath.php", "version": "2.6.1", "source": {"type": "git", "url": "https://github.com/jmespath/jmespath.php.git", "reference": "9b87907a81b87bc76d19a7fb2d61e61486ee9edb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jmespath/jmespath.php/zipball/9b87907a81b87bc76d19a7fb2d61e61486ee9edb", "reference": "9b87907a81b87bc76d19a7fb2d61e61486ee9edb", "shasum": ""}, "require": {"php": "^5.4 || ^7.0 || ^8.0", "symfony/polyfill-mbstring": "^1.17"}, "require-dev": {"composer/xdebug-handler": "^1.4 || ^2.0", "phpunit/phpunit": "^4.8.36 || ^7.5.15"}, "bin": ["bin/jp.php"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.6-dev"}}, "autoload": {"files": ["src/JmesPath.php"], "psr-4": {"JmesPath\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Declaratively specify how to extract elements from a JSON document", "keywords": ["json", "jsonpath"], "support": {"issues": "https://github.com/jmespath/jmespath.php/issues", "source": "https://github.com/jmespath/jmespath.php/tree/2.6.1"}, "time": "2021-06-14T00:11:39+00:00"}, {"name": "nesbot/carbon", "version": "1.39.1", "source": {"type": "git", "url": "https://github.com/briannesbitt/Carbon.git", "reference": "4be0c005164249208ce1b5ca633cd57bdd42ff33"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/briannesbitt/Carbon/zipball/4be0c005164249208ce1b5ca633cd57bdd42ff33", "reference": "4be0c005164249208ce1b5ca633cd57bdd42ff33", "shasum": ""}, "require": {"kylekatarnls/update-helper": "^1.1", "php": ">=5.3.9", "symfony/translation": "~2.6 || ~3.0 || ~4.0"}, "require-dev": {"composer/composer": "^1.2", "friendsofphp/php-cs-fixer": "~2", "phpunit/phpunit": "^4.8.35 || ^5.7"}, "bin": ["bin/upgrade-carbon"], "type": "library", "extra": {"update-helper": "Carbon\\Upgrade", "laravel": {"providers": ["Carbon\\Laravel\\ServiceProvider"]}}, "autoload": {"psr-4": {"": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://nesbot.com"}], "description": "A simple API extension for DateTime.", "homepage": "http://carbon.nesbot.com", "keywords": ["date", "datetime", "time"], "support": {"issues": "https://github.com/briannesbitt/Carbon/issues", "source": "https://github.com/briannesbitt/Carbon"}, "time": "2019-10-14T05:51:36+00:00"}, {"name": "nicmart/string-template", "version": "v0.1.3", "source": {"type": "git", "url": "https://github.com/nicmart/StringTemplate.git", "reference": "2a62c240a35a4a20b1be8bd5aa51d4efe93ee4ae"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nicmart/StringTemplate/zipball/2a62c240a35a4a20b1be8bd5aa51d4efe93ee4ae", "reference": "2a62c240a35a4a20b1be8bd5aa51d4efe93ee4ae", "shasum": ""}, "require": {"php": ">=7.2"}, "require-dev": {"php-coveralls/php-coveralls": "^2", "phpunit/phpunit": "^8 || ^9"}, "type": "library", "autoload": {"psr-4": {"StringTemplate\\": "src/StringTemplate/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "StringTemplate is a very simple string template engine for php. I've written it to have a thing like sprintf, but with named and nested substutions.", "support": {"issues": "https://github.com/nicmart/StringTemplate/issues", "source": "https://github.com/nicmart/StringTemplate/tree/v0.1.3"}, "time": "2022-10-25T08:03:55+00:00"}, {"name": "nikic/php-parser", "version": "v4.17.1", "source": {"type": "git", "url": "https://github.com/nikic/PHP-Parser.git", "reference": "a6303e50c90c355c7eeee2c4a8b27fe8dc8fef1d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nikic/PHP-Parser/zipball/a6303e50c90c355c7eeee2c4a8b27fe8dc8fef1d", "reference": "a6303e50c90c355c7eeee2c4a8b27fe8dc8fef1d", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": ">=7.0"}, "require-dev": {"ircmaxell/php-yacc": "^0.0.7", "phpunit/phpunit": "^6.5 || ^7.0 || ^8.0 || ^9.0"}, "bin": ["bin/php-parse"], "type": "library", "extra": {"branch-alias": {"dev-master": "4.9-dev"}}, "autoload": {"psr-4": {"PhpParser\\": "lib/Php<PERSON><PERSON>er"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>"}], "description": "A PHP parser written in PHP", "keywords": ["parser", "php"], "support": {"issues": "https://github.com/nikic/PHP-Parser/issues", "source": "https://github.com/nikic/PHP-Parser/tree/v4.17.1"}, "time": "2023-08-13T19:53:39+00:00"}, {"name": "paragonie/random_compat", "version": "v2.0.21", "source": {"type": "git", "url": "https://github.com/paragonie/random_compat.git", "reference": "96c132c7f2f7bc3230723b66e89f8f150b29d5ae"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/random_compat/zipball/96c132c7f2f7bc3230723b66e89f8f150b29d5ae", "reference": "96c132c7f2f7bc3230723b66e89f8f150b29d5ae", "shasum": ""}, "require": {"php": ">=5.2.0"}, "require-dev": {"phpunit/phpunit": "*"}, "suggest": {"ext-libsodium": "Provides a modern crypto API that can be used to generate random bytes."}, "type": "library", "autoload": {"files": ["lib/random.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com"}], "description": "PHP 5.x polyfill for random_bytes() and random_int() from PHP 7", "keywords": ["csprng", "polyfill", "pseudorandom", "random"], "support": {"email": "<EMAIL>", "issues": "https://github.com/paragonie/random_compat/issues", "source": "https://github.com/paragonie/random_compat"}, "time": "2022-02-16T17:07:03+00:00"}, {"name": "php-http/client-common", "version": "2.7.0", "source": {"type": "git", "url": "https://github.com/php-http/client-common.git", "reference": "880509727a447474d2a71b7d7fa5d268ddd3db4b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/client-common/zipball/880509727a447474d2a71b7d7fa5d268ddd3db4b", "reference": "880509727a447474d2a71b7d7fa5d268ddd3db4b", "shasum": ""}, "require": {"php": "^7.1 || ^8.0", "php-http/httplug": "^2.0", "php-http/message": "^1.6", "psr/http-client": "^1.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.0 || ^2.0", "symfony/options-resolver": "~4.0.15 || ~4.1.9 || ^4.2.1 || ^5.0 || ^6.0", "symfony/polyfill-php80": "^1.17"}, "require-dev": {"doctrine/instantiator": "^1.1", "guzzlehttp/psr7": "^1.4", "nyholm/psr7": "^1.2", "phpspec/phpspec": "^5.1 || ^6.3 || ^7.1", "phpspec/prophecy": "^1.10.2", "phpunit/phpunit": "^7.5.20 || ^8.5.33 || ^9.6.7"}, "suggest": {"ext-json": "To detect JSON responses with the ContentTypePlugin", "ext-libxml": "To detect XML responses with the ContentTypePlugin", "php-http/cache-plugin": "PSR-6 Cache plugin", "php-http/logger-plugin": "PSR-3 Logger plugin", "php-http/stopwatch-plugin": "Symfony Stopwatch plugin"}, "type": "library", "autoload": {"psr-4": {"Http\\Client\\Common\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Common HTTP Client implementations and tools for HTTPlug", "homepage": "http://httplug.io", "keywords": ["client", "common", "http", "httplug"], "support": {"issues": "https://github.com/php-http/client-common/issues", "source": "https://github.com/php-http/client-common/tree/2.7.0"}, "time": "2023-05-17T06:46:59+00:00"}, {"name": "php-http/discovery", "version": "1.19.1", "source": {"type": "git", "url": "https://github.com/php-http/discovery.git", "reference": "57f3de01d32085fea20865f9b16fb0e69347c39e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/discovery/zipball/57f3de01d32085fea20865f9b16fb0e69347c39e", "reference": "57f3de01d32085fea20865f9b16fb0e69347c39e", "shasum": ""}, "require": {"composer-plugin-api": "^1.0|^2.0", "php": "^7.1 || ^8.0"}, "conflict": {"nyholm/psr7": "<1.0", "zendframework/zend-diactoros": "*"}, "provide": {"php-http/async-client-implementation": "*", "php-http/client-implementation": "*", "psr/http-client-implementation": "*", "psr/http-factory-implementation": "*", "psr/http-message-implementation": "*"}, "require-dev": {"composer/composer": "^1.0.2|^2.0", "graham-campbell/phpspec-skip-example-extension": "^5.0", "php-http/httplug": "^1.0 || ^2.0", "php-http/message-factory": "^1.0", "phpspec/phpspec": "^5.1 || ^6.1 || ^7.3", "symfony/phpunit-bridge": "^6.2"}, "type": "composer-plugin", "extra": {"class": "Http\\Discovery\\Composer\\Plugin", "plugin-optional": true}, "autoload": {"psr-4": {"Http\\Discovery\\": "src/"}, "exclude-from-classmap": ["src/Composer/Plugin.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Finds and installs PSR-7, PSR-17, PSR-18 and HTTPlug implementations", "homepage": "http://php-http.org", "keywords": ["adapter", "client", "discovery", "factory", "http", "message", "psr17", "psr7"], "support": {"issues": "https://github.com/php-http/discovery/issues", "source": "https://github.com/php-http/discovery/tree/1.19.1"}, "time": "2023-07-11T07:02:26+00:00"}, {"name": "php-http/guzzle6-adapter", "version": "v2.0.2", "source": {"type": "git", "url": "https://github.com/php-http/guzzle6-adapter.git", "reference": "9d1a45eb1c59f12574552e81fb295e9e53430a56"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/guzzle6-adapter/zipball/9d1a45eb1c59f12574552e81fb295e9e53430a56", "reference": "9d1a45eb1c59f12574552e81fb295e9e53430a56", "shasum": ""}, "require": {"guzzlehttp/guzzle": "^6.0", "php": "^7.1 || ^8.0", "php-http/httplug": "^2.0", "psr/http-client": "^1.0"}, "provide": {"php-http/async-client-implementation": "1.0", "php-http/client-implementation": "1.0", "psr/http-client-implementation": "1.0"}, "require-dev": {"ext-curl": "*", "php-http/client-integration-tests": "^2.0 || ^3.0", "phpunit/phpunit": "^7.4 || ^8.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.x-dev"}}, "autoload": {"psr-4": {"Http\\Adapter\\Guzzle6\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Guzzle 6 HTTP Adapter", "homepage": "http://httplug.io", "keywords": ["Guzzle", "http"], "support": {"issues": "https://github.com/php-http/guzzle6-adapter/issues", "source": "https://github.com/php-http/guzzle6-adapter/tree/v2.0.2"}, "time": "2021-03-02T10:52:33+00:00"}, {"name": "php-http/httplug", "version": "2.4.0", "source": {"type": "git", "url": "https://github.com/php-http/httplug.git", "reference": "625ad742c360c8ac580fcc647a1541d29e257f67"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/httplug/zipball/625ad742c360c8ac580fcc647a1541d29e257f67", "reference": "625ad742c360c8ac580fcc647a1541d29e257f67", "shasum": ""}, "require": {"php": "^7.1 || ^8.0", "php-http/promise": "^1.1", "psr/http-client": "^1.0", "psr/http-message": "^1.0 || ^2.0"}, "require-dev": {"friends-of-phpspec/phpspec-code-coverage": "^4.1 || ^5.0 || ^6.0", "phpspec/phpspec": "^5.1 || ^6.0 || ^7.0"}, "type": "library", "autoload": {"psr-4": {"Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Eric <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://sagikazarmark.hu"}], "description": "HTTPlug, the HTTP client abstraction for PHP", "homepage": "http://httplug.io", "keywords": ["client", "http"], "support": {"issues": "https://github.com/php-http/httplug/issues", "source": "https://github.com/php-http/httplug/tree/2.4.0"}, "time": "2023-04-14T15:10:03+00:00"}, {"name": "php-http/message", "version": "1.16.0", "source": {"type": "git", "url": "https://github.com/php-http/message.git", "reference": "47a14338bf4ebd67d317bf1144253d7db4ab55fd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/message/zipball/47a14338bf4ebd67d317bf1144253d7db4ab55fd", "reference": "47a14338bf4ebd67d317bf1144253d7db4ab55fd", "shasum": ""}, "require": {"clue/stream-filter": "^1.5", "php": "^7.2 || ^8.0", "psr/http-message": "^1.1 || ^2.0"}, "provide": {"php-http/message-factory-implementation": "1.0"}, "require-dev": {"ergebnis/composer-normalize": "^2.6", "ext-zlib": "*", "guzzlehttp/psr7": "^1.0 || ^2.0", "laminas/laminas-diactoros": "^2.0 || ^3.0", "php-http/message-factory": "^1.0.2", "phpspec/phpspec": "^5.1 || ^6.3 || ^7.1", "slim/slim": "^3.0"}, "suggest": {"ext-zlib": "Used with compressor/decompressor streams", "guzzlehttp/psr7": "Used with Guzzle PSR-7 Factories", "laminas/laminas-diactoros": "Used with Diactoros Factories", "slim/slim": "Used with Slim Framework PSR-7 implementation"}, "type": "library", "autoload": {"files": ["src/filters.php"], "psr-4": {"Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "HTTP Message related tools", "homepage": "http://php-http.org", "keywords": ["http", "message", "psr-7"], "support": {"issues": "https://github.com/php-http/message/issues", "source": "https://github.com/php-http/message/tree/1.16.0"}, "time": "2023-05-17T06:43:38+00:00"}, {"name": "php-http/message-factory", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/php-http/message-factory.git", "reference": "4d8778e1c7d405cbb471574821c1ff5b68cc8f57"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/message-factory/zipball/4d8778e1c7d405cbb471574821c1ff5b68cc8f57", "reference": "4d8778e1c7d405cbb471574821c1ff5b68cc8f57", "shasum": ""}, "require": {"php": ">=5.4", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Factory interfaces for PSR-7 HTTP Message", "homepage": "http://php-http.org", "keywords": ["factory", "http", "message", "stream", "uri"], "support": {"issues": "https://github.com/php-http/message-factory/issues", "source": "https://github.com/php-http/message-factory/tree/1.1.0"}, "abandoned": "psr/http-factory", "time": "2023-04-14T14:16:17+00:00"}, {"name": "php-http/promise", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/php-http/promise.git", "reference": "44a67cb59f708f826f3bec35f22030b3edb90119"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/promise/zipball/44a67cb59f708f826f3bec35f22030b3edb90119", "reference": "44a67cb59f708f826f3bec35f22030b3edb90119", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"friends-of-phpspec/phpspec-code-coverage": "^4.3.2 || ^6.3", "phpspec/phpspec": "^5.1.2 || ^6.2 || ^7.4"}, "type": "library", "autoload": {"psr-4": {"Http\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Promise used for asynchronous HTTP requests", "homepage": "http://httplug.io", "keywords": ["promise"], "support": {"issues": "https://github.com/php-http/promise/issues", "source": "https://github.com/php-http/promise/tree/1.2.1"}, "time": "2023-11-08T12:57:08+00:00"}, {"name": "php-jsonpointer/php-jsonpointer", "version": "v3.0.2", "source": {"type": "git", "url": "https://github.com/raphaelstolt/php-jsonpointer.git", "reference": "4428f86c6f23846e9faa5a420c4ef14e485b3afb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/raphaelstolt/php-jsonpointer/zipball/4428f86c6f23846e9faa5a420c4ef14e485b3afb", "reference": "4428f86c6f23846e9faa5a420c4ef14e485b3afb", "shasum": ""}, "require": {"php": ">=5.4"}, "require-dev": {"friendsofphp/php-cs-fixer": "^1.11", "phpunit/phpunit": "4.6.*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-0": {"Rs\\Json": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://raphaelstolt.blogspot.com/"}], "description": "Implementation of JSON Pointer (http://tools.ietf.org/html/rfc6901)", "homepage": "https://github.com/raphaelstolt/php-jsonpointer", "keywords": ["json", "json pointer", "json traversal"], "support": {"issues": "https://github.com/raphaelstolt/php-jsonpointer/issues", "source": "https://github.com/raphaelstolt/php-jsonpointer/tree/master"}, "time": "2016-08-29T08:51:01+00:00"}, {"name": "predis/predis", "version": "v1.1.10", "source": {"type": "git", "url": "https://github.com/predis/predis.git", "reference": "a2fb02d738bedadcffdbb07efa3a5e7bd57f8d6e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/predis/predis/zipball/a2fb02d738bedadcffdbb07efa3a5e7bd57f8d6e", "reference": "a2fb02d738bedadcffdbb07efa3a5e7bd57f8d6e", "shasum": ""}, "require": {"php": ">=5.3.9"}, "require-dev": {"phpunit/phpunit": "~4.8"}, "suggest": {"ext-curl": "Allows access to Webdis when paired with phpiredis", "ext-phpiredis": "Allows faster serialization and deserialization of the Redis protocol"}, "type": "library", "autoload": {"psr-4": {"Predis\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://clorophilla.net", "role": "Creator & Maintainer"}, {"name": "<PERSON>", "homepage": "https://till.im", "role": "Maintainer"}], "description": "Flexible and feature-complete Redis client for PHP and HHVM", "homepage": "http://github.com/predis/predis", "keywords": ["nosql", "predis", "redis"], "support": {"issues": "https://github.com/predis/predis/issues", "source": "https://github.com/predis/predis/tree/v1.1.10"}, "funding": [{"url": "https://github.com/sponsors/tillkruss", "type": "github"}], "time": "2022-01-05T17:46:08+00:00"}, {"name": "propaganistas/laravel-phone", "version": "4.0.3", "source": {"type": "git", "url": "https://github.com/Propaganistas/Laravel-Phone.git", "reference": "ff7651825a31dcf31282d1838ed2e0a03a45f9ea"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Propaganistas/Laravel-Phone/zipball/ff7651825a31dcf31282d1838ed2e0a03a45f9ea", "reference": "ff7651825a31dcf31282d1838ed2e0a03a45f9ea", "shasum": ""}, "require": {"giggsey/libphonenumber-for-php": "^7.0|^8.0", "illuminate/support": ">=5.0,<5.8", "illuminate/validation": ">=5.0,<5.8", "julien-c/iso3166": "^2.0", "php": ">=7.0"}, "require-dev": {"orchestra/testbench": "*", "phpunit/phpunit": "*"}, "suggest": {"propaganistas/laravel-intl": "Adds internationalization functions, including a compatible and fully translated country list API."}, "type": "library", "extra": {"laravel": {"providers": ["Propaganistas\\LaravelPhone\\PhoneServiceProvider"]}}, "autoload": {"files": ["src/helpers.php"], "psr-4": {"Propaganistas\\LaravelPhone\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Propaganistas", "email": "<EMAIL>"}], "description": "Adds phone number functionality to <PERSON><PERSON> and <PERSON><PERSON> based on Google's libphonenumber API.", "keywords": ["laravel", "libphonenumber", "lumen", "phone", "validation"], "support": {"issues": "https://github.com/Propaganistas/Laravel-Phone/issues", "source": "https://github.com/Propaganistas/Laravel-Phone/tree/master"}, "time": "2018-08-27T13:59:09+00:00"}, {"name": "psr/cache", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/cache.git", "reference": "d11b50ad223250cf17b86e38383413f5a6764bf8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/cache/zipball/d11b50ad223250cf17b86e38383413f5a6764bf8", "reference": "d11b50ad223250cf17b86e38383413f5a6764bf8", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Cache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for caching libraries", "keywords": ["cache", "psr", "psr-6"], "support": {"source": "https://github.com/php-fig/cache/tree/master"}, "time": "2016-08-06T20:24:11+00:00"}, {"name": "psr/http-client", "version": "1.0.3", "source": {"type": "git", "url": "https://github.com/php-fig/http-client.git", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-client/zipball/bb5906edc1c324c9a05aa0873d40117941e5fa90", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90", "shasum": ""}, "require": {"php": "^7.0 || ^8.0", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP clients", "homepage": "https://github.com/php-fig/http-client", "keywords": ["http", "http-client", "psr", "psr-18"], "support": {"source": "https://github.com/php-fig/http-client"}, "time": "2023-09-23T14:17:50+00:00"}, {"name": "psr/http-factory", "version": "1.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/http-factory.git", "reference": "e616d01114759c4c489f93b099585439f795fe35"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-factory/zipball/e616d01114759c4c489f93b099585439f795fe35", "reference": "e616d01114759c4c489f93b099585439f795fe35", "shasum": ""}, "require": {"php": ">=7.0.0", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interfaces for PSR-7 HTTP message factories", "keywords": ["factory", "http", "message", "psr", "psr-17", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-factory/tree/1.0.2"}, "time": "2023-04-10T20:10:41+00:00"}, {"name": "psr/http-message", "version": "1.1", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "cb6ce4845ce34a8ad9e68117c10ee90a29919eba"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/cb6ce4845ce34a8ad9e68117c10ee90a29919eba", "reference": "cb6ce4845ce34a8ad9e68117c10ee90a29919eba", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-message/tree/1.1"}, "time": "2023-04-04T09:50:52+00:00"}, {"name": "psr/log", "version": "1.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "4ebe3a8bf773a19edfe0a84b6585ba3d401b724d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/4ebe3a8bf773a19edfe0a84b6585ba3d401b724d", "reference": "4ebe3a8bf773a19edfe0a84b6585ba3d401b724d", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "Psr/Log/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "support": {"source": "https://github.com/php-fig/log/tree/master"}, "time": "2016-10-10T12:19:37+00:00"}, {"name": "ralouphie/getallheaders", "version": "2.0.5", "source": {"type": "git", "url": "https://github.com/ralouphie/getallheaders.git", "reference": "5601c8a83fbba7ef674a7369456d12f1e0d0eafa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/5601c8a83fbba7ef674a7369456d12f1e0d0eafa", "reference": "5601c8a83fbba7ef674a7369456d12f1e0d0eafa", "shasum": ""}, "require": {"php": ">=5.3"}, "require-dev": {"phpunit/phpunit": "~3.7.0", "satooshi/php-coveralls": ">=1.0"}, "type": "library", "autoload": {"files": ["src/getallheaders.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "support": {"issues": "https://github.com/ralouphie/getallheaders/issues", "source": "https://github.com/ralouphie/getallheaders/tree/2.0.5"}, "time": "2016-02-11T07:05:27+00:00"}, {"name": "ramsey/array_column", "version": "1.1.3", "source": {"type": "git", "url": "https://github.com/ramsey/array_column.git", "reference": "f8e52eb28e67eb50e613b451dd916abcf783c1db"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ramsey/array_column/zipball/f8e52eb28e67eb50e613b451dd916abcf783c1db", "reference": "f8e52eb28e67eb50e613b451dd916abcf783c1db", "shasum": ""}, "require-dev": {"jakub-onderka/php-parallel-lint": "0.8.*", "phpunit/phpunit": "~4.5", "satooshi/php-coveralls": "0.6.*", "squizlabs/php_codesniffer": "~2.2"}, "type": "library", "autoload": {"files": ["src/array_column.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "http://benramsey.com"}], "description": "Provides functionality for array_column() to projects using PHP earlier than version 5.5.", "homepage": "https://github.com/ramsey/array_column", "keywords": ["array", "array_column", "column"], "support": {"issues": "https://github.com/ramsey/array_column/issues", "source": "https://github.com/ramsey/array_column"}, "abandoned": "it-for-free/array_column", "time": "2015-03-20T22:07:39+00:00"}, {"name": "ramsey/uuid", "version": "3.9.7", "source": {"type": "git", "url": "https://github.com/ramsey/uuid.git", "reference": "dc75aa439eb4c1b77f5379fd958b3dc0e6014178"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ramsey/uuid/zipball/dc75aa439eb4c1b77f5379fd958b3dc0e6014178", "reference": "dc75aa439eb4c1b77f5379fd958b3dc0e6014178", "shasum": ""}, "require": {"ext-json": "*", "paragonie/random_compat": "^1 | ^2 | ^9.99.99", "php": "^5.4 | ^7.0 | ^8.0", "symfony/polyfill-ctype": "^1.8"}, "replace": {"rhumsaa/uuid": "self.version"}, "require-dev": {"codeception/aspect-mock": "^1 | ^2", "doctrine/annotations": "^1.2", "goaop/framework": "1.0.0-alpha.2 | ^1 | >=2.1.0 <=2.3.2", "mockery/mockery": "^0.9.11 | ^1", "moontoast/math": "^1.1", "nikic/php-parser": "<=4.5.0", "paragonie/random-lib": "^2", "php-mock/php-mock-phpunit": "^0.3 | ^1.1 | ^2.6", "php-parallel-lint/php-parallel-lint": "^1.3", "phpunit/phpunit": ">=4.8.36 <9.0.0 | >=9.3.0", "squizlabs/php_codesniffer": "^3.5", "yoast/phpunit-polyfills": "^1.0"}, "suggest": {"ext-ctype": "Provides support for PHP Ctype functions", "ext-libsodium": "Provides the PECL libsodium extension for use with the SodiumRandomGenerator", "ext-openssl": "Provides the OpenSSL extension for use with the OpenSslGenerator", "ext-uuid": "Provides the PECL UUID extension for use with the PeclUuidTimeGenerator and PeclUuidRandomGenerator", "moontoast/math": "Provides support for converting UUID to 128-bit integer (in string form).", "paragonie/random-lib": "Provides RandomLib for use with the RandomLibAdapter", "ramsey/uuid-console": "A console application for generating UUIDs with ramsey/uuid", "ramsey/uuid-doctrine": "Allows the use of Ramsey\\Uuid\\Uuid as Doctrine field type."}, "type": "library", "autoload": {"files": ["src/functions.php"], "psr-4": {"Ramsey\\Uuid\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://benramsey.com"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Formerly rhumsaa/uuid. A PHP 5.4+ library for generating RFC 4122 version 1, 3, 4, and 5 universally unique identifiers (UUID).", "homepage": "https://github.com/ramsey/uuid", "keywords": ["guid", "identifier", "uuid"], "support": {"issues": "https://github.com/ramsey/uuid/issues", "rss": "https://github.com/ramsey/uuid/releases.atom", "source": "https://github.com/ramsey/uuid", "wiki": "https://github.com/ramsey/uuid/wiki"}, "funding": [{"url": "https://github.com/ramsey", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/ramsey/uuid", "type": "tidelift"}], "time": "2022-12-19T21:55:10+00:00"}, {"name": "rlanvin/php-rrule", "version": "v2.4.0", "source": {"type": "git", "url": "https://github.com/rlanvin/php-rrule.git", "reference": "2acd9950e803ea65514d6440212d39096df9c528"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/rlanvin/php-rrule/zipball/2acd9950e803ea65514d6440212d39096df9c528", "reference": "2acd9950e803ea65514d6440212d39096df9c528", "shasum": ""}, "require": {"php": ">=5.6.0"}, "require-dev": {"phpmd/phpmd": "@stable", "phpunit/phpunit": "^5.7|^6.5|^8.0"}, "suggest": {"ext-intl": "Intl extension is needed for humanReadable()"}, "type": "library", "autoload": {"psr-4": {"RRule\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Lightweight and fast recurrence rules for PHP (RFC 5545)", "homepage": "https://github.com/rlanvin/php-rrule", "keywords": ["date", "ical", "recurrence", "recurring", "r<PERSON>le"], "support": {"issues": "https://github.com/rlanvin/php-rrule/issues", "source": "https://github.com/rlanvin/php-rrule/tree/v2.4.0"}, "time": "2023-01-06T10:19:10+00:00"}, {"name": "sabberworm/php-css-parser", "version": "v8.8.0", "source": {"type": "git", "url": "https://github.com/MyIntervals/PHP-CSS-Parser.git", "reference": "3de493bdddfd1f051249af725c7e0d2c38fed740"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MyIntervals/PHP-CSS-Parser/zipball/3de493bdddfd1f051249af725c7e0d2c38fed740", "reference": "3de493bdddfd1f051249af725c7e0d2c38fed740", "shasum": ""}, "require": {"ext-iconv": "*", "php": "^5.6.20 || ^7.0.0 || ~8.0.0 || ~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0"}, "require-dev": {"phpunit/phpunit": "5.7.27 || 6.5.14 || 7.5.20 || 8.5.41"}, "suggest": {"ext-mbstring": "for parsing UTF-8 CSS"}, "type": "library", "extra": {"branch-alias": {"dev-main": "9.0.x-dev"}}, "autoload": {"psr-4": {"Sabberworm\\CSS\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Parser for CSS Files written in PHP", "homepage": "https://www.sabberworm.com/blog/2010/6/10/php-css-parser", "keywords": ["css", "parser", "stylesheet"], "support": {"issues": "https://github.com/MyIntervals/PHP-CSS-Parser/issues", "source": "https://github.com/MyIntervals/PHP-CSS-Parser/tree/v8.8.0"}, "time": "2025-03-23T17:59:05+00:00"}, {"name": "sabre/uri", "version": "2.3.2", "source": {"type": "git", "url": "https://github.com/sabre-io/uri.git", "reference": "eceb4a1b8b680b45e215574222d6ca00be541970"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sabre-io/uri/zipball/eceb4a1b8b680b45e215574222d6ca00be541970", "reference": "eceb4a1b8b680b45e215574222d6ca00be541970", "shasum": ""}, "require": {"php": "^7.4 || ^8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.9", "phpstan/phpstan": "^1.8", "phpunit/phpunit": "^9.0"}, "type": "library", "autoload": {"files": ["lib/functions.php"], "psr-4": {"Sabre\\Uri\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://evertpot.com/", "role": "Developer"}], "description": "Functions for making sense out of URIs.", "homepage": "http://sabre.io/uri/", "keywords": ["rfc3986", "uri", "url"], "support": {"forum": "https://groups.google.com/group/sabredav-discuss", "issues": "https://github.com/sabre-io/uri/issues", "source": "https://github.com/fruux/sabre-uri"}, "time": "2022-09-19T11:58:52+00:00"}, {"name": "sabre/vobject", "version": "4.1.0", "source": {"type": "git", "url": "https://github.com/sabre-io/vobject.git", "reference": "8899c0e856b3178b17f4e9a4e85010209f32a2fa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sabre-io/vobject/zipball/8899c0e856b3178b17f4e9a4e85010209f32a2fa", "reference": "8899c0e856b3178b17f4e9a4e85010209f32a2fa", "shasum": ""}, "require": {"ext-mbstring": "*", "php": ">=5.5", "sabre/xml": "~1.1"}, "require-dev": {"phpunit/phpunit": "*", "sabre/cs": "~0.0.3"}, "suggest": {"hoa/bench": "If you would like to run the benchmark scripts"}, "bin": ["bin/vobject", "bin/generate_vcards"], "type": "library", "extra": {"branch-alias": {"dev-master": "4.0.x-dev"}}, "autoload": {"psr-4": {"Sabre\\VObject\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://evertpot.com/", "role": "Developer"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://tobschall.de/", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://mnt.io/", "role": "Developer"}], "description": "The VObject library for PHP allows you to easily parse and manipulate iCalendar and vCard objects", "homepage": "http://sabre.io/vobject/", "keywords": ["availability", "freebusy", "iCalendar", "ics", "jCal", "jCard", "recurrence", "rfc2425", "rfc2426", "rfc2739", "rfc4770", "rfc5545", "rfc5546", "rfc6321", "rfc6350", "rfc6351", "rfc6474", "rfc6638", "rfc6715", "rfc6868", "vCard", "vcf", "xCal", "xCard"], "support": {"forum": "https://groups.google.com/group/sabredav-discuss", "issues": "https://github.com/sabre-io/vobject/issues", "source": "https://github.com/fruux/sabre-vobject"}, "time": "2016-04-07T00:48:27+00:00"}, {"name": "sabre/xml", "version": "1.5.1", "source": {"type": "git", "url": "https://github.com/sabre-io/xml.git", "reference": "a367665f1df614c3b8fefc30a54de7cd295e444e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sabre-io/xml/zipball/a367665f1df614c3b8fefc30a54de7cd295e444e", "reference": "a367665f1df614c3b8fefc30a54de7cd295e444e", "shasum": ""}, "require": {"ext-dom": "*", "ext-xmlreader": "*", "ext-xmlwriter": "*", "lib-libxml": ">=2.6.20", "php": ">=5.5.5", "sabre/uri": ">=1.0,<3.0.0"}, "require-dev": {"phpunit/phpunit": "~4.8|~5.7", "sabre/cs": "~1.0.0"}, "type": "library", "autoload": {"files": ["lib/Deserializer/functions.php", "lib/Serializer/functions.php"], "psr-4": {"Sabre\\Xml\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://evertpot.com/", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "sabre/xml is an XML library that you may not hate.", "homepage": "https://sabre.io/xml/", "keywords": ["XMLReader", "XMLWriter", "dom", "xml"], "support": {"forum": "https://groups.google.com/group/sabredav-discuss", "issues": "https://github.com/sabre-io/xml/issues", "source": "https://github.com/fruux/sabre-xml"}, "time": "2019-01-09T13:51:57+00:00"}, {"name": "saritasa/laravel-fluent-validation", "version": "1.2.0", "source": {"type": "git", "url": "https://github.com/Saritasa/php-laravel-fluent-validation.git", "reference": "f2c095cd935467a934e8086cfd405ec008ecc988"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Saritasa/php-laravel-fluent-validation/zipball/f2c095cd935467a934e8086cfd405ec008ecc988", "reference": "f2c095cd935467a934e8086cfd405ec008ecc988", "shasum": ""}, "require": {"illuminate/database": "^5.4 || ^6.0 || ^7.0 || ^8.0 || ^9.0", "illuminate/support": "^5.4 || ^6.0 || ^7.0 || ^8.0 || ^9.0", "illuminate/validation": "^5.4 || ^6.0 || ^7.0 || ^8.0 || ^9.0", "php": ">=7.1", "propaganistas/laravel-phone": "^3.0 || ^4.0 || ^5.0", "saritasa/php-common": "^1.0"}, "require-dev": {"mockery/mockery": "^1.0", "phpunit/phpunit": "^6.0 || ^7.0 || ^8.0", "squizlabs/php_codesniffer": "^3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}, "laravel": {"providers": ["Saritasa\\Laravel\\Validation\\FluentValidationServiceProvider"]}}, "autoload": {"psr-4": {"Saritasa\\Laravel\\Validation\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Set of fluent builders for Laravel request validation rules", "keywords": ["fluent", "laravel", "validation"], "support": {"issues": "https://github.com/Saritasa/php-laravel-fluent-validation/issues", "source": "https://github.com/Saritasa/php-laravel-fluent-validation/tree/1.2.0"}, "time": "2022-07-20T12:20:02+00:00"}, {"name": "saritasa/php-common", "version": "1.2.2", "source": {"type": "git", "url": "https://github.com/Saritasa/php-common.git", "reference": "75d38b1c4644d980a169f33f13781b14761e5c6b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Saritasa/php-common/zipball/75d38b1c4644d980a169f33f13781b14761e5c6b", "reference": "75d38b1c4644d980a169f33f13781b14761e5c6b", "shasum": ""}, "require": {"php": ">=7.0"}, "require-dev": {"phpunit/phpunit": "^6.0", "squizlabs/php_codesniffer": "^3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Saritasa\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Saritasa classes, that can be universally used in any application", "keywords": ["php", "saritasa"], "support": {"issues": "https://github.com/Saritasa/php-common/issues", "source": "https://github.com/Saritasa/php-common/tree/1.2.2"}, "time": "2022-10-10T11:52:02+00:00"}, {"name": "sentry/sentry", "version": "3.22.1", "source": {"type": "git", "url": "https://github.com/getsentry/sentry-php.git", "reference": "8859631ba5ab15bc1af420b0eeed19ecc6c9d81d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/getsentry/sentry-php/zipball/8859631ba5ab15bc1af420b0eeed19ecc6c9d81d", "reference": "8859631ba5ab15bc1af420b0eeed19ecc6c9d81d", "shasum": ""}, "require": {"ext-json": "*", "ext-mbstring": "*", "guzzlehttp/promises": "^1.5.3|^2.0", "jean85/pretty-package-versions": "^1.5|^2.0.4", "php": "^7.2|^8.0", "php-http/async-client-implementation": "^1.0", "php-http/client-common": "^1.5|^2.0", "php-http/discovery": "^1.15", "php-http/httplug": "^1.1|^2.0", "php-http/message": "^1.5", "php-http/message-factory": "^1.1", "psr/http-factory": "^1.0", "psr/http-factory-implementation": "^1.0", "psr/log": "^1.0|^2.0|^3.0", "symfony/options-resolver": "^3.4.43|^4.4.30|^5.0.11|^6.0|^7.0", "symfony/polyfill-php80": "^1.17"}, "conflict": {"php-http/client-common": "1.8.0", "raven/raven": "*"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.19|3.4.*", "guzzlehttp/psr7": "^1.8.4|^2.1.1", "http-interop/http-factory-guzzle": "^1.0", "monolog/monolog": "^1.6|^2.0|^3.0", "nikic/php-parser": "^4.10.3", "php-http/mock-client": "^1.3", "phpbench/phpbench": "^1.0", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^1.3", "phpstan/phpstan-phpunit": "^1.0", "phpunit/phpunit": "^8.5.14|^9.4", "symfony/phpunit-bridge": "^5.2|^6.0", "vimeo/psalm": "^4.17"}, "suggest": {"monolog/monolog": "Allow sending log messages to Sentry by using the included Monolog handler."}, "type": "library", "autoload": {"files": ["src/functions.php"], "psr-4": {"Sentry\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Sentry", "email": "<EMAIL>"}], "description": "A PHP SDK for Sentry (http://sentry.io)", "homepage": "http://sentry.io", "keywords": ["crash-reporting", "crash-reports", "error-handler", "error-monitoring", "log", "logging", "sentry"], "support": {"issues": "https://github.com/getsentry/sentry-php/issues", "source": "https://github.com/getsentry/sentry-php/tree/3.22.1"}, "funding": [{"url": "https://sentry.io/", "type": "custom"}, {"url": "https://sentry.io/pricing/", "type": "custom"}], "time": "2023-11-13T11:47:28+00:00"}, {"name": "spatie/url-signer", "version": "1.2.3", "source": {"type": "git", "url": "https://github.com/spatie/url-signer.git", "reference": "801e65277e27fc7aba2abe6859284677aaf8b0a4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/spatie/url-signer/zipball/801e65277e27fc7aba2abe6859284677aaf8b0a4", "reference": "801e65277e27fc7aba2abe6859284677aaf8b0a4", "shasum": ""}, "require": {"league/uri": "^6.0", "league/uri-components": "^2.2", "php": "^7.4|^8.0"}, "require-dev": {"phpunit/phpunit": "^9.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"psr-4": {"Spatie\\UrlSigner\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sebastian<PERSON><PERSON>ne", "role": "Developer"}], "description": "Generate a url with an expiration date and signature to prevent unauthorized access", "homepage": "https://github.com/spatie/url-signer", "keywords": ["encryption", "security", "sign", "spatie", "url"], "support": {"issues": "https://github.com/spatie/url-signer/issues", "source": "https://github.com/spatie/url-signer/tree/1.2.3"}, "time": "2022-09-20T15:05:57+00:00"}, {"name": "symfony/console", "version": "v3.4.47", "source": {"type": "git", "url": "https://github.com/symfony/console.git", "reference": "a10b1da6fc93080c180bba7219b5ff5b7518fe81"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/console/zipball/a10b1da6fc93080c180bba7219b5ff5b7518fe81", "reference": "a10b1da6fc93080c180bba7219b5ff5b7518fe81", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/debug": "~2.8|~3.0|~4.0", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"symfony/dependency-injection": "<3.4", "symfony/process": "<3.3"}, "provide": {"psr/log-implementation": "1.0"}, "require-dev": {"psr/log": "~1.0", "symfony/config": "~3.3|~4.0", "symfony/dependency-injection": "~3.4|~4.0", "symfony/event-dispatcher": "~2.8|~3.0|~4.0", "symfony/lock": "~3.4|~4.0", "symfony/process": "~3.3|~4.0"}, "suggest": {"psr/log": "For using the console logger", "symfony/event-dispatcher": "", "symfony/lock": "", "symfony/process": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Console Component", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/console/tree/v3.4.47"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-24T10:57:07+00:00"}, {"name": "symfony/debug", "version": "v3.4.47", "source": {"type": "git", "url": "https://github.com/symfony/debug.git", "reference": "ab42889de57fdfcfcc0759ab102e2fd4ea72dcae"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/debug/zipball/ab42889de57fdfcfcc0759ab102e2fd4ea72dcae", "reference": "ab42889de57fdfcfcc0759ab102e2fd4ea72dcae", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "psr/log": "~1.0"}, "conflict": {"symfony/http-kernel": ">=2.3,<2.3.24|~2.4.0|>=2.5,<2.5.9|>=2.6,<2.6.2"}, "require-dev": {"symfony/http-kernel": "~2.8|~3.0|~4.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Debug\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Debug Component", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/debug/tree/v3.4.47"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "abandoned": "symfony/error-handler", "time": "2020-10-24T10:57:07+00:00"}, {"name": "symfony/deprecation-contracts", "version": "v2.5.2", "source": {"type": "git", "url": "https://github.com/symfony/deprecation-contracts.git", "reference": "e8b495ea28c1d97b5e0c121748d6f9b53d075c66"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/e8b495ea28c1d97b5e0c121748d6f9b53d075c66", "reference": "e8b495ea28c1d97b5e0c121748d6f9b53d075c66", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"files": ["function.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v2.5.2"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-01-02T09:53:40+00:00"}, {"name": "symfony/event-dispatcher", "version": "v4.4.44", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher.git", "reference": "1e866e9e5c1b22168e0ce5f0b467f19bba61266a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/1e866e9e5c1b22168e0ce5f0b467f19bba61266a", "reference": "1e866e9e5c1b22168e0ce5f0b467f19bba61266a", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/event-dispatcher-contracts": "^1.1", "symfony/polyfill-php80": "^1.16"}, "conflict": {"symfony/dependency-injection": "<3.4"}, "provide": {"psr/event-dispatcher-implementation": "1.0", "symfony/event-dispatcher-implementation": "1.1"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^3.4|^4.0|^5.0", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/error-handler": "~3.4|~4.4", "symfony/expression-language": "^3.4|^4.0|^5.0", "symfony/http-foundation": "^3.4|^4.0|^5.0", "symfony/service-contracts": "^1.1|^2", "symfony/stopwatch": "^3.4|^4.0|^5.0"}, "suggest": {"symfony/dependency-injection": "", "symfony/http-kernel": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\EventDispatcher\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools that allow your application components to communicate with each other by dispatching events and listening to them", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v4.4.44"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-07-20T09:59:04+00:00"}, {"name": "symfony/event-dispatcher-contracts", "version": "v1.10.0", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher-contracts.git", "reference": "761c8b8387cfe5f8026594a75fdf0a4e83ba6974"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/761c8b8387cfe5f8026594a75fdf0a4e83ba6974", "reference": "761c8b8387cfe5f8026594a75fdf0a4e83ba6974", "shasum": ""}, "require": {"php": ">=7.1.3"}, "suggest": {"psr/event-dispatcher": "", "symfony/event-dispatcher-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.1-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\EventDispatcher\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to dispatching event", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v1.10.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-07-20T09:59:04+00:00"}, {"name": "symfony/filesystem", "version": "v5.4.25", "source": {"type": "git", "url": "https://github.com/symfony/filesystem.git", "reference": "0ce3a62c9579a53358d3a7eb6b3dfb79789a6364"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/filesystem/zipball/0ce3a62c9579a53358d3a7eb6b3dfb79789a6364", "reference": "0ce3a62c9579a53358d3a7eb6b3dfb79789a6364", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.8", "symfony/polyfill-php80": "^1.16"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Filesystem\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides basic utilities for the filesystem", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/filesystem/tree/v5.4.25"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-05-31T13:04:02+00:00"}, {"name": "symfony/finder", "version": "v3.4.47", "source": {"type": "git", "url": "https://github.com/symfony/finder.git", "reference": "b6b6ad3db3edb1b4b1c1896b1975fb684994de6e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/finder/zipball/b6b6ad3db3edb1b4b1c1896b1975fb684994de6e", "reference": "b6b6ad3db3edb1b4b1c1896b1975fb684994de6e", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Finder\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Finder Component", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/finder/tree/v3.4.47"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-11-16T17:02:08+00:00"}, {"name": "symfony/http-foundation", "version": "v3.4.47", "source": {"type": "git", "url": "https://github.com/symfony/http-foundation.git", "reference": "b9885fcce6fe494201da4f70a9309770e9d13dc8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-foundation/zipball/b9885fcce6fe494201da4f70a9309770e9d13dc8", "reference": "b9885fcce6fe494201da4f70a9309770e9d13dc8", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/polyfill-mbstring": "~1.1", "symfony/polyfill-php70": "~1.6"}, "require-dev": {"symfony/expression-language": "~2.8|~3.0|~4.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpFoundation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony HttpFoundation Component", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/http-foundation/tree/v3.4.47"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-24T10:57:07+00:00"}, {"name": "symfony/http-kernel", "version": "v3.4.49", "source": {"type": "git", "url": "https://github.com/symfony/http-kernel.git", "reference": "5aa72405f5bd5583c36ed6e756acb17d3f98ac40"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-kernel/zipball/5aa72405f5bd5583c36ed6e756acb17d3f98ac40", "reference": "5aa72405f5bd5583c36ed6e756acb17d3f98ac40", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "psr/log": "~1.0", "symfony/debug": "^3.3.3|~4.0", "symfony/event-dispatcher": "~2.8|~3.0|~4.0", "symfony/http-foundation": "~3.4.12|~4.0.12|^4.1.1", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-php56": "~1.8"}, "conflict": {"symfony/config": "<2.8", "symfony/dependency-injection": "<3.4.10|<4.0.10,>=4", "symfony/var-dumper": "<3.3", "twig/twig": "<1.34|<2.4,>=2"}, "provide": {"psr/log-implementation": "1.0"}, "require-dev": {"psr/cache": "~1.0", "symfony/browser-kit": "~2.8|~3.0|~4.0", "symfony/class-loader": "~2.8|~3.0", "symfony/config": "~2.8|~3.0|~4.0", "symfony/console": "~2.8|~3.0|~4.0", "symfony/css-selector": "~2.8|~3.0|~4.0", "symfony/dependency-injection": "^3.4.10|^4.0.10", "symfony/dom-crawler": "~2.8|~3.0|~4.0", "symfony/expression-language": "~2.8|~3.0|~4.0", "symfony/finder": "~2.8|~3.0|~4.0", "symfony/process": "~2.8|~3.0|~4.0", "symfony/routing": "~3.4|~4.0", "symfony/stopwatch": "~2.8|~3.0|~4.0", "symfony/templating": "~2.8|~3.0|~4.0", "symfony/translation": "~2.8|~3.0|~4.0", "symfony/var-dumper": "~3.3|~4.0"}, "suggest": {"symfony/browser-kit": "", "symfony/config": "", "symfony/console": "", "symfony/dependency-injection": "", "symfony/finder": "", "symfony/var-dumper": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpKernel\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony HttpKernel Component", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/http-kernel/tree/v3.4.49"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-05-19T12:06:59+00:00"}, {"name": "symfony/options-resolver", "version": "v5.4.21", "source": {"type": "git", "url": "https://github.com/symfony/options-resolver.git", "reference": "4fe5cf6ede71096839f0e4b4444d65dd3a7c1eb9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/options-resolver/zipball/4fe5cf6ede71096839f0e4b4444d65dd3a7c1eb9", "reference": "4fe5cf6ede71096839f0e4b4444d65dd3a7c1eb9", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-php73": "~1.0", "symfony/polyfill-php80": "^1.16"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\OptionsResolver\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an improved replacement for the array_replace PHP function", "homepage": "https://symfony.com", "keywords": ["config", "configuration", "options"], "support": {"source": "https://github.com/symfony/options-resolver/tree/v5.4.21"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-02-14T08:03:56+00:00"}, {"name": "symfony/polyfill-ctype", "version": "v1.28.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-ctype.git", "reference": "ea208ce43cbb04af6867b4fdddb1bdbf84cc28cb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/ea208ce43cbb04af6867b4fdddb1bdbf84cc28cb", "reference": "ea208ce43cbb04af6867b4fdddb1bdbf84cc28cb", "shasum": ""}, "require": {"php": ">=7.1"}, "provide": {"ext-ctype": "*"}, "suggest": {"ext-ctype": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.28-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.28.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-01-26T09:26:14+00:00"}, {"name": "symfony/polyfill-intl-idn", "version": "v1.27.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-idn.git", "reference": "639084e360537a19f9ee352433b84ce831f3d2da"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/639084e360537a19f9ee352433b84ce831f3d2da", "reference": "639084e360537a19f9ee352433b84ce831f3d2da", "shasum": ""}, "require": {"php": ">=7.1", "symfony/polyfill-intl-normalizer": "^1.10", "symfony/polyfill-php72": "^1.10"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.27-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Idn\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's idn_to_ascii and idn_to_utf8 functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "idn", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-idn/tree/v1.27.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-11-03T14:55:06+00:00"}, {"name": "symfony/polyfill-intl-normalizer", "version": "v1.28.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-normalizer.git", "reference": "8c4ad05dd0120b6a53c1ca374dca2ad0a1c4ed92"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/8c4ad05dd0120b6a53c1ca374dca2ad0a1c4ed92", "reference": "8c4ad05dd0120b6a53c1ca374dca2ad0a1c4ed92", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.28-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Normalizer\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's Normalizer class and related functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "intl", "normalizer", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.28.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-01-26T09:26:14+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.28.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "42292d99c55abe617799667f454222c54c60e229"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/42292d99c55abe617799667f454222c54c60e229", "reference": "42292d99c55abe617799667f454222c54c60e229", "shasum": ""}, "require": {"php": ">=7.1"}, "provide": {"ext-mbstring": "*"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.28-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-mbstring/tree/v1.28.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-07-28T09:04:16+00:00"}, {"name": "symfony/polyfill-php56", "version": "v1.20.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php56.git", "reference": "54b8cd7e6c1643d78d011f3be89f3ef1f9f4c675"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php56/zipball/54b8cd7e6c1643d78d011f3be89f3ef1f9f4c675", "reference": "54b8cd7e6c1643d78d011f3be89f3ef1f9f4c675", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "metapackage", "extra": {"branch-alias": {"dev-main": "1.20-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 5.6+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php56/tree/v1.20.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-23T14:02:19+00:00"}, {"name": "symfony/polyfill-php70", "version": "v1.20.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php70.git", "reference": "5f03a781d984aae42cebd18e7912fa80f02ee644"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php70/zipball/5f03a781d984aae42cebd18e7912fa80f02ee644", "reference": "5f03a781d984aae42cebd18e7912fa80f02ee644", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "metapackage", "extra": {"branch-alias": {"dev-main": "1.20-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.0+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php70/tree/v1.20.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-23T14:02:19+00:00"}, {"name": "symfony/polyfill-php72", "version": "v1.27.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php72.git", "reference": "869329b1e9894268a8a61dabb69153029b7a8c97"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php72/zipball/869329b1e9894268a8a61dabb69153029b7a8c97", "reference": "869329b1e9894268a8a61dabb69153029b7a8c97", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.27-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php72\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.2+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php72/tree/v1.27.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-11-03T14:55:06+00:00"}, {"name": "symfony/polyfill-php73", "version": "v1.28.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php73.git", "reference": "fe2f306d1d9d346a7fee353d0d5012e401e984b5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php73/zipball/fe2f306d1d9d346a7fee353d0d5012e401e984b5", "reference": "fe2f306d1d9d346a7fee353d0d5012e401e984b5", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.28-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php73\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.3+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php73/tree/v1.28.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-01-26T09:26:14+00:00"}, {"name": "symfony/polyfill-php80", "version": "v1.28.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php80.git", "reference": "6caa57379c4aec19c0a12a38b59b26487dcfe4b5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php80/zipball/6caa57379c4aec19c0a12a38b59b26487dcfe4b5", "reference": "6caa57379c4aec19c0a12a38b59b26487dcfe4b5", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.28-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php80\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.0+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php80/tree/v1.28.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-01-26T09:26:14+00:00"}, {"name": "symfony/process", "version": "v3.4.47", "source": {"type": "git", "url": "https://github.com/symfony/process.git", "reference": "b8648cf1d5af12a44a51d07ef9bf980921f15fca"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/process/zipball/b8648cf1d5af12a44a51d07ef9bf980921f15fca", "reference": "b8648cf1d5af12a44a51d07ef9bf980921f15fca", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Process\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Process Component", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/process/tree/v3.4.47"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-24T10:57:07+00:00"}, {"name": "symfony/routing", "version": "v3.4.47", "source": {"type": "git", "url": "https://github.com/symfony/routing.git", "reference": "3e522ac69cadffd8131cc2b22157fa7662331a6c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/routing/zipball/3e522ac69cadffd8131cc2b22157fa7662331a6c", "reference": "3e522ac69cadffd8131cc2b22157fa7662331a6c", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8"}, "conflict": {"symfony/config": "<3.3.1", "symfony/dependency-injection": "<3.3", "symfony/yaml": "<3.4"}, "require-dev": {"doctrine/annotations": "~1.0", "psr/log": "~1.0", "symfony/config": "^3.3.1|~4.0", "symfony/dependency-injection": "~3.3|~4.0", "symfony/expression-language": "~2.8|~3.0|~4.0", "symfony/http-foundation": "~2.8|~3.0|~4.0", "symfony/yaml": "~3.4|~4.0"}, "suggest": {"doctrine/annotations": "For using the annotation loader", "symfony/config": "For using the all-in-one router or any loader", "symfony/expression-language": "For using expression matching", "symfony/http-foundation": "For using a Symfony Request object", "symfony/yaml": "For using the YAML loader"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Routing\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Routing Component", "homepage": "https://symfony.com", "keywords": ["router", "routing", "uri", "url"], "support": {"source": "https://github.com/symfony/routing/tree/v3.4.47"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-24T10:57:07+00:00"}, {"name": "symfony/serializer", "version": "v5.4.31", "source": {"type": "git", "url": "https://github.com/symfony/serializer.git", "reference": "15574cfa408a6082b6d66c2b6922f95db6cab26d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/serializer/zipball/15574cfa408a6082b6d66c2b6922f95db6cab26d", "reference": "15574cfa408a6082b6d66c2b6922f95db6cab26d", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-php80": "^1.16"}, "conflict": {"doctrine/annotations": "<1.12", "phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/dependency-injection": "<4.4", "symfony/property-access": "<5.4", "symfony/property-info": "<5.4.24|>=6,<6.2.11", "symfony/uid": "<5.3", "symfony/yaml": "<4.4"}, "require-dev": {"doctrine/annotations": "^1.12|^2", "phpdocumentor/reflection-docblock": "^3.2|^4.0|^5.0", "symfony/cache": "^4.4|^5.0|^6.0", "symfony/config": "^4.4|^5.0|^6.0", "symfony/dependency-injection": "^4.4|^5.0|^6.0", "symfony/error-handler": "^4.4|^5.0|^6.0", "symfony/filesystem": "^4.4|^5.0|^6.0", "symfony/form": "^4.4|^5.0|^6.0", "symfony/http-foundation": "^4.4|^5.0|^6.0", "symfony/http-kernel": "^4.4|^5.0|^6.0", "symfony/mime": "^4.4|^5.0|^6.0", "symfony/property-access": "^5.4|^6.0", "symfony/property-info": "^5.4.24|^6.2.11", "symfony/uid": "^5.3|^6.0", "symfony/validator": "^4.4|^5.0|^6.0", "symfony/var-dumper": "^4.4|^5.0|^6.0", "symfony/var-exporter": "^4.4|^5.0|^6.0", "symfony/yaml": "^4.4|^5.0|^6.0"}, "suggest": {"psr/cache-implementation": "For using the metadata cache.", "symfony/config": "For using the XML mapping loader.", "symfony/mime": "For using a MIME type guesser within the DataUriNormalizer.", "symfony/property-access": "For using the ObjectNormalizer.", "symfony/property-info": "To deserialize relations.", "symfony/var-exporter": "For using the metadata compiler.", "symfony/yaml": "For using the default YAML mapping loader."}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Serializer\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Handles serializing and deserializing data structures, including object graphs, into array structures or other formats like XML and JSON.", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/serializer/tree/v5.4.31"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-10-31T07:58:33+00:00"}, {"name": "symfony/translation", "version": "v4.3.11", "source": {"type": "git", "url": "https://github.com/symfony/translation.git", "reference": "46e462be71935ae15eab531e4d491d801857f24c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation/zipball/46e462be71935ae15eab531e4d491d801857f24c", "reference": "46e462be71935ae15eab531e4d491d801857f24c", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/polyfill-mbstring": "~1.0", "symfony/translation-contracts": "^1.1.6"}, "conflict": {"symfony/config": "<3.4", "symfony/dependency-injection": "<3.4", "symfony/yaml": "<3.4"}, "provide": {"symfony/translation-implementation": "1.0"}, "require-dev": {"psr/log": "~1.0", "symfony/config": "~3.4|~4.0", "symfony/console": "~3.4|~4.0", "symfony/dependency-injection": "~3.4|~4.0", "symfony/finder": "~2.8|~3.0|~4.0", "symfony/http-kernel": "~3.4|~4.0", "symfony/intl": "~3.4|~4.0", "symfony/service-contracts": "^1.1.2", "symfony/var-dumper": "~3.4|~4.0", "symfony/yaml": "~3.4|~4.0"}, "suggest": {"psr/log-implementation": "To use logging capability in translator", "symfony/config": "", "symfony/yaml": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.3-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Translation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Translation Component", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/translation/tree/4.3"}, "time": "2020-01-04T12:24:57+00:00"}, {"name": "symfony/translation-contracts", "version": "v1.10.0", "source": {"type": "git", "url": "https://github.com/symfony/translation-contracts.git", "reference": "7462e5c4cb8b9cd152f992e8f10963b5641921f6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation-contracts/zipball/7462e5c4cb8b9cd152f992e8f10963b5641921f6", "reference": "7462e5c4cb8b9cd152f992e8f10963b5641921f6", "shasum": ""}, "require": {"php": ">=7.1.3"}, "suggest": {"symfony/translation-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.1-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Translation\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to translation", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/translation-contracts/tree/v1.10.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-06-27T13:16:42+00:00"}, {"name": "symfony/yaml", "version": "v4.4.45", "source": {"type": "git", "url": "https://github.com/symfony/yaml.git", "reference": "aeccc4dc52a9e634f1d1eebeb21eacfdcff1053d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/yaml/zipball/aeccc4dc52a9e634f1d1eebeb21eacfdcff1053d", "reference": "aeccc4dc52a9e634f1d1eebeb21eacfdcff1053d", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/polyfill-ctype": "~1.8"}, "conflict": {"symfony/console": "<3.4"}, "require-dev": {"symfony/console": "^3.4|^4.0|^5.0"}, "suggest": {"symfony/console": "For validating YAML files using the lint command"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Yaml\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Loads and dumps YAML files", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/yaml/tree/v4.4.45"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-08-02T15:47:23+00:00"}, {"name": "tgalopin/html-sanitizer", "version": "1.5.0", "source": {"type": "git", "url": "https://github.com/tgalopin/html-sanitizer.git", "reference": "5d02dcb6f2ea4f505731eac440798caa1b3b0913"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/tgalopin/html-sanitizer/zipball/5d02dcb6f2ea4f505731eac440798caa1b3b0913", "reference": "5d02dcb6f2ea4f505731eac440798caa1b3b0913", "shasum": ""}, "require": {"ext-dom": "*", "league/uri-parser": "^1.4.1", "masterminds/html5": "^2.4", "php": ">=7.1", "psr/log": "^1.0|^2.0|^3.0"}, "require-dev": {"phpunit/phpunit": "^7.4", "symfony/var-dumper": "^4.1"}, "type": "library", "autoload": {"psr-4": {"HtmlSanitizer\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Sanitize untrustworthy HTML user input", "support": {"issues": "https://github.com/tgalopin/html-sanitizer/issues", "source": "https://github.com/tgalopin/html-sanitizer/tree/1.5.0"}, "abandoned": "symfony/html-sanitizer", "time": "2021-09-14T08:27:50+00:00"}, {"name": "webmozart/expression", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/webmozart/expression.git", "reference": "35c222fc49533894228e1240cc844ae91d6a9ef2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webmozart/expression/zipball/35c222fc49533894228e1240cc844ae91d6a9ef2", "reference": "35c222fc49533894228e1240cc844ae91d6a9ef2", "shasum": ""}, "require": {"php": ">=5.3.9"}, "require-dev": {"phpunit/phpunit": "^4.6", "sebastian/version": "^1.0.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"psr-4": {"Webmozart\\Expression\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Formulate expressions and search criteria using PHP objects.", "keywords": ["criteria", "expression", "filter", "formula"], "support": {"issues": "https://github.com/webmozart/expression/issues", "source": "https://github.com/webmozart/expression/tree/1.0.0"}, "time": "2015-12-17T10:43:13+00:00"}, {"name": "wrep/notificato", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/mac-cain13/notificato.git", "reference": "7232b69a097c94346f61aaf3aa7db13924b108e3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mac-cain13/notificato/zipball/7232b69a097c94346f61aaf3aa7db13924b108e3", "reference": "7232b69a097c94346f61aaf3aa7db13924b108e3", "shasum": ""}, "require": {"ext-openssl": "*", "ext-sockets": "*", "ext-spl": "*", "lib-openssl": "*", "php": ">=5.4", "psr/log": "1.0.*"}, "replace": {"wrep/notificare": "*"}, "require-dev": {"phpunit/phpunit": "4.4.*", "sami/sami": "dev-master"}, "suggest": {"wrep/notificato-symfony": "Integrate Notificato into Symfony"}, "type": "library", "autoload": {"psr-0": {"Wrep\\Notificato\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Takes care of push notifications in your PHP projects.", "homepage": "https://github.com/mac-cain13/notificato", "keywords": ["apns", "aps", "ios", "notifications", "push", "push notifications", "pushnotifications"], "support": {"issues": "https://github.com/mac-cain13/notificato/issues", "source": "https://github.com/mac-cain13/notificato/tree/1.2.1"}, "time": "2019-05-27T08:43:30+00:00"}, {"name": "zendesk/zendesk_api_client_php", "version": "v2.2.11", "source": {"type": "git", "url": "https://github.com/zendesk/zendesk_api_client_php.git", "reference": "b451b743d9d6d81a9abf7cb86e70ec9c5332123e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/zendesk/zendesk_api_client_php/zipball/b451b743d9d6d81a9abf7cb86e70ec9c5332123e", "reference": "b451b743d9d6d81a9abf7cb86e70ec9c5332123e", "shasum": ""}, "require": {"guzzlehttp/guzzle": "^6.0 || ^7.0", "mmucklo/inflect": "0.3.*", "php": ">=5.5.0"}, "require-dev": {"fzaninotto/faker": ">=1.5.0", "phpmd/phpmd": "@stable", "phpunit/phpunit": "4.5.*", "psy/psysh": "@stable", "squizlabs/php_codesniffer": "2.*"}, "type": "library", "autoload": {"psr-0": {"Zendesk\\API\\": "src/", "Zendesk\\Console\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "PHP Client for Zendesk REST API. See https://developer.zendesk.com/rest_api/docs/core/introduction .", "homepage": "https://github.com/zendesk/zendesk_api_client_php", "support": {"issues": "https://github.com/zendesk/zendesk_api_client_php/issues", "source": "https://github.com/zendesk/zendesk_api_client_php/tree/v2.2.11"}, "time": "2021-04-28T21:55:42+00:00"}, {"name": "zircote/swagger-php", "version": "2.1.2", "source": {"type": "git", "url": "https://github.com/zircote/swagger-php.git", "reference": "f144351118e6bcc04a275f490d7e359a3dd62586"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/zircote/swagger-php/zipball/f144351118e6bcc04a275f490d7e359a3dd62586", "reference": "f144351118e6bcc04a275f490d7e359a3dd62586", "shasum": ""}, "require": {"doctrine/annotations": "^1.7", "php": ">=7.2", "symfony/finder": ">=3.4"}, "require-dev": {"phpunit/phpunit": "^8 || ^9", "squizlabs/php_codesniffer": ">=2.7"}, "bin": ["bin/swagger"], "type": "library", "autoload": {"files": ["src/functions.php"], "psr-4": {"Swagger\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.zircote.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://bfanger.nl"}], "description": "Swagger-PHP - Generate interactive documentation for your RESTful API using phpdoc annotations", "homepage": "https://github.com/zircote/swagger-php/", "keywords": ["api", "json", "rest", "service discovery"], "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/2.1.2"}, "time": "2022-06-18T00:00:23+00:00"}], "packages-dev": [{"name": "brainmaestro/composer-git-hooks", "version": "v2.8.5", "source": {"type": "git", "url": "https://github.com/BrainMaestro/composer-git-hooks.git", "reference": "ffed8803690ac12214082120eee3441b00aa390e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/BrainMaestro/composer-git-hooks/zipball/ffed8803690ac12214082120eee3441b00aa390e", "reference": "ffed8803690ac12214082120eee3441b00aa390e", "shasum": ""}, "require": {"php": "^5.6 || >=7.0", "symfony/console": "^3.2 || ^4.0 || ^5.0"}, "require-dev": {"ext-json": "*", "friendsofphp/php-cs-fixer": "^2.9", "phpunit/phpunit": "^5.7 || ^7.0"}, "bin": ["cghooks"], "type": "library", "extra": {"hooks": {"pre-commit": "composer check-style", "pre-push": ["composer test", "appver=$(grep -o -E '\\d.\\d.\\d' cghooks)", "tag=$(git describe --tags --abbrev=0)", "if [ \"$tag\" != \"v$appver\" ]; then", "echo \"The most recent tag $tag does not match the application version $appver\\n\"", "tag=${tag#v}", "sed -i -E \"s/$appver/$tag/\" cghooks", "exit 1", "fi"]}}, "autoload": {"files": ["src/helpers.php"], "psr-4": {"BrainMaestro\\GitHooks\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Easily manage git hooks in your composer config", "keywords": ["HOOK", "composer", "git"], "support": {"issues": "https://github.com/BrainMaestro/composer-git-hooks/issues", "source": "https://github.com/BrainMaestro/composer-git-hooks/tree/v2.8.5"}, "time": "2021-02-08T15:59:11+00:00"}, {"name": "brunty/cigar", "version": "1.12.3", "source": {"type": "git", "url": "https://github.com/Brunty/cigar.git", "reference": "dba36d74ef8303d4e1cf7deca8f2654a07e90083"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Brunty/cigar/zipball/dba36d74ef8303d4e1cf7deca8f2654a07e90083", "reference": "dba36d74ef8303d4e1cf7deca8f2654a07e90083", "shasum": ""}, "require": {"ext-curl": "*", "ext-json": "*", "php": ">=7.0"}, "require-dev": {"kahlan/kahlan": "^4.0", "mikey179/vfsstream": "^1.6", "satooshi/php-coveralls": "^1.0", "symfony/process": "^3.3"}, "bin": ["bin/cigar"], "type": "library", "autoload": {"psr-4": {"Brunty\\Cigar\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Smoke testing tool written in PHP.", "support": {"issues": "https://github.com/Brunty/cigar/issues", "source": "https://github.com/Brunty/cigar/tree/1.12.3"}, "time": "2019-06-18T11:09:07+00:00"}, {"name": "codacy/coverage", "version": "dev-master", "source": {"type": "git", "url": "https://github.com/codacy/php-codacy-coverage.git", "reference": "656913b35e22ae0d1ec352bc00e3ad90616efb7a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/codacy/php-codacy-coverage/zipball/656913b35e22ae0d1ec352bc00e3ad90616efb7a", "reference": "656913b35e22ae0d1ec352bc00e3ad90616efb7a", "shasum": ""}, "require": {"gitonomy/gitlib": ">=1.0", "php": ">=5.3.3", "symfony/console": "~2.5|~3.0|~4.0|~5.0"}, "require-dev": {"clue/phar-composer": "^1.1", "phpunit/phpunit": "~6.5"}, "bin": ["bin/codacycoverage"], "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Sends PHP test coverage information to Codacy.", "homepage": "https://github.com/codacy/php-codacy-coverage", "support": {"issues": "https://github.com/codacy/php-codacy-coverage/issues", "source": "https://github.com/codacy/php-codacy-coverage/tree/master"}, "abandoned": true, "time": "2020-02-11T15:55:24+00:00"}, {"name": "composer/ca-bundle", "version": "1.3.7", "source": {"type": "git", "url": "https://github.com/composer/ca-bundle.git", "reference": "76e46335014860eec1aa5a724799a00a2e47cc85"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/ca-bundle/zipball/76e46335014860eec1aa5a724799a00a2e47cc85", "reference": "76e46335014860eec1aa5a724799a00a2e47cc85", "shasum": ""}, "require": {"ext-openssl": "*", "ext-pcre": "*", "php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^0.12.55", "psr/log": "^1.0", "symfony/phpunit-bridge": "^4.2 || ^5", "symfony/process": "^2.5 || ^3.0 || ^4.0 || ^5.0 || ^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.x-dev"}}, "autoload": {"psr-4": {"Composer\\CaBundle\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Lets you find a path to the system CA bundle, and includes a fallback to the Mozilla CA bundle.", "keywords": ["cabundle", "cacert", "certificate", "ssl", "tls"], "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/ca-bundle/issues", "source": "https://github.com/composer/ca-bundle/tree/1.3.7"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2023-08-30T09:31:38+00:00"}, {"name": "composer/composer", "version": "2.2.22", "source": {"type": "git", "url": "https://github.com/composer/composer.git", "reference": "fedc76ee3f3e3d57d20993b9f4c5fcfb2f8596aa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/composer/zipball/fedc76ee3f3e3d57d20993b9f4c5fcfb2f8596aa", "reference": "fedc76ee3f3e3d57d20993b9f4c5fcfb2f8596aa", "shasum": ""}, "require": {"composer/ca-bundle": "^1.0", "composer/metadata-minifier": "^1.0", "composer/pcre": "^1.0", "composer/semver": "^3.0", "composer/spdx-licenses": "^1.2", "composer/xdebug-handler": "^2.0 || ^3.0", "justinrainbow/json-schema": "^5.2.11", "php": "^5.3.2 || ^7.0 || ^8.0", "psr/log": "^1.0 || ^2.0", "react/promise": "^1.2 || ^2.7", "seld/jsonlint": "^1.4", "seld/phar-utils": "^1.0", "symfony/console": "^2.8.52 || ^3.4.35 || ^4.4 || ^5.0", "symfony/filesystem": "^2.8.52 || ^3.4.35 || ^4.4 || ^5.0 || ^6.0", "symfony/finder": "^2.8.52 || ^3.4.35 || ^4.4 || ^5.0 || ^6.0", "symfony/process": "^2.8.52 || ^3.4.35 || ^4.4 || ^5.0 || ^6.0"}, "require-dev": {"phpspec/prophecy": "^1.10", "symfony/phpunit-bridge": "^4.2 || ^5.0 || ^6.0"}, "suggest": {"ext-openssl": "Enabling the openssl extension allows you to access https URLs for repositories and packages", "ext-zip": "Enabling the zip extension allows you to unzip archives", "ext-zlib": "Allow gzip compression of HTTP requests"}, "bin": ["bin/composer"], "type": "library", "extra": {"branch-alias": {"dev-main": "2.2-dev"}}, "autoload": {"psr-4": {"Composer\\": "src/Composer"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://www.naderman.de"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "https://seld.be"}], "description": "Composer helps you declare, manage and install dependencies of PHP projects. It ensures you have the right stack everywhere.", "homepage": "https://getcomposer.org/", "keywords": ["autoload", "dependency", "package"], "support": {"irc": "ircs://irc.libera.chat:6697/composer", "issues": "https://github.com/composer/composer/issues", "source": "https://github.com/composer/composer/tree/2.2.22"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2023-09-29T08:53:46+00:00"}, {"name": "composer/metadata-minifier", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/composer/metadata-minifier.git", "reference": "c549d23829536f0d0e984aaabbf02af91f443207"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/metadata-minifier/zipball/c549d23829536f0d0e984aaabbf02af91f443207", "reference": "c549d23829536f0d0e984aaabbf02af91f443207", "shasum": ""}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"composer/composer": "^2", "phpstan/phpstan": "^0.12.55", "symfony/phpunit-bridge": "^4.2 || ^5"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.x-dev"}}, "autoload": {"psr-4": {"Composer\\MetadataMinifier\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Small utility library that handles metadata minification and expansion.", "keywords": ["composer", "compression"], "support": {"issues": "https://github.com/composer/metadata-minifier/issues", "source": "https://github.com/composer/metadata-minifier/tree/1.0.0"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2021-04-07T13:37:33+00:00"}, {"name": "composer/pcre", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/composer/pcre.git", "reference": "67a32d7d6f9f560b726ab25a061b38ff3a80c560"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/pcre/zipball/67a32d7d6f9f560b726ab25a061b38ff3a80c560", "reference": "67a32d7d6f9f560b726ab25a061b38ff3a80c560", "shasum": ""}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^1.3", "phpstan/phpstan-strict-rules": "^1.1", "symfony/phpunit-bridge": "^4.2 || ^5"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.x-dev"}}, "autoload": {"psr-4": {"Composer\\Pcre\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "PCRE wrapping library that offers type-safe preg_* replacements.", "keywords": ["PCRE", "preg", "regex", "regular expression"], "support": {"issues": "https://github.com/composer/pcre/issues", "source": "https://github.com/composer/pcre/tree/1.0.1"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2022-01-21T20:24:37+00:00"}, {"name": "composer/semver", "version": "3.4.0", "source": {"type": "git", "url": "https://github.com/composer/semver.git", "reference": "35e8d0af4486141bc745f23a29cc2091eb624a32"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/semver/zipball/35e8d0af4486141bc745f23a29cc2091eb624a32", "reference": "35e8d0af4486141bc745f23a29cc2091eb624a32", "shasum": ""}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^1.4", "symfony/phpunit-bridge": "^4.2 || ^5"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.x-dev"}}, "autoload": {"psr-4": {"Composer\\Semver\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.naderman.de"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://robbast.nl"}], "description": "Semver library that offers utilities, version constraint parsing and validation.", "keywords": ["semantic", "semver", "validation", "versioning"], "support": {"irc": "ircs://irc.libera.chat:6697/composer", "issues": "https://github.com/composer/semver/issues", "source": "https://github.com/composer/semver/tree/3.4.0"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2023-08-31T09:50:34+00:00"}, {"name": "composer/spdx-licenses", "version": "1.5.8", "source": {"type": "git", "url": "https://github.com/composer/spdx-licenses.git", "reference": "560bdcf8deb88ae5d611c80a2de8ea9d0358cc0a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/spdx-licenses/zipball/560bdcf8deb88ae5d611c80a2de8ea9d0358cc0a", "reference": "560bdcf8deb88ae5d611c80a2de8ea9d0358cc0a", "shasum": ""}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^0.12.55", "symfony/phpunit-bridge": "^4.2 || ^5"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.x-dev"}}, "autoload": {"psr-4": {"Composer\\Spdx\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.naderman.de"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://robbast.nl"}], "description": "SPDX licenses list and validation library.", "keywords": ["license", "spdx", "validator"], "support": {"irc": "ircs://irc.libera.chat:6697/composer", "issues": "https://github.com/composer/spdx-licenses/issues", "source": "https://github.com/composer/spdx-licenses/tree/1.5.8"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2023-11-20T07:44:33+00:00"}, {"name": "composer/xdebug-handler", "version": "2.0.5", "source": {"type": "git", "url": "https://github.com/composer/xdebug-handler.git", "reference": "9e36aeed4616366d2b690bdce11f71e9178c579a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/xdebug-handler/zipball/9e36aeed4616366d2b690bdce11f71e9178c579a", "reference": "9e36aeed4616366d2b690bdce11f71e9178c579a", "shasum": ""}, "require": {"composer/pcre": "^1", "php": "^5.3.2 || ^7.0 || ^8.0", "psr/log": "^1 || ^2 || ^3"}, "require-dev": {"phpstan/phpstan": "^1.0", "phpstan/phpstan-strict-rules": "^1.1", "symfony/phpunit-bridge": "^4.2 || ^5.0 || ^6.0"}, "type": "library", "autoload": {"psr-4": {"Composer\\XdebugHandler\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "john-s<PERSON><PERSON><PERSON>@blueyonder.co.uk"}], "description": "Restarts a process without Xdebug.", "keywords": ["Xdebug", "performance"], "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/xdebug-handler/issues", "source": "https://github.com/composer/xdebug-handler/tree/2.0.5"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2022-02-24T20:20:32+00:00"}, {"name": "doctrine/instantiator", "version": "1.5.0", "source": {"type": "git", "url": "https://github.com/doctrine/instantiator.git", "reference": "0a0fa9780f5d4e507415a065172d26a98d02047b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/instantiator/zipball/0a0fa9780f5d4e507415a065172d26a98d02047b", "reference": "0a0fa9780f5d4e507415a065172d26a98d02047b", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9 || ^11", "ext-pdo": "*", "ext-phar": "*", "phpbench/phpbench": "^0.16 || ^1", "phpstan/phpstan": "^1.4", "phpstan/phpstan-phpunit": "^1", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "vimeo/psalm": "^4.30 || ^5.4"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Instantiator\\": "src/Doctrine/Instantiator/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://ocramius.github.io/"}], "description": "A small, lightweight utility to instantiate objects in PHP without invoking their constructors", "homepage": "https://www.doctrine-project.org/projects/instantiator.html", "keywords": ["constructor", "instantiate"], "support": {"issues": "https://github.com/doctrine/instantiator/issues", "source": "https://github.com/doctrine/instantiator/tree/1.5.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Finstantiator", "type": "tidelift"}], "time": "2022-12-30T00:15:36+00:00"}, {"name": "filp/whoops", "version": "2.15.1", "source": {"type": "git", "url": "https://github.com/filp/whoops.git", "reference": "e864ac957acd66e1565f25efda61e37791a5db0b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/filp/whoops/zipball/e864ac957acd66e1565f25efda61e37791a5db0b", "reference": "e864ac957acd66e1565f25efda61e37791a5db0b", "shasum": ""}, "require": {"php": "^5.5.9 || ^7.0 || ^8.0", "psr/log": "^1.0.1 || ^2.0 || ^3.0"}, "require-dev": {"mockery/mockery": "^0.9 || ^1.0", "phpunit/phpunit": "^4.8.36 || ^5.7.27 || ^6.5.14 || ^7.5.20 || ^8.5.8 || ^9.3.3", "symfony/var-dumper": "^2.6 || ^3.0 || ^4.0 || ^5.0"}, "suggest": {"symfony/var-dumper": "Pretty print complex values better with var-dumper available", "whoops/soap": "Formats errors as SOAP responses"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.7-dev"}}, "autoload": {"psr-4": {"Whoops\\": "src/Whoops/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://github.com/filp", "role": "Developer"}], "description": "php error handling for cool kids", "homepage": "https://filp.github.io/whoops/", "keywords": ["error", "exception", "handling", "library", "throwable", "whoops"], "support": {"issues": "https://github.com/filp/whoops/issues", "source": "https://github.com/filp/whoops/tree/2.15.1"}, "funding": [{"url": "https://github.com/denis-so<PERSON><PERSON>", "type": "github"}], "time": "2023-03-06T18:09:13+00:00"}, {"name": "friendsofphp/php-cs-fixer", "version": "v2.19.3", "source": {"type": "git", "url": "https://github.com/FriendsOfPHP/PHP-CS-Fixer.git", "reference": "75ac86f33fab4714ea5a39a396784d83ae3b5ed8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/FriendsOfPHP/PHP-CS-Fixer/zipball/75ac86f33fab4714ea5a39a396784d83ae3b5ed8", "reference": "75ac86f33fab4714ea5a39a396784d83ae3b5ed8", "shasum": ""}, "require": {"composer/semver": "^1.4 || ^2.0 || ^3.0", "composer/xdebug-handler": "^1.2 || ^2.0", "doctrine/annotations": "^1.2", "ext-json": "*", "ext-tokenizer": "*", "php": "^5.6 || ^7.0 || ^8.0", "php-cs-fixer/diff": "^1.3", "symfony/console": "^3.4.43 || ^4.1.6 || ^5.0", "symfony/event-dispatcher": "^3.0 || ^4.0 || ^5.0", "symfony/filesystem": "^3.0 || ^4.0 || ^5.0", "symfony/finder": "^3.0 || ^4.0 || ^5.0", "symfony/options-resolver": "^3.0 || ^4.0 || ^5.0", "symfony/polyfill-php70": "^1.0", "symfony/polyfill-php72": "^1.4", "symfony/process": "^3.0 || ^4.0 || ^5.0", "symfony/stopwatch": "^3.0 || ^4.0 || ^5.0"}, "require-dev": {"justinrainbow/json-schema": "^5.0", "keradus/cli-executor": "^1.4", "mikey179/vfsstream": "^1.6", "php-coveralls/php-coveralls": "^2.4.2", "php-cs-fixer/accessible-object": "^1.0", "php-cs-fixer/phpunit-constraint-isidenticalstring": "^1.2", "php-cs-fixer/phpunit-constraint-xmlmatchesxsd": "^1.2.1", "phpspec/prophecy-phpunit": "^1.1 || ^2.0", "phpunit/phpunit": "^5.7.27 || ^6.5.14 || ^7.5.20 || ^8.5.13 || ^9.5", "phpunitgoodpractices/polyfill": "^1.5", "phpunitgoodpractices/traits": "^1.9.1", "sanmai/phpunit-legacy-adapter": "^6.4 || ^8.2.1", "symfony/phpunit-bridge": "^5.2.1", "symfony/yaml": "^3.0 || ^4.0 || ^5.0"}, "suggest": {"ext-dom": "For handling output formats in XML", "ext-mbstring": "For handling non-UTF8 characters.", "php-cs-fixer/phpunit-constraint-isidenticalstring": "For IsIdenticalString constraint.", "php-cs-fixer/phpunit-constraint-xmlmatchesxsd": "For XmlMatchesXsd constraint.", "symfony/polyfill-mbstring": "When enabling `ext-mbstring` is not possible."}, "bin": ["php-cs-fixer"], "type": "application", "extra": {"branch-alias": {"dev-master": "2.19-dev"}}, "autoload": {"psr-4": {"PhpCsFixer\\": "src/"}, "classmap": ["tests/Test/AbstractFixerTestCase.php", "tests/Test/AbstractIntegrationCaseFactory.php", "tests/Test/AbstractIntegrationTestCase.php", "tests/Test/Assert/AssertTokensTrait.php", "tests/Test/IntegrationCase.php", "tests/Test/IntegrationCaseFactory.php", "tests/Test/IntegrationCaseFactoryInterface.php", "tests/Test/InternalIntegrationCaseFactory.php", "tests/Test/IsIdenticalConstraint.php", "tests/Test/TokensWithObservedTransformers.php", "tests/TestCase.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A tool to automatically fix PHP code style", "support": {"issues": "https://github.com/FriendsOfPHP/PHP-CS-Fixer/issues", "source": "https://github.com/FriendsOfPHP/PHP-CS-Fixer/tree/v2.19.3"}, "funding": [{"url": "https://github.com/keradus", "type": "github"}], "time": "2021-11-15T17:17:55+00:00"}, {"name": "gitonomy/gitlib", "version": "v1.3.7", "source": {"type": "git", "url": "https://github.com/gitonomy/gitlib.git", "reference": "00b57b79f02396aa4c7c163f76fe2bc48faebbb7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/gitonomy/gitlib/zipball/00b57b79f02396aa4c7c163f76fe2bc48faebbb7", "reference": "00b57b79f02396aa4c7c163f76fe2bc48faebbb7", "shasum": ""}, "require": {"ext-pcre": "*", "php": "^5.6 || ^7.0 || ^8.0", "symfony/polyfill-mbstring": "^1.7", "symfony/process": "^3.4 || ^4.4 || ^5.0 || ^6.0"}, "require-dev": {"ext-fileinfo": "*", "phpspec/prophecy": "^1.10.2", "phpunit/phpunit": "^5.7.27 || ^6.5.14 || ^7.5.20 || ^8.5.20 || ^9.5.9", "psr/log": "^1.0"}, "suggest": {"ext-fileinfo": "Required to determine the mimetype of a blob", "psr/log": "Required to use loggers for reporting of execution"}, "type": "library", "autoload": {"psr-4": {"Gitonomy\\Git\\": "src/Gitonomy/Git/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/juliendidier"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/lyrixx"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/alexandresalome"}], "description": "Library for accessing git", "support": {"issues": "https://github.com/gitonomy/gitlib/issues", "source": "https://github.com/gitonomy/gitlib/tree/v1.3.7"}, "funding": [{"url": "https://tidelift.com/funding/github/packagist/gitonomy/gitlib", "type": "tidelift"}], "time": "2022-10-04T14:20:15+00:00"}, {"name": "hamcrest/hamcrest-php", "version": "v2.0.1", "source": {"type": "git", "url": "https://github.com/hamcrest/hamcrest-php.git", "reference": "8c3d0a3f6af734494ad8f6fbbee0ba92422859f3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hamcrest/hamcrest-php/zipball/8c3d0a3f6af734494ad8f6fbbee0ba92422859f3", "reference": "8c3d0a3f6af734494ad8f6fbbee0ba92422859f3", "shasum": ""}, "require": {"php": "^5.3|^7.0|^8.0"}, "replace": {"cordoval/hamcrest-php": "*", "davedevelopment/hamcrest-php": "*", "kodova/hamcrest-php": "*"}, "require-dev": {"phpunit/php-file-iterator": "^1.4 || ^2.0", "phpunit/phpunit": "^4.8.36 || ^5.7 || ^6.5 || ^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.1-dev"}}, "autoload": {"classmap": ["hamcrest"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "This is the PHP port of Hamcrest Matchers", "keywords": ["test"], "support": {"issues": "https://github.com/hamcrest/hamcrest-php/issues", "source": "https://github.com/hamcrest/hamcrest-php/tree/v2.0.1"}, "time": "2020-07-09T08:09:16+00:00"}, {"name": "justin<PERSON><PERSON>/json-schema", "version": "5.2.12", "source": {"type": "git", "url": "https://github.com/justinrainbow/json-schema.git", "reference": "ad87d5a5ca981228e0e205c2bc7dfb8e24559b60"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/justinrainbow/json-schema/zipball/ad87d5a5ca981228e0e205c2bc7dfb8e24559b60", "reference": "ad87d5a5ca981228e0e205c2bc7dfb8e24559b60", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"friendsofphp/php-cs-fixer": "~2.2.20||~2.15.1", "json-schema/json-schema-test-suite": "1.2.0", "phpunit/phpunit": "^4.8.35"}, "bin": ["bin/validate-json"], "type": "library", "extra": {"branch-alias": {"dev-master": "5.0.x-dev"}}, "autoload": {"psr-4": {"JsonSchema\\": "src/JsonSchema/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "i<PERSON>@wiedler.ch"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A library to validate a json schema.", "homepage": "https://github.com/justinrainbow/json-schema", "keywords": ["json", "schema"], "support": {"issues": "https://github.com/justinrainbow/json-schema/issues", "source": "https://github.com/justinrainbow/json-schema/tree/5.2.12"}, "time": "2022-04-13T08:02:27+00:00"}, {"name": "mockery/mockery", "version": "1.3.6", "source": {"type": "git", "url": "https://github.com/mockery/mockery.git", "reference": "dc206df4fa314a50bbb81cf72239a305c5bbd5c0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mockery/mockery/zipball/dc206df4fa314a50bbb81cf72239a305c5bbd5c0", "reference": "dc206df4fa314a50bbb81cf72239a305c5bbd5c0", "shasum": ""}, "require": {"hamcrest/hamcrest-php": "^2.0.1", "lib-pcre": ">=7.0", "php": ">=5.6.0"}, "require-dev": {"phpunit/phpunit": "^5.7.10|^6.5|^7.5|^8.5|^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "autoload": {"psr-0": {"Mockery": "library/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://blog.astrumfutura.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://davedevelopment.co.uk"}], "description": "Mockery is a simple yet flexible PHP mock object framework", "homepage": "https://github.com/mockery/mockery", "keywords": ["BDD", "TDD", "library", "mock", "mock objects", "mockery", "stub", "test", "test double", "testing"], "support": {"issues": "https://github.com/mockery/mockery/issues", "source": "https://github.com/mockery/mockery/tree/1.3.6"}, "time": "2022-09-07T15:05:49+00:00"}, {"name": "myclabs/deep-copy", "version": "1.11.1", "source": {"type": "git", "url": "https://github.com/myclabs/DeepCopy.git", "reference": "7284c22080590fb39f2ffa3e9057f10a4ddd0e0c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/7284c22080590fb39f2ffa3e9057f10a4ddd0e0c", "reference": "7284c22080590fb39f2ffa3e9057f10a4ddd0e0c", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "conflict": {"doctrine/collections": "<1.6.8", "doctrine/common": "<2.13.3 || >=3,<3.2.2"}, "require-dev": {"doctrine/collections": "^1.6.8", "doctrine/common": "^2.13.3 || ^3.2.2", "phpunit/phpunit": "^7.5.20 || ^8.5.23 || ^9.5.13"}, "type": "library", "autoload": {"files": ["src/DeepCopy/deep_copy.php"], "psr-4": {"DeepCopy\\": "src/DeepCopy/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Create deep copies (clones) of your objects", "keywords": ["clone", "copy", "duplicate", "object", "object graph"], "support": {"issues": "https://github.com/myclabs/DeepCopy/issues", "source": "https://github.com/myclabs/DeepCopy/tree/1.11.1"}, "funding": [{"url": "https://tidelift.com/funding/github/packagist/myclabs/deep-copy", "type": "tidelift"}], "time": "2023-03-08T13:26:56+00:00"}, {"name": "php-cs-fixer/diff", "version": "v1.3.1", "source": {"type": "git", "url": "https://github.com/PHP-CS-Fixer/diff.git", "reference": "dbd31aeb251639ac0b9e7e29405c1441907f5759"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHP-CS-Fixer/diff/zipball/dbd31aeb251639ac0b9e7e29405c1441907f5759", "reference": "dbd31aeb251639ac0b9e7e29405c1441907f5759", "shasum": ""}, "require": {"php": "^5.6 || ^7.0 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^5.7.23 || ^6.4.3 || ^7.0", "symfony/process": "^3.3"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "SpacePossum"}], "description": "sebastian/diff v2 backport support for PHP5.6", "homepage": "https://github.com/PHP-CS-Fixer", "keywords": ["diff"], "support": {"issues": "https://github.com/PHP-CS-Fixer/diff/issues", "source": "https://github.com/PHP-CS-Fixer/diff/tree/v1.3.1"}, "abandoned": true, "time": "2020-10-14T08:39:05+00:00"}, {"name": "phpcompatibility/php-compatibility", "version": "9.3.5", "source": {"type": "git", "url": "https://github.com/PHPCompatibility/PHPCompatibility.git", "reference": "9fb324479acf6f39452e0655d2429cc0d3914243"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPCompatibility/PHPCompatibility/zipball/9fb324479acf6f39452e0655d2429cc0d3914243", "reference": "9fb324479acf6f39452e0655d2429cc0d3914243", "shasum": ""}, "require": {"php": ">=5.3", "squizlabs/php_codesniffer": "^2.3 || ^3.0.2"}, "conflict": {"squizlabs/php_codesniffer": "2.6.2"}, "require-dev": {"phpunit/phpunit": "~4.5 || ^5.0 || ^6.0 || ^7.0"}, "suggest": {"dealerdirect/phpcodesniffer-composer-installer": "^0.5 || This Composer plugin will sort out the PHPCS 'installed_paths' automatically.", "roave/security-advisories": "dev-master || Helps prevent installing dependencies with known security issues."}, "type": "phpcodesniffer-standard", "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON><PERSON>", "homepage": "https://github.com/wimg", "role": "lead"}, {"name": "<PERSON>", "homepage": "https://github.com/jrfnl", "role": "lead"}, {"name": "Contributors", "homepage": "https://github.com/PHPCompatibility/PHPCompatibility/graphs/contributors"}], "description": "A set of sniffs for PHP_CodeSniffer that checks for PHP cross-version compatibility.", "homepage": "http://techblog.wimgodden.be/tag/codesniffer/", "keywords": ["compatibility", "phpcs", "standards"], "support": {"issues": "https://github.com/PHPCompatibility/PHPCompatibility/issues", "source": "https://github.com/PHPCompatibility/PHPCompatibility"}, "time": "2019-12-27T09:44:58+00:00"}, {"name": "phpdocumentor/reflection-common", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionCommon.git", "reference": "1d01c49d4ed62f25aa84a747ad35d5a16924662b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionCommon/zipball/1d01c49d4ed62f25aa84a747ad35d5a16924662b", "reference": "1d01c49d4ed62f25aa84a747ad35d5a16924662b", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-2.x": "2.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Common reflection classes used by phpdocumentor to reflect the code structure", "homepage": "http://www.phpdoc.org", "keywords": ["FQSEN", "phpDocumentor", "phpdoc", "reflection", "static analysis"], "support": {"issues": "https://github.com/phpDocumentor/ReflectionCommon/issues", "source": "https://github.com/phpDocumentor/ReflectionCommon/tree/2.x"}, "time": "2020-06-27T09:03:43+00:00"}, {"name": "phpdocumentor/reflection-docblock", "version": "5.3.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "reference": "622548b623e81ca6d78b721c5e029f4ce664f170"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/622548b623e81ca6d78b721c5e029f4ce664f170", "reference": "622548b623e81ca6d78b721c5e029f4ce664f170", "shasum": ""}, "require": {"ext-filter": "*", "php": "^7.2 || ^8.0", "phpdocumentor/reflection-common": "^2.2", "phpdocumentor/type-resolver": "^1.3", "webmozart/assert": "^1.9.1"}, "require-dev": {"mockery/mockery": "~1.3.2", "psalm/phar": "^4.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "With this component, a library can provide support for annotations via DocBlocks or otherwise retrieve information that is embedded in a DocBlock.", "support": {"issues": "https://github.com/phpDocumentor/ReflectionDocBlock/issues", "source": "https://github.com/phpDocumentor/ReflectionDocBlock/tree/5.3.0"}, "time": "2021-10-19T17:43:47+00:00"}, {"name": "phpdocumentor/type-resolver", "version": "1.7.1", "source": {"type": "git", "url": "https://github.com/phpDocumentor/TypeResolver.git", "reference": "dfc078e8af9c99210337325ff5aa152872c98714"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/dfc078e8af9c99210337325ff5aa152872c98714", "reference": "dfc078e8af9c99210337325ff5aa152872c98714", "shasum": ""}, "require": {"doctrine/deprecations": "^1.0", "php": "^7.4 || ^8.0", "phpdocumentor/reflection-common": "^2.0", "phpstan/phpdoc-parser": "^1.13"}, "require-dev": {"ext-tokenizer": "*", "phpbench/phpbench": "^1.2", "phpstan/extension-installer": "^1.1", "phpstan/phpstan": "^1.8", "phpstan/phpstan-phpunit": "^1.1", "phpunit/phpunit": "^9.5", "rector/rector": "^0.13.9", "vimeo/psalm": "^4.25"}, "type": "library", "extra": {"branch-alias": {"dev-1.x": "1.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A PSR-5 based resolver of Class names, Types and Structural Element Names", "support": {"issues": "https://github.com/phpDocumentor/TypeResolver/issues", "source": "https://github.com/phpDocumentor/TypeResolver/tree/1.7.1"}, "time": "2023-03-27T19:02:04+00:00"}, {"name": "phpspec/prophecy", "version": "v1.10.3", "source": {"type": "git", "url": "https://github.com/phpspec/prophecy.git", "reference": "451c3cd1418cf640de218914901e51b064abb093"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpspec/prophecy/zipball/451c3cd1418cf640de218914901e51b064abb093", "reference": "451c3cd1418cf640de218914901e51b064abb093", "shasum": ""}, "require": {"doctrine/instantiator": "^1.0.2", "php": "^5.3|^7.0", "phpdocumentor/reflection-docblock": "^2.0|^3.0.2|^4.0|^5.0", "sebastian/comparator": "^1.2.3|^2.0|^3.0|^4.0", "sebastian/recursion-context": "^1.0|^2.0|^3.0|^4.0"}, "require-dev": {"phpspec/phpspec": "^2.5 || ^3.2", "phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.5 || ^7.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.10.x-dev"}}, "autoload": {"psr-4": {"Prophecy\\": "src/Prophecy"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://everzet.com"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Highly opinionated mocking framework for PHP 5.3+", "homepage": "https://github.com/phpspec/prophecy", "keywords": ["Double", "Dummy", "fake", "mock", "spy", "stub"], "support": {"issues": "https://github.com/phpspec/prophecy/issues", "source": "https://github.com/phpspec/prophecy/tree/v1.10.3"}, "time": "2020-03-05T15:02:03+00:00"}, {"name": "phpstan/phpdoc-parser", "version": "1.18.0", "source": {"type": "git", "url": "https://github.com/phpstan/phpdoc-parser.git", "reference": "882eabc9b6a12e25c27091a261397f9c8792e722"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/882eabc9b6a12e25c27091a261397f9c8792e722", "reference": "882eabc9b6a12e25c27091a261397f9c8792e722", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"php-parallel-lint/php-parallel-lint": "^1.2", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^1.5", "phpstan/phpstan-phpunit": "^1.1", "phpstan/phpstan-strict-rules": "^1.0", "phpunit/phpunit": "^9.5", "symfony/process": "^5.2"}, "type": "library", "autoload": {"psr-4": {"PHPStan\\PhpDocParser\\": ["src/"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "PHPDoc parser with support for nullable, intersection and generic types", "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.18.0"}, "time": "2023-04-06T07:26:43+00:00"}, {"name": "phpstan/phpstan", "version": "1.10.32", "source": {"type": "git", "url": "https://github.com/phpstan/phpstan.git", "reference": "c47e47d3ab03137c0e121e77c4d2cb58672f6d44"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpstan/phpstan/zipball/c47e47d3ab03137c0e121e77c4d2cb58672f6d44", "reference": "c47e47d3ab03137c0e121e77c4d2cb58672f6d44", "shasum": ""}, "require": {"php": "^7.2|^8.0"}, "conflict": {"phpstan/phpstan-shim": "*"}, "bin": ["phpstan", "phpstan.phar"], "type": "library", "autoload": {"files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "PHPStan - PHP Static Analysis Tool", "keywords": ["dev", "static analysis"], "support": {"docs": "https://phpstan.org/user-guide/getting-started", "forum": "https://github.com/phpstan/phpstan/discussions", "issues": "https://github.com/phpstan/phpstan/issues", "security": "https://github.com/phpstan/phpstan/security/policy", "source": "https://github.com/phpstan/phpstan-src"}, "funding": [{"url": "https://github.com/ondrejmirtes", "type": "github"}, {"url": "https://github.com/phpstan", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/phpstan/phpstan", "type": "tidelift"}], "time": "2023-08-24T21:54:50+00:00"}, {"name": "phpunit/php-code-coverage", "version": "4.0.8", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "reference": "ef7b2f56815df854e66ceaee8ebe9393ae36a40d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/ef7b2f56815df854e66ceaee8ebe9393ae36a40d", "reference": "ef7b2f56815df854e66ceaee8ebe9393ae36a40d", "shasum": ""}, "require": {"ext-dom": "*", "ext-xmlwriter": "*", "php": "^5.6 || ^7.0", "phpunit/php-file-iterator": "^1.3", "phpunit/php-text-template": "^1.2", "phpunit/php-token-stream": "^1.4.2 || ^2.0", "sebastian/code-unit-reverse-lookup": "^1.0", "sebastian/environment": "^1.3.2 || ^2.0", "sebastian/version": "^1.0 || ^2.0"}, "require-dev": {"ext-xdebug": "^2.1.4", "phpunit/phpunit": "^5.7"}, "suggest": {"ext-xdebug": "^2.5.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that provides collection, processing, and rendering functionality for PHP code coverage information.", "homepage": "https://github.com/sebastian<PERSON>mann/php-code-coverage", "keywords": ["coverage", "testing", "xunit"], "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/4.0"}, "time": "2017-04-02T07:44:40+00:00"}, {"name": "phpunit/php-file-iterator", "version": "1.4.5", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "reference": "730b01bc3e867237eaac355e06a36b85dd93a8b4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-file-iterator/zipball/730b01bc3e867237eaac355e06a36b85dd93a8b4", "reference": "730b01bc3e867237eaac355e06a36b85dd93a8b4", "shasum": ""}, "require": {"php": ">=5.3.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "FilterIterator implementation that filters files based on a list of suffixes.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/", "keywords": ["filesystem", "iterator"], "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/tree/1.4.5"}, "time": "2017-11-27T13:52:08+00:00"}, {"name": "phpunit/php-text-template", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template.git", "reference": "31f8b717e51d9a2afca6c9f046f5d69fc27c8686"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/php-text-template/zipball/31f8b717e51d9a2afca6c9f046f5d69fc27c8686", "reference": "31f8b717e51d9a2afca6c9f046f5d69fc27c8686", "shasum": ""}, "require": {"php": ">=5.3.3"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Simple template engine.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/", "keywords": ["template"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/tree/1.2.1"}, "time": "2015-06-21T13:50:34+00:00"}, {"name": "phpunit/php-timer", "version": "1.0.9", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-timer.git", "reference": "3dcf38ca72b158baf0bc245e9184d3fdffa9c46f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-timer/zipball/3dcf38ca72b158baf0bc245e9184d3fdffa9c46f", "reference": "3dcf38ca72b158baf0bc245e9184d3fdffa9c46f", "shasum": ""}, "require": {"php": "^5.3.3 || ^7.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Utility class for timing", "homepage": "https://github.com/sebastian<PERSON>mann/php-timer/", "keywords": ["timer"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/php-timer/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-timer/tree/master"}, "time": "2017-02-26T11:10:40+00:00"}, {"name": "phpunit/php-token-stream", "version": "1.4.12", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-token-stream.git", "reference": "1ce90ba27c42e4e44e6d8458241466380b51fa16"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-token-stream/zipball/1ce90ba27c42e4e44e6d8458241466380b51fa16", "reference": "1ce90ba27c42e4e44e6d8458241466380b51fa16", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": ">=5.3.3"}, "require-dev": {"phpunit/phpunit": "~4.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Wrapper around PHP's tokenizer extension.", "homepage": "https://github.com/sebastian<PERSON>mann/php-token-stream/", "keywords": ["tokenizer"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/php-token-stream/issues", "source": "https://github.com/sebastian<PERSON>mann/php-token-stream/tree/1.4"}, "abandoned": true, "time": "2017-12-04T08:55:13+00:00"}, {"name": "phpunit/phpunit", "version": "5.7.27", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/phpunit.git", "reference": "b7803aeca3ccb99ad0a506fa80b64cd6a56bbc0c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/phpunit/zipball/b7803aeca3ccb99ad0a506fa80b64cd6a56bbc0c", "reference": "b7803aeca3ccb99ad0a506fa80b64cd6a56bbc0c", "shasum": ""}, "require": {"ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-xml": "*", "myclabs/deep-copy": "~1.3", "php": "^5.6 || ^7.0", "phpspec/prophecy": "^1.6.2", "phpunit/php-code-coverage": "^4.0.4", "phpunit/php-file-iterator": "~1.4", "phpunit/php-text-template": "~1.2", "phpunit/php-timer": "^1.0.6", "phpunit/phpunit-mock-objects": "^3.2", "sebastian/comparator": "^1.2.4", "sebastian/diff": "^1.4.3", "sebastian/environment": "^1.3.4 || ^2.0", "sebastian/exporter": "~2.0", "sebastian/global-state": "^1.1", "sebastian/object-enumerator": "~2.0", "sebastian/resource-operations": "~1.0", "sebastian/version": "^1.0.6|^2.0.1", "symfony/yaml": "~2.1|~3.0|~4.0"}, "conflict": {"phpdocumentor/reflection-docblock": "3.0.2"}, "require-dev": {"ext-pdo": "*"}, "suggest": {"ext-xdebug": "*", "phpunit/php-invoker": "~1.1"}, "bin": ["phpunit"], "type": "library", "extra": {"branch-alias": {"dev-master": "5.7.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "The PHP Unit Testing framework.", "homepage": "https://phpunit.de/", "keywords": ["phpunit", "testing", "xunit"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/phpunit/issues", "source": "https://github.com/sebastian<PERSON>mann/phpunit/tree/5.7.27"}, "time": "2018-02-01T05:50:59+00:00"}, {"name": "phpunit/phpunit-mock-objects", "version": "3.4.4", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/phpunit-mock-objects.git", "reference": "a23b761686d50a560cc56233b9ecf49597cc9118"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/phpunit-mock-objects/zipball/a23b761686d50a560cc56233b9ecf49597cc9118", "reference": "a23b761686d50a560cc56233b9ecf49597cc9118", "shasum": ""}, "require": {"doctrine/instantiator": "^1.0.2", "php": "^5.6 || ^7.0", "phpunit/php-text-template": "^1.2", "sebastian/exporter": "^1.2 || ^2.0"}, "conflict": {"phpunit/phpunit": "<5.4.0"}, "require-dev": {"phpunit/phpunit": "^5.4"}, "suggest": {"ext-soap": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.2.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Mock Object library for PHPUnit", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/phpunit-mock-objects/", "keywords": ["mock", "xunit"], "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/phpunit-mock-objects/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/phpunit-mock-objects/tree/3.4"}, "abandoned": true, "time": "2017-06-30T09:13:00+00:00"}, {"name": "psr/container", "version": "1.1.2", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "513e0666f7216c7459170d56df27dfcefe1689ea"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/513e0666f7216c7459170d56df27dfcefe1689ea", "reference": "513e0666f7216c7459170d56df27dfcefe1689ea", "shasum": ""}, "require": {"php": ">=7.4.0"}, "type": "library", "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "support": {"issues": "https://github.com/php-fig/container/issues", "source": "https://github.com/php-fig/container/tree/1.1.2"}, "time": "2021-11-05T16:50:12+00:00"}, {"name": "react/promise", "version": "v2.11.0", "source": {"type": "git", "url": "https://github.com/reactphp/promise.git", "reference": "1a8460931ea36dc5c76838fec5734d55c88c6831"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/reactphp/promise/zipball/1a8460931ea36dc5c76838fec5734d55c88c6831", "reference": "1a8460931ea36dc5c76838fec5734d55c88c6831", "shasum": ""}, "require": {"php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "^9.6 || ^5.7 || ^4.8.36"}, "type": "library", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"React\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://sorgalla.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://clue.engineering/"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://wyrihaximus.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://cboden.dev/"}], "description": "A lightweight implementation of CommonJS Promises/A for PHP", "keywords": ["promise", "promises"], "support": {"issues": "https://github.com/reactphp/promise/issues", "source": "https://github.com/reactphp/promise/tree/v2.11.0"}, "funding": [{"url": "https://opencollective.com/reactphp", "type": "open_collective"}], "time": "2023-11-16T16:16:50+00:00"}, {"name": "rector/rector", "version": "0.18.1", "source": {"type": "git", "url": "https://github.com/rectorphp/rector.git", "reference": "ee72ef542680a7f47ed8c6784f78b032c0d2f381"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/rectorphp/rector/zipball/ee72ef542680a7f47ed8c6784f78b032c0d2f381", "reference": "ee72ef542680a7f47ed8c6784f78b032c0d2f381", "shasum": ""}, "require": {"php": "^7.2|^8.0", "phpstan/phpstan": "^1.10.31"}, "conflict": {"rector/rector-doctrine": "*", "rector/rector-downgrade-php": "*", "rector/rector-phpunit": "*", "rector/rector-symfony": "*"}, "bin": ["bin/rector"], "type": "library", "autoload": {"files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Instant Upgrade and Automated Refactoring of any PHP code", "keywords": ["automation", "dev", "migration", "refactoring"], "support": {"issues": "https://github.com/rectorphp/rector/issues", "source": "https://github.com/rectorphp/rector/tree/0.18.1"}, "funding": [{"url": "https://github.com/tomasvotruba", "type": "github"}], "time": "2023-08-28T18:01:58+00:00"}, {"name": "sebastian/code-unit-reverse-lookup", "version": "1.0.2", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/code-unit-reverse-lookup.git", "reference": "1de8cd5c010cb153fcd68b8d0f64606f523f7619"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/code-unit-reverse-lookup/zipball/1de8cd5c010cb153fcd68b8d0f64606f523f7619", "reference": "1de8cd5c010cb153fcd68b8d0f64606f523f7619", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"phpunit/phpunit": "^8.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Looks up which function or method a line of code belongs to", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/tree/1.0.2"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-11-30T08:15:22+00:00"}, {"name": "sebastian/comparator", "version": "1.2.4", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/comparator.git", "reference": "2b7424b55f5047b47ac6e5ccb20b2aea4011d9be"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/2b7424b55f5047b47ac6e5ccb20b2aea4011d9be", "reference": "2b7424b55f5047b47ac6e5ccb20b2aea4011d9be", "shasum": ""}, "require": {"php": ">=5.3.3", "sebastian/diff": "~1.2", "sebastian/exporter": "~1.2 || ~2.0"}, "require-dev": {"phpunit/phpunit": "~4.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides the functionality to compare PHP values for equality", "homepage": "http://www.github.com/sebastian<PERSON>mann/comparator", "keywords": ["comparator", "compare", "equality"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/comparator/issues", "source": "https://github.com/sebastian<PERSON>mann/comparator/tree/1.2"}, "time": "2017-01-29T09:50:25+00:00"}, {"name": "sebastian/diff", "version": "1.4.3", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/diff.git", "reference": "7f066a26a962dbe58ddea9f72a4e82874a3975a4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/diff/zipball/7f066a26a962dbe58ddea9f72a4e82874a3975a4", "reference": "7f066a26a962dbe58ddea9f72a4e82874a3975a4", "shasum": ""}, "require": {"php": "^5.3.3 || ^7.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Diff implementation", "homepage": "https://github.com/sebastian<PERSON>mann/diff", "keywords": ["diff"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/diff/issues", "source": "https://github.com/sebastian<PERSON>mann/diff/tree/1.4"}, "time": "2017-05-22T07:24:03+00:00"}, {"name": "sebastian/environment", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/environment.git", "reference": "5795ffe5dc5b02460c3e34222fee8cbe245d8fac"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/environment/zipball/5795ffe5dc5b02460c3e34222fee8cbe245d8fac", "reference": "5795ffe5dc5b02460c3e34222fee8cbe245d8fac", "shasum": ""}, "require": {"php": "^5.6 || ^7.0"}, "require-dev": {"phpunit/phpunit": "^5.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to handle HHVM/PHP environments", "homepage": "http://www.github.com/sebastianbergmann/environment", "keywords": ["Xdebug", "environment", "hhvm"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/environment/issues", "source": "https://github.com/sebastian<PERSON>mann/environment/tree/master"}, "time": "2016-11-26T07:53:53+00:00"}, {"name": "sebastian/exporter", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/exporter.git", "reference": "ce474bdd1a34744d7ac5d6aad3a46d48d9bac4c4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/exporter/zipball/ce474bdd1a34744d7ac5d6aad3a46d48d9bac4c4", "reference": "ce474bdd1a34744d7ac5d6aad3a46d48d9bac4c4", "shasum": ""}, "require": {"php": ">=5.3.3", "sebastian/recursion-context": "~2.0"}, "require-dev": {"ext-mbstring": "*", "phpunit/phpunit": "~4.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides the functionality to export PHP variables for visualization", "homepage": "http://www.github.com/sebastianbergmann/exporter", "keywords": ["export", "exporter"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/exporter/issues", "source": "https://github.com/sebastian<PERSON>mann/exporter/tree/master"}, "time": "2016-11-19T08:54:04+00:00"}, {"name": "sebastian/global-state", "version": "1.1.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/global-state.git", "reference": "bc37d50fea7d017d3d340f230811c9f1d7280af4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/global-state/zipball/bc37d50fea7d017d3d340f230811c9f1d7280af4", "reference": "bc37d50fea7d017d3d340f230811c9f1d7280af4", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"phpunit/phpunit": "~4.2"}, "suggest": {"ext-uopz": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Snapshotting of global state", "homepage": "http://www.github.com/sebastian<PERSON>mann/global-state", "keywords": ["global state"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/global-state/issues", "source": "https://github.com/sebastian<PERSON>mann/global-state/tree/1.1.1"}, "time": "2015-10-12T03:26:01+00:00"}, {"name": "sebastian/object-enumerator", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/object-enumerator.git", "reference": "1311872ac850040a79c3c058bea3e22d0f09cbb7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-enumerator/zipball/1311872ac850040a79c3c058bea3e22d0f09cbb7", "reference": "1311872ac850040a79c3c058bea3e22d0f09cbb7", "shasum": ""}, "require": {"php": ">=5.6", "sebastian/recursion-context": "~2.0"}, "require-dev": {"phpunit/phpunit": "~5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Traverses array structures and object graphs to enumerate all referenced objects", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/tree/master"}, "time": "2017-02-18T15:18:39+00:00"}, {"name": "sebastian/recursion-context", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context.git", "reference": "2c3ba150cbec723aa057506e73a8d33bdb286c9a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/recursion-context/zipball/2c3ba150cbec723aa057506e73a8d33bdb286c9a", "reference": "2c3ba150cbec723aa057506e73a8d33bdb286c9a", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"phpunit/phpunit": "~4.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to recursively process PHP variables", "homepage": "http://www.github.com/sebastian<PERSON>mann/recursion-context", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/tree/master"}, "time": "2016-11-19T07:33:16+00:00"}, {"name": "sebastian/resource-operations", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/resource-operations.git", "reference": "ce990bb21759f94aeafd30209e8cfcdfa8bc3f52"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/resource-operations/zipball/ce990bb21759f94aeafd30209e8cfcdfa8bc3f52", "reference": "ce990bb21759f94aeafd30209e8cfcdfa8bc3f52", "shasum": ""}, "require": {"php": ">=5.6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides a list of PHP built-in functions that operate on resources", "homepage": "https://www.github.com/sebastianbergmann/resource-operations", "support": {"issues": "https://github.com/sebastian<PERSON>mann/resource-operations/issues", "source": "https://github.com/sebastian<PERSON>mann/resource-operations/tree/master"}, "time": "2015-07-28T20:34:47+00:00"}, {"name": "sebastian/version", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/version.git", "reference": "99732be0ddb3361e16ad77b68ba41efc8e979019"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/version/zipball/99732be0ddb3361e16ad77b68ba41efc8e979019", "reference": "99732be0ddb3361e16ad77b68ba41efc8e979019", "shasum": ""}, "require": {"php": ">=5.6"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that helps with managing the version number of Git-hosted PHP projects", "homepage": "https://github.com/sebastian<PERSON>mann/version", "support": {"issues": "https://github.com/sebastian<PERSON>mann/version/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/version/tree/master"}, "time": "2016-10-03T07:35:21+00:00"}, {"name": "seld/jsonlint", "version": "1.10.0", "source": {"type": "git", "url": "https://github.com/Seldaek/jsonlint.git", "reference": "594fd6462aad8ecee0b45ca5045acea4776667f1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/jsonlint/zipball/594fd6462aad8ecee0b45ca5045acea4776667f1", "reference": "594fd6462aad8ecee0b45ca5045acea4776667f1", "shasum": ""}, "require": {"php": "^5.3 || ^7.0 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^1.5", "phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.0 || ^8.5.13"}, "bin": ["bin/jsonlint"], "type": "library", "autoload": {"psr-4": {"Seld\\JsonLint\\": "src/Seld/JsonLint/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "JSON Linter", "keywords": ["json", "linter", "parser", "validator"], "support": {"issues": "https://github.com/Seldaek/jsonlint/issues", "source": "https://github.com/Seldaek/jsonlint/tree/1.10.0"}, "funding": [{"url": "https://github.com/Seldaek", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/seld/jsonlint", "type": "tidelift"}], "time": "2023-05-11T13:16:46+00:00"}, {"name": "seld/phar-utils", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/Seldaek/phar-utils.git", "reference": "ea2f4014f163c1be4c601b9b7bd6af81ba8d701c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/phar-utils/zipball/ea2f4014f163c1be4c601b9b7bd6af81ba8d701c", "reference": "ea2f4014f163c1be4c601b9b7bd6af81ba8d701c", "shasum": ""}, "require": {"php": ">=5.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Seld\\PharUtils\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be"}], "description": "PHAR file format utilities, for when PHP phars you up", "keywords": ["phar"], "support": {"issues": "https://github.com/Seldaek/phar-utils/issues", "source": "https://github.com/Seldaek/phar-utils/tree/1.2.1"}, "time": "2022-08-31T10:31:18+00:00"}, {"name": "smalot/pdfparser", "version": "v2.11.0", "source": {"type": "git", "url": "https://github.com/smalot/pdfparser.git", "reference": "ac8e6678b0940e4b2ccd5caadd3fb18e68093be6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/smalot/pdfparser/zipball/ac8e6678b0940e4b2ccd5caadd3fb18e68093be6", "reference": "ac8e6678b0940e4b2ccd5caadd3fb18e68093be6", "shasum": ""}, "require": {"ext-iconv": "*", "ext-zlib": "*", "php": ">=7.1", "symfony/polyfill-mbstring": "^1.18"}, "type": "library", "autoload": {"psr-0": {"Smalot\\PdfParser\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Pdf parser library. Can read and extract information from pdf file.", "homepage": "https://www.pdfparser.org", "keywords": ["extract", "parse", "parser", "pdf", "text"], "support": {"issues": "https://github.com/smalot/pdfparser/issues", "source": "https://github.com/smalot/pdfparser/tree/v2.11.0"}, "time": "2024-08-16T06:48:03+00:00"}, {"name": "squizlabs/php_codesniffer", "version": "3.7.2", "source": {"type": "git", "url": "https://github.com/squizlabs/PHP_CodeSniffer.git", "reference": "ed8e00df0a83aa96acf703f8c2979ff33341f879"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/squizlabs/PHP_CodeSniffer/zipball/ed8e00df0a83aa96acf703f8c2979ff33341f879", "reference": "ed8e00df0a83aa96acf703f8c2979ff33341f879", "shasum": ""}, "require": {"ext-simplexml": "*", "ext-tokenizer": "*", "ext-xmlwriter": "*", "php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "^4.0 || ^5.0 || ^6.0 || ^7.0"}, "bin": ["bin/phpcs", "bin/phpcbf"], "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "role": "lead"}], "description": "PHP_CodeSniffer tokenizes PHP, JavaScript and CSS files and detects violations of a defined set of coding standards.", "homepage": "https://github.com/squizlabs/PHP_CodeSniffer", "keywords": ["phpcs", "standards", "static analysis"], "support": {"issues": "https://github.com/squizlabs/PHP_CodeSniffer/issues", "source": "https://github.com/squizlabs/PHP_CodeSniffer", "wiki": "https://github.com/squizlabs/PHP_CodeSniffer/wiki"}, "time": "2023-02-22T23:07:41+00:00"}, {"name": "symfony/service-contracts", "version": "v2.5.2", "source": {"type": "git", "url": "https://github.com/symfony/service-contracts.git", "reference": "4b426aac47d6427cc1a1d0f7e2ac724627f5966c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/service-contracts/zipball/4b426aac47d6427cc1a1d0f7e2ac724627f5966c", "reference": "4b426aac47d6427cc1a1d0f7e2ac724627f5966c", "shasum": ""}, "require": {"php": ">=7.2.5", "psr/container": "^1.1", "symfony/deprecation-contracts": "^2.1|^3"}, "conflict": {"ext-psr": "<1.1|>=2"}, "suggest": {"symfony/service-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Service\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to writing services", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/service-contracts/tree/v2.5.2"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-05-30T19:17:29+00:00"}, {"name": "symfony/stopwatch", "version": "v5.4.21", "source": {"type": "git", "url": "https://github.com/symfony/stopwatch.git", "reference": "f83692cd869a6f2391691d40a01e8acb89e76fee"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/stopwatch/zipball/f83692cd869a6f2391691d40a01e8acb89e76fee", "reference": "f83692cd869a6f2391691d40a01e8acb89e76fee", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/service-contracts": "^1|^2|^3"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Stopwatch\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a way to profile code", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/stopwatch/tree/v5.4.21"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-02-14T08:03:56+00:00"}, {"name": "webmozart/assert", "version": "1.11.0", "source": {"type": "git", "url": "https://github.com/webmozarts/assert.git", "reference": "11cb2199493b2f8a3b53e7f19068fc6aac760991"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webmozarts/assert/zipball/11cb2199493b2f8a3b53e7f19068fc6aac760991", "reference": "11cb2199493b2f8a3b53e7f19068fc6aac760991", "shasum": ""}, "require": {"ext-ctype": "*", "php": "^7.2 || ^8.0"}, "conflict": {"phpstan/phpstan": "<0.12.20", "vimeo/psalm": "<4.6.1 || 4.6.2"}, "require-dev": {"phpunit/phpunit": "^8.5.13"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.10-dev"}}, "autoload": {"psr-4": {"Webmozart\\Assert\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Assertions to validate method input/output with nice error messages.", "keywords": ["assert", "check", "validate"], "support": {"issues": "https://github.com/webmozarts/assert/issues", "source": "https://github.com/webmozarts/assert/tree/1.11.0"}, "time": "2022-06-03T18:03:27+00:00"}], "aliases": [], "minimum-stability": "stable", "stability-flags": {"codacy/coverage": 20, "emarref/jwt": 20, "gianksp/cakephp-amazon-s3": 20, "gianksp/cakephp-swagger": 20}, "prefer-stable": true, "prefer-lowest": false, "platform": {"php": "7.4.*", "ext-json": "*", "ext-intl": "*", "ext-mongodb": "*", "ext-openssl": "*"}, "platform-dev": {}, "plugin-api-version": "2.6.0"}