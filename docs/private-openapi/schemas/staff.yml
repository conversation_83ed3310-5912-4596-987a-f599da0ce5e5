type: object
properties:
  _id:
    type: string
    pattern: '^[a-f\d]{24}$'
  branch_id:
    type: string
    pattern: '^[a-f\d]{24}$'
  namespace:
    type: string
  active:
    type: boolean
  type:
    type: string
    enum:
      - admin
      - reception
      - trainer
  first_name:
    type: string
  last_name:
    type: string
  description:
    type: string
    nullable: true
  name:
    type: string
  image_url:
    type: string
    nullable: true
