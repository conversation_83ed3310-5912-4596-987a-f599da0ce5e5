<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\Interactions\UseCase;

use App;
use Glofox\Datasource\Exceptions\InvalidMongoIdException;
use Glofox\Domain\Branches\Exceptions\BranchNotFoundException;
use Glofox\Domain\Branches\Models\Branch;
use Glofox\Domain\Branches\Repositories\BranchesRepository;
use Glofox\Domain\Interactions\InteractionIdentifier;
use Glofox\Domain\Interactions\Models\Interaction;
use Glofox\Domain\Interactions\Repositories\InteractionsRepository;
use Glofox\Domain\Interactions\UseCase\CreateInteractions;
use Glofox\Domain\Interactions\UseCase\CreateInteractionsParams;
use Glofox\Domain\Users\Exceptions\UserNotFoundException;
use Glofox\Domain\Users\Models\User;
use Glofox\Domain\Users\Repositories\UsersRepository;
use GlofoxTestCase;
use Illuminate\Support\Collection;
use Mockery;

App::import('Test/Case', 'GlofoxTestCase');

class CreateInteractionsTest extends GlofoxTestCase
{
    private UsersRepository $usersRepoMocked;
    private BranchesRepository $branchesRepoMocked;
    private InteractionsRepository $interactionsRepoMocked;
    private Interaction $interactionsModelMocked;
    private User $usersModelMocked;

    public function setUp(): void
    {
        parent::setUp();

        $this->usersRepoMocked = Mockery::mock(UsersRepository::class);
        $this->branchesRepoMocked = Mockery::mock(BranchesRepository::class);
        $this->interactionsRepoMocked = Mockery::mock(InteractionsRepository::class);
        $this->interactionsModelMocked = Mockery::mock(Interaction::class);
        $this->usersModelMocked = Mockery::mock(User::class);
    }

    public function tearDown(): void
    {
        parent::tearDown();

        Mockery::close();
    }

    /**
     * @throws BranchNotFoundException
     * @throws InvalidMongoIdException
     * @throws UserNotFoundException
     */
    public function testCreateNewInteraction(): void
    {
        $data = new Collection([
            'description' => 'Test Interactions'
        ]);
        $branchId = '6242bcf2b7d0174f9e572f36';
        $userId = '64e631a5e2a8ccf9730cece3';
        $interactionData = [
            $userId,
            $branchId,
            'automationtzzvt',
            $data->get('description'),
            InteractionIdentifier::NOTE,
            null
        ];

        $this->usersRepoMocked
            ->shouldReceive('getByIdAndBranchesId')
            ->withArgs([$userId, [$branchId]])
            ->andReturn(User::make(['_id' => $userId]))
            ->once();

        $this->branchesRepoMocked
            ->shouldReceive('getById')
            ->withArgs(function (string $param) use ($branchId) {
                self::assertEquals($branchId, $param);
                return true;
            })
            ->andReturn(Branch::make(['_id' => $branchId, 'namespace' => 'automationtzzvt']))
            ->once();

        $this->interactionsRepoMocked
            ->shouldReceive('countManualByUser')
            ->andReturn(1)
            ->once();

        $this->interactionsRepoMocked
            ->shouldReceive('save')
            ->andReturn($interactionData)
            ->once();

        $params = new CreateInteractionsParams(
            $data,
            $branchId,
            $userId,
            $data['description'],
            InteractionIdentifier::byName('NOTE')
        );

        $result = (
        new CreateInteractions(
            $this->usersRepoMocked,
            $this->branchesRepoMocked,
            $this->interactionsRepoMocked
        )
        )->execute($params);

        $this->assertEquals($interactionData, $result);
    }
}
