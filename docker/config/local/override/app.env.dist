PHP_XDEBUG_ENABLE=1
#PHP_XDEBUG_MODE=profile
# Uncomment the following line in case of debugging through VS Code
# PHP_XDEBUG_DISCOVER_CLIENT_HOST=1

#mongodb => test
#GLOFOX_DATABASE_NAME=local
GLOFOX_DATABASE_NAME=test
AUTHENTICATION_SERVICE_URL="http://localhost:3000/"
JWT_SALT_GENERATE="{{ https://eu-west-1.console.aws.amazon.com/systems-manager/parameters/?region=eu-west-1&tab=Table#list_parameter_filters=Name:Contains:JWT_SALT_GENERATE }}"
JWT_SALT_VERIFY="{{ https://eu-west-1.console.aws.amazon.com/systems-manager/parameters/?region=eu-west-1&tab=Table#list_parameter_filters=Name:Contains:JWT_SALT_VERIFY }}"
GLOFOX_DATABASE_DEFAULT_MAX_TIME_MS=30000
STAFF_AVAILABILITY_SERVICE_URL="{{Example: http://api.internal-gateway.hop.service.private}}"
GLOFOX_ROOT_DOMAIN="{{Example: https://hopper.glofox.com}}"

SENDGRID_PASSWORD="{{ https://eu-west-1.console.aws.amazon.com/systems-manager/parameters/?region=eu-west-1&tab=Table#list_parameter_filters=Name:Contains:SENDGRID_PASSWORD }}"
HONEYCOMB_BASE_URL="https://api.honeycomb.io"
HONEYCOMB_DATASET_NAME="{{Example: glofox-hop}}"
HONEYCOMB_API_KEY="{{ https://eu-west-1.console.aws.amazon.com/systems-manager/parameters/?region=eu-west-1&tab=Table#list_parameter_filters=Name:Contains:HONEYCOMB_API_KEY }}"
APPLE_DEV_KEY="{{ https://eu-west-1.console.aws.amazon.com/systems-manager/parameters/?region=eu-west-1&tab=Table#list_parameter_filters=Name:Contains:APPLE_DEV_KEY }}"


# External
#GLOFOX_DATABASE_SSL=
#GLOFOX_DATABASE_NAME=
#GLOFOX_DATABASE_HOST=
#GLOFOX_DATABASE_PORT=
#GLOFOX_DATABASE_USERNAME=
#GLOFOX_DATABASE_PASSWORD=
#GLOFOX_DATABASE_REPLICASET_NAME=
#GLOFOX_DATABASE_READ_PREFERENCE=
#GLOFOX_DATABASE_FOR_AUTHENTICATION=
#OPENSHIFT_APP_NAME=
#JWT_SALT_GENERATE=
#JWT_SALT_VERIFY=

COMMUNICATIONS_SERVICE_TOPIC=test_topic_arn
