<?php

declare(strict_types=1);

namespace Glofox\Infrastructure\Flags;

use <PERSON><PERSON><PERSON><PERSON>\Enum;

/**
 * @method static CONSENT()
 * @method static PRODUCT_LED_GROWTH()
 * @method static RECEPTIONIST_RESTRICTIONS_REVENUE()
 * @method static ACTIVE_BOOKINGS_LIMIT()
 * @method static CLASS_CANCELLATION_MESSAGE_ENABLED()
 * @method static INTEGRATOR_ENABLE_CHARGE_IRIS()
 * @method static IS_LEAD_SOURCE_CAPTURE_ENABLED()
 * @method static IS_COMMUNICATION_BOOKINGS_EMAILS_ENABLED()
 * @method static RECEIPT_WITH_PLAN_NAME()
 * @method static IS_PLANCODE_CAST_STRING_ENABLED()
 * @method static SHOW_LINK_ON_PAYMENT_COLLECTOR()
 * @method static IS_PAYMENT_COLLECTOR_V2_ENABLED()
 * @method static USE_PAYMENT_COLLECTOR_V2_SETUP_INTENT()
 * @method static IS_ROAMING_MEMBERS_RESTRICTED_PROFILE_ENABLED()
 * @method static IS_MARKETING_SOURCE_PAGINATION_ENABLED()
 * @method static IS_NO_MONTH_OVERFLOW_ENABLED()
 * @method static IS_MEMBERSHIP_CANCELLATION_REASONS_FOR_UNLIMITED_ENABLED()
 * @method static IS_NEW_STATUS_NO_SALE_TRIAL_ENABLED()
 * @method static IS_WEBPORTAL_MEMBERSHIP_START_DATE()
 * @method static IS_RESTRICTED_RENEWAL_THROUGH_MEMBERSHIPS_ENABLED()
 * @method static IS_FETCHING_FINANCIAL_ADDRESS_FROM_STRIPE_ENABLED()
 * @method static ARE_RCB_OVERDUE_MEMBERSHIPS_CANCELLATIONS_DISABLED()
 * @method static RECEIPT_WITH_INVOICE_PRICE_BREAKDOWN()
 * @method static US_FTC_COMPLIANCE_ENABLED()
 * @method static FTC_RENEWAL_CONSENT()
 * @method static IS_CANCEL_BOOKINGS_WHEN_MEMBERSHIP_IS_PAUSED_ENABLED()
 * @method static IS_CONTENT_MODERATION_ENABLED()
 * @method static IS_RE_REGISTRATION_FOR_LEADS_ENABLED()
 * @method static IS_EDIT_NEXT_PAYMENT_DATE_RESTRICTED_ENABLED()
 * @method static IS_SYNCHRONOUS_WAITLIST_REQUEST_VALIDATION_ENABLED()
 * @method static POLISH_PDF_RECEIPT_RENDER()
 * @method static IS_PASSWORD_RESET_RATE_LIMIT_ENABLED()
 * @method static IS_CANCEL_BOOKINGS_WHEN_MEMBERSHIP_IS_CANCELLED_ENABLED()
 * @method static IS_EAGREEMENT_SANITIZE_ENABLED()
 * @method static ARE_APPOINTMENTS_EDITS_ENABLED()
 * @method static IS_STAFF_AVAILABILITY_MULTIPLE_SCHEDULES_FIX_ENABLED()
 */
class Flag extends Enum
{
    public const CONSENT = 'consent-1';
    public const PRODUCT_LED_GROWTH = 'product-led-growth';
    public const RECEPTIONIST_RESTRICTIONS_REVENUE = 'receptionist-restrictions-revenue';
    public const ACTIVE_BOOKINGS_LIMIT = 'active-bookings-limit-iris';
    public const CLASS_CANCELLATION_MESSAGE_ENABLED = 'is-class-cancellation-message-enabled-iris';
    public const INTEGRATOR_ENABLE_CHARGE_IRIS = 'integrator-enable-charge-iris';
    public const IS_LEAD_SOURCE_CAPTURE_ENABLED = 'is-lead-source-capture-enabled';
    public const IS_COMMUNICATION_BOOKINGS_EMAILS_ENABLED = 'is-communication-bookings-emails-enabled';
    public const RECEIPT_WITH_PLAN_NAME = 'receipt-with-plan-name';
    public const IS_PLANCODE_CAST_STRING_ENABLED = 'is-plancode-cast-string-enabled';
    public const SHOW_LINK_ON_PAYMENT_COLLECTOR = 'show-link-on-payment-collector';
    public const IS_PAYMENT_COLLECTOR_V2_ENABLED = 'is-payment-collector-v2-enabled';
    public const USE_PAYMENT_COLLECTOR_V2_SETUP_INTENT = 'use-payment-collector-v2-setup-intent';
    public const IS_ROAMING_MEMBERS_RESTRICTED_PROFILE_ENABLED = 'is-roaming-members-restricted-profile-enabled';
    public const IS_MARKETING_SOURCE_PAGINATION_ENABLED = 'is-marketing-source-pagination-enabled';
    public const IS_NO_MONTH_OVERFLOW_ENABLED = 'is-no-month-overflow-enabled';
    public const IS_MEMBERSHIP_CANCELLATION_REASONS_FOR_UNLIMITED_ENABLED='is-membership-cancellation-reasons-for-unlimited-enabled';
    public const IS_NEW_STATUS_NO_SALE_TRIAL_ENABLED = 'is-new-status-no-sale-trial-enabled';
    public const IS_WEBPORTAL_MEMBERSHIP_START_DATE = 'webportal-membership-start-date';
    public const IS_RESTRICTED_RENEWAL_THROUGH_MEMBERSHIPS_ENABLED = 'is-restricted-renewal-through-memberships-enabled';
    public const IS_FETCHING_FINANCIAL_ADDRESS_FROM_STRIPE_ENABLED = 'is-fetching-financial-address-from-stripe-enabled';
    public const ARE_RCB_OVERDUE_MEMBERSHIPS_CANCELLATIONS_DISABLED = 'are-rcb-overdue-memberships-cancellations-disabled';
    public const RECEIPT_WITH_INVOICE_PRICE_BREAKDOWN = 'receipt-with-invoice-price-breakdown';
    public const US_FTC_COMPLIANCE_ENABLED = 'us-ftc-compliance-enabled';
    public const FTC_RENEWAL_CONSENT = 'ftc-renewal-consent';
    public const IS_CANCEL_BOOKINGS_WHEN_MEMBERSHIP_IS_PAUSED_ENABLED = 'is-cancel-bookings-when-membership-is-paused-enabled';
    public const IS_CONTENT_MODERATION_ENABLED = 'is-content-moderation-enabled';
    public const IS_RE_REGISTRATION_FOR_LEADS_ENABLED = 'is-re-registration-for-leads-enabled';
    public const IS_EDIT_NEXT_PAYMENT_DATE_RESTRICTED_ENABLED = 'is-edit-npd-restricted-enabled';
    public const IS_SYNCHRONOUS_WAITLIST_REQUEST_VALIDATION_ENABLED = 'is-synchronous-waitlist-request-validation-enabled';
    public const POLISH_PDF_RECEIPT_RENDER = 'polish-pdf-receipt-render';
    public const IS_PASSWORD_RESET_RATE_LIMIT_ENABLED = 'is_password_reset_rate_limit_enabled';
    public const IS_CANCEL_BOOKINGS_WHEN_MEMBERSHIP_IS_CANCELLED_ENABLED = 'is-cancel-bookings-when-membership-is-cancelled-enabled';
    public const IS_EAGREEMENT_SANITIZE_ENABLED = 'e-agreement-sanitize-enabled';
    public const ARE_APPOINTMENTS_EDITS_ENABLED = 'are-appointments-edits-enabled';
    public const IS_STAFF_AVAILABILITY_MULTIPLE_SCHEDULES_FIX_ENABLED = 'is-staff-availability-multiple-schedules-fix-enabled';
}
