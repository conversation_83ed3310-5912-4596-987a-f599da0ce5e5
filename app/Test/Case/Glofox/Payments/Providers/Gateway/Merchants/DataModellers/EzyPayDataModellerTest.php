<?php

namespace CakeTestCases\Glofox\Payments\Providers\Gateway\Merchants\DataModellers;

use Glofox\Domain\Branches\Models\Branch;
use Glofox\Domain\PaymentProviders\Models\PaymentProvider;
use Glofox\Payments\Providers\Gateway\Merchants\DataModellers\EzyPayDataModeller;
use Illuminate\Validation\ValidationException;

\App::import('Test/Case', 'GlofoxTestCase');

class EzyPayDataModellerTest extends \GlofoxTestCase
{
    public function test_it_should_throw_an_exception_when_the_create_is_missing_fields()
    {
        $dataModeler = new EzyPayDataModeller();

        $this->setExpectedException(ValidationException::class);
        $dataModeler->modelForCreating($this->getPaymentProvider(), $this->getEzyPayEnabledBranch(), collect());
    }

    public function test_it_should_create_using_the_right_format_given_a_ezypay_enabled_branch_and_payload()
    {
        $dataModeler = new EzyPayDataModeller();

        $payload = collect([
            'username' => 'username',
            'password' => 'password',
            'merchant_id' => 'merchant_id'
        ]);

        $result = $dataModeler->modelForCreating($this->getPaymentProvider(), $this->getEzyPayEnabledBranch(), $payload);

        $this->assertEquals([
            'email' => '<EMAIL>',
            'phone' => '111',
            'country' => 'AUS',
            'currency' => 'aud',
            'business_name' => 'EzyPay Gym',
            'auth_object' => [
                'username' => 'username',
                'password' => 'password',
                'merchant_id' => 'merchant_id',
                'mode' => 'live',
            ],
        ], $result->toArray());
    }

    private function getEzyPayEnabledBranch(): Branch
    {
        return Branch::make([
            'name' => 'EzyPay Gym',
            'phone' => '111',
            'email' => '<EMAIL>',
            'address' => [
                'country_code' => 'AU',
                'currency' => 'AUD'
            ]
        ]);
    }

    private function getPaymentProvider(): PaymentProvider
    {
        return PaymentProvider::make([
            'active' => true,
            'gateway_id' => '40',
            'payment_method_type_id' => 'CARD',
            'verification_flow' => null,
            'handler_id' => 'GATEWAY_HANDLER',
            'available_countries' => [
                [
                    'country_code' => 'AU',
                    'default_charge_percentage' => 0.0,
                    'default_fixed_charge' => 0.0
                ]
            ],
            'name' => 'EZYPAY_CARD',
            'registration_fields' => [
                [
                    'id' => 'username',
                    'name' => 'USERNAME',
                    'origin' => 'INTERNAL',
                    'type' => 'STRING',
                ],
                  [
                    'id' => 'password',
                    'name' => 'PASSWORD',
                    'origin' => 'INTERNAL',
                    'type' => 'STRING',
                  ],
                  [
                    'id' => 'merchant_id',
                    'name' => 'MERCHANT_ID',
                    'origin' => 'INTERNAL',
                    'type' => 'STRING',
                  ],
                  [
                    'id' => 'mode',
                    'name' => 'MODE',
                    'origin' => 'HARDCODED',
                    'type' => 'STRING',
                    'default_value' => 'live',
                  ]
            ],
            'registration_flow' => [
                'type' => 'INTERNAL_FORM'
            ],
            'tokenization_handler' => 'EZYPAY_CARD'
        ]);
    }
}
