<?php

namespace Glofox\Payments\Providers\Noop\Plans;

use Glofox\Domain\PaymentMethods\Models\PaymentMethod;
use Glofox\Domain\PaymentProviders\Models\PaymentProvider;
use Glofox\Payments\Entities\Plan\Contracts\PlanHandlerContract;
use Glofox\Payments\Entities\Plan\Models\Plan;
use Glofox\Payments\Exceptions\MethodNotImplemented;
use Glofox\Payments\Entities\Plan\Internal\CreatePlan;

/**
 * Class NoopPlans
 * @package Glofox\Payments\Providers\Noop\Plans
 */
class NoopPlans implements PlanHandlerContract
{
    private \Glofox\Domain\PaymentMethods\Models\PaymentMethod $paymentMethod;

    private \Glofox\Domain\PaymentProviders\Models\PaymentProvider $paymentProvider;

    /**
     * @inheritDoc
     */
    public function __construct(PaymentMethod $paymentMethod, PaymentProvider $paymentProvider)
    {
        $this->paymentProvider = $paymentProvider;
        $this->paymentMethod = $paymentMethod;
    }

    /**
     * @inheritDoc
     */
    public function create(CreatePlan $parameters): Plan
    {
        throw new MethodNotImplemented();
    }

    /**
     * @inheritDoc
     */
    public function getById($planId): ?Plan
    {
        throw new MethodNotImplemented();
    }

    /**
     * @inheritDoc
     */
    public function update(Plan $plan): ?Plan
    {
        throw new MethodNotImplemented();
    }

    /**
     * @inheritDoc
     */
    public function delete($planId): bool
    {
        throw new MethodNotImplemented();
    }
}
