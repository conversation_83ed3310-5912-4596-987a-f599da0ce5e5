type: object
properties:
  _id:
    type: string
  branch_id:
    type: string
  user_id:
    type: string
  description:
    type: string
  type:
    type: string
    enum:
      - NOTE
      - CALLED_AND_CONNECTED
      - CALLED_AND_NO_ANSWER
      - MANUAL_EMAIL
      - MEMBER_SERVICE_INTERACTION
      - AUTO_EMAIL
      - AUTO_SMS
      - AUTO_PUSH_NOTIFICATION
      - AMPLIFY_EMAIL
      - AMPLIFY_SMS
      - AMPLIFY_PUSH
  created:
    type: integer
