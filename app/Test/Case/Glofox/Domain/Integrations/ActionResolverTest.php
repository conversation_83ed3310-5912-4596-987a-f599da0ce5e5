<?php

namespace CakeTestCases\Glofox\Domain\Integrations;

use Glofox\Domain\Integrations\ActionResolver;
use Glofox\Domain\Integrations\ClassPass\ActionProvider;

\App::import('Test/Case', 'GlofoxTestCase');

/**
 * Class ListEventsTest.
 */
class ActionResolverTest extends \GlofoxTestCase
{
    /**
     * Fixtures.
     *
     * @var array
     */
    public $fixtures = [];

    /**
     * setUp method.
     */
    public function setUp()
    {
        parent::setUp();
        $actionProvider = new ActionProvider();
        $this->ActionResolver = new ActionResolver($actionProvider);
    }

    /**
     * tearDown method.
     */
    public function tearDown()
    {
        parent::tearDown();
        unset($this->ActionResolver);
    }

    public function testGetActionName()
    {
        // List action with no id provided
        $requestMethod = 'ListEvents';
        $id = null;
        $result = $this->ActionResolver->getActionName($requestMethod, $id);
        $expected = 'ListEvents';
        $this->assertEquals($expected, $result, 'List action with no id provided');

        // List action with id provided
        $requestMethod = 'ListEvents';
        $id = '12312';
        $result = $this->ActionResolver->getActionName($requestMethod, $id);
        $expected = 'GetEvent';
        $this->assertEquals($expected, $result, 'List action with id provided');

        // Random word with the word List but it doesnt start with List
        $requestMethod = 'EventsList';
        $id = '12312';
        $result = $this->ActionResolver->getActionName($requestMethod, $id);
        $expected = 'EventsList';
        $this->assertEquals($expected, $result, 'Random word with the word List but it doesnt start with List');

        // List action with id provided
        $requestMethod = 'ListResources';
        $id = '123';
        $result = $this->ActionResolver->getActionName($requestMethod, $id);
        $expected = 'GetResource';
        $this->assertEquals($expected, $result, 'List action with id provided');
    }
}
