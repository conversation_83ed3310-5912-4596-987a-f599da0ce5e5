<?php

namespace Glofox\Repositories\Search\Filters\Shared;

use Glofox\Domain\Leads\Search\Expressions\LeadStatus;
use Glofox\Domain\Leads\Status;
use Glofox\Repositories\Filters\Evaluable;
use Glofox\Repositories\Filters\Expressionable;

use Glofox\Repositories\Search\Expressions\Conditionable;
use Glofox\Repositories\Search\Expressions\HasConditionableExpression;
use Glofox\Repositories\Search\Expressions\HasEvaluableExpression;


use Glofox\Repositories\Search\Expressions\Shared\Active;
use Webmozart\Expression\Expression;

class IsActive implements Evaluable, Expressionable, Conditionable
{
    use HasEvaluableExpression;
    use HasConditionableExpression;

    public function expression() : Conditionable
    {
        return new Active(true);
    }
}
