<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Http\Proxy\Services\AddOns;

use Glofox\Http\Proxy\NotAuthorisedException;
use Glofox\Http\Proxy\Services\AddOns\AuthoriseRequests;
use GuzzleHttp\Psr7\Request;
use GuzzleHttp\Psr7\Response;

\App::import('Test/Case', 'GlofoxTestCase');

final class AuthoriseRequestsTest extends \GlofoxTestCase
{
    public $fixtures = [];
    private ?\Closure $next = null;

    public function setUp(): void
    {
        parent::setUp();
        $this->next = fn () => new Response(200);
    }

    public function testExceptionMethod(): void
    {
        $middleware = new AuthoriseRequests();

        $this->setExpectedException(NotAuthorisedException::class);

        $request = new Request('POST', '/foo/v3/services');
        $middleware->handle($request, $this->next);
    }

    public function testExceptionPath(): void
    {
        $middleware = new AuthoriseRequests();

        $this->setExpectedException(NotAuthorisedException::class);

        $request = new Request('GET', '/foo/v4/services'); // v4 is not authorised
        $middleware->handle($request, $this->next);
    }

    public function testSuccess(): void
    {
        $middleware = new AuthoriseRequests();

        $request = new Request('GET', '/foo/v3/services');
        $response = $middleware->handle($request, $this->next);

        $this->assertSame(200, $response->getStatusCode());
    }
}
