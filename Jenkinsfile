pipeline {
    agent any
    options {
        timestamps()
    }
    parameters {
        booleanParam(name: 'RELEASE_PR', defaultValue: false, description: 'Enable to release the PR to the QA Environment')
        choice(name: 'QA_ENVIRONMENT', choices: ['none', 'starwars', 'terminator', 'speedly', 'avengers'], description: '')
    }
    environment {
        BUILD_ENVIRONMENT = "${env.BRANCH_NAME == "master" ? "platform" : "non-platform"}"
    }
    stages {
        stage ('Build') {
            steps {
                sh '''
                    docker build --build-arg ENV=$BUILD_ENVIRONMENT --build-arg GITHUB_ACCESS_TOKEN=$GITHUB_ACCESS_TOKEN -t quay.io/glofox/api:$BRANCH_NAME-$GIT_COMMIT .
                    echo "FROM quay.io/glofox/api:$BRANCH_NAME-$GIT_COMMIT\nCMD /var/www/app/Console/cake ApiWorker" | docker build -t quay.io/glofox/apiworker:$BRANCH_NAME-$GIT_COMMIT -
                '''
            }
            post {
                success {
                    slackSend (color: '#00FF00', channel: '#quality_team', message: "Successfully Built '${BRANCH_NAME}' - '${GIT_COMMIT}' (${env.RUN_DISPLAY_URL})")
                }
                failure {
                    slackSend (color: '#FF0000', channel: '#quality_team', message: "Failed to Build '${BRANCH_NAME}' - '${GIT_COMMIT}' (${env.RUN_DISPLAY_URL})")
                }
            }
        }
        stage('Test') {
            when {
                not {
                    anyOf {
                        expression { params.RELEASE_PR == true }
                        expression { env.BRANCH_NAME ==~ /staging|master/ }
                        expression { env.BRANCH_NAME ==~ /PR*/ && env.CHANGE_TARGET ==~ /staging|master/ }
                    }
                }
            }
            stages {
                stage('run the MongoDB container') {
                    steps {
                        sh '''
                            docker run --rm -d --name db-$GIT_COMMIT --net=db-net mongo:3.6.5
                        '''
                        sh '''#!/bin/bash
                            attempt=0
                            while [ $attempt -le 59 ]; do
                                attempt=$(( $attempt + 1 ))
                                echo "Waiting for server to be up (attempt: $attempt)..."
                                result=$(docker logs db-$GIT_COMMIT)
                                if grep -q 'waiting for connections on port 27017' <<< $result ; then
                                    echo "Mongodb is up!"
                                    break
                                fi
                                sleep 2
                            done
                        '''
                    }
                }
                stage('run the API container') {
                    steps {
                        sh '''
                            docker run --rm --net=db-net -e GLOFOX_DATABASE_HOST=db-$GIT_COMMIT --env-file ./docker/test/localhost.env --name tests-$GIT_COMMIT -d quay.io/glofox/api:$BRANCH_NAME-$GIT_COMMIT
                        '''
                    }
                }
                // stage('run tests with xdebug') {
                //     when {
                //         anyOf {
                //             branch 'development'
                //             expression {
                //                 env.BRANCH_NAME ==~ /^PR-.*/ && env.CHANGE_TARGET == 'development'
                //             }
                //         }
                //     }
                //     steps {
                //         sh '''
                //             docker exec tests-$GIT_COMMIT bash -c "./app/Console/cake test --debug --stop-on-error --stop-on-failure app AllTests --coverage-clover $COVERAGE_XML_PATH"
                //         '''
                //     }
                // }
                stage('run tests without xdebug') {
                    when {
                        anyOf {
                            branch 'development'
                            branch 'master'
                            branch 'staging'
                            expression {
                                env.BRANCH_NAME ==~ /^PR-.*/
                            }
                        }
                    }
                    steps {
                        sh '''
                            docker exec tests-$GIT_COMMIT bash -c "./contrib/scripts/xdebug.sh disable && ./app/Console/cake test --no-colors app AllTests && ./contrib/scripts/xdebug.sh enable"
                        '''
                    }
                }
                // stage('run coverage report') {
                //     when {
                //         anyOf {
                //             branch 'development'
                //             expression {
                //                 env.BRANCH_NAME ==~ /^PR-.*/ && env.CHANGE_TARGET == 'development'
                //             }
                //         }
                //     }
                //     steps {
                //         sh '''
                //             docker exec tests-$GIT_COMMIT bash -c "php /var/www/vendor/bin/codacycoverage clover $COVERAGE_XML_PATH && rm $COVERAGE_XML_PATH"
                //         '''
                //     }
                // }
            }
            post {
                always {
                    sh '''
                        docker kill db-$GIT_COMMIT tests-$GIT_COMMIT
                    '''
                }
            }
        }
        stage('Publish Image') {
            stages {
                stage('publish branch image') {
                    when {
                        not {
                            branch 'PR*'
                        }
                    }
                    steps {
                        sh '''
                            docker push quay.io/glofox/api:$BRANCH_NAME-$GIT_COMMIT
                            docker tag quay.io/glofox/api:$BRANCH_NAME-$GIT_COMMIT quay.io/glofox/api:$BRANCH_NAME
                            docker push quay.io/glofox/api:$BRANCH_NAME
                            docker push quay.io/glofox/apiworker:$BRANCH_NAME-$GIT_COMMIT
                            docker tag quay.io/glofox/apiworker:$BRANCH_NAME-$GIT_COMMIT quay.io/glofox/apiworker:$BRANCH_NAME
                            docker push quay.io/glofox/apiworker:$BRANCH_NAME
                        '''
                    }
                }
                stage('publish pull request image') {
                    when {
                        anyOf {
                            branch 'PR*'
                        }
                        expression { params.RELEASE_PR == true }
                    }
                    steps {
                        sh '''
                            docker tag quay.io/glofox/api:$BRANCH_NAME-$GIT_COMMIT quay.io/glofox/api:$CHANGE_BRANCH-$GIT_COMMIT
                            docker push quay.io/glofox/api:$CHANGE_BRANCH-$GIT_COMMIT
                            docker tag quay.io/glofox/apiworker:$BRANCH_NAME-$GIT_COMMIT quay.io/glofox/apiworker:$CHANGE_BRANCH-$GIT_COMMIT
                            docker push quay.io/glofox/apiworker:$CHANGE_BRANCH-$GIT_COMMIT
                        '''
                    }
                }
            }
        }
        stage('Release') {
            environment {
                IMAGE_TAG = "${BRANCH_NAME}-${GIT_COMMIT}"
            }
            stages {
                stage('release development') {
                    when {
                        anyOf {
                            branch 'development'
                        }
                    }
                    steps {
                        build job: 'Dev-Stg-ECS-Service-Deploy', parameters: [string(name: 'IMAGE_TAG', value: IMAGE_TAG), string(name: 'ENVIRONMENT', value: 'development'), string(name: 'PROJECT', value: 'api'), string(name: 'EXTRA_VARS', value: '')]
                        build job: 'Dev-Stg-ECS-Service-Deploy', parameters: [string(name: 'IMAGE_TAG', value: IMAGE_TAG), string(name: 'ENVIRONMENT', value: 'development'), string(name: 'PROJECT', value: 'apiworker'), string(name: 'EXTRA_VARS', value: '')]
                    }
                }
                stage('release staging') {
                    when {
                        anyOf {
                            branch 'staging'
                        }
                    }
                    steps {
                        build job: 'Dev-Stg-ECS-Service-Deploy', parameters: [string(name: 'IMAGE_TAG', value: IMAGE_TAG), string(name: 'ENVIRONMENT', value: 'staging'), string(name: 'PROJECT', value: 'api'), string(name: 'EXTRA_VARS', value: '')]
                        build job: 'Dev-Stg-ECS-Service-Deploy', parameters: [string(name: 'IMAGE_TAG', value: IMAGE_TAG), string(name: 'ENVIRONMENT', value: 'staging'), string(name: 'PROJECT', value: 'apiworker'), string(name: 'EXTRA_VARS', value: '')]
                    }
                }
                stage('release master') {
                    when {
                        anyOf {
                            branch 'master'
                        }
                    }
                    parallel {
                        stage('Release API Platform') {
                            steps {
                                build job: 'Platform-Service-ECS-Deploy', parameters: [string(name: 'IMAGE_TAG', value: IMAGE_TAG), string(name: 'ENVIRONMENT', value: 'platform'), string(name: 'PROJECT', value: 'api'), string(name: 'REGION', value: 'eu-west-1'), string(name: 'EXTRA_VARS', value: '')]
                                build job: 'Platform-Service-ECS-Deploy', parameters: [string(name: 'IMAGE_TAG', value: IMAGE_TAG), string(name: 'ENVIRONMENT', value: 'platform'), string(name: 'PROJECT', value: 'api'), string(name: 'REGION', value: 'ap-southeast-1'), string(name: 'EXTRA_VARS', value: '')]
                            }
                        }
                        stage('Release API Sandbox') {
                            steps {
                                build job: 'Platform-Service-ECS-Deploy', parameters: [string(name: 'IMAGE_TAG', value: IMAGE_TAG), string(name: 'ENVIRONMENT', value: 'sandbox'), string(name: 'PROJECT', value: 'api'), string(name: 'REGION', value: 'eu-west-1'), string(name: 'EXTRA_VARS', value: '')]
                                build job: 'Platform-Service-ECS-Deploy', parameters: [string(name: 'IMAGE_TAG', value: IMAGE_TAG), string(name: 'ENVIRONMENT', value: 'sandbox'), string(name: 'PROJECT', value: 'api'), string(name: 'REGION', value: 'ap-southeast-1'), string(name: 'EXTRA_VARS', value: '')]
                            }
                        }
                        stage('Release API Worker') {
                            steps {
                                build job: 'Platform-Service-ECS-Deploy', parameters: [string(name: 'IMAGE_TAG', value: IMAGE_TAG), string(name: 'ENVIRONMENT', value: 'platform'), string(name: 'PROJECT', value: 'apiworker'), string(name: 'REGION', value: 'eu-west-1'), string(name: 'EXTRA_VARS', value: '')]
                                build job: 'Platform-Service-ECS-Deploy', parameters: [string(name: 'IMAGE_TAG', value: IMAGE_TAG), string(name: 'ENVIRONMENT', value: 'sandbox'), string(name: 'PROJECT', value: 'apiworker'), string(name: 'REGION', value: 'eu-west-1'), string(name: 'EXTRA_VARS', value: '')]
                            }
                        }
                    }
                    post {
                        success {
                            slackSend (color: '#00FF00', channel: '#quality_team', message: "Successfully Deployed to Production & Sandbox '${BRANCH_NAME}' - '${GIT_COMMIT}' (${env.RUN_DISPLAY_URL})")
                            sh '''
                                curl -X POST 'https://api.newrelic.com/v2/applications/154701314/deployments.json' \
                                -H 'X-Api-Key:6790515af4918b86d5a318fb2dc5ba8e0c2b1e116783101' -i \
                                -H 'Content-Type: application/json' \
                                -d '{ "deployment": { "revision": "'"$GIT_COMMIT"'", "changelog": "'"https://jenkins.glofox.com/job/api/job/master/$BUILD_NUMBER"'", "description": "Jenkins deployment EU", "user": "Jenkins" } }'
                            '''
                            sh '''
                                curl -X POST 'https://api.newrelic.com/v2/applications/154701480/deployments.json' \
                                -H 'X-Api-Key:6790515af4918b86d5a318fb2dc5ba8e0c2b1e116783101' -i \
                                -H 'Content-Type: application/json' \
                                -d '{ "deployment": { "revision": "'"$GIT_COMMIT"'", "changelog": "'"https://jenkins.glofox.com/job/api/job/master/$BUILD_NUMBER"'", "description": "Jenkins deployment AP", "user": "Jenkins" } }'
                            '''
                        }
                        failure {
                            slackSend (color: '#FF0000', channel: '#quality_team', message: "Failed to Deploy to Production & Sandbox '${BRANCH_NAME}' - '${GIT_COMMIT}' (${env.RUN_DISPLAY_URL})")
                        }
                    }
                }
                stage('AUTOMATION-SMOKE-TEST') {
                    when {
                        anyOf {
                            branch 'development'
                        }
                    }
                    steps {
                        build job: 'glofox-api-tests/master', parameters: [string(name: 'env', value: 'development'), string(name: 'namespace', value: 'automatesmoke'), string(name: 'superAdminLogin', value: '<EMAIL>'), string(name: 'superAdminPassword', value: '123456'), string(name: 'adminLogin', value: '<EMAIL>'), string(name: 'adminPassword', value: '123456'), string(name: 'tags', value: 'SMOKE'), string(name: 'threadCount', value: '1')], propagate: false, quietPeriod: 300, wait: false
                    }
                }
                stage('release pull request') {
                    environment {
                        PR_IMAGE_TAG = "${CHANGE_BRANCH}-${GIT_COMMIT}"
                    }
                    when {
                        anyOf {
                            branch 'PR*'
                        }
                        expression { params.RELEASE_PR == true }
                    }
                    steps {
                        build job: 'Dev-Stg-ECS-Service-Deploy', parameters: [string(name: 'IMAGE_TAG', value: PR_IMAGE_TAG), string(name: 'ENVIRONMENT', value: QA_ENVIRONMENT), string(name: 'PROJECT', value: 'api'), string(name: 'EXTRA_VARS', value: '')]
                        build job: 'Dev-Stg-ECS-Service-Deploy', parameters: [string(name: 'IMAGE_TAG', value: PR_IMAGE_TAG), string(name: 'ENVIRONMENT', value: QA_ENVIRONMENT), string(name: 'PROJECT', value: 'apiworker'), string(name: 'EXTRA_VARS', value: '')]
                    }
                    post {
                        success {
                            slackSend (color: '#00FF00', channel: '#quality_team', message: "Successfully Deployed '${CHANGE_BRANCH}' to '${QA_ENVIRONMENT}' (${env.RUN_DISPLAY_URL})")
                        }
                        failure {
                            slackSend (color: '#FF0000', channel: '#quality_team', message: "Failed to Deploy '${CHANGE_BRANCH}' to '${QA_ENVIRONMENT}' (${env.RUN_DISPLAY_URL})")
                        }
                    }
                }
            }
        }
    }
    post {
        always {
            cleanWs()
        }
    }
}
