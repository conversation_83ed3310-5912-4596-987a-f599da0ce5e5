var users = db.users.find({
    "active": true,
    "membership.type": {
        $in: ['time', 'time_classes']
    },
    $and: [
        {$or:[
                {
                    "membership.subscription": {
                        $exists: false
                    }},
                {
                    "membership.subscription": {
                        $eq: null
                    }}
            ]},
        {$or: [
                {
                    "membership.duration_time_unit": {
                        $exists: false
                    }
                },
                {
                    "membership.duration_time_unit_count": {
                        $exists: false
                    }
                },
                {
                    "membership.duration_time_unit_count": {
                        $in: [null, ""]
                    }
                },
                {
                    "membership.duration_time_unit": {
                        $in: [null, ""]
                    }
                }
            ]}
    ]
});


var i = 1;
var error = 0;
var success = 0;
var total = users.count();
var detailedErrorList = {};
users.forEach((user) => {

    try {

        if (!user.membership._id) {
            throw new Error(`Cannot recover ${user._id} since it's missing its membership id`)
        }

        if (!user.membership.plan_code) {
            throw new Error(`Cannot recover ${user.membership._id} since it's missing its membership plan_code`)
        }

        // console.info(`Fetching membership ${user.membership._id}`);

        var membership = db.memberships.findOne({
            _id: new ObjectId(user.membership._id)
        });

        if (!membership) {
            throw new Error(`Membership ${user.membership._id} from user ${user._id} was not found in the db. Cannot recover the duration of this member's membership.`);
        }

        var userPlanCode = user.membership.plan_code;

        var plans = membership.plans.filter((plan) => {
            return plan.code.toString() == userPlanCode.toString()
        })

        if (plans.length === 0) {
            throw new Error(`Plan ${userPlanCode} from user ${user._id} was not found in the db. Cannot recover the duration of this member's membership.`);
        }

        var plan = plans[0];

        if (!plan.duration_time_unit) {
            throw new Error(`The fetched plan for user ${user._id} doesnt have a duration time unit`);
        }

        if (!plan.duration_time_unit_count) {
            throw new Error(`The fetched plan for user ${user._id} doesnt have a duration time unit count`);
        }

        var query = {
            _id: user._id
        };

        var instruction = {
            $set: {
                "membership.duration_time_unit": plan.duration_time_unit,
                "membership.duration_time_unit_count": parseInt(plan.duration_time_unit_count)
            }
        };

        console.info(`User ${user._id} will have its duration dates set to`, instruction);

        db.users.update(query, instruction);

        success++;

        console.info(`[${i}/${total}] Membership duration recovered successfully (s: ${success} / e: ${error})`);
    }
    catch (err) {

        // console.error(err.message, err.stack);
        error++;

        console.info(`[${i}/${total}] Membership duration recovery failed (s: ${success} / e: ${error}) -  ${err.message} (${user.namespace}, ${user.created})`);

        var branchId = (Array.isArray(user.branch_id) ? user.branch_id[0] : user.branch_id);
        var originBranchId =  user.origin_branch_id;
        var namespace = user.namespace;

        var errorDetail = {
            userId: user._id+"",
            branchId: originBranchId || branchId,
            namespace: namespace,
            error: err.message
        }

        if (!detailedErrorList[branchId]) {
            detailedErrorList[branchId] = {
                details: []
            };
        }

        detailedErrorList[branchId].details.push(errorDetail);
    }

    i++;
});

console.info(`.`);
console.info(`.`);
console.info(`[${success}/${total}] Membership duration recovered successfully`);
console.info(`[${error}/${total}] Membership duration recovery failed`);


print("branchId,namespace,totalErrors")
for (var branchId in detailedErrorList) {
    print([
        branchId,
        detailedErrorList[branchId].details[0].namespace,
        detailedErrorList[branchId].details.length
    ].join(','))
}
