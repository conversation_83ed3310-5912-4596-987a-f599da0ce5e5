<?php

declare(strict_types=1);

namespace Glofox\Infrastructure\Flags;

use Glofox\Domain\Branches\Models\Branch;
use Glofox\Domain\Experiments\Services\ExperimentsClient;
use Glofox\Domain\FeatureFlags\FeatureFlagInterface;
use Illuminate\Http\Response;

class Flagger implements FeatureFlagInterface
{
    private ExperimentsClient $flaggerClient;
    private Flag $featureFlag;

    /**
     * After instantiating the Flagger class, is required to call the {@see self::withFlag()} method to set the feature
     * flag
     * @example app()->make(Flagger::class)->withFlag(Flag::SOME_FLAG());
     */
    public function __construct()
    {
        $this->flaggerClient = app()->make(ExperimentsClient::class);
    }

    /**
     * Setting the feature flag. This method is required to be called after instantiating the Flagger class and before
     * using the other methods, like {@see self::enable()}, {@see self::disable()}, {@see self::has()}, etc.
     * @example app()->make(Flagger::class)->withFlag(Flag::SOME_FLAG());
     */
    public function withFlag(Flag $featureFlag): self
    {
        $this->featureFlag = $featureFlag;
        return $this;
    }

    /**
     * @deprecated use {@see self::enableByBranchId()} instead.
     * It is required to call the {@see self::withFlag()} method before calling this method
     */
    public function enable(Branch $branch): bool
    {
        return $this->enableByBranchId($branch->id());
    }

    /**
     * It is required to call the {@see self::withFlag()} method before calling this method
     */
    public function enableByBranchId(string $branchId): bool
    {
        $this->flaggerClient->post(
            'UPDATE_TEST_TARGETS',
            [
                'testIdentifier' => $this->featureFlag->getValue(),
            ],
            [
                'targetId' => $branchId,
            ]
        );
        $response = $this->flaggerClient->getLastResponse();

        return $response->getStatusCode() === Response::HTTP_OK;
    }

    /**
     * @deprecated use {@see self::disableByBranchId()} instead.
     * It is required to call the {@see self::withFlag()} method before calling this method
     */
    public function disable(Branch $branch): bool
    {
        return $this->disableByBranchId($branch->id());
    }

    /**
     * It is required to call the {@see self::withFlag()} method before calling this method
     */
    public function disableByBranchId(string $branchId): bool
    {
        $this->flaggerClient->delete(
            'UPDATE_TEST_TARGETS',
            [
                'testIdentifier' => $this->featureFlag->getValue(),
            ],
            [
                'targetId' => $branchId,
            ]
        );
        $response = $this->flaggerClient->getLastResponse();

        return $response->getStatusCode() === Response::HTTP_OK;
    }

    /**
     * @deprecated use {@see self::hasByBranchId()} instead.
     * It is required to call the {@see self::withFlag()} method before calling this method
     */
    public function has(Branch $branch): bool
    {
        return $this->hasByBranchId($branch->id());
    }

    /**
     * It is required to call the {@see self::withFlag()} method before calling this method
     */
    public function hasByBranchId(string $branchId): bool
    {
        $this->flaggerClient->post(
            'GET_OUTCOMES',
            [
                'testIdentifier' => $this->featureFlag->getValue(),
            ],
            [
                'user' => [
                    'branch_id' => $branchId,
                ],
            ]
        );

        $response = $this->flaggerClient->getLastResponse();
        if ($response->getStatusCode() === Response::HTTP_FORBIDDEN) {
            return false;
        }
        $body = json_decode(
            (string)$response->getBody(),
            $assoc = true,
            512,
            JSON_PARTIAL_OUTPUT_ON_ERROR
        );

        if (empty($body)) {
            return false;
        }
        return $this->isEnabled($body);
    }

    private function isEnabled(array $body): bool
    {
        if (isset($body['name']) && is_string($body['name'])) {
            return strpos($body['name'], 'ENABLED') !== false;
        }
        return isset($body['name']) && $body['name'] === true;
    }
}
