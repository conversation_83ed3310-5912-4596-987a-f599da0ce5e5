<?php

namespace Glofox\Repositories\Search\Expressions;

use Webmozart\Expression\Expression;

trait HasMongoResolvers
{
    /**
     * @return Expression
     */
    abstract public function getExpression(); // : Expression

    /**
     * @return MongoOperatorResolver
     */
    protected function operatorResolver() : MongoOperatorResolver
    {
        return app()->make(MongoOperatorResolver::class);
    }

    /**
     * @return mixed
     * @throws \Exception
     */
    protected function dbValueFromExpression()
    {
        $expression = $this->getExpression();

        if (method_exists($expression, 'getComparedValue')) {
            $value = $expression->getComparedValue();
        }

        if (method_exists($expression, 'getAcceptedValues')) {
            $value = $expression->getAcceptedValues();
        }

        if ($value instanceof \MongoRegex) {
            return [
                '$regex' => $value->regex,
                '$options' => $value->flags
            ];
        }

        return $value;
    }
}
