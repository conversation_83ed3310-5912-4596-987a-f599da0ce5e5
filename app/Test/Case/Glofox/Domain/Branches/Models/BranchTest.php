<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\Branches\Models;

use Glofox\Domain\Branches\Models\Branch;
use Glofox\Domain\Branches\Models\BranchFiscal;
use Glofox\Domain\Branches\Models\MailchimpDetails;
use Mockery;
use Money\Currency;

\App::import('Test/Case', 'GlofoxTestCase');

class BranchTest extends \GlofoxTestCase
{
    public $fixtures = [];

    public function tearDown(): void
    {
        parent::tearDown();
        Mockery::close();
    }

    public function testName(): void
    {
        $branch = new Branch(['name' => 'Test Branch']);
        $this->assertEquals('Test Branch', $branch->name());
    }

    public function testEmail(): void
    {
        $branch = new Branch(['email' => '<EMAIL>']);
        $this->assertEquals('<EMAIL>', $branch->email());
    }

    public function testPhone(): void
    {
        $branch = new Branch(['phone' => '123456789']);
        $this->assertEquals('123456789', $branch->phone());
    }

    public function testWebsite(): void
    {
        $branch = new Branch(['website' => 'https://example.com']);
        $this->assertEquals('https://example.com', $branch->website());
    }

    public function testTimezone(): void
    {
        $branch = new Branch(['address' => ['timezone_id' => 'Europe/Dublin']]);
        $this->assertEquals(new \DateTimeZone('Europe/Dublin'), $branch->timezone());
    }

    public function testAddress(): void
    {
        $branch = new Branch(
            [
                'address' => [
                    'street' => '123 Main St',
                    'city' => 'Dublin',
                    'state' => 'Leinster',
                    'country_code' => 'IE',
                    'postal_code' => 'D18',
                    'currency' => 'USD',
                ],
            ]
        );
        $this->assertEquals('123 Main St', $branch->address()->street());
        $this->assertEquals('Dublin', $branch->address()->city());
        $this->assertEquals('Leinster', $branch->address()->state());
        $this->assertEquals('IE', $branch->address()->country());
        $this->assertEquals('IE', $branch->address()->countryCode());
        $this->assertEquals('D18', $branch->address()->postalCode());
        $this->assertEquals('USD', $branch->address()->currency());
    }

    public function testConfiguration(): void
    {
        $branch = new Branch(['configuration' => ['key' => 'value']]);
        $this->assertEquals(['key' => 'value'], $branch->configuration()->toArray());
    }

    public function testPushConfiguration(): void
    {
        $branch = new Branch(['push_config' => ['key' => 'value']]);
        $this->assertEquals(['key' => 'value'], $branch->pushConfiguration()->toArray());
    }

    public function testFeatures(): void
    {
        $branch = new Branch([
            'features' => [
                'booking' => [
                    'overbooking_enabled' => true,
                    'strike_system' => [
                        'enabled' => true,
                        'max_strikes' => 3,
                    ],
                    'auto_booking_enabled' => true,
                    'refunded_credit_expiry' => 3,
                    'late_cancellation_enabled' => true,
                    'booking_close_window' => 3,
                    'booking_open_window' => 3,
                    'booking_cancel_window' => 3,
                    'limit_booking' => 3,
                    'limit_booking_period' => 'day',
                    'limit_booking_enabled' => true,
                    'waiting_list_enabled' => true,
                    'waiting_list' => 3,
                    'active_limit_booking' => [
                        'enabled' => true,
                        'max_active' => 3,
                    ],
                ],
                'classes' => [
                    'weeks_display' => 3,
                ],
                'subscriptions' => [
                    'edit_price' => [
                        'enabled' => true,
                    ],
                ],
                'lead_management' => [
                    'enabled' => true,
                ],
                'courses' => [
                    'enabled' => true,
                ],
            ],
        ]);
        $this->assertTrue($branch->features()->isOverbookingEnabled());
        $this->assertTrue($branch->features()->isStrikeSystemEnabled());
        $this->assertEquals(3, $branch->features()->getMaxStrikesAllowed());
        $this->assertTrue($branch->features()->areCoursesEnabled());
        $this->assertTrue($branch->features()->isAutoBookingEnabled());
        $this->assertTrue($branch->features()->isEditSubscriptionPriceEnabled());
        $this->assertTrue($branch->features()->isLeadManagementEnabled());
        $this->assertEquals(3, $branch->features()->waitingList());
        $this->assertEquals(3, $branch->features()->bookingCloseWindow());
        $this->assertEquals(3, $branch->features()->bookingOpenWindow());
        $this->assertEquals(3, $branch->features()->bookingCancelWindow());
        $this->assertEquals(3, $branch->features()->limitBooking());
        $this->assertEquals('day', $branch->features()->limitBookingPeriod());
        $this->assertTrue($branch->features()->isLimitBookingEnabled());
        $this->assertTrue($branch->features()->isWaitingListEnabled());
        $this->assertEquals(3, $branch->features()->classWeeksDisplay());
        $this->assertTrue($branch->features()->isLateCancellationEnabled());
        $this->assertTrue($branch->features()->isActiveLimitBookingEnabled());
    }

    public function testLanguage(): void
    {
        $branch = new Branch(['language' => 'fr-FR']);
        $this->assertEquals('fr-FR', $branch->language());
    }

    public function testLocale(): void
    {
        $branch = new Branch(['language' => 'fr-FR']);
        $this->assertEquals('fr_FR', $branch->locale());
    }

    public function testCurrency(): void
    {
        $branch = new Branch(['address' => ['currency' => 'USD']]);
        $this->assertEquals(new Currency('USD'), $branch->currency());
    }

    public function testDeactivate(): void
    {
        $branch = new Branch(['active' => true]);
        $branch->deactivate();
        $this->assertFalse($branch->isActive());
    }

    public function testIsActive(): void
    {
        $branch = new Branch(['active' => true]);
        $this->assertTrue($branch->isActive());
    }

    public function testIsMailchimpPresent(): void
    {
        $branch = new Branch(['mailchimp' => ['api_key' => 'key']]);
        $this->assertTrue($branch->isMailchimpPresent());
    }

    public function testIsPlatinum(): void
    {
        $branch = new Branch(['stripe_plan_code' => 'platinum']);
        $this->assertTrue($branch->isPlatinum());
    }

    public function testTaxNumber(): void
    {
        $branch = new Branch(['configuration' => ['sales_tax' => ['tax_number' => '12345']]]);
        $this->assertEquals('12345', $branch->taxNumber());
    }

    public function testCompanyId(): void
    {
        $branch = new Branch(['configuration' => ['sales_tax' => ['company_id' => '67890']]]);
        $this->assertEquals('67890', $branch->companyId());
    }

    public function testLegalName(): void
    {
        $branch = new Branch(['configuration' => ['sales_tax' => ['legal_name' => 'Test Company']]]);
        $this->assertEquals('Test Company', $branch->legalName());
    }

    public function testGlofoxPlan(): void
    {
        $branch = new Branch(['stripe_plan_code' => 'basic']);
        $this->assertEquals('basic', $branch->glofoxPlan());
    }

    public function testFiscal(): void
    {
        $branch = new Branch(
            [
                'configuration' => [
                    'fiscal' => [
                        'member_tax_id_options' => [
                            'enabled' => true,
                            'required' => true,
                        ],
                    ],
                ],
            ]
        );
        $this->assertNotNull($branch->fiscal());
        $this->assertInstanceOf(BranchFiscal::class, $branch->fiscal());
        $this->assertTrue($branch->fiscal()->memberTaxIdOptions()->isEnable());
        $this->assertTrue($branch->fiscal()->memberTaxIdOptions()->isRequired());
    }

    public function testMailchimp(): void
    {
        $branch = new Branch(['mailchimp' => ['api_key' => 'key']]);
        $this->assertNotNull($branch->mailchimp());
        $this->assertInstanceOf(MailchimpDetails::class, $branch->mailchimp());
    }

    public function testCorporateId(): void
    {
        $branch = new Branch(['corporate_id' => 'corp_Lift']);
        $this->assertEquals('corp_Lift', $branch->corporateId());
    }

    public function testBelongsToLift(): void
    {
        $branch = new Branch(['corporate_id' => 'corp_Lift']);
        $this->assertTrue($branch->belongsToLift());
    }

    public function testClosingTimes(): void
    {
        $branch = new Branch(['closing_times' => ['Monday' => '18:00']]);
        $this->assertEquals(['Monday' => '18:00'], $branch->closingTimes());
    }
}
