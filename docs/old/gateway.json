{"info": {"name": "Glofox API Gateway (Examples)", "_postman_id": "963a37b4-7408-5ce2-94f5-51dd022f45e1", "description": "", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "GetMemberById", "request": {"method": "GET", "header": [{"key": "x-glofox-branch-id", "value": "5b17b8bbec0a6d0d7a08b8f3"}, {"key": "x-glofox-api-token", "value": "RpjVR+OZOWbJlAZvApWu6JrlowYUM/O3r94bBIeIGGI="}, {"key": "x-api-key", "value": "ZNk57dkXnegwZL3KoEBl6jMEPBSSoDQ27IFex8Z5"}], "body": {"mode": "raw", "raw": "{\"namespace\":\"glofox\",\"login\":\"<EMAIL>\",\"password\":\"123456\"}\n"}, "url": {"raw": "https://gf-api.aws.glofox.com/dev/2.0/members/5b1e86b68b78a5012f5ccf94", "protocol": "https", "host": ["gf-api", "aws", "glofox", "com"], "path": ["dev", "2.0", "members", "5b1e86b68b78a5012f5ccf94"]}}, "response": []}, {"name": "GetClassesByStudio", "request": {"method": "GET", "header": [{"key": "x-glofox-branch-id", "value": "5b17b8bbec0a6d0d7a08b8f3"}, {"key": "x-glofox-api-token", "value": "RpjVR+OZOWbJlAZvApWu6JrlowYUM/O3r94bBIeIGGI="}, {"key": "x-api-key", "value": "ZNk57dkXnegwZL3KoEBl6jMEPBSSoDQ27IFex8Z5"}], "body": {"mode": "raw", "raw": "{\"namespace\":\"glofox\",\"login\":\"<EMAIL>\",\"password\":\"123456\"}\n"}, "url": {"raw": "https://gf-api.aws.glofox.com/dev/2.0/events?start=1525215600&end=1529535599&sort_by=time_start", "protocol": "https", "host": ["gf-api", "aws", "glofox", "com"], "path": ["dev", "2.0", "events"], "query": [{"key": "start", "value": "1525215600", "equals": true}, {"key": "end", "value": "1529535599", "equals": true}, {"key": "sort_by", "value": "time_start", "equals": true}]}}, "response": []}, {"name": "CreateBooking", "request": {"method": "POST", "header": [{"key": "x-glofox-branch-id", "value": "5b17b8bbec0a6d0d7a08b8f3"}, {"key": "x-glofox-api-token", "value": "RpjVR+OZOWbJlAZvApWu6JrlowYUM/O3r94bBIeIGGI="}, {"key": "x-api-key", "value": "ZNk57dkXnegwZL3KoEBl6jMEPBSSoDQ27IFex8Z5"}], "body": {"mode": "raw", "raw": "{\n  \"pay_gym\"   : true,\n  \"guest_bookings\" : 0,\n  \"model\" : \"event\",\n  \"model_id\" : \"5b17f32f231d2e02b53f7493\"\n}"}, "url": {"raw": "https://gf-api.aws.glofox.com/dev/2.0/bookings", "protocol": "https", "host": ["gf-api", "aws", "glofox", "com"], "path": ["dev", "2.0", "bookings"]}}, "response": []}, {"name": "CreateMember", "request": {"method": "POST", "header": [{"key": "x-glofox-branch-id", "value": "5b17b8bbec0a6d0d7a08b8f3"}, {"key": "x-glofox-api-token", "value": "RpjVR+OZOWbJlAZvApWu6JrlowYUM/O3r94bBIeIGGI="}, {"key": "x-api-key", "value": "ZNk57dkXnegwZL3KoEBl6jMEPBSSoDQ27IFex8Z5"}], "body": {"mode": "raw", "raw": "{\n  \"first_name\" : \"<PERSON>\",\n  \"last_name\"  : \"<PERSON>\",\n  \"email\"      : \"<EMAIL>\",\n  \"phone\"      : \"123445675\",\n  \"password\"   : \"12345678\",\n  \"receive_marketing\" : true\n}"}, "url": {"raw": "https://gf-api.aws.glofox.com/dev/2.0/register", "protocol": "https", "host": ["gf-api", "aws", "glofox", "com"], "path": ["dev", "2.0", "register"]}}, "response": []}]}