<?php

namespace Glofox\Payments\Providers\Gateway\ProtoParsers;

use Glofox\Payments\Entities\Payout\Models\PayoutTransaction as ProviderPayoutTransaction;
use Glofox\Payments\Util;
use Google\Protobuf\Internal\RepeatedField;
use GRPC\Payments\TransactionService\PayoutTransaction as PayoutTransaction;

/**
 * Class PayoutTransactionParser.
 */
class PayoutTransactionParser
{
    private \Glofox\Payments\Providers\Gateway\ProtoParsers\TransactionParser $transactionParser;

    /**
     * TransactionParser constructor.
     */
    public function __construct()
    {
        $this->transactionParser = new TransactionParser();
    }

    /**
     * @param PayoutTransaction $payoutTransaction
     *
     * @return ProviderPayoutTransaction
     */
    public function parseFromProtoMessage($payoutTransaction): ProviderPayoutTransaction
    {
        $providerPayoutTransaction = new ProviderPayoutTransaction();

        $grossAmount = $payoutTransaction->getGrossAmount();
        $fee = $payoutTransaction->getFee();
        $netAmount = $payoutTransaction->getNetAmount();

        if (Util::isFractionalCurrency($payoutTransaction->getTransaction()->getCurrency())) {
            $grossAmount = Util::convertFromCents($payoutTransaction->getGrossAmount());
            $fee = Util::convertFromCents($payoutTransaction->getFee());
            $netAmount = Util::convertFromCents($payoutTransaction->getNetAmount());
        }

        $providerPayoutTransaction
            ->setID($payoutTransaction->getID())
            ->setGrossAmount($grossAmount)
            ->setFee($fee)
            ->setNetAmount($netAmount);

        if (!empty($payoutTransaction->getTransaction())) {
            $transaction = $this->transactionParser->parseFromProtoMessage($payoutTransaction->getTransaction());
            $providerPayoutTransaction->setTransaction($transaction);
        }

        return $providerPayoutTransaction;
    }

    /**
     * @param RepeatedField|PayoutTransaction[] $payoutTransactions
     *
     * @return ProviderPayoutTransaction[]
     */
    public function parseFromProtoMessageCollection(RepeatedField $payoutTransactions): ?array
    {
        $providerPayoutTransactions = null;

        if (empty($payoutTransactions)) {
            return $providerPayoutTransactions;
        }

        foreach ($payoutTransactions as $payoutTransaction) {
            $providerPayoutTransactions[] = $this->parseFromProtoMessage($payoutTransaction);
        }

        return $providerPayoutTransactions;
    }
}
