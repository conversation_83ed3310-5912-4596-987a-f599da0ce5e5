<?php

declare(strict_types=1);

namespace Glofox\Domain\AppointmentSlots\Http;

use App;
use AppController;
use Carbon\Carbon;
use Glofox\Domain\AppointmentSlots\Exceptions\AppointmentSlotNotDeletedException;
use Glofox\Domain\AppointmentSlots\Http\Requests\CreateAppointmentSlotRequest;
use Glofox\Domain\AppointmentSlots\Http\Requests\DeleteAppointmentSlotRequest;
use Glofox\Domain\AppointmentSlots\Http\Requests\GetAppointmentSlotRequest;
use Glofox\Domain\AppointmentSlots\Http\Requests\UpdateAppointmentSlotRequest;
use Glofox\Domain\AppointmentSlots\Http\Responses\CreateAppointmentSlotReponse;
use Glofox\Domain\AppointmentSlots\Http\Responses\DeleteAppointmentSlotResponse;
use Glofox\Domain\AppointmentSlots\Http\Responses\GetAppointmentSlotResponse;
use Glofox\Domain\AppointmentSlots\Http\Responses\UpdateAppointmentSlotResponse;
use Glofox\Domain\AppointmentSlots\UseCase\CreateAppointmentSlot;
use Glofox\Domain\AppointmentSlots\UseCase\DeleteAppointmentSlot;
use Glofox\Domain\AppointmentSlots\UseCase\DeleteAppointmentSlotParams;
use Glofox\Domain\AppointmentSlots\UseCase\GetAppointmentSlot;
use Glofox\Domain\AppointmentSlots\UseCase\SaveAppointmentSlotParams;
use Glofox\Domain\AppointmentSlots\UseCase\UpdateAppointmentSlot;
use Glofox\Domain\AppointmentSlots\UseCase\UpdateAppointmentSlotParams;
use Glofox\Domain\Authentication\Auth;
use Glofox\Domain\TimeSlotPatterns\Repositories\TimeSlotPatternsRepository;
use Glofox\Domain\TimeSlots\Repositories\TimeSlotRepository;
use Illuminate\Http\JsonResponse;

App::uses('AppController', 'Controller');

class AppointmentSlotsController extends AppController
{
    private TimeSlotPatternsRepository $timeSlotPatternsRepository;
    private TimeSlotRepository $timeSlotRepository;

    public function __construct($request = null, $response = null)
    {
        parent::__construct($request, $response);

        $this->timeSlotPatternsRepository = app()->make(TimeSlotPatternsRepository::class);
        $this->timeSlotRepository = app()->make(TimeSlotRepository::class);
    }

    /**
     * @param CreateAppointmentSlotRequest $request
     * @param CreateAppointmentSlot $useCase
     * @return JsonResponse
     */
    public function create(
        CreateAppointmentSlotRequest $request,
        CreateAppointmentSlot $useCase
    ): JsonResponse {
        $timeSlotPattern = $this->timeSlotPatternsRepository->getById($request->appointmentId());

        $params = new SaveAppointmentSlotParams(
            $timeSlotPattern,
            $request->branchId(),
            Auth::user()->namespace(),
            Carbon::createFromTimestamp($request->timeStart()),
            $request->staffId(),
            $request->private(),
            $request->booked()
        );

        $response = $useCase->execute($params);
        $responseArray = $response->toArray();
        $responseArray['appointment'] = $timeSlotPattern;

        return (new CreateAppointmentSlotReponse($responseArray))->toJsonResponse();
    }

    public function get(
        GetAppointmentSlotRequest $request,
        GetAppointmentSlot $useCase
    ): JsonResponse {
        $appointmentSlot = $useCase->execute($request->slotId());

        return (new GetAppointmentSlotResponse(
            $appointmentSlot->toArray()
        ))->toJsonResponse();
    }

    public function update(
        UpdateAppointmentSlotRequest $request,
        UpdateAppointmentSlot $useCase
    ): JsonResponse {
        // If time_start is not provided, we need to get the current time_start from the existing slot
        $timeStart = $request->timeStart();
        if ($timeStart === null) {
            $existingSlot = $this->timeSlotRepository->getActiveSlotById($request->slotId());
            $timeStart = $existingSlot->timeStart();
        }

        $params = new UpdateAppointmentSlotParams(
            $request->slotId(),
            $request->locationId(),
            $timeStart,
            $request->staffId(),
            $request->active()
        );

        $result = $useCase->execute($params);

        return (new UpdateAppointmentSlotResponse($result->toArray()))->toJsonResponse();
    }


    /**
     * @param DeleteAppointmentSlotRequest $request
     * @param DeleteAppointmentSlot $useCase
     * @return DeleteAppointmentSlotResponse
     * @throws AppointmentSlotNotDeletedException
     */
    public function delete(
        DeleteAppointmentSlotRequest $request,
        DeleteAppointmentSlot $useCase
    ): DeleteAppointmentSlotResponse {

        $useCase->execute(
            new DeleteAppointmentSlotParams($request->slotId())
        );

        return new DeleteAppointmentSlotResponse();
    }
}