<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Infrastructure\GoogleRecaptcha\Factory;

use Glofox\Domain\Recaptcha\Exception\EmptyRecaptchaToken;
use Glofox\Infrastructure\GoogleRecaptcha\Factory\VerifyRequestFactory;

\App::import('Test/Case', 'GlofoxTestCase');

class VerifyRequestFactoryTest extends \GlofoxTestCase
{
    private VerifyRequestFactory $verifyRequestFactory;

    public function setUp(): void
    {
        parent::setUp();

        $this->verifyRequestFactory = new VerifyRequestFactory('secret-stub');
    }

    public function test_it_fails_when_recaptcha_token_is_empty(): void
    {
        $this->expectException(EmptyRecaptchaToken::class);
        $this->expectExceptionMessage('Recaptcha token is empty');

        $this->verifyRequestFactory->create("");
    }

    public function test_it_creates_a_request(): void
    {
        $request = $this->verifyRequestFactory->create("recaptcha-token-stub");

        $payload = $request->getPayload();

        self::assertSame('recaptcha-token-stub', $payload['response']);
        self::assertSame('secret-stub', $payload['secret']);
    }
}