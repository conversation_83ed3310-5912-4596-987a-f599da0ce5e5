<?php

namespace Glofox\Payments\Providers\Gateway\ProtoParsers;

use Glofox\Payments\Entities\TerminalReader\Models\TerminalReader as ProviderTerminalReader;
use GRPC\Payments\AccountService\TerminalReader;

class TerminalReaderParser
{
    public function parseFromProtoMessage(TerminalReader $terminalReader): ProviderTerminalReader
    {
        $providerTerminalReader = new ProviderTerminalReader();
        $providerTerminalReader->setId((string) $terminalReader->getID());
        $providerTerminalReader->setLabel($terminalReader->getLabel());
        $providerTerminalReader->setDeviceType($terminalReader->getDeviceType());
        $providerTerminalReader->setSerialNumber($terminalReader->getSerialNumber());
        return $providerTerminalReader;
    }
}