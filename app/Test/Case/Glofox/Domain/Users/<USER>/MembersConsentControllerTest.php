<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\Users\Http;

use CakeTestCases\Glofox\Domain\Authentication\Token\TokenGeneration;
use Glofox\Domain\Branches\Repositories\BranchesRepository;
use Glofox\Domain\Integrators\Repositories\IntegratorsRepository;
use Glofox\Domain\Integrators\Search\Expressions\IntegratorName;
use Glofox\Repositories\Search\Expressions\Shared\Id;

\App::import('Test/Case', 'GlofoxControllerTestCase');

class MembersConsentControllerTest extends \GlofoxControllerTestCase
{
    use TokenGeneration;

    public $fixtures = [
        'app.branch',
        'app.user',
        'app.integrator',
    ];

    public function test_it_should_update_members_consent(): void
    {
        $branch = app()->make(BranchesRepository::class)
            ->addCriteria(new Id('49a7011a05c677b9a916612a'))
            ->firstOrFail();

        $integrator = app()->make(IntegratorsRepository::class)
            ->addCriteria(new IntegratorName('INTEGRATOR-TEST-TOKEN'))
            ->firstOrFail();

        $token = $this->getTokenGenerator()->generateForIntegratorAndBranch(
            $integrator,
            $branch
        );

        $this->loginByToken($token);

        $userId = '610005a8f676d06319f9e2b9';

        $payload = [
            'consent' => [
                'email' => [
                    'active' => false,
                ],
            ],
        ];

        $url = sprintf('/2.2/members/%s/consent', $userId);

        $this->testAction($url, ['data' => $payload, 'method' => 'PATCH']);

        $updatedUser = $this->fetchUser($userId);

        $this->assertFalse($updatedUser['consent']['email']['active']);
    }
}
