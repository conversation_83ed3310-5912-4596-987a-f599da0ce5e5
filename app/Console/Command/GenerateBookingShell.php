<?php

declare(strict_types=1);

use Glofox\Datasource\Exceptions\InvalidMongoIdException;
use Glofox\Domain\Bookings\Commands\GenerateBooking;
use Glofox\Domain\Bookings\Internal\Parameters\GenerateBookingParameters;
use Glofox\Domain\Branches\Exceptions\BranchNotFoundException;
use Glofox\Domain\Branches\Models\Branch as BranchModel;
use Glofox\Domain\Branches\Repositories\BranchesRepository;
use Glofox\Domain\Events\Exceptions\EventNotFoundException;
use Glofox\Domain\Events\Models\Event as EventModel;
use Glofox\Domain\Events\Repositories\EventsRepository;
use Glofox\Domain\Users\Exceptions\UserNotFoundException;
use Glofox\Domain\Users\Models\User as UserModel;
use Glofox\Domain\Users\Repositories\UsersRepository;
use Psr\Log\LoggerInterface;

App::uses('Shell', 'Console');

class GenerateBookingShell extends Shell
{
    private LoggerInterface $logger;
    private UsersRepository $usersRepository;
    private EventsRepository $eventsRepository;
    private BranchesRepository $branchesRepository;
    private GenerateBooking $generateBooking;

    public function __construct($stdout = null, $stderr = null, $stdin = null)
    {
        parent::__construct($stdout, $stderr, $stdin);

        $this->logger = app()->make(LoggerInterface::class);
        $this->usersRepository = app()->make(UsersRepository::class);
        $this->eventsRepository = app()->make(EventsRepository::class);
        $this->branchesRepository = app()->make(BranchesRepository::class);
        $this->generateBooking = app()->make(GenerateBooking::class);
    }

    public function getOptionParser()
    {
        $parser = parent::getOptionParser();

        $parser->addOption('branchId', [
            'short' => 'b',
            'help' => 'Branch ID',
            'required' => true
        ]);

        $parser->addOption('eventId', [
            'short' => 't',
            'help' => 'Batch ID',
            'required' => true
        ]);

        $parser->addOption('userId', [
            'short' => 'u',
            'help' => 'User ID',
            'required' => true
        ]);

        $parser->addOption('batchId', [
            'short' => 'd',
            'help' => 'Batch ID',
            'required' => false
        ]);

        return $parser;
    }

    public function main(): void
    {
        $this->logger->info('[GenerateBooking] Started');

        try {
            $branch = $this->resolveBranch();
            $event = $this->resolveEvent();
            $user = $this->resolveUser();
            $bot = $this->resolveBot($branch);

            $params = new GenerateBookingParameters(
                $event,
                $user,
                null,
                $branch,
                false,
                PaymentMethods::COMPLIMENTARY,
                $bot,
                false,
                false,
                (string) $this->params['batchId']
            );

            $result = $this->generateBooking->generate($params);
            if (!$result['success']) {
                $this->logger->error('[GenerateBooking] An error occurred', [
                    'message' => $result['message'],
                ]);
            }
        } catch (Throwable $exception) {
            $this->logger->error('[GenerateBooking] An error occurred', [
                'message' => $exception->getMessage(),
            ]);
        }

        $this->logger->info('[GenerateBooking] Finished');
    }

    /**
     * @throws BranchNotFoundException
     * @throws InvalidMongoIdException
     */
    private function resolveBranch(): BranchModel
    {
        return $this->branchesRepository->getById((string) $this->params['branchId']);
    }

    /**
     * @throws EventNotFoundException
     * @throws InvalidMongoIdException
     */
    private function resolveEvent(): EventModel
    {
        return $this->eventsRepository->getById((string) $this->params['eventId']);
    }

    /**
     * @throws InvalidMongoIdException
     * @throws UserNotFoundException
     */
    private function resolveUser(): UserModel
    {
        return $this->usersRepository->getById((string) $this->params['userId']);
    }

    private function resolveBot(BranchModel $branch): UserModel
    {
        return UserModel::make([
            '_id' => UserModel::WORKER_FAKE_ID,
            'namespace' => $branch->namespace(),
            'branch_id' => $branch->id(),
            'first_name' => 'Auto-Booking',
            'last_name' => 'From ProcessBookingShell',
            'type' => \UserType::SUPERADMIN,
            'active' => true,
        ]);
    }
}
