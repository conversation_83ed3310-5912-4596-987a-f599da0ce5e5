<?php

use Carbon\Carbon;
use Glofox\AuditLog\Models\TrackableDataGateway;
use Glofox\Date\IntervalType;
use Glofox\Domain\Bookings\Models\Booking;
use Glofox\Domain\Bookings\Repositories\BookingsRepository;
use Glofox\Domain\Credits\Factories\CreditPackSourceFactory;
use Glofox\Domain\Credits\Models\CreditPack;
use Glofox\Domain\Credits\Models\CreditPackSource;
use Glofox\Domain\Credits\Models\CreditPackSourceType;
use Glofox\Domain\Credits\Repositories\CreditsRepository;
use Glofox\Domain\Credits\Search\Expressions\StartDate;
use Glofox\Domain\Credits\Search\Expressions\UserMembershipId;
use Glofox\Domain\Credits\Source\SourceIsCancelledBookingMarker;
use Glofox\Domain\Credits\Transformers\CreditEndDateIntervalParams;
use Glofox\Domain\Credits\Transformers\CreditEndDateRangeParams;
use Glofox\Domain\Credits\Transformers\CreditEndDateTransformer;
use Glofox\Domain\Credits\Validation\Schema\CompositeCreditPackSchemaValidator;
use Glofox\Domain\Credits\Validation\Validator as CreditsValidator;
use Glofox\Domain\UserCreditsHistory\UseCase\SaveUserCreditsHistory;
use Glofox\Domain\UserCreditsHistory\UseCase\SaveUserCreditsHistoryParams;
use Glofox\Domain\Users\Events\LastCreditWasSpent;
use Glofox\Domain\Users\Exceptions\CurrentBranchIdException;
use Glofox\Domain\Users\Exceptions\CurrentBranchIdFromTokenException;
use Glofox\Domain\Users\Models\User;
use Glofox\Domain\Users\Models\UserMembership;
use Glofox\Domain\Users\Models\UserMembershipSubscription;
use Glofox\Domain\Users\Repositories\UsersRepository;
use Glofox\Domain\Users\Search\Expressions\UserId;
use Glofox\Events\EventManager;
use Glofox\Models\Validators\Schema\SchemaValidator;
use Glofox\Repositories\Search\Expressions\Shared\Active;
use Glofox\Repositories\Search\Expressions\Shared\Id;
use Psr\Log\LoggerInterface;

class UserCredit extends TrackableDataGateway
{
    public $primaryKey = '_id';

    public array $mongoSchema = [
        'namespace' => [
            'type' => 'string'
        ],
        'branch_id' => [
            'type' => 'string'
        ],
        'user_id' => [
            'type' => 'string'
        ],
        'model' => [
            'type' => 'string'
        ],
        'category_id' => [
            'type' => 'string'
        ],
        'model_ids' => [
            'type' => 'array'
        ],
        'user_membership_id' => [
            'type' => 'string'
        ],
        'membership_id' => [
            'type' => 'string'
        ],
        'membership_name' => [
            'type' => 'string'
        ],
        'plan_name' => [
            'type' => 'string'
        ],
        'plan_code' => [
            'type' => 'string'
        ],
        'num_sessions' => [
            'type' => 'integer'
        ],
        'available' => [
            'type' => 'integer'
        ],
        'active' => [
            'type' => 'boolean'
        ],
        'bookings' => [
            'type' => 'array'
        ],
        'start_date' => [
            'type' => 'date'
        ],
        'end_date' => [
            'type' => 'date'
        ],
        'created' => [
            'type' => 'datetime'
        ],
        'starts_on' => [
            'type' => 'string'
        ],
        'expiry' => [
            'type' => 'array'
        ],
        'modified' => [
            'type' => 'datetime'
        ],
        'source' => [
            'type' => [
                'type' => CreditPackSourceType::class
            ],
            'staff_id' => [
                'type' => 'string'
            ]
        ],
    ];

    /**
     * @var array<int, class-string<SchemaValidator>>
     */
    protected static array $schemaValidators = [
        CompositeCreditPackSchemaValidator::class,
    ];

    public $validate = [
        'num_sessions' => [
            'isNumSessionsSmallerOrEqualThanBookingsSize' => [
                'rule' => 'isNumSessionsSmallerOrEqualThanBookingsSize',
                'message' => 'The number of sessions cannot be smaller that the amount of bookings.',
                'on' => 'update',
            ],
        ],
    ];

    public function getAll($params = [])
    {
        $params['include'] = isset($params['include']) ? explode(',', $params['include']) : [];
        $fields = [
            'namespace',
            'branch_id',
            'model',
            'model_ids',
            'category_id',
            'num_sessions',
            'active',
            'bookings',
            'start_date',
            'end_date',
            'created',
            'modified',
            'user_id',
            'membership_id',
        ];
        $query = $this->buildPaginatedConditions($fields);

        //If staff filter by parameter user_id
        $query['conditions']['user_id'] = $this->loggedUser['_id'];
        if (!in_array($this->loggedUser['type'], [UserType::MEMBER, UserType::GUEST]) && isset($params['user_id'])) {
            $query['conditions']['user_id'] = $params['user_id'];
            if ('any' == $params['user_id']) {
                unset($query['conditions']['user_id']);
            }
        }

        $this->Branch = ClassRegistry::init('Branch');
        $timezone = $this->Branch->getTimeZone($this->loggedUser['branch_id']);

        // Fetch user to define if is under subscription
        /** @var User $user */
        $user = app()->make(UsersRepository::class)
            ->addCriteria(new Id($query['conditions']['user_id']))
            ->firstOrFail();
        $userIsUnderSubscription = $user->membership()->hasSubscription();

        //Make sure we exclude expired credits
        $end = [
            '$or' => [
                ['end_date' => ['$gte' => new MongoDate()]],
                ['end_date' => ['$exists' => false]],
                ['end_date' => null],
            ]
        ];

        array_push($query['conditions']['$and'], $end);

        // when a event_id or a timeslot_id passed, the related model is fetched
        // the query is modified by validating that the credits should have
        // the model_id in the model_ids field and at least one of its categories
        if (!empty($params['event_id']) || !empty($params['timeslot_id'])) {
            if (!empty($params['event_id'])) {
                $event = ClassRegistry::init('Event')->findById($params['event_id']);
                $model = ClassRegistry::init('Program')->findById($event['Event']['program_id']);
                if (empty($event) || empty($model)) {
                    return ['total_value' => 0];
                }
                $modelName = 'programs';
                $modelId = $model['Program']['_id'];
                $modelCategories = $model['Program']['categories'];
                $modelStartDate = $event['Event']['time_start'];
            } else {
                $timeslot = ClassRegistry::init('TimeSlot')->findById($params['timeslot_id']);
                $model = ClassRegistry::init('TimeSlot')->findRelatedModel((string)$params['timeslot_id']);
                if (empty($timeslot) || empty($model)) {
                    return ['total_value' => 0];
                }
                $modelName = $timeslot['TimeSlot']['model'];
                $modelId = $model['_id'];
                $modelCategories = $model['categories'];
                $modelStartDate = $timeslot['TimeSlot']['time_start'];
            }

            // We are setting the model start date as the beginning of the day to fetch the credits
            // because they are stored as dates, not datetimes
            $carbonModelStartDateStartOfDay = Carbon::parse($modelStartDate, $timezone)->startOfDay();
            $carbonModelStartDateEndOfDay = Carbon::parse($modelStartDate, $timezone)->endOfDay();

            $modelCategories[] = null;
            $query['conditions']['model'] = $modelName;
            $query['conditions']['model_ids'] = ['$in' => [$modelId, null]];
            $query['conditions']['category_id'] = ['$in' => $modelCategories];

            // This is to includes those credits that are "first booking membership" type
            $startDate = [
                '$or' => [
                    ['start_date' => ['$lte' => new MongoDate($carbonModelStartDateEndOfDay->getTimestamp())]],
                    ['start_date' => ['$exists' => false]],
                    ['start_date' => null],
                    [
                        '$and' => [
                            ['starts_on' => MembershipPlanStartsOn::FIRST_BOOKING_DATE],
                            [
                                '$or' => [
                                    ['start_date' => null],
                                    [
                                        'start_date' => [
                                            '$gte' => new MongoDate(
                                                strtotime($carbonModelStartDateEndOfDay->getTimestamp())
                                            )
                                        ]
                                    ],
                                ],
                            ],
                        ],
                    ],
                ],
            ];
            array_push($query['conditions']['$and'], $startDate);

            // If user is not under subscription, them filter credits by end_date
            if (!$userIsUnderSubscription) {
                $expiryDate = [
                    '$or' => [
                        ['end_date' => ['$gte' => new MongoDate($carbonModelStartDateStartOfDay->getTimestamp())]],
                        ['end_date' => ['$exists' => false]],
                        ['end_date' => null],
                    ],
                ];
                array_push($query['conditions']['$and'], $expiryDate);
            }
        }

        //Only filter usable credits (where there are available uses)
        $query['conditions']['$and'][] = [
            '$expr' => ['$lt' => [['$size' => '$bookings'], '$num_sessions']]
        ];

        $excludedCreditPackIds = [];

        if (
            (!empty($params['event_id']) ||
                !empty($params['timeslot_id'])) && !empty($carbonModelStartDateStartOfDay)
        ) {
            // Normalize the records [{n}.UserCredit] before crediting a CreditPacksCollection instance
            $credits = $this->find('all', $query);
            $credits = \Hash::extract($credits, sprintf('{n}.%s', $this->alias));
            $modelHydrator = app()->make(\Glofox\Traversable\Hydrators\ModelHydrator::class);
            foreach ($credits as &$row) {
                $row = $modelHydrator->expects(CreditPack::class)->fill($row);
            }
            /**
             * The following list of credit packs should represent credits that were marked as 'Eligible' by the previous
             * Query logic, but after applying ExcludeCreditPacksGivenSessionDate we determined that such credits can
             * Not be use because they would shift the original credit-pack outside of the allowed time-frame.
             */
            $excludedCreditPackIds = app()->make(
                Glofox\Domain\Credits\Services\ExcludeCreditPacksGivenSessionDate::class
            )
                ->execute(
                    \Glofox\Domain\Credits\CreditPacksCollection::make($credits),
                    Carbon::parse($carbonModelStartDateStartOfDay)
                );
        }

        $list = $this->paginate($query);

        $this->Membership = ClassRegistry::init('Membership');
        $membershipsMap = $this->Membership->getMap($this->loggedUser['namespace'], [$this->loggedUser['branch_id']]);

        //Iterate through list and calculate individual and global availability, set total value in wrapper as total_value
        $available = 0;
        foreach ($list['data'] as $itemIndex => &$item) {
            if (in_array(new MongoId($item['_id']), $excludedCreditPackIds)) {
                unset($list['data'][$itemIndex]);
                continue;
            }

            $item['available'] = $item['num_sessions'] - (is_countable($item['bookings']) ?
                    count($item['bookings']) :
                    0);
            $available += $item['available'];
            if (!empty($item['membership_id']) && isset($membershipsMap[$item['membership_id']])) {
                $item['membership_name'] = $membershipsMap[$item['membership_id']]['name'];
            }

            if (in_array('bookings', $params['include'])) {
                $item['bookings_obj'] = $this->attachBookings($item['bookings']);
            }
        }
        $list['total_value'] = $available;

        return $list;
    }

    /**
     * @SWG\Get(
     *     path="/credits/{id}",
     *     summary="Find credit by id",
     *     description="Returns a single credit by id",
     *     operationId="getCreditById",
     *     tags={"Credits"},
     *     produces={"application/json"},
     *     @SWG\Parameter(
     *         description="Unique identifier of the credit ex. 12f3d21fd3ef13f123d12e312",
     *         in="path",
     *         name="id",
     *         required=true,
     *         type="string"
     *     ),
     *     @SWG\Response(
     *         response=200,
     *         description="successful operation",
     *         @SWG\Schema(ref="#/definitions/UserCredit")
     *     ),
     *     @SWG\Response(
     *         response="400",
     *         description="Invalid credit id"
     *     ),
     *     @SWG\Response(
     *         response="403",
     *         description="Your API token is invalid or does not grant you privileges for this resource"
     *     ),
     *     @SWG\Response(
     *         response="404",
     *         description="Credit not found"
     *     ),
     *     security={{
     *       "api_key":{}
     *     }}
     * )
     *
     * @param string $identifier [description]
     * @param array $params [description]
     *
     * @return array
     */
    public function getById($identifier, $params = [])
    {
        $conditions = $this->injector->getSingleRecordConditions();

        $conditions = [
            'conditions' => $conditions,
            'fields' => [
                'namespace',
                'branch_id',
                'model',
                'model_ids',
                'category_id',
                'num_sessions',
                'active',
                'bookings',
                'start_date',
                'end_date',
                'created',
                'modified',
                'user_id',
                'membership_id'
            ],
        ];

        $credit = $this->find('first', $conditions);
        $credit = $this->mapObject($credit);

        //Calculate availability
        $credit['available'] = $credit['num_sessions'] - (is_countable($credit['bookings']) ?
                count($credit['bookings']) :
                0);

        // Add Membership Name
        if (isset($credit['membership_id'])) {
            $this->Membership = ClassRegistry::init('Membership');
            $membership = $this->Membership->findByIdSetFields($credit['membership_id'], ['name']);
            $membershipName = $membership['Membership']['name'] ?? null;
            $credit['membership_name'] = $membershipName;
        }

        return $credit;
    }

    /**
     * Create.
     *
     * @param array $data [description]
     * @TODO: implement
     */
    public function post($data)
    {
    }

    /**
     * Update.
     *
     * @param string $identifier
     * @param array $data [description]
     * @TODO: implement
     */
    public function patchById($identifier, $data)
    {
    }

    /**
     * Delete.
     *
     * @param string $identifier
     * @TODO: implement the method
     */
    public function deleteById($identifier)
    {
    }

    // CALLBACKS
    public function afterFind($results, $primary = false)
    {
        try {
            $results = $this->convertFieldsToDate($results, ['created', 'modified'], 'datetime');
        } catch (\Exception $e) {
            $this->log($e->getMessage());
            $this->log($e->getTraceAsString());
        }

        try {
            $results = $this->convertFieldsToDate($results, ['start_date', 'end_date'], 'datetime');
        } catch (\Exception $e) {
            $this->log($e->getMessage());
            $this->log($e->getTraceAsString());
        }

        foreach ($results as $key => $val) {
            if (isset($val[$this->alias]['branch_id'])) {
                try {
                    $results[$key][$this->alias]['branch_id'] = $this->getFlattenBranchIdUsingTokenFromRecord($val);
                } catch (CurrentBranchIdException|CurrentBranchIdFromTokenException $e) {
                    $rawBranchId = $val[$this->alias]['branch_id'];
                    $results[$key][$this->alias]['branch_id'] = is_array($rawBranchId) && !empty($rawBranchId[0]) ?
                        $rawBranchId[0] :
                        '';
                }
            }
        }

        return $results;
    }

    /**
     * @return bool
     */
    public function beforeSave($options = [])
    {
        $this->convertFieldsToMongoDate(['created', 'modified', 'start_date', 'end_date']);
        if (!isset($this->data[$this->alias]['bookings'])) {
            $this->data[$this->alias]['bookings'] = [];
        }

        // If this is a update and it doesn't have the override branch id attribute,
        // we shouldn't let them modify the branch_id
        if (!empty($this->data[$this->alias]['_id']) && empty($this->data[$this->alias]['override_branch_id'])) {
            unset($this->data[$this->alias]['branch_id']);
            unset($this->data[$this->alias]['override_branch_id']);
        }

        \DataSourceHelper::integer($this->data[$this->alias], 'num_sessions', 0, null);

        if ($this->isInserting()) {
            return true;
        }

        $this->setModifiedDate();

        return parent::beforeSave($options);
    }

    /**
     * @return bool|mixed
     * @throws \Glofox\AuditLog\Validation\Exceptions\InvalidLogDataException
     */
    protected function logActivity()
    {
        $creditPackBefore = isset($this->getOldData()['UserCredit']) ?
            CreditPack::make($this->getOldData()['UserCredit']) :
            null;
        $creditPackAfter = isset($this->getNewData()['UserCredit']) ?
            CreditPack::make($this->getNewData()['UserCredit']) :
            null;

        app()
            ->make(SaveUserCreditsHistory::class)
            ->execute(new SaveUserCreditsHistoryParams($creditPackBefore, $creditPackAfter));

        return parent::logActivity();
    }

    // Model Validation Functions
    public function isNumSessionsSmallerOrEqualThanBookingsSize($check)
    {
        $num_sessions = Hash::get($this->data[$this->alias], 'num_sessions');
        $bookings = Hash::get($this->data[$this->alias], 'bookings');

        return $num_sessions >= (is_countable($bookings) ? count($bookings) : 0);
    }

    public function assignCreditsToUser(
        User $user,
        array $credits,
        array $dateRange,
        string $membershipId,
        ?string $userMembershipId,
        string $startsOn,
        float $percentage,
        bool $inAdvance,
        ?CreditPackSource $source = null,
        bool $isLastCycle = false
    ) {
        /** @var LoggerInterface $logger */
        $logger = app()->make(LoggerInterface::class);

        /** @var CreditsRepository $creditsRepository */
        $creditsRepository = app()->make(CreditsRepository::class);

        $membershipStartDate = $dateRange['start_date'] ?? null;
        $membershipExpiryDate = $dateRange['expiry_date'] ?? null;

        if (MembershipPlanStartsOn::FIRST_BOOKING_DATE !== $startsOn) {
            $membershipStartDate ??= Carbon::yesterday('UTC');
        }

        $userMembership = $user->membership();

        $logger->info(sprintf('Start assigning credits to User %s', $user->id()), [
            'new_membership_start_date' => $membershipStartDate instanceof Carbon ?
                $membershipStartDate->toDateTimeString() : $membershipStartDate,
            'new_membership_expiry_date' => $membershipExpiryDate instanceof Carbon ?
                $membershipExpiryDate->toDateTimeString() : $membershipExpiryDate,
            'current_membership_start_date' => $userMembership->startDate()->toDateTimeString(),
            'current_membership_expiry_date' => $userMembership->expiryDate()->toDateTimeString(),
            'in_advance' => $inAdvance,
            'is_last_cycle' => $isLastCycle,
        ]);

        $branchesWithAccess = $userMembership->isRoaming() ? $user->branches() : $user->originBranchId();
        $branchesWithAccess = is_array($branchesWithAccess) && count($branchesWithAccess) === 1 ?
            $branchesWithAccess[0] :
            $branchesWithAccess;

        foreach ($credits as $credit) {
            $newCreditPackData = [
                'namespace' => $user->namespace(),
                'branch_id' => $branchesWithAccess,
                'user_id' => $user->id(),
                'membership_name' => $userMembership->membershipName(),
                'plan_name' => $userMembership->planName(),
                'plan_code' => $userMembership->planCode(),
                'model' => $credit['model'],
                'num_sessions' => ceil(($credit['num_sessions'] * $percentage) / 100),
                'active' => true,
                'bookings' => [],
                'start_date' => $membershipStartDate->toDateTimeString(),
                'in_advance' => $inAdvance,
            ];

            if (
                isset($credit['expiry']) && !empty($credit['expiry']['interval']) &&
                !empty($credit['expiry']['interval_count'])
            ) {
                $newCreditPackData['expiry'] = $credit['expiry'];

                if (MembershipPlanStartsOn::FIRST_BOOKING_DATE !== $startsOn) {
                    $credit['end_date'] = $this->calculateCreditEndDate(
                        $membershipId,
                        $user,
                        $userMembership,
                        $membershipStartDate,
                        $credit,
                        $inAdvance,
                        $isLastCycle
                    );
                }
            }

            if (!empty($credit['category_id'])) {
                $newCreditPackData['category_id'] = $credit['category_id'];
            }

            if (!empty($credit['entities']) || !empty($credit['model_ids'])) {
                $newCreditPackData['model_ids'] = $credit['model_ids'] ?? $credit['entities'];
            }

            if (!empty($membershipId)) {
                $newCreditPackData['membership_id'] = $membershipId;
            }

            if (!empty($membershipExpiryDate)) {
                $newCreditPackData['end_date'] = $membershipExpiryDate->toDateTimeString();
            }

            if (!empty($credit['end_date'])) {
                $newCreditPackData['end_date'] = $credit['end_date']->toDateTimeString();
            }

            if (!empty($startsOn)) {
                $newCreditPackData['starts_on'] = $startsOn;
            }

            if (!empty($userMembershipId)) {
                $newCreditPackData['user_membership_id'] = $userMembershipId;
            }

            if ($source !== null) {
                $newCreditPackData['source'] = $source->toArray();
            }

            if (isset($newCreditPackData['start_date'], $newCreditPackData['end_date'])) {
                $startDate = Carbon::parse($newCreditPackData['start_date']);
                $endDate = Carbon::parse($newCreditPackData['end_date']);
                if ($endDate < $startDate) {
                    $logger->info(
                        sprintf(
                            'User Credits: user %s has credits with an end date: %s before the start date: %s',
                            $user->toJson(),
                            $newCreditPackData['end_date'],
                            $newCreditPackData['start_date']
                        )
                    );
                }
            }

            $savedCreditPack = $creditsRepository->saveOrFail(CreditPack::make($newCreditPackData));

            $logger->info(
                sprintf(
                    'Credits %s assigned to User %s successfully',
                    json_encode($savedCreditPack->toArray(), JSON_PARTIAL_OUTPUT_ON_ERROR),
                    $user->id()
                )
            );
        }

        return [
            'success' => true
        ];
    }

    public function cancel_credits_for_membership($user_id)
    {
        $this->User = ClassRegistry::init('User');
        $user = is_array($user_id)
            ? $user_id
            : $this->User->findByIdSetFields($user_id, ['namespace', 'branch_id', 'membership']);
        $user_id = $user['User']['_id'];
        $membership_id = $user['User']['membership']['_id'] ?? null;
        $today = $this->getCurrentDate('date');

        $conditions = [
            'user_id' => $user_id,
            'membership_id' => $membership_id,
            '$or' => [
                [
                    'end_date' => [
                        '$gte' => new MongoDate(strtotime($today)),
                        '$exists' => true,
                    ],
                ],
                [
                    'end_date' => ['$exists' => false],
                ],
            ],
        ];

        $user_credits = $this->find('all', ['conditions' => $conditions]);
        if (!empty($user_credits)) {
            foreach ($user_credits as $user_credit) {
                $this->read(null, $user_credit['UserCredit']['_id']);
                $user_credit['UserCredit']['num_sessions'] = is_countable(
                    $user_credit['UserCredit']['bookings']
                ) ? count($user_credit['UserCredit']['bookings']) : 0;
                $this->save($user_credit);
            }
        }
    }

    /**
     * @deprecated Use responsible domain service {@see CreditGrantingService}
     */
    public function grantCreditsInAdvance(User $user): void
    {
        $logger = app()->make(LoggerInterface::class);
        $creditPackSourceFactory = app()->make(CreditPackSourceFactory::class);

        $membership = $user->membership();
        $subscription = $membership->subscription();

        if (
            !$this->hasValidMembershipForAdvancedCredits($membership, $user, $logger) ||
            !$this->hasValidSubscriptionForAdvancedCredits($subscription, $user, $logger)
        ) {
            return;
        }

        $nextPeriodStartDate = $membership->expiryDate()->addDay()->startOfDay();
        $nextPeriodExpiryDate = CreditEndDateTransformer::transform(
            $user->currentBranchId(),
            new CreditEndDateRangeParams(
                $nextPeriodStartDate,
                $membership->expiryDate()
            ),
            new CreditEndDateIntervalParams(
                $subscription->intervalCount(),
                IntervalType::fromString($subscription->interval()),
                true
            ),
            $membership->isInLastCycle()
        );

        if ($subscription->isProrated()) {
            $nextPeriodStartDate = $membership->startDate()->startOfMonth()->addMonth();
            $nextPeriodExpiryDate = $nextPeriodStartDate->copy()->endOfMonth();
        }

        $hasCreditForNextCycle = $this->hasCreditsForNextCycle($membership, $user, $nextPeriodStartDate);

        if ($hasCreditForNextCycle) {
            $logger->info(sprintf('Credits for user %s were previously renewed', $user->id()));
            return;
        }

        $dateRange = [
            'start_date' => $nextPeriodStartDate,
            'expiry_date' => $nextPeriodExpiryDate,
        ];
        $percentage = 100;
        $membershipPlanStartsOn = MembershipPlanStartsOn::PURCHASE_DATE;
        $assigningCreditsInAdvance = true;

        $source = $creditPackSourceFactory->createAdvancedWhenBooking();

        $this->assignCreditsToUser(
            $user,
            $subscription->credits()->toArray(),
            $dateRange,
            $membership->id(),
            $membership->userMembershipId(),
            $membershipPlanStartsOn,
            $percentage,
            $assigningCreditsInAdvance,
            $source
        );
    }

    public function createCreditForBookingCancelled(User $author, array $booking_data, int $total_bookings = 1)
    {
        $booking_data = $booking_data['Booking'];
        $this->Branch = ClassRegistry::init('Branch');
        $branch = $this->Branch->getBranchFeatures($booking_data['branch_id'], 'booking', false);
        $cancel_credit_option = !empty($branch['features']['booking']['cancel_credit_option'])
            ? $branch['features']['booking']['cancel_credit_option']
            : 'model_id';

        if ('events' == $booking_data['type']) {
            $this->Event = ClassRegistry::init('Event');
            $event = $this->Event->findByIdSetFields($booking_data['event_id'], ['program_id'], false);
            $model = 'programs';
            $model_ids = [$event['program_id']];

            $this->Program = ClassRegistry::init('Program');
            $program = $this->Program->findByIdSetFields($event['program_id'], ['categories'], false);
            $category_id = $program['categories'][0] ?? null;
        } elseif ('time_slots' == $booking_data['type']) {
            $ModelName = Inflector::classify($booking_data['model']);
            $model = $booking_data['model'];
            $model_ids = [$booking_data['model_id']];

            $this->$ModelName = ClassRegistry::init($ModelName);
            $entity = $this->$ModelName->findByIdSetFields($booking_data['model_id'], ['categories'], false);
            $category_id = $program['categories'][0] ?? null;
        }

        $credit_data = [
            'namespace' => $booking_data['namespace'],
            'branch_id' => $booking_data['branch_id'],
            'num_sessions' => $total_bookings,
            'active' => true,
            'start_date' => $this->getCurrentDate('date'),
            'bookings' => [],
            'model' => $model,
            //'model_ids' => $model_ids,
            //'category_id' => $category_id,
            'user_id' => $booking_data['user_id'],
        ];

        if ('category_id' == $cancel_credit_option) {
            $credit_data['category_id'] = $category_id;
        } elseif ('model_id' == $cancel_credit_option) {
            $credit_data['category_id'] = $category_id;
            $credit_data['model_ids'] = $model_ids;
        }

        $refunded_credit_number_of_days = $this->Branch->findRevertCreditExpiry($booking_data['branch_id']);
        if ($refunded_credit_number_of_days) {
            $credit_data['end_date'] = $this->addNumberOfDays(
                $this->getCurrentDate('date'),
                $refunded_credit_number_of_days,
                'date'
            );
        }

        $marker = app()->make(SourceIsCancelledBookingMarker::class);

        $booking = Booking::make($booking_data);
        $pack = CreditPack::make($credit_data);

        $marker->mark($pack, $author, $booking);

        $credit_data = $pack->toArray();

        $this->create();

        return $this->save($credit_data);
    }

    public function findCreditForBooking(
        $branch_id,
        $user_id,
        $session_date,
        $model,
        $model_id = '',
        $categories = null
    ) {
        $categories = is_array($categories) ? $categories : [];

        $conditions = $this->getCreditForBookingConditions(
            $branch_id,
            $user_id,
            $session_date,
            $model,
            $model_id,
            $categories
        );

        $order = ['model_ids' => -1, 'category_id' => -1];

        $credits = $this->find('all', [
            'conditions' => $conditions,
            'order' => $order,
        ]);

        return $this->prioritizeCredits($credits)->first();
    }

    /**
     * Is going to the receive the booking information and is going to determine
     * if the member has a credit for that booking and use it in case that he has one available.
     *
     * @param $branchId
     * @param $userId
     * @param $sessionDate
     * @param $model
     * @param string $modelId
     * @param null $categories
     * @param bool $memberIsUnderSubscriptionAndBookingForNextPeriod
     *
     * @return array
     */
    public function findCreditsForBooking(
        $branchId,
        $userId,
        $sessionDate,
        $model,
        $modelId = '',
        $categories = null,
        $memberIsUnderSubscriptionAndBookingForNextPeriod = false
    ) {
        $categories = is_array($categories) ? $categories : [];
        $numSessions = 0;
        $conditions = $this->getCreditForBookingConditions(
            $branchId,
            $userId,
            $sessionDate,
            $model,
            $modelId,
            $categories,
            $memberIsUnderSubscriptionAndBookingForNextPeriod
        );

        $order = [
            'model_ids' => -1,
            'category_id' => -1,
            'end_date' => 1,
        ];

        $credits = $this->find(
            'all',
            [
                'conditions' => $conditions,
                'order' => $order,
            ]
        );

        if ($credits) {
            foreach ($credits as $credit) {
                $creditNumSession = ($credit['UserCredit']['num_sessions'] - (is_countable(
                    $credit['UserCredit']['bookings']
                ) ? count($credit['UserCredit']['bookings']) : 0));
                $numSessions += $creditNumSession;
            }

            $credits = $this->prioritizeCredits($credits)->toArray();
        }

        return [
            'num_sessions' => $numSessions,
            'credits' => $credits,
        ];
    }

    /**
     * Is going to receive the booking information and is going to determine
     * if the member has a credit for that booking and use it in case that he has one available.
     *
     * @param string $branch_id id of the branch
     * @param string $user_id id of the member
     * @param string $model the model of what you are booking (programs, facilities, users)
     * @param string $model_id id of the program/facility/user that you are booking
     * @param array $categories categories of the program/facility/user that you are booking
     *
     * @return bool true if a credit was successfully used for that booking
     */
    public function findAllCredits(
        $branch_id,
        $user_id,
        $model,
        $model_id = '',
        $categories = null,
        $return_model = true
    ) {
        $categories = is_array($categories) ? $categories : [];

        $conditions = [
            'branch_id' => $branch_id,
            'user_id' => $user_id,
            'model' => $model,
            '$and' => [
                [
                    '$or' => [
                        [
                            'category_id' => null,
                            'model_ids' => [
                                '$exists' => true,
                                '$in' => [$model_id],
                            ],
                        ],
                        [
                            'category_id' => [
                                '$exists' => true,
                                '$in' => $categories,
                            ],
                            'model_ids' => [
                                '$exists' => true,
                                '$in' => [$model_id],
                            ],
                        ],
                        [
                            'category_id' => [
                                '$exists' => true,
                                '$in' => $categories,
                            ],
                            'model_ids' => ['$exists' => false],
                        ],
                        [
                            'category_id' => ['$exists' => false],
                            'model_ids' => ['$exists' => false],
                        ],
                    ],
                ],
                [
                    '$or' => [
                        [
                            'end_date' => [
                                '$gte' => new MongoDate(time()),
                                '$exists' => true,
                            ],
                        ],
                        [
                            'end_date' => ['$exists' => false],
                        ],
                        [
                            'end_date' => null,
                        ],
                    ],
                ],
                [
                    '$expr' => ['$lt' => [['$size' => '$bookings'], '$num_sessions']],
                ],
            ],
        ];

        $order = ['model_ids' => -1, 'category_id' => -1];
        $credits = $this->find('all', ['conditions' => $conditions, 'order' => $order]);

        if (!$return_model) {
            foreach ($credits as &$credit) {
                $credit = $credit[$this->alias];
            }
        }

        return $credits;
    }

    /**
     * @param $branch_id
     * @param $user_id
     * @param bool $return_model
     *
     * @return array|null
     */
    public function findAllAvailableByBranchIdAndUserId($branch_id, $user_id, $return_model = true)
    {
        $logger = app()->make(LoggerInterface::class);

        $startOfToday = Carbon::today()->startOfDay()->getTimestamp();

        $conditions = [
            'branch_id' => $branch_id,
            'user_id' => $user_id,
            '$and' => [
                [
                    '$or' => [
                        [
                            'end_date' => [
                                '$gte' => new MongoDate($startOfToday),
                                '$exists' => true,
                            ],
                        ],
                        [
                            'end_date' => ['$exists' => false],
                        ],
                        [
                            'end_date' => null,
                        ],
                    ],
                ],
                [
                    '$expr' => ['$lt' => [['$size' => '$bookings'], '$num_sessions']],
                ],
            ],
        ];

        $logger->info(
            sprintf(
                'Looking for credits using the following conditions: %s',
                json_encode($conditions, JSON_PARTIAL_OUTPUT_ON_ERROR)
            )
        );

        $order = ['start_date' => 1];
        $user_credits = $this->find('all', ['conditions' => $conditions, 'order' => $order]);

        $logger->info(
            sprintf('Found %s credits for user %s', is_countable($user_credits) ? count($user_credits) : 0, $user_id)
        );

        $credit_types = Configure::read('credits');
        if (!$return_model) {
            foreach ($user_credits as $key => &$credit) {
                $user_credits[$key]['UserCredit']['model_name'] = $credit_types[$user_credits[$key]['UserCredit']['model']];
                $credit = $credit[$this->alias];
            }
        }

        return $user_credits;
    }

    /**
     * [findAvailableByUserId description].
     *
     * @param [type] $user_id [description]
     *
     * @return [type] [description]
     */
    public function findAllByBranchIdAndUserId($branch_id, $user_id, $return_model = true)
    {
        $conditions = [
            'branch_id' => $branch_id,
            'user_id' => $user_id,
        ];
        $order = ['start_date' => -1];
        $user_credits = $this->find('all', ['conditions' => $conditions, 'order' => $order]);
        $credit_types = Configure::read('credits');
        if (!$return_model) {
            foreach ($user_credits as $key => &$credit) {
                $user_credits[$key]['UserCredit']['model_name'] = $credit_types[$user_credits[$key]['UserCredit']['model']];
                $credit = $credit[$this->alias];
            }
        }
        $this->Membership = ClassRegistry::init('Membership');
        $user_credits = $this->Membership->getMembershipPlanNames($user_credits);

        return $user_credits;
    }

    /**
     * [findAllByUserIdAndMembershipIdAndStartDate description].
     *
     * @param [type] $user_id               [description]
     * @param [type] $membership_id         [description]
     * @param [type] $membership_start_date [description]
     *
     * @return [type] [description]
     */
    public function findAllByUserIdAndMembershipIdAndStartDate($user_id, $membership_id, $membership_start_date)
    {
        $conditions = [
            'user_id' => $user_id,
            'membership_id' => $membership_id,
            'start_date' => new MongoDate(strtotime($membership_start_date)),
            'active' => true,
        ];
        $params = ['conditions' => $conditions];

        return $this->find('all', $params);
    }


    /**
     * @throws Exception
     */
    public function spendUserCredit($user_credit_id, $booking_id)
    {
        $user_credit = $this->findById($user_credit_id);
        $user_credit[$this->alias]['bookings'][] = $booking_id;

        $userCredit = $this->save($user_credit);

        $numberOfSessions = $userCredit[$this->alias]['num_sessions'];
        $numberOfBookings = is_countable($userCredit[$this->alias]['bookings']) ? count(
            $userCredit[$this->alias]['bookings']
        ) : 0;

        if ($numberOfBookings >= $numberOfSessions) {
            $eventManager = new EventManager();
            $eventManager->emit(LastCreditWasSpent::class, [$userCredit]);
        }

        return $userCredit;
    }

    /**
     * @throws Exception
     */
    public function spendUserCredits($userCredits, $bookingId, $totalBookings = 1)
    {
        $creditsTaken = 0;

        foreach ($userCredits as $userCredit) {
            $creditsRemaining = $userCredit[$this->alias]['num_sessions'] - (is_countable(
                $userCredit[$this->alias]['bookings']
            ) ? count(
                $userCredit[$this->alias]['bookings']
            ) : 0);
            $creditsToTake = ($creditsRemaining >= $totalBookings) ?
                $totalBookings :
                $creditsRemaining;

            for ($index = 1; $index <= $creditsToTake; ++$index) {
                $userCredit[$this->alias]['bookings'][] = $bookingId;
            }

            $this->read(null, $userCredit[$this->alias]['_id']);
            $userCredit = $this->updateStartDateOnBooking($userCredit, $bookingId);

            $saveCredit = $this->save($userCredit);

            $numberOfSessions = $saveCredit[$this->alias]['num_sessions'];
            $numberOfBookings = is_countable($saveCredit[$this->alias]['bookings']) ? count(
                $saveCredit[$this->alias]['bookings']
            ) : 0;

            if ($numberOfBookings >= $numberOfSessions) {
                $eventManager = new EventManager();
                $eventManager->emit(LastCreditWasSpent::class, [$userCredit]);
            }

            $creditsTaken = $creditsTaken + $creditsToTake;

            if ($creditsTaken >= $totalBookings) {
                break;
            }

            $totalBookings = $totalBookings - $creditsTaken;
        }

        return [$saveCredit, $creditsTaken];
    }

    public function updateStartDateOnBooking(array $credit, string $bookingId): array
    {
        $credit = CreditPack::make($credit['UserCredit']);
        if (!$credit->isStartingOnFirstBookingDate()) {
            return $credit->toLegacy();
        }

        /** @var Booking $booking */
        $booking = app()->make(BookingsRepository::class)
            ->addCriteria(new Id($bookingId))
            ->firstOrFail();

        $bookingStartDate = $booking->startingTime();

        if ($credit->hasStartDate() && $credit->startDate()->isBefore($bookingStartDate)) {
            return $credit->toLegacy();
        }

        $newCreditStartDate = $bookingStartDate->startOfDay();

        if ($credit->hasDuration()) {
            $newCreditExpiryDate = CreditEndDateTransformer::transform(
                $booking->user()->currentBranchId(),
                new CreditEndDateRangeParams(
                    $bookingStartDate,
                    $credit->expiryDate()
                ),
                new CreditEndDateIntervalParams(
                    $credit->intervalCount(),
                    IntervalType::fromString($credit->intervalPeriod())
                )
            );
        }

        $credit->put('start_date', $newCreditStartDate->toDateTimeString());
        if (isset($newCreditExpiryDate)) {
            $credit->put('end_date', $newCreditExpiryDate->toDateTimeString());
        }

        return $credit->toLegacy();
    }

    public function revertCreditsSpent(string $branch_id, string $user_id, string $booking_id)
    {
        $conditions = ['branch_id' => $branch_id, 'user_id' => $user_id, 'bookings' => $booking_id, 'active' => true];
        $credits = $this->find('all', ['conditions' => $conditions]);
        $num_credits_used = 0;

        foreach ($credits as $credit) {
            while (false !== ($key = array_search($booking_id, $credit['UserCredit']['bookings']))) {
                unset($credit['UserCredit']['bookings'][$key]);
                $credit['UserCredit']['bookings'] = array_values($credit['UserCredit']['bookings']);
                $num_credits_used = $num_credits_used + 1;
            }
            $credit = $this->updateDatesOnCancel($credit);
            $this->save($credit);
        }

        return $num_credits_used;
    }

    public function updateDatesOnCancel(array $credit): array
    {
        $creditModel = CreditPack::make($credit['UserCredit']);
        if (!$creditModel->isStartingOnFirstBookingDate()) {
            return $credit;
        }

        /** @var \Booking $bookingCakeModel */
        $bookingCakeModel = \ClassRegistry::init('Booking');
        $numberOfBookings = is_countable($credit['UserCredit']['bookings']) ? count(
            $credit['UserCredit']['bookings']
        ) : 0;
        if (0 === $numberOfBookings) {
            $credit['UserCredit']['start_date'] = null;
            $credit['UserCredit']['end_date'] = null;

            return $credit;
        }

        $oldestBookingConditions = [
            '_id' => ['$in' => $credit['UserCredit']['bookings']],
            'user_id' => $credit['UserCredit']['user_id'],
        ];

        $oldestBooking = $bookingCakeModel->find(
            'first',
            [
                'conditions' => $oldestBookingConditions,
                'order' => ['time_start' => 1],
            ]
        );

        $bookingStartDate = $oldestBooking['Booking']['time_start'];
        $creditEndDate = $credit['UserCredit']['end_date'] ?? null;

        if ($bookingStartDate instanceof MongoDate) {
            $bookingStartDate = $bookingStartDate->toDateTime()->format('Y-m-d');
        }

        if ($creditEndDate instanceof MongoDate) {
            $creditEndDate = $creditEndDate->toDateTime()->format('Y-m-d');
        }

        $newStartDate = date('Y-m-d', strtotime($bookingStartDate));
        $credit['UserCredit']['start_date'] = $newStartDate;

        if (
            isset($credit['UserCredit']['expiry']['interval_count']) &&
            isset($credit['UserCredit']['expiry']['interval'])
        ) {
            $credit['UserCredit']['end_date'] = CreditEndDateTransformer::transform(
                $creditModel->user()->currentBranchId(),
                new CreditEndDateRangeParams(
                    Carbon::parse($bookingStartDate),
                    Carbon::parse($creditEndDate)
                ),
                new CreditEndDateIntervalParams(
                    $credit['UserCredit']['expiry']['interval_count'],
                    IntervalType::fromString($credit['UserCredit']['expiry']['interval'])
                ),
                true
            )->toDateTimeString();
        }

        return $credit;
    }

    /**
     * @return CreditPack[]
     */
    public function userGotAlreadySubscriptionCredits(User $user, ?Carbon $startDateInLocalTimezoneInFalseGmt): array
    {
        if (!$user->membership()->hasSubscription() || !$user->membership()->subscription()->hasCredits()) {
            return [];
        }

        if (empty($startDateInLocalTimezoneInFalseGmt)) {
            $startDateInLocalTimezoneInFalseGmt = $user->membership()->startDate();
        }

        $startDateInLocalTimezoneWithTheProperOffsetApplied = $startDateInLocalTimezoneInFalseGmt->copy()->setTimezone(
            $user->branch()->timezone()
        );
        $startDateInLocalTimezoneWithTheProperOffsetApplied = $startDateInLocalTimezoneWithTheProperOffsetApplied
            ->startOfDay()->getTimestamp();

        $startDateInRealGmt = Carbon::createFromTimestamp(
            $startDateInLocalTimezoneWithTheProperOffsetApplied
        )->startOfDay()->getTimestamp();

        $startDateInLocalTimezoneInFalseGmt = $startDateInLocalTimezoneInFalseGmt->getTimestamp();

        $conditions = [
            'active' => true,
            'branch_id' => $user->membership()->branchId(),
            'user_id' => $user->id(),
            'user_membership_id' => $user->membership()->userMembershipId(),
            'num_sessions' => [
                '$gte' => 1,
            ],
            '$and' => [
                [
                    '$or' => [
                        ['end_date' => ['$gte' => new MongoDate(Carbon::now()->getTimestamp())]],
                        ['end_date' => ['$exists' => false]],
                        ['end_date' => null],
                    ],
                ],
                [
                    '$or' => [
                        ['start_date' => new MongoDate($startDateInLocalTimezoneInFalseGmt)],
                        ['start_date' => new MongoDate($startDateInRealGmt)],
                        // Due to dayLight saving time setting, renewal could happen few hours before
                        // it cause a date different and not match with existing credit's start date.
                        // Eg: membership renew on 27-03-2023T23:00:00Z whereas ADVANCED_WHEN_BOOKING credit's start date is 2023-03-28T00:00:00
                        ['start_date' => ['$gte' => new MongoDate($startDateInRealGmt)]]
                    ]
                ],
                [
                    '$expr' => ['$lte' => [['$size' => '$bookings'], '$num_sessions']],
                ],
            ],
        ];

        $credits = $this->find('all', ['conditions' => $conditions]);

        /** @var LoggerInterface $logger */
        $logger = app()->make(LoggerInterface::class);

        foreach ($credits as &$credit) {
            $logger->info(
                sprintf(
                    'Found credit pack %s while looking for subscription credits of member %s',
                    $credit['UserCredit']['_id'],
                    $user->id()
                )
            );

            $credit = CreditPack::make($credit['UserCredit']);
        }

        return $credits;
    }

    public function hasSubscriptionCreditsForNextPeriod(User $user): bool
    {
        $membership = $user->membership();

        if (!$membership->hasExpiryDate()) {
            return true;
        }

        return !empty($this->userGotAlreadySubscriptionCredits($user, $membership->expiryDate()));
    }

    /**
     * @param CreditPack $credit
     * @param Carbon $startDate
     * @param Carbon|null $endDate
     *
     * @return mixed
     *
     * @throws Exception
     */
    public function updateCreditPackDates(CreditPack $credit, Carbon $startDate, Carbon $endDate = null)
    {
        $creditAsArray = $credit->toArray();

        $creditAsArray['start_date'] = $startDate->format('Y-m-d');

        // If this is a credit pack that's supposed to last forever, its end date cannot be changed
        // unless if done strictly through the "update credits" action.
        if (!$credit->lastsForever()) {
            if (isset($creditAsArray['expiry']['interval']) && isset($creditAsArray['expiry']['interval_count'])) {
                $creditAsArray['end_date'] = CreditEndDateTransformer::transform(
                    $credit->branchId(),
                    new CreditEndDateRangeParams(
                        $startDate,
                        $credit->expiryDate()
                    ),
                    new CreditEndDateIntervalParams(
                        $creditAsArray['expiry']['interval_count'],
                        IntervalType::fromString($creditAsArray['expiry']['interval'])
                    )
                )->toDateTimeString();
            }

            if ($endDate) {
                $creditAsArray['end_date'] = $endDate->format('Y-m-d');
            }
        }

        $this->read(null, $creditAsArray['_id']);

        return $this->save($creditAsArray);
    }

    public function resetFirstBookingDateCredits(array $member)
    {
        try {
            $member = $member['User'] ?? $member;

            if (!isset($member['membership']['_id'])) {
                throw new Exception('MEMBER_SHOULD_HAVE_A_MEMBERSHIP');
            }
            $credits = $this->find(
                'all',
                [
                    'conditions' => [
                        'active' => true,
                        'user_id' => $member['_id'],
                        'membership_id' => $member['membership']['_id'],
                        'starts_on' => MembershipPlanStartsOn::FIRST_BOOKING_DATE,
                    ],
                ]
            );

            foreach ($credits as &$credit) {
                $membershipStartDate = $member['membership']['start_date'] ?? null;
                $membershipExpiryDate = $member['membership']['expiry_date'] ?? null;

                if (!$membershipStartDate) {
                    $credit['UserCredit']['start_date'] = null;
                    $credit['UserCredit']['end_date'] = null;

                    $this->save($credit);
                    continue;
                }

                if ($membershipStartDate instanceof MongoDate) {
                    $membershipStartDate = $membershipStartDate->toDateTime()->format('Y-m-d');
                }

                $newStartDate = date('Y-m-d', strtotime($membershipStartDate));
                $credit['UserCredit']['start_date'] = $newStartDate;
                $creditIntervalCount = $credit['UserCredit']['expiry']['interval_count'] ?? null;
                $creditIntervalUnit = $credit['UserCredit']['expiry']['interval'] ?? null;

                if (null !== $creditIntervalCount && null !== $creditIntervalUnit) {
                    $credit['UserCredit']['end_date'] = CreditEndDateTransformer::transform(
                        User::make($member)->currentBranchId(),
                        new CreditEndDateRangeParams(
                            Carbon::parse($newStartDate),
                            Carbon::parse(
                                $membershipExpiryDate instanceof MongoDate ?
                                    $membershipExpiryDate->toDateTime()->format('Y-m-d') : $membershipExpiryDate
                            )
                        ),
                        new CreditEndDateIntervalParams(
                            $creditIntervalCount,
                            IntervalType::fromString($creditIntervalUnit)
                        )
                    )->toDateTimeString();
                }

                $this->save($credit);
            }

            return ['success' => true];
        } catch (Exception $e) {
            // the message of the response of this method is just for debugging propose,
            // it will never send back to the front end, so it is not a dictionary key
            return [
                'success' => false,
                'message' => $e->getMessage(),
            ];
        }
    }

    public function validateCredits(array $credits): bool
    {
        /** @var CreditsValidator $creditsValidator */
        $creditsValidator = $this->container()->make(CreditsValidator::class);

        foreach ($credits as $credit) {
            $creditsValidator->withData($credit)->validate();
        }

        return true;
    }

    /**
     * @param $records
     *
     * @return mixed
     */
    private function attachBookings($records)
    {
        $bookingModel = ClassRegistry::init('Booking');

        $query = [
            'conditions' => ['_id' => ['$in' => $records]],
            'fields' => ['model', 'model_name', 'guest_bookings', 'time_start', 'date_start', 'date_finish', 'attended'],
            'page' => 1,
            'limit' => 100,
        ];

        return $bookingModel->find('all', $query);
    }

    /**
     * @param $branchId
     * @param $userId
     * @param $sessionDate
     * @param $model
     * @param string $modelId
     * @param null $categories
     * @param bool $memberIsUnderSubscriptionAndBookingForNextPeriod
     *
     * @return array
     */
    private function getCreditForBookingConditions(
        $branchId,
        $userId,
        $sessionDate,
        $model,
        $modelId = '',
        $categories = null,
        $memberIsUnderSubscriptionAndBookingForNextPeriod = false
    ) {
        date_default_timezone_set('UTC');

        // this is to fix the problem when credits have category_id:'' set on database
        // but we need to prevent that assignation wherever it is taking place.
        if (is_array($categories)) {
            $categories[] = '';
            $categories[] = null;
        }

        $isValidForTheGivenEventType = [
            '$or' => [
                [
                    '$or' => [
                        ['category_id' => ['$exists' => false]],
                        ['category_id' => null],
                    ],
                    'model_ids' => [
                        '$exists' => true,
                        '$in' => [$modelId],
                    ],
                ],
                [
                    'category_id' => [
                        '$exists' => true,
                        '$in' => $categories,
                    ],
                    'model_ids' => [
                        '$exists' => true,
                        '$in' => [$modelId],
                    ],
                ],
                [
                    'category_id' => [
                        '$exists' => true,
                        '$in' => $categories,
                    ],
                    '$or' => [
                        ['model_ids' => ['$exists' => false]],
                        ['model_ids' => null],
                    ],
                ],
                [
                    '$and' => [
                        [
                            '$or' => [
                                ['category_id' => ['$exists' => false]],
                                ['category_id' => null],
                            ],
                        ],
                        [
                            '$or' => [
                                ['model_ids' => ['$exists' => false]],
                                ['model_ids' => null],
                            ],
                        ],
                    ],
                ],
            ],
        ];

        $hasEndDateOnTheFutureOrInfinite = [
            '$or' => [
                [
                    'end_date' => [
                        '$gte' => new MongoDate(strtotime($sessionDate)),
                        '$exists' => true,
                    ],
                ],
                [
                    'end_date' => ['$exists' => false],
                ],
                [
                    'end_date' => null,
                ],
            ],
        ];

        $hasStartDateInTheFuture = [
            'start_date' => [
                '$lte' => new MongoDate(strtotime($sessionDate)),
                '$exists' => true,
            ],
        ];

        $hasNoStartDateAtAll = [
            'start_date' => ['$exists' => false],
        ];

        $hasUndefinedStartDate = [
            'start_date' => null,
        ];

        $hasDateOfFirstBookingPackAndDateIsNotSetOrInTheFuture = [
            '$and' => [
                ['starts_on' => MembershipPlanStartsOn::FIRST_BOOKING_DATE],
                [
                    '$or' => [
                        ['start_date' => null],
                        ['start_date' => ['$gte' => new MongoDate(strtotime($sessionDate))]],
                    ],
                ],
            ],
        ];

        $hasValidStartDate = [
            '$or' => [
                $hasStartDateInTheFuture,
                $hasNoStartDateAtAll,
                $hasUndefinedStartDate,
                $hasDateOfFirstBookingPackAndDateIsNotSetOrInTheFuture,
            ],
        ];

        $restrictions = [$isValidForTheGivenEventType, $hasValidStartDate];
        if (!$memberIsUnderSubscriptionAndBookingForNextPeriod) {
            $restrictions[] = $hasEndDateOnTheFutureOrInfinite;
        }

        $and = $restrictions;
        $and[] = ['$expr' => ['$lt' => [['$size' => '$bookings'], '$num_sessions']]];

        $query = [
            'branch_id' => $branchId,
            'user_id' => $userId,
            'model' => $model,
            '$expr' => ['$lt' => [['$size' => '$bookings'], '$num_sessions']],
            '$and' => $and,
        ];

        /**
         * @see https://glofox.atlassian.net/browse/DASH2-5887
         * When you try to determine the eligibility of credit packs given a specific event, we need to consider if
         * the credit pack needs to [and] should be shifted to accommodate the new booking withing the duration of the
         * credit pack. This is a complex query and the current solution was to performed in the PHP layer.
         *
         * This should be revisited as soon as possible given that this solution is really costly in terms
         * of performance
         */

        /**
         * We need to run this query ourselves just so we can apply ExcludeCreditPacksGivenSessionDate
         * In case of an empty result, this would be that such filter doesn't need to be apply and we
         * Can simply return the query for this normal use.
         */
        $credits = $this->find('all', ['conditions' => $query]);
        if (empty($credits)) {
            return $query;
        }

        // Normalize the records [{n}.UserCredit] before crediting a CreditPacksCollection instance
        $credits = \Hash::extract($credits, sprintf('{n}.%s', $this->alias));
        $modelHydrator = app()->make(\Glofox\Traversable\Hydrators\ModelHydrator::class);
        foreach ($credits as &$row) {
            $row = $modelHydrator->expects(CreditPack::class)->fill($row);
        }

        /**
         * The following list of credit packs should represent credits that were marked as 'Eligible' by the previous
         * Query logic, but after applying ExcludeCreditPacksGivenSessionDate we determined that such credits can
         * Not be use because they would shift the original credit-pack outside of the allowed time-frame.
         */
        $excludedCreditPackIds = app()->make(Glofox\Domain\Credits\Services\ExcludeCreditPacksGivenSessionDate::class)
            ->execute(
                \Glofox\Domain\Credits\CreditPacksCollection::make($credits),
                Carbon::parse($sessionDate)
            );

        $excludeCreditsThatCanNotBeShiftedDueToCurrentBookings = ['_id' => ['$nin' => $excludedCreditPackIds]];

        $query['$and'][] = $excludeCreditsThatCanNotBeShiftedDueToCurrentBookings;

        return $query;
    }

    private function prioritizeCredits(array $credits)
    {
        // @see https://glofox.atlassian.net/wiki/spaces/PF/pages/580190327/Credits+-+Using+them+and+their+expiry topic 2.1
        $creditsWithoutExpiryDate = collect();
        $creditsWithExpiryDate = collect();

        foreach ($credits as $credit) {
            /** @var \Illuminate\Support\Collection $creditObject */
            $userCredit = collect($credit['UserCredit']);

            if (!$userCredit->get('end_date')) {
                $creditsWithoutExpiryDate->push($credit);
                continue;
            }

            $creditsWithExpiryDate->push($credit);
        }

        // @see https://glofox.atlassian.net/wiki/spaces/PF/pages/580190327/Credits+-+Using+them+and+their+expiry topic 2.1
        $sortedCreditWithExpiryDate = $creditsWithExpiryDate->sortBy('UserCredit.end_date');

        // @see https://glofox.atlassian.net/wiki/spaces/PF/pages/580190327/Credits+-+Using+them+and+their+expiry topic 2.1
        return $sortedCreditWithExpiryDate->merge($creditsWithoutExpiryDate);
    }

    public function hasCreditsForNextCycle(
        UserMembership $membership,
        User $user,
        Carbon $nextPeriodStartDate
    ): bool {
        /** @var CreditsRepository $creditsRepository */
        $creditsRepository = app()->make(CreditsRepository::class);
        $creditsRepository
            ->addCriteria(new Active(true))
            ->addCriteria(new UserId($user->id()))
            ->addCriteria(StartDate::greaterThanOrEqual($nextPeriodStartDate))
            ->addCriteria(new UserMembershipId($membership->userMembershipId()));

        return $creditsRepository->count() > 0;
    }

    /**
     * @param int|string $id ID of record to check is valid mongo id or exists in db
     *
     * @return bool
     */
    public function exists($id = null)
    {
        /** Same as the current cakephp implementation **/
        if ($id === null) {
            $id = $this->getID();
        }

        if ($id === false) {
            return false;
        }

        if ($this->useTable === false) {
            return false;
        }
        /** ****************************************** */

        if ($id instanceof \MongoId || \MongoId::isValid($id)) {
            if ($id instanceof \MongoId) {
                return $id->__toString() === $this->getID();
            }

            return $id === $this->getID();
        }

        return parent::exists($id);
    }

    private function isCreditsAlignedWithMembership(UserMembership $userMembership, array $credit): bool
    {
        if (!$userMembership->hasSubscription()) {
            return true;
        }

        $userSubscription = $userMembership->subscription();

        $membershipIsForceToStartFirstOfMonth = $userSubscription->isProrated();
        $subscriptionIntervalCount = $userSubscription->intervalCount();
        $subscriptionInterval = $userSubscription->interval();

        $sameInterval = $credit['expiry']['interval'] === $subscriptionInterval;
        $sameIntervalCount = $credit['expiry']['interval_count'] === $subscriptionIntervalCount;
        $creditsHaveSameIntervalThanMembership = $sameInterval && $sameIntervalCount;

        return $membershipIsForceToStartFirstOfMonth && $creditsHaveSameIntervalThanMembership;
    }

    private function hasValidMembershipForAdvancedCredits(
        UserMembership $membership,
        User $user,
        LoggerInterface $logger
    ): bool {
        if (!$membership->hasSubscription()) {
            $logger->info(
                sprintf(
                    'Could not renew credits for user %s since the membership is not a subscription',
                    $user->id()
                )
            );

            return false;
        }

        if (!$membership->subscription()->hasCredits()) {
            $logger->info(
                sprintf(
                    'Could not renew credits for user %s since the subscription does not have credits',
                    $user->id()
                )
            );

            return false;
        }

        if (!$membership->hasStartDate()) {
            $logger->info(
                sprintf(
                    'Could not renew credits for user %s since the membership does not have start date',
                    $user->id()
                )
            );

            return false;
        }

        if (!$membership->hasExpiryDate()) {
            $logger->info(
                sprintf(
                    'Could not renew credits for user %s since the membership does not have expiry date',
                    $user->id()
                )
            );

            return false;
        }

        return true;
    }

    private function hasValidSubscriptionForAdvancedCredits(
        UserMembershipSubscription $subscription,
        User $user,
        LoggerInterface $logger
    ): bool {
        if (!$subscription->has('interval')) {
            $logger->info(
                sprintf(
                    'Could not renew credits for user %s since the subscription does not have interval',
                    $user->id()
                )
            );

            return false;
        }

        if (!$subscription->has('interval_count')) {
            $logger->info(
                sprintf(
                    'Could not renew credits for user %s since the subscription does not have interval count',
                    $user->id()
                )
            );

            return false;
        }

        return true;
    }

    private function calculateCreditEndDate(
        string $membershipId,
        User $user,
        UserMembership $userMembership,
        Carbon $membershipStartDate,
        array $credit,
        bool $inAdvance,
        bool $isLastCycle
    ): Carbon {
        $endDate = CreditEndDateTransformer::transform(
            $user->currentBranchId(),
            new CreditEndDateRangeParams(
                $membershipStartDate,
                $userMembership->expiryDate()
            ),
            new CreditEndDateIntervalParams(
                $credit['expiry']['interval_count'],
                IntervalType::fromString($credit['expiry']['interval']),
                $inAdvance
            ),
            $isLastCycle
        );

        if ($inAdvance) {
            return $endDate;
        }

        $isPrepaidOrCreditsAlignedWithMembership = $this->isCreditsAlignedWithMembership(
            $userMembership,
            $credit
        );

        $creditsBelongsToMembership = !empty($membershipId) && $membershipId === $userMembership->id();

        if ($isPrepaidOrCreditsAlignedWithMembership && $creditsBelongsToMembership) {
            return $userMembership->expiryDate();
        }

        return $endDate;
    }
}
