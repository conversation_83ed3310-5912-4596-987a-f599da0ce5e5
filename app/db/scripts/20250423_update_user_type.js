//To update a user type. For more details see STAF-2173 (https://glofox.atlassian.net/browse/STAF-2173)

const branchId = "674da57deadb0145fd0b0d19";

const emailList = [
    "<EMAIL>",
    "<EMAIL>",
];

const currentDate = new Date();

db.users.updateMany(
  {
    branch_id: branchId,
    email: { $in: emailList },
    type: "ADMIN"
  },
  {
    $set: {
      type: "SUPERADMIN",
      modified: currentDate
    }
  }
);