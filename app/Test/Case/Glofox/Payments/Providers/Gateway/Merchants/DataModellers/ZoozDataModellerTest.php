<?php

namespace CakeTestCases\Glofox\Payments\Providers\Gateway\Merchants\DataModellers;

use Glofox\Domain\Branches\Models\Branch;
use Glofox\Domain\PaymentProviders\Models\PaymentProvider;
use Glofox\Payments\Providers\Gateway\Merchants\DataModellers\ZoozDataModeller;
use Illuminate\Validation\ValidationException;

\App::import('Test/Case', 'GlofoxTestCase');

class ZoozDataModellerTest extends \GlofoxTestCase
{
    public function test_it_should_throw_an_exception_when_the_create_is_missing_fields()
    {
        $dataModeler = new ZoozDataModeller();

        $this->setExpectedException(ValidationException::class);
        $dataModeler->modelForCreating($this->getPaymentProvider(), $this->getPayURussiaBranch(), collect());
    }

    public function test_it_should_throw_an_exception_when_the_update_is_missing_fields()
    {
        $dataModeler = new ZoozDataModeller();

        $this->setExpectedException(ValidationException::class);
        $dataModeler->modelForUpdating($this->getPaymentProvider(), $this->getPayURussiaBranch(), collect(['app_id' => 'bar']));
    }

    public function test_it_should_create_using_the_right_format_given_a_payu_russia_branch_and_payload()
    {
        $dataModeler = new ZoozDataModeller();

        $payload = collect([
            'app_id' => 'app_111',
            'secret_key' => 'secret_111',
            'public_key' => 'public_111',
            'environment' => 'test'
        ]);

        $result = $dataModeler->modelForCreating($this->getPaymentProvider(), $this->getPayURussiaBranch(), $payload);

        $this->assertEquals([
            'email' => '<EMAIL>',
            'phone' => '111',
            'country' => 'RUS',
            'currency' => 'rub',
            'business_name' => 'PayU Gym',
            'auth_object' => [
                'id' => 'app_111',
                'environment' => 'test',
                'integration_type' => 'PAYU_RUSSIA',
                'keys' => [
                    'secret_key' => 'secret_111',
                    'publishable' => 'public_111',
                ],
            ],
        ], $result->toArray());
    }

    public function test_it_should_create_using_the_right_format_given_a_payu_turkey_branch_and_payload()
    {
        $dataModeler = new ZoozDataModeller();

        $payload = collect([
            'app_id' => 'app_111',
            'secret_key' => 'secret_111',
            'public_key' => 'public_111',
            'environment' => 'test'
        ]);

        $result = $dataModeler->modelForCreating($this->getPaymentProvider(), $this->getPayUTurkeyBranch(), $payload);

        $this->assertEquals([
            'email' => '<EMAIL>',
            'phone' => '111',
            'country' => 'TUR',
            'currency' => 'try',
            'business_name' => 'PayU Gym',
            'auth_object' => [
                'id' => 'app_111',
                'environment' => 'test',
                'integration_type' => 'PAYU_TURKEY',
                'keys' => [
                    'secret_key' => 'secret_111',
                    'publishable' => 'public_111',
                ],
            ],
        ], $result->toArray());
    }

    public function test_it_should_update_using_the_right_format_given_a_payu_russia_branch_and_payload()
    {
        $dataModeler = new ZoozDataModeller();

        $payload = collect([
            'app_id' => 'app_111',
            'secret_key' => 'secret_111',
            'public_key' => 'public_111',
            'environment' => 'test'
        ]);

        $result = $dataModeler->modelForUpdating($this->getPaymentProvider(), $this->getPayURussiaBranch(), $payload);

        $this->assertEquals([
            'email' => '<EMAIL>',
            'phone' => '111',
            'country' => 'RUS',
            'currency' => 'rub',
            'business_name' => 'PayU Gym',
            'auth_object' => [
                'id' => 'app_111',
                'environment' => 'test',
                'integration_type' => 'PAYU_RUSSIA',
                'keys' => [
                    'secret_key' => 'secret_111',
                    'publishable' => 'public_111',
                ],
            ],
        ], $result->toArray());
    }

    public function test_it_should_update_using_the_right_format_given_a_payu_turkey_branch_and_payload()
    {
        $dataModeler = new ZoozDataModeller();

        $payload = collect([
            'app_id' => 'app_111',
            'secret_key' => 'secret_111',
            'public_key' => 'public_111',
            'environment' => 'test'
        ]);

        $result = $dataModeler->modelForUpdating($this->getPaymentProvider(), $this->getPayUTurkeyBranch(), $payload);

        $this->assertEquals([
            'email' => '<EMAIL>',
            'phone' => '111',
            'country' => 'TUR',
            'currency' => 'try',
            'business_name' => 'PayU Gym',
            'auth_object' => [
                'id' => 'app_111',
                'environment' => 'test',
                'integration_type' => 'PAYU_TURKEY',
                'keys' => [
                    'secret_key' => 'secret_111',
                    'publishable' => 'public_111',
                ],
            ],
        ], $result->toArray());
    }

    private function getPayURussiaBranch(): Branch
    {
        return Branch::make([
            'name' => 'PayU Gym',
            'phone' => '111',
            'email' => '<EMAIL>',
            'address' => [
                'country_code' => 'RU',
                'currency' => 'RUB'
            ]
        ]);
    }

    private function getPayUTurkeyBranch(): Branch
    {
        return Branch::make([
            'name' => 'PayU Gym',
            'phone' => '111',
            'email' => '<EMAIL>',
            'address' => [
                'country_code' => 'TR',
                'currency' => 'TRY'
            ]
        ]);
    }

    private function getPaymentProvider(): PaymentProvider
    {
        return PaymentProvider::make([
            'active' => true,
            'gateway_id' => '6',
            'payment_method_type_id' => 'CARD',
            'verification_flow' => null,
            'handler_id' => 'GATEWAY_HANDLER',
            'available_countries' => [
                [
                    'country_code' => 'RU',
                    'default_charge_percentage' => 0.0,
                    'default_fixed_charge' => 0.0
                ],
                [
                    'country_code' => 'TR',
                    'default_charge_percentage' => 0.0,
                    'default_fixed_charge' => 0.0
                ]
            ],
            'name' => 'ZOOZ',
            'registration_fields' => [
                [
                    'id' => 'environment',
                    'name' => 'PUR_ENVIRONMENT',
                    'origin' => 'HARDCODED',
                    'type' => 'STRING',
                    'default_value' => 'test',
                ],
                [
                    'id' => 'app_id',
                    'name' => 'PUR_APP_ID',
                    'origin' => 'INTERNAL',
                    'type' => 'STRING'
                ],
                [
                    'id' => 'secret_key',
                    'name' => 'PUR_SECRET_KEY',
                    'origin' => 'INTERNAL',
                    'type' => 'STRING'
                ],
                [
                    'id' => 'public_key',
                    'name' => 'PUR_PUBLIC_KEY',
                    'origin' => 'INTERNAL',
                    'type' => 'STRING'
                ],
            ],
            'registration_flow' => [
                'type' => 'INTERNAL_FORM'
            ],
            'tokenization_handler' => 'ZOOZ'
        ]);
    }
}