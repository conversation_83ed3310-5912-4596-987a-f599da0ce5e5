<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: billing_details.proto

namespace GRPC\Payments\AccountService;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>grpc.Address</code>
 */
class Address extends \Google\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string PostalCode = 1;</code>
     */
    protected $PostalCode = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $PostalCode
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\BillingDetails::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string PostalCode = 1;</code>
     * @return string
     */
    public function getPostalCode()
    {
        return $this->PostalCode;
    }

    /**
     * Generated from protobuf field <code>string PostalCode = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setPostalCode($var)
    {
        GPBUtil::checkString($var, True);
        $this->PostalCode = $var;

        return $this;
    }

}

