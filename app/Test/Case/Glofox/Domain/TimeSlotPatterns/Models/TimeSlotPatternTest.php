<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\TimeSlotPatterns\Models;

use App;
use Glofox\Domain\TimeSlotPatterns\Models\TimeSlotPattern;
use Glofox\Domain\TimeSlotPatterns\Repositories\TimeSlotPatternsRepository;
use GlofoxTestCase;

App::import('Test/Case', 'GlofoxTestCase');

class TimeSlotPatternTest extends GlofoxTestCase
{
    public $fixtures = ['app.time_slot_pattern'];

    public function tearDown(): void
    {
        parent::tearDown();

        app()->forgetInstance(TimeSlotPatternsRepository::class);
    }

    /**
     * @dataProvider sizeDataProvider
     * @param array $params
     */
    public function testSizeTimeSlotPattern(array $params): void
    {
        $timeSlotPatternRepository = app()->make(TimeSlotPatternsRepository::class);
        $timeSlotPatternSize = $timeSlotPatternRepository->getById($params['timeSlotPatternId'])->size();

        self::assertEquals($params['expectedSize'], $timeSlotPatternSize);
    }

    public function sizeDataProvider(): array
    {
        return [
            '1. When I attempt to get the size of the time slot pattern and the size is set to 3, Then I should get 3'
            => [
                'params' => [
                    'timeSlotPatternId' => '66ebc6fcf728da7a01baaae0',
                    'expectedSize' => 3,
                ],
            ],
            '2. When I attempt to get the size of the time slot pattern is not set, Then I should get 1' => [
                'params' => [
                    'timeSlotPatternId' => '66ebc6fcf728da7a01baaae1',
                    'expectedSize' => 1,
                ],
            ],
        ];
    }

    public function testMigrateNothingWhenItHasNoStaff(): void
    {
        $timeSlotPattern = TimeSlotPattern::make();

        $timeSlotPattern->migrateToTrainer([]);

        self::assertNull($timeSlotPattern->modelId());
    }

    public function testMigrateNothingWhenTrainerIsNotInPatternStaff(): void
    {
        $timeSlotPattern = TimeSlotPattern::make([
            'staff_ids' => [
                '67335e094e945f62881acd9d',
                '67335f7e769ad390df940162',
            ]
        ]);

        $availableTrainers = [
            '67335f718a49f5e42f48580e',
            '67335f77205774ee7c2caf4f',
        ];
        $timeSlotPattern->migrateToTrainer($availableTrainers);

        self::assertNull($timeSlotPattern->modelId());
    }

    public function testMigrateTrainerWhenItIsInPatternStaff(): void
    {
        $desiredStaffId = '67335d249ea2fe31bc9c60b1';
        $timeSlotPattern = TimeSlotPattern::make([
            'staff_ids' => [
                '67335e094e945f62881acd9d',
                '67335e267f82a103e639ed0a',
                $desiredStaffId
            ]
        ]);

        $availableTrainers = [$desiredStaffId];
        $timeSlotPattern->migrateToTrainer($availableTrainers);

        self::assertEquals($desiredStaffId, $timeSlotPattern->modelId());
    }
}
