type: object
properties:
  slots:
    type: array
    items:
      type: object
      properties:
        bookable:
          type: boolean
          description: If the slot can be booked
        reservable:
          type: boolean
          description: If the slot can be reserved
        startTime:
          type: integer
          description: The local timestamp that the slot starts
        endTime:
          type: integer
          description: The local timestamp that the slot ends
        errors:
          type: array
          items:
            type: string
            description: The variety of errors that can be returned
            enum:
              - MEMBER_HAS_ANOTHER_RESERVATION_AT_THE_TIME
              - EVENT_DOES_NOT_EXIST
              - SLOT_IS_IN_THE_PAST
              - BOOKING_WINDOW_IS_CLOSED
              - BOOKING_WINDOW_IS_NOT_OPEN_YET
              - CLASS_IS_NOT_IN_PAYMENT_CYCLE
              - MEMBER_IS_NOT_AVAILABLE_AT_THE_TIME
              - MEMBERSHIP_IS_NOT_ALLOWED_TO_BOOK_IN_THE_CLASS
              - MEMBER_IS_OUT_OF_CREDITS
              - MEMBER_HAS_TO_PAY_FOR_THE_CLASS
              - MEMBERSHIP_IS_EXPIRED
              - MEMBERSHIP_IS_LOCKED
              - MEMBERSHIP_IS_PAUSED
              - MEMBERSHIP_IS_NOT_UNLIMITED_OR_RESTRICTED
              - MEMBER_HAS_REACHED_MAX_NUMBER_OF_STRIKES
              - CLASS_IS_FULL
        schedule_code:
          type: string
          description: Refers to the code of the schedule object retrieved
        batchId:
          type: string
          description: Unique identifier for the whole operation.
        memberId:
          type: string
          description: Refers to the booking's member.
        programId:
          type: string
          description: Refers to the program selected.
