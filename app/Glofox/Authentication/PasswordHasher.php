<?php

namespace Glofox\Authentication;

use UnsuccessfulOperation;

class PasswordHasher
{
    private int $hashCost = 10;

    public function __construct(int $hashCost)
    {
        $this->hashCost = $hashCost;
    }

    public function verify(string $password, string $passwordHash): bool
    {
        return password_verify($password, $passwordHash);
    }

    public function make(string $password): string
    {
        $hash = password_hash($password, PASSWORD_ARGON2I, [
            'cost' => $this->hashCost,
        ]);

        if (\gettype($hash) === 'string') {
            return $hash;
        }

        throw new UnsuccessfulOperation('Exception when create a hash');
    }
}
