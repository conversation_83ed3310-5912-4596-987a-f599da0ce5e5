<?php

declare(strict_types=1);

namespace Glofox\Infrastructure\Locker;

use Carbon\Carbon;
use Glofox\Domain\Cache\CacheClientInterface;
use Glofox\Domain\Locker\BookingEventLockerInterface;
use Glofox\Domain\Locker\EventBookingLockerException;
use Psr\Log\LoggerInterface;

class BookingEvent<PERSON>ocker implements BookingEventLockerInterface
{
    private const RETRIES = 10;
    private const SLEEP_TIME_IN_MICROSECONDS = 500_000;

    private CacheClientInterface $cache;

    private LoggerInterface $logger;

    public function __construct(
        CacheClientInterface $cache,
        LoggerInterface $logger
    ) {
        $this->cache = $cache;
        $this->logger = $logger;
    }

    /**
     * @throws EventBookingLockerException
     */
    public function lock(string $branchId, string $eventId): void
    {
        $lockKey = $this->computeLockKey($branchId, $eventId);

        $this->logger->info(
            sprintf('attempt to lock bookings for event with key %s', $lockKey)
        );

        $added = false;
        for ($i = 0; $i < static::RETRIES; $i++) {
            $added = $this->cache->setIfNotExist($lockKey, $lockKey);
            if ($added) {
                break;
            }

            usleep(static::SLEEP_TIME_IN_MICROSECONDS);
        }

        if (!$added) {
            throw EventBookingLockerException::forEventIdInBranch($branchId, $eventId);
        }

        $expiresAt = Carbon::now()->addSeconds(15);
        $this->cache->expiresAt($lockKey, $expiresAt);

        $this->logger->info(
            sprintf('lock successfully created for event bookings with key %s', $lockKey),
            ['expiresAt' => $expiresAt->toISOString()]
        );
    }

    public function unlock(string $branchId, string $eventId): void
    {
        $lockKey = $this->computeLockKey($branchId, $eventId);
        $this->cache->delete($lockKey);

        $this->logger->info(
            sprintf('booking lock deleted for event with key %s', $lockKey)
        );
    }

    private function computeLockKey(string $branchId, string $eventId): string
    {
        return sprintf('%s-%s', $branchId, $eventId);
    }
}
