<?php

use Glofox\Domain\Branches\Repositories\BranchesRepository;

class ConfigureSMSForBranchShell extends AppShell
{
    public const COMMUNICATIONS_SMS_OPT_OUT_MESSAGE = "COMMUNICATIONS_SMS_OPT_OUT_MESSAGE";

    private $branchesRepository;
    private $messageConfigModel;
    private $twilioAccountSid;
    private $twilioAuthToken;
    private $dictionaryModel;
    
    public function __construct($stdout = null, $stderr = null, $stdin = null)
    {
        parent::__construct($stdout, $stderr, $stdin);

        $this->branchesRepository = app()->make(BranchesRepository::class);
        $this->messageConfigModel = \ClassRegistry::init(MessageConfig::class);
        $this->dictionaryModel = \ClassRegistry::init(Dictionary::class);
    }

    public function main()
    {
        $branchId = $this->args[0] ?? null;
        if (empty($branchId)) {
            $this->log('Branch id has to be provided', LOG_ERR);

            return;
        }

        $this->twilioAccountSid = $this->args[1] ?? null;
        if (empty($this->twilioAccountSid)) {
            $this->log('Twilio Account Sid has to be provided', LOG_ERR);

            return;
        }

        $this->twilioAuthToken = $this->args[2] ?? null;
        if (empty($this->twilioAuthToken)) {
            $this->log('Twilio Auth Token has to be provided', LOG_ERR);

            return;
        }

        $branch = $this->branchesRepository->findById($branchId);

        $smsEnabled = $branch['Branch']['features']['message']['sms']['enabled'] ?? false;

        if ($smsEnabled) {
            $this->log('SMS is already enabled for this branch', LOG_INFO);

            return;
        }

        $subAccount = $this->createTwilioSubAccount($branch);

        // If US/CA, create mixed messaging service only else create separate transactional and marketing services
        $countryCode = $this->args[3] ?? null;
        if ($countryCode === 'US' || $countryCode === 'CA') {
            $mixedService = $this->createTwilioMessagingService($subAccount, 'mixed', $branch['Branch']['_id']);

            $messagingServices['mixed'] = $mixedService;

            $this->saveMessageConfig($branch, $subAccount->auth_token, $messagingServices);
        } else {
            $this->createSeparateServices($branch, $subAccount);
        }

        if ($countryCode === 'US') {
            $this->dictionaryModel->updateWord(
                "en",
                null,
                $branch['Branch']['namespace'],
                $branch['Branch']['_id'],
                self::COMMUNICATIONS_SMS_OPT_OUT_MESSAGE,
                "\n-\nReply STOP to unsubscribe"
            );
        }
    }

    private function createSeparateServices($branch, $subAccount)
    {
        $transactionalService = $this->createTwilioMessagingService($subAccount, 'transactional',
            $branch['Branch']['_id']);

        $marketingService = $this->createTwilioMessagingService($subAccount, 'marketing', $branch['Branch']['_id']);

        $messagingServices['transactional'] = $transactionalService;
        $messagingServices['marketing'] = $marketingService;

        $this->saveMessageConfig($branch, $subAccount->auth_token, $messagingServices);
    }

    private function createTwilioSubAccount($branch)
    {
        $payload = http_build_query([
            'FriendlyName' => sprintf('%s - %s', $branch['Branch']['_id'], $branch['Branch']['namespace']),
        ]);

        $ch = curl_init('https://api.twilio.com/2010-04-01/Accounts.json');
        curl_setopt($ch, CURLOPT_POSTFIELDS, $payload);
        curl_setopt($ch, CURLOPT_USERPWD, sprintf('%s:%s', $this->twilioAccountSid, $this->twilioAuthToken));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $result = json_decode(curl_exec($ch), null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        curl_close($ch);

        return $result;
    }

    private function createTwilioMessagingService($twilioSubAccount, $prefix, $branchId)
    {
        $messagingPayload = http_build_query([
            'FriendlyName' => sprintf('%s_messaging_service', $prefix),
            'InboundRequestUrl' => sprintf('%s/2.2/webhooks/%s/twilio?secret=%s', env('GLOFOX_ROOT_DOMAIN'), $branchId,
                env('GLOFOX_TWILIO_WEBHOOK_SECRET_KEY')),
        ]);

        $ch = curl_init('https://messaging.twilio.com/v1/Services');
        curl_setopt($ch, CURLOPT_POSTFIELDS, $messagingPayload);
        curl_setopt($ch, CURLOPT_USERPWD, sprintf('%s:%s', $twilioSubAccount->sid, $twilioSubAccount->auth_token));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $result = json_decode(curl_exec($ch), null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        curl_close($ch);

        $notifyPayload = http_build_query([
            'FriendlyName' => sprintf('%s_notify', $prefix),
            'MessagingServiceSid' => $result->sid,
        ]);

        $ch = curl_init('https://notify.twilio.com/v1/Services');
        curl_setopt($ch, CURLOPT_POSTFIELDS, $notifyPayload);
        curl_setopt($ch, CURLOPT_USERPWD, sprintf('%s:%s', $twilioSubAccount->sid, $twilioSubAccount->auth_token));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $notifyResult = json_decode(curl_exec($ch), null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        curl_close($ch);

        return $notifyResult;
    }

    private function saveMessageConfig($branch, $token, $messagingServices)
    {
        $config = [
            'MessageConfig' => [
                'branch_id' => $branch['Branch']['_id'],
                'namespace' => $branch['Branch']['namespace'],
                'provider' => 'TWILIO',
                'has_opt_out_url' => true,
            ],
        ];

        if (isset($messagingServices['transactional']) && isset($messagingServices['marketing'])) {
            $config['MessageConfig']['transactional'] = [
                'account_sid' => $messagingServices['transactional']->account_sid,
                'auth_token' => $token,
                'notify_service_id' => $messagingServices['transactional']->sid,
                'transactional_service_id' => $messagingServices['transactional']->messaging_service_sid,
            ];
            $config['MessageConfig']['marketing'] = [
                'account_sid' => $messagingServices['marketing']->account_sid,
                'auth_token' => $token,
                'notify_service_id' => $messagingServices['marketing']->sid,
                'marketing_service_id' => $messagingServices['marketing']->messaging_service_sid,
            ];
        } elseif (isset($messagingServices['mixed'])) {
            
            $config['MessageConfig']['mixed'] = [
                'account_sid' => $messagingServices['mixed']->account_sid,
                'auth_token' => $token,
                'notify_service_id' => $messagingServices['mixed']->sid,
                'mixed_service_id' => $messagingServices['mixed']->messaging_service_sid,
            ];
        }

        $this->messageConfigModel->save($config);
    }
}
