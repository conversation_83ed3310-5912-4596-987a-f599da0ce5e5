<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Payments\Providers\Gateway\WebHooks\Handlers;

use CakeTestCases\Glofox\Domain\Logger\Traits\MockedAbClientValidatorTrait;
use Carbon\Carbon;
use Glofox\Domain\ABTestClients\Repositories\ABTestClientsRepository;
use Glofox\Domain\Bookings\Models\BookableEntityType;
use Glofox\Domain\Charges\Events\ChargeWasUpdated;
use Glofox\Domain\Charges\Repositories\ChargesRepository;
use Glofox\Domain\Credits\Models\CreditPackSourceMembershipRenewed;
use Glofox\Domain\Credits\Repositories\CreditsRepository;
use Glofox\Domain\FeatureFlags\FeatureFlagInterface;
use Glofox\Domain\Memberships\Models\Addon;
use Glofox\Domain\Memberships\Services\AddonServiceInterface;
use Glofox\Domain\Memberships\Services\MembershipsClient;
use Glofox\Domain\Memberships\Services\Subscription\RenewSubscriptionService;
use Glofox\Domain\PaymentMethods\Models\PaymentMethod;
use Glofox\Domain\SalesTaxes\ServiceType;
use Glofox\Domain\Subscriptions\Services\SubscriptionsEventPublisher;
use Glofox\Domain\Users\Exceptions\UserNotFoundException;
use Glofox\Domain\Users\Repositories\UsersRepository;
use Glofox\Domain\Users\Search\Expressions\UserId;
use Glofox\Domain\Users\Services\Membership\UserMembershipUnlocker;
use Glofox\Infrastructure\Flags\Flag;
use Glofox\Events\EventManager;
use Glofox\Infrastructure\Flags\Flagger;
use Glofox\Payments\Contracts\PaymentProviderContract;
use Glofox\Payments\Entities\Customer\Contracts\CustomerHandlerContract;
use Glofox\Payments\Entities\Customer\Models\Customer;
use Glofox\Payments\Entities\Invoice\Contracts\InvoiceHandlerContract;
use Glofox\Payments\Entities\Invoice\Models\InvoiceEntity;
use Glofox\Payments\Entities\Invoice\Models\InvoiceStatus;
use Glofox\Payments\Entities\Subscription\Contracts\SubscriptionHandlerContract;
use Glofox\Payments\Entities\Subscription\Events\SubscriptionCycleWasPaid;
use Glofox\Payments\Entities\Subscription\Models\Subscription;
use Glofox\Payments\Entities\Transaction\Contracts\ChargeHandlerContract;
use Glofox\Payments\Entities\Transaction\Models\Transaction;
use Glofox\Payments\Entities\Transaction\Status;
use Glofox\Payments\Entities\Transaction\Type;
use Glofox\Payments\Providers\Gateway\WebHooks\Handlers\InvoicePaymentSucceeded;
use Glofox\Payments\Providers\Gateway\WebHooks\Search\SubscriptionWebHookUserFinder;
use Glofox\Payments\Services\MembershipRenewalValidatorServiceFactory;
use Glofox\Repositories\Search\Expressions\Shared\Id;
use Mockery;
use MongoId;
use Psr\Log\LoggerInterface;

\App::import('Test/Case', 'GlofoxTestCase');

class InvoicePaymentSucceededTest extends \GlofoxTestCase
{
    use MockedAbClientValidatorTrait;

    public $fixtures = [
        'app.user',
        'app.membership',
        'app.branch',
        'app.stripe_charge',
    ];
    private UsersRepository $usersRepository;
    private FeatureFlagInterface $featureFlag;

    public function setUp()
    {
        parent::setUp();

        app()->forgetInstance(ChargesRepository::class);

        $this->usersRepository = app()->make(UsersRepository::class);

        $membershipClientMock = \Mockery::mock(MembershipsClient::class)
            ->shouldReceive('post')
            ->andReturn([])
            ->getMock();
        app()->instance(MembershipsClient::class, $membershipClientMock);

        $subscriptionsEventPublisherMock = \Mockery::mock(SubscriptionsEventPublisher::class);
        $subscriptionsEventPublisherMock->shouldReceive('sendSubscriptionRenewedEvent');
        app()->instance(SubscriptionsEventPublisher::class, $subscriptionsEventPublisherMock);

        app()->forgetInstance(ABTestClientsRepository::class);
        app()->forgetInstance(ChargesRepository::class);
        $this->featureFlag = $this->givenFeatureFlagsMock();
    }

    public function tearDown()
    {
        $this->tearDownMockedAbTestValidator();

        app()->forgetInstance(MembershipsClient::class);
        app()->forgetInstance(SubscriptionsEventPublisher::class);
        app()->forgetInstance(FeatureFlagInterface::class);

        \Mockery::close();

        parent::tearDown();
    }

    public function testHandlerUserNotFound(): void
    {
        $event = collect([
            'id' => 'evt_123',
            'psp_response' => collect([
                'subscription_id' => 'sub_testSubscriptionPayment',
                'customer_account_id' => 'cusAcctID',
            ])->toJson(),
        ]);

        $subscription = new Subscription();
        $subscription->setId('sub_00X');
        $charge = new Transaction();
        $charge->setStatus(Status::PAYMENT_STATUS_SUCCESS)
            ->setId('charge1')
            ->setAmount(100)
            ->setCurrency('CAD')
            ->setCustomerAccountId('5d6826348d8540851fe60662')
            ->setCreatedAt(1)
            ->setUpdatedAt(1)
            ->setPaymentMethod('CARD');

        $paymentMethod = PaymentMethod::make([
            '_id' => 'payment-method-id',
            'branch_id' => '49a7011a05c677b9a916612a',
            'type_id' => 'CARD',
        ]);

        /** @var PaymentProviderContract $paymentProvider */
        $paymentProvider = Mockery::mock(PaymentProviderContract::class)
            ->allows([
                'subscriptions' => Mockery::mock(SubscriptionHandlerContract::class)
                    ->allows(['getById' => $subscription]),
                'charges' => Mockery::mock(ChargeHandlerContract::class)
                    ->allows(['getById' => $charge, 'getRefundsByChargeId' => []]),
                'customers' => Mockery::mock(CustomerHandlerContract::class)
                    ->shouldReceive('getById')
                    ->andReturn(
                        (new Customer())->setId('cus_from_stripe')
                    )
                    ->once()
                    ->getMock()
                    ->allows(['create' => new Customer()]),
                'paymentMethod' => $paymentMethod,
            ]);

        $subscriptionWebHookUserFinder = Mockery::mock(SubscriptionWebHookUserFinder::class);
        $subscriptionWebHookUserFinder
            ->shouldReceive('find')
            ->andReturn(null)
            ->once();

        $logger = Mockery::mock(LoggerInterface::class);
        $logger
            ->shouldReceive('info');
        $this->setExpectedException(UserNotFoundException::class);

        $handler = new InvoicePaymentSucceeded(
            $event,
            $paymentProvider,
            app()->make(UsersRepository::class),
            $logger,
            app()->make(RenewSubscriptionService::class),
            app()->make(MembershipRenewalValidatorServiceFactory::class),
            $subscriptionWebHookUserFinder,
            app()->make(UserMembershipUnlocker::class),
            app()->make(EventManager::class),
            app()->make(SubscriptionsEventPublisher::class),
            $this->featureFlag
        );
        $handler->handle();
    }

    public function testHandlerStoresSubscriptionCharge(): void
    {
        $invoiceId = (string) new MongoId();
        $event = collect([
            'id' => 'evt_123',
            'psp_response' => collect([
                'subscription_id' => 'sub_testSubscriptionPayment',
                'customer_account_id' => 'cusAcctID',
                'invoice' => [
                    'id' => $invoiceId,
                    'amount' => 100,
                    'latest_transaction_group' => [
                        'id' => 'trx-group-1',
                        'transactions' => [
                            [
                                'id' => 'trx-1',
                            ],
                        ],
                    ],
                ],
                'data' => [
                    'object' => [
                        'lines' => [
                            'type' => 'invoice.payment_succeeded',
                            'data' => [
                                [
                                    'period' => [
                                        'start' => Carbon::now()->getTimestamp(),
                                        'end' => Carbon::tomorrow()->getTimestamp(),
                                    ],
                                ],
                            ],
                        ],
                    ],
                ],
            ])->toJson(),
        ]);

        $userId = '5d6826348d8540851fe60662';

        $subscription = new Subscription();
        $subscription->setId('sub_00X');

        $invoice = new InvoiceEntity();
        $invoice->setId($invoiceId);
        $invoice->setStatus(InvoiceStatus::PAID());

        $charge = new Transaction();
        $charge->setStatus(Status::PAYMENT_STATUS_SUCCESS)
            ->setId('charge1')
            ->setAmount(100)
            ->setCurrency('CAD')
            ->setCustomerAccountId($userId)
            ->setCreatedAt(Carbon::now()->getTimestamp())
            ->setUpdatedAt(Carbon::now()->getTimestamp())
            ->setPaymentMethod('CARD')
            ->setType(Type::TRANSACTION_TYPE_CHARGE)
            ->setInvoiceId($invoiceId);

        $paymentMethod = PaymentMethod::make([
            '_id' => 'payment-method-id',
            'branch_id' => '49a7011a05c677b9a916612a',
            'type_id' => 'CARD',
        ]);

        /** @var PaymentProviderContract $paymentProvider */
        $paymentProvider = Mockery::mock(PaymentProviderContract::class)
            ->allows([
                'subscriptions' => Mockery::mock(SubscriptionHandlerContract::class)
                    ->allows(['getById' => $subscription]),
                'invoices' => \Mockery::mock(InvoiceHandlerContract::class)
                    ->allows(['get' => $invoice]),
                'charges' => Mockery::mock(ChargeHandlerContract::class)
                    ->allows(['getById' => $charge, 'getRefundsByChargeId' => []]),
                'customers' => Mockery::mock(CustomerHandlerContract::class)
                    ->shouldReceive('getById')
                    ->andReturn(
                        (new Customer())->setId('cus_from_stripe')
                    )
                    ->once()
                    ->getMock()
                    ->allows(['create' => new Customer()]),
                'paymentMethod' => $paymentMethod,
            ]);

        $user = $this->usersRepository->addCriteria(new Id($userId))->firstOrFail();
        $subscriptionWebHookUserFinder = Mockery::mock(SubscriptionWebHookUserFinder::class);
        $subscriptionWebHookUserFinder
            ->shouldReceive('find')
            ->andReturn($user)
            ->once();

        $logger = Mockery::mock(LoggerInterface::class);
        $logger
            ->shouldReceive('info');

        $eventManager = Mockery::mock(EventManager::class);
        $eventManager
            ->shouldReceive('emit')
            ->withArgs(function (string $listener, array $params) {
                $this->assertSame(SubscriptionCycleWasPaid::class, $listener);

                return true;
            })
            ->once()
            ->shouldReceive('emit')
            ->withArgs(function (string $listener, array $params) {
                $this->assertSame(ChargeWasUpdated::class, $listener);

                return true;
            })
            ->once();

        $userMembershipUnlocker = Mockery::mock(UserMembershipUnlocker::class);
        $userMembershipUnlocker->shouldReceive('unlock')
            ->once();
        $renewSubscriptionService = Mockery::mock(RenewSubscriptionService::class);
        $membershipData = (object)[
            'duration' => (object)[
                'startDate'=> Carbon::parse('2025-01-01 13:00:00')->getTimestamp(),
                'expiryDate'=> Carbon::parse('2025-02-01 13:00:00')->getTimestamp(),
            ]
        ];
        $renewSubscriptionService
            ->shouldReceive('execute')
            ->andReturn($membershipData)
            ->getMock()
            ->shouldReceive('setInvoiceId')
            ->getMock()
            ->shouldReceive('setResourceId')
            ->getMock();

        $handler = new InvoicePaymentSucceeded(
            $event,
            $paymentProvider,
            app()->make(UsersRepository::class),
            $logger,
            $renewSubscriptionService,
            app()->make(MembershipRenewalValidatorServiceFactory::class),
            $subscriptionWebHookUserFinder,
            $userMembershipUnlocker,
            $eventManager,
            app()->make(SubscriptionsEventPublisher::class),
            $this->featureFlag
        );
        $status = $handler->handle();

        self::assertSame(200, $status);
    }

    public function testSubscriptionStartAndEndDatesAreSame(): void
    {
        $irisIsGrantAdvancedCreditByUserMembershipIdFlagger = Mockery::mock(Flagger::class);
        $irisIsGrantAdvancedCreditByUserMembershipIdFlagger->shouldReceive('withFlag')->andReturnSelf();
        $irisIsGrantAdvancedCreditByUserMembershipIdFlagger->shouldReceive('hasByBranchId')->andReturn(true);

        app()->instance(Flagger::class, $irisIsGrantAdvancedCreditByUserMembershipIdFlagger);

        $startDate = Carbon::now();
        $invoiceId = '62e2f038a6a74e7671619dd3';
        $event = collect([
            'id' => 'evt_123',
            'psp_response' => collect([
                'subscription_id' => 'sub_testSubscriptionPayment',
                'customer_account_id' => 'cusAcctID',
                'invoice' => [
                    'id' => $invoiceId,
                    'amount' => 100,
                    'latest_transaction_group' => [
                        'id' => 'trx-group-1',
                        'transactions' => [
                            [
                                'id' => 'trx-1',
                            ],
                        ],
                    ],
                ],
                'line_items' => [
                    [
                        'resource_id' => 'cchg_2CWJzd6MDcgur4NOlXz4YuFYcHX',
                        'amount' => 0,
                        'name' => 'Membership 1',
                        'description' => '',
                        'service' => ServiceType::MEMBERSHIPS,
                        'external_reference' => '62e2f038a6a74e7671619dee',
                    ]
                ],
                'data' => [
                    'object' => [
                        'lines' => [
                            'type' => 'invoice.payment_succeeded',
                            'data' => [
                                [
                                    'period' => [
                                        'start' => $startDate->getTimestamp(),
                                        'end' => Carbon::now()->getTimestamp(),
                                    ],
                                ],
                            ],
                        ],
                    ],
                ],
            ])->toJson(),
        ]);

        $userId = '5d6826348d8540851fe60777';

        $subscription = new Subscription();
        $subscription->setId('sub_00X');

        $invoice = new InvoiceEntity();
        $invoice->setId($invoiceId);
        $invoice->setStatus(InvoiceStatus::PAID());

        $charge = new Transaction();
        $charge->setStatus(Status::PAYMENT_STATUS_SUCCESS)
            ->setId('charge1')
            ->setAmount(100)
            ->setCurrency('CAD')
            ->setCustomerAccountId($userId)
            ->setCreatedAt(Carbon::now()->getTimestamp())
            ->setUpdatedAt(Carbon::now()->getTimestamp())
            ->setPaymentMethod('CARD')
            ->setType(Type::TRANSACTION_TYPE_CHARGE)
            ->setInvoiceId($invoiceId);

        $paymentMethod = PaymentMethod::make([
            '_id' => 'payment-method-id',
            'branch_id' => '49a7011a05c677b9a916612a',
            'type_id' => 'CARD',
        ]);

        /** @var PaymentProviderContract $paymentProvider */
        $paymentProvider = Mockery::mock(PaymentProviderContract::class)
            ->allows([
                'subscriptions' => Mockery::mock(SubscriptionHandlerContract::class)
                    ->allows(['getById' => $subscription]),
                'invoices' => \Mockery::mock(InvoiceHandlerContract::class)
                    ->allows(['get' => $invoice]),
                'charges' => Mockery::mock(ChargeHandlerContract::class)
                    ->allows(['getById' => $charge, 'getRefundsByChargeId' => []]),
                'customers' => Mockery::mock(CustomerHandlerContract::class)
                    ->shouldReceive('getById')
                    ->andReturn(
                        (new Customer())->setId('cus_from_stripe')
                    )
                    ->once()
                    ->getMock()
                    ->allows(['create' => new Customer()]),
                'paymentMethod' => $paymentMethod,
            ]);

        $user = $this->usersRepository->addCriteria(new Id($userId))->firstOrFail();
        $subscriptionWebHookUserFinder = Mockery::mock(SubscriptionWebHookUserFinder::class);
        $subscriptionWebHookUserFinder
            ->shouldReceive('find')
            ->andReturn($user)
            ->once();

        $logger = Mockery::mock(LoggerInterface::class);
        $logger
            ->shouldReceive('info');

        $eventManager = Mockery::mock(EventManager::class);
        $eventManager
            ->shouldReceive('emit')
            ->withArgs(function (string $listener, array $params) {
                $this->assertSame(SubscriptionCycleWasPaid::class, $listener);

                return true;
            })
            ->once()
            ->shouldReceive('emit')
            ->withArgs(function (string $listener, array $params) {
                $this->assertSame(ChargeWasUpdated::class, $listener);

                return true;
            })
            ->once();

        $userMembershipUnlocker = Mockery::mock(UserMembershipUnlocker::class);
        $userMembershipUnlocker->shouldReceive('unlock')
            ->once();
        $membershipData = (object)[
            'duration' => (object)[
                'startDate'=> Carbon::parse('2025-01-01 13:00:00')->getTimestamp(),
                'expiryDate'=> Carbon::parse('2025-02-01 13:00:00')->getTimestamp(),
            ]
        ];
        $renewSubscriptionService = Mockery::mock(RenewSubscriptionService::class);
        $renewSubscriptionService->shouldReceive('execute')
            ->andReturn($membershipData)
            ->getMock()
            ->shouldReceive('setInvoiceId')
            ->getMock()
            ->shouldReceive('setResourceId')
            ->getMock();
        $handler = new InvoicePaymentSucceeded(
            $event,
            $paymentProvider,
            app()->make(UsersRepository::class),
            $logger,
            $renewSubscriptionService,
            app()->make(MembershipRenewalValidatorServiceFactory::class),
            $subscriptionWebHookUserFinder,
            $userMembershipUnlocker,
            $eventManager,
            app()->make(SubscriptionsEventPublisher::class),
            $this->featureFlag
        );
        $status = $handler->handle();

        self::assertSame(200, $status);

        $creditsRepository = app()->make(CreditsRepository::class);
        $credits = $creditsRepository->addCriteria(new UserId($userId))->first();

        self::assertSame('1506335805777', $credits->planCode());
        self::assertTrue($credits->startDate()->isSameAs($startDate->startOfDay()));
        self::assertTrue($credits->expiryDate()->isSameAs($startDate->addWeek(1)->endOfDay()));

        $source = $credits->source();

        \assert($source instanceof CreditPackSourceMembershipRenewed);

        self::assertSame('MEMBERSHIP_RENEWED', $source->type()->getValue());
        self::assertSame('62e2f038a6a74e7671619dd3', $source->invoiceId());
        self::assertSame('cchg_2CWJzd6MDcgur4NOlXz4YuFYcHX', $source->resourceId());
        app()->forgetInstance(Flagger::class);
    }

    public function testHandlerStoresSubscriptionChargeWithLineItems(): void
    {
        $invoiceId = (string) new MongoId();
        $event = collect([
            'id' => 'evt_123',
            'psp_response' => collect([
                'customer_account_id' => 'cusAcctID',
                'subscription_id' => 'sub_testSubscriptionPayment',
                'invoice' => [
                    'id' => $invoiceId,
                    'amount' => 100,
                    'latest_transaction_group' => [
                        'id' => 'trx-group-1',
                        'transactions' => [
                            [
                                'id' => 'trx-1',
                            ],
                        ],
                    ],
                ],
                'line_items' => [
                    [
                        'service' => 'services',
                        'amount' => 11,
                        'external_reference' => 'mock-serviceId:mock-serviceDefinitionId',
                    ],
                ],
                'data' => [
                    'object' => [
                        'lines' => [
                            'type' => 'invoice.payment_succeeded',
                            'data' => [
                                [
                                    'period' => [
                                        'start' => Carbon::now()->getTimestamp(),
                                        'end' => Carbon::tomorrow()->getTimestamp(),
                                    ],
                                ],
                            ],
                        ],
                        'id' => $invoiceId,
                    ],
                ],
            ],
            )->toJson(),
        ]);

        $userId = '5d6826348d8540851fe60662';
        $branchId = '49a7011a05c677b9a916612a';

        $subscription = new Subscription();
        $subscription->setId('sub_00X');

        $invoice = new InvoiceEntity();
        $invoice->setId($invoiceId);
        $invoice->setStatus(InvoiceStatus::PAID());

        $charge = new Transaction();
        $charge->setStatus(Status::PAYMENT_STATUS_SUCCESS)
            ->setId('charge1')
            ->setAmount(100)
            ->setCurrency('CAD')
            ->setCustomerAccountId($userId)
            ->setCreatedAt(Carbon::now()->getTimestamp())
            ->setUpdatedAt(Carbon::now()->getTimestamp())
            ->setPaymentMethod('CARD')
            ->setType(Type::TRANSACTION_TYPE_CHARGE)
            ->setInvoiceId($invoiceId);

        $paymentMethod = PaymentMethod::make([
            '_id' => 'payment-method-id',
            'branch_id' => $branchId,
            'type_id' => 'CARD',
        ]);

        /** @var PaymentProviderContract $paymentProvider */
        $paymentProvider = Mockery::mock(PaymentProviderContract::class)
            ->allows([
                'subscriptions' => Mockery::mock(SubscriptionHandlerContract::class)
                    ->allows(['getById' => $subscription]),
                'invoices' => \Mockery::mock(InvoiceHandlerContract::class)
                    ->allows(['get' => $invoice]),
                'charges' => Mockery::mock(ChargeHandlerContract::class)
                    ->allows(['getById' => $charge, 'getRefundsByChargeId' => []]),
                'customers' => Mockery::mock(CustomerHandlerContract::class)
                    ->shouldReceive('getById')
                    ->andReturn(
                        (new Customer())->setId('cus_from_stripe')
                    )
                    ->once()
                    ->getMock()
                    ->allows(['create' => new Customer()]),
                'paymentMethod' => $paymentMethod,
            ]);

        $user = $this->usersRepository->addCriteria(new Id($userId))->firstOrFail();
        $subscriptionWebHookUserFinder = Mockery::mock(SubscriptionWebHookUserFinder::class);
        $subscriptionWebHookUserFinder
            ->shouldReceive('find')
            ->andReturn($user)
            ->once();

        $logger = Mockery::mock(LoggerInterface::class);
        $logger
            ->shouldReceive('info');

        $eventManager = Mockery::mock(EventManager::class);
        $eventManager
            ->shouldReceive('emit')
            ->withArgs(function (string $listener, array $params) {
                $this->assertSame(SubscriptionCycleWasPaid::class, $listener);

                return true;
            })
            ->once()
            ->shouldReceive('emit')
            ->withArgs(function (string $listener, array $params) {
                $this->assertSame(ChargeWasUpdated::class, $listener);

                return true;
            })
            ->once();

        $userMembershipUnlocker = Mockery::mock(UserMembershipUnlocker::class);
        $userMembershipUnlocker->shouldReceive('unlock')
            ->once();

        $serviceId = 'mock-serviceId';
        $serviceDefinitionId = 'mock-serviceDefinitionId';

        $addonServiceInterface = \Mockery::mock(AddonServiceInterface::class);
        $addonServiceInterface
            ->shouldReceive('getAddon')
            ->withArgs(
                function (string $serviceIdSentToService, string $branchIdIdSentToService) use ($serviceId, $branchId) {
                    self::assertEquals($serviceId, $serviceIdSentToService);
                    self::assertEquals($branchId, $branchIdIdSentToService);

                    return true;
                })
            ->andReturn(new Addon(
                $serviceId,
                $serviceDefinitionId,
                'mock-serviceDefinitionPlanId',
                'Tanning',
                'Recurring',
                BookableEntityType::FACILITY,
                Carbon::yesterday()->getTimestamp(),
                false
            ))
            ->once();

        app()->instance(AddonServiceInterface::class, $addonServiceInterface);
        $membershipData = (object)[
            'duration' => (object)[
                'startDate'=> Carbon::parse('2025-01-01 13:00:00')->getTimestamp(),
                'expiryDate'=> Carbon::parse('2025-02-01 13:00:00')->getTimestamp(),
            ]
        ];
        $renewSubscriptionService = Mockery::mock(RenewSubscriptionService::class);
        $renewSubscriptionService->shouldReceive('execute')
            ->andReturn($membershipData)
            ->getMock()
            ->shouldReceive('setInvoiceId')
            ->getMock()
            ->shouldReceive('setResourceId')
            ->getMock();
        $handler = new InvoicePaymentSucceeded(
            $event,
            $paymentProvider,
            app()->make(UsersRepository::class),
            $logger,
            $renewSubscriptionService,
            app()->make(MembershipRenewalValidatorServiceFactory::class),
            $subscriptionWebHookUserFinder,
            $userMembershipUnlocker,
            $eventManager,
            app()->make(SubscriptionsEventPublisher::class),
            $this->featureFlag
        );
        $status = $handler->handle();

        self::assertSame(200, $status);

        app()->forgetInstance(AddonServiceInterface::class);
    }

    private function givenFeatureFlagsMock(
        bool $isUseMembershipServiceDatesOnCreditsEnabled = false,
        bool $isRestrictedRenewalThroughMembershipsEnabled = false,
        array $orderInWhichFFsWillBeReturned = []
    ): FeatureFlagInterface {
        $flagger = Mockery::mock(FeatureFlagInterface::class);
        $flagger
            ->shouldReceive('withFlag')->with(
                Flag::IS_RESTRICTED_RENEWAL_THROUGH_MEMBERSHIPS_ENABLED()
            )->andReturnSelf()
            ->getMock();

        $flagger->shouldReceive('hasByBranchId')->andReturnValues(
            $orderInWhichFFsWillBeReturned ?? [
            $isUseMembershipServiceDatesOnCreditsEnabled,
            $isRestrictedRenewalThroughMembershipsEnabled
        ]);

        return $flagger;
    }
}
