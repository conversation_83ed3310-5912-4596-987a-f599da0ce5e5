<?php

namespace Glofox\Response\Models\Branches\Features;

use Glofox\Response\BaseModel;

/**
 * @SuppressWarnings(PHPMD)
 */
class StoreModel extends BaseModel
{
    /**
     * @var bool
     */
    public $enabled = false;

    /**
     * @var int
     */
    public $order = 0;

    protected array $allowedFields = [
        'order',
        'enabled',
    ];

    public function __construct(array $data)
    {
        $this->setModelFields($data);
    }
}
