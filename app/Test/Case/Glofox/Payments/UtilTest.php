<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Payments;

use Glofox\Payments\Util;

\App::import('Test/Case', 'GlofoxTestCase');

class UtilTest extends \GlofoxTestCase
{
    /**
     * @dataProvider centsDataProvider
     * @param $input
     * @param int $expectation
     */
    public function test_it_converts_float_to_cents_notation_with_two_decimal_cases($input, int $expectation): void
    {
        $result = Util::convertToCents($input);

        $this->assertSame($expectation, $result);
    }

    public function centsDataProvider(): array
    {
        return [
            [19.99, 1999],
            [20.03, 2003],
            [299.99, 29999],
            ["19.99", 1999],
            ["19.98", 1998],
            ["19.90", 1990],
            [19.998, 1999],
        ];
    }
}
