<?php

declare(strict_types=1);

namespace CakeTestCases\Response\Models\Branches\Features\Message;

use App;
use Glofox\Response\Models\Branches\Features\Message\SmsModel;
use GlofoxTestCase;

App::import('Test/Case', 'GlofoxTestCase');

class SmsModelTest extends GlofoxTestCase
{
    public $fixtures = [];

    /**
     * @dataProvider modelProvider
     */
    public function testModel(array $data, SmsModel $expectedResult): void
    {
        $actualResult = new SmsModel($data);
        $this->assertEquals($expectedResult, $actualResult);
    }

    public function modelProvider(): array
    {
        return [
            'when data has expected fields, then it creates the model' => [
                [
                    'enabled' => true,
                ],
                new SmsModel(
                    [
                        'enabled' => true,
                    ]
                ),
            ],
            'when data has additional fields, then it creates the model ignoring those fields' => [
                [
                    'enabled' => true,
                    'test-extra-field' => 1,
                ],
                new SmsModel(
                    [
                        'enabled' => true,
                    ]
                ),
            ],
            'when data is empty, then it creates the model with default values and expected structure' => [
                [],
                new SmsModel(
                    [
                        'enabled' => false,
                    ]
                ),
            ],
        ];
    }
}
