<?php

namespace Glofox\Authentication\PasswordRules;

use Glofox\Domain\Users\Models\User;

class PasswordDoesNotIncludePersonalDetails implements PasswordRuleInterface
{
    public function check(User $target, string $password): bool
    {
        $parts = explode('@', $target->email());

        $personalDetails = $parts;

        $personalDetails[] = $target->firstName();
        $personalDetails[] = $target->lastName();
        $personalDetails[] = $target->phone();

        foreach ($personalDetails as $detail) {

            if (strstr($password, (string) $detail)) {
                return false;
            }
        }

        return true;
    }

    public function getMessage(): string
    {
        return 'Your password must not contain personal details like your email, name or phone number';
    }

    public function getMessageCode(): string
    {
        return 'PASSWORD_RULE_NO_PERSONAL_DETAILS';
    }
}
