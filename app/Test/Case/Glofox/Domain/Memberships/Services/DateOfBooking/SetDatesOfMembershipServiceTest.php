<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\Memberships\Services\DateOfBooking;

use Glofox\Domain\Memberships\Services\DateOfBooking\SetDatesOfMembershipService;
use Glofox\Domain\Memberships\Services\MembershipsClient;
use Glofox\Domain\Users\Models\UserMembership;
use Psr\Log\LoggerInterface;

\App::import('Test/Case', 'GlofoxTestCase');

class SetDatesOfMembershipServiceTest extends \GlofoxTestCase
{
    public $fixtures = [];

    private $membershipsClient;

    private $logger;

    public function setUp()
    {
        parent::setUp();

        $this->membershipsClient = \Mockery::spy(MembershipsClient::class);
        $this->logger = \Mockery::mock(LoggerInterface::class);
        $this->logger->shouldReceive('warning');
    }

    public function tearDown()
    {
        parent::tearDown();
        \Mockery::close();
    }

    private function createService()
    {
        return new SetDatesOfMembershipService($this->membershipsClient, $this->logger);
    }

    public function test_it_should_post_to_memberships_service_dates_for_user_membership()
    {
        $userMembership = \Mockery::mock(UserMembership::class);
        $userMembership->shouldReceive('userMembershipId')->andReturn('123123');
        $startDate = new \DateTime('now');
        $userMembership->shouldReceive('get')->with('start_date')->andReturn($startDate);
        $endDate = new \DateTime('+1 month');
        $userMembership->shouldReceive('get')->with('expiry_date')->andReturn($endDate);

        $this->createService()->execute($userMembership);

        $this->membershipsClient
            ->shouldHaveReceived('post')
            ->with(
                'UPDATE_DURATION',
                [
                    'userMembershipId' => '123123'
                ],
                [
                    'startDate' => $startDate,
                    'expiryDate' => $endDate,
                ]
            );
    }

    public function test_it_should_skip_posting_to_memberships_service_dates_if_no_user_membership_id_is_present(): void
    {
        $userMembership = \Mockery::mock(UserMembership::class);
        $userMembership->shouldReceive('userMembershipId')->andReturn(null);

        $this->createService()->execute($userMembership);

        $this->membershipsClient
            ->shouldNotHaveReceived('post');
    }
}
