version: 2.1

orbs:
  gf-tests: glofoxinc/e2e-tests-orb@0
  gf-circle: glofoxinc/gf-cicd-orb@1

.services: &services
  - "api"
  - "api-primary"
  - "apiworker-default"
  - "apiworker-batch"
  - "api-internal"
  - "api-internal-mmt"
  - "apiworker-fulfilment"
  - "apiworker-memberships-priority"

# the default pipeline parameters, which will be updated according to
# the results of the path-filtering orb
parameters:
  run-e2e-tests-and-deploy:
    type: boolean
    default: false
  deploy-only:
    type: boolean
    default: true

jobs:
  stub-job:
    docker:
      - image: cimg/node:17.2.0
    steps:
      - run: echo "Stub job"

workflows:
  e2e-tests-and-deploy:
    when: << pipeline.parameters.run-e2e-tests-and-deploy>>
    jobs:
      - gf-tests/run-e2e-tests:
          context: org-global
          name: << matrix.command >>
          command: npm run test:api:<< matrix.command >>:ci
          matrix:
            alias: mandatory-e2e-tests
            parameters:
              command:
                [
                  "dashboard:payments",
                  "dashboard:members",
                  "dashboard:reports",
                  "dashboard:permissions",
                  "dashboard:appointments",
                  "pro_app",
                  "dashboard:store",
                  "dashboard:appointments",
                  "member_app",
                  "cart",
                  "dashboard:memberships",
                  "dashboard:bookings",
                ]
          filters:
            branches:
              only: master
      - gf-tests/run-e2e-tests:
          context: org-global
          name: portal
          command: npm run test:ci -- --suite portal
          filters:
            branches:
              only: master
      - gf-tests/run-e2e-tests:
          context: org-global
          name: << matrix.command >>
          command: npm run test:api:<< matrix.command >>:ci
          circle_ip_ranges: true
          matrix:
            alias: non-mandatory-e2e-tests
            parameters:
              command: ["pos"]
          filters:
            branches:
              only: master
      - stub-job:
          name: Wait for approval to deploy
          type: approval
          requires:
            - portal
            - mandatory-e2e-tests
          filters:
            branches:
              only: master
      - gf-circle/ecs_deploy:
          context: deployments
          name: "<< matrix.environment >> Deploy << matrix.service >>"
          environment: << matrix.environment >>
          service: << matrix.service >>
          matrix:
            parameters:
              environment: ["staging", "platform"]
              service: *services
          requires:
            - Wait for approval to deploy
          filters:
            branches:
              only: master
      - gf-circle/generate-client:
          name: "Generate Client"
          input_file_path: "docs/private-openapi/openapi.yml"
          service_name: coreapi
          context: org-global
          requires:
            - Wait for approval to deploy
          filters:
            branches:
              only: master
      - gf-circle/ecs-wait-for-stable:
          context: org-global
          name: "Wait for Stable Platform"
          environment: platform
          service: api api-primary apiworker-default api-internal api-internal-mmt apiworker-fulfilment apiworker-memberships-priority
          requires:
            - "platform Deploy api"
            - "platform Deploy api-primary"
            - "platform Deploy apiworker-default"
            - "platform Deploy api-internal"
            - "platform Deploy api-internal-mmt"
            - "platform Deploy apiworker-fulfilment"
            - "platform Deploy apiworker-memberships-priority"
          filters:
            branches:
              only: master

  deploy:
    when: << pipeline.parameters.deploy-only >>
    jobs:
      - gf-circle/ecs_deploy:
          context: deployments
          name: "<< matrix.environment >> Deploy << matrix.service >>"
          environment: << matrix.environment >>
          service: << matrix.service >>
          matrix:
            parameters:
              environment: ["staging", "platform"]
              service: *services
          filters:
            branches:
              only: master
      - gf-circle/generate-client:
          name: "Generate Client"
          input_file_path: "docs/private-openapi/openapi.yml"
          service_name: coreapi
          context: org-global
          filters:
            branches:
              only: master
      - gf-circle/ecs-wait-for-stable:
          context: org-global
          name: "Wait for Stable Platform"
          environment: platform
          service: api api-primary apiworker-default api-internal api-internal-mmt apiworker-fulfilment apiworker-memberships-priority
          requires:
            - "platform Deploy api"
            - "platform Deploy api-primary"
            - "platform Deploy apiworker-default"
            - "platform Deploy api-internal"
            - "platform Deploy api-internal-mmt"
            - "platform Deploy apiworker-fulfilment"
            - "platform Deploy apiworker-memberships-priority"
          filters:
            branches:
              only: master
