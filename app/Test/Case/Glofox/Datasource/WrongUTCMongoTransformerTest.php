<?php

namespace CakeTestCases\Glofox\Datasource;

use Glofox\Datasource\WrongUTCMongoTransformer;

\App::import('Test/Case', 'GlofoxTestCase');

class WrongUTCMongoTransformerTest extends \GlofoxTestCase
{
    public function testFromRequestToDB()
    {
        $result = WrongUTCMongoTransformer::fromRequestToDB(1_542_967_200, 'Asia/Kolkata');
        $this->assertEquals(1_542_987_000, $result);
    }

    public function testFromDBToResponse()
    {
        $mongoDate = new \MongoDate(1_542_987_000);
        $result = WrongUTCMongoTransformer::fromDBToResponse($mongoDate, 'Asia/Kolkata');
        $this->assertEquals(1_542_967_200, $result);
    }
}
