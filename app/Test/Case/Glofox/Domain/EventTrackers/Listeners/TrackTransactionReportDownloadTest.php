<?php

namespace CakeTestCases\Glofox\Domain\EventTrackers\Listeners;

use App;
use GlofoxTestCase;
use CakeTestCases\Glofox\Domain\Authentication\Token\TokenGeneration;
use CakeTestCases\Glofox\Domain\Users\Traits\FetchUsersTrait;
use Glofox\Domain\AsyncEvents\Events\TrackingEventMeta;
use Glofox\Domain\AsyncEvents\Events\TrackingEventPayload;
use Glofox\Domain\EventTrackers\Listeners\TrackTransactionReportDownload;
use Glofox\Domain\EventTrackers\Services\TrackEventPublisher;
use League\Event\EventInterface;
use Mockery;

App::import('Test/Case', 'GlofoxTestCase');

class TrackTransactionReportDownloadTest extends GlofoxTestCase
{
    use FetchUsersTrait;
    use TokenGeneration;

    private ?array $event = null;

    public function setUp(): void
    {
        parent::setUp();

        $this->event = [
            'branch_id' => '123',
            'data' => ['foo']
        ];
    }

    public function tearDown(): void
    {
        parent::tearDown();

        Mockery::close();
    }

    public function test_handle_method_requests_tracking_the_proper_data(): void
    {
        $adminUser = $this->fetchUser('5dc5bb4d7d54963c7c846b5a');
        $this->loginAsUser($adminUser);

        $trackEventPublisher = Mockery::mock(TrackEventPublisher::class);
        $trackEventPublisher
            ->shouldReceive('sendEventToTrack')
            ->withArgs(function (TrackingEventMeta $meta, TrackingEventPayload $payload) {
                $this->assertEquals(
                    new TrackingEventPayload(
                        [
                            'eventName' => 'Transaction report downloaded',
                            'branchId' => $this->event['branch_id'],
                            'userId' => '5dc5bb4d7d54963c7c846b5a',
                            'platform' => 'CORE-API',
                            'data' => $this->event['data']
                        ]
                    ),
                    $payload
                );

                return true;
            })
            ->once();

        $listener = new TrackTransactionReportDownload($trackEventPublisher);
        $listener->handle($this->mockEvent());
    }

    private function mockEvent(): EventInterface
    {
        $event = Mockery::mock(EventInterface::class);
        $event
            ->shouldReceive('getBranchId')
            ->andReturn($this->event['branch_id'])
            ->once()
            ->getMock()
            ->shouldReceive('getData')
            ->andReturn($this->event['data'])
            ->once()
            ->getMock();

        return $event;
    }
}
