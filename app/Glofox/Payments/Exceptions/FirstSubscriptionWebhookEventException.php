<?php

namespace Glofox\Payments\Exceptions;

class FirstSubscriptionWebhookEventException extends \Exception
{
    protected $code = 203;

    public static function withSubscriptionId(string $subscriptionId)
    {
        $message = "First subscription webhook ignored, information is already saved on Glofox. Subscription Id: %s";

        return new self(
            sprintf(
                $message,
                $subscriptionId
            )
        );
    }
}
