<?php

namespace Glofox\Response\Transformers;

use Glofox\Domain\Clients\Search\Expressions\ClientNamespace;
use Glofox\Domain\TimeSlots\Models\TimeSlot;
use Glofox\Domain\Users\Repositories\UsersRepository;
use Glofox\Domain\Users\Search\Expressions\Type;
use Glofox\Domain\Users\Services\Avatar\AvatarUrlFactory;
use Glofox\Domain\Users\UserTypesCollection;
use Glofox\Repositories\Search\Expressions\Shared\BranchId;
use Glofox\Response\Models\BasicModels\MainModel;
use Glofox\Response\Models\EventModel;
use Glofox\Response\Models\Events\FacilityObjModel;
use Glofox\Response\Models\Events\ProgramObj\PricingModel;
use Glofox\Response\Models\Events\ProgramObjModel;
use Glofox\Response\Models\Events\TrainersObjModel;

/**
 * Class EventsTransformer
 * @package Glofox\Response\Transformers
 * @SuppressWarnings(PHPMD)
 */
class EventsTransformer extends BaseTransformer
{
    private UsersRepository $usersRepository;
    private AvatarUrlFactory $avatarUrlFactory;

    public function __construct()
    {
        $this->usersRepository = app()->make(UsersRepository::class);
        $this->avatarUrlFactory = app()->make(AvatarUrlFactory::class);
    }

    /**
     * @param array $results
     * @return MainModel
     */
    public function setFromArrayCollection(array $results): MainModel
    {
        $dataModelArrayCollection = [];
        $namespace = $results['data'][0]['namespace'] ?? null;
        $branchId = $results['data'][0]['branch_id'] ?? null;

        if (!empty($results) && !empty($namespace) && !empty($branchId)) {
            $clientTrainers = $this->usersRepository
                ->addCriteria(new ClientNamespace($namespace))
                ->addCriteria(new BranchId($branchId))
                ->addCriteria(new Type(UserTypesCollection::make([\UserType::TRAINER()])))
                ->find();

            foreach ($results['data'] as $data) {
                $dataModelArrayCollection[] = $this->setEventModel($data, (array)$clientTrainers);
            }
        }

        return $this->setMainModelFromArray($results, $dataModelArrayCollection);
    }

    /**
     * @param array $event
     * @return EventModel
     */
    public function setEventModel(array $event = [], array $clientTrainers): EventModel
    {
        $trainersObjModel = [];
        if (isset($event['type']) && $event['type'] === 'course') {
            $event['trainers_obj'] = $this->setTrainersObjForCourses($event, $clientTrainers);
        }

        if (isset($event['trainers_obj']) && !empty($event['trainers_obj'])) {
            foreach ($event['trainers_obj'] as $trainer) {
                $trainersObjModel[] = $this->setTrainersObjModel($trainer);
            }
        }

        if ($this->isAppointmentSlot($event)) {
            $appointmentSlot = TimeSlot::make($event);
            $trainer = $appointmentSlot->staff();
            $trainer['image_url'] = $this->avatarUrlFactory->create($trainer);

            $trainersObjModel[] = $this->setTrainersObjModel($trainer->toArray());
            $event['image_url'] = $trainer['image_url'];
        }

        $programObjModel = null;
        if (isset($event['program_obj'])) {
            $programObjModel = $this->setProgramObjModel($event['program_obj']);
        }

        $facilityObjModel = null;
        if (isset($event['facility_obj'])) {
            $facilityObjModel = $this->setFacilityObjModel($event['facility_obj']);
        }

        $eventModelData = [
            '_id' => $event['_id'] ?? '',
            'namespace' => $event['namespace'] ?? '',
            'branch_id' => $event['branch_id'] ?? '',
            'program_id' => $event['program_id'] ?? '',
            'active' => $event['active'] ?? false,
            'name' => $event['name'] ?? '',
            'description' => $event['description'] ?? '',
            'time_start' => $event['time_start'] ?? 0,
            'level' => $event['level'] ?? null,
            'size' => $event['size'] ?? 0,
            'facility' => $event['facility'] ?? '',
            'trainers' => $event['trainers'] ?? [],
            'private' => $event['private'] ?? false,
            'booked' => $event['booked'] ?? 0,
            'waiting' => $event['waiting'] ?? 0,
            'has_booked' => $event['has_booked'] ?? false,
            'booking_status' => $event['booking_status'] ?? null,
            'duration' => $event['duration'] ?? 0,
            'type' => $event['type'] ?? '',
            'program_obj' => $programObjModel,
            'trainers_obj' => $trainersObjModel,
            'facility_obj' => $facilityObjModel,
            'position' => $event['position'] ?? null,
            'course_id' => $event['course_id'] ?? null,
            'session_id' => $event['session_id'] ?? null,
            'open_booking_time' => $event['open_booking_time'] ?? null,
            'close_booking_time' => $event['close_booking_time'] ?? null,
            'image_url' => $event['image_url'] ?? '',
            'status' => $event['status'] ?? null,
            'booking_id' => $event['booking_id'] ?? null,
            'model' => $event['model'] ?? null,
            'model_id' => $event['model_id'] ?? null,
            'current_user_eligibility' => $event['current_user_eligibility'] ?? null,
            'is_online' => $event['is_online'] ?? false,
            'modified' => $event['modified'] ?? 0,
        ];

        if (isset($event['parent'])) {
            $eventModelData['parent'] = $event['parent'];
        }

        return new EventModel($eventModelData);
    }

    /**
     * @param array $trainer
     * @return TrainersObjModel
     */
    public function setTrainersObjModel(array $trainer): TrainersObjModel
    {
        return new TrainersObjModel(
            [
                '_id' => $trainer['_id'] ?? '',
                'first_name' => $trainer['first_name'] ?? '',
                'last_name' => $trainer['last_name'] ?? '',
                'branch_id' => $trainer['branch_id'] ?? '',
                'type' => $trainer['type'] ?? '',
                'image_url' => $trainer['image_url'] ?? '',
                'name' => $trainer['name'] ?? '',
                'description' => $trainer['description'] ?? '',
            ]
        );
    }

    /**
     * @param array $facility
     * @return FacilityObjModel
     */
    public function setFacilityObjModel(array $facility): FacilityObjModel
    {
        return new FacilityObjModel(
            [
                '_id' => $facility['_id'] ?? '',
                'branch_id' => $facility['branch_id'] ?? '',
                'description' => $facility['description'] ?? '',
                'type' => $facility['type'] ?? '',
                'name' => $facility['name'] ?? '',
                'modified' => $facility['modified'] ?? 0,
                'image_url' => $facility['image_url'] ?? '',
            ]
        );
    }

    /**
     * @param $program
     * @return ProgramObjModel
     */
    public function setProgramObjModel(array $program): ProgramObjModel
    {
        $programData = $program;
        $pricing = [];
        if (isset($program['pricing']) && !empty($program['pricing'])) {
            foreach ($program['pricing'] as $price) {
                $pricing[] = $this->setPricingModel($price);
            }
        }

        return new ProgramObjModel(
            [
                '_id' => $programData['_id'] ?? '',
                'allowed_member_types' => $programData['allowed_member_types'] ?? [],
                'branch_id' => $programData['branch_id'] ?? '',
                'description' => $programData['description'] ?? '',
                'modified' => $programData['modified'] ?? 0,
                'name' => $programData['name'] ?? '',
                'pricing' => $pricing,
                'type' => $programData['type'] ?? '',
                'image_url' => $programData['image_url'] ?? '',
                'room_map_image_url' => $programData['room_map_image_url'] ?? null,
                'categories' => $programData['categories'] ?? null,
            ]
        );
    }

    /**
     * @param array $pricing
     * @return PricingModel
     */
    public function setPricingModel(array $pricing): PricingModel
    {
        return new PricingModel(
            [
                'price' => $pricing['price'] ?? '',
                'name' => $pricing['name'] ?? '',
                'type' => $pricing['type'] ?? '',
            ]
        );
    }

    private function setTrainersObjForCourses(array $course, array $clientTrainers): array
    {
        $trainersObj = [];
        foreach ($course['trainers'] as $trainerID) {
            $trainerKey = array_search(
                $trainerID,
                array_column(
                    json_decode(
                        json_encode($clientTrainers, JSON_PARTIAL_OUTPUT_ON_ERROR),
                        true,
                        512,
                        JSON_PARTIAL_OUTPUT_ON_ERROR
                    ),
                    '_id'
                )
            );
            $trainerObjModel = (array)$this->setTrainersObjModel($clientTrainers[$trainerKey]->toArray());
            array_push($trainersObj, $trainerObjModel);
        }

        return $trainersObj;
    }

    private function isAppointmentSlot(array $event): bool
    {
        return (!isset($event['trainers_obj']) || empty($event['trainers_obj']))
            && isset($event['model'])
            && $event['model'] === 'appointments'
            && isset($event['staff_id']);
    }
}
