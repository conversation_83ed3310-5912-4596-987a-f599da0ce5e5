parameters:
  - schema:
      type: string
      pattern: '^[a-f\d]{24}$'
    name: branchId
    in: path
    required: true
  - schema:
      type: string
      pattern: '^[a-f\d]{24}$'
    name: payrollRateId
    in: path
    required: true
get:
  tags:
    - Payroll Rates
  summary: ''
  operationId: get-payroll-rate-by-id
  description: |-
    This endpoint returns payroll rate data. If the id is not found, it returns a
    404 status code with an error.
  x-internal: true
  security:
    - coreapi_auth: [ ]
  responses:
    '200':
      description: OK
      content:
        application/json:
          schema:
            $ref: '../../schemas/payroll_rate.yml'
    '404':
      description: Bad request
      content:
        application/json:
          schema:
            type: object
            properties:
              success:
                type: boolean
                default: false
              message:
                type: string
                enum:
                  - You must provide a Payroll Rate ID
              message_code:
                type: string
                enum:
                  - EMPTY_PAYROLL_RATE_ID
              message_data:
                type: array
                items:
                  type: string
              errors:
                type: array
                items:
                  type: string
put:
  tags:
    - Payroll Rates
  summary: ''
  operationId: update-payroll-rate-by-id
  description: If the id is not found, it returns a 404 status code with an error
  x-internal: true
  security:
    - coreapi_auth: [ ]
  requestBody:
    required: true
    content:
      application/json:
        schema:
          $ref: '../../requests/2.2/create_payroll_rate.yml'
  responses:
    '200':
      description: OK
      content:
        application/json:
          schema:
            $ref: '../../schemas/payroll_rate.yml'
    '404':
      description: Not found
      content:
        application/json:
          schema:
            $ref : "../../schemas/error_payroll_rate.yml"
