<?php

use Glofox\Console\ShellHelper;
use Glofox\Domain\Authentication\Auth;
use Glofox\Domain\Users\Models\User;
use Glofox\Payments\Entities\Transaction\Models\Transaction;
use Glofox\Payments\Services\CustomChargeResponse;
use Glofox\Payments\Services\CustomChargeService;
use Glofox\Payments\Services\CustomChargeUseCase;
use Illuminate\Support\Collection;
use League\Csv\AbstractCsv;
use League\Csv\Reader;
use League\Csv\Writer;
use Psr\Log\LoggerInterface;

App::uses('AmazonS3', 'AmazonS3.Lib');

/*
 * The input CSV format is member_id,branch_id,description,payment_method,sold_by_user_id,amount
 * where description and sold_by_user_id are optional
 */

/**
 * Class BatchCustomChargeShell
 */
class BatchCustomChargeShell extends AppShell
{
    use ShellHelper;

    private const DEFAULT_CUSTOM_CHARGE_DESCRIPTION = 'CUSTOM_CHARGE';

    /**
     * @var Dictionary
     */
    private Dictionary $dictionary;

    /**
     * @var User
     */
    private $userCakeModel;

    /**
     * @var string
     */
    private string $branchId;

    /**
     * @var bool
     */
    private bool $isDryRun;

    /**
     * @var string
     */
    private string $sourceFilePath;

    /**
     * @var string
     */
    private string $reportFilePath;

    /**
     * @var bool
     */
    private bool $uploadReport;

    /**
     * @var LoggerInterface
     */
    private LoggerInterface $logger;

    /**
     * @var Collection
     */
    private Collection $reportHeaderNameMap;

    private ?AbstractCsv $reportCsvWriter = null;

    public function __construct($stdout = null, $stderr = null, $stdin = null)
    {
        parent::__construct($stdout, $stderr, $stdin);
        $this->userCakeModel = ClassRegistry::init('User',);
    }

    /**
     * @throws Exception
     */
    public function main()
    {
        $this->parseParams();
        $this->init();

        $filepath = $this->getCsvFileFromS3($this->sourceFilePath);
        $sourceFileReader = Reader::createFromPath($filepath, 'r');
        $records = $sourceFileReader->fetchAssoc();
        $records->next();
        $this->validateCsvStructure(array_keys($records->current()));

        $admin = $this->userCakeModel->findLatestAdminForOriginBranch($this->branchId, null);
        $this->logger->info('found latest admin', ['admin' => $admin['User'] ?? $admin]);
        Auth::loginAs(User::make($admin['User'] ?? $admin));
        $this->logger->info('logged in as admin', ['admin' => $admin['User'] ?? $admin]);

        foreach ($records as $row) {
            $this->validateCsvRow($row);

            if ($this->isDryRun) {
                $this->logger->info('execution mode is dry-run, skipping charge');
                continue;
            }

            try {
                $this->customCharge($row);
            } catch (Exception $ex) {
                $this->logger->warning($ex->getMessage());
                $this->logger->warning($ex->getTraceAsString());
                $row['error'] = $ex->getMessage();
            }

            $this->insertReportEntry($row);
        }

        if (!$this->uploadReport) {
            return;
        }

        $s3Path = $this->getS3PathForBranchEnvironment($this->branchId);
        try {
            $this->uploadReportToS3($this->reportFilePath, $s3Path);
        } catch (Exception $e) {
            $this->logger->error(sprintf('error uploading to S3: %s', $e->getMessage()));
            throw $e;
        }
    }

    /**
     * @param string $filePath
     * @return string
     */
    private function getCsvFileFromS3(string $filePath): string
    {
        $config = Configure::read('amazon_s3');
        $amazonS3 = new AmazonS3(
            [
                $config['access_key'],
                $config['secret_key'],
                $config['bucket_name'],
                $config['region']
            ]
        );

        $amazonS3->get($filePath, TMP);

        return TMP . $filePath;
    }

    /**
     * @return string[]
     */
    private function getInputHeaders(): array
    {
        return ['member_id', 'branch_id', 'description', 'payment_method', 'sold_by_user_id', 'amount'];
    }

    /**
     * @param array $headers
     * @throws Exception
     */
    private function validateCsvStructure(array $headers)
    {
        $expectedColumns = count($this->getInputHeaders());
        if (count($headers) > $expectedColumns) {
            throw new Exception(sprintf('too many columns, expecting %d columns', $expectedColumns));
        }
        if (count($headers) < $expectedColumns) {
            throw new Exception(sprintf('too few columns, expecting %d columns', $expectedColumns));
        }
    }

    /**
     * @param array $row
     * @throws Exception
     */
    private function validateCsvRow(array $row)
    {
        $failFunc = function (string $msg) use ($row) {
            $this->logger->error($msg, $row);
            throw new Exception($msg);
        };

        if (empty($row['member_id'])) {
            $failFunc('member_id is required');
        }

        if (empty($row['branch_id'])) {
            $failFunc('branch_id is required');
        }

        if ($row['branch_id'] !== $this->branchId) {
            $failFunc(
                sprintf(
                    'the branch_id specified in the csv does not match the script branch id %s != %s',
                    $row['branch_id'],
                    $this->branchId
                )
            );
        }

        if (empty($row['payment_method'])) {
            $failFunc('payment_method is required');
        }

        if (empty($row['amount'])) {
            $failFunc('amount is required');
        }

        if ($row['amount'] <= 0) {
            $failFunc('amount must be a positive float');
        }
    }

    /**
     * @param array $row
     * @return mixed|string|string[]
     */
    private function extractDescription(array $row)
    {
        $description = $row['description'];
        if (strlen($description) == 0) {
            $description = $this->dictionary->getTranslatedWordByBranchIdAndWordKey(
                $row['branch_id'],
                static::DEFAULT_CUSTOM_CHARGE_DESCRIPTION,
            );
        }

        return $description;
    }

    /**
     * @param array $row
     * @return Transaction
     * @throws Exception
     */
    private function customCharge(array $row): Transaction
    {
        $memberId = $row['member_id'];
        $branchId = $row['branch_id'];
        $soldBy = $row['sold_by_user_id'];
        if (empty($soldBy)) {
            $soldBy = null;
        }
        $description = $this->extractDescription($row);

        $member = $this->userCakeModel->findByIdAndBranchId($memberId, $branchId);
        if (!$member) {
            throw new UnauthorizedException(sprintf('cannot find member [%s] on branch [%s]', $memberId, $branchId));
        }

        /** @var CustomChargeResponse $result */
        $result = app()->make(CustomChargeService::class)
            ->execute(
                new CustomChargeUseCase(
                    $memberId,
                    $branchId,
                    $row['payment_method'],
                    $description,
                    $soldBy,
                    (float)$row['amount'],
                    true,
                    [],
                    [],
                    Auth::user()
                )
            );

        $transaction = $result->transaction();
        $successfulTransaction = $transaction->isPaid() || $transaction->isPendingOrPendingIntent();

        if ($successfulTransaction) {
            return $transaction;
        }
        throw new Exception(
            sprintf(
                'transaction %s failed due to: %s',
                $transaction->id() ?? $transaction->invoiceId(),
                $transaction->failureReason()
            )
        );
    }

    /**
     * @return ConsoleOptionParser
     */
    public function getOptionParser(): ConsoleOptionParser
    {
        $parser = parent::getOptionParser();
        $parser->addOption('dry-run', [
            'short' => 'd',
            'help' => 'Should perform a dry-run and not charge customers',
            'boolean' => true,
        ]);

        $parser->addOption('upload-report', [
            'short' => 'u',
            'help' => 'Upload report to s3',
            'boolean' => true,
        ]);

        $parser->addOption('source-file', [
            'short' => 's',
            'help' => 'The customers csv file',
            'default' => '',
        ]);

        $parser->addOption('report-path', [
            'short' => 'r',
            'help' => 'The path to the folder to output csv the report',
            'default' => null,
        ]);

        $parser->addOption('branch-id', [
            'short' => 'b',
            'help' => 'The id of the branch for which we are running the script',
            'default' => '',
        ]);

        return $parser;
    }


    /**
     * @throws Exception
     */
    private function parseParams(): void
    {
        $this->branchId = (string)$this->param('branch-id');
        if (empty($this->branchId)) {
            throw new Exception('The branch id is missing');
        }

        $this->sourceFilePath = (string)$this->param('source-file');
        if (empty($this->sourceFilePath)) {
            throw new Exception('The import csv file is missing');
        }

        $this->isDryRun = true;
        if ($this->param('dry-run') !== null) {
            $this->isDryRun = (bool)$this->param('dry-run');
        }

        $reportPath = (string)$this->param('report-path');
        if (empty($reportPath)) {
            $reportPath = LOGS;
        }

        $this->reportFilePath = sprintf(
            '%s/batch-custom-charges-%s.csv',
            rtrim($reportPath, '/'),
            uniqid('', false)
        );

        $this->uploadReport = $this->params['upload-report'];

        $this->log(sprintf('Report path is: %s', $this->reportFilePath), LOG_INFO);
        $this->log(sprintf('Run mode is: %s', $this->isDryRun ? 'dry-run' : 'NOT dry-run'), LOG_INFO);
    }

    private function init(): void
    {
        $this->logger = app()->make(LoggerInterface::class);

        $this->reportHeaderNameMap = new Collection();
        $reportHeaders = $this->buildReportHeaders();
        foreach ($reportHeaders as $header) {
            $this->reportHeaderNameMap[$header] = 'n/a';
        }

        $this->reportCsvWriter = $this->buildReportWriter();
    }

    /**
     * @return AbstractCsv
     */
    private function buildReportWriter(): AbstractCsv
    {
        $reportCsvWriter = Writer::createFromPath($this->reportFilePath, 'w+');
        $reportCsvWriter->insertOne(array_keys($this->reportHeaderNameMap->toArray()));

        return $reportCsvWriter;
    }


    /**
     * @param array $result
     */
    private function insertReportEntry(array $result): void
    {
        $entry = [];
        foreach ($this->reportHeaderNameMap as $key => $defaultValue) {
            $entry[$key] = $result[$key] ?? $defaultValue;
        }

        $this->reportCsvWriter->insertOne($entry);
    }

    /**
     * @return string[]
     */
    private function buildReportHeaders(): array
    {
        return ['member_id', 'branch_id', 'description', 'payment_method', 'sold_by_user_id', 'amount', 'error'];
    }


}
