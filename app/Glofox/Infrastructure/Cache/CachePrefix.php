<?php

namespace Glofox\Infrastructure\Cache;

use Glofox\Exception;

class CachePrefix
{
    public const WEBHOOK_LOCKER = '[core][webhook-locker]';
    public const PAYMENT_METHOD_ONBOARDING_LOCKER = '[core][payment-method-onboarding-locker]';
    public const SETUP_INTENT_LOCKER = '[core][setup-intent-locker]';
    public const SPOT_BOOKING_LOCKER = '[core][spot-booking-locker]';
    public const API_WORKER_TASK_LOCKER = '[api-worker][task-locker]';
    public const BOOKING_APPOINTMENT_LOCKER = '[api-worker][booking-appointment-locker]';
    public const BOOKING_EVENT_LOCKER = '[core][booking-event-locker]';
    public const HTML_EVALUATION_LOCKER = '[communications][html-evaluation-locker]';
    public const USER_REQUESTED_PASSWORD_CHANGE = '[core][user_requested_password_change]';


    private string $prefix;

    public function __construct(string $prefix)
    {
        // @see https://glofox.slack.com/archives/CJ5HUQEEA/p1583245793122900?thread_ts=1583243419.120200&cid=CJ5HUQEEA
        if (!preg_match('/^\[(.*)\]\[(.*)\]$/', $prefix)) {
            throw new Exception(sprintf('Invalid redis prefix: %s', $prefix));
        }

        $this->prefix = $prefix;
    }

    public function toString(): string
    {
        return $this->prefix;
    }
}
