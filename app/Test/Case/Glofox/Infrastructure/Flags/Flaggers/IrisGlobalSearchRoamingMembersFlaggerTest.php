<?php

namespace CakeTestCases\Glofox\Infrastructure\Flags\Flaggers;

use App;
use Glofox\Domain\Branches\Models\Branch;
use Glofox\Domain\Experiments\Services\ExperimentsClient;
use Glofox\Infrastructure\Flags\Flaggers\IrisGlobalSearchRoamingMembersFlagger;
use GlofoxTestCase;
use Mockery;

App::import('Test/Case', 'GlofoxTestCase');

class IrisGlobalSearchRoamingMembersFlaggerTest extends GlofoxTestCase
{
    /** @var ExperimentsClient */
    private $irisHttpClient;

    public function setUp(): void
    {
        parent::setUp();
        $this->irisHttpClient = Mockery::mock(ExperimentsClient::class);
    }

    public function test_it_returns_true_on_should_enable_for_any_branch(): void
    {
        $branch = \Mockery::mock(Branch::class);
        $result = $this->createFlagger()->shouldEnable($branch);

        $this->assertTrue($result);
    }

    public function test_it_uses_correct_slug(): void
    {
        $result = $this->createFlagger()->getSlug();

        $this->assertEquals('global-search-roaming-members-iris', $result);
    }

    public function test_it_uses_correct_enabled_option(): void
    {
        $result = $this->createFlagger()->getTestEnabledOption();

        $this->assertEquals('ENABLED', $result);
    }

    private function createFlagger(): IrisGlobalSearchRoamingMembersFlagger
    {
        return new IrisGlobalSearchRoamingMembersFlagger(
            $this->irisHttpClient
        );
    }
}
