<?php

App::import('Test/Case', 'GlofoxTestCase');

use Glofox\Response\Models\Branches\Features\GeneralModel;

/**
 * Test cases
 * 1 Model contains all fields with data
 * 2 Missing fields and returns correct structure
 * 3 Additional fields and return correct structure
 * 4 Empty array fields and return correct structure.
 *
 * Class GeneralModelTest
 */
class GeneralModelInFeaturesTest extends GlofoxTestCase
{
    public $fixtures = [];

    /**
     * @dataProvider modelProvider
     */
    public function testModel($dataSent, $result): void
    {
        $model = new GeneralModel($dataSent);
        $this->assertEquals($model, $result);
    }

    public function modelProvider(): array
    {
        return [
            '1 Model contains all fields with data' => [
                [
                    'order' => 1,
                    'enabled' => false,
                    'login_method' => 'string',
                    'private_access' => true,
                ],
                new GeneralModel(
                    [
                        'order' => 1,
                        'enabled' => false,
                        'login_method' => 'string',
                        'private_access' => true,
                    ]
                ),
            ],
            '2 Missing fields and returns correct structure' => [
                [
                    // order missing
                    'enabled' => false,
                    'login_method' => 'string',
                    'private_access' => true,
                ],
                new GeneralModel(
                    [
                        'order' => 0,
                        'enabled' => false,
                        'login_method' => 'string',
                        'private_access' => true,
                    ]
                ),
            ],
            '3 Additional fields and return correct structure' => [
                [
                    'extra' => 'test', // additional field
                    'order' => 1,
                    'enabled' => false,
                    'login_method' => 'string',
                    'private_access' => true,
                ],
                new GeneralModel(
                    [
                        'order' => 1,
                        'enabled' => false,
                        'login_method' => 'string',
                        'private_access' => true,
                    ]
                ),
            ],
            '4 Empty array fields and return correct structure' => [
                [],
                new GeneralModel(
                    [
                        'order' => 0,
                        'enabled' => false,
                        'login_method' => '',
                        'private_access' => false,
                    ]
                ),
            ],
        ];
    }
}
