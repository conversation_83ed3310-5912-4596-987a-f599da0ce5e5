get:
  tags:
    - Staff
  summary: ''
  operationId: get-staff
  description: This endpoint returns a filtered staff list from a given branch
  x-internal: true
  security:
    - coreapi_auth: [ ]
  parameters:
    - schema:
        type: string
        enum: [ADMIN, MEMBER, RECEPTION, TRAINER]
      in: query
      name: type
      description: type of the staff
    - schema:
        type: string
      in: query
      name: active
    - schema:
        type: string
      in: query
      name: sort-by
      description: field | -field
    - schema:
        type: integer
        minimum: 1
      in: query
      name: page
    - schema:
        type: integer
        minimum: 1
        maximum: 50
      in: query
      name: limit
  responses:
    '200':
      description: OK
      content:
        application/json:
          schema:
            allOf:
              - $ref: "../../schemas/pagination.yml"
              - type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: "../../schemas/staff.yml"