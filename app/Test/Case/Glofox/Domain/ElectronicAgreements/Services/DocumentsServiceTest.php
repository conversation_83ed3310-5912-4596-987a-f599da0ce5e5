<?php

namespace CakeTestCases\Glofox\Domain\ElectronicAgreements\Services;

use CakeTestCases\Glofox\Domain\Authentication\Token\TokenGeneration;
use CakeTestCases\Glofox\Domain\Users\Traits\FetchUsersTrait;
use Glofox\Domain\Branches\Repositories\BranchesRepository;
use Glofox\Domain\ElectronicAgreements\Models\Agreement;
use Glofox\Domain\ElectronicAgreements\Models\Document;
use Glofox\Domain\ElectronicAgreements\Requests\CreateAgreementRequest;
use Glofox\Domain\ElectronicAgreements\Requests\RedirectAgreementRequest;
use Glofox\Domain\ElectronicAgreements\Services\DocumentsService;
use Glofox\Domain\ElectronicAgreements\Services\ElectronicAgreementsServiceInterface;
use Glofox\Domain\ElectronicAgreements\Trigger;
use Glofox\Domain\Memberships\Type as MembershipType;
use Glofox\Domain\Terms\Repositories\TermsRepository;
use Glofox\Domain\Terms\Type;
use Glofox\Domain\Users\Exceptions\UserNotFoundException;
use Glofox\Domain\Users\Models\User;
use Glofox\Domain\Users\Repositories\UsersRepository;
use Glofox\Infrastructure\ElectronicAgreements\ElectronicAgreementsHttpClient;
use League\Fractal\Manager;
use Psr\Log\LoggerInterface;

\App::import('Test/Case', 'GlofoxTestCase');

class DocumentsServiceTest extends \GlofoxTestCase
{
    use FetchUsersTrait;
    use TokenGeneration;

    /**
     * Fixtures.
     *
     * @var array
     */
    public $fixtures = [
        'app.branch',
        'app.event',
        'app.terms_condition',
        'app.user',
        'app.client',
        'app.access_control_list',
        'app.branch',
        'app.membership',
    ];

    private ?string $branchId = null;

    /**
     * @var User
     */
    private $user;

    private ?string $termId = null;
    /**
     * @var ElectronicAgreementsHttpClient|\Mockery\LegacyMockInterface|\Mockery\MockInterface
     */
    private $client;
    /**
     * @var TermsRepository|\Mockery\LegacyMockInterface|\Mockery\MockInterface
     */
    private $termsRepository;
    /**
     * @var UsersRepository|\Mockery\LegacyMockInterface|\Mockery\MockInterface
     */
    private $usersRepository;
    /**
     * @var Manager
     */
    private $fractalManager;

    private ?\Psr\Log\LoggerInterface $logger = null;

    public function setUp()
    {
        parent::setUp();

        app()->forgetInstance(ElectronicAgreementsServiceInterface::class);
        app()->forgetInstance(DocumentsService::class);
        app()->forgetInstance(UsersRepository::class);
        app()->forgetInstance(BranchesRepository::class);

        $this->client = \Mockery::mock(ElectronicAgreementsHttpClient::class);
        $this->termsRepository = app()->make(TermsRepository::class);
        $this->usersRepository = app()->make(UsersRepository::class);
        $this->fractalManager = app()->make(Manager::class);
        $this->branchId = '49a7011a05c677b9a916612a';
        $this->termId = '54259f18d7b6ddd9a25c9b2a';
        $this->logger = $this->makeMockedLogger();
    }

    private function makeMockedLogger(): LoggerInterface
    {
        $logger = \Mockery::mock(LoggerInterface::class);
        $logger->shouldReceive('debug');
        $logger->shouldReceive('info');
        $logger->shouldReceive('error');

        return $logger;
    }

    public function tearDown()
    {
        parent::tearDown();

        app()->forgetInstance(DocumentsService::class);

        \Mockery::close();
    }

    public function test_it_successfully_find_terms_and_conditions_from_legacy_implementation()
    {
        $this->client
            ->shouldReceive('isElectronicAgreementsEnabled')
            ->withArgs(fn(string $studioId) => $studioId == '49a7011a05c677b9a916612a')
            ->andReturn(false)
            ->once();

        $documentsService = new DocumentsService($this->client, $this->termsRepository, $this->usersRepository,
            $this->fractalManager, $this->logger);

        $result = $documentsService->findTermsAndConditions($this->branchId);

        $this->assertGreaterThan(1, $result);
        $this->assertEquals($result[0]['content'], '<p>Fancy terms</p>');
        $this->assertEquals($result[1]['content'], '<p>Fancy terms2</p>');
        $this->assertEquals($result[0]['service'], 'legacy');
        $this->assertEquals($result[1]['service'], 'legacy');

        app()->forgetInstance(ElectronicAgreementsServiceInterface::class);
    }

    public function test_it_successfully_find_terms_and_conditions_from_ea_implementation()
    {
        $this->client
            ->shouldReceive('isElectronicAgreementsEnabled')
            ->withArgs(fn(string $studioId) => $studioId == '49a7011a05c677b9a916612a')
            ->andReturn(true)
            ->once();

        $this->client
            ->shouldReceive('findDocuments')
            ->withArgs(fn(string $studioId) => $studioId == '49a7011a05c677b9a916612a')
            ->andReturn([
                new Document([
                    'id' => 'document_id',
                    'studio_id' => '49a7011a05c677b9a916612a',
                    'trigger' => Trigger::MEMBER_AUTHENTICATED,
                    'valid_agreement_kinds' => ['signature'],
                ], [
                    'document_id' => 'document_id',
                    'template' => 'template',
                ])
            ])->once();

        $this->client
            ->shouldReceive('isSupportedType')
            ->withArgs(fn(string $type) => $type == Trigger::MEMBER_AUTHENTICATED)
            ->andReturn(true)
            ->once();

        $this->client
            ->shouldReceive('isSupportedType')
            ->withArgs(fn(string $type) => $type == Trigger::MEMBERSHIP_PURCHASED)
            ->andReturn(false)
            ->once();

        $documentsService = new DocumentsService($this->client, $this->termsRepository, $this->usersRepository,
            $this->fractalManager, $this->logger);

        $result = $documentsService->findTermsAndConditions($this->branchId);

        $this->assertGreaterThan(1, $result);
        $this->assertEquals('template', $result[0]['content']);
        $this->assertEquals('<p>Fancy terms2</p>', $result[1]['content']);
        $this->assertEquals('electronic_agreements', $result[0]['service']);
        $this->assertEquals('legacy', $result[1]['service']);
        $this->assertEquals('document_id', $result[0]['_id']);

        app()->forgetInstance(ElectronicAgreementsServiceInterface::class);
    }

    public function test_it_successfully_find_terms_and_conditions_with_terms_from_ea_implementation()
    {
        $this->client
            ->shouldReceive('isElectronicAgreementsEnabled')
            ->withArgs(fn(string $studioId) => $studioId == '49a7011a05c677b9a916612a')
            ->andReturn(true)
            ->once();

        $this->client
            ->shouldReceive('findDocument')
            ->withArgs(fn(string $studioId, string $termId, array $includes) => $studioId == '49a7011a05c677b9a916612a' &&
                $termId == $this->termId &&
                $includes == ['latest_version'])
            ->andReturn(new Document([
                'id' => 'document_id',
                'studio_id' => '49a7011a05c677b9a916612a',
                'trigger' => Trigger::MEMBER_AUTHENTICATED,
                'valid_agreement_kinds' => ['signature'],
            ], [
                'document_id' => 'document_id',
                'template' => 'template',
            ]))->once();

        $this->client
            ->shouldReceive('isSupportedType')
            ->withArgs(fn(string $type) => $type == Trigger::MEMBER_AUTHENTICATED)
            ->andReturn(true)
            ->once();

        $documentsService = new DocumentsService($this->client, $this->termsRepository, $this->usersRepository,
            $this->fractalManager, $this->logger);

        $result = $documentsService->findTermsAndConditions($this->branchId, $this->termId, null, ['latest_version']);

        $this->assertGreaterThan(1, $result);
        $this->assertEquals('template', $result[0]['content']);
        $this->assertEquals('electronic_agreements', $result[0]['service']);

        app()->forgetInstance(ElectronicAgreementsServiceInterface::class);
    }

    public function test_accepted_membership_terms_falls_back_when_ea_not_enabled(): void
    {
        $this->client
            ->shouldReceive('isElectronicAgreementsEnabled')
            ->withArgs(fn(string $studioId) => $studioId == '49a7011a05c677b9a916612a')
            ->andReturn(false);

        $documentsService = new DocumentsService($this->client, $this->termsRepository, $this->usersRepository,
            $this->fractalManager, $this->logger);

        $result = $documentsService->hasAcceptedMembershipTerms('49a7011a05c677b9a916612a', User::make([
            'MEMBERPURCHASE' => true,
        ]));
        self::assertTrue($result);

        $result = $documentsService->hasAcceptedMembershipTerms('49a7011a05c677b9a916612a', User::make([
            'MEMBERPURCHASE' => false,
        ]));
        self::assertFalse($result);
    }

    public function test_accepted_membership_terms_uses_ea_when_ea_enabled(): void
    {
        $this->client
            ->shouldReceive('isElectronicAgreementsEnabled')
            ->withArgs(fn(string $studioId) => $studioId == '49a7011a05c677b9a916612a')
            ->andReturn(true);

        $documentsService = new DocumentsService($this->client, $this->termsRepository, $this->usersRepository,
            $this->fractalManager, $this->logger);

        // EA responds with empty list
        $this->client
            ->shouldReceive('listAgreements')
            ->withArgs(fn(string $studioId, string $memberId, bool $hasMembership, Trigger $trigger) => $studioId == '49a7011a05c677b9a916612a'
                && $memberId == '59a7011a05c677bda916612c'
                && $hasMembership == true
                && $trigger == Trigger::MEMBERSHIP_PURCHASED())
            ->andReturn([])
            ->once();

        $result = $documentsService->hasAcceptedMembershipTerms('49a7011a05c677b9a916612a', User::make([
            '_id' => '59a7011a05c677bda916612c',
            'membership' => [
                'type' => MembershipType::TIME
            ]
        ]));
        self::assertFalse($result);

        // EA responds with outdated agreement
        $this->client
            ->shouldReceive('listAgreements')
            ->withArgs(fn(string $studioId, string $memberId, bool $hasMembership, Trigger $trigger) => $studioId == '49a7011a05c677b9a916612a'
                && $memberId == '59a7011a05c677bda916612c'
                && $hasMembership == true
                && $trigger == Trigger::MEMBERSHIP_PURCHASED())
            ->andReturn([
                new Agreement([
                    'status' => Agreement::AGREEMENT_STATUS_OUTSTANDING,
                ]),
            ])
            ->once();

        $result = $documentsService->hasAcceptedMembershipTerms('49a7011a05c677b9a916612a', User::make([
            '_id' => '59a7011a05c677bda916612c',
            'membership' => [
                'type' => MembershipType::TIME
            ]
        ]));
        self::assertFalse($result);

        // EA responds with valid agreement
        $this->client
            ->shouldReceive('listAgreements')
            ->withArgs(fn(string $studioId, string $memberId, bool $hasMembership, Trigger $trigger) => $studioId == '49a7011a05c677b9a916612a'
                && $memberId == '59a7011a05c677bda916612c'
                && $hasMembership == true
                && $trigger == Trigger::MEMBERSHIP_PURCHASED())
            ->andReturn([
                new Agreement([
                    'status' => Agreement::AGREEMENT_STATUS_ACCEPTED,
                ]),
            ])
            ->once();

        $result = $documentsService->hasAcceptedMembershipTerms('49a7011a05c677b9a916612a', User::make([
            '_id' => '59a7011a05c677bda916612c',
            'membership' => [
                'type' => MembershipType::TIME
            ]
        ]));
        self::assertTrue($result);

        // EA responds with valid agreement when hasMembership is type payg
        $this->client
            ->shouldReceive('listAgreements')
            ->withArgs(fn(string $studioId, string $memberId, bool $hasMembership, Trigger $trigger) => $studioId == '49a7011a05c677b9a916612a'
                && $memberId == '59a7011a05c677bda916612c'
                && $hasMembership == false
                && $trigger == Trigger::MEMBERSHIP_PURCHASED())
            ->andReturn([
                new Agreement([
                    'status' => Agreement::AGREEMENT_STATUS_ACCEPTED,
                ]),
            ])
            ->once();

        $result = $documentsService->hasAcceptedMembershipTerms('49a7011a05c677b9a916612a', User::make([
            '_id' => '59a7011a05c677bda916612c',
            'membership' => [
                'type' => MembershipType::PAYG
            ]
        ]));
        self::assertTrue($result);
    }

    public function test_it_should_throw_exception_when_user_is_null_when_checking_for_outstanding(): void
    {
        $studioId = '49a7011a05c677b9a916612a';
        $memberId = 'invalid';

        $usersRepository = \Mockery::mock(UsersRepository::class);
        $usersRepository
            ->shouldReceive('findById')
            ->withArgs([$memberId])
            ->andReturn(null)
            ->once();

        $this->client
            ->shouldReceive('isElectronicAgreementsEnabled')
            ->withArgs(fn(string $id) => $id == $studioId)
            ->andReturn(true)
            ->once();


        self::setExpectedException(UserNotFoundException::class);
        $documentsService = new DocumentsService($this->client, $this->termsRepository, $usersRepository,
            $this->fractalManager, $this->logger);
        $documentsService->hasOutstandingAgreements($studioId, $memberId);
    }


    public function test_it_should_return_false_when_checking_outstanding_and_ea_is_not_enabled(): void
    {
        $studioId = '49a7011a05c677b9a916612a';
        $memberId = '49a7011a05c677b9a916612b';

        $this->client
            ->shouldReceive('isElectronicAgreementsEnabled')
            ->withArgs(fn(string $id) => $studioId == $id)
            ->andReturn(false)
            ->once();

        $documentsService = new DocumentsService($this->client, $this->termsRepository, $this->usersRepository,
            $this->fractalManager, $this->logger);
        self::assertFalse($documentsService->hasOutstandingAgreements($studioId, $memberId));
    }

    public function test_it_return_false_when_member_has_no_outstanding_agreements(): void
    {
        $studioId = '49a7011a05c677b9a916612a';
        $memberId = '49a7011a05c677b9a916612b';

        $usersRepository = \Mockery::mock(UsersRepository::class);
        $usersRepository
            ->shouldReceive('findById')
            ->withArgs(fn(string $id) => $memberId == $id)
            ->andReturn(['type' => 'MEMBER'])
            ->once();

        $this->client
            ->shouldReceive('isElectronicAgreementsEnabled')
            ->withArgs(fn(string $id) => $studioId == $id)
            ->andReturn(true)
            ->once();

        $this->client
            ->shouldReceive('hasOutstandingAgreements')
            ->withArgs(fn(string $sId, string $mId) => $studioId == $sId && $memberId == $mId)
            ->andReturn(false)
            ->once();

        $documentsService = new DocumentsService($this->client, $this->termsRepository, $usersRepository,
            $this->fractalManager, $this->logger);

        self::assertFalse($documentsService->hasOutstandingAgreements($studioId, $memberId));
    }

    public function test_it_should_return_true_when_member_has_outstanding_agreements(): void
    {
        $studioId = '49a7011a05c677b9a916612a';
        $memberId = '49a7011a05c677b9a916612b';

        $usersRepository = \Mockery::mock(UsersRepository::class);
        $usersRepository
            ->shouldReceive('findById')
            ->withArgs(fn(string $id) => $memberId == $id)
            ->andReturn(['type' => 'MEMBER'])
            ->once();

        $this->client
            ->shouldReceive('isElectronicAgreementsEnabled')
            ->withArgs(fn(string $id) => $studioId == $id)
            ->andReturn(true)
            ->once();

        $this->client
            ->shouldReceive('hasOutstandingAgreements')
            ->withArgs(fn(string $sId, string $mId) => $studioId == $sId && $memberId == $mId)
            ->andReturn(true)
            ->once();

        $documentsService = new DocumentsService($this->client, $this->termsRepository, $usersRepository,
            $this->fractalManager, $this->logger);

        self::assertTrue($documentsService->hasOutstandingAgreements($studioId, $memberId));
    }

    public function test_it_indicates_outstanding_agreements_in_batch(): void
    {
        $members = [
            User::make([
                '_id' => '49a7011a05c677b9a916612b',
                'branch_id' => '49a7011a05c677b9a916612a',
                'origin_branch_id' => '49a7011a05c677b9a916612a',
                'membership' => [
                    'type' => MembershipType::TIME
                ],
            ]),
            User::make([
                '_id' => '49a7011a05c677b9a916612c',
                'branch_id' => '49a7011a05c677b9a916612a',
                'origin_branch_id' => '49a7011a05c677b9a916612a',
                'membership' => [
                    'type' => MembershipType::PAYG
                ],
            ])
        ];

        $this->client
            ->shouldReceive('isElectronicAgreementsEnabled')
            ->withArgs(fn(string $studioId) => $studioId == '49a7011a05c677b9a916612a')
            ->andReturn(false)
            ->once();

        $documentsService = new DocumentsService($this->client, $this->termsRepository, $this->usersRepository,
            $this->fractalManager, $this->logger);

        // not enabled
        $res = $documentsService->hasOutstandingAgreementsByMembers($members);
        self::assertFalse($res[$members[0]->id()]);
        self::assertFalse($res[$members[1]->id()]);

        $this->client
            ->shouldReceive('isElectronicAgreementsEnabled')
            ->withArgs(fn(string $studioId) => $studioId == '49a7011a05c677b9a916612a')
            ->andReturn(true);

        $documentsService = new DocumentsService($this->client, $this->termsRepository, $this->usersRepository,
            $this->fractalManager, $this->logger);

        // empty agreements list
        $this->client
            ->shouldReceive('listAgreementsForMembers')
            ->withArgs(fn(string $studioId, array $memberIds, array $memberHasActiveMemberships) => $studioId == '49a7011a05c677b9a916612a'
                && $memberIds == ['49a7011a05c677b9a916612b', '49a7011a05c677b9a916612c']
                && $memberHasActiveMemberships == [true, false])
            ->andReturn([])
            ->once();

        $res = $documentsService->hasOutstandingAgreementsByMembers($members);
        self::assertFalse($res[$members[0]->id()]);
        self::assertFalse($res[$members[1]->id()]);

        // no outstanding agreement
        $this->client
            ->shouldReceive('listAgreementsForMembers')
            ->withArgs(fn(string $studioId, array $memberIds, array $memberHasActiveMemberships) => $studioId == '49a7011a05c677b9a916612a'
                && $memberIds == ['49a7011a05c677b9a916612b', '49a7011a05c677b9a916612c']
                && $memberHasActiveMemberships == [true, false])
            ->andReturn([
                '49a7011a05c677b9a916612b' => [
                    new Agreement(['status' => 'accepted']),
                    new Agreement(['status' => 'accepted_outdated']),
                ],
                '49a7011a05c677b9a916612c' => [
                    new Agreement(['status' => 'accepted']),
                    new Agreement(['status' => 'accepted_outdated']),
                ],
            ])
            ->once();

        $res = $documentsService->hasOutstandingAgreementsByMembers($members);
        self::assertFalse($res[$members[0]->id()]);
        self::assertFalse($res[$members[1]->id()]);

        // one has outstanding agreement
        $this->client
            ->shouldReceive('listAgreementsForMembers')
            ->withArgs(fn(string $studioId, array $memberIds, array $memberHasActiveMemberships) => $studioId == '49a7011a05c677b9a916612a'
                && $memberIds == ['49a7011a05c677b9a916612b', '49a7011a05c677b9a916612c']
                && $memberHasActiveMemberships == [true, false])
            ->andReturn([
                '49a7011a05c677b9a916612b' => [
                    new Agreement(['status' => 'accepted']),
                    new Agreement(['status' => 'accepted_outdated']),
                ],
                '49a7011a05c677b9a916612c' => [
                    new Agreement(['status' => 'accepted']),
                    new Agreement(['status' => 'outstanding']),
                ],
            ])
            ->once();

        $res = $documentsService->hasOutstandingAgreementsByMembers($members);
        self::assertFalse($res[$members[0]->id()]);
        self::assertTrue($res[$members[1]->id()]);

        // all have outstanding agreement
        $this->client
            ->shouldReceive('listAgreementsForMembers')
            ->withArgs(fn(string $studioId, array $memberIds, array $memberHasActiveMemberships) => $studioId == '49a7011a05c677b9a916612a'
                && $memberIds == ['49a7011a05c677b9a916612b', '49a7011a05c677b9a916612c']
                && $memberHasActiveMemberships == [true, false])
            ->andReturn([
                '49a7011a05c677b9a916612b' => [
                    new Agreement(['status' => 'outstanding']),
                    new Agreement(['status' => 'accepted_outdated']),
                ],
                '49a7011a05c677b9a916612c' => [
                    new Agreement(['status' => 'accepted']),
                    new Agreement(['status' => 'outstanding']),
                ],
            ])
            ->once();

        $res = $documentsService->hasOutstandingAgreementsByMembers($members);
        self::assertTrue($res[$members[0]->id()]);
        self::assertTrue($res[$members[1]->id()]);
    }

    public function test_it_successfully_find_latest_version_of_document_version()
    {
        $this->client
            ->shouldReceive('findDocumentByTrigger')
            ->withArgs(fn(string $studioId, Trigger $type, array $includes) => $studioId == '49a7011a05c677b9a916612a' &&
                $type == Trigger::MEMBERSHIP_PURCHASED() &&
                $includes == ['latest_version'])
            ->andReturn(new Document([
                'id' => 'document_id',
                'studio_id' => '49a7011a05c677b9a916612a',
                'trigger' => Trigger::MEMBER_AUTHENTICATED,
                'valid_agreement_kinds' => ['signature'],
            ], [
                'document_id' => 'document_id',
                'template' => 'template',
            ]))
            ->once();

        $documentsService = new DocumentsService($this->client, $this->termsRepository, $this->usersRepository,
            $this->fractalManager, $this->logger);

        $result = $documentsService->findLatestDocumentVersion($this->branchId, Type::MEMBERPURCHASE);

        $this->assertEquals('document_id', $result->id());
        $this->assertEquals('template', $result->latestVersion()->template());

        app()->forgetInstance(ElectronicAgreementsServiceInterface::class);
    }

    public function test_outstanding_agreements_for_empty_members_returns_empty(): void
    {
        $documentsService = new DocumentsService($this->client, $this->termsRepository, $this->usersRepository,
            $this->fractalManager, $this->logger);
        $res = $documentsService->hasOutstandingAgreementsByMembers([]);
        self::assertEquals([], $res);
    }

    public function test_outstanding_agreements_for_members_handles_roaming(): void
    {
        $members = [
            User::make([
                '_id' => '49a7011a05c677b9a916612b',
                'branch_id' => 'branch-a',
                'origin_branch_id' => 'branch-a',
            ]),
            User::make([
                '_id' => '49a7011a05c677b9a916612c',
                'branch_id' => 'branch-b',
                'origin_branch_id' => 'branch-b',
            ]),
            User::make([
                '_id' => '49a7011a05c677b9a916612d',
                'branch_id' => 'branch-a',
                'origin_branch_id' => 'branch-a',
            ]),
        ];

        $this->client
            ->shouldReceive('isElectronicAgreementsEnabled')
            ->withArgs(fn(string $studioId) => $studioId == 'branch-a')
            ->andReturn(true)
            ->once();

        $this->client
            ->shouldReceive('isElectronicAgreementsEnabled')
            ->withArgs(fn(string $studioId) => $studioId == 'branch-b')
            ->andReturn(true)
            ->once();

        $documentsService = new DocumentsService($this->client, $this->termsRepository, $this->usersRepository,
            $this->fractalManager, $this->logger);

        $this->client
            ->shouldReceive('listAgreementsForMembers')
            ->withArgs(fn(string $studioId, array $memberIds) => $studioId == 'branch-a'
                && $memberIds == ['49a7011a05c677b9a916612b', '49a7011a05c677b9a916612d'])
            ->andReturn([
                '49a7011a05c677b9a916612b' => [
                    new Agreement(['status' => 'outstanding']),
                    new Agreement(['status' => 'accepted_outdated']),
                ],
                '49a7011a05c677b9a916612d' => [
                    new Agreement(['status' => 'accepted']),
                    new Agreement(['status' => 'accepted']),
                ],
            ])
            ->once();
        $this->client
            ->shouldReceive('listAgreementsForMembers')
            ->withArgs(fn(string $studioId, array $memberIds) => $studioId == 'branch-b'
                && $memberIds == ['49a7011a05c677b9a916612c'])
            ->andReturn([
                '49a7011a05c677b9a916612c' => [
                    new Agreement(['status' => 'outstanding']),
                    new Agreement(['status' => 'accepted_outdated']),
                ],
            ])
            ->once();

        $res = $documentsService->hasOutstandingAgreementsByMembers($members);
        self::assertTrue($res[$members[0]->id()]);
        self::assertTrue($res[$members[1]->id()]);
        self::assertFalse($res[$members[2]->id()]);
    }

    public function test_it_creates_waiver_agreement(): void
    {
        $document = new Document([
            'trigger' => Trigger::MEMBER_AUTHENTICATED,
        ]);
        $newAgreement = new Agreement([
            'id' => '1',
        ]);

        $this->client->shouldReceive('createAgreement')
            ->withArgs(function (CreateAgreementRequest $request) {
                self::assertEmpty($request->getTemplateTags());
                return true;
            })
            ->andReturn($newAgreement)
            ->once();

        $documentsService = new DocumentsService($this->client, $this->termsRepository, $this->usersRepository,
            $this->fractalManager, $this->logger);
        $res = $documentsService->createAgreement($document, new CreateAgreementRequest(
            'memberId', 'studioId', 'documentId', 1,
            'other', 'outstanding', 'signature',
            'carl', 'weathers', '<EMAIL>', null
        ));
        self::assertEquals($res, $newAgreement);
    }

    public function test_it_creates_membership_agreement(): void
    {
        $document = new Document([
            'trigger' => Trigger::MEMBERSHIP_PURCHASED,
        ]);
        $newAgreement = new Agreement([
            'id' => '1',
            'memberId' => '5ecafa8b0ff27e6848419e88',
        ]);

        $this->client->shouldReceive('createAgreement')
            ->withArgs(function (CreateAgreementRequest $request) {
                self::assertEquals([
                    'name' => 'Starts on First Booking Membership',
                    'plan_name' => 'Start on first booking Credit Pack',
                    'currency' => 'EUR',
                    'upfront_fee' => 0,
                    'upfront_fee_edited' => false,
                    'price' => 1000,
                    'price_edited' => false,
                    'is_subscription' => false,
                    'interval' => null,
                    'interval_count' => 0,
                    'branch_name' => 'ClassPass Integration Branch',
                    'branch_address' => '66 Ranelagh Road, Ireland, , Dublin, Dublin, IE'
                ], $request->getTemplateTags()->get('membership_template_tags'));
                return true;
            })
            ->andReturn($newAgreement)
            ->once();

        $documentsService = new DocumentsService($this->client, $this->termsRepository, $this->usersRepository,
            $this->fractalManager, $this->logger);
        $res = $documentsService->createAgreement($document, new CreateAgreementRequest(
            '5ecafa8b0ff27e6848419e88', 'studioId', 'documentId', 1,
            'other', 'outstanding', 'signature',
            'carl', 'weathers', '<EMAIL>', "metadata"
        ));
        self::assertEquals($res, $newAgreement);
    }

    public function test_it_returns_agreement_redirect_url(): void
    {
        $document = new Document([
            'trigger' => Trigger::MEMBERSHIP_PURCHASED,
        ]);

        $this->client->shouldReceive('agreementRedirectUrl')
            ->withArgs(function (RedirectAgreementRequest $request) {
                self::assertEquals([
                    'name' => 'Starts on First Booking Membership',
                    'plan_name' => 'Start on first booking Credit Pack',
                    'currency' => 'EUR',
                    'upfront_fee' => 0,
                    'upfront_fee_edited' => false,
                    'price' => 1000,
                    'price_edited' => false,
                    'is_subscription' => false,
                    'interval' => null,
                    'interval_count' => 0,
                    'branch_name' => 'ClassPass Integration Branch',
                    'branch_address' => '66 Ranelagh Road, Ireland, , Dublin, Dublin, IE'
                ], $request->getTemplateTags()->get('membership_template_tags'));
                return true;
            })
            ->andReturn("example.com")
            ->once();

        $documentsService = new DocumentsService($this->client, $this->termsRepository, $this->usersRepository,
            $this->fractalManager, $this->logger);
        $res = $documentsService->agreementRedirectUrl($document, new RedirectAgreementRequest(
            '5ecafa8b0ff27e6848419e88', 'studioId', 'documentId',
            'carl', 'weathers', '<EMAIL>', null
        ));
        self::assertEquals("example.com", $res);
    }
}
