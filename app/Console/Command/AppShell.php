<?php

use Glofox\Infrastructure\Honeycomb\HoneycombTracker;

App::uses('Shell', 'Console');

/**
 * Application Shell
 *
 * Add your application-wide methods in the class below, your shells
 * will inherit them.
 *
 * @package       app.Console.Command
 */
class AppShell extends Shell
{
    public function __construct($stdout = null, $stderr = null, $stdin = null)
    {
        parent::__construct($stdout, $stderr, $stdin);

        $this->trackUsage();
    }

    private function trackUsage(): void
    {
        $honeycombTracker = app()->make(HoneycombTracker::class);

        $honeycombTracker->track(
            [
                'service_name' => 'track-shell-usage',
                'name' => get_class($this),
            ]
        );
    }
}
