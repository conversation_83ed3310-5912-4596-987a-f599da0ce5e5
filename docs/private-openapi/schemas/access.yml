type: object
properties:
  _id:
    type: string
    description: Unique identifier of the access log.
  user_id:
    type: string
    description: Unique identifier of the user accessed the studio.
  branch_id:
    type: string
    description: Unique identifier of the branch the user got access.
  namespace:
    type: string
    description: The namespace the studio belongs to.
  entry_at:
    type: integer
    description: The UTC Timestamp of the time when the user entered the studio.
  valid_on_entry:
    type: boolean
    description: |
      If the user's membership was valid when they entered the studio. 
      Or if there where upcoming active bookings.
  status:
    type: string
    enum:
      - GRANTED
      - FAILED
    description: |
      The status of the access request.
      The access is GRANTED if valid_on_entry is true or if it was granted directly by integrators.
      In all other cases the status would be FAILED.
      Important to note even when the status is not granted by the system,
      it does not prevent physical access to the studio.
  origin_branch_id:
    type: string
    description: |
      Unique identifier of the origin branch the user belongs to.
      It works for roaming studios, when the member belongs to the concrete branch,
      but is allowed to visit other branches within roaming.
  origin_branch_name:
    type: string
    description: The name of the origin branch the user belongs to.
  created:
    type: integer
    description: The UTC Timestamp when the access log was added.
  modified:
    type: integer
    description: The UTC Timestamp when the access log was modified.
