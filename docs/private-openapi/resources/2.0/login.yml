post:
  tags:
    - Auth
  summary: ''
  operationId: login
  x-internal: true
  description: If login fails, it returns a 200 status code with an error
  requestBody:
    required: true
    content:
      application/json:
        schema:
          type: object
          properties:
            branch_id:
              type: string
            login:
              type: string
              format: email
            password:
              type: string
              format: password
          required:
            - branch_id
            - login
            - password
  responses:
    '200':
      description: OK
      content:
        application/json:
          schema:
            type: object
            properties:
              user:
                $ref: '../../schemas/user.yml'
              token:
                type: string
              branch:
                $ref: '../../schemas/branch.yml'