@startuml
!theme cerulean-outline

User *-- UserAccount
User *-- UserLeadStatus
User *-- UserAgreement
User *-- UserTracking

class User {
  -id: String
  -firstName: String
  -lastName: String
  -phone: String
  -emergencyContact: String
  -birth: Date
  -gender: UserGender
  -type: String
  -active: Boolean
  -answers: Array
  -categories: Array
  -consent: UserConsent
  -metadata: UserMetadata
  -membership: UserMembership

  +id(): String
  +firstName(): String
  +lastName(): String
  +fullName(): String
  +phone(): String
  +emergencyContact(): String
  +birthDate(): Date
  +gender(): UserGender
  +type(): String
  +isStaff(): Boolean
  +isActive(): Boolean
  +answers(): Array
  +categories(): Array
  +consent(): UserConsent
  +metadata(): UserMetadata
  +membership(): UserMembership
}

class UserAccount {
  -email: String
  -login: String
  -password: String

  +email(): String
  +login(): String
  +password(): String
}

class UserLeadStatus {
  -leadStatus: String

  +leadStatus(): String
}

class UserAgreement {
  -waiver: Boolean
  -memberPurchase: Boolean
  -paygPayment: Boolean
  -smsWaiver: Boolean

  +waiver(): Boolean
  +memberPurchase(): Boolean
  +paygPayment(): Boolean
  +smsWaiver(): Boolean
}

class UserTracking {
  -device: UserDevice
  -source: String
  -namespace: String
  -branchId: String
  -originBranchId: String
  -created: Date
  -modified: Date
  -joinedAt: Date

  +device(): UserDevice
  +source(): String
  +namespace(): String
  +branchId(): String
  +originBranchId(): String
  +created(): Date
  +modified(): Date
  +joinedAt(): Date
}

class UserGender {
  -name: String
  -label: String

  +name(): String
  +label(): String
}

class UserParent {
  -parentId: String
  -useParentPhone: Boolean
  -useParentEmail: Boolean

  +parentId(): String
  +isUsingParentPhone(): Boolean
  +isUsingParentEmail(): Boolean
}

class UserDevice {
  -os: String
  -id: String
  -bundle: String
  -version: UserDeviceVersion

  +os(): String
  +id(): String
  +bundle(): String
  +version(): UserDeviceVersion
}

class UserDeviceVersion {
  -mayor: Integer
  -minor: Integer
  -revision: Integer

  +mayor(): Integer
  +minor(): Integer
  +revision(): Integer
}

class UserConsent {
  -email: UserConsentDetail
  -sms: UserConsentDetail
  -push: UserConsentDetail

  +email(): UserConsentDetail
  +sms(): UserConsentDetail
  +push(): UserConsentDetail
}

class UserConsentDetail {
  -active: Boolean
  -modifiedAt: Date
  -modifiedByUserId: String
  -modifiedFromIdAddress: Array
  -message: String

  +isActive(): Boolean
  +modifiedAt(): Date
  +modifiedByUserId(): String
  +modifiedFromIdAddress(): Array
  +message(): String
}

class UserMetadata {
  -insurance: UserMetadataInsurance
  -gympass: UserMetadataGympass
  -classpass: UserMetadataClasspass
  -fiscal: UserMetadataFiscal
  -twilio: UserMetadataTwilio

  +insurance(): UserMetadataInsurance
  +gympass(): UserMetadataGympass
  +classplass(): UserMetadataClasspass
  +fiscal(): UserMetadataFiscal
  +twilio(): UserMetadataTwilio
}

class UserMetadataInsurance {
  -policyNumber: String
  -companyId: String
  -groupId: String

  +policyNumber(): String
  +companyId(): String
  +groupId(): String
}

class UserMetadataGympass {
  -id: String

  +id(): String
}

class UserMetadataClasspass {
  -id: String

  +id(): String
}

class UserMetadataFiscal {
  -taxId: String

  +taxId(): String
}

class UserMetadataTwilio {
  -phoneNumber: String

  +phoneNumber(): String
}

class UserMembership {
  -id: String
  -type: String
  -userMembershipId: String
  -startDate: Date
  -status: String
  -membershipGroupId: String
  -planCode: String
  -planPrice: Double
  -planUpfrontFee: Double
  -branchId: String
  -branchName: String
  -roamingEnabled: Boolean
  -branches: Array
  -startOn: String
  -trial: Boolean
  -expiryDate: Date
  -bookedEvents: Integer
  -durationTimeUnit: String
  -durationTimeUnitCount: Integer
  -subscription: UserMembershipSubscription

  +id(): String
  +type(): String
  +userMembershipId(): String
  +startDate(): Date
  +status(): String
  +membershipGroupId(): String
  +planCode(): String
  +planPrice(): Double
  +planUpfrontFee(): Double
  +branchId(): String
  +branchName(): String
  +isRoamingEnabled(): Boolean
  +branches(): Array
  +startOn(): String
  +isTrial(): Boolean
  +expiryDate(): Date
  +bookedEvents(): Integer
  +durationTimeUnit(): String
  +durationTimeUnitCount(): Integer
  +subscription(): UserMembershipSubscription
}

class UserMembershipSubscription {
  -subscriptionPlanId: String
  -interval: String
  -intervalCount: Integer
  -price: Double
  -upfrontFee: Double
  -stripeId: String
  -paymentGatewayId: String
  -paymentMethodTypeId: String
  -autoRenewal: Boolean
  -forceStart: Boolean
  -pause: Boolean
  -duration: Integer
  -freeTimeUnitCount: Integer
  -endDate: Date
  -credits: UserMembershipSubscriptionCredit

  +subscriptionPlanId(): String
  +interval(): String
  +intervalCount(): Integer
  +price(): Double
  +upfrontFee(): Double
  +stripeId(): String
  +paymentGatewayId(): String
  +paymentMethodTypeId(): String
  +isAutoRenewal(): Boolean
  +isForceStart(): Boolean
  +isPause(): Boolean
  +duration(): Integer
  +freeTimeUnitCount(): Integer
  +endDate(): Date
  +credits(): UserMembershipSubscriptionCredit
}

class UserMembershipSubscriptionCredit {
  -branchId: String
  -namespace: String
  -active: Boolean
  -model: String
  -categoryId: String
  -numSessions: Integer
  -modelIds: Array
  -expiry: UserMembershipSubscriptionCreditExpiration
  -endDate: Date

  +branchId(): String
  +namespace(): String
  +active(): Boolean
  +model(): String
  +categoryId(): String
  +numSessions(): Integer
  +modelIds(): Array
  +expiry(): UserMembershipSubscriptionCreditExpiration
  +endDate(): Date
}

class UserMembershipSubscriptionCreditExpiration {
  -interval: String
  -intervalCount: Integer

  +interval(): String
  +intervalCount(): Integer
}

@enduml
