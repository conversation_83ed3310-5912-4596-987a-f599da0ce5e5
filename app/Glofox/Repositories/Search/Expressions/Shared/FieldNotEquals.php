<?php

namespace Glofox\Repositories\Search\Expressions\Shared;

use Glofox\Repositories\Filters\ConditionsCollection;
use Glofox\Repositories\Filters\Evaluable;
use Glofox\Repositories\Repository;
use Glofox\Repositories\Search\Expressions\Conditionable;
use Glofox\Repositories\Search\Expressions\HasConditions;
use Webmozart\Expression\Expr;
use Glofox\Repositories\Search\Expressions\Key;

class FieldNotEquals extends Key implements Evaluable, Conditionable
{
    use HasConditions;

    private string $field;

    private string $value;

    public function __construct(string $field, string $value)
    {
        $this->field = $field;
        $this->value = $value;

        parent::__construct($field, Expr::notEquals($value));
    }

    public function field() : string
    {
        return $this->field;
    }

    public function applyToConditions(ConditionsCollection $conditions, Repository $repository = null)
    {
        $conditions->push([
            $this->field() => ['$ne' => $this->value]
        ]);
    }
}
