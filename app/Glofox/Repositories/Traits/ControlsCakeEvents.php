<?php

namespace Glofox\Repositories\Traits;

use Glofox\Repositories\FetchType;
use Glofox\Repositories\FetchTypeWrapper;

trait ControlsCakeEvents
{
    protected $skipCallbacks = false;

    protected $skipValidations = false;

    public function skipCallbacks()
    {
        $this->skipCallbacks = true;

        return $this;
    }

    public function skipValidations()
    {
        $this->skipValidations = true;

        return $this;
    }
}
