<?php


class RefundFixture extends CakeTestFixture
{

    public $import = ['model' => 'Refund'];

    public function init()
    {
        $this->records = [
            [
                '_id' => new MongoId('5d286f992226df003b18d9a3'),
                'parent_id' => '5db89b3d2cab68041a2707ce',
                'branch_id' => '5db7f2d7e136e453eb0d4089',
                'namespace' => 'foo',
                'amount' => 3,
                'status' => 'SUCCESS',
                'created' => date('Y-m-d H:m:i'),
            ],
            [
                '_id' => new MongoId('5d28762c3ab2e4002d453795'),
                'parent_id' => '5db89b3d2cab68041a2707cd',
                'branch_id' => '5db7f2d7e136e453eb0d4089',
                'namespace' => 'foo',
                'amount' => 7,
                'status' => 'SUCCESS',
                'created' => date('Y-m-d H:m:i'),
            ],
            [
                '_id' => new MongoId('5d28762c3ab2e4002d453796'),
                'parent_id' => '5841700f7cad363dc88b4569',
                'branch_id' => '5db1b008f15a8f08c04f28af',
                'namespace' => 'csv-transaction-report',
                'amount' => 10,
                'status' => 'SUCCESS',
                'created' => date('Y-m-d H:i:s', strtotime('2019-10-26 14:08:23')),
            ],
            [
                '_id' => new MongoId('5a3a51b994b7bd9305000001'),
                'parent_id' => '5a3a5054948dae0706000001',
                'branch_id' => '5db1b008f15a8f08c04f28af',
                'namespace' => 'csv-transaction-report',
                'amount' => 10,
                'created' => date('Y-m-d H:i:s', strtotime('2019-10-26 14:09:23')),
                'status' => 'SUCCESS',
                'payout' => [
                    'id' => 'payout-4',
                    'transaction_id' => 'transaction-6',
                    'gross_amount' => -50,
                    'fee' => 0,
                    'net_amount' => 50,
                    'date' => '2019-10-26',
                ],
            ],
            [
                '_id' => new MongoId('5a3a51b994b7bd9305000009'),
                'parent_id' => '5a3a5054948dae0706000009',
                'branch_id' => '5db1b008f15a8f08c04f28af',
                'namespace' => 'csv-transaction-report',
                'amount' => 10,
                'created' => date('Y-m-d H:i:s', strtotime('2019-10-26 14:10:23')),
                'status' => 'SUCCESS',
                'payout' => [
                    'id' => 'payout-2',
                    'transaction_id' => 'transaction-3',
                    'gross_amount' => -50,
                    'fee' => 0,
                    'net_amount' => 50,
                    'date' => '2019-10-26',
                ],
            ],
            [
                '_id' => new MongoId('5a3a5054948dae0706000019'),
                'parent_id' => '5a3a5054948dae0706000019',
                'branch_id' => '5db1b008f15a8f08c04f28af',
                'namespace' => 'csv-transaction-report',
                'amount' => 10,
                'created' => date('Y-m-d H:i:s', strtotime('2019-10-26 14:11:23')),
                'status' => 'ERROR',
                'payout' => [
                    'id' => 'payout-2',
                    'transaction_id' => 'transaction-3',
                    'gross_amount' => -50,
                    'fee' => 0,
                    'net_amount' => -50,
                    'date' => '2019-10-26',
                ],
            ],
            [
                '_id' => new MongoId('5a3a51b994b7bd9305000004'),
                'parent_id' => '5a3a5054948dae0706000004',
                'branch_id' => '5db1b008f15a8f08c04f28a3',
                'namespace' => 'csv-transaction-report',
                'amount' => 10,
                'created' => date('Y-m-d H:i:s', strtotime('2019-10-26 14:09:23')),
                'status' => 'SUCCESS',
                'payout' => [
                    'id' => 'payout-4',
                    'transaction_id' => 'transaction-6',
                    'gross_amount' => -50,
                    'fee' => 0,
                    'net_amount' => 50,
                    'date' => '2019-10-26',
                ],
            ]
        ];

        parent::init();
    }

}
