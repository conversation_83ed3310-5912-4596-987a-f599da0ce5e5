type: object
properties:
  _id:
    type: string
    description: Announcment ID
  branch_id:
    type: string
  namespace:
    type: string
  active:
    type: boolean
  title:
    type: string
  content:
    type: string
  type:
    type: string
    enum:
      - IMAGE
      - VIDEO
      - ARTICLE
  author:
    type: object
    properties:
      user_id:
        type: string
      first_name:
        type: string
      last_name:
        type: string
  image_url:
    type: string
  video_url:
    type: string
  scheduled:
    type: object
    properties:
      is_scheduled:
        type: boolean
      start_date:
        type: integer
        description: Timestamp in UTC time zone.
      end_date:
        type: integer
        description: Timestamp in UTC time zone.
  tags:
    type: array
    description: An array of tags to mark the post.
    items:
      type: string
      enum:
        - News
        - Fitness
        - Trending
  restrictions:
    type: object
    description: 'Restict, who can open the content.'
    properties:
      memberships:
        type: array
        items: {}
  reasons:
    type: array
    description: |-
      Reasons array is returned as a part of response,
       but it doesn't exist in announcement Model in DB.
       I would suggest to make it deprecated.
    deprecated: true
    items:
      type: string
  created:
    type: integer
    description: Timestamp of created date and time.
  modified:
    type: integer
    description: Timestamp of modified date and time.
  published:
    type: integer
    description: Timestamp of published date and time.
required:
  - _id
  - branch_id
  - namespace
  - title
  - type