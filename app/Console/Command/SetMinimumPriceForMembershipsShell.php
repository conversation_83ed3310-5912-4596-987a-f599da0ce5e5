<?php

declare(strict_types=1);

use Glofox\Domain\Memberships\Exceptions\MembershipNotFoundException;
use Glofox\Domain\Memberships\Handlers\SetMinimumPriceCommand;
use Glofox\Domain\Memberships\Handlers\SetMinimumPriceHandler;
use Glofox\Domain\Memberships\Repositories\MembershipsRepository;
use Glofox\Domain\Memberships\Models\Membership;

App::uses('Shell', 'Console');

class SetMinimumPriceForMembershipsShell extends AppShell
{
    private MembershipsRepository $membershipsRepository;
    private SetMinimumPriceHandler $handler;

    public function __construct($stdout = null, $stderr = null, $stdin = null)
    {
        parent::__construct($stdout, $stderr, $stdin);

        $this->membershipsRepository = app()->make(MembershipsRepository::class);
        $this->handler = app()->make(SetMinimumPriceHandler::class);
    }

    public function main(): void
    {
        [$memberships, $minimumPriceRaw] = $this->args;

        $membershipsId = explode(',', $memberships);
        $priceUpdates = explode(',', $minimumPriceRaw);

        if (count($membershipsId) !== count($priceUpdates)) {
            $this->out(
                'Plans and membership ids are not correctly formatted. You need to mention membership id equal to each plan you want to update in it.'
            );

            return;
        }

        $plansPerMembership = $this->groupPlansPerMembership($membershipsId, $priceUpdates);

        $countMinimumPriceRaw = 0;

        foreach ($plansPerMembership as $id => $plansWithPrices) {
            $this->out(sprintf('Setting the minimum plan price for membership %s', $id));

            if (!$this->validateMembershipId($id)) {
                continue;
            }

            $countMinimumPriceRaw++;

            try {
                $this->handler->handle(SetMinimumPriceCommand::createFromRaw($id, $plansWithPrices));
            } catch (\Glofox\Exception $exception) {
                $this->out($exception->getMessage());
            }

            $this->out('');
            $this->out('============================================================');
            $this->out('');
        }

        $this->out(
            'To find if a minimum price is greater than the current price, search for "is greater than current price"'
        );
        $this->out('To find successful price setup, search for "Setting minimum price of"');
        $this->out('');
        $this->out(
            'If there was an error updating the membership, you will find a message like "Could not set minimum price to plans of membership"'
        );
        $this->out(
            'If membership does not have such plan, you will find a message like "does not have plan"'
        );
        $this->out(
            'If there was an error fetching the membership, you will find a message like "Membership not found"'
        );
        $this->out(
            sprintf(
                'A total of %s %s got updated',
                $countMinimumPriceRaw,
                $countMinimumPriceRaw !== 1 ? ' unique memberships' : 'membership'
            )
        );

        $this->validateAppliedChanges($memberships, $minimumPriceRaw);
    }

    private function validateMembershipId(string $membershipId): bool
    {
        try {
            $this->membershipsRepository->getById($membershipId);
        } catch (MembershipNotFoundException $exception) {
            $this->out(sprintf('Membership %s not found', $membershipId));

            return false;
        }

        return true;
    }

    private function validateAppliedChanges(string $membershipsIds, string $priceUpdates): void
    {
        $membershipsAfterUpdate = $this->membershipsRepository->findByIds(explode(',', $membershipsIds));
        $allPriceUpdates = explode(',', $priceUpdates);

        $priceUpdatePerPlan = [];

        foreach ($allPriceUpdates as $planCodeWithPrice) {
            $explodedPrice = explode(':', $planCodeWithPrice);
            $priceUpdatePerPlan[$explodedPrice[0]] = $explodedPrice[1];
        }

        foreach ($membershipsAfterUpdate as $membershipArray) {
            $membership = Membership::make($membershipArray['Membership']);

            foreach ($priceUpdatePerPlan as $planCode => $priceUpdate) {
                $updatedPlan = $membership->planByCode($planCode);

                if ($updatedPlan === null) {
                    continue;
                }

                $expectedPrice = (float)$priceUpdate;
                $actualPrice = $updatedPlan->minPrice();
                if ($actualPrice !== $expectedPrice) {
                    $this->out(
                        sprintf(
                            'PRICE UPDATE FAILURE: membership %s, planCode %s, expectedPrice %s, actualPrice %s',
                            $membership->id(),
                            $planCode,
                            $expectedPrice,
                            $actualPrice,
                        )
                    );
                }
            }
        }
    }

    private function groupPlansPerMembership(array $membershipsId, array $priceUpdates): array
    {
        $priceUpdatesPerMembership = [];

        foreach ($membershipsId as $index => $id) {
            if (!array_key_exists($id, $priceUpdatesPerMembership)) {
                $priceUpdatesPerMembership[$id] = [];
            }

            $priceUpdatesPerMembership[$id][] = $priceUpdates[$index];
        }

        return $priceUpdatesPerMembership;
    }
}
