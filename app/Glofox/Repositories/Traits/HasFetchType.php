<?php

namespace Glofox\Repositories\Traits;

use Glofox\Repositories\FetchType;
use Glofox\Repositories\FetchTypeWrapper;

trait HasFetchType
{

    /**
     * @var string
     */
    private $fetchType = FetchType::ALL;

    /**
     * @return string
     */
    protected function fetchType()
    {
        return $this->fetchType;
    }

    /**
     * @return string
     */
    protected function getFetchTypeAndReset()
    {
        $type = $this->fetchType;

        $this->resetFetchType();

        return $type;
    }

    /**
     * @param FetchType $type
     *
     * @return self
     */
    public function withFetchType(FetchType $type)
    {
        $this->fetchType = $type->getValue();

        return $this;
    }

    /**
     * @return $this
     */
    public function resetFetchType()
    {
        $this->withFetchType(FetchType::ALL());

        return $this;
    }

    /**
     * @return $this

    public function count()
    {
        $wrapper = new FetchTypeWrapper($this, FetchType::COUNT());

        return $wrapper;
    }

    /**
     * @return $this

    public function all()
    {
        $wrapper = new FetchTypeWrapper($this, FetchType::ALL());

        return $wrapper;
    }

    /**
     * @return $this

    public function first()
    {
        $wrapper = new FetchTypeWrapper($this, FetchType::FIRST());

        return $wrapper;
    }
     * */
}
