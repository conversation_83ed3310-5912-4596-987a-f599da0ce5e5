type: object
properties:
  _id:
    type: string
    description: The unique identifier for the facility
    pattern: '^[a-f\d]{24}$'
  location_id:
    type: string
    description: The unique identifier for the location
    pattern: '^[a-f\d]{24}$'
  description:
    type: string
    description: A brief summary of the facility
  name:
    type: string
    description: The name of the facility
  namespace:
    type: string
    description: The namespace the location is associated with
  bookable:
    type: boolean
    description: Indicates whether the facility is bookable
  is_online:
    type: boolean
    description: Indicates whether the facility is online
  categories:
    type: array
    items:
      type: string
      description: Unique identifier associated with the category
      pattern: '^[a-f\d]{24}$'
  list_visible:
    type: boolean
    description: Indicates whether the facility is set to public or private
  created_at:
    type: string
    format: date-time
    description: The date and time when the facility was created
