<?php

use Glofox\Console\ShellHelper;
use Glofox\Domain\ABTestClients\Repositories\ABTestClientsRepository;
use Glofox\Domain\ABTestClients\Search\Expressions\Type;
use Glofox\Domain\Authentication\Auth;
use Glofox\Domain\Branches\Models\Branch;
use Glofox\Domain\Branches\Repositories\BranchesRepository;
use Glofox\Domain\Branches\Search\Expressions\BranchId;
use Glofox\Domain\Events\Search\Expressions\Id;
use Glofox\Domain\Memberships\Services\Stack\FetchStackedMembershipsService;
use Glofox\Domain\Users\Models\User;
use Glofox\Domain\Users\Repositories\UsersRepository;
use Glofox\Domain\Users\Search\Expressions\UserMembershipId;
use Glofox\Domain\Users\Search\Expressions\UserMembershipType;
use Glofox\Repositories\Search\Expressions\Shared\Active;
use Glofox\Repositories\Search\Expressions\Shared\FieldNotExists;
use Glofox\Repositories\Search\Expressions\Shared\InIds;
use Illuminate\Support\Collection;
use Psr\Log\LoggerInterface;

/**
 * Class VerifyMembershipsUpdatedShell.
 *
 * app/Console/cake fix_members_without_user_membership_id --method=dry-run > export.json
 * app/Console/cake fix_members_without_user_membership_id --method=apply > export.json
 */
class FixMembersWithoutUserMembershipIdShell extends AppShell
{
    use ShellHelper;

    private ?bool $isDryRun = null;

    /** @var ?string */
    private $branchId;

    /** @var FetchStackedMembershipsService */
    private $fetchStackedMembershipsService;

    /** @var UsersRepository */
    private $usersRepository;

    /** @var LoggerInterface */
    private $logger;

    /** @var BranchesRepository */
    private $branchesRepository;

    /** @var ABTestClientsRepository */
    private $abTestRepository;

    /** @var array */
    private $output = [];

    private int $membersToBeFixedReflectingStateFromMembershipService = 0;

    private int $membersToBeFixedUpdatingLocalUserMembershipId = 0;

    private int $membersToBeFixedMigrating = 0;

    /** @var string[] */
    private $emailsToNotify;

    /**
     * ImportStripeCustomCardsShell constructor.
     *
     * @param null $stdout
     * @param null $stderr
     * @param null $stdin
     */
    public function __construct($stdout = null, $stderr = null, $stdin = null)
    {
        parent::__construct($stdout, $stderr, $stdin);

        $this->fetchStackedMembershipsService = app()->make(FetchStackedMembershipsService::class);
        $this->usersRepository = app()->make(UsersRepository::class);
        $this->branchesRepository = app()->make(BranchesRepository::class);
        $this->abTestRepository = app()->make(ABTestClientsRepository::class);
        $this->logger = app()->make(LoggerInterface::class);
    }

    /**
     * Add specific arguments to this script.
     *
     * @return ConsoleOptionParser
     */
    public function getOptionParser()
    {
        $parser = parent::getOptionParser();

        $parser->addOption('method', [
            'short' => 'm',
            'help' => 'The method to execute',
            'default' => 'dry-run',
        ]);

        $parser->addOption('branch-id', [
            'short' => 'b',
            'help' => 'The branch to use in this execution',
            'default' => '',
        ]);

        $parser->addOption('email-to-notify', [
            'short' => 'e',
            'help' => 'A comma separated list of email to notify about result',
            'default' => '<EMAIL>',
        ]);

        return $parser;
    }

    public function main()
    {
        $this->parseParams();

        $branches = $this->findActiveBranchesUsingMembershipService();

        $this->logger->info(
            sprintf('Working with %d branches: %s', $branches->count(), $branches->pluck('_id')->toJson())
        );

        $total = $this->countUsersMissingUserMembershipId($branches->pluck('_id'));

        $i = 0;
        /** @var Branch $branch */
        foreach ($branches as $branch) {
            try {
                $this->logger->info(
                    sprintf('Attempting to fix members of %s', $branch->id())
                );

                $users = $this->findUsersMissingUserMembershipId($branch);

                $this->logger->info(
                    sprintf('Found %d members to be fixed: %s', $users->count(), $users->pluck('_id')->toJson())
                );

                foreach ($users as $user) {
                    $this->updateMissingUserMembershipId($user);

                    ++$i;
                    $this->logger->info(
                        sprintf('[%d/%d] Procesed user %s', $i, $total, $user->id())
                    );
                }
            } catch (Exception $e) {
                $this->logger->error(
                    sprintf('An error ocurred when fixing branch %s, message: %s', $branch->id(), $e->getMessage())
                );

                $this->logger->error(
                    var_export($e->getTraceAsString())
                );
            }
        }

        $this->outputResult();
    }

    /**
     * Parse cli params to current scope.
     */
    private function parseParams()
    {
        $this->isDryRun = !('apply' === $this->param('method'));

        $methodAsText = $this->isDryRun ? 'dry-run' : 'apply';

        $this->logger->info(sprintf('Execution method set to: %s', $methodAsText));

        $emails = $this->param('email-to-notify');
        $this->emailsToNotify = explode(',', $emails);
        $this->logger->info('Email to notify: ' . $emails);
    }

    /**
     * @param Branch $branch
     *
     * @return UsersRepository
     */
    private function queryUsersMissingUserMembershipId(): UsersRepository
    {
        return $this->usersRepository
            ->addOrCriteria([
                new UserMembershipType(\Glofox\Domain\Memberships\Type::PAYG()),
                new UserMembershipType(\Glofox\Domain\Memberships\Type::TIME()),
                new UserMembershipType(\Glofox\Domain\Memberships\Type::TIME_CLASSES()),
            ])
            ->addOrCriteria([
                new FieldNotExists(
                    'membership.user_membership_id'
                ),
                new UserMembershipId(null),
            ]);
    }

    /**
     * @param Branch $branch
     *
     * @return Collection<User>
     */
    private function findUsersMissingUserMembershipId(Branch $branch): Collection
    {
        $users = $this->queryUsersMissingUserMembershipId()
            ->addCriteria(new BranchId($branch->id()))
            ->find();

        return collect($users);
    }

    /**
     * @return int
     */
    private function countUsersMissingUserMembershipId(Collection $branchIds): int
    {
        $count = $this->queryUsersMissingUserMembershipId()
            ->addCriteria(BranchId::in($branchIds))
            ->count();

        return $count;
    }

    /**
     * @return Collection<Branch>
     */
    private function findActiveBranchesUsingMembershipService(): Collection
    {
        $entries = $this->abTestRepository
            ->addCriteria(new Type('memberships_service'))
            ->addCriteria(new Active(true))
            ->find();

        $branchIds = collect($entries)
            ->pluck('branch_id')
            ->map(fn(string $id) => new \MongoId($id));

        $this->branchesRepository
            ->addCriteria(new Active(true));

        if ($this->branchId) {
            $branches = $this->branchesRepository
                ->addCriteria(new Id($this->branchId))
                ->findOrFail();

            return collect($branches);
        }

        $branches = $this->branchesRepository
            ->addCriteria(new InIds($branchIds))
            ->find();

        $branches = collect($branches)->filter(fn(Branch $branch) => 'finnsfitness' !== $branch->namespace());

        return $branches;
    }

    private function updateMissingUserMembershipId(User $user)
    {
        try {
            if ($user->membership()->userMembershipId()) {
                $this->logger->info(
                    sprintf(
                        'Attempted to update missing "user membership id" of user %s, but this user already has a "user membership id": %s',
                        $user->id(),
                        $user->membership()->userMembershipId()
                    )
                );

                return $user;
            }

            $this->logger->info(
                sprintf('Attempting to fix user %s', $user->id())
            );

            $membershipServiceState = $this->findMembershipStateInMembershipService($user);

            if ($this->shouldBeFixedUsingMigration($user, $membershipServiceState)) {
                $this->logger->info(
                    sprintf('Member %s must be fixed using "Migration"', $user->id())
                );

                $this->addMigrationUseCaseToOutput($user, $membershipServiceState, false);

                return;
            }

            if ($this->shouldBeFixedUpdatingTheLocalUserMembershipId($user, $membershipServiceState)) {
                $this->logger->info(
                    sprintf('Member %s is going to be fixed using "Update Local UserMembershipId"', $user->id())
                );

                $this->fixMemberUpdatingLocalUserMembershipId($user, $membershipServiceState);
                $this->addUpdateUseCaseToOutput($user, $membershipServiceState, !$this->isDryRun);

                return;
            }

            if ($this->shouldBeFixedReflectingStateFromMembershipService($user, $membershipServiceState)) {
                $this->logger->info(
                    sprintf('Member %s must be fixed using "Reflecting State From Membership Service"', $user->id())
                );

                $this->addReflectUseCaseToOutput($user, $membershipServiceState, false);

                return;
            }

            throw new Exception('Could not find out how this member should be fixed');
        } catch (Exception $e) {
            $this->logger->error(
                sprintf('"User membership id" update failed, user [%s] with error: %s', $user->id(), $e->getMessage())
            );
        }
    }

    private function findMembershipStateInMembershipService(User $user): ?object
    {
        Auth::loginAs($user);

        $memberships = $this->fetchStackedMembershipsService->execute($user, true);

        $this->logger->info(
            sprintf('Found %d memberships in the membership service for user %s', count($memberships), $user->id())
        );

        $validMembershipStatuses = [
            'LOCKED',
            'PAUSED',
            'ACTIVE',
        ];

        $membershipServiceState = null;
        foreach ($memberships as $membership) {
            $this->logger->info(
                sprintf('Checking membership %s with status %s', $membership->id, $membership->status)
            );

            if (in_array($membership->status, $validMembershipStatuses)) {
                $this->logger->info(
                    sprintf('Found "user membership id" %s as ACTIVE user %s', $membership->id, $user->id())
                );

                $membershipServiceState = $membership;
                break;
            }
        }

        if (!$membershipServiceState) {
            $this->logger->info(
                sprintf('Could not find any ACTIVE, LOCKED or PAUSED "user membership id" for user %s', $user->id())
            );
        }

        return $membershipServiceState;
    }

    private function fixMemberUpdatingLocalUserMembershipId(User $user, ?object $membershipServiceState): void
    {
        $userMembershipId = $membershipServiceState->id;

        $membership = $user->get('membership');
        $membership['user_membership_id'] = $userMembershipId;

        if ('PAUSED' === $membershipServiceState->status) {
            $this->logger->info(
                sprintf('Setting the membership of user %s as PAUSED', $user->id())
            );

            $membership['paused'] = true;
        }

        $this->logger->info(
            sprintf('Persisting "user membership id" %s to user %s', $userMembershipId, $user->id())
        );

        if (!$this->isDryRun) {
            $this->usersRepository->saveField($user, [
                'membership' => $membership,
            ]);
        }
    }

    private function shouldBeFixedUsingMigration(User $user, ?object $membershipServiceState): bool
    {
        if (null !== $membershipServiceState) {
            return false;
        }

        return true;
    }

    private function shouldBeFixedReflectingStateFromMembershipService(
        User $user,
        ?object $membershipServiceState
    ): bool {
        $isPaygInCoreApi = $user->membership()->isPayg();
        $isPaygInMembershipService = $membershipServiceState->payg;

        if ($isPaygInCoreApi && !$isPaygInMembershipService) {
            return true;
        }

        if (!$isPaygInCoreApi && $isPaygInMembershipService) {
            return true;
        }

        if (!$isPaygInCoreApi && !$isPaygInMembershipService) {
            return true;
        }

        return false;
    }

    private function shouldBeFixedUpdatingTheLocalUserMembershipId(User $user, ?object $membershipServiceState): bool
    {
        $isPaygInCoreApi = $user->membership()->isPayg();
        $isPaygInMembershipService = $membershipServiceState->payg;

        if ($isPaygInCoreApi && $isPaygInMembershipService) {
            return true;
        }

        $hasMetadata = (bool)($membershipServiceState->membershipMetaData ?? false);

        if (!$hasMetadata) {
            return false;
        }

        $isTimeInCoreApi = (\Glofox\Domain\Memberships\Type::TIME === $user->membership()->type());
        $isTimeInMembershipService = 'TIME' === $membershipServiceState->membershipMetaData->planType;

        if ($isTimeInCoreApi && $isTimeInMembershipService) {
            if ($membershipServiceState->membershipId === $user->membership()->id()) {
                return true;
            }
        }

        $isTimeClassesInCoreApi = (\Glofox\Domain\Memberships\Type::TIME_CLASSES === $user->membership()->type());
        $isTimeClassesInMembershipService = 'TIME_CLASSES' === $membershipServiceState->membershipMetaData->planType;

        if ($isTimeClassesInCoreApi && $isTimeClassesInMembershipService) {
            if ($membershipServiceState->membershipId === $user->membership()->id()) {
                return true;
            }
        }

        return false;
    }

    private function addMigrationUseCaseToOutput(User $user, ?object $membershipServiceState, $wasFixed): void
    {
        $membershipServiceState = $membershipServiceState ? json_encode(
            $membershipServiceState,
            JSON_PARTIAL_OUTPUT_ON_ERROR
        ) : null;

        $this->output[] = [
            'userId' => $user->id(),
            'namespace' => $user->namespace(),
            'originBranchId' => $user->originBranchId(),
            'coreApiMembershipType' => $user->membership()->type(),
            'membershipServiceState' => $membershipServiceState,
            'shouldBeFixedBy' => 'Re-running the initial migration',
            'fixType' => 'migration',
            'wasFixedByScript' => $wasFixed,
            'created' => $user->created() ? $user->created()->format('Y-m-d H:i:s') : null,
        ];

        ++$this->membersToBeFixedMigrating;
    }

    private function addUpdateUseCaseToOutput(User $user, ?object $membershipServiceState, $wasFixed): void
    {
        $membershipServiceState = $membershipServiceState ? json_encode(
            $membershipServiceState,
            JSON_PARTIAL_OUTPUT_ON_ERROR
        ) : null;

        $this->output[] = [
            'userId' => $user->id(),
            'namespace' => $user->namespace(),
            'originBranchId' => $user->originBranchId(),
            'coreApiMembershipType' => $user->membership()->type(),
            'membershipServiceState' => $membershipServiceState,
            'shouldBeFixedBy' => 'Update Local UserMembershipId',
            'fixType' => 'update',
            'wasFixedByScript' => $wasFixed,
            'created' => $user->created() ? $user->created()->format('Y-m-d H:i:s') : null,
        ];

        ++$this->membersToBeFixedUpdatingLocalUserMembershipId;
    }

    private function addReflectUseCaseToOutput(User $user, ?object $membershipServiceState, $wasFixed): void
    {
        $membershipServiceStateAsJson = $membershipServiceState ? json_encode(
            $membershipServiceState,
            JSON_PARTIAL_OUTPUT_ON_ERROR
        ) : null;

        $membershipServiceType = null;
        $membershipServiceId = null;

        if ($membershipServiceState) {
            $membershipServiceType = $membershipServiceState->membershipMetaData ? $membershipServiceState->membershipMetaData->planType : null;
            $membershipServiceId = $membershipServiceState->membershipId;

            if (!$membershipServiceType) {
                $membershipServiceType = $membershipServiceState->payg ? 'payg' : 'unknown';
            }
        }

        $this->output[] = [
            'userId' => $user->id(),
            'namespace' => $user->namespace(),
            'originBranchId' => $user->originBranchId(),
            'coreApiMembershipType' => $user->membership()->type(),
            'coreApiMembershipId' => $user->membership()->id(),
            'coreApiEmail' => $user->email(),
            'coreApiCycleStartDate' => $user->membership()->startDate(),
            'membershipServiceType' => $membershipServiceType,
            'membershipServiceCoreMembershipId' => $membershipServiceId,
            'membershipServiceState' => $membershipServiceStateAsJson,
            'shouldBeFixedBy' => 'Reflecting State From Membership Service',
            'fixType' => 'reflect',
            'wasFixedByScript' => $wasFixed,
            'created' => $user->created() ? $user->created()->format('Y-m-d H:i:s') : null,
        ];

        ++$this->membersToBeFixedReflectingStateFromMembershipService;
    }

    private function outputResult(): void
    {
        $total = $this->membersToBeFixedMigrating
            + $this->membersToBeFixedReflectingStateFromMembershipService
            + $this->membersToBeFixedUpdatingLocalUserMembershipId;

        $this->output[] = [
            'migrationUseCases' => $this->membersToBeFixedMigrating,
            'reflectUseCases' => $this->membersToBeFixedReflectingStateFromMembershipService,
            'updateUseCases' => $this->membersToBeFixedUpdatingLocalUserMembershipId,
            'total' => $total,
        ];

        print_r("\n\n\n\n\n\n\n\n\n\n\n");
        print_r(json_encode($this->output, JSON_PARTIAL_OUTPUT_ON_ERROR));
    }

}
