description: DiscountInsightsUsages states the usage related data for a discount.
properties:
  total:
    description: Total is the total number of pending and successful applications
      of the discount
    type: integer
  totalDirectUses:
    description: |-
      TotalDirectUses is the total number of pending and successful applications exclusively for
      manual applications of the discount.
    type: integer
  totalPromoCodeUses:
    description: |-
      TotalPromoCodeUses is the total number of pending and successful applications exclusively for
      uses of a promo code.
    type: integer
  usagesByInterval:
    $ref: './discount_insights_usages_by_interval.yml'
    description: UsagesByInterval defines the the breakdown of usages based on
      a specified interval.
  usagesBySource:
    $ref: './discount_insights_usages_by_source.yml'
    description: UsagesBySources defines the the breakdown of usages by source.
type: object