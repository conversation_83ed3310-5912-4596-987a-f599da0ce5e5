<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\AppointmentSlots\Validation\Validators;

use App;
use CakeTestCases\Glofox\Domain\Users\Traits\AuthenticateUsersTrait;
use Carbon\Carbon;
use Glofox\Domain\AppointmentSlots\Exceptions\TrainersCannotAddSlot;
use Glofox\Domain\AppointmentSlots\UseCase\SaveAppointmentSlotParams;
use Glofox\Domain\AppointmentSlots\Validation\Validators\CanCreateAppointmentSlotOtherTrainerValidator;
use Glofox\Domain\TimeSlotPatterns\Models\TimeSlotPattern;
use Mockery;

App::import('Test/Case', 'GlofoxTestCase');

/**
 * <AUTHOR>
 */
class CreateAppointmentSlotOtherTrainerValidatorTest extends \GlofoxTestCase
{
    use AuthenticateUsersTrait;

    public function tearDown()
    {
        parent::tearDown();

        Mockery::close();
    }

    public function testTrainersCanCreateSlots(): void
    {
        $this->authenticateAsTrainer();
        $params = $this->createParams($this->trainerId);

        $validator = new CanCreateAppointmentSlotOtherTrainerValidator();
        $validator->validate($params);
    }

    public function testAdminsCanCreateSlots(): void
    {
        $this->authenticateAsAdmin();
        $params = $this->createParams('other-staff-id');

        $validator = new CanCreateAppointmentSlotOtherTrainerValidator();
        $validator->validate($params);
    }

    public function testReceptionsCanCreateSlots(): void
    {
        $this->authenticateAsReception();
        $params = $this->createParams('other-staff-id');

        $validator = new CanCreateAppointmentSlotOtherTrainerValidator();
        $validator->validate($params);
    }

    public function testTrainersCannotCreateSlotsForOtherTrainers(): void
    {
        $this->authenticateAsTrainer();
        $params = $this->createParams('other-staff-id');

        $this->expectExceptionMessage(TrainersCannotAddSlot::becauseTheyCannotAddItForOtherTrainers()->getMessage());

        $validator = new CanCreateAppointmentSlotOtherTrainerValidator();
        $validator->validate($params);
    }

    private function createParams(string $staffId): SaveAppointmentSlotParams
    {
        $appointment = Mockery::mock(TimeSlotPattern::class, ['id' => 'test-appointment-id']);
        return new SaveAppointmentSlotParams(
            $appointment,
            '',
            '',
            Carbon::now(),
            $staffId,
            false,
            false,
            ''
        );
    }
}
