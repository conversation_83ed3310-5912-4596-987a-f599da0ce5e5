<?php

namespace Glofox\Response\Transformers\Invoices;

use Glofox\Payments\Entities\Invoice\Models\InvoiceAddress;
use Glofox\Response\Models\Invoice\InvoiceAddressModel;
use Glofox\Response\Transformers\BaseTransformer;

/**
 * Class InvoiceAddressTransformer
 * @package Glofox\Response\Transformers\Invoices
 */
class InvoiceAddressTransformer extends BaseTransformer
{
    /**
     * @param InvoiceAddress $providerInvoiceAddress
     * @return InvoiceAddressModel
     */
    public function setFromPaymentProvider(InvoiceAddress $providerInvoiceAddress): InvoiceAddressModel
    {
        return new InvoiceAddressModel(
            [
                'owner' => $providerInvoiceAddress->owner(),
                'name' => $providerInvoiceAddress->name(),
                'address_first' => $providerInvoiceAddress->addressLine1(),
                'address_second' => $providerInvoiceAddress->addressLine2(),
                'address_third' => $providerInvoiceAddress->addressLine3(),
            ]
        );
    }
}
