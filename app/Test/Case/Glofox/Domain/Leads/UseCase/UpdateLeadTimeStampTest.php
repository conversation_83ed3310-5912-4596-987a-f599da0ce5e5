<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\Leads\UseCase;

use Glofox\Domain\Leads\Enums\LeadSchemaFields;
use Glofox\Domain\Leads\Status as LeadStatus;
use Glofox\Domain\Leads\UseCase\UpdateLeadTimeStamp;
use Glofox\Domain\Leads\UseCase\UpdateLeadTimeStampParams;
use Glofox\Domain\Users\Models\User;
use Glofox\Domain\Users\Repositories\UsersRepository;
use Mockery;
use MongoDB\BSON\UTCDateTime;
use PHPUnit\Framework\TestCase;
use UserType;

class UpdateLeadTimeStampTest extends TestCase
{
    private UsersRepository $usersRepository;
    private UpdateLeadTimeStamp $updateLeadStatusUseCase;

    public function setUp(): void
    {
        parent::setUp();
        $this->usersRepository = Mockery::mock(UsersRepository::class);
        $this->updateLeadStatusUseCase = new UpdateLeadTimeStamp($this->usersRepository);
    }

    public function tearDown(): void
    {
        parent::tearDown();
        Mockery::close();
    }

    /**
     * @dataProvider leadStatusDataProvider
     * @throws \UnsuccessfulOperation
     */
    public function testExecute(
        $initialStatus,
        $newStatus,
        $expectedLeads,
        $userType
    ): void {
        $userId = '66eaaea84e7bbf07f4733f6f';

        $user = Mockery::mock(User::class)->makePartial();
        $user->shouldReceive('id')->andReturn($userId);
        $user->shouldReceive('leads')->andReturn();
        $user->shouldReceive('type')->andReturn($userType);

        $params = Mockery::mock(UpdateLeadTimeStampParams::class);
        $params->shouldReceive('user')->andReturn($user);
        $params->shouldReceive('newStatus')->andReturn($newStatus);
        $params->shouldReceive('previousStatus')->andReturn($initialStatus);

        $newStatus = LeadStatus::getNewStatus($params->newStatus());
        $initialStatus = LeadStatus::getNewStatus($params->previousStatus());

        if (
            $userType === UserType::MEMBER &&
            $newStatus !== null &&
            $this->updateLeadStatusUseCase->isDifferent($newStatus, $initialStatus)
        ) {
            $this->usersRepository
                ->shouldReceive('updateOneField')
                ->once()
                ->with(
                    $userId,
                    'leads',
                    Mockery::on(static function ($leads) use ($expectedLeads) {
                        if (empty($expectedLeads)) {
                            return empty($leads);
                        }
                        return isset($leads[LeadSchemaFields::STATUS_MODIFIED])
                            && $leads[LeadSchemaFields::STATUS_MODIFIED] instanceof UTCDateTime
                            && $leads[LeadSchemaFields::STATUS] === $expectedLeads[LeadSchemaFields::STATUS];
                    })
                )
                ->andReturn(true);
        } else {
            $this->usersRepository
                ->shouldNotReceive('updateOneField');
        }

        $this->updateLeadStatusUseCase->execute($params);

        $this->assertTrue(true);
    }

    public function leadStatusDataProvider(): array
    {
        return [
            [
                'initialStatus' => LeadStatus::TRIAL,
                'newStatus' => LeadStatus::COLD,
                'expectedLeads' => [
                    LeadSchemaFields::STATUS_MODIFIED => new \MongoDate(),
                    LeadSchemaFields::STATUS => LeadStatus::COLD,

                ],
                'userType' => UserType::MEMBER,
            ],

            [
                'initialStatus' => LeadStatus::COLD,
                'newStatus' => LeadStatus::COLD,
                'expectedLeads' => [],
                'userType' => UserType::MEMBER,
            ],
            [
                'initialStatus' => LeadStatus::WARM,
                'newStatus' => LeadStatus::LEAD,
                'expectedLeads' => [],
                'userType' => UserType::MEMBER,
            ],
            [
                'initialStatus' => null,
                'newStatus' => LeadStatus::COLD,
                'expectedLeads' => [
                    LeadSchemaFields::STATUS_MODIFIED => new \MongoDate(),
                    LeadSchemaFields::STATUS => LeadStatus::COLD,
                ],
                'userType' => UserType::MEMBER,
            ],
            [
                'initialStatus' => LeadStatus::COLD,
                'newStatus' => null,
                'expectedLeads' => [],
                'userType' => UserType::MEMBER,
            ],

        ];
    }
}
