<?php

declare(strict_types=1);

use Glofox\Domain\Authentication\Auth;
use Glofox\Domain\Authentication\Token\TokenGeneratorDto;
use Glofox\Domain\Authentication\TokenGenerator;
use Glofox\Domain\Branches\Repositories\BranchesRepository;
use Glofox\Domain\Programs\Repositories\ProgramsRepository;
use Glofox\Domain\Programs\Services\ProgramUpsertService;
use Glofox\Domain\Users\Models\User;
use Glofox\Repositories\Search\Expressions\Shared\BranchId;
use Glofox\Repositories\Search\Expressions\Shared\InIds;
use Glofox\Repositories\Search\Filters\Shared\IsActive;
use Psr\Log\LoggerInterface;

class UpdateFacilityForProgramAndSchedulesShell extends Shell
{
    private LoggerInterface $logger;
    private ProgramsRepository $programsRepository;
    private BranchesRepository $branchesRepository;
    private ProgramUpsertService $programUpsertService;

    public function __construct($stdout = null, $stderr = null, $stdin = null)
    {
        parent::__construct($stdout, $stderr, $stdin);

        $this->logger = app()->make(LoggerInterface::class);
        $this->programsRepository = app()->make(ProgramsRepository::class);
        $this->programUpsertService = app()->make(ProgramUpsertService::class);
        $this->branchesRepository = app()->make(BranchesRepository::class);
    }

    public function getOptionParser(): ConsoleOptionParser
    {
        $parser = parent::getOptionParser();

        $parser->addOption('branchId', [
            'short' => 'b',
            'help' => 'Branch ID',
            'required' => true
        ]);

        $parser->addOption('facilityId', [
            'short' => 'f',
            'help' => 'New Facility ID',
            'required' => true
        ]);

        $parser->addOption('onlyActivePrograms', [
            'short' => 'n',
            'help' => 'Update also for not active programs',
            'required' => false
        ]);

        $parser->addOption('programIds', [
            'short' => 'p',
            'help' => 'Update just for given programs',
            'required' => false
        ]);

        return $parser;
    }

    public function main(): void
    {
        $this->logger->info('[UpdateFacilityForProgramAndSchedules] Started');

        $branchId = $this->params['branchId'];
        $facilityId = $this->params['facilityId'];
        $branch = $this->branchesRepository->getById($branchId);

        $bot = $this->getUserBot($branch->id(), $branch->namespace());
        $authToken = app()->make(TokenGenerator::class)->generate(
            new TokenGeneratorDto($bot->toArray()),
            (date("Y") + 1) . '-01-01 00:00:00'
        );
        $_SERVER['HTTP_AUTHORIZATION'] = $authToken;
        Auth::loginAs($bot);

        $this->logger->info('Running for branch ID: ' . $branchId);

        $this->programsRepository->addCriteria(new BranchId($branchId));

        if ($this->params['onlyActivePrograms'] ?? false) {
            $this->programsRepository->addCriteria(new IsActive());
        }

        if (!empty ($this->params['programIds'])) {
            $ids = explode(',', $this->params['programIds']);
            $programIds = array_map(static fn($id) => new MongoId($id), $ids);
            $this->programsRepository->addCriteria(
                new InIds(collect($programIds))
            );
        }

        $programs = $this->programsRepository->find();

        foreach ($programs as $program) {
            $this->logger->info('Running for program ID: ' . $program['_id']);

            $this->updateFacilityForProgram($program, $facilityId, $bot);
        }

        $this->logger->info('[UpdateFacilityForProgramAndSchedules] Finished');
    }

    private function updateFacilityForProgram(array $program, string $newFacility, User $bot): void
    {
        if (isset($program['schedule_default']['facility'])) {
            $program['schedule_default']['facility'] = $newFacility;
        }

        if (isset($program['schedule'])) {
            foreach ($program['schedule'] as &$schedule) {
                $schedule['facility'] = $newFacility;
            }
            unset($schedule);
        }


        try {
            $this->programUpsertService->execute(
                $program,
                $bot
            );
        } catch (Exception $e) {
            $this->logger->error('Error updating program', ['programId' => $program['_id'], 'error' => $e->getMessage()]
            );
        }
    }

    private function getUserBot(string $branchId, string $namespace): User
    {
        return User::make([
            '_id' => User::WORKER_FAKE_ID,
            'namespace' => $namespace,
            'branch_id' => $branchId,
            'first_name' => 'Update-Facility',
            'last_name' => 'From UpdateFacilityForProgramAndSchedulesShell',
            'type' => UserType::SUPERADMIN,
            'active' => true,
        ]);
    }
}
