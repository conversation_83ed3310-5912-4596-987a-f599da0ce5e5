<?php

namespace Glofox\Domain\Integrations;

use Glofox\Domain\Integrations\ActionProvider as AbstractActionProvider;
use Glofox\Domain\Integrations\Exceptions\ActionNotFoundException;

class ActionResolver
{
    public function __construct(AbstractActionProvider $provider)
    {
        $this->provider = $provider;
    }

    /**
     * [resolveByRequest description].
     *
     * @param \CakeRequest $request [description]
     *
     * @return [type] [description]
     */
    public function resolveByRequest(\CakeRequest $request)
    {
        $classes = $this->provider->get();
        $params = $request->params;

        $actionName = null;
        if (\is_array($params['pass'])) {
            $requestMethod = array_shift($params['pass']);
            $id = array_shift($params['pass']);
            $actionName = $this->getActionName($requestMethod, $id);
        }

        if (empty($actionName) || !isset($classes[$actionName])) {
            throw new ActionNotFoundException(sprintf('Action %s wasnt found in Integrations\%s\Actions', $actionName, $this->provider->getIntegrationIdentifier()));
        }

        return $classes[$actionName];
    }

    /**
     * Given a request action name and id, it will determine
     * the name of the action that is going to be invoked. At the
     * moment we are resolving the List(ResourcePlural) requests with an id provided
     * to a Get(ResourceSingular) action.
     *
     * @param [type] $requestMethod [description]
     * @param [type] $id            [description]
     *
     * @return [type] [description]
     */
    public function getActionName($requestMethod, $id = null): string
    {
        $listRequestResource = $this->getListRequestResource($requestMethod);
        if ($listRequestResource && !empty($id)) {
            $stringTemplate = new \StringTemplate\Engine('{{', '}}');
            $requestMethod = $stringTemplate->render('Get{{resource}}', ['resource' => $listRequestResource]);
        }

        return $requestMethod;
    }

    private function getListRequestResource(string $requestMethod): ?string
    {
        $resource = null;
        preg_match('/^List(.*)/', $requestMethod, $matches);
        $resourceNamePlural = $matches[1] ?? null;
        $isListRequest = !empty($resourceNamePlural);
        if ($isListRequest) {
            $resource = \Inflector::singularize($resourceNamePlural);
        }

        return $resource;
    }
}
