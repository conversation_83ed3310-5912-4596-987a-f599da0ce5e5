<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: action_request.proto

namespace GRPC\Payments\TransactionService;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>grpc.SelfRecordReq</code>
 */
class SelfRecordReq extends \Google\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>int32 ProviderID = 1;</code>
     */
    protected $ProviderID = 0;
    /**
     * Generated from protobuf field <code>int32 CustomerAccountID = 2;</code>
     */
    protected $CustomerAccountID = 0;
    /**
     * Generated from protobuf field <code>int32 DestinationAccountID = 3;</code>
     */
    protected $DestinationAccountID = 0;
    /**
     * Generated from protobuf field <code>int32 Fee = 4;</code>
     */
    protected $Fee = 0;
    /**
     * Generated from protobuf field <code>string PSPUUID = 5;</code>
     */
    protected $PSPUUID = '';
    /**
     * Generated from protobuf field <code>string IntentPSPUUID = 6;</code>
     */
    protected $IntentPSPUUID = '';
    /**
     * Generated from protobuf field <code>bool RecordRefunds = 7;</code>
     */
    protected $RecordRefunds = false;
    /**
     * Generated from protobuf field <code>int32 ActionRequestID = 8;</code>
     */
    protected $ActionRequestID = 0;
    /**
     * Generated from protobuf field <code>string InvoiceId = 9;</code>
     */
    protected $InvoiceId = '';
    /**
     * Generated from protobuf field <code>string TransactionGroupId = 10;</code>
     */
    protected $TransactionGroupId = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int $ProviderID
     *     @type int $CustomerAccountID
     *     @type int $DestinationAccountID
     *     @type int $Fee
     *     @type string $PSPUUID
     *     @type string $IntentPSPUUID
     *     @type bool $RecordRefunds
     *     @type int $ActionRequestID
     *     @type string $InvoiceId
     *     @type string $TransactionGroupId
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\ActionRequest::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>int32 ProviderID = 1;</code>
     * @return int
     */
    public function getProviderID()
    {
        return $this->ProviderID;
    }

    /**
     * Generated from protobuf field <code>int32 ProviderID = 1;</code>
     * @param int $var
     * @return $this
     */
    public function setProviderID($var)
    {
        GPBUtil::checkInt32($var);
        $this->ProviderID = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 CustomerAccountID = 2;</code>
     * @return int
     */
    public function getCustomerAccountID()
    {
        return $this->CustomerAccountID;
    }

    /**
     * Generated from protobuf field <code>int32 CustomerAccountID = 2;</code>
     * @param int $var
     * @return $this
     */
    public function setCustomerAccountID($var)
    {
        GPBUtil::checkInt32($var);
        $this->CustomerAccountID = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 DestinationAccountID = 3;</code>
     * @return int
     */
    public function getDestinationAccountID()
    {
        return $this->DestinationAccountID;
    }

    /**
     * Generated from protobuf field <code>int32 DestinationAccountID = 3;</code>
     * @param int $var
     * @return $this
     */
    public function setDestinationAccountID($var)
    {
        GPBUtil::checkInt32($var);
        $this->DestinationAccountID = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 Fee = 4;</code>
     * @return int
     */
    public function getFee()
    {
        return $this->Fee;
    }

    /**
     * Generated from protobuf field <code>int32 Fee = 4;</code>
     * @param int $var
     * @return $this
     */
    public function setFee($var)
    {
        GPBUtil::checkInt32($var);
        $this->Fee = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string PSPUUID = 5;</code>
     * @return string
     */
    public function getPSPUUID()
    {
        return $this->PSPUUID;
    }

    /**
     * Generated from protobuf field <code>string PSPUUID = 5;</code>
     * @param string $var
     * @return $this
     */
    public function setPSPUUID($var)
    {
        GPBUtil::checkString($var, True);
        $this->PSPUUID = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string IntentPSPUUID = 6;</code>
     * @return string
     */
    public function getIntentPSPUUID()
    {
        return $this->IntentPSPUUID;
    }

    /**
     * Generated from protobuf field <code>string IntentPSPUUID = 6;</code>
     * @param string $var
     * @return $this
     */
    public function setIntentPSPUUID($var)
    {
        GPBUtil::checkString($var, True);
        $this->IntentPSPUUID = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>bool RecordRefunds = 7;</code>
     * @return bool
     */
    public function getRecordRefunds()
    {
        return $this->RecordRefunds;
    }

    /**
     * Generated from protobuf field <code>bool RecordRefunds = 7;</code>
     * @param bool $var
     * @return $this
     */
    public function setRecordRefunds($var)
    {
        GPBUtil::checkBool($var);
        $this->RecordRefunds = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 ActionRequestID = 8;</code>
     * @return int
     */
    public function getActionRequestID()
    {
        return $this->ActionRequestID;
    }

    /**
     * Generated from protobuf field <code>int32 ActionRequestID = 8;</code>
     * @param int $var
     * @return $this
     */
    public function setActionRequestID($var)
    {
        GPBUtil::checkInt32($var);
        $this->ActionRequestID = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string InvoiceId = 9;</code>
     * @return string
     */
    public function getInvoiceId()
    {
        return $this->InvoiceId;
    }

    /**
     * Generated from protobuf field <code>string InvoiceId = 9;</code>
     * @param string $var
     * @return $this
     */
    public function setInvoiceId($var)
    {
        GPBUtil::checkString($var, True);
        $this->InvoiceId = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string TransactionGroupId = 10;</code>
     * @return string
     */
    public function getTransactionGroupId()
    {
        return $this->TransactionGroupId;
    }

    /**
     * Generated from protobuf field <code>string TransactionGroupId = 10;</code>
     * @param string $var
     * @return $this
     */
    public function setTransactionGroupId($var)
    {
        GPBUtil::checkString($var, True);
        $this->TransactionGroupId = $var;

        return $this;
    }

}

