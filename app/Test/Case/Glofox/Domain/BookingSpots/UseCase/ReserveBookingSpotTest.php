<?php

namespace CakeTestCases\Glofox\Domain\BookingSpots\UseCase;

use Glofox\Domain\BookingSpots\Models\BookingSpot;
use Glofox\Domain\BookingSpots\Repositories\BookingSpotsRepository;
use Glofox\Domain\BookingSpots\UseCase\ReserveBookingSpot;
use Glofox\Domain\BookingSpots\UseCase\ReserveBookingSpotParams;
use Glofox\Domain\BookingSpots\Validation\ReserveBookingSpotValidator;
use Glofox\Exception;
use Glofox\Infrastructure\Locker\BookingSpotLocker;

\App::import('Test/Case', 'GlofoxTestCase');

class ReserveBookingSpotTest extends \GlofoxTestCase
{
    public $fixtures = [];

    public function tearDown(): void
    {
        parent::tearDown();

        \Mockery::close();
    }

    public function test_it_should_fail_if_unable_to_acquire_lock(): void
    {
        $locker = \Mockery::mock(BookingSpotLocker::class)
            ->shouldReceive('lockWithMultipleRetries')
            ->andThrow(Exception::class, 'UNABLE_TO_ACQUIRE_LOCK')
            ->getMock();

        $validator = \Mockery::mock(ReserveBookingSpotValidator::class);
        $bookingSpotRepo = \Mockery::mock(BookingSpotsRepository::class);

        $useCase = new ReserveBookingSpot($locker, $bookingSpotRepo, $validator);

        $this->expectExceptionMessage('UNABLE_TO_ACQUIRE_LOCK');

        $useCase->execute(
            new ReserveBookingSpotParams(
                'branch-id',
                'user-id',
                'model-id',
                'booking-id',
                'spot-id',
            )
        );
    }

    public function test_it_should_fail_if_validation_fails(): void
    {
        $locker = \Mockery::mock(BookingSpotLocker::class)
            ->shouldReceive('lockWithMultipleRetries')
            ->andReturn()
            ->shouldReceive('unlock')
            ->andReturn()
            ->getMock();

        $validator = \Mockery::mock(ReserveBookingSpotValidator::class)
            ->shouldReceive('validate')
            ->andThrow(Exception::class, 'VALIDATION_ERROR')
            ->getMock();

        $bookingSpotRepo = \Mockery::mock(BookingSpotsRepository::class);

        $useCase = new ReserveBookingSpot($locker, $bookingSpotRepo, $validator);

        $this->expectExceptionMessage('VALIDATION_ERROR');

        $useCase->execute(
            new ReserveBookingSpotParams(
                'branch-id',
                'user-id',
                'model-id',
                'booking-id',
                'spot-id',
            )
        );
    }

    public function test_it_should_reserve_spot(): void
    {
        $locker = \Mockery::mock(BookingSpotLocker::class)
            ->shouldReceive('lockWithMultipleRetries')
            ->andReturn()
            ->shouldReceive('unlock')
            ->andReturn()
            ->getMock();

        $validator = \Mockery::mock(ReserveBookingSpotValidator::class)
            ->shouldReceive('validate')
            ->andReturn()
            ->getMock();

        $bookingSpotRepo = \Mockery::mock(BookingSpotsRepository::class)
            ->shouldReceive('save')
            ->andReturn(BookingSpot::make())
            ->getMock();

        $useCase = new ReserveBookingSpot($locker, $bookingSpotRepo, $validator);
        $useCase->execute(
            new ReserveBookingSpotParams(
                'branch-id',
                'user-id',
                'model-id',
                'booking-id',
                'spot-id',
            )
        );
    }
}
