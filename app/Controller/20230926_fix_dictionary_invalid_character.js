// UPDATE - <PERSON><PERSON><PERSON> to update invalid characters in a specific language defined for a dictionary
const filter = { // The language we're looking to modify
    'language.code': 'fr',
    'language.name': 'French'
};

const cursor = db.dictionaries.find(filter);

let bulkUpdateOps = [];

cursor.forEach((doc) => {
    let updateDoc = {};
    for (let key in doc.dictionary) {
        if (doc.dictionary.hasOwnProperty(key)) {
            updateDoc[`dictionary.${key}`] = doc.dictionary[key].replace(/&#39;/g, "'"); //Characters to replace
        }
    }
    updateDoc.modified = new Date();
    bulkUpdateOps.push({
        updateOne: {
            filter: {_id: doc._id},
            update: {$set: updateDoc}
        }
    });
});

if (bulkUpdateOps.length > 0) {
    db.dictionaries.bulkWrite(bulkUpdateOps);
}
