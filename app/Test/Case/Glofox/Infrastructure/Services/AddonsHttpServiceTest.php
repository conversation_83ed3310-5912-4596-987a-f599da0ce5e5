<?php

namespace CakeTestCases\Glofox\Infrastructure\Services;

use App;
use CakeTestCases\Glofox\Domain\Logger\Traits\MockedLoggerTrait;
use Carbon\Carbon;
use Glofox\Domain\Bookings\Models\BookableEntityType;
use Glofox\Infrastructure\Exception\HttpClientResponseException;
use Glofox\Infrastructure\Services\AddonsHttpService;
use GlofoxTestCase;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\ServerException;
use GuzzleHttp\Psr7\Request;
use GuzzleHttp\Psr7\Response;
use Mockery;
use MongoId;
use Psr\Http\Message\ResponseInterface;

App::import('Test/Case', 'GlofoxTestCase');

class AddonsHttpServiceTest extends GlofoxTestCase
{
    use MockedLoggerTrait;

    public const GENERIC_SERVER_EXCEPTION_MESSAGE = 'Foo bar';
    public const GENERIC_SERVER_EXCEPTION_CODE = 500;
    public const PAST_BOOKING_ERROR = 'CANNOT_BOOK_IN_THE_PAST_WHEN_ADDON_SERVICE_IS_ACTIVE';
    public const PAST_BOOKING_ERROR_RAW = 'activeDate must not be in the past';
    public const ADDON_SERVICE_VALIDATION_ERROR_RAW = 'VALIDATION_FAILED';
    public const ADDON_SERVICE_VALIDATION_ERROR = 'ADDON_SERVICE_VALIDATION_ERROR';

    public function setUp(): void
    {
        parent::setUp();

        $this->mockLogger();
    }

    public function tearDown(): void
    {
        parent::tearDown();

        Mockery::close();
        $this->teardownLogger();
    }

    public function test_get_user_addons_returns_valid_response(): void
    {
        $httpClient = Mockery::mock(Client::class);
        $body = [
            [
                'id' => 'id-1234',
                'definition' => [
                    'id' => 'definition-id-1234',
                    'plan' => [
                        'id' => 'definition-plan-id-123',
                        'name' => 'Recurring',
                        'duration' => ['hasEndDate' => false],
                        'isCreditBased' => false,
                    ],
                    'name' => 'Tanning',
                    'type' => 'FACILITY'
                ],
                'price' => 13,
                'purchasedAt' => Carbon::yesterday()->getTimestamp(),
            ]
        ];

        $branchId = 'mocked-branch-id';
        $userId = 'mocked-user-id';
        $activeOn = new Carbon('2021-09-24T20:00:00+01:00');

        $expectedUrl = 'v3/services';
        $expectedBranchId = $branchId;
        $expectedUserId = $userId;
        $expectedActiveOn = '2021-09-24T19:00:00Z';

        $httpResponse = Mockery::mock(ResponseInterface::class);
        $httpResponse
            ->shouldReceive('getBody')
            ->andReturn(json_encode($body))
            ->once();

        $httpClient
            ->shouldReceive('get')
            ->withArgs(function (string $url, array $options) use (
                $expectedUrl,
                $expectedBranchId,
                $expectedUserId,
                $expectedActiveOn
            ) {

                self::assertEquals($expectedUrl, $url);

                self::assertEquals($expectedUserId, $options['query']['memberId']);
                self::assertEquals($expectedActiveOn, $options['query']['activeDate']);

                self::assertEquals($expectedBranchId, $options['headers']['x-glofox-branch-id']);

                return true;
            })
            ->andReturn($httpResponse)
            ->once();

        $addonService = new AddonsHttpService($httpClient, $this->loggerMock);
        $addons = $addonService->getUserAddonsActiveForUsageOnDate($branchId, $userId, $activeOn);


        $addon = $addons[0];

        self::assertEquals('id-1234', $addon->serviceId());
        self::assertEquals('definition-id-1234', $addon->serviceDefinitionId());
        self::assertEquals('definition-plan-id-123', $addon->serviceDefinitionPlanId());
        self::assertEquals('Tanning', $addon->serviceDefinitionName());
        self::assertEquals('Recurring', $addon->serviceDefinitionPlanName());
        self::assertEquals(BookableEntityType::FACILITY, $addon->serviceDefinitionType());
    }

    public function test_get_addon_makes_the_request_to_get_addon_and_receives_valid_response(): void
    {
        $addonId = 'addon1234';
        $branchId = new MongoId();
        $url = 'v3/services/:addonId';
        $url = str_replace(':addonId', $addonId, $url);
        $httpClient = Mockery::mock(Client::class);
        $body = [
            'id' => 'id-1234',
            'definition' => [
                'id' => 'definition-id-1234',
                'plan' => [
                    'id' => 'definition-plan-id-123',
                    'name' => 'Recurring',
                    'duration' => ['hasEndDate' => false],
                    'isCreditBased' => false,
                ],
                'name' => 'Tanning',
                'type' => 'FACILITY'
            ],
            'price' => 13,
            'purchasedAt' => Carbon::yesterday()->getTimestamp(),
        ];
        $httpResponse = Mockery::mock(ResponseInterface::class);
        $httpResponse
            ->shouldReceive('getBody')
            ->andReturn(json_encode($body, JSON_THROW_ON_ERROR))
            ->once();
        $httpClient
            ->shouldReceive('get')
            ->withArgs(function (string $urlParam, array $options) use ($url, $branchId) {
                self::assertTextEquals($url, $urlParam);
                self::assertTrue($options['verify']);
                self::assertTextEquals($branchId, $options['headers']['x-glofox-branch-id']);

                return true;
            })
            ->andReturn($httpResponse)
            ->once();

        $addonService = new AddonsHttpService($httpClient, $this->loggerMock);
        $addon = $addonService->getAddon($addonId, $branchId);

        self::assertEquals('id-1234', $addon->serviceId());
        self::assertEquals('definition-id-1234', $addon->serviceDefinitionId());
        self::assertEquals('definition-plan-id-123', $addon->serviceDefinitionPlanId());
        self::assertEquals('Tanning', $addon->serviceDefinitionName());
        self::assertEquals('Recurring', $addon->serviceDefinitionPlanName());
    }

    public function test_get_addon_makes_the_request_to_get_addon_and_receives_error_message_from_addon_service(): void
    {
        $addonId = 'addon1234';
        $branchId = new MongoId();
        $httpClient = Mockery::mock(Client::class);
        $httpClient
            ->shouldReceive('get')
            ->andThrow($this->genericGenericServerException())
            ->once();

        $this->setExpectedException(
            HttpClientResponseException::class,
            self::GENERIC_SERVER_EXCEPTION_MESSAGE,
            self::GENERIC_SERVER_EXCEPTION_CODE
        );

        $addonService = new AddonsHttpService($httpClient, $this->loggerMock);
        $addonService->getAddon($addonId, $branchId);
    }

    public function test_get_addons_by_member_id_success(): void
    {
        $memberId = 'member-123';
        $branchId = new MongoId();
        $url = 'v3/services';
        $httpClient = Mockery::mock(Client::class);
        $body = [
            [
                'id' => 'id-1234',
                'definition' => [
                    'id' => 'definition-id-1234',
                    'plan' => [
                        'id' => 'definition-plan-id-123',
                        'name' => 'Recurring',
                        'isCreditBased' => false
                    ],
                    'name' => 'Tanning'
                ],
                'price' => 13
            ]
        ];

        $httpResponse = Mockery::mock(ResponseInterface::class);
        $httpResponse
            ->shouldReceive('getBody')
            ->andReturn(json_encode($body))
            ->once();
        $httpResponse
            ->shouldReceive('getStatusCode')
            ->andReturn('200')
            ->once();
        $httpClient
            ->shouldReceive('get')
            ->withArgs(function (string $urlParam, array $options) use ($url, $branchId) {
                self::assertTextEquals($url, $urlParam);
                self::assertEquals([
                    'memberId' => 'member-123'
                ], $options['query']);
                self::assertTrue($options['verify']);
                self::assertTextEquals($branchId, $options['headers']['x-glofox-branch-id']);

                return true;
            })
            ->andReturn($httpResponse)
            ->once();

        $addonService = new AddonsHttpService($httpClient, $this->loggerMock);
        $response = $addonService->getAddonsByMemberId($branchId, $memberId);
        $addons = $response->getData();

        self::assertCount(1, $addons);
        self::assertEquals('id-1234', $addons[0]->id);
        self::assertEquals('definition-id-1234', $addons[0]->definition->id);
        self::assertEquals('definition-plan-id-123', $addons[0]->definition->plan->id);
        self::assertEquals('Tanning', $addons[0]->definition->name);
        self::assertEquals('Recurring', $addons[0]->definition->plan->name);
    }

    public function test_get_addons_by_member_id_services_service_error(): void
    {
        $addonId = 'addon1234';
        $branchId = new MongoId();
        $httpClient = Mockery::mock(Client::class);
        $httpClient
            ->shouldReceive('get')
            ->andThrow($this->genericGenericServerException())
            ->once();

        $this->setExpectedException(
            HttpClientResponseException::class,
            self::GENERIC_SERVER_EXCEPTION_MESSAGE,
            self::GENERIC_SERVER_EXCEPTION_CODE
        );

        $addonService = new AddonsHttpService($httpClient, $this->loggerMock);
        $addonService->getAddon($addonId, $branchId);

        $memberId = 'member-123';
        $branchId = new MongoId();
        $httpClient = Mockery::mock(Client::class);
        $body = [
            [
                'id' => 'id-1234',
                'definition' => [
                    'id' => 'definition-id-1234',
                    'plan' => [
                        'id' => 'definition-plan-id-123',
                        'name' => 'Recurring',
                        'isCreditBased' => false,
                    ],
                    'name' => 'Tanning'
                ],
                'price' => 13
            ]
        ];

        $httpResponse = Mockery::mock(ResponseInterface::class);
        $httpResponse
            ->shouldReceive('getBody')
            ->andReturn(json_encode($body))
            ->once();
        $httpResponse
            ->shouldReceive('getStatusCode')
            ->andReturn('500')
            ->once();
        $httpClient
            ->shouldReceive('get')
            ->andThrow($this->genericGenericServerException())
            ->once();

        $this->setExpectedException(
            HttpClientResponseException::class,
            self::GENERIC_SERVER_EXCEPTION_MESSAGE,
            self::GENERIC_SERVER_EXCEPTION_CODE
        );

        $addonService = new AddonsHttpService($httpClient, $this->loggerMock);
        $addonService->getAddonsByMemberId($branchId, $memberId);
    }

    public function testGetUserAddonsReturnsValidResponseWithCredits(): void
    {
        $httpClient = Mockery::mock(Client::class);
        $body = [
            [
                'id' => 'id-1234',
                'definition' => [
                    'id' => 'definition-id-1234',
                    'plan' => [
                        'id' => 'definition-plan-id-123',
                        'name' => 'Recurring',
                        'duration' => ['hasEndDate' => false],
                        'isCreditBased' => true,
                    ],
                    'name' => 'Tanning',
                    'type' => 'CLASS'
                ],
                'availableCredits' => 2,
                'price' => 13,
                'purchasedAt' => Carbon::yesterday()->getTimestamp(),
            ]
        ];

        $branchId = 'mocked-branch-id';
        $userId = 'mocked-user-id';
        $activeOn = new Carbon('2021-09-24T20:00:00+01:00');

        $httpResponse = Mockery::mock(ResponseInterface::class);
        $httpResponse
            ->shouldReceive('getBody')
            ->andReturn(json_encode($body))
            ->once();
        $httpClient
            ->shouldReceive('get')
            ->andReturn($httpResponse)
            ->once();

        $addonService = new AddonsHttpService($httpClient, $this->loggerMock);
        $addons = $addonService->getUserAddonsActiveForUsageOnDate($branchId, $userId, $activeOn);
        $addon = $addons[0];

        self::assertEquals('id-1234', $addon->serviceId());
        self::assertEquals('definition-id-1234', $addon->serviceDefinitionId());
        self::assertEquals('definition-plan-id-123', $addon->serviceDefinitionPlanId());
        self::assertEquals('Tanning', $addon->serviceDefinitionName());
        self::assertEquals('Recurring', $addon->serviceDefinitionPlanName());
        self::assertEquals(BookableEntityType::CLASSES, $addon->serviceDefinitionType());
        self::assertEquals(2, $addon->serviceAvailableCredits());
    }

    public function testConsumeCredit(): void
    {
        $httpClient = Mockery::mock(Client::class);
        $body = [
            'status' => 'success'
        ];

        $addonId = 'addon-id';
        $credits = 2;
        $branchId = 'mocked-branch-id';
        $bookingId = 'mocked-user-id';
        $activeOn = new Carbon('2021-09-24T20:00:00+01:00');
        $url = 'v3/services/addon-id/credits/consume';

        $httpResponse = Mockery::mock(ResponseInterface::class);
        $httpResponse
            ->shouldReceive('getBody')
            ->andReturn(json_encode($body))
            ->once();
        $httpResponse
            ->shouldReceive('getStatusCode')
            ->andReturn('200')
            ->once();
        $httpClient
            ->shouldReceive('put')
            ->withArgs(function (string $urlParam, array $options) use (
                $url,
                $branchId,
                $credits,
                $activeOn,
                $bookingId
            ) {
                self::assertTextEquals($url, $urlParam);
                self::assertTrue($options['verify']);
                self::assertTextEquals($branchId, $options['headers']['x-glofox-branch-id']);
                self::assertTextEquals('application/json', $options['headers']['Content-Type']);
                $body = json_decode($options['body'], null, 512, JSON_THROW_ON_ERROR);
                self::assertEquals($credits, $body->consumedCredits);
                self::assertEquals($activeOn->toIso8601ZuluString(), $body->eventTimestamp);
                self::assertEquals($bookingId, $body->refId);

                return true;
            })
            ->andReturn($httpResponse)
            ->once();

        $addonService = new AddonsHttpService($httpClient, $this->loggerMock);
        $addonService->consumeAddonCredits($addonId, $credits, $bookingId, $branchId, $activeOn);
    }

    public function test_refund_calls_add_on_service(): void
    {
        $branchId = 'mock-branchId';
        $addOnId = 'mock-addOnId';
        $bookingId = 'mock-bookingId';

        $httpResponse = Mockery::mock(ResponseInterface::class);
        $httpResponse
            ->shouldReceive('getBody')
            ->andReturn(json_encode([]))
            ->once();
        $httpResponse
            ->shouldReceive('getStatusCode')
            ->andReturn('200')
            ->once();

        $httpClient = Mockery::mock(Client::class);
        $httpClient
            ->shouldReceive('put')
            ->withArgs(function (string $urlParam, array $options) use ($branchId, $addOnId, $bookingId) {
                self::assertEquals('v3/services/mock-addOnId/credits/refund', $urlParam);
                self::assertTrue($options['verify']);
                self::assertEquals($options['headers']['x-glofox-branch-id'], $branchId);
                self::assertEquals($options['body'], json_encode(['refId' => $bookingId]));

                return true;
            })
            ->andReturn($httpResponse)
            ->once();

        $addonService = new AddonsHttpService($httpClient, $this->loggerMock);
        $addonService->refundCreditByAddOnIdAndBookingId($branchId, $addOnId, $bookingId);
    }

    public function test_refund_handles_failure_response_from_add_on_service(): void
    {
        $branchId = 'mock-branchId';
        $addOnId = 'mock-addOnId';
        $bookingId = 'mock-bookingId';

        $httpClient = Mockery::mock(Client::class);
        $httpClient
            ->shouldReceive('put')
            ->andThrow($this->genericGenericServerException())
            ->once();

        $this->setExpectedException(
            HttpClientResponseException::class,
            self::GENERIC_SERVER_EXCEPTION_MESSAGE,
            self::GENERIC_SERVER_EXCEPTION_CODE
        );

        $addonService = new AddonsHttpService($httpClient, $this->loggerMock);
        $addonService->refundCreditByAddOnIdAndBookingId($branchId, $addOnId, $bookingId);
    }

    public function testGetUserAddonsReturnsDateValidationError(): void
    {
        $branchId = 'mocked-branch-id';
        $userId = 'mocked-user-id';
        $activeOn = new Carbon('2021-09-24T20:00:00+01:00');

        $httpClient = Mockery::mock(Client::class);
        $httpClient
            ->shouldReceive('get')
            ->andThrow($this->pastBookingAddonServerException())
            ->once();

        $this->setExpectedException(
            HttpClientResponseException::class,
            self::PAST_BOOKING_ERROR,
            self::GENERIC_SERVER_EXCEPTION_CODE
        );

        $addonService = new AddonsHttpService($httpClient, $this->loggerMock);
        $addonService->getUserAddonsActiveForUsageOnDate($branchId, $userId, $activeOn);
    }

    public function testGetUserAddonsReturnsValidationError(): void
    {
        $branchId = 'mocked-branch-id';
        $userId = 'mocked-user-id';
        $activeOn = new Carbon('2021-09-24T20:00:00+01:00');

        $httpClient = Mockery::mock(Client::class);
        $httpClient
            ->shouldReceive('get')
            ->andThrow($this->addonServerException())
            ->once();

        $this->setExpectedException(
            HttpClientResponseException::class,
            self::ADDON_SERVICE_VALIDATION_ERROR,
            self::GENERIC_SERVER_EXCEPTION_CODE
        );

        $addonService = new AddonsHttpService($httpClient, $this->loggerMock);
        $addonService->getUserAddonsActiveForUsageOnDate($branchId, $userId, $activeOn);
    }

    private function pastBookingAddonServerException(): ServerException
    {
        $body = [
            'errors' => [
                [
                    'key' => self::PAST_BOOKING_ERROR_RAW
                ]
            ]
        ];

        return new ServerException(
            'Validation failed',
            new Request('GET', '/foo'),
            new Response(self::GENERIC_SERVER_EXCEPTION_CODE, [], json_encode($body))
        );

    }
    private function addonServerException(): ServerException
    {
        $body = [
            'key' => self::ADDON_SERVICE_VALIDATION_ERROR_RAW,
            'errors' => [
                [
                    'key' => self::GENERIC_SERVER_EXCEPTION_MESSAGE
                ]
            ]
        ];

        return new ServerException(
            'Validation failed',
            new Request('GET', '/foo'),
            new Response(self::GENERIC_SERVER_EXCEPTION_CODE, [], json_encode($body))
        );

    }

    private function genericGenericServerException(): ServerException
    {
        return new ServerException(
            self::GENERIC_SERVER_EXCEPTION_MESSAGE,
            new Request('GET', '/foo'),
            new Response(self::GENERIC_SERVER_EXCEPTION_CODE)
        );
    }
}
