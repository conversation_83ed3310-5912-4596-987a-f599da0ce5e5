# Glofox API CircleCI Configuration
#
# Required Environment Variables:
#   JENKINS_API_USER: Username of the Jenkins API user.
#   JENKINS_API_TOKEN: Jenkins API token.
#   DOCKER_USER: Username for Quay.io authentication.
#   DOCKER_PASSWORD: Password for Quay.io authentication.
version: 2.1

setup: true

parameters:
  mongo-version:
    type: string
    default: 7.0.18
  # Add docker registry parameter as global parameter to allow more control
  docker_registry:
    type: string
    default: 108272622497.dkr.ecr.eu-west-1.amazonaws.com

orbs:
  #Reference Glofox Jenkins Deploy Orb
  gf-circle: glofoxinc/gf-cicd-orb@1
  gf-tests: glofoxinc/e2e-tests-orb@0
  path-filtering: circleci/path-filtering@0.1.3
  continuation: circleci/continuation@0.3.1

defaults: &defaults
  machine:
    image: default

.available_environments: &available_environments
  - "bartik"
  - "lovelace"
  - "hamilton"
  - "hopper"
  - "feature"
  - "terminator"
  - "starwars"
  - "lamarr"
  - "turing"
  - "clarke"
  - "franklin"

.services: &services
  - "api"
  - "apiworker-default"
  - "apiworker-batch"
  - "api-internal"
  - "api-internal-mmt"
  - "apiworker-fulfilment"
  - "apiworker-memberships-priority"

.services_dev: &services_dev
  - "api"
  - "api-primary" # Only deployed in dev/stg/plat
  - "apiworker-default"
  - "apiworker-batch"
  - "api-internal"
  - "api-internal-mmt"
  - "apiworker-fulfilment"
  - "apiworker-memberships-priority"

aliases:
  - &restore_cache
    restore_cache:
      keys:
        - source-v1-{{ .Branch }}-{{ .Revision }}
        - source-v1-{{ .Branch }}-
        - source-v1-
  - &save_cache
    save_cache:
      key: source-v1-{{ .Branch }}-{{ .Revision }}
      paths:
        - ".git"

commands:
  build_docker_image:
    description: Post step on building docker images
    parameters:
      env:
        description: The environment the application is built for
        default: non-platform
        type: string
    steps:
      - run:
          name: Build Api & Apiworker Docker images
          command: |
            # build a docker image with a unique build number
            docker build --build-arg ENV=<< parameters.env >> --build-arg GITHUB_ACCESS_TOKEN=$GITHUB_ACCESS_TOKEN --build-arg VERSION=$CIRCLE_SHA1 -t << pipeline.parameters.docker_registry >>/api:${CIRCLE_BRANCH//\//-}-${CIRCLE_SHA1} .
            echo -e "FROM << pipeline.parameters.docker_registry >>/api:${CIRCLE_BRANCH//\//-}-${CIRCLE_SHA1}\nCMD /var/www/app/Console/cake ApiWorker" | docker build -t << pipeline.parameters.docker_registry >>/apiworker:${CIRCLE_BRANCH//\//-}-${CIRCLE_SHA1} -
            echo -e "FROM << pipeline.parameters.docker_registry >>/api:${CIRCLE_BRANCH//\//-}-${CIRCLE_SHA1}\nCMD /var/www/app/Console/cake ApiWorkerFulfilment" | docker build -t << pipeline.parameters.docker_registry >>/apiworker-fulfilment:${CIRCLE_BRANCH//\//-}-${CIRCLE_SHA1} -

jobs:
  build:
    <<: *defaults
    parameters:
      env:
        type: string
        default: non-platform
    steps:
      - *restore_cache
      - checkout
      - gf-circle/ecr_login
      - build_docker_image:
          env: << parameters.env >>
      - gf-circle/publish_ecr_image:
          service: api
      - gf-circle/publish_ecr_image:
          service: apiworker
      - gf-circle/publish_ecr_image:
          service: apiworker-fulfilment

  rector-checker:
    <<: *defaults
    steps:
      #- *restore_cache
      - checkout
      - gf-circle/ecr_login
      - run:
          name: Build & Run Docker containers
          command: make build
      - run:
          name: Generate rector-paths.json
          command: make generate-rector-paths
      - run:
          name: Run rector dry-run
          command: docker exec glofox_app ./vendor/bin/rector process --dry-run

  run-tests:
    <<: *defaults
    parallelism: 5
    steps:
      - *restore_cache
      - checkout
      - gf-circle/ecr_login
      - build_docker_image
      - run:
          name: Create Docker Network
          command: docker network create db-$CIRCLE_SHA1
      - run:
          name: Start Redis
          command: docker run --rm -d --name redis-$CIRCLE_SHA1 --net=db-$CIRCLE_SHA1 --expose 6379 redis:5.0.3-alpine
      - run:
          name: Start MongoDB
          command: docker run --rm -d --name db-$CIRCLE_SHA1 --net=db-$CIRCLE_SHA1 mongo:<< pipeline.parameters.mongo-version >>
      - run:
          name: Wait for Mongo to start
          command: ./docker/mongowait.sh
      - run:
          name: Run tests
          command: (docker run --net=db-$CIRCLE_SHA1 -e GLOFOX_DATABASE_HOST=db-$CIRCLE_SHA1 -e GLOFOX_REDIS_URL=redis-$CIRCLE_SHA1:6379 -e SENDGRID_PASSWORD=$SENDGRID_PASSWORD -e CIRCLE_NODE_TOTAL=$CIRCLE_NODE_TOTAL -e CIRCLE_NODE_INDEX=$CIRCLE_NODE_INDEX --env-file docker/test/localhost.env --name tests-$CIRCLE_SHA1 << pipeline.parameters.docker_registry >>/api:${CIRCLE_BRANCH//\//-}-${CIRCLE_SHA1} bash -c "./contrib/scripts/xdebug.sh disable && ./app/Console/cake test --debug --no-colors app AllTests") | tee /tmp/testoutput.out && (tail -n 2 /tmp/testoutput.out | head -1 | grep -q 'OK' || (echo 'Test result does not contain OK' && exit 1))

  validate-private-docs:
    <<: *defaults
    steps:
      - *restore_cache
      - checkout
      - *save_cache
      - run:
          name: Run private-docs-lint
          command: make private-docs-lint
      - run:
          name: Validate private docs are up to date
          command: |
            make private-docs
            git diff --quiet && exit 0
            echo "**************************************\n"
            echo "Private docs are not up to date, please run 'make private-docs' in order to bundle the latest changes"
            exit 1

workflows:
  version: 2
  "Build and Test":
    jobs:
      - gf-circle/prebuild-checks:
          context: org-global
      - validate-private-docs:
          context: org-global
          requires:
            - gf-circle/prebuild-checks
          filters:
            branches:
              ignore:
                - master
      - build:
          context: org-global
          requires:
            - gf-circle/prebuild-checks
          filters:
            branches:
              ignore:
                - master
      - rector-checker:
          context: org-global
          requires:
            - gf-circle/prebuild-checks
          filters:
            branches:
              ignore: master

      - run-tests:
          context: org-global
          requires:
            - gf-circle/prebuild-checks

      - build:
          name: build-master
          context: org-global
          env: platform
          filters:
            branches:
              only:
                - master
      # Hold for approval
      - hold_for_approval:
          type: approval
          requires:
            - build
          matrix:
            parameters:
              environment: *available_environments
          filters:
            branches:
              ignore:
                - master
      - gf-circle/ecs_deploy:
          name: << matrix.environment >> Deploy << matrix.service >>
          context: deployments
          environment: << matrix.environment >>
          service: << matrix.service >>
          matrix:
            parameters:
              environment: *available_environments
              service: *services
          requires:
            - hold_for_approval-<< matrix.environment >>
          filters:
            branches:
              ignore:
                - master
      - gf-circle/ecs-wait-for-stable:
          name: Wait for Stable << matrix.environment >>
          context: org-global
          environment: << matrix.environment >>
          service: api apiworker-default api-internal api-internal-mmt apiworker-fulfilment apiworker-memberships-priority
          matrix:
            parameters:
              environment: *available_environments
          requires:
            - << matrix.environment >> Deploy api
            - << matrix.environment >> Deploy apiworker-default
            - << matrix.environment >> Deploy api-internal
            - << matrix.environment >> Deploy api-internal-mmt
            - << matrix.environment >> Deploy apiworker-fulfilment
            - << matrix.environment >> Deploy apiworker-memberships-priority
          filters:
            branches:
              ignore:
                - master
      - run_e2e_tests:
          type: approval
          name: << matrix.environment >> Run E2E tests
          matrix:
            parameters:
              environment: *available_environments
          requires:
            - Wait for Stable << matrix.environment >>
          filters:
            branches:
              ignore:
                - master
      - gf-tests/run-e2e-tests:
          context: org-global
          name: << matrix.command >> on << matrix.environment >>
          requires:
            - << matrix.environment >> Run E2E tests
          command: npm run test:api:<< matrix.command >>:ci
          environment: << matrix.environment >>
          matrix:
            alias: mandatory-e2e-tests
            parameters:
              environment: *available_environments
              command: [ "dashboard:payments", "dashboard:memberships", "dashboard:bookings",
                         "dashboard:members", "dashboard:reports", "dashboard:permissions",
                         "dashboard:appointments", "dashboard:store", "member_app", "pro_app",
                         "cart" ]
          filters:
            branches:
              ignore:
                - master
      - gf-tests/run-e2e-tests:
          context: org-global
          name: << matrix.command >> on << matrix.environment >>
          requires:
            - << matrix.environment >> Run E2E tests
          command: npm run test:api:<< matrix.command >>:ci
          environment: << matrix.environment >>
          circle_ip_ranges: true
          matrix:
            alias: non-mandatory-e2e-tests
            parameters:
              environment: *available_environments
              command: [ "pos" ]
          filters:
            branches:
              ignore:
                - master
      - gf-tests/run-e2e-tests:
          context: org-global
          name: << matrix.command >> on << matrix.environment >>
          requires:
            - << matrix.environment >> Run E2E tests
          command: npm run test:ci -- --suite << matrix.command >>
          environment: << matrix.environment >>
          matrix:
            parameters:
              environment: *available_environments
              command: [ "portal" ]
          filters:
            branches:
              ignore:
                - master
      - gf-circle/ecs_deploy:
          context: deployments
          name: "Development Deploy << matrix.service >>"
          environment: development
          service: << matrix.service >>
          matrix:
            parameters:
              service: *services_dev
          requires:
            - build-master
            - run-tests
          filters:
            branches:
              only: master
      - gf-circle/ecs-wait-for-stable:
          context: org-global
          name: "Wait for Stable"
          environment: development
          service: api api-primary apiworker-default api-internal api-internal-mmt apiworker-fulfilment apiworker-memberships-priority
          requires:
            - "Development Deploy api"
            - "Development Deploy api-primary"
            - "Development Deploy apiworker-default"
            - "Development Deploy api-internal"
            - "Development Deploy api-internal-mmt"
            - "Development Deploy apiworker-fulfilment"
            - "Development Deploy apiworker-memberships-priority"
          filters:
            branches:
              only: master
      - continuation/continue:
          context: org-global
          name: rerun-check
          requires:
            - "Wait for Stable"
          configuration_path: .circleci/e2e-and-deploy-config.yml
          parameters: /home/<USER>/params.json
          pre-steps:
            - run:
                command: |
                  STATUS=$(curl --request GET --url https://circleci.com/api/v2/insights/github/glofoxinc/api/workflows/e2e-tests-and-deploy?branch=master -H "Circle-Token: $CIRCLE_TOKEN" | jq -r '. | .items[0] | .status')
                  if [[ "$STATUS" == "success" ]]; then       # do not continue here as no need to rerun passed workflow
                    circleci-agent step halt
                  else
                    echo '{ "run-e2e-tests-and-deploy": true, "deploy-only": false }' >> /home/<USER>/params.json
                  fi
          filters:
            branches:
              only: master
      - path-filtering/filter:
          context: org-global
          name: "trigger workflow"
          requires:
            - rerun-check
          # run e2e if code changed in app folder excluding app/Test, otherwise just do deploy
          mapping: |
            app\/(?!Test).* run-e2e-tests-and-deploy true
            app\/(?!Test).* deploy-only false
            .circleci.* run-e2e-tests-and-deploy true
            .circleci.* deploy-only false
            composer.* run-e2e-tests-and-deploy true
            composer.* deploy-only false
            docker.* run-e2e-tests-and-deploy true
            docker.* deploy-only false
            index.php run-e2e-tests-and-deploy true
            index.php deploy-only false
            Docker.* run-e2e-tests-and-deploy true
            Docker.* deploy-only false
          base-revision: master
          config-path: .circleci/e2e-and-deploy-config.yml
          pre-steps:
            - run:
                command: |
                  PIPELINE_ID=$(curl --request GET --url https://circleci.com/api/v2/workflow/${CIRCLE_WORKFLOW_ID} -H "Circle-Token: $CIRCLE_TOKEN" | jq -r '. | .pipeline_id')
                  PIPELINE_STATE=$(curl --request GET --url https://circleci.com/api/v2/pipeline/${PIPELINE_ID} -H "Circle-Token: $CIRCLE_TOKEN" | jq -r '. | .state')
                  if [[ "$PIPELINE_STATE" != "setup" ]]; then
                    echo "Pipeline not in setup mode, so stop or this part fails"
                    circleci-agent step halt
                  fi
          filters:
            branches:
              only: master
