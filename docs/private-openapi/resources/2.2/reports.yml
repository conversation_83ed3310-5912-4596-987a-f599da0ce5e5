get:
  tags:
    - Reports
  summary: ''
  operationId: reportId
  description: This endpoint returns metrics relating to reportId referenced
  x-internal: true
  security:
    - coreapi_auth: [ ]
  parameters:
    - schema:
        type: string
      name: branchId
      in: path
      required: true
    - schema:
        type: string
        enum: [ "current-members/list",
                "current-members/by-membership-name",
                "current-members/by-membership-status",
                "current-members/by-addon-name"
              ]
      name: reportId
      in: path
      required: true
  responses:
    '200':
      description: OK
      content:
        application/json:
          schema:
            oneOf:
              - $ref: "../../schemas/current_members.yml"
              - $ref: "../../schemas/current_members_by_membership_status.yml"
              - $ref: "../../schemas/current_members_by_membership_name.yml"
              - $ref: "../../schemas/current_members_by_addon_name.yml"