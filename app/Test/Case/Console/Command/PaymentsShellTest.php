<?php

namespace CakeTestCases\Console\Command;

use CakeTestCases\Glofox\Domain\Logger\Traits\MockedLoggerTrait;
use Glofox\Console\CommandParameter;
use Glofox\Console\CommandParametersCollection;
use Glofox\Infrastructure\Honeycomb\HoneycombTracker;
use Monolog\Logger;
use Psr\Log\LoggerInterface;
use Illuminate\Validation\ValidationException;

\App::import('Test/Case', 'GlofoxTestCase');
\App::import('Console/Command', 'PaymentsShell');

class PaymentsShellTest extends \GlofoxTestCase
{
    use MockedLoggerTrait;

    public function setUp(): void
    {
        parent::setUp();

        $honeyCombTracker = \Mockery::mock(HoneycombTracker::class);
        $honeyCombTracker->shouldReceive('track')->getMock();

        app()->instance(HoneycombTracker::class, $honeyCombTracker);
    }

    public function tearDown(): void
    {
        \Mockery::close();
        $this->teardownLogger();

        parent::tearDown();

        app()->forgetInstance(HoneycombTracker::class);
    }

    public function test_it_should_inject_the_right_logger_for_command_classes()
    {
        $this->teardownLogger();

        $requestedLogger = app()->make(LoggerInterface::class);
        $this->assertInstanceOf(Logger::class, $requestedLogger);
    }

    public function test_it_should_validate_command_specific_parameters_upon_input()
    {
        $logs = collect();
        $mockedLogger = \Mockery::mock(LoggerInterface::class)
            ->shouldReceive('info', 'error', 'warning', 'error', 'debug')
            ->andReturnUsing(function($msg) use ($logs) {
                $logs->push($msg);
            })
            ->getMock();

        app()->instance(LoggerInterface::class, $mockedLogger);

        $paymentsShell = new \PaymentsShell();

        $spec = new CommandParametersCollection();

        $spec->push(
            (new CommandParameter())
            ->setName('param_foo')
        );

        try {
            $this->invokeMethod($paymentsShell, 'validateParameters', [[], $spec]);
        }
        catch (\Exception $exception) {
            $this->assertInstanceOf(ValidationException::class, $exception);
        }

        $this->assertEquals('{"param_foo":["The param foo field is required."]}', $logs->get(0));

        $this->invokeMethod($paymentsShell, 'validateParameters', [['param_foo' => 'bar'], $spec]);
    }

    private function invokeMethod(&$object, $methodName, array $parameters = [])
    {
        $reflection = new \ReflectionClass(\get_class($object));
        $method = $reflection->getMethod($methodName);
        $method->setAccessible(true);

        return $method->invokeArgs($object, $parameters);
    }
}
