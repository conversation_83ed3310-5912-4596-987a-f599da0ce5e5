@startuml
start

:bookTimeSlotAgnostic(payload, bookingRequestParams);
#pink:slotID = getSlotId(payload, bookingRequestParams);
if (isStaff) then (yes)
  #green:result = time_slot_add_web();
else (not)
  #green:result = time_slot_add();
endif
if (resultIsSuccess) then (yes)
else (not)
  if (isNotAnAppointment || thereIsNotBookings) then (yes)
    #pink:removeSlotById(payload, slotID);
  else (not)
  endif
endif
:return result;

stop
@enduml
