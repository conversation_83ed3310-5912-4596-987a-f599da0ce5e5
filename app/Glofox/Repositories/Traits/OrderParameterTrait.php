<?php

declare(strict_types=1);

namespace Glofox\Repositories\Traits;

use Glofox\Request;

trait OrderParameterTrait
{
    private static $defaultOrder = 'created|asc';

    protected function orderParameter(Request $request, string $defaultOrder = null): array
    {
        $defaultOrder = $defaultOrder ?: static::$defaultOrder;
        $orderParameter = $request->query->get('order', $defaultOrder);
        [$field, $direction] = explode('|', $orderParameter);
        $direction = 'asc' === strtolower($direction) ? 1 : -1;

        return [$field => $direction];
    }
}
