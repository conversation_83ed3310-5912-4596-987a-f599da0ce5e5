<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\Communications\Services;

use Glofox\Domain\Authentication\TokenGenerator;
use Glofox\Domain\Communications\UseCase\ModerateMessageParams;
use Glofox\Domain\RequestLog\Repositories\RequestLogRepository;
use Glofox\Domain\Users\Models\User;
use Glofox\Http\Requests\TracingContextInterface;
use Glofox\Domain\Communications\Services\CommunicationsClient;
use Glofox\Infrastructure\Cache\RedisCacheClient;
use GuzzleHttp\ClientInterface;
use Psr\Http\Message\ResponseInterface;
use Psr\Log\LoggerInterface;
use Glofox\Infrastructure\Honeycomb\HoneycombTracker;

\App::import('Test/Case', 'GlofoxTestCase');

class CommunicationsClientTest extends \GlofoxTestCase
{
    public $fixtures = [
        'app.branch',
    ];
    private ClientInterface $httpClient;
    private RequestLogRepository $logRepository;
    private LoggerInterface $logger;
    private TracingContextInterface $tracingContext;
    private RedisCacheClient $cacheClient;
    private HoneycombTracker $honeyCombTracker;
    private User $user;
    private string $content = 'test content';
    private array $attachments;

    public function setUp(): void
    {
        parent::setUp();

        $this->logger = \Mockery::mock(LoggerInterface::class);
        $this->logger->shouldReceive('info');
        $this->logger->shouldReceive('warning');
        $this->logger->shouldReceive('error');

        $this->tracingContext = \Mockery::mock(TracingContextInterface::class);
        $this->cacheClient = \Mockery::mock(RedisCacheClient::class);
        $this->tracingContext->shouldReceive('getTracingHeaderForPropagation')
            ->andReturn('1;trace_id=b4915148da953bf82cd30854cd7dba30,parent_id=7248651b52e67446');
        $this->tracingContext->shouldReceive('getSpanId')
            ->andReturn('7248651b52e67446');
        $this->tracingContext->shouldReceive('getTraceId')
            ->andReturn('b4915148da953bf82cd30854cd7dba30');
            
        $this->user = new User([
            'branch_id' => '49a7011a05c677b9a916612a',
            'origin_branch_id' => '49a7011a05c677b9a916612a',
            'namespace' => 'glofox',
            'first_name' => 'Test',
            'last_name' => 'User',
            'type' => 'MEMBER',
            '_id' => '606d649eedb582159a46ef09',
        ]);

        $_SERVER['HTTP_X_GLOFOX_BRANCH_ID'] = '49a7011a05c677b9a916612a';
        $_SERVER['HTTP_X_REQUEST_ID'] = 'fake-request-id';

        $fileName = 'receipt.pdf';
        $filePath = $this->createAttachmentFile($fileName, $this->content);

        $this->attachments = [
            'receipt.pdf' => [
                'file' => $filePath,
                'mimetype' => 'application/pdf',
            ],
        ];

        $tokenGeneratorMock = \Mockery::mock(TokenGenerator::class);
        $tokenGeneratorMock->shouldReceive('generate')->andReturn('fake-token');
        app()->instance(TokenGenerator::class, $tokenGeneratorMock);

        $this->honeyCombTracker = \Mockery::mock(HoneycombTracker::class);
        $this->honeyCombTracker->shouldReceive('track');
        app()->instance(HoneycombTracker::class, $this->honeyCombTracker);
    }

    public function tearDown()
    {
        parent::tearDown();
        \Mockery::close();
        unset($_SERVER['HTTP_X_GLOFOX_BRANCH_ID']);
        unset($_SERVER['HTTP_X_REQUEST_ID']);
        unset($_SERVER['HTTP_X_GLOFOX_API_TOKEN']);
        app()->forgetInstance(TokenGenerator::class);
        app()->forgetInstance(HoneycombTracker::class);
    }

    public function test_upload_attachment_success()
    {
        $this->httpClient = \Mockery::mock(ClientInterface::class);

        $this->httpClient->shouldReceive('request')
            ->once()
            ->with(
                'POST',
                'v1/upload-attachment',
                \Mockery::on(fn($arg) => isset($arg['headers']['X-Glofox-Api-Token']) &&
                    isset($arg['headers']['Content-Type']) &&
                    $arg['http_errors'] === false)
            )
            ->andReturn(
                \Mockery::mock(ResponseInterface::class)
                    ->shouldReceive('getBody')
                    ->andReturn('{"url":"http://example.com"}')
                    ->shouldReceive('getStatusCode')
                    ->andReturn(200)
                    ->shouldReceive('getHeader')
                    ->andReturn(['application/json'])
                    ->getMock()
            );

        $this->logRepository = \Mockery::mock(RequestLogRepository::class);

        $response = $this->createClient()->uploadAttachment($this->attachments, $this->user);

        $this->assertEquals('http://example.com', $response);
    }

    public function test_upload_attachment_failure()
    {
        $this->httpClient = \Mockery::mock(ClientInterface::class);

        $this->httpClient->shouldReceive('request')
            ->once()
            ->with(
                'POST',
                'v1/upload-attachment',
                \Mockery::on(fn($arg) => isset($arg['headers']['X-Glofox-Api-Token']) &&
                    isset($arg['headers']['Content-Type']) &&
                    $arg['http_errors'] === false)
            )
            ->andReturn(
                \Mockery::mock(ResponseInterface::class)
                    ->shouldReceive('getBody')
                    ->andReturn('{"status":500}')
                    ->shouldReceive('getStatusCode')
                    ->andReturn(500)
                    ->shouldReceive('getHeader')
                    ->andReturn(['application/json'])
                    ->getMock()
            );

        $this->logRepository = \Mockery::mock(RequestLogRepository::class);

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Received a failed response from Communications API');

        $this->createClient()->uploadAttachment($this->attachments, $this->user);

    }

    public function test_moderate_message_success()
    {
        $params = \Mockery::mock(ModerateMessageParams::class);
        $params->shouldReceive('branchId')->andReturn('49a7011a05c677b9a916612a');
        $params->shouldReceive('message')->andReturn('This is a test message');
        $params->shouldReceive('identifier')->andReturn('PASSWORD_RESET');

        $mockResponse = \Mockery::mock(ResponseInterface::class);
        $mockResponse->shouldReceive('getStatusCode')->andReturn(200);
        $mockResponse->shouldReceive('getBody')->andReturn('{"is_risky": false}');
        $mockResponse->shouldReceive('getHeader')->andReturn(['application/json']);

        $this->cacheClient->shouldReceive("get")->once();
        $this->cacheClient->shouldReceive("setIfNotExist")->once();

        $this->httpClient = \Mockery::mock(ClientInterface::class);
        $this->httpClient->shouldReceive('request')
            ->once()
            ->with(
                'POST',
                'v1/messages/moderate',
                \Mockery::on(fn($args) => isset($args['headers']['X-Glofox-Api-Token']) &&
                    $args['http_errors'] === false &&
                    str_contains($args['body'], 'This is a test message')
                )
            )
            ->andReturn($mockResponse);

        $this->logRepository = \Mockery::mock(RequestLogRepository::class);

        $client = $this->createClient();

        $client->moderateMessage($params);
    }

    public function test_moderate_message_failure()
    {
        $params = \Mockery::mock(ModerateMessageParams::class);
        $params->shouldReceive('branchId')->andReturn('49a7011a05c677b9a916612a');
        $params->shouldReceive('message')->andReturn('Malicious content');
        $params->shouldReceive('identifier')->andReturn('PASSWORD_RESET');

        $mockResponse = \Mockery::mock(ResponseInterface::class);
        $mockResponse->shouldReceive('getStatusCode')->andReturn(400);
        $mockResponse->shouldReceive('getBody')->andReturn('Bad Request');
        $mockResponse->shouldReceive('getHeader')->andReturn(['application/json']);

        $this->cacheClient->shouldReceive("get")->once();

        $this->httpClient = \Mockery::mock(ClientInterface::class);
        $this->httpClient->shouldReceive('request')
            ->once()
            ->andReturn($mockResponse);

        $this->logRepository = \Mockery::mock(RequestLogRepository::class);

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Received a failed response from Communications API');

        $client = $this->createClient();
        $client->moderateMessage($params);
    }

    public function test_moderate_message_throws_json_exception()
    {
        $params = \Mockery::mock(ModerateMessageParams::class);
        $params->shouldReceive('branchId')->andReturn('49a7011a05c677b9a916612a');
        $params->shouldReceive('message')->andReturn("\xB1\x31"); // invalid JSON string
        $params->shouldReceive('identifier')->andReturn('PASSWORD_RESET');

        $this->httpClient = \Mockery::mock(ClientInterface::class);
        $this->logRepository = \Mockery::mock(RequestLogRepository::class);

        $this->cacheClient->shouldReceive("get")->once();

        $this->expectException(\JsonException::class);
        $this->expectExceptionMessage('Malformed UTF-8 characters');

        $client = $this->createClient();
        $client->moderateMessage($params);
    }

    private function createClient(): CommunicationsClient
    {
        return new CommunicationsClient(
            $this->httpClient,
            $this->logRepository,
            $this->logger,
            $this->tracingContext,
            $this->cacheClient,
            $this->honeyCombTracker
        );
    }

    private function createAttachmentFile(string $fileName, string $content): string
    {
        $filePath = '/var/www/app/tmp/' . $fileName;
        file_put_contents($filePath, $content);
        $mimeType = mime_content_type($filePath);

        $this->attachments[$fileName] = [
            'file' => $filePath,
            'mimetype' => $mimeType,
        ];

        return $filePath;
    }
}
