<?php

namespace Glofox\Response\Models\Events;

use Glofox\Response\BaseModel;

/**
 * @SuppressWarnings(PHPMD)
 */
class ProgramObjModel extends BaseModel
{
    /**
     * @var string
     */
    public $_id = '';

    /**
     * @var array
     */
    public $allowed_member_types = [];

    /**
     * @var string
     */
    public $branch_id = '';

    /**
     * @var string
     */
    public $description = '';

    /**
     * @var int
     */
    public $modified = 0;

    /**
     * @var string
     */
    public $name = '';

    /**
     * @var array
     */
    public $pricing = [];

    /**
     * @var string
     */
    public $type = '';

    /**
     * @var string
     */
    public $image_url = '';

    /**
     * @var array|null
     */
    public $categories = null;

    protected array $allowedFields = [
        '_id',
        'allowed_member_types',
        'branch_id',
        'description',
        'modified',
        'name',
        'pricing',
        'type',
        'image_url',
        'room_map_image_url',
        'categories'
    ];

    public function __construct(array $data)
    {
        $this->setModelFields($data);
    }
}
