<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\Memberships\Http;

use App;
use Glofox\Domain\Memberships\Services\EditPause\EditPauseService;
use Glofox\Domain\Memberships\Services\EditPause\PutPauseParams;
use Glofox\Domain\Memberships\Services\EditPause\GetPauseParams;
use GlofoxControllerTestCase;
use Illuminate\Http\JsonResponse;
use Mockery;

App::import('Test/Case', 'GlofoxControllerTestCase');

final class MembershipEditPauseControllerTest extends GlofoxControllerTestCase
{
    public function tearDown(): void
    {
        parent::tearDown();
        Mockery::close();
    }

    public function test_get(): void
    {
        $this->loginAsUser(
            $this->fetchUser('58568a8fa875ab19530041a7')
        );

        $branchId = '49a7011a05c677b9a916612a';
        $userMembershipId = 'userMembershipId';

        $useCase = Mockery::mock(EditPauseService::class);
        $useCase->shouldReceive('get')
            ->once()
            ->withArgs(function (GetPauseParams $params) use ($userMembershipId) {
                $this->assertSame($userMembershipId, $params->getUserMembershipId());

                return true;
            })
            ->andReturn(
                new JsonResponse([
                    'name' => 'John',
                    'age' => 50,
                ], 200)
            );

        app()->instance(EditPauseService::class, $useCase);

        $url = sprintf('/2.2/branches/%s/memberships/%s/pause', $branchId, $userMembershipId);
        $body = $this->testAction($url, ['method' => 'GET']);

        $this->assertSame('{"name":"John","age":50}', $body);
        $this->assertSame(200, $this->response->statusCode());
        app()->forgetInstance(EditPauseService::class);
    }

    public function test_put(): void
    {
        $this->loginAsUser(
            $this->fetchUser('59a7011a05c677bda916612a')
        );

        $branchId = '49a7011a05c677b9a916612a';
        $userMembershipId = 'user-membership-id-123';

        $data = [
            'pauseStartDate' => '2020-05-06T16:15:31+01:00',
            'durationAmount' => '10',
            'durationUnit' => 'DAY',
        ];

        $useCase = Mockery::mock(EditPauseService::class);
        $useCase->shouldReceive('put')
            ->once()
            ->withArgs(function (PutPauseParams $params) use ($userMembershipId, $data) {
                $this->assertSame($userMembershipId, $params->getUserMembershipId());
                $this->assertSame($data['pauseStartDate'], $params->getPauseStartDate());
                $this->assertSame(10, $params->getDurationAmount());
                $this->assertSame($data['durationUnit'], $params->getDurationUnit());

                return true;
            })
            ->andReturn(
                new JsonResponse([
                    'foo' => 10,
                    'bar' => 'baz',
                ], 200)
            );

        app()->instance(EditPauseService::class, $useCase);

        $url = sprintf(
            '/2.2/branches/%s/memberships/%s/pause',
            $branchId,
            $userMembershipId
        );

        $body = $this->testAction($url, [
            'method' => 'PUT',
            'data' => $data,
        ]);

        $this->assertSame('{"foo":10,"bar":"baz"}', $body);
        $this->assertSame(200, $this->response->statusCode());
        app()->forgetInstance(EditPauseService::class);
    }

    public function test_delete(): void
    {
        $this->loginAsUser(
            $this->fetchUser('59a7011a05c677bda916612a')
        );

        $branchId = '49a7011a05c677b9a916612a';
        $userMembershipId = 'user-membership-id-123';

        $useCase = Mockery::mock(EditPauseService::class);
        $useCase->shouldReceive('delete')
            ->once()
            ->withArgs(function (GetPauseParams $params) use ($userMembershipId) {
                $this->assertSame($userMembershipId, $params->getUserMembershipId());

                return true;
            })
            ->andReturn(
                new JsonResponse(null, 204)
            );

        app()->instance(EditPauseService::class, $useCase);

        $url = sprintf(
            '/2.2/branches/%s/memberships/%s/pause',
            $branchId,
            $userMembershipId
        );

        $body = $this->testAction($url, [
            'method' => 'DELETE',
        ]);

        $this->assertSame('{}', $body);
        $this->assertSame(204, $this->response->statusCode());
        app()->forgetInstance(EditPauseService::class);
    }

    public function test_post(): void
    {
        $this->loginAsUser(
            $this->fetchUser('59a7011a05c677bda916612a')
        );

        $branchId = '49a7011a05c677b9a916612a';
        $userMembershipId = 'user-membership-id-123';

        $data = [
            'pauseStartDate' => '2020-05-06T16:15:31+01:00',
            'durationAmount' => '10',
            'durationUnit' => 'DAY',
        ];

        $useCase = Mockery::mock(EditPauseService::class);
        $useCase->shouldReceive('post')
            ->once()
            ->withArgs(function (PutPauseParams $params) use ($userMembershipId, $data) {
                $this->assertSame($userMembershipId, $params->getUserMembershipId());
                $this->assertSame($data['pauseStartDate'], $params->getPauseStartDate());
                $this->assertSame(10, $params->getDurationAmount());
                $this->assertSame($data['durationUnit'], $params->getDurationUnit());

                return true;
            })
            ->andReturn(
                new JsonResponse([
                    'foo' => 10,
                    'bar' => 'baz',
                ], 201)
            );

        app()->instance(EditPauseService::class, $useCase);

        $url = sprintf(
            '/2.2/branches/%s/memberships/%s/pause/calculate',
            $branchId,
            $userMembershipId
        );

        $body = $this->testAction($url, [
            'method' => 'POST',
            'data' => $data,
        ]);

        $this->assertSame('{"foo":10,"bar":"baz"}', $body);
        $this->assertSame(201, $this->response->statusCode());
        app()->forgetInstance(EditPauseService::class);
    }
}
