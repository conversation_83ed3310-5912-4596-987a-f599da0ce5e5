<?php

namespace CakeTestCases\Glofox\Payments\Providers\Gateway\Subscriptions;

use Glofox\Calendar\ICalendar\Event;
use Glofox\Calendar\ICalendar\Reader;
use Glofox\Domain\PaymentMethods\Models\PaymentMethod;
use Glofox\Domain\PaymentMethods\Type;
use Glofox\Domain\PaymentProviders\Models\PaymentProvider;
use Glofox\Payments\Entities\Subscription\Internal\CreateSubscription;
use Glofox\Payments\Entities\Subscription\Models\Subscription;
use Glofox\Payments\Entities\Subscription\Models\SubscriptionCycleConfirmation;
use Glofox\Payments\Providers\Gateway\GRPC\Connection;
use Glofox\Payments\Providers\Gateway\Subscriptions\Subscriptions;
use Glofox\Request;
use GRPC\Payments\SubscriptionService\ConfirmSubscriptionCycleReq;
use GRPC\Payments\SubscriptionService\CreateSubscriptionReq;
use GRPC\Payments\SubscriptionService\EmptySubscriptionObject;
use GRPC\Payments\SubscriptionService\Subscription as PaymentsSubscription;
use GRPC\Payments\SubscriptionService\SubscriptionServiceClient;
use Grpc\UnaryCall;

\App::import('Test/Case', 'GlofoxTestCase');

class SubscriptionsTest extends \GlofoxTestCase
{
    public function tearDown()
    {
        parent::tearDown();

        \Mockery::close();
    }

    public function test_it_sends_transaction_id_to_ical_event(): void
    {
        $this->mockConnection(
            $expectedValue = 'trans-id'
        );

        $parameters = (new CreateSubscription())->setSubscription(
            (new Subscription())
                ->setTransactionUUID('trans-id')
                ->setAmount(1000)
                ->setFee(0)
                ->setPlanId(11)
                ->setTrialEndAt(strtotime('+1 week'))
                ->setMetadata([])
        );

        $subscriptionsHandler = new Subscriptions(new PaymentMethod(['type_id' => Type::CARD]), new PaymentProvider());
        $subscriptionsHandler->subscribe($parameters);

        app()->forgetInstance(Connection::class);
    }

    public function test_it_sends_an_empty_string_if_transaction_id_is_not_set(): void
    {
        $this->mockConnection(
            $expectedValue = ''
        );

        $parameters = (new CreateSubscription())->setSubscription(
            (new Subscription())
                ->setAmount(1000)
                ->setFee(0)
                ->setPlanId(11)
                ->setTrialEndAt(strtotime('+1 week'))
                ->setMetadata([])
        );

        $subscriptionsHandler = new Subscriptions(new PaymentMethod(['type_id' => Type::CARD]), new PaymentProvider());
        $subscriptionsHandler->subscribe($parameters);

        app()->forgetInstance(Connection::class);
    }

    public function test_it_confirms_subscription_cycle(): void
    {
        $request = \Mockery::mock(Request::class);
        app()->instance(Request::class, $request);

        $connection = \Mockery::mock(Connection::class);
        $connection->subscriptionService = \Mockery::mock(SubscriptionServiceClient::class);
        $connection->subscriptionService->shouldReceive('ConfirmSubscriptionCycle')
            ->withArgs(function (ConfirmSubscriptionCycleReq $request) {
                $this->assertSame(123, $request->getSubscriptionCycleID());
                $this->assertSame(0, $request->getConfirmed());
                return true;
            })
            ->andReturn(
                \Mockery::mock(UnaryCall::class)
                    ->shouldReceive('wait')
                    ->andReturn([
                        new EmptySubscriptionObject(),
                        (object) ['code' => 0],
                    ])
                    ->getMock()
            );
        app()->instance(Connection::class, $connection);

        $subscriptions = new Subscriptions(new PaymentMethod(), new PaymentProvider());
        $subscriptions->confirmSubscriptionCycle('123', SubscriptionCycleConfirmation::CONFIRMED());

        app()->forgetInstance(Request::class);
        app()->forgetInstance(Connection::class);
    }

    public function test_it_sends_discounts_if_any(): void
    {
        $discounts = ['foo', 'bar'];

        $connection = \Mockery::mock(Connection::class);
        $connection->subscriptionService = \Mockery::mock(SubscriptionServiceClient::class);
        $connection->subscriptionService->shouldReceive('Create')
            ->withArgs(function (CreateSubscriptionReq $request) use ($discounts) {
                $values = iterator_to_array($request->getDiscounts()->getIterator());
                $this->assertSame($discounts, $values);

                return true;
            })
            ->andReturn(
                \Mockery::mock(UnaryCall::class)
                    ->shouldReceive('wait')
                    ->andReturn([
                        (new PaymentsSubscription())
                            ->setID(2345)
                            ->setICalRule($this->icalStub()),
                        (object) ['code' => 0],
                    ])
                    ->getMock()
            );

        app()->instance(Connection::class, $connection);

        $parameters = (new CreateSubscription())
            ->setSubscription(
                (new Subscription())
                    ->setAmount(1000)
                    ->setFee(0)
                    ->setPlanId(11)
                    ->setTrialEndAt(strtotime('+1 week'))
                    ->setMetadata([])
                    ->setDiscounts($discounts)
            );

        $paymentMethod = new PaymentMethod(['type_id' => Type::CARD]);
        $paymentProvider = new PaymentProvider();

        $subscriptions = new Subscriptions($paymentMethod, $paymentProvider);
        $subscriptions->subscribe($parameters);

        app()->forgetInstance(Connection::class);
    }

    private function mockConnection(string $expectedValue): Connection
    {
        $connection = \Mockery::mock(Connection::class);
        $connection->subscriptionService = \Mockery::mock(SubscriptionServiceClient::class);
        $connection->subscriptionService->shouldReceive('Create')
            ->withArgs(function (CreateSubscriptionReq $request) use ($expectedValue) {
                $calendar = Reader::read($request->getICalRule());
                $calendar->events()->each(function (Event $event) use ($expectedValue) {
                    $this->assertSame($expectedValue, $event->getDescription()->get('transaction_uuid'));
                });

                return true;
            })
            ->andReturn(
                \Mockery::mock(UnaryCall::class)
                    ->shouldReceive('wait')
                    ->andReturn([
                        (new PaymentsSubscription())
                            ->setID(2345)
                            ->setICalRule($this->icalStub()),
                        (object) ['code' => 0],
                    ])
                    ->getMock()
            );

        app()->instance(Connection::class, $connection);

        return $connection;
    }

    private function iCalStub(): string
    {
        return <<<CALENDAR_WRAP
BEGIN:VCALENDAR
VERSION:2.0
PRODID:https://glofox.com
BEGIN:VEVENT
UID:5dcc6710cda64
DTSTART:20191120T202756Z
SEQUENCE:0
TRANSP:OPAQUE
DTEND:20191120T202756Z
SUMMARY:10 USD every 1 days
CLASS:PUBLIC
DESCRIPTION:{"amount":1000,"fee":0,"currency":"USD","transactionUUID":"tran
 s-id","descriptions":"[]","payment_method":null}
DTSTAMP:20191113T202656Z
END:VEVENT
END:VCALENDAR
CALENDAR_WRAP;
    }
}
