<?php

use Glofox\Domain\TimeSlots\Events\TimeslotWasDeleted;
use Glofox\Domain\TimeSlots\Http\Requests\HasTimeslotsRequest;
use Glofox\Domain\TimeSlots\Repositories\FindAllByBranchIdAndDateRangeParams;
use Glofox\Domain\TimeSlots\Repositories\TimeSlotRepository;
use Glofox\Domain\TimeSlots\Validation\PersonalTrainingAppointmentValidator;
use Glofox\Events\EventManager;

/**
 * TimeSlotsController class.
 *
 * @uses          AppController
 */
class TimeSlotsController extends \Glofox\Domain\TimeSlots\Http\TimeSlotsController
{
    /**
     * @var string
     */
    public $name = 'TimeSlot';

    /**
     * @var array
     */
    public $uses = [
        'TimeSlot',
        'TimeSlotPattern',
        'User',
    ];

    /**
     * @var array
     */
    public $components = [
        'Utility',
        'JWT',
    ];

    /*******************************************************************************************
     *                                API 2.0 New Dashboard
     *****************************************************************************************
     *
     * @param $identifier
     *
     * @return string
     */

    /*!
       * Dispatcher for Model
       * @param  [type] $identifier [description]
       * @return [type]             [description]
       */
    public function dispatcher($identifier)
    {
        return parent::dispatch($identifier, $this->TimeSlot);
    }

    /**
     * Get slots by specifying all in branch, all in branch or by specific id. If an id is specified, the event iself is brought
     * if no id is specified, we ge the events from here to the visible date displayed.
     *
     * @param string $id Event id
     *
     * @return array List of events that match the entry params
     * @internal   This convention is a little boring for clients, but helps us keep clean backend
     *
     * @example    https://www.glofoxlogin/timeslot/view/{id}
     *
     */
    public function view($id)
    {
        $user = $this->JWT->parseToken();
        $date_format = $this->TimeSlot->get_date_format('date');
        $start_date = $this->TimeSlot->getCurrentDate($date_format);
        $weeks_to_display = $this->Branch->findWeeksToDisplay($user['branch_id'])['users'];
        $end_date = $this->Branch->addNumberOfWeeks($start_date, $weeks_to_display, $date_format);

        return isset($id) ? json_encode($this->TimeSlot->findById($id), JSON_PARTIAL_OUTPUT_ON_ERROR) : $this->dateRange(
            $user['branch_id'],
            $start_date,
            $end_date
        );
    }

    /**
     * Save a slots object including schedules and patterns, if object does not have an _id
     * it will add it new, else it will update that current instance, This does not upload images
     * to AWS. You must use the image upload service as a separate request to upload images.
     *
     * _pattern : {
     *         schedule: [
     *             {
     *                 week_day: 1, start_time: "8:00", end_time: "20:00", active: true, _total: 12,…},…],…}
     *                 schedule: [{ week_day: 1, start_time: "8:00", end_time: "20:00", active: true, _total: 12,…},…] 0 :
     *             {
     *                 week_day: 1, start_time: "8:00", end_time: "20:00", active: true, _total: 12,…} 1 : {week_day: 3, start_time: "8:00", end_time: "20:00", active: true, _total: 12,…} time_slot_length : 60
     *
     * @param \Glofox\Request $request
     *
     * @return object {Course:object,success:boolean}
     *
     * @throws Exception
     * @example    https://www.glofoxlogin/timeslot/upsert Payload: Course object directly
     *
     * @internal   Some data validation required since its a complex object. TO_DO
     *
     */
    public function upsert(\Glofox\Request $request)
    {
        $data = $request->data();

        $user = $this->JWT->parseToken();

        $allowedRoles = [
            UserType::SUPERADMIN,
            UserType::ADMIN,
            UserType::RECEPTIONIST,
            UserType::TRAINER,
        ];
        $this->JWT->validateOrFail($allowedRoles, $user['namespace'], $user['branch_id']);

        $model = $data->get('model');

        if ('users' === $model) {
            $personalTrainingValidator = app()->make(PersonalTrainingAppointmentValidator::class);
            $personalTrainingValidator->validate($data);
        }

        $data = $data->toArray();
        $response = $this->TimeSlot->save($data);

        return response()->json($response);
    }

    /**
     * Delete slots given its _id.
     *
     * @param string $id Event _id
     *
     * @return object {Event:object, success:boolean}
     * @internal   Logic delete, setting "active" attribute to false
     *
     * @example    https://www.glofoxlogin/timeslot/remove/{id}
     *
     */
    public function remove($id)
    {
        $user = $this->JWT->parseToken();

        $allowedRoles = [
            UserType::SUPERADMIN,
            UserType::ADMIN,
            UserType::RECEPTIONIST,
            UserType::TRAINER,
        ];
        $this->JWT->validateOrFail($allowedRoles, $user['namespace'], $user['branch_id']);

        $timeslot = $this->TimeSlot->findById($id);
        $timeslot['TimeSlot']['active'] = false;
        $timeslot = $this->TimeSlot->save($timeslot);

        $eventManager = new EventManager();
        $eventManager->emit(TimeslotWasDeleted::class, [$timeslot]);

        $active = $timeslot['TimeSlot']['active'];

        $result = [
            'success' => !$active,
        ];

        $response = json_encode($result, JSON_PARTIAL_OUTPUT_ON_ERROR);

        return $response;
    }

    /**
     * @param null $week_number
     * @param string $year
     *
     * @return string
     */
    public function getNextWeek($week_number = null, $year = '')
    {
        $date['week'] = date('W', strtotime($year . 'W' . str_pad($week_number, 2, 0, STR_PAD_LEFT) . ' +1 week'));
        $date['year'] = date('o', strtotime($year . 'W' . str_pad($week_number, 2, 0, STR_PAD_LEFT) . ' +1 week'));

        return json_encode(['success' => true, 'week_number' => $date['week'], 'year' => $date['year']], JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    /**
     * @param null $week_number
     * @param string $year
     *
     * @return string
     */
    public function getPreviousWeek($week_number = null, $year = '')
    {
        $date['week'] = date('W', strtotime($year . 'W' . str_pad($week_number, 2, 0, STR_PAD_LEFT) . ' -1 week'));
        $date['year'] = date('o', strtotime($year . 'W' . str_pad($week_number, 2, 0, STR_PAD_LEFT) . ' -1 week'));

        return json_encode(['success' => true, 'week_number' => $date['week'], 'year' => $date['year']], JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    /**
     * @param null $day
     * @param null $month
     * @param string $year
     *
     * @return string
     */
    public function getNextDay($day = null, $month = null, $year = '')
    {
        $cur_date = date('Y-m-d', strtotime($year . '-' . $month . '-' . $day));
        $cur_date = date('Y-m-d', strtotime($cur_date . ' +1 day'));
        $date = explode('-', $cur_date);

        return json_encode(['success' => true, 'day' => $date['2'], 'month' => $date['1'], 'year' => $date['0']], JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    /**
     * @param null $day
     * @param null $month
     * @param string $year
     *
     * @return string
     */
    public function getPreviousDay($day = null, $month = null, $year = '')
    {
        $cur_date = date('Y-m-d', strtotime($year . '-' . $month . '-' . $day));
        $cur_date = date('Y-m-d', strtotime($cur_date . ' - 1 day'));
        $date = explode('-', $cur_date);

        return json_encode(['success' => true, 'day' => $date['2'], 'month' => $date['1'], 'year' => $date['0']], JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    /**
     * @param        $model
     * @param        $model_id
     * @param null $week_number
     * @param string $year
     *
     * @return string
     */
    public function findCalendarInfo($model, $model_id, $week_number = null, $year = '')
    {
        $week_number = $week_number ?: date('W');
        $year = empty($year) ? date('Y') : $year;
        $week_days = $this->TimeSlot->getDaysGivenWeekNumberAndYear($week_number, $year);

        $this->loadModel('TimeSlotPattern');
        $timeSlotPattern = $this->TimeSlotPattern->findByModelAndModelId($model, $model_id);

        $this->loadModel('Branch');
        $default_slot_length = $this->Branch->findDefaultTimeSlotLength(
            $timeSlotPattern['TimeSlotPattern']['branch_id'],
            $model
        );
        $time_slot_length = $timeSlotPattern['TimeSlotPattern']['time_slot_length'] ?? $default_slot_length;

        $time_range = $this->TimeSlotPattern->getTimeRangeById($timeSlotPattern, $time_slot_length);

        $this->loadModel('TimeSlot');
        $time_slot_ids = $this->TimeSlot->findIdsByTimeSlotPatternId($timeSlotPattern['TimeSlotPattern']['_id']);

        $this->loadModel('Booking');
        $bookings = $this->Booking->findByTimeSlotIds($time_slot_ids);

        $calendar_info = $this->TimeSlot->buildDate(
            $timeSlotPattern['TimeSlotPattern']['_id'],
            $week_days['dates'],
            $time_range['times'],
            $bookings,
            $week_number,
            $year
        );

        return json_encode(['success' => true, 'calendar' => $calendar_info, 'week_number' => $week_number], JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    /**
     * @param        $model
     * @param        $model_id
     * @param null $day
     * @param null $month
     * @param string $year
     *
     * @return string
     */
    public function findCalendarInfoByDay($model, $model_id, $day = null, $month = null, $year = '')
    {
        $this->loadModel('Booking');
        $this->loadModel('User');
        $day = $day ?: date('d');
        $month = $month ?: date('m');
        $year = empty($year) ? date('Y') : $year;
        $slot_date = date('Y-m-d', strtotime($day . '-' . $month . '-' . $year));
        $timeSlotPattern = $this->TimeSlotPattern->findByModelAndModelId($model, $model_id);
        $time_slots = $this->TimeSlot->buildDayInfo($timeSlotPattern['TimeSlotPattern']['_id'], $slot_date);

        foreach ($time_slots as &$time_slot) {
            $time_slot['TimeSlot']['start_time'] = $this->TimeSlot->formatDate(
                $time_slot['TimeSlot']['time_start'],
                'H:i A'
            );
            $time_slot['TimeSlot']['finish_time'] = $this->TimeSlot->formatDate(
                $time_slot['TimeSlot']['time_finish'],
                'H:i A'
            );
            if ($time_slot['TimeSlot']['booked']) {
                $booking = $this->Booking->find(
                    'first',
                    [
                        'conditions' => [
                            'type' => 'time_slots',
                            'time_slot_id' => $time_slot['TimeSlot']['_id'],
                            'status' => 'BOOKED',
                        ],
                    ]
                );

                $time_slot['Booking'] = $booking['Booking'];
                $time_slot['TimeSlot']['booking_id'] = $booking['Booking']['_id'];
                $user = $this->User->findById($booking['Booking']['user_id']);
                $time_slot['TimeSlot']['booked_member'] = $user['User'];
                $time_slot['User'] = $user['User'];
            }
        }

        return json_encode(['success' => true, 'day' => $time_slots, 'date' => date('Y-m-d', strtotime($slot_date))], JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    /**
     * @param      $model
     * @param      $model_id
     * @param      $date
     * @param null $user_id
     * @param bool $allow_private
     *
     * @return string
     */
    public function findAllByModelAndModelIdAndDate($model, $model_id, $date, $user_id = null, $allow_private = false)
    {
        //<editor-fold desc="Rushed hotfix added to support Buddhist Calendars requested urgently by the Product Team.">
        // Plan of Action decided by the Product Team: https://glofox.atlassian.net/wiki/spaces/PF/pages/580255749/Get+Classes
        // @TODO: Remove this after legacy apps are deprecated
        if ($date) {
            $date = new \Carbon\Carbon($date);

            /** @var \Glofox\Calendar\CurrentYearConverter $converter */
            $converter = app()->make(\Glofox\Calendar\CurrentYearConverter::class);
            $date = $converter->convert($date)->format('Y-m-d');
        }
        //</editor-fold>

        // Check if Branch Id is empty
        $is_valid_mongoid = $this->Utility->is_valid_mongoid($model_id);
        if (empty($is_valid_mongoid)) {
            return json_encode(['success' => false, 'message' => __('Invalid Model Id')], JSON_PARTIAL_OUTPUT_ON_ERROR);
        }

        // Check if the date is valid
        $is_date_valid = $this->Utility->validate_date_format($date);
        if (!$is_date_valid['success']) {
            return json_encode(['success' => false, 'message' => $is_date_valid['message']], JSON_PARTIAL_OUTPUT_ON_ERROR);
        }

        $is_private = $this->TimeSlotPattern->isPrivate($model, $model_id);
        $allow_private = filter_var($allow_private, FILTER_VALIDATE_BOOLEAN);
        $booked = false;
        $return_model = false;
        $fields = null;
        $time_slots = (!$allow_private && $is_private)
            ? []
            : $this->TimeSlot->findAllByModelAndModelIdAndDateAndBooked(
                $model,
                $model_id,
                $date,
                $booked,
                $fields,
                $return_model
            );
        $price = null;

        // Calculate Price
        if (!empty($time_slots)) {
            $price_and_permission = $this->getPrice($user_id, $time_slots[0]);
        }

        foreach ($time_slots as &$time_slot) {
            $time_slot['time_start'] = $this->TimeSlot->formatDate($time_slot['time_start'], 'time');
            $time_slot['time_finish'] = $this->TimeSlot->formatDate($time_slot['time_finish'], 'time');
            if ($user_id) {
                $time_slot['price'] = $price_and_permission['price'];
                $time_slot['is_allowed'] = $price_and_permission['is_allowed'];
            }
        }

        return json_encode(['success' => true, 'time_slots' => $time_slots], JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    /**
     * Find all the timeslots for a specific model - model_.
     *
     * @param      $branch_id
     * @param      $model
     * @param      $model_id
     * @param bool $allow_private
     *
     * @return string [type]            [description]
     *
     * @internal param $ [type] $branch_id [description]
     * @internal param $ [type] $model     [description]
     * @internal param $ [type] $model_id  [description]
     */
    public function findBookableByBranchIdAndModelAndModelId($branch_id, $model, $model_id, $allow_private = false)
    {
        // Check if Branch Id is empty
        $is_valid_mongoid = $this->Utility->is_valid_mongoid($model_id);
        if (empty($is_valid_mongoid)) {
            return json_encode(['success' => false, 'message' => __('Invalid Model Id')], JSON_PARTIAL_OUTPUT_ON_ERROR);
        }

        $is_private = $this->TimeSlotPattern->isPrivate($model, $model_id);
        $allow_private = filter_var($allow_private, FILTER_VALIDATE_BOOLEAN);
        $booked = false;
        $this->loadModel('Branch');
        $date_format = $this->TimeSlot->get_date_format('date');
        $start_date = $this->TimeSlot->getCurrentDate($date_format);
        $weeks_to_display = $this->Branch->findWeeksToDisplay($branch_id, $model);
        $end_date = $this->Branch->addNumberOfWeeks($start_date, $weeks_to_display, $date_format);

        $time_slots = (!$allow_private && $is_private)
            ? []
            : $this->TimeSlot->findAllByModelAndModelIdAndDateRangeAndBooked(
                $model,
                $model_id,
                $start_date,
                $end_date,
                $booked
            );
        // Calculate Price
        if (!empty($time_slots)) {
            $price_and_permission = $this->getPrice($user_id, $time_slots[0]);
        }

        if ($user_id && !empty($time_slots)) {
            foreach ($time_slots as &$time_slot) {
                $time_slot['TimeSlot']['price'] = $price_and_permission['price'];
                $time_slot['TimeSlot']['is_allowed'] = $price_and_permission['is_allowed'];
            }
        }

        return json_encode(['success' => true, 'time_slots' => $time_slots], JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    /**
     * [findByIdAndUserId description].
     *
     * @param $time_slot_id
     * @param [type] $time_slot_id [description]
     *
     * @return string [type]               [description]
     */
    public function findByIdAndUserId($time_slot_id, $user_id = null)
    {
        // Check if Branch Id is empty
        $is_valid_mongoid = $this->Utility->is_valid_mongoid($time_slot_id);
        if (empty($is_valid_mongoid)) {
            return json_encode(['success' => false, 'message' => __('Invalid Time Slot Id')], JSON_PARTIAL_OUTPUT_ON_ERROR);
        }

        $time_slot = $this->TimeSlot->findById($time_slot_id);
        if (!$time_slot) {
            return json_encode(['success' => false, 'message' => 'There is no Time Slot with the given Id']);
        }

        $branch_id = $time_slot['TimeSlot']['branch_id'];
        $model = $time_slot['TimeSlot']['model'];
        $model_id = $time_slot['TimeSlot']['model_id'];
        $session_date = $time_slot['TimeSlot']['time_start'];

        $entity = $this->TimeSlotPattern->findEntityByBranchIdModelAndModelId($model, $model_id);

        $this->loadModel('UserCredit');
        $credit = $this->UserCredit->findCreditForBooking(
            $branch_id,
            $user_id,
            $session_date,
            $model,
            $model_id,
            $entity['categories']
        );

        // Find Currency
        $this->loadModel('Branch');
        $currency = $this->Branch->getCurrency($branch_id);

        $has_credit = ($credit) ? true : false;
        // Calculate Price
        $price_and_permission = $this->getPrice($user_id, $time_slot);

        $time_slot['TimeSlot']['has_credit'] = $has_credit;
        $time_slot['TimeSlot']['price'] = ($has_credit) ? 0 : $price_and_permission['price'];
        $time_slot['TimeSlot']['currency'] = $currency;
        $time_slot['TimeSlot']['is_allowed'] = ($has_credit) ? true : $price_and_permission['is_allowed'];
        $time_slot['TimeSlot']['time_start'] = $this->TimeSlot->formatDate(
            $time_slot['TimeSlot']['time_start'],
            'time'
        );
        $time_slot['TimeSlot']['time_finish'] = $this->TimeSlot->formatDate(
            $time_slot['TimeSlot']['time_finish'],
            'time'
        );

        return json_encode(['success' => true, 'time_slot' => $time_slot['TimeSlot']], JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    /**
     * @return string
     */
    public function delete()
    {
        $user = $this->JWT->parseToken();

        $allowedRoles = [
            UserType::SUPERADMIN,
            UserType::ADMIN,
            UserType::RECEPTIONIST,
            UserType::TRAINER,
        ];
        $this->JWT->validateOrFail($allowedRoles, $user['namespace'], $user['branch_id']);

        $logger = app()->make(\Psr\Log\LoggerInterface::class);
        $logger->info(
            "[TimeSlotsDelete] User type: '" . ($user['type'] ?? 'None') . "', " .
            "Namespace: '" . ($user['namespace'] ?? 'None') . "'"
        );

        if (!$this->request->is('post')) {
            return json_encode(['success' => false, 'message' => 'Invalid POST Request']);
        }

        $input_data = file_get_contents('php://input');
        $input_data = json_decode($input_data, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        $time_slot_id = $input_data['time_slot_id'] ?? null;
        $namespace = $input_data['namespace'] ?? null;
        $branch_id = $input_data['branch_id'] ?? null;

        // Check if Time Slot Id is empty
        $is_valid_mongoid = $this->Utility->is_valid_mongoid($time_slot_id);
        if (empty($is_valid_mongoid)) {
            return json_encode(['success' => false, 'message' => __('Invalid Time Slot Id')], JSON_PARTIAL_OUTPUT_ON_ERROR);
        }

        $time_slot = $this->TimeSlot->findByIdAndBranchIdAndNamespace($time_slot_id, $branch_id, $namespace);
        if (!$time_slot) {
            return json_encode(['success' => false, 'message' => __('There is no time slot with the given id.')], JSON_PARTIAL_OUTPUT_ON_ERROR);
        }

        // Check if Branch Id is empty
        $is_valid_mongoid = $this->Utility->is_valid_mongoid($branch_id);
        if (empty($is_valid_mongoid)) {
            return json_encode(['success' => false, 'message' => __('Invalid Branch Id')], JSON_PARTIAL_OUTPUT_ON_ERROR);
        }

        if ($time_slot['TimeSlot']['booked']) {
            $time_slot['TimeSlot']['booked'] = false;
            $time_slot['TimeSlot']['active'] = false;
            $save_time_slot = $this->TimeSlot->save();

            if ($save_time_slot) {
                return json_encode(['success' => true, 'message' => 'You have successfully deleted the time slot.']);
            } else {
                return json_encode(['success' => false, 'message' => 'Ocurred a problem deleting the time slot.']);
            }
            // Cancel
        } else {
            $this->TimeSlot->delete($time_slot_id);

            return json_encode(['success' => true, 'message' => 'You have successfully deleted the time slot.']);
        }
    }

    /**
     * @return string
     */
    public function get_available_models()
    {
        return json_encode($this->TimeSlot->get_available_models(), JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    /**
     * [getNotesGivenBranchIdAndTimeSlotId description].
     *
     * @param $branch_id
     * @param $time_slot_id
     *
     * @return string [type]               [description]
     *
     * @internal param $ [type] $branch_id    [description]
     * @internal param $ [type] $time_slot_id [description]
     */
    public function getNotesGivenBranchIdAndTimeSlotId($branch_id, $time_slot_id)
    {
        return json_encode($this->TimeSlot->getNotesGivenBranchIdAndTimeSlotId($branch_id, $time_slot_id), JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    /**
     * [addNote description].
     *
     * @param [type] $branch_id    [description]
     * @param [type] $time_slot_id [description]
     *
     * @return string
     */
    public function addNote($branch_id, $time_slot_id)
    {
        $input_data = json_decode(file_get_contents('php://input'), true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        if (empty($input_data) || !is_array($input_data)) {
            return json_encode(['success' => false, 'message' => 'Invalid Json sent.']);
        }
        if (empty($input_data['message'])) {
            return json_encode(['success' => false, 'message' => 'There is no message in the note.']);
        }
        $message = $input_data['message'];

        if (empty($input_data['user_id'])) {
            return json_encode(['success' => false, 'message' => 'There is no user_id attribute in the JSON.']);
        }
        $user_id = $input_data['user_id'];

        $this->loadModel('User');
        $user = $this->User->findByIdSetFields($user_id, ['first_name', 'last_name', 'type']);

        $addNote = $this->TimeSlot->addNote(
            $time_slot_id,
            $user_id,
            $user['User']['first_name'],
            $user['User']['last_name'],
            $user['User']['type'],
            $message
        );

        return json_encode($addNote['TimeSlot']['notes'], JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    /**
     * [block_time_slot description].
     *
     * @param $namespace
     * @param $branch_id
     * @param $time_slot_id
     *
     * @return string [type]               [description]
     *
     * @internal param $ [type] $namespace    [description]
     * @internal param $ [type] $branch_id    [description]
     * @internal param $ [type] $time_slot_id [description]
     */
    public function block_time_slot($namespace, $branch_id, $time_slot_id)
    {
        $time_slot = $this->TimeSlot->findByIdAndBranchIdAndNamespace($time_slot_id, $branch_id, $namespace);
        if (!$time_slot) {
            return json_encode(
                ['success' => false, 'message' => 'Time Slot not found.', 'message_code' => 'TIME_SLOT_NOT_FOUND']
            );
        }

        if ($time_slot['TimeSlot']['booked']) {
            return json_encode(
                [
                    'success' => false,
                    'message' => 'The Time Slot is already booked.',
                    'message_code' => 'TIME_SLOT_ALREADY_BOOKED'
                ]
            );
        }

        $message = file_get_contents('php://input');
        if (empty($message)) {
            return json_encode(
                ['success' => false, 'message' => 'The message is empty.', 'message_code' => 'MESSAGE_IS_EMPTY']
            );
        }

        return json_encode($this->TimeSlot->blockTimeSlot($time_slot, $message), JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    /**
     * [unblock_time_slot description].
     *
     * @param $namespace
     * @param $branch_id
     * @param $time_slot_id
     *
     * @return string [type]               [description]
     *
     * @internal param $ [type] $namespace    [description]
     * @internal param $ [type] $branch_id    [description]
     * @internal param $ [type] $time_slot_id [description]
     */
    public function unblock_time_slot($namespace, $branch_id, $time_slot_id)
    {
        $time_slot = $this->TimeSlot->findByIdAndBranchIdAndNamespace($time_slot_id, $branch_id, $namespace);
        if (!$time_slot) {
            return json_encode(
                ['success' => false, 'message' => 'Time Slot not found.', 'message_code' => 'TIME_SLOT_NOT_FOUND']
            );
        }

        return json_encode($this->TimeSlot->unblockTimeSlot($time_slot), JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    /************************************************ NEW DASHBOARD METHODS *******************************************/

    /**
     * Get all slots for a given branch /branch/<branch_id>/events/<start>/<end>/<filter> from start date to finish date.
     *
     * @param type $id
     * @param null $start
     * @param null $finish
     *
     * @return string
     */
    public function dateRange($id = null, $start = null, $finish = null)
    {
        $user = $this->JWT->parseToken();

        $isValidMongoid = $this->Utility->is_valid_mongoid($id);
        if (empty($isValidMongoid)) {
            return json_encode([
                'success' => false,
                'message' => __('Invalid Branch Id')
            ], JSON_THROW_ON_ERROR);
        }

        if ('DELETE' === $_SERVER['REQUEST_METHOD']) {
            $conditions = [
                'branch_id' => $id,
                'active' => true,
                'date' => [
                    '$gte' => new MongoDate(strtotime($start)),
                    '$lte' => new MongoDate(strtotime($finish)),
                ],
            ];

            $this->TimeSlot->deleteAll($conditions);
        }

        $params = new FindAllByBranchIdAndDateRangeParams($id, $start, $finish, $user);

        $timeSlotRepository = app()->make(TimeSlotRepository::class);
        $timeSlots = $timeSlotRepository->findAllByBranchIdAndDateRange($params);


        return json_encode($timeSlots, JSON_THROW_ON_ERROR);
    }

    public function hasTimeSlots(HasTimeslotsRequest $request)
    {
        return $this->TimeSlot->branchHasTimeSlots($request->getBranchId());
    }

    /**
     * @param $user_id
     * @param $time_slot
     *
     * @return mixed
     */
    private function getPrice($user_id, $time_slot)
    {
        $this->loadModel('User');
        $time_slot['TimeSlot'] ??= $time_slot;
        $user = $this->User->findById($user_id);
        $model = $time_slot['TimeSlot']['model'];
        $model_id = $time_slot['TimeSlot']['model_id'];

        $time_slot_pattern = $this->TimeSlotPattern->findByModelAndModelId($model, $model_id);

        $allowed_member_types = $time_slot_pattern['TimeSlotPattern']['allowed_member_types'] ?? null;

        $membership_type = $user['User']['membership']['type'] ?? $this->User->get_membership_type('payg');

        $membership_id = $user['User']['membership']['_id'] ?? null;

        $membership_group_id = $user['User']['membership']['membership_group_id'] ?? null;

        $default_price = $time_slot_pattern['TimeSlotPattern']['default_price'] ?? null;

        $price_and_permission = $this->User->get_price_and_permission(
            $allowed_member_types,
            $membership_type,
            $membership_id,
            $membership_group_id,
            $default_price
        );

        if ($price_and_permission['is_allowed']) {
            return $price_and_permission;
        }

        $this->loadModel('UserCredit');
        $credits = $this->UserCredit->findAllAvailableByBranchIdAndUserId(
            $time_slot_pattern['TimeSlotPattern']['branch_id'],
            $user_id
        );
        foreach ($credits as $credit) {
            if (isset($credit['UserCredit']['membership_id'])) {
                $membership_type = 'member';
                $membership_id = $credit['UserCredit']['membership_id'];
                $membership_group_id = null;
                $price_and_permission = $this->User->get_price_and_permission(
                    $time_slot_pattern['TimeSlotPattern']['allowed_member_types'],
                    $membership_type,
                    $membership_id,
                    $membership_group_id,
                    $time_slot_pattern['TimeSlotPattern']['default_price']
                );

                if (!empty($credit['UserCredit']['model_ids']) && !in_array(
                        $time_slot_pattern['TimeSlotPattern']['model_id'],
                        $credit['UserCredit']['model_ids']
                    )) {
                    continue;
                }

                if ($price_and_permission['is_allowed']) {
                    return $price_and_permission;
                }
            }
        }

        return $price_and_permission;
    }
}
