post:
  summary: 'Uploads a new asset'
  operationId: asset-upload
  tags:
    - Assets
  description: 'Uploads a new asset'
  security:
    - coreapi_auth: [ ]
  parameters:
    - name: model
      in: path
      required: true
      schema:
        type: string
    - name: id
      in: path
      required: true
      schema:
        type: string
    - name: type
      in: path
      required: true
      schema:
        type: string

  requestBody:
    required: true
    content:
      multipart/form-data:
        schema:
          type: object
          properties:
            Image:
              type: string
              format: binary
            ImageName:
              type: string
              enum:
                - default
                - room_map
  responses:
    '200':
      description: 'OK'
      content:
        application/json:
          schema:
            type: object
            properties:
              success:
                type: boolean
                default: true
              message:
                type: string
                default: 'Image successfully uploaded'
