<?php

declare(strict_types=1);

class APIExceptionRender extends ExceptionRenderer
{
    /**
     * ExceptionRender implementation
     * Catches all the exceptions and transforms them into an static response schema
     * This customized render is only triggered with the API version is
     */
    public function render(): void
    {
        if ($this->error instanceof MissingControllerException && (env('GLOFOX_DEBUG_LEVEL') === 0)) {
            $this->error = new \Exception("Route not found", $this->error->getCode(), $this->error->getPrevious());
        }

        $status = $this->error->getCode();

        if ($status < 200 || $status > 600) {
            $status = 500;
        }

        if ($status >= 500 && app()->isSentryEnabled()) {
            Sentry\captureException($this->error);
        }

        if ($status >= 500 && env('GLOFOX_DEBUG_LEVEL') > 2) {
            whoops()->handleException($this->error);
            die;
        }

        $errors = $this->getErrors();

        $content = [
            'success' => false,
            'code' => $this->error->getCode(),
            'message' => $this->error->getMessage(),
            'message_data' => $this->error->messageData ?? [],
            'errors' => $errors
        ];

        // API 2.0 or above
        $this->controller->response->type('application/json');
        $this->controller->response->body(json_encode($content, JSON_PARTIAL_OUTPUT_ON_ERROR));

        http_response_code($status);
        $this->controller->response->statusCode($status);
        $this->controller->response->header('HTTP/1.0 ' . $status);

        $this->controller->response->send();
    }

    private function getMessageCode(): string
    {
        if (is_callable([$this->error, 'getMessageCode'])) {
            return $this->error->getMessageCode();
        }

        $message = is_array($this->error->getMessage()) ? $this->error->getMessage()[0] : $this->error->getMessage();
        $message = preg_replace('/[^ \w]+/', '', $message);

        return str_replace(' ', '_', strtoupper($message));
    }

    private function getErrors()
    {
        if (isset($this->error->errors)) {
            return $this->error->errors;
        }

        $messageCode = $this->getMessageCode();

        if ($messageCode) {
            return [$messageCode];
        }

        return [];
    }
}
