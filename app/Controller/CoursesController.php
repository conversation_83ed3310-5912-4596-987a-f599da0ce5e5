<?php

declare(strict_types=1);

use Carbon\Carbon;
use Glofox\CdnProvider;
use Glofox\Datasource\Exceptions\InvalidMongoIdException;
use Glofox\Domain\Authentication\Auth;
use Glofox\Domain\Courses\Requests\DeleteByIdRequest;
use Glofox\Domain\Courses\Requests\UpsertCourseRequest;
use Glofox\Domain\Courses\UseCase\CreateOrUpdateOne;
use Glofox\Domain\Courses\UseCase\CreateOrUpdateOneParams;
use Glofox\Domain\Courses\UseCase\DeleteOne;
use Glofox\Domain\Courses\Validation\ScheduleEmptinessValidator;
use Glofox\Domain\Courses\Validation\UserAuthorization\CreateOrUpdateValidator;
use Glofox\Domain\Courses\Validation\UserAuthorization\DeleteValidator;
use Glofox\Domain\Memberships\Services\Validate\ValidateUserHasActiveNonPAYGMembership;
use Illuminate\Http\JsonResponse;
use Psr\Log\LoggerInterface;

App::uses('Folder', 'Utility');
App::uses('File', 'Utility');
App::uses('AppController', 'Controller');

class CoursesController extends \Glofox\Domain\Courses\Http\CoursesController
{
    /**
     * Name of the feature.
     *
     * @var [type]
     */
    public $feature_name;

    /**
     * Default image name for items of this type of feature.
     *
     * @var [type]
     */
    public $image_default_name;

    public $components = [
        'S3',
        'Paginator',
        'Utility',
        'Notification',
        'JWT',
    ];

    /**
     * name property.
     *
     * @var string 'Programs'
     */
    public $name = 'Courses';
    public $uses = ['Branch', 'Event', 'Course', 'User', 'Facility', 'Booking', 'Membership', 'Booking'];

    /*******************************************************************************************
     *                                API 2.0 New Dashboard
     *****************************************************************************************
     *
     * @param null $identifier
     *
     * @return string
     */

    /** @var ValidateUserHasActiveNonPAYGMembership */
    private $userHasMembershipValidator;

    public function __construct($request, $response)
    {
        parent::__construct($request, $response);
        $this->userHasMembershipValidator = app()->make(ValidateUserHasActiveNonPAYGMembership::class);
    }

    /*!
       * Dispatcher for Model
       * @param  [type] $identifier [description]
       * @return [type]             [description]
       */
    public function dispatcher($identifier = null)
    {
        return parent::dispatch($identifier, $this->Course);
    }

    /**
     * Get courses by specifying all in branch, all in branch or by specific id.
     *
     * @param string $id Course id
     *
     * @return array List of courses that match the entry params
     * @internal   This convention is a little boring for clients, but helps us keep clean backend
     *
     * @example    https://www.glofoxlogin/view/{id}
     *
     */
    public function view($id)
    {
        $user = $this->JWT->parseToken();
        $params = ['branch_id' => $user['branch_id']];
        if (isset($id)) {
            $params['_id'] = $id;
        }

        return json_encode($this->Course->find('all', ['conditions' => array_merge($params, ['active' => true])]), JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    public function upsert(UpsertCourseRequest $req): JsonResponse
    {
        app()->make(CreateOrUpdateValidator::class)
            ->validate(Auth::user(), $req->branchId(), $req->namespace(), $req->id());

        try {
            app()->make(ScheduleEmptinessValidator::class)
                ->validate($req->schedule());
        } catch (UnsuccessfulOperation $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ]);
        }

        $params = new CreateOrUpdateOneParams(
            $req->id(),
            $req->branchId(),
            $req->namespace(),
            $req->name(),
            $req->description(),
            $req->image(),
            $req->isActive(),
            $req->isPrivate(),
            $req->pricing(),
            $req->facility(),
            $req->trainers(),
            $req->taxIds(),
            $req->allowedMemberTypes(),
            $req->schedule()
        );

        $useCase = app()->make(CreateOrUpdateOne::class);
        $result = $useCase->execute($params);

        return response()->json($result);
    }

    /**
     * @param DeleteByIdRequest $req
     * @return JsonResponse
     * @throws InvalidMongoIdException
     * @throws UnsuccessfulOperation
     */
    public function remove(DeleteByIdRequest $req): JsonResponse
    {
        app()->make(DeleteValidator::class)->validate(Auth::user(), $req->courseId());

        $useCase = app()->make(DeleteOne::class);
        $result = $useCase->execute($req->courseId());

        return response()->json($result);
    }

    /*!
     * Search list with params and pagination
     * @param      [type]                   $start  [description]
     * @param      [type]                   $limit  [description]
     * @param      [type]                   $search [description]
     * @return     [type]                           [description]
     */

    /**
     * @param int $page
     * @param int $limit
     * @param null $search
     * @param string $active
     *
     * @return string
     */
    public function listview($page = 1, $limit = 30, $search = null, $active = '1')
    {
        //Force return json
        $this->response->type('json');
        $this->autoRender = false;
        //Obtain data
        $user = $this->JWT->parseToken();
        $active = '0' == $active ? false : true;
        $params = ['branch_id' => $user['branch_id'], 'active' => $active];
        $this->encodeSearchRegex($params, $search, $this->Course); //<- add to params built Regex for global search
        $page = $page > 0 ? (int)$page : 1;
        $limit = $limit > 0 ? (int)$limit : 100;
        $result = $this->Course->find(
            'all',
            [
                'conditions' => $params,
                'limit' => $limit,
                'page' => $page,
                'order' => ['name' => 1],
            ]
        );

        foreach ($result as $row) {
            $startDate = Carbon::parse(
                $row['Course']['start_date'] ?? null
            );
            $row['Course']['start_date'] = $startDate->format('Y-m-d 00:00:00');

            $endDate = Carbon::parse(
                $row['Course']['end_date'] ?? null
            );
            $row['Course']['end_date'] = $endDate->format('Y-m-d 00:00:00');
        }

        return response()->json($result);
    }

    /*!
     * Count total records in this collection
     * @return     [type]                   [description]
     */

    /**
     * @param string $active
     *
     * @return string
     */
    public function count($active = '1')
    {
        $user = $this->JWT->parseToken();
        $active = '0' == $active ? false : true;
        $params = ['branch_id' => $user['branch_id'], 'active' => $active];
        $result = $this->Course->find('count', ['conditions' => $params]);

        return json_encode($result, JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    /**
     * Configure this controller instance with the information by convention about this model
     * including the default image name and model name.
     */
    public function beforeFilter()
    {
        parent::beforeFilter();
        $this->feature_name = 'courses';
        $this->image_default_name = 'default.png';
    }

    /*******************************************************************************************
     *                                     REST WEBSERVICE METHODS
     ******************************************************************************************/

    /**
     * Display the products view.
     *
     * @deprecated
     */
    public function index()
    {
        $this->layout = 'dashboard';
        $this->render('index');
    }

    /**
     * Find all the courses assossiated to a branch.
     *
     * @return string [type] [description]
     * @deprecated
     *
     */
    public function findAll()
    {
        $this->response->type('json');
        $this->autoRender = false;

        //Get current user session info to obtain the namespace and branch identifier
        $user = $this->JWT->getUser();
        $allowed_roles = ['admin', 'superadmin'];
        $namespace = $user['User']['namespace'];
        $branch_id = $user['User']['branch_id'];
        $this->JWT->validateOrFail($allowed_roles, $user['User']['namespace']);

        //Get Course list for this branch id
        $courses = $this->Course->findAllByBranchId($branch_id);

        return json_encode($courses, JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    /**
     * List courses by branch.
     *
     * @param null $branch_id
     * @param null $user_id
     * @param bool $fetch_images
     *
     * @return string [type]            [description]
     *
     * @deprecated
     *
     * @internal param $ [type] $branch_id [description]
     */
    public function findByBranchId($branch_id = null, $user_id = null, $fetch_images = false)
    {
        $this->response->type('json');
        $this->autoRender = false;

        $branch_is_valid_mongoid = $this->Utility->is_valid_mongoid($branch_id);

        if (empty($branch_is_valid_mongoid)) {
            return json_encode(['success' => false, 'message' => __('Invalid Branch Id')], JSON_PARTIAL_OUTPUT_ON_ERROR);
        }

        $user_is_valid_mongoid = $this->Utility->is_valid_mongoid($user_id);
        $user_id = empty($user_is_valid_mongoid) ? null : $user_id;

        $results = $this->Course->findAllByBranchId($branch_id);
        $member = ($user_id) ? $this->User->findById($user_id) : null;
        $membership = $member ? $this->validateUserMembership($member) : null;

        $fetch_images = filter_var($fetch_images, FILTER_VALIDATE_BOOLEAN);

        if ($user_id || $fetch_images) {
            foreach ($results as &$course) {
                // Fetch Price
                if ($user_id) {
                    $course['Course']['default_price'] = $this->Course->priceFor($membership, $course);
                }

                // Fetch Images
                if ($fetch_images) {
                    $namespace = $course['Course']['namespace'];
                    $branch_id = $course['Course']['branch_id'];
                    $feature = 'courses';

                    if ($this->S3->crudImageExists($namespace, $branch_id, $feature, $course['Course']['_id'])) {
                        $course['Course']['image_url'] = $this->S3->getCrudImageUrl(
                            $namespace,
                            $branch_id,
                            $feature,
                            $course['Course']['_id']
                        );
                    }
                }
            }
        }

        return json_encode($results, JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    /**
     * Find one by course id /course/<course_id>.
     *
     * @param null $course_id
     *
     * @return type
     *
     * @deprecated
     *
     * @internal param type $id_course
     */
    public function findByCourseId($course_id = null)
    {
        $this->response->type('json');
        $this->autoRender = false;

        //Find course
        $course = $this->Course->findById($course_id);
        //Attach specific price for this user to the course to return "Payg" or "Default" price
        $course['Course']['default_price'] = $this->Course->priceFor(null, $course);

        //Return
        $response = !empty($course) ? ['success' => true, 'Course' => $course['Course']] :
            ['success' => false, 'message' => 'Invalid course'];

        return json_encode($response, JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    /**
     * Find a specific course for a specific user. This includes the particular price for this user.
     *
     * @param null $course_id
     * @param null $user_id
     *
     * @return string [type]            [description]
     *
     * @deprecated
     *
     * @internal param $ [type] $branch_id [description]
     * @internal param $ [type] $course_id [description]
     * @internal param $ [type] $user_id   [description]
     */
    public function findByCourseIdAndUserId($course_id = null, $user_id = null)
    {
        $this->response->type('json');
        $this->autoRender = false;

        //Find course
        $course = $this->Course->findById($course_id);
        $user = $this->User->findById($user_id);

        $membership = $this->validateUserMembership($user);

        //Attach specific price for this user to the course to return
        $course['Course']['default_price'] = $this->Course->priceFor($membership, $course);

        //Return
        $response = !empty($course) ? ['success' => true, 'Course' => $course['Course']] :
            ['success' => false, 'message' => 'Invalid course'];

        return json_encode($response, JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    /**
     * [getCrudImageUrlOrPlaceholder description].
     *
     * @param $namespace
     * @param $branch_id
     * @param $id
     * @param int $width [description]
     * @param int $height [description]
     *
     * @return mixed [type] [description]
     * @deprecated
     */
    public function getCrudImageUrlOrPlaceholder($namespace, $branch_id, $id, $width = 640, $height = 300)
    {
        return $this->S3->getCrudImageUrlOrPlaceholder(
            $namespace,
            $branch_id,
            $this->feature_name,
            $id,
            'default.png',
            $width,
            $height
        );
    }

    /**
     * Returns the price of the course for an specific User.
     *
     * @param $course_id
     * @param $member_id
     *
     * @return string [type]            [description]
     *
     * @deprecated
     *
     * @internal param $ [type] $branch_id [description]
     * @internal param $ [type] $member_id [description]
     * @internal param $ [type] $course_id [description]
     */
    public function getPrice($course_id, $member_id)
    {
        $this->response->type('json');
        $this->autoRender = false;

        $course = $this->Course->findById($course_id);
        if (!$course) {
            return json_encode(['success' => false, 'message' => 'Invalid Course Id.']);
        }

        $this->loadModel('User');
        $user_fields = ['membership'];
        $member = $this->User->findByIdSetFields($member_id, $user_fields);
        if (!$member) {
            return json_encode(['success' => false, 'message' => 'Invalid Member Id.']);
        }

        $membership = $this->validateUserMembership($member);

        $price = $this->Course->priceFor($membership, $course);

        return json_encode(['success' => true, 'price' => $price], JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    /**
     * Download csv of class.
     *
     * @param $id
     *
     * @return CakeResponse|null [type] [description]
     * @deprecated
     * had to do fast for client so code shoul be cleaned up
     *
     */
    public function download_csv($id)
    {
        $user = $this->JWT->getUser();
        $allowed_roles = ['admin', 'superadmin'];
        $namespace = $user['User']['namespace'];
        $branch_id = $user['User']['branch_id'];
        $this->JWT->validateOrFail($allowed_roles, $user['User']['namespace']);
        $lines = 'First Name, Last Name, Email, Phone' . PHP_EOL;
        $bookings = $this->Booking->find('all', [
            'conditions' => [
                'session_id' => $id,
                'status' => 'BOOKED',
            ],
        ]);
        foreach ($bookings as $key => $booking) {
            $user = $this->User->find('first', [
                'conditions' => [
                    '_id' => $booking['Booking']['user_id'],
                ],
            ]);
            $lines = $lines . $user['User']['first_name'] . ',' . $user['User']['last_name'] . ',' . $user['User']['email'] . ',' . $user['User']['phone'] . PHP_EOL;
        }
        $dir = APP . 'webroot/files/';

        $file = new File($dir . '/bookings_' . $id . '.csv', true, 0644);
        $this->log($file);
        // $contents = $file->read();
        $file->write($lines);
        // $path = $this->YourModel->aMagicFunctionThatReturnsThePathToYourFile($id);
        $this->response->file($file->path, [
            'download' => true,
            'name' => 'course_list.csv',
        ]);

        return $this->response;
    }

    /**
     * Find one by course id and branch id /branch/<branch_id>/course/<course_id>.
     *
     * @param null $course_id
     *
     * @return type
     *
     * @deprecated
     *
     * @internal param type $id
     * @internal param type $id_course
     */
    public function find($course_id = null)
    {
        $this->response->type('json');
        $this->autoRender = false;

        //Find course
        $course = $this->Course->find('first', ['conditions' => ['_id' => $course_id]]);

        return json_encode($course, JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    /**
     * @return string
     *
     * @throws Exception
     */
    public function uploads(
        Glofox\Request $request
    ) {
        $logger = app()->make(LoggerInterface::class);
        $logger->info('Log to track the use of this uploads method from CoursesController');

        $user = Auth::user();
        $fileKey = 'coursesImage';
        $feature = 'courses';

        if (!$request->hasFile($fileKey)) {
            throw new UnsuccessfulOperation('THERE_IS_NO_FILE_TO_UPLOAD');
        }

        $saveImage = $this->S3->saveCrudImage(
            $_FILES[$fileKey],
            $user->namespace(),
            $user->currentBranchId(),
            $feature
        );

        if (!$saveImage['success']) {
            throw new UnsuccessfulOperation($saveImage['message']);
        }

        $fileUrl = $this->S3->getCrudImageUrl($user->namespace(), $user->currentBranchId(), $feature);
        $cdnUrl = str_replace('https://glofox.s3.amazonaws.com/', \sprintf('%s/', CdnProvider::getUrl()), $fileUrl);

        return response()->json([
            'success' => true,
            'url' => $cdnUrl,
        ]);
    }

    private function setDaysToCorrectSize(array $course): array
    {
        foreach ($course['schedule'] as &$schedule) {
            $correctSizeVariable = $schedule['size'] = !$schedule['size'] ? 100 : $schedule['size'];
            foreach ($schedule['days'] as &$day) {
                $day['size'] = $correctSizeVariable;
            }
        }

        return $course;
    }

    private function validateUserMembership(array $user): array
    {
        $membership = $user['User']['membership'] ?? null;
        $has_an_active_membership = empty($membership) ? false : $this->userHasMembershipValidator->execute($user);
        if (!$has_an_active_membership) {
            $membership = ['type' => 'payg'];
        }
        return $membership;
    }
}
