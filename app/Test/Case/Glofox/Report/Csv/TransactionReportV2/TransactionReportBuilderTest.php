<?php

namespace Glofox\Report\Csv\TransactionReportV2;

use Carbon\Carbon;
use Glofox\Domain\Bookings\Models\BookableEntityType;
use Glofox\Domain\Branches\Models\Branch;
use Glofox\Domain\Dictionaries\Models\Dictionary;
use Glofox\Domain\Dictionaries\Repositories\DictionariesRepository;
use Glofox\Domain\Memberships\Models\Addon;
use Glofox\Domain\Memberships\Repositories\MembershipsRepository;
use Glofox\Domain\Memberships\Services\AddonServiceInterface;
use User as UserCakeModel;

\App::import('Test/Case', 'GlofoxTestCase');

class TransactionReportBuilderTest extends \GlofoxTestCase
{

    public function test_it_should_build_transaction_report()
    {
        $membershipsRepository = \Mockery::mock(MembershipsRepository::class)
            ->shouldReceive('addCriteria')
            ->andReturnSelf()
            ->getMock();
        $membershipsRepository
            ->shouldReceive('first')
            ->andReturn([]);
        $userCakeModel = \Mockery::mock(UserCakeModel::class)
            ->shouldReceive('find')
            ->andReturn([[
                'User' => [
                    '_id' => 'uid',
                    'name' => 'uname',
                    'email' => '<EMAIL>',
                ]
            ]])->getMock();
        $dictionariesRepository = \Mockery::mock(DictionariesRepository::class)
            ->shouldReceive('findByBranchId')
            ->andReturn(new Dictionary())
            ->getMock();

        $transactionReportBuilder = new TransactionReportBuilder($membershipsRepository, $userCakeModel, $dictionariesRepository, new TransactionReportConfig());
        $branch = new Branch([
            '_id' => '123',
            'configuration'=>[]
        ]);

        // Check card gross_amount from payout is formatted
        $transaction = [
            'StripeCharge' => [
                '_id' => '507f191e810c19729de860ef',
                'created' => '2020-05-12 12:33:34',
                'sold_by_user_id' => 'suid',
                'paid' => true,
                'amount' => 50,
                'description' => 'description of the charge',
                'metadata' => [
                    'user_id' => 'uid',
                    'plan_code' => 'pcode',
                    'membership_id' => '507f191e810c19729de860ea',
                    'user_name' => 'uname',
                    'glofox_event' => 'charge',
                    'payment_method' => 'card',
                ],
                'payout' => [
                    'id' => 'payout-1',
                    'gross_amount' => 50.00,
                    'fee' => 0,
                    'net_amount' => 50.00,
                    'date' => '2019-10-26',
                ]
            ]
        ];
        $priceBreakdownReport = \Mockery::mock(PriceBreakdownReport::class)
            ->shouldReceive('hasTaxes')
            ->andReturn(false)
            ->getMock()
            ->shouldReceive('hasDiscounts')
            ->andReturn(false)
            ->getMock();

        $priceBreakdownReport->shouldReceive('getTaxHeaders')->andReturn([]);
        $priceBreakdownReport->shouldReceive('getTaxValues')->andReturn([]);
        $priceBreakdownReport->shouldReceive('getDiscountHeaders')->andReturn([]);
        $priceBreakdownReport->shouldReceive('getDiscountValues')->andReturn([]);

        $transactionReport = $transactionReportBuilder->build($branch, [$transaction], $priceBreakdownReport, false);

        $this->assertEquals(1, sizeof($transactionReport));
        $this->assertEquals(['12/05/2020',
            '12:33',
            'uname',
            '<EMAIL>',
            '',
            'description of the charge',
            '',
            'CARD',
            50.0,
            0,
            0,
            50.0,
            'PAID',
            '26/10/2019',
            'payout-1',
            ''],
            $transactionReport[0]);

        // Check direct debit gross_amount from payout is formatted
        $transaction = [
            'StripeCharge' => [
                '_id' => '507f191e810c19729de860ef',
                'created' => '2020-05-12 12:33:34',
                'sold_by_user_id' => 'suid',
                'paid' => true,
                'amount' => 15.00,
                'description' => 'description of the charge',
                'metadata' => [
                    'user_id' => 'uid',
                    'plan_code' => 'pcode',
                    'membership_id' => '507f191e810c19729de860ea',
                    'user_name' => 'uname',
                    'glofox_event' => 'charge',
                    'payment_method' => 'direct_debit',
                ],
                'payout' => [
                    'id' => '5fa0aa0be3b9423bb3021853',
                    'gross_amount' => 15.00
                ]
            ]
        ];

        $transactionReport = $transactionReportBuilder->build($branch, [$transaction], $priceBreakdownReport, false);

        $this->assertEquals(1, sizeof($transactionReport));
        $this->assertEquals(['12/05/2020',
            '12:33',
            'uname',
            '<EMAIL>',
            '',
            'description of the charge',
            '',
            'DIRECT_DEBIT',
            15.0,
            0,
            '',
            '',
            'PAID',
            'Pending',
            '',
            ''],
            $transactionReport[0]);


        // Check direct debit without payout uses amount
        $transaction = [
            'StripeCharge' => [
                '_id' => '507f191e810c19729de860ef',
                'created' => '2020-05-12 12:33:34',
                'sold_by_user_id' => 'suid',
                'paid' => true,
                'amount' => 15.00,
                'description' => 'description of the charge',
                'metadata' => [
                    'user_id' => 'uid',
                    'plan_code' => 'pcode',
                    'membership_id' => '507f191e810c19729de860ea',
                    'user_name' => 'uname',
                    'glofox_event' => 'charge',
                    'payment_method' => 'direct_debit',
                ]
            ]
        ];

        $transactionReport = $transactionReportBuilder->build($branch, [$transaction], $priceBreakdownReport, false);

        $this->assertEquals(1, sizeof($transactionReport));
        $this->assertEquals(['12/05/2020',
            '12:33',
            'uname',
            '<EMAIL>',
            '',
            'description of the charge',
            '',
            'DIRECT_DEBIT',
            15.0,
            0,
            '',
            '',
            'PAID',
            'Pending',
            '',
            ''],
            $transactionReport[0]);
    }

    public function test_it_should_build_transaction_report_with_user_tax_id_enabled_but_no_tax_id()
    {
        $membershipsRepository = \Mockery::mock(MembershipsRepository::class)
            ->shouldReceive('addCriteria')
            ->andReturnSelf()
            ->getMock();
        $membershipsRepository
            ->shouldReceive('first')
            ->andReturn([]);
        $userCakeModel = \Mockery::mock(UserCakeModel::class)
            ->shouldReceive('find')
            ->andReturn([[
                'User' => [
                    '_id' => 'uid',
                    'name' => 'uname',
                    'email' => '<EMAIL>',
                ]
            ]])->getMock();
        $dictionariesRepository = \Mockery::mock(DictionariesRepository::class)
            ->shouldReceive('findByBranchId')
            ->andReturn(new Dictionary())
            ->getMock();

        $transactionReportBuilder = new TransactionReportBuilder($membershipsRepository, $userCakeModel, $dictionariesRepository, new TransactionReportConfig());
        $branch = new Branch([
            '_id' => '123',
            'configuration' => [
                'fiscal' => [
                    'member_tax_id_options' => [
                        'enabled' => true
                    ]
                ]
            ]
        ]);

        // Check card gross_amount from payout is formatted
        $transaction = [
            'StripeCharge' => [
                '_id' => '507f191e810c19729de860ef',
                'created' => '2020-05-12 12:33:34',
                'sold_by_user_id' => 'suid',
                'paid' => true,
                'amount' => 50,
                'description' => 'description of the charge',
                'metadata' => [
                    'user_id' => 'uid',
                    'plan_code' => 'pcode',
                    'membership_id' => '507f191e810c19729de860ea',
                    'user_name' => 'uname',
                    'glofox_event' => 'charge',
                    'payment_method' => 'card',
                ],
                'payout' => [
                    'id' => 'payout-1',
                    'gross_amount' => 50.00,
                    'fee' => 0,
                    'net_amount' => 50.00,
                    'date' => '2019-10-26',
                ]
            ]
        ];
        $priceBreakdownReport = \Mockery::mock(PriceBreakdownReport::class)
            ->shouldReceive('hasTaxes')
            ->andReturn(false)
            ->getMock()
            ->shouldReceive('hasDiscounts')
            ->andReturn(false)
            ->getMock();

        $priceBreakdownReport->shouldReceive('getTaxHeaders')->andReturn([]);
        $priceBreakdownReport->shouldReceive('getTaxValues')->andReturn([]);
        $priceBreakdownReport->shouldReceive('getDiscountHeaders')->andReturn([]);
        $priceBreakdownReport->shouldReceive('getDiscountValues')->andReturn([]);

        $transactionReport = $transactionReportBuilder->build($branch, [$transaction], $priceBreakdownReport, false);

        $this->assertEquals(1, sizeof($transactionReport));
        $this->assertEquals(['12/05/2020',
            '12:33',
            'uname',
            '<EMAIL>',
            '',
            '',
            'description of the charge',
            '',
            'CARD',
            50.0,
            0,
            0,
            50.0,
            'PAID',
            '26/10/2019',
            'payout-1',
            ''],
            $transactionReport[0]);

    }
    public function test_it_should_build_transaction_report_with_user_tax_id()
    {
        $membershipsRepository = \Mockery::mock(MembershipsRepository::class)
            ->shouldReceive('addCriteria')
            ->andReturnSelf()
            ->getMock();
        $membershipsRepository
            ->shouldReceive('first')
            ->andReturn([]);
        $userCakeModel = \Mockery::mock(UserCakeModel::class)
            ->shouldReceive('find')
            ->andReturn([[
                'User' => [
                    '_id' => 'uid',
                    'name' => 'uname',
                    'email' => '<EMAIL>',
                ]
            ]])->getMock();
        $dictionariesRepository = \Mockery::mock(DictionariesRepository::class)
            ->shouldReceive('findByBranchId')
            ->andReturn(new Dictionary())
            ->getMock();

        $transactionReportBuilder = new TransactionReportBuilder($membershipsRepository, $userCakeModel, $dictionariesRepository, new TransactionReportConfig());
        $branch = new Branch([
            '_id' => '123',
            'configuration' => [
                'fiscal' => [
                    'member_tax_id_options' => [
                        'enabled' => true
                    ]
                ]
            ]

        ]);

        // Check card gross_amount from payout is formatted
        $transaction = [
            'StripeCharge' => [
                '_id' => '507f191e810c19729de860ef',
                'created' => '2020-05-12 12:33:34',
                'sold_by_user_id' => 'suid',
                'paid' => true,
                'amount' => 50,
                'description' => 'description of the charge',
                'metadata' => [
                    'user_id' => 'uid',
                    'plan_code' => 'pcode',
                    'membership_id' => '507f191e810c19729de860ea',
                    'user_name' => 'uname',
                    'glofox_event' => 'charge',
                    'payment_method' => 'card',
                    'user_tax_id' => 'tax-id123'
                ],
                'payout' => [
                    'id' => 'payout-1',
                    'gross_amount' => 50.00,
                    'fee' => 0,
                    'net_amount' => 50.00,
                    'date' => '2019-10-26',
                ]
            ]
        ];
        $priceBreakdownReport = \Mockery::mock(PriceBreakdownReport::class)
            ->shouldReceive('hasTaxes')
            ->andReturn(false)
            ->getMock()
            ->shouldReceive('hasDiscounts')
            ->andReturn(false)
            ->getMock();

        $priceBreakdownReport->shouldReceive('getTaxHeaders')->andReturn([]);
        $priceBreakdownReport->shouldReceive('getTaxValues')->andReturn([]);
        $priceBreakdownReport->shouldReceive('getDiscountHeaders')->andReturn([]);
        $priceBreakdownReport->shouldReceive('getDiscountValues')->andReturn([]);

        $transactionReport = $transactionReportBuilder->build($branch, [$transaction], $priceBreakdownReport, false);

        $this->assertEquals(1, sizeof($transactionReport));
        $this->assertEquals(['12/05/2020',
            '12:33',
            'uname',
            '<EMAIL>',
            'tax-id123',
            '',
            'description of the charge',
            '',
            'CARD',
            50.0,
            0,
            0,
            50.0,
            'PAID',
            '26/10/2019',
            'payout-1',
            ''],
            $transactionReport[0]);

    }

    public function test_it_should_build_transaction_report_with_addon_info()
    {
        $membershipsRepository = \Mockery::mock(MembershipsRepository::class)
            ->shouldReceive('addCriteria')
            ->andReturnSelf()
            ->getMock();
        $membershipsRepository
            ->shouldReceive('first')
            ->andReturn([]);
        $userCakeModel = \Mockery::mock(UserCakeModel::class)
            ->shouldReceive('find')
            ->andReturn([[
                'User' => [
                    '_id' => 'uid',
                    'name' => 'uname',
                    'email' => '<EMAIL>',
                ]
            ]])->getMock();
        $dictionariesRepository = \Mockery::mock(DictionariesRepository::class)
            ->shouldReceive('findByBranchId')
            ->andReturn(new Dictionary())
            ->getMock();

            $addonServiceInterface = \Mockery::mock(AddonServiceInterface::class)
            ->shouldReceive('getAddon')
            ->andReturn(new Addon(
                'addonid',
                'addondefinitionid',
                'addonplandefinitionid',
                'Tanning Addon',
                'Weekly Tanning',
                BookableEntityType::FACILITY,
                Carbon::yesterday()->getTimestamp(),
                false
            ))
            ->getMock();

            app()->instance(AddonServiceInterface::class, $addonServiceInterface);

        $transactionReportBuilder = new TransactionReportBuilder($membershipsRepository, $userCakeModel, $dictionariesRepository, new TransactionReportConfig());
        $branch = new Branch([
            '_id' => '123',
            'configuration' => [
                'fiscal' => [
                    'member_tax_id_options' => [
                        'enabled' => true
                    ]
                ]
            ]

        ]);

        // Check card gross_amount from payout is formatted
        $transaction = [
            'StripeCharge' => [
                '_id' => '507f191e810c19729de860ef',
                'created' => '2020-05-12 12:33:34',
                'sold_by_user_id' => 'suid',
                'paid' => true,
                'amount' => 50,
                'description' => 'Tanning Addon',
                'metadata' => [
                    'user_id' => 'uid',
                    'services' => [
                        [
                            'service_id' => 'addonid',
                            'service_definition_id' => 'addondefinitionid',
                            'service_definition_plan_id' => 'addonplandefinitionid'
                        ]
                    ],
                    'user_name' => 'uname',
                    'branch_id' => 'branchid',
                    'glofox_event' => 'service_prepaid_payment',
                    'payment_method' => 'card',
                    'user_tax_id' => 'tax-id123'
                ],
                'payout' => [
                    'id' => 'payout-1',
                    'gross_amount' => 50.00,
                    'fee' => 0,
                    'net_amount' => 50.00,
                    'date' => '2019-10-26',
                ]
            ]
        ];
        $priceBreakdownReport = \Mockery::mock(PriceBreakdownReport::class)
            ->shouldReceive('hasTaxes')
            ->andReturn(false)
            ->getMock()
            ->shouldReceive('hasDiscounts')
            ->andReturn(false)
            ->getMock();

        $priceBreakdownReport->shouldReceive('getTaxHeaders')->andReturn([]);
        $priceBreakdownReport->shouldReceive('getTaxValues')->andReturn([]);
        $priceBreakdownReport->shouldReceive('getDiscountHeaders')->andReturn([]);
        $priceBreakdownReport->shouldReceive('getDiscountValues')->andReturn([]);
        
        $transactionReport = $transactionReportBuilder->build($branch, [$transaction], $priceBreakdownReport, false);

        $this->assertEquals(1, sizeof($transactionReport));
        $this->assertEquals(['12/05/2020',
            '12:33',
            'uname',
            '<EMAIL>',
            'tax-id123',
            '',
            'Tanning Addon',
            'Weekly Tanning',
            'CARD',
            50.0,
            0,
            0,
            50.0,
            'PAID',
            '26/10/2019',
            'payout-1',
            ''],
            $transactionReport[0]);

            app()->forgetInstance(AddonServiceInterface::class);
    }

    public function tearDown()
    {
        parent::tearDown();

        \Mockery::close();
    }
}
