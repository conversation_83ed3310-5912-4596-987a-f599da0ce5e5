<?php

declare(strict_types=1);

namespace CakeTestCases\Controller;

use CakeTestCases\Glofox\Domain\Authentication\Token\TokenGeneration;
use CakeTestCases\Glofox\Domain\Users\Traits\FetchUsersTrait;
use Glofox\Http\Proxy\ProxyDispatcher;
use Glofox\Request;
use GuzzleHttp\Psr7\Response;

\App::import('Test/Case', 'GlofoxControllerTestCase');

final class ProxyControllerTest extends \GlofoxControllerTestCase
{
    use FetchUsersTrait;
    use TokenGeneration;

    public function testHandle(): void
    {
        app()->forgetInstance(Request::class);

        $this->loginAsUser(
            $this->fetchUser('59a7011a05c677bda512212a')
        );

        $dispatcher = \Mockery::mock(ProxyDispatcher::class);
        $dispatcher->shouldReceive('dispatch')
            ->withArgs(
                function (string $service, string $method, string $uri) {
                    $this->assertSame('serv1', $service);
                    $this->assertSame('GET', $method);
                    $this->assertSame('v1/hello', $uri);

                    return true;
                }
            )
            ->andReturn(
                new Response(201, [
                    'x-response-success' => 'hooray',
                    'content-type' => 'application/json',
                ], '{"success":true}')
            )
            ->once();

        app()->instance(ProxyDispatcher::class, $dispatcher);

        $this->testAction('/2.2/proxy/serv1/v1/hello', [
            'method' => 'GET',
        ]);

        $this->assertInstanceOf(\CakeResponse::class, $this->response);
        $this->assertSame('{"success":true}', $this->response->body());
        $this->assertSame(201, $this->response->statusCode());

        $headers = $this->response->header();

        $this->assertSame('hooray', $headers['x-response-success'][0]);
        $this->assertSame('application/json', $this->response->type());

        app()->forgetInstance(ProxyDispatcher::class);
    }
}
