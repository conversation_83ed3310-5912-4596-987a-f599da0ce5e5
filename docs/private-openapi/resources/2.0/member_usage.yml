get:
  tags:
    - Experimental
  operationId: member-usage
  description: The end point calculates the number of booking and accesses a member made in a time range.
    Bbookings are filtered by status; booked, attended; true and type; events. The accesses are filtered by status; Granted
  x-internal: true
  security:
    - coreapi_auth: [ ]
  parameters:
    - schema:
        type: string
      name: branchId
      in: path
      required: true
      description: The query will limit results to bookings and access made in this location
    - schema:
        type: string
      in: query
      name: time_start
      description: Unix time stamp. The query will calculate bookings and accesses greater than this timestamp
    - schema:
        type: string
      in: query
      name: time_finish
      description: Unix time stamp. The query will calculate bookings and accesses lower than this timestamp
  responses:
    '200':
      description: OK
      content:
        application/json:
          schema:
            type: object
            properties:
              user:
                $ref: '../../schemas/analytics.yml'