@startuml
start

:time_slot_add_web(timeSlotID, userID, ?paymentMethod, ?price, ?batchId);
if (isStaff) then (not)
  stop
else (yes)
  if (memberIsActive) then (not)
    stop
  else (yes)
    #green:return process_time_slot_booking(
      timeslotID,
      member = fromRequest,
      autoGenerate = false,
      payGym = ?fromRequest,
      isWebAdmin = true,
      paymentMethod = ?fromRequest,
      price = ?fromRequest,
      offSession = true,
      batchID = ?fromRequest
    );
  endif
endif

stop
@enduml
