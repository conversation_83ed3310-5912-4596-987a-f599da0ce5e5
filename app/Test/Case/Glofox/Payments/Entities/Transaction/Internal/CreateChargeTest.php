<?php

namespace CakeTestCases\Glofox\Payments\Entities\Transaction\Internal;

use Glofox\Payments\Entities\Transaction\Internal\CreateCharge;
use Glofox\Payments\Entities\Transaction\Models\Transaction;

\App::import('Test/Case', 'GlofoxTestCase');

final class CreateChargeTest extends \GlofoxTestCase
{
    public function test_it_copies_values(): void
    {
        $transaction = new Transaction();
        $transaction->setTransactionUUID('trans-id');
        $transaction->setAmount(1000);
        $transaction->setFee(0);
        $transaction->setCurrency('EUR');
        $transaction->setOffSession(true);
        $transaction->setUseAvailableAccountBalance(true);

        $createCharge = new CreateCharge();
        $createCharge->setTransaction($transaction);

        $this->assertSame('trans-id', $createCharge->transactionUUID());
        $this->assertTrue($createCharge->useAvailableAccountBalance());
    }

    public function test_it_uses_defaults(): void
    {
        $transaction = new Transaction();
        $transaction->setAmount(1000);
        $transaction->setFee(0);
        $transaction->setCurrency('EUR');
        $transaction->setOffSession(true);
        $createCharge = new CreateCharge();
        $createCharge->setTransaction($transaction);

        $this->assertSame('', $createCharge->transactionUUID());
        $this->assertFalse($createCharge->useAvailableAccountBalance());
    }

}
