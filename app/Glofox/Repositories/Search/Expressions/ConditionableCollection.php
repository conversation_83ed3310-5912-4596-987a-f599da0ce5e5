<?php

namespace Glofox\Repositories\Search\Expressions;

use Glofox\Repositories\Filters\ConditionsCollection;
use Glofox\Repositories\Repository;
use Glofox\Traversable\StrictTypeCollection;
use Illuminate\Support\Collection;

class ConditionableCollection extends StrictTypeCollection implements Conditionable
{
    /**
     * @var string
     */
    protected $operator = '$and';

    /**
     * @return array
     */
    protected function allowedType()
    {
        return [
            ConditionableCollection::class,
            Conditionable::class
        ];
    }

    /**
     * @param string $operator
     *
     * @return ConditionableCollection
     */
    public function withOperator(string $operator) : ConditionableCollection
    {
        $this->operator = $operator;

        return $this;
    }

    /**
     * @return string
     */
    public function operator() : string
    {
        return $this->operator;
    }

    /**
     * @return string
     */
    public function field() : string
    {
        throw new \RunException('The ConditionableCollection field is not available');
    }

    /**
     * @param $value
     */
    public function evaluate($value)
    {
        throw new \RunException('The ConditionableCollection evaluation is not available');
    }

    public function generateConditions(): Collection
    {
        $generatedConditions = ConditionsCollection::make();

        /** @var Conditionable $condition */
        foreach ($this->items as $condition) {
            $condition->applyToConditions($generatedConditions);
        }

        return $generatedConditions;
    }

    /**
     * @param ConditionsCollection $conditions
     */
    public function applyToConditions(ConditionsCollection $conditions, Repository $repository = null)
    {
        $generatedConditions = $this->generateConditions();

        $conditions->push([
            $this->operator() => $generatedConditions->toArray()
        ]);
    }
}
