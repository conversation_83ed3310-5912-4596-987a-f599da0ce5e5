<?php

use MabeEnum\Enum;

class GlofoxEnvironment extends Enum
{
    public const DEVELOPMENT  = 'development';
    public const STAGING 	  = 'staging';
    public const LOCAL 	      = 'local';
    public const PLATFORM     = 'platform';
    public const ALPHA        = 'alpha';
    public const HAMILTON     = 'hamilton';
    public const HOPPER       = 'hopper';
    public const LOVELACE     = 'lovelace';
    public const BARTIK       = 'bartik';
    public const LAMARR       = 'lamarr';
    public const CLARKE       = 'clarke';
    public const TURING       = 'turing';
    public const FRANKLIN     = 'franklin';

    public static function getEnvUrl($env): string
    {
        return $env === self::PLATFORM ? 'https://app.glofox.com' : 'https://'.$env.'.glofox.com';
    }

    public function livemode(): bool
    {
        return env('OPENSHIFT_APP_NAME') === self::PLATFORM;
    }

    /**
     * @return GlofoxEnvironment::*
     */
    public static function currentEnvironment(): string
    {
        return $_ENV['OPENSHIFT_APP_NAME'] ?? self::LOCAL;
    }
}
