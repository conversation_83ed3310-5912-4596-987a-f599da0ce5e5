<?php

declare(strict_types=1);

use Glofox\MessageLibs\Consumer\Fulfilment\FulfilmentCoreapiConsumer;

class ApiWorkerFulfilmentShell extends Shell
{
    public function main()
    {
        $count = 0;
        while (!newrelic_set_appname(ini_get('newrelic.appname')) && $count < 100) {
            ++$count;
            sleep(1);
            $this->log('Attempt to connect to New Relic daemon #' . $count, LOG_INFO);
        }
        newrelic_start_transaction(ini_get('newrelic.appname'));

        $this->log('Starting API Fulfilment SQS Worker', LOG_INFO);

        /** @var FulfilmentCoreapiConsumer $fulfilmentConsumer */
        $fulfilmentConsumer = app()->make(FulfilmentCoreapiConsumer::class);

        while (true) {
            $fulfilmentConsumer->consume();
        }
    }
}
