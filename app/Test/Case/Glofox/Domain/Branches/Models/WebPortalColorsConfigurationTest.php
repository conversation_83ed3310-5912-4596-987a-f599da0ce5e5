<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\Branches\Models;

use Glofox\Domain\Branches\Models\Branch;
use Glofox\Domain\Branches\Models\WebPortalColorsConfiguration;
use Mockery;

\App::import('Test/Case', 'GlofoxTestCase');

class WebPortalColorsConfigurationTest extends \GlofoxTestCase
{
    public $fixtures = [];

    public function tearDown(): void
    {
        Mockery::close();
    }

    public function testCustomBackground(): void
    {
        $branch = new Branch([
            'id' => 1,
            'name' => 'Test Branch',
            'stripe_plan_code' => 'platinum',
        ]);

        $webPortalColorsConfig = new WebPortalColorsConfiguration([
            'background' => 'FFFFFF',
        ]);
        $webPortalColorsConfig->setBranchModel($branch);

        $color = $webPortalColorsConfig->background();

        $this->assertEquals('#FFFFFF', $color->hexadecimal());
    }

    public function testDefaultBackground(): void
    {
        $branch = new Branch([
            'id' => 1,
            'name' => 'Test Branch',
            'stripe_plan_code' => 'gold',
        ]);

        $webPortalColorsConfig = new WebPortalColorsConfiguration([
            'background' => 'FFFFFF',
        ]);
        $webPortalColorsConfig->setBranchModel($branch);

        $background = $webPortalColorsConfig->background();

        $this->assertEquals('#330F2F', $background->hexadecimal());
    }

    public function testCustomAccent(): void
    {
        $branch = new Branch([
            'id' => 1,
            'name' => 'Test Branch',
            'stripe_plan_code' => 'platinum',
        ]);

        $webPortalColorsConfig = new WebPortalColorsConfiguration([
            'accent' => 'FFFFFF',
        ]);
        $webPortalColorsConfig->setBranchModel($branch);

        $color = $webPortalColorsConfig->accent();

        $this->assertEquals('#FFFFFF', $color->hexadecimal());
    }

    public function testDefaultAccent(): void
    {
        $branch = new Branch([
            'id' => 1,
            'name' => 'Test Branch',
            'stripe_plan_code' => 'gold',
        ]);

        $webPortalColorsConfig = new WebPortalColorsConfiguration([
            'accent' => 'FFFFFF',
        ]);
        $webPortalColorsConfig->setBranchModel($branch);

        $background = $webPortalColorsConfig->accent();

        $this->assertEquals('#3BD8C3', $background->hexadecimal());
    }

    public function testCustomText(): void
    {
        $branch = new Branch([
            'id' => 1,
            'name' => 'Test Branch',
            'stripe_plan_code' => 'platinum',
        ]);

        $webPortalColorsConfig = new WebPortalColorsConfiguration([
            'text' => '3BD8C3',
        ]);
        $webPortalColorsConfig->setBranchModel($branch);

        $color = $webPortalColorsConfig->text();

        $this->assertEquals('#3BD8C3', $color->hexadecimal());
    }

    public function testDefaultText(): void
    {
        $branch = new Branch([
            'id' => 1,
            'name' => 'Test Branch',
            'stripe_plan_code' => 'gold',
        ]);

        $webPortalColorsConfig = new WebPortalColorsConfiguration([
            'text' => '3BD8C3',
        ]);
        $webPortalColorsConfig->setBranchModel($branch);

        $background = $webPortalColorsConfig->text();

        $this->assertEquals('#FFFFFF', $background->hexadecimal());
    }
}
