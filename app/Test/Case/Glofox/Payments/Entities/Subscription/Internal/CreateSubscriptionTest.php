<?php

namespace CakeTestCases\Glofox\Payments\Entities\Subscription\Internal;

use Glofox\Payments\Entities\Subscription\Internal\CreateSubscription;
use Glofox\Payments\Entities\Subscription\Models\Subscription;

\App::import('Test/Case', 'GlofoxTestCase');

final class CreateSubscriptionTest extends \GlofoxTestCase
{

    public function test_set_subscription()
    {
        $sub = (new Subscription())
            ->setCustomerId("cus-123")
            ->setMerchantId("mer-123")
            ->setPlanId("plan-123")
            ->setFee(100)
            ->setAmount(200)
            ->setTrialStartAt(1000)
            ->setTrialEndAt(2000)
            ->setMetadata(["key" => "value"])
            ->setEndAt(3000);

        $create = (new CreateSubscription())->setSubscription($sub);

        self::assertEquals("cus-123", $create->customerId());
        self::assertEquals("mer-123", $create->merchantId());
        self::assertEquals("plan-123", $create->planId());
        self::assertEquals(100, $create->fee());
        self::assertEquals(200, $create->amount());
        self::assertEquals(1000, $create->trialStartAt());
        self::assertEquals(2000, $create->trialEndAt());
        self::assertEquals(3000, $create->endAt());
        self::assertEquals(["key"=>"value"], $create->metadata());
    }

    public function test_it_sets_subscription_using_transaction_uuid(): void
    {
        $subscription = new Subscription();
        $subscription->setTransactionUUID('trans-id');
        $subscription->setAmount(1000);
        $subscription->setFee(0);

        $createSubscription = new CreateSubscription();
        $createSubscription->setSubscription($subscription);

        $this->assertSame('trans-id', $createSubscription->transactionUUID());
    }

    public function test_it_returns_an_empty_string_if_transaction_id_was_not_set(): void
    {
        $subscription = new Subscription();
        $subscription->setAmount(1000);
        $subscription->setFee(0);

        $createSubscription = new CreateSubscription();
        $createSubscription->setSubscription($subscription);

        $this->assertSame('', $createSubscription->transactionUUID());
    }

    public function test_it_sets_subscription_using_external_ref(): void
    {
        $subscription = new Subscription();
        $subscription->setExternalRef('external-ref');
        $subscription->setAmount(1000);
        $subscription->setFee(0);

        $createSubscription = new CreateSubscription();
        $createSubscription->setSubscription($subscription);

        $this->assertSame('external-ref', $createSubscription->externalRef());
    }

    public function test_it_returns_an_empty_string_if_external_ref_was_not_set(): void
    {
        $subscription = new Subscription();
        $subscription->setAmount(1000);
        $subscription->setFee(0);

        $createSubscription = new CreateSubscription();
        $createSubscription->setSubscription($subscription);

        $this->assertSame('', $createSubscription->externalRef());
    }

    public function test_it_sets_subscription_using_last_executed_at(): void
    {
        $subscription = new Subscription();
        $subscription->setLastExecutedAt(12345);
        $subscription->setAmount(1000);
        $subscription->setFee(0);

        $createSubscription = new CreateSubscription();
        $createSubscription->setSubscription($subscription);

        $this->assertSame(12345, $createSubscription->lastExecutedAt());
    }

    public function test_it_returns_zero_without_last_executed_at(): void
    {
        $subscription = new Subscription();
        $subscription->setAmount(1000);
        $subscription->setFee(0);

        $createSubscription = new CreateSubscription();
        $createSubscription->setSubscription($subscription);

        $this->assertSame(0, $createSubscription->lastExecutedAt());
    }

    public function test_it_sets_end_at(): void
    {
        $createSubscription = new CreateSubscription();

        $createSubscription->setEndAt(10);
        $this->assertSame(10, $createSubscription->endAt());

        $createSubscription->setEndAt(null);
        $this->assertSame(0, $createSubscription->endAt());
    }

    public function test_it_works_with_discounts(): void
    {
        $discounts = ['foo', 'bar'];

        $subscription = new Subscription();
        $subscription->setDiscounts($discounts);
        $subscription->setAmount(1000);
        $subscription->setFee(0);

        $createSubscription = new CreateSubscription();
        $createSubscription->setSubscription($subscription);

        $this->assertSame($discounts, $createSubscription->discounts());

        // without discounts
        $subscription = new Subscription();
        $subscription->setAmount(1000);
        $subscription->setFee(0);

        $createSubscription = new CreateSubscription();
        $createSubscription->setSubscription($subscription);

        $this->assertSame([], $createSubscription->discounts());
    }
}
