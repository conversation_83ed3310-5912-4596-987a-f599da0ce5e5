<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\Users\Requests;

use Glofox\Domain\Users\Requests\SyncCurrentMembershipRequest;

\App::import('Test/Case', 'GlofoxControllerTestCase');

class SyncCurrentMembershipRequestTest extends \GlofoxControllerTestCase
{
    public $fixtures = [];

    public function test_it_returns_the_tc_field_from_membership_service(): void
    {
        $request = new SyncCurrentMembershipRequest([
            'isTermsAndConditionsAccepted' => true,
        ]);

        $data = $request->membershipData();
        $this->assertTrue($data->get('isTermsAndConditionsAccepted'));

        $request = new SyncCurrentMembershipRequest();
        $data = $request->membershipData();
        $this->assertFalse($data->get('isTermsAndConditionsAccepted'));
    }
}
