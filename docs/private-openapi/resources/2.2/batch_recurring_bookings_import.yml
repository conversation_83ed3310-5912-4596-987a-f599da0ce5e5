post:
  tags:
    - Bookings
  summary: Import recurrent bookings
  operationId: import-recurrent-bookings
  description: 'This endpoint will first check bookings availability and then, will try to book or reserve available booking slots within the timeframe. Bookings will be based on the program schedule and it will attempt to book all possible slots. This endpoint is only used by Import service and all data are based on provided CSV file. To see how process is going you need to use `GET /2.2/branches/{branchId}/bookings/recurrent/{batchId}`'
  security:
    - coreapi_auth: [ ]
  requestBody:
    required: true
    content:
      application/json:
        schema:
          $ref: '../../requests/2.2/import_recurrent_bookings.yml'
  parameters:
    - schema:
        type: string
        pattern: '^[a-f\d]{24}$'
      name: branchId
      in: path
      required: true
      description: The unique identifier of the branch where the bookings are to be created.
  responses:
    '202':
      description: Ok
      content:
        application/json:
          schema:
            $ref: '../../schemas/recurrent_bookings_import.yml'
    '400':
      description: Bad request
      content:
        application/json:
          schema:
            $ref: '../../schemas/error.yml'
    '401':
      description: Unauthorized
      content:
        application/json:
          schema:
            $ref: '../../schemas/error.yml'
