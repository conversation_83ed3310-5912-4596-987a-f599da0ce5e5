<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\Announcements\Restrictions;

use Glofox\Domain\Announcements\Models\Announcement;
use Glofox\Domain\Announcements\Restrictions\HideContentValidatorPipeline;
use Glofox\Domain\Announcements\Restrictions\Validators\RestrictionValidatorInterface;
use Glofox\Domain\Users\Models\User;

\App::import('Test/Case', 'GlofoxTestCase');

class HideContentValidatorPipelineTest extends \GlofoxTestCase
{
    public $fixtures = [];

    public function tearDown(): void
    {
        parent::tearDown();
        \Mockery::close();
    }

    public function test_it_clears_reasons_for_each_announcement(): void
    {
        $validator = \Mockery::mock(RestrictionValidatorInterface::class);
        $validator->shouldReceive('isRestricted')->once()->andReturnTrue();
        $validator->shouldReceive('getReason')->once()->andReturn('FOO');
        $validator->shouldReceive('isRestricted')->once()->andReturnFalse();

        $pipeline = new HideContentValidatorPipeline();
        $pipeline->addValidator($validator);
        $this->assertEmpty($pipeline->getReasons());

        $pipeline->handle(new Announcement(), new User());
        $this->assertCount(1, $pipeline->getReasons());
        $this->assertSame(['FOO'], $pipeline->getReasons());

        $pipeline->handle(new Announcement(), new User());
        $this->assertEmpty($pipeline->getReasons());
    }

    public function test_it_does_not_change_the_content_if_the_validation_is_false(): void
    {
        $validator = \Mockery::mock(RestrictionValidatorInterface::class);
        $validator->shouldReceive('isRestricted')->andReturnFalse();

        $pipeline = new HideContentValidatorPipeline();
        $pipeline->addValidator($validator);

        $announcement = new Announcement(['content' => 'bar']);
        $pipeline->handle($announcement, new User());

        $this->assertSame('bar', $announcement->get('content'));
        $this->assertEmpty($pipeline->getReasons());
    }

    public function test_it_set_content_as_empty_if_validator_returns_true(): void
    {
        $validator = \Mockery::mock(RestrictionValidatorInterface::class);
        $validator->shouldReceive('isRestricted')->andReturnTrue();
        $validator->shouldReceive('getReason')->andReturn('FOO');

        $pipeline = new HideContentValidatorPipeline();
        $pipeline->addValidator($validator);

        $announcement = new Announcement(['content' => 'bar']);
        $pipeline->handle($announcement, new User());

        $this->assertEmpty($announcement->get('content'));
        $this->assertSame(['FOO'], $pipeline->getReasons());
    }
}
