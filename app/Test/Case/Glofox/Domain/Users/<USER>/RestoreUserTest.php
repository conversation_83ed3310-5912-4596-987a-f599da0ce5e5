<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\Users\UseCase;

use App;
use Glofox\Domain\Users\Exceptions\UserNotUpdatedException;
use Mockery;
use GlofoxTestCase;
use Exception;
use UnsuccessfulOperation;
use Glofox\Domain\Users\Services\UsersPublisher;
use Glofox\Domain\Users\UseCase\RestoreUser;
use Glofox\Domain\Users\UseCase\RestoreUserParams;
use Glofox\Domain\Users\Repositories\UsersRepository;

App::import('Test/Case', 'GlofoxTestCase');

class RestoreUserTest extends GlofoxTestCase
{
    public $fixtures = [];
    private UsersRepository $usersRepositoryMock;
    private UsersPublisher $publisherMock;

    public function setUp(): void
    {
        parent::setUp();
        $this->usersRepositoryMock = Mockery::mock(UsersRepository::class);
        $this->publisherMock = Mockery::mock(UsersPublisher::class);
    }

    public function tearDown(): void
    {
        parent::tearDown();
        Mockery::close();
    }

    /**
     * @throws UserNotUpdatedException
     * @throws UnsuccessfulOperation
     * @throws Exception
     */
    public function testRestoreUser(): void
    {
        $branchId = 'test-branch-id';
        $userId = 'test-user-id';

        $this->usersRepositoryMock->shouldReceive('activate')->once()->withArgs(
            function (
                string $userIdParam
            ) use ($userId) {
                self::assertEquals(
                    $userId,
                    $userIdParam
                );
                return true;
            }
        )->andReturn(true);

        $this->publisherMock->shouldReceive('sendMemberUpdatedEvent');

        $useCase = new RestoreUser(
            $this->usersRepositoryMock,
            $this->publisherMock
        );
        $params = new RestoreUserParams(
            $branchId,
            $userId
        );

        $useCase->execute($params);
    }
}
