<?php

declare(strict_types=1);

namespace Glofox\Infrastructure\Services\Reports\Requests;

use Psr\Http\Message\RequestInterface;

class GetReportsRequest
{
    /** @var string */
    private string $branchId;

    /** @var string */
    private string $reportId;

    /** @var RequestInterface */
    private RequestInterface $request;

    public function __construct(
        string $branchId,
        string $reportId,
        RequestInterface $request
    ) {
        $this->branchId = $branchId;
        $this->reportId = $reportId;
        $this->request = $request;
    }

    public function getReportId(): string
    {
        return $this->reportId;
    }

    public function getBranchId(): string
    {
        return $this->branchId;
    }

    public function getRequest(): RequestInterface
    {
        return $this->request;
    }
}
