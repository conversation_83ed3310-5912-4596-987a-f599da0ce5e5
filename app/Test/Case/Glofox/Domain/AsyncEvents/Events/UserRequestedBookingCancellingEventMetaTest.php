<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\AsyncEvents\Events;

use Glofox\Domain\AsyncEvents\Events\UserRequestedBookingCancellingEventMeta;

\App::import('Test/Case', 'GlofoxTestCase');

class UserRequestedBookingCancellingEventMetaTest extends \GlofoxTestCase
{
    public $fixtures = [];

    public function test_it_should_serialize_to_json(): void
    {
        $meta = new UserRequestedBookingCancellingEventMeta(['event_id' => '1234']);

        $this->assertTextContains('"correlation":{"event_id":"1234"}', json_encode($meta, JSON_THROW_ON_ERROR));
    }
}
