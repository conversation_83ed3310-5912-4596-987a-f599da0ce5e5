description: UsagesByInterval defines the the breakdown of usages based on a specified
  interval.
properties:
  intervalType:
    description: 'IntervalType refers to the type of interval: day, week, month,
      year'
    type: string
  intervalValue:
    description: IntervalValue refers to the value to apply to the interval.
    type: integer
  results:
    description: Results is the usage results based on the interval.
    items:
      $ref: './discount_insights_usages_by_interval_item.yml'
    type: array
type: object