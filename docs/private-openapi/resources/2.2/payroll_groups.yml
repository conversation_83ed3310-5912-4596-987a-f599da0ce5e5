parameters:
  - schema:
      type: string
      pattern: '^[a-f\d]{24}$'
    name: branchId
    in: path
    required: true
get:
  tags:
    - Payroll Groups
  summary: ''
  operationId: get-payroll-groups
  description: This endpoint returns filtered payroll groups of a given branch
  x-internal: true
  security:
    - coreapi_auth: [ ]
  parameters:
    - schema:
        type: string
      in: query
      name: sort-by
      description: field | -field
    - schema:
        type: integer
        minimum: 1
        default: 1
      in: query
      name: page
    - schema:
        type: integer
        minimum: 1
        maximum: 100
        default: 50
      in: query
      name: limit
  responses:
    '200':
      description: OK
      content:
        application/json:
          schema:
            allOf:
              - $ref: "../../schemas/pagination.yml"
              - type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: "../../schemas/payroll_group.yml"
post:
  tags:
    - Payroll Groups
  summary: ''
  operationId: create-payroll-group
  description: This endpoint will create a payroll group
  x-internal: true
  security:
    - coreapi_auth: [ ]
  requestBody:
    required: true
    content:
      application/json:
        schema:
          $ref: '../../requests/2.2/create_payroll_group.yml'
  responses:
    '201':
      description: OK
      content:
        application/json:
          schema:
            $ref: '../../schemas/payroll_group.yml'
    '400':
      description: Bad request
      content:
        application/json:
          schema:
            $ref: "../../schemas/error_payroll_group.yml"
