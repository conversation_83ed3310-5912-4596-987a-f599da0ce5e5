<?php

namespace Glofox\Authentication\Traits;

use Glofox\Domain\Integrators\Exceptions\CoexistingJwtAndApiTokenException;
use Glofox\Request;

trait ValidatesCoexistingAuthorizationToken {

    private function validatesCoexistingAuthorizationToken(Request $request)
    {
        if ($request->headers->get('authorization')) {
            throw new CoexistingJwtAndApiTokenException();
        }
    }
}