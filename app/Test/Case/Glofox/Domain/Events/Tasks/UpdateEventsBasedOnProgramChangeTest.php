<?php

namespace CakeTestCases\Glofox\Domain\Events\Tasks;

use Glofox\Domain\AsyncEvents\Events\ProgramUpdatedEvent;
use Glofox\Domain\AsyncEvents\Events\ProgramUpdatedEventMeta;
use Glofox\Domain\AsyncEvents\Events\ProgramUpdatedEventPayload;
use Glofox\Domain\AsyncEvents\Events\UserUpdatedEventEventMeta;
use Glofox\Domain\AsyncEvents\Events\UserUpdatedEventEventPayload;
use Glofox\Domain\Branches\Models\Branch;
use Glofox\Domain\Branches\Repositories\BranchesRepository;
use Glofox\Domain\Events\Models\Event;
use Glofox\Domain\Events\Repositories\EventsRepository;
use Glofox\Domain\Events\Services\EventsPublisher;
use Glofox\Domain\Events\Tasks\UpdateEventsBasedOnProgramChange;
use Glofox\Domain\Events\Transformers\AddVirtualOnlineFieldTransformer;
use Glofox\Domain\FeatureFlags\FeatureFlagInterface;
use Psr\Log\LoggerInterface;

\App::import('Test/Case', 'GlofoxTestCase');

class UpdateEventsBasedOnProgramChangeTest extends \GlofoxTestCase
{
    public $fixtures = [];
    private $branchesRepository;
    private $eventsRepository;
    private ?array $events = null;
    private $eventsPublisher;
    private ?\Glofox\Domain\AsyncEvents\Events\ProgramUpdatedEvent $domainEvent = null;
    private $logger;
    private $onlineFieldTransformer;

    public function setUp()
    {
        parent::setUp();

        $this->branchesRepository = \Mockery::mock(BranchesRepository::class);
        $this->branchesRepository->shouldReceive('addCriteria')->andReturnSelf();
        $this->branchesRepository->shouldReceive('firstOrFail')
            ->andReturn(
                new Branch([
                    'features' => [],
                    '_id' => '234',
                    'namespace' => 'test',
                    'timezone' => 'UTC',
                ])
            );

        $event = new Event([
            'name' => 'pname',
            'description' => 'pdesc',
            'program_id' => '345',
            'time_start' => new \MongoDate(strtotime('2020-02-19T10:00:00Z')),
            'time_finish' => new \MongoDate(strtotime('2020-02-19T11:00:00Z')),
            'size' => 10,
            'created' => new \MongoDate(strtotime('2020-02-10T11:00:00+00:00')),
            'modified' => new \MongoDate(strtotime('2020-02-19T11:00:00+00:00')),
            'namespace' => 'glofox',
            'level' => 'Advanced',
            'active' => true,
            'private' => true,
            'total_bookings' => 10,
            '_id' => '123',
            'external_provider_stream_url' => 'https://youtu.be/oavMtUWDBTM',
        ]);

        $this->events = [$event];

        $this->eventsRepository = \Mockery::mock(EventsRepository::class);
        $this->eventsRepository->shouldReceive('addCriteria')->andReturnSelf();
        $this->eventsRepository->shouldReceive('find')->andReturn($this->events);
        $this->eventsRepository->shouldReceive('save');

        $this->domainEvent = new ProgramUpdatedEvent(
            new ProgramUpdatedEventMeta([
                'branchId' => '49a7011a05c677b9a916612a',
                'gympassGymId' => null,
                'gympassProductId' => null,
                'gympassPassTypeNumber' => null,
                'gympassValidationApiAuthToken' => null,
                'gympassClientId' => null,
                'gympassClientSecret' => null,
            ]),
            new ProgramUpdatedEventPayload([
                'programId' => 'p123',
                'name' => 'pname',
                'description' => 'pdesc',
                'active' => true,
                'private' => true,
                'gympassEnabled' => true,
            ])
        );

        $this->eventsPublisher = \Mockery::mock(EventsPublisher::class);

        $this->logger = \Mockery::mock(LoggerInterface::class);
        $this->logger->shouldReceive('info');

        $this->onlineFieldTransformer = \Mockery::mock(AddVirtualOnlineFieldTransformer::class);
        $this->onlineFieldTransformer->shouldReceive('execute')
            ->andReturn([
                array_merge(
                    $event->toArray(),
                    ['is_online' => true] // TODO move is_online to facility domain
                ),
            ]);
    }

    private function createTask()
    {
        return new UpdateEventsBasedOnProgramChange(
            $this->eventsRepository,
            $this->branchesRepository,
            $this->eventsPublisher,
            $this->logger,
            $this->onlineFieldTransformer
        );
    }

    public function tearDown()
    {
        parent::tearDown();
        app()->forgetInstance(FeatureFlagInterface::class);
        \Mockery::close();
    }

    public function test_it_should_update_events_based_on_program_change()
    {
        $this->eventsPublisher
            ->shouldReceive('sendEventUpdatedEvent')
            ->once()
            ->withArgs(
                function (UserUpdatedEventEventMeta $meta, UserUpdatedEventEventPayload $payload) {
                    $metaData = $meta->jsonSerialize();
                    $payloadData = $payload->jsonSerialize();

                    $expectedMetaData = [
                        'branchId' => '234',
                        'namespace' => 'test',
                        'branchTimezone' => 'UTC',
                        'gympassProductId' => null,
                        'gympassGymId' => null,
                        'gympassPassTypeNumber' => null,
                        'gympassValidationApiAuthToken' => null,
                        'gympassClientId' => null,
                        'gympassClientSecret' => null,
                    ];
                    $expectedPayloadData = [
                        'eventId' => '123',
                        'isOnline' => true,
                        'programId' => '345',
                        'timeStart' => '2020-02-19T10:00:00+00:00',
                        'timeFinish' => '2020-02-19T11:00:00+00:00',
                        'size' => 10,
                        'active' => true,
                        'private' => true,
                        'totalBooked' => 10,
                        'hasDateChanged' => false,
                        'gympassEnabled' => true,
                        'name' => 'pname',
                        'namespace' => 'glofox',
                        'level' => 'Advanced',
                        'description' => 'pdesc',
                        'externalProviderStreamUrl' => 'https://youtu.be/oavMtUWDBTM',
                        'created' => '2020-02-10T11:00:00+00:00',
                        'modified' => '2020-02-19T11:00:00+00:00',
                    ];

                    $this->assertEquals($metaData['correlation'], $expectedMetaData);
                    $this->assertEquals($expectedPayloadData, $payloadData);

                    return true;
                }
            );

        $this->createTask()->execute($this->domainEvent);
    }
}
