<?php

namespace Glofox\Repositories\Search\Expressions;

use Glofox\Repositories\Search\Expressions\Exceptions\ExpressionNotSupportedException;
use Webmozart\Expression\Constraint\In;
use Webmozart\Expression\Constraint\Equals;
use Webmozart\Expression\Constraint\GreaterThan;
use Webmozart\Expression\Constraint\GreaterThanEqual;
use Webmozart\Expression\Constraint\LessThan;
use Webmozart\Expression\Constraint\LessThanEqual;
use Webmozart\Expression\Constraint\Matches;
use Webmozart\Expression\Constraint\NotEquals;
use Webmozart\Expression\Constraint\Same;
use Webmozart\Expression\Expression;

class MongoOperatorResolver
{
    public function resolve(Expression $expression)
    {
        switch (get_class($expression)) {
            case Matches::class:
                return '$in';

            case Equals::class:
            case Same::class:
                return '$eq';

            case NotEquals ::class:
                return '$ne';

            case In::class:
                return '$in';

            case GreaterThan::class:
                return '$gt';

            case GreaterThanEqual::class:
                return '$gte';

            case LessThan::class:
                return '$lt';

            case LessThanEqual::class:
                return '$lte';

            default:
                throw ExpressionNotSupportedException::withExpression($expression);
        }

        return $value;
    }
}
