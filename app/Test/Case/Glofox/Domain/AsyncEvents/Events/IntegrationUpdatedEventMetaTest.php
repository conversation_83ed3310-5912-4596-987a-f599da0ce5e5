<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\AsyncEvents\Events;

use Glofox\Domain\AsyncEvents\Events\IntegrationUpdatedEventMeta;

\App::import('Test/Case', 'GlofoxTestCase');

class IntegrationUpdatedEventMetaTest extends \GlofoxTestCase
{
    public $fixtures = [];

    public function testItShouldSerializeToJson(): void
    {
        $meta = new IntegrationUpdatedEventMeta([
            'integration' => 'MAILCHIMP',
            'action' => 'BULK_UPDATE',
        ]);

        $this->assertTextContains('"correlation":{"integration":"MAILCHIMP","action":"BULK_UPDATE"}', json_encode($meta, JSON_THROW_ON_ERROR));
    }
}
