// agreed with <PERSON> and <PERSON><PERSON> on 15/11/2019
var users = db.users.find({
    "active": true,
    "namespace": "glenroyal",
    "membership.type": {
        $in: ['time', 'time_classes', 'num_classes']
    },
    $or: [
        {
            "membership.plan_code": {
                $exists: false
            }
        },
        {
            "membership.plan_code": {
                $in: ["", 0, null]
            }
        }
    ]
});

var i = 1;
var error = 0;
var success = 0;
var total = users.count();
var detailedErrorList = {};

users.forEach((user) => {

    try {

        var membershipId = '5497fa2b7cad3623c08b4570';
        var planCode = '5497fa3c17e49';

        var query = {
            _id: user._id
        };

        var instruction = {
            $set: {
                "membership._id": membershipId,
                "membership.plan_code": planCode
            }
        };

        console.info(`User ${user._id.toString()} will have its plan_code set to`, instruction);

        // db.users.update(query, instruction);

        success++;

        console.info(`[${i}/${total}] Plan code recovered successfully (s: ${success} / e: ${error})`);

    }
    catch (err) {

        // console.error(err.message, err.stack);
        error++;

        console.info(`[${i}/${total}] Plan code recovery failed (s: ${success} / e: ${error}) -  ${err.message} (${user.namespace}, ${user.created})`);

        var branchId = (Array.isArray(user.branch_id) ? user.branch_id[0] : user.branch_id);
        var originBranchId =  user.origin_branch_id;
        var namespace = user.namespace;

        var errorDetail = {
            userId: user._id+"",
            branchId: originBranchId || branchId,
            namespace: namespace,
            error: err.message
        }

        if (!detailedErrorList[branchId]) {
            detailedErrorList[branchId] = {
                details: []
            };
        }

        detailedErrorList[branchId].details.push(errorDetail);
    }

    i++;
});

console.info(`.`);
console.info(`.`);
console.info(`[${success}/${total}] Membership & plan code recovered successfully`);
console.info(`[${error}/${total}] Membership & plan code recovery failed`);

print("branchId,namespace,totalErrors")
for (var branchId in detailedErrorList) {
    print([
        branchId,
        detailedErrorList[branchId].details[0].namespace,
        detailedErrorList[branchId].details.length
    ].join(','))
}