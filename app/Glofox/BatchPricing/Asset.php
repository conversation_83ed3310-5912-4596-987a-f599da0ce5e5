<?php

namespace Glofox\BatchPricing;

/**
 * An asset is an associative array which represents a Glofox record with price.
 * This can range from a program to a product all the way through memberships.
 *
 * The asset array contains a set of key pieces of data:
 *   - mongo_collection: which collection the record exists in
 *   - mongo_record_id: the id of the asset
 *   - service_type: the type of the asset, i.e: facility, product, etc.
 *   - service_id: the id of the asset. It can differ from mongo_record_id in
 *     some cases such as the facilities because the prices are stored in a
 *     different collection than the actual asset.
 *   - price: the price of the asset
 *   - price_type; the category of the asset which has the price, i.e: PAYG, membership, etc.
 *
 * The key idea is that a single asset mongo record, say a course, will
 * generate multiple asset arrays, one for each price. This is to ease the
 * processing afterwards.
 */
class Asset
{
    public static function fromProgram(array $program): array
    {
        if (!isset($program['allowed_member_types']) || !is_array($program['allowed_member_types'])) {
            return [];
        }

        return array_reduce($program['allowed_member_types'], function (array $acc, array $memberType) use ($program) {
            if (isset($memberType['price'])) {
                $acc[] = [
                        'mongo_collection' => 'programs',
                        'mongo_record_id' => $program['_id'],
                        'price_type' => $memberType['type'],
                        'service_type' => 'classes',
                        'service_id' => $program['_id'],
                        'price' => Helpers::formatPrice($memberType['price']),
                    ];
            }

            return $acc;
        }, []);
    }

    public static function fromProduct(array $product): array
    {
        if (!isset($product['presentations']) || !is_array($product['presentations'])) {
            return [];
        }

        return array_reduce($product['presentations'], function (array $acc, array $presentation) use ($product) {
            if (isset($presentation['retail_price'])) {
                $acc[] = [
                    'mongo_collection' => 'products',
                    'mongo_record_id' => $product['_id'],
                    'price_type' => 'retail_price',
                    'service_type' => 'products',
                    'service_id' => $product['_id'],
                    'price' => Helpers::formatPrice($presentation['retail_price']),
                    ];
            }

            return $acc;
        }, []);
    }

    public static function fromTrainer(array $trainer): array
    {
        $memberTypes = self::extractMemberTypesFromTrainer($trainer);

        $assets = array_reduce($memberTypes, function (array $acc, array $memberType) use ($trainer) {
            if (isset($memberType['price'])) {
                $acc[] = [
                    'mongo_collection' => 'users',
                    'mongo_record_id' => $trainer['_id'],
                    'price_type' => $memberType['type'],
                    'service_type' => 'trainers',
                    'service_id' => $trainer['_id'],
                    'price' => Helpers::formatPrice($memberType['price']),
                    ];
            }

            return $acc;
        }, []);

        if (isset($trainer['_pattern']['default_price'])) {
            $assets[] = [
                    'mongo_collection' => 'users',
                    'mongo_record_id' => $trainer['_id'],
                    'price_type' => 'default_price',
                    'service_type' => 'trainers',
                    'service_id' => $trainer['_id'],
                    'price' => Helpers::formatPrice($trainer['_pattern']['default_price']),
                ];
        }

        return $assets;
    }

    public static function fromTimeslotPattern(array $timeslot): array
    {
        if (!isset($timeslot['allowed_member_types']) || !is_array($timeslot['allowed_member_types'])) {
            return [];
        }

        return array_reduce($timeslot['allowed_member_types'], function (array $acc, array $memberType) use ($timeslot) {
            if (isset($memberType['price'])) {
                $acc[] = [
                    'mongo_collection' => 'time_slot_patterns',
                    'mongo_record_id' => $timeslot['_id'],
                    'price_type' => $memberType['type'],
                    'service_type' => 'facilities',
                    'service_id' => $timeslot['model_id'],
                    'price' => Helpers::formatPrice($memberType['price']),
                    ];
            }

            return $acc;
        }, []);
    }

    public static function fromMembership(array $membership): array
    {
        // Memberships have two different prices we must update: the upfront fee
        // and the price. It can have both, one, or neither. They live in the
        // plans property.

        if (!isset($membership['plans']) || !is_array($membership['plans'])) {
            return [];
        }

        return array_reduce($membership['plans'], function (array $acc, array $plan) use ($membership) {
            if (isset($plan['upfront_fee'])) {
                $acc[] = [
                        'mongo_collection' => 'memberships',
                        'mongo_record_id' => $membership['_id'],
                        'price_type' => 'upfront_fee',
                        'service_type' => 'memberships',
                        'service_id' => $membership['_id'],
                        'price' => Helpers::formatPrice($plan['upfront_fee']),
                    ];
            }

            if (isset($plan['price'])) {
                $acc[] = [
                        'mongo_collection' => 'memberships',
                        'mongo_record_id' => $membership['_id'],
                        'price_type' => 'price',
                        'service_type' => 'memberships',
                        'service_id' => $membership['_id'],
                        'price' => Helpers::formatPrice($plan['price']),
                    ];
            }

            return $acc;
        }, []);
    }

    public static function fromCourse(array $course): array
    {
        if (!isset($course['allowed_member_types']) || !is_array($course['allowed_member_types'])) {
            return [];
        }

        return array_reduce($course['allowed_member_types'], function (array $acc, array $memberType) use ($course) {
            if (isset($memberType['price'])) {
                $acc[] = [
                    'mongo_collection' => 'courses',
                    'mongo_record_id' => $course['_id'],
                    'price_type' => $memberType['type'],
                    'service_type' => 'courses',
                    'service_id' => $course['_id'],
                    'price' => Helpers::formatPrice($memberType['price']),
                ];
            }

            return $acc;
        }, []);
    }

    private static function extractMemberTypesFromTrainer(array $trainer): array
    {
        if (isset($trainer['_pattern'])
        && is_array($trainer['_pattern'])
        && isset($trainer['_pattern']['allowed_member_types'])
        && is_array($trainer['_pattern']['allowed_member_types'])) {
            return $trainer['_pattern']['allowed_member_types'];
        }
        return [];
    }
}
