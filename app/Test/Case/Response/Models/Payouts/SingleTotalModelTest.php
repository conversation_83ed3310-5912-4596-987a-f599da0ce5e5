<?php

use Glofox\Response\Models\Payouts\SingleTotalModel;

class SingleTotalModelTest extends GlofoxTestCase
{
    /**
     * @param $comparisonArray
     * @param $result
     * @dataProvider modelProvider
     */
    public function testModel($comparisonArray, $result)
    {
        $model = new SingleTotalModel($comparisonArray);
        $this->assertEquals($model, $result);
    }

    public function modelProvider(): array
    {
        return [
            'Simple model, without transaction or totals' => [
                [
                    'type' => 'CHARGE',
                    'count' => 12,
                    'gross' => 242,
                    'net_fees' => 242,
                    'total' => 242,
                ],
                new SingleTotalModel(
                    [
                        'type' => 'CHARGE',
                        'count' => 12,
                        'gross' => 242,
                        'net_fees' => 242,
                        'total' => 242,
                    ]
                ),
            ],
        ];
    }
}
