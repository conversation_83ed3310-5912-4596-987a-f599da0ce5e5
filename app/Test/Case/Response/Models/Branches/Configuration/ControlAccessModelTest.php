<?php

App::import('Test/Case', 'GlofoxTestCase');

use Glofox\Response\Models\Branches\Configuration\ControlAccessModel;

/**
 * Test cases
 * 1 Model contains all fields with data
 * 2 Missing fields and returns correct structure
 * 3 Additional fields and return correct structure
 * 4 Empty array fields and return correct structure.
 *
 * Class ControlAccessModelTest
 */
class ControlAccessModelTest extends GlofoxTestCase
{
    public $fixtures = [];

    /**
     * @dataProvider modelProvider
     */
    public function testModel($dataSent, $result): void
    {
        $model = new ControlAccessModel($dataSent);
        $this->assertEquals($model, $result);
    }

    public function modelProvider(): array
    {
        return [
            '1 Model contains all fields with data' => [
                [
                    'barcode' => true,
                    'booking_only_checkin' => true,
                    'enabled' => true,
                    'sound' => true,
                ],
                new ControlAccessModel(
                    [
                        'barcode' => true,
                        'booking_only_checkin' => true,
                        'enabled' => true,
                        'sound' => true,
                    ]
                ),
            ],
            '2 Missing fields and returns correct structure' => [
                [
                    // barcode missing
                    'booking_only_checkin' => true,
                    'enabled' => true,
                    'sound' => true,
                ],
                new ControlAccessModel(
                    [
                        'barcode' => false,
                        'booking_only_checkin' => true,
                        'enabled' => true,
                        'sound' => true,
                    ]
                ),
            ],
            '3 Additional fields and return correct structure' => [
                [
                    'test' => 0, // additional
                    'barcode' => true,
                    'booking_only_checkin' => true,
                    'enabled' => true,
                    'sound' => true,
                ],
                new ControlAccessModel(
                    [
                        'barcode' => true,
                        'booking_only_checkin' => true,
                        'enabled' => true,
                        'sound' => true,
                    ]
                ),
            ],
            '4 Empty array fields and return correct structure' => [
                [],
                new ControlAccessModel(
                    [
                        'barcode' => false,
                        'booking_only_checkin' => false,
                        'enabled' => false,
                        'sound' => false,
                    ]
                ),
            ],
        ];
    }
}
