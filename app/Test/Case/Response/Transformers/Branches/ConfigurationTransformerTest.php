<?php

declare(strict_types=1);

App::import('Test/Case', 'GlofoxTestCase');

use Glofox\Response\Models\Branches\Configuration\ConfigurationModel;
use Glofox\Response\Models\Branches\Configuration\ControlAccessModel;
use Glofox\Response\Models\Branches\Configuration\FormatsModel;
use Glofox\Response\Models\Branches\Configuration\KioskModel;
use Glofox\Response\Models\Branches\Configuration\SalesTaxModel;
use Glofox\Response\Transformers\Branches\ConfigurationTransformer;

/**
 * Class ConfigurationTransformerTest.
 */
class ConfigurationTransformerTest extends GlofoxTestCase
{
    public $fixtures = [];

    /**
     * @dataProvider configurationModelProvider
     */
    public function testSetConfigurationModel($dataArray, $featureModel): void
    {
        $transformer = new ConfigurationTransformer();
        $transformedModel = $transformer->setConfigurationModel($dataArray);
        $this->assertEquals($featureModel, $transformedModel);
    }

    public function configurationModelProvider(): array
    {
        return [
            'Check model and transformers return with the same structure' => [
                [
                    'only_payg' => false,
                    'formats' => [
                        'date' => 'DD/MM/YYYY',
                        'time' => 'HH:mm',
                    ],
                    'webportal' => null,
                    'control_access' => [
                        'barcode' => true,
                        'booking_only_checkin' => true,
                        'enabled' => true,
                        'sound' => true,
                    ],
                    'kiosk' => [
                        'colors' => null,
                        'sign_in_view' => 'class',
                        'allow_booking' => false,
                    ],
                    'sales_tax' => [
                        'legal_name' => null,
                        'tax_number' => null,
                        'company_id' => null
                    ]
                ],
                new ConfigurationModel(
                    [
                        'only_payg' => false,
                        'formats' => new FormatsModel(
                            [
                                'date' => 'DD/MM/YYYY',
                                'time' => 'HH:mm',
                            ]
                        ),
                        'webportal' => null,
                        'control_access' => new ControlAccessModel(
                            [
                                'barcode' => true,
                                'booking_only_checkin' => true,
                                'enabled' => true,
                                'sound' => true,
                            ]
                        ),
                        'kiosk' => new KioskModel(
                            [
                                'colors' => null,
                                'sign_in_view' => 'class',
                                'allow_booking' => false,
                            ]
                        ),
                        'facebookPixel' => [
                            'pixelId' => null,
                        ],
                        'sales_tax' => new SalesTaxModel(
                            [
                                'tax_number' => '',
                                'legal_name' => '',
                                'company_id' => '',
                            ]
                        )
                    ]
                ),
            ],
        ];
    }
}
