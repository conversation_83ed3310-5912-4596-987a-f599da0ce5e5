<?php

declare(strict_types=1);

use Glofox\Domain\Authentication\Auth;
use Glofox\Domain\Credits\Models\CreditPack;
use Glofox\Domain\Credits\Source\SourceIsImportedMarker;
use Glofox\Domain\Credits\Source\SourceIsManuallyAddedByStaffMarker;
use Glofox\Domain\Credits\Transformers\SaveBatchRequestTransformer;
use Glofox\Domain\Credits\UseCases\AlignAdvancedCreditsWithMembership;
use Glofox\Domain\Credits\UseCases\AlignCurrentCycleCreditsWithMembership;
use Glofox\Domain\Users\Validation\Guard\ReceptionistRestrictionRevenueGuard;

App::uses('AppController', 'Controller');

class UserCreditsController extends \Glofox\Domain\Credits\Http\UserCreditsController
{
    /**
     * @var string
     */
    public $name = 'UserCredits';

    /**
     * @var array<int, string>
     */
    public $uses = [
        'UserCredit',
        'Booking',
        'Event',
        'TimeSlot',
    ];

    /**
     * @var array<int, string>
     */
    public $components = [
        'Utility',
        'JWT',
    ];

    private SourceIsManuallyAddedByStaffMarker $sourceIsManuallyAddedByStaffMarker;

    private SourceIsImportedMarker $sourceIsImportedMarker;

    private ReceptionistRestrictionRevenueGuard $receptionistRevenueActionRestrictionGuard;

    private AlignAdvancedCreditsWithMembership $alignAdvancedCreditsWithMembershipUseCase;

    private AlignCurrentCycleCreditsWithMembership $alignCurrentCycleCreditsWithMembershipUseCase;

    public function __construct($request = null, $response = null)
    {
        parent::__construct($request, $response);

        $this->sourceIsManuallyAddedByStaffMarker = app()->make(SourceIsManuallyAddedByStaffMarker::class);
        $this->sourceIsImportedMarker = app()->make(SourceIsImportedMarker::class);
        $this->receptionistRevenueActionRestrictionGuard = app()->make(ReceptionistRestrictionRevenueGuard::class);
        $this->alignAdvancedCreditsWithMembershipUseCase = app()->make(
            AlignAdvancedCreditsWithMembership::class
        );
        $this->alignCurrentCycleCreditsWithMembershipUseCase = app()->make(
            AlignCurrentCycleCreditsWithMembership::class
        );
    }

    public function dispatcher($identifier = null)
    {
        return parent::dispatch($identifier, $this->UserCredit);
    }

    public function findAllAvailableByBranchIdAndUserId(string $branch_id, string $user_id)
    {
        $user_credits = $this->UserCredit->findAllAvailableByBranchIdAndUserId($branch_id, $user_id, false);

        $result = [
            'success' => true,
            'user_credits' => $user_credits,
        ];

        return json_encode($result, JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    /**
     * @param $branch_id
     * @param $user_id
     *
     * @return string
     */
    public function findAllByBranchIdAndUserId($branch_id, $user_id)
    {
        $user_credits = $this->UserCredit->findAllByBranchIdAndUserId($branch_id, $user_id, false);

        foreach ($user_credits as &$credit) {
            foreach ($credit['bookings'] as &$bookingId) {
                // @see https://glofox.atlassian.net/browse/DASH2-3126
                $booking = $this->Booking->findById($bookingId);
                $booking = Hash::extract($booking, 'Booking');

                if (isset($booking['created']) && $booking['created'] instanceof MongoDate) {
                    /** @var MongoDate $created */
                    $created = $booking['created'];
                    $booking['created'] = $created->toDateTime()->format('Y-m-d H:i:s');
                }

                // retro compatibility
                $booking['name'] = '';
                if (isset($booking['model_name'])) {
                    $booking['name'] = $booking['model_name'];
                } elseif (isset($booking['event_name'])) {
                    $booking['name'] = $booking['event_name'];
                } elseif (isset($booking['user_name'])) {
                    $booking['name'] = $booking['user_name'];
                }

                $bookingId = $booking;
            }
        }

        return json_encode(['success' => true, 'user_credits' => $user_credits], JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    public function saveBatch($member_id = null)
    {
        $this->loadModel('User');
        $member = $this->User->findByIdSetFields($member_id, ['namespace', 'branch_id'], false);
        $allowed_roles = ['admin', 'superadmin', 'receptionist'];
        $namespace = $member['namespace'] ?? '';
        $branch_id = $member['branch_id'] ?? '';

        $has_access = $this->JWT->validate($allowed_roles, $namespace, $branch_id);

        if (!$has_access['success']) {
            return json_encode($has_access, JSON_PARTIAL_OUTPUT_ON_ERROR);
        }

        $authenticatedUser = Auth::user();

        $this->receptionistRevenueActionRestrictionGuard->check($authenticatedUser);

        $input_credits = json_decode($this->request->input(), true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        $credits_to_update = [];
        $new_credits = [];

        $this->UserCredit->validateCredits($input_credits);

        $input_credits = app()
            ->make(SaveBatchRequestTransformer::class)
            ->transform($input_credits, $branch_id);

        if (!empty($input_credits)) {
            foreach ($input_credits as $input_credit) {
                $input_credit['namespace'] = $namespace;
                $input_credit['branch_id'] = $branch_id;

                if (!empty($input_credit['_id'])) {
                    $credits_to_update[$input_credit['_id']] = $input_credit;
                } else {
                    $new_credits[] = $input_credit;
                }
            }
        }

        $conditions = ['_id' => ['$in' => array_keys($credits_to_update)]];
        $credits = $this->UserCredit->find('all', ['conditions' => $conditions]);
        $num_credits_updated = 0;

        foreach ($credits as $credit) {
            $this->UserCredit->read(null, $credit['UserCredit']['_id']);
            $credit['UserCredit'] = array_merge(
                $credit['UserCredit'],
                $credits_to_update[$credit['UserCredit']['_id']]
            );
            $num_credits_updated = ($this->UserCredit->save($credit)) ? $num_credits_updated + 1 : $num_credits_updated;
        }

        // This is to save new credit same way the old ones are getting updated
        $num_new_credits = 0;
        $creditPacksCreated = [];

        if (!empty($new_credits)) {
            foreach ($new_credits as $new_credit) {
                $this->UserCredit->create();

                $pack = CreditPack::make($new_credit);

                if ($authenticatedUser->isIntegrator()) {
                    $this->sourceIsImportedMarker->mark($pack);
                } else {
                    $this->sourceIsManuallyAddedByStaffMarker->mark($pack, $authenticatedUser);
                }

                $new_credit = $pack->toArray();

                $creditPack = $this->UserCredit->save($new_credit);

                $num_new_credits = ($creditPack) ? $num_new_credits + 1 : $num_new_credits;

                if ($creditPack) {
                    $creditPacksCreated[] = $creditPack;
                }
            }
        }

        if ($num_credits_updated > 0 || $num_new_credits > 0) {
            return json_encode([
                'success' => true,
                'num_credits_updated' => $num_credits_updated,
                'num_new_credits' => $num_new_credits,
                'message_code' => 'CREDITS_SUCCESSFULLY_SAVED',
                'credit_packs' => array_map(fn($credit) => $credit['UserCredit']['_id'], $creditPacksCreated),
            ], JSON_PARTIAL_OUTPUT_ON_ERROR);
        }

        return json_encode([
            'success' => false,
            'message_code' => 'OCURRED_A_PROBLEM_SAVING_THE_CREDITS',
        ], JSON_THROW_ON_ERROR);
    }

    public function alignWithMembership()
    {
        $authUser = Auth::user();

        $isAdmin = $authUser && $authUser->isAdmin();
        $isIntegrator = $isAdmin && $authUser->isIntegrator();
        $isGlofoxStaff = $isAdmin && $authUser->isGlofoxStaff();

        if (!$isIntegrator && !$isGlofoxStaff) {
            return json_encode(['success' => false, 'message_code' => 'ACTION_IS_NOT_ALLOWED'], JSON_THROW_ON_ERROR);
        }

        $userId = $this->request->param('userId');
        $branchId = $this->request->param('branchId');
        $isDryRun = filter_var($this->request->query('is-dry-run') ?? true, FILTER_VALIDATE_BOOLEAN);

        $this->alignCurrentCycleCreditsWithMembershipUseCase->alignCredits($userId, $branchId, $isDryRun);
        $this->alignAdvancedCreditsWithMembershipUseCase->alignCredits($userId, $branchId, $isDryRun);

        return json_encode(['success' => true], JSON_THROW_ON_ERROR);
    }
}
