<?php

declare(strict_types=1);

namespace Glofox\Infrastructure\GoogleRecaptcha;

use Glofox\Infrastructure\GoogleRecaptcha\Enum\ErrorCode;

class VerifyResponse
{
    private bool $isSuccess;

    /**
     * @var array<int, ErrorCode::*>
     */
    private array $errorCodes;

    public function __construct(
        bool $isSuccess,
        array $errorCodes
    ) {
        $this->isSuccess = $isSuccess;
        $this->errorCodes = $errorCodes;
    }

    public function isSuccess(): bool
    {
        return $this->isSuccess;
    }

    /**
     * @var array<int, ErrorCode::*>
     */
    public function getErrorCodes(): array
    {
        return $this->errorCodes;
    }
}