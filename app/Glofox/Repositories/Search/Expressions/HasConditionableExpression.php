<?php

namespace Glofox\Repositories\Search\Expressions;

use Glofox\Repositories\Filters\ConditionsCollection;
use Glofox\Repositories\Repository;
use Illuminate\Support\Collection;
use Webmozart\Expression\Expression;

trait HasConditionableExpression
{
    /**
     * @return Expression
     */
    public function getExpression() // : Expression
    {
        return $this->expression();
    }

    public function field() : string
    {
        /** @var Conditionable $expression */
        $expression = $this->expression();

        return $expression->field();
    }

    public function generateConditions() : Collection
    {
        /** @var Conditionable $expression */
        $expression = $this->expression();

        return $expression->generateConditions();
    }

    public function applyToConditions(ConditionsCollection $conditions, Repository $repository = null)
    {
        /** @var Conditionable $expression */
        $expression = $this->expression();

        return $expression->applyToConditions($conditions, $repository);
    }
}
