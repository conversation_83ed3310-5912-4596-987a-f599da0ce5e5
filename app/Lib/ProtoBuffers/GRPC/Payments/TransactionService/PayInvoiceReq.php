<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: invoice.proto

namespace GRPC\Payments\TransactionService;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>grpc.PayInvoiceReq</code>
 */
class PayInvoiceReq extends \Google\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string InvoiceID = 1;</code>
     */
    protected $InvoiceID = '';
    /**
     * Generated from protobuf field <code>int32 CustomerAccountID = 2;</code>
     */
    protected $CustomerAccountID = 0;
    /**
     * Generated from protobuf field <code>int32 DestinationAccountID = 3;</code>
     */
    protected $DestinationAccountID = 0;
    /**
     * Generated from protobuf field <code>int32 Amount = 4;</code>
     */
    protected $Amount = 0;
    /**
     * Generated from protobuf field <code>string PaymentMethod = 5;</code>
     */
    protected $PaymentMethod = '';
    /**
     * Generated from protobuf field <code>string Descriptions = 6;</code>
     */
    protected $Descriptions = '';
    /**
     * Generated from protobuf field <code>bool OffSession = 7;</code>
     */
    protected $OffSession = false;
    /**
     * Generated from protobuf field <code>bool AllowOverdraft = 8;</code>
     */
    protected $AllowOverdraft = false;
    /**
     * Generated from protobuf field <code>bool Recurring = 9;</code>
     */
    protected $Recurring = false;
    /**
     * Generated from protobuf field <code>bool UseAvailableAccountBalance = 10;</code>
     */
    protected $UseAvailableAccountBalance = false;
    /**
     * Generated from protobuf field <code>bool ForceSingleTransaction = 11;</code>
     */
    protected $ForceSingleTransaction = false;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $InvoiceID
     *     @type int $CustomerAccountID
     *     @type int $DestinationAccountID
     *     @type int $Amount
     *     @type string $PaymentMethod
     *     @type string $Descriptions
     *     @type bool $OffSession
     *     @type bool $AllowOverdraft
     *     @type bool $Recurring
     *     @type bool $UseAvailableAccountBalance
     *     @type bool $ForceSingleTransaction
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Invoice::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string InvoiceID = 1;</code>
     * @return string
     */
    public function getInvoiceID()
    {
        return $this->InvoiceID;
    }

    /**
     * Generated from protobuf field <code>string InvoiceID = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setInvoiceID($var)
    {
        GPBUtil::checkString($var, True);
        $this->InvoiceID = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 CustomerAccountID = 2;</code>
     * @return int
     */
    public function getCustomerAccountID()
    {
        return $this->CustomerAccountID;
    }

    /**
     * Generated from protobuf field <code>int32 CustomerAccountID = 2;</code>
     * @param int $var
     * @return $this
     */
    public function setCustomerAccountID($var)
    {
        GPBUtil::checkInt32($var);
        $this->CustomerAccountID = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 DestinationAccountID = 3;</code>
     * @return int
     */
    public function getDestinationAccountID()
    {
        return $this->DestinationAccountID;
    }

    /**
     * Generated from protobuf field <code>int32 DestinationAccountID = 3;</code>
     * @param int $var
     * @return $this
     */
    public function setDestinationAccountID($var)
    {
        GPBUtil::checkInt32($var);
        $this->DestinationAccountID = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 Amount = 4;</code>
     * @return int
     */
    public function getAmount()
    {
        return $this->Amount;
    }

    /**
     * Generated from protobuf field <code>int32 Amount = 4;</code>
     * @param int $var
     * @return $this
     */
    public function setAmount($var)
    {
        GPBUtil::checkInt32($var);
        $this->Amount = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string PaymentMethod = 5;</code>
     * @return string
     */
    public function getPaymentMethod()
    {
        return $this->PaymentMethod;
    }

    /**
     * Generated from protobuf field <code>string PaymentMethod = 5;</code>
     * @param string $var
     * @return $this
     */
    public function setPaymentMethod($var)
    {
        GPBUtil::checkString($var, True);
        $this->PaymentMethod = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string Descriptions = 6;</code>
     * @return string
     */
    public function getDescriptions()
    {
        return $this->Descriptions;
    }

    /**
     * Generated from protobuf field <code>string Descriptions = 6;</code>
     * @param string $var
     * @return $this
     */
    public function setDescriptions($var)
    {
        GPBUtil::checkString($var, True);
        $this->Descriptions = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>bool OffSession = 7;</code>
     * @return bool
     */
    public function getOffSession()
    {
        return $this->OffSession;
    }

    /**
     * Generated from protobuf field <code>bool OffSession = 7;</code>
     * @param bool $var
     * @return $this
     */
    public function setOffSession($var)
    {
        GPBUtil::checkBool($var);
        $this->OffSession = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>bool AllowOverdraft = 8;</code>
     * @return bool
     */
    public function getAllowOverdraft()
    {
        return $this->AllowOverdraft;
    }

    /**
     * Generated from protobuf field <code>bool AllowOverdraft = 8;</code>
     * @param bool $var
     * @return $this
     */
    public function setAllowOverdraft($var)
    {
        GPBUtil::checkBool($var);
        $this->AllowOverdraft = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>bool Recurring = 9;</code>
     * @return bool
     */
    public function getRecurring()
    {
        return $this->Recurring;
    }

    /**
     * Generated from protobuf field <code>bool Recurring = 9;</code>
     * @param bool $var
     * @return $this
     */
    public function setRecurring($var)
    {
        GPBUtil::checkBool($var);
        $this->Recurring = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>bool UseAvailableAccountBalance = 10;</code>
     * @return bool
     */
    public function getUseAvailableAccountBalance()
    {
        return $this->UseAvailableAccountBalance;
    }

    /**
     * Generated from protobuf field <code>bool UseAvailableAccountBalance = 10;</code>
     * @param bool $var
     * @return $this
     */
    public function setUseAvailableAccountBalance($var)
    {
        GPBUtil::checkBool($var);
        $this->UseAvailableAccountBalance = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>bool ForceSingleTransaction = 11;</code>
     * @return bool
     */
    public function getForceSingleTransaction()
    {
        return $this->ForceSingleTransaction;
    }

    /**
     * Generated from protobuf field <code>bool ForceSingleTransaction = 11;</code>
     * @param bool $var
     * @return $this
     */
    public function setForceSingleTransaction($var)
    {
        GPBUtil::checkBool($var);
        $this->ForceSingleTransaction = $var;

        return $this;
    }

}

