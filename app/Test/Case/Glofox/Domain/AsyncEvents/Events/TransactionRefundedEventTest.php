<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\AsyncEvents\Events;

use Glofox\Domain\AsyncEvents\Events\TransactionRefundedEvent;
use Glofox\Domain\AsyncEvents\Events\TransactionRefundedEventMeta;
use Glofox\Domain\AsyncEvents\Events\TransactionRefundedEventPayload;

\App::import('Test/Case', 'GlofoxTestCase');

class TransactionRefundedEventTest extends \GlofoxTestCase
{
    public $fixtures = [];

    public function test_it_should_serialize_to_json(): void
    {
        $payload = \Mockery::mock(TransactionRefundedEventPayload::class);

        $payload
            ->shouldReceive('jsonSerialize')
            ->andReturn(['charge' => ['price' => 100]]);

        $meta = new TransactionRefundedEventMeta([]);
        $event = new TransactionRefundedEvent($meta, $payload);

        $json = json_encode($event, JSON_THROW_ON_ERROR);
        $this->assertTextContains('"type":"TRANSACTION_REFUNDED"', $json);
        $this->assertTextContains('"payload":{"charge":{"price":100}}', $json);
    }
}
