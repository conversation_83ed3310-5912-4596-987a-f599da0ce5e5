<?php

namespace Glofox\Payments\Providers\Gateway;

use Glofox\Domain\PaymentMethods\Models\PaymentMethod;
use Glofox\Domain\PaymentProviders\Models\PaymentProvider;
use Glofox\Payments\Entities\Capability\Contracts\CapabilityHandlerContract;
use Glofox\Payments\Entities\Card\Contracts\CardHandlerContract;
use Glofox\Payments\Entities\Mandate\Contracts\MandateHandlerContract;
use Glofox\Payments\Entities\Subscription\Contracts\SubscriptionHandlerContract;
use Glofox\Payments\Entities\TerminalReader\Contracts\TerminalReaderHandlerContract;
use Glofox\Payments\Entities\Transaction\Contracts\ChargeHandlerContract;
use Glofox\Payments\Entities\Customer\Contracts\CustomerHandlerContract;
use Glofox\Payments\Entities\Invoice\Contracts\InvoiceHandlerContract;
use Glofox\Payments\Entities\Merchant\Contracts\MerchantHandlerContract;
use Glofox\Payments\Contracts\PaymentProviderContract;
use Glofox\Payments\Entities\Payout\Contracts\PayoutHandlerContract;
use Glofox\Payments\Entities\Plan\Contracts\PlanHandlerContract;
use Glofox\Payments\Entities\WebHook\Contracts\WebHookHandlerContract;
use Glofox\Payments\Providers\Gateway\Capabilities\Capabilities;
use Glofox\Payments\Providers\Gateway\Cards\Cards;
use Glofox\Payments\Providers\Gateway\Charges\Charges;
use Glofox\Payments\Providers\Gateway\Customers\Customers;
use Glofox\Payments\Providers\Gateway\Invoices\Invoices;
use Glofox\Payments\Providers\Gateway\Mandates\Mandates;
use Glofox\Payments\Providers\Gateway\Merchants\Merchants;
use Glofox\Payments\Providers\Gateway\Payouts\Payouts;
use Glofox\Payments\Providers\Gateway\Plans\Plans;
use Glofox\Payments\Providers\Gateway\Subscriptions\Subscriptions;
use Glofox\Payments\Providers\Gateway\TerminalReaders\TerminalReaders;
use Glofox\Payments\Providers\Gateway\WebHooks\WebHooks;

/**
 * Class Handler
 * @package Glofox\Payments\Providers\Gateway
 */
class Handler implements PaymentProviderContract
{
    /**
     * @var PaymentMethod
     */
    protected $paymentMethod;

    /**
     * @var PaymentProvider
     */
    protected $paymentProvider;

    public function __construct(PaymentMethod $paymentMethod, PaymentProvider $paymentProvider)
    {
        $this->paymentMethod = $paymentMethod;
        $this->paymentProvider = $paymentProvider;
    }

    /**
     * @return PaymentMethod
     */
    public function paymentMethod(): PaymentMethod
    {
        return $this->paymentMethod;
    }

    /**
     * @return PaymentProvider
     */
    public function paymentProvider(): PaymentProvider
    {
        return $this->paymentProvider;
    }

    /**
     * @return MerchantHandlerContract
     */
    public function merchants(): MerchantHandlerContract
    {
        return $this->buildHandlerContract(Merchants::class);
    }

    /**
     * @return CustomerHandlerContract
     */
    public function customers(): CustomerHandlerContract
    {
        return $this->buildHandlerContract(Customers::class);
    }

    /**
     * @return CardHandlerContract
     */
    public function cards(): CardHandlerContract
    {
        return $this->buildHandlerContract(Cards::class);
    }

    /**
     * @return MandateHandlerContract
     */
    public function mandates(): MandateHandlerContract
    {
        return $this->buildHandlerContract(Mandates::class);
    }

    public function terminalReaders(): TerminalReaderHandlerContract
    {
        return $this->buildHandlerContract(TerminalReaders::class);
    }

    /**
     * @return ChargeHandlerContract
     */
    public function charges(): ChargeHandlerContract
    {
        return $this->buildHandlerContract(Charges::class);
    }

    /**
     * @return PlanHandlerContract
     */
    public function plans(): PlanHandlerContract
    {
        return $this->buildHandlerContract(Plans::class);
    }

    /**
     * @return SubscriptionHandlerContract
     */
    public function subscriptions(): SubscriptionHandlerContract
    {
        return $this->buildHandlerContract(Subscriptions::class);
    }

    /**
     * @return PayoutHandlerContract
     */
    public function payouts(): PayoutHandlerContract
    {
        return $this->buildHandlerContract(Payouts::class);
    }

    /**
     * @return InvoiceHandlerContract
     */
    public function invoices(): InvoiceHandlerContract
    {
        return $this->buildHandlerContract(Invoices::class);
    }

    /**
     * @return WebHookHandlerContract
     */
    public function webhooks(): WebHookHandlerContract
    {
        return $this->buildHandlerContract(WebHooks::class);
    }

    /**
     * @return CapabilityHandlerContract
     */
    public function capabilities(): CapabilityHandlerContract
    {
        return $this->buildHandlerContract(Capabilities::class);
    }

    /**
     * @param $class
     * @return mixed
     */
    private function buildHandlerContract($class)
    {
        return app()->makeWith($class, [
            'paymentMethod' => $this->paymentMethod,
            'paymentProvider' => $this->paymentProvider
        ]);
    }
}
