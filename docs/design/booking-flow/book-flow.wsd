@startuml
start

:book(BookingRequest);
:payload = reqInput;
:$bookingRequestParams = BookingRequestParams(reqCreateSlot, reqUserID, reqBranchID);
switch (method?)
case (POST)
  switch (model)
  case (course)
    #pink:response = bookCourse(payload);
  case (timeslot)
    #green:response = bookTimeslot(payload, bookingRequestParams);
  case (event)
    #pink:response = bookEvent(payload);
  endswitch
case (DELETE)
  #pink:response = cancel(identifier, userID, authorID);
case (default)
  :resopnse = [];
endswitch
:response = normalizeBookingResponse(reqUserID, reqBranchID, payload, response);
:return response;

stop
@enduml
