<?php
App::uses('AppShell', 'Console/Command');
App::uses('ComponentCollection', 'Controller');
App::uses('Controller', 'Controller');
App::uses('Folder', 'Utility');
App::uses('File', 'Utility');

class F45SubscriptionsShell extends AppShell
{
    public $uses = [
        'User',
        'Membership',
        'Program',
        'Branch',
        'UserCredit',
        'Booking',
        'Event',
        'TimeSlotPattern',
        'TimeSlot',
        'StripeCharge'
    ];

    //  Remember change $allowPriceOverride = in_array($this->loggedUser['type'], UserType::ADMINISTRATORS); to true from Membership Model
    public function assignMembership()
    {
        $this->log("~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~", LOG_INFO);
        $this->log("Cron Started assignMembership", LOG_INFO);
        App::import('Controller', 'Users');

        $dir = ROOT . '/vendor/import_folder/TODO/';
        $client_csv = new File($dir . 'subscriptions.csv');
        $rows = preg_split('&\r\n|\r|\n&', $client_csv->read());

        $branch_id = 'TODO';
        $namespace = 'TODO';

        $branch = $this->Branch->findOrFail($branch_id);
        $payment_method = 'credit_card';

        $successMembers = [];
        $failMembers = [];
        $skippedMembers = [];

        foreach ($rows as $row) {
            $field = explode(",", $row);

            if ($field != null && is_array($field) && !empty($field['0'])) {
                $email = trim(strtolower($field['0']));

                $user = $this->User->find(
                    'first',
                    [
                        'conditions' => [
                            'namespace' => $namespace,
                            'type' => 'MEMBER',
                            'active' => true,
                            'email' => $email,
                            'membership.subscription' => ['$exists' => false],
                            'stripe_customer_id' => ['$exists' => true],
                            '$or' => [
                                [
                                    '$and' => [
                                        [
                                            'origin_branch_id' => ['$exists' => true],
                                            'origin_branch_id' => $branch_id
                                        ]
                                    ]
                                ],
                                [
                                    '$and' => [
                                        [
                                            'origin_branch_id' => ['$exists' => false],
                                            'branch_id' => $branch_id
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                );

                $this->log('$email => ' . json_encode($email, JSON_PARTIAL_OUTPUT_ON_ERROR), LOG_INFO);

                if ($user != null && !empty($user) && isset($user['User']['stripe_customer_id']) && !empty($user['User']['stripe_customer_id'])) {
                    $glofoxMembership = [];
                    $options = [];

                    $glofoxMembership['_id'] = $field['1'];
                    $glofoxMembership['plan_code'] = $field['2'];
                    $glofoxMembership['start_date'] = 'TODO';
                    $glofoxMembership['expiry_date'] = $field['4'];
                    $glofoxMembership['subscription']['end_date'] = !empty($field['4']) ? $field['4'] : null;

                    $glofoxMembership['trial'] = $field['3'];

                    $membership = $this->Membership->findOrFail($glofoxMembership['_id']);
                    $plan_code = $glofoxMembership['plan_code'];
                    $options['start_date'] = $glofoxMembership['trial'];

                    try {
                        $response = $this->Membership->buyMembershipProcess($branch, $user, $payment_method, $membership, $plan_code, $options);

                        if ($response['success']) {
                            $user = $this->User->findById($user['User']['_id']);
                            $user['User']['membership']['start_date'] = $glofoxMembership['start_date'];

                            if ($glofoxMembership['subscription']['end_date'] && isset($user['User']['membership']['subscription']['end_date'])) {
                                $user['User']['membership']['subscription']['end_date'] = $glofoxMembership['subscription']['end_date'];
                            }

                            if ($this->User->save($user['User']) == false) {
                                $failMembers[] = [
                                    'email => ' => $user['User']['email'],
                                    'first name => ' => $user['User']['first_name'],
                                    'last name => ' => $user['User']['last_name']
                                ];
                            } else {
                                $successMembers[] = [
                                    'email => ' => $user['User']['email'],
                                    'first name => ' => $user['User']['first_name'],
                                    'last name => ' => $user['User']['last_name']
                                ];
                            }
                        } else {
                            $this->log('skipped => ' . json_encode($field, JSON_PARTIAL_OUTPUT_ON_ERROR), LOG_INFO);
                            $failMembers[] = [
                                'email => ' => $user['User']['email'],
                                'first name => ' => $user['User']['first_name'],
                                'last name => ' => $user['User']['last_name']
                            ];
                        }
                    } catch (Exception $e) {
                        $this->log('Caught exception => ' . json_encode($e->getMessage(), JSON_PARTIAL_OUTPUT_ON_ERROR), LOG_ERR);
                    }
                } else {
                    $skippedMembers[] = $email;
                }
            }
        }
        $this->log('successMembers => ' . json_encode($successMembers, JSON_PARTIAL_OUTPUT_ON_ERROR), LOG_INFO);
        $this->log('$failMembers => ' . json_encode($failMembers, JSON_PARTIAL_OUTPUT_ON_ERROR), LOG_INFO);
        $this->log('$skippedMembers => ' . json_encode($skippedMembers, JSON_PARTIAL_OUTPUT_ON_ERROR), LOG_INFO);

        $this->log("Cron Ended assignMembership", LOG_INFO);
    }

    public function addCreditCard()
    {
        $this->log("~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~", LOG_INFO);
        $this->log("Cron Started assignMembership", LOG_INFO);
        App::import('Controller', 'Users', LOG_INFO);

        $dir = ROOT . '/vendor/import_folder/TODO/';
        $client_csv = new File($dir . 'credit_cards.csv');
        $rows = preg_split('&\r\n|\r|\n&', $client_csv->read());

        $branch_id = 'TODO';
        $namespace = 'TODO';

        $successMembers = [];
        $failMembers = [];
        $skippedMembers = [];

        foreach ($rows as $row) {
            $field = explode(",", $row);

            if ($field != null && is_array($field) && !empty($field['0']) && !empty($field['1'])) {
                $email = trim(strtolower($field['1']));
                $stripeCustomerId = trim($field['0']);

                $conditions = [
                    'namespace' => $namespace,
                    'type' => 'MEMBER',
                    'active' => true,
                    'email' => $email,
                    'stripe_customer_id' => null,
                    '$or' => [
                        [
                            '$and' => [
                                [
                                    'origin_branch_id' => ['$exists' => true],
                                    'origin_branch_id' => $branch_id
                                ]
                            ]
                        ],
                        [
                            '$and' => [
                                [
                                    'origin_branch_id' => ['$exists' => false],
                                    'branch_id' => $branch_id
                                ]
                            ]
                        ]
                    ]
                ];

                $user = $this->User->find('first', ['conditions' => $conditions]);

                $this->log('$email => ' . json_encode($email, JSON_PARTIAL_OUTPUT_ON_ERROR), LOG_INFO);

                if ($user != null && !empty($user)) {
                    $user['User']['stripe_cc_token'] = 'token_xxx';
                    $user['User']['stripe_customer_id'] = $stripeCustomerId;

                    if ($this->User->save($user['User']) == false) {
                        $failMembers[] = [
                            'email => ' => $user['User']['email'],
                            'first name => ' => $user['User']['first_name'],
                            'last name => ' => $user['User']['last_name']
                        ];
                    } else {
                        $successMembers[] = [
                            'email => ' => $user['User']['email'],
                            'first name => ' => $user['User']['first_name'],
                            'last name => ' => $user['User']['last_name']
                        ];
                    }
                } else {
                    $skippedMembers[] = $email;
                }
            }
        }

        $this->log('successMembers => ' . json_encode($successMembers, JSON_PARTIAL_OUTPUT_ON_ERROR), LOG_INFO);
        $this->log('$failMembers => ' . json_encode($failMembers, JSON_PARTIAL_OUTPUT_ON_ERROR), LOG_INFO);
        $this->log('$skippedMembers => ' . json_encode($skippedMembers, JSON_PARTIAL_OUTPUT_ON_ERROR), LOG_INFO);

        $this->log("Cron Ended addCreditCard");
    }

    public function pauseSubscriptions()
    {
        $this->log("~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~", LOG_INFO);
        $this->log("Cron Started pauseSubscriptions");
        App::import('Controller', 'Users');

        $branch_id = 'TODO';
        $namespace = 'TODO';
        $today = 'TODO';
        $expiryDate = 'TODO';
        $pauseUntil = 'TODO';
        $paymentMethod = 'credit_card';

        $successMembers = [];
        $failMembers = [];

        $branch = $this->Branch->findOrFail($branch_id);
        $branchTimeZone = $branch['Branch']['address']['timezone_id'];
        //      $pauseUntil = \Carbon\Carbon::parse($pauseUntil, $branchTimeZone)->getTimestamp();

        $users = $this->User->find(
            'all',
            [
                'conditions' => [
                    'namespace' => $namespace,
                    'type' => 'MEMBER',
                    'active' => true,
                    'membership.subscription' => ['$exists' => true],
                    'membership.expiry_date' => new MongoDate(strtotime($expiryDate)),
                    'stripe_customer_id' => ['$exists' => true],
                    '$or' => [
                        [
                            '$and' => [
                                [
                                    'origin_branch_id' => ['$exists' => true],
                                    'origin_branch_id' => $branch_id
                                ]
                            ]
                        ],
                        [
                            '$and' => [
                                [
                                    'origin_branch_id' => ['$exists' => false],
                                    'branch_id' => $branch_id
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        );

        foreach ($users as $user) {
            if ($user != null && !empty($user) && isset($user['User']['stripe_customer_id']) && !empty($user['User']['stripe_customer_id'])) {
                $successMembers[] = [
                    'email => ' => $user['User']['email'],
                    'first name => ' => $user['User']['first_name'],
                    'last name => ' => $user['User']['last_name']
                ];

                $options = [];
                $memberId = $user['User']['_id'];
                $membership = $this->Membership->findOrFail($user['User']['membership']['_id']);
                $planCode = $user['User']['membership']['plan_code'];
                $options['start_date'] = $pauseUntil;

                try {
                    $response = $this->Membership->buyMembershipProcess($branch, $user, $paymentMethod, $membership, $planCode, $options);

                    if ($response['success']) {
                        $user = $this->User->findById($memberId);
                        $user['User']['membership']['start_date'] = $today;

                        if ($this->User->save($user['User']) == false) {
                            $failMembers[] = [
                                'email => ' => $user['User']['email'],
                                'first name => ' => $user['User']['first_name'],
                                'last name => ' => $user['User']['last_name']
                            ];
                        } else {
                            $successMembers[] = [
                                'email => ' => $user['User']['email'],
                                'first name => ' => $user['User']['first_name'],
                                'last name => ' => $user['User']['last_name']
                            ];
                        }
                    } else {
                        $failMembers[] = [
                            'email => ' => $user['User']['email'],
                            'first name => ' => $user['User']['first_name'],
                            'last name => ' => $user['User']['last_name']
                        ];
                    }
                } catch (Exception $e) {
                    $this->log('Caught exception => ' . json_encode($e->getMessage(), JSON_PARTIAL_OUTPUT_ON_ERROR));
                }
            }
        }
        $this->log('successMembers => ' . json_encode($successMembers, JSON_PARTIAL_OUTPUT_ON_ERROR));
        $this->log('$failMembers => ' . json_encode($failMembers, JSON_PARTIAL_OUTPUT_ON_ERROR));

        $this->log("Cron Ended pauseSubscriptions");
    }

    public function changeTimezone()
    {
        $this->log("~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~", LOG_INFO);
        $this->log("Cron Started changeTimezone");
        App::import('Controller', 'Users');

        $dir = ROOT . '/vendor/import_folder/TODO/';
        $client_csv = new File($dir . 'customers_ids.csv');
        $rows = preg_split('&\r\n|\r|\n&', $client_csv->read());

        $branch_id = 'TODO';
        $namespace = 'TODO';
        $today = 'TODO';
        $pauseUntil = 'TODO';

        $branch = $this->Branch->findOrFail($branch_id);
        $payment_method = 'credit_card';

        $successMembers = [];
        $failMembers = [];
        $skippedMembers = [];

        foreach ($rows as $row) {
            $field = explode(",", $row);

            if ($field != null && is_array($field) && !empty($field['0'])) {
                $stripe_customer_id = trim($field['0']);

                $user = $this->User->find(
                    'first',
                    [
                        'conditions' => [
                            'namespace' => $namespace,
                            'type' => 'MEMBER',
                            'active' => true,
                            'membership.subscription' => ['$exists' => true],
                            'stripe_customer_id' => $stripe_customer_id,
                            '$or' => [
                                [
                                    '$and' => [
                                        [
                                            'origin_branch_id' => ['$exists' => true],
                                            'origin_branch_id' => $branch_id
                                        ]
                                    ]
                                ],
                                [
                                    '$and' => [
                                        [
                                            'origin_branch_id' => ['$exists' => false],
                                            'branch_id' => $branch_id
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                );

                $this->log('$email => ' . json_encode($stripe_customer_id, JSON_PARTIAL_OUTPUT_ON_ERROR));

                if ($user != null && !empty($user) && isset($user['User']['stripe_customer_id']) && !empty($user['User']['stripe_customer_id'])) {
                    $membership = $this->Membership->findOrFail($user['User']['membership']['_id']);
                    $plan_code = $user['User']['membership']['plan_code'];
                    $options['start_date'] = $pauseUntil;

                    try {
                        $response = $this->Membership->buyMembershipProcess($branch, $user, $payment_method, $membership, $plan_code, $options);

                        if ($response['success']) {
                            $user = $this->User->findById($user['User']['_id']);
                            $user['User']['membership']['start_date'] = $today;

                            if ($this->User->save($user['User']) == false) {
                                $failMembers[] = [
                                    'email => ' => $user['User']['email'],
                                    'first name => ' => $user['User']['first_name'],
                                    'last name => ' => $user['User']['last_name']
                                ];
                            } else {
                                $successMembers[] = [
                                    'email => ' => $user['User']['email'],
                                    'first name => ' => $user['User']['first_name'],
                                    'last name => ' => $user['User']['last_name']
                                ];
                            }
                        } else {
                            $this->log('skipped => ' . json_encode($field, JSON_PARTIAL_OUTPUT_ON_ERROR));
                            $failMembers[] = [
                                'email => ' => $user['User']['email'],
                                'first name => ' => $user['User']['first_name'],
                                'last name => ' => $user['User']['last_name']
                            ];
                        }
                    } catch (Exception $e) {
                        $this->log('Caught exception => ' . json_encode($e->getMessage(), JSON_PARTIAL_OUTPUT_ON_ERROR));
                    }
                } else {
                    $skippedMembers[] = $stripe_customer_id;
                }
            }
        }
        $this->log('successMembers => ' . json_encode($successMembers, JSON_PARTIAL_OUTPUT_ON_ERROR));
        $this->log('$failMembers => ' . json_encode($failMembers, JSON_PARTIAL_OUTPUT_ON_ERROR));
        $this->log('$skippedMembers => ' . json_encode($skippedMembers, JSON_PARTIAL_OUTPUT_ON_ERROR));

        $this->log("Cron Ended changeTimezone");
    }
}
