const menaCountries = [
  "EG", // Egypt
  "SA", // Saudi Arabia
  "QA", // Qatar
  "KW", // Kuwait
  "OM", // Oman
  "BH", // Bahrain
  "JO", // Jordan
  "LB", // Lebanon
];

const apsAvailableCountries = menaCountries.map((countryCode) => {
  return {
    country_code: countryCode,
    default_charge_percentage: 0,
    default_fixed_charge: 0,
  };
});

// Adding Amazon Payment Services to providers list
db.payment_providers.insertOne({
  active: false, // Set to true when ready to accept new customers in platform
  available_countries: apsAvailableCountries,
  gateway_id: "19",
  handler_id: "GATEWAY_HANDLER",
  is_restricted: false,
  name: "AMAZON_PAYMENT_SERVICES",
  payment_method_type_id: "CARD",
  registration_fields: [
    {
      id: "merchant_id",
      name: "APS_MERCHANT_ID",
      origin: "INTERNAL",
      type: "STRING",
    },
    {
      id: "access_code",
      name: "APS_ACCESS_CODE",
      origin: "INTERNAL",
      type: "STRING",
    },
    {
      id: "sha_type",
      name: "APS_SHA_TYPE",
      origin: "INTERNAL",
      type: "STRING",
    },
    {
      id: "sha_request_phrase",
      name: "APS_SHA_REQUEST_PHRASE",
      origin: "INTERNAL",
      type: "STRING",
    },
    {
      id: "sha_response_phrase",
      name: "APS_SHA_RESPONSE_PHRASE",
      origin: "INTERNAL",
      type: "STRING",
    },
    {
      id: "payment_page_url",
      name: "APS_PAYMENT_PAGE_URL",
      origin: "HARDCODED",
      type: "STRING",
      default_value: "https://checkout.payfort.com/FortAPI/paymentPage"
    },
    {
      id: "payment_api_url",
      name: "APS_PAYMENT_API_URL",
      origin: "HARDCODED",
      type: "STRING",
      default_value: "https://paymentservices.payfort.com/FortAPI/paymentApi"
    },
    {
      id: "mode",
      name: "APS_GATEWAY_MODE",
      origin: "HARDCODED",
      type: "STRING",
      default_value: "live"
    }
  ],
  registration_flow: { type: "INTERNAL_FORM" },
  tokenization_handler: "AMAZON_PAYMENT_SERVICES",
  verification_flow: null,
});

// Need to remove other countries from the previous card provider to ensure that new customers are onboarding to Amazon Payment Services.
