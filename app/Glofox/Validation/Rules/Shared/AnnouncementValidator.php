<?php

namespace Glofox\Validation\Rules\Shared;

use Illuminate\Validation\Validator;

final class AnnouncementValidator
{

    public function validateRestrictions(string $attribute, $value, array $parameters, Validator $validator): bool
    {
        if (!is_array($value)) {
            return false;
        }

        if (sizeof($value) === 1 && array_key_exists('memberships', $value)) {
            $memberships = $value['memberships'];
            return is_array($memberships) && empty($memberships);
        }

        return sizeof($value) === 0;
    }
}