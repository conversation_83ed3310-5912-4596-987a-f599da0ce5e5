# Redis Locker

This is the documentation for Redis Locker.

- [<PERSON>is Locker](#redis-locker)
  - [Folders Structure](#folders-structure)
  - [Code Architecture](#code-architecture)
    - [Redis keys prefixes definition](#redis-keys-prefixes-definition)
    - [Locker class](#locker-class)
    - [Params class](#params-class)
    - [Service Provider Configuration](#service-provider-configuration)
  - [Locker usage example](#locker-usage-example)
  - [Recommendations](#recommendations)

## Folders Structure

Each domain must contain its own Locker classes. For example, `AppointmentSlots` domain will have their classes into `app/Glofox/Domain/AppointmentSlots/` folder.

There are two basic folders for lockers:

- `Exceptions`: this folder contains all the exceptions of the domain. Locker exceptions must be defined there.
- `Locker`: this folder contains all the Locker classes, except for the `Exceptions`.

## Code Architecture

### Redis keys prefixes definition

All redis keys must be define into a class called `LockerPrefix`. This class must extend from [CachePrefix](/app/Glofox/Infrastructure/Cache/CachePrefix.php) class and should include only the public constant definitions of the different prefixes. Example:

[https://github.com/glofoxinc/api/blob/99388e1ab825baa3267ddc16274950a22abc3e11/app/Glofox/Domain/AppointmentSlots/Locker/LockerPrefix.php#L9-L12](https://github.com/glofoxinc/api/blob/99388e1ab825baa3267ddc16274950a22abc3e11/app/Glofox/Domain/AppointmentSlots/Locker/LockerPrefix.php#L9-L12)

### Locker class

Each Locker class must extend from [app/Glofox/Domain/Locker/Locker.php](/app/Glofox/Domain/Locker/Locker.php), requiring to define the following methods:

- `processName(): string`: it defines the Locker process name, currently used for logging purposes.
- `maxRetries(): int`: it defined the maximum number of retries. [app/Glofox/Domain/Locker/Locker.php](/app/Glofox/Domain/Locker/Locker.php) provides automatically a retry mechanism in case of failures. Setting it to 1 means that locking process will fail at the first attempt.
- `sleepTimeInMicrosec(): int`: it defines the number of microseconds between attempts in case of failures.
- `ttlInSec(): int`: it defines the time to live (TTL) of the lock. After that time the lock will be automatically unlocked.
- `throwNotLockedError(): void`: it throws the not locked exception when the process cannot be locked. It must throw a class that extends from [app/Glofox/Domain/Locker/LockerException.php](/app/Glofox/Domain/Locker/LockerException.php).

Exception example:

[https://github.com/glofoxinc/api/blob/b969023f070e638e8e40225e5e7bd9058d55bf27/app/Glofox/Domain/AppointmentSlots/Exceptions/AppointmentSlotCreationLockerException.php#L7-L15](https://github.com/glofoxinc/api/blob/b969023f070e638e8e40225e5e7bd9058d55bf27/app/Glofox/Domain/AppointmentSlots/Exceptions/AppointmentSlotCreationLockerException.php#L7-L15)

Locker example:

[https://github.com/glofoxinc/api/blob/b969023f070e638e8e40225e5e7bd9058d55bf27/app/Glofox/Domain/AppointmentSlots/Locker/AppointmentSlotCreationLocker.php#L10-L36](https://github.com/glofoxinc/api/blob/b969023f070e638e8e40225e5e7bd9058d55bf27/app/Glofox/Domain/AppointmentSlots/Locker/AppointmentSlotCreationLocker.php#L10-L36)

### Params class

[app/Glofox/Domain/Locker/Locker.php](/app/Glofox/Domain/Locker/Locker.php) expects to receive a [app/Glofox/Domain/Locker/LockerParamsInterface.php](/app/Glofox/Domain/Locker/LockerParamsInterface.php) object for locking and unlocking. This object is just an implementation of that interface, and is in charge of receiving the params that provides context of the Locker, and computes the lock key that will be used for locking and unlocking.

Example of params class:

[https://github.com/glofoxinc/api/blob/b969023f070e638e8e40225e5e7bd9058d55bf27/app/Glofox/Domain/AppointmentSlots/Locker/AppointmentSlotCreationLockerParams.php#L9-L58](https://github.com/glofoxinc/api/blob/b969023f070e638e8e40225e5e7bd9058d55bf27/app/Glofox/Domain/AppointmentSlots/Locker/AppointmentSlotCreationLockerParams.php#L9-L58)

### Service Provider Configuration

The locker class must be defined in the corresponding service provider of its domain, as a singleton and using `RedisCacheClientFactory`.

Example:

[https://github.com/glofoxinc/api/blob/b969023f070e638e8e40225e5e7bd9058d55bf27/app/Glofox/Domain/AppointmentSlots/Providers/AppointmentSlotsServiceProvider.php#L80-L94](https://github.com/glofoxinc/api/blob/b969023f070e638e8e40225e5e7bd9058d55bf27/app/Glofox/Domain/AppointmentSlots/Providers/AppointmentSlotsServiceProvider.php#L80-L94)

## Locker usage example

This is an example on how to use locker in the code.

Lock process:

[https://github.com/glofoxinc/api/blob/b969023f070e638e8e40225e5e7bd9058d55bf27/app/Glofox/Domain/TimeSlots/UseCase/BookTimeSlot.php#L94-L102](https://github.com/glofoxinc/api/blob/b969023f070e638e8e40225e5e7bd9058d55bf27/app/Glofox/Domain/TimeSlots/UseCase/BookTimeSlot.php#L94-L102)

Unlock process:

[https://github.com/glofoxinc/api/blob/b969023f070e638e8e40225e5e7bd9058d55bf27/app/Glofox/Domain/TimeSlots/UseCase/BookTimeSlot.php#L163-L167](https://github.com/glofoxinc/api/blob/b969023f070e638e8e40225e5e7bd9058d55bf27/app/Glofox/Domain/TimeSlots/UseCase/BookTimeSlot.php#L163-L167)

## Recommendations

- Instead of calling multiple time `$locker->unlock($params)`, use the `finally` clause of a `try-catch` to always unlock at the end of the process if possible, regardless if there was an exception or not.
- Use observability and data to setup the right retry values.
