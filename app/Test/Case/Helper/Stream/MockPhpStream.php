<?php

namespace CakeTestCases\Helper\Stream;

class MockPhpStream
{
    private static string $startingData = '';
    private $index = 0;
    private int $length;
    private string $data;

    public static function setStartingData($data)
    {
        self::$startingData = $data;
    }

    public function __construct()
    {
        $this->data = self::$startingData;
        $this->length = strlen(self::$startingData);
    }

    public function stream_open($path, $mode, $options, &$opened_path)
    {
        return true;
    }

    public function stream_close()
    {
    }

    public function stream_stat()
    {
        return [];
    }

    public function stream_flush()
    {
        return true;
    }

    public function stream_read($count)
    {
        $length = min($count, $this->length - $this->index);
        $data = substr($this->data, $this->index);
        $this->index = $this->index + $length;

        return $data;
    }

    public function stream_eof()
    {
        return ($this->index >= $this->length);
    }

    public function stream_write($data)
    {
        return 0;
    }
}
