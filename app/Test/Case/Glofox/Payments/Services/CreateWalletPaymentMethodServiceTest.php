<?php


namespace CakeTestCases\Glofox\Payments\Services;

use Glofox\Domain\Branches\Models\Branch;
use Glofox\Domain\Branches\Repositories\BranchesRepository;
use Glofox\Domain\Events\Search\Expressions\Id;
use Glofox\Domain\PaymentMethods\Events\PaymentMethodWasCreated;
use Glofox\Domain\PaymentMethods\Models\PaymentMethod;
use Glofox\Domain\PaymentMethods\Models\Provider;
use Glofox\Domain\PaymentMethods\Type;
use Glofox\Payments\Services\CreateWalletPaymentMethodService;

\App::import('Test/Case', 'GlofoxTestCase');

class CreateWalletPaymentMethodServiceTest extends \GlofoxTestCase
{

    public $fixtures = [
        'app.branch',
        'app.payment_method',
        'app.payment_provider',
    ];

    /** @var BranchesRepository */
    private $branchesRepository;

    public function setUp()
    {
        parent::setUp();

        $this->branchesRepository = app()->make(BranchesRepository::class);
    }

    public function test_it_skips_when_excluded_corporation()
    {
        // on stripe standard
        /** @var Branch $branch */
        $branch = $this->branchesRepository
            ->addCriteria(new Id('49a7011a05c677b9a916613b'))
            ->firstOrFail();
        $paymentMethod = Paymentmethod::make([
            'branch_id' => $branch->id(),
            'type_id' => Type::CARD,
            'provider' => Provider::make([
                'account_id' => '54321',
                'name' => 'STRIPE_CUSTOM'
            ]),
        ]);
        $branch->set("corporate_id", "corp_Jazzercise");

        /** @var CreateWalletPaymentMethodService $service */
        $service = app()->make(CreateWalletPaymentMethodService::class);
        $service->handle(new PaymentMethodWasCreated($branch, $paymentMethod));
        self::assertEmpty($service->getCreatedWallet());
    }


    public function test_it_skips_when_stripe_standard()
    {
        // on stripe standard
        /** @var Branch $branch */
        $branch = $this->branchesRepository
            ->addCriteria(new Id('49a7011a05c677b9a916613b'))
            ->firstOrFail();
        $paymentMethod = Paymentmethod::make([
            'branch_id' => $branch->id(),
            'type_id' => Type::CARD,
            'provider' => Provider::make([
                'account_id' => '54321',
                'name' => 'STRIPE_STANDARD'
            ]),
        ]);

        /** @var CreateWalletPaymentMethodService $service */
        $service = app()->make(CreateWalletPaymentMethodService::class);
        $service->handle(new PaymentMethodWasCreated($branch, $paymentMethod));
        self::assertEmpty($service->getCreatedWallet());
    }

    public function test_it_skips_when_not_card()
    {
        // on stripe standard
        /** @var Branch $branch */
        $branch = $this->branchesRepository
            ->addCriteria(new Id('5d56d214be51ef060e38409c'))
            ->firstOrFail();
        $paymentMethod = Paymentmethod::make([
            'branch_id' => $branch->id(),
            'type_id' => Type::DIRECT_DEBIT
        ]);

        /** @var CreateWalletPaymentMethodService $service */
        $service = app()->make(CreateWalletPaymentMethodService::class);
        $service->handle(new PaymentMethodWasCreated($branch, $paymentMethod));
        self::assertEmpty($service->getCreatedWallet());
    }

    public function test_it_skips_when_wallet_already_exists()
    {
        // has wallet
        /** @var Branch $branch */
        $branch = $this->branchesRepository
            ->addCriteria(new Id('49a7011a05c677b9a916612b'))
            ->firstOrFail();
        $paymentMethod = Paymentmethod::make([
            'branch_id' => $branch->id(),
            'type_id' => Type::CARD,
            'provider' => Provider::make([
                'account_id' => '12345',
                'name' => 'STRIPE_CUSTOM_US'
            ]),
        ]);

        /** @var CreateWalletPaymentMethodService $service */
        $service = app()->make(CreateWalletPaymentMethodService::class);
        $service->handle(new PaymentMethodWasCreated($branch, $paymentMethod));
        self::assertEmpty($service->getCreatedWallet());
    }

    public function test_it_creates_wallet_payment_method()
    {
        // card only
        /** @var Branch $branch */
        $branch = $this->branchesRepository
            ->addCriteria(new Id('5d6868a48d8540851fe6066c'))
            ->firstOrFail();
        $paymentMethod = Paymentmethod::make([
            'branch_id' => $branch->id(),
            'type_id' => Type::CARD,
            'provider' => Provider::make([
                'account_id' => '12345',
                'name' => 'STRIPE_CUSTOM_US'
            ]),
        ]);

        /** @var CreateWalletPaymentMethodService $service */
        $service = app()->make(CreateWalletPaymentMethodService::class);
        $service->handle(new PaymentMethodWasCreated($branch, $paymentMethod));

        $wallet = $service->getCreatedWallet();
        self::assertNotEmpty($wallet);
        self::assertNotEmpty($wallet->id());

        self::assertEquals('5d6868a48d8540851fe6066c', $wallet->branchId());
        self::assertEquals('WALLET', $wallet->typeId());
        self::assertEquals(false, $wallet->isActive());
        self::assertEquals([
            'id' => '5b2a3489c7805f005e37dcb5',
            'name' => 'WALLET',
            'fixed_charge' => 0.0,
            'charge_percentage' => 0.0,
            'publishable_key' => null,
            'access_token' => null,
            'refreshable_token' => null,
            'tokenization_handler' => null,
            'account_id' => '12345',
        ], $wallet->provider()->toArray());
    }

    public function test_it_creates_wallet_payment_method_premium_plan()
    {
        /** @var Branch $branch */
        $branch = $this->branchesRepository
            ->addCriteria(new Id('5d6868a48d8540851fe6077c'))
            ->firstOrFail();
        $paymentMethod = Paymentmethod::make([
            'branch_id' => $branch->id(),
            'type_id' => Type::CARD,
            'provider' => Provider::make([
                'account_id' => '12345',
                'name' => 'STRIPE_CUSTOM_US'
            ]),
        ]);

        /** @var CreateWalletPaymentMethodService $service */
        $service = app()->make(CreateWalletPaymentMethodService::class);
        $service->handle(new PaymentMethodWasCreated($branch, $paymentMethod));

        $wallet = $service->getCreatedWallet();
        self::assertNotEmpty($wallet);
        self::assertNotEmpty($wallet->id());

        \Mockery::close();
    }
}
