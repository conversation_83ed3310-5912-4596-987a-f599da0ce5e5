<?php

App::uses('AppController', 'Controller');
App::uses('HtmlSanitizer', 'Lib/HtmlSanitizer');
App::uses('HtmlSanitizerFactory', 'Lib/HtmlSanitizer');

use Glofox\Authentication\Exceptions\AuthenticationException;
use Glofox\Domain\ElectronicAgreements\Requests\CreateDocumentVersionRequest;
use Glofox\Domain\ElectronicAgreements\Services\CreateOrUpdateDocumentVersion;
use Glofox\Domain\ElectronicAgreements\Services\DocumentsService;
use Glofox\Domain\ElectronicAgreements\Services\ElectronicAgreementsServiceInterface;
use Glofox\Domain\Terms\Models\Term;
use Glofox\Domain\Users\Models\User;
use Glofox\Domain\Users\Repositories\UsersRepository;
use Glofox\Repositories\Search\Expressions\Shared\Id;
use Illuminate\Auth\Access\AuthorizationException;
use Psr\Log\LoggerInterface;
use Glofox\Infrastructure\Flags\Flagger;
use Glofox\Infrastructure\Flags\Flag;

/**
 * @property JWTComponent $JWT
 */
class TermsConditionsController extends AppController
{
    /**
     * @var string
     */
    public $name = 'TermsConditions';
    /**
     * @var array
     */
    public $components = [
        'Utility',
        'Notification',
        'JWT',
    ];
    /*******************************************************************************************
     *                                API 2.0 New Dashboard
     ******************************************************************************************/

    /**
     * @var ElectronicAgreementsServiceInterface
     */
    private $electronicAgreements;
    /**
     * @var DocumentsService
     */
    private $documentsService;
    /**
     * @var UsersRepository
     */
    private $usersRepository;
    /**
     * @var TermsCondition
     */
    private $termsCondition;
    /** @var LoggerInterface */
    private $logger;

    /**
     * TermsConditionsController constructor.
     *
     * @param null $request
     * @param null $response
     */
    public function __construct($request = null, $response = null)
    {
        parent::__construct($request, $response);
        $this->electronicAgreements = app()->make(ElectronicAgreementsServiceInterface::class);
        $this->documentsService = app()->make(DocumentsService::class);
        $this->termsCondition = app()->make(TermsCondition::class);
        $this->usersRepository = app()->make(UsersRepository::class);
        $this->logger = app()->make(LoggerInterface::class);
        $this->flagger = app()->make(Flagger::class);
    }

    /**
     * Dispatcher for Model.
     *
     * @param string $identifier [description]
     *
     * @return string
     */
    public function dispatcher($identifier = null)
    {
        return parent::dispatch($identifier, $this->termsCondition);
    }

    /**
     * Get terms and conditions by specifying all in branch,.
     *
     * @return array List of terms that match the entry params
     * @example    https://www.glofoxlogin/view
     *
     * @internal   This convention is a little boring for clients, but helps us keep clean backend
     *
     */
    public function view()
    {
        $user = $this->JWT->parseToken();
        $branchId = $user['branch_id'];

        $forUserId = $this->request->query('userId');
        if (!empty($forUserId)) {
            /** @var User $user */
            $user = $this->usersRepository
                ->addCriteria(new Id($forUserId))
                ->firstOrFail();
            $branchId = $user->originBranchId();
        }

        $includes = explode(',', $this->request->query('include') ?? '');
        $result = $this->documentsService->findTermsAndConditions($branchId, null, User::make($user), $includes);

        return json_encode($result, JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    /**
     * Save a term and condition object.
     *
     * @example    https://www.glofoxlogin/termsconditions/upsert Payload: Course object directly
     *
     * @return false|string {Course:object,success:boolean}
     * @throws AuthenticationException
     * @throws UnsuccessfulOperation
     * @throws AuthorizationException
     * @throws \Glofox\Exception
     * @throws JsonException
     */
    public function upsert()
    {
        $this->logger->info('TermsConditionsController::upsert');
        $term = $this->validateRequest();
        $branchId = $term['branch_id'];

        if ($this->shouldSanitizeContent($term)) {
            $this->logger->info('TermsConditionsController::upsert sanitizing');
            $term['content'] = $this->sanitizeHtml($term['content']);
        }

        // if this is supported type then we save the documents on mongo and the EA service as well
        // dual write mechanism so that the supported types are kept in sync between legacy and new systems
        if ($this->electronicAgreements->isSupportedType($term['type'])) {
            // We attempt the dual write but do not fail the process if the EA write fails
            try {
                $createOrUpdateDocumentVersion = app()->make(CreateOrUpdateDocumentVersion::class);
                $createDocumentVersionRequest = CreateDocumentVersionRequest::fromTerm(Term::make($term));
                $createDocumentVersionRequest->setStudioID($branchId);
                $document = $createOrUpdateDocumentVersion->execute($createDocumentVersionRequest);
                $term = $this->updateTermContent($term, $document->latestVersion());
            } catch (Exception $e) {
                $this->logger->error(
                    sprintf(
                        "Error writing document to Electronic Agreements. BranchId: %s. DocumentId: %s",
                        $branchId,
                        $createDocumentVersionRequest->getDocumentID()
                    ),
                    [
                        'trace' => $e->getTrace(),
                    ]
                );

                // we want to rethrow this exception to warn the caller an error has occurred
                throw $e;
            }
        }

        try {
            $existingWaivers = $this->termsCondition->find('all', [
                'conditions' => ['branch_id' => $branchId, 'type' => $term['type']],
            ]);
            $thisIdExists = false;
            foreach ($existingWaivers as $waiver) {
                if ($waiver['TermsCondition']['_id'] === $term['_id']) {
                    $thisIdExists = true;
                    break;
                }
            }
            if (!$thisIdExists && is_countable($existingWaivers) && count($existingWaivers) > 0) {
                $term['_id'] = $existingWaivers[0]['TermsCondition']['_id'];
            }
            if (
                isset($term['_id'])
                && !$thisIdExists
                && (
                    !is_countable($existingWaivers)
                    || count($existingWaivers) === 0
                )
            ) {
                $isValidMongoId = MongoId::isValid($term['_id']);

                if (!$isValidMongoId) {
                    unset($term['_id']);
                }
            }

            // return TC legacy response all the time
            return json_encode($this->termsCondition->save($term), JSON_THROW_ON_ERROR);
        } catch (Exception $e) {
            $this->logger->error(
                sprintf(
                    "Error writing Terms and Conditions. BranchId: %s. DocumentId: %s",
                    $branchId,
                    $createDocumentVersionRequest->getDocumentID()
                ),
                $e->getTrace()
            );

            // we want to rethrow this exception to warn the caller an error has occurred
            throw $e;
        }
    }

    /**
     * @param null $branch_id
     *
     * @return string
     */
    public function findByBranchId($branch_id = null)
    {
        if (!$branch_id) {
            return json_encode(['success' => false, 'message' => __('No Branch Id')], JSON_PARTIAL_OUTPUT_ON_ERROR);
        }

        $result = $this->documentsService->findTermsAndConditions($branch_id);

        return json_encode(['success' => true, 'Terms' => $result], JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    /**
     * @param null $branch_id
     * @param null $term_id
     *
     * @return string
     */
    public function findByBranchIdandTermId($branch_id = null, $term_id = null)
    {
        if (!$branch_id) {
            return json_encode(['success' => false, 'message' => __('No Branch Id')], JSON_PARTIAL_OUTPUT_ON_ERROR);
        }

        if (!$term_id) {
            return json_encode(['success' => false, 'message' => __('No Term Id')], JSON_PARTIAL_OUTPUT_ON_ERROR);
        }

        $result = $this->documentsService->findTermsAndConditions($branch_id, $term_id);

        return json_encode(['success' => true, 'Terms' => $result], JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    private function validateRequest()
    {
        $user = $this->JWT->parseToken();
        $branchId = $user['branch_id'];
        $allowedRoles = [
            UserType::ADMIN,
            UserType::SUPERADMIN,
        ];

        $this->JWT->validateOrFail($allowedRoles, $user['namespace'], $branchId);
        $term = json_decode($this->request->input(), true, 512, JSON_THROW_ON_ERROR);

        if ($term['branch_id'] !== $branchId) {
            throw new AuthorizationException('You are not authorized to perform this action');
        }

        return $term;
    }

    private function shouldSanitizeContent($term): bool
    {
        $isEagreementSanitizeEnabled = $this->flagger->withFlag(Flag::IS_EAGREEMENT_SANITIZE_ENABLED())->hasByBranchId($term['branch_id']);
        return $isEagreementSanitizeEnabled && isset($term['content']) && !empty($term['content']);
    }

    private function updateTermContent($term, $latestVersion) {
        $isEagreementSanitizeEnabled = $this->flagger->withFlag(Flag::IS_EAGREEMENT_SANITIZE_ENABLED())->hasByBranchId($term['branch_id']);
        if ($latestVersion != null && $isEagreementSanitizeEnabled) {
            $content = str_replace('[signature]', '', $latestVersion->template());
            $term['content'] = $content;
        }
        return $term;
    }

    private function sanitizeHtml($html): string
    {
        $sanitizer = HtmlSanitizerFactory::getSanitizer(
            $html,
            HtmlSanitizerFactory::HTML_SANITIZER_TYPE
        );
        if ($sanitizer === null) {
            return $html;
        }

        $sanitizer->execute();

        return $sanitizer->getSanitizedHtml();
    }
}
