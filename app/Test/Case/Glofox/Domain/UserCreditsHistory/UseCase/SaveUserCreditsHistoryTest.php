<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\UserCreditsHistory\UseCase;


use App;
use CakeTestCases\Glofox\Domain\Logger\Traits\MockedLoggerTrait;
use CakeTestCases\Glofox\Domain\Users\Traits\AuthenticateUsersTrait;
use Carbon\Carbon;
use Glofox\Domain\Credits\Models\CreditPack;
use Glofox\Domain\Credits\Models\CreditPackSourceType;
use Glofox\Domain\UserCreditsHistory\Models\UserCreditsHistory;
use Glofox\Domain\UserCreditsHistory\Models\UserCreditsHistoryModification;
use Glofox\Domain\UserCreditsHistory\Repositories\UserCreditsHistoryRepository;
use Glofox\Domain\UserCreditsHistory\UseCase\SaveUserCreditsHistory;
use Glofox\Domain\UserCreditsHistory\UseCase\SaveUserCreditsHistoryParams;
use Glofox\Domain\Users\Search\Expressions\UserId;

App::import('Test/Case', 'GlofoxTestCase');

class SaveUserCreditsHistoryTest extends \GlofoxTestCase
{
    use MockedLoggerTrait;
    use AuthenticateUsersTrait;

    private const DEFAULT_USER_ID = '77197c1cd7b6ddc3a98b4587';
    private const WORKER_USER_ID = '[worker]';
    private const DEFAULT_ADMIN_USER_ID = '59a7011a05c677bda916612a';
    private const DEFAULT_BRANCH_ID = '49a7011a05c677b9a916612a';
    private const DEFAULT_USER_CREDIT_ID = '8888c2ac1453065a5b321bbf';

    public $fixtures = [
        'app.user',
        'app.branch'
    ];

    private UserCreditsHistoryRepository $userCreditsHistoryRepository;
    private \UserCreditsHistory $userCreditsHistoryCakeModel;

    public function setUp(): void
    {
        parent::setUp();
        $this->userCreditsHistoryRepository = app()->make(UserCreditsHistoryRepository::class);
        $this->userCreditsHistoryCakeModel = app()->make(\UserCreditsHistory::class);
    }

    public function tearDown(): void
    {
        $this->userCreditsHistoryCakeModel->deleteAll(['user_id' => self::DEFAULT_USER_ID]);

        \Mockery::close();
        parent::tearDown();
    }

    public function testDoesNothingIfCreditPackIsEmpty(): void
    {
        $useCase = app()->make(SaveUserCreditsHistory::class);
        $useCase->execute(new SaveUserCreditsHistoryParams(null, null));

        $this->assertHistoryNotCreated();
    }

    public function testDoesNothingIfNothingImportantChanged(): void
    {
        $creditPackBefore = CreditPack::make([
            'bookings' => []
        ]);
        $creditPackAfter = CreditPack::make([
            'bookings' => ['123abc']
        ]);

        $useCase = app()->make(SaveUserCreditsHistory::class);
        $useCase->execute(new SaveUserCreditsHistoryParams($creditPackBefore, $creditPackAfter));

        $this->assertHistoryNotCreated();
    }

    public function testCreatedCreditPackHistoryIsSaved(): void
    {
        $creditPackBefore = null;
        $creditPackAfter = CreditPack::make([
            '_id' => self::DEFAULT_USER_CREDIT_ID,
            'user_id' => self::DEFAULT_USER_ID,
        ]);

        $useCase = app()->make(SaveUserCreditsHistory::class);
        $useCase->execute(new SaveUserCreditsHistoryParams($creditPackBefore, $creditPackAfter));

        $historyRecord = $this->fetchHistory();

        $this->assertHistoryRecordIsCorrect($historyRecord);
        $this->assertEquals(self::WORKER_USER_ID, $historyRecord->performedByUserId());

        /** @var UserCreditsHistory $historyRecord */
        $this->assertEquals('CREATED', $historyRecord->action()->getValue());
        $this->assertEmpty($historyRecord->modifications());
        $this->assertEquals($creditPackAfter->toArray(), $historyRecord->state());
    }

    public function testDeletedCreditPackHistoryIsSaved(): void
    {
        $creditPackBefore = CreditPack::make([
            '_id' => self::DEFAULT_USER_CREDIT_ID,
            'user_id' => self::DEFAULT_USER_ID,
        ]);
        $creditPackAfter = null;

        $useCase = app()->make(SaveUserCreditsHistory::class);
        $useCase->execute(new SaveUserCreditsHistoryParams($creditPackBefore, $creditPackAfter));

        $historyRecord = $this->fetchHistory();

        $this->assertHistoryRecordIsCorrect($historyRecord);
        $this->assertEquals(self::WORKER_USER_ID, $historyRecord->performedByUserId());

        $this->assertNull($historyRecord->state());
        $this->assertEquals('DELETED', $historyRecord->action()->getValue());
        $this->assertEmpty($historyRecord->modifications());
    }

    public function testDeletedCreditPackByWorker(): void
    {
        $creditPackBefore = CreditPack::make([
            '_id' => self::DEFAULT_USER_CREDIT_ID,
            'user_id' => self::DEFAULT_USER_ID,
        ]);
        $creditPackAfter = null;

        $useCase = app()->make(SaveUserCreditsHistory::class);
        $useCase->execute(new SaveUserCreditsHistoryParams($creditPackBefore, $creditPackAfter));

        $historyRecord = $this->fetchHistory();

        $this->assertEquals(self::DEFAULT_USER_ID, $historyRecord->userId());
        $this->assertEquals(self::DEFAULT_USER_CREDIT_ID, $historyRecord->userCreditId());
        $this->assertEquals(self::WORKER_USER_ID, $historyRecord->performedByUserId());
        $this->assertNotEmpty($historyRecord->created());

        $this->assertNull($historyRecord->state());
        $this->assertEquals('DELETED', $historyRecord->action()->getValue());
        $this->assertEmpty($historyRecord->modifications());
    }

    public function importantFieldsModifiedDataProvider(): array
    {
        $changeExistingField = [
            'model modification' => [
                ['model' => 'program'],
                ['model' => 'facility'],
                [UserCreditsHistoryModification::byValue('model')]
            ],
            'model_ids modification' => [
                ['model_ids' => null],
                ['model_ids' => [1, 2, 3]],
                [UserCreditsHistoryModification::byValue('model_ids')]
            ],
            'category_id modification' => [
                ['category_id' => null],
                ['category_id' => [4, 5]],
                [UserCreditsHistoryModification::byValue('category_id')]
            ],
            'num_sessions modification' => [
                ['num_sessions' => 1],
                ['num_sessions' => 10],
                [UserCreditsHistoryModification::byValue('num_sessions')]
            ],
            'start_date modification' => [
                ['start_date' => new \MongoDate(Carbon::now()->getTimestamp())],
                ['start_date' => new \MongoDate(Carbon::now()->addDay()->getTimestamp())],
                [UserCreditsHistoryModification::byValue('start_date')]
            ],
            'end_date modification' => [
                ['end_date' => new \MongoDate(Carbon::now()->getTimestamp())],
                ['end_date' => new \MongoDate(Carbon::now()->addDay()->getTimestamp())],
                [UserCreditsHistoryModification::byValue('end_date')]
            ],
            'expiry modification' => [
                ['expiry' => ['interval' => 'month', 'interval_count' => 1]],
                ['expiry' => ['interval' => 'week', 'interval_count' => 3]],
                [UserCreditsHistoryModification::byValue('expiry')]
            ],
            'source modification' => [
                [
                    'source' => [
                        'type' => CreditPackSourceType::ADVANCED_WHEN_BOOKING,
                        'invoice_id' => null,
                        'resource_id' => null,
                    ]
                ],
                [
                    'source' => [
                        'type' => CreditPackSourceType::MEMBERSHIP_RENEWED,
                        'invoice_id' => 'test-invoice-id',
                        'resource_id' => 'test-resource-id',
                    ]
                ],
                [UserCreditsHistoryModification::byValue('source')]
            ],
        ];

        return array_merge($changeExistingField, $this->generateCasesForMissingFields($changeExistingField));
    }

    /** @dataProvider importantFieldsModifiedDataProvider */
    public function testUpdatedCreditPackHistoryIsSaved(array $before, array $after, array $expectedModifications): void
    {
        $before['_id'] = self::DEFAULT_USER_CREDIT_ID;
        $before['user_id'] = self::DEFAULT_USER_ID;

        $after['_id'] = self::DEFAULT_USER_CREDIT_ID;
        $after['user_id'] = self::DEFAULT_USER_ID;

        $creditPackBefore = CreditPack::make($before);
        $creditPackAfter = CreditPack::make($after);

        $this->authenticateAsAdmin(self::DEFAULT_ADMIN_USER_ID);

        $useCase = app()->make(SaveUserCreditsHistory::class);
        $useCase->execute(new SaveUserCreditsHistoryParams($creditPackBefore, $creditPackAfter));

        $historyRecord = $this->fetchHistory();

        $this->assertHistoryRecordIsCorrect($historyRecord);

        /** @var UserCreditsHistory $historyRecord */
        $this->assertEquals('UPDATED', $historyRecord->action()->getValue());
        $this->assertEquals($expectedModifications, $historyRecord->modifications());
        $this->assertEquals($creditPackAfter->toArray(), $historyRecord->state());
    }

    public function testMultipleFieldsModification(): void
    {
        $creditPackBefore = CreditPack::make([
            '_id' => self::DEFAULT_USER_CREDIT_ID,
            'user_id' => self::DEFAULT_USER_ID,
            'model' => 'program',
            'model_ids' => [1],
            'start_date' => new \MongoDate(Carbon::now()->getTimestamp()),
            'end_date' => new \MongoDate(Carbon::now()->addDay()->getTimestamp()),
        ]);
        $creditPackAfter = CreditPack::make([
            '_id' => self::DEFAULT_USER_CREDIT_ID,
            'user_id' => self::DEFAULT_USER_ID,
            'model' => 'appointment',
            'model_ids' => [2, 3],
            'start_date' => new \MongoDate(Carbon::now()->addDay()->getTimestamp()),
            'end_date' => null,
        ]);

        $expectedModifications = [
            UserCreditsHistoryModification::byValue('model'),
            UserCreditsHistoryModification::byValue('model_ids'),
            UserCreditsHistoryModification::byValue('start_date'),
            UserCreditsHistoryModification::byValue('end_date'),
        ];

        $useCase = app()->make(SaveUserCreditsHistory::class);
        $useCase->execute(new SaveUserCreditsHistoryParams($creditPackBefore, $creditPackAfter));

        $historyRecord = $this->fetchHistory();

        $this->assertHistoryRecordIsCorrect($historyRecord);

        /** @var UserCreditsHistory $historyRecord */
        $this->assertEquals('UPDATED', $historyRecord->action()->getValue());
        $this->assertEquals($expectedModifications, $historyRecord->modifications());
        $this->assertEquals($creditPackAfter->toArray(), $historyRecord->state());
    }

    private function assertHistoryNotCreated(): void
    {
        $this->assertEmpty(
            $this->userCreditsHistoryRepository
                ->addCriteria(new UserId(self::DEFAULT_USER_ID))
                ->find()
        );
    }

    private function fetchHistory(): UserCreditsHistory
    {
        $savedHistory = $this->userCreditsHistoryRepository
            ->addCriteria(new UserId(self::DEFAULT_USER_ID))
            ->find();

        $this->assertCount(1, $savedHistory);

        $historyRecord = current($savedHistory);

        $this->assertInstanceOf(UserCreditsHistory::class, $historyRecord);

        return $historyRecord;
    }

    private function assertHistoryRecordIsCorrect(UserCreditsHistory $historyRecord): void
    {
        $this->assertEquals(self::DEFAULT_USER_ID, $historyRecord->userId());
        $this->assertEquals(self::DEFAULT_USER_CREDIT_ID, $historyRecord->userCreditId());
        $this->assertNotEmpty($historyRecord->created());
    }

    private function generateCasesForMissingFields(array $changeExistingField): array
    {
        $result = [];

        foreach ($changeExistingField as $change) {
            $fieldIsMissingBefore = $change;
            $fieldIsMissingBefore[0] = [];

            $fieldIsMissingAfter = $change;
            $fieldIsMissingAfter[1] = [];

            $result[] = $fieldIsMissingBefore;
            $result[] = $fieldIsMissingAfter;
        }

        return $result;
    }
}
