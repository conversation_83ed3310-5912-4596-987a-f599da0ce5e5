<IfModule mod_rewrite.c>
   RewriteEngine on

   #REDIRECT LEGACY APPS: AS LONG AS THERE ARE MOBILE APPS POINTING TO OPENSHIFT, THE BELOW CODE MUST REMAIN
   RewriteCond %{HTTP_HOST}   ^development-zappy\.rhcloud\.com [NC]
   RewriteRule (.*)$ https://dev.glofox.com/$1 [R=307,L]

   RewriteCond %{HTTP_HOST}   ^staging-zappy\.rhcloud\.com [NC]
   RewriteRule (.*)$ https://demo.glofox.com/$1 [R=307,L]

   RewriteCond %{HTTP_HOST}   ^www\.glofoxlogin\.com [NC]
   RewriteRule (.*)$ https://app.glofox.com/$1 [R=307,L]

   RewriteCond %{HTTP_HOST}   ^platform-zappy\.rhcloud\.com [NC]
   RewriteRule (.*)$ https://app.glofox.com/$1 [R=307,L]

   #CakePHP Webroot standard
   RewriteRule    ^$ app/webroot/    [L]
   RewriteRule    (.*) app/webroot/$1 [L]
</IfModule>

# BEGIN Compress text files
<ifModule mod_deflate.c>
  AddOutputFilterByType DEFLATE text/html text/xml text/css text/plain
  AddOutputFilterByType DEFLATE image/svg+xml application/xhtml+xml application/xml
  AddOutputFilterByType DEFLATE application/rdf+xml application/rss+xml application/atom+xml
  AddOutputFilterByType DEFLATE text/javascript application/javascript application/x-javascript application/json
  AddOutputFilterByType DEFLATE application/x-font-ttf application/x-font-otf
  AddOutputFilterByType DEFLATE font/truetype font/opentype
</ifModule>
# END Compress text files

# BEGIN Expire headers
#<ifModule mod_expires.c>
#  ExpiresActive On
#  ExpiresByType image/x-icon "access plus 2592000 seconds"
#  ExpiresByType image/jpeg "access plus 2592000 seconds"
#  ExpiresByType image/png "access plus 2592000 seconds"
#  ExpiresByType image/gif "access plus 2592000 seconds"
#  ExpiresByType application/x-shockwave-flash "access plus 2592000 seconds"
#  ExpiresByType text/css "access plus 604800 seconds"
#  ExpiresByType text/javascript "access plus 216000 seconds"
#  ExpiresByType application/javascript "access plus 216000 seconds"
#  ExpiresByType application/x-javascript "access plus 216000 seconds"
#  ExpiresByType application/xhtml+xml "access plus 600 seconds"
#  ExpiresDefault "access plus 30 seconds"
#  ExpiresByType text/html "access plus 30 seconds"
#</ifModule>
# END Expire headers

# BEGIN Cache-Control Headers
#<ifModule mod_headers.c>
#	Header set Connection keep-alive
#  <filesMatch "\.(ico|jpe?g|png|gif|swf)$">
#    Header set Cache-Control "public"
#  </filesMatch>
#  <filesMatch "\.(css)$">
#    Header set Cache-Control "public"
#  </filesMatch>
#  <filesMatch "\.(js)$">
#    Header set Cache-Control "private"
#  </filesMatch>
# <filesMatch "\.(x?html?|php)$">
#    Header set Cache-Control "private, must-revalidate"
#  </filesMatch>
#</ifModule>
# END Cache-Control Headers

# BEGIN Turn ETags Off
#FileETag None
# END Turn ETags Off
