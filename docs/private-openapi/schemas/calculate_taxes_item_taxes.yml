type: object
properties:
  applied_taxes:
    items:
      $ref: './calculate_taxes_applied_tax.yml'
    type: array
  net_price:
    type: number
    description: The product price minus total taxes.
    example: 1.6
  product_price:
    type: number
    description: The original price of the product (after discounts).
    example: 1.6
  tax_total:
    type: number
    description: The sum of all the applied taxes.
    example: 0.19
  total_price:
    type: number
    description: The final price after applying the taxes (and discounts).
    example: 1.79