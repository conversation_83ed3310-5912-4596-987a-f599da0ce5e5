<?php

namespace Glofox\Domain\Transactional\Events\Listeners;

use Glofox\Domain\Branches\Repositories\BranchesRepository;
use Glofox\Domain\ElectronicAgreements\Trigger;
use Glofox\Domain\FeatureFlags\Flaggers\EAParentalSignatureFlagger;
use Glofox\Domain\Transactional\Composer as MessageComposer;
use Glofox\Domain\Transactional\Messages\Identifier as MessageIdentifier;
use Glofox\Domain\Transactional\Messages\Variable;
use Glofox\Domain\Transactional\Messages\Variables\Strategies\DocumentNameParams;
use Glofox\Domain\Transactional\Messages\Variables\Strategies\DocumentUrlParams;
use Glofox\Domain\Transactional\Messages\Variables\ValueFetcher;
use Glofox\Domain\Users\Repositories\UsersRepository;
use Glofox\Repositories\Search\Expressions\Shared\Id;
use Illuminate\Support\Collection;
use League\Event\EventInterface;
use Psr\Log\LoggerInterface;

/**
 * Class SendSignatureRequestMessage.
 */
class SendSignatureRequestMessage extends TransactionalListener
{
    /** @var BranchesRepository */
    private $branchesRepository;

    /** @var UsersRepository */
    private $usersRepository;

    /** @var LoggerInterface */
    private $logger;

    /** @var EAParentalSignatureFlagger  */
    private $electronicAgreementParentalSignatureFlagger;

    /**
     * SendSignatureRequestMessage constructor.
     */
    public function __construct()
    {
        parent::__construct();

        $this->branchesRepository = app()->make(BranchesRepository::class);
        $this->usersRepository = app()->make(UsersRepository::class);
        $this->logger = app()->make(LoggerInterface::class);
        $this->electronicAgreementParentalSignatureFlagger = app()->make(EAParentalSignatureFlagger::class);
    }

    /**
     * @return bool|mixed
     *
     * @throws \Glofox\Datasource\Exceptions\InvalidMongoIdException
     */
    public function handle(EventInterface $event)
    {
        /** @var string $branchId */
        $branchId = $event->branchId;
        /** @var string $userId */
        $userId = $event->userId;
        /** @var Trigger $trigger */
        $trigger = $event->trigger;
        /** @var string | null $userMembershipId */
        $userMembershipId = $event->userMembershipId;

        $this->logger->info('Starting SendSignatureRequestMessage', [
            'branchId' => $branchId,
            'userId' => $userId,
            'trigger' => $trigger,
            'userMembershipId' => $userMembershipId,
        ]);

        $branch = $this->branchesRepository->addCriteria(new Id($branchId))->firstOrFail();
        $user = $this->usersRepository->addCriteria(new Id($userId))->firstOrFail();

        if($user->isChild()){
            $isEAParentalSignatureEnabled = $this->electronicAgreementParentalSignatureFlagger->has($branchId);
            if($isEAParentalSignatureEnabled){
                return $this->sendChildAgreementToParent($branch, $trigger, $userMembershipId, $user);
            }
        }
        $legacyBranch = [
            'Branch' => $branch->toArray(),
        ];
        $legacyUser = [
            'User' => $user->toArray(),
        ];

        $message = $this->transactionalMessagesRepository->
        findByBranchAndIdentifierWithDefaults($legacyBranch, MessageIdentifier::EA_SIGNATURE_REQUEST());

        $transactionalMessage = $this->transactionalMessageFactory->create($message);
        if (!$transactionalMessage->isEnabled()) {
            return false;
        }

        $branchNameFetcher = new ValueFetcher(Variable::BRANCH_NAME());
        $memberFirstNameFetcher = new ValueFetcher(Variable::MEMBER_FIRST_NAME());
        $documentUrlFetcher = new ValueFetcher(Variable::DOCUMENT_URL());
        $documentNameFetcher = new ValueFetcher(Variable::DOCUMENT_NAME());

        $variables = Collection::make([
            Variable::BRANCH_NAME()->getName() => $branchNameFetcher->fetch($legacyBranch),
            Variable::MEMBER_FIRST_NAME()->getName() => $memberFirstNameFetcher->fetch($legacyUser),
            Variable::DOCUMENT_NAME()->getName() => $documentNameFetcher->fetch(
                new DocumentNameParams($branchId, $trigger)),
            Variable::DOCUMENT_URL()->getName() => $documentUrlFetcher->fetch(
                new DocumentUrlParams($user, $trigger, $userMembershipId)),
        ]);

        // Apply context to the message.
        $parsedMessage = ( new MessageComposer($transactionalMessage, $variables) )->compose();

        $result = $this->messageDispatcher->withBranch($legacyBranch)
            ->withMessageDefinition($transactionalMessage)
            ->dispatchTo($legacyUser, $parsedMessage);

        return $result;
    }

    private function sendChildAgreementToParent(Collection $branch, $trigger, $userMembershipId, $child) :bool {
        $legacyBranch = [
            'Branch' => $branch->toArray(),
        ];
        $legacyChildUser = [
            'User' => $child->toArray(),
        ];
        $legacyParentUser = [
            'User' => $child->parent(),
        ];

        $message = $this->transactionalMessagesRepository->findByBranchAndIdentifierWithDefaults(
            $legacyBranch,
            MessageIdentifier::EA_CHILD_SIGNATURE_REQUEST());

        $transactionalMessage = $this->transactionalMessageFactory->create($message);
        if (!$transactionalMessage->isEnabled()) {
            return false;
        }

        $branchNameFetcher = new ValueFetcher(Variable::BRANCH_NAME());
        $memberFirstNameFetcher = new ValueFetcher(Variable::MEMBER_FIRST_NAME());
        $childFirstNameFetcher = new ValueFetcher(Variable::CHILD_FIRST_NAME());
        $documentUrlFetcher = new ValueFetcher(Variable::DOCUMENT_URL());
        $documentNameFetcher = new ValueFetcher(Variable::DOCUMENT_NAME());

        $variables = Collection::make([
            Variable::BRANCH_NAME()->getName() => $branchNameFetcher->fetch($legacyBranch),
            Variable::MEMBER_FIRST_NAME()->getName() => $memberFirstNameFetcher->fetch($legacyParentUser),
            Variable::CHILD_FIRST_NAME()->getName() => $childFirstNameFetcher->fetch($legacyChildUser),
            Variable::DOCUMENT_NAME()->getName() => $documentNameFetcher->fetch(
                new DocumentNameParams($legacyBranch['Branch']['_id'], $trigger)),
            Variable::DOCUMENT_URL()->getName() => $documentUrlFetcher->fetch(
                new DocumentUrlParams($child, $trigger, $userMembershipId)),
        ]);

        // Apply context to the message.
        $parsedMessage = ( new MessageComposer($transactionalMessage, $variables) )->compose();

        $this->logger->info('SendSignatureRequestMessage - sending childs agreement to parent email', [
            'child user_id' => $legacyParentUser['User']['_id'],
            'parent user_id' => $legacyChildUser['User']['_id'],
        ]);

        return $this->messageDispatcher->withBranch($legacyBranch)
            ->withMessageDefinition($transactionalMessage)
            ->dispatchTo($legacyParentUser, $parsedMessage);
    }
}
