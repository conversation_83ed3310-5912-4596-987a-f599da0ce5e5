<?php

namespace Glofox\Response\Models\Branches\Configuration\Webportal;

use Glofox\Response\BaseModel;
use Glofox\Response\Models\Branches\Configuration\Webportal\Features\EventDetailsModel;
use Glofox\Response\Models\Branches\Configuration\Webportal\Features\FiltersModel;
use Glofox\Response\Models\Branches\Configuration\Webportal\Features\GeneralModel;

/**
 * @SuppressWarnings(PHPMD)
 */
class FeaturesModel extends BaseModel
{
    /**
     * @var EventDetailsModel
     */
    public $event_details;

    /**
     * @var FiltersModel
     */
    public $filters;

    /**
     * @var  GeneralModel
     */
    public $general;

    protected array $allowedFields = [
        'event_details',
        'filters',
        'general',
    ];

    public function __construct(array $data)
    {
        $this->setModelFields($data);
    }
}
