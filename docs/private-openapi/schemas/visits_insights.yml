type: object
properties:
  visits_insights_metrics:
    type: object
    properties:
      branch_id:
        type: string
        pattern: '^[a-f\\d]{24}$'
      total_members:
        type: integer
      total_members_previous_period:
        type: integer
      total_unique_users:
        type: integer
      total_unique_roaming_visits:
        type: integer
      total_unique_roaming_visits_previous_period:
        type: integer
      total_unique_visits:
        type: integer
      total_unique_visits_previous_period:
        type: integer
      total_visits:
        type: integer
      total_visits_previous_period:
        type: integer
      median_visits:
        type: number
      median_visits_previous_period:
        type: number
  visits_details:
    type: object
    properties:
      VisitsDetailsCollection:
        type: array
        items:
          properties:
            member_id:
              type: string
            user_full_name:
              type: string
            user_email:
              type: string
            user_phone:
              type: string
            user_barcode:
              type: string
            membership_name:
              type: string
            membership_plan_name:
              type: string
            membership_status:
              type: string
            discount_name:
              type: string
            is_trial:
              type: boolean
            is_payg:
              type: boolean
            total_visits_in_period:
              type: integer
            total_unique_daily_visits_in_period:
              type: integer
