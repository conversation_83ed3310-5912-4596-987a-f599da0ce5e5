delete:
  tags:
    - Bookings
  summary: Cancel a list of bookings
  operationId: batch-bookings-cancel
  description:
    Use this call to cancel a list of bookings that were created in batch from a starting date onwards.
  security:
    - coreapi_auth: [ ]
  parameters:
    - schema:
        type: string
        pattern: '^[a-f\d]{24}$'
      name: branchId
      in: path
      required: true
      description: The unique identifier of the branch where the bookings were created.
    - schema:
        type: string
        pattern: '^[a-f\d]{24}$'
      name: batchId
      in: path
      required: true
      description: The unique identifier to fetch bookings of the requested batch.
    - schema:
        type: integer
      name: utc-start-time
      in: query
      required: true
      description: UTC timestamp of the starting time to fetch bookings of the requested batch.
  responses:
    '202':
      description: Accepted
    '400':
      description: Bad request
      content:
        application/json:
          schema:
            $ref: '../../schemas/error.yml'
    '404':
      description: Not Found
      content:
        application/json:
          schema:
            type: object
            properties:
              success:
                type: boolean
                default: false
              message:
                type: string
                enum:
                  - Batch id not found
              message_code:
                type: string
                enum:
                  - BATCH_ID_NOT_FOUND

