<?php

namespace Glofox\Providers;

use Glofox\Providers\Contracts\Provider as ProviderContract;
use Illuminate\Translation\Translator;
use Illuminate\Translation\TranslationServiceProvider as IlluminateTranslationServiceProvider;

class TranslationServiceProvider extends IlluminateTranslationServiceProvider implements ProviderContract
{
    public function register()
    {
        $this->registerLoader();

        $this->app->singleton('translator', function ($app) {
            $loader = $app['translation.loader'];

            // When registering the translator component, we'll need to set the default
            // locale as well as the fallback locale. So, we'll grab the application
            // configuration so we can easily get both of these values from there.
            $locale = 'en';

            $trans = new Translator($loader, $locale);

            $trans->setFallback('en');

            return $trans;
        });
    }
}
