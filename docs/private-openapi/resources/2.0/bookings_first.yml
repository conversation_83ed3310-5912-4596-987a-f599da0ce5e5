parameters:
  - in: query
    name: end
    required: true
    schema:
      type: integer
    description: The end date to look for the bookings collection in UTC Timestamp format
  - in: query
    name: start
    required: true
    schema:
      type: integer
    description: The start date to look for the bookings collection in UTC Timestamp format
get:
  tags:
    - Bookings
  summary: List first booking
  operationId: list-bookings-first
  description:
    Use this call to list the first bookings that users make in  a given date range.
  security:
    - coreapi_auth: [ ]
  responses:
    '200':
      description: OK
      content:
        application/json:
          schema:
            $ref: '../../schemas/booking_first.yml'
    '400':
      description: Bad request
      content:
        application/json:
          schema:
            $ref: "../../schemas/error.yml"
