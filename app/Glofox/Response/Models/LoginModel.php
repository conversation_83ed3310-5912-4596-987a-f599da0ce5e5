<?php

namespace Glofox\Response\Models;

use App\Models\GlofoxHub\Response\Models\Register\UserModel;
use Glofox\Response\BaseModel;

/**
 * @SuppressWarnings(PHPMD)
 */
class LoginModel extends BaseModel
{
    /**
     * @var UserModel
     */
    public $user = '';

    /**
     * @var string
     */
    public $token = '';

    /**
     * @var
     */
    public $branch;

    protected array $allowedFields = [
        'user',
        'token',
        'branch',
    ];

    public function __construct(array $data)
    {
        $this->setModelFields($data);
    }
}
