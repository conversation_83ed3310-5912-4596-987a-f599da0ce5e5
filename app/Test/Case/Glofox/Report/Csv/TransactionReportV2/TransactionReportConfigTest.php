<?php

namespace Glofox\Report\Csv\TransactionReportV2;

use Glofox\Domain\PaymentMethods\Type as PaymentMethodType;

\App::import('Test/Case', 'GlofoxTestCase');

class TransactionReportConfigTest extends \GlofoxTestCase
{

    public function test_it_return_true_for_allowed_payment_method()
    {
        $transactionReportConfig = new TransactionReportConfig();

        $this->assertTrue($transactionReportConfig->doesPaymentMethodSupportPayouts("credit_card"));
        $this->assertTrue($transactionReportConfig->doesPaymentMethodSupportPayouts("CREDIT_CARD"));
        $this->assertTrue($transactionReportConfig->doesPaymentMethodSupportPayouts("card"));
        $this->assertTrue($transactionReportConfig->doesPaymentMethodSupportPayouts(PaymentMethodType::CARD));
        $this->assertTrue($transactionReportConfig->doesPaymentMethodSupportPayouts(PaymentMethodType::DIRECT_DEBIT));
        $this->assertFalse($transactionReportConfig->doesPaymentMethodSupportPayouts(PaymentMethodType::BANK_TRANSFER));
    }
}
