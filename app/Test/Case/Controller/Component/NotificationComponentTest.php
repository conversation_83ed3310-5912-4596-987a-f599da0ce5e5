<?php

use CakeTestCases\Glofox\Domain\Logger\Traits\MockedLoggerTrait;
use Glofox\Domain\Branches\Models\Branch;
use Glofox\Domain\Branches\Models\BranchPushConfiguration;
use Glofox\Domain\Notifications\AndroidDeliveryMethod;
use Glofox\Domain\Notifications\Services\NotificationsPublisher;
use Glofox\Domain\Notifications\Services\PushNotificationsServiceInterface;

App::import('Test/Case', 'GlofoxTestCase');
App::uses('ComponentCollection', 'Controller');
App::uses('Component', 'Controller');
App::uses('Controller', 'Controller');
App::uses('NotificationComponent', 'Controller/Component');

class NotificationComponentTest extends GlofoxTestCase
{
    use MockedLoggerTrait;

    public function setUp()
    {
        parent::setUp();

        $this->mockLogger();

        $collection = new ComponentCollection();
        $this->Notification = new NotificationComponent($collection);

        app()->forgetInstance(NotificationsPublisher::class);
        app()->forgetInstance(PushNotificationsServiceInterface::class);
    }

    /**
     * tearDown method.
     */
    public function tearDown()
    {
        \Mockery::close();

        app()->forgetInstance(NotificationsPublisher::class);
        app()->forgetInstance(PushNotificationsServiceInterface::class);
        $this->teardownLogger();

        unset($this->Notification);
        parent::tearDown();
    }

    public function test_it_should_publish_notifications_through_sns()
    {
        $pushConfiguration = \Mockery::mock(BranchPushConfiguration::class);
        $pushConfiguration
            ->shouldReceive('androidDeliveryMethod')
            ->andReturn(AndroidDeliveryMethod::byValue(AndroidDeliveryMethod::FCM));

        $branch = \Mockery::mock(Branch::class);
        $branch
            ->shouldReceive('pushConfiguration')
            ->andReturn($pushConfiguration)
            ->shouldReceive('id')
            ->andReturn('123')
            ->shouldReceive('name')
            ->andReturn('Test Branch');

        app()->bind(NotificationsPublisher::class, function () {
            $publisher = \Mockery::mock(NotificationsPublisher::class);
            $publisher->shouldReceive('sendMobileNotificationInitiatedEvent');

            return $publisher;
        });

        $result = $this->Notification->send('test', ['android-token', 'ios-token'], '_glofox', 'glofox', $branch);

        $this->assertNull($result['err']);
    }

    public function test_it_should_log_when_an_error_occurs_during_publishing()
    {
        $pushConfiguration = \Mockery::mock(BranchPushConfiguration::class);
        $pushConfiguration
            ->shouldReceive('androidDeliveryMethod')
            ->andReturn(AndroidDeliveryMethod::byValue(AndroidDeliveryMethod::FCM));

        $branch = \Mockery::mock(Branch::class);
        $branch
            ->shouldReceive('pushConfiguration')
            ->andReturn($pushConfiguration)
            ->shouldReceive('id')
            ->andReturn('123')
            ->shouldReceive('name')
            ->andReturn('Test Branch');

        app()->bind(NotificationsPublisher::class, function () {
            $publisher = \Mockery::mock(NotificationsPublisher::class);
            $publisher->shouldReceive('sendMobileNotificationInitiatedEvent')
                ->andThrow(new \Exception('something bad happened'));

            return $publisher;
        });

        $result = $this->Notification->send('test', ['android-token', 'ios-token'], '_glofox', 'glofox', $branch);

        $this->assertEquals('something bad happened', $result['err']);
    }

    public function test_it_should_get_status_ok_on_status_endpoint(){
        $testResponse = ['testResponse'=> 'ok'];

        app()->forgetInstance(PushNotificationsServiceInterface::class);

        app()->bind(PushNotificationsServiceInterface::class, function() use ($testResponse){
            $pushNotificationsServiceInterface = \Mockery::mock(PushNotificationsServiceInterface::class);
            $pushNotificationsServiceInterface
                ->shouldReceive('getMessagesStatusesInDateRange')
                ->andReturn($testResponse);
            return $pushNotificationsServiceInterface;
        });

        // need to initialize the NotificationComponent here as only now we have bound the mocks to the DI container
        $this->Notification->initialize(new Controller());

        $result = $this->Notification->status('test', 0, 0, 'glofox');

        $this->assertEquals(['success' => true, 'response' => $testResponse], $result);
    }
}
