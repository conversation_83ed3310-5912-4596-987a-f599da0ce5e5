<?php

namespace Glofox\Validation\Rules\Shared;

use Illuminate\Validation\ValidationException;
use Illuminate\Validation\Validator;

class TimeStampValidator
{
    public function validate(string $attribute, $value, array $parameters, Validator $validator): bool
    {
        $this->validateTimeStamp($attribute, $value, $validator);
        return true;
    }

    public function validateBefore(string $attribute, $value, array $parameters, Validator $validator): bool
    {
        $data = $validator->getData();

        $this->validateTimeStamp($attribute, $value, $validator);
        if (empty($parameters[0])) {
            $validator->getMessageBag()->add($attribute, 'timestamp_before requires one parameter');
            throw new ValidationException($validator);
        }

        $after = $data[$parameters[0]] ?? $parameters[0];

        if (is_string($after)) {
            $after = strtotime($after);
        }

        if (!((int)$value < $after)) {
            return false;
        }

        return true;
    }

    public function validateAfter(string $attribute, $value, array $parameters, Validator $validator): bool
    {
        $data = $validator->getData();

        $this->validateTimeStamp($attribute, $value, $validator);
        if (empty($parameters[0])) {
            $validator->getMessageBag()->add($attribute, 'timestamp_after requires one parameter');
            throw new ValidationException($validator);
        }

        $after = $data[$parameters[0]] ?? $parameters[0];

        if (is_string($after)) {
            $after = strtotime($after);
        }

        if (!((int)$value > $after)) {
            return false;
        }

        return true;
    }

    private function validateTimeStamp(string $attribute, $timestamp, Validator $validator): void
    {
        $isValid = ((string)(int)$timestamp === (string)$timestamp)
            && $timestamp <= PHP_INT_MAX
            && $timestamp >= ~PHP_INT_MAX;

        if (!$isValid) {
            $validator->getMessageBag()->add($attribute, "The $attribute field has an invalid timestamp.");
            throw new ValidationException($validator);
        }
    }
}
