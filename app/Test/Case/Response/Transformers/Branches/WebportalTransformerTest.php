<?php

declare(strict_types=1);

App::import('Test/Case', 'GlofoxTestCase');

use Glofox\Response\Models\Branches\Configuration\Webportal\Features\EventDetailsModel;
use Glofox\Response\Models\Branches\Configuration\Webportal\Features\FiltersModel;
use Glofox\Response\Models\Branches\Configuration\Webportal\Features\GeneralModel;
use Glofox\Response\Models\Branches\Configuration\WebportalModel;
use Glofox\Response\Transformers\Branches\WebportalTransformer;

/**
 * Class WebportalTransformerTest.
 */
class WebportalTransformerTest extends GlofoxTestCase
{
    public $fixtures = [];

    /**
     * @dataProvider webportalModelProvider
     */
    public function testSetModelFromArray($dataArray): void
    {
        $model = new WebportalModel($dataArray);
        $transformer = new WebportalTransformer();
        $transformedModel = $transformer->setModelFromArray($dataArray);
        $this->assertEquals($model, $transformedModel);
    }

    /**
     * @return array
     */
    public function webportalModelProvider()
    {
        return [
            'Check model and transformers return with the same structure' => [
                [
                    'classes_view' => 'day',
                    'colors' => null,
                    'features' => null,
                ],
            ],
        ];
    }

    /**
     * @dataProvider featuresModelProvider
     */
    public function testSetFeaturesModel($dataArray, $featureModel)
    {
        $transformer = new WebportalTransformer();
        $transformedModel = $transformer->setModelFromArray($dataArray);
        $this->assertEquals($featureModel, $transformedModel);
    }

    public function featuresModelProvider(): array
    {
        return [
            'Check model and transformers return with the same structure' => [
                [
                    'filters' => [
                        'classes' => false,
                        'courses' => false,
                        'facilities' => true,
                        'trainers' => false,
                    ],
                    'event_details' => [
                        'facility_name' => false,
                        'level' => false,
                        'price' => false,
                        'size' => false,
                        'total_bookings' => false,
                        'total_waiting_list' => false,
                        'trainer_name' => true,
                    ],
                    'general' => [
                        'app_download_link' => false,
                        'classes' => false,
                        'courses' => true,
                        'facilities' => true,
                        'memberships' => false,
                        'show_past_events' => true,
                        'trainers' => true,
                    ],
                ],
                new WebportalModel(
                    [
                        'filters' => new FiltersModel(
                            [
                                'classes' => false,
                                'courses' => false,
                                'facilities' => true,
                                'trainers' => false,
                            ]
                        ),
                        'event_details' => new EventDetailsModel(
                            [
                                'facility_name' => false,
                                'level' => false,
                                'price' => false,
                                'size' => false,
                                'total_bookings' => false,
                                'total_waiting_list' => false,
                                'trainer_name' => true,
                            ]
                        ),
                        'general' => new GeneralModel(
                            [
                                'app_download_link' => false,
                                'classes' => false,
                                'courses' => true,
                                'facilities' => true,
                                'memberships' => false,
                                'show_past_events' => true,
                                'trainers' => true,
                            ]
                        ),
                    ]
                ),
            ],
        ];
    }
}
