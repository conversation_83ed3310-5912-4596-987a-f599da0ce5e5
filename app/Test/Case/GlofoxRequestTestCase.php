<?php

declare(strict_types=1);

namespace CakeTestCases;

use CakeTestCase;
use CakeTestCases\Glofox\Domain\Logger\Traits\MockedLoggerTrait;
use CakeTestCases\Glofox\Stub\TestStubOverridesServiceProvider;
use Carbon\Carbon;
use Glofox\Application;
use Glofox\Exception;
use Glofox\Request;
use Illuminate\Validation\ValidationException;

abstract class GlofoxRequestTestCase extends CakeTestCase
{
    use MockedLoggerTrait;
    use RebootsApplication;

    public array $fixtures = [];

    protected const UNAUTHORIZED_EXCEPTION = 'This action is unauthorized.';

    abstract protected function requestClassName(): string;

    public function __construct($name = null, array $data = [], $dataName = '')
    {
        parent::__construct($name, $data, $dataName);

        $this->providers = app()->make('config')->get('app.providers');
        $this->providers[] = new TestStubOverridesServiceProvider(app());
    }

    public function setUp()
    {
        $this->rebootApplication();

        Carbon::setTestNow();

        $this->mockLogger();

        parent::setUp();
    }

    public function tearDown()
    {
        parent::tearDown();

        app()->flush();
    }

    public function validateRules(
        GlofoxRequestTestParams $requestParams,
        array $expectedErrors
    ): void
    {
        try {
            $this
                ->getRequest($requestParams)
                ->validate();
            $this->assertEmpty($expectedErrors);
        } catch (ValidationException $exception) {
            $errors = $exception->validator->getMessageBag()->all();
            $this->assertNotEmpty($expectedErrors);
            $this->assertEmpty(array_diff($expectedErrors, $errors));
        }
    }

    public function validateAuthorization(
        GlofoxRequestTestParams $requestParams,
        bool $expectedResult
    ): void
    {
        try {
            $this
                ->getRequest($requestParams)
                ->validate();
            $this->assertTrue($expectedResult);
        } catch (ValidationException $exception) {
            /*
             * As validation method combines payload and authorization validation,
             * we are going to skip payload validation here and focus on authorization only.
             */
        } catch (Exception $exception) {
            $this->assertFalse($expectedResult);
            $this->assertEquals(self::UNAUTHORIZED_EXCEPTION, $exception->getMessage());
        }
    }

    private function getRequest(GlofoxRequestTestParams $requestParams): Request
    {
        /** @var Request $request */
        $request = call_user_func(
            [$this->requestClassName(), 'create'],
            $requestParams->getUri(),
            $requestParams->getMethod(),
            $requestParams->getParameters(),
            $requestParams->getCookies(),
            $requestParams->getFiles(),
            $requestParams->getServer(),
            $requestParams->getContent()
        );
        $request->setContainer(Application::getInstance());

        return $request;
    }
}
