get:
  tags:
    - Reports
  summary: ''
  operationId: visits-insights
  description: This endpoint returns branch level kpis relating to visits and visit details
  x-internal: true
  security:
    - coreapi_auth: [ ]
  parameters:
    - schema:
        type: string
      name: branchId
      in: path
      required: true
    - schema:
        type: integer
      in: query
      required: false
      name: limit
    - schema:
        type: integer
      in: query
      required: false
      name: offset
    - schema:
        type: string
      in: query
      required: false
      name: sortBy
  responses:
    '200':
      description: OK
      content:
        application/json:
          schema:
            $ref: "../../schemas/visits_insights.yml"