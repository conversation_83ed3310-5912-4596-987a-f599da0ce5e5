<?php

use Glofox\Datasource\Exceptions\InvalidMongoIdException;
use Glofox\Domain\Authentication\Auth;
use Glofox\Domain\Branches\Exceptions\InvalidBranchIdException;
use Glofox\Domain\FeatureFlags\FeatureFlagInterface;
use Glofox\Domain\Store\Products\Requests;
use Glofox\Domain\Store\Products\Requests\UpsertProductRequest;
use Glofox\Domain\Store\Products\UseCase\CreateOrUpdateOne;
use Glofox\Domain\Store\Products\UseCase\CreateOrUpdateOneParams;
use Glofox\Domain\Store\Products\Validation\UserAuthorization\CreateOrUpdateValidator;
use Glofox\Domain\Store\Sales\Exceptions\StoreSaleIsAlreadyCollectedException;
use Glofox\Domain\Store\Sales\Exceptions\StoreSaleNotFountException;
use Glofox\Domain\Store\Sales\Models\StoreSalesRequest;
use Glofox\Domain\Store\Sales\Repositories\SalesRepository;
use Glofox\Domain\Store\Sales\Services\ProductSale;
use Glofox\Domain\Store\Sales\UseCase\CollectStoreSales;
use Glofox\Domain\Store\Sales\UseCase\CollectStoreSalesParams;
use Glofox\Domain\Store\Sales\Validation\Validators\StoreSalesCollectedValidator;
use Glofox\Domain\Users\Lock\Locker;
use Glofox\Domain\Users\Lock\Resource;
use Glofox\Infrastructure\Flags\Flag;
use Glofox\Payments\Entities\Invoice\Models\InvoiceEntity;
use Illuminate\Http\JsonResponse;
use Psr\Log\LoggerInterface;

App::uses('AppController', 'Controller');

class ProductsController extends \Glofox\Domain\Store\Products\Http\ProductsController
{
    /**
     * @var array
     */
    public $uses = [
        'User',
        'Branch',
        'Product',
        'StripeCharge',
    ];

    /**
     * @var array
     */
    public $components = [
        'S3',
        'Utility',
        'JWT',
        'Notification',
    ];

    /**
     * Name of the feature.
     *
     * @var [type]
     */
    public $feature_name;

    /**
     * Default image name for items of this type of feature.
     *
     * @var [type]
     */
    public $image_default_name;

    private Locker $userLocker;
    private FeatureFlagInterface $flagger;
    private LoggerInterface $logger;

    public function __construct($request = null, $response = null)
    {
        parent::__construct($request, $response);

        $this->userLocker = app()->make(Locker::class);
        $this->flagger = app()->make(FeatureFlagInterface::class);
        $this->logger = app()->make(LoggerInterface::class);
    }

    /*******************************************************************************************
     *                                API 2.0 New Dashboard
     *****************************************************************************************
     *
     * @param null $identifier
     *
     * @return string
     */

    /*!
       * Dispatcher for Model
       * @param  [type] $identifier [description]
       * @return [type]             [description]
       */
    public function dispatcher($identifier = null)
    {
        return parent::dispatch($identifier, $this->Product);
    }

    /**
     * @param UpsertProductRequest $req
     * @return JsonResponse
     */
    public function upsert(UpsertProductRequest $req): JsonResponse
    {
        app()->make(CreateOrUpdateValidator::class)
            ->validate(Auth::user(), $req->branchId(), $req->namespace(), $req->id());

        $params = new CreateOrUpdateOneParams(
            $req->id(),
            $req->branchId(),
            $req->namespace(),
            $req->name(),
            $req->description(),
            $req->image(),
            $req->isActive(),
            $req->isPrivate(),
            $req->isFeatured(),
            $req->categories(),
            $req->presentations(),
            $req->taxIds()
        );

        $useCase = app()->make(CreateOrUpdateOne::class);
        $result = $useCase->execute($params);

        return response()->json($result);
    }

    /*!
     * Count for total purchased products pending
     * @param      integer                  $page   [description]
     * @param      integer                  $limit  [description]
     * @param      [type]                   $filter [description]
     * @return     [type]                           [description]
     */

    /**
     * @param int $page
     * @param int $limit
     * @param null $filter
     *
     * @return string
     */
    public function purchasecount($page = 1, $limit = 100, $filter = null)
    {
        $user = $this->JWT->parseToken();
        $purchases = $this->StripeCharge->findPurchasesByBranch($user['branch_id'], $page, $limit, $filter);

        return json_encode(is_countable($purchases) ? count($purchases) : 0, JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    /*!
     * Mark as collected
     * TO_DO should be validated that only an admin of that gym can mark
     * @param      [type]                   $code [description]
     * @return     [type]                         [description]
     */

    /**
     * @param $code
     *
     * @return array|string
     * @throws JsonException
     * @throws InvalidMongoIdException
     * @throws InvalidBranchIdException
     * @throws StoreSaleNotFountException
     */
    public function collect($code)
    {
        $user = $this->JWT->parseToken();

        $this->JWT->validateOrFail([
            UserType::ADMIN,
            UserType::SUPERADMIN,
            UserType::RECEPTIONIST,
            UserType::TRAINER,
            UserType::MEMBER,
        ], $user['namespace'], $user['branch_id']);

        $purchase = $this->StripeCharge->findPurchaseByCode($code);

        if (!$purchase) {
            return [
                'success' => false,
                'message' => 'Purchase code invalid, no transaction attached found',
            ];
        }
        $purchase['StripeCharge']['metadata']['collected_date'] = new MongoDate();
        $purchase = $this->StripeCharge->save($purchase);

        /** @var SalesRepository $salesRepository */
        $salesRepository = app()->make(SalesRepository::class);
        $storeSale = $salesRepository->findByPurchaseCode($code);
        if ($storeSale === null) {
            return json_encode($purchase, JSON_THROW_ON_ERROR);
        }

        /** @var CollectStoreSales $useCase */
        $useCase = app()->make(CollectStoreSales::class);
        /** @var StoreSalesCollectedValidator $validator */
        $validator = app()->make(StoreSalesCollectedValidator::class);

        try {
            $validator->validate($storeSale->id());
        } catch (StoreSaleIsAlreadyCollectedException $e) {
            return json_encode($purchase, JSON_THROW_ON_ERROR);
        }

        $params = new CollectStoreSalesParams($storeSale->id(), $storeSale->branch());
        $useCase->execute($params);

        return json_encode($purchase, JSON_THROW_ON_ERROR);
    }

    /*!
     * Search list with params and pagination
     * @param      [type]                   $start  [description]
     * @param      [type]                   $limit  [description]
     * @param      [type]                   $search [description]
     * @return     [type]                           [description]
     */

    /**
     * @param int $page
     * @param int $limit
     * @param null $search
     * @param string $active
     *
     * @return string
     */
    public function listview($page = 1, $limit = 30, $search = null, $active = '1')
    {
        $user = $this->JWT->parseToken();
        $active = '0' == $active ? false : true;
        $params = ['branch_id' => $user['branch_id'], 'active' => $active];
        $this->encodeSearchRegex($params, $search, $this->Product); //<- add to params built Regex for global search
        $page = (int)$page;
        $limit = (int)$limit;
        $result = $this->Product->find(
            'all',
            [
                'conditions' => $params,
                'limit' => $limit,
                'page' => $page,
                'order' => ['name' => 1],
            ]
        );

        return json_encode($result, JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    /*!
     * Count total records in this collection
     * @return     [type]                   [description]
     */

    /**
     * @param string $active
     *
     * @return string
     */
    public function count($active = '1')
    {
        $user = $this->JWT->parseToken();
        $active = '0' == $active ? false : true;
        $params = ['branch_id' => $user['branch_id'], 'active' => $active];
        $result = $this->Product->find('count', ['conditions' => $params]);

        return json_encode($result, JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    /**
     * Filter to apply.
     */
    public function beforeFilter()
    {
        parent::beforeFilter();
        $this->feature_name = 'products';
        $this->image_default_name = 'default.png';
    }

    /*******************************************************************************************
     *                            METHODS USED IN PHP RELATED VIEWS
     ******************************************************************************************/

    /**
     * Display the products view.
     */
    public function index()
    {
        $this->layout = 'dashboard';
        $this->render('index');
    }

    /*******************************************************************************************
     *                                      REST API
     ******************************************************************************************/

    /**
     * Find all the products assossiated to a branch.
     *
     * @return string [type] [description]
     */
    public function findAll()
    {
        //Get current user session info to obtain the namespace and branch identifier
        $user = $this->JWT->getUser();
        $allowed_roles = ['admin', 'superadmin', 'receptionist'];
        $namespace = $user['namespace'];
        $branch_id = $user['branch_id'];
        $this->JWT->validateOrFail($allowed_roles, $namespace);

        //Get Products info and add missing items to this course (branch and namespace information). Save course
        $products = $this->Product->findByBranch($branch_id);

        return json_encode($products, JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    /**
     * Find by Id.
     *
     * @param $product_id
     *
     * @return string [type] [description]
     */
    public function findById($product_id)
    {
        //Get current user session info to obtain the namespace and branch identifier
        $user = $this->JWT->getUser();
        $allowed_roles = ['admin', 'superadmin', 'receptionist'];
        $this->JWT->validateOrFail($allowed_roles, $user['User']['namespace']);

        //Get Products info and add missing items to this course (branch and namespace information). Save course
        $product = $this->Product->findById($product_id);

        return json_encode($product, JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    /**
     * List products by branch.
     *
     * @param [type] $branch_id [description]
     *
     * @return string [type]            [description]
     */
    public function findByBranchId($branch_id = null)
    {
        $this->response->type('json');
        $this->autoRender = false;

        $results = $this->Product->find(
            'all',
            [
                'conditions' => ['branch_id' => $branch_id],
                'order' => ['name' => 1],
            ]
        );

        return json_encode($results, JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    /**
     * @param $branch_id
     *
     * @return string
     */
    public function findAllByBranchId($branch_id)
    {
        if (empty($branch_id)) {
            return json_encode(['success' => false, 'message' => __('The Branch Id is empty')], JSON_PARTIAL_OUTPUT_ON_ERROR);
        }
        $is_valid_mongoid = $this->Utility->is_valid_mongoid($branch_id);

        if (empty($is_valid_mongoid)) {
            return json_encode(['success' => false, 'message' => __('Invalid Branch Id')], JSON_PARTIAL_OUTPUT_ON_ERROR);
        }

        $result = $this->Product->find(
            'all',
            ['conditions' => ['branch_id' => $branch_id, 'active' => true]]
        );

        if (!empty($result)) {
            foreach ($result as &$result_temp) {
                $result_temp = $result_temp['Product'];
                $result_temp['image'] = $this->getCrudImageUrlOrPlaceholder(
                    $result_temp['namespace'],
                    $result_temp['branch_id'],
                    $result_temp['_id']
                );
            }
        }

        return json_encode(['success' => true, 'products' => $result], JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    /**
     * Find one by product id /product/<product_id>.
     *
     * @param null $product_id
     *
     * @return type
     *
     * @internal param type $id_product
     */
    public function findByProductId($product_id = null)
    {
        $this->response->type('json');
        $this->autoRender = false;

        //Find course
        $product = $this->Product->findById($product_id);
        $response = !empty($product) ? ['success' => true, 'Product' => $product['Product']] :
            ['success' => false, 'message' => 'Invalid product'];

        return json_encode($response, JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    /**
     * @param null $product_id
     * @param null $presentation_id
     * @param null $user_id
     * @param null $price
     * @param string $payment_method
     * @param int $quantity
     * @param bool $offSession
     *
     * @return string|type
     * @throws JsonException
     */
    public function buyAdmin(
        $product_id = null,
        $presentation_id = null,
        $user_id = null,
        $price = null,
        $payment_method = 'credit_card',
        $quantity = 1,
        $offSession = true
    ) {
        // Get current user session info to obtain the namespace and branch identifier
        $user = $this->JWT->getUser();
        $allowed_roles = ['admin', 'superadmin', 'receptionist'];
        $namespace = $user['namespace'] ?? '';
        $branch_id = $user['branch_id'] ?? '';

        $has_access = $this->JWT->validate($allowed_roles, $namespace, $branch_id);

        if (!$has_access['success']) {
            return json_encode($has_access, JSON_PARTIAL_OUTPUT_ON_ERROR);
        }

        $user = $this->User->findById($user_id);

        return $this->buy($product_id, $presentation_id, $user, $price, $payment_method, $quantity, $offSession);
    }

    /**
     * @param null $productId
     * @param null $presentationId
     * @param null $userId
     * @param null $price
     * @param string $paymentMethod
     * @param int $quantity
     * @param bool $offSession
     *
     * @return string
     * @throws JsonException
     * @throws \Glofox\NotFoundException
     */
    public function buy(
        $productId = '',
        $presentationId = null,
        $userId = null,
        $price = null,
        $paymentMethod = 'credit_card',
        $quantity = 1,
        $offSession = true
    ) {
        $this->logger->info(
            '[ProductsBuyCall] ProductsController::buy is called with params:',
            [
                'productId' => $productId,
                'presentationId' => $presentationId,
                'userId' => $userId,
                'price' => $price,
                'paymentMethod' => $paymentMethod,
                'quantity' => $quantity,
                'offSession' => $offSession,
            ]
        );

        $this->response->type('json');
        $this->autoRender = false;
        $isPost = $this->request->is('post');
        $user = is_array($userId) ? $userId : $this->User->findById($userId);
        $sessionUser = $this->JWT->getUser();

        if ($quantity < 1) {
            return json_encode([
                'success' => false,
                'message' => 'The quantity must be at least 1.',
                'message_code' => 'The quantity must be at least 1.'
            ], JSON_THROW_ON_ERROR);
        }

        $resource = new Resource($user['User']['_id']);
        $lock = $this->userLocker->lock($resource);
        if (!$lock) {
            return json_encode([
                'success' => false,
                'message' => 'A transaction is already in progress',
                'message_code' => 'A_TRANSACTION_IS_ALREADY_IN_PROGRESS'
            ], JSON_THROW_ON_ERROR);
        }

        try {
            if (!$isPost) {
                throw new Exception('Invalid request, must be post');
            }

            $purchasingUserId = is_array($userId) ? $user['User']['_id'] : $userId;
            $payload = $this->getPayload();
            $discounts = isset($payload['discounts']) ? (array)$payload['discounts'] : [];
            $soldByUserId = isset($payload['sold_by_user_id']) ? (string)$payload['sold_by_user_id'] : null;

            $storeSalesRequest = new StoreSalesRequest(
                $productId,
                (int)$presentationId,
                $purchasingUserId ?? '',
                $price,
                $paymentMethod,
                $quantity
            );
            $storeSalesRequest
                ->withLoggedInUserType($sessionUser['type'])
                ->setDiscounts($discounts)
                ->setSoldByUserId($soldByUserId)
                ->setOffSession((bool)$offSession);

            /** @var ProductSale $productSale */
            $productSale = app()->make(ProductSale::class);
            $productSale->finalize($storeSalesRequest);

            $this->userLocker->unlock();

            return json_encode([
                'success' => true,
                'message' => 'CUSTOM_CHARGE_SUCCESS',
                'message_code' => 'CUSTOM_CHARGE_SUCCESS',
                'purchase_code' => $productSale->getStripeChargeData()->getPurchaseCode(),
                'price' => $productSale->getStripeChargeData()->getAmount(),
                'charge_id' => $productSale->getStripeChargeData()->getChargeId(),
                'payment_intents' => $productSale->getTransactionData()->getIntents(),
            ], JSON_THROW_ON_ERROR);
        } catch (Exception $e) {
            $this->userLocker->unlock();

            return json_encode([
                'success' => false,
                'message' => $e->getMessage(),
                'message_code' => $e->getMessage(),
            ], JSON_THROW_ON_ERROR);
        }
    }

    /**
     * Mark a transaction as collected by setting a collected date on it.
     *
     * @param $purchase_code
     *
     * @return array|string [type]                [description]
     *
     * @internal param $ [type] $purchase_code [description]
     */
    public function tagAsCollected($purchase_code)
    {
        // Get Product Purchase information by code
        $purchase = $this->StripeCharge->findPurchaseByCode($purchase_code);
        if (!$purchase) {
            return ['success' => false, 'message' => 'Purchase code invalid, no transaction attached found'];
        }

        $purchase['StripeCharge']['metadata']['collected_date'] = new MongoDate();
        $purchase = $this->StripeCharge->save($purchase);

        return json_encode($purchase, JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    /**
     * Find all product purchases.
     *
     * @param int $page
     * @param null $limit
     * @param null $filter
     *
     * @return string [type] [description]
     */
    public function findPurchases($page = 0, $limit = null, $filter = null)
    {
        //Get current user session info to obtain the namespace and branch identifier
        $user = $this->JWT->getUserOrFail();
        $allowed_roles = ['admin', 'superadmin'];
        $namespace = $user['User']['namespace'];
        $branch_id = $user['User']['branch_id'];
        $this->JWT->validateOrFail($allowed_roles, $namespace);

        //Get Product Purchases still pending for collection (collected_date is null)
        $purchases = $this->StripeCharge->findPurchasesByBranch($branch_id, $page, $limit, $filter);

        return json_encode($purchases, JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    /**
     * [getCrudImageUrlOrPlaceholder description].
     *
     * @param $namespace
     * @param $branch_id
     * @param $id
     * @param int $width [description]
     * @param int $height [description]
     *
     * @return mixed [type] [description]
     */
    public function getCrudImageUrlOrPlaceholder($namespace, $branch_id, $id, $width = 640, $height = 300)
    {
        return $this->S3->getCrudImageUrlOrPlaceholder(
            $namespace,
            $branch_id,
            $this->feature_name,
            $id,
            $this->image_default_name,
            $width,
            $height
        );
    }

    /**
     * [getCrudImageUrl description].
     *
     * @param $namespace
     * @param $branch_id
     * @param $id
     *
     * @return mixed [type] [description]
     *
     * @internal param $ [type]  $namespace [description]
     * @internal param $ [type]  $branch_id [description]
     * @internal param $ [type]  $id        [description]
     * @internal param int $width [description]
     * @internal param int $height [description]
     */
    public function getCrudImageUrl($namespace, $branch_id, $id)
    {
        return $this->S3->getCrudImageUrl($namespace, $branch_id, $this->feature_name, $id, $this->image_default_name);
    }

    /**
     * @throws \Glofox\NotFoundException
     * @throws UnsuccessfulOperation
     */
    public function purchase(Requests\Purchase $request)
    {
        $data = $request->data();
        $quantity = $data->get('quantity');
        $userId = $data->get('by');
        $offSession = $data->get('off_session', true);
        $requestOrigin = $data->get('request_origin', "");

        // Grab the parameter from the route
        $productId = $request->cakeRouteParams()->get('productId');
        $presentationId = (int)$request->cakeRouteParams()->get('presentationId');
        $branchId = $request->getBranchId();
        $paymentMethod = \PaymentMethods::byValue($data->get('payment_method', \PaymentMethods::CREDIT_CARD));
        $totalPrice = $data->get('total_price', null); // It would be null if the token is not from the staff
        $payload = $this->getPayload();
        $discounts = isset($payload['discounts']) ? (array)$payload['discounts'] : [];
        $soldByUserId = isset($payload['sold_by_user_id']) ? (string)$payload['sold_by_user_id'] : null;
        $source = $request->header('x-glofox-source') ?? null;

        $lock = $this->userLocker->lock(new Resource($userId));
        if (!$lock) {
            throw new UnsuccessfulOperation('A_TRANSACTION_IS_ALREADY_IN_PROGRESS');
        }

        // TODO: Temporary work around - this will be removed eventually when we start using cart for member app for product purchases
        // only product purchases from x-glofox-source member app via payment collector v2 will receive a different api response
        // so won't affect integrators or other services
        $shouldUseV2FulfillmentFlow = $this->useFulfillmentService($source, $requestOrigin, $paymentMethod);
         if($shouldUseV2FulfillmentFlow)
         {
            $isUsingV2PaymentCollector = $this->flagger->withFlag(Flag::IS_PAYMENT_COLLECTOR_V2_ENABLED())->hasByBranchId($branchId);
            if($isUsingV2PaymentCollector){
                $this->logger->info('ProductsController::purchase using v2 flow ',[$shouldUseV2FulfillmentFlow, $isUsingV2PaymentCollector]);
                $purchaseCode = $data->get('purchase_code');
                $customerId = $data->get('customer_id');
                $storeSalesRequest = new StoreSalesRequest(
                    $productId,
                    $presentationId,
                    $userId,
                    $totalPrice,
                    $paymentMethod->getValue(),
                    $quantity
                );
                $storeSalesRequest
                    ->withLoggedInUserType(Auth::user()->type())
                    ->setDiscounts($discounts)
                    ->setSoldByUserId($soldByUserId)
                    ->setOffSession((bool)$offSession);
                try{
                    $productSale = app()->make(ProductSale::class);

                    $invoice = $productSale->open($storeSalesRequest, $purchaseCode, $customerId);

                    $this->userLocker->unlock();

                    return response()->json([
                        'success' => true,
                        'message' => 'product validation success and invoice created',
                        'invoice_id' => $invoice->getId(),
                        'invoice_amount' => $invoice->getAmount(),
                        'transaction_group_id' => $invoice->getLatestTrxGroup()->getId()
                    ]);
                } catch (Exception $e) {
                    $this->userLocker->unlock();

                    return response()->json([
                        'success' => false,
                        'message' => $e->getMessage(),
                        'message_code' => $e->getMessage(),
                        ]);
                }
            }
        }

        try {
            $this->logger->info('ProductsController::purchase using v1 flow');
            $storeSalesRequest = new StoreSalesRequest(
                $productId,
                $presentationId,
                $userId,
                $totalPrice,
                $paymentMethod->getValue(),
                $quantity
            );
            $storeSalesRequest
                ->withLoggedInUserType(Auth::user()->type())
                ->setDiscounts($discounts)
                ->setSoldByUserId($soldByUserId)
                ->setOffSession((bool)$offSession);

            /** @var ProductSale $productSale */
            $productSale = app()->make(ProductSale::class);
            $productSale->finalize($storeSalesRequest);
        } catch (Exception $e) {
            $this->userLocker->unlock();

            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
                'message_code' => $e->getMessage(),
            ]);
        }

        $this->userLocker->unlock();

        return response()->json([
            'success' => true,
            'purchase_code' => $productSale->getStripeChargeData()->getPurchaseCode(),
            'message' => 'You have successfully bought the Product',
        ]);
    }

    private function sanitizeAmounts(array $purchases): array
    {
        foreach ($purchases as $key => $purchase) {
            if (array_key_exists('amount', $purchase['StripeCharge']['metadata'])) {
                unset($purchases[$key]['StripeCharge']['metadata']['amount']);
            }
        }

        return $purchases;
    }

    private function useFulfillmentService(?string $source, ?string $requestOrigin, ?string $paymentMethod): bool
    {
        return strtoupper($source) === "MEMBER_APP" &&
        $requestOrigin === "payment_collector_v2" && 
        strtoupper($paymentMethod) != \PaymentMethods::COMPLIMENTARY();
    }
}
