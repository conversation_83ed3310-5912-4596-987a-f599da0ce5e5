<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\Accesses\Http\Responses;

use Glofox\Domain\Accesses\Http\Responses\GetAccessesListResponse;
use Glofox\Domain\Accesses\Http\Transformers\AccessesListTransformer;
use Glofox\Domain\Http\Request\BaseCriteria;

\App::import('Test/Case', 'GlofoxTestCase');

class GetAccessesListResponseTest extends \GlofoxTestCase
{
    public $fixtures = [];

    private BaseCriteria $baseCriteria;
    private AccessesListTransformer $accessesListTransformer;

    public function setUp()
    {
        parent::setUp();

        $this->baseCriteria = \Mockery::mock(BaseCriteria::class);
        $this->accessesListTransformer = \Mockery::mock(AccessesListTransformer::class);
    }

    public function tearDown()
    {
        parent::tearDown();

        \Mockery::close();
    }

    /**
     * @dataProvider testToJsonResponseDataProvider
     * @param array $baseCriteria
     * @param int $totalCount
     * @param array $data
     * @param array $expectedResult
     * @return void
     */
    public function testToJsonResponse(array $baseCriteria, int $totalCount, array $data, array $expectedResult): void
    {
        $this->baseCriteria
            ->expects('page')
            ->andReturn($baseCriteria['page'])
            ->getMock()
            ->expects('limit')
            ->andReturn($baseCriteria['limit']);

        $this->accessesListTransformer
            ->expects('transform')
            ->andReturn($data[0] ?? []);
        app()->instance(AccessesListTransformer::class, $this->accessesListTransformer);

        $getAccessListResponse = new GetAccessesListResponse($this->baseCriteria, $totalCount, $data);
        $result = $getAccessListResponse->toJsonResponse();

        self::assertEquals($expectedResult, $result->getData(true));
    }

    public function testToJsonResponseDataProvider(): array
    {
        return [
            'When data are passed into response and not all are fetched because of the limit, Then hasMore
            need to be calculated as True' => [
                'baseCriteria' =>  ['page' => 1, 'limit' => 2],
                'totalCount' => 10,
                'data' => [
                    ['some-test-index-1' => 'some-test-data-1'],
                ],
                'expectedResult' => [
                    'object' => 'accesses',
                    'page' => 1,
                    'limit' => 2,
                    'hasMore' => true,
                    'totalCount' => 10,
                    'data' => [
                        ['some-test-index-1' => 'some-test-data-1'],
                    ],
                ],
            ],
        ];
    }
}
