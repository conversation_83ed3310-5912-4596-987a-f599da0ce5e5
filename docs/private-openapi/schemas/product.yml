type: object
properties:
  _id:
    type: string
    pattern: '^[a-f\d]{24}$'
  branch_id:
    type: string
    pattern: '^[a-f\d]{24}$'
  namespace:
    type: string
  name:
    type: string
  description:
    type: string
  featured:
    type: boolean
  presentations:
    type: array
    items:
      type: object
  type:
    type: string
    description: Type is product
  image_url:
    type: string
    nullable: true