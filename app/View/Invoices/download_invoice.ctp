<?php
/** @var Glofox\Payments\Entities\Invoice\Models\Invoice $invoice */
$invoice = $data->invoice;

/** @var Glofox\Domain\Branches\Models\Branch $branch */
$branch = $data->branch;

/** @var string $logo */
$logo = $data->logo;
?>
<!DOCTYPE html>
<html lang="en">
<head>

<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">

<style>
    @font-face {
    font-family: 'DejaVu Sans';
    src: url('https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.36/fonts/dejavu-sans/DejaVuSans.ttf');
}

.page {
    width: 656px;
    min-height: 400px;
    margin: 0 auto;
    font-family: 'DejaVu Sans', sans-serif;
}

.table {
    width: 100%;
    color: #1f3a61;
    font-size: 12px;
    border-spacing: 0;
    border-collapse: collapse;
}

.more-line-height {
    line-height: 18px;
}

.td-20 {
    width: 20%;
}

.td-40 {
    width: 40%;
}

.td-60 {
    width: 60%;
}

.td-80 {
    width: 80%;
}

.padding-top-30 {
    padding-top: 40px;
}

.align-right {
    text-align: right;
}

.title {
    width: 100%;
    padding-top: 30px;
    color: #1f3a61;
    font-size: 17px;
    font-weight: 700;
    text-align: center;
}

.table-head {
    color: #5c708b;
    font-size: 10px;
    font-weight: 400;
    padding-bottom: 4px;
    border-bottom: 1px solid rgba(19,25,31,0.1);
}

.table-row.bg {
    background-color: #f8f9fd;
}

.table-cell {
    padding: 8px 0px 9px 0px;
    border-bottom: 1px solid rgba(19,25,31,0.1);
}

.table-cell-bold {
    font-weight: 700;
}

.table-cell-indent-1 {
    padding-left: 30px;
}

.table-cell-indent-2 {
    padding-left: 110px;
}

.table-cell-indent-3 {
    padding-left: 110px;
}

.title-small {
    padding-top: 106px;
    color: #1f3a61;
    font-size: 13px;
    font-weight: 700;
}

.paragraph {
    color: #1f3a61;
    font-size: 10px;
    font-weight: 400;
    line-height: 14px;
}

.img-logo {
    display: block;
    margin-left: 0;
    max-height: 49px;
    max-width: 151px;
}

</style>

</head>
<body>

<div class="page">

    <table class="table">

        <tr>
            <td class="td-40" valign="top">
                <img src="<?php echo $logo; ?>" class="img-logo" />
            </td>
            <td class="td-60 align-right">
                <table class="table">
                    <tr class="align-right">
                        <td class="td-half">
                            <b>Invoice date:</b>
                        </td>
                        <td class="td-half">
                            <?php echo Carbon\Carbon::createFromTimestamp($invoice->date())->format('d F Y'); ?>
                        </td>
                    </tr>
                    <tr class="align-right">
                        <td class="td-half">
                            <b>Service month:</b>
                        </td>
                        <td class="td-half">
                            <?php echo (Carbon\Carbon::now())->month($invoice->month())->year($invoice->year())->format('F Y'); ?>
                        </td>
                    </tr>
                    <tr class="align-right">
                        <td class="td-half">
                            <b>Invoice number:</b>
                        </td>
                        <td class="td-half">
                            <?php echo $invoice->number(); ?>
                        </td>
                    </tr>
                    <tr class="align-right">
                        <td class="td-half">
                            <b>Clients Account Number:</b>
                        </td>
                        <td class="td-half">
                            <?php echo $invoice->accountNumber(); ?>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>

    </table>

    <table class="table">

        <tr>
            <td class="td-40 padding-top-30 more-line-height" valign="top">
                Glofox,<br/>
                23 South William Street,<br/>
                Dublin,<br/>
                D02FK10,<br/>
                Ireland<br/>
                <br/>
                <EMAIL><br/>
                <br/>
            </td>
            <td class="td-20"></td>
            <td class="td-40 padding-top-30 more-line-height" valign="top">
                <?php echo $invoice->address()->owner(); ?>,<br/>
                <?php echo $invoice->address()->name(); ?>,<br/>
                <?php echo $invoice->address()->addressLine1(); ?>,<br/>
                <?php echo $invoice->address()->addressLine2(); ?>,<br/>
                <?php echo $invoice->address()->addressLine3(); ?><br/>
                <br/>
            </td>
        </tr>

    </table>

    <table class="table">
        <tr>
            <td class="td-40 more-line-height" valign="top">
                <b>VAT Number:</b> IE 3240287SH
            </td>
            <td class="td-20"></td>
            <td class="td-40 more-line-height" valign="top">
                <?php
                    if ($branch->address()->isEuCountry() && $branch->taxNumber()) {
                        ?>
                        <b>VAT Number:</b>
                        <?php if (strtoupper($branch->address()->countryCode()) === "ES") { echo "ES"; } ?>
                        <?php echo $branch->taxNumber(); ?>
                        <?php
                    }
                ?>
            </td>
        </tr>
    </table>

    <p class="title">
        INVOICE
    </p>

    <table class="table">

        <tr class="bg">
            <td class="td-80 table-head">
                TRANSFER CURRENCY: <?php echo strtoupper($invoice->currency()); ?>
            </td>
            <td class="td-20 table-head">
                FEE AMOUNT
            </td>
        </tr>

        <tr class="table-row">
            <td class="td-80 table-cell table-cell-indent-1 table-cell-bold">
                Processing fees
            </td>
            <td class="td-20 table-cell table-cell-bold">
                <?php echo $invoice->amount()->processingFees() . ' ' . strtoupper($invoice->currency()); ?>
            </td>
        </tr>

        <tr class="table-row">
            <td class="td-80 table-cell table-cell-indent-1 table-cell-bold">
                Dispute fees
            </td>
            <td class="td-20 table-cell table-cell-bold">
                <?php echo $invoice->amount()->disputeFees() . ' ' . strtoupper($invoice->currency()); ?>
            </td>
        </tr>

        <tr class="table-row">
            <td class="td-80 table-cell table-cell-indent-3 table-cell-bold">
                Glofox fees (Processing + Dispute fees)
            </td>
            <td class="td-20 table-cell table-cell-bold">
                <?php echo $invoice->amount()->processingFees() + $invoice->amount()->disputeFees() . ' ' . strtoupper($invoice->currency()); ?>
            </td>
        </tr>

        <?php
            if ($branch->address()->countryCode() === 'IE') {
        ?>
        <tr class="table-row bg">
            <td class="td-80 table-cell table-cell-indent-3">
                Total VAT
            </td>
            <td class="td-20 table-cell">
                <?php echo $invoice->amount()->totalVat() . ' ' . strtoupper($invoice->currency()); ?>
            </td>
        </tr>
        <?php
            }
        ?>

        <tr class="table-row">
            <td class="td-80 table-cell table-cell-indent-3 table-cell-bold">
                Total
            </td>
            <td class="td-20 table-cell table-cell-bold">
                <?php echo $invoice->amount()->total() . ' ' . strtoupper($invoice->currency()); ?>
            </td>
        </tr>

        <tr class="table-row bg">
            <td class="td-80 table-cell table-cell-indent-3">
                Debited from your balance
            </td>
            <td class="td-20 table-cell">
                - <?php echo $invoice->amount()->debited() . ' ' . strtoupper($invoice->currency()); ?>
            </td>
        </tr>

        <tr class="table-row">
            <td class="td-80 table-cell table-cell-indent-3 table-cell-bold">
                Total due
            </td>
            <td class="td-20 table-cell table-cell-bold">
                <?php echo $invoice->amount()->totalDue() . ' ' . strtoupper($invoice->currency()); ?>
            </td>
        </tr>

    </table>

    <p class="title-small">
        NOTES
    </p>

    <p class="paragraph">
        The total above has been debited from your balance before funds are transferred to your bank account.
        <br/><br/>
        This invoice is subject to the reverse charge mechanism, if applicable. Glofox is registered in Ireland, company number IE538463. Registered Office: 23 South William Street, Dublin, D02FK10.
        <br/><br/>
        * VAT is calculated using cent values, on a per transaction basis, which may result in rounding up or down to the nearest cent.
        <br/><br/>
        Questions? We're here to help. Email <NAME_EMAIL>
    </p>

</div>

</body>
</html>
