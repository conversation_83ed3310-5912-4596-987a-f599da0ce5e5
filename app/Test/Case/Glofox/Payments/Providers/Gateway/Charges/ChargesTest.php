<?php

namespace CakeTestCases\Glofox\Payments\Providers\Gateway\Charges;

use Glofox\Domain\PaymentMethods\Models\PaymentMethod;
use Glofox\Domain\PaymentMethods\Type;
use Glofox\Domain\PaymentProviders\Models\PaymentProvider;
use Glofox\Payments\Entities\Transaction\Internal\FinalizeFlexible;
use Glofox\Payments\Entities\Transaction\Status;
use Glofox\Payments\Providers\Gateway\Charges\Charges;
use Glofox\Payments\Providers\Gateway\GRPC\Connection;
use GRPC\Payments\TransactionService\FinalizeFlexibleChargeReq;
use GRPC\Payments\TransactionService\Transaction as PaymentsTransaction;
use GRPC\Payments\TransactionService\TransactionServiceClient;
use Grpc\UnaryCall;

\App::import('Test/Case', 'GlofoxTestCase');

class ChargesTest extends \GlofoxTestCase
{
    public function test_finalize_flexible_charge(): void
    {
        $connection = \Mockery::mock(Connection::class);
        $connection->transactionService = \Mockery::mock(TransactionServiceClient::class);
        $connection->transactionService->shouldReceive('FinalizeFlexibleCharge')
            ->withArgs(function (FinalizeFlexibleChargeReq $request) {
                $this->assertSame(Type::CASH, $request->getPaymentMethod());
                $this->assertSame(123456, $request->getChargeID());
                $this->assertSame(123123, $request->getDestinationAccountID());
                $this->assertSame(654321, $request->getCustomerAccountID());

                return true;
            })
            ->andReturn(
                \Mockery::mock(UnaryCall::class)
                    ->shouldReceive('wait')
                    ->andReturn([
                        (new PaymentsTransaction())
                            ->setID(2345)
                            ->setStatus(Status::PAYMENT_STATUS_SUCCESS)
                            ->setAmount(1000),
                        (object) ['code' => 0],
                    ])
                    ->getMock()
            );
        app()->instance(Connection::class, $connection);

        $parameters = (new FinalizeFlexible())
            ->setPaymentMethod(Type::CASH)
            ->setChargeId(123456)
            ->setDestinationAccountId(123123)
            ->setCustomerAccountId(654321);

        $charges = new Charges(new PaymentMethod(['type_id' => Type::CASH]), new PaymentProvider());

        $result = $charges->finalizeFlexible($parameters);
        self::assertEquals(1000, $result->amount());

        app()->forgetInstance(Connection::class);
    }
}
