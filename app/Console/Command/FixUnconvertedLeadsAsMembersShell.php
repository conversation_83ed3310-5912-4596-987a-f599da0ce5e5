<?php

declare(strict_types=1);

use Glofox\Datasource\Exceptions\InvalidMongoIdException;
use Glofox\Domain\Leads\Repositories\LeadEventRepository;
use Glofox\Domain\Leads\Search\Expressions\LeadStatus;
use Glofox\Domain\Leads\Search\Filters\IsMember;
use Glofox\Domain\Leads\Search\Filters\IsTrial;
use Glofox\Domain\Leads\Status;
use Glofox\Domain\Memberships\Models\Membership as MembershipModel;
use Glofox\Domain\Memberships\Repositories\MembershipsRepository;
use Glofox\Domain\Users\Models\User as UserModel;
use Glofox\Domain\Users\Repositories\UsersRepository;
use Glofox\Domain\Users\Search\Expressions\Active;
use Glofox\Domain\Users\Search\Expressions\HasTrialMembership;
use Glofox\Domain\Users\Search\Expressions\NoPaygMembership;
use Glofox\Domain\Users\Search\Expressions\Type;
use Glofox\Domain\Users\UserTypesCollection;
use Glofox\Repositories\Search\Expressions\Shared\FieldExists;
use Glofox\Repositories\Search\Expressions\Shared\Id;
use Psr\Log\LoggerInterface;

App::uses('Shell', 'Console');

class FixUnconvertedLeadsAsMembersShell extends AppShell
{
    private const LIMIT = 1000;
    private const LEAD_STATUS_FIELD = 'lead_status';

    private User $userCakeModel;
    private UsersRepository $usersRepo;
    private MembershipsRepository $membershipsRepo;
    private LeadEventRepository $leadEventRepo;
    private LoggerInterface $logger;

    private Status $leadStatusToCheck;

    public function __construct($stdout = null, $stderr = null, $stdin = null)
    {
        parent::__construct($stdout, $stderr, $stdin);

        $this->userCakeModel = \ClassRegistry::init('User');
        $this->usersRepo = app()->make(UsersRepository::class);
        $this->membershipsRepo = app()->make(MembershipsRepository::class);
        $this->leadEventRepo = app()->make(LeadEventRepository::class);
        $this->logger = app()->make(LoggerInterface::class);
    }

    public function getOptionParser()
    {
        $parser = parent::getOptionParser();

        $parser->addOption('limit', [
            'short' => 'l',
            'help' => 'The max number of lead to check',
            'default' => self::LIMIT,
        ]);

        $parser->addOption('status', [
            'short' => 's',
            'help' => 'Lead status to check',
            'default' => Status::LEAD
        ]);

        return $parser;
    }

    public function main(): void
    {
        $this->logger->info('Start LEAD conversion based on membership');

        $limit = (int) $this->param('limit');
        if ($limit < 1) {
            $this->logger->error('Limit should be provided as a positive integer');
            exit(1);
        }

        $this->leadStatusToCheck = Status::byValue($this->param('status'));

        $this->usersRepo
            ->addCriteria(new Active(true))
            ->addCriteria(new Type(UserTypesCollection::make([UserType::MEMBER()])))
            ->addCriteria(new LeadStatus($this->leadStatusToCheck))
            ->addCriteria(new FieldExists('membership._id'))
            ->addCriteria(new NoPaygMembership());

        if ($this->leadStatusToCheck->is(Status::TRIAL())) {
            $this->usersRepo
                ->addCriteria(new HasTrialMembership(false));
        }

        $members = $this->usersRepo
            ->limit($limit)
            ->find();

        /** @var UserModel $member */
        foreach ($members as $member) {
            $membershipId = $member->membership()->id();
            try {
                $membership = $this->membershipsRepo
                    ->addCriteria(new Id($membershipId))
                    ->first();
            } catch (InvalidMongoIdException $e) {
                $this->logger->warning(sprintf('Invalid MongoID %s', $membershipId));
                continue;
            }

            if ($membership === null) {
                $this->logger->info(sprintf('Skipping lead %s since does not have a valid membership', $member->id()));
                continue;
            }

            try {
                $this->convertToTrial($member, $membership);
            } catch (UnsuccessfulOperation $e) {
                $this->logger->error(sprintf('Cannot convert lead %s to trial', $member->id()));
                continue;
            }

            try {
                $this->convertToMember($member, $membership);
            } catch (UnsuccessfulOperation $e) {
                $this->logger->error(sprintf('Cannot convert lead %s to member', $member->id()));
                continue;
            }

            $this->logger->info(sprintf('Lead %s successfully converted', $member->id()));
        }

        $this->logger->info('Finish LEAD conversion based on membership');
    }

    /**
     * @throws UnsuccessfulOperation
     */
    private function convertToTrial(UserModel $member, MembershipModel $membership): void
    {
        if (!$membership->isTrial()) {
            return;
        }

        if ((new IsTrial())->evaluate($member)) {
            return;
        }

        $this->userCakeModel->id = $member->id();
        $leadStatusUpdated = $this->userCakeModel->saveField(
            self::LEAD_STATUS_FIELD,
            Status::TRIAL,
            ['callbacks' => false]
        );

        if (!$leadStatusUpdated) {
            throw new UnsuccessfulOperation();
        }

        $this->leadEventRepo
            ->trackLead($member, Status::TRIAL);
    }

    /**
     * @throws UnsuccessfulOperation
     */
    private function convertToMember(UserModel $member, MembershipModel $membership): void
    {
        if ($membership->isTrial()) {
            return;
        }

        if ((new IsMember())->evaluate($member)) {
            return;
        }

        $this->userCakeModel->id = $member->id();
        $leadStatusUpdated = $this->userCakeModel->saveField(
            self::LEAD_STATUS_FIELD,
            Status::MEMBER,
            ['callbacks' => false]
        );

        if (!$leadStatusUpdated) {
            throw new UnsuccessfulOperation();
        }

        $this->leadEventRepo
            ->trackLead($member, Status::CLIENT);
    }
}
