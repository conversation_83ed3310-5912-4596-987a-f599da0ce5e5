<?php

declare(strict_types=1);

namespace Glofox\Validation\Rules\Shared;

use Glofox\Domain\Memberships\Events\MembershipFloorPricingPurchaseAttempt;
use Glofox\Domain\Memberships\Exceptions\MembershipPlanMinimumPriceValidationException;
use Glofox\Domain\Memberships\Models\Membership;
use Glofox\Domain\Memberships\Repositories\MembershipsRepository;
use Glofox\Domain\Memberships\Validation\PlanMinimumPriceValidator;
use Glofox\Events\EventManager;
use Glofox\NotFoundException;
use Glofox\Repositories\Search\Expressions\Shared\Id;
use Illuminate\Validation\Validator;

final class MembershipPlanValidator
{
    private \Glofox\Domain\Memberships\Repositories\MembershipsRepository $membershipsRepository;

    private \Glofox\Domain\Memberships\Validation\PlanMinimumPriceValidator $minimumPriceValidator;

    private \Glofox\Events\EventManager $eventManager;

    public function __construct(
        MembershipsRepository $membershipsRepository,
        PlanMinimumPriceValidator $minimumPriceValidator,
        EventManager $eventManager
    ) {
        $this->membershipsRepository = $membershipsRepository;
        $this->minimumPriceValidator = $minimumPriceValidator;
        $this->eventManager = $eventManager;
    }

    public function validateMinimumPrice(
        string $attribute,
        $value,
        array $parameters,
        Validator $validator
    ): bool {
        if (null === $value) {
            return true;
        }

        /** @var Membership $membership */
        $membership = $this->membershipsRepository
            ->addCriteria(new Id($parameters[0]))
            ->firstOrFail();

        $price = (float)$value;

        $plan = $membership->planByCode($parameters[1]);
        if (!$plan) {
            throw new NotFoundException('MEMBERSHIP_PLAN_CODE_NOT_FOUND');
        }

        try {
            $this->minimumPriceValidator->validate($plan, $membership->id(), $membership->branchId(), $price);
        } catch (MembershipPlanMinimumPriceValidationException $e) {
            $this->eventManager->emit(
                MembershipFloorPricingPurchaseAttempt::class,
                [
                    $membership,
                    $plan,
                    $price,
                ]
            );

            throw $e;
        }

        return true;
    }
}
