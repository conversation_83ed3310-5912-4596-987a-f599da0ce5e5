<?php


namespace Glofox\Domain\Activities\Events\Listeners;

use Glofox\Domain\Activities\Contexts\ActivityContext;
use Glofox\Domain\Activities\Contexts\HistoricTransactionImportedContext;
use Glofox\Domain\Activities\Type;
use Glofox\Domain\Charges\Type as GlofoxChargeType;
use Glofox\Domain\Users\Repositories\UsersRepository;
use League\Event\EventInterface;

class HistoricTransactionImported extends ActivityListener
{
    /**
     * @var UsersRepository
     */
    private $usersRepository;

    /**
     * HistoricTransactionImported constructor.
     */
    public function __construct()
    {
        $this->usersRepository = app()->make(UsersRepository::class);
        parent::__construct();
    }

    /**
     * @return string
     */
    public function getActivityIdentifier(): string
    {
        return Type::HISTORIC_TRANSACTION_IMPORTED;
    }

    /**
     * @return ActivityContext
     */
    public function getActivityContextBuilder(): ActivityContext
    {
        return new HistoricTransactionImportedContext();
    }

    /**
     * @param EventInterface $event
     *
     * @return bool
     */
    public function handle(EventInterface $event): bool
    {
        if (empty($event->charge)) {
            return true;
        }

        $charge = $event->charge['StripeCharge'] ?? $event->charge;

        $event->user = $this->usersRepository->findById($charge['metadata']['user_id']);

        if (GlofoxChargeType::IMPORTED_HISTORIC_TRANSACTION != $charge['metadata']['glofox_event']) {
            return true;
        }

        return parent::handle($event);
    }
}
