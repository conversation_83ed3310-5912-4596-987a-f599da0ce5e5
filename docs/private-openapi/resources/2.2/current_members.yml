get:
  tags:
    - Reports
  summary: ''
  operationId: current-members
  description: This endpoint returns metrics relating to current members
  x-internal: true
  security:
    - coreapi_auth: [ ]
  parameters:
    - schema:
        type: string
      name: branchId
      in: path
      required: true
    - schema:
        type: string
      name: reportId
      in: query
      required: true
    - schema:
        type: string
      name: membershipStatus
      in: query
      required: false
    - schema:
        type: string
      name: membershipName
      in: query
      required: false
    - schema:
        type: string
      name: membershipPlanName
      in: query
      required: false
    - schema:
        type: integer
      in: query
      required: false
      name: limit
    - schema:
        type: integer
      in: query
      required: false
      name: offset
    - schema:
        type: string
      in: query
      required: false
      name: sortBy
  responses:
    '200':
      description: OK
      content:
        application/json:
          schema:
            $ref: "../../schemas/current_members.yml"