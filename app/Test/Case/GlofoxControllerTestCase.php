<?php

use CakeTestCases\Glofox\Domain\Authentication\Token\TokenGeneration;
use CakeTestCases\Glofox\Domain\Logger\Traits\MockedLoggerTrait;
use CakeTestCases\Glofox\Domain\Users\Traits\FetchUsersTrait;
use CakeTestCases\Glofox\Domain\Users\Traits\MockedCommunicationsPublisher;
use CakeTestCases\Glofox\Domain\Users\Traits\MockedConsentFlagger;
use CakeTestCases\Glofox\Infrastructure\Honeycomb\MockedHoneycombTrackerTrait;
use CakeTestCases\Glofox\Stub\TestStubOverridesServiceProvider;
use CakeTestCases\RebootsApplication;
use Carbon\Carbon;
use Glofox\Domain\Authentication\Auth;
use Glofox\Domain\Authentication\Token\TokenGeneratorDto;
use Glofox\Domain\Authentication\TokenGenerator;
use Glofox\Domain\FeatureFlags\FeatureFlagInterface;
use Glofox\Domain\Users\Models\User as UserModel;
use Glofox\Infrastructure\Flags\Flag;
use Mockery\MockInterface;
use Symfony\Component\HttpFoundation\ParameterBag;

App::import('Test/Case', 'GlofoxTestCase');

App::uses('AppController', 'Controller');

/**
 * Class GlofoxControllerTestCase.
 *
 * @SuppressWarnings(PHPMD)
 */
abstract class GlofoxControllerTestCase extends ControllerTestCase
{
    use MockedLoggerTrait;
    use MockedConsentFlagger;
    use TokenGeneration;
    use FetchUsersTrait;
    use RebootsApplication;
    use MockedCommunicationsPublisher;
    use MockedHoneycombTrackerTrait;

    public $fixtures = [
        'app.branch_configuration_mobile',
        'app.payment_method',
        'app.payment_provider',
        'app.access_control_list',
        'app.branch',
        'app.user',
        'app.client',
        'app.membership',
        'app.interaction',
        'app.event',
        'app.booking',
        'app.program',
        'app.facility',
        'app.role',
        'app.time_slot_pattern',
        'app.time_slot',
        'app.push_notification',
        'app.integration',
        'app.integrator',
    ];
    /**
     * @var string
     */
    protected $currentToken;
    /**
     * @var \Illuminate\Container\Container
     */
    protected $container;
    /**
     * @var CakeResponse
     */
    protected $response;

    public function __construct($name = null, array $data = [], $dataName = '')
    {
        parent::__construct($name, $data, $dataName);

        $this->providers = app()->make('config')->get('app.providers');
        $this->providers[] = new TestStubOverridesServiceProvider(app());
    }

    public function setUp()
    {
        $this->rebootApplication();

        $this->mockLogger();
        $this->setupConsentFlagger();
        $this->setupCommunicationsPublisher();
        $this->mockHoneycombTracker();
        GlofoxTestCase::setTestEnvironment();

        parent::setUp();

        Carbon::setTestNow();
        $this->container = \Glofox\Application::getInstance();
    }

    public function tearDown()
    {
        parent::tearDown();

        unset($_SERVER['HTTP_AUTHORIZATION']);
        app()->flush();
        $this->teardownHoneycombTracker();
    }

    /**
     * Overrides the parent method in order to allow
     * auto-injection for controllers while running unit tests
     * {@inheritdoc}
     */
    public function generate($controller, $mocks = [])
    {
        [$plugin, $controller] = pluginSplit($controller);
        if ($plugin) {
            App::uses($plugin . 'AppController', $plugin . '.Controller');
            $plugin .= '.';
        }
        App::uses($controller . 'Controller', $plugin . 'Controller');

        if (!class_exists($controller . 'Controller')) {
            throw new MissingControllerException([
                'class' => $controller . 'Controller',
                'plugin' => substr($plugin, 0, -1),
            ]);
        }

        ClassRegistry::flush();

        $mocks = array_merge_recursive([
            'methods' => ['_stop'],
            'models' => [],
            'components' => [],
        ], (array)$mocks);

        [$plugin, $name] = pluginSplit($controller);

        /** @var CakeRequest|PHPUnit_Framework_MockObject_MockObject $request */
        $request = $this->getMock('CakeRequest');

        /** @var CakeResponse|PHPUnit_Framework_MockObject_MockObject $response */
        $response = $this->getMock($this->_responseClass, ['_sendHeader']);

        $controllerObj = $this->container->makeWith($name . 'Controller', [
            'request' => $request,
            'response' => $response,
        ]);

        $controllerObj->Components->setController($controllerObj);

        $this->initialiseControllerComponent($controllerObj, $mocks);

        $controllerObj->constructClasses();
        $this->_dirtyController = false;

        $this->controller = $controllerObj;

        return $this->controller;
    }

    public static function createRequestFromMock($get, $post, $server)
    {
        if ('cli-server' === PHP_SAPI) {
            if (array_key_exists('HTTP_CONTENT_LENGTH', $server)) {
                $server['CONTENT_LENGTH'] = $server['HTTP_CONTENT_LENGTH'];
            }
            if (array_key_exists('HTTP_CONTENT_TYPE', $server)) {
                $server['CONTENT_TYPE'] = $server['HTTP_CONTENT_TYPE'];
            }
        }

        $request = new \Illuminate\Http\Request($get, $post, [], [], [], $server);

        if (0 === strpos($request->headers->get('CONTENT_TYPE'), 'application/x-www-form-urlencoded')
            && in_array(strtoupper($request->server->get('REQUEST_METHOD', 'GET')), ['PUT', 'DELETE', 'PATCH'])
        ) {
            parse_str($request->getContent(), $data);
            $request->request = new ParameterBag($data);
        }

        return $request;
    }

    /**
     * @deprecated Use instead {@see \CakeTestCases\Glofox\Domain\Users\Traits\AuthenticateUsersTrait}
     */
    protected function authenticateAsAdmin(): UserModel
    {
        app()->forgetInstance(Auth::class);

        $user = $this->fetchUser('59a7011a05c677bda916612a');
        $token = $this->loginAsUser($user);

        $this->currentToken = $token;
        $_SERVER['HTTP_AUTHORIZATION'] = $this->currentToken;

        return $user;
    }

    /**
     * @deprecated Use instead {@see \CakeTestCases\Glofox\Domain\Users\Traits\AuthenticateUsersTrait}
     */
    protected function authenticateAsSuperAdmin(): void
    {
        app()->forgetInstance(Auth::class);

        $user = $this->fetchUser('59a7011a05c677bda916619a');
        $token = $this->loginAsUser($user);

        $this->currentToken = $token;
        $_SERVER['HTTP_AUTHORIZATION'] = $this->currentToken;
    }

    /**
     * @deprecated Use instead {@see \CakeTestCases\Glofox\Domain\Users\Traits\AuthenticateUsersTrait}
     */
    protected function authenticateAsReception(): void
    {
        app()->forgetInstance(Auth::class);

        $user = $this->fetchUser('59a7011a05c677bda916619b');
        $token = $this->loginAsUser($user);

        $this->currentToken = $token;
        $_SERVER['HTTP_AUTHORIZATION'] = $this->currentToken;
    }

    /**
     * @deprecated Use instead {@see \CakeTestCases\Glofox\Domain\Users\Traits\AuthenticateUsersTrait}
     */
    protected function authenticateAsTrainer(): void
    {
        app()->forgetInstance(Auth::class);

        $user = $this->fetchUser('58568a8fa875ab19530041a7');
        $token = $this->loginAsUser($user);

        $this->currentToken = $token;
        $_SERVER['HTTP_AUTHORIZATION'] = $this->currentToken;
    }

    /**
     * @deprecated Use instead {@see \CakeTestCases\Glofox\Domain\Users\Traits\AuthenticateUsersTrait}
     */
    protected function authenticateAsMember(): void
    {
        app()->forgetInstance(Auth::class);

        $user = $this->fetchUser('59a3011a05c677bda916544a');
        $token = $this->loginAsUser($user);

        $this->currentToken = $token;
        $_SERVER['HTTP_AUTHORIZATION'] = $this->currentToken;
    }

    /**
     * @deprecated Use instead {@see \CakeTestCases\Glofox\Domain\Users\Traits\AuthenticateUsersTrait}
     */
    protected function authenticateAsGuest(): void
    {
        app()->forgetInstance(Auth::class);

        $user = UserModel::make([
            '_id' => 'guest',
            'namespace' => 'glofox',
            'branch_id' => '49a7011a05c677b9a916612a',
            'first_name' => 'Guest',
            'last_name' => 'User',
            'type' => 'GUEST',
            'isSuperAdmin' => false,
        ]);

        $token = $this->loginAsUser($user);

        $this->currentToken = $token;
        $_SERVER['HTTP_AUTHORIZATION'] = $this->currentToken;
    }

    protected function _testAction($url, $options = [])
    {
        $this->vars = $this->result = $this->view = $this->contents = $this->headers = null;

        $options += [
            'data' => [],
            'method' => 'POST',
            'return' => 'result',
        ];

        if (is_array($url)) {
            $url = Router::url($url);
        }

        $restore = ['get' => $_GET, 'post' => $_POST];

        $server = $_SERVER;
        $get = $_GET;
        $post = $_POST;

        $_SERVER['REQUEST_METHOD'] = strtoupper($options['method']);
        $server['REQUEST_METHOD'] = strtoupper($options['method']);

        $contentType = null;

        if (is_string($options['data'])) {
            $contentType = 'application/json';
        }

        $_SERVER['HTTP_CONTENT_TYPE'] = $contentType;
        $_SERVER['CONTENT_TYPE'] = $contentType;

        $server['HTTP_CONTENT_TYPE'] = $contentType;
        $server['CONTENT_TYPE'] = $contentType;

        if (is_array($options['data'])) {
            if ('GET' === strtoupper($options['method'])) {
                $_GET = $options['data'];
                $get = $options['data'];
                $_POST = [];
                $post = [];
            } else {
                $_POST = $options['data'];
                $post = $options['data'];
                $_GET = [];
                $get = [];
            }
        }

        if (false !== strpos($url, '?')) {
            [$url, $query] = explode('?', $url, 2);
            parse_str($query, $queryArgs);
            $_GET += $queryArgs;
            $get += $queryArgs;
        }

        $_SERVER['REQUEST_URI'] = $url;
        $server['REQUEST_URI'] = $url;

        /** @var CakeRequest|PHPUnit_Framework_MockObject_MockObject $request */
        $request = $this->getMock('CakeRequest', ['_readInput']);

        if (is_string($options['data'])) {
            $request->expects($this->any())
                ->method('_readInput')
                ->will($this->returnValue($options['data']));
        }

        $container = \Glofox\Application::getInstance();

        /** @var \Illuminate\Http\Request $capturedRequest */
        $capturedRequest = \Illuminate\Http\Request::capture();

        if ('application/json' == $contentType) {
            $encodedBody = json_decode($options['data'], null, 512, JSON_THROW_ON_ERROR);
            $body = \Illuminate\Support\Collection::make($encodedBody);

            $capturedRequest->setJson($body);
        }

        $container->instance('request', $capturedRequest);

        $dispatcher = new \Glofox\Tests\GlofoxControllerTestDispatcher();
        $dispatcher->withContainer($container);

        foreach (Router::$routes as $route) {
            if ($route instanceof RedirectRoute) {
                $route->response = $this->getMock('CakeResponse', ['send']);
            }
        }
        $dispatcher->loadRoutes = $this->loadRoutes;
        $dispatcher->parseParams(new CakeEvent('ControllerTestCase', $dispatcher, ['request' => $request]));
        if (!isset($request->params['controller']) && Router::currentRoute()) {
            $this->headers = Router::currentRoute()->response->header();

            return null;
        }
        if ($this->_dirtyController) {
            $this->controller = null;
        }

        $plugin = empty($request->params['plugin']) ? '' : Inflector::camelize($request->params['plugin']) . '.';
        if (null === $this->controller && $this->autoMock) {
            $this->generate($plugin . Inflector::camelize($request->params['controller']));
        }
        $params = [];
        if ('result' === $options['return']) {
            $params['return'] = 1;
            $params['bare'] = 1;
            $params['requested'] = 1;
        }
        $dispatcher->testController = $this->controller;
        $dispatcher->response = $this->getMock($this->_responseClass, ['send', '_clearBuffer']);
        $this->result = $dispatcher->dispatch($request, $dispatcher->response, $params);

        $this->response = $dispatcher->response;

        // Clear out any stored requests.
        while (Router::getRequest()) {
            Router::popRequest();
        }

        $this->controller = $dispatcher->testController;
        $this->vars = $this->controller->viewVars;
        $this->contents = $this->controller->response->body();
        if (isset($this->controller->View)) {
            $this->view = $this->controller->View->fetch('__view_no_layout__');
        }
        $this->_dirtyController = true;
        $this->headers = $dispatcher->response->header();

        $_GET = $restore['get'];
        $_POST = $restore['post'];

        return $this->{$options['return']};
    }

    protected function token($type = 'ADMIN')
    {
        /** @var TokenGenerator $tokenGenerator */
        $tokenGenerator = app()->make(TokenGenerator::class);

        $data = [
            '_id' => '59a7011a05c677bda916612a',
            'namespace' => 'glofox',
            'branch_id' => '49a7011a05c677b9a916612a',
            'first_name' => 'Admin',
            'last_name' => 'Istrator',
            'type' => $type,
            'isSuperAdmin' => true,
        ];

        return $tokenGenerator->generate(new TokenGeneratorDto($data));
    }

    /**
     * Mocks {@see FeatureFlagInterface::enableByBranchId()} of {@see FeatureFlagInterface}
     */
    protected function mockFlaggerEnable(Flag $flag, bool $success = true): void
    {
        $flagger = $this->mockFlaggerWithFlag($flag);
        $flagger->shouldReceive('enableByBranchId')->once()
            ->andReturn($success);
        app()->instance(FeatureFlagInterface::class, $flagger);
    }

    /**
     * Mocks {@see FeatureFlagInterface::disableByBranchId()} of {@see FeatureFlagInterface}
     */
    protected function mockFlaggerDisable(Flag $flag, bool $success = true): void
    {
        $flagger = $this->mockFlaggerWithFlag($flag);
        $flagger->shouldReceive('disableByBranchId')->once()
            ->andReturn($success);
        app()->instance(FeatureFlagInterface::class, $flagger);
    }

    /**
     * Mocks {@see FeatureFlagInterface::hasByBranchId()} of {@see FeatureFlagInterface}
     */
    protected function mockFlaggerHas(Flag $flag, bool $success = true): void
    {
        $flagger = $this->mockFlaggerWithFlag($flag);
        $flagger->shouldReceive('hasByBranchId')->once()
            ->andReturn($success);
        app()->instance(FeatureFlagInterface::class, $flagger);
    }

    /**
     * Mocks {@see FeatureFlagInterface::withFlag()} of {@see FeatureFlagInterface}
     */
    private function mockFlaggerWithFlag(Flag $flag): MockInterface
    {
        $flagger = Mockery::mock(FeatureFlagInterface::class);
        $flagger->shouldReceive('withFlag')->with($flag)->andReturnSelf();
        return $flagger;
    }

    /**
     * Part of the generate() method.
     * split in order to decrease the complexity of the original method.
     *
     * @param $controllerObj
     * @param $mocks
     */
    private function initialiseControllerComponent(&$controllerObj, $mocks): void
    {
        $config = ClassRegistry::config('Model');
        foreach ($mocks['models'] as $model => $methods) {
            if (is_string($methods)) {
                $model = $methods;
                $methods = true;
            }
            if (true === $methods) {
                $methods = [];
            }
            $this->getMockForModel($model, $methods, $config);
        }

        foreach ($mocks['components'] as $component => $methods) {
            if (is_string($methods)) {
                $component = $methods;
                $methods = true;
            }
            if (true === $methods) {
                $methods = [];
            }

            [$plugin, $name] = pluginSplit($component, true);
            $componentClass = $name . 'Component';

            App::uses($componentClass, $plugin . 'Controller/Component');
            if (!class_exists($componentClass)) {
                throw new MissingComponentException([
                    'class' => $componentClass,
                ]);
            }

            $config = $controllerObj->components[$component] ?? [];

            /** @var Component|PHPUnit_Framework_MockObject_MockObject $componentObj */
            $componentObj = $this->getMock($componentClass, $methods, [$controllerObj->Components, $config]);

            $controllerObj->Components->set($name, $componentObj);
            $controllerObj->Components->enable($name);
        }
    }
}
