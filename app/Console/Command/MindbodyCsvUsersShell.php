<?php

use Glofox\Console\Mindbody\UsersPaymentsCsvGenerator;
use Glofox\Domain\Branches\Models\Branch;
use Glofox\Domain\Branches\Repositories\BranchesRepository;
use Glofox\Domain\Events\Search\Expressions\Id;

class MindbodyCsvUsersShell extends AppShell
{
    /** @var UsersPaymentsCsvGenerator */
    private $consoleCommand;

    /** @var BranchesRepository */
    private $branchesRepository;

    public function __construct($stdout = null, $stderr = null, $stdin = null)
    {
        parent::__construct($stdout, $stderr, $stdin);
        $this->consoleCommand = app()->make(UsersPaymentsCsvGenerator::class);
        $this->branchesRepository = app()->make(BranchesRepository::class);
    }

    public function main()
    {
        $this->log("MindbodyCsvUsersShell: This job has been deprecated");
        return;
        $branch = $this->fetchBranch($this->args[0]);
        $this->consoleCommand->generate($branch);
    }

    private function fetchBranch(string $identifier): Branch
    {
        return app()->make(BranchesRepository::class)
            ->addCriteria(new Id($identifier))
            ->firstOrFail();
    }
}
