<?php

class TransactionalMessageFixture extends CakeTestFixture
{
    public $import = 'TransactionalMessage';

    public function init()
    {
        $this->records = [
            [
                '_id' => new MongoId('5a4e507ee8549c12e69aeb0b'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'identifier' => \Glofox\Domain\Transactional\Messages\Identifier::WELCOME()->getName(),
                'enabled' => true,
            ],
        ];

        parent::init();
    }
}
