<?php

declare(strict_types=1);

namespace Glofox\Repositories\Search\Expressions\Shared;

use Glofox\Repositories\Filters\Evaluable;
use Glofox\Repositories\Search\Expressions\Conditionable;
use Glofox\Repositories\Search\Expressions\HasConditions;
use Glofox\Repositories\Search\Expressions\Key;
use Webmozart\Expression\Expr;

//Default is not a valid class name hence the suffix
class DefaultField extends Key implements Evaluable, Conditionable
{
    use HasConditions;

    private const FIELD = 'default';

    public function __construct($defaultValue)
    {
        parent::__construct(self::FIELD, Expr::equals($defaultValue));
    }

    public function field(): string
    {
        return self::FIELD;
    }
}
