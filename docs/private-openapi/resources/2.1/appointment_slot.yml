parameters:
  - schema:
      type: string
    name: branchId
    in: path
    required: true
  - schema:
      type: string
    name: appointmentId
    in: path
    required: true
  - schema:
      type: string
    name: slotId
    in: path
    required: true
get:
  tags:
    - Appointment Slots
  summary: ''
  operationId: get-appointment-slot-by-id
  x-internal: true
  security:
    - coreapi_auth: [ ]
  responses:
    '200':
      description: OK
      content:
        application/json:
          schema:
            $ref: '../../schemas/appointment_slot.yml'
    '400':
      description: Bad request
      content:
        application/json:
          schema:
            $ref : "../../schemas/error.yml"
    '404':
      description: Not found
      content:
        application/json:
          schema:
            $ref: "../../schemas/error.yml"
put:
  tags:
    - Appointment Slots
  summary: ''
  operationId: update-appointment-slot-by-id
  description: This endpoint updates an existing appointment slot
  x-internal: true
  security:
    - coreapi_auth: [ ]
  requestBody:
    required: true
    content:
      application/json:
        schema:
          $ref: '../../requests/2.1/update_appointment_slot.yml'
  responses:
    '200':
      description: OK
      content:
        application/json:
          schema:
            $ref: '../../schemas/appointment_slot.yml'
    '400':
      description: Bad request
      content:
        application/json:
          schema:
            $ref: "../../schemas/error.yml"
    '403':
      description: Forbidden
      content:
        application/json:
          schema:
            $ref: "../../schemas/error.yml"
    '404':
      description: Not found
      content:
        application/json:
          schema:
            $ref: "../../schemas/error.yml"
delete:
  tags:
    - Appointment Slots
  summary: ''
  operationId: delete-appointment-slot-by-id
  x-internal: true
  security:
    - coreapi_auth: [ ]
  responses:
    '204':
      description: No Content
    '400':
      description: Bad request
      content:
        application/json:
          schema:
            $ref : "../../schemas/error.yml"
    '403':
      description: Forbidden
      content:
        application/json:
          schema:
            $ref: "../../schemas/error.yml"
    '404':
      description: Not found
      content:
        application/json:
          schema:
            $ref: "../../schemas/error.yml"