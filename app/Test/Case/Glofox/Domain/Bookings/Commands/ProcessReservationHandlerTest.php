<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\Bookings\Commands;

use Carbon\Carbon;
use Glofox\Domain\Bookings\Commands\ProcessReservationHandler;
use Glofox\Domain\Bookings\Commands\ProcessReservationOrBookingCommand;
use Glofox\Domain\Bookings\Events\ReservationWasLinkedToEvent;
use Glofox\Domain\Bookings\Models\Booking;
use Glofox\Domain\Bookings\Repositories\BookingsRepository;
use Glofox\Domain\Bookings\Status;
use Glofox\Domain\Bookings\Validation\Validators\EventHasAvailableSpot;
use Glofox\Domain\Bookings\Validation\Validators\EventNotGeneratedHasAvailableSpot;
use Glofox\Domain\Branches\Repositories\BranchesRepository;
use Glofox\Domain\Events\Models\Event;
use Glofox\Domain\Events\Repositories\EventsRepository;
use Glofox\Domain\Integrations\Origin;
use Glofox\Domain\Programs\Models\Program;
use Glofox\Domain\Users\Models\User;
use Glofox\Events\EventManager;
use Mockery;

\App::import('Test/Case', 'GlofoxTestCase');

class ProcessReservationHandlerTest extends \GlofoxTestCase
{
    public $fixtures = [
        'app.branch',
    ];
    private BranchesRepository $branchesRepository;

    public function setUp(): void
    {
        parent::setUp();

        $this->branchesRepository = app()->make(BranchesRepository::class);
    }

    public function tearDown(): void
    {
        parent::tearDown();

        \Mockery::close();
    }

    public function testBookingsRepositoryCreateIsCalledCorrectlyWhenEventExists(): void
    {
        $branchId = '49a7011a05c677b9a916612a';

        $command = Mockery::mock(ProcessReservationOrBookingCommand::class);
        $command
            ->shouldReceive('getProgram')
            ->andReturn(
                new Program([
                    '_id' => '5b5b28a73355dafa1a354d88',
                    'branch_id' => $branchId
                ])
            )
            ->twice()
            ->shouldReceive('getStartTime')
            ->andReturn(123456)
            ->once()
            ->shouldReceive('getScheduleCode')
            ->andReturn('foobar')
            ->twice()
            ->shouldReceive('getUser')
            ->andReturn(new User())
            ->once()
            ->shouldReceive('getBatchId')
            ->andReturn(null)
            ->once()
            ->shouldReceive('getRequestedByUser')
            ->andReturn(new User())
            ->twice();

        $eventsRepository = Mockery::mock(EventsRepository::class);
        $eventsRepository
            ->shouldReceive('addCriteria')
            ->times(4)
            ->andReturnSelf()
            ->getMock()
            ->shouldReceive('first')
            ->andReturn(
                new Event([
                    '_id' => 'event-id',
                    'branch_id' => $branchId,
                    'time_start' => new \MongoDate(strtotime('2020-02-19T10:00:00Z')),
                    'time_finish' => new \MongoDate(strtotime('2020-02-19T11:00:00Z')),
                ])
            )
            ->once();

        $bookingsRepository = Mockery::mock(BookingsRepository::class);
        $bookingsRepository
            ->shouldReceive('create')
            ->once()
            ->andReturn(
                new Booking([
                    '_id' => 'booking-id',
                    'status' => Status::RESERVED,
                    'event_id' => '5b5b28a73355dafa1a354d99'
                ])
            );

        $eventManager = \Mockery::mock(EventManager::class);
        $eventManager
            ->shouldReceive('emit')
            ->withArgs(function (string $listener, array $params) {
                self::assertSame(ReservationWasLinkedToEvent::class, $listener);
                self::assertInstanceOf(Booking::class, $params[0]);
                self::assertInstanceOf(Event::class, $params[1]);

                return true;
            })
            ->once();

        $eventHasAvailableSpot = \Mockery::mock(EventHasAvailableSpot::class);
        $eventHasAvailableSpot
            ->shouldReceive('validate')
            ->once();

        $notGeneratedEventHasAvailableSpot = \Mockery::mock(EventNotGeneratedHasAvailableSpot::class);
        $notGeneratedEventHasAvailableSpot
            ->shouldNotReceive('validate');

        $handler = $this->getProcessReservationHandler(
            $bookingsRepository,
            $eventsRepository,
            $eventManager,
            $eventHasAvailableSpot,
            $notGeneratedEventHasAvailableSpot
        );

        $result = $handler->handle($command);
        self::assertEquals('booking-id', $result->id());
        self::assertEquals('RESERVED', $result->status());
    }

    public function testBookingsRepositoryCreateIsCalledCorrectlyWhenEventDoesNotExist(): void
    {
        $branchId = '49a7011a05c677b9a916612a';
        $program = new Program([
            '_id' => '5b5b28a73355dafa1a354d88',
            'branch_id' => $branchId,
            'schedule' => [
                [
                    'days_week' => 0,
                    'start_time' => '23:00',
                    'end_time' => '00:00',
                    'active' => true,
                    'facility' => ['52a7011a05c677bda826611b'],
                    'level' => 'advanced',
                    'trainers' => ['59a7011a05c677bda916612c'],
                    'size' => 100,
                    'code' => '5856c03455fd1',
                ],
            ]
        ]);

        $command = Mockery::mock(ProcessReservationOrBookingCommand::class);
        $command
            ->shouldReceive('getProgram')
            ->andReturn($program)
            ->times(3)
            ->shouldReceive('getStartTime')
            ->andReturn(Carbon::today('Europe/Dublin')->setTime(23, 00, 00)->getTimestamp())
            ->twice()
            ->shouldReceive('getScheduleCode')
            ->andReturn('5856c03455fd1')
            ->times(3)
            ->shouldReceive('getUser')
            ->andReturn(new User())
            ->once()
            ->shouldReceive('getBatchId')
            ->andReturn(null)
            ->once()
            ->shouldReceive('getRequestedByUser')
            ->andReturn(new User())
            ->once();

        $eventsRepository = Mockery::mock(EventsRepository::class);
        $eventsRepository
            ->shouldReceive('addCriteria')
            ->times(4)
            ->andReturnSelf()
            ->getMock()
            ->shouldReceive('first')
            ->andReturn(null)
            ->once();

        $bookingsRepository = Mockery::mock(BookingsRepository::class);
        $bookingsRepository
            ->shouldReceive('create')
            ->once()
            ->andReturn(
                new Booking([
                    '_id' => 'booking-id',
                    'status' => Status::RESERVED,
                    'event_id' => null
                ])
            );

        $eventManager = \Mockery::mock(EventManager::class);
        $eventHasAvailableSpot = \Mockery::mock(EventHasAvailableSpot::class);

        $notGeneratedEventHasAvailableSpot = \Mockery::mock(EventNotGeneratedHasAvailableSpot::class);
        $notGeneratedEventHasAvailableSpot
            ->shouldReceive('validate')
            ->once();

        $handler = $this->getProcessReservationHandler(
            $bookingsRepository,
            $eventsRepository,
            $eventManager,
            $eventHasAvailableSpot,
            $notGeneratedEventHasAvailableSpot
        );

        $result = $handler->handle($command);
        self::assertEquals('booking-id', $result->id());
        self::assertEquals('RESERVED', $result->status());
    }

    public function testBookingIsCreatingCorrectlyWhenStudioTimezoneIsNotUtcWhenEventDoesNotExist(): void
    {
        $startTime = 1_639_998_000;
        $branchId = '61c59e525f7ab689af46ccfb';
        $program = new Program([
            '_id' => '5b5b28a73355dafa1a354d88',
            'branch_id' => $branchId,
            'schedule' => [
                [
                    'days_week' => 0,
                    'start_time' => '08:00',
                    'end_time' => '09:00',
                    'active' => true,
                    'facility' => ['52a7011a05c677bda826611b'],
                    'level' => 'advanced',
                    'trainers' => ['59a7011a05c677bda916612c'],
                    'size' => 100,
                    'code' => '5856c03455fd1',
                ],
            ]
        ]);

        $command = Mockery::mock(ProcessReservationOrBookingCommand::class);
        $command
            ->shouldReceive('getProgram')
            ->andReturn($program)
            ->times(3)
            ->shouldReceive('getStartTime')
            ->andReturn($startTime)
            ->twice()
            ->shouldReceive('getScheduleCode')
            ->andReturn('5856c03455fd1')
            ->times(3)
            ->shouldReceive('getUser')
            ->andReturn(new User())
            ->once()
            ->shouldReceive('getBatchId')
            ->andReturn(null)
            ->once()
            ->shouldReceive('getRequestedByUser')
            ->andReturn(new User())
            ->once();

        $eventsRepository = Mockery::mock(EventsRepository::class);
        $eventsRepository
            ->shouldReceive('addCriteria')
            ->times(4)
            ->andReturnSelf()
            ->getMock()
            ->shouldReceive('first')
            ->andReturn(null)
            ->once();

        $bookingsRepository = Mockery::mock(BookingsRepository::class);
        $bookingsRepository
            ->shouldReceive('create')
            ->withArgs(function (Booking $booking) use ($startTime) {
                $startTimeInUtc = $startTime - (3 * HOUR);
                $endTimeInUtc = $startTimeInUtc + HOUR;
                self::assertEquals($booking['time_start']->toDateTime()->getTimeStamp(), $startTimeInUtc);
                self::assertEquals($booking['time_finish']->toDateTime()->getTimeStamp(), $endTimeInUtc);

                return true;
            })
            ->andReturn(
                new Booking([
                    'event_id' => null,
                ])
            )
            ->once();

        $eventManager = \Mockery::mock(EventManager::class);
        $eventHasAvailableSpot = \Mockery::mock(EventHasAvailableSpot::class);

        $notGeneratedEventHasAvailableSpot = \Mockery::mock(EventNotGeneratedHasAvailableSpot::class);
        $notGeneratedEventHasAvailableSpot
            ->shouldReceive('validate')
            ->once();

        $handler = $this->getProcessReservationHandler(
            $bookingsRepository,
            $eventsRepository,
            $eventManager,
            $eventHasAvailableSpot,
            $notGeneratedEventHasAvailableSpot
        );

        $handler->handle($command);
    }

    public function testBookingIsCreatingCorrectlyWhenStudioTimezoneIsNotUtcWhenEventExists(): void
    {
        $startTime = 1_639_998_000;

        $branchId = '61c59e525f7ab689af46ccfb';

        $command = Mockery::mock(ProcessReservationOrBookingCommand::class);
        $command
            ->shouldReceive('getProgram')
            ->andReturn(
                new Program([
                    '_id' => '5b5b28a73355dafa1a354d88',
                    'branch_id' => $branchId,
                ])
            )
            ->times(2)
            ->shouldReceive('getStartTime')
            ->andReturn($startTime)
            ->once()
            ->shouldReceive('getScheduleCode')
            ->andReturn('5856c03455fd1')
            ->twice()
            ->shouldReceive('getUser')
            ->andReturn(new User())
            ->once()
            ->shouldReceive('getBatchId')
            ->andReturn(null)
            ->once()
            ->shouldReceive('getRequestedByUser')
            ->andReturn(new User())
            ->zeroOrMoreTimes();

        $eventStartTime = strtotime('2021-12-20T08:00:00Z');
        $eventEndTime = strtotime('2021-12-20T09:00:00Z');
        $eventsRepository = Mockery::mock(EventsRepository::class);
        $eventsRepository
            ->shouldReceive('addCriteria')
            ->times(4)
            ->andReturnSelf()
            ->getMock()
            ->shouldReceive('first')
            ->andReturn(
                new Event([
                    '_id' => 'event-id',
                    'branch_id' => $branchId,
                    'time_start' => new \MongoDate($eventStartTime),
                    'time_finish' => new \MongoDate($eventEndTime),
                ])
            )
            ->once();

        $bookingsRepository = Mockery::mock(BookingsRepository::class);
        $bookingsRepository
            ->shouldReceive('create')
            ->withArgs(function (Booking $booking) use ($eventStartTime, $eventEndTime) {
                self::assertEquals($booking['time_start']->toDateTime()->getTimeStamp(), $eventStartTime);
                self::assertEquals($booking['time_finish']->toDateTime()->getTimeStamp(), $eventEndTime);

                return true;
            })
            ->andReturn(
                new Booking([
                    'event_id' => null,
                ])
            )
            ->once();

        $eventManager = \Mockery::mock(EventManager::class);

        $eventHasAvailableSpot = \Mockery::mock(EventHasAvailableSpot::class);
        $eventHasAvailableSpot
            ->shouldReceive('validate')
            ->once();

        $notGeneratedEventHasAvailableSpot = \Mockery::mock(EventNotGeneratedHasAvailableSpot::class);
        $notGeneratedEventHasAvailableSpot
            ->shouldNotReceive('validate');

        $handler = $this->getProcessReservationHandler(
            $bookingsRepository,
            $eventsRepository,
            $eventManager,
            $eventHasAvailableSpot,
            $notGeneratedEventHasAvailableSpot
        );

        $handler->handle($command);
    }

    public function testClassPassBookingPassesValidationWhenEventDoesNotExistsAndThereIsAvailableSpot(): void
    {
        $startTime = 1_639_998_000;
        $branchId = '61c59e525f7ab689af46ccfb';
        $program = new Program([
            '_id' => '5b5b28a73355dafa1a354d88',
            'branch_id' => $branchId,
            'schedule' => [
                [
                    'days_week' => 0,
                    'start_time' => '08:00',
                    'end_time' => '09:00',
                    'active' => true,
                    'trainers' => ['59a7011a05c677bda916612c'],
                    'size' => 5,
                    'code' => '5856c03455fd1',
                ],
            ],
            'metadata' => [
                'classpass' => [
                    'size' => 1
                ]
            ]
        ]);

        $command = Mockery::mock(ProcessReservationOrBookingCommand::class);
        $command
            ->shouldReceive('getProgram')
            ->andReturn($program)
            ->times(3)
            ->shouldReceive('getStartTime')
            ->andReturn($startTime)
            ->twice()
            ->shouldReceive('getScheduleCode')
            ->andReturn('5856c03455fd1')
            ->times(3)
            ->shouldReceive('getUser')
            ->andReturn(new User())
            ->once()
            ->shouldReceive('getBatchId')
            ->andReturn(null)
            ->once()
            ->shouldReceive('getRequestedByUser')
            ->andReturn(new User([
                'integrator' => Origin::CLASSPASS
            ]))
            ->zeroOrMoreTimes();

        $eventStartTime = strtotime('2021-12-20T08:00:00Z');
        $eventEndTime = strtotime('2021-12-20T09:00:00Z');
        $eventsRepository = Mockery::mock(EventsRepository::class);
        $eventsRepository
            ->shouldReceive('addCriteria')
            ->times(4)
            ->andReturnSelf()
            ->getMock()
            ->shouldReceive('first')
            ->andReturn(null)
            ->once();

        $bookingsRepository = Mockery::mock(BookingsRepository::class);
        $bookingsRepository
            ->shouldReceive('create')
            ->withArgs(function (Booking $booking) use ($eventStartTime, $eventEndTime) {
                self::assertEquals($booking['time_start']->toDateTime()->getTimeStamp(), $eventStartTime);
                self::assertEquals($booking['time_finish']->toDateTime()->getTimeStamp(), $eventEndTime);

                return true;
            })
            ->andReturn(
                new Booking([
                    'event_id' => null,
                ])
            )
            ->once();

        $eventManager = \Mockery::mock(EventManager::class);

        $eventHasAvailableSpot = \Mockery::mock(EventHasAvailableSpot::class);
        $eventHasAvailableSpot
            ->shouldNotReceive('validate');

        $notGeneratedEventHasAvailableSpot = \Mockery::mock(EventNotGeneratedHasAvailableSpot::class);
        $notGeneratedEventHasAvailableSpot
            ->shouldReceive('validate')
            ->once();

        $handler = $this->getProcessReservationHandler(
            $bookingsRepository,
            $eventsRepository,
            $eventManager,
            $eventHasAvailableSpot,
            $notGeneratedEventHasAvailableSpot
        );

        $handler->handle($command);
    }

    private function getProcessReservationHandler(
        BookingsRepository $bookingsRepository,
        EventsRepository $eventsRepository,
        EventManager $eventManager,
        EventHasAvailableSpot $eventHasAvailableSpot,
        EventNotGeneratedHasAvailableSpot $eventNotGeneratedHasAvailableSpot
    ): ProcessReservationHandler
    {

        return new ProcessReservationHandler(
            $bookingsRepository,
            $eventsRepository,
            $this->branchesRepository,
            $eventManager,
            $eventHasAvailableSpot,
            $eventNotGeneratedHasAvailableSpot
        );
    }
}
