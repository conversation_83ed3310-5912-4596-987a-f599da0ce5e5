<?php

namespace CakeTestCases\Glofox\Domain\Events\Generators;

use Glofox\Domain\Events\Generators\OnlineEventUrlGenerator;
use Glofox\Domain\Events\Models\Event;
use Glofox\Domain\Events\Validation\EventIsOnlineValidatorInterface;
use Glofox\Domain\Users\Models\User;
use Mockery;

\App::import('Test/Case', 'GlofoxControllerTestCase');

class OnlineEventUrlGeneratorTest extends \GlofoxTestCase
{
    public $fixtures = [];
    /** @var EventIsOnlineValidatorInterface */
    private $eventIsOnlineValidator;
    private ?string $liveStreamServiceSalt = null;
    private ?string $liveStreamServiceUrl = null;
    /** @var Event */
    private $event;
    /** @var User */
    private $user;

    public function setUp()
    {
        parent::setUp();

        $this->eventIsOnlineValidator = Mockery::mock(EventIsOnlineValidatorInterface::class);

        $this->liveStreamServiceSalt = 'DuMmY_SaLt';
        $this->liveStreamServiceUrl = 'https://www.glofoxtests.com';

        $this->event = Event::make(['_id' => 'this_is_the_event_id', 'branch_id' => 'this_is_the_branch_id']);
        $this->user = User::make(['_id' => 'this_is_the_member_id']);
    }

    public function tearDown()
    {
        parent::tearDown();

        Mockery::close();
    }

    public function test_online_event_url_generator_returns_null_for_none_online_events()
    {
        $this->eventIsOnlineValidator
            ->shouldReceive('validate')
            ->andReturnFalse()
            ->once();

        $onlineEventUrlGenerator = new OnlineEventUrlGenerator(
            $this->eventIsOnlineValidator,
            $this->liveStreamServiceSalt,
            $this->liveStreamServiceUrl
        );

        $result = $onlineEventUrlGenerator->generate($this->event, $this->user);

        self::assertNull($result);
    }

    public function test_online_event_url_generator_generates_the_expected_url_for_online_events()
    {
        $this->eventIsOnlineValidator
            ->shouldReceive('validate')
            ->andReturnTrue()
            ->once();

        $onlineEventUrlGenerator = new OnlineEventUrlGenerator(
            $this->eventIsOnlineValidator,
            $this->liveStreamServiceSalt,
            $this->liveStreamServiceUrl
        );

        $token = hash(
            'sha256',
            sprintf('%s.%s.%s', $this->event->id(), $this->user->id(), $this->liveStreamServiceSalt)
        );
        $expected = sprintf(
            '%s/studios/%s/events/%s/members/%s?token=%s',
            $this->liveStreamServiceUrl,
            $this->event->branchId(),
            $this->event->id(),
            $this->user->id(),
            $token
        );

        $result = $onlineEventUrlGenerator->generate($this->event, $this->user);

        self::assertTextEquals($expected, $result);
    }

    public function test_online_event_url_generator_does_not_break_if_base_url_has_an_extra_slash_at_the_end()
    {
        $liveStreamServiceUrlWithSlash = sprintf('%s/', $this->liveStreamServiceUrl);
        $this->eventIsOnlineValidator
            ->shouldReceive('validate')
            ->andReturnTrue()
            ->once();

        $onlineEventUrlGenerator = new OnlineEventUrlGenerator(
            $this->eventIsOnlineValidator,
            $this->liveStreamServiceSalt,
            $liveStreamServiceUrlWithSlash
        );

        $token = hash(
            'sha256',
            sprintf('%s.%s.%s', $this->event->id(), $this->user->id(), $this->liveStreamServiceSalt)
        );
        $expected = sprintf(
            '%s/studios/%s/events/%s/members/%s?token=%s',
            $this->liveStreamServiceUrl,
            $this->event->branchId(),
            $this->event->id(),
            $this->user->id(),
            $token
        );

        $result = $onlineEventUrlGenerator->generate($this->event, $this->user);

        self::assertTextEquals($expected, $result);
    }
}
