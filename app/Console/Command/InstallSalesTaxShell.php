<?php

declare(strict_types=1);

use Glofox\Domain\Branches\Models\Branch as BranchModel;
use Glofox\Domain\Branches\Repositories\BranchesRepository;
use Glofox\Domain\SalesTaxes\Services\PriceCalculatorServiceInterface;
use Glofox\Domain\SalesTaxes\ServiceType;
use Glofox\Payments\Util;
use Glofox\Repositories\Search\Expressions\Shared\Active;
use Glofox\Repositories\Search\Expressions\Shared\Id;
use Psr\Log\LoggerInterface;

App::uses('Shell', 'Console');

class InstallSalesTaxShell extends AppShell
{
    private const AVAILABLE_TAX_MODES = ['inclusive', 'exclusive'];

    /** @var LoggerInterface */
    private $logger;

    /** @var BranchesRepository */
    private $branchesRepository;

    /** @var PriceCalculatorServiceInterface */
    private $salesTaxService;

    public function __construct($stdout = null, $stderr = null, $stdin = null)
    {
        parent::__construct($stdout, $stderr, $stdin);

        $this->logger = app()->make(LoggerInterface::class);
        $this->branchesRepository = app()->make(BranchesRepository::class);
        $this->salesTaxService = app()->make(PriceCalculatorServiceInterface::class);
    }

    public function getOptionParser()
    {
        $parser = parent::getOptionParser();

        $parser->addOption('studiosIds', [
            'short' => 's',
            'help' => 'The studios ids to install sales tax',
            'default' => '',
        ]);

        $parser->addOption('taxMode', [
            'short' => 'm',
            'help' => 'The studios tax mode to be assigned',
            'default' => 'inclusive',
        ]);

        $parser->addOption('taxes', [
            'short' => 't',
            'help' => 'The taxes assignation to services',
            'default' => '',
        ]);

        return $parser;
    }

    public function main(): void
    {
        $branchesIds = $this->param('studiosIds');
        $branchesIds = explode(PHP_EOL, $branchesIds);

        $taxMode = strtolower($this->param('taxMode'));
        if (!in_array($taxMode, static::AVAILABLE_TAX_MODES, true)) {
            $errorMessage = sprintf(
                'There was an error installing sales tax because %s is not a valid tax mode',
                $taxMode
            );
            $this->logger->error($errorMessage);
            throw new UnsuccessfulOperation($errorMessage);
        }

        $rawTaxesDetails = $this->param('taxes');
        $rawTaxesDetails = explode(PHP_EOL, $rawTaxesDetails);

        $taxesData = [
            'tax_mode' => $taxMode,
            'taxes' => $this->sanitiseTaxesDetails($rawTaxesDetails),
        ];

        foreach ($branchesIds as $branchId) {
            $this->logger->info(sprintf('Starting process to install Sales Tax to studio %s', $branchId));

            try {
                /** @var BranchModel $branch */
                $branch = $this->branchesRepository
                    ->addCriteria(new Active(true))
                    ->addCriteria(new Id($branchId))
                    ->firstOrFail();
            } catch (Exception $exception) {
                $this->logger->error(
                    sprintf(
                        'Cannot install sales tax on studio %s because %s',
                        $branchId,
                        $exception->getMessage()
                    )
                );
                continue;
            }

            $tax = $this->getLegacySalesTaxField($branch);
            $this->logger->info(sprintf('Legacy Sales Tax value: %s', $tax));
            $taxesData['legacy_sales_tax'] = $tax;

            try {
                $this->salesTaxService->install($branch, $taxesData);
            } catch (UnsuccessfulOperation $exception) {
                $this->logger->error(
                    sprintf(
                        'There was an error installing sales tax for studio %s because %s',
                        $branch->id(),
                        $exception->getMessage()
                    )
                );
                continue;
            }

            $configuration = $branch->configuration();
            $configuration->forget('tax_rate');
            $branch->put('configuration', $configuration->toArray());

            try {
                $this->branchesRepository->legacySaveOrFail(
                    $branch->toArray()
                );
            } catch (UnsuccessfulOperation $exception) {
                $this->logger->error(
                    sprintf(
                        'Sales tax enabled but legacy default tax rate could not be removed for branch_id %s because %s',
                        $branch->id(),
                        $exception->getMessage()
                    )
                );
                continue;
            }

            $this->logger->info(sprintf('Sales Tax successfully installed to studio %s', $branchId));
        }
    }

    private function sanitiseTaxesDetails(array $rawData): array
    {
        $pattern = '/(.+)\:([0-9\.]+)_([a-z,]+)/i';
        $result = [];

        foreach ($rawData as $rawLine) {
            if (empty($rawLine)) {
                return $result;
            }

            if (preg_match($pattern, $rawLine, $matches) !== 1) {
                $errorMessage = sprintf(
                    'There was an error installing sales tax because %s is not a valid setting',
                    $rawLine
                );
                $this->logger->error($errorMessage);
                throw new UnsuccessfulOperation($errorMessage);
            }
            [, $name, $rate, $services] = $matches;

            $tmpResult = [];
            $tmpResult['name'] = $name;

            if (!is_numeric($rate)) {
                $errorMessage = sprintf(
                    'There was an error installing sales tax because %s is not a valid tax rate',
                    $rate
                );
                $this->logger->error($errorMessage);
                throw new UnsuccessfulOperation($errorMessage);
            }
            $tmpResult['rate'] = Util::convertToCents((float) $rate, 3);

            $tmpResult['services'] = [];
            $services = explode(',', $services);
            foreach ($services as $service) {
                $service = strtolower($service);

                if (!ServiceType::exists($service)) {
                    $errorMessage = sprintf(
                        'There was an error installing sales tax because %s is not a valid service',
                        $service
                    );
                    $this->logger->error($errorMessage);
                    throw new UnsuccessfulOperation($errorMessage);
                }
                $tmpResult['services'][] = $service;
            }

            $result[] = $tmpResult;
        }

        return $result;
    }

    private function getLegacySalesTaxField(BranchModel $branch): ?int
    {
        $tax = $branch->configuration()->get('tax_rate');

        if (is_numeric($tax)) {
            return Util::convertToCents((float) $tax, 3);
        }

        return null;
    }
}
