service_name                   = "apiworker-fulfilment-service"
image_name                     = "apiworker-fulfilment"
component_name                 = "apiworker-fulfilment"
container_port                 = 80
containers_count               = 2
max_capacity                   = 4
ecs_network_mode               = "awsvpc"
fargate_private_service_enable = true
ecs_service_enable             = 0
container_memory               = 2048
reserved_memory                = 1024
container_cpu                  = 1024

autoscaling_scale_type            = "queue"
autoscaling_scale_queue_name      = "fulfilment-coreapi-plat"
autoscaling_scale_down_threshold  = 10
autoscaling_scale_up_threshold    = 100
autoscaling_scale_up_period       = 60
autoscaling_scale_up_adjustment   = 2

accessible_sqs_queues_arn = [ "arn:aws:sqs:eu-west-1:108272622497:fulfilment-coreapi-plat" ]
accessible_sns_topics_arn = [ "arn:aws:sns:eu-west-1:108272622497:glofox-events-topic-plat", "arn:aws:sns:eu-west-1:108272622497:glofox-communications-commands-topic-plat" ]

env_vars = {
  GLOFOX_DATABASE_NAME                    = "platform",
  GLOFOX_DATABASE_USERNAME                = "api_service_plat",
  GLOFOX_DATABASE_REPLICASET_NAME         = "platform-shard-0",
  GLOFOX_DATABASE_SSL                     = 1,
  GLOFOX_DATABASE_PORT                    = 27017,
  GLOFOX_DATABASE_USE_ENVIRONMENT         = 1,
  GLOFOX_DATABASE_READ_PREFERENCE         = "nearest",
  GLOFOX_DATABASE_DEFAULT_MAX_TIME_MS     = "30000",
  NEW_RELIC_APP_NAME                      = "ApiWorkerFulfilment-Platform-EU",
  NEW_RELIC_ENABLED                       = true,
  REGION                                  = "eu-west-1",
  SERVER                                  = "platform",
  SERVICE_NAME                            = "apiworker-fulfilment",
  PHP_XDEBUG_ENABLE                       = "0",
  GLOFOX_DEBUG_LEVEL                      = "0",
  OPENSHIFT_APP_NAME                      = "platform",
  PHP_ENABLE_OPCACHE                      = 1,
  GLOFOX_ROOT_DOMAIN                      = "https://app.glofox.com"
  GLOFOX_AWS_S3_BUCKET                    = "glofox",
  GLOFOX_AWS_S3_HOST                      = "s3.amazonaws.com",
  GLOFOX_GRPC_API_CLIENT_HOST             = "payments-api.plat.service.private:44440",
  GLOFOX_GRPC_ACCOUNT_CLIENT_HOST         = "payments-account.plat.service.private:44441",
  GLOFOX_GRPC_TRANSACTION_CLIENT_HOST     = "payments-transaction.plat.service.private:44442",
  GLOFOX_GRPC_SUBSCRIPTION_CLIENT_HOST    = "payments-subscription.plat.service.private:44443",
  GLOFOX_WEB_COMPONENT_HTTP_HOST          = "https://payments-web-service-plat.aws.glofox.com",
  GLOFOX_PUSHER_URL                       = "https://pusher-service-plat.aws.glofox.com/",
  GLOFOX_PUSHER_URL_NEW                   = "http://push-notifications.plat.service.private/",
  GLOFOX_SENTRY_URL                       = "https://<EMAIL>/1268734"
  GLOFOX_ASYNC_REGION                     = "eu-west-1"
  GLOFOX_SNS_NAME                         = "glofox-events-topic-plat"
  GLOFOX_MEMBERSHIPS_API_URL              = "http://memberships.plat.service.private:8080"
  GLOFOX_WALLETS_API_URL                  = "http://memberships.plat.service.private:8080"
  GLOFOX_TAX_API_URL                      = "http://tax.plat.service.private:80"
  GLOFOX_SQS_API_QUEUE_URL                = "https://sqs.eu-west-1.amazonaws.com/108272622497/api-plat"
  GLOFOX_SNS_REGION                       = "eu-west-1"
  GLOFOX_REDIS_CLUSTER_ENABLED            = "1"
  GLOFOX_EXPERIMENTS_API_URL              = "https://iris-plat.aws.glofox.com"
  LIVE_STREAM_SERVICE_URL                 = "https://live.glofox.com/v3"
  LIVE_STREAM_INSTALLER_URL               = "https://api.internal-gateway.plat.service.private/live-stream-installer"
  ELECTRONIC_AGREEMENTS_SERVICE_URL       = "https://api.internal-gateway.plat.service.private"
  REPORTS_SERVICE_URL                     = "http://reports.plat.service.private:8080"
  INVOICE_SERVICE_URL                     = "http://invoice.plat.service.private:80/invoice"
  RECEIPTS_SERVICE_URL                    = "http://receipts.plat.service.private:80/receipts-api"
  SALES_TAX_SERVICE_URL                   = "http://sales-tax.plat.service.private:80/sales-tax"
  ADDON_SERVICE_URL                       = "http://services.plat.service.private:8083/services-api"
  COMMUNICATIONS_INTERNAL_SERVICE_URL     = "http://communications.plat.service.private:80/communications-internal"
  CART_SERVICE_URL                        = "http://cart.plat.service.private:80"
  SENDGRID_USERNAME                       = "apikey"
  PASSWORD_HASH_COST                      = "10"
  EAGREEMENT_MUST_SIGN_DATE               = "2021-05-20 00:00:01"
  PASSWORD_RESET_TOKEN_VALIDITY           = "60"
  PASSWORD_RESET_TOKEN_SIZE               = "24"
  LAUNCH_DARKLY_API_URL                   = "https://app.launchdarkly.com/api/"
  AUTHENTICATION_SERVICE_URL              = "https://auth.glofox.com/"
  HONEYCOMB_DATASET_NAME                  = "glofox-plat"
  HONEYCOMB_BASE_URL                      = "https://api.honeycomb.io"
  ENVIRONMENT_SHORT_NAME                  = "plat"
  GOOGLE_RECAPTCHA_BASE_URL               = "https://www.google.com/recaptcha/api"
  COMMUNICATIONS_SERVICE_TOPIC         = "arn:aws:sns:eu-west-1:108272622497:glofox-communications-commands-topic-plat"
}
secret_vars = {
  GLOFOX_DATABASE_HOST                                   = "glofox/apiworker/plat/GLOFOX_DATABASE_HOST"
  GLOFOX_DATABASE_PASSWORD                               = "glofox/apiworker/plat/GLOFOX_DATABASE_PASSWORD_V2"
  GLOFOX_DATABASE_FOR_AUTHENTICATION                     = "glofox/apiworker/plat/GLOFOX_DATABASE_FOR_AUTHENTICATION"
  NEW_RELIC_LICENCE                                      = "glofox/apiworker/plat/NEW_RELIC_LICENCE"
  GLOFOX_STRIPE_CUSTOM_EU_PUBLISHABLE_KEY                = "glofox/apiworker/plat/GLOFOX_STRIPE_CUSTOM_EU_PUBLISHABLE_KEY"
  GLOFOX_STRIPE_CUSTOM_US_PUBLISHABLE_KEY                = "glofox/apiworker/plat/GLOFOX_STRIPE_CUSTOM_US_PUBLISHABLE_KEY"
  GLOFOX_STRIPE_CUSTOM_US_LIFT_BRANDS_PUBLISHABLE_KEY    = "glofox/apiworker/plat/GLOFOX_STRIPE_CUSTOM_US_LIFT_BRANDS_PUBLISHABLE_KEY"
  GLOFOX_STRIPE_CUSTOM_UK_LIFT_BRANDS_PUBLISHABLE_KEY    = "glofox/apiworker/plat/GLOFOX_STRIPE_CUSTOM_UK_LIFT_BRANDS_PUBLISHABLE_KEY"
  GLOFOX_STRIPE_CUSTOM_EU_LIFT_BRANDS_PUBLISHABLE_KEY    = "glofox/apiworker/plat/GLOFOX_STRIPE_CUSTOM_EU_LIFT_BRANDS_PUBLISHABLE_KEY"
  GLOFOX_ANDROID_GCM_KEY                                 = "glofox/apiworker/plat/GLOFOX_ANDROID_GCM_KEY"
  GLOFOX_IOS_APNS_KEY                                    = "glofox/apiworker/plat/GLOFOX_IOS_APNS_KEY"
  GLOFOX_AWS_S3_ACCESS_KEY                               = "glofox/apiworker/plat/GLOFOX_AWS_S3_ACCESS_KEY"
  GLOFOX_AWS_S3_SECRET_KEY                               = "glofox/apiworker/plat/GLOFOX_AWS_S3_SECRET_KEY"
  GLOFOX_WEB_COMPONENT_SECRET                            = "glofox/apiworker/plat/GLOFOX_WEB_COMPONENT_SECRET"
  PUSHER_AUTH_KEY                                        = "glofox/apiworker/plat/PUSHER_AUTH_KEY"
  GLOFOX_INTEGRATIONS_CLASSPASS                          = "glofox/apiworker/plat/GLOFOX_INTEGRATIONS_CLASSPASS"
  GLOFOX_ASYNC_ACCOUNT_ID                                = "glofox/apiworker/plat/GLOFOX_ASYNC_ACCOUNT_ID"
  GLOFOX_REDIS_URL                                       = "glofox/apiworker/plat/GLOFOX_REDIS_URL"
  GLOFOX_EXPERIMENTS_API_KEY                             = "glofox/apiworker/plat/GLOFOX_EXPERIMENTS_API_KEY"
  LIVE_STREAM_SERVICE_SALT                               = "glofox/apiworker/plat/LIVE_STREAM_SERVICE_SALT"
  GLOFOX_MIXPANEL_TOKEN                                  = "glofox/apiworker/plat/GLOFOX_MIXPANEL_TOKEN"
  JWT_SALT_GENERATE                                      = "glofox/apiworker/plat/JWT_SALT_GENERATE"
  JWT_SALT_VERIFY                                        = "glofox/apiworker/plat/JWT_SALT_VERIFY"
  URL_SIGNATURE_KEY                                      = "glofox/apiworker/plat/URL_SIGNATURE_KEY"
  SENDGRID_PASSWORD                                      = "glofox/apiworker/plat/SENDGRID_PASSWORD"
  ADDON_SERVICE_API_TOKEN                                = "glofox/apiworker/plat/ADDON_SERVICE_API_TOKEN"
  LAUNCH_DARKLY_API_TOKEN                                = "glofox/apiworker/plat/LAUNCH_DARKLY_API_TOKEN"
  GLOFOX_MAILCHIMP_WEBHOOK_SECRET_KEY                    = "glofox/apiworker/plat/GLOFOX_MAILCHIMP_WEBHOOK_SECRET_KEY"
  GLOFOX_TWILIO_WEBHOOK_SECRET_KEY                       = "glofox/apiworker/plat/GLOFOX_TWILIO_WEBHOOK_SECRET_KEY"
  LAUNCH_DARKLY_SDK_KEY                                  = "glofox/apiworker/plat/LAUNCH_DARKLY_SDK_KEY"
  GLOFOX_ZENDESK_TOKEN                                   = "glofox/apiworker/plat/GLOFOX_ZENDESK_TOKEN"
  GLOFOX_ZENDESK_USERNAME                                = "glofox/apiworker/plat/GLOFOX_ZENDESK_USERNAME"
  HONEYCOMB_API_KEY                                      = "glofox/HONEYCOMB_APIKEY"
  GOOGLE_RECAPTCHA_SECRET                                = "glofox/recaptcha_service_account_key"
  RECEIPTS_HTTP_API_KEY                                  = "receipts/plat/HTTP_API_KEY"
  APPLE_DEV_KEY                                          = "glofox/standalone-apps/APPLE_DEV_KEY"
  ACTIVE_BRANCHES_COMMUNICATIONS                         = "glofox/api/plat/ACTIVE_BRANCHES_COMMUNICATIONS"
  COMMUNICATIONS_HTTP_API_KEY                            = "communications/plat/HTTP_API_KEY"
}
