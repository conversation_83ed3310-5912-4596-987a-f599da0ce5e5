<?php

namespace CakeTestCases\Glofox\Domain\Experiments\Services;

use Glofox\Domain\Experiments\Services\ExperimentsClient;
use Glofox\Domain\RequestLog\Repositories\RequestLogRepository;
use Psr\Log\LoggerInterface;

\App::import('Test/Case', 'GlofoxTestCase');

class ExperimentsClientTest extends \GlofoxTestCase
{
    protected $httpClient;

    protected $logRepository;

    protected $logger;

    public function setUp()
    {
        parent::setUp();

        $this->logger = \Mockery::mock(LoggerInterface::class);
        $this->logger->shouldReceive('info');
        $this->logger->shouldReceive('warning');
    }

    public function tearDown()
    {
        parent::tearDown();
        \Mockery::close();
    }

    public function test_should_create_a_get_request()
    {
        $this->httpClient = \Mockery::mock(\GuzzleHttp\ClientInterface::class);
        $this->httpClient
            ->shouldReceive('request')
            ->with('GET', '/experiments/123/outcomes', ['query' => null, 'http_errors' => false, 'headers' => []])
            ->andReturn(
                \Mockery::mock(\Psr\Http\Message\ResponseInterface::class)
                    ->shouldReceive('getBody')
                    ->andReturn('{"foo":"bar"}')
                    ->shouldReceive('getStatusCode')
                    ->andReturn(200)
                    ->shouldReceive('getHeader')
                    ->andReturn(['application/json'])
                    ->getMock()
            );
        $this->logRepository = \Mockery::mock(RequestLogRepository::class);

        $response = $this->createClient()->get('GET_OUTCOMES', ['testIdentifier' => '123']);

        $this->assertEquals('bar', $response->foo);
    }

    public function test_should_create_a_get_request_with_query_parameters()
    {
        $this->httpClient = \Mockery::mock(\GuzzleHttp\ClientInterface::class);
        $this->httpClient
            ->shouldReceive('request')
            ->with('GET', '/experiments/123/outcomes', ['query' => ['foo' => 'bar'], 'http_errors' => false, 'headers' => ['Authorization' => 'Bearer: Test']])
            ->andReturn(
                \Mockery::mock(\Psr\Http\Message\ResponseInterface::class)
                    ->shouldReceive('getBody')
                    ->andReturn('{"foo":"bar"}')
                    ->shouldReceive('getStatusCode')
                    ->andReturn(200)
                    ->shouldReceive('getHeader')
                    ->andReturn(['application/json'])
                    ->getMock()
            );
        $this->logRepository = \Mockery::mock(RequestLogRepository::class);

        $response = $this->createClient()->get('GET_OUTCOMES', ['testIdentifier' => '123'], ['foo' => 'bar'], ['Authorization' => 'Bearer: Test']);

        $this->assertEquals('bar', $response->foo);
    }

    public function test_should_log_failed_get_request()
    {
        $this->httpClient = \Mockery::mock(\GuzzleHttp\ClientInterface::class);
        $this->httpClient
            ->shouldReceive('request')
            ->with('GET', '/experiments/123/outcomes', ['query' => ['foo' => 'bar'], 'http_errors' => false, 'headers' => []])
            ->andReturn(
                \Mockery::mock(\Psr\Http\Message\ResponseInterface::class)
                    ->shouldReceive('getBody')
                    ->andReturn('{"foo":"bar"}')
                    ->shouldReceive('getStatusCode')
                    ->andReturn(500)
                    ->shouldReceive('getHeader')
                    ->andReturn(['application/json'])
                    ->getMock()
            );
        $this->logRepository = \Mockery::mock(RequestLogRepository::class);
        $this->logRepository
            ->shouldReceive('saveOrFail')
            ->with([
                'service' => 'experiments',
                'status_code' => 500,
                'request_uri' => '/experiments/123/outcomes?foo=bar'
            ]);

            $response = $this->createClient()->get('GET_OUTCOMES', ['testIdentifier' => '123'], ['foo' => 'bar']);

        $this->assertEquals('bar', $response->foo);
    }

    public function test_should_create_a_post_request()
    {
        $this->httpClient = \Mockery::mock(\GuzzleHttp\ClientInterface::class);
        $this->httpClient
            ->shouldReceive('request')
            ->with('POST', '/experiments/123/outcomes', ['form_params' => ['foo' => 'bar'], 'http_errors' => false, 'headers' => ['Authorization' => 'Bearer: Test']])
            ->andReturn(
                \Mockery::mock(\Psr\Http\Message\ResponseInterface::class)
                    ->shouldReceive('getBody')
                    ->andReturn('{"foo":"bar"}')
                    ->shouldReceive('getStatusCode')
                    ->andReturn(200)
                    ->shouldReceive('getHeader')
                    ->andReturn(['application/json'])
                    ->getMock()
            );
        $this->logRepository = \Mockery::mock(RequestLogRepository::class);

        $response = $this->createClient()->post('GET_OUTCOMES', ['testIdentifier' => '123'], ['foo' => 'bar'], ['Authorization' => 'Bearer: Test']);

        $this->assertEquals('bar', $response->foo);
    }

    public function test_should_log_failed_post_request()
    {
        $this->httpClient = \Mockery::mock(\GuzzleHttp\ClientInterface::class);
        $this->httpClient
            ->shouldReceive('request')
            ->with('POST', '/experiments/123/outcomes', ['form_params' => ['foo' => 'bar'], 'http_errors' => false, 'headers' => []])
            ->andReturn(
                \Mockery::mock(\Psr\Http\Message\ResponseInterface::class)
                    ->shouldReceive('getBody')
                    ->andReturn('{"foo":"bar"}')
                    ->shouldReceive('getStatusCode')
                    ->andReturn(400)
                    ->shouldReceive('getHeader')
                    ->andReturn(['application/json'])
                    ->getMock()
            );
        $this->logRepository = \Mockery::mock(RequestLogRepository::class);
        $this->logRepository
            ->shouldReceive('saveOrFail')
            ->with([
                'service' => 'experiments',
                'status_code' => 400,
                'request_uri' => '/experiments/123/outcomes?foo=bar'
            ]);

        $response = $this->createClient()->post('GET_OUTCOMES', ['testIdentifier' => '123'], ['foo' => 'bar']);

        $this->assertEquals('bar', $response->foo);
    }

    public function test_should_throw_an_error_if_endpoint_key_is_undefined()
    {
        $this->httpClient = \Mockery::mock(\GuzzleHttp\ClientInterface::class);
        $this->httpClient->shouldReceive('request');
        $this->logRepository = \Mockery::mock(RequestLogRepository::class);

        $exception = null;
        try {
            $this->createClient()->get('test_endpoint_key');
        } catch (\Exception $e) {
            $exception = $e;
        }

        $this->assertInstanceOf(\Exception::class, $exception);
        $this->assertEquals('No valid endpoint for key "test_endpoint_key"', $exception->getMessage());
    }

    private function createClient()
    {
        return new ExperimentsClient($this->httpClient, $this->logRepository, $this->logger);
    }
}
