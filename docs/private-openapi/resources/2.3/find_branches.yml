get:
  summary: Get Branches
  operationId: filterBranches
  tags:
    - Branches
  description: >
    Finds branches with search parameters. It also includes color configurations from the branch_configuration_mobile collection.
  parameters:
    - in: query
      name: name
      schema:
        type: string
      required: true
      description: The name of the branch for a partial search, requiring a minimum of 3 characters.
    - in: query
      name: bundle
      schema:
        type: string
        default: '_glofox'
      required: false
      description: Specifies the client bundle. Defaults to '_glofox' if not provided.
    - in: query
      name: role
      schema:
        type: string
        enum: [MEMBER]
      required: false
      description: Used to select by role (MEMBER) to filter branches that are member_facing true or null - locations that are available/visible to members when logging in

  responses:
    '200':
      description: >
        A list of branches, each including name, namespace, client bundle information, and color configurations.
      content:
        application/json:
          schema:
            type: object
            properties:
              success:
                type: boolean
              entities:
                type: array
                items:
                  type: object
                  properties:
                    branch_id:
                      type: string
                      description: Unique identifier of the branch.
                    branch_name:
                      type: string
                      description: Name of the branch.
                    branch_namespace:
                      type: string
                      description: Namespace associated with the branch.
                    branch_colors:
                      type: object
                      properties:
                        background:
                          type: string
                          description: Background color.
                        accent:
                          type: string
                          description: Accent color.
                        text:
                          type: string
                          description: Text color.
                      description: Color configuration for the branch.
                    logo:
                      type: string
                      description: URL to the branch's logo.
    '400':
      description: Bad request
      content:
        application/json:
          schema:
            $ref: '../../schemas/error.yml'
