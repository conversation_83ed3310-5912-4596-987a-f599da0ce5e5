<?php

declare(strict_types=1);

namespace Glofox\Domain\Users\Formatters;

use Glofox\Datasource\Exceptions\InvalidMongoIdException;
use Glofox\Domain\Branches\Exceptions\BranchNotFoundException;
use Glofox\Domain\Users\Models\User;
use libphonenumber\NumberParseException;
use libphonenumber\PhoneNumberFormat;
use libphonenumber\PhoneNumberUtil;

class PhoneNumberFormatter
{
    public const FORMAT_E164 = PhoneNumberFormat::E164;

    private PhoneNumberUtil $formatter;

    public function __construct(PhoneNumberUtil $formatter)
    {
        $this->formatter = $formatter;
    }

    public function format(User $user, int $numberFormat = self::FORMAT_E164): string
    {
        $phone = $user->phone();
        if (!$phone) {
            return '';
        }

        $countryCode = null;

        if ($user->address()) {
            $userCountryCode = $user->address()->get('country_code');
            $countryCode = $userCountryCode !== '' ? $userCountryCode : null;
        }

        if (!$countryCode && $user->has('branch_id')) {
            try {
                $branch = $user->originBranch();
            } catch (BranchNotFoundException|InvalidMongoIdException $e) {
                return '';
            }
            $countryCode = $branch->address() ?
                $branch->address()->countryCode() :
                null;
        }

        if (!$countryCode) {
            return $phone;
        }

        try {
            $number = $this->formatter->parse($phone, $countryCode);

            return $this->formatter->format($number, $numberFormat);
        } catch (NumberParseException $e) {
            return '';
        }
    }
}
