<?php

App::uses('Folder', 'Utility');
App::uses('File', 'Utility');

use Glofox\Datasource\Exceptions\InvalidMongoIdException;
use Glofox\Domain\AsyncEvents\Events\BranchUpdatedEventMeta;
use Glofox\Domain\AsyncEvents\Events\BranchUpdatedEventPayload;
use Glofox\Domain\Authentication\Auth;
use Glofox\Domain\Branches\Events\BranchWasUpdated;
use Glofox\Domain\Branches\Exceptions\BranchNotFoundException;
use Glofox\Domain\Branches\Exceptions\CouldNotUploadLogoException;
use Glofox\Domain\Branches\Http\Requests\Api1\FindBranchByIdRequest;
use Glofox\Domain\Branches\Http\Requests\Api1\ResetAllUsersStrikesRequest;
use Glofox\Domain\Branches\Http\Requests\Api1\UploadBranchLogoRequest;
use Glofox\Domain\Branches\Http\Requests\Api1\UpsertBranchRequest;
use Glofox\Domain\Branches\Http\Response\Api1;
use Glofox\Domain\Branches\Models\Branch as BranchModel;
use Glofox\Domain\Branches\Repositories\BranchesRepository;
use Glofox\Domain\Branches\Services\Avatar\BranchLogoRequestParser;
use Glofox\Domain\Branches\Services\BranchesPublisher;
use Glofox\Domain\Branches\Services\Logo\BranchLogoUploader;
use Glofox\Domain\Branches\Services\Logo\BranchLogoValidator;
use Glofox\Domain\Branches\Validation\Validator as BranchValidator;
use Glofox\Domain\PaymentMethods\Type;
use Glofox\Http\Responses\DeprecatedEndpointResponse;
use Glofox\Repositories\Search\Expressions\Shared\Id;
use Glofox\Request;
use Illuminate\Http\JsonResponse;

/**
 * BranchesController class.
 *
 * @uses          AppController
 *
 * @property Booking $Booking
 * @property User $User
 * @property Event $Event
 * @property Branch $Branch
 * @property Program $Program
 * @property Membership $Membership
 * @property StripeCharge $StripeCharge
 * @property UserCredit $UserCredit
 */
class BranchesController extends \Glofox\Domain\Branches\Http\BranchesController
{
    public $components = [
        'S3',
        'Utility',
        'Notification',
        'JWT',
    ];
    public $uses = [
        'Branch',
        'User',
        'Booking',
        'StripeCharge',
        'Membership',
        'Event',
        'Client',
    ];
    public $name = 'Branches';

    public function dispatcher($identifier = null)
    {
        return parent::dispatch($identifier, $this->Branch);
    }

    /**
     * @throws InvalidMongoIdException
     */
    public function upsert(
        BranchesRepository $branchesRepository,
        UpsertBranchRequest $request,
        BranchValidator $validator,
        BranchesPublisher $branchesPublisher
    ): JsonResponse {
        $user = Auth::user();

        $branchId = $request->getId();

        $data = $request->getBranch()->toArray();

        $validator->withData($data)->validate();

        $previousBranchState = $branchesRepository->addCriteria(new Id($branchId))
            ->skipCallbacks()
            ->firstOrFail();

        // Check for Integrator must go first, otherwise it will fail because of non-existing user.
        if (!$user->isIntegrator() && !$user->belongsTo($previousBranchState)) {
            $response = [
                'success' => false,
                'message' => 'YOU_DO_NOT_HAVE_ACCESS_TO_THAT_BRANCH',
                'message_code' => 'YOU_DO_NOT_HAVE_ACCESS_TO_THAT_BRANCH',
            ];

            return response()->json(
                $response,
                401
            );
        }

        if ($request->hasBrandName()) {
            $data['brand_name'] = $request->getBrandName();
        }

        if ($request->hasCorporateId()) {
            $corporateId = $request->getCorporateId();

            if (null !== $corporateId) {
                $this->Branch->validateCorporateId($corporateId);
            }

            $data['corporate_id'] = $corporateId;
        }

        $branch = array_replace_recursive($previousBranchState->toArray(), $data);

        // @see https://glofox.atlassian.net/browse/CI-869
        // Questions is the only node that must be replaced directly, without recursiviness,
        // to avoid the behaviour where this node would remain unchanged
        if (isset($data['questions'])) {
            $branch['questions'] = $data['questions'];
        }

        try {
            $branch = $this->sanitizeFeaturesClassesLevels($branch);
        } catch (UnsuccessfulOperation $e) {
            return response()->json([
                'success' => false,
                'message' => 'INVALID_BRANCH_FEATURES_DATA',
                'message_code' => 'INVALID_BRANCH_FEATURES_DATA',
            ]);
        }

        $result = $this->Branch->save($branch);

        if (!$result) {
            $errorMessage = $this->Branch->get_latest_error();
            throw new Exception($errorMessage);
        }

        $currentBranchState = BranchModel::make($result['Branch']);

        $branchesPublisher->sendBranchUpdatedEvent(
            new BranchUpdatedEventMeta([
                'branchId' => $currentBranchState->id(),
            ]),
            new BranchUpdatedEventPayload([
                'name' => $currentBranchState->name(),
                'namespace' => $currentBranchState->namespace(),
                'active' => $currentBranchState->isActive(),
                'website' => $currentBranchState->website(),
                'address' => $currentBranchState->address()->toArray(),
            ])
        );

        event()->emit(BranchWasUpdated::class, [$previousBranchState, $currentBranchState]);

        $response = new Api1\UpsertBranchResponse($branch);

        return $response->toJsonResponse();
    }

    /**
     * Function that reset all users' strikes of a given branch.
     */
    public function reset_all_users_strikes(ResetAllUsersStrikesRequest $request)
    {
        $fields = [
            'strike' => 0,
        ];

        $conditions = [
            'branch_id' => Auth::branch()->id(),
            'type' => 'MEMBER',
            'active' => true,
        ];

        try {
            $reset = $this->User->updateAll($fields, $conditions);
        } catch (Exception $ex) {
            return json_encode([
                'success' => false,
                'message' => $ex->getMessage(),
            ], JSON_PARTIAL_OUTPUT_ON_ERROR);
        }

        return json_encode([
            'success' => true,
        ]);
    }

    /*!
     * List branches basic info
     * @return [type] [description]
     */

    /**
     * @return string
     */
    public function list_branches()
    {
        $this->response->type('json');
        $this->autoRender = false;

        $conditions = ['active' => true];
        $fields = ['name', 'namespace', 'description'];

        // Check for query parameters to include extra fields (e.g. corporate_id)
        // This is to ensure that existing clients can still use the endpoint without breaking
        // the existing functionality.
        // Have it a hardcoded list of allowed fields to prevent any security issues
        if (!empty($this->request->query('include_fields'))) {
            $includeFields = htmlspecialchars($this->request->query('include_fields'), ENT_QUOTES, 'UTF-8');
            $extraFields = explode(',', $includeFields);
            $allowedFields = ['corporate_id'];
            $extraFields = array_intersect($extraFields, $allowedFields);
            $fields = [...$fields, ...$extraFields];
        }

        $params = [
            'conditions' => $conditions,
            'order' => ['name' => 1],
            'fields' => $fields,
        ];

        $results = $this->Branch->find('all', $params);
        $results = Hash::extract($results, '{n}.Branch');

        return json_encode(['success' => true, 'branches' => $results], JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    /**
     * [list_branches_by_user description].
     *
     * @return string [type] [description]
     */
    public function list_branches_by_user()
    {
        $user = $this->getUser();
        $user = $this->User->findOrFail($user['_id'], false);
        $user_branches = is_array(
            $user['User']['branch_id']
        ) ? $user['User']['branch_id'] : [$user['User']['branch_id']];

        $conditions = ['_id' => ['$in' => $user_branches]];
        $params = [
            'conditions' => $conditions,
            'order' => ['name' => 1],
            'fields' => ['name', 'namespace', 'description', 'address'],
        ];

        $results = $this->Branch->find('all', $params);
        $results = Hash::extract($results, '{n}.Branch');

        return json_encode(['success' => true, 'branches' => $results], JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    public function findById(FindBranchByIdRequest $request): JsonResponse
    {
        $id = $request->getId();

        $results = $this->Branch->findAllById($id);
        $response = $this->Branch->formatLevelsForApps($results);

        $response = new Api1\FindBranchByIdResponse($response);

        return $response->toJsonResponse();
    }

    // I reverted the change because we were getting 502 errors
    // with the "Argument 1 passed to BranchesController::findByBundle() must be an instance of Glofox\\Request, string given"
    // public function findByBundle(
    //     Glofox\Request $request
    // )
    public function findByBundle($bundle)
    {
        // $bundle = $request->cakeRouteParams()->get('bundle');

        if (!$bundle) {
            throw new Exception('bundle is missing');
        }

        $fields = [
            'name',
            'namespace',
            'description',
            'bundles',
            'bundle_config',
            'end_date',
        ];

        $conditions = [
            'active' => true,
            'bundles' => $bundle,
        ];

        $options = [
            'conditions' => $conditions,
            'order' => [
                'name' => 1,
            ],
            'fields' => $fields,
        ];

        $clients = $this->Client->find('all', $options);

        $namespaces = Hash::extract($clients, '{n}.Client.namespace');

        $conditions = [
            'active' => true,
            'namespace' => [
                '$in' => $namespaces,
            ],
        ];

        $options = [
            'conditions' => $conditions,
            'order' => [
                'name' => 1,
            ],
            'fields' => [
                '_id',
                'name',
                'namespace',
            ],
        ];

        $branches = $this->Branch->find('all', $options);
        $branches = Hash::extract($branches, '{n}.Branch');

        $response = [
            'success' => true,
            'branches' => $branches,
        ];

        return response()->json($response);
    }

    /**
     * @param null $namespace
     *
     * @return string
     */
    public function getBranchesListByNamespace($namespace = null)
    {
        return json_encode($this->Branch->findListByNamespace($namespace), JSON_PARTIAL_OUTPUT_ON_ERROR);
    }


    /**
     * @throws BranchNotFoundException
     * @throws InvalidMongoIdException
     * @throws CouldNotUploadLogoException
     * @throws UnsuccessfulOperation
     */
    public function uploads(
        UploadBranchLogoRequest $request,
        BranchLogoRequestParser $branchLogoRequestParser,
        BranchLogoValidator $branchLogoValidator,
        BranchLogoUploader $branchLogoUploader
    ): JsonResponse {
        $branch = Auth::user()->branch();

        $image = $branchLogoRequestParser->parse($request);

        if ($image === null) {
            throw new UnsuccessfulOperation('THERE_IS_NO_FILE_TO_UPLOAD');
        }

        $branchLogoValidator->validate($image);
        $url = $branchLogoUploader->upload($branch, $image);

        return response()->json([
            'success' => true,
            'url' => $url,
        ]);
    }

    /**
     * @throws UnsuccessfulOperation
     */
    private function sanitizeFeaturesClassesLevels(array $branch): array
    {
        if (empty($branch['features'])) {
            $branch['features'] = [];

            return $branch;
        }

        if (empty($branch['features']['classes'])) {
            $branch['features']['classes'] = [];

            return $branch;
        }

        if (empty($branch['features']['classes']['levels'])) {
            $branch['features']['classes']['levels'] = [];

            return $branch;
        }

        if (is_array($branch['features']['classes']['levels'])) {
            $branch['features']['classes']['levels'] = array_values(
                array_unique($branch['features']['classes']['levels'])
            );

            return $branch;
        }

        if (is_string($branch['features']['classes']['levels'])) {
            $branch['features']['classes']['levels'] = array_values(
                array_unique(explode(',', $branch['features']['classes']['levels']))
            );

            return $branch;
        }

        throw new UnsuccessfulOperation();
    }
}
