type: object
properties:
  rate_id:
    type: string
    pattern: '^[a-f\d]{24}$'
  event_type:
    type: string
    enum:
      - class
      - appointment
  event_id:
    type: string
    pattern: '^[a-f\d]{24}$'
  rate_type:
    type: string
    enum:
      - base
      - conditional
  rate_price:
    type: integer
    minimum: 1
  rate_group_id:
    type: string
    pattern: '^[a-f\d]{24}$'
  rate_bonus:
    type: array
    items:
      type: object
      properties:
        bonus_id:
          type: string
          pattern: '^[a-f\d]{24}$'
        bonus_type:
          type: string
          enum:
            - SINGLE_THRESHOLD
        bonus_settings:
          type: array
          items:
            $ref: './../../schemas/payroll_bonus_single_threshold.yml'
      required:
        - bonus_id
        - bonus_type
        - bonus_settings
required:
  - rate_id
  - event_type
  - event_id
  - rate_type
  - rate_price
