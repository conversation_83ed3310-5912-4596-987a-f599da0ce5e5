<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\Users\Http;

use CakeTestCases\Glofox\Domain\Authentication\Token\TokenGeneration;
use Glofox\Domain\Authentication\Http\Response\Api2\ChildAccountsResponse;
use Glofox\Domain\Authentication\Services\AuthenticationClient;
use Glofox\Domain\Users\Events\UserLoggedIn;
use Glofox\Domain\Users\Models\User;
use Glofox\Domain\Users\Repositories\UsersRepository;
use Glofox\Events\EventManager;
use Glofox\Repositories\Search\Expressions\Shared\Id;
use Glofox\Request;

\App::import('Test/Case', 'GlofoxControllerTestCase');

class LinkedAccountsControllerTest extends \GlofoxControllerTestCase
{
    use TokenGeneration;

    public function setUp(): void
    {
        parent::setUp();

        app()->forgetInstance(Request::class);
        app()->forgetInstance(UsersRepository::class);
    }

    public function tearDown()
    {
        parent::tearDown();

        \Mockery::close();
    }

    public function testItGeneratesAToken(): void
    {
        $parentId = '5c8b0615deb2eae76ec28673';
        $_SERVER['HTTP_AUTHORIZATION'] = $this->generateToken([
            '_id' => $parentId,
            'branch_id' => '5c783d4cd510f9635ad4a6b3'
        ]);
        $childId = '5c8b0642deb2eae76ec28674';
        $child = $this->getUser($childId);
        $device = ['os' => 'ios', 'version' => '8.2.6'];

        $data = [
            'device' => $device,
        ];

        $authClientMock = \Mockery::mock(AuthenticationClient::class);
        $authClientMock
            ->shouldReceive('switchToChildAccount')
            ->with($parentId, $childId)
            ->andReturn(
                new ChildAccountsResponse(
                    true,
                    '',
                    'MESSAGE_CODE_SUCCESS',
                    'test-token',
                    'test-refresh-token'
                )
            );
        app()->instance(AuthenticationClient::class, $authClientMock);

        $eventManager = \Mockery::mock(EventManager::class);
        $eventManager
            ->shouldReceive('emit')
            ->withArgs(function(string $event, array $params) use ($child, $device) {
                self::assertEquals(UserLoggedIn::class, $event);
                self::assertEquals($child, $params[0]);
                self::assertEquals(['device' => $device], $params[1]);

                return true;
            })
            ->once();
        app()->instance(EventManager::class, $eventManager);

        $url = sprintf('2.2/users/%s/parent-login/%s', $parentId, $childId);
        $result = $this->testAction($url, ['method' => 'POST', 'data' => $data]);
        $result = json_decode($result, true, 512, JSON_THROW_ON_ERROR);

        $this->assertEquals([
            'success' => true,
            'message' => '',
            'message_code' => 'MESSAGE_CODE_SUCCESS',
            'token' => 'test-token',
            'refresh_token' => 'test-refresh-token'
        ], $result);

        app()->forgetInstance(AuthenticationClient::class);
        app()->forgetInstance(EventManager::class);
    }

    public function testItGeneratesATokenWhenEmptyDeviceAndNotMobileRequest(): void
    {
        $parentId = '5c8b0615deb2eae76ec28673';
        $_SERVER['HTTP_AUTHORIZATION'] = $this->generateToken([
            '_id' => $parentId,
            'branch_id' => '5c783d4cd510f9635ad4a6b3'
        ]);
        $_SERVER['HTTP_X_GLOFOX_SOURCE'] = 'DASHBOARD';
        $childId = '5c8b0642deb2eae76ec28674';
        $child = $this->getUser($childId);

        $authClientMock = \Mockery::mock(AuthenticationClient::class);
        $authClientMock
            ->shouldReceive('switchToChildAccount')
            ->with($parentId, $childId)
            ->andReturn(
                new ChildAccountsResponse(
                    true,
                    '',
                    'MESSAGE_CODE_SUCCESS',
                    'test-token',
                    'test-refresh-token'
                )
            );
        app()->instance(AuthenticationClient::class, $authClientMock);

        $eventManager = \Mockery::mock(EventManager::class);
        $eventManager
            ->shouldReceive('emit')
            ->withArgs(function(string $event, array $params) use ($child) {
                self::assertEquals(UserLoggedIn::class, $event);
                self::assertEquals($child, $params[0]);
                self::assertEquals(['device' => []], $params[1]);

                return true;
            })
            ->once();
        app()->instance(EventManager::class, $eventManager);

        $url = sprintf('2.2/users/%s/parent-login/%s', $parentId, $childId);
        $result = $this->testAction($url, ['method' => 'POST', 'data' => []]);
        $result = json_decode($result, true, 512, JSON_THROW_ON_ERROR);

        $this->assertEquals([
            'success' => true,
            'message' => '',
            'message_code' => 'MESSAGE_CODE_SUCCESS',
            'token' => 'test-token',
            'refresh_token' => 'test-refresh-token'
        ], $result);

        app()->forgetInstance(AuthenticationClient::class);
        app()->forgetInstance(EventManager::class);
    }

    public function testItThrowsExceptionWhenEmptyDeviceInMobileRequest(): void
    {
        $parentId = '5c8b0615deb2eae76ec28673';
        $_SERVER['HTTP_AUTHORIZATION'] = $this->generateToken([
            '_id' => $parentId,
            'branch_id' => '5c783d4cd510f9635ad4a6b3'
        ]);
        $childId = '5c8b0642deb2eae76ec28674';
        $_SERVER['HTTP_X_GLOFOX_SOURCE'] = 'MEMBER_APP';

        $url = sprintf('2.2/users/%s/parent-login/%s', $parentId, $childId);
        $result = $this->testAction($url, ['method' => 'POST', 'data' => []]);
        $result = json_decode($result, true, 512, JSON_THROW_ON_ERROR);

        $this->assertFalse($result['success']);
        $this->assertTextEquals('DEVICE_INFO_SHOULD_BE_PROVIDED', $result['message']);

        app()->forgetInstance(Request::class);
    }

    public function testItThrowsExceptionWhenChildNotFound(): void
    {
        $parentId = '5c8b0615deb2eae76ec28673';

        $_SERVER['HTTP_AUTHORIZATION'] = $this->generateToken([
            '_id' => $parentId,
            'branch_id' => '5c783d4cd510f9635ad4a6b3'
        ]);

        $childId = '5c8b0642deb2eae76ec28655';
        $data = [
            'device' => ['os' => 'ios', 'version' => '8.2.6'],
        ];

        $url = sprintf('2.2/users/%s/parent-login/%s', $parentId, $childId);
        $result = $this->testAction($url, ['method' => 'POST', 'data' => $data]);
        $result = json_decode($result, true, 512, JSON_THROW_ON_ERROR);

        $this->assertFalse($result['success']);
        $this->assertTextEquals('User not found - id: 5c8b0642deb2eae76ec28655', $result['message']);
    }

    public function testItThrowsExceptionWhenLoggedUserIsNotAParent(): void
    {
        $_SERVER['HTTP_AUTHORIZATION'] = $this->generateToken();
        $parentId = '5c8b0615deb2eae76ec28673';
        $childId = '5c8b0642deb2eae76ec28674';
        $data = [
            'device' => ['os' => 'ios', 'version' => '8.2.6'],
        ];

        $url = sprintf('2.2/users/%s/parent-login/%s', $parentId, $childId);
        $result = $this->testAction($url, ['method' => 'POST', 'data' => $data]);
        $result = json_decode($result, true, 512, JSON_THROW_ON_ERROR);

        $this->assertFalse($result['success']);
        $this->assertTextEquals('Unauthorized', $result['message']);
    }

    public function testItThrowsExceptionWhenLoggedParentIsNotTheSameInTheUrl(): void
    {
        $parentId = '5c8b0615deb2eae76ec28673';

        $_SERVER['HTTP_AUTHORIZATION'] = $this->generateToken([
            '_id' => $parentId,
            'branch_id' => '5c783d4cd510f9635ad4a6b3'
        ]);

        $childId = '5c8b0642deb2eae76ec28674';
        $data = [
            'device' => ['os' => 'ios', 'version' => '8.2.6'],
        ];

        $url = sprintf('2.2/users/5c7828a4d510f9635ad4a6b1/parent-login/%s', $childId);
        $result = $this->testAction($url, ['method' => 'POST', 'data' => $data]);
        $result = json_decode($result, true, 512, JSON_THROW_ON_ERROR);

        $this->assertFalse($result['success']);
        $this->assertTextEquals('Unauthorized', $result['message']);
    }

    public function testItThrowsExceptionWhenChildIdInTheUrlIsNotAChild(): void
    {
        $parentId = '5c8b0615deb2eae76ec28673';

        $_SERVER['HTTP_AUTHORIZATION'] = $this->generateToken([
            '_id' => $parentId,
            'branch_id' => '5c783d4cd510f9635ad4a6b3'
        ]);

        $data = [
            'device' => ['os' => 'ios', 'version' => '8.2.6'],
        ];

        $url = sprintf('2.2/users/%s/parent-login/5c7828a4d510f9635ad4a6b1', $parentId);
        $result = $this->testAction($url, ['method' => 'POST', 'data' => $data]);
        $result = json_decode($result, true, 512, JSON_THROW_ON_ERROR);

        $this->assertFalse($result['success']);
        $this->assertTextEquals('THE_USER_IS_NOT_A_CHILD', $result['message_code']);
    }

    public function testItThrowsExceptionWhenParentIdTheUrlIsNotAParent(): void
    {
        $parentId = '5c8b0615deb2eae76ec28123';

        $_SERVER['HTTP_AUTHORIZATION'] = $this->generateToken([
            '_id' => $parentId,
            'branch_id' => '5c783d4cd510f9635ad4a6b3'
        ]);

        $childId = '5c8b0642deb2eae76ec28674';
        $data = [
            'device' => ['os' => 'ios', 'version' => '8.2.6'],
        ];

        $url = sprintf('2.2/users/%s/parent-login/%s', $parentId, $childId);
        $result = $this->testAction($url, ['method' => 'POST', 'data' => $data]);
        $result = json_decode($result, true, 512, JSON_THROW_ON_ERROR);

        $this->assertFalse($result['success']);
        $this->assertTextEquals('THE_USER_IS_NOT_A_PARENT_OF_THE_CHILD', $result['message_code']);
    }

    public function testItSavesTheDeviceInfoIfPresent(): void
    {
        $_SERVER['HTTP_AUTHORIZATION'] = $this->generateToken([
            '_id' => '5c8b0615deb2eae76ec28673',
            'branch_id' => '5c783d4cd510f9635ad4a6b3',
        ]);

        $this->testAction(
            '/2.2/users/5c8b0615deb2eae76ec28673/parent-login/5c8b0642deb2eae76ec28674',
            [
                'method' => 'POST',
                'data' => [
                    'device' => [
                        'id' => 'foo',
                        'os' => 'bar',
                        'version' => '1.2.3',
                    ],
                ],
            ]
        );

        $child = $this->getUser('5c8b0642deb2eae76ec28674');

        $this->assertNotEmpty($child->device());
        $this->assertEquals('foo', $child->device()->id());
        $this->assertEquals('bar', $child->device()->os());
        $this->assertEquals('1.2.3', $child->device()->version()->toString());
    }

    private function getUser(string $identifier): User
    {
        return app()->make(UsersRepository::class)
            ->addCriteria(new Id($identifier))
            ->firstOrFail();
    }
}
