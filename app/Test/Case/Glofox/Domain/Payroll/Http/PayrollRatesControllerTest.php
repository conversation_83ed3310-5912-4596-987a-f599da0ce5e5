<?php

declare(strict_types=1);

use CakeTestCases\Glofox\Domain\Users\Traits\AuthenticateUsersTrait;
use Carbon\Carbon;
use Glofox\Domain\Payroll\Models\EventType;
use Glofox\Domain\Payroll\Models\GetPayrollRateListResult;
use Glofox\Domain\Payroll\Models\PayrollRate;
use Glofox\Domain\Payroll\Models\PayrollRatesCollection;
use Glofox\Domain\Payroll\Models\Type;
use Glofox\Domain\Payroll\Repositories\PayrollRatesRepository;
use Glofox\Domain\Payroll\UseCase\GetPayrollRateList;

App::import('Test/Case', 'GlofoxControllerTestCase');

class PayrollRatesControllerTest extends GlofoxControllerTestCase
{
    use AuthenticateUsersTrait;

    private const BRANCH_ID = '64b803ccfc8ba4d6f8d99c9c';
    private const ADMIN_ID = '64b804217388a46d30844c1d';
    private const PROGRAM_1 = '64b7ec83ca6e0791480419d3';
    private const PAYROLL_RATE_1 = '64e5de884ad77bcc9887df9a';
    private const PAYROLL_RATE_APPOINTMENT_1 = '64e5de96e498759a4449fbc6';
    private const EVENT_CLASS = 'class';
    private const EVENT_APPOINTMENT = 'appointment';
    private const APPOINTMENT_1 = '64b8068173b491198db332bc';
    private const GROUP_ID_1 = '65206510e32d1f291a5f224a';
    public $fixtures = [
        'app.branch',
        'app.user',
        'app.payroll_rate',
        'app.payroll_rate_group',
        'app.program',
        'app.time_slot_pattern',
    ];

    public function tearDown()
    {
        parent::tearDown();
        Mockery::close();
    }

    public function testCheckGetRatesEndpoint(): void
    {
        $this->authenticateAsAdmin(self::ADMIN_ID);
        $uri = sprintf('/2.2/branches/%s/payroll-rates', self::BRANCH_ID);
        $response = $this->testAction($uri, ['method' => 'GET']);
        $result = json_decode($response, true, 512, JSON_THROW_ON_ERROR);

        $this->assertTrue(isset($result['data']));
        $this->assertEquals(true, (is_countable($result['data']) ? count($result['data']) : 0) >= 5);
        $rate = $result['data'][0];
        $this->assertEquals(self::PAYROLL_RATE_1, $rate['_id']);
        $this->assertEquals(self::PROGRAM_1, $rate['event_id']);
        $this->assertEquals('Payroll Class', $rate['event_name']);
        $this->assertEquals(self::EVENT_CLASS, $rate['event_type']);
        $this->assertEquals(20, $rate['rate_price']);
        $this->assertEquals('Appointment for Payroll 1', $result['data'][2]['event_name']);
    }

    public function testListReturnsProperResponse(): void
    {
        $totalCount = 123;
        $page = 1;
        $limit = 100;

        $this->authenticateAsAdmin(self::ADMIN_ID);

        $useCase = Mockery::mock(GetPayrollRateList::class)
            ->shouldReceive('execute')
            ->once()
            ->andReturn(new GetPayrollRateListResult($this->getMockedPayRates(), $totalCount))
            ->getMock();
        app()->instance(GetPayrollRateList::class, $useCase);

        $url = sprintf("/2.2/branches/%s/payroll-rates?page=%s&limit=%s&sort-by=3", self::BRANCH_ID, $page, $limit);

        $result = json_decode($this->testAction($url, ['method' => 'GET']), null, 512, JSON_THROW_ON_ERROR);

        $this->assertEquals('payrollRate', $result->object);
        $this->assertEquals($page, $result->page);
        $this->assertEquals($limit, $result->limit);
        $this->assertEquals(true, $result->hasMore);
        $this->assertEquals($totalCount, $result->totalCount);
        $this->assertEquals([
            '_id' => 'test-pay-rate-id-1',
            'event_type' => 'class',
            'event_id' => 'test-program-id-1',
            'event_name' => 'Test Class Name',
            'rate_price' => 1200,
            'rate_group_id' => 'test-group-id',
            'rate_bonus' => []
        ], get_object_vars($result->data[0]));
    }

    /**
     * @dataProvider getRateByIdDataProvider
     * @param string $rateId
     * @param array $expected
     * @return void
     */
    public function testGetRateByIdEndpoint(string $rateId, array $expected): void
    {
        $this->authenticateAsAdmin(self::ADMIN_ID);
        //

        $uri = sprintf('/2.2/branches/%s/payroll-rates/%s', self::BRANCH_ID, $rateId);
        $response = $this->testAction($uri, ['method' => 'GET']);
        $rate = json_decode($response, true, 512, JSON_THROW_ON_ERROR);

        $this->assertNotEmpty($rate);
        $this->assertEquals($rateId, $rate['_id']);
        $this->assertEquals($expected['event_id'], $rate['event_id']);
        $this->assertEquals($expected['event_name'], $rate['event_name']);
        $this->assertEquals($expected['event_type'], $rate['event_type']);
        $this->assertEquals($expected['rate_price'], $rate['rate_price']);
    }

    public function getRateByIdDataProvider(): array
    {
        return [
            'When class ID is provided, Then I want to get Class rate data' => [
                'rateId' => self::PAYROLL_RATE_1,
                'expected' => [
                    'event_id' => self::PROGRAM_1,
                    'event_name' => 'Payroll Class',
                    'event_type' => self::EVENT_CLASS,
                    'rate_price' => 20,
                ],
            ],
            'When appointment ID is provided, Then I want to get Appointment rate data' => [
                'rateId' => self::PAYROLL_RATE_APPOINTMENT_1,
                'expected' => [
                    'event_id' => self::APPOINTMENT_1,
                    'event_name' => 'Appointment for Payroll 1',
                    'event_type' => self::EVENT_APPOINTMENT,
                    'rate_price' => 40,
                ],
            ],
        ];
    }

    public function testAddNewRate(): void
    {
        $this->authenticateAsAdmin(self::ADMIN_ID);

        foreach (
            [
                self::EVENT_CLASS => self::PROGRAM_1,
                self::EVENT_APPOINTMENT => self::APPOINTMENT_1,
            ] as $eventType => $eventId
        ) {
            $payload = [
                'event_type' => $eventType,
                'event_id' => $eventId,
                'rate_type' => 'base',
                'rate_price' => 4550,
                'rate_group_id' => self::GROUP_ID_1,
            ];

            $url = sprintf('/2.2/branches/%s/payroll-rates', self::BRANCH_ID);

            $response = $this->testAction($url, ['method' => 'POST', 'data' => $payload]);
            $this->assertEquals(201, $this->response->statusCode());
            $rate = json_decode($response, true, 512, JSON_THROW_ON_ERROR);

            $this->assertNotEmpty($rate['_id']);
            $this->assertEquals($eventType, $rate['event_type']);
            $this->assertEquals($eventId, $rate['event_id']);
            $this->assertEquals(4550, $rate['rate_price']);
            $this->assertEmpty($rate['event_name']);
        }
    }

    public function testUpdateRate(): void
    {
        $this->authenticateAsAdmin(self::ADMIN_ID);
        $payrollRateId = '64e5de884ad77bcc9887df9a';

        $payload = [
            'event_type' => 'class',
            'event_id' => '64b7ec83ca6e0791480419d3',
            'rate_type' => 'base',
            'rate_price' => 3150,
            'rate_group_id' => self::GROUP_ID_1,
        ];

        $payrollRateRepository = app()->make(PayrollRatesRepository::class);

        $url = sprintf('/2.2/branches/%s/payroll-rates/%s', self::BRANCH_ID, $payrollRateId);
        $result = json_decode($this->testAction($url, ['method' => 'PUT', 'data' => $payload]), null, 512, JSON_THROW_ON_ERROR);

        $oldPayrollRate = $payrollRateRepository->getById($payrollRateId);
        $updatedPayrollRate = $payrollRateRepository->getById($result->_id);

        $this->assertNotEquals($payrollRateId, $result->_id);
        $this->assertEquals(
            Carbon::yesterday('UTC')->endOfDay()->getTimestamp(),
            $oldPayrollRate->utcEndDate()->getTimestamp()
        );
        $this->assertEquals(
            Carbon::today('UTC')->startOfDay()->getTimestamp(),
            $updatedPayrollRate->utcStartDate()->getTimestamp()
        );
        $this->assertEmpty($updatedPayrollRate->utcEndDate());

        $this->assertEquals('class', $result->event_type);
        $this->assertEquals('64b7ec83ca6e0791480419d3', $result->event_id);
        $this->assertEquals(3150, $result->rate_price);
        $this->assertEmpty($result->event_name);
    }

    public function testUpdateRateNotFound(): void
    {
        $this->authenticateAsAdmin(self::ADMIN_ID);

        foreach (['GET', 'PUT'] as $method) {
            $response = $this->sendPayrollRate([
                'event_type' => self::EVENT_CLASS,
                'event_id' => self::PROGRAM_1,
                'rate_type' => 'base',
                'rate_price' => 200,
                'rate_group_id' => self::GROUP_ID_1,
            ], $method, '64e5de884ad77fff9887df9f');
            $this->assertEquals(404, $this->response->statusCode());
            $data = json_decode($response, true, 512, JSON_THROW_ON_ERROR);
            $this->assertFalse($data['success']);
            $this->assertEquals('INVALID_PAYROLL_RATE_ID', $data['message_code']);
        }
    }

    public function testAddRateTypeValidation(): void
    {
        $this->authenticateAsAdmin(self::ADMIN_ID);
        foreach (['POST', 'PUT'] as $method) {
            $response = $this->sendPayrollRate([
                'event_type' => 'bad-data',
                'event_id' => self::PROGRAM_1,
                'rate_type' => 'base',
                'rate_price' => 200,
                'rate_group_id' => self::GROUP_ID_1,
            ], $method, self::PAYROLL_RATE_1);
            $this->assertEquals(400, $this->response->statusCode());
            $data = json_decode($response, true, 512, JSON_THROW_ON_ERROR);
            $this->assertFalse($data['success']);
            $this->assertEquals('PAYROLL_RATE_INVALID_EVENT_TYPE', $data['message']);
        }
    }

    public function testAddRateEventIdValidation(): void
    {
        $this->authenticateAsAdmin(self::ADMIN_ID);
        foreach (['POST', 'PUT'] as $method) {
            $response = $this->sendPayrollRate([
                'event_type' => self::EVENT_CLASS,
                'event_id' => '',
                'rate_type' => 'base',
                'rate_price' => 200,
                'rate_group_id' => self::GROUP_ID_1,
            ], $method, self::PAYROLL_RATE_1);
            $this->assertEquals(400, $this->response->statusCode());
            $data = json_decode($response, true, 512, JSON_THROW_ON_ERROR);
            $this->assertFalse($data['success']);
            $this->assertEquals('PAYROLL_RATE_MISSING_EVENT_ID_FIELD', $data['message']);
        }
    }

    public function testAddRateRateTypValidation(): void
    {
        $this->authenticateAsAdmin(self::ADMIN_ID);
        foreach (['POST', 'PUT'] as $method) {
            $response = $this->sendPayrollRate([
                'event_type' => self::EVENT_CLASS,
                'event_id' => self::PROGRAM_1,
                'rate_type' => '',
                'rate_price' => 200,
                'rate_group_id' => self::GROUP_ID_1,
            ], $method, self::PAYROLL_RATE_1);
            $this->assertEquals(400, $this->response->statusCode());
            $data = json_decode($response, true, 512, JSON_THROW_ON_ERROR);
            $this->assertFalse($data['success']);
            $this->assertEquals('PAYROLL_RATE_MISSING_RATE_TYPE_FIELD', $data['message']);
        }
    }

    public function testAddRateRatePriceValidation(): void
    {
        $this->authenticateAsAdmin(self::ADMIN_ID);
        foreach (['POST', 'PUT'] as $method) {
            $response = $this->sendPayrollRate([
                'event_type' => self::EVENT_CLASS,
                'event_id' => self::PROGRAM_1,
                'rate_type' => 'base',
                'rate_price' => 'aassasa',
                'rate_group_id' => self::GROUP_ID_1,
            ], $method, self::PAYROLL_RATE_1);
            $this->assertEquals(400, $this->response->statusCode());
            $data = json_decode($response, true, 512, JSON_THROW_ON_ERROR);
            $this->assertFalse($data['success']);
            $this->assertEquals(
                'PAYROLL_RATE_PRICE_MUST_BE_AN_INTEGER PAYROLL_RATE_PRICE_MIN_ERROR',
                $data['message']
            );
        }
    }

    private function sendPayrollRate(array $data, $method = 'POST', $id = '')
    {
        $uri = sprintf('/2.2/branches/%s/payroll-rates', self::BRANCH_ID);
        if (in_array($method, ['GET', 'PUT'])) {
            $uri .= '/' . $id;
        }
        return $this->testAction($uri, [
            'method' => $method,
            'data' => $data,
        ]);
    }

    private function getMockedPayRates(): PayrollRatesCollection
    {
        return new PayrollRatesCollection([
            PayrollRate::make([
                '_id' => 'test-pay-rate-id-1',
                'type' => Type::BASE,
                'model' => EventType::INTERNAL_CLASS_TYPE,
                'model_id' => 'test-program-id-1',
                'value' => 1200,
                'payroll_rate_group_id' => 'test-group-id',
            ])->setEventName('Test Class Name'),
        ]);
    }
}
