<?php

namespace CakeTestCases\Glofox\Payments\Entities\Customer\Resolvers;

use CakeTestCases\Glofox\Domain\Authentication\Token\TokenGeneration;
use CakeTestCases\Glofox\Domain\Users\Traits\FetchUsersTrait;
use Glofox\Domain\Cards\Models\Card;
use Glofox\Domain\Cards\Repositories\CardsRepository;
use Glofox\Domain\Mandates\Models\Mandate;
use Glofox\Domain\Mandates\Repository\MandatesRepository;
use Glofox\Domain\PaymentMethods\Models\PaymentMethod;
use Glofox\Domain\PaymentMethods\Repositories\PaymentMethodsRepository;
use Glofox\Domain\PaymentMethods\Search\Expressions\PaymentMethodId;
use Glofox\Domain\PaymentMethodUsers\Models\PaymentMethodUser;
use Glofox\Domain\PaymentMethodUsers\Repository\PaymentMethodUsersRepository;
use Glofox\Domain\PaymentProviders\Models\PaymentProvider;
use Glofox\Domain\Users\Exceptions\UserNotFoundException;
use Glofox\Domain\Users\Models\User;
use Glofox\Domain\Users\Repositories\UsersRepository;
use Glofox\Domain\Users\Search\Expressions\UserId;
use Glofox\Payments\Contracts\PaymentProviderContract;
use Glofox\Payments\Entities\Customer\Resolvers\IdResolver;
use Glofox\Payments\PaymentsHandler;
use Glofox\Repositories\Search\Expressions\Shared\Id;
use MongoDB\BSON\ObjectId;
use MongoId;

\App::import('Test/Case', 'GlofoxTestCase');

final class IdResolverTest extends \GlofoxTestCase
{
    use FetchUsersTrait;
    use TokenGeneration;

    public $fixtures = [
        'user',
        'branch',
        'card',
        'mandate',
        'payment_method',
        'payment_method_user',
        'payment_provider',
    ];

    public function setUp()
    {
        parent::setUp();

        $this->loginAsUser(
            $this->fetchUser('5c8b0642deb2eae76ec28675')
        );
    }

    public function tearDown(): void
    {
        parent::tearDown();
        \Mockery::close();
    }

    public function test_it_gets_the_parent_custom_provider_id_if_the_user_is_a_child(): void
    {
        $child = $this->fetchUser('5c8b0642deb2eae76ec28675');
        $parentCard = $this->getCardFromUser($child->parent());

        $customerProviderId = IdResolver::fromUserIdAndPaymentMethodId(
            $child->id(), $parentCard->paymentMethodId()
        );

        $parentPaymentMethodUser = $this->getPaymentMethodUserFromCard($parentCard);
        $this->assertEquals($parentPaymentMethodUser->customerProviderId(), $customerProviderId);
    }

    public function test_it_gets_the_parent_custom_provider_id_for_mandates_payment_type(): void
    {
        $child = $this->fetchUser('5c8b0642deb2eae76ec28675');
        $parentMandate = $this->getFirstMandateForUser($child->parent());

        $customerProviderId = IdResolver::fromUserIdAndPaymentMethodId(
            $child->id(), $parentMandate->paymentMethodId()
        );

        $parentPaymentMethodUser = $this->getPaymentMethodUserFromMandate($parentMandate);
        $this->assertEquals($parentPaymentMethodUser->customerProviderId(), $customerProviderId);
    }

    public function test_it_fails_creating_customer_if_user_does_not_exist(): void
    {
        $paymentMethodId = (string) (new MongoId());
        $userId = (string) (new MongoId());

        $paymentMethodRepo = \Mockery::mock(PaymentMethodsRepository::class);
        $paymentMethodRepo
            ->shouldReceive('addCriteria')
            ->withArgs(function (Id $id) use ($paymentMethodId) {
                $this->assertEquals(new Id($paymentMethodId), $id);

                return true;
            })
            ->getMock()
            ->shouldReceive('first')
            ->andReturn(
                $paymentMethod = new PaymentMethod(['_id' => 'pm-123'])
            )
            ->getMock();
        app()->instance(PaymentMethodsRepository::class, $paymentMethodRepo);

        $paymentHandler = \Mockery::mock(PaymentsHandler::class);
        $paymentHandler
            ->shouldReceive('providerByPaymentMethod')
            ->with($paymentMethod)
            ->andReturn(
                \Mockery::mock(PaymentProviderContract::class)
                    ->shouldReceive('paymentProvider')
                    ->andReturn(
                        \Mockery::mock(PaymentProvider::class)
                            ->shouldReceive('customerRegistrationRequired')
                            ->andReturnTrue()
                            ->getMock()
                    )
                    ->getMock()
            );
        app()->instance(PaymentsHandler::class, $paymentHandler);

        $usersRepo = \Mockery::mock(UsersRepository::class);
        $usersRepo
            ->shouldReceive('skipCallbacks')
            ->andReturnSelf()
            ->getMock()
            ->shouldReceive('addCriteria')
            ->withArgs(function (Id $id) use ($userId) {
                $this->assertEquals(new Id($userId), $id);

                return true;
            })
            ->getMock()
            ->shouldReceive('firstOrFail')
            ->andThrow(new UserNotFoundException('user not found'))
            ->shouldReceive('setReadConcern');
        app()->instance(UsersRepository::class, $usersRepo);

        $method = new \ReflectionMethod(IdResolver::class, 'createCustomer');
        $method->setAccessible(true);

        try {
            $method->invokeArgs(null, [$userId, $paymentMethodId, null]);
        } catch (UserNotFoundException $e) {
            $this->assertSame('user not found', $e->getMessage());
        }

        app()->forgetInstance(PaymentMethodsRepository::class);
        app()->forgetInstance(PaymentsHandler::class);
        app()->forgetInstance(UsersRepository::class);
    }

    private function getCardFromUser(User $user): Card
    {
        return app()->make(CardsRepository::class)
            ->getFirstCardByUserId($user->id());
    }

    private function getPaymentMethodUserFromCard(Card $card): PaymentMethodUser
    {
        return app()->make(PaymentMethodUsersRepository::class)
            ->addCriteria(new Id($card->paymentMethodUserId()))
            ->firstOrFail();
    }

    private function getFirstMandateForUser(User $user): Mandate
    {
        return app()->make(MandatesRepository::class)
            ->addCriteria(new UserId($user->id()))
            ->firstOrFail();
    }

    private function getPaymentMethodUserFromMandate(Mandate $mandate): PaymentMethodUser
    {
        return app()->make(PaymentMethodUsersRepository::class)
            ->addCriteria(new UserId($mandate->userId()))
            ->addCriteria(new PaymentMethodId($mandate->paymentMethodId()))
            ->firstOrFail();
    }
}
