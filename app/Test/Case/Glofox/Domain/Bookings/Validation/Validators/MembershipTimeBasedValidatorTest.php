<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\Bookings\Validation\Validators;

use App;
use Glofox\Datasource\Exceptions\InvalidMongoIdException;
use Glofox\Domain\Bookings\Models\BookingSlot;
use Glofox\Domain\Bookings\Validation\Validators\MembershipTimeBasedValidator;
use Glofox\Domain\Branches\Models\Branch;
use Glofox\Domain\Memberships\Type;
use Glofox\Domain\Users\Models\User;
use Glofox\Domain\Users\Models\UserMembership;
use Glofox\Validation\Exceptions\ValidationException;
use GlofoxTestCase;

App::import('Test/Case', 'GlofoxTestCase');

class MembershipTimeBasedValidatorTest extends GlofoxTestCase
{
    public $fixtures = [];

    public function tearDown()
    {
        parent::tearDown();
        \Mockery::close();
    }

    /**
     * @dataProvider getMembershipTypes
     * @throws ValidationException|InvalidMongoIdException
     */
    public function testMembershipsTypesForRecurringClassBooking(string $type, bool $exception): void
    {
        $membership = UserMembership::make([
            'type' => $type,
        ]);

        $bookingSlotMock = \Mockery::mock(BookingSlot::class);
        $bookingSlotMock->shouldReceive('getMembership')
            ->andReturn($membership);

        if ($exception) {
            $this->setExpectedException(ValidationException::class);
        }

        $membershipTimeBaseValidator = app()->make(MembershipTimeBasedValidator::class);
        $membershipTimeBaseValidator->validate($bookingSlotMock);
    }

    public function getMembershipTypes(): array
    {
        return [
            'Given a payg membership, Then it should throw an exception' => [
                'type' => Type::PAYG,
                'exception' => true,
            ],
            'Given an unlimited membership, Then it should not throw an exception' => [
                'type' => Type::TIME,
                'exception' => false,
            ],
            'Given a restricted membership, Then it should not throw an exception' => [
                'type' => Type::TIME_CLASSES,
                'exception' => false,
            ],
        ];
    }
}
