<?php

use Carbon\Carbon;
use Glofox\Payments\Providers\Gateway\ProtoParsers\SubscriptionParser;

\App::import('Test/Case', 'GlofoxTestCase');

class SubscriptionParserTest extends \GlofoxTestCase
{
    private ?\Glofox\Payments\Providers\Gateway\ProtoParsers\SubscriptionParser $parser = null;

    public function setUp()
    {
        parent::setUp();

        $this->parser = new SubscriptionParser();
    }

    public function test_it_should_parse_subscription_from_proto_message()
    {
        self::markTestSkipped();
        $subscription = $this->parser->parseFromProtoMessage($this->getMockedSubscription());

        $this->assertInstanceOf(
            \Glofox\Payments\Entities\Subscription\Models\Subscription::class,
            $subscription
        );

        $this->assertEquals([
            'branch_id' => '111'
        ], $subscription->metadata());

        $this->assertEquals(6000, $subscription->amount());
    }

    private function getMockedSubscription()
    {
        $icalString = \file_get_contents(
            sprintf('%s/IcalFixtures/%s', __DIR__.'/../../../../Calendar/ICalendar', 'ical_with_2_simple_events.ical')
        );
        $subscription = \Mockery::mock(\GRPC\Payments\SubscriptionService\Subscription::class);
        $subscription
            ->shouldReceive('getID')
            ->andReturn('1')
            ->shouldReceive('getCustomerID')
            ->andReturn('1')
            ->shouldReceive('getMerchantID')
            ->andReturn('1')
            ->shouldReceive('getStartAt')
            ->andReturn(Carbon::now()->toDateTimeString())
            ->shouldReceive('getLastExecutedAt')
            ->andReturn(Carbon::now()->toDateTimeString())
            ->shouldReceive('getDeletedAt')
            ->andReturn(Carbon::now()->toDateTimeString())
            ->shouldReceive('getICalRule')
            ->andReturn($icalString)
            ->shouldReceive('getCurrentRetries')
            ->andReturn(0)
            ->shouldReceive('getCreatedAt')
            ->andReturn(Carbon::now()->toDateTimeString())
            ->shouldReceive('getUpdatedAt')
            ->andReturn(Carbon::now()->toDateTimeString());

        return $subscription;
    }
}
