<?php

namespace Glofox\Domain\Users\Search;

use Glofox\Repositories\Filters\ConditionsCollection;
use Glofox\Repositories\Repository;
use Glofox\Repositories\Search\Expressions\Conditionable;
use Glofox\Repositories\Search\Expressions\ConditionableCollection;
use Glofox\Repositories\Search\SearchContract;
use Illuminate\Support\Collection;

class UserSearchCriteriaGroup implements SearchContract, Conditionable
{
    /**
     * @var ConditionableCollection
     */
    protected $conditions;

    protected $value;

    public function __construct()
    {
        $this->conditions = ConditionsCollection::make();
    }

    public function for($value): Conditionable
    {
        $this->value = $value;

        return $this;
    }

    public function generateConditions(): Collection
    {
        $this->conditions->push([
            '$text' => [
                '$search' => $this->value,
                '$caseSensitive' => false,
                '$diacriticSensitive' => false,
            ],
        ]);

        return $this->conditions;
    }

    public function applyToConditions(ConditionsCollection $conditions, Repository $repository = null)
    {
        $generatedConditions = $this->generateConditions();

        $conditions->push([
            '$or' => $generatedConditions->toArray(),
        ]);

        $repository->fields([
            'score' => [
                '$meta' => 'textScore',
            ],
        ]);

        $repository->order([
            'score' => [
                '$meta' => 'textScore',
            ],
        ]);
    }

    public function evaluate($value)
    {
        /** @var Conditionable $condition */
        foreach ($this->conditions as $condition) {
            if (true === $condition->evaluate($value)) {
                return true;
            }
        }

        return false;
    }

    public function field(): string
    {
        return '';
    }
}
