const chunk = 1000;

let appointmentsBulk = db.time_slot_patterns.initializeUnorderedBulkOp();
let count = 0;

db.time_slot_patterns.find({
    'created': {
        '$type': 'object',
    },
}).forEach((appointment) => {
    let created = new Date(appointment.created.sec * 1000)

    appointmentsBulk.find({ _id: appointment._id }).updateOne({
        $set: {
            'created': new ISODate(created.toISOString()),
        },
    });

    if (++count === chunk) {
        appointmentsBulk.execute();
        appointmentsBulk = db.time_slot_patterns.initializeUnorderedBulkOp();
        count = 0;
    }
})

if (count > 0) {
    appointmentsBulk.execute();
}
