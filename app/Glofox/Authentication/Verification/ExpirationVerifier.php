<?php

declare(strict_types=1);

namespace Glofox\Authentication\Verification;

use Emarref\Jwt\Token;
use Glofox\AuditLog\Resolvers\OriginByRequestResolver;
use Glofox\Http\Source;

/**
 * <AUTHOR>
 */
class ExpirationVerifier extends \Emarref\Jwt\Verification\ExpirationVerifier
{
    protected static ?OriginByRequestResolver $originByRequestResolver = null;

    /**
     * @param Token $token
     * @return void
     * @throws \Emarref\Jwt\Exception\ExpiredException
     */
    public function verify(Token $token): void
    {
        // Avoid issues with legacy apps.
        if (!$this->shouldVerify($token)) {
            return;
        }

        parent::verify($token);
    }

    protected function shouldVerify(Token $token): bool
    {
        $source = $this->originByRequestResolver()->resolve(\Router::getRequest());

        if ($source->is(Source::IMPORTS())) {
            return false;
        }

        if ($source->is(Source::UNKNOWN())) {
            return false;
        }

        return true;
    }

    protected function originByRequestResolver(): OriginByRequestResolver
    {
        if (!self::$originByRequestResolver) {
            self::$originByRequestResolver = app()->make(OriginByRequestResolver::class);
        }
        return self::$originByRequestResolver;
    }
}
