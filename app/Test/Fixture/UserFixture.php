<?php

use Carbon\Carbon;
use Glofox\Domain\Leads\Status;
use Glofox\Domain\Memberships\Type;

class UserFixture extends CakeTestFixture
{
    private const VIRTUAL_SLOT_BRANCH_ID = '6461ed6fdef3e8f4c6d4b00c';
    private const VIRTUAL_SLOT_BRANCH_NAMESPACE = 'virtualslotstest';
    private const GLOFOX_BRANCH_ID = '49a7011a05c677b9a916612a';
    public $import = 'User';

    public function init()
    {
        $this->records = [
            ["_id" => new MongoId("606d649eedb582159a46ef09"),
                "membership" => [
                    "_id" => "6044c2ac1453065a5b321bbf",
                    "type" => "time_classes",
                    "membership_group_id" => null,
                    "plan_code" => 1_618_831_155_124,
                    "plan_price" => 45,
                    "plan_upfront_fee" => 0,
                    "boooked_events" => 0,
                    "branch_id" => self::GLOFOX_BRANCH_ID,
                    "branch_name" => "Glofox",
                    "starts_on" => "PURCHASE_DATE",
                    "roaming_enabled" => false,
                    "start_date" => "2021-08-01",
                    "subscription" => [
                        "subscription_plan_id" => "607d67ad243a1f04e82ded4d",
                        "interval" => "month",
                        "interval_count" => 1,
                        "price" => 45,
                        "upfront_fee" => 0,
                        "force_start" => true,
                        "credits" => [
                            [
                                "branch_id" => self::GLOFOX_BRANCH_ID,
                                "namespace" => "aptitudehealthfitnes",
                                "active" => true,
                                "model" => "programs",
                                "category_id" => null,
                                "num_sessions" => 20,
                                "model_ids" => [
                                    "605372db4a815b6f257b552e",
                                    "5f60e577d9f5d25c593cc9c6",
                                    "5f60e790df891658aa394011",
                                    "5f6123a5a2441b014204ea66",
                                    "608e639fe89b297c3a01a78c",
                                    "5fc96518603e3e40664677fb",
                                    "5f612212da11300ea537e810",
                                    "5f6122f074f0d60fab605a6b",
                                    "608e66c82fef040e5f743046",
                                    "5f60e1a589039e4c8b213732",
                                    "608e693b3a69bc11147ebe62",
                                    "5f611db274f0d60a033469e6",
                                    "5f612533da1130133b2cc123",
                                    "605374337011a74ec37d62a1",
                                    "5f611d2a7c33737c4165d417",
                                    "5f6cc24bbe23cf00354cc321",
                                    "5f61227974f0d60fc10ee19b",
                                    "6130b8f64769273ed30b4e9c",
                                    "6130be74f0dbd7739e6ef7e2",
                                ],
                                "expiry" => [
                                    "interval" => "month",
                                    "interval_count" => 1,
                                ],
                                "end_date" => null,
                            ],
                        ],
                        "stripe_id" => "842258",
                        "payment_gateway_id" => "842258",
                        "payment_method_type_id" => "CARD",
                    ],
                    "expiry_date" => "2021-09-01",
                    "user_membership_id" => "60e56a2d8ca664531b2a2c4a",
                    "status" => "ACTIVE",
                ],
                "first_name" => "dasdasd",
                "last_name" => "dasdasdas",
                "phone" => "dasdasdas",
                "email" => "<EMAIL>",
                "branch_id" => self::GLOFOX_BRANCH_ID,
                "birth" => "1978-06-05",
                "type" => "MEMBER",
                "active" => true,
                "namespace" => "glofox",
                "modified" => "2021-06-15 09:32:05",
                "created" => "2020-07-30 09:28:53",
                "origin_branch_id" => self::GLOFOX_BRANCH_ID,
                "name" => "Test Credit during Booking",
                "user_id" => "606d649eedb582159a46ef09",
            ],
            [
                "_id" => new MongoId("5f2292d576f2490ee93de046"),
                "membership" => [
                    "_id" => "5f2299ace708c61dbd3f2aaf",
                    "type" => "time_classes",
                    "membership_group_id" => null,
                    "plan_code" => 1_611_551_283_945,
                    "plan_price" => 1310,
                    "plan_upfront_fee" => 0,
                    "boooked_events" => 0,
                    "branch_id" => "49a7011a05c677b9a916614d",
                    "branch_name" => "Glofox Singapore",
                    "starts_on" => "PURCHASE_DATE",
                    "roaming_enabled" => false,
                    "start_date" => "2021-07-13",
                    "subscription" => [
                        "subscription_plan_id" => "600e52d7fbc81d261f04be8f",
                        "interval" => "week",
                        "interval_count" => 5,
                        "price" => 1310,
                        "upfront_fee" => 0,
                        "force_start" => false,
                        "duration" => 6,
                        "credits" => [
                            [
                                "branch_id" => "49a7011a05c677b9a916614d",
                                "namespace" => "glofoxsingapore",
                                "active" => true,
                                "model" => "programs",
                                "category_id" => null,
                                "num_sessions" => 10,
                                "model_ids" => [
                                ],
                                "expiry" => [
                                    "interval" => "week",
                                    "interval_count" => 5,
                                ],
                                "end_date" => null,
                            ],
                            [
                                "branch_id" => "49a7011a05c677b9a916614d",
                                "namespace" => "glofoxsingapore",
                                "active" => true,
                                "model" => "users",
                                "category_id" => null,
                                "num_sessions" => 5,
                                "model_ids" => null,
                                "expiry" => [
                                    "interval" => "week",
                                    "interval_count" => 5,
                                ],
                                "end_date" => null,
                            ],
                        ],
                        "end_date" => "2021-09-21",
                        "stripe_id" => "627531",
                        "payment_gateway_id" => "627531",
                        "payment_method_type_id" => "CARD",
                    ],
                    "expiry_date" => (new Carbon())->modify('+10 days')->format('Y-m-d'),
                    "user_membership_id" => "6033371cb1876b1928ccccef",
                    "status" => "ACTIVE",
                    "name" => "Hybrid Subscriptions (30 Wks 2 Group + 1 Private Training)",
                ],
                "first_name" => "dasdasd",
                "last_name" => "dasdasdas",
                "phone" => "dasdasdas",
                "email" => "<EMAIL>",
                "branch_id" => "49a7011a05c677b9a916614d",
                "birth" => "1978-06-05",
                "type" => "MEMBER",
                "active" => true,
                "namespace" => "glofoxsingapore",
                "modified" => "2021-06-15 09:32:05",
                "created" => "2020-07-30 09:28:53",
                "origin_branch_id" => "49a7011a05c677b9a916614d",
                "name" => "Test Credit in Advance",
                "user_id" => "5f2292d576f2490ee93de046",
            ],
            [
                '_id' => new MongoId('59a7011a05c677bda512212a'),
                'branch_id' => '5addc25383266f65abf515c4',
                'namespace' => 'classpass_integrated',
                'active' => true,
                'categories' => [],
                'type' => 'ADMIN',
                'name' => 'Admin Istrator',
                'first_name' => 'Admin',
                'last_name' => 'Istrator',
                'phone' => '65132056768451320',
                'description' => 'Worst Trainer Ever!',
                'email' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'bookable' => true,
                'login' => '<EMAIL>',
                'experience' => 'A lot...',
                'gender' => 'M',
                'birth' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'twitter' => '@timmy',
                'facebook' => 'https://facebook.com/timmy',
                'phone' => '123123321',
                'bookable' => false,
                'stripe_token' => null,
                'answers' => [],
                'card_id' => null,
                'stripe_customer_id' => null,
                'stripe_cc_token' => null,
                'auto_generated' => false,
                'access_barcode' => '390943430434',
                'created' => new MongoDate(),
                'modified' => new MongoDate(),
                'last_login' => new MongoDate(),
                'strike' => 0,
                'private_desc' => 'Why so secret?',
                'membership' => [
                    'type' => 'payg',
                ],
                'device' => [
                    'os' => 'android',
                ],
                '_pattern' => null,
                'version' => [
                    'minor' => 9,
                    'major' => 9,
                    'revision' => 25,
                ],
                'WAIVER' => true,
                'MEMBERPURCHASE' => true,
                'PAYGPAYMENT' => true,
                'transaction' => null,
                'emergency_contact' => '<EMAIL>',
                'pwd_token' => null,
            ],
            [
                '_id' => new MongoId('59a7011a05c677bda916612c'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'categories' => [],
                'type' => 'TRAINER',
                'name' => 'Trainer Name',
                'first_name' => 'Trainer',
                'last_name' => 'Name',
                'phone' => '0899991827',
                'description' => 'Worst Trainer Ever!',
                'email' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'bookable' => true,
                'login' => '<EMAIL>',
                'experience' => 'A lot...',
                'gender' => 'M',
                'birth' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'twitter' => '@timmy',
                'facebook' => 'https://facebook.com/timmy',
                'phone' => '123123321',
                'bookable' => false,
                'stripe_token' => null,
                'answers' => [],
                'card_id' => null,
                'stripe_customer_id' => null,
                'stripe_cc_token' => null,
                'auto_generated' => false,
                'access_barcode' => '924892480209384',
                'created' => new MongoDate(),
                'modified' => new MongoDate(),
                'last_login' => new MongoDate(),
                'strike' => 0,
                'private_desc' => 'Why so secret?',
                'membership' => null,
                'device' => [
                    'os' => 'android',
                ],
                '_pattern' => null,
                'version' => [
                    'minor' => 9,
                    'major' => 9,
                    'revision' => 25,
                ],
                'WAIVER' => true,
                'MEMBERPURCHASE' => true,
                'PAYGPAYMENT' => true,
                'transaction' => null,
                'emergency_contact' => '<EMAIL>',
                'pwd_token' => null,
            ],
            [
                '_id' => new MongoId('59a7011a05c677bda916612d'),
                'branch_id' => [
                    '49a7011a05c677b9a916612a',
                    '49a7011a05c677b9a916612b',
                    '49a7011a05c677b9a916612c',
                ],
                'namespace' => 'glofox',
                'active' => true,
                'categories' => [],
                'type' => 'ADMIN',
                'name' => 'Timmy Fisher Roaming',
                'first_name' => 'Timmy',
                'last_name' => 'Fisher Roaming',
                'phone' => '0899991827R',
                'description' => 'Worst Trainer Ever!',
                'email' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'bookable' => true,
                'login' => '<EMAIL>',
                'experience' => 'A lot...',
                'gender' => 'M',
                'birth' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'twitter' => '@timmy',
                'facebook' => 'https://facebook.com/timmy',
                'stripe_token' => null,
                'answers' => [],
                'card_id' => null,
                'stripe_customer_id' => null,
                'stripe_cc_token' => null,
                'auto_generated' => false,
                'access_barcode' => '123123321',
                'created' => new MongoDate(),
                'modified' => new MongoDate(),
                'last_login' => new MongoDate(),
                'strike' => 0,
                'private_desc' => 'Why so secret?',
                'membership' => null,
                'device' => [
                    'os' => 'android',
                ],
                '_pattern' => null,
                'version' => [
                    'minor' => 9,
                    'major' => 9,
                    'revision' => 25,
                ],
                'WAIVER' => true,
                'MEMBERPURCHASE' => true,
                'PAYGPAYMENT' => true,
                'transaction' => null,
                'emergency_contact' => '<EMAIL>',
                'pwd_token' => null,
            ],
            [
                '_id' => new MongoId('59a7011a05c677bda916612a'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'categories' => [],
                'type' => 'ADMIN',
                'name' => 'Admin Istrator',
                'first_name' => 'Admin',
                'last_name' => 'Istrator',
                'phone' => '0899991827',
                'description' => 'Worst Trainer Ever!',
                'email' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'bookable' => true,
                'login' => '<EMAIL>',
                'experience' => 'A lot...',
                'gender' => 'M',
                'birth' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'twitter' => '@timmy',
                'facebook' => 'https://facebook.com/timmy',
                'phone' => '123123321',
                'bookable' => false,
                'stripe_token' => null,
                'answers' => [],
                'card_id' => null,
                'stripe_customer_id' => null,
                'stripe_cc_token' => null,
                'auto_generated' => false,
                'access_barcode' => '24234234234234',
                'created' => new MongoDate(),
                'modified' => new MongoDate(),
                'last_login' => new MongoDate(),
                'strike' => 0,
                'private_desc' => 'Why so secret?',
                'membership' => [
                    'type' => 'payg',
                ],
                'device' => [
                    'os' => 'android',
                ],
                '_pattern' => null,
                'version' => [
                    'minor' => 9,
                    'major' => 9,
                    'revision' => 25,
                ],
                'WAIVER' => true,
                'MEMBERPURCHASE' => true,
                'PAYGPAYMENT' => true,
                'transaction' => null,
                'emergency_contact' => '<EMAIL>',
                'pwd_token' => null,
            ],
            [
                '_id' => new MongoId('59a7011a05c677bda916613a'),
                'branch_id' => '5d56d214be51ef060f38408f',
                'namespace' => 'namespace-5d56d214be51ef060f38408f',
                'active' => true,
                'categories' => [],
                'type' => 'ADMIN',
                'name' => 'Admin Istrator',
                'first_name' => 'Admin',
                'last_name' => 'Istrator',
                'description' => 'Worst Trainer Ever!',
                'email' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'login' => '<EMAIL>',
                'experience' => 'A lot...',
                'gender' => 'M',
                'birth' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'twitter' => '@timmy',
                'facebook' => 'https://facebook.com/timmy',
                'phone' => '123123321',
                'bookable' => false,
                'stripe_token' => null,
                'answers' => [],
                'card_id' => null,
                'stripe_customer_id' => null,
                'stripe_cc_token' => null,
                'auto_generated' => false,
                'access_barcode' => '123123321',
                'created' => new MongoDate(),
                'modified' => new MongoDate(),
                'last_login' => new MongoDate(),
                'strike' => 0,
                'private_desc' => 'Why so secret?',
                'membership' => [
                    'type' => 'payg',
                ],
                'device' => [
                    'os' => 'android',
                ],
                '_pattern' => null,
                'version' => [
                    'minor' => 9,
                    'major' => 9,
                    'revision' => 25,
                ],
                'WAIVER' => true,
                'MEMBERPURCHASE' => true,
                'PAYGPAYMENT' => true,
                'transaction' => null,
                'emergency_contact' => '<EMAIL>',
                'pwd_token' => null,
            ],
            [
                '_id' => new MongoId('59a7011a05c677bda916619a'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'categories' => [],
                'type' => 'SUPERADMIN',
                'name' => 'Super Admin Istrator',
                'first_name' => 'Super Admin',
                'last_name' => 'Istrator',
                'phone' => '0899991827',
                'description' => 'Worst Trainer Ever!',
                'email' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'bookable' => true,
                'login' => '<EMAIL>',
                'experience' => 'A lot...',
                'gender' => 'M',
                'birth' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'twitter' => '@timmy',
                'facebook' => 'https://facebook.com/timmy',
                'phone' => '123123321',
                'bookable' => false,
                'stripe_token' => null,
                'answers' => [],
                'card_id' => null,
                'stripe_customer_id' => null,
                'stripe_cc_token' => null,
                'auto_generated' => false,
                'access_barcode' => '123123321',
                'created' => new MongoDate(),
                'modified' => new MongoDate(),
                'last_login' => new MongoDate(),
                'strike' => 0,
                'private_desc' => 'Why so secret?',
                'membership' => [
                    'type' => 'payg',
                ],
                'device' => [
                    'os' => 'android',
                ],
                '_pattern' => null,
                'version' => [
                    'minor' => 9,
                    'major' => 9,
                    'revision' => 25,
                ],
                'WAIVER' => true,
                'MEMBERPURCHASE' => true,
                'PAYGPAYMENT' => true,
                'transaction' => null,
                'emergency_contact' => '<EMAIL>',
                'pwd_token' => null,
            ],

            [
                '_id' => new MongoId('59a7011a05c677bda916619b'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'categories' => [],
                'type' => 'RECEPTION',
                'name' => 'RECEPTION',
                'first_name' => 'Reception',
                'last_name' => 'isf',
                'phone' => '0899991827',
                'email' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'bookable' => true,
                'login' => '<EMAIL>',
                'created' => new MongoDate(),
                'modified' => new MongoDate(),
                'description' => 'foo',
            ],

            [
                '_id' => new MongoId('59a3011a05c677bda916612a'),
                'branch_id' => '49a7011a05c677b9a916612b',
                'namespace' => 'test',
                'active' => true,
                'categories' => [],
                'type' => 'ADMIN',
                'name' => 'Test Admin',
                'first_name' => 'Test N',
                'last_name' => 'Admin',
                'phone' => '0899991827',
                'description' => 'Worst Trainer Ever!',
                'email' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'bookable' => true,
                'login' => '<EMAIL>',
                'experience' => 'A lot...',
                'gender' => 'M',
                'birth' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'twitter' => '@timmy',
                'facebook' => 'https://facebook.com/timmy',
                'phone' => '123123321',
                'bookable' => false,
                'stripe_token' => null,
                'answers' => [],
                'card_id' => null,
                'stripe_customer_id' => null,
                'stripe_cc_token' => null,
                'auto_generated' => false,
                'access_barcode' => '092340923492',
                'created' => new MongoDate(),
                'modified' => new MongoDate(),
                'strike' => 0,
                'private_desc' => 'Why so secret?',
                'membership' => null,
                'device' => [
                    'os' => 'android',
                ],
                '_pattern' => null,
                'version' => [
                    'minor' => 9,
                    'major' => 9,
                    'revision' => 25,
                ],
                'WAIVER' => true,
                'MEMBERPURCHASE' => true,
                'PAYGPAYMENT' => true,
                'transaction' => null,
                'emergency_contact' => '<EMAIL>',
                'pwd_token' => null,
                'last_login' => new MongoDate(),
            ],

            [
                '_id' => new MongoId('59a3011a05c677bda916612d'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'origin_branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'categories' => [],
                'type' => 'MEMBER',
                'first_name' => 'Test',
                'last_name' => 'Member',
                'phone' => '0899991827',
                'description' => 'Worst Trainer Ever!',
                'email' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'bookable' => true,
                'login' => '<EMAIL>',
                'experience' => 'A lot...',
                'gender' => 'M',
                'birth' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'twitter' => '@timmy',
                'facebook' => 'https://facebook.com/timmy',
                'phone' => '113322',
                'bookable' => false,
                'stripe_token' => null,
                'answers' => [],
                'card_id' => null,
                'stripe_customer_id' => null,
                'stripe_cc_token' => null,
                'auto_generated' => false,
                'access_barcode' => '345353535345',
                'created' => new MongoDate(),
                'modified' => new MongoDate(),
                'strike' => 0,
                'private_desc' => 'Why so secret?',
                'membership' => [
                    'type' => 'payg',
                ],
                'device' => [
                    'os' => 'android',
                ],
                '_pattern' => null,
                'version' => [
                    'minor' => 9,
                    'major' => 9,
                    'revision' => 25,
                ],
                'WAIVER' => true,
                'MEMBERPURCHASE' => true,
                'PAYGPAYMENT' => true,
                'transaction' => null,
                'emergency_contact' => '<EMAIL>',
                'pwd_token' => null,
                'last_login' => new MongoDate(),
            ],

            [
                '_id' => new MongoId('59a3011a05c677bda916613d'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'origin_branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'categories' => [],
                'type' => 'MEMBER',
                'first_name' => 'Test',
                'last_name' => 'Purcahse in MembershipsController',
                'phone' => '0899991827',
                'email' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'login' => '<EMAIL>',
                'phone' => '113322',
                'bookable' => false,
                'stripe_token' => null,
                'answers' => [],
                'card_id' => null,
                'stripe_customer_id' => null,
                'stripe_cc_token' => null,
                'auto_generated' => false,
                'access_barcode' => '85837583758375',
                'created' => new MongoDate(),
                'modified' => new MongoDate(),
                'strike' => 0,
                'membership' => [
                    '_id' => '56ceca057cad3653598b4582',
                    'type' => 'time',
                    'start_date' => date('Y-m-d', strtotime('-7 day')),
                    'plan_code' => '56ceca59dccde',
                    'boooked_events' => 0,
                    'expiry_date' => date('Y-m-d', strtotime('-1 day')),
                ],
                'device' => [
                    'os' => 'android',
                ],
                'WAIVER' => true,
                'MEMBERPURCHASE' => true,
                'PAYGPAYMENT' => true,
                'transaction' => null,
                'emergency_contact' => '<EMAIL>',
                'pwd_token' => null,
                'last_login' => new MongoDate(),
            ],

            [
                '_id' => new MongoId('59a3011a05c677bda916611c'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'origin_branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'categories' => [],
                'type' => 'MEMBER',
                'first_name' => 'Test',
                'last_name' => 'Member',
                'phone' => '0899991827',
                'description' => 'Worst Trainer Ever!',
                'email' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'bookable' => true,
                'login' => '<EMAIL>',
                'experience' => 'A lot...',
                'gender' => 'M',
                'birth' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'twitter' => '@timmy',
                'facebook' => 'https://facebook.com/timmy',
                'phone' => '113322',
                'bookable' => false,
                'stripe_token' => null,
                'answers' => [],
                'card_id' => null,
                'stripe_customer_id' => null,
                'stripe_cc_token' => null,
                'auto_generated' => false,
                'access_barcode' => '938383883838',
                'created' => new MongoDate(),
                'modified' => new MongoDate(),
                'strike' => 0,
                'private_desc' => 'Why so secret?',
                'membership' => [
                    'type' => 'payg',
                ],
                'device' => [
                    'os' => 'android',
                    'id' => 'cd6WazzdtF8:APA91bF4rcSujv9OH7-39EzUG9kc5Psoy69J0CH2Iu_i5ofZtpmNbBvNXZ2lQelJHsFOHfGaFeo3HQ-yrw2s6FVvlYDG1DAWtRSWdtJiB2DETsWEJkZIXhtSneM_eDShlFh_9npwBqXi',
                ],
                '_pattern' => null,
                'version' => [
                    'minor' => 9,
                    'major' => 9,
                    'revision' => 25,
                ],
                'WAIVER' => true,
                'MEMBERPURCHASE' => true,
                'PAYGPAYMENT' => true,
                'transaction' => null,
                'emergency_contact' => '<EMAIL>',
                'pwd_token' => null,
                'last_login' => new MongoDate(),
            ],

            [
                '_id' => new MongoId('59a3011a05c677bda916632c'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'origin_branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'categories' => [],
                'type' => 'MEMBER',
                'first_name' => 'Test',
                'last_name' => 'Member',
                'phone' => '08999918233337',
                'description' => 'Worst Trainer Ever!',
                'email' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'bookable' => true,
                'login' => '<EMAIL>',
                'experience' => 'A lot...',
                'gender' => 'M',
                'birth' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'twitter' => '@timmy',
                'facebook' => 'https://facebook.com/timmy',
                'phone' => '113322',
                'bookable' => false,
                'stripe_token' => null,
                'answers' => [],
                'card_id' => null,
                'stripe_customer_id' => null,
                'stripe_cc_token' => null,
                'auto_generated' => false,
                'access_barcode' => '72467642747',
                'created' => new MongoDate(),
                'modified' => new MongoDate(),
                'strike' => 0,
                'private_desc' => 'Why so secret?',
                'membership' => [
                    'type' => 'payg',
                ],
                'device' => [
                    'os' => 'android',
                ],
                '_pattern' => null,
                'version' => [
                    'minor' => 9,
                    'major' => 9,
                    'revision' => 25,
                ],
                'WAIVER' => true,
                'MEMBERPURCHASE' => true,
                'PAYGPAYMENT' => true,
                'transaction' => null,
                'emergency_contact' => '<EMAIL>',
                'pwd_token' => null,
                'last_login' => new MongoDate(),
            ],

            [
                '_id' => new MongoId('59a3011a05c677bda916711c'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'categories' => [],
                'type' => 'MEMBER',
                'first_name' => 'Test',
                'last_name' => 'Member',
                'phone' => '0899991827',
                'email' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'bookable' => true,
                'login' => '<EMAIL>',
                'phone' => '113322',
                'created' => new MongoDate(),
                'modified' => new MongoDate(),
                'strike' => 0,
                'private_desc' => 'Why so secret?',
                'membership' => [
                    'type' => 'payg',
                ],
                'WAIVER' => true,
                'MEMBERPURCHASE' => true,
                'PAYGPAYMENT' => true,
                'transaction' => null,
                'emergency_contact' => '<EMAIL>',
                'last_login' => new MongoDate(),
            ],
            [
                '_id' => new MongoId('69a3011a05c677bda916711c'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'categories' => [],
                'type' => 'MEMBER',
                'first_name' => 'Test appointment credit',
                'last_name' => 'Member',
                'phone' => '0899991827',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'phone' => '113322',
                'created' => new MongoDate(),
                'modified' => new MongoDate(),
                'strike' => 0,
                'membership' => [
                    'type' => 'payg',
                ],
                'WAIVER' => true,
                'MEMBERPURCHASE' => true,
                'PAYGPAYMENT' => true,
                'transaction' => null,
                'emergency_contact' => '<EMAIL>',
                'last_login' => new MongoDate(),
            ],
            [
                '_id' => new MongoId('69a3011a05c677bda916711d'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'categories' => [],
                'type' => 'MEMBER',
                'first_name' => 'Test appointment credit',
                'last_name' => 'Member',
                'phone' => '0899991827',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'phone' => '113322',
                'created' => new MongoDate(),
                'modified' => new MongoDate(),
                'strike' => 0,
                'membership' => [
                    'type' => 'payg',
                ],
                'WAIVER' => true,
                'MEMBERPURCHASE' => true,
                'PAYGPAYMENT' => true,
                'transaction' => null,
                'emergency_contact' => '<EMAIL>',
                'last_login' => new MongoDate(),
            ],
            [
                '_id' => new MongoId('69a3011a05c677bda916712d'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'categories' => [],
                'type' => 'MEMBER',
                'first_name' => 'Test appointment credit',
                'last_name' => 'Member',
                'phone' => '0899991827',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'phone' => '113322',
                'created' => new MongoDate(),
                'modified' => new MongoDate(),
                'strike' => 0,
                'membership' => [
                    'type' => 'payg',
                ],
                'WAIVER' => true,
                'MEMBERPURCHASE' => true,
                'PAYGPAYMENT' => true,
                'transaction' => null,
                'emergency_contact' => '<EMAIL>',
                'last_login' => new MongoDate(),
            ],
            [
                '_id' => new MongoId('a9a3321a05c677bda916611c'),
                'user_id' => 'a9a3321a05c677bda916611c',
                'branch_id' => '49a7011a05c677b9a916612a',
                'origin_branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'categories' => [],
                'type' => 'MEMBER',
                'first_name' => 'Test 2',
                'last_name' => 'Member 2',
                'phone' => '0899991827',
                'description' => 'Worst Trainer Ever!',
                'email' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'bookable' => true,
                'login' => '<EMAIL>',
                'experience' => 'A lot...',
                'gender' => 'M',
                'birth' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'twitter' => '@timmy',
                'facebook' => 'https://facebook.com/timmy',
                'phone' => '112233',
                'bookable' => false,
                'stripe_token' => null,
                'answers' => [],
                'card_id' => null,
                'stripe_customer_id' => null,
                'stripe_cc_token' => null,
                'auto_generated' => false,
                'access_barcode' => '88888888888',
                'created' => new MongoDate(),
                'modified' => new MongoDate(),
                'strike' => 0,
                'private_desc' => 'Why so secret?',
                'membership' => [
                    '_id' => '56ceca057cad3653598b4582',
                    'type' => 'time',
                    'start_date' => date('Y-m-d', strtotime('-7 day')),
                    'plan_code' => '56ceca59dccde',
                    'boooked_events' => 0,
                    'expiry_date' => date('Y-m-d', strtotime('-1 day')),
                    'user_membership_id' => '56ceca057cad3653598b4583'
                ],
                'device' => [
                    'os' => 'android',
                ],
                '_pattern' => null,
                'version' => [
                    'minor' => 9,
                    'major' => 9,
                    'revision' => 25,
                ],
                'WAIVER' => true,
                'MEMBERPURCHASE' => true,
                'PAYGPAYMENT' => true,
                'transaction' => null,
                'emergency_contact' => '<EMAIL>',
                'pwd_token' => null,
                'last_login' => new MongoDate(),
            ],

            [
                '_id' => new MongoId('a9a2222a05c677bda916613d'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'origin_branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'categories' => [],
                'type' => 'MEMBER',
                'first_name' => 'Test 22',
                'last_name' => 'Member 22',
                'description' => 'Worst Trainer Ever!',
                'email' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'login' => '<EMAIL>',
                'experience' => 'A lot...',
                'gender' => 'M',
                'birth' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'twitter' => '@timmy',
                'facebook' => 'https://facebook.com/timmy',
                'phone' => '112233',
                'stripe_token' => null,
                'answers' => [],
                'card_id' => null,
                'stripe_customer_id' => null,
                'stripe_cc_token' => null,
                'auto_generated' => false,
                'access_barcode' => '9324092842234',
                'created' => new MongoDate(),
                'modified' => new MongoDate(),
                'strike' => 0,
                'private_desc' => 'Why so secret?',
                'membership' => [
                    '_id' => '56ceca057cad3653598b4582',
                    'type' => 'time',
                    'start_date' => date('Y-m-d 00:00:00', strtotime('-7 day')),
                    'plan_code' => '56ceca59dccde',
                    'boooked_events' => 0,
                    'branch_id' => '49a7011a05c677b9a916612a',
                    'subscription' => [
                        'payment_method_type_id' => Glofox\Domain\PaymentMethods\Type::CARD,
                        'stripe_id' => '60000',
                        'payment_gateway_id' => '60000',
                        'end_date' => date('Y-m-d 00:00:00', strtotime('0 day')),
                        'subscription_plan_id' => '60000',
                    ],
                ],
                'device' => [
                    'os' => 'android',
                ],
                '_pattern' => null,
                'version' => [
                    'minor' => 9,
                    'major' => 9,
                    'revision' => 25,
                ],
                'WAIVER' => true,
                'MEMBERPURCHASE' => true,
                'PAYGPAYMENT' => true,
                'transaction' => null,
                'emergency_contact' => '<EMAIL>',
                'pwd_token' => null,
                'last_login' => new MongoDate(),
            ],

            // Expired Membership
            [
                '_id' => new MongoId('a9a2222a05c677bda916611c'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'origin_branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'categories' => [],
                'type' => 'MEMBER',
                'first_name' => 'Test 22',
                'last_name' => 'Member 22',
                'phone' => '0899991827',
                'description' => 'Worst Trainer Ever!',
                'email' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'bookable' => true,
                'login' => '<EMAIL>',
                'experience' => 'A lot...',
                'gender' => 'M',
                'birth' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'twitter' => '@timmy',
                'facebook' => 'https://facebook.com/timmy',
                'phone' => '112233',
                'bookable' => false,
                'stripe_token' => null,
                'answers' => [],
                'card_id' => null,
                'stripe_customer_id' => null,
                'stripe_cc_token' => null,
                'auto_generated' => false,
                'access_barcode' => '111111111',
                'created' => new MongoDate(),
                'modified' => new MongoDate(),
                'strike' => 0,
                'private_desc' => 'Why so secret?',
                'membership' => [
                    '_id' => '56ceca057cad3653598b4582',
                    'type' => 'time',
                    'start_date' => date('Y-m-d', strtotime('-7 day')),
                    'plan_code' => '56ceca59dccde',
                    'boooked_events' => 0,
                    'subscription' => [
                        'stripe_id' => 'sub_00X',
                        'end_date' => date('Y-m-d', strtotime('-1 day')),
                        'interval' => 'week',
                        'interval_count' => 1,
                    ],
                ],
                'device' => [
                    'os' => 'android',
                ],
                '_pattern' => null,
                'version' => [
                    'minor' => 9,
                    'major' => 9,
                    'revision' => 25,
                ],
                'WAIVER' => true,
                'MEMBERPURCHASE' => true,
                'PAYGPAYMENT' => true,
                'transaction' => null,
                'emergency_contact' => '<EMAIL>',
                'pwd_token' => null,
                'last_login' => new MongoDate(),
            ],
            [
                '_id' => new MongoId('59a3011a05c677bda916619b'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'categories' => [],
                'type' => 'MEMBER',
                'first_name' => 'Test',
                'last_name' => "O'connell",
                'lead_status' => \Glofox\Domain\Leads\Status::MEMBER,
                'phone' => '0899991823',
                'description' => 'Worst Trainer Ever!',
                'email' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'bookable' => true,
                'login' => '<EMAIL>',
                'experience' => 'A lot...',
                'gender' => 'M',
                'birth' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'twitter' => '@timmy',
                'facebook' => 'https://facebook.com/timmy',
                'bookable' => false,
                'stripe_token' => null,
                'answers' => [],
                'card_id' => null,
                'stripe_customer_id' => null,
                'stripe_cc_token' => null,
                'auto_generated' => false,
                'access_barcode' => '1231233218',
                'created' => new MongoDate(),
                'modified' => new MongoDate(),
                'strike' => 0,
                'private_desc' => 'Why so secret?',
                'membership' => [
                    '_id' => '56ceca057cad3653598b4582',
                    'type' => 'time',
                    'start_date' => date('Y-m-d', strtotime('-7 day')),
                    'plan_code' => '56ceca59dccde',
                    'boooked_events' => 0,
                    'expiry_date' => date('Y-m-d', strtotime('+1 day')),
                ],
                'device' => [
                    'os' => 'android',
                ],
                '_pattern' => null,
                'version' => [
                    'minor' => 9,
                    'major' => 9,
                    'revision' => 25,
                ],
                'WAIVER' => true,
                'MEMBERPURCHASE' => true,
                'PAYGPAYMENT' => true,
                'transaction' => null,
                'emergency_contact' => '<EMAIL>',
                'pwd_token' => null,
                'last_login' => new MongoDate(),
            ],
            [
                '_id' => new MongoId('69a3011a05c677bda916619b'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'categories' => [],
                'type' => 'MEMBER',
                'first_name' => 'Test',
                'last_name' => "O'connell",
                'lead_status' => \Glofox\Domain\Leads\Status::MEMBER,
                'phone' => '0899991823',
                'email' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'bookable' => true,
                'login' => '<EMAIL>',
                'gender' => 'M',
                'birth' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'twitter' => '@timmy',
                'facebook' => 'https://facebook.com/timmy',
                'bookable' => false,
                'stripe_token' => null,
                'answers' => [],
                'card_id' => null,
                'stripe_customer_id' => null,
                'stripe_cc_token' => null,
                'auto_generated' => false,
                'access_barcode' => '1231233218',
                'created' => new MongoDate(),
                'modified' => new MongoDate(),
                'strike' => 0,
                'private_desc' => 'Why so secret?',
                'membership' => [
                    'type' => 'payg',
                    'start_date' => date('Y-m-d', strtotime('-7 day')),
                ],
                'WAIVER' => true,
                'MEMBERPURCHASE' => true,
                'PAYGPAYMENT' => true,
                'transaction' => null,
                'emergency_contact' => '<EMAIL>',
                'pwd_token' => null,
                'last_login' => new MongoDate(),
            ],
            [
                '_id' => new MongoId('59a3010a05c677bda916619b'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'origin_branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'categories' => [],
                'type' => 'MEMBER',
                'first_name' => 'Tester',
                'last_name' => "O'connell",
                'lead_status' => \Glofox\Domain\Leads\Status::MEMBER,
                'phone' => '0899991823',
                'email' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'bookable' => true,
                'login' => '<EMAIL>',
                'experience' => 'A lot...',
                'gender' => 'M',
                'birth' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'bookable' => false,
                'stripe_token' => null,
                'answers' => [],
                'card_id' => null,
                'stripe_customer_id' => null,
                'stripe_cc_token' => null,
                'auto_generated' => false,
                'access_barcode' => '1231233218',
                'created' => new MongoDate(),
                'modified' => new MongoDate(),
                'strike' => 0,
                'private_desc' => 'Why so secret?',
                'membership' => [
                    '_id' => '56ceca057cad3653598b4582',
                    'type' => 'time',
                    'start_date' => date('Y-m-d', strtotime('-7 day')),
                    'plan_code' => '56ceca59dccde',
                    'boooked_events' => 0,
                    'expiry_date' => date('Y-m-d', strtotime('+1 day')),
                ],
                'device' => [
                    'os' => 'android',
                ],
                '_pattern' => null,
                'version' => [
                    'minor' => 9,
                    'major' => 9,
                    'revision' => 25,
                ],
                'WAIVER' => true,
                'MEMBERPURCHASE' => true,
                'PAYGPAYMENT' => true,
                'transaction' => null,
                'emergency_contact' => '<EMAIL>',
                'pwd_token' => null,
                'last_login' => new MongoDate(),
            ],
            [
                '_id' => new MongoId('58568a8fa875ab19530041a7'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'categories' => [],
                'type' => 'TRAINER',
                'first_name' => 'Test',
                'last_name' => 'Test',
                'phone' => '0899991823',
                'description' => 'Worst Trainer Ever!',
                'email' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'bookable' => true,
                'login' => '<EMAIL>',
                'experience' => 'A lot...',
                'gender' => 'M',
                'birth' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'twitter' => '@timmy',
                'facebook' => 'https://facebook.com/timmy',
                'bookable' => false,
                'stripe_token' => null,
                'answers' => [],
                'card_id' => null,
                'stripe_customer_id' => null,
                'stripe_cc_token' => null,
                'auto_generated' => false,
                'access_barcode' => '1231233218',
                'created' => new MongoDate(),
                'modified' => new MongoDate(),
                'strike' => 0,
                'private_desc' => 'Why so secret?',
                'membership' => [
                    '_id' => '56ceca057cad3653598b4582',
                    'type' => 'time',
                    'start_date' => date('Y-m-d', strtotime('-7 day')),
                    'plan_code' => '56ceca59dccde',
                    'boooked_events' => 0,
                    'expiry_date' => date('Y-m-d', strtotime('now')),
                ],
                'device' => [
                    'os' => 'android',
                ],
                '_pattern' => null,
                'version' => [
                    'minor' => 9,
                    'major' => 9,
                    'revision' => 25,
                ],
                'WAIVER' => true,
                'MEMBERPURCHASE' => true,
                'PAYGPAYMENT' => true,
                'transaction' => null,
                'emergency_contact' => '<EMAIL>',
                'pwd_token' => 'w$afeon2i$v=19$m=1024wdzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkz',
                'pwd_token_expiry' => new MongoDate(strtotime('2028-10-10 00:00:00')),
                'last_login' => new MongoDate(),
            ],
            [
                '_id' => new MongoId('58568a8fa875ab19630041a7'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'categories' => [],
                'type' => 'TRAINER',
                'first_name' => 'Test',
                'last_name' => 'Test',
                'phone' => '0899991823',
                'description' => 'Worst Trainer Ever!',
                'email' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'bookable' => true,
                'login' => '<EMAIL>',
                'experience' => 'A lot...',
                'gender' => 'M',
                'birth' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'twitter' => '@timmy',
                'facebook' => 'https://facebook.com/timmy',
                'stripe_token' => null,
                'answers' => [],
                'card_id' => null,
                'stripe_customer_id' => null,
                'stripe_cc_token' => null,
                'auto_generated' => false,
                'access_barcode' => '1231233218',
                'created' => new MongoDate(),
                'modified' => new MongoDate(),
                'strike' => 0,
                'private_desc' => 'Why so secret?',
                'membership' => [
                    '_id' => '56ceca057cad3653598b4582',
                    'type' => 'time',
                    'start_date' => date('Y-m-d', strtotime('-7 day')),
                    'plan_code' => '56ceca59dccde',
                    'boooked_events' => 0,
                    'expiry_date' => date('Y-m-d', strtotime('now')),
                ],
                'device' => [
                    'os' => 'android',
                ],
                '_pattern' => null,
                'version' => [
                    'minor' => 9,
                    'major' => 9,
                    'revision' => 25,
                ],
                'WAIVER' => true,
                'MEMBERPURCHASE' => true,
                'PAYGPAYMENT' => true,
                'transaction' => null,
                'emergency_contact' => '<EMAIL>',
                'pwd_token' => null,
                'last_login' => new MongoDate(),
            ],
            [
                '_id' => new MongoId('58568a8fa875ab19530041b8'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => false,
                'categories' => [],
                'type' => 'TRAINER',
                'first_name' => 'Test',
                'last_name' => 'Test',
                'phone' => '0899991823',
                'description' => 'Worst Trainer Ever!',
                'email' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'bookable' => true,
                'login' => '<EMAIL>',
                'experience' => 'A lot...',
                'gender' => 'M',
                'birth' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'twitter' => '@timmy',
                'facebook' => 'https://facebook.com/timmy',
                'bookable' => false,
                'stripe_token' => null,
                'answers' => [],
                'card_id' => null,
                'stripe_customer_id' => null,
                'stripe_cc_token' => null,
                'auto_generated' => false,
                'access_barcode' => '1231233218',
                'created' => new MongoDate(),
                'modified' => new MongoDate(),
                'strike' => 0,
                'private_desc' => 'Why so secret?',
                'membership' => [
                    '_id' => '56ceca057cad3653598b4582',
                    'type' => 'time',
                    'start_date' => date('Y-m-d', strtotime('-7 day')),
                    'plan_code' => '56ceca59dccde',
                    'boooked_events' => 0,
                    'expiry_date' => date('Y-m-d', strtotime('now')),
                ],
                'device' => [
                    'os' => 'android',
                ],
                '_pattern' => null,
                'version' => [
                    'minor' => 9,
                    'major' => 9,
                    'revision' => 25,
                ],
                'WAIVER' => true,
                'MEMBERPURCHASE' => true,
                'PAYGPAYMENT' => true,
                'transaction' => null,
                'emergency_contact' => '<EMAIL>',
                'pwd_token' => null,
                'last_login' => new MongoDate(),
            ],
            [
                '_id' => new MongoId('58568b8fa875ab19530041b8'),
                'branch_id' => [
                    '5c783d4cd510f9635ad4a6b6',
                    '5a9591bcdb07bce527400717',
                ],
                'namespace' => 'glofox',
                'active' => true,
                'categories' => [],
                'type' => 'ADMIN',
                'first_name' => 'Roaming',
                'last_name' => 'Admin',
                'phone' => '0899991823',
                'description' => 'Roams between to glofox branhces',
                'email' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'bookable' => true,
                'login' => '<EMAIL>',
                'created' => new MongoDate(),
                'modified' => new MongoDate(),
                'last_login' => new MongoDate(),
            ],
            [
                '_id' => new MongoId('5a2aad49e1608aa72b0041a7'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'origin_branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'first_name' => 'Testing',
                'last_name' => 'FirstBookingMembership',
                'gender' => [
                    'name' => 'FEMALE',
                    'label' => 'F',
                ],
                'phone' => '101010',
                'email' => '<EMAIL>',
                'birth' => null,
                'emergency_contact' => null,
                'membership' => [
                    '_id' => '5ab113cf773dd001e3570322',
                    'type' => 'time_classes',
                    'membership_group_id' => null,
                    'plan_code' => 1_521_554_388_953,
                    'branch_id' => '49a7011a05c677b9a916612a',
                    'branch_name' => 'Glofox',
                    'starts_on' => 'FIRST_BOOKING_DATE',
                    'roaming_enabled' => false,
                    'duration_time_unit' => 'week',
                    'duration_time_unit_count' => 1,
                    'start_date' => date('Y-m-d', strtotime('7 day')),
                    'expiry_date' => date('Y-m-d', strtotime('14 day')),
                ],
                'strike' => null,
                'type' => 'MEMBER',
                'login' => '<EMAIL>',
                'modified' => new MongoDate(),
                'created' => new MongoDate(),
                'categories' => [],
                'name' => 'Testing FirstBookingMembership',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'PAYGPAYMENT' => true,
            ],
            [
                '_id' => new MongoId('5a2aad49e1608aa72b0041c1'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'origin_branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'first_name' => 'Testing',
                'last_name' => 'FirstBookingMembership',
                'gender' => [
                    'name' => 'FEMALE',
                    'label' => 'F',
                ],
                'phone' => '101010',
                'email' => '<EMAIL>',
                'birth' => null,
                'emergency_contact' => null,
                'lead_status' => 'MEMBER',
                'membership' => [
                    '_id' => '5ab113cf773dd001e3570322',
                    'type' => 'time_classes',
                    'membership_group_id' => null,
                    'plan_code' => 1_521_554_388_953,
                    'branch_id' => '49a7011a05c677b9a916612a',
                    'branch_name' => 'Glofox',
                    'starts_on' => 'PURCHASE_DATE',
                    'roaming_enabled' => false,
                    'duration_time_unit' => 'week',
                    'duration_time_unit_count' => 1,
                    'start_date' => date('Y-m-d', strtotime('7 day')),
                    'expiry_date' => date('Y-m-d', strtotime('14 day')),
                ],
                'strike' => null,
                'type' => 'MEMBER',
                'login' => '<EMAIL>',
                'modified' => new MongoDate(),
                'created' => new MongoDate(),
                'categories' => [],
                'name' => 'Testing FirstBookingMembership',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'PAYGPAYMENT' => true,
            ],
            [
                '_id' => new MongoId('5a2aad49e1608aa72b004443'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'first_name' => 'Testing',
                'last_name' => 'Subscription',
                'gender' => [
                    'name' => 'FEMALE',
                    'label' => 'F',
                ],
                'phone' => '101010',
                'email' => '<EMAIL>',
                'birth' => null,
                'emergency_contact' => null,
                'membership' => [
                    'type' => 'payg',
                ],
                'strike' => null,
                'type' => 'MEMBER',
                'login' => '<EMAIL>',
                'modified' => new MongoDate(),
                'created' => new MongoDate(),
                'categories' => [],
                'name' => 'Testing Subscription',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'PAYGPAYMENT' => true,
                'origin_branch_id' => '49a7011a05c677b9a916612a',
            ],
            [
                '_id' => new MongoId('5a2aad49e1608aa72b004444'),
                'user_id' => '5a2aad49e1608aa72b004444',
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'first_name' => 'Testing',
                'last_name' => 'address',
                'gender' => [
                    'name' => 'FEMALE',
                    'label' => 'F',
                ],
                'phone' => '101010',
                'email' => '<EMAIL>',
                'birth' => null,
                'emergency_contact' => null,
                'membership' => [
                    'type' => 'payg',
                ],
                'strike' => null,
                'type' => 'MEMBER',
                'login' => '<EMAIL>',
                'modified' => new MongoDate(),
                'created' => new MongoDate(),
                'categories' => [],
                'name' => 'Testing address',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'PAYGPAYMENT' => true,
                'address' => [
                    'street' => 'test street',
                    'city' => 'test city',
                    'state' => 'test state',
                    'country' => 'test country',
                    'postal_code' => '300000',
                    'country_code' => 'GB',
                ],

            ],
            [
                '_id' => new MongoId('5a2aad49e1608aa72b0041b4'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'first_name' => 'Testing',
                'last_name' => 'FirstBookingMembership2',
                'gender' => [
                    'name' => 'FEMALE',
                    'label' => 'F',
                ],
                'phone' => '101010',
                'email' => '<EMAIL>',
                'birth' => null,
                'emergency_contact' => null,
                'membership' => [
                    '_id' => '5ab113cf773dd001e3570322',
                    'type' => 'time_classes',
                    'membership_group_id' => null,
                    'plan_code' => 1_521_554_388_953,
                    'branch_id' => '49a7011a05c677b9a916612a',
                    'branch_name' => 'glofox',
                    'starts_on' => 'FIRST_BOOKING_DATE',
                    'roaming_enabled' => false,
                    'duration_time_unit' => 'week',
                    'duration_time_unit_count' => 1,
                    'start_date' => date('Y-m-d', strtotime('13 day')),
                    'expiry_date' => date('Y-m-d', strtotime('20 day')),
                ],
                'strike' => null,
                'type' => 'MEMBER',
                'login' => '<EMAIL>',
                'modified' => new MongoDate(),
                'created' => new MongoDate(),
                'categories' => [],
                'name' => 'Testing FirstBookingMembership2',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'PAYGPAYMENT' => true,
            ],
            [
                '_id' => new MongoId('5a2aad49e1608aa72b0041b9'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'first_name' => 'Testing',
                'last_name' => 'AddWebAdminRequest',
                'gender' => [
                    'name' => 'FEMALE',
                    'label' => 'F',
                ],
                'phone' => '101010',
                'email' => '<EMAIL>',
                'membership' => [
                    'type' => 'payg',
                ],
                'strike' => null,
                'type' => 'MEMBER',
                'login' => '<EMAIL>',
                'modified' => new MongoDate(),
                'created' => new MongoDate(),
                'name' => 'Testing AddWebAdminRequest',
                'PAYGPAYMENT' => true,
            ],
            $this->createPaygMember([
                '_id' => '5a2aad49e1608aa72b0041d9',
                'branch_id' => 'login_branch_id',
                'namespace' => 'random_namespace',
                'first_name' => 'random_first_name',
                'last_name' => 'random_last_name',
                'type' => 'random_type',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
            ]),
            $this->createAdmin([
                '_id' => new MongoId('5e9ed06cd575e6003c37648e'),
                'branch_id' => '5e9ed069e0a7da003254711d',
            ]),
            $this->createAdmin([
                '_id' => new MongoId('5e9ed06cd575e6003c38648e'),
                'branch_id' => '5e9ed069e0a7da003254712d',
            ]),
            $this->createPaygMember([
                '_id' => '59ba576ef09e52064e23414d',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
            ]),
            $this->createPaygMember([
                '_id' => '59a7010a05c677b3a916612d',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
            ]),
            $this->createPaygMember([
                '_id' => '59a7010a05c677b3a9166111',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
            ]),
            $this->createPaygMember([
                '_id' => '59a7010a05c677b3a916615d',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
            ]),
            $this->createPaygMember([
                '_id' => '59a7010a05c676b3a916625d',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
            ]),
            $this->createMemberWithSubscriptionCredits(
                '59c92229c3fad88b481add96',
                '<EMAIL>',
                'sub_BSpsTBDgn8Rqs7',
                new MongoDate(),
                '6132024dd92141aa0d7b46ec'
            ),
            $this->createMemberWithSubscriptionCredits(
                '59c92229c3fad88b481add12',
                '<EMAIL>',
                'sub_BSpsTBDgn8Rqs7',
                new MongoDate(),
                '6132024dd92141aa0d7b46ec'
            ),
            $this->createMemberWithSubscriptionCredits(
                '5fc6f4e8c94345291a02edee',
                '<EMAIL>',
                'sub_BSpsTBDgn8Rqs7',
                null,
                '5fc6f52c2b84dbcdda4d20f6'
            ),
            $this->createMemberWithSubscriptionCredits('59cd0e4b2a04b921fb568dc6', '<EMAIL>'),
            $this->createMemberWithValidSubscriptionTimeMembership(
                '59e63201dccec7c20fba19f0',
                '<EMAIL>',
            ),
            $this->createMemberWithValidSubscriptionTimeMembership(
                '5a4a8e8a047673a418000009',
                '<EMAIL>',
                [
                    'branch_id' => '5addc25383266f65abf515c4',
                    'membership' => [
                        'subscription' => [
                            'stripe_id' => 'sub_testSubscriptionPause',
                            'payment_method_type_id' => 'CARD',
                        ],
                    ],
                ]
            ),
            $this->createBase([
                '_id' => '5a1445fbc836ed252aba7c23',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'membership' => [
                    '_id' => '5a146888d721b079504aa0ee',
                    'type' => 'time',
                    'starts_on' => MembershipPlanStartsOn::FIRST_BOOKING_DATE,
                    'plan_code' => '5a1430309b264e6a99186003',
                    'duration_time_unit' => 'month',
                    'duration_time_unit_count' => 1,
                ],
            ]),
            $this->createBaseWithoutPassword([
                '_id' => '5a1445fbc836ed252aba7c35',
                'phone' => '08501010101',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'answers' => [
                    [
                        'type' => 'string',
                        'value' => 'Weight',
                        'key' => 0,
                        'answer' => '11.1.25, 10.6',
                    ],
                ],
            ]),
            $this->createBaseWithoutPassword([
                '_id' => '5a1445fbc836ed252aba7c36',
                'phone' => '08501010101',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'emergency_contact' => 'My existing emergency contact',
            ]),
            $this->createPaygMember([
                '_id' => '5a96ed2e1ff6b6e397c0d31c',
                'branch_id' => '5a9591bcdb07bce527400717',
                'origin_branch_id' => '5a9591bcdb07bce527400717',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'stripe_customer_id' => 'fake_customer_id',
            ]),
            $this->createPaygMember([
                '_id' => '5a96ed2e1ff6b6e397c0d32c',
                'branch_id' => '5a9591bcdb07bce527400717',
                'origin_branch_id' => '5a9591bcdb07bce527400717',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'stripe_customer_id' => 'fake_customer_id',
            ]),
            // Member with classpass id
            $this->createPaygMember([
                '_id' => '5adde464b5cf4ad32eb7597e',
                'branch_id' => '5addc25383266f65abf515c4',
                'namespace' => 'classpass_integrated',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'birth' => '1990/12/12',
                'metadata' => [
                    'classpass' => [
                        '_id' => 'randomId123',
                    ],
                ],
            ]),
            // Member with gympass id
            $this->createPaygMember([
                '_id' => '5adde464b5cf4ad32eb7597f',
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'gympass_integrated',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'metadata' => [
                    'gympass' => [
                        'id' => '123',
                    ],
                ],
            ]),
            // Member without classpass id
            $this->createPaygMember([
                '_id' => '5adde862426413b9e9b4be9e',
                'branch_id' => '5addc25383266f65abf515c4',
                'namespace' => 'classpass_integrated',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
            ]),
            // Trainer for classpass integrated branch
            [
                '_id' => new MongoId('5adf4d52f35bbcfb4251f960'),
                'branch_id' => '5addc25383266f65abf515c4',
                'namespace' => 'classpass_integrated',
                'active' => true,
                'categories' => [],
                'type' => 'TRAINER',
                'first_name' => 'Classpass Integrated',
                'last_name' => 'Trainer',
                'phone' => '08933221823',
                'description' => 'Classpass Integrated',
                'email' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'bookable' => false,
                'login' => '<EMAIL>',
                'gender' => 'M',
                'birth' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'created' => new MongoDate(),
                'modified' => new MongoDate(),
                'last_login' => new MongoDate(),
            ],
            // Trainer for with custom pricing
            [
                '_id' => new MongoId('5bab6151a376c1eabda143e8'),
                'branch_id' => '5addc25383266f65abf515c4',
                'namespace' => 'classpass_integrated',
                'active' => true,
                'categories' => [],
                'type' => 'TRAINER',
                'first_name' => 'BookableTrainer',
                'last_name' => 'With Custom Pricing',
                'phone' => '08933221823',
                'description' => 'Bookable trainer with custom pricing',
                'email' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'bookable' => true,
                'login' => '<EMAIL>',
                'gender' => 'M',
                'birth' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'created' => new MongoDate(),
                'modified' => new MongoDate(),
                'last_login' => new MongoDate(),
            ],
            // Member with metadata
            $this->createPaygMember([
                '_id' => '5ae1a75a8d7de985e383494d',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'password' => '202cb962ac59075b964b07152d234b70',
                'metadata' => [
                    'classpass' => [
                        '_id' => 'randomClassPassId',
                    ],
                    'fakeIntegration' => [
                        'fakeKey' => '444',
                    ],
                ],
            ]),
            $this->createPaygMember([
                '_id' => '5b17f141a330c3d7b8633fc1',
                'branch_id' => '5addc25383266f65abf515c4',
                'namespace' => 'classpass_integrated',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
            ]),

            $this->createPaygMember([
                '_id' => '5baad3028229f23197479ad4',
                'branch_id' => '5addc25383266f65abf515c4',
                'namespace' => 'classpass_integrated',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
            ]),

            $this->createBase([
                '_id' => '5b18027406b15ba28c9b046e',
                'branch_id' => '5addc25383266f65abf515c4',
                'namespace' => 'classpass_integrated',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'membership' => [
                    '_id' => '5b1802b34606334e235394df',
                    'type' => 'time_classes',
                    'plan_code' => 1_521_554_388_959,
                    'start_date' => date('Y-m-d', strtotime('7 day')),
                    'expiry_date' => date('Y-m-d', strtotime('14 day')),
                ],
            ]),

            $this->createBase([
                '_id' => '5b18027406b15ba28c9b021a',
                'branch_id' => '5addc25383266f65abf515c4',
                'namespace' => 'classpass_integrated',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'strike' => 5,
                'membership' => [
                    '_id' => '5b1802b34606334e235394df',
                    'type' => 'time_classes',
                    'plan_code' => 1_521_554_388_959,
                    'start_date' => date('Y-m-d', strtotime('7 day')),
                    'expiry_date' => date('Y-m-d', strtotime('14 day')),
                ],
            ]),

            $this->createBase([
                '_id' => '5b1807eaeb3fd384228a263f',
                'branch_id' => '5addc25383266f65abf515c4',
                'namespace' => 'classpass_integrated',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'membership' => [
                    '_id' => '5b1802b34606334e235394df',
                    'type' => 'time_classes',
                    'plan_code' => 1_521_554_388_959,
                    'start_date' => date('Y-m-d', strtotime('+2 days')),
                    'expiry_date' => date('Y-m-d', strtotime('+6 days')),
                ],
            ]),

            $this->createBase([
                '_id' => '5b181291aadae0f18e8983a2',
                'branch_id' => '5addc25383266f65abf515c4',
                'namespace' => 'classpass_integrated',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'membership' => [
                    'type' => 'payg',
                ],
            ]),

            $this->createBase([
                '_id' => '5b181291aadae0f18e8983a3',
                'branch_id' => '5addc25383266f65abf515c4',
                'namespace' => 'classpass_integrated',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'membership' => [
                    'type' => 'payg',
                ],
            ]),

            $this->createBase([
                '_id' => '5b181291aadae0f18e8983a4',
                'branch_id' => '5addc25383266f65abf515c4',
                'namespace' => 'classpass_integrated',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'membership' => [
                    'type' => 'payg',
                ],
            ]),

            $this->createBase([
                '_id' => '5b19430ea0d987945a16432d',
                'branch_id' => '5addc25383266f65abf515c4',
                'namespace' => 'classpass_integrated',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'membership' => [
                    '_id' => '5b19486ac13acae3b31cf562',
                    'type' => 'time_classes',
                    'plan_code' => 1_521_554_388_959,
                    'start_date' => date('Y-m-d', strtotime('7 day')),
                    'expiry_date' => date('Y-m-d', strtotime('14 day')),
                    'subscription' => [
                        'subscription_plan_id' => '59c8dc7c3ebaf48d6b0041a9',
                        'interval' => 'month',
                        'interval_count' => 1,
                        'price' => 20,
                        'upfront_fee' => 0,
                        'force_start' => false,
                        'stripe_id' => 'sub_fakeStripeId',
                    ],
                ],
            ]),

            $this->createBase([
                '_id' => '5b19430ea0d987945a16432f',
                'type' => 'MEMBER',
                'branch_id' => '49a7011a05c677b9a916612a',
                'origin_branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'membership' => [
                    '_id' => '5b19486ac13acae3b31cf562',
                    'type' => 'time_classes',
                    'plan_code' => 1_521_554_388_959,
                    'start_date' => date('Y-m-d', strtotime('7 day')),
                    'expiry_date' => date('Y-m-d', strtotime('14 day')),
                    'subscription' => [
                        'subscription_plan_id' => '59c8dc7c3ebaf48d6b0041a9',
                        'interval' => 'month',
                        'interval_count' => 1,
                        'price' => 20,
                        'upfront_fee' => 0,
                        'force_start' => false,
                        'stripe_id' => 'sub_fakeStripeId2',
                        'payment_gateway_id' => '23523532',
                    ],
                ],
            ]),

            // Roaming Member with 3 branches
            $this->createBase([
                '_id' => '5b2a698c912db0f8e5f958b9',
                'branch_id' => [
                    '5b2a6999389704011b02d6bf',
                    '5b2a69a23152c0744c2df3aa',
                    '5b2a69ab7358de215ef5fe2c',
                ],
            ]),

            // Member without roaming
            $this->createBase([
                '_id' => '5b2a6c8cc5bfdbba5f755f8c',
                'branch_id' => '5b2a6c972325f90e70e01618',
            ]),
            $this->createBase([
                '_id' => '5a2b6d8dc5bfdbba5f578f8d',
                'branch_id' => '5c783d4cd510f9635ad4a6b3',
                'email' => '<EMAIL>',
            ]),
            $this->createBase([
                '_id' => '5a2b6d8dc5bfdbba5f578f8e',
                'branch_id' => '5c783d4cd510f9635ad4a6b3',
                'email' => '<EMAIL>',
                'type' => 'RECEPTION',
            ]),
            $this->createPaygMember([
                '_id' => '5b3b59f0ee494da4117d9915',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'branch_id' => '5addc25383266f65abf515c4',
                'namespace' => 'classpass_integrated',
            ]),
            // Testing Update First Booking Flag - member with 2 bookings
            $this->createBase([
                '_id' => '5b3cd978d47b164572a65efa',
                'branch_id' => '5addc25383266f65abf515c4',
                'namespace' => 'classpass_integrated',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'membership' => [
                    'type' => 'payg',
                ],
            ]),
            $this->createPaygMember([
                '_id' => '5b3b7ead5c993094fb28aeea',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'branch_id' => '5addc25383266f65abf515c4',
                'namespace' => 'classpass_integrated',
                'receive_marketing' => true,
            ]),
            [
                '_id' => new MongoId('5a2aad49e1608aa72b0041a8'),
                'branch_id' => '5addc25383266f65abf515c4',
                'namespace' => 'classpass_integrated',
                'active' => true,
                'first_name' => 'Testing',
                'last_name' => 'CreditBasedMembership',
                'gender' => [
                    'name' => 'FEMALE',
                    'label' => 'F',
                ],
                'phone' => '1010103423',
                'email' => '<EMAIL>',
                'birth' => null,
                'emergency_contact' => null,
                'membership' => [
                    '_id' => '5ab113cf773dd001e3570322',
                    'type' => 'time_classes',
                    'membership_group_id' => null,
                    'plan_code' => 1_521_554_388_953,
                    'branch_id' => '49a7011a05c677b9a916612a',
                    'branch_name' => 'Glofox',
                    'roaming_enabled' => false,
                    'duration_time_unit' => 'week',
                    'duration_time_unit_count' => 1,
                    'start_date' => date('Y-m-d', strtotime('-7 day')),
                    'expiry_date' => date('Y-m-d', strtotime('+14 day')),
                ],
                'strike' => null,
                'type' => 'MEMBER',
                'email' => '<EMAIL>',
                'modified' => new MongoDate(),
                'created' => new MongoDate(),
                'categories' => [],
                'name' => 'Testing CreditBasedMembership',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'PAYGPAYMENT' => true,
            ],
            $this->createBase([
                '_id' => '5b19430ea0d987945a164333',
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'membership' => [
                    '_id' => '5b19486ac13acae3b31cf562',
                    'type' => 'time',
                    'plan_code' => '1521554388959',
                    'start_date' => date('Y-m-d H:i:s', strtotime('-1 month')),
                    'expiry_date' => date('Y-m-d H:i:s'),
                    'subscription' => [
                        'subscription_plan_id' => '59c8dc7c3ebaf48d6b0041a9',
                        'interval' => 'month',
                        'interval_count' => 1,
                        'price' => 20,
                        'upfront_fee' => 0,
                        'force_start' => false,
                        'stripe_id' => 'sub_00Z',
                    ],
                ],
            ]),
            $this->createBase([
                '_id' => '5b19430ea0d987945a164334',
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'membership' => [
                    '_id' => '5b19486ac13acae3b31cf563',
                    'type' => 'time',
                    'plan_code' => '1521554388960',
                    'start_date' => date('Y-m-d H:i:s', strtotime('-1 month')),
                    'expiry_date' => date('Y-m-d H:i:s'),
                    'subscription' => [
                        'subscription_plan_id' => '59c8dc7c3ebaf48d6b0041a9',
                        'interval' => 'month',
                        'interval_count' => 1,
                        'price' => 20,
                        'upfront_fee' => 0,
                        'force_start' => false,
                        'stripe_id' => 'sub_007',
                    ],
                ],
            ]),

            $this->createBase([
                '_id' => '5b19430ea0d987945a164349',
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'membership' => [
                    '_id' => '5b19486ac13acae3b31cf563',
                    'type' => 'time',
                    'plan_code' => '1521554388960',
                    'start_date' => date('Y-m-d', strtotime('-5 day')),
                    'expiry_date' => date('Y-m-d'),
                    'subscription' => [
                        'subscription_plan_id' => '59c8dc7c3ebaf48d6b0041a9',
                        'interval' => 'month',
                        'interval_count' => 1,
                        'price' => 20,
                        'upfront_fee' => 0,
                        'force_start' => false,
                        'stripe_id' => 'sub_001',
                    ],
                ],
            ]),

            $this->createBase([
                '_id' => '5b19430ea0d987945a164350',
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'membership' => [
                    '_id' => '5b19486ac13acae3b31cf563',
                    'type' => 'time',
                    'plan_code' => '1521554388960',
                    'start_date' => date('Y-m-d'),
                    'expiry_date' => date('Y-m-d', strtotime('+7 day')),
                    'subscription' => [
                        'subscription_plan_id' => '59c8dc7c3ebaf48d6b0041a9',
                        'interval' => 'month',
                        'interval_count' => 1,
                        'price' => 20,
                        'upfront_fee' => 0,
                        'force_start' => false,
                        'stripe_id' => 'sub_001x',
                    ],
                ],
            ]),

            $this->createMemberWithSubscriptionCredits(
                '59cd0e4b2a04b921fb568dd6',
                '<EMAIL>',
                'sub_008',
                new MongoDate(Carbon::now()->modify('-7 days')->getTimestamp())
            ),
            $this->createMemberWithSubscriptionCredits(
                '59cd0e4b2a04b921fb568dd7',
                '<EMAIL>',
                'sub_009',
                new MongoDate(Carbon::now()->modify('-7 days')->getTimestamp())
            ),
            $this->createMemberWithSubscriptionCredits(
                '5b76ea7ecfc922c482b174c7',
                '<EMAIL>',
                'sub_010',
                new MongoDate(Carbon::now()->modify('-7 days')->getTimestamp())
            ),
            [
                '_id' => new MongoId('5b5b28f72e6cb30f934ee2d8'),
                'branch_id' => '5b5b265bce2cf6bdb9ad6d9a',
                'namespace' => 'ukclient',
                'active' => true,
                'categories' => [],
                'type' => 'TRAINER',
                'first_name' => 'Test',
                'last_name' => 'Test',
                'phone' => '0899991823',
                'description' => 'Worst Trainer Ever!',
                'email' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'bookable' => true,
                'login' => '<EMAIL>',
                'experience' => 'A lot...',
                'gender' => 'M',
                'birth' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'twitter' => '@timmy',
                'facebook' => 'https://facebook.com/timmy',
                'bookable' => true,
                'stripe_token' => null,
                'answers' => [],
                'card_id' => null,
                'stripe_customer_id' => null,
                'stripe_cc_token' => null,
                'auto_generated' => false,
                'access_barcode' => '1231233218',
                'created' => new MongoDate(),
                'modified' => new MongoDate(),
                'strike' => 0,
                'private_desc' => 'Why so secret?',
            ],

            $this->createBase([
                '_id' => '5b5b38e9efb690f511a6458d',
                'branch_id' => '5b5b265bce2cf6bdb9ad6d9a',
                'namespace' => 'ukclient',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'membership' => [
                    'type' => 'payg',
                ],
            ]),

            [
                '_id' => new MongoId('59a3011a05c677bda916644a'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'categories' => [],
                'type' => 'MEMBER',
                'first_name' => 'Test',
                'last_name' => "O'connell",
                'lead_status' => \Glofox\Domain\Leads\Status::MEMBER,
                'phone' => '0899991823',
                'description' => 'Member to be paused!',
                'email' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'bookable' => true,
                'login' => '<EMAIL>',
                'experience' => 'A lot...',
                'gender' => 'M',
                'birth' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'twitter' => '@timmy',
                'facebook' => 'https://facebook.com/timmy',
                'bookable' => false,
                'stripe_token' => null,
                'answers' => [],
                'card_id' => null,
                'stripe_customer_id' => null,
                'stripe_cc_token' => null,
                'auto_generated' => false,
                'access_barcode' => '1231233218',
                'created' => new MongoDate(),
                'modified' => new MongoDate(),
                'strike' => 0,
                'private_desc' => 'Why so secret?',
                'membership' => [
                    '_id' => '54107c1cd7b6ddc3a98b4577',
                    'type' => 'time',
                    'start_date' => date('Y-m-d', strtotime('-7 day')),
                    'plan_code' => '1506335805840',
                    'boooked_events' => 0,
                    'expiry_date' => date('Y-m-d', strtotime('+23 day')),
                    'subscription' => [
                        'subscription_plan_id' => '59c8dc7c3ebaf48d6b0041a9',
                        'interval' => 'month',
                        'interval_count' => 1,
                        'price' => 10,
                        'upfront_fee' => 0,
                        'force_start' => false,
                        'credits' => [
                            [
                                'model' => 'programs',
                                'category_id' => null,
                                'num_sessions' => 10,
                                'model_ids' => null,
                                'expiry' => [
                                    'interval' => 'month',
                                    'interval_count' => 1,
                                ],
                                'end_date' => null,
                            ],
                        ],
                        'stripe_id' => 'sub_tobepaused',
                    ],
                ],
                'device' => [
                    'os' => 'android',
                ],
                '_pattern' => null,
                'version' => [
                    'minor' => 9,
                    'major' => 9,
                    'revision' => 25,
                ],
                'WAIVER' => true,
                'MEMBERPURCHASE' => true,
                'PAYGPAYMENT' => true,
                'transaction' => null,
                'emergency_contact' => '<EMAIL>',
                'pwd_token' => null,
                'last_login' => new MongoDate(),
            ],

            [
                '_id' => new MongoId('5b5b348a06e5fac00b811a6d'),
                'branch_id' => '5b5b265bce2cf6bdb9ad6d9a',
                'namespace' => 'ukclient',
                'active' => true,
                'categories' => [],
                'type' => 'ADMIN',
                'name' => 'Admin Istrator',
                'first_name' => 'Admin',
                'last_name' => 'Istrator',
                'phone' => '65132056768451320',
                'description' => 'Worst Trainer Ever!',
                'email' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'bookable' => true,
                'login' => '<EMAIL>',
                'experience' => 'A lot...',
                'gender' => 'M',
                'birth' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'twitter' => '@timmy',
                'facebook' => 'https://facebook.com/timmy',
                'phone' => '123123321',
                'bookable' => false,
                'stripe_token' => null,
                'answers' => [],
                'card_id' => null,
                'stripe_customer_id' => null,
                'stripe_cc_token' => null,
                'auto_generated' => false,
                'access_barcode' => '123123321',
                'created' => new MongoDate(),
                'modified' => new MongoDate(),
                'last_login' => new MongoDate(),
                'strike' => 0,
                'private_desc' => 'Why so secret?',
                'membership' => [
                    'type' => 'payg',
                ],
                'device' => [
                    'os' => 'android',
                ],
                '_pattern' => null,
                'version' => [
                    'minor' => 9,
                    'major' => 9,
                    'revision' => 25,
                ],
                'WAIVER' => true,
                'MEMBERPURCHASE' => true,
                'PAYGPAYMENT' => true,
                'transaction' => null,
                'emergency_contact' => '<EMAIL>',
                'pwd_token' => null,
            ],

            $this->createBase([
                '_id' => '5b181291aadae0f18e898455',
                'branch_id' => '5addc25383266f65abf515c4',
                'namespace' => 'classpass_integrated',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
            ]),
            $this->createBase([
                '_id' => '5b7ad05af520ea6624c59bc5',
                'origin_branch_id' => '49a7011a05c677b9a916612a',
                'branch_id' => [
                    '49a7011a05c677b9a916612a',
                    '5a9591bcdb07bce527400717',
                ],
                'first_name' => 'Testing Roaming Subscriptions',
                'last_name' => 'with credits',
                'login' => '<EMAIL>',
                'email' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'membership' => [
                    '_id' => '5b7ad3cd01c9bb54498cac07',
                    'type' => 'time_classes',
                    'start_date' => new MongoDate(Carbon::now()->modify('-8 days')->getTimestamp()),
                    'membership_group_id' => null,
                    'plan_code' => '1506335305324',
                    'boooked_events' => 0,
                    'expiry_date' => new MongoDate(Carbon::now()->modify('-1 days')->getTimestamp()),
                    'roaming_enabled' => true,
                    'subscription' => [
                        'subscription_plan_id' => '59c8dc7c3ebaf48d6b0041a9',
                        'interval' => 'week',
                        'interval_count' => 1,
                        'price' => 10,
                        'upfront_fee' => 0,
                        'force_start' => false,
                        'credits' => [
                            [
                                'model' => 'programs',
                                'category_id' => null,
                                'num_sessions' => 10,
                                'model_ids' => null,
                                'expiry' => [
                                    'interval' => 'month',
                                    'interval_count' => 1,
                                ],
                                'end_date' => null,
                            ],
                        ],
                        'stripe_id' => 'sub_011',
                    ],
                ],
            ]),
            [
                '_id' => new MongoId('5b7beae94803d9602de2dda5'),
                'branch_id' => '5addc25383266f65abf515c4',
                'namespace' => 'classpass_integrated',
                'active' => true,
                'first_name' => 'Testing',
                'last_name' => 'Expired member with credits',
                'gender' => [
                    'name' => 'FEMALE',
                    'label' => 'F',
                ],
                'phone' => '101010343413',
                'email' => '<EMAIL>',
                'birth' => null,
                'emergency_contact' => null,
                'membership' => [
                    '_id' => '5ab113cf773dd001e3570322',
                    'type' => 'time_classes',
                    'membership_group_id' => null,
                    'plan_code' => 1_521_554_388_953,
                    'duration_time_unit' => 'week',
                    'duration_time_unit_count' => 1,
                    'start_date' => date('Y-m-d', strtotime('-14 day')),
                    'expiry_date' => date('Y-m-d', strtotime('-1 day')),
                ],
                'strike' => null,
                'type' => 'MEMBER',
                'email' => '<EMAIL>',
                'modified' => new MongoDate(),
                'created' => new MongoDate(),
                'categories' => [],
                'name' => 'Testing CreditBasedMembership',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'PAYGPAYMENT' => true,
            ],
            [
                '_id' => new MongoId('5b7beae94813d9602de2dda5'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'origin_branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => '5e7cb6703d36321bf52c63d7',
                'active' => true,
                'first_name' => 'Testing',
                'last_name' => 'not attended gets a strike',
                'gender' => [
                    'name' => 'FEMALE',
                    'label' => 'F',
                ],
                'phone' => '101010343413',
                'email' => '<EMAIL>',
                'birth' => null,
                'emergency_contact' => null,
                'membership' => [
                    'type' => 'payg',
                ],
                'strike' => 0,
                'type' => 'MEMBER',
                'login' => '<EMAIL>',
                'modified' => new MongoDate(),
                'created' => new MongoDate(),
                'categories' => [],
                'name' => 'Testing not attended gets a strike',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'PAYGPAYMENT' => true,
            ],
            [
                '_id' => new MongoId('5b7beae94813d9632de2dda5'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'origin_branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => '5e7cb6703d36321bf52c63d7',
                'active' => true,
                'first_name' => 'Testing',
                'last_name' => 'attended does not get a strike',
                'gender' => [
                    'name' => 'FEMALE',
                    'label' => 'F',
                ],
                'phone' => '101010343413',
                'email' => '<EMAIL>',
                'birth' => null,
                'emergency_contact' => null,
                'membership' => [
                    'type' => 'payg',
                ],
                'strike' => 0,
                'type' => 'MEMBER',
                'login' => '<EMAIL>',
                'modified' => new MongoDate(),
                'created' => new MongoDate(),
                'categories' => [],
                'name' => 'Testing attended does not get a strik',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'PAYGPAYMENT' => true,
            ],
            [
                '_id' => new MongoId('5b7beae94823d9602de2dda5'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => '5e7cb6703d36321bf52c63d7',
                'active' => true,
                'first_name' => 'Testing',
                'last_name' => 'Waiting does not get a strike',
                'gender' => [
                    'name' => 'FEMALE',
                    'label' => 'F',
                ],
                'phone' => '101010343413',
                'email' => '<EMAIL>',
                'birth' => null,
                'emergency_contact' => null,
                'membership' => [
                    'type' => 'payg',
                ],
                'strike' => 0,
                'type' => 'MEMBER',
                'login' => '<EMAIL>',
                'modified' => new MongoDate(),
                'created' => new MongoDate(),
                'categories' => [],
                'name' => 'Testing Waiting does not get a strike',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'PAYGPAYMENT' => true,
            ],
            [
                '_id' => new MongoId('5b7beae94833d9602de2dda5'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => '5e7cb6703d36321bf52c63d7',
                'active' => true,
                'first_name' => 'Testing',
                'last_name' => 'Canceled does not get a strike',
                'gender' => [
                    'name' => 'FEMALE',
                    'label' => 'F',
                ],
                'phone' => '101010343413',
                'email' => '<EMAIL>',
                'birth' => null,
                'emergency_contact' => null,
                'membership' => [
                    'type' => 'payg',
                ],
                'strike' => 0,
                'type' => 'MEMBER',
                'login' => '<EMAIL>',
                'modified' => new MongoDate(),
                'created' => new MongoDate(),
                'categories' => [],
                'name' => 'Testing Canceled does not get a strike',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'PAYGPAYMENT' => true,
            ],
            $this->createBase([
                '_id' => '5b8c597271d970618cc5dcdf',
                'namespace' => 'glofox',
                'branch_id' => '49a7011a05c677b9a916612a',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
            ]),
            // Member with first booking credits
            $this->createBase([
                '_id' => '5b8c6dd726dd9ebba8eed07b',
                'namespace' => 'glofox',
                'branch_id' => '49a7011a05c677b9a916612a',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
            ]),
            // Member with 1 trainers credits
            $this->createBase([
                '_id' => '5ba8cdfd6b24361fa2da2249',
                'namespace' => 'glofox',
                'branch_id' => '49a7011a05c677b9a916612a',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
            ]),
            // Member with 1 program credits
            $this->createBase([
                '_id' => '5ba8cdfd6b24361fa2da3333',
                'namespace' => 'glofox',
                'branch_id' => '49a7011a05c677b9a916612a',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
            ]),

            // incomplete member by alexis for testing membership validation
            $this->createBase([
                '_id' => '59d3aa6c6ce72e9af964d1b1',
                'branch_id' => '59d3aa6591f32f90bc96d81e',
                'email' => '<EMAIL>',
                'membership' => [
                    'type' => 'payg',
                ],
                'strike' => 0,
            ]),

            $this->createBase([
                '_id' => '5b19430ea0d988945a164335',
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'membership' => [
                    '_id' => '54107c1cd7b6ddc3a98b4577',
                    'type' => 'time',
                    'plan_code' => '1506335805840',
                    'start_date' => date('Y-m-d', strtotime('-28 day')),
                    'expiry_date' => date('Y-m-d', strtotime('+2 day')),
                ],
            ]),
            $this->createBase([
                '_id' => '5b19430ea0d988945a164344',
                'branch_id' => '49a7011a05c677b9a916612a',
                'origin_branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'membership' => [
                    '_id' => '54107c1cd7b6ddc3a98b4577',
                    'user_membership_id' => '5cf91831b3cb1f0001a6f4a6',
                    'type' => 'time',
                    'plan_code' => '1506335805841',
                    'start_date' => date('Y-m-d', strtotime('-30 day')),
                    'expiry_date' => date('Y-m-d'),
                    'subscription' => [
                        'subscription_plan_id' => '5be4551117844100fd1f0e84',
                        'interval' => 'month',
                        'interval_count' => 1,
                        'price' => 10,
                        'upfront_fee' => 0,
                        'force_start' => false,
                        'stripe_id' => 'sub_STUB_4344',
                        'payment_gateway_id' => 'sub_STUB_4344',
                        'paused' => false,
                        'payment_method_type_id' => 'CARD',
                    ],
                ],
            ]),
            $this->createBase([
                '_id' => '5b1940d9889430ea5a164344',
                'branch_id' => '49a7011a05c677b9a916612a',
                'origin_branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'membership' => [
                    '_id' => '54107c1cd7b6ddc3a98b4577',
                    'user_membership_id' => '5cf91831b3c1a6f4a6b1f000',
                    'type' => 'time',
                    'plan_code' => '1506335805841',
                    'start_date' => date('Y-m-d', strtotime('-30 day')),
                    'expiry_date' => date('Y-m-d'),
                    'subscription' => [
                        'subscription_plan_id' => '5be4551117844100fd1f0e84',
                        'interval' => 'month',
                        'interval_count' => 1,
                        'price' => 10,
                        'upfront_fee' => 0,
                        'force_start' => false,
                        'stripe_id' => 'sub_STUB_4344',
                        'payment_gateway_id' => 'sub_STUB_4344',
                        'paused' => false,
                        'payment_method_type_id' => 'CARD',
                    ],
                ],
            ]),
            $this->createBase([
                '_id' => '5b19430ea0d988945a164334',
                'branch_id' => '5ce808750d35f24839739957',
                'origin_branch_id' => '5ce808750d35f24839739957',
                'namespace' => 'glofox',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'membership' => [
                    '_id' => '54107c1cd7b6ddc3a98b4537',
                    'branch_id' => '5ce808750d35f24839739957',
                    'user_membership_id' => '5cf91831b3cb1f0001a6f4a6',
                    'type' => 'time_classes',
                    'plan_code' => '1506335805977',
                    'start_date' => date('Y-m-d', strtotime('today midnight')),
                    'expiry_date' => date('Y-m-d'),
                    'subscription' => [
                        'subscription_plan_id' => '5be4551117844100fd1f0e84',
                        'interval' => 'month',
                        'interval_count' => 1,
                        'price' => 10,
                        'upfront_fee' => 0,
                        'force_start' => false,
                        'stripe_id' => 'sub_STUB_4344',
                        'payment_gateway_id' => 'sub_STUB_4344',
                        'paused' => false,
                        'payment_method_type_id' => 'CARD',
                        'credits' => [
                            [
                                'branch_id' => '5ce808750d35f24839739957',
                                'namespace' => 'glofox',
                                'active' => true,
                                'model' => 'programs',
                                'category_id' => null,
                                'num_sessions' => 10,
                                'model_ids' => null,
                                'expiry' => null,
                                'end_date' => null,
                            ],
                        ],
                    ],
                ],
            ]),

            $this->createBase([
                '_id' => '6331bab35ade2753937fa6fd',
                'branch_id' => '5ce808750d35f24839739957',
                'namespace' => 'glofox',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'membership' => [
                    '_id' => '54107c1cd7b6ddc3a98b4537',
                    'branch_id' => '5ce808750d35f24839739957',
                    'user_membership_id' => '5cf91b3cb1f000a641a6f183',
                    'type' => 'time_classes',
                    'plan_code' => '1506335805977',
                    'start_date' => date('Y-m-d', strtotime('today midnight +23 hours')),
                    'expiry_date' => date('Y-m-d', strtotime('+1 months')),
                    'subscription' => [
                        'subscription_plan_id' => '5be4551117844100fd1f0e84',
                        'interval' => 'month',
                        'interval_count' => 1,
                        'price' => 10,
                        'upfront_fee' => 0,
                        'force_start' => false,
                        'stripe_id' => 'sub_STUB_4344',
                        'payment_gateway_id' => 'sub_STUB_4344',
                        'paused' => false,
                        'payment_method_type_id' => 'CARD',
                        'credits' => [
                            [
                                'branch_id' => '5ce808750d35f24839739957',
                                'namespace' => 'glofox',
                                'active' => true,
                                'model' => 'programs',
                                'category_id' => null,
                                'num_sessions' => 10,
                                'model_ids' => null,
                                'expiry' => null,
                                'end_date' => null,
                            ],
                        ],
                    ],
                ],
            ]),

            $this->createBase([
                '_id' => '5b19430ea0d988945a164f35',
                'branch_id' => '5ce808750d35f24839739957',
                'origin_branch_id' => '5ce808750d35f24839739957',
                'namespace' => 'glofox',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'membership' => [
                    '_id' => '54107c1cd7b6ddc3a98b4537',
                    'branch_id' => '5ce808750d35f24839739957',
                    'user_membership_id' => '5cf91831b3cb1f0001a6f4a6',
                    'type' => 'time_classes',
                    'plan_code' => '1506335805977',
                    'start_date' => date('Y-m-d', strtotime('-30 day')),
                    'expiry_date' => date('Y-m-d'),
                    'subscription' => [
                        'subscription_plan_id' => '5be4551117844100fd1f0e84',
                        'interval' => 'month',
                        'interval_count' => 1,
                        'price' => 10,
                        'upfront_fee' => 0,
                        'force_start' => false,
                        'stripe_id' => 'sub_STUB_4344',
                        'payment_gateway_id' => 'sub_STUB_4344',
                        'paused' => false,
                        'payment_method_type_id' => 'CARD',
                        'credits' => [
                            [
                                'branch_id' => '5ce808750d35f24839739957',
                                'namespace' => 'glofox',
                                'active' => true,
                                'model' => 'programs',
                                'category_id' => null,
                                'num_sessions' => 10,
                                'model_ids' => null,
                                'expiry' => null,
                                'end_date' => null,
                            ],
                        ],
                    ],
                ],
            ]),

            $this->createBase([
                '_id' => '5b19430ea0d988945a164343',
                'branch_id' => '5ce808750d35f24839739959',
                'origin_branch_id' => '5ce808750d35f24839739959',
                'namespace' => 'glofox',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'membership' => [
                    '_id' => '5f159e696222aaa4d0ee4ce2',
                    'branch_id' => '5ce808750d35f24839739959',
                    'user_membership_id' => '5cf91831b3cb1f0001a6f4a6',
                    'type' => 'time_classes',
                    'plan_code' => '1506335805979',
                    'start_date' => date('Y-m-d', strtotime('-30 day')),
                    'expiry_date' => date('Y-m-d'),
                    'subscription' => [
                        'subscription_plan_id' => '5be4551117844100fd1f0e84',
                        'interval' => 'month',
                        'interval_count' => 1,
                        'price' => 10,
                        'upfront_fee' => 0,
                        'force_start' => false,
                        'stripe_id' => 'sub_STUB_4344',
                        'payment_gateway_id' => 'sub_STUB_4344',
                        'paused' => false,
                        'payment_method_type_id' => 'CARD',
                        'credits' => [
                            [
                                'branch_id' => '5ce808750d35f24839739959',
                                'namespace' => 'glofox',
                                'active' => true,
                                'model' => 'programs',
                                'category_id' => null,
                                'num_sessions' => 10,
                                'model_ids' => null,
                                'expiry' => null,
                                'end_date' => null,
                            ],
                        ],
                    ],
                ],
            ]),

            $this->createBase([
                '_id' => '5b19430ea0d988945a264343',
                'branch_id' => '5ce808750d35f24839739959',
                'namespace' => 'glofox',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'membership' => [
                    '_id' => '5f159e696222aaa4d0ee4ce2',
                    'branch_id' => '5ce808750d35f24839739959',
                    'user_membership_id' => '5cf91831b3cb1f0001a6f4a6',
                    'type' => 'time_classes',
                    'plan_code' => '1506335805979',
                    'start_date' => date('Y-m-d', strtotime('-30 day')),
                    'expiry_date' => date('Y-m-d'),
                    'subscription' => [
                        'subscription_plan_id' => '5be4551117844100fd1f0e84',
                        'interval' => 'month',
                        'interval_count' => 1,
                        'price' => 10,
                        'upfront_fee' => 0,
                        'force_start' => false,
                        'stripe_id' => 'sub_STUB_4344',
                        'payment_gateway_id' => 'sub_STUB_4344',
                        'paused' => false,
                        'payment_method_type_id' => 'CARD',
                        'credits' => [
                            [
                                'branch_id' => '5ce808750d35f24839739959',
                                'namespace' => 'glofox',
                                'active' => true,
                                'model' => 'programs',
                                'category_id' => null,
                                'num_sessions' => 10,
                                'model_ids' => null,
                                'expiry' => null,
                                'end_date' => null,
                            ],
                        ],
                    ],
                ],
            ]),

            $this->createBase([
                '_id' => '5b19430ea0d988945a164345',
                'branch_id' => '49a7011a05c677b9a916612a',
                'origin_branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'membership' => [
                    '_id' => '54107c1cd7b6ddc3a98b4577',
                    'user_membership_id' => '5cf91831b3cb1f0001a6f4a6',
                    'type' => 'time',
                    'plan_code' => '1506335805841',
                    'start_date' => date('Y-m-d', strtotime('-30 day')),
                    'expiry_date' => date('Y-m-d'),
                    'subscription' => [
                        'subscription_plan_id' => '5be4551117844100fd1f0e84',
                        'interval' => 'month',
                        'interval_count' => 1,
                        'price' => 10,
                        'upfront_fee' => 0,
                        'force_start' => false,
                        'stripe_id' => 'sub_STUB_4345',
                        'payment_gateway_id' => 'sub_STUB_4345',
                        'paused' => false,
                        'payment_method_type_id' => 'CARD',
                    ],
                ],
            ]),

            $this->createBase([
                '_id' => '5b19430ea0d988945a164346',
                'branch_id' => '49a7011a05c677b9a916612a',
                'origin_branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'membership' => [
                    '_id' => '54107c1cd7b6ddc3a98b4578',
                    'user_membership_id' => '5cf91831b3cb1f0001a6f4a7',
                    'type' => 'time_classes',
                    'plan_code' => '1506335805841',
                    'start_date' => date('Y-m-d', strtotime('-30 day')),
                    'expiry_date' => date('Y-m-d'),
                    'subscription' => [
                        'subscription_plan_id' => '5be4551117844100fd1f0e84',
                        'interval' => 'month',
                        'interval_count' => 1,
                        'price' => 10,
                        'upfront_fee' => 0,
                        'force_start' => false,
                        'stripe_id' => 'sub_STUB_4345',
                        'payment_gateway_id' => 'sub_STUB_4345',
                        'paused' => false,
                        'payment_method_type_id' => 'CARD',
                    ],
                ],
            ]),

            $this->createBase([
                '_id' => '5b19430ea0d988945a164336',
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'membership' => [
                    '_id' => '54107c1cd7b6ddc3a98b4577',
                    'type' => 'time',
                    'plan_code' => '1506335805840',
                    'start_date' => date('Y-m-d', strtotime('-35 day')),
                    'expiry_date' => date('Y-m-d', strtotime('-4 day')),
                ],
            ]),

            $this->createBase([
                '_id' => '5b19430ea0d988945a164337',
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'membership' => [
                    'type' => 'payg',
                ],
            ]),
            $this->createBase([
                '_id' => '889454f194a0d9a16433730e',
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'active' => false,
                'membership' => [
                    'type' => 'payg',
                ],
            ]),
            $this->createBase([
                '_id' => '5b19430ea0d988945a164338',
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'membership' => [
                    '_id' => '54107c1cd7b6ddc3a98b4577',
                    'user_membership_id' => '5cf91831b3cb1f0001a6f4a4',
                    'type' => 'time',
                    'plan_code' => '1506335805840',
                    'start_date' => date('Y-m-d', strtotime('-10 day')),
                    'expiry_date' => date('Y-m-d', strtotime('21 day')),
                ],
            ]),

            $this->createBase([
                '_id' => '5b19430ea0d988945a164339',
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'membership' => [
                    '_id' => '54107c1cd7b6ddc3a98b4577',
                    'user_membership_id' => '5cf91831b3cb1f0001a6f4a5',
                    'type' => 'time',
                    'plan_code' => '1506335805840',
                    'start_date' => date('Y-m-d', strtotime('-10 day')),
                    'expiry_date' => date('Y-m-d', strtotime('21 day')),
                ],
            ]),

            $this->createBase([
                '_id' => '5b19430ea0d988945a164340',
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'membership' => [
                    '_id' => '54107c1cd7b6ddc3a98b4577',
                    'user_membership_id' => '5cf91831b3cb1f0001a6f4a6',
                    'type' => 'time',
                    'plan_code' => '1506335805841',
                    'start_date' => date('Y-m-d', strtotime('-10 day')),
                    'expiry_date' => date('Y-m-d', strtotime('21 day')),
                    'subscription' => [
                        'subscription_plan_id' => '5be4551117844100fd1f0e84',
                        'interval' => 'month',
                        'interval_count' => 1,
                        'price' => 10,
                        'upfront_fee' => 0,
                        'force_start' => false,
                        'stripe_id' => 'sub_FClV60cgfdgxSd',
                        'payment_gateway_id' => 'sub_FClV60cgfdgxSd',
                        'paused' => false,
                        'payment_method_type_id' => 'CARD',
                    ],
                ],
            ]),

            $this->createBase([
                '_id' => '5b19330ea0d888945a164341',
                'branch_id' => '49a7011a05c677b9a916612b',
                'origin_branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'membership' => [
                    'type' => 'payg',
                ],
            ]),

            $this->createBase([
                '_id' => '622a0b4ae39c9971f7a28f03',
                'branch_id' => '49a7011a05c677b9a916612b',
                'origin_branch_id' => '49a7011a05c677b9a916612b',
                'namespace' => 'glofox',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'membership' => [
                    'type' => 'payg',
                ],
            ]),

            $this->createBase([
                '_id' => '622a0b4ae39c9971f7a28f04',
                'branch_id' => '49a7011a05c677b9a916612b',
                'origin_branch_id' => '49a7011a05c677b9a916612b',
                'namespace' => 'glofox',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'membership' => [
                    'type' => 'payg',
                ],
                'active' => false,
            ]),

            $this->createBase([
                '_id' => '622a0b4ae39c9971f7a28f05',
                'branch_id' => '49a7011a05c677b9a916612c',
                'origin_branch_id' => '49a7011a05c677b9a916612c',
                'namespace' => 'glofox',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'membership' => [
                    'type' => 'payg',
                ],
            ]),

            $this->createMemberWithValidSubscriptionTimeMembership(
                '5b19330ea0d888945a164346',
                '<EMAIL>',
                [
                    'branch_id' => ['49a7011a05c677b9a916612b', '49a7011a05c677b9a916612a'],
                    'origin_branch_id' => '49a7011a05c677b9a916612b',
                ]
            ),

            $this->createBase([
                '_id' => '5b19430ea0d977945a164331',
                'branch_id' => '49a7011a05c677b9a916612a',
                'origin_branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'membership' => [
                    '_id' => '54107c1cd7b5ddc3a98b4571',
                    'type' => 'num_classes',
                    'plan_code' => '1506235805841',
                    'start_date' => date('Y-m-d', strtotime('-28 day')),
                    'expiry_date' => date('Y-m-d', strtotime('+2 day')),
                ],
            ]),

            $this->createBase([
                '_id' => '5ecafa8b0ff27e6848419e88',
                'branch_id' => '5addc25383266f65abf515c4',
                'namespace' => 'glofox',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'membership' => [
                    '_id' => '5b8eb03b10e2c82a4d443f9c',
                    'type' => 'num_classes',
                    'plan_code' => '1536078615117',
                    'plan_price' => 10,
                    'plan_upfront_fee' => 0,
                    'start_date' => date('Y-m-d', strtotime('-28 day')),
                    'expiry_date' => date('Y-m-d', strtotime('+2 day')),
                ],
                'MEMBERPURCHASE' => false,
            ]),

            $this->createBase([
                '_id' => '5b19430ea0d977945a164332',
                'branch_id' => '49a7011a05c677b9a916612a',
                'origin_branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'membership' => [
                    '_id' => '54107c1cd7b5ddc3a98b4571',
                    'type' => 'time',
                    'plan_code' => '1506235805841',
                    'start_date' => date('Y-m-d', strtotime('-28 day')),
                    'expiry_date' => date('Y-m-d', strtotime('+2 day')),
                ],
            ]),

            $this->createBase([
                '_id' => '5b19430ea0d977945a164333',
                'branch_id' => '49a7011a05c677b9a916612a',
                'origin_branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'membership' => [
                    '_id' => '54107c1cd7b5ddc3a98b4571',
                    'type' => 'time_classes',
                    'plan_code' => '1506235805841',
                    'start_date' => date('Y-m-d', strtotime('-28 day')),
                    'expiry_date' => date('Y-m-d', strtotime('+2 day')),
                ],
            ]),

            $this->createBase([
                '_id' => '5b19430ea0d977945a164334',
                'branch_id' => '49a7011a05c677b9a916612a',
                'origin_branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'membership' => [
                    '_id' => '54107c1cd7b5ddc3a98b4571',
                    'type' => 'time_classes',
                    'plan_code' => '1506235805841',
                    'start_date' => date('Y-m-d', strtotime('-28 day')),
                    'expiry_date' => date('Y-m-d', strtotime('+2 day')),
                    'user_membership_id' => '5cf91831b2cb1f0001a6f4a1',
                ],
            ]),

            $this->createBase([
                '_id' => '5b19430ea0d977945a164335',
                'branch_id' => '5d56d214be51ef060e38408c',
                'namespace' => 'glofox',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'membership' => [
                    '_id' => '54107c1cd7b5ddc3a98b4572',
                    'type' => 'time_classes',
                    'plan_code' => '1506235805842',
                    'start_date' => Carbon::today('Pacific/Rarotonga')->subMonth()->toDateString(),
                    'expiry_date' => Carbon::today('Pacific/Rarotonga')->addMonth()->toDateString(),
                    'user_membership_id' => '5fc626a8f64e0a089f214420',
                ],
            ]),

            $this->createBase([
                '_id' => '5b19430ea0d977945a164336',
                'branch_id' => '5d56d214be51ef060f38408c',
                'namespace' => 'glofox',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'membership' => [
                    '_id' => '54107c1cd7b5ddc3a98b4572',
                    'type' => 'time_classes',
                    'plan_code' => '1506235805842',
                    'start_date' => Carbon::today('Pacific/Apia')->subMonth()->toDateString(),
                    'expiry_date' => Carbon::today('Pacific/Apia')->addMonth()->toDateString(),
                ],
            ]),

            $this->createBase([
                '_id' => '5ea193af03207d0e3c0b9b24',
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'membership' => [
                    '_id' => '54107c1cd7b6ddc3a98b4577',
                    'type' => 'time',
                    'plan_code' => '1506335805840',
                    'start_date' => date('Y-m-d', strtotime('-35 day')),
                    'expiry_date' => date('Y-m-d', strtotime('-4 day')),
                ],
            ]),
            $this->createBase([
                '_id' => '5ea1939f03207d0ee6324fca',
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'membership' => [
                    '_id' => '54107c1cd7b6ddc3a98b4577',
                    'type' => 'time',
                    'plan_code' => '1506335805840',
                    'start_date' => date('Y-m-d', strtotime('-35 day')),
                    'expiry_date' => date('Y-m-d', strtotime('-4 day')),
                ],
                'access_barcode' => '854123004',
            ]),
            $this->createBase([
                '_id' => '5ea193978c43570e772f6024',
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'active' => false,
                'membership' => [
                    '_id' => '54107c1cd7b6ddc3a98b4577',
                    'type' => 'time',
                    'plan_code' => '1506335805840',
                    'start_date' => date('Y-m-d', strtotime('-35 day')),
                    'expiry_date' => date('Y-m-d', strtotime('-4 day')),
                ],
                'access_barcode' => '324120089',
            ]),

            $this->createPaygMember([
                '_id' => '59a7010a05c677b3a916715d',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'branch_id' => '5e7cb6a53d36311bf52c63d8',
                'namespace' => 'testnamespace',
            ]),

            $this->createPaygMember([
                '_id' => '59a7010a05c777b3a916715d',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'branch_id' => '5e7cb6a53d36311bf52c63d8',
                'namespace' => 'testnamespace',
            ]),

            $this->createPaygMember([
                '_id' => '59a7010a05c877b3a916715d',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'branch_id' => '5e7cb6a53d36311bf52c63d8',
                'namespace' => 'testnamespace',
            ]),

            $this->createAdmin([
                '_id' => new MongoId('5e9ed06cd575e6003c38658e'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'type' => 'SUPERADMIN',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
            ]),

            $this->createBase([
                '_id' => '60117f918f4b5cc0eb2340a4',
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'password' => password_hash(
                    'password123',
                    PASSWORD_ARGON2I,
                    ['cost' => env('PASSWORD_HASH_COST') ?: 10]
                ),
                'membership' => [
                    'type' => 'payg',
                ],
            ]),

            [
                '_id' => new MongoId('5e9ed06cd573e6003c38658a'),
                'branch_id' => '49a7011a05c677b9a916613a',
                'namespace' => 'glofox',
                'type' => 'MEMBER',
                'active' => true,
            ],

            [
                '_id' => new MongoId('59a3011a05c677bda916544a'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'origin_branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'categories' => [],
                'type' => 'MEMBER',
                'first_name' => 'LoginUser',
                'last_name' => "Testing",
                'lead_status' => \Glofox\Domain\Leads\Status::MEMBER,
                'phone' => '0899991823',
                'email' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'login' => '<EMAIL>',
                'created' => new MongoDate(),
                'modified' => new MongoDate(),
                'strike' => 0,
                'membership' => [
                    'type' => 'payg',
                ],
                'device' => [
                    'os' => 'android',
                    'version' => [
                        'minor' => 9,
                        'major' => 9,
                        'revision' => 25,
                    ],
                ],
                '_pattern' => null,
                'WAIVER' => true,
                'MEMBERPURCHASE' => true,
                'PAYGPAYMENT' => true,
                'pwd_token' => null,
                'last_login' => new MongoDate(),
            ],
            [
                '_id' => new MongoId('602a42a98765fc1cd3044f3c'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'categories' => [],
                'type' => 'MEMBER',
                'first_name' => 'LoginUser',
                'last_name' => "Testing",
                'lead_status' => \Glofox\Domain\Leads\Status::MEMBER,
                'phone' => '0899991823',
                'email' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$dEtyS0dzZ0pWOEpROEtSbA$HNyatlWKq+FYKgxWVSy6Ywbr8P8as0nD5kS8DjiD/N4',
                'login' => '<EMAIL>',
                'created' => new MongoDate(),
                'modified' => new MongoDate(),
                'strike' => 0,
                'membership' => [
                    'type' => 'payg',
                ],
                'device' => [
                    'os' => 'android',
                ],
                '_pattern' => null,
                'version' => [
                    'minor' => 9,
                    'major' => 9,
                    'revision' => 25,
                ],
                'WAIVER' => true,
                'MEMBERPURCHASE' => true,
                'PAYGPAYMENT' => true,
                'pwd_token' => null,
                'last_login' => new MongoDate(),
            ],

            // Member with empty metadata
            $this->createPaygMember([
                '_id' => '5adde862426415b9e9b4be9e',
                'email' => '<EMAIL>',
                'metadata' => [],
            ]),

            // Member with lead status that does not support updates
            [
                '_id' => new MongoId('5adde862427415b9e9b4be9e'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'type' => UserType::MEMBER,
                'first_name' => 'Warm',
                'last_name' => 'Lead',
                'phone' => '11123',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'lead_status' => 'MEMBER',
                'membership' => [
                    'type' => 'payg',
                ],
                'strike' => 0,
            ],

            [
                '_id' => new MongoId('5adde862427415b8e9b4be9e'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'origin_branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'type' => UserType::MEMBER,
                'first_name' => 'Meta',
                'last_name' => 'Insurance',
                'phone' => '11123',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'lead_status' => 'MEMBER',
                'membership' => [
                    'type' => 'payg',
                ],
                'strike' => 0,
                'metadata' => [
                    'insurance' => [
                        'policy_number' => 'test_policy_number',
                        'company_id' => 'test_company_id',
                        'group_id' => 'test_group_id',
                    ],
                ],
            ],

            [
                '_id' => new MongoId('5b19430ea0d987045a16432f'),
                'user_id' => '5b19430ea0d987045a16432f',
                'branch_id' => '49a7011a05c677b9a916612a',
                'origin_branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'type' => UserType::MEMBER,
                'first_name' => 'Lead',
                'last_name' => 'To Member',
                'phone' => '11123',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'lead_status' => \Glofox\Domain\Leads\Status::MEMBER,
                'membership' => [
                    'type' => 'payg',
                ],
            ],

            [
                '_id' => new MongoId('5b19430ea0d987046a16432f'),
                'user_id' => '5b19430ea0d987046a16432f',
                'branch_id' => '49a7011a05c677b9a916612a',
                'origin_branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'type' => UserType::MEMBER,
                'first_name' => 'Lead',
                'last_name' => 'To Cold',
                'phone' => '11123',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'lead_status' => \Glofox\Domain\Leads\Status::LEAD,
                'membership' => [
                    'type' => 'payg',
                ],
            ],

            [
                '_id' => new MongoId('5d6826348d8540851fe60662'),
                'user_id' => '5d6826348d8540851fe60662',
                'branch_id' => '49a7011a05c677b9a916612a',
                'origin_branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'type' => UserType::MEMBER,
                'first_name' => 'Member',
                'last_name' => 'To renew',
                'phone' => '11123',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'lead_status' => \Glofox\Domain\Leads\Status::LEAD,
                'membership' => [
                    '_id' => '54107c1cd7b6ddc3a98b4577',
                    'type' => 'time',
                    'start_date' => date('Y-m-d', strtotime('- 7 days')),
                    'expiry_date' => date('Y-m-d', strtotime('- 1 day')),
                    'plan_code' => '1506335805020',
                    'boooked_events' => 0,
                    'subscription' => [
                        'subscription_plan_id' => '59e63a1773fa6cf751723246',
                        'interval' => 'month',
                        'interval_count' => 1,
                        'price' => 10,
                        'upfront_fee' => 0,
                        'force_start' => false,
                    ],
                ],
            ],
            [
                '_id' => new MongoId('5d6826348d8540851fe60777'),
                'user_id' => '5d6826348d8540851fe60777',
                'branch_id' => '49a7011a05c677b9a916612a',
                'origin_branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'type' => UserType::MEMBER,
                'first_name' => 'Member',
                'last_name' => 'To renew',
                'phone' => '11123',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'lead_status' => \Glofox\Domain\Leads\Status::LEAD,
                'membership' => [
                    '_id' => '54107c1cd7b6ddc3a98b4577',
                    'type' => 'time_classes',
                    'start_date' => Carbon::today()->subWeek(1)->startOfDay()->toDateString(),
                    'expiry_date' => Carbon::today()->endOfDay()->toDateString(),
                    'plan_code' => '1506335805777',
                    'boooked_events' => 0,
                    'subscription' => [
                        'subscription_plan_id' => '59e63a1773fa6cf751723246',
                        'interval' => 'week',
                        'interval_count' => 1,
                        'price' => 10,
                        'upfront_fee' => 0,
                        'force_start' => false,
                        'end_date' => Carbon::now()->addWeek(1)->toDateString(),
                        'credits' => [
                            [
                                'branch_id' => '49a7011a05c677b9a916612a',
                                'namespace' => 'glofox',
                                'active' => true,
                                'model' => 'programs',
                                'category_id' => null,
                                'num_sessions' => 10,
                                'model_ids' => null,
                                'expiry' => null,
                                'end_date' => null,
                            ],
                        ],
                    ],
                ],
            ],
            [
                '_id' => new MongoId('610005a8f676d06319f9e2b9'),
                'user_id' => '610005a8f676d06319f9e2b9',
                'branch_id' => '49a7011a05c677b9a916612a',
                'origin_branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'type' => UserType::MEMBER,
                'first_name' => 'Consent',
                'last_name' => 'Test',
                'phone' => '11123',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'lead_status' => \Glofox\Domain\Leads\Status::MEMBER,
                'membership' => [
                    'type' => 'payg',
                ],
                'consent' => [
                    'email' => [
                        'active' => true,
                        'modified_at' => new MongoDate(),
                        'modified_by_user_id' => 'test-user-id',
                        'modified_from_ip_address' => '127.0.0.1',
                        'message' => 'test-message',
                    ],
                    'sms' => [
                        'active' => false,
                        'modified_at' => new MongoDate(),
                        'modified_by_user_id' => 'test-user-id',
                        'modified_from_ip_address' => '127.0.0.1',
                        'message' => 'test-message',
                    ],
                ],
            ],
            [
                '_id' => new MongoId('6103f9ac5eed4f6e10679640'),
                'branch_id' => '5addc25383266f65abf515c4',
                'namespace' => 'personaltraining',
                'active' => true,
                'categories' => [],
                'type' => 'TRAINER',
                'first_name' => 'Personal',
                'last_name' => 'Trainer',
                'phone' => '08933221823',
                'description' => 'PT',
                'email' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'bookable' => false,
                'login' => '<EMAIL>',
                'gender' => 'M',
                'birth' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'created' => new MongoDate(),
                'modified' => new MongoDate(),
                'last_login' => new MongoDate(),
            ],
            [
                '_id' => new MongoId('6103fd68e4b1f67168115fc2'),
                'branch_id' => '5addc25383266f65abf515c4',
                'namespace' => 'personaltraining',
                'active' => true,
                'categories' => [],
                'type' => 'TRAINER',
                'first_name' => 'Personal',
                'last_name' => 'Trainer',
                'phone' => '08933221823',
                'description' => 'PT',
                'email' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'bookable' => true,
                'login' => '<EMAIL>',
                'gender' => 'M',
                'birth' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'created' => new MongoDate(),
                'modified' => new MongoDate(),
                'last_login' => new MongoDate(),
            ],
            [
                '_id' => new MongoId('58568b8fa875ab1953123123'),
                'branch_id' => [
                    '5c783d4cd510f9635ad4a6b6',
                    '5a9591bcdb07bce527400717',
                ],
                'namespace' => 'glofox',
                'active' => true,
                'categories' => [],
                'type' => 'SUPERADMIN',
                'first_name' => 'some',
                'last_name' => 'SUPERADMIN',
                'phone' => '0899991823',
                'description' => 'Roams between to glofox branhces',
                'email' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'bookable' => true,
                'login' => '<EMAIL>',
                'created' => new MongoDate(),
                'modified' => new MongoDate(),
                'last_login' => new MongoDate(),
            ],
            [
                '_id' => new MongoId('58568b8fa875ab1953123124'),
                'branch_id' => [
                    '5c783d4cd510f9635ad4a6b6',
                    '5a9591bcdb07bce527400717',
                ],
                'namespace' => 'glofox',
                'active' => true,
                'categories' => [],
                'type' => 'RECEPTION',
                'first_name' => 'some',
                'last_name' => 'RECEPTIONIST',
                'phone' => '0899991823',
                'description' => 'Roams between to glofox branhces',
                'email' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'bookable' => true,
                'login' => '<EMAIL>',
                'created' => new MongoDate(),
                'modified' => new MongoDate(),
                'last_login' => new MongoDate(),
            ],
            [
                '_id' => new MongoId('61234dd4cd3f573330f67e45'),
                'branch_id' => '61234de2be6a046c3186db0d',
                'origin_branch_id' => '61234de2be6a046c3186db0d',
                'namespace' => 'glofox',
                'active' => true,
                'type' => 'MEMBER',
                'first_name' => 'Webhook',
                'last_name' => 'Tester',
                'phone' => '0123456789',
                'description' => 'MailChimp webhook tester guy',
                'email' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'login' => '<EMAIL>',
                'consent' => [
                    'email' => [
                        'active' => true,
                        'modified_at' => new MongoDate(),
                        'modified_by_user_id' => 'test-user-id',
                        'modified_from_ip_address' => '127.0.0.1',
                        'message' => 'test-message',
                    ],
                    'sms' => [
                        'active' => false,
                        'modified_at' => new MongoDate(),
                        'modified_by_user_id' => 'test-user-id',
                        'modified_from_ip_address' => '127.0.0.1',
                        'message' => 'test-message',
                    ],
                ],
                'created' => new MongoDate(),
                'modified' => new MongoDate(),
                'last_login' => new MongoDate(),
            ],
            [
                '_id' => new MongoId('613b13dc0d48037de4c57770'),
                'branch_id' => '61234de2be6a046c3186db0d',
                'origin_branch_id' => '61234de2be6a046c3186db0d',
                'namespace' => 'glofox',
                'active' => true,
                'categories' => [],
                'type' => 'MEMBER',
                'first_name' => 'Consent',
                'last_name' => 'Test',
                'phone' => '123456789',
                'description' => 'Consent tester',
                'email' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'login' => '<EMAIL>',
                'gender' => 'M',
                'birth' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'created' => new MongoDate(),
                'modified' => new MongoDate(),
                'last_login' => new MongoDate(),
            ],
            [
                '_id' => new MongoId('61234dd4cd3f573330f67e46'),
                'branch_id' => '61234de2be6a046c3186db0d',
                'origin_branch_id' => '61234de2be6a046c3186db0d',
                'namespace' => 'glofox',
                'active' => true,
                'type' => 'MEMBER',
                'first_name' => 'Webhook',
                'last_name' => 'Tester',
                'phone' => '0123456789',
                'description' => 'MailChimp webhook tester guy',
                'email' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'login' => '<EMAIL>',
                'consent' => [
                    'email' => [
                        'active' => true,
                        'modified_at' => new MongoDate(),
                        'modified_by_user_id' => 'test-user-id',
                        'modified_from_ip_address' => '127.0.0.1',
                        'message' => 'test-message',
                    ],
                    'sms' => [
                        'active' => true,
                        'modified_at' => new MongoDate(),
                        'modified_by_user_id' => 'test-user-id',
                        'modified_from_ip_address' => '127.0.0.1',
                        'message' => 'test-message',
                    ],
                ],
                'metadata' => [
                    'twilio' => [
                        'phone_number' => '+123123123123',
                    ],
                ],
                'created' => new MongoDate(),
                'modified' => new MongoDate(),
                'last_login' => new MongoDate(),
            ],
            [
                '_id' => new MongoId('615c1fc44248e805a3850a8f'),
                'branch_id' => '6103f87afb7e569994a13036',
                'namespace' => 'testnamespace',
                'active' => true,
                'type' => 'ADMIN',
                'first_name' => 'Client',
                'last_name' => 'Updater',
                'phone' => '0123456789',
                'description' => 'Client update tester admin',
                'email' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'login' => '<EMAIL>',
                'created' => new MongoDate(),
                'modified' => new MongoDate(),
                'last_login' => new MongoDate(),
            ],
            [
                '_id' => new MongoId('61234dd4cd3f573330f67e47'),
                'branch_id' => '6103f87afb7e569994a13036',
                'namespace' => 'glofox',
                'active' => true,
                'type' => 'MEMBER',
                'first_name' => 'Webhook',
                'last_name' => 'Tester',
                'phone' => '0123456789',
                'description' => 'MailChimp webhook tester guy',
                'email' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'login' => '<EMAIL>',
                'consent' => [
                    'email' => [
                        'active' => true,
                        'modified_at' => new MongoDate(),
                        'modified_by_user_id' => 'test-user-id',
                        'modified_from_ip_address' => '127.0.0.1',
                        'message' => 'test-message',
                    ],
                    'sms' => [
                        'active' => true,
                        'modified_at' => new MongoDate(),
                        'modified_by_user_id' => 'test-user-id',
                        'modified_from_ip_address' => '127.0.0.1',
                        'message' => 'test-message',
                    ],
                ],
                'metadata' => [
                    'twilio' => [
                        'phone_number' => '+123123123123',
                    ],
                ],
                'created' => new MongoDate(),
                'modified' => new MongoDate(),
                'last_login' => new MongoDate(),
            ],
            [
                '_id' => new MongoId('61c9af765978a5b354bfb0b6'),
                'branch_id' => '49a7011a05c677b9a916612b',
                'namespace' => 'test',
                'active' => false,
                'type' => 'MEMBER',
                'first_name' => 'Inactive',
                'last_name' => 'Member',
                'phone' => '0123456789',
                'description' => 'I am deactivated',
                'email' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'login' => '<EMAIL>',
                'created' => new MongoDate(),
                'modified' => new MongoDate(),
                'last_login' => new MongoDate(),
            ],
            $this->createPaygMember([
                '_id' => new MongoId('61f921834b4fc528013ef1e0'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'origin_branch_id' => '49a7011a05c677b9a916612a',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'card_id' => 'gantern-card-id',
            ]),
            $this->createBase([
                '_id' => '6243236a107bebc0a065fc22',
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'origin_branch_id' => '49a7011a05c677b9a916612a',
                'membership' => [
                    'type' => 'time',
                ],
            ]),
            $this->createBase([
                '_id' => '6268fbe2c301457c212709f9',
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'membership' => [
                    'type' => 'num_classes',
                    '_id' => '5b8c5bc05e195e316e074ff4',
                    'start_date' => date('Y-m-d', strtotime('-7 days')),
                    'expiry_date' => date('Y-m-d', strtotime('+1 day')),
                    'plan_code' => '1529406154886',
                    'boooked_events' => 0,
                ],
            ]),
            $this->createBase([
                '_id' => '627916beef2a0f58b1defddb',
                'branch_id' => '6279159a1d7cc351e50674bb',
                'namespace' => 'midnightb',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'type' => 'ADMIN',
                'active' => true,
            ]),
            $this->createBase([
                '_id' => '62bd96eb69d55476cf8fa051',
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'password' => password_hash(
                    'password123',
                    PASSWORD_ARGON2I,
                    ['cost' => env('PASSWORD_HASH_COST') ?: 10]
                ),
            ]),
            $this->createBase([
                '_id' => '62d8f0a7f592084274a9868e',
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'password' => password_hash(
                    'password123',
                    PASSWORD_ARGON2I,
                    ['cost' => env('PASSWORD_HASH_COST') ?: 10]
                ),
                'type' => 'TRAINER',
                'first_name' => 'Trainer',
                'last_name' => 'Cannot be deleted',
            ]),
            $this->createPaygWithCreditPackageMember([
                '_id' => '59a7010a05c677b3a916612a',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
            ]),
            ...$this->addUserForCi1808UseCase(),
            ...$this->addUserForCi1263UseCase(),
            ...$this->withPreDefinedJoinedAt(),
        ];

        $this->records += array_merge(
            $this->records,
            $this->leads(),
            $this->usersWithAbilities(),
            $this->familyAccountsUsers(),
            $this->usersWithContracts(),
            $this->mindbodyUsers(),
            $this->goCardlessImporterUsers(),
            $this->forPaymentIntent(),
            $this->forRecordMissingTransactionsScript(),
            $this->forUserMembershipImportShell(),
            $this->forCsvTransactionReport(),
            $this->forFutureMemberships(),
            $this->forPausingSubscriptionsOnRemovedPlans(),
            $this->forIntegrators(),
            $this->forVirtualIsOnlineFieldOnEvents(),
            $this->forStripeDD(),
            $this->forSaveBatchCreditsTransformer(),
            $this->forGetAllUsers(),
            $this->forPushNotificationsControllerTests(),
            $this->forRecurringReservationBookingsTests(),
            $this->forSCATests(),
            $this->forLeadsFilterTest(),
            $this->forTerminalPOS(),
            $this->forNicepayProvider(),
            $this->forUserCreditsForBookingAppointmentSlot(),
            $this->forBookingsMetadata(),
            $this->forAccessesReport(),
            $this->forFilterBookingsTest(),
            $this->forUnBookableTrainersFilter(),
            $this->forMinimumAgeTest(),
            $this->forStaffTypeChangeTest(),
            $this->forListAllBookingsActionTest(),
            $this->forVirtualSlotsTest(),
            $this->forBookingWindow(),
            $this->forVirtualAppointmentTrainerDelete(),
            $this->forPayroll(),
            $this->forAccessesList(),
            $this->forSendGroupMessage(),
            $this->forUserRestore(),
            $this->forUserDataUpdate(),
            $this->forAnalyticsReports(),
            $this->forExecuteBookingCharge(),
            $this->forRecurringClasses(),
            $this->forValidNumberOfChildren(),
            $this->forNamespaceUsersSearch(),
            $this->forHasAccessToBranch(),
            $this->forInteractions(),
            $this->forEventBatchId(),
            $this->forBranchesUpsert(),
            $this->forActiveLimitBookings(),
            $this->forGympassPaygDisabled(),
            $this->forLeadStatusUpdateCapture(),
            $this->forLeadStatusUpdateCaptureTrial(),
            $this->forEventBookingsTotal(),
            $this->forEventDeletedEventHandle(),
            $this->forCreditConsumptionForIntegrators(),
            $this->forRestrictedMembershipNoCreditsNextCycle(),
            $this->forLeadsWithMarketingAndContactResources(),
            $this->forGrantCreditsOnImport(),
            $this->forUserWithBranchThatHaveCorporateId(),
            $this->forSemiPrivateAppointments(),
            $this->forBranchesWithLiftCorpId(),
            $this->forUsersAccessBarcodes(),
            $this->forBranchesWithoutLiftCorpId(),
            $this->forMembershipActivatedAssignsCreditsForRestrictedMemberships(),
            $this->forAssignCreditsOnMembershipPurchasedEvent(),
            $this->forMemberRestrictedProfileRules(),
            $this->forNoSaleTrialStatus(),
            $this->forRemoveSubscriptionOnMembershipUpdate(),
            $this->forShouldAllowCancelSubscriptionForFutureMembership(),
            $this->forEventsUpdate(),
            $this->forGetAllBookings(),
            $this->forLeadSources(),
            $this->forTourStatus(),
            $this->forNoSaleTourStatus(),
            $this->forGetMembers(),
            $this->forResetDOFBCredits(),
            $this->forWaitingListEmailOnRoaming(),
            $this->forCreditsHealing(),
            $this->forRestrictedMembershipRenewal(),
            $this->forOverdueMembershipsBookings(),
            $this->forCreditsDuplicatesOnRenewal(),
            $this->forUserCreditsHistory(),
            $this->forUserCreditsPermanentDeletion(),
            $this->forValidationSupportedCategoryOrClasses(),
            $this->forSubscriptionRenewalCreditSourceUpdater(),
            $this->forCancelBookingsPausedMembership(),
            $this->forAdvancedCycleCreditsWithDifferentThanMembershipDuration(),
            $this->forFacilitiesControllerList(),
            $this->forAdvancedCycleCreditsWithOneMonthDuration(),
        );

        parent::init();
    }

    /**
     * It creates a Payg member.
     *
     * @param array $data [description]
     *
     * @return [type] [description]
     */
    public function createPaygMember($data = [])
    {
        $data['membership'] = ['type' => 'payg'];
        $data['type'] = 'MEMBER';

        return $this->createBase($data);
    }

    public function createMemberWithSubscriptionCredits(
        $id,
        $email = '<EMAIL>',
        $subId = 'sub_BSpsTBDgn8Rqs7',
        MongoDate $startDate = null,
        $userMembershipId = null
    ) {
        date_default_timezone_set('UTC');

        if (!$startDate) {
            $startDate = new MongoDate();
        }

        $expiryDate = new MongoDate(
            $startDate->toDateTime()->modify('+1 week')->getTimestamp()
        );

        $data = $this->createBase([
            '_id' => $id,
            'first_name' => 'Testing Subscriptions',
            'last_name' => 'with credits',
            'login' => $email,
            'email' => $email,
            'password' => '$argon2i$v=19$m=1024,t=2,p=2$VDkza2c4aU92VldaRnZraw$hERW8grxf9NI6X1GA0b7HYg14UFoR4whTfDdFkxTWh4',
            'membership' => [
                '_id' => '54107c1cd7b6ddc3a98b4577',
                'type' => 'time_classes',
                'start_date' => $startDate,
                'membership_group_id' => null,
                'plan_code' => '1506335805020',
                'plan_price' => 10,
                'boooked_events' => 0,
                'expiry_date' => $expiryDate,
                'user_membership_id' => $userMembershipId,
                'subscription' => [
                    'subscription_plan_id' => '59c8dc7c3ebaf48d6b0041a9',
                    'interval' => 'week',
                    'interval_count' => 1,
                    'price' => 10,
                    'upfront_fee' => 0,
                    'force_start' => false,
                    'credits' => [
                        [
                            'branch_id' => '49a7011a05c677b9a916612a',
                            'namespace' => 'glofox',
                            'active' => true,
                            'model' => 'programs',
                            'category_id' => null,
                            'num_sessions' => 10,
                            'model_ids' => null,
                            'expiry' => [
                                'interval' => 'month',
                                'interval_count' => 1,
                            ],
                            'end_date' => null,
                        ],
                    ],
                    'stripe_id' => $subId,
                ],
            ],
        ]);

        return $data;
    }

    public function createMemberWithValidSubscriptionTimeMembership(
        $id,
        $email = '<EMAIL>',
        $custom = []
    ): array {
        $membership = $this->createMembership($custom['membership'] ?? []);
        $data = $this->createBase(
            array_merge_recursive(
                [
                    '_id' => $id,
                    'login' => $email,
                    'email' => $email,
                    'password' => '$argon2i$v=19$m=1024,t=2,p=2$VDkza2c4aU92VldaRnZraw$hERW8grxf9NI6X1GA0b7HYg14UFoR4whTfDdFkxTWh4',
                    'stripe_customer_id' => 'cus_BSpssOoZZ8Wu0o',
                ],
                $custom
            )
        );

        $data['membership'] = $membership;
        $data['membership']['subscription']['stripe_id'] ??= 'sub_BSpsTBDgn8Rqs8';

        return $data;
    }

    public function createMembership(
        $custom = []
    ): array {
        return array_replace_recursive(
            [
                '_id' => '54107c1cd7b6ddc3a98b4577',
                'type' => 'time',
                'start_date' => new MongoDate(),
                'plan_code' => '1506335805020',
                'boooked_events' => 0,
                'expiry_date' => new MongoDate(strtotime('+1 week')),
                'subscription' => [
                    'subscription_plan_id' => '59e63a1773fa6cf751723246',
                    'interval' => 'month',
                    'interval_count' => 1,
                    'price' => 10,
                    'upfront_fee' => 0,
                    'force_start' => false,
                ],
            ],
            $custom
        );
    }

    public function createPaygWithCreditPackageMember($data = [])
    {
        $data['membership'] = ['type' => 'num_classes'];
        $data['type'] = 'MEMBER';

        return $this->createBase($data);
    }

    private function createBaseWithoutPassword(array $data): array
    {
        $data = $this->createBase($data);

        unset($data['password']);

        return $data;
    }

    /**
     * This method create a basic user, having defaults for most fields in order
     * to call it with the least amount of fields set, just the ones that are relevant
     * to our test.
     *
     * @param [type] $data [description]
     *
     * @return [type] [description]
     */
    public function createBase($data)
    {
        $branchId = $data['branch_id'] ?? '49a7011a05c677b9a916612a';

        $originBranchId = $data['origin_branch_id'] ?? $branchId;
        if (is_array($originBranchId)) {
            $originBranchId = $originBranchId[0];
        }

        $type = $data['type'] ?? 'MEMBER';

        return [
            '_id' => isset($data['_id']) ? new MongoId($data['_id']) : new MongoId(),
            'branch_id' => $branchId,
            'origin_branch_id' => ($type === 'MEMBER') ? $originBranchId : null,
            'namespace' => $data['namespace'] ?? 'glofox',
            'active' => $data['active'] ?? true,
            'categories' => $data['categories'] ?? [],
            'type' => $type,
            'name' => $data['name'] ?? 'Timmy Fisher',
            'first_name' => $data['first_name'] ?? 'Timmy',
            'last_name' => $data['last_name'] ?? 'Fisher',
            'phone' => $data['phone'] ?? '0899991827',
            'description' => $data['description'] ?? 'Worst Trainer Ever!',
            'email' => $data['email'] ?? '<EMAIL>',
            'password' => $data['password'] ?? '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
            'bookable' => $data['bookable'] ?? false,
            'login' => $data['login'] ?? '<EMAIL>',
            'birth' => isset($data['birth']) ? new MongoDate(strtotime($data['birth'])) : new MongoDate(),
            'twitter' => $data['twitter'] ?? '@timmy',
            'facebook' => $data['facebook'] ?? 'https://facebook.com/timmy',
            'stripe_token' => $data['stripe_token'] ?? null,
            'card_id' => $data['card_id'] ?? null,
            'stripe_customer_id' => $data['stripe_customer_id'] ?? null,
            'stripe_cc_token' => $data['stripe_cc_token'] ?? null,
            'auto_generated' => $data['auto_generated'] ?? false,
            'access_barcode' => $data['access_barcode'] ??  $this->generateRandomAccessBarcode(),
            'created' => $data['created'] ?? new MongoDate(),
            'modified' => $data['modified'] ?? new MongoDate(),
            'last_login' => $data['last_login'] ?? new MongoDate(),
            'strike' => $data['strike'] ?? 0,
            'private_desc' => $data['private_desc'] ?? 'Why so secret?',
            'membership' => $data['membership'] ?? null,
            'device' => $data['device'] ?? ['os' => 'android'],
            '_pattern' => $data['_pattern'] ?? null,
            'WAIVER' => $data['WAIVER'] ?? true,
            'MEMBERPURCHASE' => $data['MEMBERPURCHASE'] ?? true,
            'PAYGPAYMENT' => $data['PAYGPAYMENT'] ?? true,
            'transaction' => $data['transaction'] ?? null,
            'emergency_contact' => $data['emergency_contact'] ?? '<EMAIL>',
            'pwd_token' => $data['pwd_token'] ?? null,
            'version' => $data['version'] ?? ['minor' => 9, 'major' => 9, 'revision' => 25],
            'metadata' => $data['metadata'] ?? [],
            'receive_marketing' => $data['receive_marketing'] ?? false,
            'answers' => $data['answers'] ?? [],
            'consent' => $data['consent'] ?? [
                    'email' => [
                        'active' => true,
                        'modified_at' => new MongoDate(),
                        'modified_by_user_id' => 'test-user-id',
                        'modified_from_ip_address' => '127.0.0.1',
                        'message' => 'test-message',
                    ],
                    'sms' => [
                        'active' => false,
                        'modified_at' => new MongoDate(),
                        'modified_by_user_id' => 'test-user-id',
                        'modified_from_ip_address' => '127.0.0.1',
                        'message' => 'test-message',
                    ],
                ],
            'lead_status' => $data['lead_status'] ?? 'MEMBER',
            'source' => $data['source'] ?? 'default-source',
        ];
    }
    private function generateRandomAccessBarcode()
    {
        $access_barcode = '';
        for ($i = 0; $i < 10; $i++) {
            $access_barcode .= random_int(0, 9);
        }
        return $access_barcode;
    }
    public function leads()
    {
        return [
            [
                '_id' => new MongoId('59a7011a05c677bda916623d'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'origin_branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'type' => UserType::MEMBER,
                'first_name' => 'Warm',
                'last_name' => 'Lead',
                'phone' => '11123',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'lead_status' => \Glofox\Domain\Leads\Status::WARM,
                'membership' => [
                    'type' => 'payg',
                ],
            ],
            [
                '_id' => new MongoId('59a7011a05c677bda916623e'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'origin_branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'type' => UserType::MEMBER,
                'first_name' => 'Cold',
                'last_name' => 'Lead',
                'phone' => '11124',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'lead_status' => \Glofox\Domain\Leads\Status::COLD,
                'membership' => [
                    'type' => 'payg',
                ],
            ],
            [
                '_id' => new MongoId('59a7011a05c677bda916623f'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'origin_branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'type' => UserType::MEMBER,
                'first_name' => 'Untouched',
                'last_name' => 'Lead',
                'phone' => '11125',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'lead_status' => \Glofox\Domain\Leads\Status::UNTOUCHED,
                'membership' => [
                    'type' => 'payg',
                ],
            ],
            [
                '_id' => new MongoId('59a7011a05c677bda916624a'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'origin_branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'type' => UserType::MEMBER,
                'first_name' => 'Trial',
                'last_name' => 'Lead',
                'phone' => '11126',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'lead_status' => \Glofox\Domain\Leads\Status::TRIAL,
                'membership' => [
                    'type' => 'member',
                    'expiry_date' => new \MongoDate(Carbon::now()->modify('+7 days')->getTimestamp()),
                ],
            ],
            [
                '_id' => new MongoId('59a7011a05c677bda916624b'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'origin_branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'type' => UserType::MEMBER,
                'first_name' => 'Untouched2',
                'last_name' => 'Lead',
                'phone' => '11127',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'lead_status' => \Glofox\Domain\Leads\Status::UNTOUCHED,
                'membership' => [
                    'type' => 'payg',
                ],
            ],
            [
                '_id' => new MongoId('59a7011a05c677bda916624c'),
                'branch_id' => '49a7011a05c677b9a916612b',
                'origin_branch_id' => '49a7011a05c677b9a916612b',
                'namespace' => 'glofox',
                'active' => true,
                'type' => UserType::MEMBER,
                'first_name' => 'jidioadjiasd',
                'last_name' => 'ncasjklda',
                'phone' => '11128',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'lead_status' => \Glofox\Domain\Leads\Status::LEAD,
                'membership' => [
                    'type' => 'payg',
                ],
            ],
            [
                '_id' => new MongoId('59a7011a05c677bda916625a'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'origin_branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'type' => UserType::MEMBER,
                'first_name' => 'Trial Expired',
                'last_name' => 'Lead',
                'phone' => '11129',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'lead_status' => \Glofox\Domain\Leads\Status::TRIAL,
                'membership' => [
                    '_id' => '56ceca057cad3653598b4582',
                    'type' => 'time',
                    'start_date' => new \MongoDate(Carbon::now()->modify('-7 days')->getTimestamp()),
                    'plan_code' => '56ceca59dccde',
                    'boooked_events' => 0,
                    'expiry_date' => new \MongoDate(Carbon::now()->modify('-2 days')->getTimestamp()),
                ],
            ],
            [
                '_id' => new MongoId('59a7011a05c677bda916123a'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'origin_branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'type' => UserType::MEMBER,
                'first_name' => 'To be reverted to PAYG',
                'last_name' => 'Lead',
                'phone' => '11129',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'membership' => [
                    '_id' => '56ceca057cad3653598b4582',
                    'type' => 'time',
                    'start_date' => new \MongoDate(Carbon::now()->modify('-7 days')->getTimestamp()),
                    'plan_code' => '56ceca59dccde',
                    'boooked_events' => 0,
                    'expiry_date' => new \MongoDate(Carbon::now()->modify('-2 days')->getTimestamp()),
                ],
            ],
            [
                '_id' => new MongoId('59a7011a05c677bda916626d'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'origin_branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => false,
                'type' => UserType::MEMBER,
                'first_name' => 'Warm',
                'last_name' => 'Lead Not active',
                'phone' => '11123',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'lead_status' => \Glofox\Domain\Leads\Status::WARM,
                'membership' => [
                    'type' => 'payg',
                ],
            ],
            [
                '_id' => new MongoId('59a7011a05c677bda916627e'),
                'branch_id' => '49a7011a05c677b9a916615b',
                'origin_branch_id' => '49a7011a05c677b9a916615b',
                'namespace' => 'someothernamespace',
                'active' => false,
                'type' => UserType::MEMBER,
                'first_name' => 'Untouched',
                'last_name' => 'Lead to be converted to warm',
                'phone' => '11123',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'lead_status' => \Glofox\Domain\Leads\Status::UNTOUCHED,
                'membership' => [
                    'type' => 'payg',
                ],
            ],
            [
                '_id' => new MongoId('59a7011a05c677bda916627f'),
                'branch_id' => '49a7011a05c677b9a916615b',
                'origin_branch_id' => '49a7011a05c677b9a916615b',
                'namespace' => 'someothernamespace',
                'active' => false,
                'type' => UserType::MEMBER,
                'first_name' => 'Untouched',
                'last_name' => 'Lead to be converted to warm2',
                'phone' => '11123',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'lead_status' => \Glofox\Domain\Leads\Status::UNTOUCHED,
                'membership' => [
                    'type' => 'payg',
                ],
            ],
            [
                '_id' => new MongoId('59a7011a05c677bda916621f'),
                'user_id' => '59a7011a05c677bda916621f',
                'branch_id' => '49a7011a05c677b9a916612a',
                'origin_branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => false,
                'type' => UserType::MEMBER,
                'first_name' => 'Untouched',
                'last_name' => 'To test min price purchase',
                'phone' => '11123',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'lead_status' => \Glofox\Domain\Leads\Status::UNTOUCHED,
                'membership' => [
                    'type' => 'payg',
                ],
            ],
        ];
    }

    public function usersWithContracts(): array
    {
        $records = [];

        $records[] = $this->createBase([
            '_id' => '5a9591bcdb07bce523400718',
            'branch_id' => '5a9591bcdb07bce527400717',
            'namespace' => 'glofox',
            'email' => 'user-with-contract+' . uniqid('', false) . '@glofox.com',
            'login' => 'user-with-contract+' . uniqid('', false) . '@glofox.com',
            'membership' => [
                '_id' => '5b19486ac13acae3b31cf563',
                'type' => 'time',
                'plan_code' => '1506335305324',
                'start_date' => Carbon::now()->subDay(7)->toDateString(),
                'expiry_date' => Carbon::now()->subDay()->toDateString(),
                'subscription' => [
                    'subscription_plan_id' => '59c8dc7c3ebaf48d6b0041a9',
                    'interval' => 'month',
                    'interval_count' => 1,
                    'price' => 20,
                    'upfront_fee' => 0,
                    'force_start' => false,
                    'stripe_id' => 'sub_fakeStripeId',
                    'end_date' => Carbon::now()->subDay()->toDateString(),
                ],
            ],
        ]);

        $records[] = $this->createBase([
            '_id' => '5a9591bcdb07bce523400715',
            'branch_id' => '5a9591bcdb07bce527400717',
            'namespace' => 'glofox',
            'email' => 'user-with-contract+' . uniqid('', false) . '@glofox.com',
            'login' => 'user-with-contract+' . uniqid('', false) . '@glofox.com',
            'membership' => [
                '_id' => '5b19486ac13acae3b31cf563',
                'type' => 'time',
                'plan_code' => '1506335305324',
                'start_date' => Carbon::now()->subDay(7)->toDateString(),
                'expiry_date' => Carbon::now()->toDateString(),
                'subscription' => [
                    'subscription_plan_id' => '59c8dc7c3ebaf48d6b0041a9',
                    'interval' => 'month',
                    'interval_count' => 1,
                    'price' => 20,
                    'upfront_fee' => 0,
                    'force_start' => false,
                    'stripe_id' => 'sub_fakeStripeId',
                    'end_date' => Carbon::now()->toDateString(),
                ],
            ],
        ]);

        return $records;
    }

    public function goCardlessImporterUsers(): array
    {
        return [
            [
                '_id' => new MongoId('a9a3321a05c677bda917711c'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'type' => 'MEMBER',
                'first_name' => 'Test 2',
                'last_name' => 'Member 2',
                'phone' => '0899991827',
                'email' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'login' => '<EMAIL>',
                'gender' => 'M',
                'birth' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'membership' => [
                    '_id' => '56ceca057cad3653598b4582',
                    'type' => 'time',
                    'start_date' => date('Y-m-d', strtotime('-7 day')),
                    'plan_code' => '56ceca59dccde',
                    'boooked_events' => 0,
                    'expiry_date' => date('Y-m-d', strtotime('-1 day')),
                ],
                'WAIVER' => true,
                'MEMBERPURCHASE' => true,
                'PAYGPAYMENT' => true,
                'transaction' => null,
            ],
            [
                '_id' => new MongoId('a9a3321a05c677bda917722c'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'type' => 'MEMBER',
                'first_name' => 'Test 2',
                'last_name' => 'Member 2',
                'phone' => '0899991827',
                'email' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'login' => '<EMAIL>',
                'gender' => 'M',
                'birth' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'membership' => [
                    '_id' => '56ceca057cad3653598b4582',
                    'type' => 'time',
                    'start_date' => date('Y-m-d', strtotime('-7 day')),
                    'plan_code' => '56ceca59dccde',
                    'boooked_events' => 0,
                    'expiry_date' => date('Y-m-d', strtotime('-1 day')),
                ],
                'WAIVER' => true,
                'MEMBERPURCHASE' => true,
                'PAYGPAYMENT' => true,
                'transaction' => null,
            ],
            [
                '_id' => new MongoId('a9a3321a05c677bda917733c'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'type' => 'MEMBER',
                'first_name' => 'Test 2',
                'last_name' => 'Member 2',
                'phone' => '0899991827',
                'email' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'login' => '<EMAIL>',
                'gender' => 'M',
                'birth' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'membership' => [
                    '_id' => '56ceca057cad3653598b4582',
                    'type' => 'time',
                    'start_date' => date('Y-m-d', strtotime('-7 day')),
                    'plan_code' => '56ceca59dccde',
                    'boooked_events' => 0,
                    'expiry_date' => date('Y-m-d', strtotime('-1 day')),
                ],
                'WAIVER' => true,
                'MEMBERPURCHASE' => true,
                'PAYGPAYMENT' => true,
                'transaction' => null,
            ],
            [
                '_id' => new MongoId('a9a3321a05c699bda917733a'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'type' => 'MEMBER',
                'first_name' => 'Test 2',
                'last_name' => 'Member 2',
                'phone' => '0899991827',
                'email' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'login' => '<EMAIL>',
                'gender' => 'M',
                'birth' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'membership' => [
                    '_id' => '56ceca057cad3653598b4582',
                    'type' => 'time',
                    'start_date' => date('Y-m-d', strtotime('-7 day')),
                    'plan_code' => '56ceca59dccde',
                    'boooked_events' => 0,
                    'expiry_date' => date('Y-m-d', strtotime('-1 day')),
                ],
                'WAIVER' => true,
                'MEMBERPURCHASE' => true,
                'PAYGPAYMENT' => true,
                'transaction' => null,
            ],
        ];
    }

    public function forUserMembershipImportShell(): array
    {
        return [
            [
                '_id' => new MongoId('a9a5521a05c687bda917788c'),
                'user_id' => 'a9a5521a05c687bda917788c',
                'branch_id' => '49a7011a05c677b9a916612a',
                'origin_branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'type' => 'MEMBER',
                'first_name' => 'Test 2',
                'last_name' => 'Member 2',
                'phone' => '0899991827',
                'email' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'login' => '<EMAIL>',
                'gender' => 'M',
                'birth' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'membership' => ['type' => 'payg'],
                'WAIVER' => true,
                'MEMBERPURCHASE' => true,
                'PAYGPAYMENT' => true,
                'transaction' => null,
            ],
            [
                '_id' => new MongoId('a9a5521a05c687bda917722c'),
                'user_id' => 'a9a5521a05c687bda917722c',
                'branch_id' => '49a7011a05c677b9a916612a',
                'origin_branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'type' => 'MEMBER',
                'first_name' => 'Test 2',
                'last_name' => 'Member 2',
                'phone' => '0899991827',
                'email' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'login' => '<EMAIL>',
                'gender' => 'M',
                'birth' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'membership' => ['type' => 'payg'],
                'WAIVER' => true,
                'MEMBERPURCHASE' => true,
                'PAYGPAYMENT' => true,
                'transaction' => null,
            ],
        ];
    }

    private function familyAccountsUsers(): array
    {
        return [
            [
                '_id' => new MongoId('5c8b0615deb2eae76ec28673'),
                'branch_id' => '5c783d4cd510f9635ad4a6b3',
                'origin_branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'someothernamespace',
                'active' => true,
                'type' => UserType::MEMBER,
                'first_name' => 'Robert',
                'last_name' => 'Dad',
                'phone' => '11123',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'lead_status' => \Glofox\Domain\Leads\Status::UNTOUCHED,
                'membership' => [
                    'type' => 'payg',
                ],
                'abilities' => [
                    \Glofox\Domain\Authorization\Abilities\UserAbility::PARENT_LOGIN_AS_A_CHILD,
                ],
            ],
            [
                '_id' => new MongoId('5c8b0615deb2eae76ec28123'),
                'branch_id' => '5c783d4cd510f9635ad4a6b3',
                'origin_branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'someothernamespace',
                'active' => true,
                'type' => UserType::MEMBER,
                'first_name' => 'Robert',
                'last_name' => 'Dad',
                'phone' => '11123',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'lead_status' => \Glofox\Domain\Leads\Status::UNTOUCHED,
                'membership' => [
                    'type' => 'payg',
                ],
                'abilities' => [
                    \Glofox\Domain\Authorization\Abilities\UserAbility::PARENT_LOGIN_AS_A_CHILD,
                ],
            ],
            [
                '_id' => new MongoId('5c8b0642deb2eae76ec28674'),
                'branch_id' => '5c783d4cd510f9635ad4a6b3',
                'origin_branch_id' => '5c783d4cd510f9635ad4a6b3',
                'namespace' => 'someothernamespace',
                'active' => true,
                'type' => UserType::MEMBER,
                'first_name' => 'Jane',
                'last_name' => 'Daughter',
                'phone' => '',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'lead_status' => \Glofox\Domain\Leads\Status::UNTOUCHED,
                'gender' => [
                    'name' => 'FEMALE',
                    'label' => 'F',
                ],
                'parent_id' => '5c8b0615deb2eae76ec28673',
                'membership' => [
                    'type' => 'payg',
                ],
                'use_parent_card' => false,
            ],
            [
                '_id' => new MongoId('5c8b0642deb2eae76ec28675'),
                'branch_id' => '5c783d4cd510f9635ad4a6b3',
                'origin_branch_id' => '5c783d4cd510f9635ad4a6b3',
                'namespace' => 'glofox',
                'active' => true,
                'type' => UserType::MEMBER,
                'first_name' => 'Anne',
                'last_name' => 'Daughter',
                'phone' => '',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'lead_status' => \Glofox\Domain\Leads\Status::UNTOUCHED,
                'gender' => [
                    'name' => 'FEMALE',
                    'label' => 'F',
                ],
                'parent_id' => '5c8b0615deb2eae76ec28673',
                'membership' => [
                    'type' => 'payg',
                ],
                'avatar' => 'fakeAvatarUrl',
            ],
            [
                '_id' => new MongoId('5c8b0642deb2eae76ec28676'),
                'branch_id' => '5c783d4cd510f9635ad4a6b3',
                'origin_branch_id' => '5c783d4cd510f9635ad4a6b3',
                'namespace' => 'someothernamespace',
                'active' => false,
                'type' => UserType::MEMBER,
                'first_name' => 'Maria',
                'last_name' => 'Daughter',
                'phone' => '',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'lead_status' => \Glofox\Domain\Leads\Status::UNTOUCHED,
                'gender' => [
                    'name' => 'FEMALE',
                    'label' => 'F',
                ],
                'parent_id' => '5c8b0615deb2eae76ec28673',
                'membership' => [
                    'type' => 'payg',
                ],
            ],
            [
                '_id' => new MongoId('63a05e70bea26934651fe6c1'),
                'branch_id' => '5c783d4cd510f9635ad4a6b3',
                'origin_branch_id' => '5c783d4cd510f9635ad4a6b3',
                'namespace' => 'glofox',
                'active' => false,
                'type' => UserType::MEMBER,
                'first_name' => 'Maria',
                'last_name' => 'Daughter',
                'phone' => '+675845555',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'lead_status' => \Glofox\Domain\Leads\Status::UNTOUCHED,
                'gender' => [
                    'name' => 'FEMALE',
                    'label' => 'F',
                ],
                'parent_id' => '5c8b0615deb2eae76ec28673',
                'membership' => [
                    'type' => 'payg',
                ],
                'use_parent_email' => false,
                'use_parent_phone' => false,
            ],
        ];
    }

    private function forGetAllUsers(): array
    {
        return [
            [
                '_id' => new MongoId('5c79e58d7fb1afd6e2c806f6'),
                'branch_id' => '5c783d4cd510f9635ad4a6b3',
                'namespace' => 'get_all_test',
                'active' => true,
                'type' => UserType::ADMIN,
                'first_name' => 'Admin',
                'last_name' => 'Who is allowed to get users',
                'phone' => \Illuminate\Support\Str::random(),
                'email' => $email = \Illuminate\Support\Str::random() . '@glofox.com',
                'login' => $email,
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$ZThWbjF4bXljcjlRdGZQUQ$POBM2GtX7QecEKu95F+Skmfe1NzHgrxKwnsPX+FDeig',
                'abilities' => [],
                'origin_branch_id' => '6364be7488385df39f3a70b0'
            ],
            [
                '_id' => new MongoId('5c79e58d7fb1afd6e2c806f1'),
                'branch_id' => '5c783d4cd510f9635ad4a6b1',
                'namespace' => 'get_none',
                'active' => true,
                'type' => UserType::ADMIN,
                'first_name' => 'Admin from another branch',
                'last_name' => 'Who is NOT allowed to get users',
                'phone' => \Illuminate\Support\Str::random(),
                'email' => $email = \Illuminate\Support\Str::random() . '@glofox.com',
                'login' => $email,
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$ZThWbjF4bXljcjlRdGZQUQ$POBM2GtX7QecEKu95F+Skmfe1NzHgrxKwnsPX+FDeig',
                'abilities' => [],
            ],
            [
                '_id' => new MongoId('5c8b0615deb1eae76ec28123'),
                'branch_id' => '5c783d4cd510f9635ad4a6b3',
                'origin_branch_id' => '5c783d4cd510f9635ad4a6b3',
                'namespace' => 'get_all_test',
                'active' => true,
                'type' => UserType::MEMBER,
                'first_name' => 'Robert',
                'last_name' => 'Doe',
                'phone' => '11123',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'membership' => [
                    'type' => 'payg',
                ],
            ],
            [
                '_id' => new MongoId('5c8b0642deb1eae76ec28674'),
                'branch_id' => '5c783d4cd510f9635ad4a6b3',
                'origin_branch_id' => '5c783d4cd510f9635ad4a6b3',
                'namespace' => 'get_all_test',
                'active' => true,
                'type' => UserType::MEMBER,
                'first_name' => 'Jane',
                'last_name' => 'Doe',
                'phone' => '',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'gender' => [
                    'name' => 'FEMALE',
                    'label' => 'F',
                ],
                'membership' => [
                    'type' => 'payg',
                ],
            ],
        ];
    }

    private function usersWithAbilities(): array
    {
        return [
            [
                '_id' => new MongoId('5c7828a4d510f9635ad4a6b1'),
                'branch_id' => '5c783d4cd510f9635ad4a6b3',
                'namespace' => 'glofox',
                'active' => true,
                'type' => UserType::MEMBER,
                'first_name' => 'John',
                'last_name' => 'Member',
                'phone' => \Illuminate\Support\Str::random(),
                'email' => $email = \Illuminate\Support\Str::random() . '@glofox.com',
                'login' => $email,
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$ZThWbjF4bXljcjlRdGZQUQ$POBM2GtX7QecEKu95F+Skmfe1NzHgrxKwnsPX+FDeig',
                'lead_status' => \Glofox\Domain\Leads\Status::WARM,
                'abilities' => [
                    \Glofox\Domain\Authorization\Abilities\BranchAbility::LIST_BY_NAMESPACE,
                    \Glofox\Domain\Authorization\Abilities\BranchAbility::UPDATE,
                ],
                'membership' => [
                    'type' => 'payg',
                ],
            ],
            [
                '_id' => new MongoId('5c79e58d7fb4afd6e2c806f6'),
                'branch_id' => '5c783d4cd510f9635ad4a6b3',
                'namespace' => 'glofox',
                'active' => true,
                'type' => UserType::ADMIN,
                'first_name' => 'John',
                'last_name' => 'Admin',
                'phone' => \Illuminate\Support\Str::random(),
                'email' => $email = \Illuminate\Support\Str::random() . '@glofox.com',
                'login' => $email,
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$ZThWbjF4bXljcjlRdGZQUQ$POBM2GtX7QecEKu95F+Skmfe1NzHgrxKwnsPX+FDeig',
                'lead_status' => \Glofox\Domain\Leads\Status::WARM,
                'abilities' => [],
                'membership' => [
                    'type' => 'payg',
                ],
            ],
            [
                '_id' => new MongoId('5c79e58d7fb4afd6e2c806f9'),
                'branch_id' => '5c783d4cd510f9635ad4a6b3',
                'namespace' => 'glofox',
                'active' => true,
                'type' => 'FOO',
                'first_name' => 'John',
                'last_name' => 'Missing',
                'phone' => \Illuminate\Support\Str::random(),
                'email' => $email = \Illuminate\Support\Str::random() . '@glofox.com',
                'login' => $email,
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$ZThWbjF4bXljcjlRdGZQUQ$POBM2GtX7QecEKu95F+Skmfe1NzHgrxKwnsPX+FDeig',
                'lead_status' => \Glofox\Domain\Leads\Status::WARM,
                'abilities' => [],
                'membership' => [
                    'type' => 'payg',
                ],
            ],

            [
                '_id' => new MongoId('59a7011a05c677bda916616c'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'categories' => [],
                'type' => 'ADMIN',
                'name' => 'Admin Istrator',
                'first_name' => 'Admin',
                'last_name' => 'Istrator',
                'phone' => '0899991827',
                'description' => 'Worst Trainer Ever!',
                'email' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'bookable' => true,
                'login' => '<EMAIL>',
                'experience' => 'A lot...',
                'gender' => 'M',
                'birth' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'twitter' => '@timmy',
                'facebook' => 'https://facebook.com/timmy',
                'phone' => '123123321',
                'bookable' => false,
                'stripe_token' => null,
                'answers' => [],
                'card_id' => null,
                'stripe_customer_id' => null,
                'stripe_cc_token' => null,
                'auto_generated' => false,
                'access_barcode' => '123123321',
                'created' => new MongoDate(),
                'modified' => new MongoDate(),
                'last_login' => new MongoDate(),
                'strike' => 0,
                'private_desc' => 'Why so secret?',
                'membership' => [
                    '_id' => '56ceca057cad3653598b4582',
                    'type' => 'time',
                    'start_date' => date('Y-m-d'),
                    'plan_code' => '56ceca59dccde',
                    'boooked_events' => 0,
                    'subscription' => [
                        'stripe_id' => 'sub_00X',
                        'end_date' => date('Y-m-d', strtotime('-1 day')),
                    ],
                ],
                'device' => [
                    'os' => 'android',
                ],
                '_pattern' => null,
                'version' => [
                    'minor' => 9,
                    'major' => 9,
                    'revision' => 25,
                ],
                'WAIVER' => true,
                'MEMBERPURCHASE' => true,
                'PAYGPAYMENT' => true,
                'transaction' => null,
                'emergency_contact' => '<EMAIL>',
                'pwd_token' => null,
            ],
        ];
    }

    private function mindbodyUsers(): array
    {
        return [
            [
                '_id' => new MongoId('5c7828a4d510f9635ad4a6b2'),
                'branch_id' => '5c783d4cd510f9635ad4a6b3',
                'namespace' => 'glofox',
                'active' => true,
                'type' => UserType::MEMBER,
                'first_name' => 'John',
                'last_name' => 'Member',
                'phone' => '**********',
                'email' => $email = '<EMAIL>',
                'login' => $email,
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$ZThWbjF4bXljcjlRdGZQUQ$POBM2GtX7QecEKu95F+Skmfe1NzHgrxKwnsPX+FDeig',
                'lead_status' => \Glofox\Domain\Leads\Status::WARM,
                'membership' => [
                    'type' => 'payg',
                    'subscription' => [
                        'foo' => 'bar',
                    ],
                ],
                'mindbody_id' => 123,
            ],
            [
                '_id' => new MongoId('5c7828a4d510f9635ad4a6b3'),
                'branch_id' => '5c783d4cd510f9635ad4a6b3',
                'namespace' => 'glofox',
                'active' => true,
                'type' => UserType::MEMBER,
                'first_name' => 'John',
                'last_name' => 'Member',
                'phone' => '0987654321',
                'email' => $email = '<EMAIL>',
                'login' => $email,
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$ZThWbjF4bXljcjlRdGZQUQ$POBM2GtX7QecEKu95F+Skmfe1NzHgrxKwnsPX+FDeig',
                'lead_status' => \Glofox\Domain\Leads\Status::WARM,
                'membership' => [
                    'type' => 'time',
                    '_id' => '54107c1cd7b6ddc3a98b4678',
                    'plan_code' => '54107aced7b6dd7aab8b4567',
                    'expiry_date' => new MongoDate(strtotime('2018-08-08')),
                    'subscription' => [
                        'foo' => 'bar',
                    ],
                ],
                'mindbody_id' => 124,
            ],
        ];
    }

    private function forPaymentIntent(): array
    {
        return [
            [
                '_id' => new MongoId('5d6867f38d8540851fe6066b'),
                'branch_id' => '5d6868a48d8540851fe6066c',
                'namespace' => 'payment-intent',
                'active' => true,
                'type' => UserType::MEMBER,
                'first_name' => 'John',
                'last_name' => 'Member',
                'phone' => '0987654321',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$ZThWbjF4bXljcjlRdGZQUQ$POBM2GtX7QecEKu95F+Skmfe1NzHgrxKwnsPX+FDeig',
                'lead_status' => \Glofox\Domain\Leads\Status::WARM,
                'membership' => [
                    'type' => 'payg',
                ],
            ],
        ];
    }

    private function forRecordMissingTransactionsScript(): array
    {
        return [
            [
                '_id' => new MongoId('a9a5521a05c687bda917785c'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'type' => 'MEMBER',
                'first_name' => 'Test 2',
                'last_name' => 'Member 2',
                'phone' => '0899991827',
                'email' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'login' => '<EMAIL>',
                'gender' => 'M',
                'birth' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'membership' => [
                    '_id' => '54107c1cd7b6ddc3a98b4577',
                    'branch_id' => '49a7011a05c677b9a916612a',
                    'type' => 'time',
                    'start_date' => date('Y-m-d', strtotime('-7 day')),
                    'plan_code' => '54107aced7b6dd7aab8b4567',
                    'boooked_events' => 0,
                    'expiry_date' => date('Y-m-d', strtotime('-1 day')),
                    'subscription' => [
                        'subscription_plan_id' => '59c8dc7c3ebaf48d6b0041a9',
                        'interval' => 'month',
                        'interval_count' => 1,
                        'price' => 20,
                        'upfront_fee' => 0,
                        'force_start' => false,
                        'stripe_id' => '1111',
                    ],
                ],
                'WAIVER' => true,
                'MEMBERPURCHASE' => true,
                'PAYGPAYMENT' => true,
                'transaction' => null,
            ],
            [
                '_id' => new MongoId('a9a5521a05c687bda9171234'),
                'branch_id' => '5b5b265bce2cf6bdb9ad6d9a',
                'namespace' => 'glofox',
                'active' => true,
                'type' => 'MEMBER',
                'first_name' => 'Test 2',
                'last_name' => 'Member 2',
                'phone' => '0899991827',
                'email' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'login' => '<EMAIL>',
                'gender' => 'M',
                'birth' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'membership' => [
                    '_id' => '54107c1cd7b6ddc3a98b4577',
                    'branch_id' => '49a7011a05c677b9a916612a',
                    'type' => 'time',
                    'start_date' => date('Y-m-d', strtotime('-7 day')),
                    'plan_code' => '54107aced7b6dd7aab8b4567',
                    'boooked_events' => 0,
                    'expiry_date' => date('Y-m-d', strtotime('-1 day')),
                    'subscription' => [
                        'subscription_plan_id' => '59c8dc7c3ebaf48d6b0041a9',
                        'interval' => 'month',
                        'interval_count' => 1,
                        'price' => 20,
                        'upfront_fee' => 0,
                        'force_start' => false,
                        'stripe_id' => '1111',
                    ],
                ],
                'WAIVER' => true,
                'MEMBERPURCHASE' => true,
                'PAYGPAYMENT' => true,
                'transaction' => null,
            ],
            [
                '_id' => new MongoId('a9a5521a05c687bda917755c'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'type' => 'MEMBER',
                'first_name' => 'Test 2',
                'last_name' => 'Member 2',
                'phone' => '0899991827',
                'email' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'login' => '<EMAIL>',
                'gender' => 'M',
                'birth' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'membership' => [
                    '_id' => '54107c1cd7b6ddc3a98b4577',
                    'branch_id' => '49a7011a05c677b9a916612a',
                    'type' => 'time',
                    'start_date' => date('Y-m-d', strtotime('-7 day')),
                    'plan_code' => '54107aced7b6dd7aab8b4567',
                    'boooked_events' => 0,
                    'expiry_date' => date('Y-m-d', strtotime('-1 day')),
                    'subscription' => [
                        'subscription_plan_id' => '59c8dc7c3ebaf48d6b0041a9',
                        'interval' => 'month',
                        'interval_count' => 1,
                        'price' => 20,
                        'upfront_fee' => 0,
                        'force_start' => false,
                        'stripe_id' => 'record_missing_transaction_sub_test_2',
                    ],
                ],
                'WAIVER' => true,
                'MEMBERPURCHASE' => true,
                'PAYGPAYMENT' => true,
                'transaction' => null,
            ],
        ];
    }

    private function forCsvTransactionReport(): array
    {
        return [
            [
                '_id' => new MongoId('5dc5bb4d7d54963c7c846b5a'),
                'user_id' => '5dc5bb4d7d54963c7c846b5a',
                'branch_id' => ['5db1b008f15a8f08c04f28ae', '5db1b008f15a8f08c04f28af'],
                'namespace' => 'csv-transaction-report',
                'active' => true,
                'type' => 'ADMIN',
                'first_name' => 'User CSV',
                'last_name' => 'Generator',
                'phone' => '**********',
                'email' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'login' => '<EMAIL>',
                'gender' => 'M',
                'birth' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'membership' => ['type' => 'payg'],
            ],
            [
                '_id' => new MongoId('5db1affef15a8f08c04f28ad'),
                'user_id' => '5db1affef15a8f08c04f28ad',
                'branch_id' => '5db1b008f15a8f08c04f28ae',
                'namespace' => 'csv-transaction-report',
                'active' => true,
                'type' => 'MEMBER',
                'first_name' => 'User CSV',
                'last_name' => 'Generator',
                'phone' => '**********',
                'email' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'login' => '<EMAIL>',
                'gender' => 'M',
                'birth' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'membership' => ['type' => 'payg'],
            ],
            [
                '_id' => new MongoId('5db1b2a0f15a8f08c04f28b1'),
                'user_id' => '5db1b2a0f15a8f08c04f28b1',
                'branch_id' => '5db1b008f15a8f08c04f28ae',
                'namespace' => 'csv-transaction-report',
                'active' => true,
                'type' => 'MEMBER',
                'first_name' => 'User CSV',
                'last_name' => 'Generator',
                'phone' => '**********',
                'email' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'login' => '<EMAIL>',
                'gender' => 'M',
                'birth' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'membership' => ['type' => 'payg'],
            ],
            [
                '_id' => new MongoId('5db1b2a0f15a8f08c04f28b2'),
                'user_id' => '5db1b2a0f15a8f08c04f28b2',
                'branch_id' => '5db1b008f15a8f08c04f28ae',
                'namespace' => 'csv-transaction-report',
                'active' => true,
                'type' => 'MEMBER',
                'first_name' => 'User CSV',
                'last_name' => 'Generator',
                'phone' => '**********',
                'email' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'login' => '<EMAIL>',
                'gender' => 'M',
                'birth' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'membership' => ['type' => 'payg'],
                'metadata' => [
                    'fiscal' => [
                        'tax_id' => 'tax-id-123',
                    ],
                ],
            ],
        ];
    }

    private function forFutureMemberships(): array
    {
        return [
            [
                '_id' => new MongoId('5df2655700708b481642fab1'),
                'user_id' => '5df2655700708b481642fab1',
                'branch_id' => '49a7011a05c677b9a916612a',
                'origin_branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'type' => 'MEMBER',
                'first_name' => 'Carla',
                'last_name' => 'Perez',
                'phone' => '**********',
                'email' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'login' => '<EMAIL>',
                'gender' => 'F',
                'birth' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'membership' => [
                    'type' => Type::TIME_CLASSES,
                    'subscription' => [
                        'paused' => true,
                    ],
                    'start_date' => date('Y-m-d', strtotime('+14 days')),
                    'expiry_date' => date('Y-m-d', strtotime('+14 days')),
                    'status' => 'FUTURE',
                ],
            ],
        ];
    }

    private function forPausingSubscriptionsOnRemovedPlans(): array
    {
        return [
            [
                '_id' => new MongoId('5e00ee8867633f29e477101b'),
                'user_id' => '5e00ee8867633f29e477101b',
                'branch_id' => '49a7011a05c677b9a916612a',
                'origin_branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'type' => 'MEMBER',
                'first_name' => 'Roberto',
                'last_name' => 'Carlos',
                'phone' => '**********',
                'email' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'login' => '<EMAIL>',
                'gender' => 'M',
                'birth' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'membership' => [
                    '_id' => '54107c1cd7b6ddc3a98b4543',
                    'type' => 'time',
                    'membership_group_id' => null,
                    'plan_code' => '123-removed-plan-id',
                    'booked_events' => 0,
                    'branch_id' => '49a7011a05c677b9a916612a',
                    'branch_name' => 'Glofox',
                    'starts_on' => 'PURCHASE_DATE',
                    'roaming_enabled' => false,
                    'start_date' => new MongoDate(strtotime('-2 hours')),
                    'plan_price' => 10.0,
                    'subscription' => [
                        'subscription_plan_id' => '5a53b98625f149480f000002',
                        'interval' => 'month',
                        'interval_count' => 1,
                        'price' => 10.0,
                        'upfront_fee' => 0.0,
                        'force_start' => false,
                        'credits' => [],
                        'stripe_id' => 'sub_GPj8xwqMy9h4lM',
                        'payment_gateway_id' => 'sub_GPj8xwqMy9h4lM',
                        'paused' => false,
                        'payment_method_type_id' => 'CARD',
                    ],
                    'expiry_date' => new MongoDate(strtotime('+1 month -2 hours')),
                ],
            ],
        ];
    }

    private function forIntegrators(): array
    {
        return [
            [
                '_id' => new MongoId('59a7011a05c677bda916612b'),
                'user_id' => '59a7011a05c677bda916612b',
                'branch_id' => '5d9cdc06aad495538f810244',
                'namespace' => 'restricted',
                'active' => true,
                'first_name' => 'admin',
                'last_name' => 'admin',
                'type' => 'ADMIN',
                'phone' => '**********',
                'email' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'login' => '<EMAIL>',
                'gender' => 'M',
                'birth' => new MongoDate(strtotime('1990-10-10 00:00:00')),
            ],
        ];
    }

    private function forStripeDD(): array
    {
        return [
            [
                '_id' => new MongoId('59a7011a02c677bda916612b'),
                'user_id' => '59a7011a02c677bda916612b',
                'branch_id' => '49a7011a08c677b9a916612a',
                'namespace' => 'stripedd',
                'active' => true,
                'first_name' => 'admin',
                'last_name' => 'admin',
                'type' => 'ADMIN',
                'phone' => '**********',
                'email' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'login' => '<EMAIL>',
                'gender' => 'M',
                'birth' => new MongoDate(strtotime('1990-10-10 00:00:00')),
            ],
            [
                '_id' => new MongoId('59a7011a02c677bda9166578'),
                'user_id' => '59a7011a02c677bda9166578',
                'branch_id' => '49a7011a08c677111916613a',
                'namespace' => 'stripedd',
                'active' => true,
                'first_name' => 'admin',
                'last_name' => 'admin',
                'type' => 'ADMIN',
                'phone' => '**********',
                'email' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'login' => '<EMAIL>',
                'gender' => 'M',
                'birth' => new MongoDate(strtotime('1990-10-10 00:00:00')),
            ],
            [
                '_id' => new MongoId('59a7011a02c677bda9166560'),
                'user_id' => '59a7011a02c677bda9166560',
                'branch_id' => '49a7011a08c845111916614d',
                'namespace' => 'stripeddacss',
                'active' => true,
                'first_name' => 'admin',
                'last_name' => 'admin',
                'type' => 'ADMIN',
                'phone' => '**********',
                'email' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'login' => '<EMAIL>',
                'gender' => 'M',
                'birth' => new MongoDate(strtotime('1990-10-10 00:00:00')),
            ],
            [
                '_id' => new MongoId('49a7011a08c845111916613c'),
                'user_id' => '49a7011a08c845111916613c',
                'branch_id' => '49a7011a08c845111916613c',
                'namespace' => 'stripecard',
                'active' => true,
                'first_name' => 'admin',
                'last_name' => 'admin',
                'type' => 'ADMIN',
                'phone' => '**********',
                'email' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'login' => '<EMAIL>',
                'gender' => 'M',
                'birth' => new MongoDate(strtotime('1990-10-10 00:00:00')),
            ],
        ];
    }

    private function forTerminalPOS(): array
    {
        return [
            [
                '_id' => new MongoId('59a7011a02c677bda916612c'),
                'user_id' => '59a7011a02c677bda916612c',
                'branch_id' => '59a7011a02c677bda916612c',
                'namespace' => 'terminalpos',
                'active' => true,
                'first_name' => 'admin',
                'last_name' => 'admin',
                'type' => 'ADMIN',
                'phone' => '**********',
                'email' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'login' => '<EMAIL>',
                'gender' => 'M',
                'birth' => new MongoDate(strtotime('1990-10-10 00:00:00')),
            ],
        ];
    }

    private function forNicepayProvider(): array
    {
        return [
            [
                '_id' => new MongoId('682b43bcfc88abbdcb0e867d'),
                'user_id' => '682b43bcfc88abbdcb0e867d',
                'branch_id' => '682b43bcfc88abbdcb0e867d',
                'namespace' => 'nicepaytestbranch',
                'active' => true,
                'first_name' => 'admin',
                'last_name' => 'admin',
                'type' => 'ADMIN',
                'phone' => '**********',
                'email' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'login' => '<EMAIL>',
                'gender' => 'M',
                'birth' => new MongoDate(strtotime('1990-10-10 00:00:00')),
            ],
        ];
    }

    private function forVirtualIsOnlineFieldOnEvents(): array
    {
        return [
            [
                '_id' => new MongoId('5e7cb74c3d36311bf52c63d9'),
                'user_id' => '5e7cb74c3d36311bf52c63d9',
                'branch_id' => '5e7cb6a53d36311bf52c63d8',
                'namespace' => 'namespace-5e7cb6a53d36311bf52c63d8',
                'active' => true,
                'first_name' => 'Online',
                'last_name' => 'Admin',
                'type' => 'ADMIN',
                'phone' => '**********',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'gender' => 'M',
                'birth' => new MongoDate(strtotime('1990-10-10 00:00:00')),
            ],
        ];
    }

    private function forSaveBatchCreditsTransformer(): array
    {
        return [
            [
                '_id' => new MongoId('5e7cb75c3d36311bf52c63d8'),
                'user_id' => '5e7cb75c3d36311bf52c63d8',
                'branch_id' => '5ce808750d35f24839739959',
                'namespace' => 'glofox',
                'active' => true,
                'first_name' => 'Foo',
                'last_name' => 'Bar',
                'type' => 'MEMBER',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'gender' => 'M',
            ],
        ];
    }

    private function createAdmin(array $override): array
    {
        $default = [
            '_id' => new MongoId(),
            'branch_id' => (string)(new MongoId()),
            'namespace' => 'testnamespace',
            'active' => true,
            'type' => 'ADMIN',
            'name' => 'Random Admin',
            'first_name' => 'Random',
            'last_name' => 'Admin',
            'phone' => '0899991827',
            'description' => 'Random Admin for testing',
            'email' => '<EMAIL>',
            'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
            'login' => '<EMAIL>',
            'bookable' => false,
        ];

        return array_replace_recursive($default, $override);
    }

    private function createTrainer(array $override): array
    {
        $default = [
            '_id' => new MongoId(),
            'branch_id' => (string)(new MongoId()),
            'namespace' => 'testnamespace',
            'active' => true,
            'type' => 'TRAINER',
            'name' => 'Random Trainer',
            'first_name' => 'Random',
            'last_name' => 'Trainer',
            'phone' => '0899991827',
            'description' => 'Random Trainer for testing',
            'email' => '<EMAIL>',
            'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
            'login' => '<EMAIL>',
            'bookable' => true,
        ];

        return array_replace_recursive($default, $override);
    }

    private function createReceptionist(array $override): array
    {
        $default = [
            '_id' => new MongoId(),
            'branch_id' => (string)(new MongoId()),
            'namespace' => 'testnamespace',
            'active' => true,
            'type' => UserType::RECEPTIONIST,
            'name' => 'Random Receptionist',
            'first_name' => 'Random',
            'last_name' => 'Receptionist',
            'phone' => '0899991827',
            'description' => 'Random Receptionist for testing',
            'email' => '<EMAIL>',
            'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
            'login' => '<EMAIL>',
            'bookable' => true,
        ];

        return array_replace_recursive($default, $override);
    }

    private function forPushNotificationsControllerTests(): array
    {
        return [
            [
                '_id' => new MongoId('5e7cb75c3d36311bf52c63d9'),
                'user_id' => '5e7cb75c3d36311bf52c63d9',
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'first_name' => 'Push Consent',
                'last_name' => 'True',
                'receive_marketing' => true,
                'type' => 'MEMBER',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'gender' => 'M',
                'device' => [
                    'os' => 'android',
                    'version' => [
                        'major' => 8,
                        'minor' => 1,
                        'revision' => 2,
                    ],
                    'id' => 'long-mock-of-a-device-id-long-long-mock-of-a-device-id-long-mock-of-a-device-id-5e7cb75c3d36311bf52c63d9',
                ],
            ],
            [
                '_id' => new MongoId('5e7cb75c3d36311bf52c63d0'),
                'user_id' => '5e7cb75c3d36311bf52c63d0',
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'first_name' => 'Push Consent',
                'last_name' => 'False',
                'receive_marketing' => false,
                'type' => 'MEMBER',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'gender' => 'M',
                'device' => [
                    'os' => 'android',
                    'version' => [
                        'major' => 8,
                        'minor' => 1,
                        'revision' => 2,
                    ],
                    'id' => 'long-mock-of-a-device-id-long-long-mock-of-a-device-id-long-mock-of-a-device-id-5e7cb75c3d36311bf52c63d0',
                ],
            ],
            [
                '_id' => new MongoId('5e7cb75c3d36311bf52c63d1'),
                'user_id' => '5e7cb75c3d36311bf52c63d1',
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'first_name' => 'Push Consent',
                'last_name' => 'True',
                'consent' => [
                    'push' => [
                        'active' => true,
                    ],
                ],
                'type' => 'MEMBER',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'gender' => 'M',
                'device' => [
                    'os' => 'android',
                    'version' => [
                        'major' => 8,
                        'minor' => 1,
                        'revision' => 2,
                    ],
                    'id' => 'long-mock-of-a-device-id-long-long-mock-of-a-device-id-long-mock-of-a-device-id-5e7cb75c3d36311bf52c63d1',
                ],
            ],
            [
                '_id' => new MongoId('5e7cb75c3d36311bf52c63d2'),
                'user_id' => '5e7cb75c3d36311bf52c63d2',
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'first_name' => 'Push Consent',
                'last_name' => 'False',
                'consent' => [
                    'push' => [
                        'active' => false,
                    ],
                ],
                'type' => 'MEMBER',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'gender' => 'M',
                'device' => [
                    'os' => 'android',
                    'version' => [
                        'major' => 8,
                        'minor' => 1,
                        'revision' => 2,
                    ],
                    'id' => 'long-mock-of-a-device-id-long-long-mock-of-a-device-id-long-mock-of-a-device-id-5e7cb75c3d36311bf52c63d2',
                ],
            ],
        ];
    }

    private function forRecurringReservationBookingsTests(): array
    {
        return [
            [
                '_id' => new MongoId('5e7cb75c3d36311bf52c63c9'),
                'user_id' => '5e7cb75c3d36311bf52c63c9',
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'first_name' => 'Reservation Test',
                'last_name' => 'True',
                'receive_marketing' => true,
                'type' => 'MEMBER',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'gender' => 'M',
                'membership' => [
                    '_id' => '56ceca057cad3653598b4582',
                    'type' => 'time',
                    'start_date' => date('Y-m-d', strtotime('-7 days')),
                    'plan_code' => '56ceca59dccde',
                    'boooked_events' => 0,
                    'expiry_date' => date('Y-m-d', strtotime('+1 year')),
                ],
            ],
        ];
    }

    private function addUserForCi1808UseCase(): array
    {
        return [
            [
                '_id' => new MongoId('618d2ea206724c52761b8c18'),
                'membership' => [
                    '_id' => '61d64bf0ff7d0b757523ec23',
                    'type' => 'num_classes',
                    'membership_group_id' => null,
                    'plan_code' => 1_641_434_052_863,
                    'plan_price' => 315,
                    'plan_upfront_fee' => 0,
                    'boooked_events' => 0,
                    'branch_id' => '5a9591bcdb07bce527400717',
                    'branch_name' => 'Glofox',
                    'starts_on' => 'PURCHASE_DATE',
                    'roaming_enabled' => true,
                    'start_date' => new MongoDate(strtotime('2022-01-28 00:00:00')),
                ],
                'first_name' => 'CI-1808',
                'last_name' => 'N',
                'phone' => 'ci1808',
                'email' => '<EMAIL>',
                'branch_id' => [
                    '5c783d4cd510f9635ad4a6b6',
                    '5a9591bcdb07bce527400717',
                ],
                'password' => '$argon2i$v=19$m=65536,t=4,p=sdasdasda',
                'birth' => null,
                'gender' => [
                    'name' => 'P',
                    'label' => 'PREFER NOT TO SAY',
                ],
                'answers' => [],
                'type' => 'MEMBER',
                'active' => true,
                'emergency_contact' => null,
                'lead_status' => 'MEMBER',
                'WAIVER' => true,
                'receive_marketing' => false,
                'login' => '<EMAIL>',
                'namespace' => 'glofox',
                'modified' => new MongoDate(strtotime('2022-01-28 06:21:47')),
                'created' => new MongoDate(strtotime('2021-11-11T 4:54:26')),
                'origin_branch_id' => '5c783d4cd510f9635ad4a6b6',
                'source' => 'WEBPORTAL',
                'user_id' => '5a9591bcdb07bce527400717',
                'MEMBERPURCHASE' => true,
                'PAYGPAYMENT' => true,
                'stripe_cc_token' => null,
            ],
            [
                '_id' => new MongoId('6000f10124d6a44b815ebde0'),
                'membership' => [
                    '_id' => '602c805dc545440a305480c5',
                    'type' => 'num_classes',
                    'membership_group_id' => null,
                    'plan_code' => 1_613_529_075_468,
                    'plan_price' => 1650,
                    'plan_upfront_fee' => 0,
                    'boooked_events' => 0,
                    'branch_id' => '5c783d4cd510f9635ad4a6b6',
                    'branch_name' => 'Glofox',
                    'starts_on' => 'PURCHASE_DATE',
                    'roaming_enabled' => true,
                    'start_date' => new MongoDate(strtotime('2021-11-30 00:00:00')),
                ],
                'first_name' => 'Raly',
                'last_name' => 'Toch',
                'phone' => '000',
                'email' => '<EMAIL>',
                'branch_id' => [
                    '5c783d4cd510f9635ad4a6b6',
                    '5a9591bcdb07bce527400717',
                ],
                'password' => '$argon2i$v=19$m=4096,t=10,p=+EDITED',
                'birth' => null,
                'gender' => [
                    'name' => 'P',
                    'label' => 'PREFER NOT TO SAY',
                ],
                'type' => 'MEMBER',
                'active' => true,
                'emergency_contact' => null,
                'receive_marketing' => false,
                'lead_status' => 'MEMBER',
                'WAIVER' => true,
                'login' => '<EMAIL>',
                'namespace' => 'glofox',
                'modified' => new MongoDate(strtotime('2022-02-05 03:59:32')),
                'created' => new MongoDate(strtotime('2021-01-15 01:33:53')),
                'categories' => [
                ],
                'origin_branch_id' => '5c783d4cd510f9635ad4a6b6',
                'name' => 'Raly Toch',
                'source' => 'WEBPORTAL',
                'user_id' => '6000f10124d6a44b815ebde0',
                'MEMBERPURCHASE' => true,
                'transaction' => [],
                'PAYGPAYMENT' => true,
                'pwd_token' => null,
                'pwd_token_expiry' => null,
            ],
        ];
    }

    private function forSCATests(): array
    {
        return [
            [
                '_id' => new MongoId('5e7cb75c3d36311bf52c63c1'),
                'user_id' => '5e7cb75c3d36311bf52c63c9',
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'first_name' => 'SCA Test',
                'last_name' => 'True',
                'receive_marketing' => true,
                'type' => 'MEMBER',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'gender' => 'M',
                'membership' => [
                    'type' => 'payg',
                ],
            ],
            [
                '_id' => new MongoId('5e7cb75c3d36311bf52c63c2'),
                'user_id' => '5e7cb75c3d36311bf52c63c9',
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'first_name' => 'SCA Test with membership',
                'last_name' => 'True',
                'receive_marketing' => true,
                'type' => 'MEMBER',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'gender' => 'M',
                'membership' => [
                    'type' => 'time',
                ],
            ],
        ];
    }

    private function addUserForCi1263UseCase(): array
    {
        return [
            [
                '_id' => new MongoId('5fb59526c635ea07321bf2e1'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'membership' => [
                    '_id' => '5ccb4714040f1204ca7ebac6',
                    'start_date' => new MongoDate(strtotime('2022-02-03 00:00:00')),
                    "type" => "num_classes",
                    "membership_group_id" => null,
                    "plan_code" => 1_603_380_284_540,
                    "branch_id" => self::GLOFOX_BRANCH_ID,
                    "starts_on" => "PURCHASE_DATE",
                    "roaming_enabled" => false,
                ],
                'first_name' => 'Dre',
                'last_name' => 'Cooper',
                'phone' => '3059169410',
                'email' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=65536,t=4,p=1$',
                'birth' => '2001-05-13',
                'gender' => [
                    'name' => 'P',
                    'label' => 'PREFER NOT TO SAY',
                ],
                'answers' => [
                ],
                'type' => 'MEMBER',
                'active' => true,
                'emergency_contact' => null,
                'lead_status' => 'MEMBER',
                'WAIVER' => true,
                'device' => [
                    'os' => 'ios',
                    'version' => [
                        'major' => 8,
                        'minor' => 2,
                        'revision' => 6,
                    ],
                    'id' => '6c73570d0e896f34921c0c47287ad608d96796408ab4987bca2420ca43835a84',
                ],
                'login' => '<EMAIL>',
                'modified' => new MongoDate(strtotime('2022-02-23 11:25:16')),
                'created' => new MongoDate(strtotime('2020-11-18 21:41:58')),
                'categories' => [
                ],
                'origin_branch_id' => '49a7011a05c677b9a916612a',
                'name' => 'Dre Cooper',
                'source' => 'MEMBER_APP',
                'user_id' => '5fb59526c635ea07321bf2e1',
                'MEMBERPURCHASE' => false,
                'PAYGPAYMENT' => true,
                'strike' => 0,
                'metadata' => [
                    'twilio' => [
                        'phone_number' => '+13059169410',
                    ],
                ],
                'transaction' => [
                ],
            ],
        ];
    }

    private function withPreDefinedJoinedAt(): array
    {
        return [
            [
                '_id' => new MongoId('5fb59526c635ea07321bf2c2'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'membership' => [
                    "type" => "payg",
                ],
                'first_name' => 'Dre',
                'last_name' => 'Cooper',
                'phone' => '3059169410',
                'email' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=65536,t=4,p=1$',
                'birth' => '2001-05-13',
                'gender' => [
                    'name' => 'P',
                    'label' => 'PREFER NOT TO SAY',
                ],
                'answers' => [
                ],
                'type' => 'MEMBER',
                'active' => true,
                'emergency_contact' => null,
                'lead_status' => 'MEMBER',
                'WAIVER' => true,
                'device' => [
                    'os' => 'ios',
                    'version' => [
                        'major' => 8,
                        'minor' => 2,
                        'revision' => 6,
                    ],
                    'id' => '6c73570d0e896f34921c0c47287ad608d96796408ab4987bca2420ca43835a84',
                ],
                'login' => '<EMAIL>',
                'modified' => new MongoDate(strtotime('2022-02-23 11:25:16')),
                'created' => new MongoDate(strtotime('2020-11-18 21:41:58')),
                'joined_at' => new MongoDate(strtotime('2018-07-06 05:04:03')),
                'categories' => [
                ],
                'origin_branch_id' => '49a7011a05c677b9a916612a',
                'name' => 'Dre Cooper',
                'source' => 'MEMBER_APP',
                'user_id' => '5fb59526c635ea07321bf2e1',
                'MEMBERPURCHASE' => false,
                'PAYGPAYMENT' => true,
                'strike' => 0,
                'metadata' => [
                    'twilio' => [
                        'phone_number' => '+13059169410',
                    ],
                ],
                'transaction' => [
                ],
            ],
        ];
    }

    private function forLeadsFilterTest(): array
    {
        return [
            [
                '_id' => new MongoId('62e123567b914199b7b31aff'),
                'branch_id' => '62e13d1731a863c5cc75d14c',
                'namespace' => 'marynafitness',
                'active' => true,
                'categories' => [],
                'type' => 'ADMIN',
                'name' => 'Admin Maryna',
                'first_name' => 'Admin',
                'last_name' => 'Maryna',
                'phone' => '0899991827',
                'description' => 'Worst Trainer Ever!',
                'email' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'bookable' => true,
                'login' => '<EMAIL>',
                'experience' => 'A lot...',
                'gender' => 'M',
                'birth' => new MongoDate(strtotime('1990-10-10 00:00:00')),
                'twitter' => '@maryna',
                'facebook' => 'https://facebook.com/maryna',
                'phone' => '123123321',
                'bookable' => false,
                'stripe_token' => null,
                'answers' => [],
                'card_id' => null,
                'stripe_customer_id' => null,
                'stripe_cc_token' => null,
                'auto_generated' => false,
                'access_barcode' => '123123321',
                'created' => new MongoDate(),
                'modified' => new MongoDate(),
                'last_login' => new MongoDate(),
                'strike' => 0,
                'private_desc' => 'Why so secret?',
                'membership' => [
                    'type' => 'payg',
                ],
                'device' => [
                    'os' => 'android',
                ],
                '_pattern' => null,
                'version' => [
                    'minor' => 9,
                    'major' => 9,
                    'revision' => 25,
                ],
                'WAIVER' => true,
                'MEMBERPURCHASE' => true,
                'PAYGPAYMENT' => true,
                'transaction' => null,
                'emergency_contact' => '<EMAIL>',
                'pwd_token' => null,
            ],
            [
                '_id' => new MongoId('62e11558d8ac999bcb6df1ac'),
                'user_id' => '62e11558d8ac999bcb6df1ac',
                'branch_id' => '62e13d1731a863c5cc75d14c',
                'namespace' => 'glofox',
                'active' => true,
                'first_name' => 'User1',
                'last_name' => 'Lastname',
                'type' => 'MEMBER',
                'lead_status' => 'MEMBER',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'gender' => 'M',
                'membership' => [
                    'type' => 'payg',
                ],
                'source' => 'WEBPORTAL',
                'created' => new MongoDate(strtotime('2022-07-01 18:41:58')),
            ],
            [
                '_id' => new MongoId('62e23f76930c22b8fce6e91a'),
                'user_id' => '62e23f76930c22b8fce6e91a',
                'branch_id' => '62e13d1731a863c5cc75d14c',
                'namespace' => 'glofox',
                'active' => true,
                'first_name' => 'User2',
                'last_name' => 'Lastname',
                'type' => 'MEMBER',
                'lead_status' => 'MEMBER',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'gender' => 'M',
                'membership' => [
                    'type' => 'payg',
                ],
                'source' => 'WEBPORTAL',
                'created' => new MongoDate(strtotime('2022-07-02 21:41:58')),
            ],
            [
                '_id' => new MongoId('62e2426ab9a46b45ecd1f904'),
                'user_id' => '62e2426ab9a46b45ecd1f904',
                'branch_id' => '62e13d1731a863c5cc75d14c',
                'namespace' => 'glofox',
                'active' => true,
                'first_name' => 'User3',
                'last_name' => 'Lastname',
                'type' => 'MEMBER',
                'lead_status' => 'TRIAL',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'gender' => 'M',
                'membership' => [
                    'type' => 'payg',
                ],
                'source' => 'WEBPORTAL',
                'created' => new MongoDate(),
            ],
        ];
    }

    private function forUserCreditsForBookingAppointmentSlot(): array
    {
        return [
            [
                '_id' => new MongoId('63074e6944eb47f8fd61a035'),
                'user_id' => '62e23f76930c22b8fce6e91a',
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'first_name' => 'User',
                'last_name' => 'Lastname',
                'type' => 'MEMBER',
                'lead_status' => 'MEMBER',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'gender' => 'M',
                'membership' => [
                    'type' => 'payg',
                ],
                'source' => 'DASHBOARD',
            ],
        ];
    }

    private function forBookingsMetadata(): array
    {
        return [
            $this->createAdmin([
                '_id' => new MongoId('633ad7b33e8c7958abc3e372'),
                'branch_id' => '633ad68b69f7280b4564aee3',
                'namespace' => 'bookingsmetadata',
                'first_name' => 'Admin',
                'last_name' => 'Test',
                'name' => 'Admin Test',
            ]),
            $this->createTrainer([
                '_id' => new MongoId('633bc63c8b1c7ae549112f2f'),
                'branch_id' => '633ad68b69f7280b4564aee3',
                'namespace' => 'bookingsmetadata',
                'first_name' => 'Trainer',
                'last_name' => 'Test',
                'name' => 'Trainer Test',
            ]),
            $this->createTrainer([
                '_id' => new MongoId('6399dcc04111d0dededb266e'),
                'branch_id' => '633ad68b69f7280b4564aee3',
                'namespace' => 'bookingsmetadata',
                'first_name' => 'Trainer 2',
                'last_name' => 'Test',
                'name' => 'Trainer 2 Test',
            ]),
            $this->createPaygMember([
                '_id' => new MongoId('633ad7c5e757e6c240ee55ba'),
                'branch_id' => '633ad68b69f7280b4564aee3',
                'namespace' => 'bookingsmetadata',
                'first_name' => 'Payg Member',
                'last_name' => 'Test',
                'name' => 'Payg Member Test',
                'email' => '<EMAIL>',
                'phone' => '+873450977',
            ]),
            $this->createMemberWithValidSubscriptionTimeMembership(
                new MongoId('633bc32419077393a557d54d'),
                '<EMAIL>',
                [
                    'branch_id' => '633ad68b69f7280b4564aee3',
                    'namespace' => 'bookingsmetadata',
                    'first_name' => 'Unlim Membership Member',
                    'last_name' => 'Test',
                    'name' => 'Unlim Membership Member Test',
                ]
            ),
            // User with credits
            $this->createPaygMember([
                '_id' => new MongoId('63998828ca5fbb0e575cb46a'),
                'branch_id' => '633ad68b69f7280b4564aee3',
                'namespace' => 'bookingsmetadata',
                'first_name' => 'Payg Member with Credits',
                'last_name' => 'With Credits',
                'name' => 'Payg Member With Credits',
            ]),
        ];
    }

    private function forAccessesReport(): array
    {
        return [
            [
                '_id' => new MongoId('6364c2f83c2bdf4419f78e79'),
                'branch_id' => '6364be7488385df39f3a70b0',
                'namespace' => 'accessesreport',
                'active' => true,
                'type' => UserType::ADMIN,
                'first_name' => 'Admin',
                'last_name' => 'Who is allowed to get users',
                'phone' => \Illuminate\Support\Str::random(),
                'email' => $email = \Illuminate\Support\Str::random() . '@glofox.com',
                'login' => $email,
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$ZThWbjF4bXljcjlRdGZQUQ$POBM2GtX7QecEKu95F+Skmfe1NzHgrxKwnsPX+FDeig',
                'abilities' => [],
            ],
            [
                '_id' => new MongoId('6364bfe59ef554c702e0720c'),
                'branch_id' => '6364be7488385df39f3a70b0',
                'origin_branch_id' => '6364be7488385df39f3a70b0',
                'namespace' => 'accessesreport',
                'active' => true,
                'type' => UserType::MEMBER,
                'first_name' => 'John',
                'last_name' => 'Rumor',
                'phone' => '11123',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'lead_status' => \Glofox\Domain\Leads\Status::UNTOUCHED,
                'membership' => [
                    'type' => 'payg',
                ],
                'abilities' => [
                    \Glofox\Domain\Authorization\Abilities\UserAbility::PARENT_LOGIN_AS_A_CHILD,
                ],
            ],
            [
                '_id' => new MongoId('6364bff2c0b74fe571a5298b'),
                'branch_id' => '6364be7488385df39f3a70b0',
                'origin_branch_id' => '6364be7488385df39f3a70b0',
                'namespace' => 'accessesreport',
                'active' => true,
                'type' => UserType::MEMBER,
                'first_name' => 'Robert',
                'last_name' => 'Dad',
                'phone' => '11123',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'lead_status' => \Glofox\Domain\Leads\Status::UNTOUCHED,
                'membership' => [
                    'type' => 'payg',
                ],
                'abilities' => [
                    \Glofox\Domain\Authorization\Abilities\UserAbility::PARENT_LOGIN_AS_A_CHILD,
                ],
            ],
            [
                '_id' => new MongoId('6364bffe7a75f66b501ff47c'),
                'branch_id' => '6364be7488385df39f3a70b0',
                'namespace' => 'accessesreport',
                'active' => true,
                'type' => UserType::MEMBER,
                'first_name' => 'Jane',
                'last_name' => 'Daughter',
                'phone' => '',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'lead_status' => \Glofox\Domain\Leads\Status::UNTOUCHED,
                'gender' => [
                    'name' => 'FEMALE',
                    'label' => 'F',
                ],
                'parent_id' => '6364bff2c0b74fe571a5298b',
                'membership' => [
                    'type' => 'payg',
                ],
                'use_parent_card' => false,
                'origin_branch_id' => '6364be7488385df39f3a70b0'
            ],
            [
                '_id' => new MongoId('6364c0073b17c39cdfaf4c91'),
                'branch_id' => '6364be7488385df39f3a70b0',
                'namespace' => 'accessesreport',
                'active' => true,
                'type' => UserType::MEMBER,
                'first_name' => 'Anne',
                'last_name' => 'Daughter',
                'phone' => '',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'lead_status' => \Glofox\Domain\Leads\Status::UNTOUCHED,
                'gender' => [
                    'name' => 'FEMALE',
                    'label' => 'F',
                ],
                'parent_id' => '6364bff2c0b74fe571a5298b',
                'membership' => [
                    'type' => 'payg',
                ],
                'avatar' => 'fakeAvatarUrl',
                'origin_branch_id' => '6364be7488385df39f3a70b0'
            ],
            [
                '_id' => new MongoId('6364c013df7aac6e47890713'),
                'branch_id' => '6364be7488385df39f3a70b0',
                'namespace' => 'accessesreport',
                'active' => false,
                'type' => UserType::MEMBER,
                'first_name' => 'Maria',
                'last_name' => 'Daughter',
                'phone' => '',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'lead_status' => \Glofox\Domain\Leads\Status::UNTOUCHED,
                'gender' => [
                    'name' => 'FEMALE',
                    'label' => 'F',
                ],
                'parent_id' => '6364bff2c0b74fe571a5298b',
                'membership' => [
                    'type' => 'payg',
                ],
                'device' => [
                    'os' => 'android',
                    'version' => [
                        'major' => 8,
                        'minor' => 1,
                        'revision' => 2,
                    ],
                    'id' => 'long-mock-of-a-device-id-long-long-mock-of-a-device-id-long-mock-of-a-device-id-5e7cb75c3d39865bf52c63yt',
                ],
            ],
        ];
    }

    private function forAccessesList(): array
    {
        return [
            [
                '_id' => new MongoId('64ef9ee97109a72cffe3876b'),
                'branch_id' => '64ee4ec06753d53819a72923',
                'origin_branch_id' => '64ee4ec06753d53819a72923',
                'namespace' => 'accesseslist',
                'active' => true,
                'type' => UserType::MEMBER,
                'first_name' => 'Access List',
                'last_name' => 'User 2',
                'phone' => '11122',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlU' .
                    'QzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'lead_status' => \Glofox\Domain\Leads\Status::UNTOUCHED,
                'membership' => [
                    'type' => 'payg',
                ],
            ],
            [
                '_id' => new MongoId('64ee5157817012b81b1e420b'),
                'branch_id' => '64ee4ec06753d53819a72923',
                'origin_branch_id' => '64ee4ec7261028d1afc99d0b',
                'namespace' => 'accesseslist',
                'active' => true,
                'type' => UserType::MEMBER,
                'first_name' => 'Access List',
                'last_name' => 'User',
                'phone' => '11123',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlU' .
                    'QzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'lead_status' => \Glofox\Domain\Leads\Status::UNTOUCHED,
                'membership' => [
                    'type' => 'payg',
                ],
            ],
            [
                '_id' => new MongoId('64ee5110805a9074b220a585'),
                'branch_id' => '64ee4ec06753d53819a72923',
                'namespace' => 'accesseslist',
                'active' => true,
                'type' => UserType::ADMIN,
                'first_name' => 'Access List',
                'last_name' => 'Admin',
                'phone' => \Illuminate\Support\Str::random(),
                'email' => $email = \Illuminate\Support\Str::random() . '@glofox.com',
                'login' => $email,
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$ZThWbjF4bXljc' .
                    'jlRdGZQUQ$POBM2GtX7QecEKu95F+Skmfe1NzHgrxKwnsPX+FDeig',
                'abilities' => [],
            ],
        ];
    }

    private function forFilterBookingsTest(): array
    {
        return [
            $this->createPaygMember([
                '_id' => '59a7010a05c677b3a916612e',
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
            ]),
        ];
    }

    private function forUnBookableTrainersFilter(): array
    {
        return [
            $this->createTrainer([
                '_id' => new MongoId('633ad7b33e8c7958abc3e381'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'first_name' => 'UnBookable',
                'last_name' => 'Trainer',
                'name' => 'UnBookable Trainer',
                'bookable' => false,
            ]),
            $this->createTrainer([
                '_id' => new MongoId('633ad7b33e8c7958abc3e382'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'first_name' => 'Bookable',
                'last_name' => 'Trainer',
                'name' => 'Bookable Trainer',
                'bookable' => true,
            ]),
        ];
    }

    private function forMinimumAgeTest(): array
    {
        return [
            $this->createAdmin([
                '_id' => new MongoId('5e9ed06cd575e6003c38648d'),
                'branch_id' => '6364be7488385df39f3a7015',
                'namespace' => 'minimumagebranch',
            ]),
        ];
    }

    private function forStaffTypeChangeTest(): array
    {
        return [
            $this->createTrainer([
                '_id' => new MongoId('633ad7b33e8c7958abc3e383'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'first_name' => 'Trainer',
                'last_name' => 'Type',
                'name' => 'Trainer Type',
                'email' => '<EMAIL>',
            ]),
        ];
    }

    private function forListAllBookingsActionTest(): array
    {
        return [
            $this->createAdmin([
                '_id' => new MongoId('6435576e436939a4467bf624'),
                'branch_id' => '6435563a6272b68401e85728',
                'namespace' => 'listallbookings',
            ]),
            $this->createTrainer([
                '_id' => new MongoId('64356ba8219f042b5adf2bea'),
                'branch_id' => '6435563a6272b68401e85728',
                'namespace' => 'listallbookings',
            ]),
            $this->createPaygMember([
                '_id' => '64664646fef991551f0e6a58',
                'branch_id' => '6435563a6272b68401e85728',
                'namespace' => 'listallbookings',
            ]),
        ];
    }

    private function forVirtualSlotsTest(): array
    {
        return [
            $this->createAdmin([
                '_id' => new MongoId('6461eeaef303e35f65b8bef0'),
                'branch_id' => static::VIRTUAL_SLOT_BRANCH_ID,
                'namespace' => static::VIRTUAL_SLOT_BRANCH_NAMESPACE,
            ]),
            $this->createTrainer([
                '_id' => new MongoId('6461eeb3bd01a2e1dc88a96d'),
                'branch_id' => static::VIRTUAL_SLOT_BRANCH_ID,
                'namespace' => static::VIRTUAL_SLOT_BRANCH_NAMESPACE,
            ]),
            $this->createPaygMember([
                '_id' => new MongoId('6461eeba3e6feb790c7ec607'),
                'branch_id' => static::VIRTUAL_SLOT_BRANCH_ID,
                'namespace' => static::VIRTUAL_SLOT_BRANCH_NAMESPACE,
            ]),
        ];
    }

    private function forBookingWindow(): array
    {
        return [
            $this->createAdmin([
                '_id' => new MongoId('646e05a424dae62e909048b8'),
                'branch_id' => '646de8d3c539cfc3cb649547',
                'namespace' => 'appointmentsbookingwindow',
            ]),
            $this->createTrainer([
                '_id' => new MongoId('64747d4722817041ee4f8a28'),
                'branch_id' => '646de8d3c539cfc3cb649547',
                'namespace' => 'appointmentsbookingwindow',
            ]),
            $this->createPaygMember([
                '_id' => new MongoId('646e05b498dff227b968345b'),
                'branch_id' => '646de8d3c539cfc3cb649547',
                'namespace' => 'appointmentsbookingwindow',
                'login' => '<EMAIL>',
                'email' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
            ]),
            // TODO MOVE
            $this->createPaygMember([
                '_id' => new MongoId('646e05b498dff227b9683452'),
                'branch_id' => '64784ace2c8a824784ab4cc4',
                'namespace' => 'appointmentsbookingwindow',
                'login' => '<EMAIL>',
                'email' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVw',
            ]),
            $this->createAdmin([
                '_id' => new MongoId('64784b3d2926d0d99900fb00'),
                'branch_id' => '64784ace2c8a824784ab4cc4',
                'namespace' => 'classbookingwindow',
            ]),
            $this->createTrainer([
                '_id' => new MongoId('64784b4628c2ddf21e169901'),
                'branch_id' => '64784ace2c8a824784ab4cc4',
                'namespace' => 'classbookingwindow',
            ]),
            $this->createPaygMember([
                '_id' => new MongoId('64784b4e21adecb52f8aa7ae'),
                'branch_id' => '64784ace2c8a824784ab4cc4',
                'namespace' => 'classbookingwindow',
                'login' => '<EMAIL>',
                'email' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
            ]),
        ];
    }

    private function forVirtualAppointmentTrainerDelete(): array
    {
        return [
            $this->createTrainer([
                '_id' => new MongoId('64747d4722817041ee4f8a29'),
                'branch_id' => '646de8d3c539cfc3cb649547',
                'namespace' => 'appointmentsbookingwindow',
                'active' => false,
            ]),
            $this->createTrainer([
                '_id' => new MongoId('64747d4722817041ee4f8a30'),
                'branch_id' => '646de8d3c539cfc3cb649547',
                'namespace' => 'appointmentsbookingwindow',
                'active' => true,
            ]),
            $this->createTrainer([
                '_id' => new MongoId('64747d4722817041ee4f8a31'),
                'branch_id' => '646de8d3c539cfc3cb649547',
                'namespace' => 'appointmentsbookingwindow',
                'active' => true,
            ]),
        ];
    }

    private function forPayroll(): array
    {
        return [
            $this->createAdmin([
                '_id' => new MongoId('64b804217388a46d30844c1d'),
                'branch_id' => '64b803ccfc8ba4d6f8d99c9c',
                'namespace' => 'payroll',
            ]),
            $this->createTrainer([
                '_id' => new MongoId('64b8045076c575dfcc56a9da'),
                'branch_id' => '64b803ccfc8ba4d6f8d99c9c',
                'namespace' => 'payroll',
            ]),
            $this->createTrainer([
                '_id' => new MongoId('64b806df80db8155049eb1c0'),
                'branch_id' => '64b803ccfc8ba4d6f8d99c9c',
                'namespace' => 'payroll',
            ]),
            $this->createReceptionist([
                '_id' => new MongoId('64b804f955af388a410bab51'),
                'branch_id' => '64b803ccfc8ba4d6f8d99c9c',
                'namespace' => 'payroll',
            ]),
            $this->createPaygMember([
                '_id' => new MongoId('64b80a302cccbf9c9db7ba16'),
                'branch_id' => '64b803ccfc8ba4d6f8d99c9c',
                'namespace' => 'payroll',
                'login' => '<EMAIL>',
                'email' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
            ]),
        ];
    }

    private function forSendGroupMessage()
    {
        return [
            [
                '_id' => new MongoId('6506c52376d41a12833a3892'),
                'branch_id' => '64ee4ec06753d53819a72923',
                'namespace' => 'glofox',
                'active' => true,
                'type' => UserType::MEMBER,
                'first_name' => 'Of Namespace',
                'last_name' => 'User_2',
                'phone' => '12345',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlU' .
                    'QzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'lead_status' => \Glofox\Domain\Leads\Status::UNTOUCHED,
                'membership' => [
                    'type' => 'payg',
                ],
                'receive_marketing' => true,
                'device' => [
                    'os' => 'android',
                    'version' => [
                        'major' => 8,
                        'minor' => 1,
                        'revision' => 2,
                    ],
                    'id' => 'mock-of-a-device-id-6506c52376d41a12833a3892',
                ],
            ],
            [
                '_id' => new MongoId('6506bce1fca7cb4582ae53b2'),
                'branch_id' => '64ee4ec06753d53819a72923',
                'namespace' => 'glofox',
                'active' => true,
                'type' => UserType::MEMBER,
                'first_name' => 'Of Namespace',
                'last_name' => 'User',
                'phone' => '123456',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlU' .
                    'QzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'lead_status' => \Glofox\Domain\Leads\Status::UNTOUCHED,
                'membership' => [
                    'type' => 'payg',
                ],
                'receive_marketing' => false,
                'device' => [
                    'os' => 'android',
                    'version' => [
                        'major' => 8,
                        'minor' => 1,
                        'revision' => 2,
                    ],
                    'id' => 'mock-of-a-device-id-6506bce1fca7cb4582ae53b2',
                ],
                'consent' => [
                    'push' => [
                        'active' => true,
                    ],
                ],
            ],
            [
                '_id' => new MongoId('6506bce99adc376c55898e70'),
                'branch_id' => '64ee4ec7261028d1afc99d0b',
                'namespace' => 'send_group_message_alt',
                'active' => true,
                'type' => UserType::MEMBER,
                'first_name' => 'Of Different Namespace',
                'last_name' => 'User',
                'phone' => '1234567',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlU' .
                    'QzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'lead_status' => \Glofox\Domain\Leads\Status::UNTOUCHED,
                'membership' => [
                    'type' => 'payg',
                ],
                'device' => [
                    'os' => 'android',
                    'version' => [
                        'major' => 8,
                        'minor' => 1,
                        'revision' => 2,
                    ],
                    'id' => 'mock-of-a-device-id-6506bce99adc376c55898e70',
                ],
            ],
            [
                '_id' => new MongoId('6506bcf0b511dd853f0e36b0'),
                'branch_id' => '64ee4ec06753d53819a72923',
                'namespace' => 'glofox',
                'active' => true,
                'type' => UserType::ADMIN,
                'first_name' => 'Of Namespace',
                'last_name' => 'Admin',
                'phone' => '12345678',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$ZThWbjF4bXljc' .
                    'jlRdGZQUQ$POBM2GtX7QecEKu95F+Skmfe1NzHgrxKwnsPX+FDeig',
                'abilities' => [],
                'device' => [
                    'os' => 'android',
                    'version' => [
                        'major' => 8,
                        'minor' => 1,
                        'revision' => 2,
                    ],
                    'id' => 'mock-of-a-device-id-6506bcf0b511dd853f0e36b0',
                ],
            ],
        ];
    }

    private function forUserRestore(): array
    {
        $branchId = '652c3f13eb53aebfbb1266f9';
        $namespace = 'a-test-namespace';
        return [
            [
                '_id' => new MongoId('652c3f0b8c1be3593c53625f'),
                'branch_id' => $branchId,
                'namespace' => $namespace,
                'active' => true,
                'type' => UserType::ADMIN,
                'first_name' => 'admin',
                'last_name' => 'active',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$ZThWbjF4bXljc' .
                    'jlRdGZQUQ$POBM2GtX7QecEKu95F+fkmfe1NzHgrxKwnsPX+FDeig',
            ],
            [
                '_id' => new MongoId('668521545bd59dbf0ad5b7cf'),
                'branch_id' => $branchId,
                'namespace' => $namespace,
                'active' => true,
                'type' => UserType::TRAINER,
                'first_name' => 'trainer',
                'last_name' => 'active',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$ZThWbjF4bXljc' .
                    'jlRdGZQUQ$POBM2GtX7QecEKu95F+fkmfe1NzHgrxKwnsPX+FDeig',
            ],
            [
                '_id' => new MongoId('652c3c4dc788cfac1d6994ff'),
                'branch_id' => $branchId,
                'namespace' => $namespace,
                'active' => false,
                'type' => UserType::MEMBER,
                'first_name' => 'user',
                'last_name' => 'inactive',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'password' => '$argon2i$v=20$m=1024,t=2,p=2$SGRCWm9SeVlU' .
                    'QzdRcTVGdg$AcF8hKNGIBgyA51DEdgITjcC5clbAVgrkzEth/f/EVg',
                'lead_status' => Status::MEMBER,
                'membership' => [
                    'type' => 'payg',
                ],
            ],
            [
                '_id' => new MongoId('652d04632bc0f456560effcd'),
                'branch_id' => $branchId,
                'namespace' => $namespace,
                'active' => true,
                'type' => UserType::MEMBER,
                'first_name' => 'user',
                'last_name' => 'active',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'password' => '$argon2i$v=21$m=1024,t=2,p=2$SGRCWm9SeVlU' .
                    'QzdRcTVGdg$AcF8hKNGIBgyA51DEdgITjcC5clbAVgrkzEth/f/EVg',
                'lead_status' => Status::MEMBER,
                'membership' => [
                    'type' => 'payg',
                ],
            ],
        ];
    }

    private function forUserDataUpdate(): array
    {
        return [
            [
                '_id' => new MongoId('6506bcf0b511dd853f0e36b1'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'type' => UserType::ADMIN,
                'first_name' => 'Miguel',
                'last_name' => 'Admin',
                'phone' => '23456789',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$ZThWbjF4bXljc' .
                    'jlRdGZQUQ$POBM2GtX7QecEKu95F+Skmfe1NzHgrxKwnsPX+FDeiw',
                'description' => 'Miguel Admin',
                'bookable' => true,
            ],
            [
                '_id' => new MongoId('5a9fea1bfc4d3256baa1a6b3'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'type' => UserType::ADMIN,
                'first_name' => 'Generated',
                'last_name' => 'Admin',
                'phone' => '32456789',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$ZThWbjF4bXljc' .
                    'jlRdGZQUQ$POBM2GtX7QecEKu95F+Skmfe1NzHgrxKwnsPX+FDeie',
                'description' => 'Generated Admin',
                'bookable' => true,
            ],
            [
                '_id' => new MongoId('6506bcf0b511dd853f0e36b2'),
                'branch_id' => '5addc25383266f65abf515c4',
                'namespace' => 'glofox',
                'active' => true,
                'type' => UserType::ADMIN,
                'first_name' => 'Test',
                'last_name' => 'Admin',
                'phone' => '5346789',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$ZThWbjF4bXljc' .
                    'jlRdGZQUQ$POBM2GtX7QecEKu95F+Skmfe1NzHgrxKwnsPX+FDeir',
                'description' => 'Test Admin',
                'bookable' => true,
            ],
            [
                '_id' => new MongoId('65cba6c741f93fcda893859c'),
                'branch_id' => '5addc25383266f65abf515c4',
                'namespace' => 'glofox',
                'active' => true,
                'type' => UserType::ADMIN,
                'first_name' => 'Test',
                'last_name' => 'Admin',
                'phone' => '5346789',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$ZThWbjF4bXljc' .
                    'jlRdGZQUQ$POBM2GtX7QecEKu95F+Skmfe1NzHgrxasdasdKwnsPX+FDeir',
                'description' => 'Test Admin',
                'bookable' => true,
            ],
        ];
    }

    private function forAnalyticsReports(): array
    {
        $branchId = ['5d794d5c7efab3813a2dfe58', '5d56d214be51ef060e38409c'];
        return [
            [
                '_id' => new MongoId('654b69c8abca805a744a9de7'),
                'branch_id' => $branchId,
                'namespace' => 'namespace-for-analytics-reports',
                'active' => true,
                'type' => UserType::ADMIN,
                'first_name' => 'admin',
                'last_name' => 'reports',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=3,p=2$ZThWbjF4bXljc' .
                    'jlRdGZQUQ$POBM2GtX7QecEKu95F+fkmfe1NzHgrxKwnsPX+FDeig',
            ],
        ];
    }

    private function forExecuteBookingCharge(): array
    {
        return [
            'Admin of the branch with No card payments allowed' => $this->createAdmin([
                '_id' => new MongoId('6565e8e8544668b278b0f5d2'),
                'branch_id' => '6565e7275e02799353dd1f0f',
                'namespace' => 'namespace-for-execute-booking-charge',
            ]),
            'Admin of the branch with card payments allowed' => $this->createAdmin([
                '_id' => new MongoId('6565e781513193858ebb95bb'),
                'branch_id' => '6565e781513193858ebb95bb',
                'namespace' => 'namespace-for-execute-booking-charge',
            ]),
            'User with No card payments allowed' => $this->createPaygMember([
                '_id' => new MongoId('6565e9601f3165fc147d5fc7'),
                'branch_id' => '6565e781513193858ebb95bb',
                'namespace' => 'namespace-for-execute-booking-charge',
            ]),
            'User with card payments allowed' => $this->createMemberWithValidSubscriptionTimeMembership([
                '_id' => new MongoId('6565ea039bc81f4674991e5f'),
                'branch_id' => '6565e781513193858ebb95bb',
                'namespace' => 'namespace-for-execute-booking-charge',
            ]),
        ];
    }

    private function forRecurringClasses(): array
    {
        $membership = [
            'branch_id' => self::GLOFOX_BRANCH_ID,
            'membership' => [
                '_id' => '5f159e696222aaa4d0ee4ce3',
                'type' => Type::TIME_CLASSES,
                'plan_code' => 'abc123def456',
                'user_membership_id' => 'user-membership-id',
            ],
        ];

        return [
            'Client with Restricted Membership, 10 Credits' => $this->createMemberWithValidSubscriptionTimeMembership(
                '6565e8e8544668b278b0f5d3',
                '<EMAIL>',
                $membership
            ),
            'Client with Restricted Membership, No Credits' => $this->createMemberWithValidSubscriptionTimeMembership(
                '6565e8e8544668b278b0f5d4',
                '<EMAIL>',
                $membership
            ),
            'Client with Restricted Membership, Expired Credits' => $this->createMemberWithValidSubscriptionTimeMembership(
                '6565e8e8544668b278b0f5d5',
                '<EMAIL>',
                $membership
            ),
            'Client with Restricted Membership, Mixed Credits' => $this->createMemberWithValidSubscriptionTimeMembership(
                '6565e8e8544668b278b0f5d6',
                '<EMAIL>',
                $membership
            ),
            'Client with Restricted Membership, No Credits, Next Cycle Test' => $this->createMemberWithValidSubscriptionTimeMembership(
                '6565e8e8544668b278b0f5d7',
                '<EMAIL>',
                [
                    'branch_id' => self::GLOFOX_BRANCH_ID,
                    'membership' => [
                        '_id' => '5f159e696222aaa4d0ee4ce3',
                        'type' => Type::TIME_CLASSES,
                        'plan_code' => '2cycle4bookings',
                        'user_membership_id' => 'user-membership-id',
                    ],
                ]
            ),
        ];
    }

    public function forValidNumberOfChildren(): array
    {
        $parentId = new MongoId('65cbd096f3cd2908596a5021');

        return [
            [
                '_id' => $parentId,
                'first_name' => 'test-parent-user',
                'last_name' => 'user-last-name',
                'branch_id' => self::GLOFOX_BRANCH_ID,
                "namespace" => 'glofox',
                'type' => UserType::MEMBER,
                'active' => true,
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$ZThWbjF4bXljc' .
                    'jlRdGZQUQ$POBM2GtX7QecEKu95F+Skmfe1NzHgrxKwnsPX+FDeiw',
                'description' => 'Miguel Admin',
                'bookable' => true,
                "origin_branch_id" => self::GLOFOX_BRANCH_ID,
            ],
            [
                '_id' => new MongoId('65cbc14ae8cac7529c691d65'),
                'first_name' => 'test-parent-child',
                'last_name' => 'child-last-name',
                "namespace" => "glofox",
                'parent_id' => (string) $parentId,
                'email' => '<EMAIL>',
            ],
        ];
    }

    private function forNamespaceUsersSearch(): array
    {
        return [
            $this->createPaygMember([
                '_id' => new MongoId('65eee32286b89e4b49c2c24b'),
                'branch_id' => '5addc25383266f65abf515c4',
                'namespace' => 'glofox',
                'first_name' => 'Namespace',
                'last_name' => 'Search Member',
                'name' => 'Namespace Search Member',
                'email' => '<EMAIL>',
            ]),
        ];
    }

    private function forHasAccessToBranch(): array
    {
        return [
            $this->createPaygMember([
                '_id' => new MongoId('65f1d4df96b13fe3c376e311'),
                'branch_id' => '65f1d4f17938c94639b652c6',
                'namespace' => 'has-access-to-branch',
            ]),
            $this->createPaygMember([
                '_id' => new MongoId('65f1d50ae9ab5f1e77eb641c'),
                'branch_id' => ['65f1d4f17938c94639b652c6'],
                'namespace' => 'has-access-to-branch',
            ]),
            $this->createPaygMember([
                '_id' => new MongoId('65f1d5294b3f1377379e46fa'),
                'branch_id' => '65f1d4f17938c94639b652c6',
                'namespace' => 'has-access-to-branch',
            ]),
            $this->createPaygMember([
                '_id' => new MongoId('65f1d547d9a4a2d914362b65'),
                'branch_id' => ['65f1d4f17938c94639b652c6', '65f1d531a689fb464251c9a7', '65f1d56e88a39a6b5f0a7a8e'],
                'namespace' => 'has-access-to-branch',
            ]),
            $this->createPaygMember([
                '_id' => new MongoId('65fc63b05d2cc15a2766f847'),
                'branch_id' => '65f1d56e88a39a6b5f0a7a8e',
                'namespace' => 'has-access-to-branch-2',
            ]),
            $this->createAdmin([
                '_id' => new MongoId('65fc67ac84f2a2610b7800b8'),
                'branch_id' => '65f1d531a689fb464251c9a7',
                'namespace' => 'has-access-to-branch',
            ]),
            $this->createAdmin([
                '_id' => new MongoId('65f36e10aebcf2dfbc23ec14'),
                'branch_id' => '65f1d56e88a39a6b5f0a7a8e',
                'namespace' => 'has-access-to-branch-2',
            ]),
            $this->createAdmin([
                '_id' => new MongoId('65f968a64e89ebe7f6a5d382'),
                'branch_id' => ['65f1d4f17938c94639b652c6', '65f1d531a689fb464251c9a7', '65f1d56e88a39a6b5f0a7a8e'],
                'namespace' => 'has-access-to-branch',
            ]),
            $this->createTrainer([
                '_id' => new MongoId('65fc298131d921ed729f36db'),
                'branch_id' => ['65f1d4f17938c94639b652c6', '65f1d531a689fb464251c9a7', '65f1d56e88a39a6b5f0a7a8e'],
                'namespace' => 'has-access-to-branch',
            ]),
        ];
    }
    private function forInteractions(): array
    {
        return [
            $this->createPaygMember([
                '_id' => new MongoId('661e3b96f537c06c1d2b7e99'),
                'branch_id' => '661e8a9093731688897988c6',
                'namespace' => 'test-branch-interactions-namespace',
            ]),
        ];
    }

    private function forEventBatchId(): array
    {
        return [
            $this->createPaygMember([
                '_id' => new MongoId('65f1d4df96b13fe3c376e312'),
                'branch_id' => self::GLOFOX_BRANCH_ID,
                'namespace' => 'glofox',
                'email' => '<EMAIL>',
            ]),
        ];
    }

    private function forBranchesUpsert(): array
    {
        return [
            $this->createAdmin([
                '_id' => new MongoId('663a516f3e07f042edc9faa2'),
                'branch_id' => '663a4c5c348b14dbfbd65e41',
                'namespace' => 'test-branch-edit-namespace',
            ]),
        ];
    }

    private function forActiveLimitBookings(): array
    {
        return [
            $this->createPaygMember([
                '_id' => new MongoId('66727f8956a5e42aa5294836'),
                'branch_id' => self::GLOFOX_BRANCH_ID,
                'namespace' => 'glofox',
                'email' => '<EMAIL>',
            ]),
            $this->createPaygMember([
                '_id' => new MongoId('6672886ecbde2a9cec5d5df2'),
                'branch_id' => self::GLOFOX_BRANCH_ID,
                'namespace' => 'glofox',
                'email' => '<EMAIL>',
            ]),
            $this->createPaygMember([
                '_id' => new MongoId('66728eef22db8a41d451fdad'),
                'branch_id' => self::GLOFOX_BRANCH_ID,
                'namespace' => 'glofox',
                'email' => '<EMAIL>',
            ]),
        ];
    }

    private function forGympassPaygDisabled(): array
    {
        return [
            $this->createAdmin([
                '_id' => new MongoId('66c8837fa1f2b3414415825b'),
                'branch_id' => '66c6f0a307ae75cf210410b9',
            ]),
        ];
    }

    private function forLeadStatusUpdateCapture(): array
    {
        return [
            [
                '_id' => new MongoId('66d5dd24593f62f6aaa921bf'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'origin_branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'type' => UserType::MEMBER,
                'first_name' => 'leads-status-test',
                'last_name' => 'leads-status-modified-test',
                'phone' => '111278921',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'lead_status' => \Glofox\Domain\Leads\Status::COLD,
                'membership' => [
                    'type' => 'payg',
                ],
                'leads' => [
                    'status' => Status::COLD,
                    'status_modified' => new \MongoDate(strtotime('2024-04-29T13:27:32.000+00:00')),

                ],
            ],
        ];
    }

    private function forNoSaleTrialStatus(): array
    {
        return [
            [
                '_id' => new MongoId('6745a6d2b6508cd0a943f7bd'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'origin_branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'type' => UserType::MEMBER,
                'first_name' => 'leads-status-new-status-test',
                'last_name' => 'leads-status-modified-test',
                'phone' => '1112789211',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'lead_status' => \Glofox\Domain\Leads\Status::NO_SALE_TRIAL,
                'membership' => [
                    'type' => 'payg',
                ],
                'leads' => [
                    'status' => Status::NO_SALE_TRIAL,
                    'status_modified' => new \MongoDate(strtotime('2024-04-29T13:27:32.000+00:00')),
                    'first_name' => 'no-sale-trial-first-name',
                    'last_name' => 'no-sale-trial-last-name',
                    'phone' => '1112789211',
                    'email' => '<EMAIL>',
                    'login' => '<EMAIL>',
                    'lead_status' => Status::NO_SALE_TRIAL,
                    'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                    'leads' => [
                        'status' => Status::NO_SALE_TRIAL,
                        'status_modified' => new \MongoDate(strtotime('2024-04-29T13:27:32.000+00:00')),
                    ],
                ],
            ],
        ];
    }

    private function forTourStatus(): array
    {
        return [
            [
                '_id' => new MongoId('674d9c7aaa2bbb3a1c1d16e5'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'origin_branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'type' => UserType::MEMBER,
                'first_name' => 'leads-status-new-status-test',
                'last_name' => 'leads-status-modified-test',
                'phone' => '1112789211',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg/',
                'lead_status' => \Glofox\Domain\Leads\Status::TOUR,
                'membership' => [
                    'type' => 'payg',
                ],
                'leads' => [
                    'status' => Status::TOUR,
                    'status_modified' => new \MongoDate(strtotime('2024-04-29T13:27:32.000+00:00')),
                ],
            ],
        ];
    }
    private function forNoSaleTourStatus(): array
    {
        return [
            [
                '_id' => new MongoId('674ee35a294a3b61056ef409'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'origin_branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'type' => UserType::MEMBER,
                'first_name' => 'leads-status-new-no-sale-trial-test',
                'last_name' => 'leads-status-new-no-sale-trial-test',
                'phone' => '1112789211',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg//',
                'lead_status' => \Glofox\Domain\Leads\Status::NO_SALE_TOUR,
                'membership' => [
                    'type' => 'payg',
                ],
                'leads' => [
                    'status' => Status::NO_SALE_TOUR,
                    'status_modified' => new \MongoDate(strtotime('2024-04-29T13:27:32.000+00:00')),
                ],
            ],
        ];
    }

    private function forLeadsWithMarketingAndContactResources()
    {
        return [
            [
                '_id' => new MongoId('87a7011a05c677bda916615a'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'origin_branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'type' => UserType::MEMBER,
                'name' => 'Test-Name',
                'active' => true,
                'first_name' => 'Test-first-name',
                'last_name' => 'Test-last-name',
                'description' => 'Worst Trainer Ever!',
                'email' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'login' => '<EMAIL>',
                'phone' => '123123321',
                'card_id' => null,
                'bookable' => true,
                'created' => new MongoDate(),
                'modified' => new MongoDate(),
                'last_login' => new MongoDate(),
                'leads' => [
                    'marketing_source' => 'Testsource',
                    'contact_source' => 'Testsource',
                ],
            ],
        ];
    }

    private function forMemberRestrictedProfileRules(): array
    {
        return [
            [
                '_id' => new MongoId('673f7cb2728a75c772e20b0d'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'origin_branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'type' => UserType::MEMBER,
                'name' => 'test-member-restricted-profile',
                'active' => true,
                'first_name' => 'test-first-name',
                'last_name' => 'test-last-name',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'phone' => '000000000',
            ],
        ];
    }

    private function forLeadStatusUpdateCaptureTrial(): array
    {
        return [
            [
                '_id' => new MongoId('66d6f2dc7a6bd8210e78a4f4'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'origin_branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'type' => UserType::MEMBER,
                'first_name' => 'leads-status-test-warm',
                'last_name' => 'leads-status-modified-test-warm',
                'phone' => '1112789123',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'lead_status' => \Glofox\Domain\Leads\Status::TRIAL,
                'membership' => [
                    'type' => 'payg',
                ],
                'leads' => [
                    'status' => Status::TRIAL,
                    'status_modified' => new \MongoDate(strtotime('2024-04-29T13:27:32.000+00:00')),

                ],
            ],
        ];
    }

    private function forEventBookingsTotal(): array
    {
        return [
            $this->createPaygMember([
                '_id' => new MongoId('66727f8956a5e42aa5294837'),
                'branch_id' => self::GLOFOX_BRANCH_ID,
                'namespace' => 'glofox',
                'email' => '<EMAIL>',
            ]),
        ];
    }

    private function forEventDeletedEventHandle(): array
    {
        return [
            [
                '_id' => new MongoId('111122223333444455556666'),
                'branch_id' => '666666777777888888000000',
                'namespace' => 'test-branch-event-deleted',
                'active' => true,
                'type' => UserType::ADMIN,
                'first_name' => 'Henry',
                'last_name' => 'Administrator',
                'phone' => \Illuminate\Support\Str::random(),
                'email' => $email = \Illuminate\Support\Str::random() . '@glofox.com',
                'login' => $email,
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$ZThWbjF4bXljc' .
                    'jlRdGZQUQ$POBM2GtX7QecEKu95F+Skmfe1NzHgrxKwnsPX+FDeig',
                'abilities' => [],
            ],
        ];
    }

    private function forRestrictedMembershipNoCreditsNextCycle(): array
    {
        return [
            $this->createBase([
                '_id' => '59cd0e4b2a04b921fb568cc6',
                'first_name' => 'Testing Subscriptions',
                'last_name' => 'with credits',
                'login' => '<EMAIL>',
                'email' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$VDkza2c4aU92VldaRnZraw$hERW8grxf9NI6X1GA0b7HYg14UFoR4whTfDdFkxTWh4',
                'membership' => [
                    '_id' => '5f159e696222aaa4d0ee4ce5',
                    'type' => 'time_classes',
                    'start_date' => new MongoDate(Carbon::today()->startOfMonth()->getTimestamp()),
                    'membership_group_id' => '54107aced7b6dd7aab8b4899',
                    'plan_code' => '2cycle4bookings',
                    'plan_price' => 10,
                    'boooked_events' => 0,
                    'expiry_date' => new MongoDate(Carbon::today()->endOfMonth()->getTimestamp()),
                    'user_membership_id' => '66d963d1026d066fab0632de',
                    'subscription' => [
                        'subscription_plan_id' => '59c8dc7c3ebaf48d6b0041a9',
                        'interval' => 'month',
                        'interval_count' => 1,
                        'price' => 10,
                        'upfront_fee' => 0,
                        'force_start' => false,
                        'credits' => [
                            [
                                'branch_id' => '49a7011a05c677b9a916612a',
                                'namespace' => 'glofox',
                                'active' => true,
                                'model' => 'programs',
                                'category_id' => null,
                                'num_sessions' => 1,
                                'plan_code' => '2cycle4bookings',
                                'model_ids' => null,
                                'expiry' => [
                                    'interval' => 'month',
                                    'interval_count' => 1,
                                ],
                                'end_date' => null,
                            ],
                        ],
                        'stripe_id' => 'sub_BSpsTBDgn8Rqs7',
                    ],
                ],
            ]),
        ];
    }

    private function forCreditConsumptionForIntegrators(): array
    {
        return [
            [
                '_id' => new MongoId('66e17d12f30e098e6a89e110'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'active' => true,
                'first_name' => 'Arantxa',
                'last_name' => 'Test',
                'email' => '<EMAIL>',
                'namespace' => 'glofox',
                'membership' => [
                    'type' => 'payg',
                ],
                'strike' => 0,
                'type' => UserType::MEMBER,
                'PAYGPAYMENT' => true,
                'password' => '$argon2i$v=19$m=4096,t=10,p=1$dw2dfjCGA8ri5DtEXWkHcQ$QD8/BKrOjxepww42v0T0vh3amZ7zcjsL90m5UQRDf0E',
            ],
            [
                '_id' => new MongoId('66e180ba580c2c731f267842'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'active' => true,
                'first_name' => 'Antonio',
                'last_name' => 'Test',
                'email' => '<EMAIL>',
                'namespace' => 'glofox',
                'membership' => [
                    'type' => 'payg',
                ],
                'strike' => 0,
                'type' => UserType::MEMBER,
                'PAYGPAYMENT' => true,
                'password' => '$argon2i$v=19$m=4096,t=10,p=1$dw2dfjCGA8ri5DtEXWkHcQ$QD8/BKrOjxepww42v0T0vh3amZ7zcjsL90m5UQRDf0F',
            ],
            [
                '_id' => new MongoId('66e1b5a8b5bda44ec0c9f8d6'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'active' => true,
                'first_name' => 'Clara',
                'last_name' => 'Test',
                'email' => '<EMAIL>',
                'namespace' => 'glofox',
                'membership' => [
                    'type' => 'payg',
                ],
                'strike' => 0,
                'type' => UserType::MEMBER,
                'PAYGPAYMENT' => true,
                'password' => '$argon2i$v=19$m=4096,t=10,p=1$dw2dfjCGA8ri5DtEXWkHcQ$QD8/BKrOjxepww42v0T0vh3amZ7zcjsL90m5UQRDf0G',
            ],
        ];
    }

    private function forGrantCreditsOnImport(): array
    {
        return [
            [
                '_id' => new MongoId('77e1b5a8b5bda44ec0c9f8d6'),
                'branch_id' => '99984ace2c8a824784ab4cc4',
                'origin_branch_id' => '99984ace2c8a824784ab4cc4',
                'active' => true,
                'first_name' => 'Clara',
                'last_name' => 'Test',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'namespace' => 'glofox',
                'membership' => [
                    'type' => 'payg'
                ],
                'strike' => 0,
                'type' => UserType::MEMBER,
                'password' => '$argon2i$v=19$m=4096,t=10,p=1$dw2dfjCGA8ri5DtEXWkHcQ$QD8/BKrOjxepww42v0T0vh3amZ7zcjsL90m5UQRDf0G',
            ],
            [
                '_id' => new MongoId('77e1b5a8b5bda44ec0c9f8d7'),
                'branch_id' => '99984ace2c8a824784ab4cc4',
                'origin_branch_id' => '99984ace2c8a824784ab4cc4',
                'active' => true,
                'first_name' => 'Andy',
                'last_name' => 'Test',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'namespace' => 'glofox',
                'membership' => [
                    'type' => 'payg'
                ],
                'strike' => 0,
                'type' => UserType::MEMBER,
                'password' => '$argon2i$v=19$m=4096,t=10,p=1$dw2dfjCGA8ri5DtEXWkHcQ$QD8/BKrOjxepww42v0T0vh3amZ7zcjsL90m5UQRDf0H',
            ],
            [
                '_id' => new MongoId('77e1b5a8b5bda44ec0c9f8d9'),
                'branch_id' => '99984ace2c8a824784ab4cc4',
                'origin_branch_id' => '99984ace2c8a824784ab4cc4',
                'active' => true,
                'first_name' => 'Clara',
                'last_name' => 'Test',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'namespace' => 'glofox',
                'membership' => [
                    'type' => 'payg'
                ],
                'strike' => 0,
                'type' => UserType::MEMBER,
                'password' => '$argon2i$v=19$m=4096,t=10,p=1$dw2dfjCGA8ri5DtEXWkHcQ$QD8/BKrOjxepww42v0T0vh3amZ7zcjsL90m5UQRDf0G',
            ],
        ];
    }

    private function forUserWithBranchThatHaveCorporateId(): array
    {
        return [
            [
                '_id' => new MongoId('77e1b5a8b5bda44ec0c9f8d8'),
                'branch_id' => '6594235cb31b79737d0b6c57',
                'active' => true,
                'first_name' => 'User with Branch Corporate ID',
                'last_name' => 'Test',
                'email' => '<EMAIL>',
                'namespace' => 'glofox',
                'strike' => 0,
                'type' => UserType::ADMIN,
                'PAYGPAYMENT' => true,
                'password' => '$argon2i$v=19$m=4096,t=10,p=1$dw2dfjCGA8ri5DtEXWkHcQ$QD8/BKrOjxepww42v0T0vh3amZ7zcjsL90m5UQRDf0E',
            ],
        ];
    }

    private function forSemiPrivateAppointments(): array
    {
        return [
            $this->createPaygMember([
                '_id' => new MongoId('eeeeeaaaaabbbbbccccc0000'),
                'branch_id' => self::GLOFOX_BRANCH_ID,
                'namespace' => 'glofox',
                'first_name' => 'Henry',
                'last_name' => 'Fixture',
                'name' => 'Henry Fixture',
                'email' => '<EMAIL>',
            ]),
            $this->createPaygMember([
                '_id' => new MongoId('eeeeeaaaaabbbbbccccc0001'),
                'branch_id' => self::GLOFOX_BRANCH_ID,
                'namespace' => 'glofox',
                'first_name' => 'Henry2',
                'last_name' => 'Fixture2',
                'name' => 'Henry2 Fixture2',
                'email' => '<EMAIL>',
            ]),
            $this->createPaygMember([
                '_id' => new MongoId('eeeeeaaaaabbbbbccccc0002'),
                'branch_id' => self::GLOFOX_BRANCH_ID,
                'namespace' => 'glofox',
                'first_name' => 'Henry2',
                'last_name' => 'Fixture2',
                'name' => 'Henry2 Fixture2',
                'email' => '<EMAIL>',
            ]),
        ];
    }

    private function forBranchesWithLiftCorpId(): array
    {
        return [
            [
                '_id' => new MongoId('77e1b5a8b5bda44ec0c9f876'),
                'branch_id' => '6598235cb21b79737d0b6c50',
                'active' => true,
                'first_name' => 'User with Branch Corporate ID LIFT',
                'last_name' => 'Test',
                'email' => '<EMAIL>',
                'namespace' => 'glofox',
                'strike' => 0,
                'type' => UserType::ADMIN,
                'PAYGPAYMENT' => true,
                'password' => '$argon2i$v=19$m=4096,t=10,p=1$dw2dfjCGA8ri5DtEXWkHcQ$QD8/BKrOjxepww42v0T0vh3amZ7zcjsL90m5UQRDf0E',
            ],
        ];
    }

    private function forBranchesWithoutLiftCorpId(): array
    {
        return [
            [
                '_id' => new MongoId('97e1b5a8b5bda66ec0c9f800'),
                'branch_id' => '8898235cb21b78837d0b6c21',
                'active' => true,
                'first_name' => 'User with Branch Corporate ID NON LIFT',
                'last_name' => 'Test',
                'email' => '<EMAIL>',
                'namespace' => 'glofox',
                'strike' => 0,
                'type' => UserType::ADMIN,
                'PAYGPAYMENT' => true,
                'password' => '$argon2i$v=19$m=4096,t=10,p=1$dw2dfjCGA8ri5DtEXWkHcQ$QD8/BKrOjxepww42v0T0vh3amZ7zcjsL90m5UQRDf0E',
            ],
        ];
    }

    private function forUsersAccessBarcodes(): array
    {
        return [
            [
                '_id' => new MongoId('77e1b5a8b5bda44ec0c9f8e9'),
                'branch_id' => '8898235cb21b78837d0b6c21',
                'active' => true,
                'first_name' => 'User with Branch Corporate ID NON LIFT',
                'last_name' => 'Test',
                'access_barcode' => '**********',
                'email' => '<EMAIL>',
                'namespace' => 'glofox',
                'strike' => 0,
                'type' => UserType::MEMBER,
            ],
            [
                '_id' => new MongoId('74e1b5a8b5bda44ec0c9f8e0'),
                'branch_id' => '8898235cb21b78837d0b6c21',
                'active' => true,
                'first_name' => 'User Dublicate Barcode',
                'last_name' => 'Test',
                'access_barcode' => '**********',
                'email' => '<EMAIL>',
                'namespace' => 'glofox',
                'strike' => 0,
                'type' => UserType::MEMBER,
            ],
            [
                '_id' => new MongoId('74e9b5a8b5bda44ec0c9f8e0'),
                'branch_id' => '8898235cb21b78837d0b6c21',
                'active' => true,
                'first_name' => 'No barcode',
                'last_name' => 'Test',
                'access_barcode' => '',
                'email' => '<EMAIL>',
                'namespace' => 'glofox',
                'strike' => 0,
                'type' => UserType::MEMBER,
            ],
        ];
    }

    private function forMembershipActivatedAssignsCreditsForRestrictedMemberships(): array
    {
        return [
            [
                '_id' => new MongoId('77e1b5a8b5bda44ec0c9f8e0'),
                'branch_id' => '99984ace2c8a824784ab4cc4',
                'origin_branch_id' => '99984ace2c8a824784ab4cc4',
                'active' => true,
                'first_name' => 'Test',
                'last_name' => 'Test',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'namespace' => 'glofox',
                'membership' => [
                    '_id' => '5f159e696222aaa4d0ee4df5',
                    'type' => 'time_classes',
                    'start_date' => new MongoDate(Carbon::tomorrow()->startOfMonth()->getTimestamp()),
                    'membership_group_id' => null,
                    'plan_code' => '2cycle4bookings',
                    'plan_price' => 10,
                    'status' => 'ACTIVE',
                    'booked_events' => 0,
                    'expiry_date' => new MongoDate(Carbon::tomorrow()->endOfMonth()->getTimestamp()),
                    'user_membership_id' => '54107aced7b6dd7aab8b6999',
                    'subscription' => [
                        'subscription_plan_id' => '59c8dc7c3ebaf48d6b0041a9',
                        'interval' => 'month',
                        'interval_count' => 1,
                        'price' => 10,
                        'upfront_fee' => 0,
                        'force_start' => false,
                        'credits' => [
                            [
                                'branch_id' => '99984ace2c8a824784ab4cc4',
                                'namespace' => 'glofox',
                                'active' => true,
                                'model' => 'programs',
                                'category_id' => null,
                                'num_sessions' => 1,
                                'plan_code' => '2cycle4bookings',
                                'model_ids' => null,
                                'expiry' => [
                                    'interval' => 'month',
                                    'interval_count' => 1,
                                ],
                                'end_date' => null,
                            ],
                        ],
                        'stripe_id' => 'sub_BSpsTBDgn8Rqs7',
                    ],
                ],
                'strike' => 0,
                'type' => UserType::MEMBER,
                'PAYGPAYMENT' => true,
                'password' => '$argon2i$v=19$m=4096,t=10,p=1$dw2dfjCGA8ri5DtEXWkHcQ$QD8/BKrOjxepww42v0T0vh3amZ7zcjsL90m5UQRDf0E',
            ],
        ];
    }

    private function forAssignCreditsOnMembershipPurchasedEvent(): array
    {
        return [
            [
                '_id' => new MongoId('99e1b5a8b5bda44ec0c9f8d7'),
                'branch_id' => '99984ace2c8a824784ab4cc4',
                'origin_branch_id' => '99984ace2c8a824784ab4cc4',
                'active' => true,
                'first_name' => 'PlanCode',
                'last_name' => 'IsGettingFixed',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'namespace' => 'glofox',
                'membership' => [
                    'type' => 'payg'
                ],
                'strike' => 0,
                'type' => UserType::MEMBER,
                'password' => '$argon2i$v=19$m=4096,t=10,p=1$dw2dfjCGA8ri5DtEXWkHcQ$QD8/BKrOjxepww42v0T0vh3amZ7zcjsL90m5UQRDf0H',
            ]
        ];
    }

    private function forEventsUpdate(): array
    {
        $branchId = '67425ec9a14b1b40651b4990';

        return [
            [
                '_id' => new MongoId('67425eecfc4508dbeff26724'),
                'branch_id' => '67425b982a70f7afae14dc75',
                'namespace' => 'events-update-plus-utc',
                'active' => true,
                'categories' => [],
                'type' => 'TRAINER',
                'first_name' => 'Trainer',
                'last_name' => 'Test',
                'phone' => '08933221830',
                'description' => 'Trainer test',
                'email' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/TGa',
                'bookable' => true,
                'login' => '<EMAIL>',
                'gender' => 'M',
                'birth' => new MongoDate(strtotime('1984-10-10 00:00:00')),
                'created' => new MongoDate(),
                'modified' => new MongoDate(),
                'last_login' => new MongoDate(),
            ],
            [
                '_id' => new MongoId('674267e2e765beb37def5fd1'),
                'branch_id' => '67425b982a70f7afae14dc75',
                'active' => true,
                'first_name' => 'Plus UTC',
                'last_name' => 'Test',
                'access_barcode' => '',
                'email' => '<EMAIL>',
                'namespace' => 'glofox',
                'strike' => 0,
                'type' => UserType::MEMBER,
            ],
            [
                '_id' => new MongoId('67425efa1d395d128dde1ef2'),
                'branch_id' => $branchId,
                'namespace' => 'events-update-minus-utc',
                'active' => true,
                'categories' => [],
                'type' => 'TRAINER',
                'first_name' => 'Trainer',
                'last_name' => 'Test',
                'phone' => '08933221845',
                'description' => 'Trainer test',
                'email' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlUQzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/TGb',
                'bookable' => true,
                'login' => '<EMAIL>',
                'gender' => 'M',
                'birth' => new MongoDate(strtotime('1982-10-10 00:00:00')),
                'created' => new MongoDate(),
                'modified' => new MongoDate(),
                'last_login' => new MongoDate(),
            ],
            [
                '_id' => new MongoId('6742680dff4eefeb139d4347'),
                'branch_id' => $branchId,
                'active' => true,
                'first_name' => 'Minus UTC',
                'last_name' => 'Test',
                'access_barcode' => '',
                'email' => '<EMAIL>',
                'namespace' => 'glofox',
                'strike' => 0,
                'type' => UserType::MEMBER,
            ],
            [
                '_id' => new MongoId('6742681581c1229f515974b6'),
                'branch_id' => $branchId,
                'active' => true,
                'first_name' => 'Minus UTC 2',
                'last_name' => 'Test',
                'access_barcode' => '',
                'email' => '<EMAIL>',
                'namespace' => 'glofox',
                'strike' => 0,
                'type' => UserType::MEMBER,
            ],
        ];
    }
          
    private function forRemoveSubscriptionOnMembershipUpdate(): array
    {
        return [
            [
                '_id' => new MongoId('77e1b5a8b5bda44ec0c9f8e1'),
                'branch_id' => '99984ace2c8a824784ab4cc4',
                'origin_branch_id' => '99984ace2c8a824784ab4cc4',
                'active' => true,
                'first_name' => 'Test',
                'last_name' => 'Test',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'namespace' => 'glofox',
                'membership' => [
                    '_id' => '5f159e696222aaa4d0ee4df5',
                    'type' => 'time_classes',
                    'start_date' => new MongoDate(Carbon::tomorrow()->startOfMonth()->getTimestamp()),
                    'membership_group_id' => null,
                    'plan_code' => '2cycle4bookings',
                    'plan_price' => 10,
                    'status' => 'ACTIVE',
                    'booked_events' => 0,
                    'expiry_date' => new MongoDate(Carbon::tomorrow()->endOfMonth()->getTimestamp()),
                    'user_membership_id' => '54107aced7b6dd7aab8b6999',
                    'subscription' => [
                        'credits' => [
                            [
                                'branch_id' => '99984ace2c8a824784ab4cc4',
                                'namespace' => 'glofox',
                                'active' => true,
                                'model' => 'programs',
                                'category_id' => null,
                                'num_sessions' => 1,
                                'plan_code' => '2cycle4bookings',
                                'model_ids' => null,
                                'expiry' => [
                                    'interval' => 'month',
                                    'interval_count' => 1,
                                ],
                                'end_date' => null,
                            ],
                        ],
                    ],
                ],
                'strike' => 0,
                'type' => UserType::MEMBER,
                'PAYGPAYMENT' => true,
                'password' => '$argon2i$v=19$m=4096,t=10,p=1$dw2dfjCGA8ri5DtEXWkHcQ$QD8/BKrOjxepww42v0T0vh3amZ7zcjsL90m5UQRDf0E',
            ],
        ];
    }

    private function forShouldAllowCancelSubscriptionForFutureMembership(): array
    {
        return [
            [
                '_id' => new MongoId('77e1b5a8b5bda44ec0c9f8e2'),
                'branch_id' => '99984ace2c8a824784ab4cc4',
                'origin_branch_id' => '99984ace2c8a824784ab4cc4',
                'active' => true,
                'first_name' => 'PlanCode',
                'last_name' => 'IsGettingFixed',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'namespace' => 'glofox',
                'membership' => [
                    '_id' => '5f159e696222aaa4d0ee4df5',
                    'type' => 'time_classes',
                    'start_date' => new MongoDate(Carbon::tomorrow()->startOfMonth()->getTimestamp()),
                    'membership_group_id' => null,
                    'plan_code' => '2cycle4bookings',
                    'plan_price' => 10,
                    'status' => 'FUTURE',
                    'booked_events' => 0,
                    'expiry_date' => new MongoDate(Carbon::tomorrow()->endOfMonth()->getTimestamp()),
                    'user_membership_id' => '54107aced7b6dd7aab8b6999',
                    'subscription' => [
                        'credits' => [
                            [
                                'branch_id' => '99984ace2c8a824784ab4cc4',
                                'namespace' => 'glofox',
                                'active' => true,
                                'model' => 'programs',
                                'category_id' => null,
                                'num_sessions' => 1,
                                'plan_code' => '2cycle4bookings',
                                'model_ids' => null,
                                'expiry' => [
                                    'interval' => 'month',
                                    'interval_count' => 1,
                                ],
                                'end_date' => null,
                            ],
                        ],
                    ],
                ],
                'strike' => 0,
                'type' => UserType::MEMBER,
                'password' => '$argon2i$v=19$m=4096,t=10,p=1$dw2dfjCGA8ri5DtEXWkHcQ$QD8/BKrOjxepww42v0T0vh3amZ7zcjsL90m5UQRDf0H',
            ]
        ];
    }

    private function forGetAllBookings(): array
    {
        return [
            $this->createAdmin([
                '_id' => new MongoId('6748276aa77c5601e7052a89'),
                'branch_id' => '6745f7abd22f105c2aac5693',
                'namespace' => 'get-all-bookings',
            ]),
        ];
    }

    private function forLeadSources(): array
    {
        return [
            $this->createAdmin([
                '_id' => new MongoId('67487b2beb63195bc8f14fe0'),
                'branch_id' => '67485c9c1b4ba3fcf196b337',
                'namespace' => 'test-lead-sources',
                'email' => '<EMAIL>'
            ]),
            $this->createAdmin([
                '_id' => new MongoId('67487b390eb1acec6af2a7ea'),
                'branch_id' => '67485cbfffe518fbe2e18489',
                'namespace' => 'test-lead-sources-2',
                'email' => '<EMAIL>'
            ]),
        ];
    }

    private function forGetMembers(): array
    {
        return [
            $this->createAdmin([
                '_id' => new MongoId('675acdbe6e0c59e64521eeae'),
                'branch_id' => '675acdcdf1aef12a46e66bcc',
                'namespace' => 'test-get-members',
                'email' => '<EMAIL>'
            ]),
            $this->createBase([
                '_id' => '675acdc3bcbf36561aaac0fd',
                'branch_id' => '675acdcdf1aef12a46e66bcc',
                'namespace' => 'test-get-members',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'membership' => [
                    'type' => 'payg',
                ],
            ]),
            $this->createBase([
                '_id' => '675acdc70b53b54da1c0c659',
                'branch_id' => '675acdcdf1aef12a46e66bcc',
                'namespace' => 'test-get-members',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'membership' => [
                    'type' => 'payg',
                ],
            ]),
        ];
    }

    private function forResetDOFBCredits(): array
    {
        return [
            [
                '_id' => new MongoId('77e1b5a8b5bda44ec0c9c0e2'),
                'branch_id' => '99984ace2c8a824784ab4cc4',
                'origin_branch_id' => '99984ace2c8a824784ab4cc4',
                'active' => true,
                'first_name' => 'ResetDOFB',
                'last_name' => 'IsGettingFixed',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'namespace' => 'glofox',
                'membership' => [
                    '_id' => '5f159e696222aaa4d0ee4df5',
                    'type' => 'time_classes',
                    'start_date' => new MongoDate(Carbon::tomorrow()->startOfMonth()->getTimestamp()),
                    'membership_group_id' => null,
                    'plan_code' => '2cycle4bookings',
                    'plan_price' => 10,
                    'status' => 'ACTIVE',
                    'booked_events' => 0,
                    'expiry_date' => new MongoDate(Carbon::tomorrow()->endOfMonth()->getTimestamp()),
                    'user_membership_id' => '54107aced7b6dd7aab8b6999',
                    'subscription' => [
                        'credits' => [
                            [
                                'branch_id' => '99984ace2c8a824784ab4cc4',
                                'namespace' => 'glofox',
                                'active' => true,
                                'model' => 'programs',
                                'category_id' => null,
                                'num_sessions' => 1,
                                'plan_code' => '2cycle4bookings',
                                'model_ids' => null,
                                'expiry' => [
                                    'interval' => 'month',
                                    'interval_count' => 1,
                                ],
                                'end_date' => null,
                            ],
                        ],
                    ],
                ],
                'strike' => 0,
                'type' => UserType::MEMBER,
                'password' => '$argon2i$v=19$m=4096,t=10,p=1$dw2dfjCGA8ri5DtEXWkHcQ$QD8/BKrOjxepww42v0T0vh3amZ7zcjsL90m5UQRDf0H',
            ]
        ];
    }

    public function forWaitingListEmailOnRoaming(): array
    {
        return [
            $this->createBase([
                '_id' => '675acdc70b53b54da1c0c660',
                'namespace' => 'glofox',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'membership' => [
                    'type' => 'payg',
                ],
                'branch_id' => [
                    '49a7011a05c677b9a916612a',
                    '49a7011a05c677b9a916612c',
                    '49a7011a05c677b9a916612b',
                ],
                'origin_branch_id' => '49a7011a05c677b9a916612b'
            ])
        ];
    }

    public function forCreditsHealing(): array
    {
        return [
            [
                '_id' => new MongoId('77c2e5a8b5bda44ec0c9c0e2'),
                'branch_id' => self::GLOFOX_BRANCH_ID,
                'origin_branch_id' => self::GLOFOX_BRANCH_ID,
                'active' => true,
                'first_name' => 'Credits',
                'last_name' => 'Healing',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'namespace' => 'glofox',
                'membership' => [
                    '_id' => '5f159e696222bbb4d0ee4df5',
                    'type' => 'time_classes',
                    'start_date' => Carbon::parse('2025-01-31 00:00:00')->toDateTimeString(),
                    'membership_group_id' => null,
                    'plan_code' => '2cycle4bookings',
                    'plan_price' => 10,
                    'status' => 'ACTIVE',
                    'booked_events' => 0,
                    'expiry_date' => Carbon::parse('2025-02-27 23:59:59')->toDateTimeString(),
                    'user_membership_id' => '54107aced7b6dd7aab8b6999',
                    'subscription' => [
                        'interval' => 'month',
                        'interval_count' => 1,
                        'credits' => [
                            [
                                'branch_id' => self::GLOFOX_BRANCH_ID,
                                'namespace' => 'glofox',
                                'active' => true,
                                'model' => 'programs',
                                'category_id' => null,
                                'num_sessions' => 1,
                                'plan_code' => '2cycle4bookings',
                                'model_ids' => null,
                                'expiry' => [
                                    'interval' => 'month',
                                    'interval_count' => 1,
                                ],
                                'end_date' => null,
                            ],
                        ],
                    ],
                ],
                'strike' => 0,
                'type' => UserType::MEMBER,
                'password' => '$argon2i$v=19$m=4096,t=10,p=1$dw2dfjCGA8ri5DtEXWkHcQ$QD8/BKrOjxepww42v0T0vh3amZ7zcjsL90m5UQRDf0H',
            ]
        ];
    }

    private function forRestrictedMembershipRenewal(): array
    {
        return [
            [
                '_id' => new MongoId('75c2e5a8b5bda44ec0c9c1c3'),
                'branch_id' => self::GLOFOX_BRANCH_ID,
                'origin_branch_id' => self::GLOFOX_BRANCH_ID,
                'active' => true,
                'first_name' => 'Membership',
                'last_name' => 'Renewal',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'namespace' => 'glofox',
                'membership' => [
                    '_id' => '54107c1cd7b6ddc3a98b4577',
                    'type' => 'time_classes',
                    'start_date' => Carbon::parse('2025-01-31 00:00:00')->toDateTimeString(),
                    'membership_group_id' => null,
                    'plan_code' => '1506335805020',
                    'plan_price' => 10,
                    'status' => 'ACTIVE',
                    'booked_events' => 0,
                    'expiry_date' => Carbon::parse('2025-02-27 23:59:59')->toDateTimeString(),
                    'user_membership_id' => '54107aced7b6dd7ccb8c6999',
                    'subscription' => [
                        'subscription_plan_id' => '59e63a1773fa6cf751723246',
                        'interval' => 'month',
                        'interval_count' => 1,
                        'credits' => [
                            [
                                'branch_id' => self::GLOFOX_BRANCH_ID,
                                'namespace' => 'glofox',
                                'active' => true,
                                'model' => 'programs',
                                'category_id' => null,
                                'num_sessions' => 1,
                                'plan_code' => '1506335805020',
                                'model_ids' => null,
                                'expiry' => [
                                    'interval' => 'month',
                                    'interval_count' => 1,
                                ],
                                'end_date' => null,
                            ],
                        ],
                    ],
                ],
                'strike' => 0,
                'type' => UserType::MEMBER,
                'password' => '$argon2i$v=19$m=4096,t=10,p=1$dw2dfjCGA8ri5DtEXWkHcQ$QD8/BKrOjxepww42v0T0vh3amZ7zcjsL90m5UQRDf0H',
            ]
        ];
    }

    private function forOverdueMembershipsBookings(): array
    {
        return [
            [
                '_id' => new MongoId('77e1b5a8b5bda44ec0c9c0e3'),
                'branch_id' => self::GLOFOX_BRANCH_ID,
                'origin_branch_id' => self::GLOFOX_BRANCH_ID,
                'active' => true,
                'first_name' => 'Joe',
                'last_name' => 'Active Membership',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'namespace' => 'glofox',
                'membership' => [
                    '_id' => '54107c1cd7b6ddc3a98b4577',
                    'type' => 'time',
                    'start_date' => Carbon::parse('2024-11-01 00:00:00')->toDateTimeString(),
                    'membership_group_id' => null,
                    'plan_code' => '1506335805020',
                    'plan_price' => 0,
                    'status' => \Glofox\Domain\Memberships\Status::ACTIVE,
                    'booked_events' => 0,
                    'expiry_date' => Carbon::parse('2025-02-27 23:59:59')->toDateTimeString(),
                    'user_membership_id' => '54107aced7b6dd7ccb8c6999',
                    'subscription' => [
                        'subscription_plan_id' => '59e63a1773fa6cf751723246',
                        'interval' => 'month',
                        'interval_count' => 1,
                        'credits' => [],
                    ],
                ],
                'strike' => 0,
                'type' => UserType::MEMBER,
            ],
            [
                '_id' => new MongoId('77c2e5a8b5bda44ec0c9c0f1'),
                'branch_id' => self::GLOFOX_BRANCH_ID,
                'origin_branch_id' => self::GLOFOX_BRANCH_ID,
                'active' => true,
                'first_name' => 'Joe',
                'last_name' => 'Locked Membership',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'namespace' => 'glofox',
                'membership' => [
                    '_id' => '54107c1cd7b6ddc3a98b4577',
                    'type' => 'time',
                    'start_date' => Carbon::parse('2024-11-01 00:00:00')->toDateTimeString(),
                    'membership_group_id' => null,
                    'plan_code' => '1506335805020',
                    'plan_price' => 50,
                    'status' => \Glofox\Domain\Memberships\Status::LOCKED,
                    'booked_events' => 0,
                    'expiry_date' => Carbon::parse('2025-02-27 23:59:59')->toDateTimeString(),
                    'user_membership_id' => '54107aced7b6dd7ccb8c6999',
                    'subscription' => [
                        'subscription_plan_id' => '59e63a1773fa6cf751723246',
                        'interval' => 'month',
                        'interval_count' => 1,
                        'credits' => [],
                    ],
                ],
                'strike' => 0,
                'type' => UserType::MEMBER,
            ]
        ];
    }

    private function forCreditsDuplicatesOnRenewal(): array
    {
        return [
            [
                '_id' => new MongoId('77107c1cd7b6ddc3a98b4577'),
                'branch_id' => self::GLOFOX_BRANCH_ID,
                'origin_branch_id' => self::GLOFOX_BRANCH_ID,
                'active' => true,
                'first_name' => 'Credit',
                'last_name' => 'Duplicates',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'namespace' => 'glofox',
                'membership' => [
                    '_id' => '54107c1cd7b6ddc3a98b4577',
                    'branch_id' => self::GLOFOX_BRANCH_ID,
                    'type' => 'time_classes',
                    'start_date' => Carbon::today()->startOfDay()->toDateTimeString(),
                    'membership_group_id' => null,
                    'plan_code' => '1506335805020',
                    'plan_price' => 10,
                    'status' => 'ACTIVE',
                    'booked_events' => 0,
                    'expiry_date' => Carbon::today()->subDay()->endOfDay()->toDateTimeString(),
                    'user_membership_id' => '54107aced7b6dd7ccb8c6999',
                    'subscription' => [
                        'subscription_plan_id' => '59e63a1773fa6cf751723246',
                        'interval' => 'month',
                        'interval_count' => 1,
                        'duration' => 0,
                        'credits' => [
                            [
                                'branch_id' => self::GLOFOX_BRANCH_ID,
                                'namespace' => 'glofox',
                                'active' => true,
                                'model' => 'programs',
                                'category_id' => null,
                                'num_sessions' => 1,
                                'plan_code' => '1506335805020',
                                'model_ids' => null,
                                'expiry' => [
                                    'interval' => 'month',
                                    'interval_count' => 1,
                                ],
                                'end_date' => null,
                            ],
                        ],
                    ],
                ],
                'strike' => 0,
                'type' => UserType::MEMBER,
                'password' => '$argon2i$v=19$m=4096,t=10,p=1$dw2dfjCGA8ri5DtEXWkHcQ$QD8/BKrOjxepww42v0T0vh3amZ7zcjsL90m5UQRDf0H',
            ]
        ];
    }

    private function forUserCreditsHistory(): array
    {
        return [
            [
                '_id' => new MongoId('77197c1cd7b6ddc3a98b4587'),
                'branch_id' => self::GLOFOX_BRANCH_ID,
                'origin_branch_id' => self::GLOFOX_BRANCH_ID,
                'active' => true,
                'first_name' => 'Credit',
                'last_name' => 'Duplicates',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'namespace' => 'glofox',
                'membership' => [
                    '_id' => '54107c1cd7b6ddc3a98b4577',
                    'branch_id' => self::GLOFOX_BRANCH_ID,
                    'type' => 'time_classes',
                    'start_date' => Carbon::today()->startOfDay()->toDateTimeString(),
                    'membership_group_id' => null,
                    'plan_code' => '1506335805020',
                    'plan_price' => 10,
                    'status' => 'ACTIVE',
                    'booked_events' => 0,
                    'expiry_date' => Carbon::today()->subDay()->endOfDay()->toDateTimeString(),
                    'user_membership_id' => '54107aced7b6dd7ccb8c6999',
                    'subscription' => [
                        'subscription_plan_id' => '59e63a1773fa6cf751723246',
                        'interval' => 'month',
                        'interval_count' => 1,
                        'duration' => 0,
                        'credits' => [
                            [
                                'branch_id' => self::GLOFOX_BRANCH_ID,
                                'namespace' => 'glofox',
                                'active' => true,
                                'model' => 'programs',
                                'category_id' => null,
                                'num_sessions' => 1,
                                'plan_code' => '1506335805020',
                                'model_ids' => null,
                                'expiry' => [
                                    'interval' => 'month',
                                    'interval_count' => 1,
                                ],
                                'end_date' => null,
                            ],
                        ],
                    ],
                ],
                'strike' => 0,
                'type' => UserType::MEMBER,
                'password' => '$argon2i$v=19$m=4096,t=10,p=1$dw2dfjCGA8ri5DtEXWkHcQ$QD8/BKrOjxepww42v0T0vh3amZ7zcjsL90m5UQRDf0H',
            ]
        ];
    }

    public function forUserCreditsPermanentDeletion(): array
    {
        return [
            $this->createBase([
                '_id' => '77207c1cd7b6ddc3a98b4587',
                'namespace' => 'glofox',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'membership' => [
                    '_id' => '54107c1cd7b6ddc3a98b4577',
                    'branch_id' => self::GLOFOX_BRANCH_ID,
                    'type' => 'time_classes',
                    'start_date' => new MongoDate(Carbon::parse('2025-04-01')->startOfDay()->getTimestamp()),
                    'membership_group_id' => null,
                    'plan_code' => '1506335805020',
                    'plan_price' => 10,
                    'status' => 'ACTIVE',
                    'booked_events' => 0,
                    'expiry_date' => new MongoDate(Carbon::parse('2025-05-01')->endOfDay()->getTimestamp()),
                    'user_membership_id' => '54107aced7b6dd7ccb8c6999',
                    'subscription' => [
                        'subscription_plan_id' => '59e63a1773fa6cf751723246',
                        'interval' => 'month',
                        'interval_count' => 1,
                        'duration' => 0,
                        'credits' => [
                            [
                                'branch_id' => self::GLOFOX_BRANCH_ID,
                                'namespace' => 'glofox',
                                'active' => true,
                                'model' => 'programs',
                                'category_id' => null,
                                'num_sessions' => 1,
                                'plan_code' => '1506335805020',
                                'model_ids' => null,
                                'expiry' => [
                                    'interval' => 'month',
                                    'interval_count' => 1,
                                ],
                                'end_date' => null,
                            ],
                        ],
                    ],
                ],
                'branch_id' => self::GLOFOX_BRANCH_ID,
                'origin_branch_id' => self::GLOFOX_BRANCH_ID
            ])
        ];
    }

    private function forValidationSupportedCategoryOrClasses(): array
    {
        return [
            $this->createBase([
                '_id' => '77207c1cd7b6ddc3a98b4588',
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'membership' => [
                    '_id' => '54107c1cd7b6ddc3a98b4577',
                    'user_membership_id' => '5cf91831b3cb1f0001a6f4a4',
                    'type' => 'time',
                    'plan_code' => '1506335805840',
                    'start_date' => new MongoDate(Carbon::parse('2025-04-01')->startOfDay()->getTimestamp()),
                    'expiry_date' => new MongoDate(Carbon::parse('2025-04-20')->endOfDay()->getTimestamp()),
                ],
            ]),
            $this->createBase([
                '_id' => '77207c1cd7b6ddc3a98b4589',
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'membership' => [
                    '_id' => '54107c1cd7b6ddc3a98b4577',
                    'user_membership_id' => '5cf91831b3cb1f0001a6f4a4',
                    'type' => 'time',
                    'plan_code' => '1506335805840',
                    'start_date' => new MongoDate(Carbon::parse('2025-02-10')->startOfDay()->getTimestamp()),
                    'expiry_date' => new MongoDate(Carbon::parse('2025-03-10')->endOfDay()->getTimestamp()),
                    'subscription' => [
                        'subscription_plan_id' => '59e63a1773fa6cf751723246',
                        'interval' => 'month',
                        'interval_count' => 1,
                        'duration' => 0,
                        'credits' => [
                            [
                                'branch_id' => self::GLOFOX_BRANCH_ID,
                                'namespace' => 'glofox',
                                'active' => true,
                                'model' => 'programs',
                                'category_id' => null,
                                'num_sessions' => 10,
                                'plan_code' => '1506335805020',
                                'model_ids' => null,
                                'expiry' => [
                                    'interval' => 'month',
                                    'interval_count' => 1,
                                ],
                                'end_date' => null,
                            ],
                        ],
                    ],
                ],
            ]),
        ];
    }

    private function forSubscriptionRenewalCreditSourceUpdater(): array
    {
        return [
            $this->createBase([
                '_id' => '77207c1cd7b6ddc3a98b4590',
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'membership' => [
                    '_id' => '54107c1cd7b6ddc3a98b4577',
                    'user_membership_id' => '5cf91831b3cb1f0001a6f4a4',
                    'branch_id' => '49a7011a05c677b9a916612a',
                    'type' => 'time_classes',
                    'plan_code' => '1506335805840',
                    'start_date' => new MongoDate(Carbon::parse('2025-02-20')->startOfDay()->getTimestamp()),
                    'expiry_date' => new MongoDate(Carbon::parse('2025-03-20')->endOfDay()->getTimestamp()),
                    'subscription' => [
                        'subscription_plan_id' => '59e63a1773fa6cf751723246',
                        'interval' => 'month',
                        'interval_count' => 1,
                        'duration' => 0,
                        'credits' => [
                            [
                                'branch_id' => self::GLOFOX_BRANCH_ID,
                                'namespace' => 'glofox',
                                'active' => true,
                                'model' => 'programs',
                                'category_id' => null,
                                'num_sessions' => 10,
                                'plan_code' => '1506335805840',
                                'model_ids' => null,
                                'expiry' => [
                                    'interval' => 'month',
                                    'interval_count' => 1,
                                ],
                                'end_date' => null,
                            ],
                        ],
                    ],
                ],
            ]),
            $this->createBase([
                '_id' => '77207c1cd7b6ddc3a98b4591',
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'membership' => [
                    '_id' => '54107c1cd7b6ddc3a98b4577',
                    'user_membership_id' => '5cf91831b3cb1f0001a6f4a4',
                    'branch_id' => '49a7011a05c677b9a916612a',
                    'type' => 'time_classes',
                    'plan_code' => '1506335805840',
                    'start_date' => new MongoDate(Carbon::parse('2025-03-20')->startOfDay()->getTimestamp()),
                    'expiry_date' => new MongoDate(Carbon::parse('2025-04-20')->endOfDay()->getTimestamp()),
                    'subscription' => [
                        'subscription_plan_id' => '59e63a1773fa6cf751723246',
                        'interval' => 'month',
                        'interval_count' => 1,
                        'duration' => 0,
                        'credits' => [
                            [
                                'branch_id' => self::GLOFOX_BRANCH_ID,
                                'namespace' => 'glofox',
                                'active' => true,
                                'model' => 'programs',
                                'category_id' => null,
                                'num_sessions' => 10,
                                'plan_code' => '1506335805840',
                                'model_ids' => null,
                                'expiry' => [
                                    'interval' => 'month',
                                    'interval_count' => 1,
                                ],
                                'end_date' => null,
                            ],
                        ],
                    ],
                ],
            ]),
            $this->createBase([
                '_id' => '77207c1cd7b6ddc3a98b4592',
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'membership' => [
                    '_id' => '54107c1cd7b6ddc3a98b4577',
                    'user_membership_id' => '5cf91831b3cb1f0001a6f4a4',
                    'branch_id' => '49a7011a05c677b9a916612a',
                    'type' => 'time_classes',
                    'plan_code' => '1506335805840',
                    'start_date' => new MongoDate(Carbon::parse('2025-02-20')->startOfDay()->getTimestamp()),
                    'expiry_date' => new MongoDate(Carbon::parse('2025-03-20')->endOfDay()->getTimestamp()),
                    'subscription' => [
                        'subscription_plan_id' => '59e63a1773fa6cf751723246',
                        'interval' => 'month',
                        'interval_count' => 1,
                        'duration' => 0,
                        'credits' => [
                            [
                                'branch_id' => self::GLOFOX_BRANCH_ID,
                                'namespace' => 'glofox',
                                'active' => true,
                                'model' => 'programs',
                                'category_id' => null,
                                'num_sessions' => 10,
                                'plan_code' => '1506335805840',
                                'model_ids' => null,
                                'expiry' => [
                                    'interval' => 'month',
                                    'interval_count' => 1,
                                ],
                                'end_date' => null,
                            ],
                        ],
                    ],
                ],
            ]),
            $this->createBase([
                '_id' => '778b421cddc3a95917b6d07c',
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'membership' => [
                    '_id' => '54107c1cd7b6ddc3a98b4577',
                    'user_membership_id' => '5cb1cf9b3f00a4183101a6f4',
                    'branch_id' => '49a7011a05c677b9a916612a',
                    'type' => 'time_classes',
                    'plan_code' => '1506335805840',
                    'start_date' => new MongoDate(Carbon::parse('2025-03-20')->startOfDay()->getTimestamp()),
                    'expiry_date' => new MongoDate(Carbon::parse('2025-04-20')->endOfDay()->getTimestamp()),
                    'subscription' => [
                        'subscription_plan_id' => '59e63a1773fa6cf751723246',
                        'interval' => 'month',
                        'interval_count' => 1,
                        'duration' => 0,
                        'credits' => [
                            [
                                'branch_id' => self::GLOFOX_BRANCH_ID,
                                'namespace' => 'glofox',
                                'active' => true,
                                'model' => 'programs',
                                'category_id' => null,
                                'num_sessions' => 10,
                                'plan_code' => '1506335805840',
                                'model_ids' => null,
                                'expiry' => [
                                    'interval' => 'week',
                                    'interval_count' => 2,
                                ],
                                'end_date' => null,
                            ],
                        ],
                    ],
                ],
            ]),
            $this->createAdmin([
                '_id' => new MongoId('88887c1cd7b6ddc3a98b4592'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
            ]),
        ];
    }

    private function forCancelBookingsPausedMembership(): array
    {
        return [
            [
                '_id' => new MongoId('6814d3419e5eedaa97f9638a'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'first_name' => 'Paused Membership Test',
                'type' => 'MEMBER',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'membership' => [
                    '_id' => '56ceca057cad3653008b4582',
                    'user_membership_id' => '7df91831b3cb1f4351b3a3z5',
                    'type' => 'time',
                    'start_date' => date('Y-m-d', strtotime('-7 days')),
                    'plan_code' => '1506223805842',
                    'boooked_events' => 0,
                    'expiry_date' => date('Y-m-d', strtotime('+1 year')),
                ],
            ],
        ];
    }

    private function forAdvancedCycleCreditsWithDifferentThanMembershipDuration(): array
    {
        return [
            $this->createBase([
                '_id' => '77207c1cd7b6ddc3a98b4581',
                'branch_id' => self::GLOFOX_BRANCH_ID,
                'namespace' => 'glofox',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'membership' => [
                    '_id' => '54107c1cd7b6ddc3a98b4577',
                    'user_membership_id' => '5cf91831b3cb1f0001a6f4a4',
                    'branch_id' => '49a7011a05c677b9a916612a',
                    'type' => 'time_classes',
                    'plan_code' => '1506335805840',
                    'start_date' => new MongoDate(Carbon::parse('2025-02-20')->startOfDay()->getTimestamp()),
                    'expiry_date' => new MongoDate(Carbon::parse('2025-03-20')->endOfDay()->getTimestamp()),
                    'subscription' => [
                        'subscription_plan_id' => '59e63a1773fa6cf751723246',
                        'interval' => 'month',
                        'interval_count' => 1,
                        'duration' => 0,
                        'credits' => [
                            [
                                'branch_id' => self::GLOFOX_BRANCH_ID,
                                'namespace' => 'glofox',
                                'active' => true,
                                'model' => 'programs',
                                'category_id' => null,
                                'num_sessions' => 10,
                                'plan_code' => '1506335805840',
                                'model_ids' => null,
                                'expiry' => [
                                    'interval' => 'week',
                                    'interval_count' => 1,
                                ],
                                'end_date' => null,
                            ],
                        ],
                    ],
                ],
            ]),
        ];
    }

    private function forFacilitiesControllerList(): array
    {
        $locationId = '683d79b87b6562a3a269d8b9';
        $namespace = 'for-get-facilities-by-location-id';
        return [
            $this->createAdmin([
                '_id' => new MongoId('77207c1cd7b6ddc3a98b4582'),
                'branch_id' => $locationId,
                'namespace' => $namespace
            ]),
            $this->createPaygMember([
                '_id' => new MongoId('77207c1cd7b6ddc3a98b4583'),
                'branch_id' => $locationId,
                'namespace' => $namespace
            ]),
            $this->createReceptionist([
                '_id' => new MongoId('77207c1cd7b6ddc3a98b4584'),
                'branch_id' => $locationId,
                'namespace' => $namespace
            ]),
        ];
    }

    private function forAdvancedCycleCreditsWithOneMonthDuration(): array
    {
        return [
            $this->createBase([
                '_id' => '77207c1cd7b6ddc3a98b4585',
                'branch_id' => self::GLOFOX_BRANCH_ID,
                'namespace' => 'glofox',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'membership' => [
                    '_id' => '54107c1cd7b6ddc3a98b4577',
                    'user_membership_id' => '5cf91831b3cb1f0001a6f4a4',
                    'branch_id' => '49a7011a05c677b9a916612a',
                    'type' => 'time_classes',
                    'plan_code' => '1506335805840',
                    'start_date' => new MongoDate(Carbon::parse('2025-02-20')->startOfDay()->getTimestamp()),
                    'expiry_date' => new MongoDate(Carbon::parse('2025-03-20')->endOfDay()->getTimestamp()),
                    'subscription' => [
                        'subscription_plan_id' => '59e63a1773fa6cf751723246',
                        'interval' => 'month',
                        'interval_count' => 1,
                        'duration' => 0,
                        'credits' => [
                            [
                                'branch_id' => self::GLOFOX_BRANCH_ID,
                                'namespace' => 'glofox',
                                'active' => true,
                                'model' => 'programs',
                                'category_id' => null,
                                'num_sessions' => 10,
                                'plan_code' => '1506335805840',
                                'model_ids' => null,
                                'expiry' => [
                                    'interval' => 'month',
                                    'interval_count' => 1,
                                ],
                                'end_date' => null,
                            ],
                        ],
                    ],
                ],
            ]),
        ];
    }
}
