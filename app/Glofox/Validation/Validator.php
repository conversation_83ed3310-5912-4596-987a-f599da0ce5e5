<?php

namespace Glofox\Validation;

use Glofox\Traversable\AlwaysAccessibleCollection;
use Glofox\Validation\Rules\RulesCollection;
use Illuminate\Contracts\Validation\Factory as ValidationFactory;
use Illuminate\Validation\Validator as IlluminateValidator;

abstract class Validator
{
    /**
     * @var RulesCollection
     */
    protected $rules;

    /**
     * @var AlwaysAccessibleCollection
     */
    protected $data;

    /**
     * Validator constructor.
     *
     * @param ValidationFactory $validationFactory
     */
    public function __construct(ValidationFactory $validationFactory)
    {
        $this->rules = new RulesCollection();
        $this->messages = [];
        
        $this->validationFactory = $validationFactory;
    }

    /**
     * @param array $data
     *
     * @return $this
     */
    public function withData(array $data) : self
    {
        $this->data = new AlwaysAccessibleCollection($data);

        return $this;
    }

    /**
     * @return AlwaysAccessibleCollection
     */
    protected function data() : AlwaysAccessibleCollection
    {
        return $this->data;
    }

    /**
     * @return void
     */
    public function validate() : void
    {
        $this->validator()->validate();
    }

    protected function validator() : IlluminateValidator
    {
        $rules = $this->rules()->toArray();
        $data = $this->data()->toArray();
        $messages = $this->messages();

        $validator = $this->validationFactory->make($data, $rules, $messages);

        return $validator;
    }

    /**
     * @return RulesCollection
     */
    abstract public function rules() : RulesCollection;

    /**
     * @return array
     */
    abstract public function messages() : array;
}
