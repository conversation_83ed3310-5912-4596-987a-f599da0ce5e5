type: object
properties:
  program_id:
    type: string
    description: The id of the booked program.
    pattern: '^[a-f\d]{24}$'
    example: 551be393de36da56ec8b45e1
  user_id:
    type: string
    description: The id of the member.
    pattern: '^[a-f\d]{24}$'
  book_start_time:
    type: string
    nullable: true
    example: '2024-12-31T00:00:00Z'
    description: Date when recurring booking will start in `YYYY-MM-DDThh:mm:ssTZD` format. By default, will use the beginning today's date.
  book_until_time:
    type: string
    example: '2024-12-31T23:59:59Z'
    description: Date when recurring booking will end in `YYYY-MM-DDThh:mm:ssTZD` format.
  schedule_codes:
    type: array
    nullable: true
    items:
      type: string
      example: '65f06ca6c1f24'
      description: Schedule code
