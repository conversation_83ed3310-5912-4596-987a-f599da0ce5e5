description: DiscountInsightsUsagesByIntervalItem is an item containing the usages
  for a specified time period.
properties:
  date:
    description: Date is the specified time.
    type: string
  total:
    description: Total is the total number of pending and successful applications
      of the discount
    type: integer
  totalDirectUses:
    description: |-
      TotalDirectUses is the total number of pending and successful applications exclusively for
      manual applications of the discount.
    type: integer
  totalPromoCodeUses:
    description: |-
      TotalPromoCodeUses is the total number of pending and successful applications exclusively for
      uses of a promo code.
    type: integer
type: object