<?php

namespace Glofox;

/**
 * Class NotFoundException
 * @package Glofox
 */
class NotFoundException extends Exception
{
    /**
     * NotFoundException constructor.
     * @param string $message
     * @param int $code
     * @param Throwable|null $previous
     */
    public function __construct($message = "", $code = 404, Throwable $previous = null)
    {
        parent::__construct($message, $code, $previous);
    }
}