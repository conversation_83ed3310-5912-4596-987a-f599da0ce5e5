<?php

namespace CakeTestCases\Glofox\Domain\BookingRequests\Services;

use Glofox\Domain\AsyncEvents\Events\UserRequestedCourseBookingUpdateEventPayload;
use Glofox\Domain\AsyncEvents\Events\UserRequestedEventBookingUpdateEventPayload;
use Glofox\Domain\AsyncEvents\Events\UserRequestedTimeSlotBookingUpdateEventPayload;
use Glofox\Domain\BookingRequests\Services\BookingUpdateRequestEventPayloadFactory;
use Glofox\Domain\Bookings\Models\Booking;

\App::import('Test/Case', 'GlofoxTestCase');

class BookingUpdateRequestEventPayloadFactoryTest extends \GlofoxTestCase
{
    public $fixtures = [];
    /** @var BookingUpdateRequestEventPayloadFactory */
    private $bookingUpdateRequestEventPayloadFactory;

    public function setUp()
    {
        parent::setUp();

        $this->bookingUpdateRequestEventPayloadFactory = \Mockery::spy(
            app()->make(BookingUpdateRequestEventPayloadFactory::class)
        );
    }

    public function test_it_should_create_events_payload_when_receiving_event_booking(): void
    {
        $booking = $this->getMockedEventBooking();
        $payload = $this->bookingUpdateRequestEventPayloadFactory->make($booking);

        $expected = [
            'eventId' => "5d3852c165b345002e423a95",
            'branchId' => "5ce2810d227b1b034b1c6d21",
            'userId' => "5d358dbe8bd9a30ec1075a95",
            'guestBookings' => 0,
            'forceOverbook' => false,
            'status' => "BOOKED",
            'attended' => null,
            'paid' => null,
        ];

        $serialized = $payload->jsonSerialize();

        self::assertInstanceOf(UserRequestedEventBookingUpdateEventPayload::class, $payload);
        self::assertEquals($expected, $serialized);
    }

    public function test_it_should_create_time_slots_payload_when_receiving_time_slot_booking(): void
    {
        $booking = $this->getMockedTimeSlotBooking();
        $payload = $this->bookingUpdateRequestEventPayloadFactory->make($booking);

        $expected = [
            'timeSlotId' => "5d3852c165b345002e423a95",
            'branchId' => "5ce2810d227b1b034b1c6d21",
            'userId' => "5d358dbe8bd9a30ec1075a95",
            'guestBookings' => 0,
            'forceOverbook' => false,
            'status' => "BOOKED",
            'attended' => null,
            'paid' => null,
        ];

        $serialized = $payload->jsonSerialize();

        self::assertInstanceOf(UserRequestedTimeSlotBookingUpdateEventPayload::class, $payload);
        self::assertEquals($expected, $serialized);
    }

    public function test_it_should_create_courses_payload_when_receiving_course_booking(): void
    {
        $booking = $this->getMockedCourseBooking();
        $payload = $this->bookingUpdateRequestEventPayloadFactory->make($booking);

        $expected = [
            'courseId' => "5d3852c165b345002e423a95",
            'branchId' => "5ce2810d227b1b034b1c6d21",
            'userId' => "5d358dbe8bd9a30ec1075a95",
            'guestBookings' => 0,
            'forceOverbook' => false,
            'status' => "BOOKED",
            'attended' => null,
            'paid' => null,
        ];

        $serialized = $payload->jsonSerialize();

        self::assertInstanceOf(UserRequestedCourseBookingUpdateEventPayload::class, $payload);
        self::assertEquals($expected, $serialized);
    }

    public function test_it_should_add_attended_to_payload_when_it_exists(): void
    {
        $booking = $this->getMockedEventBooking();
        $booking->put('attended', true);

        $payload = $this->bookingUpdateRequestEventPayloadFactory->make($booking);

        $serialized = $payload->jsonSerialize();

        self::assertArrayHasKey('attended', $serialized);
        self::assertTrue($serialized['attended']);

        $booking = $this->getMockedEventBooking();
        $booking->put('attended', false);

        $payload = $this->bookingUpdateRequestEventPayloadFactory->make($booking);

        $serialized = $payload->jsonSerialize();

        self::assertArrayHasKey('attended', $serialized);
        self::assertFalse($serialized['attended']);
    }

    public function test_it_should_add_paid_to_payload_when_it_exists(): void
    {
        $booking = $this->getMockedEventBooking();
        $booking->put('paid', true);

        $payload = $this->bookingUpdateRequestEventPayloadFactory->make($booking);

        $serialized = $payload->jsonSerialize();

        self::assertArrayHasKey('paid', $serialized);
        self::assertTrue($serialized['paid']);
    }

    public function test_it_should_add_guests_bookings_to_payload_when_it_exists(): void
    {
        $booking = $this->getMockedEventBooking();
        $booking->put('guest_bookings', 33);

        $payload = $this->bookingUpdateRequestEventPayloadFactory->make($booking);

        $serialized = $payload->jsonSerialize();

        self::assertArrayHasKey('guestBookings', $serialized);
        self::assertEquals(33, $serialized['guestBookings']);
    }

    private function getMockedEventBooking(): Booking
    {
        return Booking::make([
            '_id' => '5d38681265b345003b2ec402',
            'type' => 'events',
            'active' => true,
            'branch_id' => '5ce2810d227b1b034b1c6d21',
            'event_id' => '5d3852c165b345002e423a95',
            'event_name' => 'Test 4',
            'force_overbook' => false,
            'guest_bookings' => 0,
            'paid' => false,
            'status' => 'BOOKED',
            'user_id' => '5d358dbe8bd9a30ec1075a95',
        ]);
    }

    private function getMockedTimeSlotBooking(): Booking
    {
        return Booking::make([
            '_id' => '5d38681265b345003b2ec402',
            'type' => 'time_slots',
            'active' => true,
            'branch_id' => '5ce2810d227b1b034b1c6d21',
            'time_slot_id' => '5d3852c165b345002e423a95',
            'event_name' => 'Test 4',
            'force_overbook' => false,
            'guest_bookings' => 0,
            'paid' => false,
            'status' => 'BOOKED',
            'user_id' => '5d358dbe8bd9a30ec1075a95',
        ]);
    }

    private function getMockedCourseBooking(): Booking
    {
        return Booking::make([
            '_id' => '5d38681265b345003b2ec402',
            'type' => 'courses',
            'active' => true,
            'branch_id' => '5ce2810d227b1b034b1c6d21',
            'course_id' => '5d3852c165b345002e423a95',
            'event_name' => 'Test 4',
            'force_overbook' => false,
            'guest_bookings' => 0,
            'paid' => false,
            'status' => 'BOOKED',
            'user_id' => '5d358dbe8bd9a30ec1075a95',
        ]);
    }
}
