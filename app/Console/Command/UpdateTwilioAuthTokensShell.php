<?php

declare(strict_types=1);

class UpdateTwilioAuthTokensShell extends AppShell
{
    private $messageConfigModel;

    private $twilioAccountSid;

    private $twilioAuthToken;

    public function __construct($stdout = null, $stderr = null, $stdin = null)
    {
        parent::__construct($stdout, $stderr, $stdin);

        $this->messageConfigModel = \ClassRegistry::init(MessageConfig::class);
    }

    public function main()
    {
        $this->twilioAccountSid = $this->args[0] ?? null;
        if (empty($this->twilioAccountSid)) {
            $this->log('Twilio Account Sid has to be provided', LOG_ERR);

            return;
        }

        $this->twilioAuthToken = $this->args[1] ?? null;
        if (empty($this->twilioAuthToken)) {
            $this->log('Twilio Auth Token has to be provided', LOG_ERR);

            return;
        }
        $messageConfigurations = $this->messageConfigModel->find('all',
            [
                'conditions' => [],
            ]);

        foreach ($messageConfigurations as $index => $config) {
            if ($index % 50 === 0) {
                $this->log(sprintf('%s entries processed', $index), LOG_INFO);
            }
            sleep(1);
            try {
                $subAccountSid = $config['MessageConfig']['transactional']['account_sid'];
                if ($subAccountSid == null) {
                    throw new Exception('missing subaccount SID');
                }
                $token = $this->fetchToken($subAccountSid);
                $this->updateConfig($config, $token);

            } catch (Exception $e) {
                $this->log(sprintf('Unexpected error: %s', $e));
                $this->log(sprintf('error in updating auth token for branch: %s',
                    $config['MessageConfig']['branch_id']), LOG_WARNING);
                continue;
            }
        }
    }

    private function fetchToken(?string $subAccountSid): ?string
    {
        $ch = curl_init(sprintf('https://api.twilio.com/2010-04-01/Accounts/%s.json', $subAccountSid));
        curl_setopt($ch, CURLOPT_USERPWD, sprintf('%s:%s', $this->twilioAccountSid, $this->twilioAuthToken));
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'GET');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $subAccounts = json_decode(curl_exec($ch), null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        curl_close($ch);

        if ($subAccounts->auth_token === null) {
            throw new Exception(sprintf('could not fetch account token: %s', $subAccounts->message));
        }

        return $subAccounts->auth_token;
    }

    private function updateConfig($config, ?string $token)
    {
        if ($token) {
            $config['MessageConfig']['transactional']['auth_token'] = $token;
            $config['MessageConfig']['marketing']['auth_token'] = $token;
            $this->messageConfigModel->save($config);
        }
    }
}
