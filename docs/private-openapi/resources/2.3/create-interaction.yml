post:
  summary: ''
  operationId: create-interaction
  tags:
    - Interactions
  description: To create a new interaction for a user
  parameters:
    - schema:
        type: string
        pattern: '^[a-f\d]{24}$'
      name: branchId
      in: path
      required: true
    - schema:
        type: string
        pattern: '^[a-f\d]{24}$'
      name: userId
      in: path
      required: true
  requestBody:
    required: true
    content:
      application/json:
        schema:
          type: object
          properties:
            type:
              type: string
              description: Type of interaction
              enum:
                - NOTE
                - CALLED_AND_CONNECTED
                - CALLED_AND_NO_ANSWER
                - MANUAL_EMAIL
                - MEMBER_SERVICE_INTERACTION
                - AUTO_EMAIL
                - AUTO_SMS
                - AUTO_PUSH_NOTIFICATION
                - AMPLIFY_EMAIL
                - AMPLIFY_SMS
                - AMPLIFY_PUSH
            description:
              type: string
              maxLength: 500
              description: description of the interaction
            created:
              type: integer
              description: timestamp for the creation of the interaction
          required:
            - type
  responses:
    '200':
      description: OK
      content:
        application/json:
          schema:
            properties:
              success:
                type: string
              data:
                $ref: "../../schemas/interaction.yml"
    '404':
      description: Not Found
      content:
        application/json:
          schema:
            $ref: "../../schemas/error.yml"
    '400':
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: "../../schemas/error.yml"
    '403':
      description: Forbidden
      content:
        application/json:
          schema:
            $ref: "../../schemas/error.yml"
