<?php

use Carbon\Carbon;
use Glofox\Domain\Authentication\Auth;
use Glofox\Domain\Branches\Exceptions\BranchNotFoundException;
use Glofox\Domain\Branches\Repositories\BranchesRepository;
use Glofox\Domain\Branches\Models\Branch;
use Glofox\Domain\Notifications\Http\NotificationsController;
use Glofox\Domain\Notifications\Requests\HasSentNotificationsRequest;
use Glofox\Domain\Notifications\Requests\SendBroadcastRequest;
use Glofox\Domain\Users\Models\ConsentPush;
use Glofox\Domain\Users\Models\User;
use Glofox\Domain\Users\Repositories\UsersRepository;
use Glofox\Domain\Events\Models\Event;
use Glofox\Domain\Events\Repositories\EventsRepository;
use Glofox\Domain\Users\Services\Notification\PushNotificationService;
use Glofox\Repositories\Search\Expressions\Shared\Id;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;

/**
 * PushNotifications class.
 *
 * @uses AppController
 *
 * @property User $User
 * @property Branch $Branch
 * @property Booking $Booking
 * @property PushNotification $PushNotification
 * @property Client $Client
 * @property JWTComponent $JWT
 */
class PushNotificationsController extends NotificationsController
{
    /**
     * @var array
     */
    public $components = [
        'Utility',
        'JWT',
    ];

    /**
     * @var array
     */
    public $uses = [
        'User',
        'Booking',
        'PushNotification',
        'Client',
    ];

    /* @var NotificationComponent */
    private $notificationComponent;

    /* @var UsersRepository */
    private $usersRepository;

    /** @var EventsRepository */
    private $eventsRepository;

    /* @var ConsentPush */
    private $consentPush;

    private PushNotificationService $pushNotificationService;

    public function __construct($request = null, $response = null)
    {
        parent::__construct($request, $response);

        $this->notificationComponent = app()->make(NotificationComponent::class);
        $this->usersRepository = app()->make(UsersRepository::class);
        $this->eventsRepository = app()->make(EventsRepository::class);
        $this->consentPush = app()->make(ConsentPush::class);
        $this->pushNotificationService = app()->make(PushNotificationService::class);
    }

    /**
     * @param $branch_id
     * @param $message
     *
     * @return JsonResponse
     */
    public function broadcast($branch_id, $message)
    {
        return response()->json($this->notificationComponent->broadcast($branch_id, $message));
    }

    /**
     * @param $branch
     * @param $token
     * @param $message
     *
     * @return JsonResponse
     */
    public function ios_push($branch, $token, $message)
    {
        $this->logger->info('Received ios_push notifications request');
        return response()->json($this->notificationComponent->ios_push($branch, $token, $message));
    }

    /**
     * @param $branch_id
     * @param $user_id
     * @param $message
     *
     * @return JsonResponse
     */
    public function push_message($branch_id, $user_id, $message)
    {
        $this->logger->info('Received push_message notifications request');
        return response()->json($this->notificationComponent->push_message($branch_id, $user_id, $message));
    }

    /*******************************************************************************************
     *                                API 2.0 New Dashboard
     *****************************************************************************************
     *
     * @param $identifier
     *
     * @return string
     */

    /*!
       * Dispatcher for Model
       * @param  [type] $identifier [description]
       * @return [type]             [description]
       */
    public function dispatcher($identifier)
    {
        return parent::dispatch($identifier, $this->PushNotification);
    }

    /*!
     * Get list of device tokens given parameters. EX URL that invoke this method
     * /bookings/not_booked/7/days -> people not booked in the last 7 days
     * /memberships/expires_within/2/months -> people with membership that will expire in 2 months
     * /memberships/expires_anytime/0/days/123456 -> people with membership id 123456 (expire whenever)
     *
     * URL ELEMENTS:
     * $collection 	// bookings, memberships
     * $state 		// bookings -> booked, not_booked // memberships -> expires_within, expired_within, expires_anytime
     * $interval 	// days, weeks, months, years
     * $interval count //number
     * $identifier // Only for memberships if specific membership id is needed
     * @param      [type]                   $collection     [description]
     * @param      [type]                   $state          [description]
     * @param      [type]                   $interval       [description]
     * @param      [type]                   $interval_count [description]
     * @param      [type]                   $identifier     [description]
     * @return     [type]                                   [description]
     */

    /**
     * @param null $collection
     * @param null $state
     * @param null $interval
     * @param null $interval_count
     * @param null $identifier
     *
     * @return JsonResponse
     */
    public function devices(
        $collection = null,
        $state = null,
        $interval = null,
        $interval_count = null,
        $identifier = null
    ) {
        $user = $this->JWT->parseToken();
        $this->authorizeAdministratorAccess($user);

        $today = $this->User->getCurrentDate('Y-m-d');
        $date = date('Y-m-d', strtotime($today . ' - ' . $interval_count . ' ' . $interval));

        $request = \Glofox\Request::capture();
        $overwriteMarketing = (bool)($request->get('overwriteMarketing'));

        $devices = [];
        if ('bookings' == $collection) {
            $devices = $this->bookingDevices($user, $date, $today, $state, $overwriteMarketing);
        } elseif ('memberships' == $collection) {
            $devices = $this->membershipDevices(
                $user,
                $date,
                $today,
                $state,
                $interval,
                $interval_count,
                $identifier,
                $overwriteMarketing
            );
        }

        return response()->json(['devices' => $devices]);
    }

    /**
     * @param $user
     * @param $date
     * @param $today (is not used, but it's right in the middle of the signature)
     * @param $state
     *
     * @return array
     */
    public function bookingDevices($user, $date, $today, $state, $overwriteMarketing = false)
    {
        // @see https://glofox.atlassian.net/browse/DASH2-3540
        // TODO: Add parameter validations

        // Fetch all the user ids from a branch. We're doing this first to make sure that
        // users romaing between branches will receive the message.

        // TODO: Keep an eye open for performance issues here, the roaming feature wasn't
        // TODO: well tought to handle scenarios like this and we might face degrated performance when
        // TODO: running queries like this that will fetch thousands of clients.
        // TODO: Use UsersRepository
        $users = $this->User->find('all', [
            'conditions' => [
                'active' => true,
                'branch_id' => $user['branch_id'],
            ],
            'fields' => ['_id'],
        ]);

        // If no users are found, we return an empty array instead of throwing an exception,
        // given this is a expected use case.
        if (!$users) {
            return [];
        }

        // Extract the ids into a list
        $userIdsFromBranch = Hash::extract($users, '{n}.User._id');

        // The default flow of this method is to consider all the users that booked in
        // the given interval.
        $userIdCondition = ['$in' => $userIdsFromBranch];

        // Criteria to fetch the bookings based on the dynamic userId condition.
        $conditions = [
            'user_id' => $userIdCondition,
            'status' => \Glofox\Domain\Bookings\Status::BOOKED,
            'created' => [
                '$gte' => new MongoDate(strtotime($date)),
            ],
        ];

        // Ok, since we got all the members (including roaming ones), let's find all of them that
        // actually have a booking in the given criteria.
        $bookings = $this->Booking->find('all', [
            'conditions' => $conditions,
            'fields' => ['user_id'],
        ]);

        $userIdsFromBookings = new Collection();

        // If no bookings are found, we proceed to cover "not_booked" cases.
        if ($bookings) {
            $extract = Hash::extract($bookings, '{n}.Booking.user_id');

            // Extract the ids into a list
            $userIdsFromBookings = Collection::make($extract);
            $userIdsFromBookings = $userIdsFromBookings->unique();
        }

        if ('not_booked' === $state) {
            $userIdsFromBookings = Collection::make($userIdsFromBranch)->diff($userIdsFromBookings->toArray());
        }

        $userIds = $userIdsFromBookings->toArray();

        return $this->deviceIds($userIds, $overwriteMarketing);
    }

    /*!
     * List of devices token from membership collection analysis, check devices() method
     * for documentation of params
     * @param      [type]                   $user [description]
     * @param      [type]                   $date [description]
     * @return     [type]                         [description]
     */

    /**
     * @param $user
     * @param $date
     * @param $today
     * @param $state
     * @param $interval
     * @param $interval_count
     * @param $identifier
     *
     * @return array
     */
    public function membershipDevices(
        $user,
        $date,
        $today,
        $state,
        $interval,
        $interval_count,
        $identifier,
        $overwriteMarketing
    ) {
        $params = [
            'branch_id' => $user['branch_id'],
            'type' => 'MEMBER',
            'active' => true,
        ];
        //Is Payg?
        if (0 == $identifier) {
            $params['membership.type'] = 'payg';
        } elseif ('since' == $state) {
            //MEMERS SINCE DATE
            $date = date('Y-m-d', strtotime($today . ' - ' . $interval_count . ' ' . $interval));
            $params['membership._id'] = ['$exists' => true];
            $params['membership.start_date'] = [
                '$lte' => new MongoDate(strtotime($today)),
                '$gte' => new MongoDate(strtotime($date)),
            ];
        } elseif ('expires_within' == $state) {
            //MEMBERS WITH MEMBERSHIP THAT WILL EXPIRE WITHIN # TIME
            $date = date('Y-m-d', strtotime($today . ' + ' . $interval_count . ' ' . $interval));
            $params['membership.subscription'] = ['$exists' => false];
            $params['membership.expiry_date'] = [
                '$lte' => new MongoDate(strtotime($date)),
                '$gte' => new MongoDate(strtotime($today)),
            ];
        } elseif ('expired_within' == $state) {
            //MEMBERS WITH MEMBERSHIPS ALREADY EXPIRED
            $date = date('Y-m-d', strtotime($today . ' - ' . $interval_count . ' ' . $interval));
            $params['membership.subscription'] = ['$exists' => false];
            $params['membership.expiry_date'] = [
                '$lte' => new MongoDate(strtotime($today)),
                '$gte' => new MongoDate(strtotime($date)),
            ];
        } elseif ('expires_anytime' == $state) {
            //MEMBERS REGARDLESS OF WHEN THEY EXPIRE
            $params['membership.type'] = ['$ne' => 'payg'];
        }

        //IF MEMBERSHIP ID IS PROVIDED
        if (isset($identifier) && !empty($identifier) && 0 != $identifier && 1 != $identifier) {
            $params['membership._id'] = $identifier;
        }

        $users = $this->User->find('all', ['conditions' => $params, 'fields' => ['_id']]);
        $ids = array_map([$this, 'mapIds'], $users);
        $devices = $this->deviceIds($ids, $overwriteMarketing);

        return $devices;
    }

    /**
     * Get notifications by specifying all in branch, all in branch or by specific id. This only includes 'broadcast'
     * 'push' and 'email' types,
     * date format is a string YYYYMMDD.
     *
     * @param string $id PushNotification id
     * @param        $date
     *
     * @return array List of notifications that match the entry params within type 'broadcast', 'email', 'push'
     * @example    https://www.glofoxlogin/view/{id}
     *
     * @internal   This convention is a little boring for clients, but helps us keep clean backend
     *
     */
    public function view($id, $date)
    {
        $user = $this->JWT->parseToken();
        $namespace = $user['namespace'];

        $branchId = $user['branch_id'];

        $timezone = $this->Branch->getTimeZone($branchId);
        $startTime = Carbon::createFromTimestampUTC($date);
        $startTime->setTimezone($timezone);
        $endTime = $startTime->copy()->addDay();

        // initializing the notificationComponent
        $this->notificationComponent->initialize($this);
        $result = $this->notificationComponent->status(
            $namespace,
            $startTime->timestamp,
            $endTime->timestamp,
            $branchId
        );

        $this->logger->info('Received push notifications list, will send response', [
            'result' => [
                'success' => $result['success'] ?? '',
                'err' => $result['err'] ?? '',
                'bytes' => strlen($result['response'] ?? ''),
            ],
            'namespace' => $namespace,
            'date' => $date,
            'startTime' => $startTime->timestamp,
            'endTime' => $endTime->timestamp,
        ]);

        return $result['response'];
    }

    /**
     * Save a course object including schedules and patterns, if object does not have an _id
     * it will add it new, else it will update that current instance, This does not upload images
     * to AWS. You must use the image upload service as a separate request to upload images.
     *
     * @return object {Course:object,success:boolean}
     * @example    https://www.glofoxlogin/courses/upsert Payload: Course object directly
     *
     * @internal   Some data validation required since its a complex object. TO_DO
     *
     */
    public function upsert()
    {
        $this->JWT->parseToken();
        $notification = json_decode(@file_get_contents('php://input'), true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        $notification['rules'] = response()->json($notification['rules']);

        return response()->json(
            $this->PushNotification->save($notification)
        );
    }

    /*!
     * Search list with params and pagination
     * @param      [type]                   $start  [description]
     * @param      [type]                   $limit  [description]
     * @param      [type]                   $search [description]
     * @return     [type]                           [description]
     */

    /**
     * @param int $page
     * @param int $limit
     * @param null $search
     *
     * @return JsonResponse
     */
    public function listview($page = 1, $limit = 20, $search = null)
    {
        $user = $this->JWT->parseToken();
        $params = ['branch_id' => $user['branch_id']];
        $this->encodeSearchRegex($params, $search); //<- add to params built Regex for global search
        $page = (int)$page;
        $limit = (int)$limit;
        $result = $this->PushNotification->find(
            'all',
            [
                'conditions' => $params,
                'limit' => $limit,
                'page' => $page,
            ]
        );

        return response()->json($result);
    }

    /*!
     * Count total records in this collection
     * @return     [type]                   [description]
     */

    /**
     * @return JsonResponse
     */
    public function count()
    {
        $user = $this->JWT->parseToken();
        $params = ['branch_id' => $user['branch_id'], 'active' => true];
        $result = $this->PushNotification->find(
            'count',
            ['conditions' => $params]
        );

        return response()->json($result);
    }

    /*!
     * Send group messaging for event id
     * @internal
     * @deprecated
     * @link
     * @example    https://www.glofoxlogin/
     * @param      [type]                   $event_id [description]
     * @return     [type]                             [description]
     */

    /**
     * @param $event_id
     *
     * @return JsonResponse
     */
    public function sendGroupMessage($event_id)
    {
        $user = Auth::user();
        $valid_id = $this->Utility->is_valid_mongoid($event_id);
        $event = $this->getEvent($event_id);

        //Validations
        if (empty($user->currentBranchId())) {
            return response()->json([
                'success' => false,
                'message' => __('Branch Id could not be validated, please try again later')
            ]);
        }

        $this->authorizeAdministratorAccess($user);

        if (empty($valid_id)) {
            return response()->json(['success' => false, 'message' => __('Invalid Event Id')]);
        }

        if ($user->currentBranchId() != $event['branch_id']) {
            return response()->json([
                'success' => false,
                'message' => __('User token branch id and event branch id does not match')
            ]);
        }

        $params = ['branch_id' => $user['branch_id'], 'event_id' => $event_id, 'status' => 'BOOKED'];
        $users = $this->Booking->find('all', ['conditions' => $params, 'fields' => 'user_id']);

        $data = $this->getPayload();
        $message = $data['message'];
        $overwriteMarketing = $data['overwriteMarketing'] ?? false;

        $user_ids = Hash::extract($users, '{n}.Booking.user_id');
        $branchId = $user['branch_id'];

        $branchesRepository = app()->make(BranchesRepository::class);
        /** @var \Glofox\Domain\Branches\Models\Branch $branch */
        $branch = $branchesRepository->addCriteria(new Id($branchId))
            ->firstOrFail(function () use ($branchId) {
                throw BranchNotFoundException::withId($branchId);
            });

        $push_data['branch_id'] = $branchId;
        $push_data['message'] = $message;
        $push_data['is_marketing'] = !$overwriteMarketing;

        $response = true;

        if (!$overwriteMarketing) {
            $user_ids = $this->getUserIdsWithMarketingConsent($user_ids, $overwriteMarketing);
        }

        //Since is not a broadcast, it must be sent individually to all members included
        foreach ($user_ids as $uid) {
            $push_data['user_id'] = $uid;

            //Get devide id for this users
            $device = $this->deviceIds([$uid], $overwriteMarketing);
            $this->PushNotification->saveBroadcastNotification($push_data);

            $client = $this->Client->getByNamespace($user['namespace'])['Client'];
            $namespace = $user['namespace'];
            $bundle = $client['bundles'] ?? null;
            $response = $this->notificationComponent->send($message, $device, $namespace, $bundle, $branch);
        }

        //We only need to notify finalisation of this task
        return response()->json(
            [
                'success' => true,
                'message' => 'Push Message Sent',
                'data' => null,
                'status' => $response,
            ]
        );
    }

    /*!
     * Broadcast to all active users of sender branch
     * URL GET PARAM -> message ex
     * pushnotifications/sendToDevices/HELLO!!!!!
     *
     * POST PARAMS ->
     * {
     * 		devices = [ androidToken, iosToken ... ]
     * }
     * @example    https://www.glofoxlogin/
     * @return     [type]                   [description]
     */

    /**
     * @return JsonResponse
     */
    public function sendToDevices()
    {
        try {
            $user = $this->JWT->parseToken();
            $this->authorizeAdministratorAccess($user);
            $data = $this->getPayload();
            $message = $data['message'];
            $overwriteMarketing = $data['overwriteMarketing'] ?? false;
            $devices = $data['devices'];

            // This should be validated in a custom request
            if (empty($message)) {
                throw new BadRequestException('MESSAGE_IS_EMPTY');
            }
            // This should be validated in a custom request
            if (empty($devices)) {
                throw new BadRequestException('DEVICES_IS_EMPTY');
            }

            //Store for each device in notification collection
            $client = $this->Client->getByNamespace($user['namespace'])['Client'];
            $namespace = $user['namespace'];
            $bundle = $client['bundles'] ?? null;

            $pushConsent = (!$overwriteMarketing) ? true : null;
            $users = $this->usersRepository->getUsersWithPushConsentByBranchIdAndDevicesIds(
                $user['branch_id'],
                $devices,
                $pushConsent
            );

            $uids = collect($users)
                ->map(fn(User $user) => $user['user_id'])->toArray();
            if (empty($uids)) {
                throw new NotFoundException('MEMBERS_NOT_FOUND_WITH_GIVEN_DEVICES_IDS');
            }
            $devices = collect($users)
                ->map(fn(User $user) => $user['device']['id'])->toArray();

            $branchId = $user['branch_id'];

            $branchesRepository = app()->make(BranchesRepository::class);
            /** @var \Glofox\Domain\Branches\Models\Branch $branch */
            $branch = $branchesRepository->addCriteria(new Id($branchId))
                ->firstOrFail(function () use ($branchId) {
                    throw BranchNotFoundException::withId($branchId);
                });

            $this->PushNotification->saveNotifications($message, $uids, $branchId, !$overwriteMarketing);

            //Check here, there is a LIMIT to the amount of devices we can send to request
            $response = $this->notificationComponent->send($message, $devices, $namespace, $bundle, $branch);

            return response()->json([
                'success' => true,
                'message' => 'Push Message Sent',
                'data' => null,
                'status' => $response,
                'ops' => null,
            ]);
        } catch (Exception $e) {
            // We should add HTTP status code
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
                'message_code' => $e->getMessage(),
            ]);
        }
    }

    public function sendBroadcast(SendBroadcastRequest $request): JsonResponse
    {
        $user = Auth::user();
        $this->authorizeAdministratorAccess($user);

        $namespace = $user->namespace();
        $branchId = $user->currentBranchId();

        $notification = $this->PushNotification->saveBroadcastNotification([
            'branch_id' => $branchId,
            'message' => $request->message(),
            'is_marketing' => !$request->overwriteMarketing()
        ]);

        $client = $this->Client->getByNamespace($namespace)['Client'];
        $bundle = $client['bundles'] ?? null;
        $devices = $this->pushNotificationService->deviceIdsByBranch($branchId, $request->overwriteMarketing());

        // Check here, there is a LIMIT to the amount of devices we can send to request
        $response = $this->notificationComponent->send(
            $request->message(),
            $devices,
            $namespace,
            $bundle,
            $user->branch(),
        );

        return response()->json(
            [
                'success' => true,
                'message' => 'Push Message Sent',
                'data' => $notification,
                'status' => $response,
                'ops' => null,
            ]
        );
    }

    /**
     * Delete notification given its _id. Return the instance with active false to keep standard the logic on the UI,
     * which, for most cases, handle logical delete.
     *
     * @example    https://www.glofoxlogin/pushnotifications/remove/{id}
     *
     * @param string $id PushNotification _id
     *
     * @return object {PushNotification:object, success:boolean}
     */
    public function remove($id)
    {
        $notify = $this->PushNotification->findById($id);
        $notify['PushNotification']['active'] = false;
        $this->PushNotification->delete($id);

        return response()->json($notify);
    }

    /*!
     * -> ENTRY POINT TO SENDING ALL TYPES OF PUSH NOTIFICATIONS and EMAILS
     * Send push notification given its ID NOTIFICATION.
     * Then.... a notification must be created first and then invoked this method wherever it wants to be send.
     * @return     [type]                   [description]
     */

    /**
     * @param $id
     *
     * @return JsonResponse
     */
    public function send($id)
    {
        $this->logger->info('Received send notifications request');
        $user = $this->JWT->parseToken();
        $notification = $this->PushNotification->findById($id)['PushNotification'];
        $branch = $this->Branch->findById($user['branch_id'])['Branch'];
        //PUSH GATEWAY
        if ('broadcast' == $notification['type'] || 'push' == $notification['type']) {
            $submission = $this->push($notification, $branch);
        }

        //Update notification with a push record
        if (!isset($notification['submissions'])) {
            $notification['submissions'] = [];
        }
        array_unshift(
            $notification['submissions'],
            $submission
        );
        $notification = $this->PushNotification->save($notification);

        return response()->json($notification);
    }

    /************************************** UTILS **********************************************
     *
     * @param $os
     * @param $branch_id
     *
     * @return array
     */
    /*!
     * List of device ids by OS for specific branch fullfilling the set of rules, if rules is empty we then assume
     * a broadcast message is intended. TO_DO this can be faster if instead of using CAKEPHP mongo models a query is
     * directly invoked against the database requesting only device ids.
     * @param      [type]                   $os        "android","ios"
     * @param      [type]                   $branch_id identifier
     * @param      [type]                   $rules     json containing rules, check Push rules on PushNotification model, "rules" field
     * @return     [type]                              array of device ids
     */
    public function deviceIdsByOs($os, $branch_id)
    {
        $this->logger->info('Received deviceIdsByOs notifications request');
        //Find Users with condition
        $users = $this->User->find(
            'all',
            [
                'fields' => 'device.id',
                'conditions' => [
                    'branch_id' => $branch_id,
                    'active' => true,
                    'device.os' => $os,
                    'device.id' => ['$ne' => null],
                ],
            ]
        );

        return array_map(
            [
                $this,
                'mapDeviceIds',
            ],
            $users
        );
    }

    /*!
     * List of device ids by user ids list. Created to support OLD DASHBOARD FUNCTIONALITIES, not part of the core new version
     * @param      [type]                   $os        "android","ios"
     * @param      [type]                   $uIds [description]
     * @return     [type]                         [description]
     */

    /**
     * @param $os
     * @param $uIds
     *
     * @return array
     */
    public function deviceIdsByUserIds($os, $uIds)
    {
        $this->logger->info('Received deviceIdsByUserIds notifications request');
        //Find Users with ids
        $users = $this->User->find(
            'all',
            [
                'fields' => 'device.id',
                'conditions' => [
                    '_id' => ['$in' => $uIds],
                    'active' => true,
                    'device.os' => $os,
                    'device.id' => ['$ne' => null],
                ],
            ]
        );

        return array_map(
            [
                $this,
                'mapDeviceIds',
            ],
            $users
        );
    }

    /****************************** WEB SERVICES *******************************************/

    /**
     * Find notifications given a branch id, user id and a date.
     *
     * @param type $branch_id
     * @param type $user_id
     * @param string $date
     *
     * @return JsonResponse
     */
    public function findByBranchIdAndUserIdAndDate($branch_id = null, $user_id = null, $date = null)
    {
        $this->authorizeAccess($branch_id,$user_id);
        //<editor-fold desc="Rushed hotfix added to support Buddhist Calendars requested urgently by the Product Team.">
        // Plan of Action decided by the Product Team: https://glofox.atlassian.net/wiki/spaces/PF/pages/580255749/Get+Classes
        // @TODO: Remove this after legacy apps are deprecated
        if ($date) {
            $date = new Carbon($date);

            /** @var \Glofox\Calendar\CurrentYearConverter $converter */
            $converter = app()->make(\Glofox\Calendar\CurrentYearConverter::class);
            $date = $converter->convert($date)->format('Y-m-d 00:00:00');
        }
        //</editor-fold>

        // Validating Branch ID is not empty
        if (empty($branch_id)) {
            return response()->json(
                [
                    'success' => false,
                    'message' => __('You must provide a branch id'),
                ]
            );
        }
        // Validating Branch ID is a valid mongodb id
        $is_valid_mongoid = $this->Utility->is_valid_mongoid($branch_id);

        if (empty($is_valid_mongoid)) {
            return response()->json(
                [
                    'success' => false,
                    'message' => __('Invalid Branch Id'),
                ]
            );
        }
        // Validating date is in the right format
        $validate_date = $this->Utility->validate_date_format(
            $date,
            true,
            'datetime'
        );

        if (!$validate_date['success']) {
            return response()->json(
                [
                    'success' => false,
                    'message' => $validate_date['message'],
                ]
            );
        }
        // Add 1 second to the date
        if (!empty($date)) {
            $date = date(
                $this->PushNotification->get_date_format('datetime'),
                strtotime($date . ' +1 seconds')
            );
        }

        $user = $this->User->find('first', ['conditions' => ['_id' => $user_id], 'fields' => ['receive_marketing']]);
        $showMarketingMessages = $user['User']['receive_marketing'] ?? false;

        $results = $this->PushNotification->findByBranchIdAndUserIdAndDate(
            $branch_id,
            $user_id,
            $date,
            $showMarketingMessages
        );

        return response()->json($results);
    }

    /**
     * @param null $branch_id
     *
     * @return JsonResponse
     */
    public function send_to_all($branch_id = null)
    {
        $this->logger->info('Received send_to_all notifications request');
        if (null == $branch_id) {
            return response()->json(
                [
                    'success' => false,
                    'message' => ' No Branch id',
                ]
            );
        }

        $data = json_decode(file_get_contents('php://input'), null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        $push_data['branch_id'] = $branch_id;
        $push_data['message'] = $data->pushMessage;
        $save_push = $this->PushNotification->saveBroadcastNotification($push_data);

        if ($save_push) {
            // REFACTOR: format the date using AppModel formatDate so we don't have to fetch the object again.
            $push_notification = $this->PushNotification->findById($save_push['PushNotification']['_id']);
            // END REFACTOR;
            // $this->Notification->broadcast($branch_id, $push_notification['PushNotification']); //----> OLD WAY
            //Batch delivery New version!
            $branch = $this->Branch->findById($branch_id)['Branch'];
            $response = $this->push(
                $push_notification['PushNotification'],
                $branch
            );

            return response()->json(
                [
                    'success' => true,
                    'message' => 'Push Message Sent',
                    'data' => $response,
                ]
            );
        } else {
            return response()->json(
                [
                    'success' => false,
                    'message' => 'Push Could not be sent. Please Try again',
                ]
            );
        }
    }

    public function hasSentNotifications(HasSentNotificationsRequest $request)
    {
        $this->logger->info('Received hasSentNotifications notifications request');
        return $this->PushNotification->branchHasSentNotifications($request->getBranchId());
    }

    /**
     * @return JsonResponse
     */
    public function health()
    {
        $this->logger->info('Received health notifications request');
        try {
            $this->notificationComponent->initialize($this);
            $code = $this->notificationComponent->getPusherStatusCode();

            // Status is always true here because when the status code is >399,
            // Guzzle throws an exception
            $result = [
                'success' => true,
                'status' => true,
                'code' => $code,
            ];
        } catch (\GuzzleHttp\Exception\RequestException $e) {
            // Success is still true here because the request itself
            // succeeded, the problem is that pusher might be down for some reason,
            // that's why we are returning the message and the code.
            $result = [
                'success' => true,
                'status' => false,
                'message' => $e->getMessage(),
                'code' => $e->getCode(),
            ];
        }

        return response()->json($result);
    }

    /*!
     * Device Ids for user ids
     * @param      [type]                   $os   [description]
     * @param      [type]                   $uIds [description]
     * @return     [type]                         [description]
     */

    /**
     * @param $uIds
     */
    private function deviceIds(array $uIds, bool $overwriteMarketing = false): array
    {
        $uIds = collect($uIds)
            ->map(fn(string $id) => new \MongoId($id))
            ->values();

        $pushConsent = (!$overwriteMarketing) ? true : null;
        $users = $this->usersRepository
            ->getUsersWithPushConsentByUserIds($uIds, $pushConsent);

        return collect($users)
            ->map(fn(User $user) => $user['device']['id'])->toArray();
    }

    private function getUserIdsWithMarketingConsent(array $userIds, bool $overwriteMarketing = false): array
    {
        $userIds = collect($userIds)
            ->map(fn(string $id) => new \MongoId($id));

        $pushConsent = (!$overwriteMarketing) ? true : null;
        $users = $this->usersRepository
            ->getUsersWithPushConsentByUserIds($userIds, $pushConsent);

        return collect($users)
            ->map(fn(User $user) => $user['user_id'])->toArray();
    }

    /*!
     * Map Device Ids. Because mongo query brings information about the User model additional to the device.id we need
     * to clean it up.
     * @param      [type]                   $user   User model to extract the device.id from
     * @return     [type]                   array of device ids
     */

    /**
     * @param $user
     *
     * @return mixed
     */
    private function mapDeviceIds($user)
    {
        return $user['User']['device']['id'];
    }

    /*!
     * Map User Ids. Because mongo query brings information about the User model additional to the device.id we need
     * to clean it up.
     * @param      [type]                   $user   User model to extract the device.id from
     * @return     [type]                   array of device ids
     */

    /**
     * @param $user
     *
     * @return mixed
     */
    private function mapIds($user)
    {
        return $user['User']['_id'];
    }

    private function authorizeAdministratorAccess($user): void
    {
        if (!in_array($user['type'], UserType::ADMINISTRATORS)) {
            $this->logger->info(sprintf('Access denied for the user %s', $user['_id']));
            throw new UnauthorizedException('The action cannot be performed by the logged-in user');
        }
    }

    private function authorizeAccess(string $branch_id, string $user_id): void
    {
        $jwtTokenUser = $this->JWT->parseToken();

        $isAdministrator = in_array($jwtTokenUser['type'], UserType::ADMINISTRATORS);

        if ($isAdministrator && $jwtTokenUser['branch_id'] === $branch_id) {
            return;
        }

        if (!$isAdministrator && $jwtTokenUser['branch_id'] === $branch_id && $jwtTokenUser['_id'] === $user_id) {
            return;
        }

        throw new UnauthorizedException('The action cannot be performed by the logged-in user');
    }
    
    private function getEvent(string $eventId): ?Event
    {
        return $this->eventsRepository
            ->addCriteria(new Id($eventId))
            ->first();
    }
}
