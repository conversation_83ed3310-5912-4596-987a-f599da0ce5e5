<?php

namespace Glofox\Validation\Exceptions;

use Glofox\Exception as Exception;

class ValidationException extends Exception
{
    protected $validationErrors;

    public function __construct($validationErrors)
    {
        parent::__construct('The given data failed to pass validation.');

        $this->validationErrors = $validationErrors;
    }

    public function validationErrors()
    {
        return $this->validationErrors;
    }
}
