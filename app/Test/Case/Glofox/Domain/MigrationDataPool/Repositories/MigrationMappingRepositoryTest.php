<?php

namespace CakeTestCases\Glofox\Domain\MigrationDataPool\Repositories;

\App::import('Test/Case', 'GlofoxTestCase');

use Glofox\Domain\Events\Search\Expressions\Id;
use Glofox\Domain\MigrationDataPool\Models\DataPoolType;
use Glofox\Domain\MigrationDataPool\Models\MigrationMapping;
use Glofox\Domain\MigrationDataPool\Repositories\MigrationMappingRepository;

class MigrationMappingRepositoryTest extends \GlofoxTestCase
{
    /** @var MigrationMappingRepository */
    private $migrationMappingRepository;

    public function setUp()
    {
        parent::setUp();

        $this->migrationMappingRepository = app()->make(MigrationMappingRepository::class);
    }

    public function test_it_saves_migration_mapping(): void
    {
        $migrationMapping = new MigrationMapping(
            null,
            collect('mock-internal-value'),
            collect('mock-foreign-value'),
            'mock-branch-id',
            DataPoolType::MEMBERSHIP_ID()
        );

        $migrationMapping = $this->migrationMappingRepository->save($migrationMapping);

        $migrationMapping = $this->migrationMappingRepository
            ->addCriteria(new Id($migrationMapping->id()))
            ->firstOrFail();

        $expected = [
            '_id' => $migrationMapping->id(),
            'branchId' => 'mock-branch-id',
            'foreign_value' => ['mock-foreign-value'],
            'internal_value' => ['mock-internal-value'],
            'mapping_type' =>  DataPoolType::MEMBERSHIP_ID
        ];

        self::assertEquals($expected, $migrationMapping->toArray());
    }

    public function test_it_deletes_migration_data_pool(): void
    {
        $migrationMapping = new MigrationMapping(
            null,
            collect('mock-internal-value'),
            collect('mock-foreign-value'),
            'mock-branch-id',
            DataPoolType::MEMBERSHIP_ID()
        );

        $migrationMapping = $this->migrationMappingRepository->save($migrationMapping);

        $this->migrationMappingRepository->delete($migrationMapping);

        $migrationMapping = $this->migrationMappingRepository
            ->addCriteria(new Id($migrationMapping->id()))
            ->first();

        self::assertNull($migrationMapping);
    }
}
