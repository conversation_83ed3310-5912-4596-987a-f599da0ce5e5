<?php

class CourseFixture extends CakeTestFixture
{
    public $import = ['model' => 'Course'];

    public function init()
    {
        $this->records = [
            [
                '_id' => new MongoId('52a7011a05c677bda826611b'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'allowed_member_types' => [
                    [
                        'price' => 8,
                        'memberships' => [
                            'id' => '5501c022d7b6dd2c23d3d476',
                            'label' => 'New membership',
                        ],
                        'type' => 'member',
                        'membership_id' => '5501c022d7b6dd2c23d3d476',
                    ],
                    [
                        'price' => 8,
                        'memberships' => [
                            'id' => '54ef2e04d7b6dd2d008b4567',
                            'label' => 'Weekly',
                        ],
                        'type' => 'member',
                        'membership_id' => '54ef2e04d7b6dd2d008b4567',
                    ],
                    [
                        'price' => 8,
                        'memberships' => [
                            'id' => 'payg',
                            'label' => 'Pay as you go',
                        ],
                        'type' => 'payg',
                        'membership_id' => 'payg',
                    ],
                ],
                'description' => '3 Month boxing course',
                'name' => 'Boxing course',
                'pricing' => 'CUSTOM',
                'schedule' => [
                    [
                        'start_date' => new MongoDate(strtotime('+1 hour')),
                        'end_date' => new MongoDate(strtotime('+2 hour')),
                        'level' => [
                            'id' => 0,
                            'label' => 'advanced',
                        ],
                        'days' => [
                            [
                                'days' => [
                                    [
                                        'id' => 1,
                                        'label' => 'MON',
                                    ],
                                ],
                                'id' => '2e7290f47aa8474d819ff01e28ee4302',
                                'start_time' => '2016-07-08T13:00:09.000Z',
                                'end_time' => '2016-07-08T13:00:09.000Z',
                                'trainers' => ['58568a8fa875ab19530041a7'],
                                'facility' => '52a7011a05c677bda826611b',
                                'label' => 'Boxing Basics',
                                '_start_time' => '2015-02-18T10:00:12.338Z',
                                '_end_time' => '2015-02-18T11:45:12.343Z',
                            ],
                        ],
                        'id' => '4e0a121e958c4582b4c9327e6baee9c1',
                        'advanced' => true,
                        'course_id' => '54e46288d7b6dd78608b456b',
                        'label' => 'Boxing course For everyone',
                        'trainers' => [],
                        'size' => 100,
                    ],
                ],
                'trainers' => ['58568a8fa875ab19530041a7'],
                'facility' => '52a7011a05c677bda826611b',
                'private' => false,
            ],
            [
                '_id' => new MongoId('52a7011a05c677bda826611c'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'allowed_member_types' => [
                    [
                        'price' => 8,
                        'memberships' => [
                            'id' => '5501c022d7b6dd2c23d3d476',
                            'label' => 'New membership',
                        ],
                        'type' => 'member',
                        'membership_id' => '5501c022d7b6dd2c23d3d476',
                    ],
                    [
                        'price' => 8,
                        'memberships' => [
                            'id' => '54ef2e04d7b6dd2d008b4567',
                            'label' => 'Weekly',
                        ],
                        'type' => 'member',
                        'membership_id' => '54ef2e04d7b6dd2d008b4567',
                    ],
                    [
                        'price' => 8,
                        'memberships' => [
                            'id' => 'payg',
                            'label' => 'Pay as you go',
                        ],
                        'type' => 'payg',
                        'membership_id' => 'payg',
                    ],
                ],
                'description' => '3 Month boxing course',
                'name' => 'Boxing course',
                'pricing' => 'CUSTOM',
                'schedule' => [
                    [
                        'start_date' => new MongoDate(strtotime('+124 hour')),
                        'end_date' => new MongoDate(strtotime('+125 hour')),
                        'level' => [
                            'id' => 0,
                            'label' => 'advanced',
                        ],
                        'days' => [
                            [
                                'days' => [
                                    [
                                        'id' => 1,
                                        'label' => 'MON',
                                    ],
                                ],
                                'id' => '2e7290f47aa8474d819ff01e28ee4302',
                                'start_time' => '2016-07-08T13:00:09.000Z',
                                'end_time' => '2016-07-08T13:00:09.000Z',
                                'trainers' => ['58568a8fa875ab19530041a7'],
                                'facility' => '52a7011a05c677bda826611b',
                                'label' => 'Boxing Basics',
                                '_start_time' => '2015-02-18T10:00:12.338Z',
                                '_end_time' => '2015-02-18T11:45:12.343Z',
                            ],
                        ],
                        'id' => '4e0a121e958c4582b4c9327e6baee9c2',
                        'advanced' => true,
                        'course_id' => '54e46288d7b6dd78608b456b',
                        'label' => 'Boxing course For everyone +124',
                        'trainers' => [],
                        'size' => 100,
                    ],
                ],
                'trainers' => ['58568a8fa875ab19530041a7'],
                'facility' => '52a7011a05c677bda826611b',
                'private' => false,
            ],
            [
                '_id' => new MongoId('60f934927a75506e58089e81'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'schedule' => [
                    0 => [
                        'size' => 100,
                        'days' => [
                            0 => [
                                'days' => [
                                    0 => [
                                        'label' => 'SUN',
                                        'id' => 0,
                                        'active' => true,
                                    ],
                                ],
                                '_start_time' => new MongoDate(strtotime('next Sunday +5 hours')),
                                '_end_time' => new MongoDate(strtotime('next Sunday +6 hours')),
                                'start_time' => new MongoDate(strtotime('next Sunday +5 hours')),
                                'end_time' => new MongoDate(strtotime('next Sunday +6 hours')),
                                'active' => true,
                                'facility' => ['5892221cdd3c2f66618b4567'],
                                'trainers' => [
                                    '58568a8fa875ab19530041a7',
                                    '58568a8fa875ab19630041a7'
                                ],
                                'size' => 100,
                                'id' => 1_626_944_650_841,
                            ],
                        ],
                        'trainers' => [
                            '59a7011a05c677bda916612c'
                        ],
                        'facility' => [
                        ],
                        'id' => 1_626_944_650_129,
                        'start_date' => new MongoDate(strtotime('next Sunday +5 hours')),
                        'end_date' => new MongoDate(strtotime('next Sunday +6 hours')),
                        'label' => 'Test Course for testing trainers',
                    ],
                ],
                'name' => 'Test Course for testing trainers',
                'description' => 'Test Coursefor testing trainers',
                'facility' => '5892221cdd3c2f66618b4567',
                'trainers' => [
                    '59a7011a05c677bda916612c',
                ],
                'private' => false,
                'allowed_member_types' => [
                ],
                'pricing' => 'SINGLE',
                'modified' => new MongoDate(strtotime('-7 days')),
                'created' => new MongoDate(strtotime('-7 days')),
            ],
            [
                '_id' => new MongoId('60f934927a75506e58089e82'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'schedule' => [
                    0 => [
                        'size' => 100,
                        'days' => [
                            0 => [
                                'days' => [
                                    0 => [
                                        'label' => 'SUN',
                                        'id' => 0,
                                        'active' => true,
                                    ],
                                ],
                                '_start_time' => new MongoDate(strtotime('next Sunday +5 hours')),
                                '_end_time' => new MongoDate(strtotime('next Sunday +6 hours')),
                                'start_time' => new MongoDate(strtotime('next Sunday +5 hours')),
                                'end_time' => new MongoDate(strtotime('next Sunday +6 hours')),
                                'active' => true,
                                'facility' => ['5892221cdd3c2f66618b4567'],
                                'trainers' => [
                                ],
                                'size' => 100,
                                'id' => 1_626_944_650_841,
                            ],
                        ],
                        'trainers' => [
                            '59a7011a05c677bda916612c'
                        ],
                        'facility' => [
                        ],
                        'id' => 1_626_944_650_129,
                        'start_date' => new MongoDate(strtotime('next Sunday +5 hours')),
                        'end_date' => new MongoDate(strtotime('next Sunday +6 hours')),
                        'label' => 'Test Course for testing trainers',
                    ],
                ],
                'name' => 'Test Course for testing trainers',
                'description' => 'Test Coursefor testing trainers',
                'facility' => '5892221cdd3c2f66618b4567',
                'trainers' => [
                    '59a7011a05c677bda916612c',
                ],
                'private' => false,
                'allowed_member_types' => [
                ],
                'pricing' => 'SINGLE',
                'modified' => new MongoDate(strtotime('-7 days')),
                'created' => new MongoDate(strtotime('-7 days')),
            ],
            [
                '_id' => new MongoId('52a7011a05c677bda826611a'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'allowed_member_types' => [
                    [
                        'price' => 5,
                        'type' => 'member',
                    ],
                ],
                'description' => '3 Month boxing course',
                'name' => 'Boxing course',
                'pricing' => 'CUSTOM',
                'schedule' => [
                    [
                        'start_date' => new MongoDate(strtotime('+1 hour')),
                        'end_date' => new MongoDate(strtotime('+2 hour')),
                        'level' => [
                            'id' => 0,
                            'label' => 'advanced',
                        ],
                        'days' => [
                            [
                                'days' => [
                                    [
                                        'id' => 1,
                                        'label' => 'MON',
                                    ],
                                ],
                                'id' => '2e7290f47aa8474d819ff01e28ee4302',
                                'start_time' => '2016-07-08T13:00:09.000Z',
                                'end_time' => '2016-07-08T13:00:09.000Z',
                                'trainers' => ['58568a8fa875ab19530041a7'],
                                'facility' => '52a7011a05c677bda826611b',
                                'label' => 'Boxing Basics',
                                '_start_time' => '2015-02-18T10:00:12.338Z',
                                '_end_time' => '2015-02-18T11:45:12.343Z',
                            ],
                        ],
                        'id' => '4e0a121e958c4582b4c9327e6baee9c5',
                        'advanced' => true,
                        'course_id' => '54e46288d7b6dd78608b456b',
                        'label' => 'Boxing course For everyone',
                        'trainers' => [],
                        'size' => 100,
                    ],
                ],
                'trainers' => ['58568a8fa875ab19530041a7'],
                'facility' => '52a7011a05c677bda826611b',
                'private' => false,
            ],
            ...$this->addCoursesForBookingsControllerTestDateRange(),
            ...$this->addCoursesForTotalBookingsAndTotalWaiting(),
            ...$this->addCoursesForWinterTzTest(),
            ...$this->addCoursesForSummerTzTest(),
            ...$this->addCoursesForGetAllByLocationId()
        ];
        parent::init();
    }

    private function addCoursesForBookingsControllerTestDateRange(): array
    {
        return [
            [
                '_id' => new MongoId('63e4a5eb27cbc28dc5b99ce5'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'allowed_member_types' => [
                    [
                        'price' => 8,
                        'memberships' => [
                            'id' => '5501c022d7b6dd2c23d3d476',
                            'label' => 'New membership',
                        ],
                        'type' => 'member',
                        'membership_id' => '5501c022d7b6dd2c23d3d476',
                    ],
                    [
                        'price' => 8,
                        'memberships' => [
                            'id' => '54ef2e04d7b6dd2d008b4567',
                            'label' => 'Weekly',
                        ],
                        'type' => 'member',
                        'membership_id' => '54ef2e04d7b6dd2d008b4567',
                    ],
                    [
                        'price' => 8,
                        'memberships' => [
                            'id' => 'payg',
                            'label' => 'Pay as you go',
                        ],
                        'type' => 'payg',
                        'membership_id' => 'payg',
                    ],
                ],
                'description' => 'Testing date range course, "copy" of course 52a7011a05c677bda826611c',
                'name' => 'Test date range course 1',
                'pricing' => 'CUSTOM',
                'schedule' => [
                    [
                        'start_date' => new MongoDate(strtotime('2016-07-08 00:00:00')),
                        'end_date' => new MongoDate(strtotime('2016-31-01 23:59:59')),
                        'level' => [
                            'id' => 0,
                            'label' => 'advanced',
                        ],
                        'days' => [
                            [
                                'days' => [
                                    [
                                        'id' => 1,
                                        'label' => 'MON',
                                    ],
                                ],
                                'id' => '2e7290f47aa8474d819ff01e28ee4302',
                                'start_time' => '2016-07-08T13:00:09.000Z',
                                'end_time' => '2016-07-08T14:00:09.000Z',
                                'trainers' => ['58568a8fa875ab19530041a7'],
                                'facility' => '52a7011a05c677bda826611b',
                                'label' => 'Test date range course 1',
                                '_start_time' => '2015-02-18T10:00:12.338Z',
                                '_end_time' => '2015-02-18T11:45:12.343Z',
                            ],
                        ],
                        'id' => 1_663_080_966_272,
                        'advanced' => true,
                        'course_id' => '63e4a5eb27cbc28dc5b99ce5',
                        'label' => 'Test date range course 1 1663080966272',
                        'trainers' => [],
                        'size' => 100,
                    ],
                ],
                'trainers' => ['58568a8fa875ab19530041a7'],
                'facility' => '52a7011a05c677bda826611b',
                'private' => false,
            ],
        ];
    }

    private function addCoursesForTotalBookingsAndTotalWaiting(): array
    {
        return [
            [
                '_id' => new MongoId('63e4a5eb27cbc28dc5b99ce6'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'allowed_member_types' => [
                    [
                        'price' => 8,
                        'memberships' => [
                            'id' => '5501c022d7b6dd2c23d3d476',
                            'label' => 'New membership',
                        ],
                        'type' => 'member',
                        'membership_id' => '5501c022d7b6dd2c23d3d476',
                    ],
                    [
                        'price' => 8,
                        'memberships' => [
                            'id' => '54ef2e04d7b6dd2d008b4567',
                            'label' => 'Weekly',
                        ],
                        'type' => 'member',
                        'membership_id' => '54ef2e04d7b6dd2d008b4567',
                    ],
                    [
                        'price' => 8,
                        'memberships' => [
                            'id' => 'payg',
                            'label' => 'Pay as you go',
                        ],
                        'type' => 'payg',
                        'membership_id' => 'payg',
                    ],
                ],
                'description' => 'Course with 5 bookings',
                'name' => 'Course with 5 bookings',
                'pricing' => 'CUSTOM',
                'schedule' => [
                    [
                        'start_date' => new MongoDate(strtotime('2016-07-08 00:00:00')),
                        'end_date' => new MongoDate(strtotime('2016-31-01 23:59:59')),
                        'level' => [
                            'id' => 0,
                            'label' => 'advanced',
                        ],
                        'days' => [
                            [
                                'days' => [
                                    [
                                        'id' => 1,
                                        'label' => 'MON',
                                    ],
                                ],
                                'id' => '2e7290f47aa8474d819ff01e28ee4302',
                                'start_time' => '2016-07-08T13:00:09.000Z',
                                'end_time' => '2016-07-08T13:00:09.000Z',
                                'trainers' => ['58568a8fa875ab19530041a7'],
                                'facility' => '52a7011a05c677bda826611b',
                                'label' => 'Course Bookings',
                                '_start_time' => '2015-02-18T10:00:12.338Z',
                                '_end_time' => '2015-02-18T11:45:12.343Z',
                            ],
                        ],
                        'id' => '4e0a121e958c4582b4c9327e6baee9s3',
                        'advanced' => true,
                        'course_id' => '63e4a5eb27cbc28dc5b99ce6',
                        'label' => 'Course with 5 bookings',
                        'trainers' => [],
                        'size' => 100,
                    ],
                ],
                'trainers' => ['58568a8fa875ab19530041a7'],
                'facility' => '52a7011a05c677bda826611b',
                'private' => false,
            ],
            [
                '_id' => new MongoId('63e4a5eb27cbc28dc5b99ce7'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'schedule' => [
                    0 => [
                        'size' => 100,
                        'days' => [
                            0 => [
                                'days' => [
                                    0 => [
                                        'label' => 'SUN',
                                        'id' => 0,
                                        'active' => true,
                                    ],
                                ],
                                '_start_time' => new MongoDate(strtotime('next Sunday +5 hours')),
                                '_end_time' => new MongoDate(strtotime('next Sunday +6 hours')),
                                'start_time' => new MongoDate(strtotime('next Sunday +5 hours')),
                                'end_time' => new MongoDate(strtotime('next Sunday +6 hours')),
                                'active' => true,
                                'facility' => ['5892221cdd3c2f66618b4567'],
                                'trainers' => [
                                    '58568a8fa875ab19530041a7',
                                    '58568a8fa875ab19630041a7'
                                ],
                                'size' => 100,
                                'id' => 1_626_944_650_841,
                            ],
                        ],
                        'trainers' => [
                            '59a7011a05c677bda916612c'
                        ],
                        'facility' => [
                        ],
                        'id' => 1_626_944_650_130,
                        'start_date' => new MongoDate(strtotime('next Sunday +5 hours')),
                        'end_date' => new MongoDate(strtotime('next Sunday +6 hours')),
                        'label' => 'Test Course for testing trainers',
                    ],
                    1 => [
                        'size' => 100,
                        'days' => [
                            0 => [
                                'days' => [
                                    0 => [
                                        'label' => 'MON',
                                        'id' => 0,
                                        'active' => true,
                                    ],
                                ],
                                '_start_time' => new MongoDate(strtotime('next Monday +5 hours')),
                                '_end_time' => new MongoDate(strtotime('next Monday +6 hours')),
                                'start_time' => new MongoDate(strtotime('next Monday +5 hours')),
                                'end_time' => new MongoDate(strtotime('next Monday +6 hours')),
                                'active' => true,
                                'facility' => ['5892221cdd3c2f66618b4567'],
                                'trainers' => [
                                    '58568a8fa875ab19530041a7',
                                    '58568a8fa875ab19630041a7'
                                ],
                                'size' => 100,
                                'id' => 1_626_944_650_842,
                            ],
                        ],
                        'trainers' => [
                            '59a7011a05c677bda916612c'
                        ],
                        'facility' => [
                        ],
                        'id' => 1_626_944_650_131,
                        'start_date' => new MongoDate(strtotime('next Monday +5 hours')),
                        'end_date' => new MongoDate(strtotime('next Monday +6 hours')),
                        'label' => 'Test Course for testing trainers',
                    ],
                ],
                'name' => 'Test Course',
                'description' => 'Test Course',
                'facility' => '5892221cdd3c2f66618b4567',
                'trainers' => [
                    '59a7011a05c677bda916612c',
                ],
                'private' => false,
                'allowed_member_types' => [
                ],
                'pricing' => 'SINGLE',
                'modified' => new MongoDate(strtotime('-7 days')),
                'created' => new MongoDate(strtotime('-7 days')),
            ],
        ];
    }

    private function addCoursesForWinterTzTest(): array
    {
        $startTime = new MongoDate(strtotime('2023-10-29 05:00:00'));
        $endTime = new MongoDate(strtotime('2023-10-29 06:00:00'));

        return [
            [
                '_id' => new MongoId('64182763a4b02bde5aea5ad4'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'schedule' => [
                    0 => [
                        'size' => 100,
                        'days' => [
                            0 => [
                                'days' => [
                                    0 => [
                                        'label' => 'SUN',
                                        'id' => 0,
                                        'active' => true,
                                    ],
                                ],
                                '_start_time' => $startTime,
                                '_end_time' => $endTime,
                                'start_time' => $startTime,
                                'end_time' => $endTime,
                                'active' => true,
                                'facility' => ['5892221cdd3c2f66618b4567'],
                                'trainers' => [
                                    '58568a8fa875ab19530041a7',
                                    '58568a8fa875ab19630041a7'
                                ],
                                'size' => 100,
                                'id' => 1_626_944_650_843,
                            ],
                        ],
                        'trainers' => [
                            '59a7011a05c677bda916612c'
                        ],
                        'facility' => [
                        ],
                        'id' => 1_626_944_650_140,
                        'start_date' => $startTime,
                        'end_date' => $endTime,
                        'label' => 'Test Course for testing trainers',
                    ],
                ],
                'name' => 'Test Course for testing trainers',
                'description' => 'Test Coursefor testing trainers',
                'facility' => '5892221cdd3c2f66618b4567',
                'trainers' => [
                    '59a7011a05c677bda916612c',
                ],
                'private' => false,
                'allowed_member_types' => [
                ],
                'pricing' => 'SINGLE',
                'modified' => new MongoDate(strtotime('-7 days')),
                'created' => new MongoDate(strtotime('-7 days')),
            ],
        ];
    }

    private function addCoursesForSummerTzTest(): array
    {
        $startTime = new MongoDate(strtotime('2023-03-26 05:00:00'));
        $endTime = new MongoDate(strtotime('2023-03-26 06:00:00'));

        return [
            [
                '_id' => new MongoId('6418297ef5617ea3f45a699e'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'schedule' => [
                    0 => [
                        'size' => 100,
                        'days' => [
                            0 => [
                                'days' => [
                                    0 => [
                                        'label' => 'SUN',
                                        'id' => 0,
                                        'active' => true,
                                    ],
                                ],
                                '_start_time' => $startTime,
                                '_end_time' => $endTime,
                                'start_time' => $startTime,
                                'end_time' => $endTime,
                                'active' => true,
                                'facility' => ['5892221cdd3c2f66618b4567'],
                                'trainers' => [
                                    '58568a8fa875ab19530041a7',
                                    '58568a8fa875ab19630041a7'
                                ],
                                'size' => 100,
                                'id' => 1_626_944_650_842,
                            ],
                        ],
                        'trainers' => ['59a7011a05c677bda916612c'],
                        'facility' => [],
                        'id' => 1_626_944_650_141,
                        'start_date' => $startTime,
                        'end_date' => $endTime,
                        'label' => 'Test Course for testing trainers',
                    ],
                ],
                'name' => 'Test Course for testing trainers',
                'description' => 'Test Coursefor testing trainers',
                'facility' => '5892221cdd3c2f66618b4567',
                'trainers' => ['59a7011a05c677bda916612c'],
                'private' => false,
                'allowed_member_types' => [],
                'pricing' => 'SINGLE',
                'modified' => new MongoDate(strtotime('-7 days')),
                'created' => new MongoDate(strtotime('-7 days')),
            ],
        ];
    }

    public function addCoursesForGetAllByLocationId(): array
    {
        $locationId = '6852c7542e0ad105d38bd320';
        $startTime = new MongoDate(strtotime('2025-01-10 09:00:00'));
        $endTime = new MongoDate(strtotime('2025-02-10 15:00:00'));

        return [
            [
                '_id' => new MongoId('6418297ef5617ea3f45a699f'),
                'branch_id' => $locationId,
                'namespace' => 'glofox',
                'active' => true,
                'schedule' => [
                    0 => [
                        'size' => 50,
                        'days' => [
                            0 => [
                                'days' => [
                                    0 => [
                                        'label' => 'MON',
                                        'id' => 0,
                                        'active' => true,
                                    ],
                                ],
                                'start_time' => $startTime,
                                'end_time' => $endTime,
                                'active' => true,
                                'facility' => ['5892221cdd3c2f66618b4567'],
                                'trainers' => [
                                    '58568a8fa875ab19530041a7',
                                    '58568a8fa875ab19630041a7'
                                ],
                                'size' => 100,
                                'id' => 1_555_989_890_000,
                            ],
                        ],
                        'trainers' => ['59a7011a05c677bda916612c'],
                        'facility' => [],
                        'id' => 1_773_154_321_000,
                        'start_date' => $startTime,
                        'end_date' => $endTime,
                        'label' => 'Test Course for Get All By Location',
                    ],
                ],
                'name' => 'Test Course for Get All By Location 1',
                'description' => 'Test Course for Get All By Location 1',
                'facility' => '5892221cdd3c2f66618b4567',
                'trainers' => ['59a7011a05c677bda916612c'],
                'private' => false,
                'allowed_member_types' => [],
                'pricing' => 'SINGLE',
                'modified' => new MongoDate(strtotime('-7 days')),
                'created' => new MongoDate(strtotime('-7 days')),
            ],
            [
                '_id' => new MongoId('6418297ef5617ea3f45a69a0'),
                'branch_id' => $locationId,
                'namespace' => 'glofox',
                'active' => true,
                'schedule' => [
                    0 => [
                        'size' => 50,
                        'days' => [
                            0 => [
                                'days' => [
                                    0 => [
                                        'label' => 'WED',
                                        'id' => 0,
                                        'active' => true,
                                    ],
                                ],
                                'start_time' => $startTime,
                                'end_time' => $endTime,
                                'active' => true,
                                'facility' => ['5892221cdd3c2f66618b4567'],
                                'trainers' => [
                                    '58568a8fa875ab19530041a7',
                                    '58568a8fa875ab19630041a7'
                                ],
                                'size' => 50,
                                'id' => 1_773_154_321_001,
                            ],
                        ],
                        'trainers' => ['59a7011a05c677bda916612c'],
                        'facility' => [],
                        'id' => 1_626_944_650_143,
                        'start_date' => $startTime,
                        'end_date' => $endTime,
                        'label' => 'Test Course for Get All By Location 2',
                    ],
                ],
                'name' => 'Test Course for Get All By Location 2',
                'description' => 'Test Course for Get All By Location 2',
                'facility' => '5892221cdd3c2f66618b4567',
                'trainers' => ['59a7011a05c677bda916612c'],
                'private' => false,
                'allowed_member_types' => [],
                'pricing' => 'SINGLE',
                'modified' => new MongoDate(strtotime('-7 days')),
                'created' => new MongoDate(strtotime('-7 days')),
            ],
            [
                '_id' => new MongoId('6418297ef5617ea3f45a69a1'),
                'branch_id' => $locationId,
                'namespace' => 'glofox',
                'active' => true,
                'schedule' => [
                    0 => [
                        'size' => 50,
                        'days' => [
                            0 => [
                                'days' => [
                                    0 => [
                                        'label' => 'FRI',
                                        'id' => 0,
                                        'active' => true,
                                    ],
                                ],
                                'start_time' => $startTime,
                                'end_time' => $endTime,
                                'active' => true,
                                'facility' => ['5892221cdd3c2f66618b4567'],
                                'trainers' => [
                                    '58568a8fa875ab19530041a7',
                                    '58568a8fa875ab19630041a7'
                                ],
                                'size' => 50,
                                'id' => 1_773_154_321_002,
                            ],
                        ],
                        'trainers' => ['59a7011a05c677bda916612c'],
                        'facility' => [],
                        'id' => 1_626_944_650_143,
                        'start_date' => $startTime,
                        'end_date' => $endTime,
                        'label' => 'Test Course for Get All By Location 3',
                    ],
                ],
                'name' => 'Test Course for Get All By Location 3',
                'description' => 'Test Course for Get All By Location 3',
                'facility' => '5892221cdd3c2f66618b4567',
                'trainers' => ['59a7011a05c677bda916612c'],
                'private' => false,
                'allowed_member_types' => [],
                'pricing' => 'SINGLE',
                'modified' => new MongoDate(strtotime('-7 days')),
                'created' => new MongoDate(strtotime('-7 days')),
            ],
        ];
    }
}
