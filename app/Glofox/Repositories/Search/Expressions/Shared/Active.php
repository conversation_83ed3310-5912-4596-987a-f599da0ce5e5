<?php

namespace Glofox\Repositories\Search\Expressions\Shared;

use Glofox\Repositories\Filters\Evaluable;
use Glofox\Repositories\Search\Expressions\Conditionable;
use Glofox\Repositories\Search\Expressions\HasConditions;
use Glofox\Repositories\Search\Expressions\Key;
use Web<PERSON>zart\Expression\Expr;

class Active extends Key implements Evaluable, Conditionable
{
    use HasConditions;

    public function __construct(bool $active)
    {
        parent::__construct('active', Expr::equals($active));
    }

    public function field(): string
    {
        return 'active';
    }
}
