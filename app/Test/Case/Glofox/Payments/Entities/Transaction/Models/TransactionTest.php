<?php

namespace CakeTestCases\Glofox\Payments\Entities\Transaction\Models;

use Glofox\Payments\Entities\Transaction\Models\Transaction;
use Glofox\Payments\Entities\Transaction\Status;

\App::import('Test/Case', 'GlofoxTestCase');

class TransactionTest extends \GlofoxTestCase
{
    public function testIsPending()
    {
        $t = new Transaction();
        self::assertFalse($t->isPendingOrPendingIntent());

        $t->setStatus(Status::PAYMENT_STATUS_SUCCESS);
        self::assertFalse($t->isPendingOrPendingIntent());

        $t->setStatus(Status::PAYMENT_STATUS_ERROR);
        self::assertFalse($t->isPendingOrPendingIntent());

        $t->setStatus(STatus::PAYMENT_STATUS_PENDING);
        self::assertTrue($t->isPendingOrPendingIntent());

        $t->setStatus(STatus::PAYMENT_STATUS_PENDING_INTENT);
        self::assertTrue($t->isPendingOrPendingIntent());
    }

    public function testServiceProviderIntentId()
    {
        $t = new Transaction();

        // nulls
        $t->setServiceProviderIntentId(null);
        self::assertNull($t->serviceProviderIntentId());

        // positive
        $t->setServiceProviderIntentId('id');
        self::assertEquals('id', $t->serviceProviderIntentId());
    }
}
