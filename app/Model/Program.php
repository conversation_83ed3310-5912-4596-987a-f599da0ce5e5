<?php

App::uses('Facility', 'Model');
App::uses('Branch', 'Model');
App::uses('User', 'Model');

use Glofox\AuditLog\Models\TrackableDataGateway;
use Glofox\Domain\Programs\Services\RoomMapImagineUrlGenerator;

/**
 * @SWG\Definition()
 */
class Program extends TrackableDataGateway
{
    public $primaryKey = '_id';

    public $mongoSchema = [
        //Foreign ids
        /*
         * @SWG\Property(
         *  property="namespace",
         *  type="string",
         *  description="Studio namespace identifier ex. mylocalgym")
         */
        'namespace' => ['type' => 'string'],
        /*
         * @SWG\Property(
         *  property="branch_id",
         *  type="string",
         *  description="Studio location identifier ex. d12fdf3d12gf412")
         */
        'branch_id' => ['type' => 'string'],

        //Properties
        /*
         * @SWG\Property(
         *  property="active",
         *  type="boolean",
         *  description="Status")
         */
        'active' => ['type' => 'boolean'],
        /*
         * @SWG\Property(
         *  property="name",
         *  type="string",
         *  description="Label to display")
         */
        'name' => ['type' => 'string'],
        /*
         * @SWG\Property(
         *  property="description",
         *  type="string",
         *  description="Short description about the program")
         */
        'description' => ['type' => 'string'],
        /*
         * @SWG\Property(
         *  property="date_start",
         *  type="string",
         *  description="")
         */
        'date_start' => ['type' => 'datetime'],
        /*
         * @SWG\Property(
         *  property="date_finish",
         *  type="string",
         *  description="")
         */
        'date_finish' => ['type' => 'datetime'],
        /*
         * @SWG\Property(
         *  property="categories",
         *  type="string",
         *  description="Categories")
         */
        'categories' => ['type' => 'string'],
        /*
         * @SWG\Property(
         *  property="pricing",
         *  type="string",
         *  description="")
         */
        'pricing' => ['type' => 'string'],
        /*
         * @SWG\Property(
         *  property="private",
         *  type="boolean",
         *  description="")
         */
        'private' => ['type' => 'boolean'],
        /*
         * @SWG\Property(
         *  property="new",
         *  type="boolean",
         *  description="")
         */
        'new' => ['type' => 'boolean'],
        /*
         * @SWG\Property(
         *  property="featured",
         *  type="boolean",
         *  description="")
         */
        'featured' => ['type' => 'boolean'],
        /*
         * @SWG\Property(
         *  property="schedule",
         *  type="array",
         *  description="")
         */
        'schedule' => ['type' => 'array'],
        /*
         * @SWG\Property(
         *  property="schedule_array",
         *  type="array",
         *  description="")
         */
        'schedule_default' => ['type' => 'array'],
        /*
         * @SWG\Property(
         *  property="auto_generated",
         *  type="boolean",
         *  description="")
         */
        'auto_generated' => ['type' => 'boolean'],
        /*
         * @SWG\Property(
         *  property="default_price",
         *  type="number",
         *  description="")
         */
        'default_price' => ['type' => 'float'],
        /*
         * @SWG\Property(
         *  property="allowed_member_types",
         *  type="array",
         *  description="")
         */
        'allowed_member_types' => [   // Array  OPTIONAL ATTRIBUTE
            'type' => ['type' => 'string'],  // PAYG, MEMBER
            'price' => ['type' => 'float'], // OPTIONAL
            'membership_group_id' => ['type' => 'string'],
        ],
        /*
         * @SWG\Property(
         *  property="email_admin",
         *  type="boolean",
         *  description="")
         */
        'email_admin' => ['type' => 'boolean'], // true or false

        'room_map_image_name' => ['type' => 'string'],
        'spot_booking_enabled' => ['type' => 'boolean'],

        //Auto generated by cake
        'created' => ['type' => 'datetime'],
        'modified' => ['type' => 'datetime'],

        // Shard Keys
        'region' => ['type' => 'string'],
        'timestamp' => ['type' => 'timestamp'],

        // Integrations
        'metadata' => [
            'classpass' => [
                'size' => ['type' => 'string'],
            ],
            'gympass' => ['type' => 'boolean'],
        ],
    ];

    public $validate = [
        'name' => [
            'required' => [
                'rule' => ['notBlank'],
                'message' => 'Please insert the class name',
            ],
        ],
        'description' => [
            'required' => [
                'rule' => ['notBlank'],
                'message' => 'Please insert the class description',
            ],
        ],
        'date_start' => [
            'required' => [
                'rule' => ['notBlank'],
                'message' => 'Please insert the class start date',
            ],
            'date' => [
                'rule' => ['date', 'ymd'],
                'message' => 'Enter a valid start date in YYYY-MM-DD format.',
            ],
        ],
    ];

    /*******************************************************************************************
     *                                API 2.0 New Dashboard
     ******************************************************************************************/

    /**
     * @SWG\Get(
     *     path="/programs",
     *     summary="Get list of programs",
     *     description="List of studio programs",
     *     operationId="getPrograms",
     *     tags={"Programs"},
     *     produces={"application/json"},
     *    @SWG\Response(
     *     response=200,
     *     description="A list of programs",
     *             @SWG\Items(
     *                  type="array",
     *                  @SWG\Items(ref="#/definitions/Program")
     *             )
     *   ),
     *   @SWG\Response(
     *      response="400",
     *      description="Invalid input parameters"
     *   ),
     *   @SWG\Response(
     *     response="default",
     *     description="an ""unexpected"" error"
     *   ),
     *   security={{
     *     "api_key":{}
     *   }}
     * )
     *
     * @param array $params
     *
     * @return array
     */
    public function getAll($params)
    {
        $fields = [
            'active',
            'allowed_member_types',
            'branch_id',
            'namespace',
            'categories',
            'date_finish',
            'date_start',
            'default_price',
            'description',
            'featured',
            'name',
            'pricing',
            'spot_booking_enabled',
        ];
        $query = $this->buildPaginatedConditions($fields);

        return $this->paginate($query);
    }

    /**
     * @SWG\Get(
     *     path="/programs/{id}",
     *     summary="Find program by id",
     *     description="Returns a single program by id",
     *     operationId="getProgramById",
     *     tags={"Programs"},
     *     produces={"application/json"},
     *     @SWG\Parameter(
     *         description="Unique identifier of the program ex. 12f3d21fd3ef13f123d12e312",
     *         in="path",
     *         name="id",
     *         required=true,
     *         type="string"
     *     ),
     *     @SWG\Response(
     *         response=200,
     *         description="successful operation",
     *         @SWG\Schema(ref="#/definitions/Program")
     *     ),
     *     @SWG\Response(
     *         response="400",
     *         description="Invalid program id"
     *     ),
     *     @SWG\Response(
     *         response="403",
     *         description="Your API token is invalid or does not grant you privileges for this resource"
     *     ),
     *     @SWG\Response(
     *         response="404",
     *         description="Program not found"
     *     ),
     *     security={{
     *       "api_key":{}
     *     }}
     * )
     *
     * @param string $identifier
     * @param array $params
     *
     * @return array
     */
    public function getById($identifier, $params = [])
    {
        $conditions = $this->injector->getSingleRecordConditions();
        $query = [
            'conditions' => $conditions,
            'fields' => [
                'active',
                'allowed_member_types',
                'branch_id',
                'namespace',
                'categories',
                'date_finish',
                'date_start',
                'default_price',
                'description',
                'featured',
                'name',
                'pricing',
                'schedule',
                'schedule_default',
                'modified',
                'spot_booking_enabled',
            ],
        ];

        $program = $this->find('first', $query);
        $program = $this->mapObject($program);

        return $program;
    }

    public function getByIdWithoutConditions($identifier, $params = [])
    {
        $filters = [];
        $filters['_id'] = new MongoId($identifier);
        $query = [
            'conditions' => $filters,
            'fields' => [
                'active',
                'allowed_member_types',
                'branch_id',
                'namespace',
                'categories',
                'date_finish',
                'date_start',
                'default_price',
                'description',
                'featured',
                'name',
                'allowed_member_types',
                'schedule',
                'schedule_default',
                'modified',
                'spot_booking_enabled',
            ],
        ];

        return $this->find('first', $query);
    }

    /**
     * Create.
     *
     * @param array $data [description]
     * @TODO: implement
     */
    public function post($data)
    {
    }

    /**
     * Update.
     *
     * @param string $identifier
     * @param array $data [description]
     * @TODO: implement
     */
    public function patchById($identifier, $data)
    {
    }

    /**
     * Delete.
     *
     * @param string $identifier
     * @TODO: implement the method
     */
    public function deleteById($identifier)
    {
    }

    /*!
     * Given 2 programs, return a program that ONLY includes added schedules that previously did not exist,
     * These schedules are easy to identify since they don't have a 'code' attribute, which we generate beforesave
     * in this model in an automated fashion
     * @param      [type]                   $program [description]  Input from REST
     * @param      [type]                   $current [description]  Current stored program
     * @return     [type]                            [description]
     */
    public function added($added, $current, $saved)
    {
        //In "saved" we already have the codes for the added elements, get list of usable codes (the ones which were saved, that were not part of the input, same order)
        $savedCodes = array_map([$this, 'getScheduleCodes'], $saved['Program']['schedule']);
        $inputCodes = array_map([$this, 'getScheduleCodes'], $added['schedule']);
        $usableCodes = array_diff($savedCodes, $inputCodes);
        $changes = $this->determine_schedule_changes($current, $added);
        $inserted = array_keys($changes['insert']);

        $schedules = [];
        foreach ($added['schedule'] as $schedule) {
            if (!isset($schedule['code'])) {
                $schedule['code'] = array_shift($usableCodes);
                $schedules[] = $schedule;
            }
        }
        $added['schedule'] = $schedules;

        return $added;
    }

    /*!
     * Given 2 programs, return a program that ONLY includes updated information regarding schedules NO SCHEDULE CHANGES TO DATES,
     * These schedules are easy to identify since they already have a 'code' attribute, which we generate beforesave
     * in this model in an automated fashion
     * @param      [type]                   $program [description]
     * @param      [type]                   $current [description]
     * @return     [type]                            [description]
     */

    /**
     *   Receives two schedule arrays, the first is the one stored in the database and the second is
     *   comes from the form in the update program page.
     */
    public function determine_schedule_changes($original_program, $new_program)
    {
        $original_schedules = $original_program['schedule'];
        $new_schedules = $new_program['schedule'];
        $date_range_changed = (($original_program['date_start'] != $new_program['date_start']) ||
            ($original_program['date_finish'] != $new_program['date_finish']));

        $changes = [];
        $changes['has_changed'] = false;

        // Index new_schedules array
        foreach ($new_schedules as $key => $new_schedule) {
            if (!isset($new_schedule['code'])) {
                $new_schedule['code'] = uniqid('', false);
            }
            $new_schedules[$new_schedule['code']] = $new_schedule;
            unset($new_schedules[$key]);
        }

        // Index original_schedules array
        foreach ($original_schedules as $key => $original_schedule) {
            $original_schedules[$original_schedule['code']] = $original_schedule;
            unset($original_schedules[$key]);
        }

        // Find Inserts
        $changes['insert'] = [];
        foreach ($new_schedules as $key => $new_schedule) {
            if (!array_key_exists($new_schedule['code'], $original_schedules)) {
                $changes['insert'][$new_schedule['code']] = [
                    'schedule' => $new_schedule,
                    'date_range' => [
                        'start' => $new_program['date_start'],
                        'end' => $new_program['date_finish'],
                    ],
                ];
                $changes['has_changed'] = true;
                unset($new_schedules[$key]);
            }
        }

        // Find Deletes
        $changes['delete'] = [];
        foreach ($original_schedules as $key => $original_schedule) {
            if (!array_key_exists($original_schedule['code'], $new_schedules)) {
                $changes['delete'][$original_schedule['code']] = [
                    'schedule' => $original_schedule,
                    'date_range' => [
                        'start' => date('Y-m-d'),
                    ],
                ];
                $changes['has_changed'] = true;
                unset($original_schedules[$key]);
            }
        }

        // Find Updates
        $changes['update'] = [];
        foreach ($original_schedules as $key => $original_schedule) {
            $schedule_changes = [];
            if ($original_schedule['days_week'] != $new_schedules[$original_schedule['code']]['days_week']) {
                $schedule_changes['days_week'] = $new_schedules[$original_schedule['code']]['days_week'];
            }

            if ($original_schedule['start_time'] != $new_schedules[$original_schedule['code']]['start_time']) {
                $schedule_changes['start_time'] = $new_schedules[$original_schedule['code']]['start_time'];
            }

            if ($original_schedule['end_time'] != $new_schedules[$original_schedule['code']]['end_time']) {
                $schedule_changes['end_time'] = $new_schedules[$original_schedule['code']]['end_time'];
            }

            if ($original_schedule['facility'] != $new_schedules[$original_schedule['code']]['facility']) {
                $schedule_changes['facility'] = $new_schedules[$original_schedule['code']]['facility'];
            }

            if ($original_schedule['size'] != $new_schedules[$original_schedule['code']]['size']) {
                $schedule_changes['size'] = $new_schedules[$original_schedule['code']]['size'];
            }

            if (isset($original_schedule['private']) && isset($new_schedules[$original_schedule['code']]['private'])) {
                // FIXME apparently there's no case where the 'private' key exists in a schedule array
                if ($original_schedule['private'] != $new_schedules[$original_schedule['code']]['private']) {
                    $schedule_changes['private'] = $new_schedules[$original_schedule['code']]['private'];
                }
            }

            if (isset($original_schedule['new']) && isset($new_schedules[$original_schedule['code']]['new'])) {
                // FIXME same as above, but for the 'new' key
                if ($original_schedule['new'] != $new_schedules[$original_schedule['code']]['new']) {
                    $schedule_changes['new'] = $new_schedules[$original_schedule['code']]['new'];
                }
            }

            if ($original_schedule['trainers'] != $new_schedules[$original_schedule['code']]['trainers']) {
                $schedule_changes['trainers'] = $new_schedules[$original_schedule['code']]['trainers'];
            }

            if ($original_schedule['level'] != $new_schedules[$original_schedule['code']]['level']) {
                $schedule_changes['level'] = $new_schedules[$original_schedule['code']]['level'];
            }

            $schedule_changed = !empty($schedule_changes);
            if ($schedule_changed || $date_range_changed) {
                $has_to_reschedule = $this->determine_has_to_reschedule($schedule_changes);
                $changes['update'][$original_schedule['code']]['date_range'] = $this->determine_schedule_date_range(
                    $original_program,
                    $new_program,
                    $schedule_changed
                );
                $changes['update'][$original_schedule['code']]['has_to_reschedule'] = $has_to_reschedule;
                $changes['update'][$original_schedule['code']]['schedule'] = $this->build_schedule(
                    $original_schedule,
                    $schedule_changes,
                    $has_to_reschedule
                );
                $changes['update'][$original_schedule['code']]['changes'] = $schedule_changes;
                $changes['has_changed'] = true;
            }
        }

        return $changes;
    }

    /*!
     * Given 2 programs, return a program that includes RESCHEDULING,
     * These schedules are easy to identify since they already have a 'code' attribute, which we generate beforesave
     * in this model in an automated fashion
     * @param      [type]                   $program [description]
     * @param      [type]                   $current [description]
     * @return     [type]                            [description]
     */

    public function determine_has_to_reschedule($schedule_changes)
    {
        return
            array_key_exists('days_week', $schedule_changes) ||
            array_key_exists('start_time', $schedule_changes) ||
            array_key_exists('end_time', $schedule_changes);
    }

    /*!
     * Given 2 programs, return a program that ONLY includes removed schedules that previously existed and now are not,
     * found in the first parameter of this method
     * @param      [type]                   $program [description]
     * @param      [type]                   $current [description]
     * @return     [type]                            [description]
     */

    public function determine_schedule_date_range($original_program, $new_program, $schedule_changed)
    {
        $user = $this->getUser();
        $timezone = (new Branch())->getTimeZone($user['branch_id']);
        $currentTime = \Carbon\Carbon::now($timezone)->format('Y-m-d H:i:s');
        $date_range = [];
        $date_range_changed = (($original_program['date_start'] != $new_program['date_start']) ||
            ($original_program['date_finish'] != $new_program['date_finish']));

        // Date Range Changed but the Schedule was not changed
        if ($date_range_changed && !$schedule_changed) {
            $date_range['start'] = (strtotime($new_program['date_start']) > strtotime($currentTime)) ?
                $new_program['date_start'] :
                $currentTime;
            $date_range['end'] = $new_program['date_finish'];
        }
        // Date Range Changed and the Schedule changed
        elseif ($date_range_changed && $schedule_changed) {
            $date_range['start'] = (strtotime($new_program['date_start']) < strtotime($currentTime)) ?
                $currentTime :
                $new_program['date_start'];
            $date_range['end'] = $new_program['date_finish'];
        }
        // Date Range was not changed and the Schedule changed
        else {   // (!$date_range_changed && $schedule_changed)
            $date_range['start'] = (strtotime($original_program['date_start']) < strtotime($currentTime)) ?
                $currentTime :
                $original_program['date_start'];
            $date_range['end'] = $original_program['date_finish'];
        }

        return $date_range;
    }

    public function build_schedule($original_schedule, $schedule_changes, $has_to_reschedule)
    {
        $schedule = [];
        foreach ($original_schedule as $key => $value) {
            $schedule[$key] = array_key_exists($key, $schedule_changes) ? $schedule_changes[$key] : $value;
        }

        return $schedule;
    }

    /*!
     * Map function to return schedule codes
     * @param      [type]                   $schedule [description]
     * @return     [type]                             [description]
     */

    public function edited($added, $current)
    {
        $changes = $this->determine_schedule_changes($current, $added);
        $updated = array_keys($changes['update']);
        $schedules = [];
        foreach ($added['schedule'] as $schedule) {
            if (
                !isset($changes['update'][$schedule['code']]['changes']['days_week']) &&
                in_array($schedule['code'], $updated)
            ) {
                $schedules[] = $schedule;
            }
        }
        $added['schedule'] = $schedules;

        return $added;
    }

    /*!
     * What fields are to be taken into consideration while doing global search by string
     * @example    https://www.glofoxlogin/
     * @return     [type]                   [description]
     */

    public function rescheduled($added, $current)
    {
        $changes = $this->determine_schedule_changes($current, $added);
        $updated = array_keys($changes['update']);
        $schedules = [];
        foreach ($added['schedule'] as $schedule) {
            if (
                isset($changes['update'][$schedule['code']]['changes']['days_week']) &&
                in_array($schedule['code'], $updated)
            ) {
                $schedules[] = $schedule;
            }
        }
        $added['schedule'] = $schedules;

        return $added;
    }

    // CALLBACKS

    public function removed($added, $current)
    {
        $changes = $this->determine_schedule_changes($current, $added);
        $updated = array_keys($changes['delete']);
        $schedules = [];
        foreach ($current['schedule'] as $schedule) {
            if (in_array($schedule['code'], $updated)) {
                $schedules[] = $schedule;
            }
        }
        $added['schedule'] = $schedules;

        return $added;
    }

    /**
     * This function receives the program that we want to update
     * and the program stored in the database and is going to
     * validate if the basic information has been changed, in order
     * to know if we need to update that info in the events.
     *
     * @param [type] $added   [description]
     * @param [type] $current [description]
     *
     * @return array [changes]
     * @deprecated use { app/Model/Program::getDetailedProgramInfoChanges } instead
     */
    public function getBasicProgramInfoChanges($added, $current): array
    {
        $changes = [];
        $fields_to_evaluate = ['name', 'description', 'date_start', 'date_finish'];

        foreach ($fields_to_evaluate as $field) {
            if ($added[$field] != $current[$field]) {
                if ($this->contains('date', $field)) {
                    $changes[$field] = (strtotime($added[$field]) > strtotime($current[$field]))
                        ? ['greater' => $current[$field]]
                        : ['less' => $current[$field]];
                } else {
                    $changes[$field] = $field;
                }
            }
        }

        return $changes;
    }

    public function getDetailedProgramInfoChanges($added, $current): array
    {
        $changes = [];
        $fields = [
            'name', 'description', 'date_start', 'date_finish', 'schedule_default', 'private', 'categories',
            'metadata', 'spot_booking_enabled', 'taxes', 'allowed_member_types', 'email_admin'
        ];

        foreach ($fields as $field) {
            if (!array_key_exists($field, $added) || !array_key_exists($field, $current)) {
                continue;
            }

            // handle sub-arrays
            if (is_array($added[$field]) && is_array($current[$field])) {
                $arrayChanges = $this->compareArrays($added[$field], $current[$field]);
                if (!empty($arrayChanges)) {
                    $changes[$field] = $arrayChanges;
                }
            }

            // handle simple values
            if (!is_array($added[$field]) && $added[$field] != $current[$field]) {
                $changes[$field] = $this->contains('date', $field)
                    ? ((strtotime($added[$field]) > strtotime($current[$field]))
                        ? ['greater' => $current[$field]]
                        : ['less' => $current[$field]])
                    : $added[$field];
            }
        }

        return $changes;
    }

    /**
     * Compare two arrays recursively and return only the changed items.
     */
    private function compareArrays(array $added, array $current): array
    {
        $changes = [];

        foreach ($added as $key => $value) {
            if (array_key_exists($key, $current)) {
                if (is_array($value) && is_array($current[$key])) {
                    // Compare nested arrays
                    $nestedChanges = $this->compareArrays($value, $current[$key]);
                    if (!empty($nestedChanges)) {
                        $changes[$key] = $nestedChanges;
                    }
                } elseif ($value != $current[$key]) {
                    // Compare simple values
                    $changes[$key] = $value;
                }
            } else {
                // new change
                $changes[$key] = $value;
            }
        }

        return $changes;
    }

    // CUSTOM FUNCTIONS

    public function getScheduleCodes($schedule)
    {
        if (isset($schedule['code'])) {
            return $schedule['code'];
        }
    }

    public function searchable()
    {
        return ['name', 'description'];
    }

    public function afterFind($results, $primary = false)
    {
        $results = $this->convertFieldsToDate($results, ['date_start', 'date_finish'], 'date');

        // @see https://glofox.atlassian.net/browse/DASH2-3444
        foreach ($results as &$row) {
            if (!isset($row['Program'])) {
                continue;
            }

            $program = $row['Program'];

            if (!isset($program['allowed_member_types'])) {
                continue;
            }

            if (!is_array($program['allowed_member_types']) && empty($program['allowed_member_types'])) {
                continue;
            }

            $allowedMemberTypes = $program['allowed_member_types'];

            foreach ($allowedMemberTypes as &$allowedMemberType) {
                if (isset($allowedMemberType['membership_id'])) {
                    continue;
                }

                if (!isset($allowedMemberType['memberships'])) {
                    continue;
                }

                if (!is_array($allowedMemberType['memberships']) || empty($allowedMemberType['memberships'])) {
                    continue;
                }

                $membership = current($allowedMemberType['memberships']);

                if ('PAYG' == $membership['label']) {
                    continue;
                }

                $membershipId = $membership['id'];
                $allowedMemberType['membership_id'] = $membershipId;
            }

            $row['Program']['allowed_member_types'] = $allowedMemberTypes;
        }

        return $results;
    }

    public function beforeSave($options = [])
    {
        if (isset($this->data['Program']['name'])) {
            $this->data['Program']['name'] = ucfirst($this->data['Program']['name']);
        }

        $this->data['Program']['email_admin'] = filter_var(
            $this->data['Program']['email_admin'],
            FILTER_VALIDATE_BOOLEAN
        );

        $this->castMongoDate(['date_start', 'date_finish']);
        // $this->castInteger(array('schedule_default' => array('size'))); //check cast method below
        $this->castFloat(['default_price', 'allowed_member_types' => ['price']]);
        $this->ensureIsArray(['categories']);

        if (isset($this->data['Program']['schedule_default']['_trainers']) && empty($this->data['Program']['schedule_default']['_trainers'])) {
            return false;
        }

        try {
            //Ensure size is an int
            if (isset($this->data['Program']['size'])) {
                $this->data['Program']['size'] = (int)($this->data['Program']['size']);
            }
        } catch (Exception $ex) {
        }

        //Check schedules, if schedules arent tagged with a code, tag them, this code will be used to tracking
        //schedules and events mapped to those schedule codes for update purposes
        $schedules = [];
        foreach ($this->data['Program']['schedule'] as $schedule) {
            if (!isset($schedule['code'])) {
                $schedule['code'] = uniqid('', false);
            }
            array_push($schedules, $schedule);
        }

        array_walk_recursive($this->data['Program'], [$this, 'cast']);
        $this->data['Program']['schedule'] = $schedules;

        if ($this->isInserting()) {
            return true;
        }

        // This can be optimised but the idea is to avoid override keys
        // from the metadata attribute
        $this->data[$this->alias]['metadata'] = $this->getMergedMetadata($this->data);

        $this->setModifiedDate();

        return true;
    }

    public function findAllActiveByBranchId($branch_id)
    {
        $today = $this->getCurrentDate('date');
        $conditions = [
            'active' => true,
            'branch_id' => $branch_id,
            '$or' => [
                [
                    'date_finish' => [
                        '$gte' => new MongoDate(strtotime($today)),
                        '$exists' => true,
                    ],
                ],
                [
                    'date_finish' => [
                        '$exists' => false,
                    ],
                ],
                [
                    'date_finish' => null,
                ],
            ],
        ];

        return $this->find('all', ['conditions' => $conditions]);
    }

    public function getByNamespaceAndBranchId($namespace, $branch_id)
    {
        $conditions = [
            'namespace' => $namespace,
            'branch_id' => $branch_id,
            'active' => true,
        ];
        $params = [
            'conditions' => $conditions,
            'order' => ['name' => 1],
        ];

        return $this->find('all', $params);
    }

    public function getByBranchId($branch_id)
    {
        $conditions = [
            'branch_id' => $branch_id,
            'active' => true,
        ];
        $params = [
            'conditions' => $conditions,
            'order' => ['name' => 1],
        ];

        return $this->find('all', $params);
    }

    public function getEmailAdminsById($id)
    {
        $conditions = [
            '_id' => $id,
        ];
        $params = [
            'conditions' => $conditions,
            'fields' => ['email_admin'],
        ];
        $program = $this->find('first', $params);

        return $program['Program']['email_admin'];
    }

    public function getByIdAndBranchId($id, $branch_id)
    {
        $params = [
            'conditions' => ['_id' => $id, 'branch_id' => $branch_id],
            'fields' => [
                'name',
                'description',
                'allowed_member_types',
                'schedule',
                'pricing',
                'schedule_default',
                'categories'
            ],
        ];

        $program = $this->find('first', $params);

        return $program;
    }

    public function getNameById($id)
    {
        $params = [
            'conditions' => ['_id' => $id],
            'fields' => ['name', 'description'],
        ];

        $program = $this->find('first', $params);

        return $program['Program']['name'];
    }

    public function findAllByIdAndBranchId($program_id, $branch_id)
    {
        $programs = $this->find('all', ['conditions' => ['_id' => $program_id, 'branch_id' => $branch_id]]);

        return $programs;
    }

    public function findByIdAndBranchId($program_id, $branch_id)
    {
        return $this->find('first', ['conditions' => ['_id' => $program_id, 'branch_id' => $branch_id]]);
    }

    public function findAllByBranchId($branch_id, $fields = null)
    {
        $params = ['conditions' => ['branch_id' => $branch_id]];
        if (!empty($fields)) {
            $params['fields'] = $fields;
        }
        $programs = $this->find('all', $params);

        return $programs;
    }

    public function findListByBranchIdAndCategoryId($branch_id, $category_id, $return_model = true)
    {
        $conditions = [
            'branch_id' => $branch_id,
            '$and' => [
                ['categories' => ['$exists' => true]],
                ['categories' => $category_id],
            ],
            'active' => true,
        ];
        $fields = ['_id', 'branch_id', 'name', 'categories'];
        $programs = $this->find('all', ['conditions' => $conditions, 'fields' => $fields]);

        if (!$return_model) {
            foreach ($programs as &$program) {
                $program = $program['Program'];
            }
        }

        return $programs;
    }

    public function set_default_schedule_values($program_data)
    {
        foreach ($program_data['schedule'] as $key => $program_instance) {
            if (
                false == $program_data['schedule'][$key]['is_different_studio'] ||
                empty($program_data['schedule'][$key]['facility'])
            ) {
                $program_data['schedule'][$key]['facility'] = $program_data['schedule_default']['facility'];
            }

            if (
                false == $program_data['schedule'][$key]['is_different_trainer'] ||
                empty($program_data['schedule'][$key]['trainers'])
            ) {
                $program_data['schedule'][$key]['trainers'] = $program_data['schedule_default']['trainers'];
            }

            if (
                false == $program_data['schedule'][$key]['is_different_level'] ||
                empty($program_data['schedule'][$key]['level'])
            ) {
                $program_data['schedule'][$key]['level'] = $program_data['schedule_default']['level'];
            }

            if (
                false == $program_data['schedule'][$key]['is_different_size'] ||
                empty($program_data['schedule'][$key]['size'])
            ) {
                $program_data['schedule'][$key]['size'] = $program_data['schedule_default']['size'];
            }

            unset($program_data['schedule'][$key]['is_different_studio']);
            unset($program_data['schedule'][$key]['is_different_trainer']);
            unset($program_data['schedule'][$key]['is_different_level']);
            unset($program_data['schedule'][$key]['is_different_size']);
        }

        return $program_data;
    }

    /**
     *   Receives a Schedule array and formats the times, set the booleans for private, new and is_active
     *   and remove the items that are not active.
     */
    public function set_up_schedule_array($schedule_array)
    {
        foreach ($schedule_array as &$schedule) {
            $schedule['start_time'] = date('G:i:s', strtotime($schedule['start_time']));
            $schedule['end_time'] = date('G:i:s', strtotime($schedule['end_time']));
            $schedule['private'] = ($schedule['private']) ? true : false;
            $schedule['new'] = ($schedule['new']) ? true : false;
            $schedule['size'] = (int)($schedule['size']);
        }

        // Remove Schedule not active
        $new_schedule_arr = [];
        foreach ($schedule_array as $class_schedule) {
            if ($class_schedule['is_active']) {
                unset($class_schedule['is_active']);

                if (is_array($class_schedule['days_week'])) {
                    foreach ($class_schedule['days_week'] as $week_day) {
                        $new_schedule = $class_schedule;
                        $new_schedule['days_week'] = $week_day;

                        if (empty($new_schedule['code'])) {
                            $new_schedule['code'] = $this->generateScheduleCode($new_schedule_arr);
                        }
                        $new_schedule_arr[] = $new_schedule;
                    }
                } else {
                    if (empty($class_schedule['code'])) {
                        $class_schedule['code'] = $this->generateScheduleCode($new_schedule_arr);
                    }
                    $new_schedule_arr[] = $class_schedule;
                }
            }
        }

        return $new_schedule_arr;
    }

    public function generateScheduleCode($schedule_array)
    {
        return $this->generateUniqueCode(is_countable($schedule_array) ? count($schedule_array) : 0);
    }

    public function create_random_programs($namespace, $branch_id, $type)
    {
        $index = Configure::read('programs_' . $type);
        foreach ($index as $key => $prog) {
            $this->create();
            $program['Program']['namespace'] = $namespace;
            $program['Program']['branch_id'] = $branch_id;
            $program['Program']['active'] = true;
            $program['Program']['auto_generated'] = true;
            $program['Program']['allowed_member_types'] = null;
            $program['Program']['name'] = $prog['name'];
            $program['Program']['description'] = $prog['description'];
            //getting random prices
            $price_array = ['0', '6', '7', '8', '9', '12'];
            $k = array_rand($price_array);
            $program['Program']['default_price'] = $price_array[$k];

            $date = $this->getCurrentDate();
            $program['Program']['date_start'] = date('Y-m-d', $date + strtotime('-2 weeks'));
            $program['Program']['date_finish'] = date('Y-m-d', $date + strtotime('+2 weeks'));
            $facility_model = new Facility();
            $branch_model = new Branch();
            $user_model = new User();
            $program['Program']['schedule_default'] = [
                'facility' => $facility_model->getFacilityByName($namespace, $branch_id, $prog['facility_name']),
                'level' => $branch_model->getRandomLevel($namespace, $branch_id),
                'trainers' => [$user_model->getRandomTrainer($namespace, $branch_id)],
                'size' => random_int(15, 25),
            ];

            for ($x = 0; $x < 7; ++$x) {
                $start_hour = random_int(10, 20);
                $end_hour = $start_hour + 1;

                $schedules[$x] = [
                    'days_week' => $x,
                    'start_time' => $start_hour . ':00:00',
                    'end_time' => $end_hour . ':00:00',
                    'facility' => $facility_model->getFacilityByName($namespace, $branch_id, $prog['facility_name']),
                    'level' => $branch_model->getRandomLevel($namespace, $branch_id),
                    'trainers' => [$user_model->getRandomTrainer($namespace, $branch_id)],
                    'size' => random_int(15, 25),
                    'private' => false,
                    'new' => false,
                ];
                $schedules[$x]['code'] = $this->generateScheduleCode($schedules[$x]);
                $program['Program']['schedule'] = $schedules;
                $this->save($program['Program']);

                $default_pictures_folder = new Folder(
                    'img' . DS . 'assets' . DS . 'autogenerated' . DS . 'images_' . $type . DS
                );
                $default_pictures = $default_pictures_folder->find('programs_image00' . $key . '.jpg');
                $num_imgs = is_countable($default_pictures) ? count($default_pictures) : 0;
                $min = 0;
                $img = random_int($min, $num_imgs);
                $program_pictures = new Folder(
                    'img' . DS . $namespace . DS . 'branches' . DS . $branch_id . DS . 'programs' . DS,
                    true
                );
                $file = new File($default_pictures_folder->path . DS . $default_pictures[$img]);
                if ($file->exists()) {
                    if (!is_dir($program_pictures->path . DS . $this->id)) {
                        if (
                            !mkdir($concurrentDirectory = $program_pictures->path . DS . $this->id) && !is_dir(
                                $concurrentDirectory
                            )
                        ) {
                            throw new \RuntimeException(
                                sprintf('Directory "%s" was not created', $concurrentDirectory)
                            );
                        }
                    }
                    $file->copy($program_pictures->path . DS . $this->id . DS . 'default.png');
                }
            }
        }
    }

    /**
     * Given an array of branches ids and an array of categories ids, it will return all the program ids that
     * belongs to those branches.
     *
     * @param array $branches_ids [description]
     * @param array $categories [description]
     *
     * @return array [description]
     */
    public function getProgramIdsByBranchesIdsAndCategories($branches_ids = [], $categories = [])
    {
        $program_ids = [];
        $conditions = ['branch_id' => ['$in' => $branches_ids], 'categories' => ['$in' => $categories]];
        $fields = ['_id'];
        $programs = $this->find('all', ['conditions' => $conditions, 'fields' => $fields]);

        if ($programs) {
            foreach ($programs as $program) {
                $program_ids[] = $program['Program']['_id'];
            }
        }

        return $program_ids;
    }

    /**
     * Find all programs by program ids.
     *
     * @param array $branch The current branch
     * @param array $programIds Array of program ids
     *
     * @return array List of programs
     */
    public function findAllByProgramIds(array $branch, array $programIds): array
    {
        $conditions = [
            'branch_id' => $branch['_id'],
            '_id' => [
                '$in' => $programIds,
            ],
        ];

        $list = $this->find('all', [
            'conditions' => $conditions,
            'order' => [
                'time_start' => 1,
            ],
            'fields' => [
                'name',
                'time_start',
                'time_finish',
            ],
        ]);

        return $list;
    }

    public function getMap($namespace, $branches)
    {
        if (!empty($branches)) {
            $conditions = ['branch_id' => ['$in' => $branches]];
        } elseif (!empty($namespace)) {
            $conditions = ['namespace' => $namespace];
        }

        $params = [
            'conditions' => $conditions,
            'fields' => [
                'name',
                'description',
                'model',
                'allowed_member_types',
                'branch_id',
                'booked',
                'modified',
                'categories',
                'namespace',
                'room_map_image_name'
            ],
        ];
        $content = $this->find('all', $params);
        $map = array_map([$this, 'mapObject'], $content);
        $map = array_combine(array_column($map, '_id'), $map);

        return $map;
    }

    public function mapObject($data)
    {
        $result = parent::mapObject($data);
        $programModel = \Glofox\Domain\Programs\Models\Program::make($result);

        if (!empty($result['room_map_image_name'])) {
            $result['room_map_image_url'] = app()->make(RoomMapImagineUrlGenerator::class)->generate($programModel);
        }

        return $result;
    }
}
