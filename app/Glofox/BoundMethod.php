<?php

namespace Glofox;

class BoundMethod extends \Illuminate\Container\BoundMethod
{

    /**
     * @inheritdoc
     */
    protected static function getMethodDependencies($container, $callback, array $parameters = [])
    {
        $dependencies = [];

        foreach (static::getCallReflector($callback)->getParameters() as $parameter) {
            static::addDependencyForCallParameter($container, $parameter, $parameters, $dependencies);
        }

        foreach ($dependencies as $key => $default) {
            if (!isset($parameters[$key])) {
                $parameters[$key] = $default;
            }
        }

        return $parameters;
        // return array_merge(array_intersect_key($parameters, $dependencies), array_diff_key($dependencies, $parameters));
    }
}
