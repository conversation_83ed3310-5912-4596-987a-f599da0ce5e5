<?php

namespace CakeTestCases\Glofox\Domain\Authentication\Http;

\App::import('Test/Case', 'GlofoxControllerTestCase');

class JwtTest extends \GlofoxControllerTestCase
{
    public function test_it_adds_expiration_verifier_for_member_app(): void
    {
        $member = $this->fetchUser('59a3011a05c677bda916611c');
        $expiredToken = $this->generateTokenForUser($member, '-2 months');
        $this->loginByToken($expiredToken);

        $_SERVER['x-glofox-source'] = 'MEMBER_APP';
        $url = sprintf('/2.0/members/%s', $member->id());
        $body = $this->testAction($url, ['method' => 'GET']);

        $data = json_decode($body, $assoc = true, 512, JSON_THROW_ON_ERROR);
        $this->assertFalse($data['success']);
    }

    public function test_it_adds_expiration_verifier_for_admin_app(): void
    {
        $member = $this->fetchUser('59a3011a05c677bda916611c');
        $expiredToken = $this->generateTokenForUser($member, '-2 months');
        $this->loginByToken($expiredToken);

        $_SERVER['x-glofox-source'] = 'ADMIN_APP';
        $url = sprintf('/2.0/members/%s', $member->id());
        $body = $this->testAction($url, ['method' => 'GET']);

        $data = json_decode($body, true, 512, JSON_THROW_ON_ERROR);
        $this->assertFalse($data['success']);
    }

    public function test_it_adds_expiration_verifier_for_admin_app_using_platform_header(): void
    {
        $member = $this->fetchUser('59a3011a05c677bda916611c');
        $expiredToken = $this->generateTokenForUser($member, '-2 months');
        $this->loginByToken($expiredToken);

        $_SERVER['x-glofox-platform'] = 'admin app';
        $url = sprintf('/2.0/members/%s', $member->id());
        $body = $this->testAction($url, ['method' => 'GET']);

        $data = json_decode($body, true, 512, JSON_THROW_ON_ERROR);
        $this->assertFalse($data['success']);
    }

    public function test_it_do_not_add_expiration_verifier_for_unknown_sources(): void
    {
        $member = $this->fetchUser('59a3011a05c677bda916611c');
        $expiredToken = $this->generateTokenForUser($member, '-2 months');
        $this->loginByToken($expiredToken);

        $_SERVER['x-glofox-source'] = 'UNKNOWN';
        $url = sprintf('/2.0/members/%s', $member->id());
        $body = $this->testAction($url, ['method' => 'GET']);

        $data = json_decode($body, $assoc = true, 512, JSON_THROW_ON_ERROR);
        $this->assertEquals($member->id(), $data['_id']);
    }

    public function test_it_adds_expiration_verifier_for_other_sources_other_than_mobile_apps(): void
    {
        $member = $this->fetchUser('59a3011a05c677bda916611c');
        $expiredToken = $this->generateTokenForUser($member, '-2 months');
        $this->loginByToken($expiredToken);

        $_SERVER['x-glofox-source'] = 'DASHBOARD';
        $url = sprintf('/2.0/members/%s', $member->id());
        $body = $this->testAction($url, ['method' => 'GET']);

        $data = json_decode($body, $assoc = true, 512, JSON_THROW_ON_ERROR);
        $this->assertFalse($data['success']);
    }
}
