<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\PaymentMethods\Transformers;

use Carbon\Carbon;
use Glofox\Domain\Payouts\Transformers\PayoutsTransformer;
use Glofox\Payments\Entities\Payout\Models\Payout;

\App::import('Test/Case', 'GlofoxControllerTestCase');

class PayoutsTransformerTest extends \GlofoxControllerTestCase
{
    public $fixtures = [];

    public function test_it_should_transform_using_the_correct_format(): void
    {
        $now = Carbon::now()->getTimestamp();

        $payout = (new Payout())
            ->setId('py_11')
            ->setAmount(100)
            ->setStatus('paid')
            ->setCurrency('eur')
            ->setArrivalDate($now)
            ->setCreatedAt($now);

        $transformer = new PayoutsTransformer();

        $transformed = $transformer->transform($payout);

        $this->assertArrayHasKey('id', $transformed);
        $this->assertArrayHasKey('amount', $transformed);
        $this->assertArrayHasKey('status', $transformed);
        $this->assertArrayHasKey('currency', $transformed);
        $this->assertArrayHasKey('statement', $transformed);
        $this->assertArrayHasKey('arrival_date', $transformed);
        $this->assertArrayHasKey('created', $transformed);

        $this->assertEquals('py_11', $transformed['id']);
        $this->assertEquals(100, $transformed['amount']);
        $this->assertEquals('paid', $transformed['status']);
        $this->assertEquals('eur', $transformed['currency']);
        $this->assertEquals($now, $transformed['arrival_date']);
        $this->assertEquals($now, $transformed['created']);
    }

    public function test_it_should_transform_using_the_correct_format_for_pending_reports(): void
    {
        $now = Carbon::now()->getTimestamp();

        $payout = (new Payout())
            ->setId('py_11')
            ->setAmount(100)
            ->setStatus('paid')
            ->setCurrency('eur')
            ->setArrivalDate($now)
            ->setCreatedAt($now)
            ->setStatus('in_transit');

        $transformer = new PayoutsTransformer();

        $transformed = $transformer->transform($payout);

        $this->assertArrayHasKey('id', $transformed);
        $this->assertArrayHasKey('amount', $transformed);
        $this->assertArrayHasKey('status', $transformed);
        $this->assertArrayHasKey('currency', $transformed);
        $this->assertArrayHasKey('statement', $transformed);
        $this->assertArrayHasKey('arrival_date', $transformed);
        $this->assertArrayHasKey('created', $transformed);

        $this->assertEquals('py_11', $transformed['id']);
        $this->assertEquals(100, $transformed['amount']);
        $this->assertEquals('in_transit', $transformed['status']);
        $this->assertEquals('eur', $transformed['currency']);
        $this->assertEquals($now, $transformed['arrival_date']);
        $this->assertEquals($now, $transformed['created']);
    }
}
