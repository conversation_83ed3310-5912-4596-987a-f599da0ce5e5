<?php

use CakeTestCases\Glofox\Domain\Authentication\Token\TokenGeneration;
use CakeTestCases\Glofox\Domain\Users\Traits\AuthenticateUsersTrait;
use Glofox\Domain\Communications\UseCase\ModerateMessage;
use Glofox\Domain\Transactional\Filters\ElectronicAgreementsTemplateFilter;
use Glofox\Domain\Transactional\Filters\OnlineBookingConfirmationTemplateFilter;
use Glofox\Domain\Transactional\Messages\Identifier;
use Glofox\Domain\Transactional\Models\TransactionalMessage;
use Glofox\Domain\Transactional\Sanitizers\TemplateContentSanitizer;

App::import('Test/Case', 'GlofoxControllerTestCase');
App::uses('BookingsController', 'Controller');
App::uses('Component', 'Controller');

class MessagesControllerTest extends GlofoxControllerTestCase
{
    use AuthenticateUsersTrait;
    use TokenGeneration;

    /**
     * Fixtures.
     *
     * @var array
     */
    public $fixtures = [
        'app.branch',
        'app.user',
        'app.transactional_messages_default',
        'app.transactional_message',
    ];

    public static $token;

    /**
     * Authenticate as User, calls authenticate with password with new api.
     */
    public function setUp(): void
    {
        parent::setUp();

        $this->authenticateAsAdmin();

        self::$token = $this->currentToken;
    }

    public function tearDown(): void
    {
        parent::tearDown();

        Mockery::close();
    }

    public function test_preview(): void
    {
        $_SERVER['HTTP_AUTHORIZATION'] = self::$token;

        $data = [
            'subject' => 'test subject',
            'content' => 'test content',
            'identifier' => Identifier::WELCOME,
        ];

        $options = [
            'data' => $data,
            'method' => 'post',
        ];

        $result = $this->testAction('/messages/preview', $options);
        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        $this->assertTrue($result->success);
        $this->assertEquals('MESSAGE_PREVIEW_SENT_SUCCESSFULY', $result->message);
    }

    public function test_upsert_errors_when_emails_in_copy_are_invalid(): void
    {
        $_SERVER['HTTP_AUTHORIZATION'] = self::$token;

        $data = [
            'subject' => 'test subject',
            'content' => 'test content',
            'copy' => [
                'emails' => [
                    'im-not-valid',
                ],
            ],
            'identifier' => Identifier::WELCOME,
        ];

        $options = [
            'data' => json_encode($data, JSON_THROW_ON_ERROR),
            'method' => 'post',
        ];


        $m = Mockery::mock(ModerateMessage::class);
        $m->shouldReceive('moderate')->once();
        app()->instance(ModerateMessage::class, $m);

        $result = $this->testAction('/messages/upsert', $options);
        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        $this->assertFalse($result->success);
        $this->assertEquals('E-mail im-not-valid is not valid.', $result->message);

        $data = [
            'subject' => 'test subject',
            'content' => 'test content',
            'copy' => [
                'emails' => [
                    '<EMAIL>',
                ],
            ],
            'identifier' => Identifier::WELCOME,
        ];

        $options = [
            'data' => json_encode($data, JSON_THROW_ON_ERROR),
            'method' => 'post',
        ];

        $result = $this->testAction('/messages/upsert', $options);
        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        $this->assertTrue($result->success);
        app()->forgetInstance(ModerateMessage::class);
    }

    public function test_template_list_filters_messages(): void
    {
        $_SERVER['HTTP_AUTHORIZATION'] = self::$token;

        $messageFilter = \Mockery::mock(OnlineBookingConfirmationTemplateFilter::class);
        $messageFilter->shouldReceive('execute')
            ->andReturn(['value' => true])
            ->once();
        app()->instance(OnlineBookingConfirmationTemplateFilter::class, $messageFilter);

        $eagreements = \Mockery::mock(ElectronicAgreementsTemplateFilter::class);
        $eagreements->shouldReceive('execute')
            ->andReturn(['value' => true])
            ->once();
        app()->instance(ElectronicAgreementsTemplateFilter::class, $eagreements);

        $result = $this->testAction('2.0/messages/templates');
        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        $this->assertTrue($result->value);

        app()->forgetInstance(ElectronicAgreementsTemplateFilter::class);
        app()->forgetInstance(OnlineBookingConfirmationTemplateFilter::class);
    }

    public function test_upsert_calls_content_sanitizer(): void
    {
        $user = $this->fetchUser('59a7011a05c677bda916612a');
        $this->loginAsUser($user);

        $data = [
            'subject' => 'test subject',
            'content' => 'test content',
            'identifier' => Identifier::WELCOME,
            'enabled' => true,
            'branch_id' => $user->currentBranchId(),
            'copy' => [
                'emails' => [
                    '<EMAIL>',
                ],
                'type' => 'cc',
            ],
        ];

        $sanitizer = \Mockery::mock(TemplateContentSanitizer::class);
        $sanitizer->shouldReceive('execute')
            ->withArgs(function (TransactionalMessage $receiveMessage) {
                $this->assertSame('test content', $receiveMessage->content());

                return true;
            })
            ->andReturn(
                new TransactionalMessage(['foo' => 'bar'])
            )
            ->once();

        app()->instance(TemplateContentSanitizer::class, $sanitizer);

        $options = [
            'data' => json_encode($data, JSON_THROW_ON_ERROR),
            'method' => 'post',
        ];
        $this->testAction('/messages/upsert', $options);

        app()->forgetInstance(TemplateContentSanitizer::class);
    }
}
