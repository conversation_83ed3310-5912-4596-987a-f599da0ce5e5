@startuml
start

:createTimeSlotBooking(createTimeSlotBookingParameters);
if (entityHasName) then (yes)
  :sendPushNotification();
else (not)
endif
if (isNotPayWithCredits && isNotPayWithAddons && (isSingleBooking || hasPrice) && statusIsBooked) then (yes)
  :paid = notPayGym;
else (not)
endif
:saveOrUpdateBooking();
if (successBooking) then (yes)
  :emit TimeslotBookingWasCreated event;
  if (thereIsAChargeAssociated) then (yes)
    :emit ChargeWasCreatedForBooking event;
  else (not)
  endif
else (not)
endif

stop
@enduml
