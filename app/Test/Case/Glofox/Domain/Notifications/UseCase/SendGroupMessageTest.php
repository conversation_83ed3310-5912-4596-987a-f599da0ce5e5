<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\Notifications\UseCase;

use Glofox\Domain\Authentication\Auth;
use App;
use Glofox\Domain\Notifications\UseCase\SendGroupMessageParams;
use GlofoxTestCase;
use Glofox\Domain\Users\Repositories\UsersRepository;
use Glofox\Domain\Notifications\UseCase\SendGroupMessage;
use Glofox\Domain\Users\Models\User as UserModel;
use Glofox\Domain\Branches\Models\Branch as BranchModel;
use Illuminate\Support\Collection;
use Mockery;

App::import('Test/Case', 'GlofoxTestCase');

class SendGroupMessageTest extends GlofoxTestCase
{
    public $fixtures = [];
    private UsersRepository $usersRepository;

    private const BRANCH_ID = 'test-branch-id';
    private const NAMESPACE = 'test-namespace';
    private const USER_ID = '6506c52376d41a12833a3892';

    public function setUp()
    {
        parent::setUp();
        $this->usersRepository = Mockery::mock(UsersRepository::class);
    }

    public function tearDown(): void
    {
        parent::tearDown();

        Mockery::close();
    }

    public function testSendGroupMessage(): void
    {
        $branchId = self::BRANCH_ID;
        $namespace = self::NAMESPACE;
        $users_ids = [self::USER_ID];
        $users_ids_collection = new Collection(
            array_map(fn($id) => new \MongoId($id), $users_ids)
        );
        $overwrite_marketing = true;
        $message = "TEST";

        $branchMock = Mockery::mock(BranchModel::class);

        $userMock = Mockery::mock(UserModel::class)->shouldIgnoreMissing();
        $userMock->shouldReceive('branch')->andReturn($branchMock);
        $userMock->shouldReceive('branchId')->andReturn($branchId);
        $userMock->shouldReceive('namespace')->andReturn($namespace);

        $authMock = Mockery::mock();
        $authMock->shouldReceive('user')->andReturn($userMock)->getMock();

        $authMock->user = $userMock;

        app()->instance(Auth::class, $authMock);

        $repoResponse = $this->getSendGroupMessageRepoResponse();

        $push_consent = null;

        $this->usersRepository->shouldReceive('getUsersWithPushConsentByUserIds')
            ->once()
            ->withArgs(
                function (
                    $user_ids_param,
                    $push_consent_param
                ) use ($users_ids_collection, $push_consent) {
                    self::AssertEquals(
                        $user_ids_param,
                        $users_ids_collection
                    );
                    self::AssertEquals(
                        $push_consent_param,
                        $push_consent
                    );
                    return true;
                }
            )->andReturn($repoResponse);

        $clientResponse = ['Client' => []];
        $clientMock = Mockery::mock(\Client::class);
        $clientMock->shouldReceive('getByNamespace')->andReturn($clientResponse);

        $pushNotificationResponse = [];
        $pushNotificationMock = Mockery::mock(\PushNotification::class);
        $pushNotificationMock->shouldReceive('saveBroadcastNotification')->andReturn($pushNotificationResponse);

        $expectedResponse = ['success' => true, 'err' => null, 'response' => null];
        $notificationComponentMock = Mockery::mock(\NotificationComponent::class);
        $notificationComponentMock->shouldReceive('send')->andReturn($expectedResponse);

        $useCase = new SendGroupMessage(
            $this->usersRepository,
            $clientMock,
            $pushNotificationMock,
            $notificationComponentMock
        );

        $params = new SendGroupMessageParams(
            $message,
            $overwrite_marketing,
            $users_ids
        );

        $result = $useCase->execute($params);

        self::assertEquals($result, $expectedResponse);

        app()->forgetInstance(Auth::class);
    }

    private function getSendGroupMessageRepoResponse(): array
    {
        return [
            [
                '_id' => self::USER_ID,
                'branch_id' => self::BRANCH_ID,
                'namespace' => self::NAMESPACE,
                'active' => true,
                'type' => 'MEMBER',
                'first_name' => 'Of Namespace',
                'last_name' => 'User_2',
                'phone' => '12345',
                'email' => '<EMAIL>',
                'login' => '<EMAIL>',
                'password' => '$argon2i$v=19$m=1024,t=2,p=2$SGRCWm9SeVlU' .
                    'QzdRcTVGdg$AcF8hKNGIBgyA51DEdbITjcC5clbAVgrkzEth/f/EVg',
                'lead_status' => 'UNTOUCHED',
                'membership' => [
                    'type' => 'payg',
                ],
                'receive_marketing' => true,
                'device' => [
                    'os' => 'android',
                    'version' => [
                        'major' => 8,
                        'minor' => 1,
                        'revision' => 2,
                    ],
                    'id' => 'mock-of-a-device-id-6506c52376d41a12833a3892',
                ],
            ]
        ];
    }
}
