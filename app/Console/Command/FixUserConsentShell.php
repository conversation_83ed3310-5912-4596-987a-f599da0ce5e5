<?php

declare(strict_types=1);

use Glofox\Domain\Branches\Repositories\BranchesRepository;
use Glofox\Domain\Users\Models\ConsentEmail;
use Glofox\Domain\Users\Models\ConsentPush;
use Glofox\Domain\Users\Models\ConsentSms;
use Glofox\Domain\Users\Models\UserConsent;
use Glofox\Domain\Users\Repositories\UsersRepository;
use Glofox\Domain\Users\Search\Expressions\Email;
use Glofox\Domain\Users\Search\Expressions\Type;
use Glofox\Domain\Users\UserTypesCollection;
use Glofox\Repositories\Search\Expressions\Shared\Id;
use League\Csv\Reader;
use Psr\Log\LoggerInterface;

App::uses('AmazonS3', 'AmazonS3.Lib');

class FixUserConsentShell extends AppShell
{
    private bool $isDryRun;

    private UsersRepository $usersRepository;

    private BranchesRepository $branchesRepository;

    private User $userModel;

    private LoggerInterface $logger;

    private \AmazonS3 $amazonS3;

    private int $totalUsers;

    private int $usersToUpdate = 0;

    private int $failures = 0;

    public function __construct($stdout = null, $stderr = null, $stdin = null)
    {
        parent::__construct($stdout, $stderr, $stdin);

        $this->usersRepository = app()->make(UsersRepository::class);
        $this->branchesRepository = app()->make(BranchesRepository::class);
        $this->userModel = app()->make(User::class);
        $this->logger = app()->make(LoggerInterface::class);
    }

    public function getOptionParser(): ConsoleOptionParser
    {
        $parser = parent::getOptionParser();

        $parser->addOption('method', [
            'short' => 'm',
            'help' => 'The method to execute',
            'default' => 'dry-run',
        ]);

        $parser->addOption('file-path', [
            'short' => 'f',
            'help' => 'The file path on S3',
            'default' => '',
        ]);

        return $parser;
    }

    public function main(): void
    {
        $this->isDryRun = $this->param('method') === 'dry-run';

        try {
            $filepath = $this->getCsvFileFromS3($this->param('file-path'));
        } catch (Exception $e) {
            $this->logger->error($e);
        }
        $file = Reader::createFromPath($filepath, 'r');

        $this->totalUsers = $file->each(fn($row) => true);
        foreach ($file as $row) {
            try {
                $this->processUser($row);
            } catch (Exception $exception) {
                $this->logger->info(sprintf("User %s failed", $row[0]));
                $this->failures++;
            }
        }

        $this->logger->info(sprintf("\n Total users: %s \n Users to update: %s \n Failures: %s", $this->totalUsers, $this->usersToUpdate, $this->failures));
    }

    private function getCsvFileFromS3(string $filePath): string
    {
        $config = Configure::read('amazon_s3');
        $this->amazonS3 = new AmazonS3(
            [
                $config['access_key'],
                $config['secret_key'],
                $config['bucket_name'],
                $config['region']
            ]
        );

        $this->amazonS3->get($filePath, TMP);

        return TMP . $filePath;
    }

    private function processUser(array $importInfo): void
    {
        $userEmail = $importInfo[0];
        if ($importInfo[1] === 'False') {
            $emailConsent = true;
        } elseif ($importInfo[1] === 'True') {
            $emailConsent = false;
        } elseif ($importInfo[1] === 'NULL') {
            $emailConsent = true;
        } else{
            $this->logger->info(sprintf('User %s has an invalid email consent', $userEmail));

            return;
        }

        if ($importInfo[2] === 'False') {
            $smsConsent = true;
        } elseif ($importInfo[2] === 'True') {
            $smsConsent = false;
        } elseif ($importInfo[2] === 'NULL') {
            $smsConsent = true;
        } else {
            $this->logger->info(sprintf('User %s has an invalid sms consent', $userEmail));

            return;
        }
        // the file only contains some user ids. So looking for emails if id does not exist
        $users = $this->usersRepository
            ->addCriteria(new Type(UserTypesCollection::make([\UserType::MEMBER()])));

        if($importInfo[3] !== ""){
            $users->addCriteria(new Id($importInfo[3]));
        }else{
            $users->addCriteria(new Email($userEmail));
        }

        $users = $this->usersRepository->find();
        foreach ($users as $user) {
            if (
                ($user->optedInForMarketingMessages() === $emailConsent || $emailConsent === null)
                && ($user->optedInForMarketingMessages() === $smsConsent || $smsConsent === null)) {
                $this->logger->info(sprintf('User %s has legacy consent and does not need to be updated', $userEmail));

                return;
            }

            $branch = $this->branchesRepository->getById($user->originBranchId());
            if ($branch->get('corporate_id') !== 'corp_Lift') {
                $this->logger->info(sprintf('User %s is not in a Lift club', $userEmail));

                return;
            }

            if (
                ($user->consent()->email()->active() === $emailConsent || $emailConsent === null)
                && ($user->consent()->sms()->active() === $smsConsent || $smsConsent === null)
            ) {
                $this->logger->info(sprintf('User %s does not need to be updated', $userEmail));

                return;
            }

            $this->logger->info(sprintf('User %s needs to be updated to email: %s & sms: %s', $userEmail,
                $emailConsent ? 'True' : 'False', $smsConsent ? 'True' : 'False'));

            $consent = new UserConsent();
            if (($emailConsent !== null && $user->consent()->email()->active() !== $emailConsent)
                || $user->consent()->email()->active() === null) {
                $emailConsentToSet = new ConsentEmail([
                    'active' => $emailConsent === true,
                    'modified_at' => new MongoDate(),
                    'modified_by_user_id' => 'ci3150-gymsales-by-email',
                    'modified_from_ip_address' => ''
                ]);
            } else {
                $emailConsentToSet = $user->consent()->email();
            }
            $consent->put('email', $emailConsentToSet);

            if (($smsConsent !== null && $user->consent()->sms()->active() !== $smsConsent)
                || $user->consent()->sms()->active() === null) {
                $smsConsentToSet = new ConsentSms([
                    'active' => $smsConsent === true,
                    'modified_at' => new MongoDate(),
                    'modified_by_user_id' => 'ci3150-gymsales-by-email',
                    'modified_from_ip_address' => ''
                ]);
            } else {
                $smsConsentToSet = $user->consent()->sms();
            }
            $consent->put('sms', $smsConsentToSet);

            if (!empty($user->consent()->pushNotification()) && $user->consent()->pushNotification() != new ConsentPush([])) {
                $consent->put('push', $user->consent()->pushNotification());
            }

            $user->put('consent', $consent);

            $this->logger->info(sprintf('Updating user %s to to be updated to have consent object: %s', $userEmail,
                json_encode($consent, JSON_PARTIAL_OUTPUT_ON_ERROR)));
            $this->usersToUpdate++;

            if (!$this->isDryRun) {
                $this->userModel->saveOrFail($user->toArray(), [
                    'callbacks' => 'before',
                ]);
            }
        }

    }
}
