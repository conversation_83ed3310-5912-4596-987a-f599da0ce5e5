<?php

declare(strict_types=1);

use Psr\Log\LoggerInterface;

class ImportCategoryShell extends Shell
{
    private LoggerInterface $logger;

    public function __construct($stdout = null, $stderr = null, $stdin = null)
    {
        parent::__construct($stdout, $stderr, $stdin);

        $this->logger = app()->make(LoggerInterface::class);
    }

    public function getOptionParser(): ConsoleOptionParser
    {
        $parser = parent::getOptionParser();

        $parser->addOption('model', [
            'short' => 'm',
            'help' => 'Model',
            'required' => true
        ]);

        $parser->addOption('name', [
            'short' => 'n',
            'help' => 'Name',
            'required' => true
        ]);

        $parser->addOption('description', [
            'short' => 'd',
            'help' => 'Description',
            'required' => false
        ]);

        $parser->addOption('imageUrl', [
            'short' => 'i',
            'help' => 'Image URL',
            'required' => false
        ]);

        $parser->addOption('active', [
            'short' => 'a',
            'help' => 'Active',
            'required' => false
        ]);

        return $parser;
    }

    /**
     * @throws Exception
     */
    public function main(): void
    {
        try {
            $this->logger->info('[ImportCategory] Started');
            $this->logger->info(
                '[ImportCategory] Name: ' . $this->params['name'] .
                ' and Model: ' . $this->params['model']
            );

            $categoryModel = ClassRegistry::init('Category');
            $this->validate($categoryModel);
            $this->create($categoryModel);
        } catch (Exception $e) {
            $this->logger->error(
                '[ImportCategory] [Error]: ' . $e->getMessage()
            );
            exit(1);
        }

        $this->logger->info('[ImportCategory] Finished');
    }

    /**
     * @throws Exception
     */
    private function validate(Category $categoryModel): void
    {
        $this->logger->info('[ImportCategory] Running Validations...');
        if (empty($this->params['model'])) {
            throw new Exception('Model cannot be empty');
        }
        if (empty($this->params['name'])) {
            throw new Exception('Name cannot be empty');
        }
        if ($categoryModel->findByNameAndModel($this->params['name'], $this->params['model'])) {
            throw new Exception('Category already exists, exiting');
        }
    }

    /**
     * @throws Exception
     */
    private function create(Category $categoryModel): void
    {
        $this->logger->info('[ImportCategory] Creating Record...');
        $categoryModel->create();
        $category = $categoryModel->save([
            'model' => $this->params['model'],
            'name' => $this->params['name'],
            'description' => $this->params['description'] ?? '',
            'image_url' => $this->params['imageUrl'] ?? '',
            'active' => filter_var($this->params['active'] ?? true, FILTER_VALIDATE_BOOLEAN),
        ]);
        if ($category) {
            $this->logger->info(
                '[ImportCategory] Category Saved! ID: ' .
                $category['Category']['_id']
            );
        }
        if (!$category) {
            throw new Exception('Category Save Failed');
        }
    }
}
