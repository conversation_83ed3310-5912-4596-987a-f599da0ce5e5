get:
  tags:
    - Enterprises
  summary: Retrieve enterprises
  operationId: get-enterprises
  description: This endpoint returns a list of enterprises.
  security:
    - coreapi_auth: [ ]
  responses:
    '200':
      description: OK
      content:
        application/json:
          schema:
            type: object
            properties:
              data:
                type: array
                items:
                  $ref: "../../schemas/enterprise.yml"
    '400':
      description: Bad Request
    '500':
      description: Internal Server Error
post:
  tags:
    - Enterprises
  summary: Create a new enterprise
  operationId: create-enterprise
  description: This endpoint allows the creation of a new enterprise by providing the required fields.
  security:
    - coreapi_auth: [ ]
  requestBody:
    description: New enterprise object to be created
    required: true
    content:
      application/json:
        schema:
          type: object
          properties:
            name:
              type: string
              description: The name of the enterprise
              example: "AirLocker"
            corporate_id:
              type: string
              description: The corporate identifier of the enterprise
              example: "corp_AirLocker"
          required:
            - name
            - corporate_id
  responses:
    '204':
      description: Enterprise successfully created
      content:
        application/json:
          schema:
            $ref: "../../schemas/enterprise.yml"
    '400':
      description: Bad Request
    '401':
      description: Unauthorized – Authentication is required and has failed or has not yet been provided.
    '403':
      description: Forbidden – The authenticated client does not have permission to perform this action.
    '500':
      description: Internal Server Error
