<?php

namespace Glofox\Domain\Bookings\Repositories;

use Carbon\Carbon;

class CountBookingsByMemberIdInPeriodParams
{
    private string $memberId;
    private string $timezone;
    private Carbon $startDate;
    private Carbon $endDate;

    public function __construct(string $memberId, string $timezone, Carbon $startDate, Carbon $endDate)
    {
        $this->memberId = $memberId;
        $this->timezone = $timezone;
        $this->startDate = $startDate;
        $this->endDate = $endDate;
    }

    public function memberId(): string
    {
        return $this->memberId;
    }

    public function timezone(): string
    {
        return $this->timezone;
    }

    public function startDate(): Carbon
    {
        return $this->startDate;
    }

    public function endDate(): Carbon
    {
        return $this->endDate;
    }
}
