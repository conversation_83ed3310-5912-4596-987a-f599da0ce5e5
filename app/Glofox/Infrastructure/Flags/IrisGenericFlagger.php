<?php

namespace Glofox\Infrastructure\Flags;

use Glofox\Domain\Branches\Models\Branch;
use Glofox\Domain\Experiments\Services\ExperimentsClient;
use Illuminate\Http\Response;

/**
 * @deprecated use {@see Flagger} instead.
 */
abstract class IrisGenericFlagger implements FlaggerInterface
{
    private ExperimentsClient $irisHttpClient;

    /**
     * IrisGenericFlagger constructor.
     *
     * @param ExperimentsClient $irisHttpClient
     */
    public function __construct(ExperimentsClient $irisHttpClient)
    {
        $this->irisHttpClient = $irisHttpClient;
    }

    public function enable(Branch $branch): bool
    {
        $this->irisHttpClient->post(
            'UPDATE_TEST_TARGETS',
            [
                'testIdentifier' => $this->getSlug(),
            ],
            [
                'targetId' => $branch->id(),
            ]
        );
        $response = $this->irisHttpClient->getLastResponse();

        return Response::HTTP_OK === $response->getStatusCode();
    }

    public function disable(Branch $branch): bool
    {
        $this->irisHttpClient->delete(
            'UPDATE_TEST_TARGETS',
            [
                'testIdentifier' => $this->getSlug(),
            ],
            [
                'targetId' => $branch->id(),
            ]
        );
        $response = $this->irisHttpClient->getLastResponse();

        return Response::HTTP_OK === $response->getStatusCode();
    }

    /**
     * @deprecated use {@see Flagger::has} instead.
     */
    public function has(Branch $branch): bool
    {
        return $this->checkFlagger($branch->id());
    }

    /**
     * @deprecated use {@see Flagger::hasByBranchId} instead.
     */
    public function hasByBranchId(string $branchId): bool
    {
        return $this->checkFlagger($branchId);
    }

    abstract public function shouldEnable(Branch $branch): bool;

    abstract public function getSlug(): string;

    abstract public function getTestEnabledOption(): string;

    /**
     * @param Branch $branch
     * @return bool
     */
    public function checkFlagger(string $branchId): bool
    {
        $this->irisHttpClient->post(
            'GET_OUTCOMES',
            [
                'testIdentifier' => $this->getSlug(),
            ],
            [
                'user' => [
                    'branch_id' => $branchId,
                ],
            ]
        );

        $response = $this->irisHttpClient->getLastResponse();

        if (Response::HTTP_FORBIDDEN === $response->getStatusCode()) {
            return false;
        }

        $body = json_decode(
            (string)$response->getBody(),
            $assoc = true,
            512,
            JSON_PARTIAL_OUTPUT_ON_ERROR
        );

        if (!isset($body['name'])) {
            return false;
        }

        return $body['name'] === $this->getTestEnabledOption() || $body['name'] === true;
    }
}
