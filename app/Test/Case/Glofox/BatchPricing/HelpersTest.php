<?php

namespace CakeTestCases\Glofox\BatchPricing;

use Glofox\BatchPricing\Helpers;

\App::import('Test/Case', 'GlofoxTestCase');

class HelpersTest extends \PHPUnit_Framework_TestCase
{
   public function testmapNewPricesEmpty()
   {
       $assets = [];
       $taxBreakdowns = [];

       $updatedAssets = Helpers::mapNewPrices($assets, $taxBreakdowns, 'inclusive');
       static::assertEquals($updatedAssets, []);

       $updatedAssets = Helpers::mapNewPrices($assets, $taxBreakdowns, 'exclusive');
       static::assertEquals($updatedAssets, []);
   }

   public function testmapNewPricesInclusiveSingle()
   {
       $programId = '615c273bc860321de7276852';
       $assets = [
           [
               'mongo_collection' => 'programs',
               'mongo_record_id' => $programId,
               'price_type' => 'payg',
               'service_type' => 'program',
               'service_id' => $programId,
               'price' => 111,
           ],
       ];

       $taxBreakdowns = [
           [
               'service_id' => $programId,
               'product_price' => 111,
               'net_price' => 222,
               'total_price' => 333,
               'tax_total' => -1,
               'applied_taxes' => [],
           ],
       ];

       $newTaxMode = 'inclusive';

       $updatedAssets = Helpers::mapNewPrices($assets, $taxBreakdowns, $newTaxMode);

       $expected = [
           [
               'result' => 'success',
               'mongo_collection' => 'programs',
               'mongo_record_id' => $programId,
               'price_type' => 'payg',
               'service_type' => 'program',
               'service_id' => $programId,
               'price' => 111,
               'new_price' => 333, // Notice that this is the breakdown's total price
           ],
       ];
       static::assertEquals($updatedAssets, $expected);
   }

   public function testIsPriceValid() {
    static::assertEquals(true, Helpers::isPriceValid(5));
    static::assertEquals(false, Helpers::isPriceValid(-5));
    static::assertEquals(false, Helpers::isPriceValid('1e+33'));
    static::assertEquals(true, Helpers::isPriceValid('1e+10'));
   }


   public function testmapNewPricesExclusiveSingle()
   {
       $programId = '615c273bc860321de7276852';
       $assets = [
           [
               'mongo_collection' => 'programs',
               'mongo_record_id' => $programId,
               'price_type' => 'payg',
               'service_type' => 'program',
               'service_id' => $programId,
               'price' => 111,
           ],
       ];

       $taxBreakdowns = [
           [
               'service_id' => $programId,
               'product_price' => 111,
               'net_price' => 222,
               'total_price' => 333,
               'tax_total' => -1,
               'applied_taxes' => [],
           ],
       ];

       $newTaxMode = 'exclusive';

       $updatedAssets = Helpers::mapNewPrices($assets, $taxBreakdowns, $newTaxMode);

       $expected = [
           [
               'result' => 'success',
               'mongo_collection' => 'programs',
               'mongo_record_id' => $programId,
               'price_type' => 'payg',
               'service_type' => 'program',
               'service_id' => $programId,
               'price' => 111,
               'new_price' => 222, // Notice that this is the breakdown's net price
           ],
       ];
       static::assertEquals($updatedAssets, $expected);
   }

   public function testseparateGoodAndBadAssets() {
    $programId = '615c273bc860321de7276852';
    $trainerId = '54944bc2d7b6dd6f2b8b4567';
    $facilityId = '57ee78049d280d63f28b4567';
    $timeslotPatternId = '5983144e25f1497b05000001';

    $assets = [
        [
            'mongo_collection' => 'programs',
            'mongo_record_id' => $programId,
            'price_type' => 'payg',
            'service_type' => 'program',
            'service_id' => $programId,
            'price' => -4.78,
        ],
        [
            'mongo_collection' => 'users',
            'mongo_record_id' => $trainerId,
            'price_type' => 'payg',
            'service_type' => 'trainer',
            'service_id' => $trainerId,
            'price' => 2.34,
        ],
        [
            'mongo_collection' => 'users',
            'mongo_record_id' => $trainerId,
            'price_type' => 'payg',
            'service_type' => 'trainer',
            'service_id' => $trainerId,
            'price' => '1e+33',
        ],
        [
            'mongo_collection' => 'time_slot_patterns',
            'mongo_record_id' => $timeslotPatternId,
            'price_type' => 'member',
            'service_type' => 'facility',
            'service_id' => $facilityId,
            'price' => 30,
        ],
    ];

    [$goodAssets, $badAssets] = Helpers::separateGoodAndBadAssets($assets);

    $expectedGoodAssets = [
        [
            'mongo_collection' => 'users',
            'mongo_record_id' => $trainerId,
            'price_type' => 'payg',
            'service_type' => 'trainer',
            'service_id' => $trainerId,
            'price' => 2.34,
        ],
        [
            'mongo_collection' => 'time_slot_patterns',
            'mongo_record_id' => $timeslotPatternId,
            'price_type' => 'member',
            'service_type' => 'facility',
            'service_id' => $facilityId,
            'price' => 30,
        ],
    ];

    $expectedBadAssets = [
        [
            'mongo_collection' => 'programs',
            'mongo_record_id' => $programId,
            'price_type' => 'payg',
            'service_type' => 'program',
            'service_id' => $programId,
            'price' => -4.78,
            'result' => 'error',
            'error' => 'invalid price format'
        ],
        [
            'mongo_collection' => 'users',
            'mongo_record_id' => $trainerId,
            'price_type' => 'payg',
            'service_type' => 'trainer',
            'service_id' => $trainerId,
            'price' => '1e+33',
            'result' => 'error',
            'error' => 'invalid price format'
        ],
    ];

    static::assertEquals($goodAssets, $expectedGoodAssets);
    static::assertEquals($badAssets, $expectedBadAssets);
   }

   public function testmapNewPricesInclusiveMultiple()
   {
       $programId = '615c273bc860321de7276852';
       $trainerId = '54944bc2d7b6dd6f2b8b4567';
       $facilityId = '57ee78049d280d63f28b4567';
       $timeslotPatternId = '5983144e25f1497b05000001';

       $assets = [
           [
               'mongo_collection' => 'programs',
               'mongo_record_id' => $programId,
               'price_type' => 'payg',
               'service_type' => 'program',
               'service_id' => $programId,
               'price' => 4.78,
           ],
           [
               'mongo_collection' => 'users',
               'mongo_record_id' => $trainerId,
               'price_type' => 'payg',
               'service_type' => 'trainer',
               'service_id' => $trainerId,
               'price' => 2.34,
           ],
           [
               'mongo_collection' => 'time_slot_patterns',
               'mongo_record_id' => $timeslotPatternId,
               'price_type' => 'member',
               'service_type' => 'facility',
               'service_id' => $facilityId,
               'price' => 30,
           ],
       ];

       $taxBreakdowns = [
           [
               'service_id' => $programId,
               'product_price' => 4.78,
               'net_price' => 222,
               'total_price' => 333,
               'tax_total' => -1,
               'applied_taxes' => [],
           ],
           [
               'service_id' => $trainerId,
               'product_price' => 2.34,
               'net_price' => 444,
               'total_price' => 555,
               'tax_total' => -1,
               'applied_taxes' => [],
           ],
           [
               'service_id' => $facilityId,
               'product_price' => 30,
               'net_price' => 666,
               'total_price' => 777,
               'tax_total' => -1,
               'applied_taxes' => [],
           ],
       ];

       $newTaxMode = 'inclusive';

       $updatedAssets = Helpers::mapNewPrices($assets, $taxBreakdowns, $newTaxMode);

       $expected = [
           [
               'result' => 'success',
               'mongo_collection' => 'programs',
               'mongo_record_id' => $programId,
               'price_type' => 'payg',
               'service_type' => 'program',
               'service_id' => $programId,
               'price' => 4.78,
               'new_price' => 333,
           ],
           [
               'result' => 'success',
               'mongo_collection' => 'users',
               'mongo_record_id' => $trainerId,
               'price_type' => 'payg',
               'service_type' => 'trainer',
               'service_id' => $trainerId,
               'price' => 2.34,
               'new_price' => 555,
           ],
           [
               'result' => 'success',
               'mongo_collection' => 'time_slot_patterns',
               'mongo_record_id' => $timeslotPatternId,
               'price_type' => 'member',
               'service_type' => 'facility',
               'service_id' => $facilityId,
               'price' => 30,
               'new_price' => 777,
           ],
       ];

       static::assertEquals($updatedAssets, $expected);
   }

   public function testmapNewPricesDuplicateTaxBreakdowns()
   {
       $programId = '615c273bc860321de7276852';
       $trainerId = '54944bc2d7b6dd6f2b8b4567';
       $facilityId = '57ee78049d280d63f28b4567';
       $timeslotPatternId = '5983144e25f1497b05000001';

       $assets = [
           [
               'mongo_collection' => 'programs',
               'mongo_record_id' => $programId,
               'price_type' => 'payg',
               'service_type' => 'program',
               'service_id' => $programId,
               'price' => 4.78,
           ],
           [
               'mongo_collection' => 'users',
               'mongo_record_id' => $trainerId,
               'price_type' => 'payg',
               'service_type' => 'trainer',
               'service_id' => $trainerId,
               'price' => 2.34,
           ],
           [
               'mongo_collection' => 'time_slot_patterns',
               'mongo_record_id' => $timeslotPatternId,
               'price_type' => 'member',
               'service_type' => 'facility',
               'service_id' => $facilityId,
               'price' => 30,
           ],
       ];

       $taxBreakdowns = [
           // Start duplicated breakdowns
           [
               'service_id' => $programId,
               'product_price' => 4.78,
               'net_price' => 222,
               'total_price' => 333,
               'tax_total' => -1,
               'applied_taxes' => [],
           ],
           [
               'service_id' => $programId,
               'product_price' => 4.78,
               'net_price' => 222,
               'total_price' => 333,
               'tax_total' => -1,
               'applied_taxes' => [],
           ],
           [
               'service_id' => $programId,
               'product_price' => 4.78,
               'net_price' => 222,
               'total_price' => 333,
               'tax_total' => -1,
               'applied_taxes' => [],
           ],
           // End duplicated breakdowns
           [
               'service_id' => $trainerId,
               'product_price' => 2.34,
               'net_price' => 444,
               'total_price' => 555,
               'tax_total' => -1,
               'applied_taxes' => [],
           ],
           [
               'service_id' => $facilityId,
               'product_price' => 30,
               'net_price' => 666,
               'total_price' => 777,
               'tax_total' => -1,
               'applied_taxes' => [],
           ],
       ];

       $newTaxMode = 'inclusive';

       $updatedAssets = Helpers::mapNewPrices($assets, $taxBreakdowns, $newTaxMode);

       $expected = [
           [
               'result' => 'success',
               'mongo_collection' => 'programs',
               'mongo_record_id' => $programId,
               'price_type' => 'payg',
               'service_type' => 'program',
               'service_id' => $programId,
               'price' => 4.78,
               'new_price' => 333,
           ],
           [
               'result' => 'success',
               'mongo_collection' => 'users',
               'mongo_record_id' => $trainerId,
               'price_type' => 'payg',
               'service_type' => 'trainer',
               'service_id' => $trainerId,
               'price' => 2.34,
               'new_price' => 555,
           ],
           [
               'result' => 'success',
               'mongo_collection' => 'time_slot_patterns',
               'mongo_record_id' => $timeslotPatternId,
               'price_type' => 'member',
               'service_type' => 'facility',
               'service_id' => $facilityId,
               'price' => 30,
               'new_price' => 777,
           ],
       ];

       static::assertEquals($updatedAssets, $expected);
   }

   public function testmapNewPricesStringToNumberConversion()
   {
       $programId = '615c273bc860321de7276852';
       $assets = [
           [
               'mongo_collection' => 'programs',
               'mongo_record_id' => $programId,
               'price_type' => 'payg',
               'service_type' => 'program',
               'service_id' => $programId,
               'price' => '111',
           ],
       ];

       $taxBreakdowns = [
           [
               'service_id' => $programId,
               'product_price' => 111,
               'net_price' => 222,
               'total_price' => 333,
               'tax_total' => -1,
               'applied_taxes' => [],
           ],
       ];

       $newTaxMode = 'exclusive';

       $updatedAssets = Helpers::mapNewPrices($assets, $taxBreakdowns, $newTaxMode);

       $expected = [
           [
               'result' => 'success',
               'mongo_collection' => 'programs',
               'mongo_record_id' => $programId,
               'price_type' => 'payg',
               'service_type' => 'program',
               'service_id' => $programId,
               'price' => '111',
               'new_price' => 222, // Notice that this is the breakdown's net price
           ],
       ];
       static::assertEquals($updatedAssets, $expected);
   }

   public function testmapNewPricesNoTaxbreakFound()
   {
       $programId = '615c273bc860321de7276852';
       $assets = [
           [
               'mongo_collection' => 'programs',
               'mongo_record_id' => $programId,
               'price_type' => 'payg',
               'service_type' => 'program',
               'service_id' => $programId,
               'price' => 111,
           ],
       ];

       $taxBreakdowns = [
           [
               'service_id' => $programId,
               'product_price' => -1, // won't match.
               'net_price' => 222,
               'total_price' => 333,
               'tax_total' => -1,
               'applied_taxes' => [],
           ],
       ];

       $newTaxMode = 'exclusive';

       $updatedAssets = Helpers::mapNewPrices($assets, $taxBreakdowns, $newTaxMode);

       $expected = [
           [
               'result' => 'error',
               'error' => 'no tax breakdown found',
               'mongo_collection' => 'programs',
               'mongo_record_id' => $programId,
               'price_type' => 'payg',
               'service_type' => 'program',
               'service_id' => $programId,
               'price' => 111,
           ],
       ];
       static::assertEquals($updatedAssets, $expected);
   }

   public function testRecursiveFlatten()
   {
       $array = [
           ['foo' => 'bar'],
           [
               ['baz' => 'numb'],
               [
                   ['rawr' => 'ops'],
                   ['daa' => 'yui'],
               ],
           ],
       ];

       $expected = [
           ['foo' => 'bar'],
           ['baz' => 'numb'],
           ['rawr' => 'ops'],
           ['daa' => 'yui'],
       ];

       $flat = Helpers::recursiveFlatten($array);
       static::assertEquals($flat, $expected);
   }

   public function testMapToResponseSuccessful()
   {
       $successfulAsset = [
           'result' => 'success',
           'price' => 111,
           'new_price' => 222,
           'service_id' => '12345',
           'service_type' => 'facilities',
           'mongo_collection' => 'timeslotpatterns',
           'mongo_record_id' => 'sdfsdf',
       ];

       $expected = [
           'result' => 'success',
           'oldPrice' => 111,
           'newPrice' => 222,
           'id' => '12345',
           'type' => 'facilities',
       ];

       $mappedResponse = Helpers::mapToResponse($successfulAsset);
       static::assertEquals($mappedResponse, $expected);
   }

   public function testMapToResponseError()
   {
       $unsuccessfulAsset = [
           'result' => 'error',
           'error' => 'foo',
           'price' => 111,
           'service_id' => '12345',
           'service_type' => 'facilities',
           'mongo_collection' => 'timeslotpatterns',
           'mongo_record_id' => 'sdfsdf',
       ];

       $expected = [
           'result' => 'error',
           'error' => 'foo',
           'oldPrice' => 111,
           'id' => '12345',
           'type' => 'facilities',
       ];

       $mappedResponse = Helpers::mapToResponse($unsuccessfulAsset);
       static::assertEquals($mappedResponse, $expected);
   }

   public function testMapToResponseErrorWithNewPrice()
   {
       $unsuccessfulAsset = [
           'result' => 'error',
           'error' => 'foo',
           'price' => 111,
           'new_price' => 222,
           'service_id' => '12345',
           'service_type' => 'facilities',
           'mongo_collection' => 'timeslotpatterns',
           'mongo_record_id' => 'sdfsdf',
       ];

       $expected = [
           'result' => 'error',
           'error' => 'foo',
           'oldPrice' => 111,
           'newPrice' => 222,
           'id' => '12345',
           'type' => 'facilities',
       ];

       $mappedResponse = Helpers::mapToResponse($unsuccessfulAsset);
       static::assertEquals($mappedResponse, $expected);
   }

   public function testIndexAssetsByRecordId()
   {
        $programId = '615c273bc860321de7276852';
        $trainerId = '54944bc2d7b6dd6f2b8b4567';
        $facilityId = '57ee78049d280d63f28b4567';
        $timeslotPatternId = '5983144e25f1497b05000001';

        $assets = [
            [
                'mongo_collection' => 'programs',
                'mongo_record_id' => $programId,
                'price_type' => 'payg',
                'service_type' => 'programs',
                'service_id' => $programId,
                'price' => 4.78,
            ],
            [
                'mongo_collection' => 'programs',
                'mongo_record_id' => $programId,
                'price_type' => 'member',
                'service_type' => 'programs',
                'service_id' => $programId,
                'price' => 12.34,
            ],
            [
                'mongo_collection' => 'users',
                'mongo_record_id' => $trainerId,
                'price_type' => 'payg',
                'service_type' => 'trainers',
                'service_id' => $trainerId,
                'price' => 2.34,
            ],
            [
                'mongo_collection' => 'time_slot_patterns',
                'mongo_record_id' => $timeslotPatternId,
                'price_type' => 'member',
                'service_type' => 'facilities',
                'service_id' => $facilityId,
                'price' => 30,
            ],
        ];

        $expected = [
            $programId => [
                'mongo_record_id' => $programId,
                'service_type' => 'programs',
                'assets' => [
                    [
                        'mongo_collection' => 'programs',
                        'mongo_record_id' => $programId,
                        'price_type' => 'payg',
                        'service_type' => 'programs',
                        'service_id' => $programId,
                        'price' => 4.78,
                    ],
                    [
                        'mongo_collection' => 'programs',
                        'mongo_record_id' => $programId,
                        'price_type' => 'member',
                        'service_type' => 'programs',
                        'service_id' => $programId,
                        'price' => 12.34,
                    ],
                ]
            ],
            $trainerId => [
                'mongo_record_id' => $trainerId,
                'service_type' => 'trainers',
                'assets' => [
                    [
                        'mongo_collection' => 'users',
                        'mongo_record_id' => $trainerId,
                        'price_type' => 'payg',
                        'service_type' => 'trainers',
                        'service_id' => $trainerId,
                        'price' => 2.34,
                    ],
                ]
            ],
            $timeslotPatternId => [
                'mongo_record_id' => $timeslotPatternId,
                'service_type' => 'facilities',
                'assets' => [
                    [
                        'mongo_collection' => 'time_slot_patterns',
                        'mongo_record_id' => $timeslotPatternId,
                        'price_type' => 'member',
                        'service_type' => 'facilities',
                        'service_id' => $facilityId,
                        'price' => 30,
                    ],
                ],
            ]
        ];

        $indexedResponse = Helpers::indexAssetsByRecordId($assets);
        static::assertEquals($indexedResponse, $expected);
   }


   public function testPrepareTrainerFields()
   {
        $trainerId = '54944bc2d7b6dd6f2b8b4567';

        $assets = [
            [
                'mongo_collection' => 'users',
                'mongo_record_id' => $trainerId,
                'price_type' => 'payg',
                'service_type' => 'trainers',
                'service_id' => $trainerId,
                'price' => 1000,
                'new_price' => 1200,
            ],
            [
                'mongo_collection' => 'users',
                'mongo_record_id' => $trainerId,
                'price_type' => 'member',
                'service_type' => 'trainers',
                'service_id' => $trainerId,
                'price' => 2000,
                'new_price' => 2400,
            ],
        ];

        $initialTrainer = [
            '_id' => $trainerId,
            '_pattern' => [
                'active' => true,
                '_id' => $trainerId,
                'allowed_member_types' => [
                    [
                        'price' => '10',
                        'type' => 'payg',
                    ],
                    [
                        'price' => '20',
                        'type' => 'member',
                    ],
                    [
                        'price' => '70',
                        'type' => 'member',
                    ],
                ],
                'default_price' => 50,
                'model' => 'users',
                'model_id' => $trainerId,
                'pricing' => 'SINGLE',
            ],
            'name' => 'Best trainer Ever',
        ];

        $expectedAllowedMemberTypes = [
            [
                'price' => 12,
                'type' => 'payg',
            ],
            [
                'price' => 24,
                'type' => 'member',
            ],
            [
                'price' => '70',
                'type' => 'member',
            ],
        ];

        $expectedPattern = [
            'active' => true,
            '_id' => $trainerId,
            'allowed_member_types' => $expectedAllowedMemberTypes,
            'default_price' => 50,
            'model' => 'users',
            'model_id' => $trainerId,
            'pricing' => 'SINGLE',
        ];

        $expectedFields = [
            '_pattern' => $expectedPattern,
        ];

        $fields = Helpers::prepareTrainerPriceUpdateFields($assets, $initialTrainer);

        static::assertEquals($expectedFields, $fields);
   }

   public function testPrepareProductFields()
   {

        $recordId = '59b6a23c25f1496b02000000';
        $assets = [
            [
                'mongo_record_id' => $recordId,
                'new_price' => 222,
                'price' => 111,
            ],
            [
                'mongo_record_id' => $recordId,
                'new_price' => 600,
                'price' => 512,
             ]
        ];

        $record = [
            '_id' => $recordId,
            'name' => 'Edited Juice',
            'presentations' => [
                [
                    'id' => '1506964157657',
                    'retail_price' => 1.11,
                    'stock' => '10',
                    'wholesale_price' => 0,
                    '_toggle' => true,
                    'description' => 'Cucumber with Grapefruit',
                ],
                [
                    'id' => '1506964157657',
                    'retail_price' => 5.12,
                    'stock' => '10',
                    'wholesale_price' => 0,
                    '_toggle' => true,
                    'description' => 'Cucumber with Grapefruit',
                ],
            ],
        ];

        $expectedPresentations = [
            [
                'id' => '1506964157657',
                'retail_price' => 2.22,
                'stock' => '10',
                'wholesale_price' => 0,
                '_toggle' => true,
                'description' => 'Cucumber with Grapefruit',
            ],
            [
                'id' => '1506964157657',
                'retail_price' => 6,
                'stock' => '10',
                'wholesale_price' => 0,
                '_toggle' => true,
                'description' => 'Cucumber with Grapefruit',
            ],
        ];

        $expected = ['presentations' => $expectedPresentations];

        $fields = Helpers::prepareProductPriceUpdateFields($assets, $record);
        static::assertEquals($expected, $fields);
   }

   public function testPrepareAllowedMemberTypeFields()
   {
        $facilityId = '57ee78049d280d63f28b4567';
        $recordId = '59b6a23c25f1496b02000000';

        $assets = [
            [
                'mongo_record_id' => $recordId,
                'price_type' => 'payg',
                'new_price' => 2220,
                'price' => 1100,
            ],
            [
                'mongo_record_id' => $recordId,
                'price_type' => 'member',
                'new_price' => 6000,
                'price' => 5100,
             ]
        ];

        $record = [
            '_id' => $recordId,
            'pricing' => 'SINGLE',
            'allowed_member_types' => [
                [
                    'price' => '11',
                    'type' => 'payg',
                ],
                [
                    'price' => '51',
                    'type' => 'member',
                ],
                [
                    'price' => '90',
                    'type' => 'member',
                ],
            ],

            'model_id' => $facilityId,
            'model' => 'facilities',
        ];

        $expectedAllowedMemberTypes = [
            [
                'price' => 22.2,
                'type' => 'payg',
            ],
            [
                'price' => 60,
                'type' => 'member',
            ],
            [
                'price' => '90',
                'type' => 'member',
            ],
        ];

        $expected = ['allowed_member_types' => $expectedAllowedMemberTypes];

        $fields = Helpers::prepareAllowedMemberTypeFields($assets, $record);
        static::assertEquals($expected, $fields);
   }

   public function testPrepareMembershipFields()
   {

        $recordId = '59b6a23c25f1496b02000000';
        $assets = [
            [
                'mongo_record_id' => $recordId,
                'price_type' => 'upfront_fee',
                'price' => 111,
                'new_price' => 888,
            ],
            [
                'mongo_record_id' => $recordId,
                'price_type' => 'price',
                'price' => 333,
                'new_price' => 666,
             ]
        ];

        $record = [
            '_id' => $recordId,
            'name' => 'Edited Juice',
            'plans' => [
                [
                    'id' => '1506964157657',
                    'upfront_fee' => 1.11,
                    'foo' => 'bar'
                ],
                [
                    'id' => '1506964157657',
                    'upfront_fee' => 2.22,
                    'price' => 3.33,
                ],
            ],
        ];

        $expectedPlans = [
            [
                'id' => '1506964157657',
                'upfront_fee' => 8.88,
                'foo' => 'bar'
            ],
            [
                'id' => '1506964157657',
                'upfront_fee' => 2.22,
                'price' => 6.66,
            ],
        ];

        $expected = ['plans' => $expectedPlans];

        $fields = Helpers::prepareMembershipPriceUpdateFields($assets, $record);
        static::assertEquals($expected, $fields);
   }
}
