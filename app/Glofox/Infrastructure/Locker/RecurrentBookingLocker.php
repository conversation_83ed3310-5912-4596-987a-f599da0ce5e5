<?php

declare(strict_types=1);

namespace Glofox\Infrastructure\Locker;

use Glofox\Domain\Locker\Locker;
use Glofox\Domain\Locker\RecurrentBookingAlreadyLockedException;

class RecurrentBookingLocker extends Locker implements RecurrentBookingLockerInterface
{
    protected function processName(): string
    {
        return 'recurrent bookings';
    }

    protected function maxRetries(): int
    {
        return 10;
    }

    protected function sleepTimeInMicrosec(): int
    {
        return 500_000;
    }

    protected function ttlInSec(): int
    {
        return 86_400;
    }

    protected function throwNotLockedError(): void
    {
        throw RecurrentBookingAlreadyLockedException::create();
    }
}
