<?php

App::uses('GlofoxTestFixture', 'Test/Fixture');

class BookingSpotFixture extends GlofoxTestFixture
{
    public $import = ['model' => 'BookingSpot'];

    protected $indexes = [
        [
            'branch_id' => 1,
            'user_id' => 1,
            'booking_id' => 1,
            'spot_id' => 1,
        ],
    ];

    public function init()
    {
        $this->records = [
            [
                '_id' => new MongoId('584cc642dd3c2fbfe13e911a'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'booking_id' => '636e25bf0560b6ca2a22cbcc',
                'model_id' => '49b7012a05c677c9a512503c',
                'spot_id' => '1',
                'user_id' => '59a7011a05c677bda916612a',
                'created' => new MongoDate(),
                'modified' => new MongoDate(),
                'deleted' => null,
            ]
        ];

        parent::init();
    }
}