<?php

namespace Glofox\Domain\Cart\Http;

use App;
use AppController;
use Carbon\Carbon;
use Glofox\AuditLog\Resolvers\OriginByRequestResolver;
use Glofox\Datasource\Exceptions\InvalidMongoIdException;
use Glofox\Domain\Authentication\Auth;
use Glofox\Domain\Branches\Repositories\BranchesRepository;
use Glofox\Domain\Branches\Search\Expressions\BranchId;
use Glofox\Domain\Cart\Requests\PreCheckoutRequest;
use Glofox\Domain\Cart\Requests\ValidateMembershipRequest;
use Glofox\Domain\Cart\Requests\ValidateProductRequest;
use Glofox\Domain\Cart\Responses\ValidateProductResponse;
use Glofox\Domain\ElectronicAgreements\Platform;
use Glofox\Domain\FeatureFlags\FeatureFlagInterface;
use Glofox\Domain\Memberships\Exceptions\MembershipPlanMinimumPriceValidationException;
use Glofox\Domain\Memberships\Exceptions\CannotPurchaseAnotherFirstDayOfBookingMembershipException;
use Glofox\Domain\Memberships\Validation\Purchase\MembershipPurchasedBeforeValidator;
use Glofox\Domain\Memberships\Models\Membership;
use Glofox\Domain\Memberships\Models\Plan;
use Glofox\Domain\Memberships\Repositories\MembershipsRepository;
use Glofox\Domain\Memberships\Services\Purchase\PurchaseService;
use Glofox\Domain\Memberships\Type as MembershipType;
use Glofox\Domain\Memberships\Validation\PlanMinimumPriceValidator;
use Glofox\Domain\PaymentMethods\Type as PaymentMethodType;
use Glofox\Domain\Store\Products\Repositories\ProductsRepository;
use Glofox\Domain\Store\Sales\Models\StoreSalesRequest;
use Glofox\Domain\Store\Sales\Validation\StoreSalesValidator;
use Glofox\Domain\Terms\Search\Expressions\Type as TermType;
use Glofox\Domain\Terms\Type as TermTypeEnum;
use Glofox\Domain\Users\Models\User;
use Glofox\Domain\Users\Repositories\UsersRepository;
use Glofox\Domain\Wallets\Services\Settings\OverdraftValidatorService;
use Glofox\Http\Requests\TracingContextInterface;
use Glofox\Infrastructure\Flags\Flag;
use Glofox\Payments\Entities\Capability\Models\Capability;
use Glofox\Payments\Entities\Customer\Resolvers\IdResolver;
use Glofox\Repositories\Search\Expressions\Shared\Active;
use Glofox\Repositories\Search\Expressions\Shared\Id;
use Illuminate\Http\JsonResponse;
use Psr\Log\LoggerInterface;
use UnsuccessfulOperation;
use MembershipPlanStartsOn;


App::uses('AppController', 'Controller');

/**
 * Class CartController.
 */
class CartController extends AppController
{
    /** @var BranchesRepository */
    protected $branchesRepository;

    /** @var MembershipsRepository */
    protected $membershipsRepository;

    /** @var UsersRepository */
    protected $usersRepository;

    /** @var OriginByRequestResolver */
    private $originByRequestResolver;

    /** @var PlanMinimumPriceValidator */
    private $planMinimumPriceValidator;

    /** @var PurchaseService */
    private $purchaseService;

    /** @var TracingContextInterface */
    private $tracingContext;

    /** @var IdResolver */
    private $idResolver;

    /**
     * @var LoggerInterface
     */
    private $logger;

    private FeatureFlagInterface $featureFlagInterface;

    /**
     * CartController constructor.
     *
     * @param null $request
     * @param null $response
     */
    public function __construct($request = null, $response = null)
    {
        parent::__construct($request, $response);

        $this->branchesRepository = app()->make(BranchesRepository::class);
        $this->membershipsRepository = app()->make(MembershipsRepository::class);
        $this->usersRepository = app()->make(UsersRepository::class);
        $this->originByRequestResolver = app()->make(OriginByRequestResolver::class);
        $this->planMinimumPriceValidator = app()->make(PlanMinimumPriceValidator::class);
        $this->purchaseService = app()->make(PurchaseService::class);
        $this->tracingContext = app()->make(TracingContextInterface::class);
        $this->logger = app()->make(LoggerInterface::class);
        $this->idResolver = app()->make(IdResolver::class);
        $this->featureFlagInterface = app()->make(FeatureFlagInterface::class);
    }

    public function validateMembership(ValidateMembershipRequest $request): JsonResponse
    {
        $traceId = $this->tracingContext->getTracingHeaderForPropagation();

        $this->logger->info('Validating membership', [
            'trace-id' => $traceId,
            'payload' => $request,
        ]);

        try {
            $output = $this->getValidatedAttributes($request);
        } catch (\Exception $e) {
            $this->logger->error('Error validating membership', [
                'trace-id' => $traceId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message_code' => $e->getMessage(),
                'message' => $e->getMessage(),
            ], 400);
        }

        $this->logger->info('Membership validated', [
            'trace-id' => $traceId,
        ]);

        return response()->json([
            'success' => false,
            'attributes' => $output,
        ]);
    }

    public function validateProduct(
        ValidateProductRequest $request,
        StoreSalesValidator $validator,
        ProductsRepository $productsRepository
    ): JsonResponse {
        $productToValidate = new StoreSalesRequest(
            $request->getProductId(),
            $request->getPresentationId(),
            $request->getUserId(),
            $request->getPrice(),
            $request->getPaymentMethod(),
            $request->getQuantity()
        );

        $productToValidate->withLoggedInUserType(Auth::user()->type());

        $validator->validate($productToValidate);

        $product = $productsRepository->findById($request->getProductId());
        $productName = $product->name();
        $presentation = $product->presentation($request->getPresentationId());

        $response = new ValidateProductResponse($productToValidate);
        $response->setPurchaseCode(uniqid('', false));
        $response->setProductName($productName);
        $response->setPresentationName($presentation->description());
        $response->setPrice($presentation->retailPrice());

        return $response->toJsonResponse();
    }

    public function preCheckout(PreCheckoutRequest $request)
    {
        $userId = $request->getUserId();
        $paymentMethod = $request->getPaymentMethod();

        /** @var User $user */
        $user = $this->usersRepository
            ->addCriteria(new Id($userId))
            ->addCriteria(new Active(true))
            ->firstOrFail();

        $paymentHandler = Auth::payments($paymentMethod->getValue());
        $customerAccountId = $this->idResolver->fromUserIdAndPaymentMethodId(
            $user->id(),
            $paymentHandler->paymentMethod()->id()
        );
        $merchantAccountId = $paymentHandler->paymentMethod()->provider()->accountId();

        $isPayingWithWallet = $paymentMethod->is(PaymentMethodType::WALLET);
        $useAvailableAccountBalance = $paymentHandler->capabilities()->getCapability(
            $merchantAccountId,
            Capability::ACCOUNT_BALANCE_FOR_SUBS
        );
        $canUserOverdraft = app()->make(OverdraftValidatorService::class)->validateByRole(
            $user->branch()->id(),
            $request->getCartUserRole()
        );

        $output = [
            'customer_account_id' => intval($customerAccountId),
            'merchant_account_id' => intval($merchantAccountId),
            'use_available_account_balance' => !$isPayingWithWallet && $useAvailableAccountBalance->isEnabled(),
            'allow_overdraft' => $isPayingWithWallet && $canUserOverdraft,
            'user_tax_id' => $user->metadata()->fiscalDetails()->taxId(),
        ];

        return response()->json([
            'success' => true,
            'result' => $output,
        ]);
    }

    /**
     * @throws InvalidMongoIdException
     * @throws MembershipPlanMinimumPriceValidationException
     * @throws UnsuccessfulOperation
     */
    private function getValidatedAttributes(ValidateMembershipRequest $request)
    {
        // 1. Retrieve core entities.
        [$user, $membership, $plan, $branch] = $this->retrieveEntities($request);

        // 2. Prepare optional parameters and get service (Membership, etc) start date.
        $optionalParameters = $this->prepareOptionalParameters($request, $branch);
        $startDate = $request->getStartDate($this->originByRequestResolver, $branch);

        // 3. Ensure the plan exists and resolve the price.
        $this->ensurePlanExists($plan);
        $price = $this->resolvePrice($optionalParameters, $plan);


        // 4. Validate user can purchase the membership
        $this->validateUserCanPurchaseMembership($membership, $user);

        // 5. Validate plan minimum price.
        $this->planMinimumPriceValidator->validate(
            $plan,
            $membership->id(),
            $branch->id(),
            $price,
            $optionalParameters->get('discounts', []),
            $optionalParameters->get('promo_code')
        );

        // 6. Determine the payment method (free memberships force complimentary).
        $paymentMethod = $this->determinePaymentMethod($plan, $membership, $request->getPaymentMethod());

        // 7. Validate platform-specific terms.
        if ($optionalParameters['platform'] === Platform::WEB_PORTAL) {
            $this->validatePlatformTerms($optionalParameters, $user);
        }

        // 8. Ensure non-staff users aren’t using complimentary (unless free).
        $this->validateNonStaffComplimentary($paymentMethod, $user, $plan, $membership);

        // 9. Validate credit pack purchase (feature flag check).
        $this->validateCreditPackPurchase($branch->id(), $user,  $plan);

        // 10. Validate booking date constraints.
        $this->validateBookingDatePlanStartDate($plan, $startDate, $branch);

        // 11. Normalize payment method and build the purchase request.
        $paymentMethod = $this->getPaymentMethodAsType($paymentMethod);
        $this->purchaseService->buildParams(
            $user,
            $membership,
            $plan,
            $paymentMethod,
            $startDate,
            $optionalParameters
        );
        return $this->purchaseService->buildLegacyPurchaseRequest(
            $user,
            $membership,
            $plan,
            $paymentMethod,
            $optionalParameters
        );
    }

    /**
     * Retrieves the user, membership, plan, and branch from the request.
     */
    private function retrieveEntities(ValidateMembershipRequest $request): array
    {
        $userId = $request->getUserId();
        $membershipId = $request->getMembershipId();
        $planCode = $request->getPlanCode();

        $user = $this->usersRepository
            ->addCriteria(new Id($userId))
            ->addCriteria(new Active(true))
            ->firstOrFail();

        $membership = $this->membershipsRepository
            ->addCriteria(new Id($membershipId))
            ->firstOrFail();

        $plan = $membership->plans()->where('code', $planCode)->first();
        $branch = $user->branch();

        return [$user, $membership, $plan, $branch];
    }

    /**
     * Prepares the optional parameters and sets the platform.
     */
    private function prepareOptionalParameters(ValidateMembershipRequest $request, $branch)
    {
        $optionalParameters = $request->getOptionalParameters($this->originByRequestResolver, $branch);
        $optionalParameters['platform'] = $_SERVER['HTTP_X_GLOFOX_SOURCE'] ?? Platform::OTHER;
        return $optionalParameters;
    }

    /**
     * Throws an exception if the plan does not exist.
     */
    private function ensurePlanExists($plan): void
    {
        if (empty($plan)) {
            throw new UnsuccessfulOperation('PLAN_DOES_NOT_EXISTS');
        }
    }

    /**
     * Resolves the price from optional parameters or falls back to the plan’s amount.
     */
    private function resolvePrice($optionalParameters, $plan)
    {
        return $optionalParameters->get('price') ?: $plan->amount();
    }

    /**
     * Determines the effective payment method. If the membership is free,
     * the payment method is forced to be complimentary.
     */
    private function determinePaymentMethod(Plan $plan, Membership $membership, $paymentMethod)
    {
        if ($this->isFreeMembership($plan->code(), $membership)) {
            return PaymentMethodType::COMPLIMENTARY;
        }
        return $paymentMethod;
    }

    /**
     * Validates that the user can purchase the membership.
     * 
     * If the membership can only be bought once, this method will validate that the user has not already purchased it.
     */
    private function validateUserCanPurchaseMembership(Membership $membership, User $user) 
    {
        if (!$membership->canOnlyBeBoughtOnce()) {
            return;
        }

        /** @var MembershipPurchasedBeforeValidator $validator */
        $validator = app()->make(MembershipPurchasedBeforeValidator::class);

        $validator->execute($user->id(), $membership->toArray());
    }

    /**
     * Validates that the user has accepted the terms on the WEB_PORTAL.
     */
    private function validatePlatformTerms(&$optionalParameters, &$user): void
    {
        $hasAcceptedTerm = $optionalParameters->get('accept_terms', true);
        $this->validateBuyMembershipTerm($user['User']['branch_id'], $hasAcceptedTerm);
        $user['User'][TermTypeEnum::MEMBERPURCHASE] = $hasAcceptedTerm;
    }

    /**
     * Ensures that non-staff users are not using a complimentary payment method
     * unless the membership is free.
     */
    private function validateNonStaffComplimentary($paymentMethod, $user, $plan, $membership): void
    {
        if (
            !Auth::user()->isStaff() &&
            !$this->isFreeMembership($plan->code(), $membership) &&
            PaymentMethodType::COMPLIMENTARY == strtoupper($paymentMethod)
        ) {
            throw new UnsuccessfulOperation('CART_VALIDATOR_MEMBERSHIP_MEMBER_NOT_ALLOWED_COMPLIMENTARY');
        }
    }

    /**
     * Validates that the start date is acceptable for first booking date memberships.
     */
    private function validateBookingDatePlanStartDate(Plan $plan, $startDate, $branch): void
    {
        if (
            $plan->isFirstBookingDate() && $startDate != null && $startDate != Carbon::today(
                $branch->timezone()
            )->getTimestamp()
        ) {
            throw new UnsuccessfulOperation('CART_VALIDATOR_MEMBERSHIP_WITH_DOFB_DOES_NOT_ALLOW_START_DATE');
        }
    }

    /**
     * Validates that a purchase of a credit pack is allowed.
     *
     * If the membership plan starts on the first booking date and the user already has a first booking membership,
     * this method will throw an exception.
     *
     * @param string $branchId The branch associated with the purchase.
     * @param User $user The user making the purchase.
     * @param Plan $plan The plan being purchased.
     *
     * @throws UnsuccessfulOperation if the credit pack purchase is not supported.
     */
    protected function validateCreditPackPurchase(string $branchId, User $user, Plan $plan): void
    {
        $isSingleCreditPack = MembershipType::NUM_CLASSES === $plan->type();

        if (!$isSingleCreditPack) {
            return;
        }    

        $planStartsOn = !empty($plan['starts_on']) ? MembershipPlanStartsOn::byValue($plan['starts_on']) :MembershipPlanStartsOn::PURCHASE_DATE;

        $memberHasFirstBookingMembership = $user->hasFirstBookingMembership();

        // we only check this for credit packs as the other membership types are validated by memberships service
        if ($planStartsOn->is(MembershipPlanStartsOn::FIRST_BOOKING_DATE) && $memberHasFirstBookingMembership) {
            throw new CannotPurchaseAnotherFirstDayOfBookingMembershipException();
        }

    }

    private function getPaymentMethodAsType(string $paymentMethod): PaymentMethodType
    {
        if ($paymentMethod === 'credit_card') {
            return PaymentMethodType::CARD();
        }

        return PaymentMethodType::byValue(strtoupper($paymentMethod));
    }

    /**
     * @throws UnsuccessfulOperation
     */
    private function isFreeMembership(string $requestedPlan, Membership $membership): bool
    {
        $plan = null;
        foreach ($membership->plans() as $membershipPlan) {
            if ($membershipPlan->code() == $requestedPlan) {
                $plan = $membershipPlan;
                break;
            }
        }

        if (null == $plan) {
            throw new UnsuccessfulOperation('There is no plan with the given code');
        }

        $planPrice = $plan->amount();

        if (isset($planPrice) && 0 == $planPrice) {
            return true;
        }

        return false;
    }

    /**
     * This method would validate if the member has accepted the
     * terms and conditions to buy a membership and it will
     * consider if the branch has their terms and conditions set.
     *
     * @param string $branchId [description]
     * @param bool $hasAcceptedTerms [description]
     *
     * @return [type] [description]
     */
    public function validateBuyMembershipTerm(string $branchId, bool $hasAcceptedTerms): bool
    {
        $termsRepository = app()->make(\Glofox\Domain\Terms\Repositories\TermsRepository::class);
        $term = $termsRepository
            ->addCriteria(new BranchId($branchId))
            ->addCriteria(new TermType(TermTypeEnum::MEMBERPURCHASE()))
            ->fetch();

        $isTermSet = !empty($term);
        if ($isTermSet && !$hasAcceptedTerms) {
            throw new UnsuccessfulOperation('YOU_NEED_TO_ACCEPT_TC_TO_BUY_MEMBERSHIP');
        }

        return true;
    }
}
