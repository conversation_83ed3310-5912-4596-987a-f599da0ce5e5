<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: api_invoice.proto

namespace GRPC\Payments\APIService;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>grpc.CreateInvoiceEntityAutoCollectDetails</code>
 */
class CreateInvoiceEntityAutoCollectDetails extends \Google\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string PaymentMethod = 1;</code>
     */
    protected $PaymentMethod = '';
    /**
     * Generated from protobuf field <code>bool Recurring = 2;</code>
     */
    protected $Recurring = false;
    /**
     * Generated from protobuf field <code>bool UseAvailableAccountBalance = 3;</code>
     */
    protected $UseAvailableAccountBalance = false;
    /**
     * Generated from protobuf field <code>bool OffSession = 4;</code>
     */
    protected $OffSession = false;
    /**
     * Generated from protobuf field <code>bool AllowOverdraft = 5;</code>
     */
    protected $AllowOverdraft = false;
    /**
     * Generated from protobuf field <code>bool ForceSingleTransaction = 6;</code>
     */
    protected $ForceSingleTransaction = false;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $PaymentMethod
     *     @type bool $Recurring
     *     @type bool $UseAvailableAccountBalance
     *     @type bool $OffSession
     *     @type bool $AllowOverdraft
     *     @type bool $ForceSingleTransaction
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\ApiInvoice::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string PaymentMethod = 1;</code>
     * @return string
     */
    public function getPaymentMethod()
    {
        return $this->PaymentMethod;
    }

    /**
     * Generated from protobuf field <code>string PaymentMethod = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setPaymentMethod($var)
    {
        GPBUtil::checkString($var, True);
        $this->PaymentMethod = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>bool Recurring = 2;</code>
     * @return bool
     */
    public function getRecurring()
    {
        return $this->Recurring;
    }

    /**
     * Generated from protobuf field <code>bool Recurring = 2;</code>
     * @param bool $var
     * @return $this
     */
    public function setRecurring($var)
    {
        GPBUtil::checkBool($var);
        $this->Recurring = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>bool UseAvailableAccountBalance = 3;</code>
     * @return bool
     */
    public function getUseAvailableAccountBalance()
    {
        return $this->UseAvailableAccountBalance;
    }

    /**
     * Generated from protobuf field <code>bool UseAvailableAccountBalance = 3;</code>
     * @param bool $var
     * @return $this
     */
    public function setUseAvailableAccountBalance($var)
    {
        GPBUtil::checkBool($var);
        $this->UseAvailableAccountBalance = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>bool OffSession = 4;</code>
     * @return bool
     */
    public function getOffSession()
    {
        return $this->OffSession;
    }

    /**
     * Generated from protobuf field <code>bool OffSession = 4;</code>
     * @param bool $var
     * @return $this
     */
    public function setOffSession($var)
    {
        GPBUtil::checkBool($var);
        $this->OffSession = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>bool AllowOverdraft = 5;</code>
     * @return bool
     */
    public function getAllowOverdraft()
    {
        return $this->AllowOverdraft;
    }

    /**
     * Generated from protobuf field <code>bool AllowOverdraft = 5;</code>
     * @param bool $var
     * @return $this
     */
    public function setAllowOverdraft($var)
    {
        GPBUtil::checkBool($var);
        $this->AllowOverdraft = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>bool ForceSingleTransaction = 6;</code>
     * @return bool
     */
    public function getForceSingleTransaction()
    {
        return $this->ForceSingleTransaction;
    }

    /**
     * Generated from protobuf field <code>bool ForceSingleTransaction = 6;</code>
     * @param bool $var
     * @return $this
     */
    public function setForceSingleTransaction($var)
    {
        GPBUtil::checkBool($var);
        $this->ForceSingleTransaction = $var;

        return $this;
    }

}

