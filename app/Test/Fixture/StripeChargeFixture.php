<?php

use Carbon\Carbon;
use Glofox\Domain\Charges\Status;
use Glofox\Domain\Charges\Type;

App::uses('GlofoxTestFixture', 'Test/Fixture');

class StripeChargeFixture extends GlofoxTestFixture
{
    public $import = 'StripeCharge';
    protected $indexes = [
        [
            'metadata.user_id' => 1,
            'created' => -1,
        ],
    ];

    public function init()
    {
        $this->records = [
            [
                '_id' => new MongoId('5422b69ad7b6dd714e8b4567'),
                'object' => 'charge',
                'created' => date('Y-m-d H:m:i'),
                'livemode' => false,
                'amount' => 10,
                'currency' => 'eur',
                'captured' => true,
                'description' => 'Test',
                'statement_description' => 'Test',
                'metadata' => [
                    'user_id' => '59a7011a05c677bda916612a',
                    'branch_id' => '49a7011a05c677b9a916612a',
                    'event_id' => '49b7012a05c677c9a512503c',
                    'namespace' => 'glofox',
                ],
            ],
            [
                '_id' => new MongoId('5422ab50d7b6dd72cd8b4568'),
                'object' => 'charge',
                'created' => date('Y-m-d H:m:i'),
                'livemode' => false,
                'amount' => 10,
                'currency' => 'eur',
                'captured' => true,
                'description' => 'Test',
                'statement_description' => 'Test',
                'metadata' => [
                    'user_id' => '59a7011a05c677bda916612a',
                    'branch_id' => '49a7011a05c677b9a916612a',
                    'membership_id' => '54107c1cd7b6ddc3a98b4577',
                    'namespace' => 'glofox',
                ],
            ],
            [
                '_id' => new MongoId('5422ab50d7b6dd72cd8b4569'),
                'object' => 'charge',
                'created' => (new Carbon())->format('Y-m-d H:m:i'),
                'livemode' => false,
                'amount' => 85,
                'paid' => false,
                'invoice_id' => 'in_testduplicatedinvoice',
                'currency' => 'eur',
                'captured' => true,
                'description' => 'Test',
                'statement_description' => 'Test Duplicated failed invoice',
                'metadata' => [
                    'user_id' => '59a7011a05c677bda916612a',
                    'branch_id' => '49a7011a05c677b9a916612a',
                    'glofox_event' => 'subscription_payment_failed',
                ],
            ],
            [
                '_id' => new MongoId('5422ab50d7b6dd72cd8b4570'),
                'object' => 'charge',
                'created' => (new Carbon())->modify('-1 day')->format('Y-m-d H:m:i'),
                'livemode' => false,
                'amount' => 85,
                'paid' => false,
                'invoice_id' => 'in_testduplicatedinvoice',
                'currency' => 'eur',
                'captured' => true,
                'description' => 'Test',
                'statement_description' => 'Test Duplicated failed invoice',
                'metadata' => [
                    'user_id' => '59a7011a05c677bda916612a',
                    'branch_id' => '49a7011a05c677b9a916612a',
                    'glofox_event' => 'subscription_payment_failed',
                ],
            ],
            [
                '_id' => new MongoId('5422ab50d7b6dd72cd8b4571'),
                'object' => 'charge',
                'created' => (new Carbon())->modify('-1 day')->format('Y-m-d H:m:i'),
                'livemode' => false,
                'amount' => 85,
                'paid' => false,
                'invoice_id' => 'in_testduplicatedinvoice2',
                'currency' => 'eur',
                'captured' => true,
                'description' => 'Test',
                'statement_description' => 'Test Duplicated failed invoice',
                'metadata' => [
                    'user_id' => '59a7011a05c677bda916612a',
                    'branch_id' => '49a7011a05c677b9a916612a',
                    'glofox_event' => 'subscription_payment_failed',
                ],
            ],
            [
                '_id' => new MongoId('5422ab50d7b6dd72cd8b4572'),
                'event_id' => 'evt_alreadyprocessed',
                'object' => 'charge',
                'created' => (new Carbon())->modify('-1 day')->format('Y-m-d H:m:i'),
                'livemode' => false,
                'amount' => 85,
                'paid' => false,
                'invoice_id' => 'in_testduplicatedinvoice2',
                'currency' => 'eur',
                'captured' => true,
                'description' => 'Test',
                'statement_description' => 'Event already exists',
                'metadata' => [
                    'user_id' => '59a7011a05c677bda916612a',
                    'branch_id' => '49a7011a05c677b9a916612a',
                    'glofox_event' => \Glofox\Domain\Charges\Type::SUBSCRIPTION_PAYMENT,
                ],
            ],
            [
                '_id' => new MongoId('5422ab50d7b6dd72cd8b4573'),
                'object' => 'charge',
                'created' => (new Carbon())->modify('-1 day')->format('Y-m-d H:m:i'),
                'amount' => 85,
                'paid' => false,
                'currency' => 'eur',
                'captured' => true,
                'description' => 'Test',
                'transaction_status' => Status::PENDING,
                'metadata' => [
                    'user_id' => '59a7011a05c677bda916612a',
                    'branch_id' => '49a7011a05c677b9a916612a',
                    'glofox_event' => \Glofox\Domain\Charges\Type::SUBSCRIPTION_PRORATE,
                ],
            ],
            [
                '_id' => new MongoId('5422ab50d7b6dd72cd8b4575'),
                'object' => 'charge',
                'created' => (new Carbon())->modify('-1 day')->format('Y-m-d H:m:i'),
                'amount' => 85,
                'paid' => false,
                'currency' => 'eur',
                'captured' => true,
                'description' => 'Test',
                'transaction_status' => Status::PENDING,
                'metadata' => [
                    'user_id' => '59a7011a05c677bda916612a',
                    'branch_id' => '49a7011a05c677b9a916612a',
                    'glofox_event' => \Glofox\Domain\Charges\Type::SUBSCRIPTION_PAYMENT,
                ],
            ],
            [
                '_id' => new MongoId('5422ab50d7b6dd72cd8b5555'),
                'object' => 'charge',
                'created' => (new Carbon())->modify('-1 day')->format('Y-m-d H:m:i'),
                'amount' => 85,
                'paid' => false,
                'currency' => 'eur',
                'captured' => true,
                'description' => 'Test',
                'transaction_status' => Status::PENDING_INTENT,
                'transaction_provider_id' => 'mocked-psp-transaction-id',
                'event_id' => 'mocked-event-id',
                'invoice_id' => 'mocked-invoice-id',
                'metadata' => [
                    'user_id' => '5b19430ea0d988945a164335',
                    'branch_id' => '49a7011a05c677b9a916612a',
                    'glofox_event' => \Glofox\Domain\Charges\Type::SUBSCRIPTION_PAYMENT,
                ],
            ],
            [
                '_id' => new MongoId('5422b69ad7b6dd324e8b8572'),
                'object' => 'charge',
                'created' => date('Y-m-d H:m:i'),
                'livemode' => false,
                'amount' => 12.12,
                'currency' => 'eur',
                'captured' => true,
                'description' => 'Test',
                'statement_description' => 'Test',
                'metadata' => [
                    'user_id' => '59a7011a05c677bda916612a',
                    'branch_id' => '49a7011a05c677b9a916612a',
                    'event_id' => '49b7012a05c677c9a512503c',
                    'namespace' => 'glofox',
                    'payment_method' => 'card',
                    'environment' => 'test',
                ],
            ],
            [
                '_id' => new MongoId('5db89b3d2cab68041a2707ce'),
                'object' => 'charge',
                'created' => date('Y-m-d H:m:i'),
                'livemode' => false,
                'amount' => 10,
                'currency' => 'eur',
                'captured' => true,
                'description' => 'Test Refund',
                'refunded' => true,
                'paid' => true,
                'transaction_provider_id' => 'trans_5db89b3d2cab68041a2707ce',
                'metadata' => [
                    'user_id' => '5d22c338feed813bfd2127fa',
                    'branch_id' => '5db7f2d7e136e453eb0d4089',
                    'namespace' => 'foo',
                    'payment_method' => 'card',
                    'environment' => 'test',
                    'glofox_event' => 'custom_charge',
                ],
            ],
            [
                '_id' => new MongoId('6486f9d90b2b69845281adef'),
                'object' => 'charge',
                'created' => date('Y-m-d H:m:i'),
                'livemode' => false,
                'amount' => 52.12,
                'amount_refunded' => 22.12,
                'currency' => 'eur',
                'captured' => true,
                'description' => 'Upfront Fee (x1) 30.00 - Membership Options (Month-to-Month) (x1) 22.12',
                'refunded' => true,
                'paid' => true,
                'transaction_provider_id' => '1224545',
                'transaction_status' => 'PARTIAL_REFUNDED',
                'refunds' => ["6495bc7e80f7190e2a04d8ad"],
                'metadata' => [
                    'user_id' => '5d22c338feed813bfd2127fa',
                    'branch_id' => '5db7f2d7e136e453eb0d4089',
                    'namespace' => 'glofox',
                    'payment_method' => 'card',
                    'environment' => 'test',
                    'glofox_event' => 'custom_charge',
                ],
            ],
            [
                '_id' => new MongoId('5d8b6b8c532c54916cf6e590'),
                'object' => 'charge',
                'created' => date('Y-m-d H:m:i', strtotime('-1 day')),
                'amount' => 50,
                'paid' => false,
                'currency' => 'eur',
                'captured' => true,
                'description' => 'Test for Purchase list, metadata.amount field is 0',
                'transaction_status' => Status::PAID,
                'event_id' => 'mocked-event-id',
                'sold_by_user_id' => 'a9a5521a05c687bda917755c',
                'metadata' => [
                    'user_id' => 'a9a5521a05c687bda917785c',
                    'branch_id' => '49a7011a05c677b9a916612a',
                    'product_id' => '5b44ae18b13d95b5f633fd06',
                    'glofox_event' => Type::BUY_PRODUCT,
                    'presentation_id' => 1_506_964_157_657,
                    'payment_method' => 'cash',
                    'amount' => 0,
                ],
            ],
            [
                '_id' => new MongoId('5d8b6b8c532c54916cf6e596'),
                'object' => 'charge',
                'created' => date('Y-m-d H:m:i', strtotime('-1 day')),
                'amount' => 50,
                'paid' => false,
                'currency' => 'eur',
                'captured' => true,
                'description' => 'Test for Purchase list, metadata.amount field is 0',
                'transaction_status' => Status::PAID,
                'event_id' => 'mocked-event-id',
                'sold_by_user_id' => 'a9a5521a05c687bda917755c',
                'metadata' => [
                    'user_id' => 'a9a5521a05c687bda917785c',
                    'branch_id' => '49a7011a05c677b9a916612a',
                    'product_id' => '5b44ae18b13d95b5f633fd06',
                    'glofox_event' => Type::BUY_PRODUCT,
                    'presentation_id' => 1_506_964_157_657,
                    'payment_method' => 'cash',
                    'amount' => 0,
                ],
            ],
            [
                '_id' => new MongoId('6422ab50d7b6dd72cd8b4568'),
                'object' => 'charge',
                'created' => date('Y-m-d H:m:i'),
                'livemode' => false,
                'amount' => 10,
                'currency' => 'eur',
                'captured' => true,
                'description' => 'Charge with no price breakdown',
                'statement_description' => 'Test',
                'metadata' => [
                    'user_id' => '59a7011a05c677bda916612a',
                    'branch_id' => '49a7011a05c677b9a916612a',
                    'membership_id' => '54107c1cd7b6ddc3a98b4577',
                    'namespace' => 'glofox',
                ],
            ],
            [
                '_id' => new MongoId('6422ab50d7b6dd72cd8b4569'),
                'object' => 'charge',
                'created' => date('Y-m-d H:m:i'),
                'livemode' => false,
                'amount' => 9,
                'currency' => 'eur',
                'captured' => true,
                'description' => 'Charge with no discount in price breakdown',
                'statement_description' => 'Test',
                'metadata' => [
                    'user_id' => '59a7011a05c677bda916612a',
                    'branch_id' => '49a7011a05c677b9a916612a',
                    'membership_id' => '54107c1cd7b6ddc3a98b4577',
                    'namespace' => 'glofox',
                ],
            ],
            [
                '_id' => new MongoId('628caaa628b06a88a684d114'),
                'id' => '9021338',
                'transaction_status' => 'PAID',
                'transaction_provider_id' => '9021338',
                'metadata' => [
                    'namespace' => 'billingstestterminat',
                    'branch_id' => '61112721154eb6320c5e2f98',
                    'glofox_event' => 'invoice_payment',
                    'description' => 'Upfront Fee (55.73) - #SCA Loophole (92.21) - Access Card (45.48)',
                    'user_id' => '627a8cddf2d6e4711c65a925',
                    'user_name' => 'A8 Client',
                    'payment_method' => 'card',
                    'is_forgiven' => false,
                    'already_paid' => false,
                    'presentation_id' => '',
                    'membership_id' => '6228d28025cdc533323c2744',
                    'membership_name' => '#SCA Loophole',
                    'user_membership_id' => 'memb_29bdnwLrvGbI4RAYy1ZBhd5Kn8j',
                    'plan_code' => '1646842218108',
                    'plan_name' => '#SCA Loophole (mem - recurring, unlimited, with UF fee)'
                ],
                'invoice_id' => '29bdnzkOQcMdgtHmykVneLC5Q55',
                'amount' => 193.42,
                'currency' => 'EUR',
                'paid' => true,
                'refunded' => false,
                'description' => 'Upfront Fee (55.73) - #SCA Loophole (92.21) - Access Card (45.48)',
                'event_id' => '',
                'transaction_group_id' => 'fee490c2-372b-4f6c-9c6e-da7e0a4a2c01',
                'sold_by_user_id' => '61112728154eb6320c5e2fa1',
                'created' => '2022-05-24T09:51:34Z',
                'modified' => '2022-05-24T09:51:36.974Z',
                'amount_refunded' => 0
            ],
        ];
        $this->createGroupedCharges();

        $this->records = array_merge(
            $this->records,
            $this->forPaymentIntent(),
            $this->forSalesAttribution(),
            $this->forSalesTax(),
            $this->forCsvTransactionReport(),
            $this->forMembershipPurchased(),
            $this->forMembershipPurchasedWithAddOns(),
            $this->forAddonPurchased(),
            $this->refund(),
            $this->flexiblePayment(),
            $this->forBookingActivityContext()
        );

        parent::init();
    }

    private function createGroupedCharges()
    {
        $transactionGroupId1 = 'TRANSACTION_GROUP_ID_1';

        $baseCharge = collect([
            'object' => 'charge',
            'created' => date('Y-m-d H:m:i'),
            'currency' => 'eur',
            'metadata' => [
                'user_id' => '59a7011a05c677bda916612a',
                'branch_id' => '49a7011a05c677b9a916612a',
                'event_id' => '49b7012a05c677c9a512503c',
                'namespace' => 'glofox',
            ],
        ]);

        $groupedCharges = collect([])
            ->push(
                $baseCharge->put('amount', 0)->put('transaction_group_id', $transactionGroupId1)
            )
            ->push(
                $baseCharge->put('amount', 5)->put('transaction_group_id', $transactionGroupId1)
            )
            ->push(
                $baseCharge->put('amount', 10)->put('transaction_group_id', $transactionGroupId1)
            );

        $this->records = array_merge($this->records, $groupedCharges->toArray());
    }

    private function forPaymentIntent(): array
    {
        return [
            [
                '_id' => new MongoId('5d682b908d8540851fe60666'),
                'object' => 'charge',
                'created' => date('Y-m-d H:m:i', strtotime('-1 day')),
                'amount' => 50,
                'paid' => false,
                'currency' => 'eur',
                'captured' => true,
                'description' => 'Test for payment intent',
                'transaction_status' => Status::PENDING,
                'transaction_provider_id' => '5d682bf08d8540851fe6066a',
                'event_id' => 'mocked-event-id',
                'metadata' => [
                    'payment_method' => 'credit_card',
                    'user_id' => '59a7011a05c677bda916612a',
                    'branch_id' => '49a7011a05c677b9a916612a',
                    'glofox_event' => \Glofox\Domain\Charges\Type::SUBSCRIPTION_PAYMENT,
                ],
            ],
            [
                '_id' => new MongoId('5d794d6a7efab3813a2dfe59'),
                'object' => 'charge',
                'created' => date('Y-m-d H:m:i', strtotime('-1 day')),
                'amount' => 50,
                'paid' => false,
                'currency' => 'eur',
                'captured' => true,
                'description' => 'Test for payment intent',
                'transaction_status' => Status::PENDING_INTENT,
                'transaction_provider_id' => '5d682bf08d8540851fe6066a',
                'event_id' => 'mocked-event-id',
                'metadata' => [
                    'branch_id' => '5d794d5c7efab3813a2dfe58',
                    'namespace' => 'foo',
                    'payment_method' => 'card',
                    'glofox_event' => \Glofox\Domain\Charges\Type::SUBSCRIPTION_PAYMENT,
                ],
            ],
            [
                '_id' => new MongoId('5422ab50d7b6dd72cd8b3569'),
                'object' => 'charge',
                'created' => date('Y-m-d H:m:i'),
                'livemode' => false,
                'amount' => 10,
                'currency' => 'eur',
                'captured' => true,
                'description' => 'Test',
                'statement_description' => 'Test',
                'metadata' => [
                    'user_id' => '59a7011a05c677bda916612a',
                    'branch_id' => '49a7011a05c677b9a916612a',
                    'membership_id' => '54107c1cd7b6ddc3a98b4577',
                    'namespace' => 'glofox',
                    'glofox_event' => \Glofox\Domain\Charges\Type::SUBSCRIPTION_PAYMENT,
                    'payment_method' => \Glofox\Domain\PaymentMethods\Type::WALLET,
                ],
            ],
        ];
    }

    private function forSalesAttribution(): array
    {
        return [
            [
                '_id' => new MongoId('5d8b6b8c532c54916cf6e595'),
                'object' => 'charge',
                'created' => date('Y-m-d H:m:i', strtotime('-1 day')),
                'amount' => 50,
                'paid' => false,
                'currency' => 'eur',
                'captured' => true,
                'description' => 'Test for Sales Attribution - Product purchase',
                'transaction_status' => Status::PENDING,
                'transaction_provider_id' => '5d682bf08d8540851fe6066a',
                'event_id' => 'mocked-event-id',
                'sold_by_user_id' => 'a9a5521a05c687bda917755c',
                'metadata' => [
                    'user_id' => 'a9a5521a05c687bda917785c',
                    'branch_id' => '49a7011a05c677b9a916612a',
                    'product_id' => '5b44ae18b13d95b5f633fd06',
                    'glofox_event' => Type::BUY_PRODUCT,
                    'presentation_id' => 1_506_964_157_657,
                    'payment_method' => 'card',
                ],
            ],
            [
                '_id' => new MongoId('5d8b70b2532c54916cf6e597'),
                'object' => 'charge',
                'created' => date('Y-m-d H:m:i', strtotime('-1 day')),
                'amount' => 5,
                'paid' => false,
                'currency' => 'eur',
                'captured' => true,
                'description' => 'Test for Sales Attribution - Custom Charge',
                'transaction_status' => Status::PENDING,
                'transaction_provider_id' => '5d682bf08d8540851fe6066a',
                'event_id' => 'mocked-event-id',
                'sold_by_user_id' => 'a9a5521a05c687bda917755c',
                'metadata' => [
                    'user_id' => 'a9a5521a05c687bda917785c',
                    'branch_id' => '49a7011a05c677b9a916612a',
                    'glofox_event' => Type::CUSTOM_CHARGE,
                    'payment_method' => 'card',
                    'membership_id' => '54107c1cd7b6ddc3a98b4577',
                    'plan_code' => '54107aced7b6dd7aab8b4567',
                ],
            ],
        ];
    }

    private function forSalesTax(): array
    {
        return [
            [
                '_id' => new MongoId('5d9d34daaad495538f8102a5'),
                'object' => 'charge',
                'created' => date('Y-m-d H:m:i', strtotime('-1 day')),
                'amount' => 50,
                'paid' => false,
                'currency' => 'eur',
                'captured' => true,
                'description' => 'Sales Tax 1',
                'transaction_status' => Status::PAID,
                'transaction_provider_id' => '5d682bf08d8540851fe6066a',
                'event_id' => 'mocked-event-id',
                'sold_by_user_id' => 'a9a5521a05c687bda917755c',
                'metadata' => [
                    'user_id' => 'a9a5521a05c687bda917785c',
                    'branch_id' => '5d9cdc06aad495538f8102a4',
                    'namespace' => 'sales-tax',
                    'product_id' => '5b44ae18b13d95b5f633fd06',
                    'glofox_event' => Type::BUY_PRODUCT,
                    'presentation_id' => 1_506_964_157_657,
                    'payment_method' => 'card',
                ],
            ],
            [
                '_id' => new MongoId('5d9d34e2aad495538f8102a6'),
                'object' => 'charge',
                'created' => date('Y-m-d H:m:i', strtotime('-1 day')),
                'amount' => 5,
                'paid' => false,
                'currency' => 'eur',
                'captured' => true,
                'description' => 'Sales Tax 2',
                'transaction_status' => Status::PAID,
                'transaction_provider_id' => '5d682bf08d8540851fe6066a',
                'event_id' => 'mocked-event-id',
                'sold_by_user_id' => 'a9a5521a05c687bda917755c',
                'metadata' => [
                    'user_id' => 'a9a5521a05c687bda917785c',
                    'branch_id' => '5d9cdc06aad495538f8102a4',
                    'namespace' => 'sales-tax',
                    'glofox_event' => Type::CUSTOM_CHARGE,
                    'payment_method' => 'card',
                ],
            ],
            [
                '_id' => new MongoId('5d9e2500aad495538f8102a7'),
                'object' => 'charge',
                'created' => date('Y-m-d H:m:i', strtotime('-1 day')),
                'amount' => 5,
                'paid' => false,
                'currency' => 'eur',
                'captured' => true,
                'description' => 'Sales Tax 2',
                'transaction_status' => Status::PAID,
                'transaction_provider_id' => '5d682bf08d8540851fe6066a',
                'event_id' => 'mocked-event-id',
                'sold_by_user_id' => 'a9a5521a05c687bda917755c',
                'metadata' => [
                    'user_id' => 'a9a5521a05c687bda917785c',
                    'branch_id' => '5d9cdc06aad495538f8102a4',
                    'namespace' => 'sales-tax',
                    'glofox_event' => Type::CUSTOM_CHARGE,
                    'payment_method' => 'card',
                ],
            ],
        ];
    }

    private function forCsvTransactionReport(): array
    {
        return [
            [
                '_id' => new MongoId('5a3a5054948dae0706000130'),
                'created' => date('Y-m-d H:i:s', strtotime('2019-10-26 14:13:00')),
                'amount' => 9,
                'paid' => true,
                'currency' => 'eur',
                'captured' => true,
                'transaction_status' => Status::PAID,
                'transaction_provider_id' => 'trans_5a3a5054948dae0706000011',
                'event_id' => 'mocked-event-id',
                'metadata' => [
                    'user_id' => '5db1b2a0f15a8f08c04f28b2',
                    'user_name' => 'Discount Fixed',
                    'branch_id' => '5db1b008f15a8f08c04f28af',
                    'namespace' => 'csv-transaction-report',
                    'membership_id' => '5a146888d721b079504aa0ff',
                    'plan_code' => '5a1430309b264e6a99186004',
                    'glofox_event' => Type::CUSTOM_CHARGE,
                    'payment_method' => 'card',
                ],
                'payout' => [
                    'id' => 'payout-2',
                    'transaction_id' => 'transaction-3',
                    'gross_amount' => 17.5,
                    'fee' => 0,
                    'net_amount' => 17.5,
                    'date' => '2019-10-26',
                ],
            ],
            [
                '_id' => new MongoId('5db1b036f15a8f08c04f28af'),
                'object' => 'charge',
                'created' => date('Y-m-d H:i:s', strtotime('2019-10-24 14:07:23')),
                'amount' => 50,
                'paid' => true,
                'currency' => 'eur',
                'captured' => true,
                'transaction_status' => Status::PAID,
                'transaction_provider_id' => '5d682bf08d8540851fe6066a',
                'sold_by_user_id' => '59a7011a05c677bda512212a',
                'event_id' => 'mocked-event-id',
                'metadata' => [
                    'user_id' => '5db1affef15a8f08c04f28ad',
                    'user_name' => 'John Doe #1',
                    'branch_id' => '5db1b008f15a8f08c04f28ae',
                    'namespace' => 'csv-transaction-report',
                    'product_id' => '5b44ae18b13d95b5f633fd06',
                    'glofox_event' => Type::BUY_PRODUCT,
                    'presentation_id' => 1_506_964_157_657,
                    'payment_method' => 'card',
                ],
            ],
            [
                '_id' => new MongoId('5db1b03df15a8f08c04f28b0'),
                'created' => date('Y-m-d H:i:s', strtotime('2019-10-24 14:07:23')),
                'amount' => 5,
                'paid' => true,
                'currency' => 'eur',
                'captured' => true,
                'transaction_status' => Status::PAID,
                'transaction_provider_id' => '5d682bf08d8540851fe6066a',
                'event_id' => 'mocked-event-id',
                'metadata' => [
                    'user_id' => '5db1b2a0f15a8f08c04f28b1',
                    'user_name' => 'John Doe #2',
                    'branch_id' => '5db1b008f15a8f08c04f28ae',
                    'namespace' => 'csv-transaction-report',
                    'glofox_event' => Type::CUSTOM_CHARGE,
                    'payment_method' => 'card',
                ],
            ],
            [
                '_id' => new MongoId('5dc5bcbe7d54963c7c846b5b'),
                'created' => date('Y-m-d H:i:s', strtotime('2019-10-21 14:07:23')),
                'amount' => 98.45,
                'paid' => true,
                'currency' => 'eur',
                'captured' => true,
                'transaction_status' => Status::PAID,
                'transaction_provider_id' => '5d682bf08d8540851fe6066a',
                'event_id' => 'mocked-event-id',
                'metadata' => [
                    'user_id' => '5db1b2a0f15a8f08c04f28b1',
                    'user_name' => 'John Doe #3',
                    'branch_id' => '5db1b008f15a8f08c04f28af',
                    'namespace' => 'csv-transaction-report',
                    'product_id' => '5b44ae18b13d95b5f633fd06',
                    'glofox_event' => Type::BUY_PRODUCT,
                    'payment_method' => 'cash',
                ],
            ],
            [
                '_id' => new MongoId('5dc5bd817d54963c7c846b5c'),
                'created' => date('Y-m-d H:i:s', strtotime('2019-10-21 14:07:23')),
                'amount' => 13.76,
                'paid' => true,
                'currency' => 'eur',
                'captured' => true,
                'transaction_status' => Status::PAID,
                'transaction_provider_id' => '5d682bf08d8540851fe6066a',
                'event_id' => 'mocked-event-id',
                'metadata' => [
                    'user_id' => '5db1b2a0f15a8f08c04f28b2',
                    'user_name' => 'John Doe #4',
                    'branch_id' => '5db1b008f15a8f08c04f28af',
                    'namespace' => 'csv-transaction-report',
                    'glofox_event' => Type::CUSTOM_CHARGE,
                    'payment_method' => 'cash',
                ],
            ],
            [
                '_id' => new MongoId('5afdc3da1129ba338f595132'),
                'created' => date('Y-m-d H:i:s', strtotime('2019-10-26 14:07:24')),
                'amount' => 98.45,
                'paid' => true,
                'currency' => 'eur',
                'captured' => true,
                'transaction_status' => Status::PAID,
                'transaction_provider_id' => '5d682bf08d8540851fe6066a',
                'event_id' => 'mocked-event-id',
                'metadata' => [
                    'user_id' => '5db1b2a0f15a8f08c04f28b1',
                    'user_name' => 'John Doe #3',
                    'branch_id' => '5db1b008f15a8f08c04f28af',
                    'namespace' => 'csv-transaction-report',
                    'product_id' => '5b44ae18b13d95b5f633fd06',
                    'glofox_event' => Type::BUY_PRODUCT,
                    'payment_method' => 'card',
                ],
                'payout' => [
                    'id' => 'payout-1',
                    'transaction_id' => 'transaction-1',
                    'gross_amount' => 98.44,
                    'fee' => -1.23,
                    'net_amount' => 97.21,
                    'date' => '2019-01-01',
                ],
            ],
            [
                '_id' => new MongoId('5841700f7cad363dc88b4568'),
                'created' => date('Y-m-d H:i:s', strtotime('2019-10-26 14:07:23')),
                'amount' => 13.76,
                'paid' => true,
                'currency' => 'eur',
                'captured' => true,
                'transaction_status' => Status::PAID,
                'transaction_provider_id' => '5d682bf08d8540851fe6066a',
                'event_id' => 'mocked-event-id',
                'metadata' => [
                    'user_id' => '5db1b2a0f15a8f08c04f28b2',
                    'user_name' => 'John Doe #4',
                    'branch_id' => '5db1b008f15a8f08c04f28af',
                    'namespace' => 'csv-transaction-report',
                    'glofox_event' => Type::CUSTOM_CHARGE,
                    'payment_method' => 'direct_debit',
                ],
                'payout' => null,
            ],
            [
                '_id' => new MongoId('5a4a8e8a047673a418000004'),
                'created' => date('Y-m-d H:i:s', strtotime('2019-10-26 14:07:23')),
                'amount' => 13.75,
                'paid' => true,
                'currency' => 'eur',
                'captured' => true,
                'transaction_status' => Status::PAID,
                'transaction_provider_id' => '5d682bf08d8540851fe6066a',
                'event_id' => 'mocked-event-id',
                'metadata' => [
                    'user_id' => '5db1b2a0f15a8f08c04f28b2',
                    'user_name' => 'John Doe #4',
                    'branch_id' => '5db1b008f15a8f08c04f28af',
                    'namespace' => 'csv-transaction-report',
                    'glofox_event' => Type::CUSTOM_CHARGE,
                    'payment_method' => 'direct_debit',
                ],
                'payout' => null,
            ],
            [
                '_id' => new MongoId('5841700f7cad363dc88b4569'),
                'created' => date('Y-m-d H:i:s', strtotime('2019-10-26 14:07:22')),
                'amount' => 10,
                'paid' => true,
                'currency' => 'eur',
                'captured' => true,
                'transaction_status' => Status::PAID,
                'transaction_provider_id' => '5d682bf08d8540851fe6066a',
                'event_id' => 'mocked-event-id',
                'metadata' => [
                    'user_id' => '5db1b2a0f15a8f08c04f28b2',
                    'user_name' => 'John Doe #4',
                    'branch_id' => '5db1b008f15a8f08c04f28af',
                    'namespace' => 'csv-transaction-report',
                    'membership_id' => '5a146888d721b079504aa0ff',
                    'plan_code' => '5a1430309b264e6a99186004',
                    'glofox_event' => Type::SUBSCRIPTION_PAYMENT,
                    'payment_method' => 'card',
                ],
                'payout' => [
                    'id' => 'payout-2',
                    'transaction_id' => 'transaction-3',
                    'gross_amount' => 10,
                    'fee' => 0,
                    'net_amount' => 10,
                    'date' => '2019-10-26',
                ],
            ],
            [
                '_id' => new MongoId('5841700f7cad363dc88b4570'),
                'created' => date('Y-m-d H:i:s', strtotime('2019-10-26 14:07:22')),
                'amount' => 25,
                'paid' => false,
                'currency' => 'eur',
                'captured' => false,
                'transaction_status' => Status::ERROR,
                'transaction_provider_id' => '5d682bf08d8540851fe6066a',
                'event_id' => 'mocked-event-id',
                'metadata' => [
                    'user_id' => '5db1b2a0f15a8f08c04f28b2',
                    'user_name' => 'John Doe #4',
                    'branch_id' => '5db1b008f15a8f08c04f28af',
                    'namespace' => 'csv-transaction-report',
                    'membership_id' => '5a146888d721b079504aa0ff',
                    'plan_code' => '5a1430309b264e6a99186004',
                    'glofox_event' => Type::SUBSCRIPTION_PAYMENT,
                    'payment_method' => 'card',
                ],
            ],
            [
                '_id' => new MongoId('5841700f7cad363dc88b4571'),
                'created' => date('Y-m-d H:i:s', strtotime('2019-10-26 14:08:22')),
                'amount' => 25,
                'paid' => false,
                'currency' => 'eur',
                'captured' => false,
                'transaction_status' => Status::PENDING,
                'transaction_provider_id' => '5d682bf08d8540851fe6066a',
                'event_id' => 'mocked-event-id',
                'metadata' => [
                    'user_id' => '5db1b2a0f15a8f08c04f28b2',
                    'user_name' => 'John Doe #4',
                    'branch_id' => '5db1b008f15a8f08c04f28af',
                    'namespace' => 'csv-transaction-report',
                    'membership_id' => '5a146888d721b079504aa0ff',
                    'plan_code' => '5a1430309b264e6a99186004',
                    'glofox_event' => Type::SUBSCRIPTION_PAYMENT,
                    'payment_method' => 'card',
                ],
                'payout' => [
                    'id' => 'payout-2',
                    'transaction_id' => 'transaction-4',
                    'gross_amount' => 25,
                    'fee' => -1,
                    'net_amount' => 24,
                    'date' => '2019-10-26',
                ],
            ],
            [
                '_id' => new MongoId('58e4cf398b8c354a0c000003'),
                'created' => date('Y-m-d H:i:s', strtotime('2019-10-26 14:06:23')),
                'amount' => 0,
                'paid' => false,
                'currency' => 'eur',
                'captured' => false,
                'transaction_status' => Status::PAID,
                'transaction_provider_id' => '5d682bf08d8540851fe6066a',
                'event_id' => 'mocked-event-id',
                'metadata' => [
                    'user_id' => '5db1b2a0f15a8f08c04f28b2',
                    'user_name' => 'John Doe #4',
                    'branch_id' => '5db1b008f15a8f08c04f28af',
                    'namespace' => 'csv-transaction-report',
                    'membership_id' => '5a146888d721b079504aa0ff',
                    'plan_code' => '5a1430309b264e6a99186004',
                    'glofox_event' => Type::SUBSCRIPTION_PAYMENT,
                    'payment_method' => 'card',
                ],
                'payout' => null,
                'invoice_id' => 'invoice_id_test',
            ],
            [
                '_id' => new MongoId('5a3a5054948dae0706000001'),
                'created' => date('Y-m-d H:i:s', strtotime('2019-10-26 14:05:23')),
                'amount' => 100,
                'paid' => false,
                'refunded' => true,
                'refunds' => [
                    '5a3a51b994b7bd9305000001',
                ],
                'currency' => 'eur',
                'captured' => false,
                'transaction_status' => Status::PARTIAL_REFUNDED,
                'transaction_provider_id' => '5d682bf08d8540851fe6066a',
                'event_id' => 'mocked-event-id',
                'metadata' => [
                    'user_id' => '5db1b2a0f15a8f08c04f28b2',
                    'user_name' => 'John Doe #4',
                    'branch_id' => '5db1b008f15a8f08c04f28af',
                    'namespace' => 'csv-transaction-report',
                    'membership_id' => '5a146888d721b079504aa0ff',
                    'plan_code' => '5a1430309b264e6a99186004',
                    'glofox_event' => Type::SUBSCRIPTION_PAYMENT,
                    'payment_method' => 'card',
                ],
                'payout' => [
                    'id' => 'payout-3',
                    'transaction_id' => 'transaction-5',
                    'gross_amount' => 100,
                    'fee' => -1,
                    'net_amount' => 99,
                    'date' => '2019-10-26T12:11:54.712169Z',
                ],
            ],
            [
                '_id' => new MongoId('5c731edc37d7e311d50bcad1'),
                'created' => date('Y-m-d H:i:s', Carbon::now()->timestamp),
                'amount' => 25,
                'paid' => false,
                'currency' => 'eur',
                'captured' => false,
                'transaction_status' => Status::ERROR,
                'transaction_provider_id' => '5d682bf08d8540851fe6066a',
                'event_id' => 'mocked-event-id',
                'metadata' => [
                    'user_id' => '5db1b2a0f15a8f08c04f28b2',
                    'user_name' => 'John Doe #5',
                    'branch_id' => '5d56d214be51ef060e38409c',
                    'namespace' => 'failed-pmts',
                    'membership_id' => '5a146888d721b079504aa0ff',
                    'plan_code' => '5a1430309b264e6a99186004',
                    'glofox_event' => Type::SUBSCRIPTION_PAYMENT,
                    'payment_method' => 'card',
                ],
            ],
            [
                '_id' => new MongoId('5c731edc37d7e311d50bcad2'),
                'created' => date('Y-m-d H:i:s', Carbon::now()->timestamp),
                'amount' => 25,
                'paid' => false,
                'currency' => 'eur',
                'captured' => false,
                'transaction_status' => Status::ERROR,
                'transaction_provider_id' => '5d682bf08d8540851fe6066a',
                'event_id' => 'mocked-event-id',
                'metadata' => [
                    'user_id' => '5db1b2a0f15a8f08c04f28b2',
                    'stripe_subscription_id' => '5db1b2a0f15a8f08c04f1234',
                    'user_name' => 'John Doe #5',
                    'branch_id' => '5d56d214be51ef060e38409c',
                    'namespace' => 'failed-pmts',
                    'membership_id' => '5a146888d721b079504aa0ff',
                    'plan_code' => '5a1430309b264e6a99186004',
                    'glofox_event' => Type::SUBSCRIPTION_PAYMENT,
                    'payment_method' => 'direct_debit',
                ],
            ],
            [
                '_id' => new MongoId('5c731edc37d7e311d50bcad3'),
                'created' => date('Y-m-d H:i:s', Carbon::now()->timestamp),
                'amount' => 25,
                'paid' => false,
                'currency' => 'eur',
                'captured' => false,
                'transaction_status' => Status::ERROR,
                'transaction_provider_id' => '5d682bf08d8540851fe6066a',
                'event_id' => 'mocked-event-id',
                'metadata' => [
                    'user_id' => '5db1b2a0f15a8f08c04f28b2',
                    'stripe_subscription_id' => '5db1b2a0f15a8f08c04f1234',
                    'user_name' => 'John Doe #5',
                    'branch_id' => '5d56d214be51ef060e38409c',
                    'namespace' => 'failed-pmts',
                    'membership_id' => '5a146888d721b079504aa0ff',
                    'plan_code' => '5a1430309b264e6a99186004',
                    'glofox_event' => Type::SUBSCRIPTION_PAYMENT,
                    'payment_method' => 'credit_card',
                ],
            ],
            [
                '_id' => new MongoId('5a3a5054948dae0706000009'),
                'created' => date('Y-m-d H:i:s', strtotime('2019-10-26 14:10:00')),
                'amount' => 10,
                'paid' => true,
                'currency' => 'eur',
                'captured' => true,
                'transaction_status' => Status::PAID,
                'transaction_provider_id' => 'trans_5a3a5054948dae0706000009',
                'event_id' => 'mocked-event-id',
                'metadata' => [
                    'user_id' => '5db1b2a0f15a8f08c04f28b2',
                    'user_name' => 'John Doe #6',
                    'branch_id' => '5db1b008f15a8f08c04f28af',
                    'namespace' => 'csv-transaction-report',
                    'membership_id' => '5a146888d721b079504aa0ff',
                    'plan_code' => '5a1430309b264e6a99186004',
                    'glofox_event' => Type::SUBSCRIPTION_PAYMENT,
                    'payment_method' => 'card',
                ],
                'payout' => [
                    'id' => 'payout-2',
                    'transaction_id' => 'transaction-3',
                    'gross_amount' => 10,
                    'fee' => 0,
                    'net_amount' => 10,
                    'date' => '2019-10-26',
                ],
            ],
            [
                '_id' => new MongoId('5a3a5054948dae0706000019'),
                'created' => date('Y-m-d H:i:s', strtotime('2019-10-26 14:11:00')),
                'amount' => 10,
                'paid' => true,
                'currency' => 'eur',
                'captured' => true,
                'transaction_status' => Status::REFUNDED,
                'transaction_provider_id' => 'trans_5a3a5054948dae0706000019',
                'event_id' => 'mocked-event-id',
                'metadata' => [
                    'user_id' => '5db1b2a0f15a8f08c04f28b2',
                    'user_name' => 'John Doe #7',
                    'branch_id' => '5db1b008f15a8f08c04f28af',
                    'namespace' => 'csv-transaction-report',
                    'membership_id' => '5a146888d721b079504aa0ff',
                    'plan_code' => '5a1430309b264e6a99186004',
                    'glofox_event' => Type::CUSTOM_CHARGE,
                    'payment_method' => 'card',
                    'user_tax_id' => 'user-tax-id-123'
                ],
                'payout' => [
                    'id' => 'payout-2',
                    'transaction_id' => 'transaction-3',
                    'gross_amount' => 10,
                    'fee' => 0,
                    'net_amount' => 10,
                    'date' => '2019-10-26',
                ],
            ],
            [
                '_id' => new MongoId('5a3a5054948dae0706000029'),
                'created' => date('Y-m-d H:i:s', strtotime('2019-10-26 14:11:00')),
                'amount' => 9,
                'paid' => true,
                'currency' => 'eur',
                'captured' => true,
                'transaction_status' => Status::PAID,
                'transaction_provider_id' => 'trans_5a3a5054948dae0706000019',
                'event_id' => 'mocked-event-id',
                'metadata' => [
                    'user_id' => '5db1b2a0f15a8f08c04f28b2',
                    'user_name' => 'Discounts 1',
                    'branch_id' => '5db1b008f15a8f08c04f28af',
                    'namespace' => 'csv-transaction-report',
                    'membership_id' => '5a146888d721b079504aa0ff',
                    'plan_code' => '5a1430309b264e6a99186004',
                    'glofox_event' => Type::CUSTOM_CHARGE,
                    'payment_method' => 'card',
                ],
                'payout' => [
                    'id' => 'payout-2',
                    'transaction_id' => 'transaction-3',
                    'gross_amount' => 9,
                    'fee' => 0,
                    'net_amount' => 9,
                    'date' => '2019-10-26',
                ],
            ],
            [
                '_id' => new MongoId('5a3a5054948dae0706000129'),
                'created' => date('Y-m-d H:i:s', strtotime('2019-10-26 14:11:00')),
                'amount' => 9,
                'paid' => true,
                'currency' => 'eur',
                'captured' => true,
                'transaction_status' => Status::PAID,
                'transaction_provider_id' => 'trans_5a3a5054948dae0706000019',
                'event_id' => 'mocked-event-id',
                'metadata' => [
                    'user_id' => '5db1b2a0f15a8f08c04f28b2',
                    'user_name' => 'Discount 2',
                    'branch_id' => '5db1b008f15a8f08c04f28af',
                    'namespace' => 'csv-transaction-report',
                    'membership_id' => '5a146888d721b079504aa0ff',
                    'plan_code' => '5a1430309b264e6a99186004',
                    'glofox_event' => Type::CUSTOM_CHARGE,
                    'payment_method' => 'card',
                ],
                'payout' => [
                    'id' => 'payout-2',
                    'transaction_id' => 'transaction-3',
                    'gross_amount' => 17.5,
                    'fee' => 0,
                    'net_amount' => 17.5,
                    'date' => '2019-10-26',
                ],
            ],
        ];
    }

    private function forMembershipPurchased(): array
    {
        return [
            [
                '_id' => new MongoId('5db1b036f15a8f08c04f28a3'),
                'object' => 'charge',
                'created' => date('Y-m-d H:i:s', strtotime('2019-10-24 14:07:23')),
                'amount' => 50,
                'paid' => true,
                'currency' => 'eur',
                'captured' => true,
                'transaction_status' => Status::PAID,
                'transaction_provider_id' => '5d682bf08d8540851fe6066a',
                'sold_by_user_id' => '59a7011a05c677bda512212a',
                'event_id' => 'mocked-event-id',
                'metadata' => [
                    'user_id' => '5db1affef15a8f08c04f28a3',
                    'user_name' => 'John Doe #1',
                    'branch_id' => '5db1b008f15a8f08c04f28a3',
                    'namespace' => 'csv-transaction-report',
                    'product_id' => '5b44ae18b13d95b5f633fd06',
                    'glofox_event' => Type::UPFRONT_PAYMENT,
                    'presentation_id' => 1_506_964_157_657,
                    'payment_method' => 'card',
                ],
            ],
        ];
    }

    private function forMembershipPurchasedWithAddOns(): array
    {
        return [
            [
                '_id' => new MongoId('5db1b036f15a8f08c04f28b3'),
                'object' => 'charge',
                'created' => date('Y-m-d H:i:s', strtotime('2019-10-24 14:07:23')),
                'amount' => 50,
                'paid' => true,
                'currency' => 'eur',
                'captured' => true,
                'transaction_status' => Status::PAID,
                'transaction_provider_id' => '5d682bf08d8540851fe6066a',
                'sold_by_user_id' => '59a7011a05c677bda512212a',
                'event_id' => 'mocked-event-id',
                'description' => 'Subscription with one add on',
                'metadata' => [
                    'user_id' => '5db1affef15a8f08c04f28a3',
                    'user_name' => 'John Doe #1',
                    'branch_id' => '49a7011a05c677b9a916612a',
                    'namespace' => 'glofox',
                    'product_id' => '5b44ae18b13d95b5f633fd06',
                    'glofox_event' => Type::SUBSCRIPTION_PAYMENT,
                    'presentation_id' => 1_506_964_157_657,
                    'payment_method' => 'credit_card',
                    'description' => 'Subscription with one add on',
                    'services' => [
                        [
                            'service_id' => 'mock-serviceId',
                            'service_definition_id' => 'mock-serviceDefinitionId',
                            'service_definition_plan_id' => 'mock-serviceDefinitionPlanId',
                            'amount' => 12.34
                        ],
                    ],
                ],
            ],
            [
                '_id' => new MongoId('5db1b036f15a8f08c04f28c3'),
                'object' => 'charge',
                'created' => date('Y-m-d H:i:s', strtotime('2019-10-24 14:07:23')),
                'amount' => 50,
                'paid' => true,
                'currency' => 'eur',
                'captured' => true,
                'transaction_status' => Status::PAID,
                'transaction_provider_id' => '5d682bf08d8540851fe6066a',
                'sold_by_user_id' => '59a7011a05c677bda512212a',
                'event_id' => 'mocked-event-id',
                'description' => 'Subscription with no add ons',
                'metadata' => [
                    'user_id' => '5db1affef15a8f08c04f28a3',
                    'user_name' => 'John Doe #1',
                    'branch_id' => '49a7011a05c677b9a916612a',
                    'namespace' => 'glofox',
                    'product_id' => '5b44ae18b13d95b5f633fd06',
                    'glofox_event' => Type::SUBSCRIPTION_PAYMENT,
                    'presentation_id' => 1_506_964_157_657,
                    'payment_method' => 'credit_card',
                ],
            ],
            [
                '_id' => new MongoId('5db1b036f15a8f08c04f28d3'),
                'object' => 'charge',
                'created' => date('Y-m-d H:i:s', strtotime('2019-10-24 14:07:23')),
                'amount' => 50,
                'paid' => true,
                'currency' => 'eur',
                'captured' => true,
                'transaction_status' => Status::PAID,
                'transaction_provider_id' => '5d682bf08d8540851fe6066a',
                'sold_by_user_id' => '59a7011a05c677bda512212a',
                'event_id' => 'mocked-event-id',
                'description' => 'Subscription with multiple add ons',
                'metadata' => [
                    'user_id' => '5db1affef15a8f08c04f28a3',
                    'user_name' => 'John Doe #1',
                    'branch_id' => '49a7011a05c677b9a916612a',
                    'namespace' => 'glofox',
                    'product_id' => '5b44ae18b13d95b5f633fd06',
                    'glofox_event' => Type::SUBSCRIPTION_PAYMENT,
                    'presentation_id' => 1_506_964_157_657,
                    'payment_method' => 'card',
                    'services' => [
                        [
                            'service_id' => 'mock-serviceId',
                            'service_definition_id' => 'mock-serviceDefinitionId',
                            'service_definition_plan_id' => 'mock-serviceDefinitionPlanId',
                            'amount' => 12.34
                        ],
                        [
                            'service_id' => 'mock-serviceId2',
                            'service_definition_id' => 'mock-serviceDefinitionId2',
                            'service_definition_plan_id' => 'mock-serviceDefinitionPlanId2',
                            'amount' => 23.45
                        ],
                    ],
                ],
            ],
        ];
    }

    private function forAddonPurchased(): array
    {
        return [
            [
                '_id' => new MongoId('5db1b036f15a8f08c04f28e3'),
                'object' => 'charge',
                'created' => date('Y-m-d H:i:s', strtotime('2019-10-24 14:07:23')),
                'amount' => 50,
                'paid' => true,
                'currency' => 'eur',
                'captured' => true,
                'transaction_status' => Status::PAID,
                'transaction_provider_id' => '5d682bf08d8540851fe6066a',
                'sold_by_user_id' => '59a7011a05c677bda512212a',
                'event_id' => 'mocked-event-id',
                'description' => 'Charge for services mock-serviceId',
                'metadata' => [
                    'user_id' => '5db1affef15a8f08c04f28a3',
                    'user_name' => 'John Doe #1',
                    'branch_id' => '49a7011a05c677b9a916612a',
                    'namespace' => 'glofox',
                    'glofox_event' => Type::ADD_ON_PREPAID,
                    'payment_method' => 'card',
                    'services' => [
                        [
                            'service_id' => 'mock-serviceId',
                            'service_definition_id' => 'mock-serviceDefinitionId',
                            'service_definition_plan_id' => 'mock-serviceDefinitionPlanId',
                            'amount' => 12.34
                        ]
                    ],
                ],
            ],
            [
                '_id' => new MongoId('5db1b036f15a8f08c04f28f3'),
                'object' => 'charge',
                'created' => date('Y-m-d H:i:s', strtotime('2019-10-24 14:07:23')),
                'amount' => 50,
                'paid' => true,
                'currency' => 'eur',
                'captured' => true,
                'transaction_status' => Status::PAID,
                'transaction_provider_id' => '5d682bf08d8540851fe6066a',
                'sold_by_user_id' => '59a7011a05c677bda512212a',
                'event_id' => 'mocked-event-id',
                'description' => 'Prorated Charge for Locker Addon Service (90%)',
                'metadata' => [
                    'user_id' => '5db1affef15a8f08c04f28a3',
                    'user_name' => 'John Doe #1',
                    'branch_id' => '49a7011a05c677b9a916612a',
                    'namespace' => 'glofox',
                    'glofox_event' => Type::ADD_ON_PREPAID,
                    'payment_method' => 'card',
                    'services' => [
                        [
                            'service_id' => 'mock-serviceId',
                            'service_definition_id' => 'mock-serviceDefinitionId',
                            'service_definition_plan_id' => 'mock-serviceDefinitionPlanId',
                            'amount' => 12.34
                        ]
                    ],
                ],
            ],
            [
                '_id' => new MongoId('5db1b036f15a8f08c04f28f0'),
                'object' => 'charge',
                'created' => date('Y-m-d H:i:s', strtotime('2019-10-24 14:07:23')),
                'amount' => 50,
                'paid' => true,
                'currency' => 'eur',
                'captured' => true,
                'transaction_status' => Status::PAID,
                'transaction_provider_id' => '5d682bf08d8540851fe6066a',
                'sold_by_user_id' => '59a7011a05c677bda512212a',
                'event_id' => 'mocked-event-id',
                'description' => 'Prorated Charge for Locker (50%) Addon Service (90%)',
                'metadata' => [
                    'user_id' => '5db1affef15a8f08c04f28a3',
                    'user_name' => 'John Doe #1',
                    'branch_id' => '49a7011a05c677b9a916612a',
                    'namespace' => 'glofox',
                    'glofox_event' => Type::ADD_ON_PREPAID,
                    'payment_method' => 'card',
                    'services' => [
                        [
                            'service_id' => 'mock-serviceId',
                            'service_definition_id' => 'mock-serviceDefinitionId',
                            'service_definition_plan_id' => 'mock-serviceDefinitionPlanId',
                            'amount' => 12.34
                        ]
                    ],
                ],
            ],
        ];
    }

    private function refund()
    {
        return [
            [
                '_id' => new MongoId('5a3a5054948dae0706000004'),
                'object' => 'charge',
                'created' => date('Y-m-d H:m:i'),
                'livemode' => false,
                'amount' => 10,
                'currency' => 'eur',
                'captured' => true,
                'description' => 'Test',
                'transaction_provider_id' => 'trans_5a3a5054948dae0706000004',
                'statement_description' => 'Test',
                'metadata' => [
                    'user_id' => '59a7011a05c677bda916612a',
                    'branch_id' => '49a7011a05c677b9a916612a',
                    'event_id' => '49b7012a05c677c9a512503c',
                    'namespace' => 'glofox',
                ],
            ],
            [
                '_id' => new MongoId('5a3a5054948dae0706000003'),
                'object' => 'charge',
                'created' => date('Y-m-d H:m:i'),
                'livemode' => false,
                'amount' => 10,
                'currency' => 'eur',
                'captured' => true,
                'description' => 'Test',
                'transaction_provider_id' => 'trans_5a3a5054948dae0706000003',
                'statement_description' => 'Test',
                'metadata' => [
                    'user_id' => '59a7011a05c677bda916612a',
                    'branch_id' => '49a7011a05c677b9a916612a',
                    'event_id' => '49b7012a05c677c9a512503c',
                    'namespace' => 'glofox',
                ],
            ],
            [
                '_id' => new MongoId('5a3a5054948dae0706000012'),
                'created' => date('Y-m-d H:m:i'),
                'livemode' => false,
                'amount' => 10,
                'currency' => 'eur',
                'captured' => true,
                'description' => 'Test',
                'transaction_provider_id' => 'trans_5a3a5054948dae0706000012',
                'statement_description' => 'Test',
                'invoice_id' => 'INVOICE-MOCK',
                'metadata' => [
                    'user_id' => '5adde862426413b9e9b4be9e',
                    'branch_id' => '5addc25383266f65abf515c4',
                    'event_id' => '5b043d0d45702d48c57deda7',
                    'booking_id' => '5b043d752a1a3a1657e6db5e',
                    'namespace' => 'classpass_integrated',
                    'payment_method' => 'CASH',
                    'glofox_event' => 'book_class',
                ],
            ],
        ];
    }

    private function flexiblePayment(): array
    {
        return [
            [
                '_id' => new MongoId('5a3a5054948dae0716000004'),
                'object' => 'charge',
                'created' => date('Y-m-d H:m:i'),
                'livemode' => false,
                'amount' => 10,
                'currency' => 'eur',
                'captured' => true,
                'description' => 'Test',
                'transaction_provider_id' => 'ch_flex_1',
                'statement_description' => 'Test',
                'metadata' => [
                    'user_id' => '59a7011a05c677bda916612a',
                    'branch_id' => '49a7011a05c677b9a916612a',
                    'event_id' => '49b7012a05c677c9a512503c',
                    'namespace' => 'glofox',
                    'payment_method' => 'flexible',
                ],
            ],
        ];
    }

    private function forBookingActivityContext(): array
    {
        return [
            [
                '_id' => new MongoId('6461e347088ef26a07a3c4c8'),
                'object' => 'charge',
                'created' => date('Y-m-d H:m:i'),
                'livemode' => false,
                'amount' => 10,
                'currency' => 'eur',
                'captured' => true,
                'description' => 'Test',
                'transaction_provider_id' => 'ch_flex_1',
                'statement_description' => 'Test',
                'refunded' => false,
                'paid' => true,
                'transaction_status' => 'PAYED',
                'invoice_id' => '6461e73a76b7ca0225864ff9',
                'metadata' => [
                    'user_id' => 'a9a5521a05c687bda917785c',
                    'branch_id' => '49a7011a05c677b9a916612a',
                    'model_id' => '6461e3f11639388b04f5965b',
                    'namespace' => 'glofox',
                    'payment_method' => 'flexible',
                    'wallet_balance_after' => 0.0,
                ],
            ],
            [
                '_id' => new MongoId('64624dbdc581ed3bee299f1b'),
                'object' => 'charge',
                'created' => date('Y-m-d H:m:i'),
                'livemode' => false,
                'amount' => 10,
                'currency' => 'eur',
                'captured' => true,
                'description' => 'Test',
                'transaction_provider_id' => 'ch_flex_1',
                'statement_description' => 'Test',
                'refunded' => false,
                'paid' => true,
                'transaction_status' => 'PAYED',
                'invoice_id' => '64624dea730dfd4c2c1b0f8d',
                'metadata' => [
                    'user_id' => 'a9a5521a05c687bda917785c',
                    'branch_id' => '49a7011a05c677b9a916612a',
                    'event_id' => '64624dc9a858dcae189920a9',
                    'namespace' => 'glofox',
                    'payment_method' => 'flexible',
                    'wallet_balance_after' => 0.0,
                ],
            ],
        ];
    }
}
