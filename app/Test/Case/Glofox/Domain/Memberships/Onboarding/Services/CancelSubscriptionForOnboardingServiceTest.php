<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\Memberships\Onboarding\Services;

use Glofox\Domain\Memberships\Onboarding\Services\CancelSubscriptionForOnboardingService;
use Glofox\Domain\Users\Models\User;
use Glofox\Domain\Users\Repositories\UsersRepository;
use Glofox\Exception;
use Glofox\Payments\Providers\Gateway\Handler as GatewayHandler;
use Glofox\Payments\Providers\Gateway\Subscriptions\Subscriptions;

\App::import('Test/Case', 'GlofoxTestCase');

class CancelSubscriptionForOnboardingServiceTest extends \GlofoxTestCase
{
    public $fixtures = [];
    private UsersRepository $usersRepository;

    public function setUp()
    {
        parent::setUp();

        $this->usersRepository = \Mockery::mock(UsersRepository::class);
    }

    public function tearDown()
    {
        parent::tearDown();

        \Mockery::close();

        $this->removeDependencies();
    }

    public function test_when_successfully_executed_and_subscription_id_is_not_numeric_it_attempted_to_update_member_as_payg()
    {
        $paymentHandler = \Mockery::mock(GatewayHandler::class);
        $member = User::make([
            'membership' => [
                'subscription' => [
                    'stripe_id' => 'testing_sub',
                ],
            ],
        ]);

        $this->usersRepository
            ->shouldReceive('legacySaveOrFail')
            ->withArgs(fn(array $params) => 'payg' === $params['membership']['type'])
            ->once()
            ->getMock();

        $this->injectDependencies();

        /** @var CancelSubscriptionForOnboardingService $service */
        $service = app()->make(CancelSubscriptionForOnboardingService::class);
        $service->execute($paymentHandler, $member);

        $this->removeDependencies();
    }

    public function test_when_successfully_executed_and_subscription_id_is_numeric_it_attempted_to_update_member_as_payg()
    {
        $subscriptions = \Mockery::mock(Subscriptions::class);
        $subscriptions
            ->shouldReceive('unsubscribe')
            ->andReturnTrue()
            ->getMock();

        $paymentHandler = \Mockery::mock(GatewayHandler::class);
        $paymentHandler
            ->shouldReceive('subscriptions')
            ->andReturn($subscriptions)
            ->getMock();

        $member = User::make([
            'membership' => [
                'subscription' => [
                    'payment_gateway_id' => 123123,
                ],
            ],
        ]);

        $this->usersRepository
            ->shouldReceive('legacySaveOrFail')
            ->withArgs(fn(array $params) => 'payg' === $params['membership']['type'])
            ->once()
            ->getMock();

        $this->injectDependencies();

        /** @var CancelSubscriptionForOnboardingService $service */
        $service = app()->make(CancelSubscriptionForOnboardingService::class);
        $service->execute($paymentHandler, $member);

        $this->removeDependencies();
    }

    public function test_when_cannot_cancel_subscription_it_throws_an_exception()
    {
        $subscriptions = \Mockery::mock(Subscriptions::class);
        $subscriptions
            ->shouldReceive('unsubscribe')
            ->andReturnFalse()
            ->getMock();

        $paymentHandler = \Mockery::mock(GatewayHandler::class);
        $paymentHandler
            ->shouldReceive('subscriptions')
            ->andReturn($subscriptions)
            ->getMock();

        $member = User::make([
            '_id' => new \MongoId('49a7011a05c677b9a916612e'),
            'membership' => [
                'subscription' => [
                    'payment_gateway_id' => 123123,
                ],
            ],
        ]);

        $this->usersRepository
            ->shouldNotReceive('legacySaveOrFail')
            ->getMock();

        $this->injectDependencies();

        $exceptionMessage = sprintf(
            'Failed to cancel subscription User[%s] - sub[%s]',
            $member->id(),
            $member->membership()->subscription()->id()
        );

        $this->setExpectedException(Exception::class, $exceptionMessage);

        /** @var CancelSubscriptionForOnboardingService $service */
        $service = app()->make(CancelSubscriptionForOnboardingService::class);
        $service->execute($paymentHandler, $member);

        $this->removeDependencies();
    }

    private function injectDependencies()
    {
        app()->instance(UsersRepository::class, $this->usersRepository);
    }

    private function removeDependencies()
    {
        app()->forgetInstance(UsersRepository::class);
    }
}
