OPENSHIFT_APP_NAME=local
REGION=local
SERVER=local
SERVICE_NAME=local

PHP_IDE_CONFIG=serverName=xdebug-docker-php7
PHP_XDEBUG_ENABLE=1
PHP_XDEBUG_HOST=host.docker.internal
XDEBUG_SESSION=docker
PHP_ENABLE_OPCACHE=0

NEW_RELIC_ENABLED=0
NEW_RELIC_APP_NAME=local

GLOFOX_DEBUG_LEVEL=0

GLOFOX_STRIPE_CUSTOM_EU_PUBLISHABLE_KEY=pk_test_1loGihTKRmyb83crV9eKxhsv
GLOFOX_STRIPE_CUSTOM_US_PUBLISHABLE_KEY=pk_test_M4tZMQtambxuT70BtHzkyXAJ
GLOFOX_STRIPE_CUSTOM_US_LIFT_BRANDS_PUBLISHABLE_KEY=pk_test_HHW7GkmEQGvAZDjIquEp7YAS00Kc2okXi8

GLOFOX_GRPC_ACCOUNT_CLIENT_HOST=payments-dev.aws.glofox.com:44441
GLOFOX_GRPC_TRANSACTION_CLIENT_HOST=payments-dev.aws.glofox.com:44442
GLOFOX_GRPC_SUBSCRIPTION_CLIENT_HOST=payments-dev.aws.glofox.com:44443

GLOFOX_AWS_S3_ACCESS_KEY=AKIAIOSFODNN7EXAMPLE
GLOFOX_AWS_S3_SECRET_KEY=wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY
GLOFOX_AWS_S3_BUCKET=glofox
GLOFOX_AWS_S3_HOST=s3.amazonaws.com

GLOFOX_ANDROID_GCM_KEY=
GLOFOX_IOS_APNS_KEY=athlone09

GLOFOX_SENTRY_URL=
GLOFOX_INTEGRATIONS_CLASSPASS=eyJhbGciOiJIUzI1NiJ9.***********************************************************************************************************************************************************************************************************************************************************************************************.6mxZTQ5Fqph88EKUfRtajEQ3Jls6fJv_2LkRGaKZ5CA
GLOFOX_ROOT_DOMAIN=https://development.glofox.com

GLOFOX_MEMBERSHIPS_API_URL=http://membership:9020
GLOFOX_WALLETS_API_URL=http://membership:9020
GLOFOX_EXPERIMENTS_API_URL=http://localhost:9021
GLOFOX_EXPERIMENTS_API_KEY=123

GLOFOX_REDIS_URL=redis://cache

GLOFOX_DATABASE_NAME=development
GLOFOX_DATABASE_HOST=db
GLOFOX_DATABASE_PORT=27017

GLOFOX_ASYNC_REGION=eu-west-1
GLOFOX_ASYNC_ACCOUNT_ID=************
GLOFOX_SNS_REGION=eu-west-1
GLOFOX_SNS_NAME=api-topic

LIVE_STREAM_SERVICE_URL="http://live-class-link/v3"
LIVE_STREAM_SERVICE_SALT="local-salt"

LIVE_STREAM_INSTALLER_URL="http://api.internal-gateway.local.service.private/live-stream-installer"
ELECTRONIC_AGREEMENTS_SERVICE_URL=http://api.internal-gateway.dev.service.private

REPORTS_SERVICE_URL="http://reports.local.service.private:8080"

GLOFOX_MIXPANEL_TOKEN="f9fbb12ea575fcd4c8ec8b832f935f6f"

INVOICE_SERVICE_URL="http://invoice.local.service.private:80/sales-tax"
SALES_TAX_SERVICE_URL="http://sales-tax.local.service.private:80/sales-tax"
ADDON_SERVICE_URL= "http://services.local.service.private:8083/services-api"
COMMUNICATIONS_INTERNAL_SERVICE_URL= "http://localhost:9905/communications-internal"
ADDON_SERVICE_API_TOKEN="check SSM on /glofox/api/dev/ADDON_SERVICE_API_TOKEN"

RECEIPTS_SERVICE_URL= "http://receipts.local.service.private:80/receipts-api"
RECEIPTS_HTTP_API_KEY=fake-api

JWT_SALT_GENERATE=
JWT_SALT_VERIFY=

SENDGRID_USERNAME=apikey
SENDGRID_PASSWORD="ask SRE"

PASSWORD_HASH_COST=10

EAGREEMENT_MUST_SIGN_DATE="2021-05-20 00:00:01"

URL_SIGNATURE_KEY=not-so-random-key

PASSWORD_RESET_TOKEN_VALIDITY=60
PASSWORD_RESET_TOKEN_SIZE=24

LAUNCH_DARKLY_API_URL="https://app.launchdarkly.com/api/"
LAUNCH_DARKLY_API_TOKEN="get from SSM"
LAUNCH_DARKLY_SDK_KEY="get from SSM"

AUTHENTICATION_SERVICE_URL="http://localhost:3000/"

GLOFOX_MAILCHIMP_WEBHOOK_SECRET_KEY="get from SSM"
GLOFOX_TWILIO_WEBHOOK_SECRET_KEY="get from SSM"

ENVIRONMENT_SHORT_NAME="local"

CART_SERVICE_URL=local

GOOGLE_RECAPTCHA_BASE_URL="http://localhost:3001/"
GOOGLE_RECAPTCHA_SECRET="very-secrety-secret"
DATA_SANITIZER_MONGO_INJECTION_ENABLED="true"
FIND_BRANCHES_LOGIN_FLOW_ENABLED="true"
COMMUNICATIONS_HTTP_API_KEY=fake-token
