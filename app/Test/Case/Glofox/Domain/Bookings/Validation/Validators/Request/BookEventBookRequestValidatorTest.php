<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\Bookings\Validation\Validators\Request;

use App;
use CakeTestCases\Glofox\Domain\Users\Traits\AuthenticateUsersTrait;
use Glofox\Domain\Authentication\Auth;
use Glofox\Domain\Bookings\Requests\BookEventAsMemberRequest;
use Glofox\Domain\Bookings\Requests\BookEventAsStaffRequest;
use Glofox\Domain\Bookings\Validation\Validators\Request\BookEventBookRequestValidator;
use GlofoxTestCase;

App::import('Test/Case', 'GlofoxTestCase');

class BookEventBookRequestValidatorTest extends GlofoxTestCase
{
    use AuthenticateUsersTrait;

    public $fixtures = [
        'app.user',
        'app.branch'
    ];
    private BookEventBookRequestValidator $validator;

    public function setUp(): void
    {
        parent::setUp();

        $this->validator = new BookEventBookRequestValidator();
    }

    public function testValidateRequestAsAMemberSuccessfully(): void
    {
        $this->authenticateAsMember();

        $request = BookEventAsMemberRequest::create('', 'POST', [
            'model' => 'event',
            'model_id' => '6759cec951232790f0382e85',
            'guest_bookings' => 2,
            'join_waiting_list' => false,
        ]);

        $this->validator->validate($request, Auth::user());
    }

    public function testValidateRequestAsAStaffSuccessfully(): void
    {
        $this->authenticateAsAdmin();

        $request = BookEventAsStaffRequest::create('', 'POST', [
            'model' => 'event',
            'model_id' => '6759cec951232790f0382e85',
            'user_id' => '6759cec951232790f0382e85',
            'payment_method' => 'complimentary',
            'price' => 23.99,
            'guest_bookings' => 2,
            'join_waiting_list' => false,
            'charge' => false,
        ]);

        $this->validator->validate($request, Auth::user());
    }
}
