kind: service
version: effx/v1
spec:
  name: 'api-worker-api'
  annotations:
    effx.io/owned-by: 'bookings-squad'
  tags:
    deployment: 'ecs-fargate'
    protocol: 'https'
    run-type: 'worker'
    has-public-api: 'false'
    has-private-api: 'false'
  description: 'Provides asynchronous capabilities using the same codebase as the core-api. Commonly used to process e-mail delivery, notifications and event processing (like bookings, for instance)'
  contact:
    issueTracker:
      url: 'https://glofox.atlassian.net/jira/software/c/projects/CI/issues/'
      label: 'CI board'
  linkGroups:
    - label: 'Dashboards'
      links:
        - label: 'APM'
          url: 'https://onenr.io/0NgR7VV1awo'
        - label: 'Logs'
          url: 'https://eu-west-1.console.aws.amazon.com/cloudwatch/home?region=eu-west-1#logsV2:log-groups/log-group/apiworker-service-plat'
    - label: 'VCS'
      links:
        - label: 'Github'
          url: 'https://github.com/glofoxinc/api'
  dependencies:
    manual:
      - name: 'core-db'
      - name: 'payments-api'
      - name: 'payments-account'
      - name: 'payments-transaction'
      - name: 'payments-subscription'
      - name: 'payments-web'
      - name: 'pusher'
      - name: 'memberships'
      - name: 'wallets'
      - name: 'iris'
      - name: 'live-stream-installer'
      - name: 'live-stream'
      - name: 'e-agreements'
      - name: 'reports'
      - name: 'sales-tax'
      - name: 'services'
      - name: 'redis'

      # third-parties
      - name: 'sendgrid'
      - name: 'launch-darkly'
      - name: 'stripe'
      - name: 'mixpanel'
      - name: 'mailchimp'
