<?php

namespace CakeTestCases\Glofox\Payments\Providers\Gateway\Invoices;

use Glofox\Domain\PaymentMethods\Models\PaymentMethod;
use Glofox\Domain\PaymentProviders\Models\PaymentProvider;
use Glofox\Payments\Entities\Invoice\Exceptions\InvoiceCreationException;
use Glofox\Payments\Entities\Invoice\Exceptions\InvoicePaymentException;
use Glofox\Payments\Entities\Invoice\Exceptions\InvoiceRetrievalException;
use Glofox\Payments\Entities\Invoice\Internal\CreateInvoice;
use Glofox\Payments\Entities\Invoice\Models\InvoiceEntity as InvoiceEntityModel;
use Glofox\Payments\Entities\Invoice\Models\InvoiceEntityAutoCollectDetails;
use Glofox\Payments\Entities\Invoice\Models\InvoiceEntityLineItem;
use Glofox\Payments\Entities\Invoice\Models\InvoiceEntityLineItems;
use Glofox\Payments\Providers\Gateway\GRPC\Connection;
use Glofox\Payments\Providers\Gateway\Invoices\Invoices;
use Glofox\Payments\Providers\Gateway\ProtoParsers\InvoiceEntityParser;
use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use GRPC\Payments\APIService\CreateInvoiceEntityAutoCollectDetails;
use GRPC\Payments\APIService\CreateInvoiceEntityReqLineItem;
use GRPC\Payments\APIService\CreateInvoiceEntityRequest;
use GRPC\Payments\APIService\ForgiveInvoiceEntityRequest;
use GRPC\Payments\APIService\GetInvoiceEntityRequest;
use GRPC\Payments\APIService\InvoiceEntity;
use GRPC\Payments\APIService\InvoiceServiceClient;
use GRPC\Payments\APIService\PayInvoiceEntityRequest;
use Grpc\UnaryCall;

\App::import('Test/Case', 'GlofoxTestCase');

class InvoicesTest extends \GlofoxTestCase
{
    /**
     * @throws InvoiceRetrievalException
     */
    public function testItGetInvoiceByInvoiceId(): void
    {
        $invoiceIdCons = 'invoice_id_1';
        $request = new GetInvoiceEntityRequest();
        $request->setID($invoiceIdCons);

        $invoiceEntity = new InvoiceEntity();
        $invoiceEntity
            ->setId($invoiceIdCons)
            ->setDestinationAccountID(1)
            ->setCustomerAccountID(1)
            ->setAmount(10)
            ->setCurrency('eur')
            ->setSource('DASHBOARD');

        $invoiceParser = \Mockery::mock(InvoiceEntityParser::class);
        $invoiceParser->shouldReceive('parseFromProtoMessage')
            ->withArgs(function (InvoiceEntity $invoiceEntity) {
                $this->assertSame('invoice_id_1', $invoiceEntity->getID());

                return true;
            })
            ->andReturn((new InvoiceEntityModel())
            ->setId('invoice_id_1'))
            ->getMock();

        app()->instance(InvoiceEntityParser::class, $invoiceParser);

        $connection = \Mockery::mock(Connection::class);
        $connection->invoiceService = \Mockery::mock(InvoiceServiceClient::class);
        $connection->invoiceService->shouldReceive('GetInvoice')
            ->withArgs(function (GetInvoiceEntityRequest $request) {
                $this->assertSame('invoice_id_1', $request->getID());

                return true;
            })
            ->andReturn(
                \Mockery::mock(UnaryCall::class)
                ->shouldReceive('wait')
                ->andReturn([$invoiceEntity, (object) ['code' => 0]])
                ->getMock()
            );

        app()->instance(Connection::class, $connection);

        $invoices = new Invoices(new PaymentMethod(), new PaymentProvider());

        $result = $invoices->get($invoiceIdCons);
        self::assertEquals($invoiceIdCons, $result->getId());

        app()->forgetInstance(InvoiceEntityParser::class);
        app()->forgetInstance(Connection::class);
    }

    /**
     * @throws InvoiceCreationException
     */
    public function testItCreateInvoice(): void
    {
        $invoiceEntity = new InvoiceEntity();
        $invoiceEntity->setID('invoice-1');

        $createInvRequestLineItem = new CreateInvoiceEntityReqLineItem();
        $createInvRequestLineItem->setName('name');
        $createInvRequestLineItem->setAmount(10);
        $createInvRequestLineItem->setQuantity(1);
        $createInvRequestLineItem->setServiceType('service-type');
        $createInvRequestLineItem->setServiceSubType('service-sub-type');
        $createInvRequestLineItem->setDiscountIDs(['discount-1', 'discount-1']);
        $createInvRequestLineItem->setExternalRef('external-ref');
        $createInvRequestLineItem->setAttributes(['key' => 'value']);

        $createInvRequestLineItems = new RepeatedField(GPBType::MESSAGE, CreateInvoiceEntityReqLineItem::class);
        $createInvRequestLineItems->offsetSet(null, $createInvRequestLineItem);

        $invoiceAutoCollectDetails = new CreateInvoiceEntityAutoCollectDetails();
        $invoiceAutoCollectDetails->setPaymentMethod('CARD');

        $createInvRequest = new CreateInvoiceEntityRequest();
        $createInvRequest
            ->setCustomerAccountID(2)
            ->setDestinationAccountID(1)
            ->setLineItems($createInvRequestLineItems)
            ->setAutoCollectDetails($invoiceAutoCollectDetails);

        $connection = \Mockery::mock(Connection::class);
        $connection->invoiceService = \Mockery::mock(InvoiceServiceClient::class);
        $connection->invoiceService->shouldReceive('CreateInvoice')
            ->withArgs(function (CreateInvoiceEntityRequest $params) {
                $this->assertSame('invoice-1', $params->getID());
                $this->assertSame(1, $params->getDestinationAccountID());
                $this->assertSame(2, $params->getCustomerAccountID());
                $this->assertSame('studio-1', $params->getStudioID());
                $this->assertSame('user-1', $params->getUserID());
                $this->assertSame('sold-by-1', $params->getSoldBy());
                $this->assertSame('DASHBOARD', $params->getSource());

                $autoCollectDetails = $params->getAutoCollectDetails();
                $this->assertSame('CARD', $autoCollectDetails->getPaymentMethod());
                $this->assertFalse($autoCollectDetails->getAllowOverdraft());

                $lineItems = $params->getLineItems()->getIterator();
                /**
                 * @var CreateInvoiceEntityReqLineItem $lineItem */
                foreach ($lineItems as $lineItem) {
                    $this->assertSame('name', $lineItem->getName());
                    $this->assertSame(10, $lineItem->getAmount());
                    $this->assertSame(1, $lineItem->getQuantity());
                    $this->assertSame('service-type', $lineItem->getServiceType());
                    $this->assertSame('service-sub-type', $lineItem->getServiceSubType());
                    $this->assertSame('discount-1', $lineItem->getDiscountIDs()->offsetGet(0));
                    $this->assertSame('discount-2', $lineItem->getDiscountIDs()->offsetGet(1));
                    $this->assertSame('external-ref', $lineItem->getExternalRef());
                }

                return true;
            })
            ->andReturn(
                \Mockery::mock(UnaryCall::class)
                    ->shouldReceive('wait')
                    ->andReturn([$invoiceEntity, (object) ['code' => 0]])
                    ->getMock()
            );

        app()->instance(Connection::class, $connection);

        $invoiceParser = \Mockery::mock(InvoiceEntityParser::class);
        $invoiceParser->shouldReceive('parseFromProtoMessage')
            ->withArgs(function (InvoiceEntity $invoiceEntity) {
                $this->assertSame('invoice-1', $invoiceEntity->getID());

                return true;
            })
            ->andReturn((new InvoiceEntityModel())
                ->setId('invoice-1'))
            ->getMock();

        app()->instance(InvoiceEntityParser::class, $invoiceParser);

        $invoices = new Invoices(new PaymentMethod(), new PaymentProvider());

        $autoCollectDetails = new InvoiceEntityAutoCollectDetails();
        $autoCollectDetails->setPaymentMethod('CARD');

        $invoiceLineItem = new InvoiceEntityLineItem();
        $invoiceLineItem->setName('name');
        $invoiceLineItem->setAmount(10);
        $invoiceLineItem->setQuantity(1);
        $invoiceLineItem->setServiceType('service-type');
        $invoiceLineItem->setServiceSubType('service-sub-type');
        $invoiceLineItem->setDiscountIDs(['discount-1', 'discount-2']);
        $invoiceLineItem->setExternalRef('external-ref');
        $invoiceLineItem->setAttributes(['key' => 'value']);
        $invoiceLineItems = new InvoiceEntityLineItems([$invoiceLineItem]);

        $createInvParams = (new CreateInvoice())
            ->setId('invoice-1')
            ->setDestinationAccountId('1')
            ->setCustomerAccountId('2')
            ->setStudioId('studio-1')
            ->setUserId('user-1')
            ->setSoldBy('sold-by-1')
            ->setAutoCollectDetails($autoCollectDetails)
            ->setLineItems($invoiceLineItems)
            ->setSource('DASHBOARD');

        $result = $invoices->create($createInvParams);
        self::assertEquals('invoice-1', $result->getId());

        app()->forgetInstance(InvoiceEntityParser::class);
        app()->forgetInstance(Connection::class);
    }

    /**
     * @throws InvoicePaymentException
     */
    public function testItPayInvoice(): void
    {
        $invoiceId = 'invoice_id_3';
        $invoiceEntity = new InvoiceEntity();
        $invoiceEntity->setID($invoiceId);

        $payInvoice = new PayInvoiceEntityRequest();
        $payInvoice
            ->setPaymentMethod('CARD')
            ->setInvoiceID($invoiceId)
            ->setUseAvailableAccountBalance(true);

        $connection = \Mockery::mock(Connection::class);
        $connection->invoiceService = \Mockery::mock(InvoiceServiceClient::class);
        $connection->invoiceService->shouldReceive('PayInvoice')
            ->withArgs(function (PayInvoiceEntityRequest $payInvoice) {
                $this->assertSame('invoice_id_3', $payInvoice->getInvoiceID());
                $this->assertSame('CARD', $payInvoice->getPaymentMethod());
                $this->assertFalse($payInvoice->getAllowOverdraft());
                $this->assertFalse($payInvoice->getOffSession());
                $this->assertFalse($payInvoice->getRecurring());
                $this->assertTrue($payInvoice->getUseAvailableAccountBalance());

                return true;
            })
            ->andReturn(
                \Mockery::mock(UnaryCall::class)
                    ->shouldReceive('wait')
                    ->andReturn([$invoiceEntity, (object) ['code' => 0]])
                    ->getMock()
            );

        app()->instance(Connection::class, $connection);

        $invoiceParser = \Mockery::mock(InvoiceEntityParser::class);
        $invoiceParser->shouldReceive('parseFromProtoMessage')
            ->withArgs(function (InvoiceEntity $invoiceEntity) {
                $this->assertSame('invoice_id_3', $invoiceEntity->getID());

                return true;
            })
            ->andReturn((new InvoiceEntityModel())
                ->setId($invoiceId))
            ->getMock();

        app()->instance(InvoiceEntityParser::class, $invoiceParser);

        $invoices = new Invoices(new PaymentMethod(), new PaymentProvider());

        $autoCollectDetails = new InvoiceEntityAutoCollectDetails();
        $autoCollectDetails->setPaymentMethod('CARD');
        $autoCollectDetails->setUseAvailableAccountBalance(true);

        $result = $invoices->pay($invoiceId, $autoCollectDetails);
        self::assertEquals('invoice_id_3', $result->getId());

        app()->forgetInstance(InvoiceEntityParser::class);
        app()->forgetInstance(Connection::class);
    }

    /**
     * @throws InvoicePaymentException
     */
    public function testItForgiveInvoice(): void
    {
        $invoiceId = 'invoice_id_4';
        $invoiceEntity = new InvoiceEntity();
        $invoiceEntity->setID($invoiceId);

        $forgiveInvoice = new ForgiveInvoiceEntityRequest();
        $forgiveInvoice->setInvoiceID($invoiceId);

        $connection = \Mockery::mock(Connection::class);
        $connection->invoiceService = \Mockery::mock(InvoiceServiceClient::class);
        $connection->invoiceService->shouldReceive('ForgiveInvoice')
            ->withArgs(function (ForgiveInvoiceEntityRequest $payInvoice) {
                $this->assertSame('invoice_id_4', $payInvoice->getInvoiceID());

                return true;
            })
            ->andReturn(
                \Mockery::mock(UnaryCall::class)
                    ->shouldReceive('wait')
                    ->andReturn([$invoiceEntity, (object) ['code' => 0]])
                    ->getMock()
            );

        app()->instance(Connection::class, $connection);

        $invoiceParser = \Mockery::mock(InvoiceEntityParser::class);
        $invoiceParser->shouldReceive('parseFromProtoMessage')
            ->withArgs(function (InvoiceEntity $invoiceEntity) {
                $this->assertSame('invoice_id_4', $invoiceEntity->getID());

                return true;
            })
            ->andReturn((new InvoiceEntityModel())
                ->setId($invoiceId))
            ->getMock();

        app()->instance(InvoiceEntityParser::class, $invoiceParser);

        $invoices = new Invoices(new PaymentMethod(), new PaymentProvider());

        $result = $invoices->forgive($invoiceId);
        self::assertEquals('invoice_id_4', $result->getId());

        app()->forgetInstance(InvoiceEntityParser::class);
        app()->forgetInstance(Connection::class);
    }
}
