<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\ElectronicAgreements\UseCase;

use App;
use Glofox\Domain\ElectronicAgreements\Exception\SendAgreementException;
use Glofox\Domain\ElectronicAgreements\UseCase\SendAgreementByTrigger;
use Glofox\Domain\ElectronicAgreements\UseCase\SendAgreementByTriggerParams;
use Glofox\Domain\Users\Exceptions\UserOriginBranchIdException;
use Glofox\Domain\Users\Models\User;
use Glofox\Domain\Users\Repositories\UsersRepository;
use Mockery;
use GlofoxTestCase;

App::import('Test/Case', 'GlofoxTestCase');

class SendAgreementByTriggerTest extends GlofoxTestCase
{
    public $fixtures = [];

    private const USER_ID = 'test-user-id';
    private const TRIGGER = 'member.authenticated';
    private const BRANCH_ID = 'test-branch-id';

    public function tearDown(): void
    {
        parent::tearDown();
        Mockery::close();
    }

    /**
     * @throws UserOriginBranchIdException
     */
    public function testSendAgreement(): void
    {
        $userRepositoryMock = $this->createUserRepositoryMock();

        $useCase = new SendAgreementByTrigger(
            $userRepositoryMock
        );

        $params = new SendAgreementByTriggerParams(
            self::USER_ID,
            self::TRIGGER
        );

        $useCase->execute($params);
    }

    private function createUserRepositoryMock(): UsersRepository
    {
        $userRepoMock = Mockery::mock(UsersRepository::class);
        $userRepoMock->shouldReceive('getById')
            ->withArgs(function (string $id) {
                $userId = self::USER_ID;
                $this->assertEquals($id, $userId);
                return true;
            })
            ->once()->andReturn($this->createUserMock());
        return $userRepoMock;
    }

    private function createUserMock(): User
    {
        $userMock = Mockery::mock(User::class);
        $userMock->shouldReceive('originBranchId')->once()->andReturn(self::BRANCH_ID);
        return $userMock;
    }
}
