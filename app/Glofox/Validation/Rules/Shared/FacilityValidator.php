<?php

namespace Glofox\Validation\Rules\Shared;

use Glofox\Domain\Bookings\Search\Expressions\BranchId;
use Glofox\Domain\Facilities\Models\Facility;
use Glofox\Domain\Facilities\Repositories\FacilitiesRepository;
use Glofox\Domain\Facilities\Search\Expressions\IsOnline;
use Glofox\Domain\Users\Search\Expressions\Active;
use Illuminate\Validation\Validator;
use Psr\Log\LoggerInterface;

final class FacilityValidator
{
    private \Glofox\Domain\Facilities\Repositories\FacilitiesRepository $facilitiesRepository;
    private \Psr\Log\LoggerInterface $logger;

    public function __construct(
        FacilitiesRepository $facilitiesRepository,
        LoggerInterface $logger
    ) {
        $this->facilitiesRepository = $facilitiesRepository;
        $this->logger = $logger;
    }

    public function validateOnlineFlag(string $attribute, bool $value, array $parameters, Validator $validator): bool
    {
        $data = $validator->getData();

        $this->logger->info('Validating if the facility can be created or updated', $data);

        $hasId = \array_key_exists('_id', $data);

        if ($hasId && $value) {
            $this->logger->warning('Failed because trying to update an existent facility to online');

            return false;
        }

        $bookable = \array_key_exists('bookable', $data) ? $data['bookable'] : false;
        if ($bookable && $value) {
            $this->logger->warning('Failed because online facility cannot be bookable');

            return false;
        }

        $branchOnlineFacilities = $this->facilitiesRepository
            ->addCriteria(new BranchId($data['branch_id']))
            ->addCriteria(new Active(true))
            ->addCriteria(new IsOnline(true))
            ->find();

        if ($value && is_countable($branchOnlineFacilities) && \count($branchOnlineFacilities) > 0) {
            $this->logger->warning('Failed because the studio already has an online facility');

            return false;
        }

        if (!$hasId) {
            return true;
        }

        $existingOnlineFacilities = array_filter(
            $branchOnlineFacilities,
            fn(Facility $element) => $element->id() === $data['_id']
        );

        if (!empty($existingOnlineFacilities)) {
            $this->logger->warning('Failed because we do not allow updating online facilities');

            return false;
        }

        return true;
    }
}
