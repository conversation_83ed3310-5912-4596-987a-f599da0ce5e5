<?php

namespace Glofox\Response\Models\Branches;

use Glofox\Response\BaseModel;
use Glofox\Response\Models\Branches\Features\BookingModel;
use Glofox\Response\Models\Branches\Features\ClassesModel;
use Glofox\Response\Models\Branches\Features\ClubInfoModel;
use Glofox\Response\Models\Branches\Features\CoursesModel;
use Glofox\Response\Models\Branches\Features\FacilitiesModel;
use Glofox\Response\Models\Branches\Features\GeneralModel;
use Glofox\Response\Models\Branches\Features\MembershipsModel;
use Glofox\Response\Models\Branches\Features\NewsModel;
use Glofox\Response\Models\Branches\Features\StoreModel;
use Glofox\Response\Models\Branches\Features\StripePaymentModel;
use Glofox\Response\Models\Branches\Features\SubscriptionsModel;
use Glofox\Response\Models\Branches\Features\TrainersModel;

/**
 * @SuppressWarnings(PHPMD)
 */
class FeaturesModel extends BaseModel
{
    /**
     * @var BookingModel
     */
    public $booking;

    /**
     * @var ClassesModel
     */
    public $classes;

    /**
     * @var ClubInfoModel
     */
    public $club_info;

    /**
     * @var CoursesModel
     */
    public $courses;

    /**
     * @var FacilitiesModel
     */
    public $facilities;

    /**
     * @var GeneralModel
     */
    public $general;

    /**
     * @var MembershipsModel
     */
    public $memberships;

    /**
     * @var SubscriptionsModel
     */
    public $subscriptions;

    /**
     * @var NewsModel
     */
    public $news;

    /**
     * @var
     */
    public $payments;

    /**
     * @var StoreModel
     */
    public $store;

    /**
     * @var StripePaymentModel
     */
    public $stripe_payment;

    /**
     * @var TrainersModel
     */
    public $trainers;

    protected array $allowedFields = [
        'booking',
        'classes',
        'club_info',
        'message',
        'courses',
        'facilities',
        'general',
        'family_accounts',
        'memberships',
        'subscriptions',
        'news',
        'payments',
        'store',
        'stripe_payment',
        'trainers',
    ];

    public function __construct(array $data)
    {
        $this->setModelFields($data);
    }
}
