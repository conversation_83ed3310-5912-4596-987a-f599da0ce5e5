<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\Memberships\Services;

use Glofox\Domain\Memberships\Services\MembershipsClient;
use Glofox\Domain\Memberships\Services\RequestMembershipSyncService;
use Glofox\Domain\Users\Models\User;
use Psr\Log\LoggerInterface;

\App::import('Test/Case', 'GlofoxTestCase');

class RequestMembershipSyncServiceTest extends \GlofoxTestCase
{
    public $fixtures = [];

    /** @var MembershipsClient */
    private $membershipsClient;

    /** @var LoggerInterface */
    private $logger;

    /** @var User */
    private $user;

    public function setUp()
    {
        parent::setUp();

        $this->membershipsClient = \Mockery::mock(MembershipsClient::class);
        $this->logger = \Mockery::mock(LoggerInterface::class);

        $this->user = User::make([
            '_id' => 'userId',
            'branch_id' => 'branchId',
            'origin_branch_id' => 'branchId',
        ]);
    }

    public function tearDown()
    {
        parent::tearDown();

        \Mockery::close();
    }

    public function test_happy_path(): void
    {
        $this->membershipsClient
            ->shouldReceive('requestSyncCurrentActiveMembership')
            ->andReturnSelf()
            ->once()
            ->getMock();

        $this->logger
            ->shouldReceive('info')
            ->andReturnSelf()
            ->times(2);

        $this->injectDependencies();

        /** @var RequestMembershipSyncService $service */
        $service = app()->make(RequestMembershipSyncService::class);
        $service->execute($this->user);

        $this->forgetDependencies();
    }

    public function test_when_there_is_a_problem_communicating_with_memberships_service_there_is_not_attempt_to_log_a_successful_message(
    ): void {
        $this->membershipsClient
            ->shouldReceive('requestSyncCurrentActiveMembership')
            ->andReturnUsing(function () {
                throw new \UnsuccessfulOperation();
            })
            ->once()
            ->getMock();

        $this->logger
            ->shouldReceive('info')
            ->andReturnSelf()
            ->once();

        $this->injectDependencies();

        $this->setExpectedException(\UnsuccessfulOperation::class);

        /** @var RequestMembershipSyncService $service */
        $service = app()->make(RequestMembershipSyncService::class);
        $service->execute($this->user);

        $this->forgetDependencies();
    }

    private function injectDependencies(): void
    {
        app()->instance(MembershipsClient::class, $this->membershipsClient);
        app()->instance(LoggerInterface::class, $this->logger);
    }

    private function forgetDependencies(): void
    {
        app()->forgetInstance(MembershipsClient::class);
        app()->forgetInstance(LoggerInterface::class);
    }
}
