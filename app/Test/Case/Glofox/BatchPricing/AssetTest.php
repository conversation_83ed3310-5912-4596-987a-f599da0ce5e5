<?php

namespace CakeTestCases\Glofox\BatchPricing;

use Glofox\BatchPricing\Asset;

\App::import('Test/Case', 'GlofoxTestCase');

class AssetTest extends \PHPUnit_Framework_TestCase
{
   public const programId = '615c273bc860321de7276852';
   public const productId = '673c273bc860321de8276892';
   public const courseId = '673c273bc860321de8276855';

   public function testFromProgramHappyPath()
   {
       $program = [
			'_id' => self::programId,
			'active' => true,
			'name' => 'Spinning Class',
			'allowed_member_types' => [
				[
					'price' => '20',
					'type' => 'payg',
				],
				[
					'price' => 0,
					'type' => 'member',
				],
				[
					'price' => 10,
					'type' => 'member',
				],
				[
					'price' => 13.2,
					'type' => 'member',
				],
				[
					'price' => null,
					'type' => 'member',
				]
			],
			'pricing' => 'CUSTOM',
		];
       $asset = Asset::fromProgram($program);

       $expected = [
			[
				'mongo_collection' => 'programs',
				'mongo_record_id' => $program['_id'],
				'price_type' => 'payg',
				'service_type' => 'classes',
				'service_id' => $program['_id'],
				'price' => 2000,
			],
			[
				'mongo_collection' => 'programs',
				'mongo_record_id' => $program['_id'],
				'price_type' => 'member',
				'service_type' => 'classes',
				'service_id' => $program['_id'],
				'price' => 0,
			],
			[
				'mongo_collection' => 'programs',
				'mongo_record_id' => $program['_id'],
				'price_type' => 'member',
				'service_type' => 'classes',
				'service_id' => $program['_id'],
				'price' => 1000,
			],
			[
				'mongo_collection' => 'programs',
				'mongo_record_id' => $program['_id'],
				'price_type' => 'member',
				'service_type' => 'classes',
				'service_id' => $program['_id'],
				'price' => 1320,
			]
		];
       static::assertEquals($expected, $asset);
   }

   public function testFromProgramNoPrice()
   {
       $program = [
			'_id' => self::programId,
			'active' => true,
			'name' => 'Spinning Class',
			'allowed_member_types' => [
				[
					'type' => 'payg',
				],
			],
			'pricing' => 'CUSTOM',
		];
       $asset = Asset::fromProgram($program);

       $expected = [];
       static::assertEquals($expected, $asset);
   }

   public function testFromProgramAwkwardAllowedMemberTypes()
   {
       $program = [
			'_id' => self::programId,
			'active' => true,
			'name' => 'Spinning Class',
			'pricing' => 'CUSTOM',
		];
       $asset = Asset::fromProgram($program);

       $expected = [];
       static::assertEquals($expected, $asset);

       $program = [
			'_id' => self::programId,
			'active' => true,
			'name' => 'Spinning Class',
			'pricing' => 'CUSTOM',
           'allowed_member_types' => 78
		];

       $asset = Asset::fromProgram($program);

       $expected = [];
       static::assertEquals($expected, $asset);
   }

   public function testFromProductHappyPath()
   {
       $product = [
			'_id' => self::productId,
			'active' => true,
			'name' => 'Spinning Class',
			'presentations' => [
				[
					'retail_price' => '20',
					'type' => 'payg',
				],
				[
					'retail_price' => 0,
					'type' => 'member',
				],
				[
					'retail_price' => 10,
					'type' => 'member',
				],
				[
					'retail_price' => 13.2,
					'type' => 'member',
				],
				[
					'retail_price' => null,
					'type' => 'member',
				]
			],
		];

       $asset = Asset::fromProduct($product);

       $expected = [
			[
				'mongo_collection' => 'products',
				'mongo_record_id' => $product['_id'],
				'price_type' => 'retail_price',
				'service_type' => 'products',
				'service_id' => $product['_id'],
				'price' => 2000,
			],
			[
				'mongo_collection' => 'products',
				'mongo_record_id' => $product['_id'],
				'price_type' => 'retail_price',
				'service_type' => 'products',
				'service_id' => $product['_id'],
				'price' => 0,
			],
			[
				'mongo_collection' => 'products',
				'mongo_record_id' => $product['_id'],
				'price_type' => 'retail_price',
				'service_type' => 'products',
				'service_id' => $product['_id'],
				'price' => 1000,
			],
			[
				'mongo_collection' => 'products',
				'mongo_record_id' => $product['_id'],
				'price_type' => 'retail_price',
				'service_type' => 'products',
				'service_id' => $product['_id'],
				'price' => 1320,
			]
		];
       static::assertEquals($expected, $asset);
   }

   public function testFromProductNoPrice()
   {
       $product = [
			'_id' => self::productId,
			'active' => true,
			'name' => 'Spinning Class',
			'presentations' => [
				[
					'type' => 'payg',
				],
			],
		];
       $asset = Asset::fromProduct($product);

       $expected = [];
       static::assertEquals($expected, $asset);
   }

   public function testFromProductAwkwardAllowedMemberTypes()
   {
       $product = [
			'_id' => self::productId,
			'active' => true,
			'name' => 'Spinning Class',
			'pricing' => 'CUSTOM',
		];

       $asset = Asset::fromProgram($product);

       $expected = [];
       static::assertEquals($expected, $asset);

       $program = [
			'_id' => self::productId,
			'active' => true,
			'name' => 'Spinning Class',
			'pricing' => 'CUSTOM',
           'presentations' => 78
		];

       $asset = Asset::fromProduct($program);

       $expected = [];
       static::assertEquals($expected, $asset);
   }

   public function testFromTrainerHappyPath()
   {
       $trainer = [
			'_id' => self::productId,
			'active' => true,
			'name' => 'Spinning Class',
			'_pattern' => [
				'default_price' => 2.3,
				"allowed_member_types" => [
					[
						'price' => '20',
						'type' => 'payg',
					],
					[
						'price' => 0,
						'type' => 'member',
					],
					[
						'price' => 10,
						'type' => 'member',
					],
					[
						'price' => 13.2,
						'type' => 'member',
					],
					[
						'price' => null,
						'type' => 'member',
					]
				]
			],
		];

       $asset = Asset::fromTrainer($trainer);

       $expected = [
			[
				'mongo_collection' => 'users',
				'mongo_record_id' => $trainer['_id'],
				'price_type' => 'payg',
				'service_type' => 'trainers',
				'service_id' => $trainer['_id'],
				'price' => 2000,
			],
			[
				'mongo_collection' => 'users',
				'mongo_record_id' => $trainer['_id'],
				'price_type' => 'member',
				'service_type' => 'trainers',
				'service_id' => $trainer['_id'],
				'price' => 0,
			],
			[
				'mongo_collection' => 'users',
				'mongo_record_id' => $trainer['_id'],
				'price_type' => 'member',
				'service_type' => 'trainers',
				'service_id' => $trainer['_id'],
				'price' => 1000,
			],
			[
				'mongo_collection' => 'users',
				'mongo_record_id' => $trainer['_id'],
				'price_type' => 'member',
				'service_type' => 'trainers',
				'service_id' => $trainer['_id'],
				'price' => 1320,
			],
			[
				'mongo_collection' => 'users',
				'mongo_record_id' => $trainer['_id'],
				'price_type' => 'default_price',
				'service_type' => 'trainers',
				'service_id' => $trainer['_id'],
				'price' => 230,
			],
		];
       static::assertEquals($expected, $asset);
   }

   public function testFromTrainerNoDefaultPrice()
   {
       $trainer = [
			'_id' => self::productId,
			'active' => true,
			'name' => 'Spinning Class',
			'_pattern' => [
				"allowed_member_types" => [
					[
						'price' => '20',
						'type' => 'payg',
					],
					[
						'price' => 0,
						'type' => 'member',
					],
					[
						'price' => 10,
						'type' => 'member',
					],
					[
						'price' => 13.2,
						'type' => 'member',
					],
					[
						'price' => null,
						'type' => 'member',
					]
				]
			],
		];

       $asset = Asset::fromTrainer($trainer);

       $expected = [
			[
				'mongo_collection' => 'users',
				'mongo_record_id' => $trainer['_id'],
				'price_type' => 'payg',
				'service_type' => 'trainers',
				'service_id' => $trainer['_id'],
				'price' => 2000,
			],
			[
				'mongo_collection' => 'users',
				'mongo_record_id' => $trainer['_id'],
				'price_type' => 'member',
				'service_type' => 'trainers',
				'service_id' => $trainer['_id'],
				'price' => 0,
			],
			[
				'mongo_collection' => 'users',
				'mongo_record_id' => $trainer['_id'],
				'price_type' => 'member',
				'service_type' => 'trainers',
				'service_id' => $trainer['_id'],
				'price' => 1000,
			],
			[
				'mongo_collection' => 'users',
				'mongo_record_id' => $trainer['_id'],
				'price_type' => 'member',
				'service_type' => 'trainers',
				'service_id' => $trainer['_id'],
				'price' => 1320,
			],
		];
       static::assertEquals($expected, $asset);
   }

   public function testFromTrainerOnlyDefaultPrice()
   {
       $trainer = [
			'_id' => self::productId,
			'active' => true,
			'name' => 'Spinning Class',
			'_pattern' => [
				'default_price' => 2.3,
				"allowed_member_types" => []
			],
		];

       $asset = Asset::fromTrainer($trainer);

       $expected = [
			[
				'mongo_collection' => 'users',
				'mongo_record_id' => $trainer['_id'],
				'price_type' => 'default_price',
				'service_type' => 'trainers',
				'service_id' => $trainer['_id'],
				'price' => 230,
			],
		];
       static::assertEquals($expected, $asset);
   }

   public function testFromTrainerNullDefaultPrice()
   {
       $trainer = [
			'_id' => self::productId,
			'active' => true,
			'name' => 'Spinning Class',
			'_pattern' => [
				'default_price' => null,
				"allowed_member_types" => []
			],
		];

       $asset = Asset::fromTrainer($trainer);

       $expected = [];
       static::assertEquals($expected, $asset);
   }

   public function testFromTrainerNoAllowedMemberTypes()
   {
       $trainer = [
			'_id' => self::productId,
			'active' => true,
			'name' => 'Spinning Class',
			'_pattern' => [],
		];

       $asset = Asset::fromTrainer($trainer);

       $expected = [];
       static::assertEquals($expected, $asset);

       $trainer = [
			'_id' => self::productId,
			'active' => true,
			'name' => 'Spinning Class',
			'_pattern' => [
				'allowed_member_types' => null
			],
		];

       $asset = Asset::fromTrainer($trainer);

       $expected = [];
       static::assertEquals($expected, $asset);

       $trainer = [
			'_id' => self::productId,
			'active' => true,
			'name' => 'Spinning Class',
			'_pattern' => [
				'allowed_member_types' => 78
			],
		];

       $asset = Asset::fromTrainer($trainer);

       $expected = [];
       static::assertEquals($expected, $asset);
   }

   public function testFromTrainerNoPattern()
   {
       $trainer = [
			'_id' => self::productId,
			'active' => true,
			'name' => 'Spinning Class',
		];

       $asset = Asset::fromTrainer($trainer);

       $expected = [];
       static::assertEquals($expected, $asset);

       $trainer = [
			'_id' => self::productId,
			'active' => true,
			'name' => 'Spinning Class',
			'_pattern' => null,
		];

       $asset = Asset::fromTrainer($trainer);

       $expected = [];
       static::assertEquals($expected, $asset);

       $trainer = [
			'_id' => self::productId,
			'active' => true,
			'name' => 'Spinning Class',
			'_pattern' => 78,
		];

       $asset = Asset::fromTrainer($trainer);

       $expected = [];
       static::assertEquals($expected, $asset);
   }

   public function testFromTimeslotHappyPath()
   {
       $timeslot = [
			'_id' => self::programId,
			'active' => true,
			'name' => 'Spinning Class',
			'allowed_member_types' => [
				[
					'price' => '20',
					'type' => 'payg',
				],
				[
					'price' => 0,
					'type' => 'member',
				],
				[
					'price' => 10,
					'type' => 'member',
				],
				[
					'price' => 13.2,
					'type' => 'member',
				],
				[
					'price' => null,
					'type' => 'member',
				]
			],
			'pricing' => 'CUSTOM',
			'model_id' => 'foo'
		];
       $asset = Asset::fromTimeslotPattern($timeslot);

       $expected = [
			[
				'mongo_collection' => 'time_slot_patterns',
				'mongo_record_id' => $timeslot['_id'],
				'price_type' => 'payg',
				'service_type' => 'facilities',
				'service_id' => 'foo',
				'price' => 2000,
			],
			[
				'mongo_collection' => 'time_slot_patterns',
				'mongo_record_id' => $timeslot['_id'],
				'price_type' => 'member',
				'service_type' => 'facilities',
				'service_id' => 'foo',
				'price' => 0,
			],
			[
				'mongo_collection' => 'time_slot_patterns',
				'mongo_record_id' => $timeslot['_id'],
				'price_type' => 'member',
				'service_type' => 'facilities',
				'service_id' => 'foo',
				'price' => 1000,
			],
			[
				'mongo_collection' => 'time_slot_patterns',
				'mongo_record_id' => $timeslot['_id'],
				'price_type' => 'member',
				'service_type' => 'facilities',
				'service_id' => 'foo',
				'price' => 1320,
			]
		];
       static::assertEquals($expected, $asset);
   }

   public function testFromTimeslotNoPrice()
   {
       $timeslot = [
			'_id' => self::programId,
			'active' => true,
			'name' => 'Spinning Class',
			'allowed_member_types' => [
				[
					'type' => 'payg',
				],
			],
			'pricing' => 'CUSTOM',
			'model_id' => 'foo'
		];
       $asset = Asset::fromTimeslotPattern($timeslot);

       $expected = [];
       static::assertEquals($expected, $asset);
   }

   public function testFromTimeslotAwkwardAllowedMemberTypes()
   {
       $timeslot = [
			'_id' => self::programId,
			'active' => true,
			'name' => 'Spinning Class',
			'pricing' => 'CUSTOM',
		];

       $asset = Asset::fromTimeslotPattern($timeslot);

       $expected = [];
       static::assertEquals($expected, $asset);

       $timeslot = [
			'_id' => self::programId,
			'active' => true,
			'name' => 'Spinning Class',
			'pricing' => 'CUSTOM',
           'allowed_member_types' => 78,
			'model_id' => 'foo'
		];

       $asset = Asset::fromTimeslotPattern($timeslot);

       $expected = [];
       static::assertEquals($expected, $asset);
   }

   public function testFromMembershipHappyPath()
   {
       $membership = [
			'_id' => self::productId,
			'plans' => [
				[
					'price' => '20',
				],
				[
					'upfront_fee' => '21.2',
				],
				[
					'upfront_fee' => 0,
					'price' => '0',
				],
				[
					'upfront_fee' => null,
					'price' => 10,
				],
				[
					'upfront_fee' => '21',
					'price' => 13.2,
				],
			],
		];

       $asset = Asset::fromMembership($membership);

       $expected = [
			[
				'mongo_collection' => 'memberships',
				'mongo_record_id' => $membership['_id'],
				'price_type' => 'price',
				'service_type' => 'memberships',
				'service_id' => self::productId,
				'price' => 2000,
			],
			[
				'mongo_collection' => 'memberships',
				'mongo_record_id' => $membership['_id'],
				'price_type' => 'upfront_fee',
				'service_type' => 'memberships',
				'service_id' => self::productId,
				'price' => 2120,
			],
			[
				'mongo_collection' => 'memberships',
				'mongo_record_id' => $membership['_id'],
				'price_type' => 'upfront_fee',
				'service_type' => 'memberships',
				'service_id' => self::productId,
				'price' => 0,
			],
			[
				'mongo_collection' => 'memberships',
				'mongo_record_id' => $membership['_id'],
				'price_type' => 'price',
				'service_type' => 'memberships',
				'service_id' => self::productId,
				'price' => 0,
			],
			[
				'mongo_collection' => 'memberships',
				'mongo_record_id' => $membership['_id'],
				'price_type' => 'price',
				'service_type' => 'memberships',
				'service_id' => self::productId,
				'price' => 1000,
			],
			[
				'mongo_collection' => 'memberships',
				'mongo_record_id' => $membership['_id'],
				'price_type' => 'upfront_fee',
				'service_type' => 'memberships',
				'service_id' => self::productId,
				'price' => 2100,
			],
			[
				'mongo_collection' => 'memberships',
				'mongo_record_id' => $membership['_id'],
				'price_type' => 'price',
				'service_type' => 'memberships',
				'service_id' => self::productId,
				'price' => 1320,
			],
		];

       static::assertEquals($expected, $asset);
   }

   public function testFromMembershipNoPrice()
   {
       $membership = [
			'_id' => self::programId,
			'plans' => [
				[
					'foo' => 'bar',
				],
				[
					'upfront_fee' => null,
				],
				[
					'price' => null,
				],
			],
		];
       $asset = Asset::fromMembership($membership);

       $expected = [];
       static::assertEquals($expected, $asset);
   }

   public function testFromMembershipAwkwardData()
   {
       $membership = [
			'_id' => self::programId,
			'plans' => [],
		];
       $asset = Asset::fromMembership($membership);

       $expected = [];
       static::assertEquals($expected, $asset);

       $membership = [
			'_id' => self::programId,
			'plans' => 32,
		];
       $asset = Asset::fromMembership($membership);

       $expected = [];
       static::assertEquals($expected, $asset);

       $membership = [
			'_id' => self::programId,
		];
       $asset = Asset::fromMembership($membership);

       $expected = [];
       static::assertEquals($expected, $asset);
   }

	public function testFromCourseHappyPath()
   {
		$course = [
			'_id' => self::courseId,
			'active' => false,
			'name' => 'Tzt2',
			'allowed_member_types' => [
				[
					'price' => 12,
					'type' => 'payg',
				],
				[
					'price' => 11,
					'type' => 'member',
				],
			],
			'pricing' => 'SINGLE',
		];
       $asset = Asset::fromCourse($course);

       $expected = [
			[
				'mongo_collection' => 'courses',
				'mongo_record_id' => self::courseId,
				'price_type' => 'payg',
				'service_type' => 'courses',
				'service_id' => self::courseId,
				'price' => 1200,
			],
			[
				'mongo_collection' => 'courses',
				'mongo_record_id' => self::courseId,
				'price_type' => 'member',
				'service_type' => 'courses',
				'service_id' => self::courseId,
				'price' => 1100,
			]
		];
       static::assertEquals($expected, $asset);
   }

   public function testFromCourseNoPrice()
   {
       $course = [
			'_id' => self::courseId,
			'active' => false,
			'name' => 'Tzt2',
			'allowed_member_types' => [
				[
					'type' => 'payg',
				],
			],
			'pricing' => 'SINGLE',
		];
       $asset = Asset::fromCourse($course);

       $expected = [];
       static::assertEquals($expected, $asset);
   }

   public function testFromCourseAwkwardAllowedMemberTypes()
   {
		$course = [
			'_id' => self::courseId,
			'active' => false,
			'name' => 'Tzt2',
			'pricing' => 'SINGLE',
		];
       $asset = Asset::fromCourse($course);

       $expected = [];
       static::assertEquals($expected, $asset);

		$course = [
			'_id' => self::courseId,
			'active' => false,
			'name' => 'Tzt2',
			'pricing' => 'SINGLE',
			'allowed_member_types' => 'foo',
		];

       $asset = Asset::fromCourse($course);

       $expected = [];
       static::assertEquals($expected, $asset);
   }
}
