<?php

namespace Glofox\Payments\Providers\Gateway\Merchants\DataModellers;

use Carbon\Carbon;
use Glofox\Domain\Branches\Models\Branch;
use Glofox\Domain\PaymentProviders\Models\PaymentProvider;
use Glofox\Payments\Util;
use Illuminate\Support\Collection;

/**
 * Class StripeCustomDataModeller.
 */
class StripeCustomHostedOnboardingDataModeller extends DefaultDataModeller
{
    public function modelForCreating(PaymentProvider $paymentProvider, Branch $branch, Collection $payload): Collection
    {
        $formatPayload = parent::modelForCreating($paymentProvider, $branch, $payload);

        $formatPayload = $formatPayload->put('external_account', $payload->get('external_account'));

        return $this->format($paymentProvider, $branch, $formatPayload);
    }

    public function modelForUpdating(PaymentProvider $paymentProvider, Branch $branch, Collection $payload): Collection
    {
        $formatPayload = parent::modelForUpdating($paymentProvider, $branch, $payload);

        $formatPayload = $formatPayload->put('external_account', $payload->get('external_account'));
        $formatPayload = $formatPayload->put('active', $payload->get('active'));

        return $this->format($paymentProvider, $branch, $formatPayload);
    }

    public function modelForRegisterUrl(PaymentProvider $paymentProvider, Branch $branch): Collection
    {
        return collect([
            'provider_id' => $paymentProvider->id(),
            'publishable_key' => env('GLOFOX_' . $paymentProvider->name() . '_PUBLISHABLE_KEY'),
            'features' => $paymentProvider->availableCountries()->findByCountryCode($branch->address()->countryCode())->features(),
        ]);
    }

    private function format(PaymentProvider $paymentProvider, Branch $branch, Collection $payload): Collection
    {
        $externalAccount = $payload->get('external_account');
        $payload = $payload->forget('external_account');

        $features = [];
        $providerCountrySettings = $paymentProvider->availableCountries()->findByCountryCode($branch->address()->countryCode());
        if (!empty($providerCountrySettings)) {
            $features = $providerCountrySettings->features();
        }

        return collect([
            'Email' => $branch->email(),
            'PayoutStatement' => $payload->get('payout_statement', 'Glofox Payment'),
            'BusinessName' => $branch->name(),
            'Country' => $branch->address()->countryCode(),
            'LegalEntity' => [
                'Type' => 'company',
                'BusinessName' => $branch->name(),
            ],
            'TOSAcceptance' => [
                'Date' => Carbon::now()->getTimestamp(),
                'UserAgent' => $_SERVER['HTTP_USER_AGENT'],
                'IP' => Util::getRequestClientIP(),
            ],
            'Payload' => $payload,
            'AccountManagementLinkSuccessURL' => sprintf('%s/dashboard/#/onboardingPayment/success', env('GLOFOX_ROOT_DOMAIN')),
            'AccountManagementLinkFailureURL' => sprintf('%s/dashboard/#/onboardingPayment/failure', env('GLOFOX_ROOT_DOMAIN')),
            'ExternalAccount' => ['Token' => $externalAccount],
            'Features' => $features,
            'Meta' => [
                'BranchId' => $branch->id(),
            ],
            'Active' => $payload->get('active', true),
        ]);
    }
}
