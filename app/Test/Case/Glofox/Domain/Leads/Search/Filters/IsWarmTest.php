<?php

namespace CakeTestCases\Glofox\Domain\Leads\Search\Filters;

use Glofox\Domain\Leads\Search\Filters\IsWarm;
use Glofox\Domain\Leads\Status;

\App::import('Test/Case', 'GlofoxTestCase');

class IsWarmTest extends \GlofoxTestCase
{
    /**
     * @var Expression
     */
    protected $evaluable;

    public function setUp()
    {
        parent::setUp();

        $this->evaluable = new IsWarm();
    }

    public function testEvaluationPasses()
    {
        $data = [
            'lead_status' => Status::WARM,
        ];

        $this->assertTrue($this->evaluable->evaluate($data));
    }

    public function testEvaluationFails()
    {
        $data = [
            'lead_status' => 'invalid',
        ];

        $this->assertFalse($this->evaluable->evaluate($data));
    }
}
