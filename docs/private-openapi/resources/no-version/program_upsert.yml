post:
  tags:
    - Program
  summary: Create a program
  operationId: upsert
  description:
    Use this call to create a program in the studio selected. If the creation does not go through it will retrieve an
    error stating the root cause that generate this error. It will trigger a bad request response if the required
    parameters are not added to the request.
  security:
    - coreapi_auth: [ ]
  requestBody:
    required: true
    content:
      application/json:
        schema:
          $ref: '../../requests/no-version/create_program.yml'
  responses:
    '200':
      description: OK
      content:
        application/json:
          schema:
            $ref: '../../schemas/program.yml'
    '400':
      description: Bad request
      content:
        application/json:
          schema:
            $ref: "../../schemas/error.yml"
