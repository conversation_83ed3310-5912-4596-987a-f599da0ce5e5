get:
  tags:
    - Facilities
  summary: ''
  operationId: facilities-list
  description: |
    This endpoint returns paginated list of the facilities for the specified location.
    Facilities are sorted by creation date in descending mode.
  x-internal: true
  security:
    - coreapi_auth: [ ]
  parameters:
    - schema:
        type: integer
        minimum: 1
        default: 1
      in: query
      name: page
    - schema:
        type: integer
        minimum: 1
        maximum: 100
        default: 50
      in: query
      name: limit
    - schema:
        type: string
      in: query
      description: field | -field (name, created_at)
      name: sort-by
  responses:
    '200':
      description: OK
      content:
        application/json:
          schema:
            allOf:
              - $ref: '../../schemas/pagination.yml'
              - type: object
                properties:
                  model:
                    type: string
                    description: The name of the represented model
                    default: facility
                  data:
                    type: array
                    example: [ ]
                    items:
                      $ref: '../../schemas/facility.yml'
    '400':
      description: Bad request
      content:
        application/json:
          schema:
            $ref: '../../schemas/error.yml'