<?php

declare(strict_types=1);

namespace Glofox\Infrastructure\Locker;

use Carbon\Carbon;
use Glofox\Domain\Cache\CacheClientInterface;
use Glofox\Domain\Locker\SetUpIntentAlreadyLockedException;
use Glofox\Domain\Locker\SetUpIntentLockerInterface;
use Psr\Log\LoggerInterface;

class SetUpIntentLocker implements SetUpIntentLockerInterface
{
    private CacheClientInterface $cache;

    private LoggerInterface $logger;

    public function __construct(
        LoggerInterface $logger,
        CacheClientInterface $cache
    ) {
        $this->logger = $logger;
        $this->cache = $cache;
    }

    public function lock(string $userId, string $token): void
    {
        $lockKey = $this->computeLockKey($userId, $token);
        $this->logger->info(sprintf('attempting to lock setup intent for user with key %s', $lockKey));

        $added = $this->cache->setIfNotExist($lockKey, $lockKey);
        if (!$added) {
            throw SetUpIntentAlreadyLockedException::withUserIdAndToken($userId, $token);
        }

        $expiresAt = Carbon::now()->addMinute();
        $this->cache->expiresAt($lockKey, $expiresAt);

        $this->logger->info(
            sprintf('lock successfully created for setup intent with key %s', $lockKey),
            [
                'expiresAt' => $expiresAt->toISOString(),
            ]
        );
    }

    public function unlock(string $userId, string $token): void
    {
        $lockKey = $this->computeLockKey($userId, $token);
        $this->cache->delete($lockKey);

        $this->logger->info(
            sprintf('lock deleted for setup intent with key %s', $lockKey)
        );
    }

    private function computeLockKey(string $userId, $token): string
    {
        return sprintf(
            '%s-%s',
            $userId,
            $token
        );
    }
}
