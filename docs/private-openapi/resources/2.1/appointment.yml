parameters:
  - schema:
      type: string
    name: branchId
    in: path
    required: true
  - schema:
      type: string
    name: appointmentId
    in: path
    required: true
get:
  tags:
    - Appointments
  summary: ''
  operationId: get-appointment-by-id
  description: |-
    This endpoint returns appointment data. If the id is not found, it returns a
    200 status code with an error.
  x-internal: true
  security:
    - coreapi_auth: [ ]
  responses:
    '200':
      description: OK
      content:
        application/json:
          schema:
            $ref: '../../schemas/appointment.yml'
    '400':
      description: Bad request
      content:
        application/json:
          schema:
            $ref : "../../schemas/error.yml"
put:
  tags:
    - Appointments
  summary: ''
  operationId: update-appointment-by-id
  description: If the id is not found, it returns a 200 status code with an error
  x-internal: true
  security:
    - coreapi_auth: [ ]
  requestBody:
    required: true
    content:
      application/json:
        schema:
          $ref: '../../requests/2.1/create_appointment.yml'
  responses:
    '200':
      description: OK
      content:
        application/json:
          schema:
            $ref: '../../schemas/appointment.yml'
    '400':
      description: Bad request
      content:
        application/json:
          schema:
            $ref : "../../schemas/error.yml"
delete:
  tags:
    - Appointments
  summary: ''
  operationId: delete-appointment-by-id
  description: If the id is not found, it still returns a 204 status code
  x-internal: true
  security:
    - coreapi_auth: [ ]
  responses:
    '204':
      description: No Content
    '400':
      description: Bad request
      content:
        application/json:
          schema:
            $ref : "../../schemas/error.yml"