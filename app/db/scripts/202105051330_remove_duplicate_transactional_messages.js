db.transactional_messages
    .aggregate([{
        "$group": {
            "_id": {
                "identifier": "$identifier",
                "branch_id": "$branch_id"
            }, "count": {"$sum": 1}
        }

    },
        {"$match": {"_id.identifier": "MEMBERSHIP_CONFIRMATION", "count": {"$gt": 1}}},
    ]).forEach(function (message) {
        print(`Removing duplicate for studio: ${message._id.branch_id}`)
        db.transactional_messages.deleteOne({
            identifier: "MEMBERSHIP_CONFIRMATION",
            branch_id: message._id.branch_id,
            enabled: false
        })
    }
)
