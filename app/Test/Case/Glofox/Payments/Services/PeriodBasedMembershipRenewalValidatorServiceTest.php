<?php

namespace CakeTestCases\Glofox\Payments\Services;

use CakeTestCases\Glofox\Domain\Logger\Traits\MockedLoggerTrait;
use Carbon\Carbon;
use Glofox\Domain\Users\Models\User;
use Glofox\Payments\Entities\Transaction\Models\Transaction;
use Glofox\Payments\Entities\WebHook\Models\EventDetailPeriod;
use Glofox\Payments\Services\MembershipRenewalValidatorUseCase;
use Glofox\Payments\Services\PeriodBasedMembershipRenewalValidatorService;

\App::import('Test/Case', 'GlofoxTestCase');

class PeriodBasedMembershipRenewalValidatorServiceTest extends \GlofoxTestCase
{
    use MockedLoggerTrait;

    /** @var PeriodBasedMembershipRenewalValidatorService */
    private $service;

    public function setUp()
    {
        parent::setUp();

        $this->service = app()->make(PeriodBasedMembershipRenewalValidatorService::class);
    }

    public function tearDown()
    {
        parent::tearDown();

        \Mockery::close();

        $this->teardownLogger();
    }

    public function test_periodShouldRenewMembership()
    {
        $now = Carbon::now('Europe/Dublin');

        //////////////////////////////////////////////////////////////////////
        // valid transaction, period is invalid since is before current cycle
        $transaction = (new Transaction())->setCreatedAt($now->copy()->addDays(2)->getTimestamp());
        $period = new EventDetailPeriod(
            $now->copy()->subDays(2)->getTimestamp(),
            $now->copy()->subDay()->getTimestamp()
        );
        $user = User::make([
            'branch_id' => '49a7011a05c677b9a916612a',
            'membership' => [
                'start_date' => new \MongoDate($now->copy()->subDay()->getTimestamp()),
                'expiry_date' => new \MongoDate($now->copy()->addDay()->getTimestamp()),
            ],
        ]);

        $useCase = new MembershipRenewalValidatorUseCase($user, $transaction, $period);
        $shouldRenew = $this->service->execute($useCase);
        self::assertFalse($shouldRenew);

        //////////////////////////////////////////////////////////////////////
        // valid transaction, the period is before current start_date but passes since it represents current period
        $transaction = (new Transaction())->setCreatedAt($now->copy()->addDays(2)->getTimestamp());
        $period = new EventDetailPeriod(
            $now->getTimestamp(),
            $now->copy()->addDay()->getTimestamp()
        );
        $user = User::make([
            'branch_id' => '49a7011a05c677b9a916612a',
            'membership' => [
                'start_date' => new \MongoDate($now->copy()->addDay()->getTimestamp()),
                'expiry_date' => new \MongoDate($now->copy()->addDays(2)->getTimestamp()),
            ],
        ]);

        $useCase = new MembershipRenewalValidatorUseCase($user, $transaction, $period);
        $shouldRenew = $this->service->execute($useCase);
        self::assertTrue($shouldRenew);

        //////////////////////////////////////////////////////////////////////
        // valid transaction, valid period
        $transaction = (new Transaction())->setCreatedAt($now->copy()->addDays(2)->getTimestamp());
        $period = new EventDetailPeriod(
            $now->copy()->addDay()->getTimestamp(),
            $now->copy()->addDays(2)->getTimestamp()
        );
        $user = User::make([
            'branch_id' => '49a7011a05c677b9a916612a',
            'membership' => [
                'start_date' => new \MongoDate($now->copy()->subDay()->getTimestamp()),
                'expiry_date' => new \MongoDate($now->copy()->addDay()->getTimestamp()),
            ],
        ]);

        $useCase = new MembershipRenewalValidatorUseCase($user, $transaction, $period);
        $shouldRenew = $this->service->execute($useCase);
        self::assertTrue($shouldRenew);
    }


    public function test_periodShouldRenewLockedMembershipInLastCycle()
    {
        $now = Carbon::now('Europe/Dublin');

        //////////////////////////////////////////////////////////////////////
        // period is in last cycle and status is ACTIVE
        $transaction = (new Transaction())->setCreatedAt($now->copy()->addDays(2)->getTimestamp());
        $period = new EventDetailPeriod(
            $now->copy()->subDays(2)->getTimestamp(),
            $now->copy()->subDays(2)->getTimestamp()
        );
        $user = User::make([
            'branch_id' => '49a7011a05c677b9a916612a',
            'membership' => [
                'start_date' => new \MongoDate($now->copy()->subDay()->getTimestamp()),
                'expiry_date' => new \MongoDate($now->copy()->addDay()->getTimestamp()),
                'status' => 'ACTIVE'
            ],
        ]);

        $useCase = new MembershipRenewalValidatorUseCase($user, $transaction, $period);
        $shouldRenew = $this->service->execute($useCase);
        self::assertFalse($shouldRenew);


        //////////////////////////////////////////////////////////////////////
        //period is in last cycle and status is LOCKED
        $transaction = (new Transaction())->setCreatedAt($now->copy()->addDays(2)->getTimestamp());
        $period = new EventDetailPeriod(
            $now->copy()->subDays(2)->getTimestamp(),
            $now->copy()->subDays(2)->getTimestamp()
        );
        $user = User::make([
            'branch_id' => '49a7011a05c677b9a916612a',
            'membership' => [
                'start_date' => new \MongoDate($now->copy()->subDay()->getTimestamp()),
                'expiry_date' => new \MongoDate($now->copy()->addDay()->getTimestamp()),
                'status' => 'LOCKED'
            ],
        ]);

        $useCase = new MembershipRenewalValidatorUseCase($user, $transaction, $period);
        $shouldRenew = $this->service->execute($useCase);
        self::assertTrue($shouldRenew);
    }
}
