<?php

App::import('Test/Case', 'GlofoxTestCase');

use Glofox\Response\Models\Branches\Features\BookingModel;

/**
 * Test cases
 * 1 Model contains all fields with data
 * 2 Missing fields and returns correct structure
 * 3 Additional fields and return correct structure
 * 4 Empty array fields and return correct structure.
 *
 * Class BookingModelTest
 */
class BookingModelTest extends GlofoxTestCase
{
    public $fixtures = [];

    /**
     * @dataProvider modelProvider
     */
    public function testModel($dataSent, $result): void
    {
        $model = new BookingModel($dataSent);
        $this->assertEquals($model, $result);
    }

    public function modelProvider(): array
    {
        return [
            '1 Model contains all fields with data' => [
                [
                    'order' => 1,
                    'enabled' => false,
                    'strike_system' => null,
                    '_booking_open_window' => false,
                    'limit_booking_enabled' => false,
                    '_cancel_credit_option' => false,
                    'cancel_credit_option' => 'model_id',
                    'refunded_credit_expiry' => 100,
                    'guest_bookings' => 5,
                    'booking_cancel_window' => 1,
                    'booking_open_window' => 1,
                    'waiting_list_enabled' => true,
                    'auto_booking_enabled' => true,
                    'waiting_list' => 5,
                    'limit_booking_period' => 'day',
                    'limit_booking' => 100,
                    'late_cancellation_enabled' => true,
                ],
                new BookingModel(
                    [
                        'order' => 1,
                        'enabled' => false,
                        'strike_system' => null,
                        '_booking_open_window' => false,
                        'limit_booking_enabled' => false,
                        '_cancel_credit_option' => false,
                        'cancel_credit_option' => 'model_id',
                        'refunded_credit_expiry' => 100,
                        'guest_bookings' => 5,
                        'booking_cancel_window' => 1,
                        'booking_open_window' => 1,
                        'waiting_list_enabled' => true,
                        'auto_booking_enabled' => true,
                        'waiting_list' => 5,
                        'limit_booking_period' => 'day',
                        'limit_booking' => 100,
                        'late_cancellation_enabled' => true,
                    ]
                ),
            ],
            '2 Missing fields and returns correct structure' => [
                [
                    // order, enabled missing
                    'strike_system' => null,
                    '_booking_open_window' => false,
                    'limit_booking_enabled' => false,
                    '_cancel_credit_option' => false,
                    'cancel_credit_option' => 'model_id',
                    'refunded_credit_expiry' => 100,
                    'guest_bookings' => 5,
                    'booking_cancel_window' => 1,
                    'booking_open_window' => 1,
                    'waiting_list_enabled' => true,
                    'auto_booking_enabled' => true,
                    'waiting_list' => 5,
                    'limit_booking_period' => 'day',
                    'limit_booking' => 100,
                    'late_cancellation_enabled' => true,
                ],
                new BookingModel(
                    [
                        'order' => 0,
                        'enabled' => false,
                        'strike_system' => null,
                        '_booking_open_window' => false,
                        'limit_booking_enabled' => false,
                        '_cancel_credit_option' => false,
                        'cancel_credit_option' => 'model_id',
                        'refunded_credit_expiry' => 100,
                        'guest_bookings' => 5,
                        'booking_cancel_window' => 1,
                        'booking_open_window' => 1,
                        'waiting_list_enabled' => true,
                        'auto_booking_enabled' => true,
                        'waiting_list' => 5,
                        'limit_booking_period' => 'day',
                        'limit_booking' => 100,
                        'late_cancellation_enabled' => true,
                    ]
                ),
            ],
            '3 Additional fields and return correct structure' => [
                [
                    'extrafileds' => '', // additional
                    'order' => 1,
                    'enabled' => false,
                    'strike_system' => null,
                    '_booking_open_window' => false,
                    'limit_booking_enabled' => false,
                    '_cancel_credit_option' => false,
                    'cancel_credit_option' => 'model_id',
                    'refunded_credit_expiry' => 100,
                    'guest_bookings' => 5,
                    'booking_cancel_window' => 1,
                    'booking_open_window' => 1,
                    'waiting_list_enabled' => true,
                    'auto_booking_enabled' => true,
                    'waiting_list' => 5,
                    'limit_booking_period' => 'day',
                    'limit_booking' => 100,
                    'late_cancellation_enabled' => true,
                ],
                new BookingModel(
                    [
                        'order' => 1,
                        'enabled' => false,
                        'strike_system' => null,
                        '_booking_open_window' => false,
                        'limit_booking_enabled' => false,
                        '_cancel_credit_option' => false,
                        'cancel_credit_option' => 'model_id',
                        'refunded_credit_expiry' => 100,
                        'guest_bookings' => 5,
                        'booking_cancel_window' => 1,
                        'booking_open_window' => 1,
                        'waiting_list_enabled' => true,
                        'auto_booking_enabled' => true,
                        'waiting_list' => 5,
                        'limit_booking_period' => 'day',
                        'limit_booking' => 100,
                        'late_cancellation_enabled' => true,
                    ]
                ),
            ],
            '4 Empty array fields and return correct structure' => [
                [],
                new BookingModel(
                    [
                        'order' => 0,
                        'enabled' => false,
                        'strike_system' => null,
                        '_booking_open_window' => false,
                        'limit_booking_enabled' => false,
                        '_cancel_credit_option' => false,
                        'cancel_credit_option' => '',
                        'refunded_credit_expiry' => 0,
                        'guest_bookings' => 0,
                        'booking_cancel_window' => 0,
                        'booking_open_window' => 0,
                        'waiting_list_enabled' => false,
                        'auto_booking_enabled' => false,
                        'waiting_list' => 0,
                        'limit_booking_period' => '',
                        'limit_booking' => 0,
                        'late_cancellation_enabled' => false,
                    ]
                ),
            ],
        ];
    }
}
