<?php

use CakeTestCases\Glofox\Domain\Authentication\Token\TokenGeneration;
use CakeTestCases\Glofox\Domain\Logger\Traits\MockedLoggerTrait;
use CakeTestCases\Glofox\Domain\Users\Traits\FetchUsersTrait;
use Carbon\Carbon;
use Glofox\Domain\Memberships\Services\MembershipsEventPublisher;
use Glofox\Domain\Users\Repositories\UsersRepository;

App::import('Test/Case', 'GlofoxControllerTestCase');

class SyncMembershipTest extends GlofoxControllerTestCase
{
    use FetchUsersTrait;
    use TokenGeneration;
    use MockedLoggerTrait;

    public $fixtures = [
        'app.user',
        'app.branch',
        'app.membership',
    ];
    /** @var UsersRepository */
    private $usersRepository;

    public function setUp()
    {
        parent::setUp();

        $this->usersRepository = app()->make(UsersRepository::class);
    }

    public function tearDown()
    {
        parent::tearDown();
        \Mockery::close();
    }

    public function test_it_stores_the_expected_subscription_interval(): void
    {
        $membershipsEventPublisherMock = Mockery::mock(MembershipsEventPublisher::class)
            ->shouldReceive('sendMembershipUpdatedEvent')
            ->getMock();

        app()->forgetInstance(MembershipsEventPublisher::class);
        app()->instance(MembershipsEventPublisher::class, $membershipsEventPublisherMock);

        $userMembershipIdMock = '5e61933206b8220e679c6df9';
        $membershipType = 'time';
        $branchId = '49a7011a05c677b9a916612a';
        $userId = '5a2aad49e1608aa72b0041c1';
        $timeUnit = 'month';

        $originalStateOfUser = $this->fetchUserWithoutCallbacks($userId);

        $data = [
            'id' => $userMembershipIdMock,
            'status' => 'ACTIVE',
            'duration' => [
                'value' => 0,
                'startDate' => Carbon::now()->getTimestamp(),
                'expiryDate' => Carbon::now()->addMonth()->getTimestamp(),
            ],
            'userId' => $userId,
            'membershipId' => '5b8c5bc05e196e316e076fe1',
            'subscriptionPlanId' => '5ccb13f151c78d0ae2595614',
            'subscriptionProviderId' => '34779',
            'planCode' => '1556812699737',
            'subscription' => true,
            'branchId' => $branchId,
            'commencedDate' => Carbon::now()->getTimestamp(),
            'endDate' => Carbon::now()->addMonth()->getTimestamp(),
            'purchaseDate' => Carbon::now()->getTimestamp(),
            'payg' => false,
            'membershipMetaData' => [
                'membershipName' => 'Membership Plans (Manager Sales Only)',
                'planName' => 'Challenge Special',
                'prorated' => false,
                'startsOn' => 'PURCHASE_DATE',
                'price' => 144,
                'planPrice' => 144,
                'duration' => [
                    'timeUnit' => $timeUnit,
                    'value' => 1,
                ],
                'contract' => [
                    'timeUnit' => $timeUnit,
                    'value' => 12,
                ],
                'autoRenewal' => false,
                'hasEndDate' => true,
                'type' => $membershipType,
                'upfrontFee' => 0,
                'paymentMethod' => 'CARD',
                'isRoaming' => false,
            ],
            'isTermsAndConditionsAccepted' => false,
        ];

        // Test month use case

        $options = [
            'method' => 'post',
            'data' => $data,
        ];

        $url = sprintf('/2.2/branches/%s/users/%s/sync-current-membership', $branchId, $userId);

        $response = $this->testAction($url, $options);
        $response = json_decode($response, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        self::assertTrue($response['success'], json_encode($response, JSON_PARTIAL_OUTPUT_ON_ERROR));

        $user = $this->fetchUserWithoutCallbacks($userId);

        self::assertEquals($userMembershipIdMock, $user->membership()->userMembershipId());
        self::assertEquals('month', $user->membership()->subscription()->interval());

        // Test week use case

        $data['membershipMetaData']['duration']['timeUnit'] = 'week';

        $options = [
            'method' => 'post',
            'data' => $data,
        ];

        $url = sprintf('/2.2/branches/%s/users/%s/sync-current-membership', $branchId, $userId);

        $response = $this->testAction($url, $options);
        $response = json_decode($response, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        self::assertTrue($response['success'], json_encode($response, JSON_PARTIAL_OUTPUT_ON_ERROR));

        $user = $this->fetchUserWithoutCallbacks($userId);

        self::assertEquals($userMembershipIdMock, $user->membership()->userMembershipId());
        self::assertEquals('week', $user->membership()->subscription()->interval());
        self::assertTrue($user->membership()->subscription()->has('auto_renewal'));
        self::assertFalse($user->membership()->subscription()->isAutoRenewalEnabled());

        // Test day use case

        $data['membershipMetaData']['duration']['timeUnit'] = 'day';

        $options = [
            'method' => 'post',
            'data' => $data,
        ];

        $url = sprintf('/2.2/branches/%s/users/%s/sync-current-membership', $branchId, $userId);

        $response = $this->testAction($url, $options);
        $response = json_decode($response, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        self::assertTrue($response['success'], json_encode($response, JSON_PARTIAL_OUTPUT_ON_ERROR));

        $user = $this->fetchUserWithoutCallbacks($userId);

        self::assertEquals($userMembershipIdMock, $user->membership()->userMembershipId());
        self::assertEquals('day', $user->membership()->subscription()->interval());

        // Test year use case

        $data['membershipMetaData']['duration']['timeUnit'] = 'year';

        $options = [
            'method' => 'post',
            'data' => $data,
        ];

        $url = sprintf('/2.2/branches/%s/users/%s/sync-current-membership', $branchId, $userId);

        $response = $this->testAction($url, $options);
        $response = json_decode($response, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        self::assertTrue($response['success'], json_encode($response, JSON_PARTIAL_OUTPUT_ON_ERROR));

        $user = $this->fetchUserWithoutCallbacks($userId);

        self::assertEquals($userMembershipIdMock, $user->membership()->userMembershipId());
        self::assertEquals('year', $user->membership()->subscription()->interval());

        // teardown
        $this->usersRepository->legacySaveOrFail($originalStateOfUser->toArray());

        app()->forgetInstance(MembershipsEventPublisher::class);
    }

    public function test_it_doesnt_send_sns_message_when_syncing_payg_membership(): void
    {
        // Assert it should NOT send the update event.
        $membershipsEventPublisherMock = Mockery::mock(MembershipsEventPublisher::class)
            ->shouldNotReceive('sendMembershipUpdatedEvent')
            ->getMock();

        app()->forgetInstance(MembershipsEventPublisher::class);
        app()->instance(MembershipsEventPublisher::class, $membershipsEventPublisherMock);

        $userMembershipIdMock = '5e61933206b8220e679c6df9';
        $branchId = '49a7011a05c677b9a916612a';
        $userId = '5a2aad49e1608aa72b0041c1';

        $originalStateOfUser = $this->fetchUserWithoutCallbacks($userId);

        // Got this structure straight from the logs.
        // @see https://eu-west-1.console.aws.amazon.com/cloudwatch/home?region=eu-west-1#logEventViewer:group=api-service-plat;stream=api-service-plat%2Fapi-service-plat%2F14510f62-d3bb-4dc6-bcb4-2aeab0b29462;reftime=1583452978790;refid=35312181413850105146413918067553900054581656630030434373
        $data = [
            'id' => $userMembershipIdMock,
            'status' => 'ACTIVE',
            'duration' => [
                'value' => 0,
                'startDate' => Carbon::now()->getTimestamp(),
            ],
            'userId' => $userId,
            'subscription' => false,
            'membershipId' => '5b8c5bc05e196e316e076fe1',
            'branchId' => $branchId,
            'payg' => true,
            'isTermsAndConditionsAccepted' => false,
        ];

        $options = [
            'method' => 'post',
            'data' => $data,
        ];

        $url = sprintf('/2.2/branches/%s/users/%s/sync-current-membership', $branchId, $userId);

        $response = $this->testAction($url, $options);
        $response = json_decode($response, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        self::assertTrue($response['success'], json_encode($response, JSON_PARTIAL_OUTPUT_ON_ERROR));

        $user = $this->fetchUserWithoutCallbacks($userId);

        self::assertTrue($user->membership()->isPayg());
        self::assertEquals($userMembershipIdMock, $user->membership()->userMembershipId());

        // teardown
        $this->usersRepository->legacySaveOrFail($originalStateOfUser->toArray());

        app()->forgetInstance(MembershipsEventPublisher::class);
    }

    public function test_it_sends_sns_message_when_syncing_time_classes_membership(): void
    {
        // Assert it should send the update event.
        $membershipsEventPublisherMock = Mockery::mock(MembershipsEventPublisher::class)
            ->shouldReceive('sendMembershipUpdatedEvent')
            ->once()
            ->getMock();

        app()->forgetInstance(MembershipsEventPublisher::class);
        app()->instance(MembershipsEventPublisher::class, $membershipsEventPublisherMock);

        $userMembershipIdMock = '5e61933206b8220e679c6df9';
        $membershipType = 'time';
        $branchId = '49a7011a05c677b9a916612a';
        $userId = '5a2aad49e1608aa72b0041c1';

        $originalStateOfUser = $this->fetchUserWithoutCallbacks($userId);

        // Got this structure straight from the logs.
        // @see https://eu-west-1.console.aws.amazon.com/cloudwatch/home?region=eu-west-1#logEventViewer:group=api-service-plat;stream=api-service-plat%2Fapi-service-plat%2F14510f62-d3bb-4dc6-bcb4-2aeab0b29462;reftime=1583452978790;refid=35312181413850105146413918067553900054581656630030434373
        $data = [
            'id' => $userMembershipIdMock,
            'status' => 'ACTIVE',
            'duration' => [
                'value' => 0,
                'startDate' => Carbon::now()->getTimestamp(),
                'expiryDate' => Carbon::now()->addMonth()->getTimestamp(),
            ],
            'userId' => $userId,
            'membershipId' => '5b8c5bc05e196e316e076fe1',
            'subscriptionPlanId' => '5ccb13f151c78d0ae2595614',
            'subscriptionProviderId' => '34779',
            'planCode' => '1556812699737',
            'subscription' => true,
            'branchId' => $branchId,
            'commencedDate' => Carbon::now()->getTimestamp(),
            'endDate' => Carbon::now()->addMonth()->getTimestamp(),
            'purchaseDate' => Carbon::now()->getTimestamp(),
            'payg' => false,
            'membershipMetaData' => [
                'membershipName' => 'Membership Plans (Manager Sales Only)',
                'planName' => 'Challenge Special',
                'prorated' => false,
                'startsOn' => 'PURCHASE_DATE',
                'price' => 144,
                'planPrice' => 144,
                'duration' => [
                    'timeUnit' => 'month',
                    'value' => 1,
                ],
                'contract' => [
                    'timeUnit' => 'month',
                    'value' => 12,
                ],
                'autoRenewal' => true,
                'hasEndDate' => true,
                'type' => $membershipType,
                'upfrontFee' => 0,
                'paymentMethod' => 'CARD',
                'isRoaming' => false,
            ],
            'isTermsAndConditionsAccepted' => false,
        ];

        $options = [
            'method' => 'post',
            'data' => $data,
        ];

        $url = sprintf('/2.2/branches/%s/users/%s/sync-current-membership', $branchId, $userId);

        $response = $this->testAction($url, $options);
        $response = json_decode($response, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        self::assertTrue($response['success'], json_encode($response, JSON_PARTIAL_OUTPUT_ON_ERROR));

        $user = $this->fetchUserWithoutCallbacks($userId);

        self::assertEquals($userMembershipIdMock, $user->membership()->userMembershipId());

        self::assertTrue($user->membership()->subscription()->has('auto_renewal'));
        self::assertTrue($user->membership()->subscription()->isAutoRenewalEnabled());

        // teardown
        $this->usersRepository->legacySaveOrFail($originalStateOfUser->toArray());

        app()->forgetInstance(MembershipsEventPublisher::class);
    }

    public function test_it_syncs_time_classes_membership(): void
    {
        $membershipsEventPublisherMock = Mockery::mock(MembershipsEventPublisher::class)
            ->shouldReceive('sendMembershipUpdatedEvent')
            ->getMock();

        app()->forgetInstance(MembershipsEventPublisher::class);
        app()->instance(MembershipsEventPublisher::class, $membershipsEventPublisherMock);

        $userMembershipIdMock = '5e61933206b8220e679c6df9';
        $membershipType = 'time_classes';
        $branchId = '49a7011a05c677b9a916612a';
        $userId = '5a2aad49e1608aa72b0041c1';
        $timeUnit = 'month';

        $originalStateOfUser = $this->fetchUserWithoutCallbacks($userId);

        $data = [
            'id' => $userMembershipIdMock,
            'status' => 'ACTIVE',
            'duration' => [
                'value' => 0,
                'startDate' => Carbon::now()->getTimestamp(),
                'expiryDate' => Carbon::now()->addMonth()->getTimestamp(),
            ],
            'userId' => $userId,
            'membershipId' => '5b8c5bc05e196e316e076fe1',
            'subscriptionPlanId' => '5ccb13f151c78d0ae2595614',
            'subscriptionProviderId' => '34779',
            'planCode' => '1556812699737',
            'subscription' => true,
            'branchId' => $branchId,
            'commencedDate' => Carbon::now()->getTimestamp(),
            'endDate' => Carbon::now()->addMonth()->getTimestamp(),
            'purchaseDate' => Carbon::now()->getTimestamp(),
            'payg' => false,
            'membershipMetaData' => [
                'membershipName' => 'Membership Plans (Manager Sales Only)',
                'planName' => 'Challenge Special',
                'prorated' => false,
                'startsOn' => 'PURCHASE_DATE',
                'price' => 144,
                'planPrice' => 144,
                'duration' => [
                    'timeUnit' => $timeUnit,
                    'value' => 1,
                ],
                'contract' => [
                    'timeUnit' => $timeUnit,
                    'value' => 12,
                ],
                'autoRenewal' => false,
                'hasEndDate' => true,
                'type' => $membershipType,
                'upfrontFee' => 0,
                'paymentMethod' => 'CARD',
                'isRoaming' => false,
            ],
            'isTermsAndConditionsAccepted' => false,
        ];

        // Test month use case

        $options = [
            'method' => 'post',
            'data' => $data,
        ];

        $url = sprintf('/2.2/branches/%s/users/%s/sync-current-membership', $branchId, $userId);

        $response = $this->testAction($url, $options);
        $response = json_decode($response, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        self::assertTrue($response['success'], json_encode($response, JSON_PARTIAL_OUTPUT_ON_ERROR));

        $user = $this->fetchUserWithoutCallbacks($userId);

        self::assertEquals($userMembershipIdMock, $user->membership()->userMembershipId());
        self::assertEquals('month', $user->membership()->subscription()->interval());
        self::assertEquals('time_classes', $user->membership()->type());

        // teardown
        $this->usersRepository->legacySaveOrFail($originalStateOfUser->toArray());

        app()->forgetInstance(MembershipsEventPublisher::class);
    }
}
