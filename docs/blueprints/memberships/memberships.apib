## Memberships [/2.1/branches/{branchId}/memberships?filters[trial]={trial}]

+ Parameters
    + branchId: 507f191e810c19729de860ea (string) - The identification of the branch
    + userId: 5b55e5742fc1bbef53293352 (string) - The identification of the user
    + trial: 1 (enum[number], optional) - When not used, lists everything.
        + Members
            + 0 - Lists only non-trial memberships
            + 1 - Lists only trial memberships

### Retrieve Memberships of a Branch [GET]

+ Request
    + Headers
          Authorization: Bearer {authToken}

+ Response 200 (application/json)
    + Attributes
        + success: true (boolean)
        + data (array, fixed)
            + (object)
        + meta (object)
            + pagination (Pagination)

+ Response 400
    + Attributes (Generic Error)