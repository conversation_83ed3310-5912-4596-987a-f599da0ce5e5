parameters:
  - schema:
      type: string
      pattern: '^[a-f\d]{24}$'
    name: branchId
    in: path
    required: true
post:
  tags:
    - Payroll Rates
  summary: ''
  operationId: create-payroll-report
  description: This endpoint will create a payroll report
  x-internal: true
  security:
    - coreapi_auth: [ ]
  requestBody:
    required: true
    content:
      application/json:
        schema:
          $ref: '../../requests/2.2/create_payroll_report.yml'
  responses:
    '200':
      description: OK
      content:
        text/csv:
          schema:
            type: string
    '400':
      description: Bad request
      content:
        application/json:
          schema:
            $ref: "../../schemas/error_payroll_rate.yml"