type: object
properties:
  _id:
    type: string
    pattern: '^[a-f\d]{24}$'
  event_type:
    type: string
    enum:
      - class
      - appointment
  event_id:
    type: string
    pattern: '^[a-f\d]{24}$'
  event_name:
    type: string
  rate_price:
    type: integer
    minimum: 1
    description: the price multiplied by 100. This is done to handle decimal.
  rate_group_id:
    type: string
    pattern: '^[a-f\d]{24}$'
  rate_bonus:
    type: array
    items:
      $ref: './payroll_bonus.yml'
