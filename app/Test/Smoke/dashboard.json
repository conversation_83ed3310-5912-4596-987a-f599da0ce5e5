[{"url": "/dictionaries/findByBranchId", "content-type": "application/json", "status": 200}, {"url": "/2.0/integrations", "content-type": "application/json", "status": 401}, {"url": "/2.0/members/download", "content-type": "application/json", "status": 401}, {"url": "/clients/list_client_names", "content-type": "application/json", "status": 404}, {"url": "/assets/colors", "content-type": "application/json", "status": 401}, {"url": "/assets/colors/iframe", "content-type": "application/json", "status": 401}, {"url": "/clientsImport/uploadCSV", "content-type": "application/json", "status": 200}, {"url": "/clientsImport/uploadData", "content-type": "application/json", "status": 200}, {"url": "/subscription_plans/view", "content-type": "application/json", "status": 401}, {"url": "/TermsConditions/view", "content-type": "application/json", "status": 401}, {"url": "/TimeSlotPatterns/clear_schedule", "content-type": "application/json", "status": 200}, {"url": "/TimeSlotPatterns/upsert", "content-type": "application/json", "status": 401}, {"url": "/time_slots/upsert", "content-type": "application/json", "status": 401}, {"url": "/assets/upload", "content-type": "application/json", "status": 401}, {"url": "/users/save", "content-type": "application/json", "status": 401}, {"url": "/users/totaldevices", "content-type": "application/json", "status": 401}, {"url": "/users/count", "content-type": "application/json", "status": 401}, {"url": "/users/funnel", "content-type": "application/json", "status": 404}, {"url": "/timeslots/upsert", "content-type": "application/json", "status": 404}, {"url": "/products/upsert", "content-type": "application/json", "status": 401}, {"url": "/products/purchasecount", "content-type": "application/json", "status": 401}, {"url": "/PushNotifications/count", "content-type": "application/json", "status": 401}, {"url": "/programs/upsert", "content-type": "application/json", "status": 200}, {"url": "/PushNotifications/upsert", "content-type": "application/json", "status": 401}, {"url": "/PushNotifications/sendBroadcast", "content-type": "application/json", "status": 401}, {"url": "/PushNotifications/sendToDevices", "content-type": "application/json", "status": 200}, {"url": "/PushNotifications/users", "content-type": "application/json", "status": 401}, {"url": "/2.0/pusher/health", "content-type": "application/json", "status": 200}, {"url": "/Analytics/report", "content-type": "application/json", "status": 404}, {"url": "/Reports/save", "content-type": "application/json", "status": 200}, {"url": "/Reports/delete/(id)", "content-type": "application/json", "status": 200}, {"url": "/2.0/members/download", "content-type": "application/json", "status": 401}, {"url": "/memberships/upsert", "content-type": "application/json", "status": 401}, {"url": "/facilities/count", "content-type": "application/json", "status": 401}, {"url": "/facilities/remove/(id)", "content-type": "application/json", "status": 401}, {"url": "/facilities/upsert", "content-type": "application/json", "status": 401}, {"url": "/Imports/uploadFile", "content-type": "application/json", "status": 200}, {"url": "/2.0/events/(id)/pdf", "content-type": "application/json", "status": 401}, {"url": "/accesses/listview/(start)/(finish)/(search)/(date)/(timezoneOffset)", "content-type": "application/json", "status": 401}, {"url": "/accesses/upsert", "content-type": "application/json", "status": 401}, {"url": "/accesses/remove/(entry_id)", "content-type": "application/json", "status": 401}, {"url": "/analytics/revenue?by=(type)", "content-type": "application/json", "status": 401}, {"url": "/analytics/bookings?by=(type)", "content-type": "application/json", "status": 401}, {"url": "/analytics/capacity?start=(timestamp)", "content-type": "application/json", "status": 401}, {"url": "/analytics/members", "content-type": "application/json", "status": 401}, {"url": "/analytics/events", "content-type": "application/json", "status": 401}, {"url": "/announcements/listview/(start)/(finish)/(search)/(act)", "content-type": "application/json", "status": 401}, {"url": "/announcements/count/(act)", "content-type": "application/json", "status": 401}, {"url": "/announcements/remove/(id)", "content-type": "application/json", "status": 401}, {"url": "/announcements/upsert", "content-type": "text/html", "status": 200}, {"url": "/bookings/funnel", "content-type": "application/json", "status": 401}, {"url": "/bookings/upsert", "content-type": "application/json", "status": 401}, {"url": "/booking/(id)/user/(user_id)/cancel", "content-type": "application/json", "status": 401}, {"url": "/branch/(id)/bookingrange/(start)/(end)", "content-type": "application/json", "status": 200}, {"url": "/bookings/listBookingStatus", "content-type": "application/json", "status": 200}, {"url": "/bookings/getAllByBranchIdAndUserId/(branch_id)/(user_id)/(limit)/(offset)/(load_title)", "content-type": "application/json", "status": 200}, {"url": "/bookings/process_payment", "content-type": "application/json", "status": 401}, {"url": "/bookings/getAllByModelIdentifierAndModelId/(modelIdentifier)/(modelId)", "content-type": "application/json", "status": 200}, {"url": "/branches/stripe", "content-type": "application/json", "status": 401}, {"url": "/branches/plan", "content-type": "application/json", "status": 401}, {"url": "/branches/plan", "content-type": "application/json", "status": 401}, {"url": "/branches/invoices", "content-type": "application/json", "status": 401}, {"url": "/branches/pay", "content-type": "application/json", "status": 401}, {"url": "/assets/review", "content-type": "application/json", "status": 200}, {"url": "/branches/coupon/(code)", "content-type": "application/json", "status": 401}, {"url": "/branches/getBranchesListByNamespace/(namespace)", "content-type": "application/json", "status": 200}, {"url": "/branches/list_branches_by_user", "content-type": "application/json", "status": 401}, {"url": "/branches/findById/(branch_id)", "content-type": "application/json", "status": 200}, {"url": "/branches/getPublishableKeyByProvider", "content-type": "application/json", "status": 200}, {"url": "/branches/upsert", "content-type": "application/json", "status": 401}, {"url": "/branches/list_branches", "content-type": "application/json", "status": 200}, {"url": "/bookings/hasBookings", "content-type": "application/json", "status": 200}, {"url": "/time_slots/hasTimeSlots", "content-type": "application/json", "status": 200}, {"url": "/push_notifications/hasSentNotifications", "content-type": "application/json", "status": 200}, {"url": "/reports/hasReports", "content-type": "application/json", "status": 200}, {"url": "/users/hasMembers", "content-type": "application/json", "status": 200}, {"url": "/users/hasMembershipsAssigned", "content-type": "application/json", "status": 200}, {"url": "/branches/reset_all_users_strikes/", "content-type": "application/json", "status": 200}, {"url": "/branches/syncMailChimp", "content-type": "application/json", "status": 401}, {"url": "/categories/findByModel/(model)", "content-type": "application/json", "status": 200}, {"url": "/categories/view/(id)", "content-type": "application/json", "status": 401}, {"url": "/clients/list_clients/(bundle)/visible", "content-type": "application/json", "status": 200}, {"url": "/assets/mobile/view", "content-type": "application/json", "status": 401}, {"url": "/clients/findByNamespace/(namespace)", "content-type": "text/html", "status": 200}, {"url": "/clients/activate/(code)", "content-type": "text/html", "status": 200}, {"url": "/clients/upsert", "content-type": "application/json", "status": 401}, {"url": "/clients/getAllBundles", "content-type": "text/html", "status": 200}, {"url": "/clients/getPreClientById/(id)", "content-type": "text/html", "status": 200}, {"url": "/course/(id)/session/(uid)/user/(id)/price/(price)/payment_method/(paymentMethod)/book_admin", "content-type": "application/json", "status": 401}, {"url": "/courses/listview/(start)/(finish)/(search)/(act)", "content-type": "application/json", "status": 401}, {"url": "/courses/findByCourseId/(course_id)", "content-type": "application/json", "status": 200}, {"url": "/courses/count/(act)", "content-type": "application/json", "status": 401}, {"url": "/courses/upsert", "content-type": "application/json", "status": 401}, {"url": "/courses/remove/(id)", "content-type": "application/json", "status": 401}, {"url": "/bookings/findCourseRevenue/(course_id)/(absolute_sessionid)", "content-type": "application/json", "status": 200}, {"url": "/branch/(branch_id)/user/(user_id)/credits", "content-type": "application/json", "status": 200}, {"url": "/user_credits/saveBatch/(user_id)", "content-type": "application/json", "status": 200}, {"url": "/2.0/credits?user_id=(userId)", "content-type": "application/json", "status": 401}, {"url": "/dictionaries/findByLanguage/(lang)", "content-type": "application/json", "status": 200}, {"url": "/dictionaries/findByBranchId/(branch_id)", "content-type": "application/json", "status": 200}, {"url": "/dictionaries/listLanguages", "content-type": "application/json", "status": 200}, {"url": "/dictionaries/udpateWord", "content-type": "application/json", "status": 200}, {"url": "/events/upsert", "content-type": "application/json", "status": 200}, {"url": "/events/attendance/(id)", "content-type": "application/json", "status": 401}, {"url": "/events/remove/(id)", "content-type": "application/json", "status": 401}, {"url": "/branch/(id)/eventsrange/(start)/(end)", "content-type": "application/json", "status": 401}, {"url": "/events/findNextEvents/(branch_id)/(limit)", "content-type": "application/json", "status": 200}, {"url": "/bookings/findClassRevenue/(event_id)/(program_id)", "content-type": "application/json", "status": 200}, {"url": "/2.0/events/(event_id)", "content-type": "application/json", "status": 401}, {"url": "/facilities/listview/(start)/(finish)/(search)/(act)", "content-type": "application/json", "status": 401}, {"url": "/Imports/getImportedFile/(folder)/(file)/(fileType)", "content-type": "application/json", "status": 200}, {"url": "/Imports/importFromMindBody", "content-type": "application/json", "status": 200}, {"url": "/branches/pay", "content-type": "application/json", "status": 401}, {"url": "/users/count/member/(act)", "content-type": "application/json", "status": 401}, {"url": "/userCredits/findAllByBranchIdAndUserId/(id)/(id)", "content-type": "application/json", "status": 200}, {"url": "/users/findUserByBranchIdBasicInfo/(branch_id)/(limit)/(offset)", "content-type": "application/json", "status": 200}, {"url": "/users/findById/(id)/(namespace)/(branch_id)", "content-type": "application/json", "status": 200}, {"url": "/users/range/(branch_id)", "content-type": "application/json", "status": 200}, {"url": "/users/cancel_subscription_admin/(member_id)", "content-type": "application/json", "status": 401}, {"url": "/users/revert_to_payg/(branch_id)/(member_id)/true", "content-type": "application/json", "status": 200}, {"url": "/users/pause_subscription_admin/(member_id)/(pause_until)", "content-type": "application/json", "status": 401}, {"url": "/users/saveCustomQuestionAnswers/(member_id)", "content-type": "application/json", "status": 401}, {"url": "/users/update_membership_expiry_date/(member_id)/(new_date)", "content-type": "application/json", "status": 401}, {"url": "/users/buy_membership_admin/(id)/(id)/(time)", "content-type": "application/json", "status": 401}, {"url": "/memberships/remove/(id)", "content-type": "application/json", "status": 401}, {"url": "/memberships/listview/(start)/(finish)/(search)/(act)", "content-type": "application/json", "status": 401}, {"url": "/memberships/count/(act)", "content-type": "application/json", "status": 401}, {"url": "/memberships/findAllByBranchId/(branch_id)", "content-type": "application/json", "status": 200}, {"url": "/products/listview/(start)/(finish)/(search)/(act)", "content-type": "application/json", "status": 401}, {"url": "/products/count/(act)", "content-type": "application/json", "status": 401}, {"url": "/products/buyAdmin/(id)/(id)/(id)/(price)/(payment_method)/(quantity)", "content-type": "application/json", "status": 401}, {"url": "/programs/remove/(id)", "content-type": "application/json", "status": 401}, {"url": "/programs/view/(id)", "content-type": "application/json", "status": 401}, {"url": "/programs/count/(act)", "content-type": "application/json", "status": 401}, {"url": "/programs/listview/(start)/(finish)/(search)/(act)", "content-type": "application/json", "status": 401}, {"url": "/programs/findByBranchId/(branch_id)/(user_id)/(fetch_images)/(fetch_credits)", "content-type": "application/json", "status": 200}, {"url": "/products/collect/(purchase_code)", "content-type": "application/json", "status": 401}, {"url": "/PushNotifications/view/null/(date)", "content-type": "application/json", "status": 401}, {"url": "/PushNotifications/remove/(id)", "content-type": "application/json", "status": 401}, {"url": "/PushNotifications/send/(id)", "content-type": "application/json", "status": 401}, {"url": "/PushNotifications/sendGroupMessage/(id)", "content-type": "application/json", "status": 401}, {"url": "/PushNotifications/devices/(path)", "content-type": "application/json", "status": 401}, {"url": "/Reports/getAllByBranchId/(branch_id)/(namespace)", "content-type": "application/json", "status": 200}, {"url": "/stripe_charges/findByBranchIdAndUserId/(branch_id)/(user_id)/(limit)/(offset)", "content-type": "application/json", "status": 200}, {"url": "/stripe_charges/refund/(stripe_charge_id)", "content-type": "application/json", "status": 200}, {"url": "/stripe_charges/retry/(user_id)/(invoice_id)", "content-type": "application/json", "status": 401}, {"url": "/TimeSlotPatterns/findByBranchId/(id)", "content-type": "application/json", "status": 200}, {"url": "/TimeSlotPatterns/findEntityByBranchIdModelAndModelId/(branch_id)/(model)/(model_id)", "content-type": "application/json", "status": 200}, {"url": "/bookings/time_slot_add_web/(id)/(user_id)/paymentMethod/(price)", "content-type": "application/json", "status": 401}, {"url": "/branch/(id)/slotsrange/(start)/(end)", "content-type": "application/json", "status": 401}, {"url": "/time_slots/unblock_time_slot/(namesapce)/(branch_id)/(time_slot_id)", "content-type": "application/json", "status": 200}, {"url": "/time_slots/remove/(time_slot_id)", "content-type": "application/json", "status": 401}, {"url": "/users/listview/(start)/(finish)/(search)/staff/(act)", "content-type": "application/json", "status": 401}, {"url": "/users/count/staff/(act)", "content-type": "application/json", "status": 401}, {"url": "/users/listview/(start)/(finish)/(search)", "content-type": "application/json", "status": 401}, {"url": "/users/findUserByBranchIdBasicInfo/(branch_id)/(limit)/(offset)", "content-type": "application/json", "status": 200}, {"url": "/accesses/remove/(entry_id)", "content-type": "application/json", "status": 401}, {"url": "/users/findById/(id)/(namespace)/(branch_id)", "content-type": "application/json", "status": 200}, {"url": "/users/view/(code)/(field)", "content-type": "application/json", "status": 401}, {"url": "/users/delete_member_service/(user_id)/(namespace)/(branch_id)", "content-type": "application/json", "status": 404}, {"url": "/users/range/(branch_id)", "content-type": "application/json", "status": 200}, {"url": "/users/findUserCountByBranchId/(branch_id)", "content-type": "application/json", "status": 200}, {"url": "/users/assignBarcode/(user_id)", "content-type": "application/json", "status": 401}, {"url": "/users/delete_credit_card/(branch_id)/(user_id)", "content-type": "application/json", "status": 200}, {"url": "/users/customCharge/(user_id)", "content-type": "application/json", "status": 401}, {"url": "/users/get_all_trainers/(namespace)/(branch_id)/", "content-type": "application/json", "status": 200}, {"url": "/users/get_all_members/(namespace)/(branch_id)/", "content-type": "application/json", "status": 200}, {"url": "/users/switch_branch/(branchId)", "content-type": "application/json", "status": 401}, {"url": "/assets/remove/(code)", "content-type": "application/json", "status": 401}, {"url": "/users/request_admin_token/(namespace)/(branch_id)", "content-type": "application/json", "status": 200}, {"url": "/2.0/integrations/(integrationId)", "content-type": "application/json", "status": 401}, {"url": "/2.0 /branches/(id)/payments/connect", "content-type": "text/html", "status": 400}, {"url": "/stripe_charges/refund/(stripeChargeId)", "content-type": "application/json", "status": 200}, {"url": "/2.0/leads/summary", "content-type": "application/json", "status": 404}, {"url": "/2.0/leads/search", "content-type": "application/json", "status": 404}, {"url": "/2.0/members", "content-type": "application/json", "status": 401}, {"url": "/2.0/interactions/search", "content-type": "application/json", "status": 404}, {"url": "/2.0/interactions", "content-type": "application/json", "status": 404}, {"url": "/2.0/members", "content-type": "application/json", "status": 401}, {"url": "/2.0/activities", "content-type": "application/json", "status": 401}, {"url": "/2.0/programs", "content-type": "application/json", "status": 401}, {"url": "/2.0/events", "content-type": "application/json", "status": 401}, {"url": "/2.0/courses", "content-type": "application/json", "status": 401}, {"url": "/2.0/memberships", "content-type": "application/json", "status": 401}, {"url": "/2.0/staff", "content-type": "application/json", "status": 401}, {"url": "/2.0/facilities", "content-type": "application/json", "status": 401}, {"url": "/2.0/analytics/revenue", "content-type": "application/json", "status": 401}, {"url": "/2.0/analytics/revenue/download", "content-type": "application/json", "status": 401}, {"url": "/2.0/payouts", "content-type": "application/json", "status": 401}, {"url": "/2.0/payouts", "content-type": "application/json", "status": 401}, {"url": "/2.0/bookings/first", "content-type": "application/json", "status": 401}, {"url": "/2.0/bookings/late_cancellations", "content-type": "application/json", "status": 401}, {"url": "/2.0/bookings/no_shows", "content-type": "application/json", "status": 401}, {"url": "/2.0/members/expiring", "content-type": "application/json", "status": 401}, {"url": "/2.0/members/active", "content-type": "application/json", "status": 401}, {"url": "/2.0/access/report", "content-type": "application/json", "status": 401}, {"url": "/2.0/analytics/trainer-performance", "content-type": "application/json", "status": 401}, {"url": "/2.0/analytics/class-performance", "content-type": "application/json", "status": 401}, {"url": "/2.0/invoices", "content-type": "application/json", "status": 401}, {"url": "/2.0/invoices/generate", "content-type": "application/json", "status": 404}, {"url": "/2.0/messages/templates", "content-type": "application/json", "status": 401}, {"url": "/2.0/messages/upsert", "content-type": "application/json", "status": 401}, {"url": "/2.0/register", "content-type": "application/json", "status": 404}, {"url": "/2.0/login", "content-type": "application/json", "status": 400}, {"url": "/2.0/reset", "content-type": "application/json", "status": 404}, {"url": "/2.0/branches/(branchId)", "content-type": "application/json", "status": 401}, {"url": "/2.0/dictionaries?branch_id=(branchId)", "content-type": "application/json", "status": 401}, {"url": "/2.0/waivers?branch_id=(branchId)", "content-type": "application/json", "status": 401}, {"url": "/2.0/members/(userId)", "content-type": "application/json", "status": 401}, {"url": "/2.0/members/(userId)", "content-type": "application/json", "status": 401}, {"url": "/2.0/members/(userId)/card", "content-type": "application/json", "status": 401}, {"url": "/2.0/credits?user_id=(userId)", "content-type": "application/json", "status": 401}, {"url": "/2.0/bookings?user_id=(userId)", "content-type": "application/json", "status": 401}, {"url": "/2.0/charges?user_id=(userId)", "content-type": "application/json", "status": 401}, {"url": "/2.0/bookings", "content-type": "application/json", "status": 401}, {"url": "/2.0/bookings/(bookingId)", "content-type": "application/json", "status": 401}, {"url": "/2.0/programs", "content-type": "application/json", "status": 401}, {"url": "/2.0/events", "content-type": "application/json", "status": 401}, {"url": "/2.0/courses", "content-type": "application/json", "status": 401}, {"url": "/2.0/memberships", "content-type": "application/json", "status": 401}, {"url": "/2.0/memberships/(membershipId)", "content-type": "application/json", "status": 401}, {"url": "/2.0/memberships/(membershipId)?user_id=(userId)", "content-type": "application/json", "status": 401}, {"url": "/2.0/memberships/(membershipId)/purchase", "content-type": "application/json", "status": 404}, {"url": "/2.0/staff", "content-type": "application/json", "status": 401}, {"url": "/2.0/staff/(staffId)", "content-type": "application/json", "status": 401}, {"url": "/2.0/facilities", "content-type": "application/json", "status": 401}, {"url": "/2.0/facilities/(facilityId)", "content-type": "application/json", "status": 401}, {"url": "/2.0/messages/preview", "content-type": "application/json", "status": 200}, {"url": "/2.0/payments/verification-fields", "content-type": "application/json", "status": 404}, {"url": "/clients", "content-type": "text/html", "status": 200}, {"url": "/users/resetpwd", "content-type": "application/json", "status": 404}, {"url": "/clients/create", "content-type": "application/json", "status": 200}, {"url": "/clients/bundles", "content-type": "application/json", "status": 401}, {"url": "/2.1/branches/(branchId)/leads", "content-type": "application/json", "status": 401}, {"url": "/2.1/branches/(branchId)/leads/(userId)/interactions", "content-type": "application/json", "status": 401}, {"url": "/2.1/branches/(branchId)/interactions/types", "content-type": "application/json", "status": 401}, {"url": "/2.1/branches/(branchId)/memberships", "content-type": "application/json", "status": 401}, {"url": "/2.1/branches/(branchId)/users/(userId)/switchTo=(targeBranchId)", "content-type": "application/json", "status": 200}]