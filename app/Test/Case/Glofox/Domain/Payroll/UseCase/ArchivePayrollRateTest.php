<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\Payroll\UseCase;

use App;
use Carbon\Carbon;
use Glofox\Domain\Payroll\Models\PayrollRate;
use Glofox\Domain\Payroll\Repositories\PayrollRatesRepository;
use Glofox\Domain\Payroll\UseCase\ArchivePayrollRate;
use Glofox\Domain\Payroll\UseCase\ArchivePayrollRateParams;
use GlofoxTestCase;
use Mockery;
use MongoDate;

App::import('Test/Case', 'GlofoxTestCase');

class ArchivePayrollRateTest extends GlofoxTestCase
{
    private const BRANCH_ID = '64b803ccfc8ba4d6f8d99c9c';
    private const ADMIN_ID = '64b804217388a46d30844c1d';

    public $fixtures = [];

    private PayrollRatesRepository $payrollRatesRepository;

    public function setUp()
    {
        parent::setUp();

        $this->payrollRatesRepository = Mockery::mock(PayrollRatesRepository::class);
    }

    public function tearDown()
    {
        parent::tearDown();
        Mockery::close();
    }

    public function testExecute(): void
    {
        $payrollRateId = '64e5de884ad77bcc9887df9a';
        $rateToArchive = $this->getPayrollRateMock($payrollRateId);
        $params = new ArchivePayrollRateParams($payrollRateId, self::ADMIN_ID);

        $this->payrollRatesRepository
            ->shouldReceive('getById')
            ->withArgs(fn($rateId) => $rateId === $payrollRateId)
            ->once()
            ->andReturn($rateToArchive)
            ->getMock()
            ->shouldReceive('archive')
            ->withArgs(fn($rateId, $rateDateFinish) => $rateId === $payrollRateId
                && $rateDateFinish === Carbon::yesterday('UTC')->endOfDay()->getTimestamp())
            ->once()
            ->getMock();

        $useCase = new ArchivePayrollRate($this->payrollRatesRepository);

        $useCase->execute($params);
    }

    public function testItThrowsAnExceptionWhenTryingToArchiveAlreadyArchivedRate(): void
    {
        $payrollRateId = '64e5de96e498759a4449fbc5';
        $rateToArchive = $this->getPayrollRateMock($payrollRateId);
        $params = new ArchivePayrollRateParams($payrollRateId, self::ADMIN_ID);

        $this->payrollRatesRepository
            ->shouldReceive('getById')
            ->withArgs(fn($rateId) => $rateId === $payrollRateId)
            ->once()
            ->andReturn($rateToArchive)
            ->getMock()
            ->shouldReceive('archive')
            ->never();

        $this->expectExceptionMessage('Payroll rate has been already archived.');

        $useCase = new ArchivePayrollRate($this->payrollRatesRepository);
        $useCase->execute($params);
    }

    private function getPayrollRateMock(string $id): PayrollRate
    {
        $payrollRates = [
            '64e5de884ad77bcc9887df9a' => PayrollRate::make([
                '_id' => '64e5de884ad77bcc9887df9a',
                'branch_id' => self::BRANCH_ID,
                'type' => 'base',
                'model' => 'programs',
                'model_id' => '64b7ec83ca6e0791480419d3',
                'value' => 20,
                'start_date' => new MongoDate(strtotime('2023-07-01 00:00:00')),
                'end_date' => null,
                'created' => new MongoDate(time()),
                'created_by' => self::ADMIN_ID,
            ]),
            '64e5de96e498759a4449fbc5' => PayrollRate::make([
                '_id' => '64e5de96e498759a4449fbc5',
                'branch_id' => self::BRANCH_ID,
                'type' => 'base',
                'model' => 'programs',
                'model_id' => '64b7ec83ca6e0791480419d3',
                'value' => 20,
                'start_date' => new MongoDate(strtotime('2023-07-01 00:00:00')),
                'end_date' => new MongoDate(strtotime('2023-08-31 00:00:00')),
                'created' => new MongoDate(strtotime('2023-08-01 00:00:00')),
                'created_by' => self::ADMIN_ID,
            ]),
        ];

        return $payrollRates[$id];
    }
}
