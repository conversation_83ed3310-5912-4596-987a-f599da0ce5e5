<?php


namespace CakeTestCases\Glofox\Payments\Providers\Gateway\ProtoParsers;

use Glofox\Payments\Providers\Gateway\ProtoParsers\CapabilityParser;
use GRPC\Payments\AccountService\AccountCapability;

\App::import('Test/Case', 'GlofoxTestCase');

class CapabilityParserTest extends \GlofoxTestCase
{

    public function test_it_parses_capability(): void
    {
        $capability = new AccountCapability();
        $capability->setAccountID(1);
        $capability->setCapability("name");
        $capability->setEnabled(true);

        /** @var CapabilityParser $parser */
        $parser = app()->make(CapabilityParser::class);
        $parsed = $parser->parseFromProtoMessage($capability);

        self::assertEquals('1', $parsed->getAccountId());
        self::assertEquals('name', $parsed->getCapability());
        self::assertEquals(true, $parsed->isEnabled());
    }

}