type: object
properties:
  branch_id:
    type: string
    description: The branch_id of the studio.
  model:
    type: string
    description: For class bookings set to events.
  model_id:
    type: string
    description: The id of the event the booking is being made for.
  user_id:
    type: string
    description: The id of the user the booking is being made for.
  payment_method:
    type: string
    description: the payment method being used.
  price:
    type: number
    description: The id of the user the booking is being made for.
    example: 123
  create_slot:
    type: string
    description: Set this string if you want to create a slot during the checkout.
    example: "appointment"
    enum:
      - appointment
  time_start:
    type: integer
    description: The timestamp start time for the new slot in UTC.
  staff_id:
    type: string
    description: The id of the staff the booking is being made for.
  private:
    type: boolean
    description: If the slot is private or not
  size:
    type: integer
    maximum: 5
    default: 1
    description: This will set the capacity of the appointment to a maximum of five. If this field is not sent, the
      default value of one will be used.
