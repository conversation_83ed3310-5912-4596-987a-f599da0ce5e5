<?php

declare(strict_types=1);

namespace Glofox\Domain\AppointmentSlots\Http\Responses;

use Glofox\Http\Responses\JsonResponseInterface;
use Illuminate\Http\JsonResponse;

class CreateAppointmentSlotResponse implements JsonResponseInterface
{
    private array $data;

    public function __construct(array $data)
    {
        $this->data = $this->transform($data);
    }

    public function toJsonResponse(): JsonResponse
    {
        return response()->json($this->data, 201);
    }

    private function transform(array $data): array
    {
        return $this->formatDateFields($data);
    }

    private function formatDateFields(array $response): array
    {
        $dateFields = ['time_start', 'time_finish', 'date'];

        foreach ($dateFields as $field) {
            if (!isset($response[$field])) {
                continue;
            }

            $response[$field] = $this->convertDateToTimestamp($response[$field]);
        }

        return $response;
    }

    private function convertDateToTimestamp($dateValue)
    {
        if ($dateValue instanceof \MongoDate) {
            return $dateValue->sec;
        }

        if ($dateValue instanceof \MongoDB\BSON\UTCDateTime) {
            return $dateValue->toDateTime()->getTimestamp();
        }

        return $dateValue;
    }
}