<?php

namespace CakeTestCases\Glofox\Domain\Memberships\Services;

use Glofox\Domain\Branches\Models\Branch;
use Glofox\Domain\Branches\Repositories\BranchesRepository;
use Glofox\Domain\Memberships\Exceptions\DuplicateMembershipPlanCode;
use Glofox\Domain\Memberships\Models\AcceptedPaymentMethod;
use Glofox\Domain\Memberships\Models\Membership;
use Glofox\Domain\Memberships\Models\Plan;
use Glofox\Domain\Memberships\Repositories\MembershipsRepository;
use Glofox\Domain\Memberships\Services\MembershipUpsertService;
use Glofox\Domain\Memberships\Services\SubscriptionPlanUpsertService;
use Glofox\Domain\Memberships\Validation\PlanMinimumPriceValidator;
use Glofox\Domain\Memberships\Validation\Validator;
use Glofox\Domain\PaymentMethods\Models\PaymentMethod;
use Glofox\Domain\PaymentMethods\Type;
use Glofox\Payments\Contracts\PaymentProviderContract;
use Glofox\Payments\PaymentsHandler;
use Glofox\Repositories\Search\Expressions\Shared\Id;
use Illuminate\Support\Collection;
use Illuminate\Validation\ValidationException;
use Mockery;
use MongoId;

\App::import('Test/Case', 'GlofoxTestCase');

class MembershipUpsertServiceTest extends \GlofoxTestCase
{
    public function tearDown()
    {
        parent::tearDown();
        app()->forgetInstance(PaymentsHandler::class);
        \Mockery::close();
    }

    public function test_it_should_throw_a_validation_exception_when_invalid_payload_is_present()
    {
        /** @var MembershipUpsertService $service */
        $service = app()->make(MembershipUpsertService::class);

        $this->setExpectedException(ValidationException::class);

        // empty payload - missing fields
        $service->execute(collect([
            'branch_id' => '49a7011a05c677b9a916612a',
        ]));
    }

    public function test_it_should_save_a_plan_with_two_payment_methods_with_card_having_priority()
    {
        app()->forgetInstance(MembershipsRepository::class);
        app()->forgetInstance(Validator::class);
        app()->forgetInstance(BranchesRepository::class);
        app()->forgetInstance(MembershipUpsertService::class);

        /** @var Membership $membership */
        $membership = app()->make(MembershipsRepository::class)
                            ->addCriteria(new Id('54107c1cd7b6ddc3a98b4576'))
                            ->firstOrFail();

        $originalNamespace = $membership->get('namespace');
        $membership->forget('namespace');

        // to restore after
        $oldPlans = $membership->plans();

        $plan = $membership->plans()->first();
        $plan->put('subscription_plan_id', true);

        $membership->put('plans', new Collection([$plan]));

        $subscriptionPlanUpsertService = \Mockery::mock(SubscriptionPlanUpsertService::class);

        $this->mockPaymentHandler(Type::CARD);

        $subscriptionPlanUpsertService->shouldReceive('execute')
            ->andReturnUsing(function (Branch $branch, PaymentProviderContract $paymentHandler, Plan $plan) {
                if (Type::CARD == $paymentHandler->paymentMethod()->typeId()) {
                    $plan->put('subscription_plan_id', 'sub-plan-id-card-priority');

                    return $plan;
                }
                $plan->put('subscription_plan_id', 'sub-plan-id-non-card-priority');

                return $plan;
            });

        $membershipValidator = app()->make(Validator::class);
        $branchesRepository = app()->make(BranchesRepository::class);

        $service = new MembershipUpsertService($membershipValidator, $subscriptionPlanUpsertService, $branchesRepository);

        $membership = $service->execute($membership);

        $this->assertEquals('sub-plan-id-card-priority', $membership->plans()->first()->get('subscription_plan_id'));
        self::assertEquals('test', $membership->get('namespace'));

        // restore the data
        $membership->put('namespace', $originalNamespace);
        $membership->put('plans', $oldPlans);
        \ClassRegistry::init('Membership')->saveOrFail($membership->toArray());
    }

    public function test_it_should_save_a_plan_with_two_payment_methods_with_direct_debit_having_priority()
    {
        app()->forgetInstance(MembershipsRepository::class);
        app()->forgetInstance(Validator::class);
        app()->forgetInstance(BranchesRepository::class);
        app()->forgetInstance(MembershipUpsertService::class);

        /** @var Membership $membership */
        $membership = app()->make(MembershipsRepository::class)
            ->addCriteria(new Id('54107c1cd7b6ddc3a98b4576'))
            ->firstOrFail();

        // to restore after
        $oldPlans = $membership->plans();

        $plan = $membership->plans()->first();

        $plan->put('subscription_plan_id', true);

        $accepted = new Collection();

        $accepted->push(AcceptedPaymentMethod::make([
                'type_id' => Type::DIRECT_DEBIT,
                'active' => true,
        ]));

        $plan->put('accepted_payment_methods', $accepted);

        $membership->put('plans', new Collection([$plan]));

        $subscriptionPlanUpsertService = \Mockery::mock(SubscriptionPlanUpsertService::class);

        $this->mockPaymentHandler(Type::DIRECT_DEBIT);

        $subscriptionPlanUpsertService->shouldReceive('execute')
            ->andReturnUsing(function (Branch $branch, PaymentProviderContract $paymentHandler, Plan $plan) {
                if (Type::CARD == $paymentHandler->paymentMethod()->typeId()) {
                    $plan->put('subscription_plan_id', 'sub-plan-id-card-priority');

                    return $plan;
                }
                $plan->put('subscription_plan_id', 'sub-plan-id-non-card-priority');

                return $plan;
            });

        $membershipValidator = app()->make(Validator::class);
        $branchesRepository = app()->make(BranchesRepository::class);

        $service = new MembershipUpsertService($membershipValidator, $subscriptionPlanUpsertService, $branchesRepository);

        $membership = $service->execute($membership);

        $this->assertEquals('sub-plan-id-non-card-priority', $membership->plans()->first()->get('subscription_plan_id'));

        // restore the data
        $membership->put('plans', $oldPlans);
        \ClassRegistry::init('Membership')->saveOrFail($membership->toArray());
    }

    public function test_it_should_keep_subscription_fields_for_recurring_plans_and_remove_them_for_non_recurring()
    {
        /** @var Membership $membership */
        $membership = app()->make(MembershipsRepository::class)
            ->addCriteria(new Id('5b8c5bc05e195e316e074ff4'))
            ->firstOrFail();

        // Fetching the existing plans so we can teardown this test.
        $oldPlans = $membership->plans();

        $acceptedPaymentMethods = collect()->push(AcceptedPaymentMethod::make([
            'type_id' => Type::DIRECT_DEBIT,
            'active' => true,
        ]));

        $plans = $membership->plans();

        // We'll give these plans accepted payment methods. This memberships has 2 plans.
        $plans->each(function (Plan $plan) use ($acceptedPaymentMethods) {
            $plan->put('accepted_payment_methods', $acceptedPaymentMethods);
        });

        // We'll set the first one as a subscription. So in this membership, there'll be
        // one plan as subscription and another as a simple plan.
        $plans->first()->put('subscription_plan_id', true);

        // We'll reassign the modified plans to the membership.
        $membership->put('plans', $plans);

        // We'll need to mock the plan upsert service to ensure we're not testing external
        // services logic during this test.
        $subscriptionPlanUpsertService = \Mockery::mock(SubscriptionPlanUpsertService::class);
        $subscriptionPlanUpsertService->shouldReceive('execute')
            ->andReturnUsing(function (Branch $branch, PaymentProviderContract $paymentHandler, Plan $plan) {
                $plan->put('subscription_plan_id', 'sub_123');

                return $plan;
            });

        $membershipValidator = \Mockery::mock(Validator::class);
        $membershipValidator->shouldReceive('withData')->andReturn($membershipValidator);
        $membershipValidator->expects('validate');

        $branchesRepository = app()->make(BranchesRepository::class);

        $service = new MembershipUpsertService($membershipValidator, $subscriptionPlanUpsertService, $branchesRepository);
        $membership = $service->execute($membership);

        $subscriptionId = $membership->plans()->first()->get('subscription_plan_id');

        // We'll assert that the first plan, which was the one we set as a subscription in the very beginning,
        // got his plan_id set up.
        self::assertEquals('sub_123', $subscriptionId);
        $secondPlan = $membership->plans()->get(1);

        // We'll assert that the second plan, which was the one we set as a normal membership (not subscription),
        // doesn't contain any subscription-related field.
        self::assertFalse($secondPlan->has('subscription_plan_id'));
        self::assertFalse($secondPlan->has('_subscription_plan_id'));
        self::assertFalse($secondPlan->has('subscription_amount'));

        // Teardown
        $membership->put('plans', $oldPlans);
        \ClassRegistry::init('Membership')->saveOrFail($membership->toArray());
    }

    public function test_it_update_num_class_plans_as_non_recurring_despite_of_subscription_id()
    {
        /** @var Membership $membership */
        $membership = app()->make(MembershipsRepository::class)
            ->addCriteria(new Id('5b8eb03b10e2c82a4d443f9c'))
            ->firstOrFail();

        // Fetching the existing plans so we can teardown this test.
        $oldPlans = $membership->plans();

        $acceptedPaymentMethods = collect()->push(AcceptedPaymentMethod::make([
            'type_id' => Type::DIRECT_DEBIT,
            'active' => true,
        ]));

        $plans = $membership->plans();

        // We'll give these plans accepted payment methods. This memberships has 2 plans.
        $plans->each(function (Plan $plan) use ($acceptedPaymentMethods) {
            $plan->put('accepted_payment_methods', $acceptedPaymentMethods);
        });

        // @see https://glofox.slack.com/archives/CJ5HUQEEA/p1559136098064500
        // We'll set the first one as a subscription, even though it's a num_class membership.
        // This is to test whether the application has the ability to overcome data
        // issues and rely on the "type" of a plan to make a decision
        // of whether this plan is a subscription or not.
        $plans->first()->put('subscription_plan_id', true);

        // We'll reassign the modified plans to the membership.
        $membership->put('plans', $plans);

        $membershipValidator = \Mockery::mock(Validator::class);
        $membershipValidator->shouldReceive('withData')->andReturn($membershipValidator);
        $membershipValidator->expects('validate');

        $subscriptionPlanUpsertService = \Mockery::mock(SubscriptionPlanUpsertService::class);
        $branchesRepository = app()->make(BranchesRepository::class);

        $service = new MembershipUpsertService($membershipValidator, $subscriptionPlanUpsertService, $branchesRepository);
        $membership = $service->execute($membership);

        $plan = $membership->plans()->first();

        // We'll assert that the second plan, which was the one we set as a normal membership (not subscription),
        // doesn't contain any subscription-related field.
        self::assertFalse($plan->has('subscription_plan_id'));
        self::assertFalse($plan->has('_subscription_plan_id'));
        self::assertFalse($plan->has('subscription_amount'));

        // Teardown
        $membership->put('plans', $oldPlans);
        \ClassRegistry::init('Membership')->saveOrFail($membership->toArray());
    }

    public function test_new_membership_added_adds_min_price_as_0()
    {
        $branchId = (string) new MongoId();

        $membership =
            Membership::make([
                'branch_id' => $branchId,
                'plans' => [
                    Plan::make([
                        'code' => '123',
                        'type' => 'time',
                    ]),
                    Plan::make([
                        'code' => '456',
                        'type' => 'time',
                    ]),
                ],
            ]);

        $subscriptionPlanUpsertService = Mockery::mock(SubscriptionPlanUpsertService::class);
        $branchesRepository = Mockery::mock(BranchesRepository::class);
        $branchesRepository
            ->shouldReceive('addCriteria')
            ->once()
            ->andReturnSelf()
            ->shouldReceive('firstOrFail')
            ->andReturn(
                new Branch(['_id' => $branchId, 'namespace' => 'foo'])
            )
            ->once();

        $membershipValidator = Mockery::mock(Validator::class);
        $membershipValidator
            ->shouldReceive('withData')
            ->andReturn($membershipValidator);
        $membershipValidator->expects('validate');

        $membershipRepository = Mockery::mock(MembershipsRepository::class);
        $membershipRepository
            ->shouldReceive('saveOrFail')
            ->withArgs(function (Membership $membership) {
                self::assertArrayNotHasKey('min_price', $membership['plans']['0']);
                self::assertArrayNotHasKey('min_price', $membership['plans']['1']);

                return true;
            })
            ->andReturn(
                new Membership($membership->toLegacy())
            )
            ->once()
            ->shouldReceive('saveOrFail')
            ->withArgs(function (Membership $membership) {
                self::assertArrayHasKey('min_price', $membership['plans']['0']);
                self::assertArrayHasKey('min_price', $membership['plans']['1']);

                return true;
            })
            ->andReturn(
                new Membership($membership->toLegacy())
            )
            ->once();

        app()->instance(MembershipsRepository::class, $membershipRepository);

        $service = new MembershipUpsertService($membershipValidator, $subscriptionPlanUpsertService,
            $branchesRepository);

        $service->execute($membership);

        app()->forgetInstance(MembershipsRepository::class);
    }

    public function test_sending_a_new_plan_to_add_min_price_sets_price_as_0()
    {
        $branchId = (string) new MongoId();
        $membershipId = (string) new MongoId();

        $membership =
            Membership::make([
                '_id' => $membershipId,
                'branch_id' => $branchId,
                'plans' => [
                    Plan::make([
                        'code' => '123',
                        'type' => 'time',
                        'price' => 10,
                        'min_price' => 0,
                    ]),
                ],
            ]);

        $request = new Membership($membership);
        $request->set('plans',
            [
                Plan::make([
                    'code' => '123',
                    'type' => 'time',
                    'price' => 10,
                ]),
                Plan::make([
                    'code' => '456',
                    'type' => 'time',
                    'price' => 10,
                ]),
            ]);

        $subscriptionPlanUpsertService = Mockery::mock(SubscriptionPlanUpsertService::class);
        $branchesRepository = Mockery::mock(BranchesRepository::class);
        $branchesRepository
            ->shouldReceive('addCriteria')
            ->once()
            ->andReturnSelf()
            ->shouldReceive('firstOrFail')
            ->andReturn(
                new Branch(['_id' => $branchId, 'namespace' => 'foo'])
            )
            ->once();

        $membershipValidator = Mockery::mock(Validator::class);
        $membershipValidator
            ->shouldReceive('withData')
            ->andReturn($membershipValidator);
        $membershipValidator->expects('validate');

        $membershipRepository = Mockery::mock(MembershipsRepository::class);
        $membershipRepository
            ->shouldReceive('addCriteria')
            ->withArgs(fn() => true)
            ->once()
            ->andReturnSelf()
            ->shouldReceive('firstOrFail')
            ->andReturn(
                $membership
            )
            ->once()
            ->shouldReceive('saveOrFail')
            ->withArgs(function (Membership $membership) {
                self::assertArrayNotHasKey('min_price', $membership['plans']['0']);
                self::assertArrayNotHasKey('min_price', $membership['plans']['1']);

                return true;
            })
            ->andReturn(
                new Membership($membership->toLegacy())
            )
            ->once()
            ->shouldReceive('saveOrFail')
            ->withArgs(function (Membership $membership) {
                self::assertArrayHasKey('min_price', $membership['plans']['0']);
                self::assertArrayHasKey('min_price', $membership['plans']['1']);

                return true;
            })
            ->andReturn(
                new Membership($membership->toLegacy())
            )
            ->once();

        app()->instance(MembershipsRepository::class, $membershipRepository);

        $service = new MembershipUpsertService($membershipValidator, $subscriptionPlanUpsertService,
            $branchesRepository);

        $service->execute($request);

        app()->forgetInstance(MembershipsRepository::class);
    }

    public function test_sending_min_price_does_not_overwrite_the_saved_min_price()
    {
        $branchId = (string) new MongoId();
        $membershipId = (string) new MongoId();

        $membership =
            Membership::make([
                '_id' => $membershipId,
                'branch_id' => $branchId,
                'plans' => [
                    Plan::make([
                        'code' => '123',
                        'type' => 'time',
                        'price' => 15,
                        'min_price' => 10,
                    ]),
                ],
            ]);

        $request = new Membership($membership);
        $request->set('plans',
            [
                Plan::make([
                    'code' => '123',
                    'type' => 'time',
                    'price' => 15,
                    'min_price' => 5,
                ]),
            ]);

        $subscriptionPlanUpsertService = Mockery::mock(SubscriptionPlanUpsertService::class);
        $branchesRepository = Mockery::mock(BranchesRepository::class);
        $branchesRepository
            ->shouldReceive('addCriteria')
            ->once()
            ->andReturnSelf()
            ->shouldReceive('firstOrFail')
            ->andReturn(
                new Branch(['_id' => $branchId, 'namespace' => 'foo'])
            )
            ->once();

        $membershipValidator = Mockery::mock(Validator::class);
        $membershipValidator
            ->shouldReceive('withData')
            ->andReturn($membershipValidator);
        $membershipValidator->expects('validate');

        $membershipRepository = Mockery::mock(MembershipsRepository::class);
        $membershipRepository
            ->shouldReceive('addCriteria')
            ->withArgs(fn() => true)
            ->once()
            ->andReturnSelf()
            ->shouldReceive('firstOrFail')
            ->andReturn(
                $membership
            )
            ->once()
            ->shouldReceive('saveOrFail')
            ->andReturn(
                new Membership($membership->toLegacy())
            )
            ->once()
            ->shouldReceive('saveOrFail')
            ->withArgs(function (Membership $membership) {
                self::assertEquals($membership['plans']['0']['min_price'], 10);

                return true;
            })
            ->andReturn(
                new Membership($membership->toLegacy())
            )
            ->once();

        app()->instance(MembershipsRepository::class, $membershipRepository);

        $service = new MembershipUpsertService($membershipValidator, $subscriptionPlanUpsertService,
            $branchesRepository);

        $service->execute($request);

        app()->forgetInstance(MembershipsRepository::class);
    }

    public function test_restoring_a_deleted_membership_with_no_min_price_sets_min_price_to_0()
    {
        $branchId = (string) new MongoId();
        $membershipId = (string) new MongoId();

        $membership =
            Membership::make([
                '_id' => $membershipId,
                'branch_id' => $branchId,
                'active' => false,
                'plans' => [
                    Plan::make([
                        'code' => '123',
                        'type' => 'time',
                        'price' => 10,
                    ]),
                ],
            ]);

        $request = new Membership($membership);
        $request->set('active', true);

        $subscriptionPlanUpsertService = Mockery::mock(SubscriptionPlanUpsertService::class);
        $branchesRepository = Mockery::mock(BranchesRepository::class);
        $branchesRepository
            ->shouldReceive('addCriteria')
            ->once()
            ->andReturnSelf()
            ->shouldReceive('firstOrFail')
            ->andReturn(
                new Branch(['_id' => $branchId, 'namespace' => 'foo'])
            )
            ->once();

        $membershipValidator = Mockery::mock(Validator::class);
        $membershipValidator
            ->shouldReceive('withData')
            ->andReturn($membershipValidator);
        $membershipValidator->expects('validate');

        $membershipRepository = Mockery::mock(MembershipsRepository::class);
        $membershipRepository
            ->shouldReceive('addCriteria')
            ->withArgs(fn() => true)
            ->once()
            ->andReturnSelf()
            ->shouldReceive('firstOrFail')
            ->andReturn(
                $membership
            )
            ->once()
            ->shouldReceive('saveOrFail')
            ->andReturn(
                new Membership($membership)
            )
            ->once()
            ->shouldReceive('saveOrFail')
            ->withArgs(function (Membership $membership) {
                self::assertEquals($membership['plans']['0']['min_price'], 0);

                return true;
            })
            ->andReturn(
                new Membership($membership->toLegacy())
            )
            ->once();

        app()->instance(MembershipsRepository::class, $membershipRepository);

        $service = new MembershipUpsertService($membershipValidator, $subscriptionPlanUpsertService,
            $branchesRepository);

        $service->execute($request);

        app()->forgetInstance(MembershipsRepository::class);
    }

    public function test_price_cannot_be_saved_lower_than_the_min_price()
    {
        $branchId = (string) new MongoId();
        $membershipId = (string) new MongoId();

        $membership =
            Membership::make([
                '_id' => $membershipId,
                'branch_id' => $branchId,
                'plans' => [
                    Plan::make([
                        'code' => '123',
                        'type' => 'time',
                        'price' => 15,
                        'min_price' => 10,
                    ]),
                ],
            ]);

        $request = new Membership($membership);
        $request->set('plans',
            [
                Plan::make([
                    'code' => '123',
                    'type' => 'time',
                    'price' => 1,
                ]),
            ]);

        $subscriptionPlanUpsertService = Mockery::mock(SubscriptionPlanUpsertService::class);
        $branchesRepository = Mockery::mock(BranchesRepository::class);
        $branchesRepository
            ->shouldReceive('addCriteria')
            ->once()
            ->andReturnSelf()
            ->shouldReceive('firstOrFail')
            ->andReturn(
                new Branch(['_id' => $branchId, 'namespace' => 'foo'])
            )
            ->once();

        $membershipValidator = Mockery::mock(Validator::class);
        $membershipValidator
            ->shouldReceive('withData')
            ->andReturn($membershipValidator);
        $membershipValidator->expects('validate');

        $membershipRepository = Mockery::mock(MembershipsRepository::class);
        $membershipRepository
            ->shouldReceive('addCriteria')
            ->withArgs(fn() => true)
            ->once()
            ->andReturnSelf()
            ->shouldReceive('firstOrFail')
            ->andReturn(
                $membership
            )
            ->once();

        app()->instance(MembershipsRepository::class, $membershipRepository);

        $minimumPriceValidator = Mockery::mock(PlanMinimumPriceValidator::class);
        $minimumPriceValidator
            ->shouldReceive('validate')
            ->andThrow(\Exception::class, 'PLAN_MINIMUM_PRICE_VALIDATION_ERROR');

        app()->instance(PlanMinimumPriceValidator::class, $minimumPriceValidator);

        $service = new MembershipUpsertService($membershipValidator, $subscriptionPlanUpsertService,
            $branchesRepository);

        try {
            $service->execute($request);
        } catch (\Exception $e) {
            self::assertEquals('PLAN_MINIMUM_PRICE_VALIDATION_ERROR', $e->getMessage());
            self::assertInstanceOf('Exception', $e);
        }
        app()->forgetInstance(MembershipsRepository::class);
        app()->forgetInstance(PlanMinimumPriceValidator::class);
    }

    public function test_removing_a_plan_from_a_saved_membership_validates_the_remaining_plans_min_price_correctly()
    {
        $branchId = (string) new MongoId();
        $membershipId = (string) new MongoId();

        $membership =
            Membership::make([
                '_id' => $membershipId,
                'branch_id' => $branchId,
                'plans' => [
                    Plan::make([
                        'code' => '123',
                        'type' => 'time',
                        'price' => 15,
                        'min_price' => 10,
                    ]),
                    Plan::make([
                        'code' => '456',
                        'type' => 'time',
                        'price' => 15,
                        'min_price' => 10,
                    ]),
                ],
            ]);

        $request = new Membership($membership);
        $request->set('plans',
            [
                Plan::make([
                    'code' => '456',
                    'type' => 'time',
                    'price' => 1,
                ]),
            ]);

        $subscriptionPlanUpsertService = Mockery::mock(SubscriptionPlanUpsertService::class);
        $branchesRepository = Mockery::mock(BranchesRepository::class);
        $branchesRepository
            ->shouldReceive('addCriteria')
            ->once()
            ->andReturnSelf()
            ->shouldReceive('firstOrFail')
            ->andReturn(
                new Branch(['_id' => $branchId, 'namespace' => 'foo'])
            )
            ->once();

        $membershipValidator = Mockery::mock(Validator::class);
        $membershipValidator
            ->shouldReceive('withData')
            ->andReturn($membershipValidator);
        $membershipValidator->expects('validate');

        $membershipRepository = Mockery::mock(MembershipsRepository::class);
        $membershipRepository
            ->shouldReceive('addCriteria')
            ->withArgs(fn() => true)
            ->once()
            ->andReturnSelf()
            ->shouldReceive('firstOrFail')
            ->andReturn(
                $membership
            )
            ->once();

        app()->instance(MembershipsRepository::class, $membershipRepository);

        $minimumPriceValidator = Mockery::mock(PlanMinimumPriceValidator::class);
        $minimumPriceValidator
            ->shouldReceive('validate')
            ->andThrow(\Exception::class, 'PLAN_MINIMUM_PRICE_VALIDATION_ERROR');

        app()->instance(PlanMinimumPriceValidator::class, $minimumPriceValidator);

        $service = new MembershipUpsertService($membershipValidator, $subscriptionPlanUpsertService,
            $branchesRepository);

        try {
            $service->execute($request);
        } catch (\Exception $e) {
            self::assertEquals('PLAN_MINIMUM_PRICE_VALIDATION_ERROR', $e->getMessage());
            self::assertInstanceOf('Exception', $e);

            app()->forgetInstance(MembershipsRepository::class);
            app()->forgetInstance(PlanMinimumPriceValidator::class);
        }
    }

    public function test_it_should_throw_exception_when_membership_has_duplicate_plan_code()
    {
        app()->forgetInstance(MembershipsRepository::class);
        app()->forgetInstance(Validator::class);
        app()->forgetInstance(BranchesRepository::class);
        app()->forgetInstance(MembershipUpsertService::class);

        $this->setExpectedException(DuplicateMembershipPlanCode::class);

        $branchId = '49a7011a05c677b9a916612b';

        $membership =
            Membership::make([
                'branch_id' => $branchId,
                'plans' => [
                    Plan::make([
                        'code' => '123',
                        'type' => 'time',
                        'price' => 15,
                        'min_price' => 10,
                    ]),
                    Plan::make([
                        'code' => '123',
                        'type' => 'time',
                        'price' => 15,
                        'min_price' => 10,
                    ]),
                ],
            ]);

        $subscriptionPlanUpsertService = \Mockery::mock(SubscriptionPlanUpsertService::class);

        $membershipValidator = app()->make(Validator::class);
        $branchesRepository = app()->make(BranchesRepository::class);

        $membershipValidator = Mockery::mock(Validator::class);
        $membershipValidator
            ->shouldReceive('withData')
            ->andReturn($membershipValidator);
        $membershipValidator->expects('validate');

        $service = new MembershipUpsertService($membershipValidator, $subscriptionPlanUpsertService, $branchesRepository);

        $service->execute($membership);
    }

    private function mockPaymentHandler($paymentMethodId): void
    {
        /** @var PaymentProviderContract $paymentHandler */
        $paymentProvider = \Mockery::mock(PaymentProviderContract::class)
            ->allows([
                'paymentMethod' => \Mockery::mock(PaymentMethod::class)
                    ->allows([
                        'typeId' => $paymentMethodId,
                    ]),
            ]);

        /** @var PaymentsHandler $paymentHandler */
        $paymentHandler = \Mockery::mock(PaymentsHandler::class)
            ->allows([
                'provider' => $paymentProvider,
                'providerByPaymentMethod' => $paymentProvider,
            ]);

        app()->instance(PaymentsHandler::class, $paymentHandler);
    }
}
