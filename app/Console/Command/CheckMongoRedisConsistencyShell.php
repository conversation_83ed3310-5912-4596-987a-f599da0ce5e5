<?php

use Glofox\Domain\BookingRequests\Models\BookingRequestType;
use Glofox\Domain\BookingRequests\Repositories\BookingRequestsCache;
use Glofox\Domain\Bookings\Repositories\BookingsRepository;
use Glofox\Domain\Bookings\Search\Expressions\EventId;
use Glofox\Domain\Bookings\Search\Expressions\Status;
use Glofox\Domain\Bookings\Status as GlofoxStatus;
use Glofox\Domain\Events\Models\Event;
use Glofox\Domain\Events\Repositories\EventsRepository;
use Glofox\Repositories\Search\Expressions\Shared\FieldGreaterThanOrEqual;
use Glofox\Repositories\Search\Expressions\Shared\InNamespaces;
use Illuminate\Support\Collection;

class CheckMongoRedisConsistencyShell extends AppShell
{
    /** @var BookingRequestsCache */
    private $bookingRequestsCache;

    /** @var BookingsRepository */
    private $bookingsRepository;

    /** @var EventsRepository */
    private $eventsRepository;

    public function __construct($stdout = null, $stderr = null, $stdin = null)
    {
        parent::__construct($stdout, $stderr, $stdin);

        $this->bookingRequestsCache = app()->make(BookingRequestsCache::class);
        $this->bookingsRepository = app()->make(BookingsRepository::class);
        $this->eventsRepository = app()->make(EventsRepository::class);
    }

    public function main()
    {
        $time = isset($this->args[0]) ? strtotime($this->args[0]) : strtotime('-1 week');
        $namespace = $this->args[1] ?? null;

        $this->eventsRepository
            ->fields(['_id', 'time_finish', 'branch_id'])
            ->addCriteria(new FieldGreaterThanOrEqual('time_start', new MongoDate($time)))
            ->addCriteria(new FieldGreaterThanOrEqual('time_finish', new MongoDate()));

        if (!empty($namespace)) {
            $this->eventsRepository->addCriteria(new InNamespaces(new Collection([$namespace])));
        }

        $eventsCount = $this->eventsRepository->count();

        $this->log(sprintf('Found %s events', $eventsCount), LOG_INFO);

        $limit = 1000;
        $lastPage = ($eventsCount / $limit) + 1;
        $incosistenCount = 0;
        for ($i = 0; $i < $lastPage; ++$i) {
            $this->eventsRepository
                ->fields(['_id', 'time_finish', 'branch_id'])
                ->addCriteria(new FieldGreaterThanOrEqual('time_start', new MongoDate($time)))
                ->addCriteria(new FieldGreaterThanOrEqual('time_finish', new MongoDate()));

            if (!empty($namespace)) {
                $this->eventsRepository->addCriteria(new InNamespaces(new Collection([$namespace])));
            }

            $events = $this->eventsRepository
                ->page($i)
                ->limit($limit)
                ->find();

            foreach ($events as $event) {
                $bookedBookings = $this->bookingsRepository
                    ->addCriteria(new EventId($event->id()))
                    ->addCriteria(new Status(GlofoxStatus::BOOKED()))
                    ->find();

                $bookedCount = is_countable($bookedBookings) ? count($bookedBookings) : 0;
                foreach ($bookedBookings as $bookedBooking) {
                    $bookedCount += $bookedBooking->guestBookings();
                }

                $waitingBookings = $this->bookingsRepository
                    ->addCriteria(new EventId($event->id()))
                    ->addCriteria(new Status(GlofoxStatus::WAITING()))
                    ->find();

                $waitingCount = is_countable($waitingBookings) ? count($waitingBookings) : 0;
                foreach ($waitingBookings as $waitingBooking) {
                    $waitingCount += $waitingBooking->guestBookings();
                }

                $canceledCount = $this->bookingsRepository
                    ->addCriteria(new EventId($event->id()))
                    ->addCriteria(new Status(GlofoxStatus::CANCELED()))
                    ->count();

                $redisBookedCount = $this->bookingRequestsCache->countByEventId($event->id(), BookingRequestType::BOOKING());
                $redisWaitingCount = $this->bookingRequestsCache->countByEventId($event->id(), BookingRequestType::WAITING());
                $redisCancelingCount = $this->bookingRequestsCache->countByEventId($event->id(), BookingRequestType::CANCELLING());

                if ($redisBookedCount !== $bookedCount || $redisWaitingCount !== $waitingCount) {
                    ++$incosistenCount;
                    $this->log(
                        sprintf(
                            'Event %s has incosistent counts. [BOOKED] Mongo: %s Redis: %s [WAITING] Mongo: %s Redis: %s [CANCELED] Mongo: %s Redis: %s',
                            $event->id(),
                            $bookedCount,
                            $redisBookedCount,
                            $waitingCount,
                            $redisWaitingCount,
                            $canceledCount,
                            $redisCancelingCount,
                        ),
                        LOG_WARNING
                    );

                    $this->removeEventBookingsFromRedis($event);

                    $this->migrateEventBookingsToRedis($event);
                }
            }
        }

        $this->log(sprintf('Incosistent events count: %s', $incosistenCount), LOG_INFO);
    }

    private function removeEventBookingsFromRedis(Event $event): void
    {
        $this->log(sprintf('Wiping bookings for %s event from Redis', $event->id()), LOG_INFO);

        $this->bookingRequestsCache->removeByEventIds([
            sprintf('%s-%s', $event->id(), BookingRequestType::WAITING()),
            sprintf('%s-%s', $event->id(), BookingRequestType::CANCELLING()),
            sprintf('%s-%s', $event->id(), BookingRequestType::BOOKING()),
        ]);

        $this->log('Bookings wiped from Redis', LOG_INFO);
    }

    private function migrateEventBookingsToRedis(Event $event)
    {
        $bookings = $this->bookingsRepository
            ->addCriteria(new EventId($event->id()))
            ->find();

        $this->log(sprintf('Found %s bookings for event %s', is_countable($bookings) ? count($bookings) : 0, $event->id()), LOG_INFO);

        foreach ($bookings as $booking) {
            try {
                $month = 2_592_000;
                $expireAt = $event->timeFinish()->getTimestamp() + $month;

                $this->bookingRequestsCache->save($booking, $expireAt);
            } catch (InvalidMongoIdException $exception) {
                $this->log(
                    sprintf(
                        'Booking %s skipped. Invalid Mongo ID: %s',
                        $booking->id(),
                        $exception->getMessage()
                    ),
                    LOG_WARNING
                );
            }
        }
    }
}
