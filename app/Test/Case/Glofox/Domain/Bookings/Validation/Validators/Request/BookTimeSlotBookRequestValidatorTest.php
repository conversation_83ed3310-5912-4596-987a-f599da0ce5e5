<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\Bookings\Validation\Validators\Request;

use App;
use CakeTestCases\Glofox\Domain\Users\Traits\AuthenticateUsersTrait;
use Glofox\Domain\Authentication\Auth;
use Glofox\Domain\Bookings\Requests\BookTimeSlotAsMemberRequest;
use Glofox\Domain\Bookings\Requests\BookTimeSlotAsStaffRequest;
use Glofox\Domain\Bookings\Validation\Validators\Request\BookTimeSlotBookRequestValidator;
use GlofoxTestCase;

App::import('Test/Case', 'GlofoxTestCase');

class BookTimeSlotBookRequestValidatorTest extends GlofoxTestCase
{
    use AuthenticateUsersTrait;

    public $fixtures = [
        'app.user',
        'app.branch'
    ];
    private BookTimeSlotBookRequestValidator $validator;

    public function setUp(): void
    {
        parent::setUp();

        $this->validator = new BookTimeSlotBookRequestValidator();
    }

    public function testValidateRequestAsAMemberSuccessfully(): void
    {
        $this->authenticateAsMember();

        $request = BookTimeSlotAsMemberRequest::create('', 'POST', [
            'model' => 'timeslot',
            'model_id' => '6759cec951232790f0382e85',
            'staff_id' => '6759cec951232790f0382e85',
            'time_start' => 1_733_938_862,
        ]);

        $this->validator->validate($request, Auth::user());
    }

    public function testValidateRequestAsAStaffSuccessfully(): void
    {
        $this->authenticateAsAdmin();

        $request = BookTimeSlotAsStaffRequest::create('', 'POST', [
            'model' => 'timeslot',
            'model_id' => '6759cec951232790f0382e85',
            'staff_id' => '6759cec951232790f0382e85',
            'time_start' => 1_733_938_862,
            'user_id' => '6759cec951232790f0382e85',
            'payment_method' => 'complimentary',
            'charge' => false,
            'price' => 23.99,
        ]);

        $this->validator->validate($request, Auth::user());
    }
}
