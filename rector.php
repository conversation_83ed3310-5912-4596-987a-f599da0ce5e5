<?php

declare(strict_types=1);

use <PERSON>\CodeQuality\Rector\Class_\InlineConstructorDefaultToPropertyRector;
use <PERSON>\Config\RectorConfig;
use <PERSON>\Set\ValueObject\LevelSetList;

return static function (RectorConfig $rectorConfig): void {
    // This is for CCI purpose only
    if (file_exists('./rector-paths.json')) {
        $paths = json_decode(
            file_get_contents('./rector-paths.json'),
            true,
            512,
            JSON_THROW_ON_ERROR
        );
        // By default, if there's any path, we set main index.php, will be a quick review
        if (empty($paths)) {
            $paths = ['/app/index.php'];
        }
    } else {
        // Local dev purpose, put here the file paths to locally run rector
        $paths = ['/app'];
    }
    $paths = array_map(static fn(string $path) => __DIR__ . $path, $paths);

    // This configuration is to improve Rector parallel execution, as default values cause "random" issues
    $rectorConfig->parallel(240, 2);
    $rectorConfig->paths($paths);

    $rectorConfig->skip([
        __DIR__ . '/app/View/Fonts',

        /*
         * They return all the time the error "Class 'App' not found in app/Glofox/Console/ShellHelper.php". These
         * classes must be manually updated.
         */
        __DIR__ . '/app/Glofox/Payments/Console/Gateway/RecordPayouts.php',
        __DIR__ . '/app/Lib/ProtoBuffers',
        __DIR__ . '/app/Test/Case/Glofox/Http/Responses/Middlewares/AddResponseSuccessHeadersTest.php',
        __DIR__ . '/app/Test/Case/Glofox/Infrastructure/Flags/FlaggerTest.php',
        __DIR__ . '/app/Test/Case/Glofox/Domain/Events/Http/EventsControllerTest.php',

        /*
         * For these files we have an interface defined for zadd method which has 2 arguments, but then Redis zadd
         * command needs 3 instead of 2... Rector is trying to follow the interface definition, so we need to skip
         * this file in order to prevent an error with zadd
         */
        __DIR__ . '/app/Glofox/Infrastructure/Cache/RedisCacheClient.php',
        __DIR__ . '/app/Test/Case/Glofox/Domain/BookingRequests/Repositories/BookingRequestsCacheTest.php',
        __DIR__ . '/app/Test/Case/Glofox/Infrastructure/Cache/RedisCacheClientTest.php',
    ]);

    // register a single rule
    $rectorConfig->rule(InlineConstructorDefaultToPropertyRector::class);

    // define sets of rules
    $rectorConfig->sets([
        LevelSetList::UP_TO_PHP_74,
    ]);
};
