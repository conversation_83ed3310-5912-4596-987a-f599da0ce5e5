<?php

namespace CakeTestCases\Glofox\AuditLog\Resolvers;

use Glofox\AuditLog\Resolvers\OriginByRequestResolver;
use Glofox\Http\Header;
use Glofox\Http\Source;
use Glofox\Request;

\App::import('Test/Case', 'GlofoxTestCase');

class OriginByRequestResolverTest extends \GlofoxTestCase
{
    private \CakeRequest $cakeRequest;
    private Request $illuminateRequest;
    private OriginByRequestResolver $originByRequestResolver;

    public function setUp()
    {
        parent::setUp();

        $this->illuminateRequest = Request::capture();

        $this->illuminateRequest->header('x-glofox-source', '');
        $this->illuminateRequest->header('x-glofox-platform', '');
        $this->illuminateRequest->header('User-Agent', '');

        $_SERVER['HTTP_X_GLOFOX_SOURCE'] = '';
        $_SERVER['HTTP_X_GLOFOX_PLATFORM'] = '';
        $_SERVER['HTTP_USER_AGENT'] = '';

        $this->cakeRequest = new \CakeRequest();

        $this->originByRequestResolver = app()->make(OriginByRequestResolver::class);
    }

    /**
     * @param Source::* $sourceHeaderValue
     *
     * @dataProvider provideLowercaseAndUppercaseSources
     */
    public function test_it_resolves_origin_using_cake_request(
        string $sourceHeaderValue,
        Source $expected
    ): void {
        $_SERVER['HTTP_X_GLOFOX_SOURCE'] = $sourceHeaderValue;

        $source = $this->originByRequestResolver->resolve($this->cakeRequest);
        $this->assertSame($expected, $source);
    }

    public function test_it_resolves_cli_using_cake_request(): void
    {
        $source = $this->originByRequestResolver->resolve(null);
        $this->assertSame(Source::CLI(), $source);
    }

    public function test_it_resolves_to_unknown_when_no_source_is_provided_using_cake_request(): void
    {
        $_SERVER['HTTP_X_GLOFOX_SOURCE'] = '';

        $source = $this->originByRequestResolver->resolve($this->cakeRequest);
        $this->assertSame(Source::UNKNOWN(), $source);
    }

    public function test_it_resolves_to_admin_app_when_expected_platform_is_provided_using_cake_request(): void
    {
        $_SERVER['HTTP_X_GLOFOX_PLATFORM'] = 'admin app';

        $source = $this->originByRequestResolver->resolve($this->cakeRequest);
        $this->assertSame(Source::ADMIN_APP(), $source);
    }

    public function test_it_resolves_to_legacy_app_when_expected_user_agent_is_provided_using_cake_request(): void
    {
        $_SERVER['HTTP_USER_AGENT'] = 'Fennec/7.7.3 (iPhone; iOS 11.2.6; Scale/2.00)';

        $source = $this->originByRequestResolver->resolve($this->cakeRequest);
        $this->assertSame(Source::LEGACY_MOBILE_APP(), $source);
    }

    public function testItResolvesRecurrentServiceSchedulerToDashboardUsingCakeRequestByPlatform(): void
    {
        $_SERVER[Header::HTTP_GLOFOX_SOURCE] = 'RECURRENT_BOOKINGS_SCHEDULER';
        $_SERVER[Header::HTTP_GLOFOX_PLATFORM] = 'dashboard';

        $source = $this->originByRequestResolver->resolveByPlatform($this->cakeRequest);
        $this->assertEquals(Source::DASHBOARD()->getName(), $source->getName());
    }

    public function testItResolvesRecurrentServiceSchedulerToUnknownUsingCakeRequestByPlatform(): void
    {
        $_SERVER[Header::HTTP_GLOFOX_SOURCE] = 'RECURRENT_BOOKINGS_SCHEDULER';

        $source = $this->originByRequestResolver->resolveByPlatform($this->cakeRequest);
        $this->assertEquals(Source::UNKNOWN()->getName(), $source->getName());
    }

    /**
     * @param Source::* $sourceHeaderValue
     *
     * @dataProvider provideLowercaseAndUppercaseSources
     */
    public function test_it_resolves_origin_using_illuminate_requests(
        string $sourceHeaderValue,
        Source $expected
    ): void {
        $this->illuminateRequest->headers->set('x-glofox-source', $sourceHeaderValue);

        $source = $this->originByRequestResolver->resolveUsingIlluminateRequest($this->illuminateRequest);
        $this->assertSame($expected, $source);
    }

    public function test_it_resolves_cli_using_illuminate_request(): void
    {
        $source = $this->originByRequestResolver->resolveUsingIlluminateRequest(null);
        $this->assertSame(Source::CLI(), $source);
    }

    public function test_it_resolves_to_unknown_when_no_source_is_provided_using_illuminate_request(): void
    {
        $this->illuminateRequest->headers->set('x-glofox-source', '');

        $source = $this->originByRequestResolver->resolveUsingIlluminateRequest($this->illuminateRequest);
        $this->assertSame(Source::UNKNOWN(), $source);
    }

    public function test_it_resolves_to_admin_app_when_expected_platform_is_provided_using_illuminate_request(): void
    {
        $this->illuminateRequest->headers->set('x-glofox-platform', 'admin app');

        $source = $this->originByRequestResolver->resolveUsingIlluminateRequest($this->illuminateRequest);
        $this->assertSame(Source::ADMIN_APP(), $source);
    }

    public function test_it_resolves_to_legacy_app_when_expected_user_agent_is_provided_using_illuminate_request(): void
    {
        $this->illuminateRequest->headers->set('User-Agent', 'Fennec/7.7.3 (iPhone; iOS 11.2.6; Scale/2.00)');

        $source = $this->originByRequestResolver->resolveUsingIlluminateRequest($this->illuminateRequest);
        $this->assertSame(Source::LEGACY_MOBILE_APP(), $source);
    }

    public function provideLowercaseAndUppercaseSources(): iterable
    {
        foreach (Source::getEnumerators() as $source) {
            yield \sprintf('lowercase source %s', $source) => [
                'sourceHeaderValue' => strtolower($source->getName()),
                'expected' => $source
            ];

            yield \sprintf('uppercase source %s', $source) => [
                'sourceHeaderValue' => strtoupper($source->getName()),
                'expected' => $source
            ];
        }
    }
}
