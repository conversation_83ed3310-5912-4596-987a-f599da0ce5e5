type: object
description: ProgramNotFound
properties:
  success:
    type: boolean
    default: false
  message:
    type: string
    default: 'Program not found - id: {program_id}'
  message_code:
    type: string
    default: 'Program not found - id: {program_id}'
  message_data:
    type: array
    items:
      type: string
  errors:
    type: string
    default: 'Program not found - id: {program_id}'
