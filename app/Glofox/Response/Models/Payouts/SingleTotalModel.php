<?php

namespace Glofox\Response\Models\Payouts;

use Glofox\Response\BaseModel;

/**
 * @SuppressWarnings(PHPMD)
 */
class SingleTotalModel extends BaseModel
{
    /**
     * @var string
     */
    public $type = '';
    /**
     * @var int
     */
    public $count = 0;
    /**
     * @var float
     */
    public $gross = 0;
    /**
     * @var float
     */
    public $net_fees = 0;
    /**
     * @var float
     */
    public $total = 0;

    protected array $allowedFields = [
        'type',
        'count',
        'gross',
        'net_fees',
        'total'
    ];

    public function __construct(array $data)
    {
        $this->setModelFields($data);
    }
}
