type: object
properties:
  staff_id:
    type: string
    description: The trainer assigned to this slot
  time_slot_pattern_id:
    type: string
    description: ID of the appointment pattern this slot belongs to
  time_start:
    type: integer
    description: Timestamp in UTC for when the slot starts
  date:
    type: integer
    description: Date timestamp for the slot
required:
  - time_start
  - staff_id
  - time_slot_pattern_id
  - date
