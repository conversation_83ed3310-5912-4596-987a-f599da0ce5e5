<?php

declare(strict_types=1);

use Glofox\Datasource\Exceptions\InvalidMongoIdException;
use Glofox\Domain\Authentication\Auth;
use Glofox\Domain\Authentication\Token\TokenGeneratorDto;
use Glofox\Domain\Authentication\TokenGenerator;
use Glofox\Domain\Bookings\Commands\GenerateBooking;
use Glofox\Domain\Bookings\Internal\Parameters\GenerateBookingParameters;
use Glofox\Domain\Bookings\Models\Booking as BookingModel;
use Glofox\Domain\Bookings\Repositories\BookingsRepository;
use Glofox\Domain\Branches\Exceptions\BranchNotFoundException;
use Glofox\Domain\Branches\Models\Branch as BranchModel;
use Glofox\Domain\Branches\Repositories\BranchesRepository;
use Glofox\Domain\Events\Exceptions\EventNotFoundException;
use Glofox\Domain\Events\Models\Event as EventModel;
use Glofox\Domain\Events\Repositories\EventsRepository;
use Glofox\Domain\Users\Exceptions\UserNotFoundException;
use Glofox\Domain\Users\Models\User as UserModel;
use Glofox\Domain\Users\Repositories\UsersRepository;
use Psr\Log\LoggerInterface;

App::uses('Shell', 'Console');

class GenerateBookingBatchShell extends Shell
{
    private LoggerInterface $logger;
    private UsersRepository $usersRepository;
    private EventsRepository $eventsRepository;
    private BranchesRepository $branchesRepository;
    private BookingsRepository $bookingsRepository;
    private GenerateBooking $generateBooking;

    public function __construct($stdout = null, $stderr = null, $stdin = null)
    {
        parent::__construct($stdout, $stderr, $stdin);

        $this->logger = app()->make(LoggerInterface::class);
        $this->usersRepository = app()->make(UsersRepository::class);
        $this->eventsRepository = app()->make(EventsRepository::class);
        $this->branchesRepository = app()->make(BranchesRepository::class);
        $this->bookingsRepository = app()->make(BookingsRepository::class);
        $this->generateBooking = app()->make(GenerateBooking::class);
    }

    public function getOptionParser()
    {
        $parser = parent::getOptionParser();

        $parser->addOption('bookingIds', [
            'short' => 'b',
            'help' => 'Bookings IDs',
            'required' => true
        ]);

        return $parser;
    }

    public function main(): void
    {
        $this->logger->info('[GenerateBookingBatch] Started');
        error_reporting(E_ERROR | E_WARNING);

        $errorsString = "";
        try {
            $bookingIds = explode(',', $this->params['bookingIds']);
            foreach ($bookingIds as $bookingId) {
                $booking = $this->bookingsRepository->getById($bookingId);
                if ($booking === null) {
                    $this->logger->warning('[GenerateBookingBatch] Booking not found', [
                        'bookingId' => $bookingId,
                    ]);
                    continue;
                }

                $branch = $this->resolveBranch($booking);
                $event = $this->resolveEvent($booking);
                $user = $this->resolveUser($booking);
                $bot = $this->resolveBot($branch);

                $authToken = app()->make(TokenGenerator::class)->generate(new TokenGeneratorDto($bot->toArray()));
                $_SERVER['HTTP_AUTHORIZATION'] = $authToken;
                Auth::loginAs($bot);

                $params = new GenerateBookingParameters(
                    $event,
                    $user,
                    null,
                    $branch,
                    false,
                    PaymentMethods::COMPLIMENTARY,
                    $bot,
                    false,
                    false,
                    $booking->batchId()
                );

                $result = $this->generateBooking->generate($params);
                if (!$result['success']) {
                    $this->logger->warning(
                        sprintf("[GenerateBookingBatch] An error occurred (%s,%s)", $bookingId, $result['message'])
                    );
                    $errorsString .= sprintf("%s,%s%s", $bookingId, $result['message'], "\n");
                    Auth::logout();

                    continue;
                }

                $this->logger->info('[GenerateBookingBatch] Booking successfully processed', [
                    'bookingId' => $bookingId,
                ]);
                Auth::logout();
            }
        } catch (Throwable $exception) {
            $this->logger->error('[GenerateBookingBatch] An error occurred', [
                'message' => $exception->getMessage(),
            ]);

            $errorsString .= sprintf("%s,%s%s", $bookingId, $exception->getMessage(), "\n");
            Auth::logout();
        }

        error_reporting(-1);
        $this->logger->info('[GenerateBookingBatch] Finished');
        $this->log(sprintf("[GenerateBookingBatch] Errors:%s%s", "\n", $errorsString));
    }

    /**
     * @throws BranchNotFoundException
     * @throws InvalidMongoIdException
     */
    private function resolveBranch(BookingModel $booking): BranchModel
    {
        return $this->branchesRepository->getById($booking->branchId());
    }

    /**
     * @throws EventNotFoundException
     * @throws InvalidMongoIdException
     */
    private function resolveEvent(BookingModel $booking): EventModel
    {
        return $this->eventsRepository->getById($booking->eventId());
    }

    /**
     * @throws InvalidMongoIdException
     * @throws UserNotFoundException
     */
    private function resolveUser(BookingModel $booking): UserModel
    {
        return $this->usersRepository->getById($booking->userId());
    }

    private function resolveBot(BranchModel $branch): UserModel
    {
        return UserModel::make([
            '_id' => UserModel::WORKER_FAKE_ID,
            'namespace' => $branch->namespace(),
            'branch_id' => $branch->id(),
            'first_name' => 'Auto-Booking',
            'last_name' => 'From ProcessBookingShell',
            'type' => \UserType::SUPERADMIN,
            'active' => true,
        ]);
    }
}
