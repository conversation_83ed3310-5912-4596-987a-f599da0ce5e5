<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Infrastructure\Honeycomb;

use App;
use Glofox\Infrastructure\Honeycomb\HoneycombTracker;
use GlofoxTestCase;
use GuzzleHttp\Client as GuzzleHttpClient;
use Mockery;

App::import('Test/Case', 'GlofoxTestCase');

class HoneycombTrackerTest extends GlofoxTestCase
{

    public function tearDown(): void
    {
        parent::tearDown();

        Mockery::close();
    }

    public function test_tracker_is_sending_data_to_honeycomb()
    {
        $mockedHttpClient = Mockery::mock(GuzzleHttpClient::class);

        $data = ['test' => 'test'];
        $body = [
            'body' => json_encode($data),
        ];
        $mockedHttpClient
            ->shouldReceive('post')
            ->withArgs(function (string $url, array $options) use ($body) {
                $this->assertTextEquals($url, '1/events/test');
                $this->assertEquals($options, $body);

                return true;
            })
            ->once();
        $honeycombTracker = new HoneycombTracker($mockedHttpClient, 'test');
        $honeycombTracker->track($data);
    }
}