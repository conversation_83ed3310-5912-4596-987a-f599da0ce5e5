<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\Transactional\Messages\Variables\Strategies;

use Glofox\Domain\ElectronicAgreements\Services\AgreementRedirectUrlGenerator;
use Glofox\Domain\ElectronicAgreements\Trigger;
use Glofox\Domain\Transactional\Messages\Variables\Strategies\DocumentUrl;
use Glofox\Domain\Transactional\Messages\Variables\Strategies\DocumentUrlParams;
use Glofox\Domain\Users\Models\User;

\App::import('Test/Case', 'GlofoxTestCase');

class DocumentUrlTest extends \GlofoxTestCase
{
    public $fixtures = [];

    public function test_it_generates_url()
    {
        $generator = \Mockery::mock(AgreementRedirectUrlGenerator::class);
        $generator->shouldReceive('generate')
            ->withArgs(fn (User $user, Trigger $trigger, string $metadata) => $user->id() == 'user-id'
                && $trigger == Trigger::MEMBERSHIP_PURCHASED()
                && $metadata == 'metadata')
            ->andReturn('generated-url')
            ->once();

        app()->instance(AgreementRedirectUrlGenerator::class, $generator);

        $user = User::make(['_id' => 'user-id']);

        $strategy = app()->make(DocumentUrl::class);
        $result = $strategy->find(new DocumentUrlParams($user, Trigger::MEMBERSHIP_PURCHASED(), 'metadata'));
        self::assertEquals('generated-url', $result);

        app()->forgetInstance(AgreementRedirectUrlGenerator::class);
    }

    public function test_it_handles_null_url()
    {
        $generator = \Mockery::mock(AgreementRedirectUrlGenerator::class);
        $generator->shouldReceive('generate')
            ->withArgs(fn (User $user, Trigger $trigger) => $user->id() == 'user-id'
                && $trigger == Trigger::MEMBERSHIP_PURCHASED())
            ->andReturn(null)
            ->once();

        app()->instance(AgreementRedirectUrlGenerator::class, $generator);

        $user = User::make(['_id' => 'user-id']);

        $strategy = app()->make(DocumentUrl::class);
        $result = $strategy->find(new DocumentUrlParams($user, Trigger::MEMBERSHIP_PURCHASED()));
        self::assertEquals('', $result);

        app()->forgetInstance(AgreementRedirectUrlGenerator::class);
    }
}
