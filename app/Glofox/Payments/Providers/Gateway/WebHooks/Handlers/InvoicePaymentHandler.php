<?php

namespace Glofox\Payments\Providers\Gateway\WebHooks\Handlers;

use Exception;
use Glofox\Domain\Charges\Models\Charge;
use Glofox\Domain\Charges\Models\ChargesGroup;
use Glofox\Domain\Charges\Repositories\ChargesRepository;
use Glofox\Domain\Charges\Services\ChargeSynchronizer;
use Glofox\Domain\Memberships\Exceptions\MembershipNotFoundException;
use Glofox\Domain\Memberships\Models\Membership;
use Glofox\Domain\Memberships\Repositories\MembershipsRepository;
use Glofox\Domain\PaymentMethods\Type;
use Glofox\Domain\Users\Models\User;
use Glofox\Events\EventManager;
use Glofox\Payments\Contracts\PaymentProviderContract;
use Glofox\Payments\Entities\Subscription\Events\SubscriptionCyclePaymentHasFailed;
use Glofox\Payments\Entities\Subscription\Events\SubscriptionCycleWasPaid;
use Glofox\Payments\Entities\Transaction\Models\Transaction;
use Glofox\Payments\Exceptions\InvoicePaymentEventWithoutTransactionsException;
use Glofox\Payments\Exceptions\SubscriptionAlreadyRenewedException;
use Glofox\Payments\Transformers\TransactionToChargeTransformer;
use Glofox\Payments\Util;
use Glofox\Repositories\Search\Expressions\Shared\Id;
use Illuminate\Support\Collection;
use StripeCharge;

/**
 * Class InvoicePaymentHandler.
 */
abstract class InvoicePaymentHandler extends SubscriptionEventHandler
{
    private StripeCharge $stripeChargeCakeModel;

    private MembershipsRepository $membershipsRepository;

    private EventManager $eventManager;

    private TransactionToChargeTransformer $transactionToChargeTransformer;

    private ChargeSynchronizer $chargeSynchronizer;

    /** @var ChargesRepository */
    private $chargesRepository;

    public function __construct(
        Collection $eventPayload,
        PaymentProviderContract $paymentHandler,
        EventManager $eventManager
    ) {
        parent::__construct($eventPayload, $paymentHandler);

        $this->stripeChargeCakeModel = app()->make(StripeCharge::class);
        $this->eventManager = $eventManager;
        $this->transactionToChargeTransformer = app()->make(TransactionToChargeTransformer::class);
        $this->chargeSynchronizer = app()->makeWith(
            ChargeSynchronizer::class,
            ['eventManager' => $eventManager]
        );
        $this->membershipsRepository = app()->make(MembershipsRepository::class);
        $this->chargesRepository = app()->make(ChargesRepository::class);
    }

    /**
     * @param string $subscriptionId
     * @param string $eventId
     * @param User $user
     * @param string|null $resourceId
     * @return ChargesGroup
     * @throws InvoicePaymentEventWithoutTransactionsException
     * @throws SubscriptionAlreadyRenewedException
     * @throws Exception
     */
    protected function storeSubscriptionCharges(
        string $subscriptionId,
        ?string $membershipId,
        ?string $membershipPlanCode,
        string $eventId,
        User $user,
        User $requester = null,
        string $resourceId = null
    ): ChargesGroup {
        $arguments = collect([$subscriptionId, $eventId, $user->toArray()])->toJson();
        logs()->push(sprintf('Preparing to store the subscription charges with values: %s, proceeding...', $arguments));

        $invoiceId = data_get(
            $this->eventPayloadPSP,
            'invoice.id',
            data_get($this->eventPayloadPSP, 'data.object.id') // fallback on the event id
        );
        $transactionGroupId = data_get($this->eventPayloadPSP, 'invoice.latest_transaction_group.id');
        $invoiceTransactions = collect(data_get(
            $this->eventPayloadPSP,
            'invoice.latest_transaction_group.transactions'
        ));

        if (empty($invoiceTransactions)) {
            throw new InvoicePaymentEventWithoutTransactionsException();
        }

        $charges = collect();
        $skipSubscriptionEvents = false;
        $shouldRenew = true;
        foreach ($invoiceTransactions as $invoiceTransaction) {
            $transaction = $this->paymentHandler->charges()->getById($invoiceTransaction['id']);

            // Make sure this charge doesn't already exist
            $existingCharge = $this->stripeChargeCakeModel->findByProviderId($transaction->id());
            if (!empty($existingCharge)) {
                $existingCharge = Charge::make($existingCharge['StripeCharge']);

                logs()->push(sprintf(
                    'Charge already exists [%s] for transaction [%s] - fetching latest state',
                    $existingCharge->id(),
                    $transaction->id()
                ));

                if (
                    $resourceId !== null && $existingCharge->resourceId() == $resourceId
                    && $invoiceId != null && $existingCharge->invoiceId() == $invoiceId
                ) {
                    $shouldRenew = false;
                }

                $existingCharge = $this->chargeSynchronizer->syncCharge(
                    $this->paymentHandler,
                    $existingCharge,
                    $invoiceId
                );

                $charges->push($existingCharge);
                $skipSubscriptionEvents = true;
                continue;
            }

            $charges->push(
                $this->storeCharge($transaction, $user, $subscriptionId, $membershipId, $membershipPlanCode, $resourceId)
            );
        }

        $chargeGroup = new ChargesGroup($invoiceId, $transactionGroupId, $charges);

        if ($skipSubscriptionEvents) {
            return $this->triggerChargeUpdateEvents($chargeGroup);
        }

        if ($this->eventPayloadPSP->get('type') == 'invoice.payment_failed') {
            $this->eventManager->emit(
                SubscriptionCyclePaymentHasFailed::class,
                [$user->toArray(), $this->eventPayload->get('id'), $chargeGroup, $requester]
            );

            return $this->triggerChargeUpdateEvents($chargeGroup);
        }

        $this->eventManager->emit(
            SubscriptionCycleWasPaid::class,
            [$user->toArray(), $this->eventPayload->get('id'), $chargeGroup, $requester]
        );
        $result = $this->triggerChargeUpdateEvents($chargeGroup);

        if (!$shouldRenew) {
            throw new SubscriptionAlreadyRenewedException();
        }

        return $result;
    }

    private function storeCharge(
        Transaction $transaction,
        User $user,
        string $subscriptionId,
        ?string $membershipId,
        ?string $membershipPlanCode,
        string $resourceId = null
    ): Charge {
        logs()->push(sprintf(
            'Creating subscription charge metadata for transaction[%s], subscription[%s], membership[%s], plan[%s] proceeding...',
            $transaction->id(),
            $subscriptionId,
            $membershipId,
            $membershipPlanCode
        ));

        $branchId = $user->originBranchId();

        // Since we have the charge data from stripe, we will build the Glofox
        // metadata to add to the charge object so we can store it in our
        // database afterwards
        // @TODO Using LEGACY method
        $paymentMethodType = $transaction->paymentMethod() ?: $this->paymentHandler->paymentMethod()->typeId();

        $metadata = $this->stripeChargeCakeModel->build_subscription_payment_metadata(
            $user,
            $branchId,
            $subscriptionId,
            $transaction->isPaid() || $transaction->isPending(),
            Util::translateToLegacyPaymentMethod(
                Type::byValue($paymentMethodType)
            ),
            $resourceId,
            $membershipId,
            $membershipPlanCode
        );

        logs()->push(sprintf('Created subscription charge metadata: %s, proceeding...', collect($metadata)->toJson()));

        logs()->push(sprintf('Collecitng Membership data from user: %s, proceeding...', collect($user)->toJson()));

        $planName = $user['membership']['plan_name'] ?? null;
        if (empty($planName)) {
            logs()->push('Membership plan_name not found in user, using membershipId to fetch from DB, proceeding...');
            $membershipId = !empty($membershipId) ? $membershipId : $metadata['membership_id'];
            $membershipPlanCode = !empty($membershipPlanCode) ? $membershipPlanCode : $metadata['plan_code'];

            logs()->push(sprintf('Finding plan %s , proceeding...', $membershipPlanCode));
        
            /** @var Membership $membership */
            $membership = $this->membershipsRepository
                ->addCriteria(new Id($membershipId))
                ->firstOrFail(function () use ($membershipId) {
                    throw MembershipNotFoundException::withId($membershipId);
                });

            $plan = $membership->planByCode($membershipPlanCode);

            logs()->push(sprintf('Plan data found: %s, proceeding...', collect($plan)->toJson()));

            // There might be cases where the plan might not exist in the database
            // anymore as it could have been deleted by the admins. In cases
            // like this, we will use the default description.
            $planName = collect($plan)->get('name') ?? 'Recurring Subscription Payment';
        } else {
            logs()->push('Membership plan_name found in user, proceeding...');
        }
        
        $description = sprintf('%s (%s)', $planName, $subscriptionId);

        $dbCharge = $this->transactionToChargeTransformer->execute(
            $transaction,
            $metadata,
            $this->eventPayloadPSP,
            $description
        );

        logs()->push('Saving charge to database, proceeding...');

        $this->stripeChargeCakeModel->clear();
        $storedCharge = $this->stripeChargeCakeModel->save(
            $dbCharge->toArray()
        );

        // In the unlikely event where a charge wasn't saved in the database, maybe
        // due a problem with the db connection or networking issues, we will
        // throw an exception, which will tell stripe to retry the request
        // after some time.
        if (!$storedCharge) {
            $error = $this->stripeChargeCakeModel->get_latest_error();
            $message = sprintf('Charge could not be saved: %s', $error);

            logs()->push($message);

            throw new Exception($message);
        }

        logs()->push(sprintf('Charge saved successfully: %s', collect($storedCharge)->toJson()));

        return Charge::make($storedCharge['StripeCharge']);
    }

    private function triggerChargeUpdateEvents(ChargesGroup $chargesGroup): ChargesGroup
    {
        logs()->push('Synchronizing invoice related transactions');

        $updatedCharges = collect();

        /** @var $charge Charge */
        foreach ($chargesGroup->getCharges() as $charge) {
            $this->chargeSynchronizer->byInvoice(
                $this->paymentHandler,
                $chargesGroup->getInvoiceId(),
                $chargesGroup->getPrimaryCharge()->isPaid() || $chargesGroup->getPrimaryCharge()->isPending(),
            );

            $updatedCharges->push(
                $this->chargesRepository->addCriteria(new Id($charge->id()))->firstOrFail()
            );
        }

        logs()->push(sprintf(
            'invoice related charges synchronized, current charge group: %s',
            $updatedCharges->toJson()
        ));

        return new ChargesGroup(
            $chargesGroup->getInvoiceId(),
            $chargesGroup->getGroupId(),
            $updatedCharges,
        );
    }
}
