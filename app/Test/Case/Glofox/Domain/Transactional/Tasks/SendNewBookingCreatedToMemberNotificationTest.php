<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\Transactional\Tasks;

use CakeTestCases\Glofox\Domain\Logger\Traits\MockedLoggerTrait;
use Carbon\Carbon;
use Glofox\Domain\AsyncEvents\Events\BookingFinishedEvent;
use Glofox\Domain\AsyncEvents\Events\BookingFinishedEventMeta;
use Glofox\Domain\AsyncEvents\Events\BookingFinishedEventPayload;
use Glofox\Domain\Branches\Models\Branch;
use Glofox\Domain\Branches\Repositories\BranchesRepository;
use Glofox\Domain\Events\Models\Event;
use Glofox\Domain\Events\Repositories\EventsRepository;
use Glofox\Domain\Transactional\Tasks\SendNewBookingCreatedToMemberNotification;
use Glofox\Domain\Users\Models\User;
use Glofox\Domain\Users\Repositories\UsersRepository;
use Mockery;
use Mockery\MockInterface;
use MongoId;

\App::import('Test/Case', 'GlofoxTestCase');

class SendNewBookingCreatedToMemberNotificationTest extends \GlofoxTestCase
{
    use MockedLoggerTrait;

    public $fixtures = [];
    private MockInterface $notificationComponent;
    private MockInterface $pushNotification;
    private MockInterface $eventsRepository;
    private MockInterface $usersRepository;
    private SendNewBookingCreatedToMemberNotification $task;

    public function setUp(): void
    {
        parent::setUp();

        $this->mockLogger();
        $this->notificationComponent = Mockery::mock(\NotificationComponent::class);
        $this->pushNotification = Mockery::mock(\PushNotification::class);
        $this->eventsRepository = Mockery::mock(EventsRepository::class);
        $this->usersRepository = Mockery::mock(UsersRepository::class);

        $this->task = new SendNewBookingCreatedToMemberNotification(
            $this->notificationComponent,
            $this->pushNotification,
            $this->eventsRepository,
            $this->usersRepository,
            $this->loggerMock,
        );
    }

    public function tearDown(): void
    {
        parent::tearDown();

        Mockery::close();

        $this->teardownLogger();
    }

    public function testItShouldSendPushNotificationSuccessfullyWhenBookedBookingIsCreated(): void
    {
        Carbon::setTestNow('2023-01-02 12:34:56');

        $branchId = (string) new MongoId();
        $memberId = '67a33ab0fad3b9978fa8a017';
        $eventId = '67a33ab0fad3b9978fa8a015';
        $bookingId = '67a33ab0fad3b9978fa8a016';
        $status = 'BOOKED';

        $domainEvent = $this->givenAValidBookingFinishedEvent($branchId, $memberId, $eventId, $bookingId, $status);

        $event = Event::make([
            '_id' => $eventId,
            'branch_id' => $branchId,
            'name' => 'Testing Event',
            'time_start' => Carbon::parse('2023-02-01 10:00:00')->toDateTimeString(),
        ]);

        $this->itShouldFetchTheEvent($eventId, $event);

        $member = User::make([
            '_id' => $memberId,
            'branch_id' => $branchId,
            'device' => [
                'id' => 'some-device-id',
                'os' => 'ios',
                'version' => '1.2.3'
            ]
        ]);

        $this->itShouldFetchTheMember($memberId, $member);

        $this->itShouldSendAPushNotification(
            $branchId,
            $memberId,
            'You have been booked into Testing Event on Wed 1st of Feb at 10:00 AM'
        );

        $this->task->execute($domainEvent);
    }

    public function testItShouldNotSendPushNotificationWhenUserHasNoDevice(): void
    {
        $branchId = (string) new MongoId();
        $memberId = '67a33ab0fad3b9978fa8a017';
        $eventId = '67a33ab0fad3b9978fa8a015';
        $bookingId = '67a33ab0fad3b9978fa8a016';
        $status = 'BOOKED';

        $domainEvent = $this->givenAValidBookingFinishedEvent($branchId, $memberId, $eventId, $bookingId, $status);

        $event = Event::make([
            '_id' => $eventId,
            'branch_id' => $branchId,
            'name' => 'Testing Event',
            'time_start' => Carbon::parse('2023-02-01 10:00:00')->toDateTimeString(),
        ]);

        $this->itShouldFetchTheEvent($eventId, $event);

        $memberWithoutDevice = User::make([
            '_id' => $memberId,
        ]);

        $this->itShouldFetchTheMember($memberId, $memberWithoutDevice);

        $this->itShouldNotSendAPushNotification();

        $this->task->execute($domainEvent);
    }

    public function testItShouldNotSendPushNotificationWhenBookingIsNotBookedOrWaiting(): void
    {
        $branchId = (string) new MongoId();
        $memberId = '67a33ab0fad3b9978fa8a017';
        $eventId = '67a33ab0fad3b9978fa8a015';
        $bookingId = '67a33ab0fad3b9978fa8a016';
        $status = 'CANCELED';

        $domainEvent = $this->givenAValidBookingFinishedEvent($branchId, $memberId, $eventId, $bookingId, $status);

        $this->itShouldNotSendAPushNotification();

        $this->task->execute($domainEvent);
    }

    public function testItShouldNotSendPushNotificationWhenEventHasNoBookingId(): void
    {
        $domainEvent = $this->givenABookingFinishedEventWithoutBookingId();

        $this->itShouldNotSendAPushNotification();

        $this->task->execute($domainEvent);
    }

    private function givenAValidBookingFinishedEvent(
        string $branchId,
        string $memberId,
        string $eventId,
        string $bookingId,
        string $status
    ): BookingFinishedEvent {
        return new BookingFinishedEvent(
            new BookingFinishedEventMeta([]),
            new BookingFinishedEventPayload([
                'id' => $bookingId,
                'namespace' => 'random-namespace',
                'branch_id' => $branchId,
                'user_id' => $memberId,
                'status' => $status,
                'time_start' => Carbon::today()->toDateTimeString(),
                'time_finish' => Carbon::today()->addHour()->toDateTimeString(),
                'guest_bookings' => 0,
                'model' => 'events',
                'model_id' => $eventId,
                'model_name' => 'random-name',
                'paid' => null,
                'attended' => false,
            ])
        );
    }

    private function givenABookingFinishedEventWithoutBookingId(): BookingFinishedEvent
    {
        return new BookingFinishedEvent(
            new BookingFinishedEventMeta([]),
            new BookingFinishedEventPayload([
                'namespace' => 'random-namespace',
                'branch_id' => 'random-branch-id',
                'user_id' => 'random-user-id',
                'status' => 'FAILED',
                'time_start' => Carbon::today()->toDateTimeString(),
                'time_finish' => Carbon::today()->addHour()->toDateTimeString(),
                'guest_bookings' => 0,
                'model' => 'events',
                'model_id' => 'random-event-id',
                'model_name' => 'random-name',
                'paid' => null,
                'attended' => false,
            ])
        );
    }

    private function itShouldFetchTheEvent(string $eventId, Event $event): void
    {
        $this->eventsRepository
            ->shouldReceive('getById')
            ->with($eventId)
            ->andReturn($event);

        // Legacy call inside the Event::timeStart() method
        $branchesRepository = Mockery::mock(BranchesRepository::class);
        $branchesRepository
            ->shouldReceive('addCriteria')
            ->andReturnSelf()
            ->getMock()
            ->shouldReceive('firstOrFail')
            ->andReturn(Branch::make());
        app()->instance(BranchesRepository::class, $branchesRepository);
    }

    private function itShouldFetchTheMember(string $memberId, User $member): void
    {
        $this->usersRepository
            ->shouldReceive('getById')
            ->withArgs(fn ($id) => $id === $memberId)
            ->andReturn($member);
    }

    private function itShouldSendAPushNotification(
        string $expectedBranchId,
        string $expectedMemberId,
        string $expectedContent
    ): void {
        $this->pushNotification
            ->shouldReceive('saveCrudNotification')
            ->once();

        $this->notificationComponent
            ->shouldReceive('push_message')
            ->withArgs(function (
                string $branchId,
                string $memberId,
                string $message
            ) use (
                $expectedBranchId,
                $expectedMemberId,
                $expectedContent
            ) {
                $this->assertEquals($expectedBranchId, $branchId);
                $this->assertEquals($expectedMemberId, $memberId);
                $this->assertEquals($expectedContent, $message);

                return true;
            })
            ->once();
    }

    private function itShouldNotSendAPushNotification(): void
    {
        $this->pushNotification
            ->shouldNotReceive('saveCrudNotification')
            ->never();

        $this->notificationComponent
            ->shouldNotReceive('push_message')
            ->never();
    }
}
