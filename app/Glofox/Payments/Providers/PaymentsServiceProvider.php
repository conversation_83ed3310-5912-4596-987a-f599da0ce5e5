<?php


namespace Glofox\Payments\Providers;

use Glofox\Application;
use Glofox\Payments\Entities\Customer\Resolvers\IdResolver;
use Glofox\Payments\Entities\Customer\Resolvers\IdResolverContract;
use Illuminate\Support\ServiceProvider;

class PaymentsServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->app->singleton(IdResolverContract::class, fn(Application $app) => new IdResolver());
    }
}
