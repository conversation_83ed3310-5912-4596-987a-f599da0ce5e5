type: object
properties:
  time_start:
    type: integer
    description: Timestamp in UTC
  staff_id:
    type: string
  private:
    type: boolean
  booked:
    type: boolean
  size:
    type: integer
    maximum: 5
    default: 1
    description: The maximum number of attendees that can book this slot. This value is a positive number that can be
      set from 1 to 5. If capacity is not sent on the payload, the default value is set to 1.
required:
  - time_start
  - staff_id
  - private