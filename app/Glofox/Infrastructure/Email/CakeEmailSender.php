<?php

namespace Glofox\Infrastructure\Email;

use CakeEmail;

class CakeEmail<PERSON>ender implements EmailSenderInterface
{
    /** @var CakeEmail */
    private $cakeEmailAdapter;

    public function __construct()
    {
        $this->cakeEmailAdapter = new CakeEmail('sendgrid');
        $this->cakeEmailAdapter->emailFormat('html');
    }

    public function subject(string $subject): EmailSenderInterface
    {
        $this->cakeEmailAdapter->subject($subject);

        return $this;
    }

    public function templateName(string $templateName): EmailSenderInterface
    {
        $this->cakeEmailAdapter->template($templateName);

        return $this;
    }

    public function addTo(string $email, string $name = null): EmailSenderInterface
    {
        $this->cakeEmailAdapter->addTo($email, $name);

        return $this;
    }

    public function data(array $data): EmailSenderInterface
    {
        $this->cakeEmailAdapter->viewVars($data);

        return $this;
    }

    public function send(): bool
    {
        $contents = $this->cakeEmailAdapter->send();

        return !empty($contents);
    }
}
