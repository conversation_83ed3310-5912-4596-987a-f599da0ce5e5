<?php

namespace Glofox\Payments\Providers\Gateway\ProtoParsers;

use Glofox\Payments\Entities\Invoice\Models\InvoiceEntity;
use Glofox\Payments\Entities\Invoice\Models\InvoiceEntityLatestTransactionGroup;
use Glofox\Payments\Entities\Invoice\Models\InvoiceEntityLineItem;
use Glofox\Payments\Entities\Invoice\Models\InvoiceEntityLineItems;
use Glofox\Payments\Entities\Invoice\Models\InvoiceStatus;
use GRPC\Payments\APIService\InvoiceLineItem;
use GRPC\Payments\TransactionService\Transaction;

class InvoiceEntityParser
{
    private \Glofox\Payments\Providers\Gateway\ProtoParsers\TransactionParser $transactionParser;

    public function __construct()
    {
        $this->transactionParser = new TransactionParser();
    }

    public function parseFromProtoMessage(\GRPC\Payments\APIService\InvoiceEntity $invoice): InvoiceEntity
    {
        $invoiceEntity = new InvoiceEntity();

        $invoiceEntity->setCustomerAccountId((string) $invoice->getCustomerAccountID());
        $invoiceEntity->setDestinationAccountId((string) $invoice->getDestinationAccountID());
        $invoiceEntity->setAmount($invoice->getAmount());
        $invoiceEntity->setCurrency($invoice->getCurrency());
        $invoiceEntity->setSource($invoice->getSource());
        $invoiceEntity->setStatus(InvoiceStatus::byValue($invoice->getStatus()));

        if (!empty($invoice->getLatestTrxGroup())) {
            $invoiceLatestTransactionGroup = new InvoiceEntityLatestTransactionGroup();
            $invoiceLatestTransactionGroup->setId($invoice->getLatestTrxGroup()->getID());

            $parsedTransactions = collect();
            /** @var Transaction $transaction */
            foreach ($invoice->getLatestTrxGroup()->getTransactions() as $transaction) {
                $parsedTransactions->push($this->transactionParser->parseFromProtoMessage($transaction));
            }

            $invoiceLatestTransactionGroup->setTransactions($parsedTransactions->toArray());
            $invoiceEntity->setLatestTrxGroup($invoiceLatestTransactionGroup);
        }

        if (!empty($invoice->getLineItems())) {
            $invoiceLineItems = $this->parseLineItemsFromProtoMessage($invoice);
            $invoiceEntity->setLineItems($invoiceLineItems);
        }

        return $invoiceEntity;
    }

    public function parseFromV2ProtoMessage(\GRPC\Payments\APIService\InvoiceEntity $invoice, $invoiceAmount): InvoiceEntity
    {
        $invoiceEntity = new InvoiceEntity();

        $invoiceEntity->setId($invoice->getID());
        $invoiceEntity->setAmount($invoiceAmount);
        if (!empty($invoice->getLatestTrxGroup())) {
            $invoiceLatestTransactionGroup = new InvoiceEntityLatestTransactionGroup();
            $invoiceLatestTransactionGroup->setId($invoice->getLatestTrxGroup()->getID());
            $invoiceEntity->setLatestTrxGroup($invoiceLatestTransactionGroup);
        }
        
        return $invoiceEntity;
    }

    private function parseLineItemsFromProtoMessage(\GRPC\Payments\APIService\InvoiceEntity $invoice): InvoiceEntityLineItems
    {
        $invoiceLineItems = new InvoiceEntityLineItems([]);

        /** @var InvoiceLineItem $lineItem */
        foreach ($invoice->getLineItems() as $lineItem) {
            $attributes = [];
            foreach ($lineItem->getAttributes() as $key => $attribute) {
                $attributes[$key] = $attribute;
            }

            $invoiceLineItems->add(
                (new InvoiceEntityLineItem())
                    ->setId($lineItem->getID())
                    ->setResourceId($lineItem->getResourceID())
                    ->setName($lineItem->getName())
                    ->setQuantity($lineItem->getQuantity())
                    ->setAmount($lineItem->getAmount())
                    ->setServiceType($lineItem->getServiceType())
                    ->setExternalRef($lineItem->getExternalRef())
                    ->setAttributes($attributes)
            );
        }
        return $invoiceLineItems;
    }
}
