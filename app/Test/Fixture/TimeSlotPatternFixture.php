<?php

class TimeSlotPatternFixture extends CakeTestFixture
{
    private const GLOFOX_BRANCH_ID = '49a7011a05c677b9a916612a';
    private const GLOFOX_NAMESPACE = 'glofox';
    private const GLOFOX_STAFF_ID = '59a7011a05c677bda916612c';

    private const VIRTUAL_SLOT_BRANCH_ID = '6461ed6fdef3e8f4c6d4b00c';
    private const VIRTUAL_SLOT_BRANCH_NAMESPACE = 'virtualslotstest';
    private const VIRTUAL_SLOT_STAFF_ID = '6461eeb3bd01a2e1dc88a96d';

    public $import = 'TimeSlotPattern';

    public function init()
    {
        $this->records = [
            [
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'schedule' => [
                    [
                        'branch_id' => '49a7011a05c677b9a916612a',
                        'namespace' => 'glofox',
                        'active' => true,
                        'week_day' => 6,
                        'start_time' => '8:00',
                        'end_time' => '09:00',
                        '_start_time' => '2016-12-19T08:00:00.000Z',
                        '_end_time' => '2016-12-19T09:00:00.000Z',
                        '_total' => 1,
                        'code' => '18585794eaccf4c',
                    ],
                ],
                'time_slot_length' => 60,
                'pricing' => 'CUSTOM',
                'date_start' => date('Y-m-d', strtotime('now')),
                'date_finish' => date('Y-m-d', strtotime('+6 month')),
                'allowed_member_types' => [
                    [
                        'price' => 10,
                        'memberships' => [
                            [
                                'id' => 0,
                                'label' => 'PAYG',
                            ],
                        ],
                        'type' => 'payg',
                    ],
                ],
                'model_id' => '59a7011a05c677bda916612c',
                'model' => 'users',
                'created' => new MongoDate(),
                'modified' => new MongoDate(),
            ],
            [
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'schedule' => [
                    [
                        'branch_id' => '49a7011a05c677b9a916612a',
                        'namespace' => 'glofox',
                        'active' => true,
                        'week_day' => 6,
                        'start_time' => '8:00',
                        'end_time' => '09:00',
                        '_start_time' => '2016-12-19T08:00:00.000Z',
                        '_end_time' => '2016-12-19T09:00:00.000Z',
                        '_total' => 1,
                        'code' => '18585794eaccf4d',
                    ],
                ],
                'time_slot_length' => 60,
                'pricing' => 'CUSTOM',
                'date_start' => date('Y-m-d', strtotime('now')),
                'date_finish' => date('Y-m-d', strtotime('+6 month')),
                'allowed_member_types' => [
                    [
                        'price' => 10,
                        'memberships' => [
                            [
                                'id' => 0,
                                'label' => 'PAYG',
                            ],
                        ],
                        'type' => 'payg',
                    ],
                    [
                        'price' => 8,
                        'memberships' => [
                            [
                                'id' => 0,
                                'label' => 'member',
                            ],
                        ],
                        'type' => 'member',
                    ],
                ],
                'model_id' => '52a7011a05c677bda826611b',
                'model' => 'facilities',
                'created' => new MongoDate(),
                'modified' => new MongoDate(),
            ],
            [
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'schedule' => [
                    [
                        'active' => true,
                        'week_day' => 6,
                        'start_time' => '8:00',
                        'end_time' => '09:00',
                        '_start_time' => '2016-12-19T08:00:00.000Z',
                        '_end_time' => '2016-12-19T09:00:00.000Z',
                        '_total' => 1,
                        'code' => '28245796wfvgl4c',
                    ],
                ],
                'time_slot_length' => 60,
                'pricing' => 'CUSTOM',
                'date_start' => date('Y-m-d', strtotime('now')),
                'date_finish' => date('Y-m-d', strtotime('+6 month')),
                'allowed_member_types' => [
                    [
                        'price' => 8,
                        'memberships' => [
                            [
                                'id' => 0,
                                'label' => 'member',
                            ],
                        ],
                        'type' => 'member',
                    ],
                ],
                'model_id' => '5b83ede21e226c745e344b43',
                'model' => 'facilities',
                'created' => new MongoDate(),
                'modified' => new MongoDate(),
            ],
            [
                'branch_id' => '5addc25383266f65abf515c4',
                'namespace' => 'classpass_integrated',
                'active' => true,
                'schedule' => [
                    [
                        'active' => true,
                        'week_day' => 6,
                        'start_time' => '8:00',
                        'end_time' => '09:00',
                        '_start_time' => '2016-12-19T08:00:00.000Z',
                        '_end_time' => '2016-12-19T09:00:00.000Z',
                        '_total' => 1,
                        'code' => '88242793wfvgl5c',
                    ],
                ],
                'time_slot_length' => 60,
                'pricing' => 'SINGLE',
                'date_start' => date('Y-m-d', strtotime('now')),
                'date_finish' => date('Y-m-d', strtotime('+6 month')),
                'allowed_member_types' => [
                    [
                        'price' => 5,
                        'type' => 'member',
                    ],
                ],
                'model_id' => '5baa63007da70701ce7e4743',
                'model' => 'facilities',
                'created' => new MongoDate(),
                'modified' => new MongoDate(),
            ],
            [
                'branch_id' => '5addc25383266f65abf515c4',
                'namespace' => 'classpass_integrated',
                'active' => true,
                'schedule' => [
                    [
                        'active' => true,
                        'week_day' => 6,
                        'start_time' => '8:00',
                        'end_time' => '09:00',
                        '_start_time' => '2016-12-19T08:00:00.000Z',
                        '_end_time' => '2016-12-19T09:00:00.000Z',
                        '_total' => 1,
                        'code' => '44242793wfvgl5c',
                    ],
                ],
                'time_slot_length' => 60,
                'pricing' => 'SINGLE',
                'date_start' => date('Y-m-d', strtotime('now')),
                'date_finish' => date('Y-m-d', strtotime('+6 month')),
                'allowed_member_types' => [
                    [
                        'price' => 8,
                        'type' => 'member',
                    ],
                    [
                        'price' => 16,
                        'type' => 'payg',
                    ],
                ],
                'model_id' => '5adf4d52f35bbcfb4251f960',
                'model' => 'users',
                'created' => new MongoDate(),
                'modified' => new MongoDate(),
            ],
            [
                'branch_id' => '5addc25383266f65abf515c4',
                'namespace' => 'classpass_integrated',
                'active' => true,
                'schedule' => [
                    [
                        'active' => true,
                        'week_day' => 6,
                        'start_time' => '8:00',
                        'end_time' => '09:00',
                        '_start_time' => '2016-12-19T08:00:00.000Z',
                        '_end_time' => '2016-12-19T09:00:00.000Z',
                        '_total' => 1,
                        'code' => '44242793wfvgl5c',
                    ],
                ],
                'time_slot_length' => 60,
                'pricing' => 'CUSTOM',
                'date_start' => date('Y-m-d', strtotime('now')),
                'date_finish' => date('Y-m-d', strtotime('+6 month')),
                'allowed_member_types' => [
                    [
                        'price' => 7,
                        'memberships' => [
                            [
                                'id' => '5b19486ac13acae3b31cf562',
                                'label' => 'Membership Name',
                            ],
                        ],
                        'type' => 'member',
                        'membership_id' => '5b19486ac13acae3b31cf562',
                    ],
                    [
                        'price' => 12,
                        'memberships' => [
                            [
                                'id' => 0,
                                'label' => 'PAYG',
                            ],
                        ],
                        'type' => 'payg',
                    ],
                ],
                'model_id' => '5bab6151a376c1eabda143e8',
                'model' => 'users',
                'created' => new MongoDate(),
                'modified' => new MongoDate(),
            ],
            [
                'branch_id' => '5b5b265bce2cf6bdb9ad6d9a',
                'namespace' => 'ukclient',
                'active' => true,
                'schedule' => [
                    [
                        'active' => true,
                        'week_day' => 6,
                        'start_time' => '8:00',
                        'end_time' => '09:00',
                        '_start_time' => '2016-12-19T08:00:00.000Z',
                        '_end_time' => '2016-12-19T09:00:00.000Z',
                        '_total' => 1,
                        'code' => '44242793wfvgl5c',
                    ],
                ],
                'time_slot_length' => 60,
                'pricing' => 'CUSTOM',
                'date_start' => date('Y-m-d', strtotime('now')),
                'date_finish' => date('Y-m-d', strtotime('+6 month')),
                'allowed_member_types' => [
                    [
                        'price' => 22,
                        'memberships' => [
                            [
                                'id' => 0,
                                'label' => 'PAYG',
                            ],
                        ],
                        'type' => 'payg',
                    ],
                ],
                'model_id' => '5b5b28f72e6cb30f934ee2d8',
                'model' => 'users',
                'created' => new MongoDate(),
                'modified' => new MongoDate(),
            ],
            [
                '_id' => new MongoId('62a31ef9cbe8aafde95b744d'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'name' => 'Some Appointment',
                'description' => 'Some Description',
                'time_slot_length' => 60,
                'pricing' => 'CUSTOM',
                'categories' => [],
                'allowed_member_types' => [
                    [
                        'price' => 22,
                        'memberships' => [
                            [
                                'id' => 0,
                                'label' => 'PAYG',
                            ],
                        ],
                        'type' => 'payg',
                    ],
                ],
                'staff_ids' => [
                    '59a7011a05c677bda916612c',
                ],
                'model' => 'users',
                'created' => new MongoDate(),
                'modified' => new MongoDate(),
            ],
            [
                '_id' => new MongoId('584ac642dd3c2fbfe13e910b'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'name' => 'Appoinment to delete',
                'description' => 'To be deleted',
                'time_slot_length' => 60,
                'pricing' => 'CUSTOM',
                'allowed_member_types' => [
                    [
                        'price' => 10,
                        'memberships' => [
                            [
                                'id' => 0,
                                'label' => 'PAYG',
                            ],
                        ],
                        'type' => 'payg',
                    ],
                ],
                'staff_ids' => [
                    '59a7011a05c677bda916612c',
                ],
                'model' => 'users',
                'created' => new MongoDate(),
                'modified' => new MongoDate(),
            ],
            [
                '_id' => new MongoId('62a70487b9be8ef0bf86d521'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'name' => 'Personal Training',
                'description' => 'PT Description',
                'time_slot_length' => 45,
                'pricing' => 'SINGLE',
                'categories' => [],
                'allowed_member_types' => [
                    [
                        'price' => 22,
                        'memberships' => [
                            [
                                'id' => 0,
                                'label' => 'PAYG',
                            ],
                        ],
                        'type' => 'payg',
                    ],
                ],
                'staff_ids' => [
                    '59a7011a05c677bda916612c',
                ],
                'model' => 'users',
                'created' => new MongoDate(),
                'modified' => new MongoDate(),
            ],
            [
                '_id' => new MongoId('62a70487b9be8ef0bf86d523'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'name' => 'Appointment name',
                'description' => 'PT Description',
                'time_slot_length' => 45,
                'pricing' => 'SINGLE',
                'categories' => [
                    '59ba57b0e5c2b7ae3683a018'
                ],
                'allowed_member_types' => [
                    [
                        'price' => 22,
                        'memberships' => [
                            [
                                'id' => 0,
                                'label' => 'PAYG',
                            ],
                        ],
                        'type' => 'payg',
                    ],
                ],
                'staff_ids' => [
                    '59a7011a05c677bda916612c',
                ],
                'created' => new MongoDate(strtotime('-1 day')),
                'modified' => new MongoDate(),
            ],
            [
                '_id' => new MongoId('62a70487b9be8ef0bf86d524'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'name' => 'Trainer Appointment',
                'description' => 'Trainer Testing Appointment',
                'time_slot_length' => 60,
                'pricing' => 'SINGLE',
                'categories' => [
                    '59ba57b0e5c2b7ae3683a018'
                ],
                'allowed_member_types' => [
                    [
                        'price' => 22,
                        'memberships' => [
                            [
                                'id' => 0,
                                'label' => 'PAYG',
                            ],
                        ],
                        'type' => 'payg',
                    ],
                ],
                'staff_ids' => [
                    '58568a8fa875ab19530041a7',
                    '59a3010a05c677bda916619b'
                ],
                'created' => new MongoDate(),
                'modified' => new MongoDate(),
            ],
            [
                '_id' => new MongoId('62a70487b9be8ef0bf86d525'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'name' => 'Trainer Appointment 2',
                'description' => 'Trainer Testing Appointment',
                'time_slot_length' => 60,
                'pricing' => 'SINGLE',
                'categories' => [
                    '59ba57b0e5c2b7ae3683a018'
                ],
                'allowed_member_types' => [
                    [
                        'price' => 22,
                        'memberships' => [
                            [
                                'id' => 0,
                                'label' => 'PAYG',
                            ],
                        ],
                        'type' => 'payg',
                    ],
                ],
                'staff_ids' => [
                    '59a3010a05c677bda916619b'
                ],
            ],
            [
                '_id' => new MongoId('633bc4e9368fcc708a18617c'),
                'branch_id' => '633ad68b69f7280b4564aee3',
                'namespace' => 'bookingsmetadata',
                'active' => true,
                'name' => 'Trainer Appointment',
                'description' => 'Trainer Testing Appointment',
                'time_slot_length' => 60,
                'pricing' => 'CUSTOM',
                'categories' => [],
                'allowed_member_types' => [
                    [
                        'price' => 22,
                        'memberships' => [
                            [
                                'id' => 0,
                                'label' => 'PAYG',
                            ],
                        ],
                        'type' => 'payg',
                    ],
                    [
                        'price' => 18,
                        'type' => 'member',
                        'membership_id' => '54107c1cd7b6ddc3a98b4577',
                        'memberships' => [
                            [
                                'id' => '54107c1cd7b6ddc3a98b4577',
                                'label' => 'CP Unlimited Paid in full'
                            ],
                        ],
                    ],
                ],
                'staff_ids' => ['633bc63c8b1c7ae549112f2f', '6399dcc04111d0dededb266e'],
            ],
            [
                '_id' => new MongoId('633bc4e9368fcc708a1861ee'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'active' => true,
                'name' => 'TrainerAvailabilityErrorTest-DoNotUse',
                'description' => 'This Appointment should have no timeslots!',
                'time_slot_length' => 60,
                'pricing' => 'CUSTOM',
                'categories' => [],
                'allowed_member_types' => [
                    [
                        'price' => 22,
                        'memberships' => [
                            [
                                'id' => 0,
                                'label' => 'PAYG',
                            ],
                        ],
                        'type' => 'payg',
                    ],
                ],
                'staff_ids' => ['59a7011a05c677bda916612c'],
            ],
            ...$this->addAppointmentsForTestingCancelBookingTrainerAvailabilitySlice1(),
            ...$this->addAppointmentsForTestingBookingsOverlappingValidations(),
            ...$this->addAppointmentsForBatchIdTesting(),
            ...$this->addAppointmentsBookingsListAllBookingsTest(),
            ...$this->forVirtualSlotTest(),
            ...$this->addAppointmentsForPriceCalculation(),
            ...$this->forBookingWindow(),
            ...$this->forVirtualAppointmentTrainerDelete(),
            ...$this->forPrivateAppointments(),
            ...$this->forPayroll(),
            ...$this->forSemiPrivateAppointments()
        ];

        parent::init();
    }

    private function addAppointmentsForTestingCancelBookingTrainerAvailabilitySlice1(): array
    {
        // Test cancel booking trainer availability service slice1
        return [
            [
                '_id' => new MongoId('636e036092c5cd5655bbdf33'),
                'branch_id' => static::GLOFOX_BRANCH_ID,
                'namespace' => static::GLOFOX_NAMESPACE,
                'active' => true,
                'name' => 'Trainer Availability Appointment cancel',
                'description' => 'Trainer Availability Appointment cancel description',
                'time_slot_length' => 60,
                'pricing' => 'CUSTOM',
                'categories' => [],
                'allowed_member_types' => [
                    [
                        'price' => 22,
                        'memberships' => [
                            [
                                'id' => 0,
                                'label' => 'PAYG',
                            ],
                        ],
                        'type' => 'payg',
                    ],
                    [
                        'price' => 18,
                        'type' => 'member',
                        'membership_id' => '54107c1cd7b6ddc3a98b4577',
                        'memberships' => [
                            [
                                'id' => '54107c1cd7b6ddc3a98b4577',
                                'label' => 'CP Unlimited Paid in full'
                            ],
                        ],
                    ],
                ],
                'staff_ids' => [static::GLOFOX_STAFF_ID],
            ],
        ];
    }

    private function addAppointmentsForTestingBookingsOverlappingValidations(): array
    {
        return [
            [
                '_id' => new MongoId('637b70c21d8141da3fab4d4e'),
                'branch_id' => static::GLOFOX_BRANCH_ID,
                'namespace' => static::GLOFOX_NAMESPACE,
                'active' => true,
                'name' => 'Appointment to test overlapping validations 1',
                'description' => 'Appointment to test overlapping validations description 1',
                'time_slot_length' => 60,
                'pricing' => 'CUSTOM',
                'categories' => [],
                'allowed_member_types' => [
                    [
                        'price' => 1,
                        'memberships' => [
                            [
                                'id' => 0,
                                'label' => 'PAYG',
                            ],
                        ],
                        'type' => 'payg',
                    ],
                ],
                'staff_ids' => [static::GLOFOX_STAFF_ID, '58568a8fa875ab19530041a7', '58568a8fa875ab19530041b8'],
            ],
            [
                '_id' => new MongoId('637b74f4db2de0a76680c598'),
                'branch_id' => static::GLOFOX_BRANCH_ID,
                'namespace' => static::GLOFOX_NAMESPACE,
                'active' => true,
                'name' => 'Appointment to test overlapping validations 2',
                'description' => 'Appointment to test overlapping validations description 2',
                'time_slot_length' => 30,
                'pricing' => 'CUSTOM',
                'categories' => [],
                'allowed_member_types' => [
                    [
                        'price' => 2,
                        'memberships' => [
                            [
                                'id' => 0,
                                'label' => 'PAYG',
                            ],
                        ],
                        'type' => 'payg',
                    ],
                ],
                'staff_ids' => [static::GLOFOX_STAFF_ID],
            ],
            [
                '_id' => new MongoId('6384ad09ba0783fea4e9c902'),
                'branch_id' => static::GLOFOX_BRANCH_ID,
                'namespace' => static::GLOFOX_NAMESPACE,
                'active' => true,
                'name' => 'Appointment to test overlapping validations 3',
                'description' => 'Appointment to test overlapping validations description 3',
                'time_slot_length' => 60,
                'pricing' => 'CUSTOM',
                'categories' => [],
                'allowed_member_types' => [
                    [
                        'price' => 1,
                        'memberships' => [
                            [
                                'id' => 0,
                                'label' => 'PAYG',
                            ],
                        ],
                        'type' => 'payg',
                    ],
                ],
                'staff_ids' => ['62d8f0a7f592084274a9868e'],
            ],
        ];
    }

    private function addAppointmentsForBatchIdTesting(): array
    {
        return [
            [
                '_id' => new MongoId('637b70c21d8141da3fab4d45'),
                'branch_id' => static::GLOFOX_BRANCH_ID,
                'namespace' => static::GLOFOX_NAMESPACE,
                'active' => true,
                'name' => 'Appointment to test batch ID',
                'description' => 'Appointment to test batch id',
                'time_slot_length' => 60,
                'pricing' => 'CUSTOM',
                'categories' => [],
                'allowed_member_types' => [
                    [
                        'price' => 1,
                        'memberships' => [
                            [
                                'id' => 0,
                                'label' => 'PAYG',
                            ],
                        ],
                        'type' => 'payg',
                    ],
                ],
                'staff_ids' => [static::GLOFOX_STAFF_ID],
            ]
        ];
    }

    private function addAppointmentsBookingsListAllBookingsTest(): array
    {
        return [
            [
                '_id' => new MongoId('64365bc0aa5a635ccacd1a28'),
                'branch_id' => '6435563a6272b68401e85728',
                'namespace' => 'listallbookings',
                'active' => true,
                'name' => 'Appointment to test list all bookings',
                'description' => 'Appointment to test list all bookings',
                'time_slot_length' => 60,
                'pricing' => 'CUSTOM',
                'categories' => [],
                'allowed_member_types' => [
                    [
                        'price' => 1,
                        'memberships' => [
                            [
                                'id' => 0,
                                'label' => 'PAYG',
                            ],
                        ],
                        'type' => 'payg',
                    ],
                ],
                'staff_ids' => ['64356ba8219f042b5adf2bea'],
            ]
        ];
    }

    private function forVirtualSlotTest(): array
    {
        return [
            [
                '_id' => new MongoId('6461ef0d1f0de25d31b39abc'),
                'branch_id' => static::VIRTUAL_SLOT_BRANCH_ID,
                'namespace' => static::VIRTUAL_SLOT_BRANCH_NAMESPACE,
                'active' => true,
                'name' => 'Appointment to test virtual slots',
                'description' => 'Appointment to test virtual slots description',
                'time_slot_length' => 60,
                'pricing' => 'CUSTOM',
                'categories' => [],
                'allowed_member_types' => [
                    [
                        'price' => 1,
                        'memberships' => [
                            [
                                'id' => 0,
                                'label' => 'PAYG',
                            ],
                        ],
                        'type' => 'payg',
                    ],
                ],
                'staff_ids' => [static::VIRTUAL_SLOT_STAFF_ID],
                'private' => false
            ],
        ];
    }

    private function addAppointmentsForPriceCalculation(): array
    {
        return [
            [
                '_id' => new MongoId('64365bc0aa5a635ccacd1a29'),
                'branch_id' => '5b5b265bce2cf6bdb9ad6d9a',
                'namespace' => 'ukclient',
                'active' => true,
                'name' => 'Appointment to test list all bookings',
                'description' => 'Appointment to test list all bookings',
                'time_slot_length' => 60,
                'pricing' => 'CUSTOM',
                'categories' => [],
                'allowed_member_types' => [
                    [
                        'price' => 11,
                        'memberships' => [
                            [
                                'id' => 0,
                                'label' => 'PAYG',
                            ],
                        ],
                        'type' => 'payg',
                    ],
                ],
                'staff_ids' => ['5b5b28f72e6cb30f934ee2d8'],
            ]
        ];
    }

    private function forBookingWindow(): array
    {
        return [
            [
                '_id' => new MongoId('64747cd5a504eaf1712ffbe2'),
                'branch_id' => '646de8d3c539cfc3cb649547',
                'namespace' => 'appointmentsbookingwindow',
                'active' => true,
                'name' => 'Personal Training',
                'description' => 'PT Description',
                'time_slot_length' => 45,
                'pricing' => 'SINGLE',
                'categories' => [],
                'allowed_member_types' => [
                    [
                        'price' => 22,
                        'memberships' => [
                            [
                                'id' => 0,
                                'label' => 'PAYG',
                            ],
                        ],
                        'type' => 'payg',
                    ],
                ],
                'staff_ids' => [
                    '59a7011a05c677bda916612c',
                ],
                'model' => 'users',
                'created' => new MongoDate(),
                'modified' => new MongoDate(),
            ],
        ];
    }

    private function forVirtualAppointmentTrainerDelete(): array
    {
        return [
            [
                '_id' => new MongoId('64747cd5a504eaf1712ffbe3'),
                'branch_id' => '646de8d3c539cfc3cb649547',
                'namespace' => 'appointmentsbookingwindow',
                'active' => true,
                'name' => 'Appointment for Trainer Deletion',
                'description' => 'Appointment for Trainer Deletion',
                'time_slot_length' => 60,
                'pricing' => 'CUSTOM',
                'categories' => [],
                'allowed_member_types' => [
                    [
                        'price' => 11,
                        'memberships' => [
                            [
                                'id' => 0,
                                'label' => 'PAYG',
                            ],
                        ],
                        'type' => 'payg',
                    ],
                ],
                'staff_ids' => [
                    '64747d4722817041ee4f8a29',
                ],
                'private' => false
            ],
            [
                '_id' => new MongoId('64747cd5a504eaf1712ffbe4'),
                'branch_id' => '646de8d3c539cfc3cb649547',
                'namespace' => 'appointmentsbookingwindow',
                'active' => true,
                'name' => 'Appointment for Trainer Removal',
                'description' => 'Appointment for Trainer Removal',
                'time_slot_length' => 60,
                'pricing' => 'CUSTOM',
                'categories' => [],
                'allowed_member_types' => [
                    [
                        'price' => 11,
                        'memberships' => [
                            [
                                'id' => 0,
                                'label' => 'PAYG',
                            ],
                        ],
                        'type' => 'payg',
                    ],
                ],
                'staff_ids' => [
                    '64747d4722817041ee4f8a30',
                ],
                'private' => false
            ]
        ];
    }

    private function forPrivateAppointments(): array
    {
        return [
            [
                '_id' => new MongoId('64747cd5a504eaf1712ffbf1'),
                'branch_id' => static::GLOFOX_BRANCH_ID,
                'namespace' => self::GLOFOX_NAMESPACE,
                'active' => true,
                'name' => 'Private Appointment 1',
                'description' => 'its private',
                'time_slot_length' => 60,
                'pricing' => 'CUSTOM',
                'categories' => [],
                'allowed_member_types' => [
                    [
                        'price' => 11,
                        'memberships' => [
                            [
                                'id' => 0,
                                'label' => 'PAYG',
                            ],
                        ],
                        'type' => 'payg',
                    ],
                ],
                'staff_ids' => [
                    static::GLOFOX_STAFF_ID,
                ],
                'private' => true
            ],
            [
                '_id' => new MongoId('64747cd5a504eaf1712ffbf2'),
                'branch_id' => static::GLOFOX_BRANCH_ID,
                'namespace' => self::GLOFOX_NAMESPACE,
                'active' => true,
                'name' => 'Private Appointment 2',
                'description' => 'its really private',
                'time_slot_length' => 60,
                'pricing' => 'CUSTOM',
                'categories' => [],
                'allowed_member_types' => [
                    [
                        'price' => 11,
                        'memberships' => [
                            [
                                'id' => 0,
                                'label' => 'PAYG',
                            ],
                        ],
                        'type' => 'payg',
                    ],
                ],
                'staff_ids' => [
                    static::GLOFOX_STAFF_ID,
                ],
                'private' => true
            ],
            [
                '_id' => new MongoId('64747cd5a504eaf1712ffbf3'),
                'branch_id' => static::GLOFOX_BRANCH_ID,
                'namespace' => self::GLOFOX_NAMESPACE,
                'active' => true,
                'name' => 'Public Appointment 1',
                'description' => 'its not private',
                'time_slot_length' => 60,
                'pricing' => 'CUSTOM',
                'categories' => [],
                'allowed_member_types' => [
                    [
                        'price' => 11,
                        'memberships' => [
                            [
                                'id' => 0,
                                'label' => 'PAYG',
                            ],
                        ],
                        'type' => 'payg',
                    ],
                ],
                'staff_ids' => [
                    static::GLOFOX_STAFF_ID,
                ],
                'private' => false
            ]
        ];
    }

    private function forPayroll(): array
    {
        return [
            [
                '_id' => new MongoId('64b8068173b491198db332bc'),
                'branch_id' => '64b803ccfc8ba4d6f8d99c9c',
                'namespace' => 'payroll',
                'active' => true,
                'name' => 'Appointment for Payroll 1',
                'description' => 'Appointment for Payroll 1',
                'time_slot_length' => 60,
                'pricing' => 'CUSTOM',
                'categories' => [],
                'allowed_member_types' => [
                    [
                        'price' => 11,
                        'memberships' => [
                            [
                                'id' => 0,
                                'label' => 'PAYG',
                            ],
                        ],
                        'type' => 'payg',
                    ],
                ],
                'staff_ids' => [
                    '64b8045076c575dfcc56a9da',
                    '64b806df80db8155049eb1c0',
                ],
                'private' => false
            ]
        ];
    }

    private function forSemiPrivateAppointments(): array
    {
        return [
            [
                '_id' => new MongoId('64b8068173b491198db332bd'),
                'branch_id' => self::GLOFOX_BRANCH_ID,
                'namespace' => self::GLOFOX_NAMESPACE,
                'active' => true,
                'name' => 'Semi Private Appointment 1',
                'description' => 'Private Appointment',
                'time_slot_length' => 60,
                'pricing' => 'CUSTOM',
                'categories' => [],
                'allowed_member_types' => [
                    [
                        'price' => 11,
                        'memberships' => [
                            [
                                'id' => 0,
                                'label' => 'PAYG',
                            ],
                        ],
                        'type' => 'payg',
                    ],
                ],
                'staff_ids' => [
                    '59a7011a05c677bda916612c'
                ],
                'private' => false,
            ],
            [
                '_id' => new MongoId('66ebc6fcf728da7a01baaae0'),
                'name' => 'TimeSlotPattern with defined size',
                'branch_id' => self::GLOFOX_BRANCH_ID,
                'namespace' => self::GLOFOX_NAMESPACE,
                'active' => true,
                'description' => 'Semiprivate appointments with size',
                'time_slot_length' => 60,
                'pricing' => 'CUSTOM',
                'categories' => [],
                'allowed_member_types' => [
                    [
                        'price' => 11,
                        'memberships' => [
                            [
                                'id' => 0,
                                'label' => 'PAYG',
                            ],
                        ],
                        'type' => 'payg',
                    ],
                ],
                'staff_ids' => [
                    '59a7011a05c677bda916612c'
                ],
                'private' => false,
                'size' => 3
            ],
            [
                '_id' => new MongoId('66ebc6fcf728da7a01baaae1'),
                'name' => 'TimeSlotPattern with defined size',
                'branch_id' => self::GLOFOX_BRANCH_ID,
                'namespace' => self::GLOFOX_NAMESPACE,
                'active' => true,
                'description' => 'Semiprivate appointments with no size',
                'time_slot_length' => 60,
                'pricing' => 'CUSTOM',
                'categories' => [],
                'allowed_member_types' => [
                    [
                        'price' => 11,
                        'memberships' => [
                            [
                                'id' => 0,
                                'label' => 'PAYG',
                            ],
                        ],
                        'type' => 'payg',
                    ],
                ],
                'staff_ids' => [
                    '59a7011a05c677bda916612c'
                ],
                'private' => false
            ],
            [
                '_id' => new MongoId('66ebc6fcf728da7a01baaae2'),
                'name' => 'TimeSlotPattern Update Test',
                'branch_id' => self::GLOFOX_BRANCH_ID,
                'namespace' => self::GLOFOX_NAMESPACE,
                'active' => true,
                'description' => 'Semiprivate appointments with size',
                'time_slot_length' => 60,
                'pricing' => 'CUSTOM',
                'categories' => [],
                'allowed_member_types' => [
                    [
                        'price' => 11,
                        'memberships' => [
                            [
                                'id' => 0,
                                'label' => 'PAYG',
                            ],
                        ],
                        'type' => 'payg',
                    ],
                ],
                'staff_ids' => [
                    '59a7011a05c677bda916612c'
                ],
                'private' => false,
                'size' => 9
            ],
            [
                '_id' => new MongoId('66ebc6fcf728da7a01baa990'),
                'name' => 'TimeSlotPattern with size 4 to create time_slots',
                'branch_id' => self::GLOFOX_BRANCH_ID,
                'namespace' => self::GLOFOX_NAMESPACE,
                'active' => true,
                'description' => 'Semiprivate appointments with size 4 to create time_slots',
                'time_slot_length' => 60,
                'pricing' => 'CUSTOM',
                'categories' => [],
                'allowed_member_types' => [
                    [
                        'price' => 0,
                        'memberships' => [
                            [
                                'id' => 0,
                                'label' => 'PAYG',
                            ],
                        ],
                        'type' => 'payg',
                    ],
                ],
                'staff_ids' => [
                    '59a7011a05c677bda916612c'
                ],
                'private' => false,
                'size' => 4
            ],
        ];
    }
}
