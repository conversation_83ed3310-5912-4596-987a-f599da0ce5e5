<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\AppointmentSlots\Events\Listeners;

use App;
use Carbon\Carbon;
use Glofox\Domain\AppointmentSlots\Events\Listeners\RemoveOverlappingSlots;
use Glofox\Domain\AppointmentSlots\UseCase\RemoveAppointmentSlot;
use Glofox\Domain\Bookings\Events\TimeslotBookingWasCreated;
use Glofox\Domain\TimeSlots\Repositories\TimeSlotRepository;
use Mockery;

App::import('Test/Case', 'GlofoxTestCase');

class RemoveOverlappingSlotsTest extends \GlofoxTestCase
{
    public $fixtures = [];
    private TimeSlotRepository $mockTimeSlotsRepository;
    private RemoveAppointmentSlot $mockRemoveSlotUseCase;

    public function setUp()
    {
        parent::setUp();

        $this->mockTimeSlotsRepository = $this->createMock(TimeSlotRepository::class);
        $this->mockRemoveSlotUseCase = $this->createMock(RemoveAppointmentSlot::class);
    }

    public function tearDown()
    {
        parent::tearDown();

        $this->teardownLogger();

        Mockery::close();
    }

    public function testRemoveOverlappingSlots(): void
    {
        $appointmentSlotId = 'test-appointment-id';
        $bookingId = 'test-bookings-id';
        $staffId = 'test-staff-id';
        $branchId = 'test-branch-id';
        $timeStart = new \MongoDate();
        $timeFinish = new \MongoDate();

        $booking = [
            'Booking' => [
                '_id' => $bookingId,
                'time_start' => $timeStart,
                'time_finish' => $timeFinish,
            ],
        ];

        $appointmentSlot = [
            'TimeSlot' => [
                '_id' => $appointmentSlotId,
                'staff_id' => $staffId,
                'model' => 'appointments',
                'branch_id' => $branchId,
            ],
        ];

        $this->mockTimeSlotsRepository->expects($this->once())
            ->method('getOverLappingSlots')
            ->with(
                $staffId,
                $branchId,
                Carbon::createFromTimestamp($timeStart->sec),
                Carbon::createFromTimestamp($timeFinish->sec),
                $appointmentSlotId
            )
            ->will(
                $this->returnValue([
                    [
                        '_id' => 'overlapping-slot-id',
                    ],
                ])
            );

        $this->mockRemoveSlotUseCase->expects($this->once())
            ->method('execute')
            ->with('overlapping-slot-id', $bookingId);

        $listener = new RemoveOverlappingSlots(
            $this->mockTimeSlotsRepository,
            $this->mockRemoveSlotUseCase,
            $this->loggerMock,
        );

        $event = new TimeslotBookingWasCreated(
            [],
            [],
            $booking,
            [],
            $appointmentSlot
        );

        $listener->handle($event);
    }

    public function testNotRemoveOverlappingSlotsWhenModelIsNotAppointment(): void
    {
        $appointmentSlot = [
            'TimeSlot' => [
                '_id' => 'test-slot-id',
                'model' => 'users',
            ],
        ];

        $this->mockTimeSlotsRepository->expects($this->never())->method($this->anything());

        $listener = new RemoveOverlappingSlots(
            $this->mockTimeSlotsRepository,
            $this->mockRemoveSlotUseCase,
            $this->loggerMock,
        );

        $event = new TimeslotBookingWasCreated(
            [],
            [],
            [],
            [],
            $appointmentSlot
        );

        $listener->handle($event);

        $this->loggerMock->shouldHaveReceived('info')->with(
            sprintf(
                '%s: Not an appointment slot. Skipping...',
                'RemoveOverlappingSlots'
            ),
            [
                'id' => 'test-slot-id',
            ]
        );
    }
}
