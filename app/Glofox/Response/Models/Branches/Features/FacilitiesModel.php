<?php

namespace Glofox\Response\Models\Branches\Features;

use Glofox\Response\BaseModel;

/**
 * @SuppressWarnings(PHPMD)
 */
class FacilitiesModel extends BaseModel
{
    /**
     * @var int
     */
    public $order = 0;

    /**
     * @var bool
     */
    public $enabled = false;

    /**
     * @var
     */
    public $booking;

    /**
     * @var bool
     */
    public $_booking_open_window = false;

    /**
     * @var bool
     */
    public $_booking_close_window = false;

    /**
     * @var bool
     */
    public $booking_enabled = false;

    /**
     * @var int
     */
    public $booking_weeks_display = 0;

    /**
     * @var int
     */
    public $booking_cancel_window = 0;

    /**
     * @var int
     */
    public $booking_open_window = 0;

    /**
     * @var int
     */
    public $booking_close_window = 0;

    /**
     * @var int
     */
    public $booking_time_slot_length = 0;

    protected array $allowedFields = [
        'order',
        'enabled',
        'booking',
        '_booking_open_window',
        '_booking_close_window',
        'booking_enabled',
        'booking_weeks_display',
        'booking_cancel_window',
        'booking_open_window',
        'booking_close_window',
        'booking_time_slot_length',
    ];

    public function __construct(array $data)
    {
        $this->setModelFields($data);
    }
}
