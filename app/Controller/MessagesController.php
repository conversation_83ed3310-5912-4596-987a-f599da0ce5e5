<?php

App::uses('AppController', 'Controller');

use Glofox\Domain\Authentication\Auth;
use Glofox\Domain\Branches\Repositories\BranchesRepository;
use Glofox\Domain\Charges\Models\Charge;
use Glofox\Domain\Communications\Exceptions\ContentModerationException;
use Glofox\Domain\Communications\UseCase\ModerateMessage;
use Glofox\Domain\Communications\UseCase\ModerationContext;
use Glofox\Domain\Transactional\Composer as MessageComposer;
use Glofox\Domain\Transactional\Filters\ElectronicAgreementsTemplateFilter;
use Glofox\Domain\Transactional\Filters\OnlineBookingConfirmationTemplateFilter;
use Glofox\Domain\Transactional\MessageDispatcher;
use Glofox\Domain\Transactional\Messages\Factory as TransactionalMessagesFactory;
use Glofox\Domain\Transactional\Messages\Identifier;
use Glofox\Domain\Transactional\Messages\Message;
use Glofox\Domain\Transactional\Messages\Variables\ExampleVariablesCollectionFactory;
use Glofox\Domain\Transactional\Models\TransactionalMessage;
use Glofox\Domain\Transactional\Repositories\TransactionalMessagesRepository;
use Glofox\Domain\Transactional\Requests\Preview as PreviewRequest;
use Glofox\Domain\Transactional\Requests\UpsertMessageRequest;
use Glofox\Domain\Transactional\Sanitizers\TemplateContentSanitizer;
use Glofox\Domain\Transactional\Templates\Template;
use Glofox\Domain\Transactional\Transformers\TransactionalMesagesTransformer;
use Glofox\Domain\Transactional\Triggers\Trigger;
use Glofox\Domain\Transactional\Validation\Validator as TransactionalMessageValidator;
use Glofox\Infrastructure\Email\EmailValidator;

class MessagesController extends AppController
{
    /**
     * @var string
     */
    public $name = 'Messages';

    /**
     * @var array
     */
    public $uses = [
        'TransactionalMessage',
        'Branch',
        'User',
    ];

    /**
     * @var array
     */
    public $components = [
        'JWT',
        'Utility',
    ];

    /**
     * Dispatcher for Model.
     *
     * @param string $identifier [description]
     *
     * @return string
     */
    public function dispatcher($identifier = null)
    {
        return parent::dispatch($identifier, $this->TransactionalMessage);
    }

    /**
     * @return string
     *
     * @throws Exception
     */
    public function templates()
    {
        $this->JWT->validate();

        /** @var array $user */
        $user = $this->JWT->getUser();

        // Add these via IOC once we have the Container in place.
        $branchesRepository = new BranchesRepository();
        $transactionalMessagesTransformer = new TransactionalMesagesTransformer();
        $transactionalMessagesRepository = new TransactionalMessagesRepository();

        // Retrieve Branch Object
        $branch = $branchesRepository->findById($user['branch_id']);

        // Retrieve all Message Templates
        $defaults = $transactionalMessagesRepository->findAll();

        // Retrieve Messages Settings from Branch
        $settings = $transactionalMessagesRepository->findAllByBranch($branch);

        // Override the Messages templates with the branch settings
        $response = $transactionalMessagesTransformer->transform($defaults, $settings);

        $messageFilters = [
            app()->make(OnlineBookingConfirmationTemplateFilter::class),
            app()->make(ElectronicAgreementsTemplateFilter::class),
        ];
        foreach ($messageFilters as $messageFilter) {
            $response = $messageFilter->execute($response, $user['branch_id']);
        }

        return response()->json($response);
    }

    public function upsert(
        UpsertMessageRequest $request,
        EmailValidator $emailValidator
    ) {
        /** @var \Glofox\Domain\Users\Models\User $user */
        $user = Auth::user();

        // "TODO: These should go in the UpsertMessageRequest object in the future"
        // Add this via IOC once we have the Container in place.
        $branchesRepository = new BranchesRepository();

        // Retrieveing the defined schema.
        $fillable = $this->TransactionalMessage->fillable();
        $fillable = array_flip($fillable);

        // Retrieving user input and filtering it according to the defined Schema
        $input = json_decode($this->request->input(), true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        $data = array_intersect_key($input, $fillable);
        $data['branch_id'] = $user->currentBranchId();

        if (!isset($data['identifier'])) {
            throw new Exception('Identifier is expected');
        }

        if (isset($data['triggers'])) {
            if (!is_array($data['triggers'])) {
                throw new Exception('Triggers are expected to be array');
            }

            $fillable = Trigger::fillable();
            $fillable = array_flip($fillable);

            foreach ($data['triggers'] as &$trigger) {
                $trigger = array_intersect_key($trigger, $fillable);
            }
        }

        // Trim all email addresses
        if (isset($data['copy']['emails'])) {
            if (is_array($data['copy']['emails'])) {
                foreach ($data['copy']['emails'] as &$email) {
                    $email = trim($email);
                    $emailValidator->validate($email);
                }
            }
        }

        // If this fails, it will throw an exception therefore validating the validity of the identifier.
        $identifier = Identifier::byName($data['identifier']);
        //endregion

        // Find branch to check if the branch id exists.
        $branch = $branchesRepository->findById($data['branch_id']);

        if (!$branch) {
            // TODO: throw new BranchNotException($data['branch_id']);
            throw new Exception('Branch id ' . $data['branch_id'] . ' not found');
        }
        // Get branch message settings, if it exists.
        $transactionalMessagesRepository = new TransactionalMessagesRepository();
        $message = $transactionalMessagesRepository->findByBranchAndIdentifier($branch, $identifier);

        // If the branch aready has settings for the given email...
        if ($message) {
            // Replace the current settings with the input
            foreach ($data as $key => $value) {
                $message['TransactionalMessage'][$key] = $value;
            }
        } else {
            // Otherwise, just create a new one.
            $message = [
                'TransactionalMessage' => $data,
            ];
        }

        /** @var TransactionalMessageValidator $transactionalMessageValidator */
        $transactionalMessageValidator = $this->container()->make(TransactionalMessageValidator::class);
        $transactionalMessageValidator->withData($message['TransactionalMessage'])->validate();

        $message = new TransactionalMessage($message['TransactionalMessage']);
        $messageSanitizer = app()->make(TemplateContentSanitizer::class);
        $message = $messageSanitizer->execute($message);

        $moderator = app()->make(ModerateMessage::class);

        try {
            $context = new ModerationContext($branch['Branch']['_id'], $identifier);
            $moderator->moderate($message, $context);
        } catch (ContentModerationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'MESSAGE_RISKY',
            ]);
        }

        // Persist changes to the repository.
        $transactionalMessagesRepository->save($message->toLegacy());

        return response()->json([
            'success' => true,
            'message' => 'SAVE_MESSAGE_CONFIGURATION_SUCCESS',
        ]);
    }

    /**
     * @param PreviewRequest|Preview          $request
     * @param BranchesRepository              $branchesRepository
     * @param TransactionalMessagesRepository $transactionalMessagesRepository
     * @param MessageDispatcher               $messageDispatcher
     * @param TransactionalMessagesFactory    $transactionalMessagesFactory
     *
     * @return bool
     *
     * @internal param TemplateManager $templateManager
     * @internal param MessageSender $messageSender
     * @internal param MessageComposer $messageComposer
     */
    public function preview(
        PreviewRequest $request,
        BranchesRepository $branchesRepository,
        TransactionalMessagesRepository $transactionalMessagesRepository,
        MessageDispatcher $messageDispatcher,
        TransactionalMessagesFactory $transactionalMessagesFactory
    ) {

        if (!$request->isMethod('POST')) {
            throw new HttpRequestMethodException();
        }

        /** @var array $user */
        $user = $this->JWT->getUser();
        $branchId = $user['branch_id'];
        $userId = $user['_id'];

        $subject = $request->data()->get('subject');
        $content = $request->data()->get('content');

        $identifier = $request->data()->get('identifier');
        $identifier = Identifier::byName($identifier);

        $user = $this->User->findById($userId);
        $branch = $branchesRepository->findById($branchId);

        /** @var Message $message */
        $message = $transactionalMessagesRepository->findByBranchAndIdentifierWithDefaults($branch, $identifier);

        $message['subject'] = $subject;
        $message['content'] = $content;

        $message = $transactionalMessagesFactory->create($message);

        $variables = (new ExampleVariablesCollectionFactory($branch, $user))->make();
        $parsedMessage = (new MessageComposer($message, $variables))->compose();

        $charge = new Charge([
            'created' => new \MongoDate(),
        ]);

        $callback = function (Template $template) use ($charge) {
            $template->with('charge', $charge);
        };

        $success = $messageDispatcher->withBranch($branch)
                                     ->withTemplateCallback($callback)
                                     ->withMessageDefinition($message)
                                     ->dispatchTo($user, $parsedMessage);

        $response = [
            'success' => $success,
            'message' => 'MESSAGE_PREVIEW_SENT_SUCCESSFULY',
        ];

        return response()->json($response);
    }
}
