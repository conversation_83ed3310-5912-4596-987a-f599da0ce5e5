<?php

namespace Glofox\Response\Transformers;

use Glofox\Response\Models\NotificationModel;
use Glofox\Response\Models\BasicModels\MainModel;

/**
 * Class EventsTransformer
 * @package Glofox\Response\Transformers
 * @SuppressWarnings(PHPMD)
 */
class NotificationsTransformer extends BaseTransformer
{
    /**
     * @param array $results
     * @return MainModel
     */
    public function setFromArrayCollection(array $results) : MainModel
    {
        $dataModelArrayCollection = [];
        if (!empty($results) && is_array($results) && isset($results['data'])) {
            foreach ($results['data'] as $data) {
                $dataModelArrayCollection[] = $this->setNotificationModelFromData($data);
            }
        }

        return $this->setMainModelFromArray($results, $dataModelArrayCollection);
    }

    /**
     * @param array $notification
     * @return NotificationModel
     */
    public function setNotificationModelFromData(array $notification = []) : NotificationModel
    {
        return new NotificationModel(
            [
                '_id'  => $notification['_id'] ?? '',
                'branch_id'  => $notification['branch_id'] ?? '',
                'user_id'  => $notification['user_id'] ?? null,
                'user_type'  => $notification['user_type'] ?? null,
                'type'  => $notification['type'] ?? null,
                'message'  => $notification['message'] ?? '',
                'created'  => $notification['created'] ?? null,
                'modified'  => $notification['modified'] ?? null,
                'is_marketing'  => $notification['is_marketing'] ?? false
            ]
        );
    }
}
