type: object
properties:
  start_time:
    type: integer
    description: The local timestamp that the slot starts
  end_time:
    type: integer
    description: The local timestamp that the slot ends
  schedule_codes:
    type: array
    items:
      type: string
      description: refers to the code of the schedule slot retrieved
  user_id:
    type: string
    pattern: '^[a-f\d]{24}$'
    description: The id of the user
required:
  - start_time
  - end_time
  - schedule_codes
  - user_id
