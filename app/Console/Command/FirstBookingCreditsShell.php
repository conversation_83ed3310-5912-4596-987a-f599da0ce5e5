<?php

ini_set('max_execution_time', 600);

App::uses('Shell', 'Console');

use Glofox\Domain\Credits\Search\Expressions\StartsOn;
use Glofox\Domain\Credits\Search\Expressions\StartDate;
use Glofox\Domain\Credits\Search\Expressions\HasBookings;
use Glofox\Domain\Credits\Repositories\CreditsRepository;
use Glofox\Domain\Credits\Models\CreditPack;

class FirstBookingCreditsShell extends AppShell
{
    /**
     * @var Booking
     */
    protected $bookingCakeModel;

    /**
     * FirstBookingCreditsShell constructor.
     * @param null $stdout
     * @param null $stderr
     * @param null $stdin
     */
    public function __construct($stdout = null, $stderr = null, $stdin = null)
    {
        parent::__construct($stdout, $stderr, $stdin);

        $this->bookingCakeModel = ClassRegistry::init('Booking');
        $this->userCreditCakeModel = ClassRegistry::init('UserCredit');
        $this->updatedCredits = 0;
        $this->excludedIds = [];
    }

    public function main()
    {
        /**
         * This script was created to fix the credits affected by
         * https://glofox.atlassian.net/browse/DASH2-4986
         * It's supposed to be run just one time because we have fixed the root cause
         * of that issue already.
         */
        $this->log("~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~", LOG_INFO);
        $this->log("Cron Started: ". self::class, LOG_INFO);

        /** @var CreditsRepository $creditsRepository */
        $creditsRepository = app()->make(CreditsRepository::class);
        $startsOnFirstBookingDate = \MembershipPlanStartsOn::FIRST_BOOKING_DATE();
        $page = 1;
        $limit = 100;
        $credits = null;
        $branchId = $this->args[0] ?? null;

        while(($page == 1) || !empty($credits)) {
            $conditions = [
                '_id' => ['$nin' => $this->excludedIds],
                'starts_on' => \MembershipPlanStartsOn::FIRST_BOOKING_DATE,
                'start_date' => null,
                '$where' => 'this.bookings.length>0'
            ];

            if ($branchId){
                $conditions['branch_id'] = $branchId;
            }

            $credits = $this->userCreditCakeModel->find('all',[
                'conditions' => $conditions,
                'limit' => $limit
            ]);

            $this->setCreditPackDates($credits);
            $page++;
        }
        // "interval_count" : NumberLong(1)

        $this->log(json_encode(['creditsSkipped' => $this->excludedIds], JSON_PARTIAL_OUTPUT_ON_ERROR), LOG_INFO);
        $this->log(sprintf("%d credits updated",$this->updatedCredits), LOG_INFO);
        $this->log("Cron Finished", LOG_INFO);
    }

    /**
     * [setCreditPackDates description]
     * @param array $credits [description]
     */
    private function setCreditPackDates(array $credits)
    {
        foreach ($credits as $credit)
        {
            $credit = CreditPack::make($credit['UserCredit']);
            $timeStart = $this->getOldestBookingTimeStart($credit);
            if (!$timeStart)
            {
                $this->excludedIds[] = $credit->id();
                continue;
            }

            $this->updateCreditPackDates($credit, $timeStart);
        }

    }

    /**
     * Given a credit pack it will try to find the oldest booking
     * that they have and retrieve the time start as a string or
     * it will return null if is not there
     * @param  CreditPack $credit [description]
     * @return [type]             [description]
     */
    private function getOldestBookingTimeStart(CreditPack $credit) : ?string
    {
        $oldestBooking = $this->bookingCakeModel->find(
            'first',
            [
                'conditions' => [
                    '_id' => ['$in' => $credit->bookingsIds()->toArray()],
                    'user_id' => $credit->userId()
                ],
                'order' => ['time_start' => 1]
            ]);

        if (empty($oldestBooking))
        {
            return null;
        }

        return $oldestBooking['Booking']['time_start'];
    }

    /**
     * Given a credit pack and a start date, it will set
     * the start date and expiry date
     * @param  CreditPack $credit    [description]
     * @param  string     $startDate [description]
     * @return [type]                [description]
     */
    public function updateCreditPackDates(CreditPack $credit, string $startDate)
    {
        $credit = $credit->toArray();

        $credit['start_date'] = date('Y-m-d', strtotime($startDate));
        if (!isset($credit['expiry']['interval']) || !isset($credit['expiry']['interval_count']))
        {
            $this->excludedIds[] = $credit['_id'];
            return null;
        }

        $credit['end_date']   = $this->userCreditCakeModel->addToDate(
            $credit['start_date'],
            $credit['expiry']['interval'],
            $credit['expiry']['interval_count']
        );


        $this->userCreditCakeModel->read(null, $credit['_id']);
        $result = $this->userCreditCakeModel->save($credit);
        if ($result)
        {
            $this->updatedCredits++;
        }

        return $result;
    }
}
