<?php

use Aws\S3\S3Client;
use Glofox\Domain\Insurance\Models\InsuranceCompany as CompanyModel;
use Glofox\Domain\Users\Exceptions\UserNotFoundException;
use Glofox\Domain\Users\Models\User;
use Glofox\Domain\Users\Repositories\UsersRepository;
use Glofox\Domain\Users\UseCase\UpdateMemberMetadata;
use Glofox\Domain\Users\UseCase\UpdateMemberMetadataParams;
use League\Csv\Reader;
use League\Flysystem\FilesystemInterface;

App::uses('Shell', 'Console');

class ImportMembersInsuranceDetailsShell extends AppShell
{
    /** @var UsersRepository */
    private $usersRepository;

    /** @var InsuranceCompany */
    private $insuranceCompanyModel;

    /** @var UpdateMemberMetadata */
    private $updateMemberMetadata;

    /** @var S3Client */
    private $s3Client;

    public function __construct($stdout = null, $stderr = null, $stdin = null)
    {
        parent::__construct($stdout, $stderr, $stdin);

        $this->usersRepository = app()->make(UsersRepository::class);
        $this->insuranceCompanyModel = app()->make(InsuranceCompany::class);
        $this->updateMemberMetadata = app()->make(UpdateMemberMetadata::class);
        $storage = app()->make(FilesystemInterface::class);
        $this->s3Client = $storage->getAdapter()->getClient();
        $this->s3Client->registerStreamWrapper(); // for stream
    }

    public function getOptionParser(): ConsoleOptionParser
    {
        $parser = parent::getOptionParser();

        $parser->addOption('csv-path', [
            'short' => 'c',
            'help' => 'the CSV content you want to import',
        ]);

        $parser->addOption('limit', [
            'short' => 'l',
            'help' => 'number of users to be imported from CSV',
            'default' => -1, // all
        ]);

        return $parser;
    }

    public function main(): void
    {
        $csvPath = (string)$this->param('csv-path');
        $limit = (int)$this->param('limit');

        $stream = fopen($csvPath, 'r');

        // COLUMNS: user_id, company_name, company_id, id_number, group_id
        $reader = Reader::createFromStream($stream);
        $reader->setOffset(1); // skip header
        $reader->setLimit($limit);

        $rows = $reader->fetch();

        foreach ($rows as $row) {
            [$memberId, $companyName, $companyId, $idNumber, $groupId] = $row;

            try {
                $member = $this->fetchMember($memberId);
            } catch (UserNotFoundException $e) {
                echo "member not found: $memberId\n";
                continue;
            }

            $insuranceCompany = $this->fetchOrCreateCompany(
                $companyName,
                $companyId,
                (array)$member->get('branch_id'), // force always an array
                $member->originBranchId()
            );

            // once it's a use case it will notify changes via webhooks as well
            $this->updateMemberMetadata->execute(
                new UpdateMemberMetadataParams($memberId, [
                    'insurance' => [
                        'policy_number' => $idNumber,
                        'company_id' => $insuranceCompany->id(),
                        'group_id' => (string)$groupId,
                    ],
                ])
            );

            echo "user imported successfully: $memberId\n";
        }
    }

    private function fetchMember(string $memberId): User
    {
        return $this->usersRepository
            ->skipCallbacks()
            ->getByMemberId($memberId);
    }

    private function fetchOrCreateCompany(
        string $companyName,
        string $companyId,
        array $branchIds,
        string $originBranchId
    ): CompanyModel {
        $data = [
            'name' => $companyName,
            'number' => $companyId,
            'branch_id' => $originBranchId,
        ];

        $result = $this->insuranceCompanyModel->find('first', [
            'conditions' => $data,
        ]);

        if ($result) {
            $company = CompanyModel::make($result['InsuranceCompany']);
            // branch ids not present in the insurance company document yet
            $branchIds = array_diff($branchIds, $company->branchId());
            if (empty($branchIds)) {
                return $company; // the insurance company already contains all branch ids, no update needed
            }
            $data = $company->toArray(); // copy the existent insurance company data to be updated
        }

        // add all missing branch ids to the branches list
        $branchIds = array_merge((array)($data['branch_id'] ?? []), $branchIds);
        $data['branch_id'] = array_values(array_unique($branchIds)); // remove duplications

        $result = $this->insuranceCompanyModel->saveOrFail($data, ['callbacks' => 'before']);

        return CompanyModel::make($result['InsuranceCompany']);
    }
}
