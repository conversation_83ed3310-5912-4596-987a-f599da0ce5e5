<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Memberships\Collections;

use Glofox\Domain\Memberships\Collections\MembershipsCollection;
use Glofox\Domain\Memberships\Filters\FilterRemovedPlans;
use Glofox\Domain\Memberships\Models\Membership;

\App::import('Test/Case', 'GlofoxTestCase');

class MembershipsCollectionTest extends \GlofoxTestCase
{
    public function test_it_creates_collection_from_legacy_format(): void
    {
        $memberships = MembershipsCollection::fromLegacy([
            [
                'Membership' => [
                    'branch_id' => 'id-123456',
                    'name' => 'First membership',
                ],
            ],
            [
                'Membership' => [
                    'branch_id' => 'id-654321',
                    'name' => 'Second membership',
                ],
            ]
        ]);

        $this->assertInstanceOf(MembershipsCollection::class, $memberships);
        $this->assertInstanceOf(Membership::class, $memberships->first());
        $this->assertSame('id-123456', $memberships->first()->branchId());
        $this->assertSame('id-654321', $memberships->last()->branchId());
        $this->assertSame('First membership', $memberships->first()->name());
        $this->assertSame('Second membership', $memberships->last()->name());
    }

    public function test_it_generates_legacy_format_from_collection(): void
    {
        $memberships = new MembershipsCollection([
            new Membership([
                'branch_id' => 'id-123456',
                'name' => 'First membership',
            ]),
            new Membership([
                'branch_id' => 'id-654321',
                'name' => 'Second membership',
            ]),
        ]);

        $this->assertSame([
            [
                'Membership' => [
                    'branch_id' => 'id-123456',
                    'name' => 'First membership',
                ],
            ],
            [
                'Membership' => [
                    'branch_id' => 'id-654321',
                    'name' => 'Second membership',
                ],
            ],
        ], $memberships->toLegacy());
    }

    public function test_it_filters_memberships_in_a_pipeline(): void
    {
        $memberships = MembershipsCollection::fromLegacy([
            [
                'Membership' => [
                    'branch_id' => 'id-123456',
                    'name' => 'First membership',
                    'plans' => [
                        [
                            'code' => 'code-123',
                            'removed' => true,
                        ],
                        [
                            'code' => 'code-234',
                        ],
                    ]
                ],
            ],
            [
                'Membership' => [
                    'branch_id' => 'id-654321',
                    'name' => 'Second membership',
                    'plans' => [
                        [
                            'code' => 'code-123',
                        ],
                        [
                            'code' => 'code-234',
                            'removed' => true,
                        ],
                        [
                            'code' => 'code-345',
                            'removed' => false,
                        ],
                    ]
                ],
            ]
        ]);

        $memberships = $memberships->pipeline([
            FilterRemovedPlans::class,
        ]);

        $this->assertInstanceOf(MembershipsCollection::class, $memberships);
        $this->assertInstanceOf(Membership::class, $memberships->first());
        $this->assertCount(1, $memberships->first()->plans());
        $this->assertCount(2, $memberships->last()->plans());
        $this->assertSame('code-234', $memberships->first()->plans()->first()->code());
        $this->assertSame('code-123', $memberships->last()->plans()->first()->code());
        $this->assertSame('code-345', $memberships->last()->plans()->last()->code());
    }
}
