<?php

namespace Glofox\Response\Transformers\Branches;

use Glofox\Response\Transformers\BaseTransformer;
use Glofox\Response\Models\BranchesModel;
use Glofox\Response\Models\Branches\AddressModel;

/**
 * Class BranchesTransformer
 * @package Glofox\Response\Transformers\Branches
 */
class BranchesTransformer extends BaseTransformer
{
    /**
     * @param array
     * @return BranchesModel
     */
    public function setFromArray(array $branch = [])
    {
        return $this->setBranchModuleFromData($branch);
    }

    /**
     * @param array
     * @return \Glofox\Response\Models\BasicModels\MainModel
     */
    public function setFromArrayCollection(array $results)
    {
        $dataModelArrayCollection = [];
        if (!empty($results) && is_array($results) && isset($results['data'])) {
            foreach ($results['data'] as $data) {
                $dataModelArrayCollection[] = $this->setBranchModuleFromData($data);
            }
        }

        return $this->setMainModelFromArray($results, $dataModelArrayCollection);
    }

    /**
     * @param array
     * @return BranchesModel
     */
    public function setBranchModuleFromData(array $data)
    {
        $AddressModel = null;
        if (!empty($data['address'])) {
            $AddressModel = $this->setAddressModelFromArray($data['address']);
        }

        $configurationModel = null;
        if (!empty($data['configuration'])) {
            $configuration = new ConfigurationTransformer();
            $configurationModel = $configuration->setConfigurationModel($data['configuration']);
        }

        $featuresModel = null;
        if (!empty($data['features'])) {
            $featuresTransformer = new FeaturesTransformer();
            $featuresModel = $featuresTransformer->setFeatureModel($data['features']);
        }

        $branchData = [
            '_id' => (string) ($data['_id'] ?? ''),
            'address' => $AddressModel,
            'categories' =>  (array) (!empty($data['categories']) ? $data['categories'] : []),
            'configuration' => $configurationModel,
            'email' => (string) ($data['email'] ?? ''),
            'facebook' => (string) ($data['facebook'] ?? ''),
            'instagram' => ($this->formatSocialAccountUsername($data['instagram'] ?? '')),
            'features' => $featuresModel,
            'name' => (string) ($data['name'] ?? ''),
            'namespace' => (string) ($data['namespace'] ?? ''),
            'phone' => (string) ($data['phone'] ?? ''),
            'questions' => (array) ($data['questions'] ?? []),
            'twitter' => (string) ($data['twitter'] ?? ''),
            'website' => (string) ($data['website'] ?? ''),
            'working_hours' => (string) ($data['working_hours'] ?? ''),
            'about' => (string) ($data['working_hours'] ?? ''),
            'stripe_plan_code' => (string) ($data['stripe_plan_code'] ?? ''),
            'type' => (string) ($data['type'] ?? ''),
            'image_url' => (string) ($data['image_url'] ?? ''),
            'corporate_id' => (string) ($data['corporate_id'] ?? ''),
        ];

        if (isset($data['mobile_config_obj']))
        {
            $branchData['mobile_config_obj'] = (array) $data['mobile_config_obj'];
        }

        return new BranchesModel($branchData);
    }

    /*
     * Member-app uses the 'user' or '@user' account identifier directly for social accounts.
     * Used in the transformer to return this substring of the account's url.
     * https://glofox.atlassian.net/browse/STAF-98
     */
    public function formatSocialAccountUsername(string $url) : string {
        $result = preg_match('#^(?:https?://)?[^/]+/([^/?]+)#', $url, $matches);
        return $result ? $matches[1] : $url;
    }

    /**
     * @param array
     * @return AddressModel
     */
    public function setAddressModelFromArray(array $address)
    {
        $lat = 0.0;
        $lng = 0.0;
        if (isset($address['location']['lat'])) {
            $lat = $address['location']['lat'];
        }

        if (isset($address['location']['lng'])) {
            $lng = $address['location']['lng'];
        }

        $location = null;
        if (!empty($address['location'])) {
            $location = [
                'lat' => (float) $lat,
                'lng' => (float) $lng,
            ];
        }

        return new AddressModel(
            [
                'street' => (string) ($address['street'] ?? ''),
                'city' => (string) ($address['city'] ?? ''),
                'state' => (string) ($address['state'] ?? ''),
                'country' => (string) ($address['country'] ?? ''),
                'country_code' => (string) ($address['country_code'] ?? ''),
                'currency' => (string) ($address['currency'] ?? ''),
                'district' => (string) ($address['district'] ?? ''),
                'latitude' =>  (float) ($address['latitude'] ?? 0.0),
                'longitude' => (float) ($address['longitude'] ?? 0.0),
                'continent' => (string) ($address['continent'] ?? ''),
                'timezone_id' => (string) ($address['timezone_id'] ?? ''),
                'timezone_name' => (string) ($address['timezone_name'] ?? ''),
                'postal_code' => (string) ($address['postal_code'] ?? ''),
                'location' => $location
            ]
        );
    }
}
