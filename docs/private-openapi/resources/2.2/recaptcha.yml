post:
  tags:
    - ReCaptcha
  summary: ''
  operationId: validate-recaptcha
  description: 'This endpoint validates a ReCaptcha token generated by the front-end UI component'
  x-internal: true
  security:
    - none: [ ]
  requestBody:
    required: true
    content:
      application/json:
        schema:
          $ref: '../../requests/2.2/validate_recaptcha.yml'
  responses:
    '200':
      description: OK
      content:
        application/json:
          schema:
            $ref: '../../schemas/recaptcha_validation_outcome.yml'