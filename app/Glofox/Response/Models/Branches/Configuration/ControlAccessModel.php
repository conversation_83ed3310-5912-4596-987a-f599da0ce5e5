<?php

namespace Glofox\Response\Models\Branches\Configuration;

use Glofox\Response\BaseModel;

/**
 * @SuppressWarnings(PHPMD)
 */
class ControlAccessModel extends BaseModel
{
    /**
     * @var bool
     */
    public $barcode = false;
    /**
     * @var bool
     */
    public $booking_only_checkin = false;

    /**
     * @var bool
     */
    public $enabled = false;

    /**
     * @var bool
     */
    public $sound = false;

    protected array $allowedFields = [
        'barcode',
        'booking_only_checkin',
        'enabled',
        'sound'
    ];

    public function __construct(array $data)
    {
        $this->setModelFields($data);
    }
}
