<?php

namespace CakeTestCases\Glofox\Payments\Providers\Gateway\WebHooks\Handlers;

use Glofox\Domain\Charges\Exceptions\ChargeNotFoundException;
use Glofox\Domain\Refunds\Exceptions\RefundNotFoundException;
use Glofox\Domain\Refunds\Repositories\RefundsRepository;
use Glofox\Domain\Refunds\Search\Expressions\ParentId;
use Glofox\Domain\Refunds\Status;
use Glofox\Payments\Contracts\PaymentProviderContract;
use Glofox\Payments\Entities\Transaction\Contracts\ChargeHandlerContract;
use Glofox\Payments\Entities\Transaction\Models\Transaction;
use Glofox\Payments\Providers\Gateway\WebHooks\Handlers\ChargeRefundUpdated;

\App::import('Test/Case', 'GlofoxTestCase');

class ChargeRefundUpdatedTest extends \GlofoxTestCase
{

    public $fixtures = [
        'app.refund',
        'app.stripeCharge'
    ];

    public function tearDown()
    {
        parent::tearDown();
        \Mockery::close();
    }

    public function test_throw_exception_when_the_refund_is_not_found()
    {
        $chargeId = '5a3a5054948dae0706000003';
        $failedEvent = collect([
            'id' => 'evt_123',
            'psp_response' => '{"data":{"object":{"id":"' . $chargeId . '","status":"failed"}}}',
        ]);

        $paymentHandler = $this->mockChargeHandler($chargeId);

        $handler = new ChargeRefundUpdated($failedEvent, $paymentHandler);

        $this->setExpectedException(RefundNotFoundException::class);
        $handler->handle();

    }

    public function test_throw_exception_when_the_stripe_charge_is_not_found()
    {
        $chargeId = 'notFound';
        $failedEvent = collect([
            'id' => 'evt_123',
            'psp_response' => '{"data":{"object":{"id":"' . $chargeId . '","status":"failed"}}}',
        ]);

        $paymentHandler = $this->mockChargeHandler($chargeId);

        $handler = new ChargeRefundUpdated($failedEvent, $paymentHandler);

        $this->setExpectedException(ChargeNotFoundException::class);
        $handler->handle();

    }

    public function test_set_refund_status_to_error_when_refund_is_failed()
    {
        $chargeId = '5a3a5054948dae0706000004';
        $failedEvent = collect([
            'id' => 'evt_123',
            'psp_response' => '{"data":{"object":{"id":"' . $chargeId . '","status":"failed"}}}',
        ]);

        $paymentHandler = $this->mockChargeHandler($chargeId);

        $handler = new ChargeRefundUpdated($failedEvent, $paymentHandler);

        $handler->handle();

        /** @var RefundsRepository $refundsRepository */
        $refundsRepository = app()->make(RefundsRepository::class);
        $refund = $refundsRepository->addCriteria(new ParentId($chargeId))->firstOrFail();

        $this->assertEquals(Status::ERROR, $refund['status']);
    }

    public function test_set_refund_status_to_error_when_refund_is_canceled()
    {
        $chargeId = '5db89b3d2cab68041a2707ce';
        $failedEvent = collect([
            'id' => 'evt_123',
            'psp_response' => '{"data":{"object":{"id":"' . $chargeId . '","status":"canceled"}}}',
        ]);

        $paymentHandler = $this->mockChargeHandler($chargeId);

        $handler = new ChargeRefundUpdated($failedEvent, $paymentHandler);

        $handler->handle();

        /** @var RefundsRepository $refundsRepository */
        $refundsRepository = app()->make(RefundsRepository::class);
        $refund = $refundsRepository->addCriteria(new ParentId($chargeId))->firstOrFail();

        $this->assertEquals(Status::ERROR, $refund['status']);
    }

    public function test_keep_refund_status_to_success_when_refund_is_success()
    {
        $chargeId = '5a3a5054948dae0706000009';
        $failedEvent = collect([
            'id' => 'evt_123',
            'psp_response' => '{"data":{"object":{"id":"' . $chargeId . '","status":"success"}}}',
        ]);

        $paymentHandler = $this->mockEmptyPaymentHandler();

        $handler = new ChargeRefundUpdated($failedEvent, $paymentHandler);

        $handler->handle();

        /** @var RefundsRepository $refundsRepository */
        $refundsRepository = app()->make(RefundsRepository::class);
        $refund = $refundsRepository->addCriteria(new ParentId($chargeId))->firstOrFail();

        $this->assertEquals(Status::SUCCESS, $refund['status']);
    }

    public function test_keep_refund_status_to_success_when_refund_is_pending()
    {
        $chargeId = '5a3a5054948dae0706000004';
        $failedEvent = collect([
            'id' => 'evt_123',
            'psp_response' => '{"data":{"object":{"id":"' . $chargeId . '","status":"pending"}}}',
        ]);

        $paymentHandler = \Mockery::mock(PaymentProviderContract::class);
        $paymentHandler->shouldNotHaveBeenCalled();

        $handler = new ChargeRefundUpdated($failedEvent, $paymentHandler);

        $handler->handle();

        /** @var RefundsRepository $refundsRepository */
        $refundsRepository = app()->make(RefundsRepository::class);
        $refund = $refundsRepository->addCriteria(new ParentId($chargeId))->firstOrFail();

        $this->assertEquals(Status::SUCCESS, $refund['status']);
    }

    /**
     * @param string $chargeId
     * @return PaymentProviderContract
     */
    public function mockChargeHandler(string $chargeId): PaymentProviderContract
    {
        $transaction = \Mockery::mock(Transaction::class);
        $transaction->shouldReceive('id')->andReturn('trans_' . $chargeId);

        $chargeHandlerContract = \Mockery::mock(ChargeHandlerContract::class);
        $chargeHandlerContract->expects('getById')
            ->andReturn($transaction);

        $paymentHandler = \Mockery::mock(PaymentProviderContract::class);
        $paymentHandler->expects('charges')
            ->andReturn($chargeHandlerContract);
        return $paymentHandler;
    }

    /**
     * @return PaymentProviderContract|\Mockery\LegacyMockInterface|\Mockery\MockInterface
     */
    public function mockEmptyPaymentHandler()
    {
        $paymentHandler = \Mockery::mock(PaymentProviderContract::class);
        $paymentHandler->shouldNotHaveBeenCalled();
        return $paymentHandler;
    }
}
