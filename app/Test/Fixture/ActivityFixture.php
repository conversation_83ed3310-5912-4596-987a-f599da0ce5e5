<?php

use Glofox\Domain\Activities\Type;
use Glofox\Domain\Charges\Status;

class ActivityFixture extends CakeTestFixture
{
    public $import = 'Activity';

    public function init()
    {
        $this->records = [
            [
                '_id' => new MongoId('584ac642dd3c2fbfe13e911b'),
                'user_id' => '59a7011a05c677bda916612a',
                'requester_user_id' => '59a7011a05c677bda916612a',
                'event_identifier' => Type::CUSTOM_CHARGE_EXECUTED,
                'event_context' => [
                    'paid' => false,
                    'charge_id' => '5422ab50d7b6dd72cd8b4573',
                    'invoice_id' => 'bbc56880-9a0f-42c9-ae01-649d6ddcdfdd',
                    'transaction_status' => Status::PENDING,
                ],
                'created' => new MongoDate(strtotime('2024-01-01 00:00:00')),
            ],
            [
                '_id' => new MongoId('584ac642dd3c2fbfe13e912b'),
                'user_id' => '59a7011a05c677bda916612a',
                'requester_user_id' => '59a7011a05c677bda916612a',
                'event_identifier' => Type::SUBSCRIPTION_CYCLE_PAID,
                'event_context' => [
                    'paid' => false,
                    'charge_id' => '5422ab50d7b6dd72cd8b4575',
                    'invoice_id' => '57f402e6-3ca3-4bef-b635-cda3e9c8ccb7',
                    'transaction_status' => Status::PENDING,
                    'charge_amount' => 10,
                ],
            ],
            [
                '_id' => new MongoId('584ac642dd3c2fbfe23e915c'),
                'user_id' => '59a7011a05c677bda916612a',
                'requester_user_id' => '59a7011a05c677bda916612a',
                'event_identifier' => Type::NSF_FEE,
                'event_context' => [
                    'description' => 'NSF for invoice 57f402e6-3ca3-4bef-b635-cda3e9c8ccb7',
                    'paid' => true,
                    'charge_id' => '5422ab50d7b6dd72cd8b5535',
                    'invoice_id' => '57f402e6-3ca3-4bef-b635-cda3e9c8aab7',
                    'transaction_status' => Status::PAID,
                    'charge_amount' => 10,
                ],
            ],
            [
                '_id' => new MongoId('584ac642dd3c2fbfe23e912b'),
                'user_id' => '59a7011a05c677bda916612a',
                'requester_user_id' => '59a7011a05c677bda916612a',
                'event_identifier' => Type::SUBSCRIPTION_CYCLE_PAID,
                'event_context' => [
                    'paid' => false,
                    'charge_id' => '5422ab50d7b6dd72cd8b5555',
                    'invoice_id' => '57f402e6-3ca3-4bef-b635-cda3e9c8ccb7',
                    'transaction_status' => Status::PENDING,
                    'charge_amount' => 10,
                ],
            ],
            [
                '_id' => new MongoId('5e00c10eb9cd4d75e7dace1c'),
                'user_id' => '59a7011a05c677bda916612a',
                'requester_user_id' => '59a7011a05c677bda916612a',
                'event_identifier' => Type::SUBSCRIPTION_PURCHASED,
                'event_context' => [
                    'plan_code' => 'mem-123',
                    'plan_name' => 'Removed plan name',
                    'charge_amount' => 10,
                    'invoice_id' => "6fa727b3-310b-4250-81cc-e0c34066b221",
                ],
            ],
            [
                '_id' => new MongoId('658ad9a34bd6621155da0973'),
                'user_id' => '59a7011a05c677bda916612a',
                'requester_user_id' => '59a7011a05c677bda916612a',
                'event_identifier' => Type::SUBSCRIPTION_CYCLE_PAID,
                'event_context' => [
                    'paid' => false,
                    'charge_id' => '5422ab50d7b6dd72cd8b4575',
                    'invoice_id' => '57f402e6-3ca3-4bef-b635-cda3e9c8ccb9',
                    'transaction_status' => Status::PAID,
                    'charge_amount' => 0,
                    'charges' => [
                        [
                            'id' => '62ab376a4180314033c69596',
                            'amount' => 0,
                            'status' => 'PAID',
                            'payment_method' => 'complimentary',
                        ],
                    ],
                ],
            ],
            [
                '_id' => new MongoId('66d034c53dd0356d7f43275e'),
                'user_id' => '59a3011a05c677bda916612d',
                'requester_user_id' => '59a3011a05c677bda916612d',
                'event_identifier' => Type::CARD_REMOVED,
                // This context doesn't need to be consistent with this event_identifier
                'event_context' => [
                    'paid' => false,
                    'charge_id' => '5422ab50d7b6dd72cd8b4573',
                    'invoice_id' => 'bbc56880-9a0f-42c9-ae01-649d6ddcdfdd',
                    'transaction_status' => Status::PENDING,
                ],
                'created' => new MongoDate(strtotime('2024-01-01 01:00:00')),
            ],
            [
                '_id' => new MongoId('66d03cde3a6e7b0a576a44d8'),
                'user_id' => '58568a8fa875ab19630041a7',
                'requester_user_id' => '58568a8fa875ab19630041a7',
                'event_identifier' => Type::CARD_REMOVED,
                // This context doesn't need to be consistent with this event_identifier
                'event_context' => [
                    'paid' => false,
                    'charge_id' => '5422ab50d7b6dd72cd8b4573',
                    'invoice_id' => 'bbc56880-9a0f-42c9-ae01-649d6ddcdfdd',
                    'transaction_status' => Status::PENDING,
                ],
            ],

            // Pause with more recent charge
            [
                '_id' => new MongoId('5e37fd5815f3ee2dad62a542'),
                'user_id' => '5b19430ea0d988945a164337',
                'requester_user_id' => '5b19430ea0d988945a164337',
                'event_identifier' => Type::SUBSCRIPTION_CYCLE_PAID,
                'event_context' => [
                    'paid' => true,
                    'transaction_status' => Status::PAID,
                    'charge_id' => '5841700f7cad363dc88b4569',
                    'charge_amount' => 10,
                ],
                'created' => new MongoDate(strtotime('-1 day')),
            ],
            [
                '_id' => new MongoId('5e37fd55eb4a6646bf3b6ba7'),
                'user_id' => '5b19430ea0d988945a164337',
                'requester_user_id' => '5b19430ea0d988945a164337',
                'event_identifier' => Type::SUBSCRIPTION_PAUSED,
                'event_context' => [],
                'created' => new MongoDate(strtotime('-2 days')),
            ],

            // Pause without more recent charge
            [
                '_id' => new MongoId('5e37fe3fd7c1471dfd563f11'),
                'user_id' => '5b19430ea0d988945a164338',
                'requester_user_id' => '5b19430ea0d988945a164338',
                'event_identifier' => Type::SUBSCRIPTION_PAUSED,
                'event_context' => [],
                'created' => new MongoDate(strtotime('-1 day')),
            ],
            [
                '_id' => new MongoId('5e37fddf70aa1d4f7cad3b49'),
                'user_id' => '5b19430ea0d988945a164338',
                'requester_user_id' => '5b19430ea0d988945a164338',
                'event_identifier' => Type::SUBSCRIPTION_CYCLE_PAID,
                'event_context' => [
                    'paid' => true,
                    'transaction_status' => Status::PAID,
                    'charge_id' => '5841700f7cad363dc88b4570',
                    'charge_amount' => 10,
                ],
                'created' => new MongoDate(strtotime('-2 days')),
            ],
            [
                '_id' => new MongoId('5dbc628635b96b2d52400fac'),
                'user_id' => '5b19430ea0d988945a164338',
                'requester_user_id' => '5b19430ea0d988945a164338',
                'event_identifier' => Type::SUBSCRIPTION_CYCLE_PAYMENT_FAILED,
                'event_context' => [
                    'paid' => true,
                    'transaction_status' => Status::ERROR,
                    'charge_amount' => 10,
                ],
                'created' => new MongoDate(strtotime('-2 days')),
            ],

            // Pause with more recent charges (one zero, one full price)
            [
                '_id' => new MongoId('6e37fddf70aa1d4f7cad3b49'),
                'user_id' => '5b19430ea0d988945a164339',
                'requester_user_id' => '5b19430ea0d988945a164339',
                'event_identifier' => Type::SUBSCRIPTION_CYCLE_PAID,
                'event_context' => [
                    'paid' => true,
                    'transaction_status' => Status::PAID,
                    'charge_id' => '5841700f7cad363dc88b4572',
                    'charge_amount' => 10,
                ],
                'created' => new MongoDate(strtotime('-1 days')),
            ],
            [
                '_id' => new MongoId('6e37fddf70aa1d4f7cad3b59'),
                'user_id' => '5b19430ea0d988945a164339',
                'requester_user_id' => '5b19430ea0d988945a164339',
                'event_identifier' => Type::SUBSCRIPTION_CYCLE_PAID,
                'event_context' => [
                    'paid' => true,
                    'transaction_status' => Status::PAID,
                    'charge_id' => '5841700f7cad363dc88b4571',
                    'charge_amount' => 0,
                ],
                'created' => new MongoDate(strtotime('-2 days')),
            ],
            [
                '_id' => new MongoId('6e37fe3fd7c1471dfd563f11'),
                'user_id' => '5b19430ea0d988945a164339',
                'requester_user_id' => '5b19430ea0d988945a164339',
                'event_identifier' => Type::SUBSCRIPTION_PAUSED,
                'event_context' => [],
                'created' => new MongoDate(strtotime('-3 days')),
            ],
            // activity for booked unpaid class
            [
                '_id' => new MongoId('6e37fe3fd7c1471dfd833a33'),
                'user_id' => '5ae1a75a8d7de985e383494d',
                'requester_user_id' => '59a7011a05c677bda916612a',
                'event_identifier' => Type::ENTITY_BOOKED,
                'event_context' => [
                    'booking_id' => '584ac642dd3c2fbfe13e911b',
                    'booking_name' => 'Test',
                    'model' => 'events',
                    'time_start' => date('Y-m-d H:m:i', strtotime('+30 minutes')),
                    'time_finish' => date('Y-m-d H:m:i', strtotime('+2 hour')),
                ],
                'created' => new MongoDate(strtotime('-3 days')),
            ],
            // activity for booked unpaid appointment
            [
                '_id' => new MongoId('633a791d517862e8d38d889f'),
                'user_id' => '5a2aad49e1608aa72b0041b9',
                'requester_user_id' => '5a2aad49e1608aa72b0041b9',
                'event_identifier' => Type::ENTITY_BOOKED,
                'event_context' => [
                    'booking_id' => '63380dd716d490132841dd33',
                    'booking_name' => 'Test Appointment pilates',
                    'model' => 'appointments',
                    'time_start' => date('Y-m-d H:m:i', strtotime('+1 day')),
                    'time_finish' => date('Y-m-d H:m:i', strtotime('+2 day')),
                ],
                'created' => new MongoDate(strtotime('-1 day')),
            ],
            [
                '_id' => new MongoId('63998c44f240ebb15d7e2e4d'),
                'user_id' => '63998828ca5fbb0e575cb46a',
                'requester_user_id' => '63998828ca5fbb0e575cb46a',
                'event_identifier' => Type::ENTITY_BOOKED,
                'event_context' => [
                    'booking_id' => '63998aec21086362d619b0c5',
                    'booking_name' => 'Test Appointment pilates',
                    'model' => 'appointments',
                    'time_start' => date('Y-m-d H:m:i', strtotime('+1 day')),
                    'time_finish' => date('Y-m-d H:m:i', strtotime('+2 day')),
                ],
                'created' => new MongoDate(strtotime('-1 day')),
            ],
            // activity for booked unpaid class
            [
                '_id' => new MongoId('6e37fe3fd7c1471dfd833a34'),
                'user_id' => '59a7010a05c677b3a9166111',
                'requester_user_id' => '59a7010a05c677b3a9166111',
                'event_identifier' => Type::ENTITY_BOOKED,
                'event_context' => [
                    'booking_id' => '584ac642dd3c2fbfe13e913c',
                    'booking_name' => 'Test',
                    'model' => 'events',
                    'time_start' => date('Y-m-d H:m:i', strtotime('+30 minutes')),
                    'time_finish' => date('Y-m-d H:m:i', strtotime('+2 hour')),
                ],
                'created' => new MongoDate(strtotime('-3 days')),
            ],
        ];

        $this->records = array_merge(
            $this->records,
            $this->forBookingActivityContext(),
            $this->forGetActivitiesByInvoiceTest(),
        );

        parent::init();
    }

    private function forBookingActivityContext(): array
    {
        return [
            [
                '_id' => new MongoId('64624efa75a2b3f3f56b602b'),
                'user_id' => 'a9a5521a05c687bda917785c',
                'requester_user_id' => '59a7010a05c677b3a9166111',
                'event_identifier' => Type::ENTITY_BOOKED,
                'event_context' => [
                    'charge_id' => '64624dbdc581ed3bee299f1b',
                ],
                'created' => new MongoDate(strtotime('-3 days')),
            ],
        ];
    }

    private function forGetActivitiesByInvoiceTest(): array
    {
        return [
            [
                "_id" => new MongoId('67337df4ae59e8c4103f3645'),
                "event_identifier" => Type::ENTITY_BOOKED,
                "requester_user_id" => "59a7011a05c677bda916612a",
                "user_id" => "59a7011a05c677bda916612a",
                "event_context" => [
                    "booking_id" => "test-booking-id",
                    "model" => "events",
                    "card_id" => "test-card-id",
                    "paid" => true,
                    "refunded" => false,
                    "transaction_status" => "PAID",
                    "mandate_id" => "test-mandate-id",
                    'invoice_id' => 'test-invoice-id',
                ],
            ],
            [
                "_id" => new MongoId("6734877c91cfbee8a8003629"),
                "event_identifier" => "SUBSCRIPTION_CYCLE_PAYMENT_FAILED",
                "user_id" => "59a3011a05c677bda916612d",
                "event_context" => [
                    "invoice_id" => "fake-invoice-id-1046f030",
                    "invoice_amount" => 12.95,
                    "amount_refunded" => 0,
                    "transaction_group_id" => "fake-group-135463b7",
                    "description" => "Single - Weekly (2887575)",
                    "paid" => false,
                    "refunded" => false,
                    "charges" => [
                        [
                            "id" => "test-charge-id-8a8003628",
                            "amount" => 12.95,
                            "status" => "ERROR",
                            "amount_refunded" => 0,
                            "payment_method" => "credit_card",
                            "card" => [
                                "id" => "test-id",
                                "last4" => "0000",
                                "brand" => "visa",
                                "year" => 2024,
                                "month" => 1,
                            ],
                        ],
                    ],
                    "failure_reason" => "Your card has insufficient funds.",
                ],
                'created' => new MongoDate(strtotime('2024-01-02 00:00:00')),
            ],
            [
                "_id" => new MongoId("6735d90a0417a1ce9c041a6a"),
                "event_identifier" => "SUBSCRIPTION_CYCLE_PAYMENT_FAILED",
                "user_id" => "59a3011a05c677bda916612d",
                "event_context" => [
                    "invoice_id" => "fake-invoice-id-1046f030",
                    "invoice_amount" => 12.95,
                    "amount_refunded" => 0,
                    "transaction_group_id" => "fake-group-2505ea51",
                    "description" => "Single - Weekly (2887575)",
                    "paid" => false,
                    "refunded" => false,
                    "charges" => [
                        [
                            "id" => "test-charge-id-ce9c041a69",
                            "amount" => 12.95,
                            "status" => "ERROR",
                            "amount_refunded" => 0,
                            "payment_method" => "credit_card",
                            "card" => [
                                "id" => "test-id",
                                "last4" => "0000",
                                "brand" => "visa",
                                "year" => 2024,
                                "month" => 1,
                            ],
                        ],
                    ],
                    "failure_reason" => "Your card has insufficient funds.",
                ],
                'created' => new MongoDate(strtotime('2024-01-03 00:00:00')),
            ],
            [
                "_id" => new MongoId("6739cd998f2f72a10c075085"),
                "event_identifier" => "SUBSCRIPTION_CYCLE_PAYMENT_FAILED",
                "user_id" => "59a3011a05c677bda916612d",
                "event_context" => [
                    "invoice_id" => "fake-invoice-id-1046f030",
                    "invoice_amount" => 12.95,
                    "amount_refunded" => 0,
                    "transaction_group_id" => "fake-group-d66c4e26",
                    "description" => "Single - Weekly (2887575)",
                    "paid" => false,
                    "refunded" => false,
                    "charges" => [
                        [
                            "id" => "test-charge-id-a10c075084",
                            "amount" => 12.95,
                            "status" => "ERROR",
                            "amount_refunded" => 0,
                            "payment_method" => "credit_card",
                            "card" => [
                                "id" => "test-id",
                                "last4" => "0000",
                                "brand" => "visa",
                                "year" => 2024,
                                "month" => 1,
                            ],
                        ],
                    ],
                    "failure_reason" => "Your card has insufficient funds.",
                ],
                'created' => new MongoDate(strtotime('2024-01-05 00:00:00')),
            ],
            [
                "_id" => new MongoId("673487792da14972cce4162d"),
                "event_identifier" => "NSF_FEE",
                "user_id" => "59a3011a05c677bda916612d",
                "event_context" => [
                    "invoice_id" => "fake-invoice-id-2onC9Ua",
                    "invoice_amount" => 5,
                    "description" => "Non-Sufficient Funds Fee for invoice fake-invoice-id-1046f030 (x1) 5.00",
                    "paid" => true,
                    "refunded" => false,
                    "already_paid" => false,
                    "is_forgiven" => false,
                    "charges" => [
                        [
                            "id" => "test-charge-e4162c",
                            "amount" => 5,
                            "status" => "PAID",
                            "amount_refunded" => 0,
                            "payment_method" => "credit_card",
                            "card" => [
                                "id" => "test-id",
                                "last4" => "0000",
                                "brand" => "visa",
                                "year" => 2024,
                                "month" => 1,
                            ],
                        ],
                    ],
                    "line_items" => [
                        [
                            "description" => "Non-Sufficient Funds Fee for invoice fake-invoice-id-1046f030",
                            "quantity" => 1,
                            "amount" => 5,
                            "translatable_description" => [
                                "key" => "PENALTY_FEE_FOR_INVOICE",
                                "args" => [
                                    "invoice_id" => "fake-invoice-id-1046f030",
                                ],
                            ],
                            "attributes" => [
                                "service_type" => "fees",
                                "service_sub_type" => "non_sufficient_funds",
                            ],
                        ],
                    ],
                    "failure_reason" => null,
                    "amount_refunded" => 0,
                ],
                'created' => new MongoDate(strtotime('2024-01-04 00:00:00')),
            ],
        ];
    }
}
