<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\Users\Services\Notification;

use Glofox\Domain\Branches\Models\Branch;
use Glofox\Domain\Branches\Repositories\BranchesRepository;
use Glofox\Domain\Users\Repositories\UsersRepository;
use Glofox\Domain\Users\Services\Notification\PushNotificationService;
use GlofoxTestCase;
use Psr\Log\LoggerInterface;

\App::import('Test/Case', 'GlofoxTestCase');

class PushNotificationServiceTest extends GlofoxTestCase
{
    protected $loggerMock;
    private UsersRepository $usersRepositoryMock;
    private BranchesRepository $branchesRepositoryMock;
    private PushNotificationService $service;
    private string $branchId = '123';
    private string $roamingBranchId = '654';
    private array $restrictedCorporations = [
        'corp_XtremeFitness',
        'corp_Spartansboxing',
        'corp_Pilates',
        'corp_SETSHybridTraining',
    ];

    public function setUp(): void
    {
        $this->loggerMock = $this->createMock(LoggerInterface::class);
        $this->usersRepositoryMock = $this->createMock(UsersRepository::class);
        $this->branchesRepositoryMock = $this->createMock(BranchesRepository::class);

        $this->service = new PushNotificationService(
            $this->loggerMock,
            $this->usersRepositoryMock,
            $this->branchesRepositoryMock
        );
    }

    /**
     * @dataProvider deviceIdsByBranchDataProvider
     */
    public function testDeviceIdsByBranch(?string $corporateId, array $users, array $expectedDeviceIds): void
    {
        $branchMock = $this->createMock(Branch::class);
        $branchMock->method('corporateId')->willReturn($corporateId);

        $this->branchesRepositoryMock
            ->expects($this->once())
            ->method('findByIdAndActive')
            ->with($this->branchId)
            ->willReturn($branchMock);

        $this->usersRepositoryMock
            ->expects($this->once())
            ->method('getUsersWithPushConsentByBranchId')
            ->with($this->branchId, true)
            ->willReturn($users);

        $this->loggerMock
            ->expects($this->once())
            ->method('info')
            ->with('Received deviceIdsByBranch notifications request');

        $result = $this->service->deviceIdsByBranch($this->branchId);

        $this->assertEquals($expectedDeviceIds, $result);
    }

    public function deviceIdsByBranchDataProvider(): array
    {
        return array_merge(
            [
                'Non-restricted branch (all users pass)' => [
                    null,
                    [
                        ["device" => ["id" => "device1"], "origin_branch_id" => $this->branchId],
                        ["device" => ["id" => "device2"], "origin_branch_id" => $this->roamingBranchId]
                    ],
                    ['device1', 'device2']
                ],
                'Restricted branch (non-roaming users pass)' => [
                    'corp_XtremeFitness',
                    [
                        ["device" => ["id" => "device1"], "origin_branch_id" => $this->branchId],
                        ["device" => ["id" => "device2"], "origin_branch_id" => $this->roamingBranchId]
                    ],
                    ['device1']
                ],
                'Restricted branch (no origin_branch_id, passes)' => [
                    'corp_Pilates',
                    [
                        ["device" => ["id" => "device1"]],
                        ["device" => ["id" => "device2"], "origin_branch_id" => $this->roamingBranchId]
                    ],
                    ['device1']
                ]
            ],
            array_combine(
                array_map(fn($corp) => "Restricted branch - {$corp}", $this->restrictedCorporations),
                array_map(fn($corp) => [
                    $corp,
                    [
                        ["device" => ["id" => "device1"]],
                        ["device" => ["id" => "device2"], "origin_branch_id" => $this->roamingBranchId]
                    ],
                    ['device1']
                ], $this->restrictedCorporations)
            )
        );
    }
}
