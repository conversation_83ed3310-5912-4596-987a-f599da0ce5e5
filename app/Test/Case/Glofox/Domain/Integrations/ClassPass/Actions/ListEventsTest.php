<?php

namespace CakeTestCases\Glofox\Domain\Integrations\ClassPass\Actions;

use Glofox\Domain\Integrations\ClassPass\Actions\ListEvents;
use Glofox\Domain\Integrations\Origin;
use Glofox\Response\Models\BasicModels\MainModel;

\App::import('Test/Case', 'GlofoxTestCase');

/**
 * Class ListEventsTest.
 */
class ListEventsTest extends \GlofoxTestCase
{
    /**
     * Fixtures.
     *
     * @var array
     */
    public $fixtures = [
        'app.event', 'app.program', 'app.booking',
    ];

    /**
     * setUp method.
     */
    public function setUp()
    {
        parent::setUp();
        $this->ListEvents = new ListEvents();
    }

    /**
     * tearDown method.
     */
    public function tearDown()
    {
        parent::tearDown();
        unset($this->ListEvents);
    }

    public function testGetProgramIdAndSizeMapByIntegrationAndBranchId()
    {
        $origin = Origin::get('classpass');
        $branchId = '5addc25383266f65abf515c4';
        $result = $this->ListEvents->getProgramIdAndSizeMapByIntegrationAndBranchId($origin, $branchId);
        $this->assertArrayHasKey('5adf442bb2db1df852f44df0', $result, 'Program with classpass size of 3');
        $this->assertEquals(3, $result['5adf442bb2db1df852f44df0'], 'Program with classpass size of 3');
        $this->assertArrayNotHasKey('5adf44e7cdd89bdf93f1df8e', $result, 'Program with classpass size of 0');
        $this->assertArrayNotHasKey('5adf44ee8181e92a30c4f6ab', $result, 'Program with no classpass set');
    }

    public function testGetEventIdAndTotalBookingsMap()
    {
        $origin = Origin::get('classpass');
        // Event with 2 bookings,  1 from glofox and 1 from classpass
        $eventIds = ['5adf43c0db6b6587ebcb2c5b'];
        $result = $this->ListEvents->getEventIdAndTotalBookingsMap($origin, $eventIds);
        $expected = ['5adf43c0db6b6587ebcb2c5b' => 1];
        $this->assertArrayContainsArray($expected, $result, 'This Event has 2 bookings, 1 from glofox and 1 from classpass');

        // Empty event ids
        $eventIds = [];
        $result = $this->ListEvents->getEventIdAndTotalBookingsMap($origin, $eventIds);
        $expected = [];
        $this->assertArrayContainsArray($expected, $result, 'Empty event ids');
    }

    public function testGetEmptyResponse()
    {
        $emptyModel = new MainModel([
            'object' => 'list',
            'page' => 1,
            'limit' => 50,
            'has_more' => false,
            'total_count' => 0,
            'data' => [],
        ]);
        $result = $this->ListEvents->getEmptyResponse();
        $this->assertEquals($result, $emptyModel, 'Empty base model');
    }

    public function testFormatEvents()
    {
        // List of events in the API 2.0 format
        $events = [
            [
                '_id' => '5adf43c0db6b6587ebcb2c5b',
                'namespace' => 'classpass_integrated',
                'branch_id' => '5addc25383266f65abf515c4',
                'program_id' => '5adf442bb2db1df852f44df0',
                'active' => true,
                'name' => 'Classpass size 3 and glofox size 10',
                'description' => 'Classpass size 3 and glofox size 10',
                'time_start' => 1_524_726_000,
                'size' => 10,
                'facility' => '5adf4cb5fca91f2d25f62df4',
                'trainers' => [
                    '5adf4d52f35bbcfb4251f960',
                ],
                'booked' => 3,
                'waiting' => 0,
                'has_booked' => false,
                'booking_status' => null,
                'duration' => 120,
                'type' => 'event',
            ],
        ];
        //This is the program that the given event belongs to
        $programIdAndSizeMap = ['5adf442bb2db1df852f44df0' => 2];

        $result = $this->ListEvents->formatEvents($events, $programIdAndSizeMap);
        $expectedChanges = [
            'size' => 10,
            'booked' => 3,
            'integrator_size' => 2,
        ];
        $expected = $events;
        $expected[0] = array_merge($expected[0], $expectedChanges);

        $this->assertArrayContainsArray($expected, $result, 'Classpass program size 10 with 3 total bookings and 2 classpass bookings made.');

        // Available spots less than the classpass size
        $eventsCopy = $events;
        $eventsCopy[0]['size'] = 4;
        $eventsCopy[0]['booked'] = 3;
        $eventsCopy[0]['integrator_booking_count'] = 2;
        $programIdAndSizeMap = ['5adf442bb2db1df852f44df0' => 3];
        $expectedChanges = ['size' => 4, 'booked' => 3, 'integrator_size' => 3];
        $expected = $eventsCopy;
        $expected[0] = array_merge($expected[0], $expectedChanges);
        $result = $this->ListEvents->formatEvents($eventsCopy, $programIdAndSizeMap);
        $this->assertArrayContainsArray($expected, $result, 'Available spots less than the classpass size.');
    }
}
