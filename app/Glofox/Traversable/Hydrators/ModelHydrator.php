<?php

namespace Glofox\Traversable\Hydrators;

use Glofox\Model;
use Glofox\Traversable\Exceptions\StrictTypeViolationException;
use Glofox\Traversable\Hydrators\Contracts\HydratorContract;
use Illuminate\Support\Collection;

class ModelHydrator extends Hydrator implements HydratorContract
{
    public function fill(array $items)
    {
        /** @var Collection $instance */
        $instance = $this->instance;

        if (!$instance && method_exists($this->expected,'fromDatabase')) {
            $instance = $this->expected::fromDatabase($items);
            return $instance;
        }

        if (!$instance) {
            $instance = app()->make($this->expected);
        }

        if (!($instance instanceof Model)) {
            throw StrictTypeViolationException::withExpectedAndActual(Model::class, get_class($instance));
        }

        foreach ($items as $property => $value) {
            $instance->put($property, $value);
        }

        return $instance;
    }
}