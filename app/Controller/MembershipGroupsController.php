<?php

/**
 * Class MembershipGroupsController.
 */
class MembershipGroupsController extends AppController
{
    /**
     * @var string
     */
    public $name = 'MembershipGroups';

    /**
     * @var array
     */
    public $components = [
        'Utility',
    ];

    /**
     * @var array
     */
    public $uses = [
        'MembershipGroup',
    ];

    /****************************** REST CALLS ********************************************
     *
     * @param $branch_id
     *
     * @return string
     */

    public function findAllByBranchId($branch_id)
    {
        // Validate Branch Id
        if (empty($branch_id)) {
            return json_encode(['success' => false, 'message' => __('The Branch Id is empty')], JSON_PARTIAL_OUTPUT_ON_ERROR);
        }
        $is_valid_mongoid = $this->Utility->is_valid_mongoid($branch_id);
        if (empty($is_valid_mongoid)) {
            return json_encode(['success' => false, 'message' => __('Invalid Branch Id')], JSON_PARTIAL_OUTPUT_ON_ERROR);
        }

        $find_membership_groups = $this->MembershipGroup->findAllByBranchId($branch_id);

        $membership_groups = [];
        $membership_groups_list = [];

        foreach ($find_membership_groups as $membership_group) {
            $membership_groups[] = $membership_group['MembershipGroup'];
            $membership_groups_list[$membership_group['MembershipGroup']['_id']] = $membership_group['MembershipGroup']['name'];
        }

        return json_encode(['success' => true, 'membershipGroups' => $membership_groups, 'membershipGroupsList' => $membership_groups_list], JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    /**
     * @param $branch_id
     * @param $membership_group_id
     *
     * @return string
     */
    public function findByBranchIdAndId($branch_id, $membership_group_id)
    {
        // Validate Branch Id
        if (empty($branch_id)) {
            return json_encode(['success' => false, 'message' => __('The Branch Id is empty')], JSON_PARTIAL_OUTPUT_ON_ERROR);
        }
        $is_valid_mongoid = $this->Utility->is_valid_mongoid($branch_id);
        if (empty($is_valid_mongoid)) {
            return json_encode(['success' => false, 'message' => __('Invalid Branch Id')], JSON_PARTIAL_OUTPUT_ON_ERROR);
        }

        // Validate Membership Group Id
        if (empty($membership_group_id)) {
            return json_encode(['success' => false, 'message' => __('The Membership Id is empty')], JSON_PARTIAL_OUTPUT_ON_ERROR);
        }
        $is_valid_mongoid = $this->Utility->is_valid_mongoid($membership_group_id);
        if (empty($is_valid_mongoid)) {
            return json_encode(['success' => false, 'message' => __('Invalid Membership Id')], JSON_PARTIAL_OUTPUT_ON_ERROR);
        }

        $membership_group = $this->MembershipGroup->findByBranchIdAndId($branch_id, $membership_group_id);
        if (empty($membership_group)) {
            return json_encode(['success' => false, 'message' => __('There is no membership group with that id')], JSON_PARTIAL_OUTPUT_ON_ERROR);
        }

        return json_encode(['success' => true, 'membershipGroup' => $membership_group['MembershipGroup']], JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    public function save()
    {
        $membership_group_data = $this->request->data['MembershipGroup'];
        if (empty($membership_group_data['_id'])) {
            $this->MembershipGroup->create();
        } else {
            $this->MembershipGroup->read(null, $membership_group_data['_id']);
        }

        $membership_group_data['active'] = true;
        // Probably we should validate in here
        $save_membership_group = $this->MembershipGroup->save($membership_group_data);

        if ($save_membership_group) {
            return json_encode(['success' => true, 'message' => 'The membership groups has been saved successfully', 'membership' => $save_membership_group['MembershipGroup']], JSON_PARTIAL_OUTPUT_ON_ERROR);
        } else {
            return json_encode(['success' => false, 'message' => 'Ocurred a problem saving your membership group.']);
        }
    }

    /**
     * @return string
     */
    public function delete()
    {
        $membership_group_data = $this->request->data['MembershipGroup'];

        $membership_group = $this->MembershipGroup->findByBranchIdAndId($membership_group_data['branch_id'], $membership_group_data['_id']);
        if (empty($membership_group)) {
            return json_encode(['success' => false, 'message' => 'Membership Group not found']);
        }

        $this->loadModel('User');
        $countMembers = $this->User->countByMembershipGroup($membership_group['branch_id'], $membership_group['_id']);

        if ($countMembers > 0) {
            $membership_group['MembershipGroup']['active'] = false;
            $this->MembershipGroup->read(null, $membership_group['MembershipGroup']['_id']);
            $success = $this->MembershipGroup->save($membership_group);
        } else {
            $success = $this->MembershipGroup->delete($membership_group['MembershipGroup']['_id']);
        }

        if ($success) {
            return json_encode(['success' => true, 'count' => $countMembers, 'message' => 'You have successfully deleted the membership group.'], JSON_PARTIAL_OUTPUT_ON_ERROR);
        } else {
            return json_encode(['success' => false, 'message' => 'Ocurred a problem deleting the membership group.']);
        }
    }

    /**
     * ANGULARJS ENABLED SERVICES BELOW.
     */

    /**
     * Find all membershipGroups for current branch.
     *
     * @return string [type] [description]
     */
    public function findAll()
    {
        //Validate user
        $user = $this->JWT->getUser();
        $allowed_roles = ['admin', 'superadmin'];
        $this->JWT->validateOrFail($allowed_roles, $user['User']['namespace']);
        $namespace = $user['User']['namespace'];
        $branch_id = $user['User']['branch_id'];

        //Get facilities
        $groups = $this->MembershipGroup->findAllByBranchId($branch_id);

        return json_encode($groups, JSON_PARTIAL_OUTPUT_ON_ERROR);
    }
}
