<?php

use CakeTestCases\Glofox\Domain\Authentication\Token\TokenGeneration;
use CakeTestCases\Glofox\Domain\Users\Traits\FetchUsersTrait;

App::import('Test/Case', 'GlofoxControllerTestCase');
App::uses('CategoriesController', 'Controller');

/**
 * CategoriesController Test Case.
 */
class CategoriesControllerTest extends GlofoxControllerTestCase
{
    use FetchUsersTrait;
    use TokenGeneration;

    /**
     * Fixtures.
     *
     * @var array
     */
    public $fixtures = [
        'app.branch',
        'app.user',
        'app.category',
        'app.access_control_list',
    ];

    public static $token;

    /*************************** API 2.0 **************************/

    /**
     * Authenticate as User, calls authenticate with password with new api.
     */
    public function testLoginRedirect()
    {
        $user = $this->fetchUser('59a7011a05c677bda916612a');
        $token = $this->loginAsUser($user);

        self::$token = $token;

        self::assertNotNull($token);
    }

    public function testGetAll()
    {
        $_SERVER['HTTP_AUTHORIZATION'] = self::$token;

        $result = $this->testAction('/2.0/categories/', ['method' => 'get']);
        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        $this->assertNotNull($result->data);
        $this->assertTrue(is_countable($result->data) && count($result->data) > 0);
    }

    public function testGetById()
    {
        $_SERVER['HTTP_AUTHORIZATION'] = self::$token;
        $result = $this->testAction('/2.0/categories/59ba57b0e5c2b7ae3683a017', ['method' => 'get']);
        $result = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        $this->assertNotEmpty($result['_id']);
    }

    public function testView()
    {
        $_SERVER['HTTP_AUTHORIZATION'] = self::$token;
        $result = $this->testAction('/categories/view/59ba57b0e5c2b7ae3683a017', ['method' => 'get']);
        $result = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        $this->assertGreaterThan(0, is_countable($result) ? count($result) : 0);
        $this->assertEquals('59ba57b0e5c2b7ae3683a017', $result['Category']['_id']);
    }

    public function testFindByModel()
    {
        $_SERVER['HTTP_AUTHORIZATION'] = self::$token;
        $result = $this->testAction('/categories/findByModel/programs', ['method' => 'get']);
        $result = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        $this->assertGreaterThan(0, is_countable($result) ? count($result) : 0);
    }

    public function testFindEntitiesByBranchIdAndCategoryId()
    {
        $_SERVER['HTTP_AUTHORIZATION'] = self::$token;
        $result = $this->testAction('/categories/findEntitiesByBranchIdAndCategoryId/49a7011a05c677b9a916612a/59ba57b0e5c2b7ae3683a017', ['method' => 'get']);
        $result = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        $this->assertTrue($result['success']);

        $result = $this->testAction('/categories/findEntitiesByBranchIdAndCategoryId/49a7011a05c677b9a916612a/59ba57b0e5c2b7ae3683a018', ['method' => 'get']);
        $result = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        $this->assertTrue($result['success']);

        $result = $this->testAction('/categories/findEntitiesByBranchIdAndCategoryId/49a7011a05c677b9a916612a/59ba57b0e5c2b7ae3683a019', ['method' => 'get']);
        $result = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        $this->assertTrue($result['success']);

        $result = $this->testAction('/categories/findEntitiesByBranchIdAndCategoryId/49a7011a05c677b9a916612a/59ba57b0e5c2b7ae3683a020', ['method' => 'get']);
        $result = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        $this->assertFalse($result['success']);
    }
}
