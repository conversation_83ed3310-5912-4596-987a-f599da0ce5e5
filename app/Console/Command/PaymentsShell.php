<?php

use Glofox\Console\CommandHandlerInterface;
use Glofox\Console\CommandInputType;
use Glofox\Console\CommandParameter;
use Glofox\Console\CommandParametersCollection;
use Glofox\Payments\Console\CommandResolver;
use Illuminate\Support\Facades\Validator;
use Psr\Log\LoggerInterface;
use Saritasa\Laravel\Validation\GenericRuleSet;

class PaymentsShell extends \AppShell
{
    /** @var Illuminate\Support\Collection */
    private $commands;

    /** @var LoggerInterface */
    private $logger;

    public function __construct($stdout = null, $stderr = null, $stdin = null)
    {
        parent::__construct($stdout, $stderr, $stdin);

        $this->commands = app()->make(CommandResolver::class)->resolveAll();

        $this->logger = app()->make(LoggerInterface::class);
    }

    public function getOptionParser()
    {
        $optionParser = parent::getOptionParser();

        /** @var CommandHandlerInterface $commandHandler */
        foreach ($this->commands as $commandHandler) {
            $subCommandOptions = [];

            /** @var CommandParameter $parameter */
            foreach ($commandHandler->parameters() as $parameter) {
                $subCommandOptions[$parameter->name()] = [
                    'help' => $parameter->description(),
                    'default' => $parameter->default(),
                    'boolean' => $parameter->type()->is(CommandInputType::BOOLEAN()),
                ];
            }

            $optionParser->addSubcommand($commandHandler->name(), [
                'parser' => ConsoleOptionParser::buildFromArray([
                    'command' => $commandHandler->name(),
                    'options' => $subCommandOptions,
                ]),
            ]);
        }

        return $optionParser;
    }

    public function main()
    {
        /** @var CommandHandlerInterface $commandHandler */
        $commandHandler = $this->getRequestedCommand();

        $this->logger->info(sprintf('Payments Shell Starting...'));
        $this->logger->info(sprintf('Command: %s, params: %s', $this->command, json_encode($this->params, JSON_PARTIAL_OUTPUT_ON_ERROR)));

        $this->validateParameters($this->params, $commandHandler->parameters());

        $commandHandler->invoke($this->params);

        $this->logger->info(sprintf('Payments Shell Finished'));
    }

    public function getRequestedCommand(): CommandHandlerInterface
    {
        $requestedCommand = $this->command;

        $command = $this->commands->first(fn(CommandHandlerInterface $commandHandler) => $commandHandler->name() === $requestedCommand);

        if (!$command) {
            throw new Exception(sprintf('Command %s not found', $requestedCommand));
        }

        return $command;
    }

    private function validateParameters(array $input, CommandParametersCollection $expectedParams): void
    {
        $rules = collect();

        /** @var CommandParameter $expectedParam */
        foreach ($expectedParams as $expectedParam) {
            $rule = new GenericRuleSet();

            $rule = $expectedParam->isRequired() ? $rule->required() : $rule->sometimes();

            $rule = $expectedParam->type()->is(CommandInputType::BOOLEAN()) ? $rule->boolean() : $rule->string();

            $rules->put($expectedParam->name(), $rule);
        }

        try {
            Validator::make($input, $rules->toArray())->validate();
        } catch (\Illuminate\Validation\ValidationException $exception) {
            $validationErrors = $exception->validator->errors()->toArray();

            $this->logger->error(json_encode($validationErrors, JSON_PARTIAL_OUTPUT_ON_ERROR));

            throw $exception;
        }
    }
}
