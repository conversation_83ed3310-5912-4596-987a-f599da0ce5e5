<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\Subscriptions\Services;

use Glofox\Domain\AsyncEvents\Events\SubscriptionRenewedEventMeta;
use Glofox\Domain\AsyncEvents\Events\SubscriptionRenewedEventPayload;
use Glofox\Domain\Subscriptions\Services\SubscriptionsEventPublisher;
use Glofox\Eventkit\Publisher\DomainEventPublisher;
use Psr\Log\LoggerInterface;

\App::import('Test/Case', 'GlofoxTestCase');

class SubscriptionsEventPublisherTest extends \GlofoxTestCase
{
    public $fixtures = [];

    public function test_it_should_send_subscription_renewed_events(): void
    {
        $logger = app()->make(LoggerInterface::class);

        $domainEventPublisher = \Mockery::mock(DomainEventPublisher::class);
        $domainEventPublisher->shouldReceive('publish');

        $meta = \Mockery::mock(SubscriptionRenewedEventMeta::class);
        $payload = \Mockery::mock(SubscriptionRenewedEventPayload::class);

        $publisher = new SubscriptionsEventPublisher($domainEventPublisher, $logger);
        $publisher->sendSubscriptionRenewedEvent($meta, $payload);
    }
}
