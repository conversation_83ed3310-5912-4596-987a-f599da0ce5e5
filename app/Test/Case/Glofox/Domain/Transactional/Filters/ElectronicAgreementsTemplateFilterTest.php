<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\Transactional\Filters;

use Glofox\Domain\Branches\Models\Branch;
use Glofox\Domain\Branches\Repositories\BranchesRepository;
use Glofox\Domain\ElectronicAgreements\Services\ElectronicAgreementsServiceInterface;
use Glofox\Domain\Transactional\Filters\ElectronicAgreementsTemplateFilter;
use Glofox\Domain\Transactional\Filters\OnlineBookingConfirmationTemplateFilter;
use Glofox\Domain\Transactional\Messages\Variable;
use Glofox\Infrastructure\Flags\FlaggerInterface;

\App::import('Test/Case', 'GlofoxTestCase');

class ElectronicAgreementsTemplateFilterTest extends \GlofoxTestCase
{
    public $fixtures = [];

    public function test_it_does_nothing_when_eagreements_enabled(): void
    {
        $branchId = 'branch-id';

        $eagreements = \Mockery::mock(ElectronicAgreementsServiceInterface::class);
        $eagreements->shouldReceive('isElectronicAgreementsEnabled')
            ->withArgs(fn (string $branchId) => $branchId == 'branch-id')
            ->andReturn(true)
            ->once();

        $filter = new ElectronicAgreementsTemplateFilter($eagreements);

        $filteredMessages = $filter->execute($this->getSampleMessages(), $branchId);
        self::assertEquals($this->getSampleMessages(), $filteredMessages);
    }

    public function test_it_filters_eagreements_variables_when_not_enabled(): void
    {
        $branchId = 'branch-id';

        $eagreements = \Mockery::mock(ElectronicAgreementsServiceInterface::class);
        $eagreements->shouldReceive('isElectronicAgreementsEnabled')
            ->withArgs(fn (string $branchId) => $branchId == 'branch-id')
            ->andReturn(false)
            ->once();

        $filter = new ElectronicAgreementsTemplateFilter($eagreements);

        $filteredMessages = $filter->execute($this->getSampleMessages(), $branchId);
        self::assertEquals([
            [
                'identifier' => 'WELCOME',
                'variables' => [
                    Variable::BRANCH_NAME,
                ]
            ],
            [
                'identifier' => 'NO_CREDITS_REMAINING',
                'variables' => [
                    Variable::BRANCH_NAME,
                    Variable::MEMBER_FIRST_NAME,
                ]
            ],
            [
                'identifier' => 'MEMBERSHIP_CONFIRMATION',
                'variables' => [
                    Variable::MEMBERSHIP_TYPE_NAME,
                ]
            ]
        ], $filteredMessages);
    }

    private function getSampleMessages(): array
    {
        return [
            [
                'identifier' => 'WELCOME',
                'variables' => [
                    Variable::BRANCH_NAME,
                    Variable::DOCUMENT_URL,
                ]
            ],
            [
                'identifier' => 'NO_CREDITS_REMAINING',
                'variables' => [
                    Variable::BRANCH_NAME,
                    Variable::MEMBER_FIRST_NAME,
                    Variable::DOCUMENT_NAME,
                ]
            ],
            [
                'identifier' => 'EA_SIGNATURE_REQUEST',
                'variables' => [
                    Variable::BRANCH_NAME,
                    Variable::MEMBER_FIRST_NAME,
                ]
            ],
            [
                'identifier' => 'MEMBERSHIP_CONFIRMATION',
                'variables' => [
                    Variable::DOCUMENT_URL,
                    Variable::MEMBERSHIP_TYPE_NAME,
                ]
            ],
            [
            'identifier' => 'EA_AGREEMENT_SIGNED_CONFIRMATION',
            'variables' => [
                Variable::BRANCH_NAME,
                Variable::MEMBER_FIRST_NAME,
            ]
        ],
        ];
    }
}
