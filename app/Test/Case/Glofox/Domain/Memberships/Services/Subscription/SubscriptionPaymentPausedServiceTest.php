<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\Memberships\Services\Subscription;

use Glofox\Domain\Events\Search\Expressions\Id;
use Glofox\Domain\Memberships\Services\MembershipsClient;
use Glofox\Domain\Memberships\Services\Subscription\SubscriptionPaymentPausedService;
use Glofox\Domain\Users\Models\User;
use Glofox\Domain\Users\Repositories\UsersRepository;

\App::import('Test/Case', 'GlofoxTestCase');

class SubscriptionPaymentPausedServiceTest extends \GlofoxTestCase
{
    public $fixtures = [
        'app.user',
        'app.branch',
    ];

    public function test_it_should_notify_membership_service_upon_execution(): void
    {
        $tester = $this;

        $start = time();
        $finish = strtotime('+ 2 weeks');

        $userId = '5b7ad05af520ea6624c59bc5';
        $user = app(UsersRepository::class)
            ->addCriteria(new Id($userId))
            ->firstOrFail();

        $user['id'] = $userId;

        $user['membership'] = [
            'subscription' => [
                'payment_gateway_id' => 'sub_001'
            ]
        ];

        $membershipClientMocked = \Mockery::mock(MembershipsClient::class)
            ->shouldReceive('post')
            ->andReturnUsing(function (string $endpointKey, array $endpointParameters, array $data) use ($tester, $start, $finish, $user) {
                $this->assertEquals('NOTIFY_SUBSCRIPTION_PAUSE', $endpointKey);

                $tester->assertEquals(['subscriptionProviderId' => 'sub_001'], $endpointParameters);

                $tester->assertEquals($start, $data['startDate']);
                $tester->assertEquals($finish, $data['expiryDate']);
                ;
                $tester->assertEquals($user->id(), $data['userId']);
                ;
            })
            ->getMock();

        $service = new SubscriptionPaymentPausedService($membershipClientMocked);

        $service->execute($user, $start, $finish);
    }
}
