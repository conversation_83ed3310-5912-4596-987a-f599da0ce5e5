<?php

use CakeTestCases\Glofox\Domain\Authentication\Token\TokenGeneration;
use CakeTestCases\Glofox\Domain\Logger\Traits\MockedAbClientValidatorTrait;
use CakeTestCases\Glofox\Domain\Logger\Traits\MockedLoggerTrait;
use CakeTestCases\Glofox\Domain\Users\Traits\AuthenticateUsersTrait;
use CakeTestCases\Glofox\Domain\Users\Traits\FetchUsersTrait;
use CakeTestCases\Helper\JsonSchema\JsonSchemaValidator;
use CakeTestCases\Helper\JsonSchema\SchemaFile;
use Carbon\Carbon;
use Glofox\Authentication\PasswordHasher;
use Glofox\Authentication\UserLoginLogger;
use Glofox\Domain\Accesses\Services\AccessesPublisher;
use Glofox\Domain\AsyncEvents\Events\MemberAccessInfoEventsMeta;
use Glofox\Domain\AsyncEvents\Events\MemberAccessInfoEventsPayload;
use Glofox\Domain\AsyncEvents\Events\MemberUpdatedEventMeta;
use Glofox\Domain\AsyncEvents\Events\MemberUpdatedEventPayload;
use Glofox\Domain\AsyncEvents\Events\NewMemberAddedEventMeta;
use Glofox\Domain\AsyncEvents\Events\NewMemberAddedEventPayload;
use Glofox\Domain\Authentication\Auth;
use Glofox\Domain\Authentication\UseCase\ResetPassword;
use Glofox\Domain\Cards\Repositories\CardsRepository;
use Glofox\Domain\Charges\Commands\SendReceiptHandler;
use Glofox\Domain\ElectronicAgreements\Models\Agreement;
use Glofox\Domain\ElectronicAgreements\Services\ElectronicAgreementsServiceInterface;
use Glofox\Domain\ElectronicAgreements\Trigger;
use Glofox\Domain\Events\Services\EventsPublisher;
use Glofox\Domain\FeatureFlags\FeatureFlagInterface;
use Glofox\Domain\FeatureFlags\Flaggers\SkipFailedPaymentEmailFlagger;
use Glofox\Domain\Leads\Enums\LeadSchemaFields;
use Glofox\Domain\Leads\Status;
use Glofox\Domain\Leads\UseCase\UpdateLeadSourcesData;
use Glofox\Domain\Memberships\Services\LegacySync\LegacySyncService;
use Glofox\Domain\Memberships\Services\MembershipsEventPublisher;
use Glofox\Domain\Memberships\Services\Pause\SubscriptionComponentPauseService;
use Glofox\Domain\Memberships\Services\Payg\GoPaygService;
use Glofox\Domain\Memberships\Services\RequestMembershipSyncService;
use Glofox\Domain\Memberships\Services\TermsAndConditions\AcceptUserTermsService;
use Glofox\Domain\Memberships\Services\Validate\ValidateUserHasActiveNonPAYGMembership;
use Glofox\Domain\Memberships\Type;
use Glofox\Domain\SalesTaxes\Services\PriceCalculatorClient;
use Glofox\Domain\Subscription\Service\RevertToPaygAggregatedValidationService;
use Glofox\Domain\Users\Http\Response\Api2\GetAllStaffResponse;
use Glofox\Domain\Users\Http\Response\Api2\GetStaffByIdResponse;
use Glofox\Domain\Users\MemberTransfer\Exception\UnauthorizedMemberTransferDataProvided;
use Glofox\Domain\Users\Models\User;
use Glofox\Domain\Users\Models\UserAddress;
use Glofox\Domain\Users\Repositories\UsersRepository;
use Glofox\Domain\Users\Services\Avatar\AvatarUploader;
use Glofox\Domain\Users\Services\Avatar\ImageModeratorClientInterface;
use Glofox\Domain\Users\Services\UsersPublisher;
use Glofox\Domain\Users\UseCase\LoginUser;
use Glofox\Domain\Users\UseCase\UserCanReRegister;
use Glofox\Domain\Users\Validation\CanChangeSuperAdminTypeValidator;
use Glofox\Domain\Users\Validation\CanCreateStaffValidator;
use Glofox\Domain\Users\Validation\CanDeleteSuperAdminValidator;
use Glofox\Domain\Users\Validation\ChildLimitValidator;
use Glofox\Domain\Users\Validation\LeadStatusChangeFlowValidator;
use Glofox\Domain\Wallets\Services\Balance\SendEmailBasedOnBalanceService;
use Glofox\Domain\Wallets\Services\Responses\BalanceResponse;
use Glofox\Domain\Wallets\Services\Responses\SettingsResponse;
use Glofox\Domain\Wallets\Services\WalletsClient;
use Glofox\Eventkit\DomainEvent\AbstractDomainEventMeta;
use Glofox\Eventkit\DomainEvent\AbstractDomainEventPayload;
use Glofox\Http\Source;
use Glofox\Infrastructure\Flags\Flag;
use Glofox\Infrastructure\Flags\Flagger;
use Glofox\Infrastructure\Flags\Flaggers\ConsentFlagger;
use Glofox\Infrastructure\Honeycomb\HoneycombTracker;
use Glofox\Payments\Contracts\PaymentProviderContract;
use Glofox\Payments\Entities\Invoice\Contracts\InvoiceHandlerContract;
use Glofox\Payments\Entities\Invoice\Models\InvoiceEntity;
use Glofox\Payments\Entities\Invoice\Models\InvoiceEntityLatestTransactionGroup;
use Glofox\Payments\Entities\Transaction\Models\Transaction;
use Glofox\Payments\PaymentsHandler;
use Glofox\Repositories\Search\Expressions\Shared\Id;
use Glofox\Request;
use Glofox\Storage\CloudStorageInterface;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use Mockery\MockInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Request as RequestAlias;

App::import('Test/Case', 'GlofoxControllerTestCase');
App::uses('UsersController', 'Controller');

class UsersControllerTest extends GlofoxControllerTestCase
{
    use FetchUsersTrait;
    use TokenGeneration;
    use MockedLoggerTrait;
    use MockedAbClientValidatorTrait;
    use AuthenticateUsersTrait;

    private const GLOFOX_BRANCH_ID = '49a7011a05c677b9a916612a';
    private const TRAINER_ID = '58568a8fa875ab19530041a7';
    private const ADMIN_ID = '59a3011a05c677bda916612a';
    private const SUPERADMIN_ID = '59a7011a05c677bda916619a';
    private const RECEPTIONIST_ID = '59a7011a05c677bda916619b';
    private const GLOFOX_NAMESPACE = 'glofox';
    private const UNAUTHORIZED_MESSAGE = 'Unauthorized';
    public static $token;
    public $fixtures = [
        'app.access',
        'app.access_control_list',
        'app.branch',
        'app.booking',
        'app.card',
        'app.membership',
        'app.payment_method',
        'app.payment_method_user',
        'app.payment_provider',
        'app.user',
        'app.user_credit',
        'app.client',
        'app.subscription_plan',
        'app.time_slot',
        'app.integrator',
        'app.lead_sources',
    ];
    public static $refId;
    /** @var JsonSchemaValidator */
    private $jsonSchemaValidator;
    /** @var PasswordHasher */
    private $passwordHasher;
    private FeatureFlagInterface $flagger;
    private HoneycombTracker $honeycombTracker;

    public function setUp()
    {
        parent::setUp();

        app()->forgetInstance(UsersPublisher::class);
        app()->forgetInstance(MembershipsEventPublisher::class);
        app()->forgetInstance(LoggerInterface::class);
        app()->forgetInstance(UsersRepository::class);
        app()->forgetInstance(ElectronicAgreementsServiceInterface::class);
        app()->forgetInstance(Auth::class);
        app()->forgetInstance(Request::class);
        app()->forgetInstance(ImageModeratorClientInterface::class);
        app()->forgetInstance(ConsentFlagger::class);
        app()->forgetInstance(FeatureFlagInterface::class);
        app()->bind(MembershipsEventPublisher::class, function () {
            $publisher = Mockery::mock(MembershipsEventPublisher::class);

            return $publisher;
        });

        app()->bind(ElectronicAgreementsServiceInterface::class, function () {
            $ea = Mockery::mock(ElectronicAgreementsServiceInterface::class);
            $ea->shouldReceive('isElectronicAgreementsEnabled')->andReturn(false);

            return $ea;
        });

        app()->bind(PriceCalculatorClient::class, function () {
            $priceCalculatorClient = Mockery::mock(PriceCalculatorClient::class);

            return $priceCalculatorClient;
        });

        $this->jsonSchemaValidator = app()->make(JsonSchemaValidator::class);
        $this->passwordHasher = app()->make(PasswordHasher::class);

        $this->mockLogger();

        app()->bind(
            SkipFailedPaymentEmailFlagger::class,
            fn () => Mockery::mock(SkipFailedPaymentEmailFlagger::class, function (MockInterface $mock) {
                $mock
                    ->shouldReceive('has')
                    ->andReturnFalse();
            })
        );
    }

    public function tearDown()
    {
        $this->tearDownMockedAbTestValidator();

        parent::tearDown();

        Mockery::close();

        app()->forgetInstance(UsersPublisher::class);
        app()->forgetInstance(MembershipsEventPublisher::class);
        app()->forgetInstance(PaymentsHandler::class);
        app()->forgetInstance(UsersRepository::class);
        app()->forgetInstance(PriceCalculatorClient::class);
        app()->forgetInstance(ImageModeratorClientInterface::class);
        app()->offsetUnset(SkipFailedPaymentEmailFlagger::class);
        app()->forgetInstance(CanChangeSuperAdminTypeValidator::class);
        app()->forgetInstance(ConsentFlagger::class);
        app()->forgetInstance(Flagger::class);
        app()->forgetInstance(GoPaygService::class);
        app()->forgetInstance(AcceptUserTermsService::class);

        $this->teardownLogger();
    }

    public function test_findMemberByBranchIdAndId_denies_access_for_guests(): void
    {
        $this->loginAsGuest('glofox', '49a7011a05c677b9a916612b');

        $result = $this->testAction('/branch/49a7011a05c677b9a916612b/user/59a7011a05c677bda916612d/find_member', [
            'method' => Request::METHOD_GET,
        ]);

        $result = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        self::assertFalse($result['success']);
        self::assertEquals(
            'Unauthorized',
            $result['message_code']
        );
    }

    public function test_findMemberByBranchIdAndId_allows_access_for_members(): void
    {
        $user = $this->fetchUser('622a0b4ae39c9971f7a28f03');
        $this->loginAsUser($user, '49a7011a05c677b9a916612b');

        $result = $this->testAction('/branch/49a7011a05c677b9a916612b/user/622a0b4ae39c9971f7a28f03/find_member', [
            'method' => Request::METHOD_GET,
        ]);

        $result = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        self::assertTrue($result['success']);
        self::assertEquals('622a0b4ae39c9971f7a28f03', $result['User']['_id']);
    }

    public function test_findTrainerByBranchIdAndUserId_denies_access_for_guests(): void
    {
        $this->loginAsGuest('glofox', '49a7011a05c677b9a916612b');

        $result = $this->testAction(
            '/users/findTrainerByBranchIdAndUserId/49a7011a05c677b9a916612b/59a7011a05c677bda916612d',
            [
                'method' => Request::METHOD_GET,
            ]
        );

        $result = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        self::assertFalse($result['success']);
        self::assertEquals('ERROR_UNAUTHORIZED', $result['message_code']);
    }

    public function test_findTrainerByBranchIdAndUserId_denies_access_for_members(): void
    {
        $this->loginAsUser($this->fetchUser('59a3011a05c677bda916619b'));

        $result = $this->testAction(
            '/users/findTrainerByBranchIdAndUserId/49a7011a05c677b9a916612b/59a7011a05c677bda916612d',
            [
                'method' => Request::METHOD_GET,
            ]
        );

        $result = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        self::assertFalse($result['success']);
        self::assertEquals('ERROR_UNAUTHORIZED', $result['message_code']);
    }

    public function test_findTrainerByBranchId_denies_access_for_guests(): void
    {
        $this->loginAsGuest('glofox', '49a7011a05c677b9a916612b');

        $result = $this->testAction('/users/findTrainerByBranchId/49a7011a05c677b9a916612b', [
            'method' => Request::METHOD_GET,
        ]);

        $result = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        self::assertFalse($result['success']);
        self::assertEquals('ERROR_UNAUTHORIZED', $result['message_code']);
    }

    public function test_findTrainerByBranchId_denies_access_for_members(): void
    {
        $this->loginAsUser($this->fetchUser('59a3011a05c677bda916619b'));

        $result = $this->testAction('/users/findTrainerByBranchId/49a7011a05c677b9a916612b', [
            'method' => Request::METHOD_GET,
        ]);

        $result = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        self::assertFalse($result['success']);
        self::assertEquals('ERROR_UNAUTHORIZED', $result['message_code']);
    }

    public function test_findUserByBranchIdBasicInfo_denies_insecure_access_for_non_whitelisted_branches(): void
    {
        $result = $this->testAction('/users/findUserByBranchIdBasicInfo/49a7011a05c677b9a916612b', [
            'method' => Request::METHOD_GET,
        ]);

        $result = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        self::assertFalse($result['success']);
        self::assertEquals('The authentication token is empty', $result['message']);
    }

    public function test_findUserByBranchIdBasicInfo_denies_access_for_guests(): void
    {
        $this->loginAsGuest('glofox', '49a7011a05c677b9a916612b');

        $result = $this->testAction('/users/findUserByBranchIdBasicInfo/49a7011a05c677b9a916612b', [
            'method' => Request::METHOD_GET,
        ]);

        $result = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        self::assertFalse($result['success']);
        self::assertEquals('ERROR_UNAUTHORIZED', $result['message_code']);
    }

    public function test_findUserByBranchIdBasicInfo_denies_access_for_members(): void
    {
        $this->loginAsUser($this->fetchUser('59a3011a05c677bda916619b'));

        $result = $this->testAction('/users/findUserByBranchIdBasicInfo/49a7011a05c677b9a916612b', [
            'method' => Request::METHOD_GET,
        ]);

        $result = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        self::assertFalse($result['success']);
        self::assertEquals('ERROR_UNAUTHORIZED', $result['message_code']);
    }

    public function test_findUserByBranchIdBasicInfo_denies_cross_branch_token_access(): void
    {
        $this->authenticateAsAdmin();

        $result = $this->testAction('/users/findUserByBranchIdBasicInfo/49a7011a05c677b9a916614d', [
            'method' => Request::METHOD_GET,
        ]);

        $result = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        self::assertFalse($result['success']);
        self::assertEquals('ERROR_UNAUTHORIZED', $result['message_code']);
    }

    public function test_findUserByBranchIdBasicInfo_allows_secure_access_for_all_branches(): void
    {
        $this->authenticateAsAdmin();

        $result = $this->testAction('/users/findUserByBranchIdBasicInfo/49a7011a05c677b9a916612a', [
            'method' => Request::METHOD_GET,
        ]);

        $result = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        self::assertTrue(is_countable($result) && count($result) > 0);
        self::assertArrayNotHasKey('success', $result);
    }

    public function forLegacyListViewEndpointDataProvider(): array
    {
        return [
            'When the user is a trainer, the legacy listview can return staff list' => [
                'user' => self::TRAINER_ID,
                'endpoint' => '/users/listview/null/null/null/staff/1',
                'expected' => 'USER_LIST',
            ],
            'When the user is a trainer, the legacy listview can return users only with trainer type' => [
                'user' => self::TRAINER_ID,
                'endpoint' => '/users/listview/null/null/null/staff/1',
                'expected' => 'TRAINER_USERS_ONLY',
            ],
            'When the user is an admin, the legacy listview can return users that are either admins, superadmin, trainers or receptionist' => [
                'user' => self::ADMIN_ID,
                'endpoint' => '/users/listview/null/null/null/staff/1',
                'expected' => 'ALL_TYPE_OF_USERS',
            ],
            'When the user is an receptionist, the legacy listview can return users that are either admins, superadmin, trainers or receptionist' => [
                'user' => self::RECEPTIONIST_ID,
                'endpoint' => '/users/listview/null/null/null/staff/1',
                'expected' => 'ALL_TYPE_OF_USERS',
            ],
            'When the user is an superadmin, the legacy listview can return users that are either admins, superadmin, trainers or receptionist' => [
                'user' => self::SUPERADMIN_ID,
                'endpoint' => '/users/listview/null/null/null/staff/1',
                'expected' => 'ALL_TYPE_OF_USERS',
            ],
            'When the user is a receptionist, the legacy listview can return staff list' => [
                'user' => self::RECEPTIONIST_ID,
                'endpoint' => '/users/listview/null/null/null/staff/1',
                'expected' => 'USER_LIST',
            ],
            'When the user is a trainer, the legacy listview fails to return user list' => [
                'user' => self::TRAINER_ID,
                'endpoint' => '/users/listview/null/null/null/member/1',
                'expected' => 'ERROR_UNAUTHORIZED',
            ],
            'When the user is a receptionist, the legacy listview fails to return user list' => [
                'user' => self::RECEPTIONIST_ID,
                'endpoint' => '/users/listview/null/null/null/member/1',
                'expected' => 'ERROR_UNAUTHORIZED',
            ],
        ];
    }

    /**
     * @dataProvider forLegacyListViewEndpointDataProvider
     */
    public function test_legacy_listview_endpoint(string $user_id, string $endpoint, string $expected): void
    {
        $user = $this->fetchUser($user_id);
        $this->loginAsUser($user);

        $result = $this->testAction($endpoint, [
            'method' => Request::METHOD_GET,
        ]);

        $result = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        switch ($expected) {
            case 'USER_LIST':
                self::assertNotNull($result[0]['User']);
                self::assertArrayNotHasKey('pwd_token', $result[0]['User']);
                self::assertArrayNotHasKey('pwd_token_expiry', $result[0]['User']);
                break;
            case 'TRAINER_USERS_ONLY':
                foreach ($result as $user) {
                    $user_type = $user['User']['type'] ?? null;
                    if ($user_type) {
                        self::assertEquals('TRAINER', $user_type);
                    }
                }
                break;
            case 'ALL_TYPE_OF_USERS':
                foreach ($result as $user) {
                    $user_type = $user['User']['type'] ?? null;
                    if ($user_type) {
                        self::assertContains($user_type, ['TRAINER', 'ADMIN', 'SUPERADMIN', 'RECEPTION']);
                    }
                }
                break;
            case 'ERROR_UNAUTHORIZED':
                self::assertFalse($result['success']);
                self::assertEquals('ERROR_UNAUTHORIZED', $result['message_code']);
                break;
            default:
                break;
        }
    }

    public function test_findById_endpoint_returns_lead_with_contact_sources_and_marketing_sources_fields(): void
    {
        $this->authenticateAsTrainer();

        $this->createRoamingMemberRestrcitedProfileFlaggerMock();
        $this->createHoneycombTrackerMock();

        $userId = '87a7011a05c677bda916615a';
        $branchId = '49a7011a05c677b9a916612a';

        $result = $this->testAction(
            sprintf(
                '/users/findById/%s/%s/%s',
                $userId,
                static::GLOFOX_NAMESPACE,
                $branchId
            ),
            ['method' => 'POST']
        );

        $response = json_decode($result, true, 512, JSON_THROW_ON_ERROR);

        $user = $response['User'];

        $this->assertEquals($userId, $user['_id']);
        $this->assertArrayHasKey('leads', $user);
        $this->assertArrayHasKey('marketing_source', $user['leads']);
        $this->assertArrayHasKey('contact_source', $user['leads']);
        $this->assertNotEmpty($user['leads']['marketing_source']);
        $this->assertNotEmpty($user['leads']['contact_source']);

        app()->forgetInstance(FeatureFlagInterface::class);
        app()->forgetInstance(HoneycombTracker::class);
    }

    /**
     * @dataProvider findByIdEndpointProvider
     */
    public function test_findById_endpoint_returns_restricted_profile_rules(string $method): void
    {
        $this->authenticateAsAdmin();

        $this->createRoamingMemberRestrcitedProfileFlaggerMock();
        $this->createHoneycombTrackerMock();

        $userId = '673f7cb2728a75c772e20b0d';
        $branchId = '49a7011a05c677b9a916612a';

        $result = $this->testAction(
            sprintf(
                '/users/findById/%s/%s/%s',
                $userId,
                static::GLOFOX_NAMESPACE,
                $branchId
            ),
            ['method' => $method]
        );

        $response = json_decode($result, true, 512, JSON_THROW_ON_ERROR);

        $user = $response['User'];

        $this->assertEquals($userId, $user['_id']);
        $this->assertArrayHasKey('restricted_profile', $user['profile_rules']);
        $this->assertTrue(is_bool($user['profile_rules']['restricted_profile']));

        app()->forgetInstance(FeatureFlagInterface::class);
        app()->forgetInstance(HoneycombTracker::class);
    }

    /**
     * Data provider for testApiEndpointsWithRoles.
     */
    public function userDataProviderForStaffEndPointEmailValidation(): array
    {
        return [
            ['authenticateAsAdmin', self::ADMIN_ID, true],
            ['authenticateAsAdmin', '', true, true],
            ['authenticateAsSuperAdmin', self::ADMIN_ID, true],
            ['authenticateAsSuperAdmin', '', true, true],
            ['authenticateAsTrainer', '', false, true],
            ['authenticateAsTrainer', self::ADMIN_ID, false],
            ['authenticateAsReception', '', false, true],
            ['authenticateAsReception', self::ADMIN_ID, false],
            ['authenticateAsMember', '', false, true],
            ['authenticateAsMember', self::ADMIN_ID, false],
        ];
    }

    /**
     * Validate Email Visibility in /2.0/staff/ Endpoint Responses by User Role
     * @dataProvider userDataProviderForStaffEndPointEmailValidation
     */
    public function testApiEndpointsWithRoles(string $authenticateMethod, string $id, bool $adminOrSA): void
    {
        $this->$authenticateMethod();
        $baseUrl = '/2.0/staff/';
        $endpoint = $baseUrl . $id;
        $result = $this->testAction($endpoint, ['method' => 'get']);
        $result = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        self::assertNotEmpty($result);

        $assertionTarget = empty($id) ? $result['data'][0] : $result;

        if ($adminOrSA === true) {
            self::assertArrayHasKey('email', $assertionTarget);
        } else {
            self::assertArrayNotHasKey('email', $assertionTarget);
        }
    }

    /**
     * Get a single staff passing the 'include param'.
     */
    public function test_apiv2_get_staff_by_id_with_includes(): void
    {
        $this->authenticateAsAdmin();

        $result = $this->testAction('/2.0/staff/59a7011a05c677bda916612c?include=timeslots', ['method' => 'get']);
        $result = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        self::assertNotEmpty($result);
        self::assertArrayHasKey('timeslots', $result);
        self::assertNotEmpty($result['timeslots']);
        self::assertArrayNotHasKey('password', $result);
    }

    public function test_api2_staff_by_id_response_without_pricing(): void
    {
        $this->authenticateAsTrainer();

        $result = $this->testAction('/2.0/staff/59a7011a05c677bda916612a', ['method' => 'get']);
        $result = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        $schema = GetStaffByIdResponse::schema;
        $this->jsonSchemaValidator->validate($result, new SchemaFile($schema));
    }

    public function test_api2_get_all_staff_response(): void
    {
        $this->authenticateAsAdmin();

        $result = $this->testAction('/2.0/staff', ['method' => 'get']);
        $result = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        $schema = GetAllStaffResponse::schema;
        $this->jsonSchemaValidator->validate($result, new SchemaFile($schema));
    }

    public function test_api2_login_doesnt_return_branch_sensitive_data(): void
    {
        $this->createHoneycombTracker();
        $data = [
            'branch_id' => '49a7011a05c677b9a916612a',
            'login' => 'GUEST',
            'password' => 'GUEST',
        ];

        $result = $this->testAction('/2.0/login', ['method' => 'post', 'data' => $data]);
        $result = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        self::assertArrayNotHasKey('mailchimp', $result['branch']);
        self::assertArrayNotHasKey('lead_management', $result['branch']['features']);
        self::assertArrayNotHasKey('release', $result['branch']['push_config']);
        self::assertArrayNotHasKey('access_token', $result['branch']['features']['stripe_payment']);
        self::assertArrayNotHasKey('refresh_token', $result['branch']['features']['stripe_payment']);
        self::assertArrayNotHasKey('password', $result['user']);

        app()->forgetInstance(HoneycombTracker::class);
    }

    public function test_blocks_updating_a_members_membership_without_plan_code(): void
    {
        $this->authenticateAsAdmin();

        $user = $this->fetchUser('a9a3321a05c677bda916611c');

        $data = $user->toArray();
        unset($data['address']);
        unset($data['access_barcode']);

        unset($data['membership']['plan_code']);

        $canChangeSuperAdminTypeValidator = Mockery::mock(CanChangeSuperAdminTypeValidator::class);
        $canChangeSuperAdminTypeValidator
            ->shouldReceive('validate')
            ->once();
        app()->instance(CanChangeSuperAdminTypeValidator::class, $canChangeSuperAdminTypeValidator);

        $response = $this->testAction('/users/save', [
            'method' => 'POST',
            'data' => $data,
        ]);

        $response = json_decode($response, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        self::assertEquals(
            'The membership type time must have a plan code and a membership id',
            $response->message,
            $response
        );
        app()->forgetInstance(CanChangeSuperAdminTypeValidator::class);
    }

    public function test_blocks_updating_a_members_membership_without_membership_id(): void
    {
        $this->authenticateAsAdmin();

        $user = $this->fetchUser('a9a3321a05c677bda916611c');

        $data = $user->toArray();

        unset($data['address']);
        unset($data['access_barcode']);
        unset($data['membership']['_id']);

        $canChangeSuperAdminTypeValidator = Mockery::mock(CanChangeSuperAdminTypeValidator::class);
        $canChangeSuperAdminTypeValidator
            ->shouldReceive('validate')
            ->once();
        app()->instance(CanChangeSuperAdminTypeValidator::class, $canChangeSuperAdminTypeValidator);

        $response = $this->testAction('/users/save', [
            'method' => 'POST',
            'data' => $data,
        ]);

        $response = json_decode($response, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        self::assertEquals(
            'The membership type time must have a plan code and a membership id',
            $response->message,
            $response
        );
        app()->forgetInstance(CanChangeSuperAdminTypeValidator::class);
    }

    public function test_legacy_users_range_only_works_for_staff_tokens(): void
    {
        // Lists members without sensitive fields
        $this->authenticateAsAdmin();

        $result = $this->testAction('/users/range', [
            'data' => ['ids' => ['59a7011a05c677bda916612c']],
            'method' => 'POST',
        ]);

        $result = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        self::assertCount(1, $result);
        self::assertArrayNotHasKey('password', $result[0]['User']);
        self::assertArrayNotHasKey('password_token', $result[0]['User']);

        // Doesn't list users from other branches
        $result = $this->testAction('/users/range', [
            'data' => ['ids' => ['59a7011a05c677bda916612c', '59a7011a05c677bda512212a']],
            'method' => 'POST',
        ]);

        $result = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        self::assertCount(1, $result);

        // Doesn't authorize GUEST tokens
        $this->loginAsGuest('glofox', '49a7011a05c677b9a916612a');

        $result = $this->testAction('/users/range', [
            'data' => [
                'ids' => ['59a7011a05c677bda916612c'],
            ],
            'method' => 'POST',
        ]);

        $result = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        self::assertFalse($result['success']);
    }

    public function test_it_sets_joined_at_automatically(): void
    {
        Carbon::setTestNow('+1 year');
        $now = Carbon::now();
        $this->createFeatureFlagMock(false);
        $publisher = Mockery::mock(UsersPublisher::class);
        $publisher
            ->shouldReceive('sendNewMemberAddedEvent')
            ->withArgs(
                function (NewMemberAddedEventMeta $meta, NewMemberAddedEventPayload $payload) {
                    $data = $payload->jsonSerialize();

                    self::assertArrayNotHasKey('password', $data['user']);

                    self::assertSame('<EMAIL>', $data['user']['email']);
                    self::assertSame('UNKNOWN', $data['user']['source']);

                    self::assertInstanceOf(\MongoDate::class, $data['user']['created']);
                    self::assertInstanceOf(\MongoDate::class, $data['user']['modified']);
                    self::assertInstanceOf(\MongoDate::class, $data['user']['joined_at']);

                    return true;
                }
            );

        app()->instance(UsersPublisher::class, $publisher);

        $this->mockMemberAccessInfoEvent([], [], true);

        $this->createConsentFlaggerMock(false);

        $this->authenticateAsAdmin();

        $payload = $this->getMockUserData([
            'email' => '<EMAIL>',
            'login' => '<EMAIL>',
            'phone' => '07300000000',
            'password' => 's3crET$$$okm',
            'address' => [
                'street' => 'custom street name',
                'city' => '',
                'state' => 'custom state name',
                'country' => 'custom country name',
                'country_code' => 'GB',
                'postal_code' => 'postal code',
            ],
        ]);

        unset($payload['_id']);

        $response = $this->testAction('/users/save', [
            'data' => $payload,
            'method' => Request::METHOD_POST,
        ]);

        $response = json_decode($response, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        self::assertTrue($response['success'], $response['message'] ?? '');

        $user = $this->fetchUser($response['entity']['_id']);

        self::assertSame($now->format('Y-m-d H:i:s'), $user->joinedAt()->format('Y-m-d H:i:s'));

        app()->make(UsersRepository::class)->delete($user);
        app()->forgetInstance(ConsentFlagger::class);
    }

    public function test_it_cannot_overwrite_joined_at(): void
    {
        app()->bind(UsersPublisher::class, function () {
            $publisher = Mockery::mock(UsersPublisher::class);
            $publisher
                ->shouldReceive('sendMemberUpdatedEvent')
                ->withArgs(
                    function (MemberUpdatedEventMeta $meta, MemberUpdatedEventPayload $payload) {
                        $correlation = $meta->correlation();
                        return '5fb59526c635ea07321bf2c2' === $correlation['memberId'];
                    }
                );

            return $publisher;
        });

        $this->mockMemberAccessInfoEvent([], [
            'memberId' => '5fb59526c635ea07321bf2c2'
        ]);

        $this->createConsentFlaggerMock(false);

        $this->authenticateAsAdmin();

        $payload = $this->getMockUserData([
            '_id' => '5fb59526c635ea07321bf2c2',
            'email' => '<EMAIL>',
            'login' => '<EMAIL>',
            'phone' => '07300000000',
            'password' => 's3crET$$$okm',
            'address' => [
                'street' => 'custom street name',
                'city' => '',
                'state' => 'custom state name',
                'country' => 'custom country name',
                'country_code' => 'GB',
                'postal_code' => 'postal code',
            ],
            'joined_at' => '2022-05-01 15:00:00',
        ]);

        $response = $this->testAction('/users/save', [
            'data' => $payload,
            'method' => Request::METHOD_POST,
        ]);

        $response = json_decode($response, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        self::assertTrue($response['success'], $response['message'] ?? '');

        $user = $this->fetchUser($response['entity']['_id']);

        self::assertSame('2018-07-06 05:04:03', $user->joinedAt()->format('Y-m-d H:i:s'));

        app()->make(UsersRepository::class)->delete($user);
        app()->forgetInstance(ConsentFlagger::class);
    }

    public function test_it_blocks_member_creation_with_member_transfer_data_when_source_is_unauthorized_on_legacy_save_endpoint(
    ): void
    {
        $this->authenticateAsAdmin();

        $unauthorizedSources = Collection::make(Source::getEnumerators())->filter(
            fn (Source $source): bool => !$source->is(Source::MANUAL_MEMBER_TRANSFER)
        );

        $password = 's3crET$$$okm';

        $payload = $this->getMockUserData([
            'email' => '<EMAIL>',
            'login' => '<EMAIL>',
            'phone' => '07300000000',
            'password' => $password,
            'address' => [
                'street' => 'custom street name',
                'city' => '',
                'state' => 'custom state name',
                'country' => 'custom country name',
                'country_code' => 'GB',
                'postal_code' => 'postal code',
            ],
            'transferred_from' => [
                'source_branch_id' => '49a7011a05c677b9a916612b',
                'source_user_id' => '622a0b4ae39c9971f7a28f03',
            ],
        ]);

        foreach ($unauthorizedSources as $source) {
            $_SERVER['HTTP_X_GLOFOX_SOURCE'] = $source->getValue();

            $response = $this->testAction('/users/save', [
                'data' => $payload,
                'method' => Request::METHOD_POST,
            ]);

            $response = json_decode($response, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

            self::assertFalse($response['success'], $source->getValue());
            self::assertSame(
                UnauthorizedMemberTransferDataProvided::bySource($source)->getMessage(),
                $response['message'],
                $source->getValue()
            );
        }
    }

    public function test_it_allows_member_creation_with_member_transfer_data_only_when_source_is_authorized_on_legacy_save_endpoint(
    ): void
    {
        app()->bind(UsersPublisher::class, function () {
            $publisher = Mockery::mock(UsersPublisher::class);
            $publisher->shouldReceive('sendNewMemberAddedEvent');

            return $publisher;
        });

        $this->mockMemberAccessInfoEvent([], [], true);

        $this->createConsentFlaggerMock(false);

        $this->authenticateAsAdmin();

        $sourceBranchId = '49a7011a05c677b9a916612b';
        $sourceUserId = '622a0b4ae39c9971f7a28f03';

        $password = 's3crET$$$okm';

        $payload = $this->getMockUserData([
            'email' => '<EMAIL>',
            'login' => '<EMAIL>',
            'phone' => '07300000000',
            'password' => $password,
            'address' => [
                'street' => 'custom street name',
                'city' => '',
                'state' => 'custom state name',
                'country' => 'custom country name',
                'country_code' => 'GB',
                'postal_code' => 'postal code',
            ],
            'transferred_from' => [
                'source_branch_id' => $sourceBranchId,
                'source_user_id' => $sourceUserId,
            ],
        ]);

        unset($payload['_id']);

        $_SERVER['HTTP_X_GLOFOX_SOURCE'] = Source::MANUAL_MEMBER_TRANSFER;

        $response = $this->testAction('/users/save', [
            'data' => $payload,
            'method' => Request::METHOD_POST,
        ]);

        $response = json_decode($response, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        self::assertTrue($response['success'], $response['message'] ?? '');

        $user = $this->fetchUser($response['entity']['_id']);

        self::assertSame($sourceBranchId, $user->transferredFrom()->sourceBranchId());
        self::assertSame($sourceUserId, $user->transferredFrom()->sourceUserId());

        app()->make(UsersRepository::class)->delete($user);
        app()->forgetInstance(ConsentFlagger::class);
    }

    public function test_it_allows_members_to_be_created_with_address_details_using_legacy_endpoint(): void
    {
        app()->bind(UsersPublisher::class, function () {
            $publisher = Mockery::mock(UsersPublisher::class);
            $publisher->shouldReceive('sendNewMemberAddedEvent');

            return $publisher;
        });

        $this->mockMemberAccessInfoEvent([], [], true);

        $this->createConsentFlaggerMock(false);

        /** @var UsersRepository $usersRepository */
        $usersRepository = app()->make(UsersRepository::class);

        $this->authenticateAsAdmin();

        $password = 's3crET$$$okm';

        $payload = $this->getMockUserData();
        $payload['email'] = '<EMAIL>';
        $payload['login'] = '<EMAIL>';
        $payload['phone'] = time();
        $payload['password'] = $password;

        $addressData = [
            'street' => 'custom street name',
            'city' => '',
            'state' => 'custom state name',
            'country' => 'custom country name',
            'country_code' => 'GB',
            'postal_code' => 'postal code',
        ];

        $payload['address'] = $addressData;

        $response = $this->testAction('/users/save', [
            'data' => $payload,
            'method' => Request::METHOD_POST,
        ]);

        $response = json_decode($response, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        self::assertTrue($response->success);

        $user = $this->fetchUser($response->entity->_id);

        self::assertTrue($this->passwordHasher->verify($password, $user->passwordHash()));

        $expected = UserAddress::make($addressData);

        self::assertEquals(
            $expected->toArray(),
            $user->address()->toArray()
        );

        // Teardown
        $usersRepository->delete($user);
        app()->forgetInstance(ConsentFlagger::class);
    }

    /**
     * @dataProvider createMembersWithDefaultConsentAndFFDataProvider
     */
    public function testCreateMemberDefaultConsentForLegacySave(array $payload, bool $ffEnabled, string $consent): void
    {
        $this->authenticateAsAdmin();
        $this->createConsentFlaggerMock($ffEnabled);
        $this->mockNewMemberAddedEventPublisherExpecting([
            'user' => [
                'email' => $payload['email'],
            ],
        ]);

        $response = $this->testAction('/users/save', [
            'data' => $payload,
            'method' => Request::METHOD_POST,
        ]);

        $response = json_decode($response, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        $this->assertTrue($response->success);

        $userConsent = $response->entity->consent ?? null;
        $this->assertConsentValues($consent, $userConsent);

        app()->forgetInstance(ConsentFlagger::class);
        app()->forgetInstance(UsersPublisher::class);
    }
    
    public function testDispatcherMemberIncludesLeadsStatusMetadata(): void
    {
        $this->authenticateAsAdmin();
        $this->mockInstancesToCreateUser();
        $this->mockGoPaygService();

        $payload = $this->getMockUserData();
        $payload['email'] = '<EMAIL>';
        $payload['login'] = '<EMAIL>';
        $payload['phone'] = time();

        $this->createFeatureFlagMock(false);

        $response = $this->testAction('/2.0/members', [
            'data' => $payload,
            'method' => Request::METHOD_POST,
        ]);

        $response = json_decode($response, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        self::assertTrue($response['success']);

        $payload = $response['user'];
        $memberId = $payload['_id'];

        $member = $this->fetchUser($memberId);
        $this->assertNotNull(Status::LEAD, $member['leads']);
        $this->assertEquals(Status::LEAD, $member['leads'][LeadSchemaFields::STATUS]);
        $timestamp = $member['leads'][LeadSchemaFields::STATUS_MODIFIED];
        $this->mockInstancesToUpdateUser();
        $payload['lead_status'] = Status::COLD;
        $payload['type'] = UserType::MEMBER;// Update the status to cold

        $response = $this->testAction('/2.0/members/' . $memberId, [
            'data' => $payload,
            'method' => Request::METHOD_PUT,
        ]);
        $response = json_decode($response, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        self::assertTrue($response['success']);

        $payload = $response['user'];
        $memberId = $payload['_id'];

        $member = $this->fetchUser($memberId);
        $this->assertNotNull(Status::LEAD, $member['leads']);
        $this->assertEquals(Status::COLD, $member['leads'][LeadSchemaFields::STATUS]);
        $this->assertNotEquals($timestamp, $member['leads'][LeadSchemaFields::STATUS_MODIFIED]);

        app()->forgetInstance(UsersPublisher::class);
        app()->forgetInstance(FeatureFlagInterface::class);
        app()->forgetInstance(AccessesPublisher::class);
        app()->forgetInstance(ConsentFlagger::class);
    }

    private function mockInstancesToCreateUser() : void
    {
        $this->createConsentFlaggerMock(false);
        $publisher = Mockery::mock(UsersPublisher::class);
        $publisher->shouldReceive('sendNewMemberAddedEvent');
        app()->instance(UsersPublisher::class, $publisher);
        $this->mockMemberAccessInfoEvent([], [], true);
    }

    private function mockInstancesToUpdateUser() : void
    {
        $this->createConsentFlaggerMock(false);
        $publisher = Mockery::mock(UsersPublisher::class);
        $publisher->shouldReceive('sendMemberUpdatedEvent');
        app()->instance(UsersPublisher::class, $publisher);
        $this->mockMemberAccessInfoEvent([]);
    }

    /**
     * @throws UnsuccessfulOperation
     */
    public function testUsersSaveIncludesLeadsStatusMetadata(): void
    {
        $this->authenticateAsAdmin();
        $this->mockInstancesToCreateUser();

        $payload = $this->getMockUserData();
        $payload['email'] = '<EMAIL>';
        $payload['login'] = '<EMAIL>';
        $payload['phone'] = time();

        $response = $this->testAction('/users/save', [
            'data' => $payload,
            'method' => Request::METHOD_POST,
        ]);

        $response = json_decode($response, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        self::assertTrue($response['success']);

        $payload = $response['entity'];
        $userId = $payload['_id'];

        $user = $this->fetchUser($userId);
        $this->assertNotNull(Status::LEAD, $user['leads']);
        $this->assertEquals(Status::LEAD, $user['leads'][LeadSchemaFields::STATUS]);
        $timestamp = $user['leads'][LeadSchemaFields::STATUS_MODIFIED];
        $this->mockInstancesToUpdateUser();

        $payload['lead_status'] = Status::COLD; // Update the status to cold

        $response = $this->testAction('/users/save', [
            'data' => $payload,
            'method' => Request::METHOD_POST,
        ]);

        $response = json_decode($response, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        self::assertTrue($response['success']);

        $payload = $response['entity'];
        $userId = $payload['_id'];

        $user = $this->fetchUser($userId);
        $this->assertNotNull(Status::LEAD, $user['leads']);
        $this->assertEquals(Status::COLD, $user['leads'][LeadSchemaFields::STATUS]);
        $this->assertNotEquals($timestamp, $user['leads'][LeadSchemaFields::STATUS_MODIFIED]);

        app()->forgetInstance(UsersPublisher::class);
        app()->forgetInstance(AccessesPublisher::class);
        app()->forgetInstance(ConsentFlagger::class);
    }

    public function testLeadStatusChangeRegister(): void
    {
        $this->authenticateAsAdmin();
        $this->mockInstancesToCreateUser();

        $payload = $this->getMockUserData();
        $payload['email'] = '<EMAIL>';
        $payload['login'] = '<EMAIL>';
        $payload['phone'] = time();
        $this->createFeatureFlagMock(false);

        $response = $this->testAction('/2.0/register', [
            'data' => $payload,
            'method' => Request::METHOD_POST,
        ]);
        $response = json_decode($response, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        self::assertTrue($response['success']);

        $payload = $response['user'];
        $memberId = $payload['_id'];

        $member = $this->fetchUser($memberId);

        $this->assertNotNull(Status::LEAD, $member['leads']);
        $this->assertEquals(Status::LEAD, $member['leads'][LeadSchemaFields::STATUS]);
        $this->assertNotNull($member['leads'][LeadSchemaFields::STATUS_MODIFIED]);

        app()->forgetInstance(UsersPublisher::class);
        app()->forgetInstance(FeatureFlagInterface::class);
        app()->forgetInstance(AccessesPublisher::class);
        app()->forgetInstance(ConsentFlagger::class);
    }

    /**
     * @dataProvider createMembersWithDefaultConsentAndFFDataProvider
     */
    public function testCreateMemberDefaultConsentForRegister(array $payload, bool $ffEnabled, string $consent): void
    {
        $this->authenticateAsAdmin();
        $this->createFeatureFlagMock(false);
        $this->createConsentFlaggerMock($ffEnabled);
        $this->mockNewMemberAddedEventPublisherExpecting([
            'user' => [
                'email' => $payload['email'],
            ],
        ]);

        $response = $this->testAction('/2.0/register', [
            'data' => $payload,
            'method' => Request::METHOD_POST,
        ]);

        $response = json_decode($response, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        $this->assertTrue($response->success);

        $userConsent = $response->user->consent ?? null;
        $this->assertConsentValues($consent, $userConsent);

        app()->forgetInstance(ConsentFlagger::class);
        app()->forgetInstance(UsersPublisher::class);
        app()->forgetInstance(FeatureFlagInterface::class);
    }

    /**
     * @dataProvider createMembersWithDefaultConsentDataProvider
     */
    public function testCreateMemberDefaultConsentForDispatcher(array $payload, string $consent): void
    {
        $this->authenticateAsAdmin();
        $this->mockGoPaygService();
        $this->mockNewMemberAddedEventPublisherExpecting([
            'user' => [
                'email' => $payload['email'],
            ],
        ]);

        $response = $this->testAction('/2.0/members', [
            'data' => $payload,
            'method' => Request::METHOD_POST,
        ]);

        $response = json_decode($response, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        $this->assertTrue($response->success);

        $userConsent = $response->user->consent ?? null;
        $this->assertConsentValues($consent, $userConsent);

        app()->forgetInstance(UsersPublisher::class);
    }

    public function createMembersWithDefaultConsentAndFFDataProvider(): array
    {
        return [
            'When the branch has the consent ff on and consent on the payload ' .
            'then it creates the member with received consent values' =>
                [
                    'payload' => $this->createMembersWithDefaultConsentPayload(true),
                    'ffEnabled' => true,
                    'consent' => 'custom',
                ],
            'When the branch has the consent ff on and no consent on the payload ' .
            'then it creates the member with default consent values' =>
                [
                    'payload' => $this->createMembersWithDefaultConsentPayload(false),
                    'ffEnabled' => true,
                    'consent' => 'default',
                ],
            'When the branch has the consent ff off and no consent on the payload ' .
            'then it creates the member without consent values' =>
                [
                    'payload' => $this->createMembersWithDefaultConsentPayload(false),
                    'ffEnabled' => false,
                    'consent' => 'none',
                ],
        ];
    }

    public function createMembersWithDefaultConsentDataProvider(): array
    {
        return [
            'When there is consent on the payload ' .
            'then it creates the member with received consent values' =>
                [
                    'payload' => $this->createMembersWithDefaultConsentPayload(true),
                    'consent' => 'custom',
                ],
            'When there isnt consent on the payload ' .
            'then it creates the member with default consent values' =>
                [
                    'payload' => $this->createMembersWithDefaultConsentPayload(false),
                    'consent' => 'default',
                ],
        ];
    }

    private function createMembersWithDefaultConsentPayload(bool $consent): array
    {
        $email = uniqid('test_user_consent_', false) . '@email.com';
        $payload = $this->getMockUserData();
        $payload['email'] = $email;
        $payload['login'] = $email;
        $payload['phone'] = '543534534';
        if ($consent) {
            $payload['consent'] =
                [
                    'email' => [
                        'active' => true,
                    ],
                    'sms' => [
                        'active' => true,
                    ],
                ];
        }
        return $payload;
    }

    private function createConsentFlaggerMock(bool $ffEnabled): void
    {
        $flagger = Mockery::mock(ConsentFlagger::class);
        $flagger->shouldReceive('hasByBranchId')->andReturn($ffEnabled);
        app()->instance(ConsentFlagger::class, $flagger);
    }
    public function createFeatureFlagMock(bool $ffEnabled): void
    {
        $updateMarketingData = Mockery::mock(FeatureFlagInterface::class);
        $updateMarketingData
            ->shouldReceive('withFlag')
            ->andReturnSelf()
            ->getMock()
            ->shouldReceive('hasByBranchId')
            ->andReturn($ffEnabled);
        app()->instance(FeatureFlagInterface::class, $updateMarketingData);
    }



    private function assertConsentValues(string $consentType, $userConsent): void
    {

        switch ($consentType) {
            case 'custom':
                $this->assertTrue($userConsent->email->active);
                $this->assertTrue($userConsent->sms->active);
                $this->assertTrue($userConsent->push->active);
                break;
            case 'default':
                $this->assertFalse($userConsent->email->active);
                $this->assertFalse($userConsent->sms->active);
                $this->assertTrue($userConsent->push->active);
                break;
            default:
                $this->assertNull($userConsent);
                break;
        }
    }

    public function test_it_allows_members_to_be_updated_with_address_details_using_legacy_endpoint(): void
    {
        $canChangeSuperAdminTypeValidator = Mockery::mock(CanChangeSuperAdminTypeValidator::class);
        $canChangeSuperAdminTypeValidator
            ->shouldReceive('validate')
            ->once();
        app()->instance(CanChangeSuperAdminTypeValidator::class, $canChangeSuperAdminTypeValidator);
        $this->createConsentFlaggerMock(false);

        app()->bind(UsersPublisher::class, function () {
            $publisher = Mockery::mock(UsersPublisher::class);
            $publisher->shouldReceive('sendNewMemberAddedEvent');

            return $publisher;
        });

        $this->mockMemberAccessInfoEvent([], [], true);

        /** @var UsersRepository $usersRepository */
        $usersRepository = app()->make(UsersRepository::class);

        $this->authenticateAsAdmin();

        $payload = $this->getMockUserData();
        $payload['email'] = '<EMAIL>';
        $payload['login'] = '<EMAIL>';
        $payload['phone'] = time();

        $response = $this->testAction('/users/save', [
            'data' => $payload,
            'method' => Request::METHOD_POST,
        ]);

        $response = json_decode($response, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        self::assertTrue($response['success']);

        $payload = $response['entity'];
        $userId = $payload['_id'];

        $addressData = [
            'street' => 'custom street name',
            'city' => 'custom city name',
            'state' => 'custom state name',
            'country' => 'custom country name',
            'country_code' => 'GB',
            'postal_code' => 'postal code',
        ];

        $payload['address'] = $addressData;

        app()->bind(UsersPublisher::class, function () use ($userId) {
            $publisher = Mockery::mock(UsersPublisher::class);
            $publisher
                ->shouldReceive('sendMemberUpdatedEvent')
                ->withArgs(
                    function (MemberUpdatedEventMeta $meta) use ($userId) {
                        $correlation = $meta->correlation();

                        return $correlation['memberId'] === $userId;
                    }
                );

            return $publisher;
        });

        $this->mockMemberAccessInfoEvent([]);

        $response = $this->testAction('/users/save', [
            'data' => $payload,
            'method' => Request::METHOD_POST,
        ]);

        $response = json_decode($response, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        self::assertTrue($response->success);

        $user = $this->fetchUser($userId);

        $expected = UserAddress::make($addressData);

        self::assertEquals(
            $expected->toArray(),
            $user->address()->toArray()
        );

        // Teardown
        $usersRepository->delete($user);
        app()->forgetInstance(CanChangeSuperAdminTypeValidator::class);
        app()->forgetInstance(ConsentFlagger::class);
    }

    public function test_it_register_endpoint_fail_when_payg_membership_disabled(): void
    {
        $user = $this->fetchUser('66c8837fa1f2b3414415825b');
        $token = $this->loginAsUser($user);

        $_SERVER['HTTP_AUTHORIZATION'] = $token;
        $payload = [
            'first_name' => 'John',
            'last_name' => 'New',
            'phone' => '0850101010',
            'email' => '<EMAIL>',
            'password' => 's3crET$$$okm',
            'branch_id' => '66c6f0a307ae75cf210410b9',
            'birth' => '2018/02/28',
        ];
        $this->createFeatureFlagMock(false);
        $this->createConsentFlaggerMock(false);
        
        $result = $this->testAction('/2.0/register', ['data' => json_encode($payload), 'method' => 'post']);
        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        $this->assertFalse($result->success);
        $this->assertEquals('This branch does not allow pay as you go', $result->message);

        app()->forgetInstance(ConsentFlagger::class);
    }

    public function test_it_allows_payg_member_registration_for_gympass(): void
    {
        app()->bind(UsersPublisher::class, function () {
            $publisher = Mockery::mock(UsersPublisher::class);
            $publisher->shouldReceive('sendNewMemberAddedEvent');

            return $publisher;
        });

        $this->mockMemberAccessInfoEvent([], [], true);

        $user = $this->fetchUser('66c8837fa1f2b3414415825b');
        $token = $this->loginAsUser($user);

        $_SERVER['HTTP_AUTHORIZATION'] = $token;
        $payload = [
            'first_name' => 'John',
            'last_name' => 'New',
            'phone' => '0850101010',
            'email' => '<EMAIL>',
            'password' => 's3crET$$$okm',
            'branch_id' => '66c6f0a307ae75cf210410b9',
            'origin' => 'gympass',
            'gympassId' => '1234',
            'birth' => '2018/02/28',
        ];
        $this->createFeatureFlagMock(false);
        $this->createConsentFlaggerMock(false);
        $result = $this->testAction('/2.0/register', ['data' => json_encode($payload), 'method' => 'post']);
        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        $this->assertTrue($result->success);
        $this->assertEquals('1234', $result->user->metadata->gympass->id);
        app()->forgetInstance(ConsentFlagger::class);
    }


    public function test_it_allows_members_to_be_created_with_address_details_using_register_endpoint(): void
    {
        app()->bind(UsersPublisher::class, function () {
            $publisher = Mockery::mock(UsersPublisher::class);
            $publisher->shouldReceive('sendNewMemberAddedEvent');

            return $publisher;
        });

        $this->mockMemberAccessInfoEvent([], [], true);

        /** @var UsersRepository $usersRepository */
        $usersRepository = app()->make(UsersRepository::class);

        $this->authenticateAsAdmin();

        $address = [
            'street' => 'custom street name',
            'city' => 'custom city name',
            'state' => 'custom state name',
            'country' => 'custom country name',
            'country_code' => 'GB',
            'postal_code' => 'postal code',
        ];

        $password = 's3crET$$$okm';
        $payload = [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'phone' => '08501010101',
            'email' => '<EMAIL>',
            'password' => $password,
            'branch_id' => '49a7011a05c677b9a916612b',
            'address' => $address,
        ];
        $this->createFeatureFlagMock(false);
        $this->createConsentFlaggerMock(false);
        $result = $this->testAction('/2.0/register', [
            'data' => $payload,
            'method' => Request::METHOD_POST,
        ]);

        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        self::assertTrue($result->success);

        $user = $this->fetchUser($result->user->_id);

        $expected = UserAddress::make($address);
        self::assertEquals(
            $expected->toArray(),
            $user->address()->toArray()
        );

        self::assertTrue($this->passwordHasher->verify($password, $user->passwordHash()));

        // Teardown
        $usersRepository->delete($user);
        app()->forgetInstance(ConsentFlagger::class);
        app()->forgetInstance(FeatureFlagInterface::class);
    }

    public function test_it_blocks_member_creation_with_member_transfer_data_when_source_is_unauthorized_when_using_api2_endpoint(
    ): void
    {
        $this->authenticateAsAdmin();

        $unauthorizedSources = Collection::make(Source::getEnumerators())->filter(
            fn (Source $source): bool => !$source->is(Source::MANUAL_MEMBER_TRANSFER)
        );

        $payload = [
            'first_name' => 'John',
            'last_name' => 'Connor',
            'namespace' => 'glofox',
            'branch_id' => '49a7011a05c677b9a916612a',
            'strike' => -5,
            'type' => 'MEMBER',
            'login' => '<EMAIL>',
            'email' => '<EMAIL>',
            'password' => '123123123',
            'phone' => '123456789A',
            'membership' => ['type' => 'payg'],
            'address' => [
                'street' => 'custom street name',
                'city' => 'custom city name',
                'state' => 'custom state name',
                'country' => 'custom country name',
                'country_code' => 'GB',
                'postal_code' => 'postal code',
            ],
            'transferred_from' => [
                'source_branch_id' => '49a7011a05c677b9a916612b',
                'source_user_id' => '622a0b4ae39c9971f7a28f03',
            ],
        ];

        foreach ($unauthorizedSources as $source) {
            $_SERVER['HTTP_X_GLOFOX_SOURCE'] = $source->getValue();

            $response = $this->testAction('/2.0/members', [
                'data' => $payload,
                'method' => Request::METHOD_POST,
            ]);

            $response = json_decode($response, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

            self::assertFalse($response['success'], $source->getValue());
            self::assertSame(
                UnauthorizedMemberTransferDataProvided::bySource($source)->getMessage(),
                $response['message'],
                $source->getValue()
            );
        }
    }

    public function test_it_allows_member_creation_with_member_transfer_data_only_when_source_is_authorized_when_using_api2_endpoint(
    ): void
    {
        $sourceBranchId = '49a7011a05c677b9a916612b';
        $sourceUserId = '622a0b4ae39c9971f7a28f03';
        $this->createFeatureFlagMock(false);

        $publisher = Mockery::mock(UsersPublisher::class);
        $publisher
            ->shouldReceive('sendNewMemberAddedEvent')
            ->withArgs(
                function (NewMemberAddedEventMeta $meta, NewMemberAddedEventPayload $payload) use (
                    $sourceBranchId,
                    $sourceUserId
                ) {
                    $data = $payload->jsonSerialize();

                    self::assertArrayNotHasKey('password', $data['user']);

                    self::assertSame('<EMAIL>', $data['user']['email']);
                    self::assertSame('MANUAL_MEMBER_TRANSFER', $data['user']['source']);

                    self::assertSame($sourceBranchId, $data['user']['transferred_from']['source_branch_id']);
                    self::assertSame($sourceUserId, $data['user']['transferred_from']['source_user_id']);

                    self::assertInstanceOf(\MongoDate::class, $data['user']['created']);
                    self::assertInstanceOf(\MongoDate::class, $data['user']['modified']);
                    self::assertInstanceOf(\MongoDate::class, $data['user']['joined_at']);

                    return true;
                }
            );

        app()->instance(UsersPublisher::class, $publisher);

        $this->mockGoPaygService();
        $this->mockMemberAccessInfoEvent([], [], true);

        $this->authenticateAsAdmin();

        $payload = [
            'first_name' => 'John',
            'last_name' => 'Connor',
            'namespace' => 'glofox',
            'branch_id' => '49a7011a05c677b9a916612a',
            'strike' => -5,
            'type' => 'MEMBER',
            'login' => '<EMAIL>',
            'email' => '<EMAIL>',
            'password' => '123123123',
            'phone' => '123456789A',
            'membership' => ['type' => 'payg'],
            'address' => [
                'street' => 'custom street name',
                'city' => 'custom city name',
                'state' => 'custom state name',
                'country' => 'custom country name',
                'country_code' => 'GB',
                'postal_code' => 'postal code',
            ],
            'transferred_from' => [
                'source_branch_id' => $sourceBranchId,
                'source_user_id' => $sourceUserId,
            ],
        ];

        $_SERVER['HTTP_X_GLOFOX_SOURCE'] = Source::MANUAL_MEMBER_TRANSFER;

        $response = $this->testAction('/2.0/members', [
            'data' => $payload,
            'method' => Request::METHOD_POST,
        ]);

        $response = json_decode($response, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        self::assertTrue($response['success'], $response['message'] ?? '');

        $user = $this->fetchUser($response['user']['_id']);

        self::assertSame($sourceBranchId, $user->transferredFrom()->sourceBranchId());
        self::assertSame($sourceUserId, $user->transferredFrom()->sourceUserId());

        app()->make(UsersRepository::class)->delete($user);
    }

    public function test_it_allows_members_to_be_created_with_address_details_using_api2_endpoint(): void
    {
        $this->mockGoPaygService();
        $this->mockNewMemberAddedEventPublisherExpecting([
            'user' => [
                'email' => '<EMAIL>',
            ],
        ]);

        /** @var UsersRepository $usersRepository */
        $usersRepository = app()->make(UsersRepository::class);

        $this->authenticateAsAdmin();

        $address = [
            'street' => 'custom street name',
            'city' => 'custom city name',
            'state' => 'custom state name',
            'country' => 'custom country name',
            'country_code' => 'GB',
            'postal_code' => 'postal code',
        ];

        $payload = [
            'first_name' => 'John',
            'last_name' => 'Connor',
            'namespace' => 'glofox',
            'branch_id' => '49a7011a05c677b9a916612a',
            'strike' => -5,
            'type' => 'MEMBER',
            'login' => '<EMAIL>',
            'email' => '<EMAIL>',
            'password' => '123123123',
            'phone' => '123456789A',
            'membership' => ['type' => 'payg'],
            'address' => $address,
        ];

        $result = $this->testAction('/2.0/members', [
            'data' => $payload,
            'method' => Request::METHOD_POST,
        ]);

        $result = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        self::assertTrue($result['success'], $result['message'] ?? '');

        $user = $this->fetchUser($result['user']['_id']);

        $expected = UserAddress::make($address);
        self::assertEquals(
            $expected->toArray(),
            $user->address()->toArray()
        );

        // Teardown
        $usersRepository->delete($user);
    }

    public function testBuyingSubscriptionOnEndpointForServicePacksThrowsAnError(): void
    {
        app()->bind(UsersPublisher::class, function () {
            $publisher = Mockery::mock(UsersPublisher::class);
            $publisher->shouldReceive('sendNewMemberAddedEvent');

            return $publisher;
        });

        $this->mockMemberAccessInfoEvent([], [], true);

        $userHasMembershipValidator = \Mockery::mock(ValidateUserHasActiveNonPAYGMembership::class);
        $userHasMembershipValidator
            ->shouldReceive('execute')
            ->andReturn(false);
        app()->instance(ValidateUserHasActiveNonPAYGMembership::class, $userHasMembershipValidator);

        $this->authenticateAsAdmin();

        $startDate = Carbon::now()->modify('-2 days');

        $subscriptionPlanId = '5a2aad49e1608aa72b004443';
        $membershipId = '54107c1cd7b6ddc3a98b4543';
        $planCode = 'a1560347192843';
        $userId = '5a2aad49e1608aa72b004443';
        $branchId = '49a7011a05c677b9a916612a';

        // We're buying a subscription membership with a start date in the past
        $purchaseMembershipPayload = [
            'membership' => [
                '_id' => $membershipId,
                'branch_id' => $branchId,
                'active' => true,
                'code' => $planCode,
                'price' => 10,
                'type' => 'time',
                'duration_time_unit' => 'day',
                'duration_time_unit_count' => 1,
                'upfront_fee' => 0,
                'credits' => [],
                'starts_on' => 'PURCHASE_DATE',
                '_toggle' => true,
                'name' => '1380 (Plan 1)',
                'accepted_payment_methods' => [
                    [
                        'type_id' => 'CARD',
                        'active' => true,
                    ],
                ],
                'amount' => 10,
                '_subscription_plan_id' => true,
                'subscription_plan_id' => $subscriptionPlanId,
                'startDate' => $startDate->format('Y-m-d'),
                'hasTimeMembership' => false,
            ],
            'payment' => [
                'price' => 0,
                'method' => 'credit_card',
                'quantity' => 1,
                'forceOverbooking' => false,
            ],
        ];

        $url = sprintf('/users/buy_membership_admin/%s/%s/%s', $branchId, $userId, $startDate->format('Y-m-d'));

        $result = $this->testAction($url, [
            'data' => json_encode($purchaseMembershipPayload),
            'method' => Request::METHOD_POST,
        ]);

        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        self::assertFalse($result->success);
        self::assertEquals('Cannot purchase this membership type, only service packs are allowed', $result->message);
    }

    public function test_it_should_not_allow_to_purchase_a_membership_for_a_deactivated_user(): void
    {
        app()->bind(UsersPublisher::class, function () {
            $publisher = Mockery::mock(UsersPublisher::class);
            $publisher->shouldReceive('sendNewMemberAddedEvent');

            return $publisher;
        });

        $this->mockMemberAccessInfoEvent([], [], true);

        app()->bind(MembershipsEventPublisher::class, function () {
            $publisher = Mockery::mock(MembershipsEventPublisher::class);
            $publisher->shouldReceive('sendMembershipBoughtEvent');

            return $publisher;
        });

        $startDate = Carbon::today();

        $userId = '61c9af765978a5b354bfb0b6';
        $branchId = '49a7011a05c677b9a916612b';

        $this->loginByToken(
            $this->generateToken([
                '_id' => '59a3011a05c677bda916612a',
                'branch_id' => $branchId,
                'namespace' => 'glofox',
                'type' => UserType::SUPERADMIN,
            ])
        );

        $url = sprintf('/users/buy_membership_admin/%s/%s/%s', $branchId, $userId, $startDate->format('Y-m-d'));

        $result = $this->testAction($url, [
            'data' => [],
            'method' => Request::METHOD_POST,
        ]);

        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        $this->assertFalse($result->success);
        $this->assertEquals($result->message, 'You can not purchase a membership for a deactivated user');
    }

    public function test_allows_password_to_be_set_when_creating_staff(): void
    {
        $this->mockNewMemberAddedEventPublisherExpecting([
            'user' => [
                'email' => '<EMAIL>',
            ],
        ]);

        // We'll get the mocked payload to create a trainer
        $data = self::createTrainerPayload();
        $expectedPassword = 'this should be his password 2(A';

        // We'll simulate the wrong requests that the dashboard makes to the api.
        $data['password'] = $expectedPassword;

        // We'll login as an admin of the same branch of the trainer.
        $this->loginAsUser(
            $this->fetchUser('59a7011a05c677bda916612a')
        );

        $response = $this->testAction('/users/save', [
            'method' => 'POST',
            'data' => $data,
        ]);

        $response = json_decode($response, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        $this->assertArrayHasKey('entity', $response, $response['message'] ?? '');

        $trainerId = $response['entity']['_id'];
        $trainer = $this->fetchUser($trainerId);

        self::assertTrue($this->passwordHasher->verify($expectedPassword, $trainer->passwordHash()));
        self::assertEquals(200, $this->response->statusCode());
    }

    public function test_it_should_update_gympass_id_for_user(): void
    {
        $this->mockMemberUpdatedPublisherExpecting([], [
            'memberId' => '5adde464b5cf4ad32eb7597e',
        ]);

        $this->mockMemberAccessInfoEvent([], [
            'memberId' => '5adde464b5cf4ad32eb7597e',
        ]);

        $this->loginAsUser(
            $this->fetchUser('6506bcf0b511dd853f0e36b2')
        );

        $response = $this->testAction('/2.0/members/5adde464b5cf4ad32eb7597e', [
            'method' => 'PUT',
            'data' => [
                'first_name' => 'Tadeusz',
                'last_name' => 'Nalepa',
                'metadata' => [
                    'gympass' => [
                        'id' => '123',
                    ],
                ],
            ],
        ]);

        $response = json_decode($response, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        self::assertEquals(200, $this->response->statusCode());
        self::assertEquals('123', $response->user->metadata->gympass->id);
        self::assertEquals('randomId123', $response->user->metadata->classpass->_id);
        self::assertEquals('Tadeusz', $response->user->first_name);
        self::assertEquals('Nalepa', $response->user->last_name);
    }

    public function test_legacy_listview_doesnt_return_password_hash(): void
    {
        $user = $this->fetchUser('59a7011a05c677bda916612a');
        $this->loginAsUser($user);

        $result = $this->testAction('/users/listview/null/null/null/staff/1', [
            'method' => Request::METHOD_GET,
        ]);

        $result = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        self::assertArrayNotHasKey('password', $result[0]['User']);
    }

    /**
     * Authenticate as User, calls authenticate with password with new api.
     */
    public function test_allows_login_as_admin(): void
    {
        $user = $this->fetchUser('59a7011a05c677bda916612a');
        $token = $this->loginAsUser($user);

        self::$token = $token;

        self::assertNotNull($token);
    }

    public function test_allows_member_updates_without_birth_dates(): void
    {
        app()->bind(UsersPublisher::class, function () {
            $publisher = Mockery::mock(UsersPublisher::class);
            $publisher->shouldReceive('sendNewMemberAddedEvent');
            $publisher->shouldReceive('sendMemberUpdatedEvent');

            return $publisher;
        });

        $this->mockMemberAccessInfoEvent([], [], true);

        $user = $this->fetchUser('59a7011a05c677bda916612a');
        $token = $this->loginAsUser($user);

        $_SERVER['HTTP_AUTHORIZATION'] = $token;

        $user = [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'phone' => '0850101010',
            'email' => '<EMAIL>',
            'password' => 's3crET$$$okm',
            'branch_id' => '49a7011a05c677b9a916612b',
            'birth' => null,
        ];
        $this->createFeatureFlagMock(false);
        $this->createConsentFlaggerMock(false);
        $result = $this->testAction('/2.0/register', [
            'data' => $user,
            'method' => Request::METHOD_POST,
        ]);

        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        self::assertTrue($result->success);

        $userId = $result->user->_id;

        $user = [
            'first_name' => 'Jonas',
            'last_name' => 'Dou',
            'phone' => '0850101010',
            'email' => '<EMAIL>',
            'password' => 's3crET$$$okm',
            'branch_id' => '49a7011a05c677b9a916612b',
            'birth' => null,
        ];

        $this->mockMemberAccessInfoEvent([], [
            'memberId' => $userId,
        ]);

        $result = $this->testAction(sprintf('/2.0/members/%s', $userId), [
            'data' => $user,
            'method' => Request::METHOD_PUT,
        ]);

        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        self::assertTrue($result->success);
        self::assertNull($result->user->birth);
        self::assertEquals('Jonas', $result->user->first_name);
        app()->forgetInstance(ConsentFlagger::class);
        app()->forgetInstance(FeatureFlagInterface::class);
    }

    public function test_it_should_allow_assigning_a_barcode_to_user(): void
    {
        app()->bind(UsersPublisher::class, function () {
            $publisher = Mockery::mock(UsersPublisher::class);
            $publisher
                ->shouldReceive('sendMemberUpdatedEvent')
                ->withArgs(
                    function (MemberUpdatedEventMeta $meta) {
                        $correlation = $meta->correlation();

                        return '59a7011a05c677bda916612c' === $correlation['memberId'];
                    }
                );

            return $publisher;
        });

        $this->mockMemberAccessInfoEvent([
            'barcode' => '123456789',
        ], [
            'memberId' => '59a7011a05c677bda916612c',
        ]);

        $this->authenticateAsAdmin();

        $result = $this->testAction('/users/assignBarcode/59a7011a05c677bda916612c', [
            'data' => ['barcode' => '123456789'],
            'method' => Request::METHOD_POST,
        ]);

        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        $this->assertEquals('BARCODE_SUCCESSFULLY_ASSIGNED', $result->message_code);

        app()->forgetInstance(UsersPublisher::class);
        app()->forgetInstance(AccessesPublisher::class);
    }

    /**
     * Authenticate as User, as guest with invalid namespace.
     */
    public function testLoginInvalidCredentials(): void
    {
        $this->createHoneycombTracker();
        $user = ['namespace' => 'glofoxx', 'login' => 'guest', 'password' => 'guest'];
        $result = $this->testAction('/2.0/login', ['data' => $user, 'method' => 'post']);
        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        $this->assertFalse($result->success);
        $this->assertEquals('There is no active branch for the namespace', $result->message);
        app()->forgetInstance(HoneycombTracker::class);
    }

    public function test_login_uses_correct_use_case_and_valid_credentials_returns_json_response(): void
    {
        $this->createHoneycombTracker();
        $input = [
            'branch_id' => '49a7011a05c677b9a916612a',
            'login' => '<EMAIL>',
            'password' => '123456',
        ];
        $loginUseCase = Mockery::mock(LoginUser::class);
        $loginUseCase
            ->shouldReceive('execute')
            ->withArgs(function ($postedInput) use ($input) {
                self::assertEquals(
                    $postedInput,
                    $input
                );

                return true;
            })
            ->andReturn(new JsonResponse([]));
        app()->instance(LoginUser::class, $loginUseCase);
        $this->testAction('/2.0/login', ['data' => $input, 'method' => 'post']);

        app()->forgetInstance(LoginUser::class);
        app()->forgetInstance(HoneycombTracker::class);
    }

    /**
     * Authenticate as User, as guest with invalid namespace.
     */
    public function testLoginGuestCredentials(): void
    {
        $this->createHoneycombTracker();
        // Using namespace
        $user = ['namespace' => 'glofox', 'login' => 'guest', 'password' => 'guest'];
        $result = $this->testAction('/2.0/login', ['data' => $user, 'method' => 'post']);
        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        $this->assertTrue(UserType::GUEST === $result->user->type);

        // Using branch_id
        $user = ['branch_id' => $result->user->branch_id, 'login' => 'guest', 'password' => 'guest'];
        $result = $this->testAction(
            '/2.0/login',
            ['data' => $user, 'method' => 'post']
        );
        $result = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        $this->assertArrayNotHasKey('password', $result['user']);
        $this->assertTrue(UserType::GUEST === $result['user']['type']);
        app()->forgetInstance(HoneycombTracker::class);
    }

    /**
     * Authenticate as User, calls authenticate with password with new api.
     */
    public function testLoginNonExistentClient(): void
    {
        $this->createHoneycombTracker();
      
        $user = ['namespace' => 'glofox', 'login' => '<EMAIL>', 'password' => '123456'];
        $result = $this->testAction('/2.0/login', ['data' => $user, 'method' => 'post']);
        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        $this->assertFalse($result->success);
        $this->assertEquals('The email or password you entered was incorrect', $result->message);

        app()->forgetInstance(HoneycombTracker::class);
    }

    public function testReRegisterDataProvider(): array
    {
        return [
            'When we attempt to register an user with same email and the existing user does not have a password then an exception is thrown and reset password email is sent' => [
                'existingUserHasPassword' => false,
                'success' => false,
                'userCanReRegisterReturn' => false,
            ],
            'When we attempt to register an user with same email and the existing user has a password then an exception is thrown and reset password email is sent' => [
                'existingUserHasPassword' => true,
                'success' => false,
                'userCanReRegisterReturn' => false,
            ],
            'When we attempt to register an user does not have Credit or Debit Card attached Then user can Re-Register' => [
                'existingUserHasPassword' => true,
                'success' => true,
                'userCanReRegisterReturn' => true,
            ],
        ];
    }
    /**
     * @dataProvider testReRegisterDataProvider
     */
    public function testRegister(
        bool $existingUserHasPassword,
        bool $success,
        bool $userCanReRegisterReturn
    ): void {
        app()->bind(UsersPublisher::class, function () {
            $publisher = Mockery::mock(UsersPublisher::class);
            $publisher->shouldReceive('sendNewMemberAddedEvent');

            return $publisher;
        });

        $this->mockMemberAccessInfoEvent([], [], true);

        if (!$success){
            $resetPasswordEmail = Mockery::mock(ResetPassword::class);
            $resetPasswordEmail
                ->shouldReceive('execute')
                ->once();
            app()->instance(ResetPassword::class, $resetPasswordEmail);
        }

        $mockUserCanReRegister = Mockery::mock(UserCanReRegister::class);
        $mockUserCanReRegister->shouldReceive('execute')->andReturn($userCanReRegisterReturn);
        app()->instance(UserCanReRegister::class, $mockUserCanReRegister);

        $this->authenticateAsAdmin();

        $user = [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'phone' => '0850101010',
            'email' => '<EMAIL>',
            'branch_id' => '49a7011a05c677b9a916612b',
            'birth' => -29_635_199,
        ];

        if ($existingUserHasPassword) {
            $user['password'] = 'aPassword1!';
        } else {
            $user['no_password'] = 1;
        }
        $this->createFeatureFlagMock(false);
        $this->createConsentFlaggerMock(false);

        $result = $this->testAction('/2.0/register', ['data' => $user, 'method' => 'post']);
        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        $this->assertTrue($result->success);

        // duplicated details

        $result = $this->testAction('/2.0/register', ['data' => $user, 'method' => 'post']);
        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        $this->assertEquals($success, $result->success);

        if (!$success) {
            $this->assertEquals('The user already exists. Reset password has been sent', $result->message);
        }
        app()->forgetInstance(ResetPassword::class);
        app()->forgetInstance(ConsentFlagger::class);
        app()->forgetInstance(FeatureFlagInterface::class);
        app()->forgetInstance(UserCanReRegister::class);
    }

    public function test_register_with_proper_birth_date_format(): void
    {
        $this->mockNewMemberAddedEventPublisherExpecting([
            'user' => [
                'email' => '<EMAIL>',
            ],
        ]);

        $user = $this->fetchUser('59a7011a05c677bda916612a');
        $token = $this->loginAsUser($user);

        $_SERVER['HTTP_AUTHORIZATION'] = $token;
        $user = [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'phone' => '0850101010',
            'email' => '<EMAIL>',
            'password' => 's3crET$$$okm',
            'branch_id' => '49a7011a05c677b9a916612b',
            'birth' => '2018/02/28',
        ];
        $this->createFeatureFlagMock(false);
        $this->createConsentFlaggerMock(false);
        $result = $this->testAction('/2.0/register', ['data' => $user, 'method' => 'post']);
        $result = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        $this->assertTrue($result['success'], $result['message'] ?? '');
        app()->forgetInstance(ConsentFlagger::class);
        app()->forgetInstance(FeatureFlagInterface::class);
    }

    public function test_register_with_origin_and_gympass_id(): void
    {
        $this->mockNewMemberAddedEventPublisherExpecting([
            'user' => [
                'email' => '<EMAIL>',
            ],
        ]);

        $user = $this->fetchUser('59a7011a05c677bda916612a');
        $token = $this->loginAsUser($user);

        $_SERVER['HTTP_AUTHORIZATION'] = $token;
        $user = [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'phone' => '0850101010',
            'email' => '<EMAIL>',
            'password' => 's3crET$$$okm',
            'branch_id' => '49a7011a05c677b9a916612b',
            'origin' => 'gympass',
            'gympassId' => '123',
            'birth' => '2018/02/28',
        ];
        $this->createFeatureFlagMock(false);
        $this->createConsentFlaggerMock(false);
        $result = $this->testAction('/2.0/register', ['data' => $user, 'method' => 'post']);
        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        $this->assertTrue($result->success);
        $this->assertEquals('123', $result->user->metadata->gympass->id);
        app()->forgetInstance(ConsentFlagger::class);
        app()->forgetInstance(FeatureFlagInterface::class);
    }

    public function test_register_with_wrong_birth_date_format(): void
    {
        $this->markTestSkipped('Skipping this test until unify the way we format dates');
        $user = $this->fetchUser('59a7011a05c677bda916612a');
        $token = $this->loginAsUser($user);

        $_SERVER['HTTP_AUTHORIZATION'] = $token;
        $user = [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'phone' => '0850101010',
            'email' => '<EMAIL>',
            'password' => 's3crET$$$okm',
            'branch_id' => '49a7011a05c677b9a916612b',
            'birth' => '2019/03/32',
        ];
        $result = $this->testAction('/2.0/register', ['data' => $user, 'method' => 'post']);
        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        $this->assertFalse($result->success);
        $this->assertEquals('The birth is not a valid date.', $result->message);
    }

    public function test_register_with_future_birth_date(): void
    {
        $this->markTestSkipped('Skipping this test until unify the way we format dates');
        $user = $this->fetchUser('59a7011a05c677bda916612a');
        $token = $this->loginAsUser($user);

        $_SERVER['HTTP_AUTHORIZATION'] = $token;
        $user = [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'phone' => '0850101010',
            'email' => '<EMAIL>',
            'password' => 's3crET$$$okm',
            'branch_id' => '49a7011a05c677b9a916612b',
            'birth' => '2050/01/01',
        ];
        $result = $this->testAction('/2.0/register', ['data' => $user, 'method' => 'post']);
        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        $this->assertFalse($result->success);
        $this->assertTrue(false !== stripos($result->message, 'The birth must be a date before'));
    }

    public function test_edit_membership_with_invalid_start_date(): void
    {
        $user = $this->fetchUser('59a7011a05c677bda916612a');
        $token = $this->loginAsUser($user);

        $_SERVER['HTTP_AUTHORIZATION'] = $token;
        $user = $this->getSampleUserData();
        $user['membership']['start_date'] = '2018-08-111T00:00:00.000Z';
        $result = $this->testAction('/users/save', ['data' => $user, 'method' => 'post']);
        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        $this->assertFalse($result->success);
        $this->assertEquals($result->message, 'The membership.start date is not a valid date.');
    }

    public function test_edit_membership_with_empty_start_date(): void
    {
        $user = $this->fetchUser('59a7011a05c677bda916612a');
        $token = $this->loginAsUser($user);

        $_SERVER['HTTP_AUTHORIZATION'] = $token;
        $user = $this->getSampleUserData();
        $user['membership']['start_date'] = ' ';
        $result = $this->testAction('/users/save', ['data' => $user, 'method' => 'post']);
        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        $this->assertFalse($result->success);
        $this->assertEquals(
            $result->message,
            'The membership.start date field is required., The membership.expiry date must be a date after membership.start date.'
        );
    }

    public function test_edit_membership_with_invalid_expiry_date(): void
    {
        $user = $this->fetchUser('59a7011a05c677bda916612a');
        $token = $this->loginAsUser($user);

        $_SERVER['HTTP_AUTHORIZATION'] = $token;
        $user = $this->getSampleUserData();
        $user['membership']['expiry_date'] = '2018-08-111T00:00:00.000Z';
        $result = $this->testAction('/users/save', ['data' => $user, 'method' => 'post']);
        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        $this->assertFalse($result->success);
        $this->assertEquals(
            $result->message,
            'The membership.expiry date is not a valid date. The membership.expiry date must be a date after membership.start date.'
        );
    }

    public function test_edit_membership_with_expiry_date_befor_start_date(): void
    {
        $user = $this->fetchUser('59a7011a05c677bda916612a');
        $token = $this->loginAsUser($user);

        $_SERVER['HTTP_AUTHORIZATION'] = $token;
        $user = $this->getSampleUserData();
        $user['membership']['expiry_date'] = '2018-08-10T00:00:00.000Z';
        $result = $this->testAction('/users/save', ['data' => $user, 'method' => 'post']);
        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        $this->assertFalse($result->success);
        $this->assertEquals($result->message, 'The membership.expiry date must be a date after membership.start date.');
    }

    public function test_it_always_formats_the_birth_data_in_date_string_format(): void
    {
        $this->mockNewMemberAddedEventPublisherExpecting([
            'user' => [
                'last_name' => 'Random Joe',
            ],
        ]);

        $user = $this->fetchUser('59a7011a05c677bda916612a');
        $token = $this->loginAsUser($user);

        $_SERVER['HTTP_AUTHORIZATION'] = $token;
        $user = [
            'first_name' => 'John',
            'last_name' => 'Random Joe',
            'email' => sprintf('%<EMAIL>', Str::random()),
            'password' => 's3crET$$$okm',
            'branch_id' => '49a7011a05c677b9a916612b',
            'birth' => '1984-12-05 09:45:49',
        ];
        $this->createFeatureFlagMock(false);
        $this->createConsentFlaggerMock(false);
        $result = $this->testAction('/2.0/register', [
            'data' => $user,
            'method' => 'post',
        ]);

        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        $this->assertTrue($result->success);
        $this->assertEquals('1984-12-05', $result->user->birth);
        app()->forgetInstance(ConsentFlagger::class);
        app()->forgetInstance(FeatureFlagInterface::class);
    }

    public function test_child_registration_with_empty_email_and_guardian_waiver(): void
    {
        $this->mockNewMemberAddedEventPublisherExpecting([
            'user' => [
                'last_name' => 'Child',
            ],
        ]);

        $user = $this->fetchUser('59a7011a05c677bda916612a');
        $token = $this->loginAsUser($user);

        $_SERVER['HTTP_AUTHORIZATION'] = $token;
        $user = [
            'first_name' => 'John',
            'last_name' => 'Child',
            'no_password' => true,
            'parent_id' => '5c8b0615deb2eae76ec28673',
            'use_parent_email' => true,
            'branch_id' => '49a7011a05c677b9a916612b',
            'GUARDIAN_WAIVER' => true,
        ];
        $this->createFeatureFlagMock(false);
        $this->createConsentFlaggerMock(false);
        $result = $this->testAction('/2.0/register', [
            'data' => $user,
            'method' => 'post',
        ]);
        $result = json_decode($result, $assoc = true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        $this->assertTrue($result['success'], $result['message'] ?? '');
        $this->assertRegExp('/^.+\+child.+@.+$/', $result['user']['login']);
        $this->assertArrayNotHasKey('password', $result['user']);
        $this->assertArrayHasKey('GUARDIAN_WAIVER', $result['user']);
        $this->assertTrue($result['user']['GUARDIAN_WAIVER']);
        app()->forgetInstance(ConsentFlagger::class);
        app()->forgetInstance(FeatureFlagInterface::class);
    }

    public function test_image_url_field_is_empty_if_no_avatar_was_sent_on_registration(): void
    {
        $this->mockNewMemberAddedEventPublisherExpecting([
            'user' => [
                'last_name' => 'Random Joe',
            ],
        ]);

        $user = $this->fetchUser('59a7011a05c677bda916612a');
        $token = $this->loginAsUser($user);

        $_SERVER['HTTP_AUTHORIZATION'] = $token;
        $user = [
            'first_name' => 'John',
            'last_name' => 'Random Joe',
            'email' => sprintf('%<EMAIL>', Str::random()),
            'password' => 's3crET$$$okm',
            'branch_id' => '49a7011a05c677b9a916612b',
            'birth' => '1984-12-05',
        ];
        $this->createFeatureFlagMock(false);
        $this->createConsentFlaggerMock(false);
        $result = $this->testAction('/2.0/register', [
            'data' => $user,
            'method' => 'post',
        ]);
        $result = json_decode($result, $assoc = true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        $this->assertTrue($result['success']);
        $this->assertNull($result['user']['image_url']);
        app()->forgetInstance(ConsentFlagger::class);
        app()->forgetInstance(FeatureFlagInterface::class);
    }

    public function test_image_url_field_is_always_generated_using_origin_branch_id(): void
    {
        $user = $this->fetchUser('59a7011a05c677bda916612c');
        $this->loginAsUser($user);  // token is generated for branch 49a7011a05c677b9a916612a

        $result = $this->testAction('/2.0/members/5b19330ea0d888945a164346', [
            'method' => 'GET',
        ]);
        $result = json_decode($result, $assoc = true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        $expectedBranchId = '49a7011a05c677b9a916612b';

        // image_url is generated using the target's origin branch id, instead of the current token's branch id
        $this->assertEquals($expectedBranchId, $result['origin_branch_id']);
        $this->assertTextContains(
            sprintf(
                "https://cdn.glofox.com/test/glofox/branches/%s/users/5b19330ea0d888945a164346.png",
                $expectedBranchId
            ),
            $result['image_url']
        );
    }

    public function test_it_includes_parent_card_if_the_user_is_a_child_and_using_parent_card(): void
    {
        $child = $this->fetchUser('5c8b0642deb2eae76ec28675');
        $this->loginAsUser($child);

        $result = $this->testAction(
            "/2.0/members/{$child->id()}?include=card",
            ['method' => 'GET']
        );
        $cardData = json_decode($result, $assoc = true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR)['card'];

        /** @var CardsRepository $cardsRepository */
        $cardsRepository = app()->make(CardsRepository::class);
        $parentCard = $cardsRepository->getFirstCardByUserId($child->parentId());

        $this->assertEquals($parentCard->brand(), $cardData['brand']);
        $this->assertEquals($parentCard->lastFour(), $cardData['last4']);
        $this->assertEquals($parentCard->expiryYear(), $cardData['exp_year']);
        $this->assertEquals($parentCard->expiryMonth(), $cardData['exp_month']);
    }

    public function test_reset_password(): void
    {
        // invalid user
        $user['email'] = '<EMAIL>';
        $user['branch_id'] = '123';
        $result = $this->testAction('/2.0/reset', ['data' => $user, 'method' => 'post']);
        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        $this->assertTrue($result->success);
        $this->assertEquals('RESET_PASSWORD_ATTEMPT_SUCCESS', $result->message);
    }

    /*!
     * Validate save member
     * @return [type] [description]
     */
    public function testPostMember(): void
    {
        app()->bind(UsersPublisher::class, function () {
            $publisher = Mockery::mock(UsersPublisher::class);
            $publisher
                ->shouldReceive('sendNewMemberAddedEvent')
                ->withArgs(
                    function (NewMemberAddedEventMeta $meta) {
                        $correlation = $meta->correlation();

                        return '49a7011a05c677b9a916612a' === $correlation['branchId'];
                    }
                );

            return $publisher;
        });

        $this->mockGoPaygService();
        $this->mockMemberAccessInfoEvent([], [], true);

        $user = $this->fetchUser('59a7011a05c677bda916612a');
        $token = $this->loginAsUser($user);

        $_SERVER['HTTP_AUTHORIZATION'] = $token;

        $data = [
            'first_name' => 'John',
            'last_name' => 'Connor',
            'namespace' => 'glofox',
            'branch_id' => '49a7011a05c677b9a916612a',
            'strike' => -5,
            'type' => 'MEMBER',
            'login' => '<EMAIL>',
            'email' => '<EMAIL>',
            'password' => '123123123',
            'phone' => '123456789A',
            'membership' => ['type' => 'payg'],
        ];

        $result = $this->testAction('/2.0/members', ['data' => $data, 'method' => 'post']);
        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        //Store this id for future requests
        self::$refId = $result->user->_id;

        $this->assertNotNull($result->user->_id);
        $this->assertNotNull($result->user->image_url);
        $this->assertTrue(UserType::MEMBER == strtoupper($result->user->type));
        //Validate non negative strikes
        $this->assertTrue(is_int($result->user->strike));
        $this->assertTrue(0 == $result->user->strike);

        // duplicated user
        $result = $this->testAction('/2.0/members', ['data' => $data, 'method' => 'post']);
        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        $this->assertFalse($result->success);
        $this->assertEquals('LOGIN_ALREADY_IN_USE,EMAIL_ALREADY_IN_USE', $result->message);
    }

    /*!
     * Get all members
     * @return [type] [description]
     */
    public function testGetAllMembers(): void
    {
        $this->authenticateAsAdmin('675acdbe6e0c59e64521eeae');

        $result = $this->testAction('/2.0/members', ['method' => 'get']);
        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        $this->assertNotNull($result->data);
        $this->assertNotNull($result->data[0]->emergency_contact);
        $this->assertNotNull($result->data[0]->birth);
        $this->assertNotNull($result->data[0]->consent);
        $this->assertNotNull($result->data[0]->created);
        $this->assertNotNull($result->data[0]->emergency_contact);
        $this->assertNotNull($result->data[0]->modified);
        $this->assertNotNull($result->data[0]->lead_status);
        $this->assertNotNull($result->data[0]->source);


        $this->assertTrue(is_countable($result->data) && count($result->data) > 0);
    }

    /*!
     * Validate save member
     * @return [type] [description]
     */
    public function testPostStaff(): void
    {
        $this->mockGoPaygService();
        $user = $this->fetchUser('59a7011a05c677bda916612a');
        $token = $this->loginAsUser($user);

        $_SERVER['HTTP_AUTHORIZATION'] = $token;

        $data = [
            'first_name' => 'John',
            'last_name' => 'Connor',
            'namespace' => 'glofox',
            'branch_id' => '49a7011a05c677b9a916612a',
            'type' => 'ADMIN',
            'login' => '<EMAIL>',
            'email' => '<EMAIL>',
            'password' => '123123123',
            'phone' => '123456789',
        ];

        $result = $this->testAction('/2.0/staff', ['data' => $data, 'method' => 'post']);
        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        $this->assertNotNull($result->user->_id);
        $this->assertNotNull($result->user->image_url);
        $this->assertTrue(UserType::ADMIN == strtoupper($result->user->type));
    }

    public function testGetAllStaffAsAdmin(): void
    {
        $user = $this->fetchUser('59a7011a05c677bda916612a');
        $token = $this->loginAsUser($user);

        $_SERVER['HTTP_AUTHORIZATION'] = $token;

        $result = $this->testAction('/2.0/staff', ['method' => 'get']);
        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        $this->assertNotNull($result->data);
        $this->assertCount(15, $result->data);
        $this->assertCount(
            1,
            array_filter($result->data, fn ($user) => $user->type == 'superadmin')
        );
        $this->assertCount(
            1,
            array_filter($result->data, fn ($user) => $user->type == 'reception')
        );
        $this->assertCount(
            6,
            array_filter($result->data, fn ($user) => $user->type == 'admin')
        );
        $this->assertCount(
            7,
            array_filter($result->data, fn ($user) => $user->type == 'trainer')
        );
    }

    public function testGetAllStaffAsGuest(): void
    {
        $token = $this->loginAsGuest('glofox', '49a7011a05c677b9a916612a');
        $_SERVER['HTTP_AUTHORIZATION'] = $token;

        $result = $this->testAction('/2.0/staff', ['method' => 'get']);
        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        $this->assertNotNull($result->data);
        $this->assertCount(7, $result->data);
        $this->assertEquals('trainer', $result->data[0]->type);
    }

    /**
     * Test param filtering with the model fields.
     */
    public function testParamFiltering(): void
    {
        $user = $this->fetchUser('59a7011a05c677bda916612a');
        $token = $this->loginAsUser($user);

        $_SERVER['HTTP_AUTHORIZATION'] = $token;

        $result = $this->testAction('/2.0/staff?type=TRAINER&active=true&first_name=Trainer', ['method' => 'get']);
        $result = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        $this->assertNotEmpty($result);
        $this->assertEquals(3, is_countable($result['data']) ? count($result['data']) : 0);
    }

    /********************************************************** API 1.0 ********************************************************/

    public function testLeadStatusForNewUsersFromWebportalShouldbeMarkedAsLead(): void
    {
        $this->mockNewMemberAddedEventPublisherExpecting([
            'user' => [
                'email' => '<EMAIL>',
            ],
        ]);

        /** @var User $userModel */
        $userModel = ClassRegistry::init('User');

        $user = $this->fetchUser('59a7011a05c677bda916612a');
        $token = $this->loginAsUser($user);

        $_SERVER['HTTP_AUTHORIZATION'] = $token;

        $data = $this->getMockUserData();

        // User created from Web portal should be UNTOUCHED
        $_SERVER['HTTP_X_GLOFOX_SOURCE'] = Source::WEBPORTAL;

        $data = array_merge($data, [
            'login' => '<EMAIL>',
            'email' => '<EMAIL>',
            'phone' => '123456789',
        ]);
        $this->createFeatureFlagMock(false);
        $this->createConsentFlaggerMock(false);

        $result = $this->testAction('/2.0/register', ['data' => $data, 'method' => 'post']);
        $result = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        $this->assertArrayHasKey('user', $result, $result['message'] ?? '');
        $user = $userModel->findById($result['user']['_id']);

        $this->assertEquals(Status::LEAD, $user['User']['lead_status']);
        $this->assertEquals(Source::WEBPORTAL, $user['User']['source']);
        app()->forgetInstance(ConsentFlagger::class);
        app()->forgetInstance(FeatureFlagInterface::class);
    }

    public function testLeadStatusForNewUsersFromDashboardShouldbeWarm(): void
    {
        $this->mockNewMemberAddedEventPublisherExpecting([
            'user' => [
                'email' => '<EMAIL>',
            ],
        ]);

        /** @var User $userModel */
        $userModel = ClassRegistry::init('User');

        $user = $this->fetchUser('59a7011a05c677bda916612a');
        $token = $this->loginAsUser($user);

        $_SERVER['HTTP_AUTHORIZATION'] = $token;

        $data = $this->getMockUserData();

        // User created from Web portal should be WARM
        $_SERVER['HTTP_X_GLOFOX_SOURCE'] = Source::DASHBOARD;

        $data = array_merge($data, [
            'login' => '<EMAIL>',
            'email' => '<EMAIL>',
            'phone' => '123456789',
        ]);

        $this->createConsentFlaggerMock(false);
        $this->createFeatureFlagMock(false);
        $result = $this->testAction('/2.0/register', ['data' => $data, 'method' => 'post']);
        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        $user = $userModel->findById($result->user->_id);

        $this->assertEquals(Status::WARM, $user['User']['lead_status']);
        $this->assertEquals(Source::DASHBOARD, $user['User']['source']);
        app()->forgetInstance(ConsentFlagger::class);
        app()->forgetInstance(FeatureFlagInterface::class);
    }

    public function testLeadStatusForNewUsersFromKioskShouldbeMarkedAsLead(): void
    {
        $this->mockNewMemberAddedEventPublisherExpecting([
            'user' => [
                'email' => '<EMAIL>',
            ],
        ]);

        /** @var User $userModel */
        $userModel = ClassRegistry::init('User');

        $user = $this->fetchUser('59a7011a05c677bda916612a');
        $token = $this->loginAsUser($user);

        $_SERVER['HTTP_AUTHORIZATION'] = $token;

        $data = $this->getMockUserData();

        // User created from Web portal should be WARM
        $_SERVER['HTTP_X_GLOFOX_SOURCE'] = Source::KIOSK;

        $data = array_merge($data, [
            'login' => '<EMAIL>',
            'email' => '<EMAIL>',
            'phone' => '123456789',
        ]);

        $this->createConsentFlaggerMock(false);
        $this->createFeatureFlagMock(false);
        $result = $this->testAction('/2.0/register', ['data' => $data, 'method' => 'post']);
        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        $user = $userModel->findById($result->user->_id);

        $this->assertEquals(Status::LEAD, $user['User']['lead_status']);
        $this->assertEquals(Source::KIOSK, $user['User']['source']);
        app()->forgetInstance(ConsentFlagger::class);
        app()->forgetInstance(FeatureFlagInterface::class);
    }

    public function testLeadStatusForNewUsersFromMemberAppShouldBeMarkedAsLead(): void
    {
        $this->mockNewMemberAddedEventPublisherExpecting([
            'user' => [
                'email' => '<EMAIL>',
            ],
        ]);

        /** @var User $userModel */
        $userModel = ClassRegistry::init('User');

        $user = $this->fetchUser('59a7011a05c677bda916612a');
        $token = $this->loginAsUser($user);

        $_SERVER['HTTP_AUTHORIZATION'] = $token;

        $data = $this->getMockUserData();

        // User created from Member App should be UNTOUCHED
        $_SERVER['HTTP_X_GLOFOX_SOURCE'] = Source::MEMBER_APP;

        $data = array_merge($data, [
            'login' => '<EMAIL>',
            'email' => '<EMAIL>',
            'phone' => '123456789',
        ]);

        $this->createConsentFlaggerMock(false);
        $this->createFeatureFlagMock(false);
        $result = $this->testAction('/2.0/register', ['data' => $data, 'method' => 'post']);
        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        $user = $userModel->findById($result->user->_id);

        $this->assertEquals(Status::LEAD, $user['User']['lead_status']);
        $this->assertEquals(Source::MEMBER_APP, $user['User']['source']);
        app()->forgetInstance(ConsentFlagger::class);
        app()->forgetInstance(FeatureFlagInterface::class);
    }

    public function testLeadStatusForNewUsersFromAdminAppShouldBeWarm(): void
    {
        $this->mockNewMemberAddedEventPublisherExpecting([
            'user' => [
                'email' => '<EMAIL>',
            ],
        ]);

        /** @var User $userModel */
        $userModel = ClassRegistry::init('User');

        $user = $this->fetchUser('59a7011a05c677bda916612a');
        $token = $this->loginAsUser($user);

        $_SERVER['HTTP_AUTHORIZATION'] = $token;

        $data = $this->getMockUserData();

        // User created from Admin App should be WARM
        $_SERVER['HTTP_X_GLOFOX_SOURCE'] = Source::ADMIN_APP;

        $data = array_merge($data, [
            'login' => '<EMAIL>',
            'email' => '<EMAIL>',
            'phone' => '123456789',
        ]);

        $this->createConsentFlaggerMock(false);
        $this->createFeatureFlagMock(false);
        $result = $this->testAction('/2.0/register', ['data' => $data, 'method' => 'post']);
        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        $user = $userModel->findById($result->user->_id);

        $this->assertEquals(Status::WARM, $user['User']['lead_status']);
        $this->assertEquals(Source::ADMIN_APP, $user['User']['source']);
        app()->forgetInstance(ConsentFlagger::class);
        app()->forgetInstance(FeatureFlagInterface::class);
    }

    public function testLeadStatusForNewUsersFromLegacyAppsShouldBeMarkedAsLead(): void
    {
        $this->mockNewMemberAddedEventPublisherExpecting([
            'user' => [
                'email' => '<EMAIL>',
            ],
        ]);

        /** @var User $userModel */
        $userModel = ClassRegistry::init('User');

        $user = $this->fetchUser('59a7011a05c677bda916612a');
        $token = $this->loginAsUser($user);

        $_SERVER['HTTP_AUTHORIZATION'] = $token;

        $data = $this->getMockUserData();

        // User created from Legacy Apps should be UNTOUCHED
        $_SERVER['HTTP_X_GLOFOX_SOURCE'] = '';
        $_SERVER['HTTP_USER_AGENT'] = 'Fennec/7.7.3 (iPhone; iOS 11.2.6; Scale/2.00)';

        $data = array_merge($data, [
            'login' => '<EMAIL>',
            'email' => '<EMAIL>',
            'phone' => '123456789',
        ]);

        $this->createConsentFlaggerMock(false);
        $this->createFeatureFlagMock(false);
        $result = $this->testAction('/2.0/register', ['data' => $data, 'method' => 'post']);
        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        $user = $userModel->findById($result->user->_id);

        $this->assertEquals(Status::LEAD, $user['User']['lead_status']);
        $this->assertEquals(Source::LEGACY_MOBILE_APP, $user['User']['source']);
        app()->forgetInstance(ConsentFlagger::class);
        app()->forgetInstance(FeatureFlagInterface::class);
    }

    public function testLeadStatusForNewUsersFromUnknownSourcesShouldBeMarkedAsLead(): void
    {
        $this->mockNewMemberAddedEventPublisherExpecting([
            'user' => [
                'email' => '<EMAIL>',
            ],
        ]);

        /** @var User $userModel */
        $userModel = ClassRegistry::init('User');

        $user = $this->fetchUser('59a7011a05c677bda916612a');
        $token = $this->loginAsUser($user);

        $_SERVER['HTTP_AUTHORIZATION'] = $token;

        $data = $this->getMockUserData();

        // User created from Unknown should be UNTOUCHED
        $_SERVER['HTTP_X_GLOFOX_SOURCE'] = '';

        $data = array_merge($data, [
            'login' => '<EMAIL>',
            'email' => '<EMAIL>',
            'phone' => '123456789',
        ]);
        $this->createConsentFlaggerMock(false);
        $this->createFeatureFlagMock(false);
        $result = $this->testAction('/2.0/register', ['data' => $data, 'method' => 'post']);
        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        $user = $userModel->findById($result->user->_id);

        $this->assertEquals(Status::LEAD, $user['User']['lead_status']);
        $this->assertEquals(Source::UNKNOWN, $user['User']['source']);
        app()->forgetInstance(ConsentFlagger::class);
        app()->forgetInstance(FeatureFlagInterface::class);
    }

    public function testLeadStatusForNewUsersFromAnySourceShouldBeTheSameAsProvidedInTheRequest(): void
    {
        $this->mockNewMemberAddedEventPublisherExpecting([
            'user' => [
                'email' => '<EMAIL>',
            ],
        ]);

        /** @var User $userModel */
        $userModel = ClassRegistry::init('User');

        $user = $this->fetchUser('59a7011a05c677bda916612a');
        $token = $this->loginAsUser($user);

        $_SERVER['HTTP_AUTHORIZATION'] = $token;

        $data = $this->getMockUserData();

        // User created from Unknown should be UNTOUCHED
        $_SERVER['HTTP_X_GLOFOX_SOURCE'] = '';

        $data = array_merge($data, [
            'login' => '<EMAIL>',
            'email' => '<EMAIL>',
            'phone' => '123456789',
            'lead_status' => Status::MEMBER,
        ]);
        $this->createConsentFlaggerMock(false);
        $this->createFeatureFlagMock(false);
        $result = $this->testAction('/2.0/register', ['data' => $data, 'method' => 'post']);
        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        $user = $userModel->findById($result->user->_id);

        $this->assertEquals(Status::MEMBER, $user['User']['lead_status']);
        $this->assertEquals(Source::UNKNOWN, $user['User']['source']);
        app()->forgetInstance(ConsentFlagger::class);
        app()->forgetInstance(FeatureFlagInterface::class);
    }

    /**
     * @throws JsonException
     */
    public function testFindByIdCannotFetchUsersFromOtherBranch(): void
    {
        $this->createRoamingMemberRestrcitedProfileFlaggerMock();
        $this->createHoneycombTrackerMock();

        $this->authenticateAsAdmin();
        $userId = '5f2292d576f2490ee93de046';
        $userNotFoundError = 'User not found';

        $result = $this->testAction(
            sprintf(
                '/users/findById/%s/%s/%s',
                $userId,
                static::GLOFOX_NAMESPACE,
                static::GLOFOX_BRANCH_ID
            ),
            ['method' => 'GET']
        );

        $response = json_decode($result, true, 512, JSON_THROW_ON_ERROR);

        $this->assertArrayNotHasKey('User', $response);
        $this->assertArrayHasKey('success', $response);
        $this->assertFalse($response['success']);
        $this->assertEquals($userNotFoundError, $response['message']);
        $this->assertEquals($userNotFoundError, $response['message_code']);

        app()->forgetInstance(FeatureFlagInterface::class);
        app()->forgetInstance(HoneycombTracker::class);
    }

    public function test_findById_endpoint_returns_correct_information(): void
    {
        $this->createRoamingMemberRestrcitedProfileFlaggerMock();
        $this->createHoneycombTrackerMock();

        $this->authenticateAsAdmin();
        $userId = '59a7011a05c677bda916612a';
        $branchId = '49a7011a05c677b9a916612a';
        $namespace = 'glofox';

        $result = $this->testAction(
            sprintf('/users/findById/%s/%s/%s', $userId, $namespace, $branchId),
            ['method' => 'GET']
        );

        $response = json_decode($result, $assoc = true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        $user = $response['User'];

        $this->assertEquals($userId, $user['_id']);
        $this->assertEquals($branchId, $user['branch_id']);
        $this->assertEquals($namespace, $user['namespace']);
        $this->assertEquals('Admin', $user['first_name']);
        $this->assertEquals('Istrator', $user['last_name']);
        $this->assertEquals('Admin Istrator', $user['name']);
        $this->assertEquals('<EMAIL>', $user['email']);

        app()->forgetInstance(FeatureFlagInterface::class);
        app()->forgetInstance(HoneycombTracker::class);
    }

    /**
     * @dataProvider findByIdEndpointProvider
     */
    public function test_findById_endpoint_on_crossTenant_request(string $method): void
    {
        $this->createRoamingMemberRestrcitedProfileFlaggerMock();
        $this->createHoneycombTrackerMock();

        $this->authenticateAsAdmin();
        $userId = '59a7011a05c677bda916612a';
        $branchId = '49a7011a05c677b9a916612b';
        $namespace = 'glofox';

        $result = $this->testAction(
            sprintf('/users/findById/%s/%s/%s', $userId, $namespace, $branchId),
            ['method' => $method]
        );

        $decodResult = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        $expectedResponse = [
            'success' => false,
            'message' => self::UNAUTHORIZED_MESSAGE,
            'message_code' => self::UNAUTHORIZED_MESSAGE,
            "message_data" => [],
            "errors" => [self::UNAUTHORIZED_MESSAGE],
        ];
        self::assertEquals(200, $this->response->statusCode());
        self::assertEquals($expectedResponse, $decodResult);
    }

    public function findByIdEndpointProvider(): array
    {
        return [
            'POST request' => ['method' => 'POST'],
            'GET request' => ['method' => 'GET'],
        ];
    }

    public function test_findById_endpoint_returns_child_information(): void
    {
        $this->createRoamingMemberRestrcitedProfileFlaggerMock();
        $this->createHoneycombTrackerMock();

        $this->loginAsUser(
            $this->fetchUser('5c79e58d7fb4afd6e2c806f6') // ADMIN
        );

        $child = $this->fetchUser('5c8b0642deb2eae76ec28675');
        $parent = $child->parent();

        $result = $this->testAction(
            sprintf(
                '/users/findById/%s/%s/%s',
                $child->id(),
                $child->namespace(),
                $child->currentBranchId()
            ),
            ['method' => 'GET']
        );

        $response = json_decode($result, $assoc = true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        $this->assertEquals($parent->email(), $response['User']['email']);
        $this->assertEquals($parent->phone(), $response['User']['phone']);
        $this->assertTrue($response['User']['use_parent_email']);
        $this->assertTrue($response['User']['use_parent_phone']);

        app()->forgetInstance(FeatureFlagInterface::class);
        app()->forgetInstance(HoneycombTracker::class);
    }

    public function test_findById_endpoint_returns_child_information_with_correct_email_and_phone(): void
    {
        $this->createRoamingMemberRestrcitedProfileFlaggerMock();
        $this->createHoneycombTrackerMock();

        $this->loginAsUser(
            $this->fetchUser('5c79e58d7fb4afd6e2c806f6') // ADMIN
        );

        $child = $this->fetchUser('63a05e70bea26934651fe6c1');

        $result = $this->testAction(
            sprintf(
                '/users/findById/%s/%s/%s',
                $child->id(),
                $child->namespace(),
                $child->currentBranchId()
            ),
            ['method' => 'GET']
        );

        $response = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        $this->assertEquals($child->email(), $response['User']['email']);
        $this->assertEquals($child->phone(), $response['User']['phone']);
        $this->assertFalse($response['User']['use_parent_email']);
        $this->assertFalse($response['User']['use_parent_phone']);

        app()->forgetInstance(FeatureFlagInterface::class);
        app()->forgetInstance(HoneycombTracker::class);
    }

    public function test_members_endpoint_returns_child_information(): void
    {
        $child = $this->fetchUser('5c8b0642deb2eae76ec28675');
        $parent = $child->parent();

        $this->loginAsUser($child);

        $result = $this->testAction(
            sprintf(
                '/2.0/members/%s',
                $child->id()
            ),
            ['method' => 'GET']
        );

        $user = json_decode($result, $assoc = true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        $this->assertEquals($parent->email(), $user['email']);
        $this->assertEquals($parent->phone(), $user['phone']);
        $this->assertTrue($user['use_parent_email']);
        $this->assertTrue($user['use_parent_phone']);
    }

    public function test_members_all_endpoint_returns_child_information(): void
    {
        $child = $this->fetchUser('5c8b0642deb2eae76ec28675');
        $parent = $child->parent();

        $this->loginAsUser($child);

        $result = $this->testAction(
            '/2.0/members',
            [
                'method' => 'GET',
                'data' => [
                    'parent_id' => '5c8b0615deb2eae76ec28673',
                ],
            ]
        );

        $response = json_decode($result, $assoc = true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        foreach ($response['data'] as $user) {
            if ($user['_id'] === $child->id()) {
                $this->assertEquals($parent->email(), $user['email']);
                $this->assertEquals($parent->phone(), $user['phone']);
                $this->assertTrue($user['use_parent_email']);
                $this->assertTrue($user['use_parent_phone']);
                break;
            }
        }
    }

    public function test_findUserByBranchIdBasicInfo_returns_child_information(): void
    {
        $child = $this->fetchUser('5c8b0642deb2eae76ec28675');
        $parent = $child->parent();

        $admin = $this->fetchUser('5c79e58d7fb1afd6e2c806f6');
        $this->loginAsUser($admin);

        $result = $this->testAction(
            sprintf(
                '/users/findUserByBranchIdBasicInfo/%s/30/0',
                $child->currentBranchId()
            ),
            [
                'method' => 'GET',
                'data' => [
                    'fields' => [
                        'email',
                        'phone',
                        'parent_id',
                    ],
                ],
            ]
        );

        $response = json_decode($result, $assoc = true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        foreach ($response as $user) {
            if ($user['User']['_id'] === $child->id()) {
                $this->assertEquals($parent->email(), $user['User']['email']);
                $this->assertEquals($parent->phone(), $user['User']['phone']);
                $this->assertTrue($user['User']['use_parent_email']);
                $this->assertTrue($user['User']['use_parent_phone']);
                break;
            }
        }
    }

    public function test_it_saves_a_child_replacing_the_email_by_the_fake_one(): void
    {
        $child = $this->fetchUser('5c8b0642deb2eae76ec28675');
        $data = $child->toArray();

        $data['email'] = $child->parent()->email();
        $data['phone'] = $child->parent()->phone();

        $this->loginAsUser(
            $this->fetchUser('5c79e58d7fb4afd6e2c806f6') // ADMIN user
        );

        $this->testAction('/users/save', [
            'method' => 'POST',
            'data' => $data,
        ]);

        $this->assertEquals(200, $this->response->statusCode());
    }

    public function test_it_saves_a_child_on_2_0_members_endpoint(): void
    {
        app()->bind(UsersPublisher::class, function () {
            $publisher = Mockery::mock(UsersPublisher::class);
            $publisher
                ->shouldReceive('sendMemberUpdatedEvent')
                ->withArgs(
                    function (MemberUpdatedEventMeta $meta) {
                        $correlation = $meta->correlation();

                        return '5c8b0642deb2eae76ec28675' === $correlation['memberId'];
                    }
                );

            return $publisher;
        });

        $this->mockMemberAccessInfoEvent([], [
            'memberId' => '5c8b0642deb2eae76ec28675'
        ]);

        $child = $this->fetchUser('5c8b0642deb2eae76ec28675');
        $data = $child->forget(['password', 'avatar'])->toArray();

        $data['email'] = $child->parent()->email();
        $data['phone'] = $child->parent()->phone();
        unset($data['address']);

        $this->loginAsUser(
            $this->fetchUser('5c79e58d7fb4afd6e2c806f6') // ADMIN user
        );

        $result = $this->testAction("/2.0/members/{$child->id()}", [
            'method' => 'PUT',
            'data' => $data,
        ]);

        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        $this->assertEquals(200, $this->response->statusCode());
        $this->assertTrue($result->success);
    }

    public function test_can_save_member_with_same_phone_number_on_put_members_endpoint(): void
    {
        app()->bind(UsersPublisher::class, function () {
            $publisher = Mockery::mock(UsersPublisher::class);
            $publisher
                ->shouldReceive('sendMemberUpdatedEvent')
                ->withArgs(
                    function (MemberUpdatedEventMeta $meta) {
                        $correlation = $meta->correlation();

                        return 'a9a3321a05c677bda916611c' === $correlation['memberId'];
                    }
                );

            return $publisher;
        });

        $this->mockAcceptUserTermsService();
        $this->mockMemberAccessInfoEvent([], [
            'memberId' => 'a9a3321a05c677bda916611c'
        ]);

        $member1 = $this->fetchUser('59a3011a05c677bda916611c');
        $member2 = $this->fetchUser('a9a3321a05c677bda916611c');

        $this->loginAsUser(
            $this->fetchUser('6506bcf0b511dd853f0e36b1')
        );

        $member2->put('phone', $member1->phone());
        $member2->forget(['address']);

        $result = $this->testAction("/2.0/members/{$member2->id()}", [
            'method' => 'PUT',
            'data' => $member2->toArray(),
        ]);

        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        $this->assertTrue($result->success);
        $this->assertEquals(200, $this->response->statusCode());
    }

    public function test_it_does_not_override_emergency_contact_when_gymsales_updates_an_user_with_invalid_details(
    ): void
    {
        $publisher = Mockery::mock(UsersPublisher::class);
        $publisher
            ->shouldReceive('sendMemberUpdatedEvent')
            ->withArgs(
                function (MemberUpdatedEventMeta $meta) {
                    $correlation = $meta->correlation();
                    return '59a3011a05c677bda916632c' === $correlation['memberId'];
                }
            );

        app()->instance(UsersPublisher::class, $publisher);

        $this->mockMemberAccessInfoEvent([], [
            'memberId' => '59a3011a05c677bda916632c'
        ]);

        $member = $this->fetchUser('59a3011a05c677bda916632c');

        $integrator = \Glofox\Domain\Integrators\Models\Integrator::make([
            '_id' => '49b7012a05c600c9a512503d',
            'name' => 'GymSales',
        ]);

        $this->loginAsIntegrator($integrator, $member->branch());

        $result = $this->testAction(\sprintf('/2.0/members/%s', $member->id()), [
            'method' => 'PUT',
            'data' => [
                'first_name' => 'new-first-name',
                'emergency_contact' => 'none',
            ],
        ]);

        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        $this->assertTrue($result->success);
        $this->assertEquals(200, $this->response->statusCode());

        /** @var UsersRepository $usersRepository */
        $usersRepository = app()->make(UsersRepository::class);

        /** @var User $user */
        $user = $usersRepository->addCriteria(new Id($member->id()))->first();

        $this->assertEquals('New-first-name', $user->firstName());
        $this->assertEquals('<EMAIL>', $user->emergencyContact());

        app()->forgetInstance(UsersPublisher::class);
    }

    public function test_login_and_email_are_consistent_on_put_updates(): void
    {
        app()->bind(UsersPublisher::class, function () {
            $publisher = Mockery::mock(UsersPublisher::class);
            $publisher
                ->shouldReceive('sendMemberUpdatedEvent')
                ->withArgs(
                    function (MemberUpdatedEventMeta $meta) {
                        $correlation = $meta->correlation();

                        return 'a9a3321a05c677bda916611c' === $correlation['memberId'];
                    }
                );

            return $publisher;
        });

        $this->mockMemberAccessInfoEvent([], [
            'memberId' => 'a9a3321a05c677bda916611c'
        ]);

        $member = $this->fetchUser('a9a3321a05c677bda916611c');

        $this->loginAsUser(
            $this->fetchUser('6506bcf0b511dd853f0e36b1')
        );

        $newEmail = '<EMAIL>';

        $result = $this->testAction("/2.0/members/{$member->id()}", [
            'method' => 'PUT',
            'data' => [
                'email' => $newEmail,
            ],
        ]);

        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        $this->assertTrue($result->success);
        $this->assertEquals(200, $this->response->statusCode());

        $member = $this->fetchUser('a9a3321a05c677bda916611c');
        $this->assertEquals($newEmail, $member->email());
        $this->assertEquals($newEmail, $member->login());
    }

    public function test_can_save_member_with_same_phone_number_on_post_members_endpoint(): void
    {
        app()->bind(UsersPublisher::class, function () {
            $publisher = Mockery::mock(UsersPublisher::class);
            $publisher
                ->shouldReceive('sendNewMemberAddedEvent')
                ->withArgs(
                    function (NewMemberAddedEventMeta $meta) {
                        $correlation = $meta->correlation();

                        return '5c783d4cd510f9635ad4a6b3' === $correlation['branchId'];
                    }
                );

            return $publisher;
        });

        $this->mockGoPaygService();
        $this->mockMemberAccessInfoEvent([], [], true);

        $member1 = $this->fetchUser('59a3011a05c677bda916611c');
        $member2 = $this->fetchUser('a9a3321a05c677bda916611c');

        $this->loginAsUser(
            $this->fetchUser('5c79e58d7fb4afd6e2c806f6')
        );

        $member2->put('phone', $member1->phone());
        $member2->put('email', uniqid('', false) . '@example.com');
        $member2->forget(['_id', 'login', 'access_barcode']);

        $result = $this->testAction('/2.0/members', [
            'method' => 'POST',
            'data' => $member2->toArray(),
        ]);

        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        $this->assertTrue($result->success);
        $this->assertEquals(200, $this->response->statusCode());
    }

    public function test_can_create_member_with_same_phone_number_on_put_save_user_endpoint(): void
    {
        app()->bind(UsersPublisher::class, function () {
            $publisher = Mockery::mock(UsersPublisher::class);
            $publisher->shouldReceive('sendNewMemberAddedEvent');

            return $publisher;
        });

        $this->mockMemberAccessInfoEvent([], [], true);

        $this->createConsentFlaggerMock(false);

        $member1 = $this->fetchUser('59a3011a05c677bda916611c');
        $member2 = $this->fetchUser('a9a3321a05c677bda916611c');

        $this->loginAsUser(
            $this->fetchUser('5c79e58d7fb4afd6e2c806f6')
        );

        $member2->put('phone', $member1->phone());
        $member2->put('email', uniqid('', false) . '@example.com');
        $member2->forget(['_id', 'login', 'address', 'access_barcode']);

        $result = $this->testAction('/users/save', [
            'method' => 'POST',
            'data' => $member2->toArray(),
        ]);

        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        $this->assertTrue($result->success);
        $this->assertEquals(200, $this->response->statusCode());
        app()->forgetInstance(ConsentFlagger::class);
    }

    public function test_can_save_member_with_same_phone_number_on_put_save_user_endpoint(): void
    {
        $canChangeSuperAdminTypeValidator = Mockery::mock(CanChangeSuperAdminTypeValidator::class);
        $canChangeSuperAdminTypeValidator
            ->shouldReceive('validate')
            ->once();
        app()->instance(CanChangeSuperAdminTypeValidator::class, $canChangeSuperAdminTypeValidator);
        $this->createConsentFlaggerMock(false);
        app()->bind(UsersPublisher::class, function () {
            $publisher = Mockery::mock(UsersPublisher::class);
            $publisher
                ->shouldReceive('sendMemberUpdatedEvent')
                ->withArgs(
                    function (MemberUpdatedEventMeta $meta) {
                        $correlation = $meta->correlation();

                        return 'a9a3321a05c677bda916611c' === $correlation['memberId'];
                    }
                );

            return $publisher;
        });

        $member1 = $this->fetchUser('59a3011a05c677bda916611c');
        $member2 = $this->fetchUser('a9a3321a05c677bda916611c');

        $this->authenticateAsAdmin();

        $member2->put('phone', $member1->phone());
        $member2->forget(['login']);
        $member2->forget(['address']);
        $member2->forget(['access_barcode']);

        $this->mockMemberAccessInfoEvent([], [
            'memberId' => 'a9a3321a05c677bda916611c'
        ]);

        $result = $this->testAction('/users/save', [
            'method' => 'PUT',
            'data' => $member2->toArray(),
        ]);

        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        $this->assertTrue($result->success);
        $this->assertEquals(200, $this->response->statusCode());
        app()->forgetInstance(CanChangeSuperAdminTypeValidator::class);
        app()->forgetInstance(ConsentFlagger::class);
    }

    public function test_can_register_member_with_same_phone_number_on_register_endpoint(): void
    {
        app()->bind(UsersPublisher::class, function () {
            $publisher = Mockery::mock(UsersPublisher::class);
            $publisher->shouldReceive('sendNewMemberAddedEvent');

            return $publisher;
        });

        $this->mockMemberAccessInfoEvent([], [], true);

        $member1 = $this->fetchUser('59a3011a05c677bda916611c');
        $member2 = $this->fetchUser('a9a3321a05c677bda916611c');
        $this->loginAsUser($member2);
        $this->createFeatureFlagMock(false);
        $this->createConsentFlaggerMock(false);
        $member2->put('phone', $member1->phone());
        $member2->put('email', uniqid('', false) . '@example.com');
        $result = $this->testAction('/2.0/register', [
            'method' => 'POST',
            'data' => $member2
                ->only([
                    'first_name',
                    'last_name',
                    'phone',
                    'email',
                    'branch_id',
                    'password',
                ])
                ->toArray(),
        ]);

        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        $this->assertTrue($result->success);
        $this->assertEquals(200, $this->response->statusCode());
        app()->forgetInstance(ConsentFlagger::class);
        app()->forgetInstance(FeatureFlagInterface::class);
    }

    public function test_it_uploads_the_user_avatar_if_present_when_saving_a_member(): void
    {
        $user = $this->fetchUser('a9a2222a05c677bda916611c');

        $this->loginAsUser(
            $this->fetchUser('59a7011a05c677bda916612a')
        );
        $_FILES = ['UserImage' => []];

        $avatarUploader = Mockery::mock(AvatarUploader::class);
        $avatarUploader->shouldReceive('upload')->andReturn('my-new-avatar-url');
        $avatarUploader->shouldReceive('hasFile')->andReturn(true);

        app()->instance(AvatarUploader::class, $avatarUploader);

        $this->testAction("/2.0/members/{$user->id()}", [
            'method' => 'PUT',
        ]);

        $this->assertEquals(200, $this->response->statusCode());

        $user = $this->fetchUser('a9a2222a05c677bda916611c');
        $this->assertEquals('my-new-avatar-url', $user->avatar());

        app()->forgetInstance(AvatarUploader::class);
    }

    public function test_it_uploads_the_user_avatar_if_present_when_creating_a_member(): void
    {
        $user = $this->fetchUser('a9a2222a05c677bda916611c');
        $data = $user->forget('_id')->toArray();
        $data['email'] = uniqid('', false) . '@glofox.com';
        $data['access_barcode'] = '387593742974294924';

        $this->loginAsUser(
            $this->fetchUser('59a7011a05c677bda916612a')
        );

        $_FILES = ['UserImage' => []];

        $usersPublisher = Mockery::mock(UsersPublisher::class);
        $usersPublisher->shouldReceive('sendNewMemberAddedEvent');
        app()->instance(UsersPublisher::class, $usersPublisher);

        $this->mockGoPaygService();
        $this->mockMemberAccessInfoEvent([], [], true);

        $avatarUploader = Mockery::mock(AvatarUploader::class);
        $avatarUploader->shouldReceive('upload')->andReturn('my-new-avatar-url');
        $avatarUploader->shouldReceive('hasFile')->andReturn(true);
        app()->instance(AvatarUploader::class, $avatarUploader);

        $result = $this->testAction('/2.0/members', [
            'method' => 'POST',
            'data' => $data,
        ]);

        $result = json_decode($result, $assoc = true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        $this->assertEquals(200, $this->response->statusCode());

        $user = $this->fetchUser($result['user']['_id']);
        $this->assertEquals('my-new-avatar-url', $user->avatar());

        app()->forgetInstance(AvatarUploader::class);
        app()->forgetInstance(UsersPublisher::class);
    }

    public function updateMembersWithDuplicateBarcodeDataProvider(): array
    {
        return [
            'When we try to update members with duplicate barcode sending POST request, we should get response that user with the same barcode already exists.' => [
                'method' => 'POST',
                'success' => false,
                'expected' => 'ANOTHER_CLIENT_HAS_THIS_BARCODE',
                'url' => '/2.0/members'
            ],
            'When we try to update members with duplicate barcode sending PUT request, we should get response that user with the same barcode already exists.' => [
                'method' => 'PUT',
                'success' => false,
                'expected' => 'ANOTHER_CLIENT_HAS_THIS_BARCODE',
                'url' => '/2.0/members/a9a2222a05c677bda916611c'
            ],
        ];
    }

    /**
     * @dataProvider updateMembersWithDuplicateBarcodeDataProvider
     * @return void
     */
    public function test_it_does_not_update_or_create_members_with_duplicate_barcode(string $method, bool $success, string $expected, string $url): void
    {
        $user = $this->fetchUser('a9a2222a05c677bda916611c');
        $data = $user->forget('_id')->toArray();
        $data['email'] = uniqid('', false) . '@glofox.com';

        $this->loginAsUser(
            $this->fetchUser('59a7011a05c677bda916612a')
        );

        $usersPublisher = Mockery::mock(UsersPublisher::class);
        $usersPublisher->shouldReceive('sendNewMemberAddedEvent');
        app()->instance(UsersPublisher::class, $usersPublisher);

        $this->mockMemberAccessInfoEvent([], [], true);

        $result = $this->testAction($url, [
            'method' => $method,
            'data' => $data,
        ]);

        $result = json_decode($result, $assoc = true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        $this->assertEquals(200, $this->response->statusCode());
        $this->assertEquals($success, $result['success']);
        $this->assertEquals($expected, $result['message_code']);

        app()->forgetInstance(UsersPublisher::class);
    }

    public function test_it_uploads_the_user_avatar_in_base64_when_saving_a_member(): void
    {
        $imageModeratorMock = Mockery::mock(ImageModeratorClientInterface::class);
        $imageModeratorMock->shouldReceive('validate');

        app()->instance(ImageModeratorClientInterface::class, $imageModeratorMock);

        $this->loginAsUser(
            $this->fetchUser('59a7011a05c677bda916612a')
        );

        $user = $this->fetchUser('a9a2222a05c677bda916611c');

        $usersPublisher = Mockery::mock(UsersPublisher::class);
        $usersPublisher->shouldReceive('sendMemberUpdatedEvent');
        app()->instance(UsersPublisher::class, $usersPublisher);

        $cloudStorage = Mockery::mock(CloudStorageInterface::class);
        $cloudStorage->shouldReceive('put')->andReturn(true);
        $cloudStorage->shouldReceive('url')->andReturn('avatar-url');
        app()->instance(CloudStorageInterface::class, $cloudStorage);

        $this->testAction("/2.0/members/{$user->id()}", [
            'method' => 'PUT',
            'data' => [
                'avatar' => 'iVBORw0KGgoAAAANSUhEUgAAADIAAAAyBAMAAADsEZWCAAAAG1BMVEXMzMyWlpaqqqq3t7exsbGcnJy+vr6jo6PFxcUFpPI/AAAACXBIWXMAAA7EAAAOxAGVKw4bAAAAQUlEQVQ4jWNgGAWjgP6ASdncAEaiAhaGiACmFhCJLsMaIiDAEQEi0WXYEiMCOCJAJIY9KuYGTC0gknpuHwXDGwAA5fsIZw0iYWYAAAAASUVORK5CYII=',
            ],
        ]);

        $this->assertEquals(200, $this->response->statusCode());
        $user = $this->fetchUser($user->id());
        $this->assertEquals('avatar-url', $user->avatar());

        app()->forgetInstance(CloudStorageInterface::class);
        app()->forgetInstance(UsersPublisher::class);
    }

    public function test_membership_data_is_taken_out_of_trainers(): void
    {
        // We'll fetch a trainer from the fixtures.
        $trainer = $this->fetchUser('59a7011a05c677bda916612c');
        $data = $trainer->toArray();

        // We'll simulate the wrong requests that the dashboard makes to the api.
        $data['membership'] = [];

        // We'll login as an admin of the same branch of the trainer.
        $this->loginAsUser(
            $this->fetchUser('59a7011a05c677bda916612a')
        );

        $this->testAction('/users/save', [
            'method' => 'POST',
            'data' => $data,
        ]);

        $trainer = $this->fetchUser('59a7011a05c677bda916612c');

        self::assertEquals(200, $this->response->statusCode());
        self::assertNull($trainer->get('membership'));
    }

    public function test_doesnt_allow_password_change_when_updating_staff(): void
    {
        // We'll fetch a trainer from the fixtures.
        $trainer = $this->fetchUser('59a7011a05c677bda916612c');
        $data = $trainer->toArray();

        $passwordBeforeUpdate = $trainer->passwordHash();

        // We'll simulate the wrong requests that the dashboard makes to the api.
        $data['password'] = $this->passwordHasher->make('should not update');

        // We'll login as an admin of the same branch of the trainer.
        $this->loginAsUser(
            $this->fetchUser('59a7011a05c677bda916612a')
        );

        $this->testAction('/users/save', [
            'method' => 'POST',
            'data' => $data,
        ]);

        $trainer = $this->fetchUser('59a7011a05c677bda916612c');

        self::assertEquals(200, $this->response->statusCode());
        self::assertEquals($passwordBeforeUpdate, $trainer->passwordHash());
    }

    public function test_register_new_member_from_kiosk(): void
    {
        $this->mockNewMemberAddedEventPublisherExpecting([
            'user' => [
                'email' => '<EMAIL>',
            ],
        ]);

        $admin = $this->fetchUser('59a7011a05c677bda916612a');
        $this->loginAsUser($admin);

        $paygUser = User::make([
            '_id' => '5b19430ea0d988945a164336',
            'membership' => ['type' => Type::PAYG],
        ]);

        $goPaygService = Mockery::mock(GoPaygService::class);
        $goPaygService->shouldReceive('execute')->andReturn($paygUser);

        app()->instance(GoPaygService::class, $goPaygService);

        $payload = [
            'branch_id' => '49a7011a05c677b9a916612a',
            'active' => true,
            'birth' => '1985-01-07',
            'receive_marketing' => true,
            'first_name' => 'Test',
            'last_name' => 'Kiosk1',
            'email' => '<EMAIL>',
            'phone' => '9284398234',
            'gender' => ['label' => 'M', 'name' => 'MALE'],
            'emergency_contact' => '234234234234',
            'membership' => ['type' => Type::PAYG],
            'WAIVER' => true,
        ];

        $result = $this->testAction('/2.0/members', [
            'data' => $payload,
            'method' => 'post',
        ]);

        $result = json_decode($result, $assoc = true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        $this->assertTrue($result['success']);
        $this->assertEquals('<EMAIL>', $result['user']['email']);
        $this->assertEquals(Type::PAYG, $result['user']['membership']['type']);

        app()->forgetInstance(GoPaygService::class);
    }

    public function test_custom_charge(): void
    {
        $admin = $this->fetchUser('59a7011a05c677bda916612a');
        $this->loginAsUser($admin);

        $walletClient = Mockery::mock(WalletsClient::class);
        $walletClient
            ->shouldReceive('getWalletSettings')
            ->andReturn(SettingsResponse::fromArray(collect()));
        app()->instance(WalletsClient::class, $walletClient);

        $payload = [
            'amount' => 5,
            'payment_method' => 'cash',
            'description' => null,
        ];

        $result = $this->testAction('/users/customCharge/59a3011a05c677bda916611c', [
            'data' => json_encode($payload, JSON_THROW_ON_ERROR),
            'method' => 'post',
        ]);
        $result = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        self::assertTrue($result['success']);
        self::assertEmpty($result['payment_intents']);

        app()->forgetInstance(WalletsClient::class);
    }

    public function test_custom_charge_with_intent(): void
    {
        $admin = $this->fetchUser('59a7011a05c677bda916612a');
        $this->loginAsUser($admin);

        $transaction = new Transaction();
        $transaction
            ->setId('ch_123')
            ->setStatus('PENDING-INTENT')
            ->setAmount(5)
            ->setCurrency('CAD')
            ->setServiceProviderIntentId('pi_123')
            ->setInvoiceId('invoice-id-1');

        $invoice = (new InvoiceEntity())
            ->setId($transaction->invoiceId())
            ->setLatestTrxGroup(
                (new InvoiceEntityLatestTransactionGroup())
                    ->setTransactions([$transaction])
            );

        /** @var PaymentProviderContract $paymentProvider */
        $paymentProvider = Mockery::mock(PaymentProviderContract::class)
            ->allows([
                'invoices' => Mockery::mock(InvoiceHandlerContract::class)
                    ->allows(['create' => $invoice]),
                'paymentMethod' => \Glofox\Domain\PaymentMethods\Models\PaymentMethod::make([
                    '_id' => '5d68269a8d8540851fe60663',
                    'type_id' => \Glofox\Domain\PaymentMethods\Type::CARD,
                    'provider' => ['charge_percentage' => 0, 'fixed_charge' => 2, 'account_id' => '123'],
                ]),
                'paymentProvider' => new \Glofox\Domain\PaymentProviders\Models\PaymentProvider(),
            ]);

        /** @var PaymentsHandler $paymentHandler */
        $paymentHandler = Mockery::mock(PaymentsHandler::class)
            ->allows([
                'provider' => $paymentProvider,
                'providerByPaymentMethod' => $paymentProvider,
            ]);
        app()->instance(PaymentsHandler::class, $paymentHandler);

        $receiptHandler = Mockery::mock(SendReceiptHandler::class)
            ->shouldReceive('handle')
            ->getMock();
        app()->instance(SendReceiptHandler::class, $receiptHandler);

        $walletClient = Mockery::mock(WalletsClient::class);
        $walletClient
            ->shouldReceive('getWalletSettings')
            ->andReturn(SettingsResponse::fromArray(collect()));
        app()->instance(WalletsClient::class, $walletClient);

        $payload = [
            'amount' => 5,
            'payment_method' => 'card',
            'description' => null,
        ];

        $result = $this->testAction('/users/customCharge/a9a3321a05c699bda917733a', [
            'data' => json_encode($payload, JSON_THROW_ON_ERROR),
            'method' => 'post',
        ]);
        $result = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        self::assertTrue($result['success']);
        self::assertEquals(['pi_123'], $result['payment_intents']);

        app()->forgetInstance(SendReceiptHandler::class);
        app()->forgetInstance(WalletsClient::class);
    }

    public function test_not_breaking_custom_charge_flow_by_adding_send_email_based_on_balance_service_using_wallet(
    ): void
    {
        $walletClient = Mockery::mock(WalletsClient::class);
        $walletClient
            ->shouldReceive('getWalletSettings')
            ->andReturn(SettingsResponse::fromArray(collect()))
            ->shouldReceive('getUserBalance')
            ->andReturn(
                BalanceResponse::fromArray(
                    collect([
                        'balance' => 20,
                    ])
                )
            );

        $sendEmailBasedOnBalanceService = Mockery::mock(SendEmailBasedOnBalanceService::class);
        $sendEmailBasedOnBalanceService
            ->shouldReceive('execute')
            ->once();

        app()->instance(WalletsClient::class, $walletClient);
        app()->instance(SendEmailBasedOnBalanceService::class, $sendEmailBasedOnBalanceService);

        $admin = $this->fetchUser('59a7011a05c677bda916612a');
        $this->loginAsUser($admin);

        $payload = [
            'amount' => 5,
            'payment_method' => 'wallet',
            'description' => null,
        ];

        $this->testAction('/users/customCharge/5ae1a75a8d7de985e383494d', [
            'data' => json_encode($payload, JSON_THROW_ON_ERROR),
            'method' => 'post',
        ]);

        app()->forgetInstance(WalletsClient::class);
        app()->forgetInstance(SendEmailBasedOnBalanceService::class);
    }

    public function test_not_breaking_custom_charge_flow_by_adding_send_email_based_on_balance_service_not_using_wallet(
    ): void
    {
        $walletClient = Mockery::mock(WalletsClient::class);
        $walletClient
            ->shouldReceive('getWalletSettings')
            ->andReturn(SettingsResponse::fromArray(collect()))
            ->shouldReceive('getUserBalance')
            ->andReturn(
                BalanceResponse::fromArray(
                    collect([
                        'balance' => 20,
                    ])
                )
            );

        $sendEmailBasedOnBalanceService = Mockery::mock(SendEmailBasedOnBalanceService::class);
        $sendEmailBasedOnBalanceService
            ->shouldNotReceive('execute');

        app()->instance(WalletsClient::class, $walletClient);
        app()->instance(SendEmailBasedOnBalanceService::class, $sendEmailBasedOnBalanceService);

        $admin = $this->fetchUser('59a7011a05c677bda916612a');
        $this->loginAsUser($admin);

        $payload = [
            'amount' => 5,
            'payment_method' => 'cash',
            'description' => null,
        ];

        $this->testAction('/users/customCharge/5ae1a75a8d7de985e383494d', [
            'data' => $payload,
            'method' => 'post',
        ]);

        app()->forgetInstance(WalletsClient::class);
        app()->forgetInstance(SendEmailBasedOnBalanceService::class);
    }

    public function test_it_should_pause_using_ical_extension(): void
    {
        $tester = $this;

        $this->authenticateAsAdmin();

        $auth = new Auth();

        $auth->paymentMethods[Glofox\Domain\PaymentMethods\Type::CARD] = Mockery::mock(PaymentProviderContract::class)
            ->shouldReceive('paymentMethod')
            ->andReturn(
                Glofox\Domain\PaymentMethods\Models\PaymentMethod::make([
                    'provider' => [],
                ])
            )
            ->shouldReceive('paymentProvider')
            ->andReturn(Glofox\Domain\PaymentProviders\Models\PaymentProvider::make([]))
            ->getMock();

        $mockedSubComponentPauseService = Mockery::mock(SubscriptionComponentPauseService::class)
            ->shouldReceive('execute')
            ->andReturnUsing(function (
                User $user,
                PaymentProviderContract $paymentHandler,
                Carbon $resumeDate,
                ?Carbon $endDate
            ) use ($tester) {
                $tester->assertEquals(
                    Carbon::now()->addWeek()->startOfDay()->getTimestamp(),
                    $user->membership()->startDate()->getTimestamp()
                );
                $tester->assertEquals(
                    Carbon::now()->addWeeks(2)->startOfDay()->getTimestamp(),
                    $user->membership()->expiryDate()->getTimestamp()
                );

                $tester->assertEquals(
                    Carbon::now()->addWeeks(2)->startOfDay()->getTimestamp(),
                    $resumeDate->getTimestamp()
                );

                $tester->assertNull($endDate);
            })
            ->getMock();

        app()->instance(Auth::class, $auth);
        app()->instance(SubscriptionComponentPauseService::class, $mockedSubComponentPauseService);

        $result = $this->testAction(
            sprintf(
                '/users/pause_subscription_admin/5b19430ea0d987945a16432f/%s',
                Carbon::now()->addWeeks(2)->toDateString()
            ),
            [
                'method' => 'post',
            ]
        );

        $result = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        $this->assertTrue($result['success']);
        $this->assertEquals('YOU_HAVE_SUCCESSFULLY_PAUSED_THE_SUBSCRIPTION', $result['message_code']);

        app()->forgetInstance(Auth::class);
    }

    public function test_assign_barcode_should_fail_because_logged_user_is_unauthorized(): void
    {
        $userId = '5b19430ea0d988945a164336';
        $notStaffUser = $this->fetchUser($userId);
        $this->loginAsUser($notStaffUser);

        $url = sprintf('/users/assignBarcode/%s', $userId);
        $payload = ['barcode' => '123456789'];
        $method = Request::METHOD_POST;

        $response = $this->testAction($url, ['data' => $payload, 'method' => $method]);
        $response = json_decode($response, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        $this->assertFalse($response['success']);
        $this->assertEquals('UNAUTHORIZED_FOR_THIS_ACTION', $response['message_code']);
    }

    public function test_assign_barcode_should_fail_because_barcode_is_not_passed(): void
    {
        $this->authenticateAsAdmin();
        $userId = '5b19430ea0d988945a164336';

        $url = sprintf('/users/assignBarcode/%s', $userId);
        $payload = [];
        $method = Request::METHOD_POST;

        $response = $this->testAction($url, ['data' => $payload, 'method' => $method]);
        $response = json_decode($response, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        $this->assertFalse($response['success']);
        $this->assertEquals('BARCODE_IS_REQUIRED', $response['message_code']);
    }

    public function test_assign_barcode_should_fail_because_barcode_is_empty(): void
    {
        $this->authenticateAsAdmin();
        $userId = '5b19430ea0d988945a164336';

        $url = sprintf('/users/assignBarcode/%s', $userId);
        $payload = ['barcode' => ''];
        $method = Request::METHOD_POST;

        $response = $this->testAction($url, ['data' => $payload, 'method' => $method]);
        $response = json_decode($response, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        $this->assertFalse($response['success']);
        $this->assertEquals('BARCODE_IS_REQUIRED', $response['message_code']);
    }

    public function test_assign_barcode_should_fail_because_user_does_not_exist(): void
    {
        $admin = $this->authenticateAsAdmin();
        $userId = '5b19430ea0d988945a164336';

        $usersRepository = Mockery::mock(UsersRepository::class);
        $usersRepository
            ->shouldReceive('withFetchType')
            ->andReturnSelf()
            ->getMock()
            ->shouldReceive('skipCallbacks')
            ->andReturnSelf()
            ->getMock()
            ->shouldReceive('find')
            ->andReturnSelf()
            ->getMock()
            ->shouldReceive('isActive')
            ->andReturnTrue()
            ->getMock()
            ->shouldReceive('email')
            ->andReturnTrue()
            ->getMock()
            ->shouldReceive('firstOrFail')
            ->andReturn(
                User::make([
                    'active' => true,
                    'email' => $admin->email(),
                    '_id' => '59a7011a05c677bda916612a',
                    'namespace' => 'glofox',
                    'branch_id' => '49a7011a05c677b9a916612a',
                    'first_name' => 'Admin',
                    'last_name' => 'Istrator',
                    'type' => 'ADMIN',
                    'isSuperAdmin' => true,
                ])
            )
            ->getMock()
            ->shouldReceive('addCriteria')
            ->andReturnSelf()
            ->getMock()
            ->shouldReceive('first')
            ->andReturn(User::make([]))
            ->getMock()
            ->shouldReceive('findById')
            ->with($userId)
            ->andReturn(null)
            ->getMock()
            ->shouldReceive('getChangePassword');

        app()->instance(UsersRepository::class, $usersRepository);

        $url = sprintf('/users/assignBarcode/%s', $userId);
        $payload = ['barcode' => '123456789'];
        $method = Request::METHOD_POST;

        $response = $this->testAction($url, ['data' => $payload, 'method' => $method]);
        $response = json_decode($response, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        $this->assertFalse($response['success']);
        $this->assertEquals('MEMBER_DOES_NOT_EXIST', $response['message_code']);

        app()->forgetInstance(UsersRepository::class);
    }

    public function test_assign_barcode_should_fail_because_users_repository_fail_saving_field(): void
    {
        $admin = $this->authenticateAsAdmin();
        $userId = '5b19430ea0d988945a164336';

        $userToUpdateBarcode = User::make([
            '_id' => $userId,
            'namespace' => 'glofox',
        ]);

        $usersRepository = Mockery::mock(UsersRepository::class);
        $usersRepository
            ->shouldReceive('withFetchType')
            ->andReturnSelf()
            ->getMock()
            ->shouldReceive('skipCallbacks')
            ->andReturnSelf()
            ->getMock()
            ->shouldReceive('find')
            ->andReturnSelf()
            ->getMock()
            ->shouldReceive('isActive')
            ->andReturnTrue()
            ->getMock()
            ->shouldReceive('email')
            ->andReturnTrue()
            ->getMock()
            ->shouldReceive('firstOrFail')
            ->andReturn(
                User::make([
                    'email' => $admin->email(),
                    'active' => true,
                    '_id' => '59a7011a05c677bda916612a',
                    'namespace' => 'glofox',
                    'branch_id' => '49a7011a05c677b9a916612a',
                    'first_name' => 'Admin',
                    'last_name' => 'Istrator',
                    'type' => 'ADMIN',
                    'isSuperAdmin' => true,
                ])
            )
            ->getMock()
            ->shouldReceive('findById')
            ->with($userId)
            ->andReturn(['User' => $userToUpdateBarcode->toArray()])
            ->getMock()
            ->shouldReceive('addCriteria')
            ->andReturnSelf()
            ->getMock()
            ->shouldReceive('isAccessBarcodeDuplicate')
            ->with('123456789', 'glofox', $userId)
            ->andReturn(null)
            ->getMock()
            ->shouldReceive('assignBarcode')
            ->with(\Mockery::type(User::class), '123456789')
            ->andReturn(0)
            ->getMock()
            ->shouldReceive('getChangePassword')
            ->shouldReceive('saveField')
            ->andReturn(false);

        app()->instance(UsersRepository::class, $usersRepository);

        $url = sprintf('/users/assignBarcode/%s', $userId);
        $payload = ['barcode' => '123456789'];
        $method = Request::METHOD_POST;

        $response = $this->testAction($url, ['data' => $payload, 'method' => $method]);
        $response = json_decode($response, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        $this->assertFalse($response['success']);
        $this->assertEquals('ERROR_ASSIGNING_THE_BARCODE', $response['message_code']);

        app()->forgetInstance(UsersRepository::class);
    }

    public function test_assign_barcode_should_successfully_assign_the_new_barcode(): void
    {
        $admin = $this->authenticateAsAdmin();
        $userId = '5b19430ea0d988945a164336';

        $userToUpdateBarcode = User::make([
            '_id' => $userId,
            'namespace' => 'glofox',
        ]);

        app()->bind(UsersPublisher::class, function () {
            $publisher = Mockery::mock(UsersPublisher::class);
            $publisher
                ->shouldReceive('sendMemberUpdatedEvent')
                ->withArgs(
                    function (MemberUpdatedEventMeta $meta) {
                        $correlation = $meta->correlation();

                        return '5b19430ea0d988945a164336' === $correlation['memberId'];
                    }
                );

            return $publisher;
        });

        $this->mockMemberAccessInfoEvent([
            'barcode' => '123456789'
        ], [
            'memberId' => '5b19430ea0d988945a164336'
        ]);

        $usersRepository = Mockery::mock(UsersRepository::class);
        $usersRepository
            ->shouldReceive('withFetchType')
            ->andReturnSelf()
            ->getMock()
            ->shouldReceive('skipCallbacks')
            ->andReturnSelf()
            ->getMock()
            ->shouldReceive('find')
            ->andReturnSelf()
            ->getMock()
            ->shouldReceive('email')
            ->andReturn($admin->email())
            ->getMock()
            ->shouldReceive('isActive')
            ->andReturnTrue()
            ->getMock()
            ->shouldReceive('firstOrFail')
            ->andReturn(
                User::make([
                    'active' => true,
                    'email' => $admin->email(),
                    '_id' => '59a7011a05c677bda916612a',
                    'namespace' => 'glofox',
                    'branch_id' => '49a7011a05c677b9a916612a',
                    'first_name' => 'Admin',
                    'last_name' => 'Istrator',
                    'type' => 'ADMIN',
                    'isSuperAdmin' => true,
                ])
            )
            ->getMock()
            ->shouldReceive('findById')
            ->with($userId)
            ->andReturn(['User' => $userToUpdateBarcode->toArray()])
            ->getMock()
            ->shouldReceive('addCriteria')
            ->andReturnSelf()
            ->getMock()
            ->shouldReceive('isAccessBarcodeDuplicate')
            ->with('123456789', 'glofox', $userId)
            ->andReturn(null)
            ->getMock()
            ->shouldReceive('assignBarcode')
            ->with(\Mockery::type(User::class), '123456789')
            ->andReturn(1)
            ->getMock()
            ->shouldReceive('getChangePassword')
            ->shouldReceive('saveField')
            ->andReturn(true);

        app()->instance(UsersRepository::class, $usersRepository);

        $url = sprintf('/users/assignBarcode/%s', $userId);
        $payload = ['barcode' => '123456789'];
        $method = Request::METHOD_POST;

        $response = $this->testAction($url, ['data' => $payload, 'method' => $method]);
        $response = json_decode($response, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        $this->assertTrue($response['success']);
        $this->assertEquals('BARCODE_SUCCESSFULLY_ASSIGNED', $response['message_code']);

        app()->forgetInstance(UsersRepository::class);
        app()->forgetInstance(UsersPublisher::class);
        app()->forgetInstance(AccessesPublisher::class);
    }

    public function test_assign_barcode_should_successfully_assign_the_new_barcode_and_only_update_barcode_field(): void
    {
        $admin = $this->authenticateAsAdmin();
        $userId = '5b19430ea0d988945a164336';

        app()->bind(UsersPublisher::class, function () {
            $publisher = Mockery::mock(UsersPublisher::class);
            $publisher
                ->shouldReceive('sendMemberUpdatedEvent')
                ->withArgs(
                    function (MemberUpdatedEventMeta $meta) {
                        $correlation = $meta->correlation();

                        return '5b19430ea0d988945a164336' === $correlation['memberId'];
                    }
                );

            return $publisher;
        });

        $this->mockMemberAccessInfoEvent([
            'barcode' => '123456789'
        ], [
            'memberId' => $userId
        ]);

        $url = sprintf('/users/assignBarcode/%s', $userId);
        $payload = [
            'barcode' => '123456789',
            'email' => '<EMAIL>',
        ];
        $method = Request::METHOD_POST;

        $response = $this->testAction($url, ['data' => $payload, 'method' => $method]);
        $response = json_decode($response, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        $this->assertTrue($response['success']);
        $this->assertEquals('BARCODE_SUCCESSFULLY_ASSIGNED', $response['message_code']);

        /** @var UsersRepository $usersRepository */
        $usersRepository = app()->make(UsersRepository::class);

        /** @var User $user */
        $user = $usersRepository->addCriteria(new Id($userId))->first();

        $this->assertNotEquals('<EMAIL>', $user->email());

        app()->forgetInstance(UsersPublisher::class);
        app()->forgetInstance(AccessesPublisher::class);
    }

    public function test_it_should_prevent_revert_to_payg_outside_of_the_home_branch(): void
    {
        $this->authenticateAsAdmin();

        $userId = '5b19330ea0d888945a164346';
        $branchId = '49a7011a05c677b9a916612a';

        $url = sprintf('/users/revert_to_payg/%s/%s/%s', $branchId, $userId, 'true');

        $result = $this->testAction($url, [
            'method' => Request::METHOD_POST,
        ]);

        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        $this->assertFalse($result->success);
        $this->assertEquals(
            $result->message,
            'Unauthorized'
        );
    }

    public function test_it_should_prevent_cancel_subscription_outside_of_the_home_branch(): void
    {
        $this->authenticateAsAdmin();

        $userId = '5b19330ea0d888945a164346';

        $url = sprintf('/users/cancel_subscription_admin/%s', $userId);

        $result = $this->testAction($url, [
            'method' => Request::METHOD_POST,
        ]);

        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        $this->assertFalse($result->success);
        $this->assertEquals(
            $result->message,
            'Unauthorized'
        );
    }

    public function test_when_attempt_to_cancel_a_num_classes_membership_and_user_membership_id_does_not_exist_it_does_not_sync_cancellation_but_it_requests_to_sync_current_active_membership(
    ): void
    {
        $this->authenticateAsAdmin();

        $this->mockAbTestValidator(true);

        $revertToPaygAggregatedValidationService = Mockery::mock(RevertToPaygAggregatedValidationService::class);
        $revertToPaygAggregatedValidationService
            ->shouldReceive('validate')
            ->andReturnTrue()
            ->once()
            ->getMock();

        $branchId = '49a7011a05c677b9a916612a';

        $legacySyncService = Mockery::mock(LegacySyncService::class);
        $legacySyncService
            ->shouldNotReceive('execute')
            ->getMock();

        $requestMembershipSyncService = Mockery::mock(RequestMembershipSyncService::class);
        $requestMembershipSyncService
            ->shouldReceive('execute')
            ->andReturnSelf()
            ->once()
            ->getMock();

        app()->instance(RevertToPaygAggregatedValidationService::class, $revertToPaygAggregatedValidationService);
        app()->instance(LegacySyncService::class, $legacySyncService);
        app()->instance(RequestMembershipSyncService::class, $requestMembershipSyncService);

        $userId = '5b19430ea0d977945a164331';

        $url = sprintf('/users/revert_to_payg/%s/%s/%s', $branchId, $userId, 'true');
        $result = $this->testAction($url, [
            'method' => Request::METHOD_POST,
        ]);

        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        self::assertTrue($result->success);

        app()->forgetInstance(RevertToPaygAggregatedValidationService::class);
        app()->forgetInstance(LegacySyncService::class);
        app()->forgetInstance(RequestMembershipSyncService::class);
    }

    public function test_when_attempt_to_cancel_a_time_membership_and_user_membership_id_does_not_exist_it_neither_sync_cancellation_nor_requests_to_sync_current_active_membership(
    ): void
    {
        $this->authenticateAsAdmin();

        $this->mockAbTestValidator(true);

        $revertToPaygAggregatedValidationService = Mockery::mock(RevertToPaygAggregatedValidationService::class);
        $revertToPaygAggregatedValidationService
            ->shouldReceive('validate')
            ->andReturnTrue()
            ->once()
            ->getMock();

        $branchId = '49a7011a05c677b9a916612a';

        $legacySyncService = Mockery::mock(LegacySyncService::class);
        $legacySyncService
            ->shouldNotReceive('execute')
            ->getMock();

        $requestMembershipSyncService = Mockery::mock(RequestMembershipSyncService::class);
        $requestMembershipSyncService
            ->shouldNotReceive('execute')
            ->getMock();

        app()->instance(RevertToPaygAggregatedValidationService::class, $revertToPaygAggregatedValidationService);
        app()->instance(LegacySyncService::class, $legacySyncService);
        app()->instance(RequestMembershipSyncService::class, $requestMembershipSyncService);

        $userId = '5b19430ea0d977945a164332';

        $url = sprintf('/users/revert_to_payg/%s/%s/%s', $branchId, $userId, 'true');
        $result = $this->testAction($url, [
            'method' => Request::METHOD_POST,
        ]);

        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        self::assertTrue($result->success);

        app()->forgetInstance(RevertToPaygAggregatedValidationService::class);
        app()->forgetInstance(LegacySyncService::class);
        app()->forgetInstance(RequestMembershipSyncService::class);
    }

    public function test_when_attempt_to_cancel_a_time_classes_membership_and_user_membership_id_does_not_exist_it_neither_syncs_cancellation_nor_requests_to_sync_current_active_membership(
    ): void
    {
        $this->authenticateAsAdmin();

        $this->mockAbTestValidator(true);

        $revertToPaygAggregatedValidationService = Mockery::mock(RevertToPaygAggregatedValidationService::class);
        $revertToPaygAggregatedValidationService
            ->shouldReceive('validate')
            ->andReturnTrue()
            ->once()
            ->getMock();

        $branchId = '49a7011a05c677b9a916612a';

        $legacySyncService = Mockery::mock(LegacySyncService::class);
        $legacySyncService
            ->shouldNotReceive('execute')
            ->getMock();

        $requestMembershipSyncService = Mockery::mock(RequestMembershipSyncService::class);
        $requestMembershipSyncService
            ->shouldNotReceive('execute')
            ->getMock();

        app()->instance(RevertToPaygAggregatedValidationService::class, $revertToPaygAggregatedValidationService);
        app()->instance(LegacySyncService::class, $legacySyncService);
        app()->instance(RequestMembershipSyncService::class, $requestMembershipSyncService);

        $userId = '5b19430ea0d977945a164333';

        $url = sprintf('/users/revert_to_payg/%s/%s/%s', $branchId, $userId, 'true');
        $result = $this->testAction($url, [
            'method' => Request::METHOD_POST,
        ]);

        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        self::assertTrue($result->success);

        app()->forgetInstance(RevertToPaygAggregatedValidationService::class);
        app()->forgetInstance(LegacySyncService::class);
        app()->forgetInstance(RequestMembershipSyncService::class);
    }

    public function test_when_attempt_to_cancel_a_membership_and_user_membership_id_exists_it_syncs_cancellation_but_it_does_not_request_to_sync_current_active_membership(
    ): void
    {
        $this->authenticateAsAdmin();

        $this->mockAbTestValidator(true);

        $revertToPaygAggregatedValidationService = Mockery::mock(RevertToPaygAggregatedValidationService::class);
        $revertToPaygAggregatedValidationService
            ->shouldReceive('validate')
            ->andReturnTrue()
            ->once()
            ->getMock();

        $branchId = '49a7011a05c677b9a916612a';

        $legacySyncService = Mockery::mock(LegacySyncService::class);
        $legacySyncService
            ->shouldReceive('execute')
            ->andReturnSelf()
            ->once()
            ->getMock();

        $requestMembershipSyncService = Mockery::mock(RequestMembershipSyncService::class);
        $requestMembershipSyncService
            ->shouldNotReceive('execute')
            ->getMock();

        app()->instance(RevertToPaygAggregatedValidationService::class, $revertToPaygAggregatedValidationService);
        app()->instance(LegacySyncService::class, $legacySyncService);
        app()->instance(RequestMembershipSyncService::class, $requestMembershipSyncService);

        $userId = '5b19430ea0d977945a164334';

        $url = sprintf('/users/revert_to_payg/%s/%s/%s', $branchId, $userId, 'true');
        $result = $this->testAction($url, [
            'method' => Request::METHOD_POST,
        ]);

        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        self::assertTrue($result->success);

        app()->forgetInstance(RevertToPaygAggregatedValidationService::class);
        app()->forgetInstance(LegacySyncService::class);
        app()->forgetInstance(RequestMembershipSyncService::class);
    }

    public function test_when_memberships_service_fails_cancelling_the_user_keeps_previous_membership(): void
    {
        $this->authenticateAsAdmin();

        $this->mockAbTestValidator(true);

        $revertToPaygAggregatedValidationService = Mockery::mock(RevertToPaygAggregatedValidationService::class);
        $revertToPaygAggregatedValidationService
            ->shouldReceive('validate')
            ->andReturnTrue()
            ->once()
            ->getMock();

        $branchId = '49a7011a05c677b9a916612a';

        $legacySyncService = Mockery::mock(LegacySyncService::class);
        $legacySyncService
            ->shouldReceive('execute')
            ->andReturnUsing(function () {
                throw new UnsuccessfulOperation();
            })
            ->once()
            ->getMock();

        $requestMembershipSyncService = Mockery::mock(RequestMembershipSyncService::class);
        $requestMembershipSyncService
            ->shouldNotReceive('execute')
            ->getMock();

        app()->instance(RevertToPaygAggregatedValidationService::class, $revertToPaygAggregatedValidationService);
        app()->instance(LegacySyncService::class, $legacySyncService);
        app()->instance(RequestMembershipSyncService::class, $requestMembershipSyncService);

        $userId = '5b19430ea0d977945a164334';

        $url = sprintf('/users/revert_to_payg/%s/%s/%s', $branchId, $userId, 'true');
        $result = $this->testAction($url, [
            'method' => Request::METHOD_POST,
        ]);

        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        self::assertFalse($result->success);

        $user = $this->fetchUser($userId);

        self::assertFalse($user->membership()->isPayg());

        app()->forgetInstance(RevertToPaygAggregatedValidationService::class);
        app()->forgetInstance(LegacySyncService::class);
        app()->forgetInstance(RequestMembershipSyncService::class);
    }

    public function test_when_memberships_service_fails_syncing_current_active_membership_the_user_keeps_previous_membership(
    ): void
    {
        $this->authenticateAsAdmin();

        $this->mockAbTestValidator(true);

        $revertToPaygAggregatedValidationService = Mockery::mock(RevertToPaygAggregatedValidationService::class);
        $revertToPaygAggregatedValidationService
            ->shouldReceive('validate')
            ->andReturnTrue()
            ->once()
            ->getMock();

        $branchId = '49a7011a05c677b9a916612a';

        $legacySyncService = Mockery::mock(LegacySyncService::class);
        $legacySyncService
            ->shouldNotReceive('execute')
            ->getMock();

        $requestMembershipSyncService = Mockery::mock(RequestMembershipSyncService::class);
        $requestMembershipSyncService
            ->shouldReceive('execute')
            ->andReturnUsing(function () {
                throw new UnsuccessfulOperation();
            })
            ->once()
            ->getMock();

        app()->instance(RevertToPaygAggregatedValidationService::class, $revertToPaygAggregatedValidationService);
        app()->instance(LegacySyncService::class, $legacySyncService);
        app()->instance(RequestMembershipSyncService::class, $requestMembershipSyncService);

        $userId = '5b19430ea0d977945a164331';

        $url = sprintf('/users/revert_to_payg/%s/%s/%s', $branchId, $userId, 'true');
        $result = $this->testAction($url, [
            'method' => Request::METHOD_POST,
        ]);

        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        self::assertFalse($result->success);

        $user = $this->fetchUser($userId);

        self::assertFalse($user->membership()->isPayg());

        app()->forgetInstance(RevertToPaygAggregatedValidationService::class);
        app()->forgetInstance(LegacySyncService::class);
        app()->forgetInstance(RequestMembershipSyncService::class);
    }

    public function test_when_attempt_to_cancel_a_membership_from_a_non_memberships_service_branch_it_updates_the_user_membership(
    ): void
    {
        $this->authenticateAsAdmin();

        $this->mockAbTestValidator(false);

        $revertToPaygAggregatedValidationService = Mockery::mock(RevertToPaygAggregatedValidationService::class);
        $revertToPaygAggregatedValidationService
            ->shouldReceive('validate')
            ->andReturnTrue()
            ->once()
            ->getMock();

        $branchId = '49a7011a05c677b9a916612a';

        $legacySyncService = Mockery::mock(LegacySyncService::class);
        $legacySyncService
            ->shouldNotReceive('execute')
            ->getMock();

        $requestMembershipSyncService = Mockery::mock(RequestMembershipSyncService::class);
        $requestMembershipSyncService
            ->shouldNotReceive('execute')
            ->getMock();

        app()->instance(RevertToPaygAggregatedValidationService::class, $revertToPaygAggregatedValidationService);
        app()->instance(LegacySyncService::class, $legacySyncService);
        app()->instance(RequestMembershipSyncService::class, $requestMembershipSyncService);

        $userId = '5b19430ea0d977945a164331';

        $url = sprintf('/users/revert_to_payg/%s/%s/%s', $branchId, $userId, 'true');
        $result = $this->testAction($url, [
            'method' => Request::METHOD_POST,
        ]);

        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        self::assertTrue($result->success);

        $user = $this->fetchUser($userId);

        self::assertTrue($user->membership()->isPayg());

        app()->forgetInstance(RevertToPaygAggregatedValidationService::class);
        app()->forgetInstance(LegacySyncService::class);
        app()->forgetInstance(RequestMembershipSyncService::class);
    }

    public function test_credits_are_found_for_remove_membership_credits_in_previous_utc_timezone(): void
    {
        $timezone = new DateTimeZone('Pacific/Rarotonga');

        $userId = '5b19430ea0d977945a164335';
        $membershipId = '54107c1cd7b5ddc3a98b4572';
        $membershipStartDate = Carbon::today($timezone)->subMonth()->toDateString();
        $cancelBookings = false;

        $url = sprintf(
            '/users/removeMembershipCredits/%s/%s/%s/%s',
            $userId,
            $membershipId,
            $membershipStartDate,
            $cancelBookings
        );

        $response = $this->testAction($url, ['method' => Request::METHOD_POST]);

        self::assertTrue($response['success']);
        self::assertNotEmpty($response['credits']);
    }

    public function test_credits_are_found_for_remove_membership_credits_in_forward_utc_timezone(): void
    {
        $timezone = new DateTimeZone('Pacific/Apia');

        $userId = '5b19430ea0d977945a164336';
        $membershipId = '54107c1cd7b5ddc3a98b4572';
        $membershipStartDate = Carbon::today($timezone)->subMonth()->toDateString();
        $cancelBookings = false;

        $url = sprintf(
            '/users/removeMembershipCredits/%s/%s/%s/%s',
            $userId,
            $membershipId,
            $membershipStartDate,
            $cancelBookings
        );

        $response = $this->testAction($url, ['method' => Request::METHOD_POST]);

        // We have some "random" failures on that call, so I add it to "log" the $response
        if ($response['success'] === false) {
            self::assertEquals('', $response);
        }
        self::assertTrue($response['success']);
        self::assertNotEmpty($response['credits']);
    }

    public function test_credits_are_not_found_for_remove_membership_credits_if_timezone_does_not_match(): void
    {
        self::markTestSkipped();
        $timezone = new DateTimeZone('Pacific/Rarotonga');

        $userId = '5b19430ea0d977945a164336'; // this user belong to a branch located in Pacific/Apia
        $membershipId = '54107c1cd7b5ddc3a98b4572';
        $membershipStartDate = Carbon::today($timezone)->subMonth()->toDateString();
        $cancelBookings = false;

        $url = sprintf(
            '/users/removeMembershipCredits/%s/%s/%s/%s',
            $userId,
            $membershipId,
            $membershipStartDate,
            $cancelBookings
        );

        $response = $this->testAction($url, ['method' => Request::METHOD_POST]);

        self::assertTrue($response['success']);
        self::assertEmpty($response['credits']);
    }

    public function test_assign_same_barcode_to_multiple_user_should_error(): void
    {
        $this->authenticateAsAdmin();
        $userId = '5ea193af03207d0e3c0b9b24';

        $url = sprintf('/users/assignBarcode/%s', $userId);
        $payload = [
            'barcode' => '854123004',
        ];
        $method = Request::METHOD_POST;

        $response = $this->testAction($url, ['data' => $payload, 'method' => $method]);
        $response = json_decode($response, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        $this->assertFalse($response['success']);
        $this->assertEquals('ANOTHER_CLIENT_HAS_THIS_BARCODE', $response['message_code']);
    }

    public function test_assign_barcode_of_inactive_user_to_active_user_should_return_success(): void
    {
        $this->authenticateAsAdmin();
        $userId = '5ea193af03207d0e3c0b9b24';
        $inActiveUserId = '5ea193978c43570e772f6024';

        $payload = [
            'barcode' => '324120089',
        ];

        $userPublisher = Mockery::mock(UsersPublisher::class);
        $userPublisher
            ->shouldReceive('sendMemberUpdatedEvent')
            ->withArgs(
                function (MemberUpdatedEventMeta $meta) {
                    $correlation = $meta->correlation();

                    return '5ea193af03207d0e3c0b9b24' === $correlation['memberId'];
                }
            );

        app()->instance(UsersPublisher::class, $userPublisher);

        $this->mockMemberAccessInfoEvent([
            'barcode' => '324120089'
        ], [
            'memberId' => $userId
        ]);

        $url = sprintf('/users/assignBarcode/%s', $userId);

        $method = Request::METHOD_POST;

        $response = $this->testAction($url, ['data' => $payload, 'method' => $method]);
        $response = json_decode($response, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        $this->assertTrue($response['success']);
        $this->assertEquals('BARCODE_SUCCESSFULLY_ASSIGNED', $response['message_code']);

        /** @var UsersRepository $usersRepository */
        $usersRepository = app()->make(UsersRepository::class);
        /** @var User $user */
        $inActiveUser = $usersRepository->addCriteria(new Id($inActiveUserId))->first();
        $this->assertEquals('', $inActiveUser['access_barcode']);

        app()->forgetInstance(UsersPublisher::class);
        app()->forgetInstance(AccessesPublisher::class);
    }

    public function test_it_logs_the_login_activity(): void
    {

        $this->createHoneycombTracker();
        $loginLogger = Mockery::mock(UserLoginLogger::class);
        $loginLogger->shouldReceive('log')
            ->withArgs(
                function (User $user): bool {
                    $this->assertSame('glofox', $user->namespace());
                    $this->assertSame('49a7011a05c677b9a916612a', $user->currentBranchId());
                    $this->assertSame('<EMAIL>', $user->email());

                    return true;
                }
            );
        app()->instance(UserLoginLogger::class, $loginLogger);

        $response = $this->testAction('/2.0/login', [
            'method' => 'POST',
            'data' => [
                'namespace' => 'glofox',
                'branch_id' => '49a7011a05c677b9a916612a',
                'login' => '<EMAIL>',
                'password' => '123456',
            ],
        ]);

        $response = json_decode($response, $assoc = true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        $this->assertArrayHasKey('token', $response);
        self::assertArrayNotHasKey('password', $response['user']);
        app()->forgetInstance(UserLoginLogger::class);
        app()->forgetInstance(HoneycombTracker::class);
    }

    public function test_unable_to_remove_barcode_to_inactive_user_should_return_error(): void
    {
        $admin = $this->authenticateAsAdmin();
        $userId = '5ea193af03207d0e3c0b9b24';
        $inActiveUserId = '5ea193978c43570e772f6024';

        $userToUpdateBarcode = User::make([
            '_id' => $userId,
            'namespace' => 'glofox',
        ]);

        $userWithDuplicateBarcode = User::make([
            '_id' => $inActiveUserId,
            'namespace' => 'glofox',
            'active' => false,
            'access_barcode' => '324120089',
        ]);

        $usersRepository = Mockery::mock(UsersRepository::class);
        $usersRepository
            ->shouldReceive('withFetchType')
            ->andReturnSelf()
            ->getMock()
            ->shouldReceive('skipCallbacks')
            ->andReturnSelf()
            ->getMock()
            ->shouldReceive('find')
            ->andReturnSelf()
            ->getMock()
            ->shouldReceive('email')
            ->andReturnTrue()
            ->getMock()
            ->shouldReceive('isActive')
            ->andReturnTrue()
            ->getMock()
            ->shouldReceive('firstOrFail')
            ->andReturn(
                User::make([
                    'active' => true,
                    'email' => $admin->email(),
                    '_id' => '59a7011a05c677bda916612a',
                    'namespace' => 'glofox',
                    'branch_id' => '49a7011a05c677b9a916612a',
                    'first_name' => 'Admin',
                    'last_name' => 'Istrator',
                    'type' => 'ADMIN',
                    'isSuperAdmin' => true,
                ])
            )
            ->getMock()
            ->shouldReceive('findById')
            ->with($userId)
            ->andReturn(['User' => $userToUpdateBarcode->toArray()])
            ->getMock()
            ->shouldReceive('isAccessBarcodeDuplicate')
            ->with('123456789', 'glofox', $userId)
            ->andReturn($userWithDuplicateBarcode)
            ->getMock()
            ->shouldReceive('removeBarcode')
            ->with($userWithDuplicateBarcode)
            ->andReturn(false)
            ->getMock()
            ->shouldReceive('addCriteria')
            ->andReturnSelf()
            ->getMock()
            ->shouldReceive('getChangePassword')
            ->shouldReceive('saveField')
            ->andReturn(false);

        app()->instance(UsersRepository::class, $usersRepository);

        $url = sprintf('/users/assignBarcode/%s', $userId);
        $payload = ['barcode' => '123456789'];
        $method = Request::METHOD_POST;

        $response = $this->testAction($url, ['data' => $payload, 'method' => $method]);
        $response = json_decode($response, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        $this->assertFalse($response['success']);
        $this->assertEquals('UNABLE_TO_REMOVE_BARCODE_FOR_INACTIVE_USER', $response['message_code']);

        app()->forgetInstance(UsersRepository::class);
    }

    public function test_login_checks_eagreements(): void
    {
        $this->createHoneycombTracker();
        $eagreements = Mockery::mock(ElectronicAgreementsServiceInterface::class);
        $eagreements->shouldReceive('isElectronicAgreementsEnabled')
            ->withArgs(
                fn ($branchId, $user): bool => $branchId == '49a7011a05c677b9a916612a' && $user->id(
                    ) == '59a7011a05c677bda916612c'
            )
            ->andReturn(true);

        $eagreements->shouldReceive('listAgreements')
            ->withArgs(
                fn ($branchId, $userId, $trigger) => $branchId == '49a7011a05c677b9a916612a' &&
                    $userId == '59a7011a05c677bda916612c' &&
                    $trigger == Trigger::MEMBER_AUTHENTICATED()
            )
            ->andReturn(
                [], // no response
                [
                    new Agreement([
                        'status' => 'outstanding',
                    ]),
                ], // requires signature
                [
                    new Agreement([
                        'status' => 'accepted',
                    ]),
                ] // already accepted
            );

        app()->instance(ElectronicAgreementsServiceInterface::class, $eagreements);

        $loginData = [
            'method' => 'POST',
            'data' => [
                'namespace' => 'glofox',
                'branch_id' => '49a7011a05c677b9a916612a',
                'login' => '<EMAIL>',
                'password' => '123456',
            ],
        ];

        // no e-agreements response
        $response = $this->testAction('/2.0/login', $loginData);
        $response = json_decode($response, $assoc = true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        $this->assertFalse($response['user']['WAIVER']);

        // requires signature
        $response = $this->testAction('/2.0/login', $loginData);
        $response = json_decode($response, $assoc = true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        $this->assertFalse($response['user']['WAIVER']);

        // already accepted
        $response = $this->testAction('/2.0/login', $loginData);
        $response = json_decode($response, $assoc = true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        $this->assertTrue($response['user']['WAIVER']);

        app()->forgetInstance(ElectronicAgreementsServiceInterface::class);
        app()->forgetInstance(HoneycombTracker::class);
    }

    public function test_uploads_method_sends_file_to_s3(): void
    {
        $user = $this->fetchUser('59a7011a05c677bda512212a');
        $this->loginAsUser($user);

        $_FILES = [
            'staffImage' => [
                'name' => 'foo.png',
                'tmp_name' => '/tmp/php42up23',
                'type' => 'image/png',
                'size' => 182,
                'error' => 0,
            ],
        ];

        file_put_contents('/tmp/php42up23', base64_decode('foo'));

        $s3Component = Mockery::mock(S3Component::class);
        $s3Component->shouldReceive('initialize');
        $s3Component->shouldReceive('saveCrudImage')
            ->withArgs(
                function ($imageData, $namespace, $branchId, $feature) use ($user) {
                    $this->assertSame('foo.png', $imageData['name']);
                    $this->assertSame($user->namespace(), $namespace);
                    $this->assertSame($user->currentBranchId(), $branchId);
                    $this->assertSame('trainers', $feature);

                    return true;
                }
            )
            ->andReturn(['success' => true]);

        $s3Component->shouldReceive('getCrudImageUrl')
            ->withArgs(
                function ($namespace, $branchId, $feature) use ($user) {
                    $this->assertSame($user->namespace(), $namespace);
                    $this->assertSame($user->currentBranchId(), $branchId);
                    $this->assertSame('trainers', $feature);

                    return true;
                }
            )
            ->andReturn('https://glofox.s3.amazonaws.com/foo');

        app()->instance(S3Component::class, $s3Component);

        $json = $this->testAction(sprintf('/2.0/branches/%s/staff/uploads', $user->currentBranchId()), [
            'method' => 'POST',
        ]);
        // response with URL pointing to cdn.glofox.com
        $json = json_decode($json, $assoc = true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        $this->assertTrue($json['success']);
        $this->assertSame('https://cdn.glofox.com/foo', $json['url']);

        app()->forgetInstance(S3Component::class);
    }

    public function test_can_change_super_admin_type_if_it_is_not_the_last_one(): void
    {
        $this->markTestSkipped('Skipped as we no longer allow the changing of the staff type on update.');

        $this->authenticateAsSuperAdmin();
        $superAdminId = '5e9ed06cd575e6003c38658e';

        $userToUpdate = $this->fetchUser($superAdminId);

        $data = [
            '_id' => $superAdminId,
            'first_name' => $userToUpdate->firstName(),
            'last_name' => $userToUpdate->lastName(),
            'email' => $userToUpdate->email(),
            'active' => true,
            'type' => 'TRAINER',
        ];

        $publisher = Mockery::mock(UsersPublisher::class);
        $publisher
            ->shouldReceive('sendMemberUpdatedEvent')
            ->once();
        app()->instance(UsersPublisher::class, $publisher);

        $canChangeSuperAdminTypeValidator = Mockery::mock(CanChangeSuperAdminTypeValidator::class);
        $canChangeSuperAdminTypeValidator
            ->shouldReceive('validate')
            ->once();
        app()->instance(CanChangeSuperAdminTypeValidator::class, $canChangeSuperAdminTypeValidator);

        $this->testAction('/users/save', [
            'method' => 'POST',
            'data' => $data,
        ]);

        $userUpdated = $this->fetchUser($superAdminId);

        self::assertEquals('SUPERADMIN', $userToUpdate->get('type'));
        self::assertEquals('TRAINER', $userUpdated->get('type'));

        app()->forgetInstance(UsersPublisher::class);
        app()->forgetInstance(CanChangeSuperAdminTypeValidator::class);
    }

    public function test_when_attempt_to_change_the_type_of_the_last_super_admin_throws_exception(): void
    {
        $this->authenticateAsSuperAdmin();
        $superAdminId = '5e9ed06cd575e6003c38658e';

        $userToUpdate = $this->fetchUser($superAdminId);

        $data = [
            '_id' => $superAdminId,
            'first_name' => $userToUpdate->firstName(),
            'last_name' => $userToUpdate->lastName(),
            'email' => $userToUpdate->email(),
            'active' => true,
            'type' => 'TRAINER',
        ];

        $canChangeSuperAdminTypeValidator = Mockery::mock(CanChangeSuperAdminTypeValidator::class);
        $canChangeSuperAdminTypeValidator
            ->shouldReceive('validate')
            ->andThrow(UnsuccessfulOperation::class, 'RANDOM_MESSAGE')
            ->once();
        app()->instance(CanChangeSuperAdminTypeValidator::class, $canChangeSuperAdminTypeValidator);

        $result = $this->testAction('/users/save', [
            'method' => 'POST',
            'data' => $data,
        ]);
        $result = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        $userUpdated = $this->fetchUser($superAdminId);

        self::assertFalse($result['success']);
        self::assertEquals('RANDOM_MESSAGE', $result['message']);
        self::assertEquals($userToUpdate->get('type'), $userUpdated->get('type'));

        app()->forgetInstance(CanChangeSuperAdminTypeValidator::class);
    }

    public function test_can_delete_super_admin_if_it_is_not_the_last_one(): void
    {
        $this->authenticateAsSuperAdmin();
        $superAdminId = '5e9ed06cd575e6003c38658e';

        $userToUpdate = $this->fetchUser($superAdminId);

        $data = [
            '_id' => $superAdminId,
            'first_name' => $userToUpdate->firstName(),
            'last_name' => $userToUpdate->lastName(),
            'email' => $userToUpdate->email(),
            'type' => 'SUPERADMIN',
            'active' => false,
        ];

        $publisher = Mockery::mock(UsersPublisher::class);
        $publisher
            ->shouldReceive('sendMemberUpdatedEvent')
            ->once();
        app()->instance(UsersPublisher::class, $publisher);

        $this->mockMemberAccessInfoEvent([], [
            'memberId' => $superAdminId
        ]);

        $canDeleteSuperAdminValidator = Mockery::mock(CanDeleteSuperAdminValidator::class);
        $canDeleteSuperAdminValidator
            ->shouldReceive('validate')
            ->andReturnSelf()
            ->once();
        app()->instance(CanDeleteSuperAdminValidator::class, $canDeleteSuperAdminValidator);

        $this->testAction('/users/save', [
            'method' => 'POST',
            'data' => $data,
        ]);

        $userUpdated = $this->fetchUser($superAdminId);

        self::assertEquals($userToUpdate->get('type'), $userUpdated->get('type'));
        self::assertFalse($userUpdated->get('active'));

        app()->forgetInstance(UsersPublisher::class);
        app()->forgetInstance(CanDeleteSuperAdminValidator::class);
    }

    public function test_when_attempt_to_delete_the_last_super_admin_throws_exception(): void
    {
        $this->authenticateAsSuperAdmin();
        $superAdminId = '5e9ed06cd575e6003c38658e';

        $userToUpdate = $this->fetchUser($superAdminId);

        $data = [
            '_id' => $superAdminId,
            'first_name' => $userToUpdate->firstName(),
            'last_name' => $userToUpdate->lastName(),
            'email' => $userToUpdate->email(),
            'type' => 'SUPERADMIN',
            'active' => false,
        ];

        $canDeleteSuperAdminValidator = Mockery::mock(CanDeleteSuperAdminValidator::class);
        $canDeleteSuperAdminValidator
            ->shouldReceive('validate')
            ->andThrow(UnsuccessfulOperation::class)
            ->once();
        app()->instance(CanDeleteSuperAdminValidator::class, $canDeleteSuperAdminValidator);

        $this->testAction('/users/save', [
            'method' => 'POST',
            'data' => $data,
        ]);

        $userUpdated = $this->fetchUser($superAdminId);

        self::assertEquals($userToUpdate->get('type'), $userUpdated->get('type'));
        self::assertTrue($userUpdated->get('active'));

        app()->forgetInstance(CanDeleteSuperAdminValidator::class);
    }

    public function test_users_range_endpoint_considers_roaming_branches_based_on_a_flag(): void
    {
        $this->authenticateAsAdmin();

        // Legacy without allow_roaming_users
        $result = $this->testAction('/users/range', [
            'data' => ['ids' => ['5e9ed06cd573e6003c38658a']],
            'method' => 'POST',
        ]);

        $result = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        self::assertCount(0, $result);

        // With allow_roaming_users set to false
        $result = $this->testAction('/users/range', [
            'data' => [
                'ids' => ['5e9ed06cd573e6003c38658a'],
                'allow_roaming_users' => false,
            ],
            'method' => 'POST',
        ]);

        $result = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        self::assertCount(0, $result);

        // With allow_roaming_users set to true
        $result = $this->testAction('/users/range', [
            'data' => [
                'ids' => ['5e9ed06cd573e6003c38658a'],
                'allow_roaming_users' => true,
            ],
            'method' => 'POST',
        ]);

        $result = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        self::assertCount(1, $result);
    }

    public function test_when_member_tries_to_change_type_to_admin_exception_is_thrown(): void
    {
        app()->bind(UsersPublisher::class, function () {
            $publisher = Mockery::mock(UsersPublisher::class);
            $publisher->shouldReceive('sendNewMemberAddedEvent');

            return $publisher;
        });

        $this->mockMemberAccessInfoEvent([], [], true);

        $this->authenticateAsAdmin();

        $memberData = [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'phone' => '0850101011',
            'email' => '<EMAIL>',
            'password' => 's3crET$$$okm',
            'branch_id' => '49a7011a05c677b9a916612b',
        ];
        $this->createFeatureFlagMock(false);
        $this->createConsentFlaggerMock(false);

        $result = $this->testAction('/2.0/register', [
            'data' => $memberData,
            'method' => Request::METHOD_POST,
        ]);

        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        self::assertTrue($result->success);

        $member = $this->fetchUser($result->user->_id);

        $this->loginAsUser($member);

        $member->put('type', UserType::ADMIN);

        $result = $this->testAction("/2.0/members/{$member->id()}", [
            'method' => 'PUT',
            'data' => $member->toArray(),
        ]);

        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        $this->assertEquals(200, $this->response->statusCode());
        self::assertFalse($result->success);
        app()->forgetInstance(ConsentFlagger::class);
        app()->forgetInstance(FeatureFlagInterface::class);
    }

    public function test_when_trainer_tries_to_change_type_to_admin_exception_is_thrown(): void
    {
        app()->bind(UsersPublisher::class, function () {
            $publisher = Mockery::mock(UsersPublisher::class);
            $publisher->shouldReceive('sendNewMemberAddedEvent');
            $publisher->shouldReceive('sendMemberUpdatedEvent');

            return $publisher;
        });

        $this->mockMemberAccessInfoEvent([], [], true);

        $this->authenticateAsTrainer();

        $memberData = [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'phone' => '0850101014',
            'email' => '<EMAIL>',
            'password' => 's3crET$$$okm',
            'branch_id' => '49a7011a05c677b9a916612b',
        ];
        $this->createFeatureFlagMock(false);
        $this->createConsentFlaggerMock(false);
        $result = $this->testAction('/2.0/register', [
            'data' => $memberData,
            'method' => Request::METHOD_POST,
        ]);

        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        self::assertTrue($result->success);

        $member = $this->fetchUser($result->user->_id);

        $member->put('type', UserType::ADMIN);

        $result = $this->testAction("/2.0/members/{$member->id()}", [
            'method' => 'PUT',
            'data' => $member->toArray(),
        ]);

        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        $this->assertEquals(200, $this->response->statusCode());
        self::assertFalse($result->success);
        app()->forgetInstance(ConsentFlagger::class);
        app()->forgetInstance(FeatureFlagInterface::class);
    }

    public function test_when_admin_tries_to_change_type_to_admin(): void
    {
        app()->bind(UsersPublisher::class, function () {
            $publisher = Mockery::mock(UsersPublisher::class);
            $publisher->shouldReceive('sendNewMemberAddedEvent');
            $publisher->shouldReceive('sendMemberUpdatedEvent');

            return $publisher;
        });

        $this->mockMemberAccessInfoEvent([], [], true);

        $this->authenticateAsAdmin();

        $memberData = [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'phone' => '0850101012',
            'email' => '<EMAIL>',
            'password' => 's3crET$$$okm',
            'branch_id' => '49a7011a05c677b9a916612b',
        ];
        $this->createFeatureFlagMock(false);
        $this->createConsentFlaggerMock(false);
        $result = $this->testAction('/2.0/register', [
            'data' => $memberData,
            'method' => Request::METHOD_POST,
        ]);

        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        self::assertTrue($result->success);

        $member = $this->fetchUser($result->user->_id);

        $member->put('type', UserType::ADMIN);

        $this->mockMemberAccessInfoEvent([], [
            'memberId' => $result->user->_id
        ]);

        $result = $this->testAction("/2.0/members/{$member->id()}", [
            'method' => 'PUT',
            'data' => $member->toArray(),
        ]);

        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        $this->assertEquals(200, $this->response->statusCode());
        self::assertTrue($result->success);
        app()->forgetInstance(ConsentFlagger::class);
        app()->forgetInstance(FeatureFlagInterface::class);
    }

    public function test_when_receptionist_tries_to_change_type_to_admin(): void
    {
        app()->bind(UsersPublisher::class, function () {
            $publisher = Mockery::mock(UsersPublisher::class);
            $publisher->shouldReceive('sendNewMemberAddedEvent');
            $publisher->shouldReceive('sendMemberUpdatedEvent');

            return $publisher;
        });

        $this->mockMemberAccessInfoEvent([], [], true);

        $this->authenticateAsReception();

        $memberData = [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'phone' => '0850101013',
            'email' => '<EMAIL>',
            'password' => 's3crET$$$okm',
            'branch_id' => '49a7011a05c677b9a916612b',
        ];
        $this->createFeatureFlagMock(false);
        $this->createConsentFlaggerMock(false);
        $result = $this->testAction('/2.0/register', [
            'data' => $memberData,
            'method' => Request::METHOD_POST,
        ]);

        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        self::assertTrue($result->success);

        $member = $this->fetchUser($result->user->_id);

        $member->put('type', UserType::ADMIN);

        $this->mockMemberAccessInfoEvent([], [
            'memberId' => $result->user->_id
        ]);

        $result = $this->testAction("/2.0/members/{$member->id()}", [
            'method' => 'PUT',
            'data' => $member->toArray(),
        ]);

        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        $this->assertEquals(200, $this->response->statusCode());
        self::assertTrue($result->success);
        app()->forgetInstance(ConsentFlagger::class);
        app()->forgetInstance(FeatureFlagInterface::class);
    }

    public function test_get_all_members_works_for_admin_tokens(): void
    {
        $user = $this->fetchUser('5c79e58d7fb1afd6e2c806f6');
        $this->loginAsUser($user);

        $result = $this->testAction('/users/get_all_members/get_all_test/5c783d4cd510f9635ad4a6b3', [
            'method' => 'POST',
        ]);

        $result = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        self::assertCount(2, $result);
    }

    public function test_get_all_members_does_not_work_for_member_tokens(): void
    {
        $user = $this->fetchUser('5c8b0615deb1eae76ec28123');
        $this->loginAsUser($user);

        $result = $this->testAction('/users/get_all_members/get_all_test/5c783d4cd510f9635ad4a6b3', [
            'method' => 'POST',
        ]);

        $result = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        self::assertFalse($result['success']);
    }

    public function test_get_all_members_does_not_work_for_staff_from_another_branch(): void
    {
        $user = $this->fetchUser('5c79e58d7fb1afd6e2c806f1');
        $this->loginAsUser($user);

        $result = $this->testAction('/users/get_all_members/get_all_test/5c783d4cd510f9635ad4a6b3', [
            'method' => 'POST',
        ]);

        $result = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        self::assertFalse($result['success']);
    }

    public function test_when_receptionist_tries_to_create_admin_exception_is_thrown(): void
    {
        $this->loginAsUser(
            $this->fetchUser('59a7011a05c677bda916619b')
        );

        $data = [
            'first_name' => 'foo',
            'last_name' => 'bar',
            'email' => '<EMAIL>',
            'active' => true,
            'type' => \UserType::ADMIN,
        ];

        $canChangeSuperAdminTypeValidator = Mockery::mock(CanCreateStaffValidator::class);
        $canChangeSuperAdminTypeValidator
            ->shouldReceive('validate')
            ->andThrow(UnsuccessfulOperation::class, 'RANDOM_MESSAGE')
            ->once();
        app()->instance(CanCreateStaffValidator::class, $canChangeSuperAdminTypeValidator);

        $result = $this->testAction('/users/save', [
            'method' => 'POST',
            'data' => $data,
        ]);
        $result = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        self::assertFalse($result['success']);
        self::assertEquals('RANDOM_MESSAGE', $result['message']);

        app()->forgetInstance(CanCreateStaffValidator::class);
    }

    public function test_member_deactivation(): void
    {
        $this->authenticateAsAdmin();

        $userId = '59a7010a05c676b3a916625d';

        $publisher = Mockery::mock(UsersPublisher::class);
        $publisher
            ->shouldReceive('sendMemberUpdatedEvent')
            ->withArgs(
                function (MemberUpdatedEventMeta $meta) use ($userId) {
                    $correlation = $meta->correlation();

                    return $userId === $correlation['memberId'];
                }
            );

        app()->instance(UsersPublisher::class, $publisher);

        $url = sprintf('/users/remove/%s', $userId);

        $method = Request::METHOD_DELETE;

        $response = $this->testAction($url, ['data' => [], 'method' => $method]);
        $response = json_decode($response, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        $this->assertTrue($response['success']);

        /** @var UsersRepository $usersRepository */
        $usersRepository = app()->make(UsersRepository::class);

        /** @var User $deactivatedUser */
        $deactivatedUser = $usersRepository->addCriteria(new Id($userId))->first();
        $this->assertFalse($deactivatedUser->isActive());

        app()->forgetInstance(UsersPublisher::class);
    }

    public function test_member_deactivation_by_guest_fails(): void
    {
        $this->authenticateAsGuest();

        $response = $this->testAction('/users/remove/59a7010a05c676b3a916625d', [
            'data' => [],
            'method' => Request::METHOD_DELETE,
        ]);
        $response = json_decode($response, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        self::assertfalse($response['success']);
        self::assertEquals('Unauthorized', $response['message']);
    }

    public function test_member_deactivation_by_trainer_fails(): void
    {
        $this->authenticateAsTrainer();

        $response = $this->testAction('/users/remove/59a7010a05c676b3a916625d', [
            'data' => [],
            'method' => Request::METHOD_DELETE,
        ]);
        $response = json_decode($response, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        self::assertfalse($response['success']);
        self::assertEquals('Unauthorized', $response['message']);
    }

    public function test_member_deactivation_from_other_branch_fails(): void
    {
        $this->authenticateAsAdmin();

        $response = $this->testAction('/users/remove/61234dd4cd3f573330f67e47', [
            'data' => [],
            'method' => Request::METHOD_DELETE,
        ]);
        $response = json_decode($response, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        self::assertfalse($response['success']);
        self::assertEquals('Resource not found', $response['message']);
    }

    public function test_dispatcherMember_only_dispatches_user_details_of_token_member(): void
    {
        $tokenMemberId = '59a7010a05c777b3a916715d';
        $requestMemberId = $tokenMemberId;

        $member = $this->fetchUser($tokenMemberId);
        $this->loginAsUser($member);

        $url = sprintf('/2.0/members/%s', $requestMemberId);
        $response = $this->testAction($url, ['method' => 'GET']);
        $result = json_decode($response, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        $this->assertTextEquals($result['_id'], $member['_id']);
        $this->assertTextEquals($result['email'], $member['email']);

        $requestMemberId = '59a7010a05c877b3a916715d';

        $url = sprintf('/2.0/members/%s', $requestMemberId);
        $response = $this->testAction($url, ['method' => 'GET']);
        $result = json_decode($response, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        $expectedMessage = "Unauthorized";
        self::assertFalse($result['success']);
        $this->assertTextEquals($expectedMessage, $result['message']);
    }

    private function getSampleUserData(): array
    {
        return [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'phone' => '0850101010',
            'email' => '<EMAIL>',
            'password' => 's3crET$$$okm',
            'branch_id' => '49a7011a05c677b9a916612b',
            'birth' => '2000/01/01',
            'type' => 'MEMBER',
            'membership' => [
                '_id' => '570b7970778f3bf31e8b4567',
                'type' => 'time_classes',
                'membership_group_id' => null,
                'plan_code' => 1_531_227_102_357,
                'boooked_events' => 0,
                'branch_id' => '570b795c778f3bbd138b4568',
                'branch_name' => 'Cookie Studio',
                'starts_on' => 'PURCHASE_DATE',
                'roaming_enabled' => false,
                'start_date' => '2018-08-11T00:00:00.000Z',
                'duration_time_unit' => 'month',
                'duration_time_unit_count' => 1,
                'expiry_date' => '2018-08-11T00:00:00.000Z',
                'start_date_moment' => '2018-08-11T23:00:00.000Z',
                'expiry_date_moment' => '2018-09-11T23:00:00.000Z',
            ],
        ];
    }

    private function getMockUserData(array $override = []): array
    {
        return array_merge([
            'first_name' => 'John',
            'last_name' => 'Mock',
            'namespace' => 'glofox',
            'branch_id' => '49a7011a05c677b9a916612a',
            'strike' => -5,
            'type' => 'MEMBER',
            'login' => 'REPLACE THIS',
            'email' => 'REPLACE THIS',
            'password' => 'OIJ3290JOimkdas$9384',
            'phone' => 'REPLACE THIS',
            'membership' => ['type' => 'payg'],
        ], $override);
    }

    private static function createTrainerPayload(): array
    {
        return [
            'branch_id' => '49a7011a05c677b9a916612a',
            'namespace' => 'glofox',
            'active' => true,
            'categories' => [],
            'type' => 'TRAINER',
            'name' => 'Timmy Fisher',
            'first_name' => 'Timmy',
            'last_name' => 'Fisher',
            'phone' => '08912491827',
            'description' => 'Worst Trainer Ever!',
            'email' => '<EMAIL>',
            'bookable' => true,
            'login' => '<EMAIL>',
            'experience' => 'A lot...',
            'gender' => 'M',
            'birth' => '1990-10-10',
            'twitter' => '@timmy',
            'facebook' => 'https://facebook.com/timmy',
        ];
    }

    public function testLoginUserWhomHaveBcryptPasswordHash(): void
    {
        $this->createHoneycombTracker();
        $user = ['namespace' => 'glofox', 'login' => '<EMAIL>', 'password' => 'password123'];
        $result = $this->testAction('/2.0/login', ['data' => $user, 'method' => 'post']);
        $result = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        self::assertEquals('<EMAIL>', $result['user']['email']);
        self::assertEquals('glofox', $result['user']['namespace']);
        app()->forgetInstance(HoneycombTracker::class);
    }

    public function test_cannot_update_member_lead_status_from_a_status_that_does_not_support_updates(): void
    {
        $this->authenticateAsAdmin();

        $user = $this->fetchUser('5adde862427415b9e9b4be9e');

        $data = $user->toArray();

        $data['lead_status'] = 'LEAD';
        unset($data['address']);

        $leadStatusChangeFlowValidator = Mockery::mock(LeadStatusChangeFlowValidator::class);
        $leadStatusChangeFlowValidator
            ->shouldReceive('validate')
            ->andThrow(UnsuccessfulOperation::class, 'Cannot update lead status from MEMBER to LEAD')
            ->once();
        app()->instance(LeadStatusChangeFlowValidator::class, $leadStatusChangeFlowValidator);

        $response = $this->testAction('/users/save', [
            'method' => 'POST',
            'data' => $data,
        ]);

        $response = json_decode($response, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        self::assertEquals('Cannot update lead status from MEMBER to LEAD', $response->message);

        app()->forgetInstance(LeadStatusChangeFlowValidator::class);
    }

    public function testResettingPasswordFailsIfEmailIsNotValid(): void
    {
        $data = [
            'email' => 'invalid-email',
        ];

        $result = $this->testAction('/users/resetpwd', ['data' => $data, 'method' => 'post']);
        $result = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        self::assertFalse($result['success']);
        self::assertEquals("The email must be a valid email address.", $result['message']);
    }

    public function test_cannot_set_member_insurance_details_from_save_method(): void
    {
        $this->authenticateAsAdmin();
        $this->createConsentFlaggerMock(false);

        $user = $this->fetchUser('5adde862427415b8e9b4be9e');

        $data = $user->toArray();

        $expectedMetadata = [
            'policy_number' => 'test_policy_number',
            'company_id' => 'test_company_id',
            'group_id' => 'test_group_id',
        ];

        self::assertEquals('Meta', $data['first_name']);
        self::assertEquals($expectedMetadata, $data['metadata']['insurance']);

        $data['first_name'] = 'Metadata';
        $data['metadata'] = [
            'insurance' => [
                'policy_number' => 'test_policy_number_edited',
                'company_id' => 'test_company_id_edited',
                'group_id' => 'test_group_id_edited',
            ],
        ];
        unset($data['address']);

        $this->testAction('/users/save', [
            'method' => 'POST',
            'data' => $data,
        ]);

        $userAfter = $this->fetchUser('5adde862427415b8e9b4be9e');

        $dataAfter = $userAfter->toArray();

        // abel to update something else but metadata.insurance
        self::assertEquals('Metadata', $dataAfter['first_name']);
        self::assertEquals($expectedMetadata, $dataAfter['metadata']['insurance']);
        app()->forgetInstance(ConsentFlagger::class);
    }

    public function test_filtering_consent_data_properly_when_fetching_user(): void
    {
        $userId = '610005a8f676d06319f9e2b9';
        $branchId = '49a7011a05c677b9a916612a';
        $namespace = 'glofox';

        $this->authenticateAsAdmin();
        
        $this->createRoamingMemberRestrcitedProfileFlaggerMock();
        $this->createHoneycombTrackerMock();

        $result = $this->testAction(
            sprintf(
                '/users/findById/%s/%s/%s',
                $userId,
                $namespace,
                $branchId
            ),
            ['method' => 'GET']
        );

        $response = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        $this->assertFalse(isset($response['User']['consent']['email']['modified_from_ip_address']));
        $this->assertFalse(isset($response['User']['consent']['email']['message']));
        $this->assertFalse(isset($response['User']['consent']['sms']['modified_from_ip_address']));
        $this->assertFalse(isset($response['User']['consent']['sms']['message']));
        app()->forgetInstance(FeatureFlagInterface::class);
        app()->forgetInstance(HoneycombTracker::class);
    }

    public function test_adding_and_filtering_consent_data_properly_when_fetching_user_on_members_endpoint(): void
    {
        $userId = '610005a8f676d06319f9e2b9';

        $this->authenticateAsAdmin();

        $result = $this->testAction(
            sprintf(
                '2.0/members/%s',
                $userId,
            ),
            ['method' => 'GET']
        );

        $response = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        $this->assertFalse(isset($response['User']['consent']['email']['modified_from_ip_address']));
        $this->assertFalse(isset($response['User']['consent']['email']['message']));
        $this->assertFalse(isset($response['User']['consent']['sms']['modified_from_ip_address']));
        $this->assertFalse(isset($response['User']['consent']['sms']['message']));
    }

    public function test_can_update_consent(): void
    {
        $this->authenticateAsAdmin();
        $user = $this->fetchUser('5b19430ea0d987945a16432f');

        $this->createConsentFlaggerMock(true);

        app()->bind(UsersPublisher::class, function () {
            $publisher = Mockery::mock(UsersPublisher::class);
            $publisher->shouldReceive('sendMemberUpdatedEvent');

            return $publisher;
        });

        $payload = $user->toArray();

        $this->mockMemberAccessInfoEvent([], [
            'memberId' => $payload['_id']
        ]);

        $payload['first_name'] = 'consent';
        $payload['access_barcode'] = '4624627648264824';
        $payload['consent'] = ['email' => ['active' => false]];
        unset($payload['address']);

        $response = $this->testAction('/users/save', [
            'data' => $payload,
            'method' => Request::METHOD_POST,
        ]);

        $response = json_decode($response, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        self::assertTrue($response->success);

        $user = $this->fetchUser('5b19430ea0d987945a16432f');

        self::assertFalse($user->consent()->email()->get('active'));
        self::assertEquals('GDPR_CONSENT_TO_MARKETING_EMAIL', $user->consent()->email()->get('message'));
        self::assertEquals('59a7011a05c677bda916612a', $user->consent()->email()->get('modified_by_user_id'));
        app()->forgetInstance(ConsentFlagger::class);
    }

    public function test_it_allows_members_to_be_created_with_consent_using_register_endpoint(): void
    {
        /** @var UsersRepository $usersRepository */
        $usersRepository = app()->make(UsersRepository::class);

        $this->loginAsGuest('glofox', '49a7011a05c677b9a916612b');

        $usersPublisher = Mockery::mock(UsersPublisher::class);
        $usersPublisher->shouldReceive('sendNewMemberAddedEvent');
        app()->instance(UsersPublisher::class, $usersPublisher);

        $this->mockMemberAccessInfoEvent([], [], true);

        $password = 's3crET$$$okm';
        $payload = [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'phone' => '08501010101',
            'email' => '<EMAIL>',
            'password' => $password,
            'branch_id' => '49a7011a05c677b9a916612b',
            'receive_marketing' => true,
            'consent' => [
                'email' => ['active' => true],
                'sms' => ['active' => true],
            ],
        ];
        $this->createFeatureFlagMock(false);
        $this->createConsentFlaggerMock(true);
        $result = $this->testAction('/2.0/register', [
            'data' => $payload,
            'method' => Request::METHOD_POST,
        ]);

        $result = json_decode($result, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        self::assertTrue($result->success);

        $user = $this->fetchUser($result->user->_id);

        self::assertTrue($user->consent()->email()->get('active'));
        self::assertEquals('GDPR_CONSENT_TO_MARKETING_EMAIL', $user->consent()->email()->get('message'));
        self::assertEquals('guest', $user->consent()->email()->get('modified_by_user_id'));
        self::assertNull($user->get('receive_marketing'));

        // Teardown
        $usersRepository->delete($user);
        app()->forgetInstance(ConsentFlagger::class);
        app()->forgetInstance(UsersPublisher::class);
    }

    public function test_gantner_entry_fails_due_to_wrong_http_method(): void
    {
        $branchId = (string) new MongoId('49a7011a05c677b9a916612a');
        $gantnerToken = 'gantner-token-123';
        $url = sprintf('/users/entry/%s/%s?gantner_token=%s', $branchId, $gantnerToken, $gantnerToken);
        $payload = [];

        $result = json_decode(
            $this->testAction($url, ['data' => $payload, 'method' => 'GET']),
            true,
            512,
            JSON_PARTIAL_OUTPUT_ON_ERROR
        );

        self::assertFalse($result['success']);
        self::assertEquals('Invalid POST Request', $result['message']);
    }

    public function test_gantner_entry_fails_due_to_invalid_branch_id(): void
    {
        $branchId = 'wrong-branch-id';
        $gantnerToken = 'gantner-token-123';
        $url = sprintf('/users/entry/%s/%s?gantner_token=%s', $branchId, $gantnerToken, $gantnerToken);
        $payload = [];

        $result = json_decode(
            $this->testAction($url, ['data' => $payload, 'method' => 'POST']),
            true,
            512,
            JSON_PARTIAL_OUTPUT_ON_ERROR
        );

        self::assertFalse($result['success']);
        self::assertEquals('Invalid branch_id', $result['message']);
    }

    public function test_gantner_entry_fails_due_to_unexisting_branch_id(): void
    {
        $branchId = (string) new MongoId('49a7011a05c677b9a916642b');
        $gantnerToken = 'gantner-token-123';
        $url = sprintf('/users/entry/%s/%s?gantner_token=%s', $branchId, $gantnerToken, $gantnerToken);
        $payload = [];

        $result = json_decode(
            $this->testAction($url, ['data' => $payload, 'method' => 'POST']),
            true,
            512,
            JSON_PARTIAL_OUTPUT_ON_ERROR
        );

        self::assertFalse($result['success']);
        self::assertEquals('Branch not found', $result['message']);
    }

    public function test_gantner_entry_fails_due_to_invalid_input(): void
    {
        $branchId = (string) new MongoId('49a7011a05c677b9a916612a');
        $gantnerToken = 'gantner-token-123';
        $url = sprintf('/users/entry/%s/%s?gantner_token=%s', $branchId, $gantnerToken, $gantnerToken);
        $payload = [];

        $result = json_decode(
            $this->testAction($url, ['data' => $payload, 'method' => 'POST']),
            true,
            512,
            JSON_PARTIAL_OUTPUT_ON_ERROR
        );

        self::assertFalse($result['success']);
        self::assertEquals('INVALID_INPUT_PROVIDED', $result['message']);
    }

    public function test_gantner_entry_fails_due_to_missing_gantner_toke_in_url(): void
    {
        // This test doesn't reach the validation in the method, but at router level,
        // so the message doesn't match the one returned from the method
        $branchId = (string) new MongoId('49a7011a05c677b9a916612a');
        $gantnerToken = 'gantner-token-123';
        $url = sprintf('/users/entry/%s/?gantner_token=%s', $branchId, $gantnerToken);
        $payload = [];

        $result = json_decode(
            $this->testAction($url, ['data' => $payload, 'method' => 'POST']),
            true,
            512,
            JSON_PARTIAL_OUTPUT_ON_ERROR
        );

        self::assertFalse($result['success']);
        self::assertEquals('This route is not allowed for Gantner', $result['message']);
    }

    public function test_gantner_entry_fails_due_to_missing_gantner_toke_in_query_params(): void
    {
        // This test doesn't reach the validation in the method, but at request level,
        // so the message doesn't match the one returned from the method
        $branchId = (string) new MongoId('49a7011a05c677b9a916612a');
        $gantnerToken = 'gantner-token-123';
        $url = sprintf('/users/entry/%s/%s?gantner_token=', $branchId, $gantnerToken);
        $payload = [];

        $result = json_decode(
            $this->testAction($url, ['data' => $payload, 'method' => 'POST']),
            true,
            512,
            JSON_PARTIAL_OUTPUT_ON_ERROR
        );

        self::assertFalse($result['success']);
        self::assertEquals('The authentication token is empty', $result['message']);
    }

    public function test_gantner_entry_fails_due_to_invalid_gantner_token(): void
    {
        $branchId = (string) new MongoId('49a7011a05c677b9a916612a');
        $gantnerToken = 'gantner-token-123';
        $invalidGantnerToken = 'invalid-gantner-token';
        $url = sprintf('/users/entry/%s/%s?gantner_token=%s', $branchId, $invalidGantnerToken, $gantnerToken);
        $payload = [0 => ['card_id' => '']];

        $result = json_decode(
            $this->testAction($url, ['data' => json_encode($payload, JSON_THROW_ON_ERROR), 'method' => 'POST']),
            true,
            512,
            JSON_PARTIAL_OUTPUT_ON_ERROR
        );

        self::assertFalse($result['success']);
        self::assertEquals('Invalid Gantner Token', $result['message']);
    }

    public function test_gantner_entry_properly_saves_access_and_mark_booking_as_attended(): void
    {
        $bookingModel = ClassRegistry::init('Booking');
        $bookingId = '5b3e01916a9b8505d9d3fb74';
        $booking = $bookingModel->findOrFail($bookingId);

        self::assertEmpty($booking['Booking']['attended']);

        $branchId = (string) new MongoId('49a7011a05c677b9a916612a');
        $gantnerToken = 'gantner-token-123';
        $entryTime = Carbon::now()->subDay()->toDateTimeString();
        $url = sprintf('/users/entry/%s/%s?gantner_token=%s', $branchId, $gantnerToken, $gantnerToken);
        $payload = [0 => ['card_id' => 'gantern-card-id', 'entry_time' => $entryTime]];

        $result = json_decode(
            $this->testAction($url, ['data' => json_encode($payload, JSON_THROW_ON_ERROR), 'method' => 'POST']),
            true,
            512,
            JSON_PARTIAL_OUTPUT_ON_ERROR
        );

        $userId = '61f921834b4fc528013ef1e0';
        $namespace = 'glofox';
        $status = 'GRANTED';

        $accessModel = ClassRegistry::init('Access');
        $accessId = $accessModel->getLastInsertID();
        $access = $accessModel->findOrFail($accessId);

        self::assertEquals($userId, $access['Access']['user_id']);
        self::assertEquals($namespace, $access['Access']['namespace']);
        self::assertEquals($branchId, $access['Access']['branch_id']);
        self::assertEquals(Carbon::createFromTimeString($entryTime)->getTimestamp(), $access['Access']['entry_at']);
        self::assertTrue($access['Access']['valid_on_entry']);
        self::assertEquals($status, $access['Access']['status']);

        $bookingModel->clear();
        $booking = $bookingModel->findOrFail($bookingId);

        self::assertTrue($booking['Booking']['attended']);

        self::assertTrue($result['success']);
    }

    public function test_gantner_entry_fails_due_to_invalid_card_id_or_error_saving_access(): void
    {
        $branchId = (string) new MongoId('49a7011a05c677b9a916612a');
        $gantnerToken = 'gantner-token-123';
        $url = sprintf('/users/entry/%s/%s?gantner_token=%s', $branchId, $gantnerToken, $gantnerToken);
        $payload = [
            0 => [
                'card_id' => 'invalid-gantern-card-id',
                'entry_time' => Carbon::yesterday()->toDateTimeString(),
            ],
        ];

        $result = json_decode(
            $this->testAction($url, ['data' => $payload, 'method' => 'POST']),
            true,
            512,
            JSON_PARTIAL_OUTPUT_ON_ERROR
        );

        self::assertFalse($result['success']);
    }

    public function test_blocks_deleting_a_trainer_has_future_bookings(): void
    {
        $this->authenticateAsAdmin();

        $trainer = $this->fetchUser('62d8f0a7f592084274a9868e');

        $data = $trainer->toArray();

        unset($data['address']);
        unset($data['access_barcode']);
        unset($data['membership']['plan_code']);

        $data['active'] = false;

        $response = $this->testAction('/users/save', [
            'method' => 'POST',
            'data' => $data,
        ]);

        $response = json_decode($response, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        self::assertfalse($response->success);
        self::assertEquals(
            'YOU_CANNOT_DELETE_A_TRAINER_WHO_HAS_FUTURE_BOOKINGS',
            $response->message_code
        );
        self::assertEquals(
            'This trainer has future bookings',
            $response->message
        );
    }

    public function testItDoesNotAllowToFetchMembersForMember(): void
    {
        $this->authenticateAsMember();
        $data = [
            'namespace' => 'glofox',
            'branch_id' => '49a7011a05c677b9a916612a',
            'member_details' => 'a',
        ];

        $response = $this->testAction('/users/findMembersBySearchString', [
            'method' => 'POST',
            'data' => $data,
        ]);

        $response = json_decode($response, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        self::assertfalse($response->success);
        self::assertEquals('Unauthorized', $response->message);
    }

    public function testItDoesNotAllowToFetchMembersForGuests(): void
    {
        $this->authenticateAsGuest();
        $data = [
            'namespace' => 'glofox',
            'branch_id' => '49a7011a05c677b9a916612a',
            'member_details' => 'a',
        ];

        $response = $this->testAction('/users/findMembersBySearchString', [
            'method' => 'POST',
            'data' => $data,
        ]);

        $response = json_decode($response, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        self::assertfalse($response->success);
        self::assertEquals('Unauthorized', $response->message);
    }

    private function mockMemberUpdatedPublisherExpecting(
        array $payloadExpectations,
        array $correlationExpectations = []
    ): void {
        $publisher = Mockery::mock(UsersPublisher::class);
        $publisher
            ->shouldReceive('sendMemberUpdatedEvent')
            ->withArgs(
                fn (MemberUpdatedEventMeta $meta, MemberUpdatedEventPayload $payload) => $this->assertEventContents(
                    $meta,
                    $payload,
                    $payloadExpectations,
                    $correlationExpectations
                )
            );

        app()->instance(UsersPublisher::class, $publisher);
    }

    private function mockMemberAccessInfoEvent(
        array $payloadExpectations,
        array $correlationExpectations = [],
        bool $created = false
    ): void {
        $memberAccessInfoMethod = $created ? 'sendMemberAccessInfoCreatedEvent' : 'sendMemberAccessInfoUpdatedEvent';
        $publisher = Mockery::mock(AccessesPublisher::class);
        $publisher
            ->shouldReceive($memberAccessInfoMethod)
            ->withArgs(
                fn (MemberAccessInfoEventsMeta $meta, MemberAccessInfoEventsPayload $payload) => $this->assertEventContents(
                    $meta,
                    $payload,
                    $payloadExpectations,
                    $correlationExpectations
                )
            );

        app()->instance(AccessesPublisher::class, $publisher);
    }

    private function mockNewMemberAddedEventPublisherExpecting(
        array $payloadExpectations,
        array $correlationExpectations = []
    ): void {
        $publisher = Mockery::mock(UsersPublisher::class);
        $publisher
            ->shouldReceive('sendNewMemberAddedEvent')
            ->withArgs(
                fn (NewMemberAddedEventMeta $meta, NewMemberAddedEventPayload $payload) => $this->assertEventContents(
                    $meta,
                    $payload,
                    $payloadExpectations,
                    $correlationExpectations
                )
            );

        app()->instance(UsersPublisher::class, $publisher);

        $this->mockMemberAccessInfoEvent([], [], true);
    }

    private function assertEventContents(
        AbstractDomainEventMeta $meta,
        AbstractDomainEventPayload $payload,
        array $payloadExpectations,
        array $correlationExpectations
    ): bool {
        $correlation = $meta->correlation();
        $payload = $payload->jsonSerialize();

        foreach (Arr::dot($payloadExpectations) as $key => $expectation) {
            $this->assertSame(Arr::get($payload, $key), $expectation);
        }

        foreach (Arr::dot($correlationExpectations) as $key => $expectation) {
            $this->assertSame(Arr::get($correlation, $key), $expectation);
        }

        return true;
    }

    public function testMinimumAgeRestriction(): void
    {
        $email = '<EMAIL>';
        $branchId = '6364be7488385df39f3a7015';
        $this->createConsentFlaggerMock(false);

        $this->mockNewMemberAddedEventPublisherExpecting([
            'user' => [
                'email' => $email,
            ],
        ]);

        app()->forgetInstance(Auth::class);
        $user = $this->fetchUser('5e9ed06cd575e6003c38648d');
        $token = $this->loginAsUser($user);
        $this->currentToken = $token;
        $_SERVER['HTTP_AUTHORIZATION'] = $this->currentToken;

        // 1. test we can add a user under the age limit
        $payload = [
            'branch_id' => $branchId,
            'namespace' => 'minimumagebranch',
            'active' => true,
            'first_name' => 'John',
            'last_name' => 'Connor',
            'gender' => [
                'name' => 'M',
                'label' => 'MALE',
            ],
            'phone' => null,
            'email' => $email,
            'birth' => Carbon::now()->subYears(2)->getTimestamp(),
            'emergency_contact' => null,
            'receive_marketing' => null,
            'membership' => [
                'type' => 'payg',
            ],
            'device_os' => null,
            'device_app_version' => null,
            'expires_moment' => null,
            'strike' => 0,
            'image' => null,
            'lead_status' => 'MEMBER',
            'type' => 'MEMBER',
            'role' => 'MEMBER',
        ];

        // 1. test we cannot add a user with an age that is too young
        $result = $this->testAction("/users/save", [
            'data' => $payload,
            'method' => RequestAlias::METHOD_POST,
        ]);
        $result = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        self::assertFalse($result['success']);
        self::assertEquals('MINIMUM_AGE_CHECK_FAILED', $result['message_code']);

        // 2. check we cannot add a user with a birthday a few days from meeting requirements
        $payload['birth'] = Carbon::now()->subYears(15)->addDays(350)->getTimestamp();
        $result = $this->testAction("/users/save", [
            'data' => $payload,
            'method' => RequestAlias::METHOD_POST,
        ]);
        $result = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        self::assertFalse($result['success']);
        self::assertEquals('MINIMUM_AGE_CHECK_FAILED', $result['message_code']);

        // 3. test we can add a user over the age limit
        $payload['birth'] = Carbon::now()->subYears(20)->getTimestamp();
        $result = $this->testAction("/users/save", [
            'data' => $payload,
            'method' => RequestAlias::METHOD_POST,
        ]);
        $result = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        self::assertTrue($result['success']);
        self::assertNotEmpty($result['entity']);
        self::assertNotEmpty($result['entity']['_id']);
        self::assertEquals($email, $result['entity']['email']);
        app()->forgetInstance(ConsentFlagger::class);
    }

    /**
     * @dataProvider saveUserDataProvider
     * @param array $payload
     * @param Closure $assertion
     * @return void
     */
    public function testUserSave(array $payload, Closure $assertion): void
    {
        $this->authenticateAsAdmin();

        if (isset($payload['_id'])) {
            $this->mockMemberUpdatedPublisherExpecting([], [
                'memberId' => $payload['_id'],
            ]);
            $this->mockMemberAccessInfoEvent([], [
                'memberId' => $payload['_id'],
            ]);
        }


        $result = $this->testAction("/users/save", [
            'data' => $payload,
            'method' => RequestAlias::METHOD_POST,
        ]);
        $result = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        $assertion->call($this, $result);
    }

    public function saveUserDataProvider(): array
    {
        return [
            'When we are trying to update user\'s type (TRAINER -> ADMIN), then we should get an error when trying to change it to anything else' => [
                'payload' => [
                    '_id' => '633ad7b33e8c7958abc3e383',
                    'branch_id' => '49a7011a05c677b9a916612a',
                    'first_name' => 'John',
                    'last_name' => 'Connor',
                    'email' => '<EMAIL>',
                    'type' => 'ADMIN',
                ],
                function ($response) {
                    self::assertFalse($response['success']);
                    self::assertEquals('CANNOT_CHANGE_STAFF_TYPE', $response['message_code']);
                },
            ],
            'When we are trying to update user\'s type (TRAINER -> MEMBER), then we should get an error when trying to change it to anything else' => [
                'payload' => [
                    '_id' => '633ad7b33e8c7958abc3e383',
                    'branch_id' => '49a7011a05c677b9a916612a',
                    'first_name' => 'John',
                    'last_name' => 'Connor',
                    'email' => '<EMAIL>',
                    'type' => 'MEMBER',
                ],
                function ($response) {
                    self::assertFalse($response['success']);
                    self::assertEquals('CANNOT_CHANGE_STAFF_TYPE', $response['message_code']);
                },
            ],
            'When we are trying to update user/s info without changing the type, then we should be able to save user\'s data successfully' => [
                'payload' => [
                    '_id' => '633ad7b33e8c7958abc3e383',
                    'branch_id' => '49a7011a05c677b9a916612a',
                    'first_name' => 'Jane',
                    'last_name' => 'UniqueUpdate',
                    'email' => '<EMAIL>',
                    'type' => 'TRAINER',
                ],
                function ($response) {
                    self::assertTrue($response['success']);
                    self::assertEquals('UniqueUpdate', $response['entity']['last_name']);

                    // Check full name is updated together with the first and last name
                    self::assertEquals('Jane UniqueUpdate', $response['entity']['name']);
                },
            ],
            'When we are trying to update user/s info with duplicate access_barcode we should get an error that the barcode is duplicated' => [
                'payload' => [
                    '_id' => '633ad7b33e8c7958abc3e383',
                    'branch_id' => '49a7011a05c677b9a916612a',
                    'first_name' => 'John',
                    'last_name' => 'UniqueUpdate',
                    'email' => '<EMAIL>',
                    'access_barcode' => '123123321',
                    'type' => 'TRAINER',
                ],
                function ($response) {
                    self::assertFalse($response['success']);
                    self::assertEquals('ANOTHER_CLIENT_HAS_THIS_BARCODE', $response['message_code']);
                },
            ],
        ];
    }

    public function testUsersViewIsDeniedForGuests(): void
    {
        $this->loginAsGuest('test', '49a7011a05c677b9a916612a');

        $result = $this->testAction('/users/view', [
            'method' => Request::METHOD_GET,
        ]);

        $result = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        self::assertFalse($result['success']);
        self::assertEquals('ERROR_UNAUTHORIZED', $result['message_code']);
    }

    private function viewUsers(): void
    {
        $result = $this->testAction('/users/view', [
            'method' => Request::METHOD_GET,
        ]);

        $result = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        // valid response does not return success field
        self::assertFalse(isset($result['success']));
        // a valid response can be empty
        self::assertFalse(is_null($result));
    }

    public function testUsersViewIsAllowedForAdmins(): void
    {
        $this->authenticateAsAdmin();
        $this->viewUsers();
    }

    public function testUsersViewIsAllowedForSuperAdmins(): void
    {
        $this->authenticateAsSuperAdmin();
        $this->viewUsers();
    }

    public function testUsersViewIsAllowedForTrainers(): void
    {
        $this->authenticateAsTrainer();
        $this->viewUsers();
    }

    public function testUsersViewIsAllowedForReceptionists(): void
    {
        $this->authenticateAsReception();
        $this->viewUsers();
    }

    public function testAdminCannotSaveUserOnDifferentBranch(): void
    {
        $this->authenticateAsAdmin();

        app()->bind(UsersPublisher::class, function () {
            $publisher = Mockery::mock(UsersPublisher::class);
            $publisher->shouldReceive('sendMemberUpdatedEvent');

            return $publisher;
        });

        $response = $this->testAction('/users/save', [
            'data' => [
                // this user id is from a different branch as the current admin
                '_id' => '5b19330ea0d888945a164341',
                'first_name' => 'bad',
                'last_name' => 'admin',
                'email' => '<EMAIL>',
                'type' => UserType::MEMBER,
            ],
            'method' => Request::METHOD_POST,
        ]);
        $response = json_decode($response, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        $this->assertFalse($response['success']);
        $this->assertEquals('YOU_ARE_NOT_ALLOWED_TO_UPDATE_THIS_USER', $response['message_code']);
    }

    public function testGetExpiringMembersWithInvalidParameters(): void
    {
        $this->authenticateAsAdmin();
        $data = [
            'start' => 'invalid_value',
            'end' => 'invalid_value',
        ];

        $response = $this->testAction('/2.0/members/expiring', ['data' => $data, 'method' => Request::METHOD_GET]);
        $response = json_decode($response, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        $this->assertFalse($response['success']);
        $this->assertEquals('The start must be a number., The end must be a number.', $response['message_code']);
    }

    public function testInValidChildAccounts(): void
    {
        $this->authenticateAsAdmin();
        $this->createFeatureFlagMock(false);

        $payload = [
            'first_name' => 'John',
            'last_name' => 'Child',
            'no_password' => 1,
            'parent_id' => '65cb60da3bc474074f4e3a85',
            'birth' => '2000-10-10',
            'use_parent_email' => true,
        ];

        $childLimitValidator = Mockery::mock(ChildLimitValidator::class)->makePartial();
        $childLimitValidator->shouldReceive('getParentChildCount')->andReturn(41);
        app()->instance(ChildLimitValidator::class, $childLimitValidator);

        $result = $this->testAction('/2.0/register', [
            'data' => json_encode($payload, JSON_PARTIAL_OUTPUT_ON_ERROR),
            'method' => RequestAlias::METHOD_POST,
        ]);
        $result = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        self::assertEquals('CHILD_ACCOUNT_LIMIT_EXCEEDED', $result['message_code']);
        app()->forgetInstance(ChildLimitValidator::class);
    }

    public function testValidChildAccounts(): void
    {
        $this->authenticateAsAdmin();

        $payload = [
            'first_name' => 'John',
            'last_name' => 'Child',
            'no_password' => 1,
            'parent_id' => '65cbd096f3cd2908596a5021',
            'birth' => '2000-10-10',
            'email' => '<EMAIL>',

        ];
        $this->createFeatureFlagMock(false);
        $this->createConsentFlaggerMock(false);
        $eventsPublisherMock = Mockery::mock(UsersPublisher::class);
        $eventsPublisherMock->shouldReceive('sendNewMemberAddedEvent');
        app()->instance(UsersPublisher::class, $eventsPublisherMock);

        $this->mockMemberAccessInfoEvent([], [], true);

        $result = $this->testAction('/2.0/register', [
            'data' => json_encode($payload, JSON_PARTIAL_OUTPUT_ON_ERROR),
            'method' => RequestAlias::METHOD_POST,
        ]);

        $result = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        self::assertTrue($result['success']);
        app()->forgetInstance(EventsPublisher::class);
        app()->forgetInstance(ConsentFlagger::class);
        app()->forgetInstance(FeatureFlagInterface::class);
    }
    private function mockFlag(Flag $flag): void
    {
        $mockFlagger = Mockery::mock(Flagger::class)
            ->shouldReceive('withFlag')
            ->with($flag)
            ->shouldReceive('hasByBranchId')
            ->andReturnTrue()
            ->getMock();

        app()->instance(Flagger::class, $mockFlagger);
    }

    private function createHoneycombTrackerMock(): void
    {
        $honeycombTracker = Mockery::mock(HoneycombTracker::class);
        $honeycombTracker->shouldNotHaveBeenCalled();
        app()->instance(HoneycombTracker::class, $honeycombTracker);
    }

    private function createHoneycombTracker(): void
    {
        $honeyCombTracker = \Mockery::mock(HoneycombTracker::class);
        $honeyCombTracker->shouldReceive('track');
        app()->instance(HoneycombTracker::class, $honeyCombTracker);
    }

    /**
     * @dataProvider registerUserWithLeadsObjectDataProvider
     * @throws \JsonException
     */
    public function testCreateRegisterUserWithLeadsObject(
        bool $featureFlagEnabled,
        ?array $leadsData,
        string $email,
        ?string $expectedExceptionMessage = null,
            bool $isLeadUser = false
    ): void {
        $admin = $this->fetchUser('59a7011a05c677bda916612a');
        $this->loginAsUser($admin);
        $this->createFeatureFlagMock($featureFlagEnabled);
        $this->createConsentFlaggerMock(false);

        $data = $isLeadUser
            ? [
                'first_name' => 'John',
                'last_name' => 'isLeadUser',
                'email' => $email,
                'gender' => 'M',
                'avatarImg' => '',
                'phone' => '64123123123',
                'birth' => '2000-10-10',
                'emergency_contact' => '',
                'no_password' => 1,
                'avatar' => '',
                'leads' => $leadsData,
            ]
            : [
                'first_name' => 'John',
                'last_name' => 'Child',
                'no_password' => 1,
                'parent_id' => '59a7011a05c677bda916612a',
                'birth' => '2000-10-10',
                'use_parent_email' => true,
                'email' => $email,
            ];

        if (!$isLeadUser && !empty($leadsData)) {
            $data['leads'] = $leadsData;
        }

        $options = [
            'data' => $data,
            'method' => 'post',
        ];
        app()->bind(UsersPublisher::class, function () {
            $publisher = Mockery::mock(UsersPublisher::class);
            $publisher->shouldReceive('sendNewMemberAddedEvent');
            return $publisher;
        });

        app()->bind(AccessesPublisher::class, function () {
            $publisher = Mockery::mock(AccessesPublisher::class);
            $publisher->shouldReceive('sendMemberAccessInfoCreatedEvent');
            return $publisher;
        });
        $updateLeadMarketingDataMock = Mockery::mock(UpdateLeadSourcesData::class);
        app()->instance(UpdateLeadSourcesData::class, $updateLeadMarketingDataMock);

        if (!empty($leadsData) && empty($expectedExceptionMessage)) {
            $updateLeadMarketingDataMock->shouldReceive('execute')
                ->once()
                ->with(Mockery::on(static fn($params) => $params->marketingSource() === ($leadsData['marketing_source'] ?? null) &&
                    $params->contactSource() === ($leadsData['contact_source'] ?? null)));
        } else {
            $updateLeadMarketingDataMock->shouldNotReceive('execute');
        }

        $result = $this->testAction('/2.0/register', $options);
        $result = json_decode($result, null, 512, JSON_THROW_ON_ERROR);

        if ($expectedExceptionMessage !== null) {
            $this->assertEquals($result->message, $expectedExceptionMessage);
        } else {
            self::assertTrue($result->success);

            if (!empty($leadsData)) {
                self::assertObjectHasAttribute('leads', $result->user);
                $this->assertEquals($result->user->leads, (object)$leadsData);
            } else {

                self::assertFalse(property_exists($result->user, 'leads'));
            }
        }

        app()->forgetInstance(UpdateLeadSourcesData::class);
        app()->forgetInstance(FeatureFlagInterface::class);
        app()->forgetInstance(ConsentFlagger::class);
    }

    private function createRoamingMemberRestrcitedProfileFlaggerMock()
    {
        $isRoaming = Mockery::mock(FeatureFlagInterface::class);
        $isRoaming
            ->shouldReceive('withFlag')
            ->with(Flag::IS_ROAMING_MEMBERS_RESTRICTED_PROFILE_ENABLED())
            ->andReturnSelf()
            ->shouldReceive('hasByBranchId')
            ->andReturnFalse();
        app()->instance(FeatureFlagInterface::class, $isRoaming);
    }

    public function registerUserWithLeadsObjectDataProvider(): array
    {
        $baseEmail = 'newchildaccount+';
        $domain = '@glofox.com';

        return [
            'When marketing source is valid, And FF is enabled, child account is created with leads Object ' => [
                true,
                'leads' => [
                    'marketing_source' => 'Join Form',
                ],
                $baseEmail . uniqid('', true) . $domain,
                null,
                false
            ],
            'When marketing source is invalid, child account is not created and exception is thrown' => [
                true,
                'leads' => [
                    'marketing_source' => 'Invalid value',
                ],
                $baseEmail . uniqid('', true) . $domain,

                'Leads Marketing source not allowed - source: Invalid value',
                false
            ],
            'When FF is disabled and marketing_source or contact_source is valid, child account is created without leads object ' => [
                false,
                [],
                $baseEmail . uniqid('', true) . $domain,
                null,
                false
            ],

            'When marketing source and contact source are provided, And FF is enabled, leads object is available' => [
                true,
                [
                    'marketing_source' => 'Join Form',
                    'contact_source' => 'Referral',
                ],
                $baseEmail . uniqid('', true) . $domain,
                null,
                false
            ],
            'Normal user creation with valid marketing source and contact source,WHEN FF is on ' => [
                true,
                [
                    'marketing_source' => 'Join Form',
                    'contact_source' => 'Referral',
                ],
                '<EMAIL>',
                null,
                true,
            ],
            'Normal user creation with valid payload and leads object,WHEN FF is on ' => [
                true,
                [
                    'marketing_source' => 'Join Form',
                    'contact_source' => 'Referral',
                    'marketing_source_details' => 'Details',
                ],
                '<EMAIL>',
                null,
                true,
            ],
            'Normal user creation with valid payload and invalid lead object, marketing_source_details exist without marketing_source,WHEN FF is on ' => [
                true,
                [
                    'contact_source' => 'Referral',
                    'marketing_source_details' => 'Details',
                ],
                '<EMAIL>',
                'Marketing source details are not allowed without a marketing source',
                false,
            ],
            'Normal user creation with valid payload and invalid lead object, marketing_source_details less then 3 characters WHEN FF is on ' => [
                true,
                [
                    'marketing_source' => 'Join Form',
                    'contact_source' => 'Referral',
                    'marketing_source_details' => 'as',
                ],
                '<EMAIL>',
                'The leads.marketing source details must be at least 3 characters.',
                false,
            ],
            'Normal user creation with valid payload and invalid lead object, marketing_source_details higher then 200 characters WHEN FF is on ' => [
                true,
                [
                    'marketing_source' => 'Join Form',
                    'contact_source' => 'Referral',
                    'marketing_source_details' => 'H4%D0eEkLdXHQe(:Up)$6nV]5jKXd%um.(:wM0T-b/yZdgSMuNL.menuqK8vaScfbDd@F:78afGDJrcg-MARK2Zzj$G}F(HGV)9REutYpb?)}1=CU9J1bg&{6_Jf7j,vky}==B%Sp2[5C3dmZeS(zv4C{Li30(/w/)FC8ibRk&qxG2iV4vZ.h5fXn8X2,JK*L$d7xruZ,',
                ],
                '<EMAIL>',
                'The leads.marketing source details may not be greater than 200 characters.',
                false,
            ],
        ];
    }
    /**
     * @dataProvider provideCreationUserAttempts
     */
    public function test_staff_creation_permission_restrictions_by_role(string $userId, string $typeToCreate, string $expectedMessage, bool $success, bool $isGlofoxStaff): void
    {
        if ($isGlofoxStaff) {
            $this->authenticateAsGlofoxStaff($userId);
        } else {
            $this->loginAsUser($this->fetchUser($userId));
        }

        if ($success) {
            $this->mockInstancesToCreateUser();
        }

        $data = [
            'first_name' => 'foo',
            'last_name' => 'bar',
            'email' => '<EMAIL>',
            'active' => true,
            'type' => $typeToCreate,
        ];

        $result = $this->testAction('/users/save', [
            'method' => 'POST',
            'data' => $data,
        ]);
        $result = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        if ($success) {
            self::assertTrue($result['success']);
        }
        else {
            self::assertFalse($result['success']);
            self::assertEquals($expectedMessage, $result['message']);
        }
    }

    public static function provideCreationUserAttempts(): array
    {
        return [
            'Receptionist creates SuperAdmin' => [
                'userId' => '59a7011a05c677bda916619b',
                'typeToCreate' => \UserType::SUPERADMIN,
                'expectedMessage' => 'ONLY_SUPER_ADMIN_CAN_CREATE_SUPERADMIN',
                'success' => false,
                'isGlofoxStaff' => false,
            ],
            'Receptionist creates Admin' => [
                'userId' => '59a7011a05c677bda916619b',
                'typeToCreate' => \UserType::ADMIN,
                'expectedMessage' => 'ONLY_SUPER_ADMIN_OR_ADMIN_CAN_CREATE_STAFF',
                'success' => false,
                'isGlofoxStaff' => false,
            ],
            'Receptionist creates Receptionist' => [
                'userId' => '59a7011a05c677bda916619b',
                'typeToCreate' => \UserType::RECEPTIONIST,
                'expectedMessage' => 'ONLY_SUPER_ADMIN_OR_ADMIN_CAN_CREATE_STAFF',
                'success' => false,
                'isGlofoxStaff' => false,
            ],
            'Receptionist creates Trainer' => [
                'userId' => '59a7011a05c677bda916619b',
                'typeToCreate' => \UserType::TRAINER,
                'expectedMessage' => 'ONLY_SUPER_ADMIN_OR_ADMIN_CAN_CREATE_STAFF',
                'success' => false,
                'isGlofoxStaff' => false,
            ],
            'Receptionist creates Member' => [
                'userId' => '59a7011a05c677bda916619b',
                'typeToCreate' => \UserType::MEMBER,
                'expectedMessage' => '',
                'success' => true,
                'isGlofoxStaff' => false,
            ],
            'Trainer creates SuperAdmin' => [
                'userId' => '58568a8fa875ab19530041a7',
                'typeToCreate' => \UserType::SUPERADMIN,
                'expectedMessage' => 'ONLY_SUPER_ADMIN_CAN_CREATE_SUPERADMIN',
                'success' => false,
                'isGlofoxStaff' => false,
            ],
            'Trainer creates Admin' => [
                'userId' => '58568a8fa875ab19530041a7',
                'typeToCreate' => \UserType::ADMIN,
                'expectedMessage' => 'ONLY_SUPER_ADMIN_OR_ADMIN_CAN_CREATE_STAFF',
                'success' => false,
                'isGlofoxStaff' => false,
            ],
            'Trainer creates Trainer' => [
                'userId' => '58568a8fa875ab19530041a7',
                'typeToCreate' => \UserType::TRAINER,
                'expectedMessage' => 'ONLY_SUPER_ADMIN_OR_ADMIN_CAN_CREATE_STAFF',
                'success' => false,
                'isGlofoxStaff' => false,
            ],
            'Trainer creates Receptionist' => [
                'userId' => '58568a8fa875ab19530041a7',
                'typeToCreate' => \UserType::RECEPTIONIST,
                'expectedMessage' => 'ONLY_SUPER_ADMIN_OR_ADMIN_CAN_CREATE_STAFF',
                'success' => false,
                'isGlofoxStaff' => false,
            ],
            'Trainer creates Member' => [
                'userId' => '58568a8fa875ab19530041a7',
                'typeToCreate' => \UserType::MEMBER,
                'expectedMessage' => '',
                'success' => true,
                'isGlofoxStaff' => false,
            ],
            'Admin creates SuperAdmin' => [
                'userId' => '59a7011a05c677bda916612a',
                'typeToCreate' => \UserType::SUPERADMIN,
                'expectedMessage' => 'ONLY_SUPER_ADMIN_CAN_CREATE_SUPERADMIN',
                'success' => false,
                'isGlofoxStaff' => false,
            ],
            'Admin creates Admin' => [
                'userId' => '59a7011a05c677bda916612a',
                'typeToCreate' => \UserType::ADMIN,
                'expectedMessage' => '',
                'success' => true,
                'isGlofoxStaff' => false,
            ],
            'Admin creates Trainer' => [
                'userId' => '59a7011a05c677bda916612a',
                'typeToCreate' => \UserType::TRAINER,
                'expectedMessage' => '',
                'success' => true,
                'isGlofoxStaff' => false,
            ],
            'Admin creates Receptionist' => [
                'userId' => '59a7011a05c677bda916612a',
                'typeToCreate' => \UserType::RECEPTIONIST,
                'expectedMessage' => '',
                'success' => true,
                'isGlofoxStaff' => false,
            ],
            'Admin creates Member' => [
                'userId' => '59a7011a05c677bda916612a',
                'typeToCreate' => \UserType::MEMBER,
                'expectedMessage' => '',
                'success' => true,
                'isGlofoxStaff' => false,
            ],
            'SuperAdmin creates SuperAdmin' => [
                'userId' => '59a7011a05c677bda916619a',
                'typeToCreate' => \UserType::SUPERADMIN,
                'expectedMessage' => '',
                'success' => true,
                'isGlofoxStaff' => false,
            ],
            'SuperAdmin creates Admin' => [
                'userId' => '59a7011a05c677bda916619a',
                'typeToCreate' => \UserType::ADMIN,
                'expectedMessage' => '',
                'success' => true,
                'isGlofoxStaff' => false,
            ],
            'SuperAdmin creates Receptionist' => [
                'userId' => '59a7011a05c677bda916619a',
                'typeToCreate' => \UserType::RECEPTIONIST,
                'expectedMessage' => '',
                'success' => true,
                'isGlofoxStaff' => false,
            ],
            'SuperAdmin creates Trainer' => [
                'userId' => '59a7011a05c677bda916619a',
                'typeToCreate' => \UserType::TRAINER,
                'expectedMessage' => '',
                'success' => true,
                'isGlofoxStaff' => false,
            ],
            'SuperAdmin creates Member' => [
                'userId' => '59a7011a05c677bda916619a',
                'typeToCreate' => \UserType::MEMBER,
                'expectedMessage' => '',
                'success' => true,
                'isGlofoxStaff' => false,
            ],
            'GlofoxStaff creates SuperAdmin' => [
                'userId' => '59a7011a05c677bda916612a',
                'typeToCreate' => \UserType::SUPERADMIN,
                'expectedMessage' => 'ONLY_SUPER_ADMIN_CAN_CREATE_SUPERADMIN',
                'success' => false,
                'isGlofoxStaff' => true,
            ],
            'GlofoxStaff creates Admin' => [
                'userId' => '59a7011a05c677bda916612a',
                'typeToCreate' => \UserType::ADMIN,
                'expectedMessage' => '',
                'success' => true,
                'isGlofoxStaff' => true,
            ],
            'GlofoxStaff creates Receptionist' => [
                'userId' => '59a7011a05c677bda916612a',
                'typeToCreate' => \UserType::RECEPTIONIST,
                'expectedMessage' => '',
                'success' => true,
                'isGlofoxStaff' => true,
            ],
            'GlofoxStaff creates Trainer' => [
                'userId' => '59a7011a05c677bda916612a',
                'typeToCreate' => \UserType::TRAINER,
                'expectedMessage' => '',
                'success' => true,
                'isGlofoxStaff' => true,
            ],
            'GlofoxStaff creates Member' => [
                'userId' => '59a7011a05c677bda916612a',
                'typeToCreate' => \UserType::MEMBER,
                'expectedMessage' => '',
                'success' => true,
                'isGlofoxStaff' => true,
            ],
        ];
    }

     /**
     * @dataProvider provideSaveUserAttempts
     */
    public function test_staff_save_user_roaming_branch_cross_tenant(array $payload,  Closure $assertion): void
    {
        $this->authenticateAsAdmin();
        if (isset($payload['_id'])) {
            $this->mockMemberUpdatedPublisherExpecting([], [
                'memberId' => $payload['_id'],
            ]);
            $this->mockMemberAccessInfoEvent([], [
                'memberId' => $payload['_id'],
            ]);
        }


        $result = $this->testAction("/users/save", [
            'data' => $payload,
            'method' => RequestAlias::METHOD_POST,
        ]);
        $result = json_decode($result, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        $assertion->call($this, $result);
    }

    public static function provideSaveUserAttempts(): array
    {
        return [
            'When we are trying to update user\'s type (ADMIN -> ADMIN), targeting unauthorized roaming branches, then we should get an unauthorized error' => [
                'payload' => [
                    '_id' => '633ad7b33e8c7958abc3e383',
                    'branch_id' => '49a7011a05c677b9a916612a',
                    'branches' => [
                        '49a7011a05c677b9a916612a',
                        '49a7011a05c677b9a916612b',
                    ],
                    'first_name' => 'John',
                    'last_name' => 'Connor',
                    'email' => '<EMAIL>',
                    'type' => 'ADMIN',
                ],
                function ($response) {
                    self::assertFalse($response['success']);
                    self::assertEquals('UNAUTHORIZED_SAVE_USER', $response['message_code']);
                },
            ],
            'When we are trying to update user\'s type (ADMIN -> ADMIN), targeting valid roaming branches, then we should get an success message' => [
                'payload' => [
                    '_id' => '6506bcf0b511dd853f0e36b1',
                    'branch_id' => '49a7011a05c677b9a916612a',
                    'branches' => [
                        '49a7011a05c677b9a916612a',
                    ],
                    'first_name' => 'John',
                    'last_name' => 'Connor',
                    'email' => '<EMAIL>',
                    'type' => 'ADMIN',
                ],
                function ($response) {
                    self::assertTrue($response['success']);
                    self::assertEquals('ENTITY_SAVE_SUCCESS', $response['message_code']);
                },
            ],
            'When we are trying to update user\'s type (ADMIN -> ADMIN), not targeting roaming branches, then we should get an success message' => [
                'payload' => [
                    '_id' => '6506bcf0b511dd853f0e36b1',
                    'branch_id' => '49a7011a05c677b9a916612a',
                    'first_name' => 'John',
                    'last_name' => 'Connor',
                    'email' => '<EMAIL>',
                    'type' => 'ADMIN',
                ],
                function ($response) {
                    self::assertTrue($response['success']);
                    self::assertEquals('ENTITY_SAVE_SUCCESS', $response['message_code']);
                },
            ],
        ];
    }
    
     /**
     * @dataProvider unauthorizedUserUpdateProvider
     */
    public function testUnauthorizedUserUpdate(
        string $loggedInUserId,
        string $targetUserId,
        string $targetUserType,
        string $expectedMessageCode
    ): void {
        $this->loginAsUser($this->fetchUser($loggedInUserId));

        app()->bind(UsersPublisher::class, function () {
            $publisher = Mockery::mock(UsersPublisher::class);
            $publisher->shouldReceive('sendMemberUpdatedEvent');
            return $publisher;
        });

        $response = $this->testAction('/users/save', [
            'data' => [
                '_id' => $targetUserId,
                'first_name' => 'bad',
                'last_name' => 'fake_name',
                'email' => '<EMAIL>',
                'type' => $targetUserType,
            ],
            'method' => Request::METHOD_POST,
        ]);

        $response = json_decode($response, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        $this->assertFalse($response['success']);
        $this->assertEquals($expectedMessageCode, $response['message_code']);
        app()->forgetInstance(UsersPublisher::class);
    }

    public function unauthorizedUserUpdateProvider(): array
    {
        return [
            'admin trying to update superadmin' => [
                'loggedInUserId' => '59a7011a05c677bda916612a',
                'targetUserId' => '59a7011a05c677bda916619a',
                'targetUserType' => UserType::SUPERADMIN,
                'expectedMessageCode' => 'ONLY_SUPER_ADMIN_CAN_UPDATE_SUPERADMIN',
            ],
            'trainer trying to update SuperAdmin' => [
                'loggedInUserId' => '59a7011a05c677bda916612c',
                'targetUserId' => '59a7011a05c677bda916619a',
                'targetUserType' => UserType::SUPERADMIN,
                'expectedMessageCode' => 'YOU_ARE_NOT_ALLOWED_TO_UPDATE_THIS_USER',
            ],
            'trainer trying to update admin' => [
                'loggedInUserId' => '59a7011a05c677bda916612c',
                'targetUserId' => '59a7011a05c677bda916612d',
                'targetUserType' => UserType::ADMIN,
                'expectedMessageCode' => 'YOU_ARE_NOT_ALLOWED_TO_UPDATE_THIS_USER',
            ],
            'trainer trying to update receptionist' => [
                'loggedInUserId' => '59a7011a05c677bda916612c',
                'targetUserId' => '59a7011a05c677bda916619b',
                'targetUserType' => UserType::RECEPTIONIST,
                'expectedMessageCode' => 'YOU_ARE_NOT_ALLOWED_TO_UPDATE_THIS_USER',
            ],
            'trainer trying to update other trainer' => [
                'loggedInUserId' => '59a7011a05c677bda916612c',
                'targetUserId' => '58568a8fa875ab19530041a7',
                'targetUserType' => UserType::RECEPTIONIST,
                'expectedMessageCode' => 'YOU_ARE_NOT_ALLOWED_TO_UPDATE_THIS_USER',
            ],
            'trainer trying to update member' => [
                'loggedInUserId' => '59a7011a05c677bda916612c',
                'targetUserId' => '59a7011a05c677bda916623f',
                'targetUserType' => UserType::MEMBER,
                'expectedMessageCode' => 'YOU_ARE_NOT_ALLOWED_TO_UPDATE_THIS_USER',
            ],
            'receptionist trying to update SuperAdmin' => [
                'loggedInUserId' => '59a7011a05c677bda916619b',
                'targetUserId' => '59a7011a05c677bda916612a',
                'targetUserType' => UserType::SUPERADMIN,
                'expectedMessageCode' => 'YOU_ARE_NOT_ALLOWED_TO_UPDATE_THIS_USER',
            ],
            'receptionist trying to update admin' => [
                'loggedInUserId' => '59a7011a05c677bda916619b',
                'targetUserId' => '59a7011a05c677bda916612d',
                'targetUserType' => UserType::ADMIN,
                'expectedMessageCode' => 'YOU_ARE_NOT_ALLOWED_TO_UPDATE_THIS_USER',
            ],
            'receptionist trying to update trainer' => [
                'loggedInUserId' => '59a7011a05c677bda916619b',
                'targetUserId' => '59a7011a05c677bda916612c',
                'targetUserType' => UserType::TRAINER,
                'expectedMessageCode' => 'YOU_ARE_NOT_ALLOWED_TO_UPDATE_THIS_USER',
            ],
            'receptionist trying to update other receptionist' => [
                'loggedInUserId' => '59a7011a05c677bda916619b',
                'targetUserId' => '58568b8fa875ab1953123124',
                'targetUserType' => UserType::RECEPTIONIST,
                'expectedMessageCode' => 'YOU_ARE_NOT_ALLOWED_TO_UPDATE_THIS_USER',
            ],
        ];
    }

    private function mockGoPaygService(): void
    {
        $goPaygService = Mockery::mock(GoPaygService::class);
        $goPaygService->shouldReceive('execute');

        app()->instance(GoPaygService::class, $goPaygService);
    }

    private function mockAcceptUserTermsService(): void
    {
        $userTermsServiceMock = Mockery::mock(AcceptUserTermsService::class);
        $userTermsServiceMock->shouldReceive('execute');

        app()->instance(AcceptUserTermsService::class, $userTermsServiceMock);
    }
}

