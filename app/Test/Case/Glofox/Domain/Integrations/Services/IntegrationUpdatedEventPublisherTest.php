<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\Integrations\Services;

use Glofox\Domain\AsyncEvents\Events\IntegrationUpdatedEventMeta;
use Glofox\Domain\AsyncEvents\Events\IntegrationUpdatedEventPayload;
use Glofox\Domain\Integrations\Services\IntegrationUpdatedEventPublisher;
use Glofox\Eventkit\Publisher\DomainEventPublisher;

\App::import('Test/Case', 'GlofoxTestCase');

class IntegrationUpdatedEventPublisherTest extends \GlofoxTestCase
{
    public function test_it_should_send_mailchimp_bulk_event(): void
    {
        $domainEventPublisher = \Mockery::mock(DomainEventPublisher::class);
        $domainEventPublisher->shouldReceive('publish');

        $meta = \Mockery::mock(IntegrationUpdatedEventMeta::class);
        $payload = \Mockery::mock(IntegrationUpdatedEventPayload::class);

        $publisher = new IntegrationUpdatedEventPublisher($domainEventPublisher);
        $publisher->sendIntegrationUpdatedEvent($meta, $payload);
    }
}