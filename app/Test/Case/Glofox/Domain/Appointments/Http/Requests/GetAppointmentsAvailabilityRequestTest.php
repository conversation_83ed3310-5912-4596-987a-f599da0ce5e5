<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\Appointments\Http\Requests;

use CakeTestCases\Glofox\Domain\Users\Traits\AuthenticateUsersTrait;
use CakeTestCases\GlofoxRequestTestCase;
use CakeTestCases\GlofoxRequestTestParams;
use Glofox\Domain\Appointments\Http\Requests\GetAppointmentsAvailabilityRequest;

class GetAppointmentsAvailabilityRequestTest extends GlofoxRequestTestCase
{
    use AuthenticateUsersTrait;

    public array $fixtures = [
        'app.branch',
        'app.user',
    ];

    private const BRANCH_ID = '49a7011a05c677b9a916612a';

    public function requestClassName(): string
    {
        return GetAppointmentsAvailabilityRequest::class;
    }

    public function tearDown(): void
    {
        parent::tearDown();

        \Mockery::close();
    }

    /**
     * @dataProvider testAuthorizeDataProvider
     */
    public function testAuthorize(\Closure $authenticate, bool $expectedResult): void
    {
        $authenticate->call($this);

        $this->validateAuthorization(
            new GlofoxRequestTestParams(
                sprintf('/2.1/branches/%s/appointments-availability', self::BRANCH_ID),
                'GET',
            ),
            $expectedResult,
        );
    }

    public function testAuthorizeDataProvider(): array
    {
        return [
            '1.  When user is authenticated as a Member, Then it should not pass.' => [
                'authorize' => fn () => $this->authenticateAsMember(),
                'expectedResult' => false,
            ],
            '2.  When user is authenticated as a no admin Staff, Then it should not pass.' => [
                'authorize' => fn () => $this->authenticateAsReception(),
                'expectedResult' => false,
            ],
            '3.  When user is authenticated as an Admin, Then it should pass.' => [
                'authorize' => fn () => $this->authenticateAsAdmin(),
                'expectedResult' => true,
            ],
        ];
    }

    /**
     * @dataProvider testRulesDataProvider
     */
    public function testRules(string $queryParams, array $expectedErrors): void
    {
        $this->authenticateAsAdmin();

        $this->validateRules(
            new GlofoxRequestTestParams(
                sprintf('/2.1/branches/%s/appointments-availability?%s', self::BRANCH_ID, $queryParams),
            ),
            $expectedErrors
        );
    }

    public function testRulesDataProvider(): array
    {
        return [
            '1. When request is well formed, Then it should return no error.' => [
                'queryParams' => http_build_query([
                    'trainer-id' => '67445de940789aa6d395538e',
                    'start-time' => '2022-01-01 09:00:00',
                    'finish-time' => '2022-01-01 23:00:00',
                ]),
                'expectedErrors' => [],
            ],
            '2. When request has no parameter, Then it should return no error.' => [
                'queryParams' => '',
                'expectedErrors' => [],
            ],
            '3. When trainer id is not valid, Then it should return an error.' => [
                'queryParams' => http_build_query([
                    'trainer-id' => 'invalid',
                ]),
                'expectedErrors' => [
                    'INVALID_TRAINER_ID'
                ],
            ],
            '4. When start time is not valid, Then it should return an error.' => [
                'queryParams' => http_build_query([
                    'start-time' => '1732533683',
                ]),
                'expectedErrors' => [
                    'INVALID_START_TIME_FORMAT'
                ],
            ],
            '5. When finish time is before start time, Then it should return an error.' => [
                'queryParams' => http_build_query([
                    'start-time' => '2022-01-01 23:00:00',
                    'finish-time' => '2022-01-01 09:00:00',
                ]),
                'expectedErrors' => [
                    'FINISH_TIME_MUST_BE_AFTER_START_TIME'
                ],
            ],
            '6. When finish time is equals as start time, Then it should return an error.' => [
                'queryParams' => http_build_query([
                    'start-time' => '2022-01-01 15:00:00',
                    'finish-time' => '2022-01-01 15:00:00',
                ]),
                'expectedErrors' => [
                    'FINISH_TIME_MUST_BE_AFTER_START_TIME'
                ],
            ],
            '7. When finish time is sent without start time, Then it should return an error.' => [
                'queryParams' => http_build_query([
                    'finish-time' => '2024-11-01',
                ]),
                'expectedErrors' => [
                    'START_TIME_IS_REQUIRED_WHEN_FINISH_TIME_IS_PROVIDED'
                ],
            ],
            '8. When start time is sent without finish time, Then it should return an error.' => [
                'queryParams' => http_build_query([
                    'start-time' => '2024-11-01',
                ]),
                'expectedErrors' => [
                    'FINISH_TIME_IS_REQUIRED_WHEN_START_TIME_IS_PROVIDED'
                ],
            ],
        ];
    }
}
