<?php

namespace CakeTestCases\Glofox\Domain\FeatureFlags\Http\Clients;

use CakeTestCases\Glofox\Domain\Logger\Traits\MockedLoggerTrait;
use Glofox\Domain\FeatureFlags\Http\Clients\LaunchDarklyHttpClient;
use GuzzleHttp\ClientInterface;
use Psr\Http\Message\ResponseInterface;

\App::import('Test/Case', 'GlofoxTestCase');

class LaunchDarklyHttpClientTest extends \GlofoxTestCase
{
    use MockedLoggerTrait;

    public $fixtures = [];

    /** @var ClientInterface */
    private $mockedClient;
    private ?LaunchDarklyHttpClient $httpClient = null;

    /** @var ResponseInterface */
    private $mockedResponse;

    public function setUp()
    {
        parent::setUp();

        $this->mockedClient = \Mockery::mock(ClientInterface::class);
        $this->httpClient = new LaunchDarklyHttpClient('test', $this->loggerMock, $this->mockedClient);
        $this->mockedResponse = \Mockery::mock(ResponseInterface::class);
        $this->mockedResponse
            ->shouldReceive('getBody')
            ->andReturnSelf()
            ->getMock()
            ->shouldReceive('getStatusCode')
            ->andReturn(200);
    }

    public function tearDown()
    {
        parent::tearDown();

        \Mockery::close();
    }

    public function test_it_adds_targets_to_flag_properly(): void
    {
        $flagKey = 'test-flag';
        $env = 'test-env';
        $targets = [
            (string) new \MongoId(),
            (string) new \MongoId(),
            (string) new \MongoId(),
        ];

        $body = $this->getRequestBody($env, $targets);

        $options = [
            'body' => $body,
        ];

        $this->mockedResponse
            ->shouldReceive('getContents')
            ->andReturn(
                json_encode([
                    'environments' => [
                        $env => [
                            'targets' => [],
                        ],
                    ],
                ], JSON_THROW_ON_ERROR)
            );

        $this->mockedClient
            ->shouldReceive('request')
            ->withArgs(function (...$params) use ($options) {
                if ($params[0] === 'PATCH') {
                    self::assertEquals($params[2], $options);
                }

                return true;
            })
            ->andReturn($this->mockedResponse);

        app()->instance(ResponseInterface::class, $this->mockedResponse);

        $this->httpClient->addTargets($env, $flagKey, $targets);

        app()->forgetInstance(ResponseInterface::class);
    }

    public function test_it_removes_targets_from_flag_properly(): void
    {
        $flagKey = 'test-flag';
        $env = 'test-env';
        $targets = [
            (string) new \MongoId(),
            (string) new \MongoId(),
            (string) new \MongoId(),
        ];

        $body = $this->getRequestBody($env, $targets);

        $options = [
            'body' => $body,
        ];

        $this->mockedResponse
            ->shouldReceive('getContents')
            ->andReturn(
                json_encode([
                    'environments' => [
                        $env => [
                            'targets' => [
                                [
                                    'variation' => 0,
                                    'values' => $targets,
                                ],
                            ],
                        ],
                    ],
                ], JSON_THROW_ON_ERROR)
            );

        $this->mockedClient
            ->shouldReceive('request')
            ->withArgs(function (...$params) use ($options) {
                if ($params[0] === 'PATCH') {
                    self::assertEquals($params[2], $options);
                }

                return true;
            })
            ->andReturn($this->mockedResponse);

        app()->instance(ResponseInterface::class, $this->mockedResponse);

        $this->httpClient->addTargets($env, $flagKey, $targets);

        app()->forgetInstance(ResponseInterface::class);
    }

    public function test_it_checks_flag_is_enabled_for_a_target(): void
    {
        $flagKey = 'test-flag';
        $env = 'test-env';
        $target = (string) new \MongoId();

        $this->mockedResponse
            ->shouldReceive('getContents')
            ->andReturn(
                json_encode([
                    'environments' => [
                        $env => [
                            'targets' => [
                                [
                                    'variation' => 0,
                                    'values' => [$target],
                                ],
                            ],
                        ],
                    ],
                ], JSON_THROW_ON_ERROR)
            );

        $this->mockedClient
            ->shouldReceive('request')
            ->andReturn($this->mockedResponse);

        $result = $this->httpClient->isEnabled($env, $flagKey, $target);

        self::assertTrue($result);
    }

    private function getRequestBody(string $env, array $targets): string
    {
        $jsonPatch = [
            [
                'op' => 'replace',
                'path' => sprintf('/environments/%s/targets', $env),
                'value' => [
                    [
                        'variation' => 0,
                        'values' => $targets,
                    ],
                ],
            ],
        ];

        return json_encode($jsonPatch, JSON_THROW_ON_ERROR);
    }
}
