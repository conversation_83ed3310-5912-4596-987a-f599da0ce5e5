<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\ElectronicAgreements\Validation;

use App;
use Glofox\Domain\Users\Repositories\UsersRepository;
use Glofox\Domain\ElectronicAgreements\Http\Requests\DeleteAgreementRequest;
use Glofox\Domain\ElectronicAgreements\Validation\Validators\CanDeleteAgreementValidator;
use Glofox\Domain\Users\Exceptions\UserNotFoundException;
use CakeTestCases\Glofox\Domain\Users\Traits\AuthenticateUsersTrait;
use GlofoxTestCase;
use Mockery;

App::import('Test/Case', 'GlofoxTestCase');

class CanDeleteAgreementValidatorTest extends GlofoxTestCase
{
    use AuthenticateUsersTrait;

    private UsersRepository $usersRepositoryMock;
    private DeleteAgreementRequest $deleteAgreementRequestMock;

    public function setUp(): void
    {
        parent::setUp();
        $this->usersRepositoryMock = Mockery::mock(UsersRepository::class);
        $this->deleteAgreementRequestMock = Mockery::mock(DeleteAgreementRequest::class);
    }

    public function tearDown(): void
    {
        parent::tearDown();
        Mockery::close();
    }

    public function testUserDoesNotBelongToBranch()
    {
        $this->authenticateAsGlofoxStaff('59a7011a05c677bda916619a');
        $userId = 'some-user-id';
        $branchId = 'some-branch-id';

        $this->deleteAgreementRequestMock->shouldReceive('getData')->andReturnSelf();
        $this->deleteAgreementRequestMock->shouldReceive('memberId')->andReturn($userId);
        $this->deleteAgreementRequestMock->shouldReceive('branchId')->andReturn($branchId);

        $this->usersRepositoryMock->shouldReceive('getById')->withArgs(
            function (
                string $userIdParam
            ) use ($userId) {
                self::assertEquals(
                    $userId,
                    $userIdParam
                );
                throw UserNotFoundException::withId($userId);
            }
        );

        $this->expectExceptionMessage(
            'User not found - id: ' . $userId
        );

        $validator = new CanDeleteAgreementValidator($this->usersRepositoryMock);
        $validator->validate($this->deleteAgreementRequestMock);
    }

    private function loginWithGlofoxStaffField(string $userId, bool $isGlofoxStaff): void
    {
        $user = $this->fetchUser($userId);
        $user->put('isGlofoxStaff', $isGlofoxStaff);
        $this->loginAsUser($user);
    }
}
