<?php

namespace Glofox\Repositories\Search\Expressions\Exceptions;

use Glofox\Exception;

use Webmozart\Expression\Expression;

class NotQueryableExpressionException extends Exception
{
    public static function withExpression(Expression $expression)
    {
        $message = sprintf('%s needs to implement Queryable interface to be converted to query expression', get_class($expression));
        return new self($message);
    }
}
