<?php

declare(strict_types=1);

namespace Glofox\Repositories\Search\Expressions\Shared;

use Glofox\Repositories\Filters\ConditionsCollection;
use Glofox\Repositories\Filters\Evaluable;
use Glofox\Repositories\Repository;
use Glofox\Repositories\Search\Expressions\Conditionable;
use Glofox\Repositories\Search\Expressions\HasConditions;
use Glofox\Repositories\Search\Expressions\Key;
use MongoException;
use MongoRegex;
use Webmozart\Expression\Expr;

/**
 * <AUTHOR>
 */
class FieldContains extends Key implements Evaluable, Conditionable
{
    use HasConditions;

    private string $field;
    private string $value;

    public function __construct(string $field, string $value)
    {
        $this->field = $field;
        $this->value = $value;
        parent::__construct($field, Expr::contains($value));
    }

    public function field(): string
    {
        return $this->field;
    }

    /**
     * @throws MongoException
     */
    public function applyToConditions(ConditionsCollection $conditions, Repository $repository = null): void
    {
        $matchStartOfWordOrAnyWordPrecededByWhitespace = new MongoRegex(
            sprintf('/((?:|\s)\d*\s%s\s|(?:|\s)\d*%s)/i', $this->value, $this->value)
        );
        $conditions->push([
            $this->field => ['$regex' => new MongoRegex($matchStartOfWordOrAnyWordPrecededByWhitespace)],
        ]);
    }
}
