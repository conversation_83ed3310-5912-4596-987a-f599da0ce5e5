## Data Structures

## Pagination
+ count: 1 (number)
+ perPage: 50 (number)
+ currentPage: 1 (number)
+ links: (object)
    + previous: (string, nullable) - A nullable string
    + next: (string, nullable) - A nullable string

## Generic Error
+ success: false (boolean)
+ message: Error message description (string)
+ message_code: ERROR MESSAGE CODE (string)
+ message_data: (array, fixed)
+ errors: (array)
    + (string)

## Authentication Token
+ data: (object)
    + token: "eyHJSDIOOs322DASKND..." (string)