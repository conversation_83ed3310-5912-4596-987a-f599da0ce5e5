<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\Appointments\Http\Transformers;

use App;
use Carbon\Carbon;
use Glofox\Domain\Appointments\Http\Transformers\AppointmentsAvailabilityTransformer;
use Glofox\Domain\AppointmentSlots\Models\VirtualAppointmentSlot;
use Glofox\Domain\Branches\Exceptions\InvalidBranchIdException;
use Glofox\Domain\Branches\Models\Branch;
use Glofox\Domain\Branches\Repositories\BranchesRepository;
use Glofox\Domain\TimeSlotPatterns\Models\TimeSlotPattern;
use Glofox\Domain\Users\Models\User;
use Glofox\Domain\Users\Services\Avatar\AvatarUrlFactory;
use GlofoxTestCase;
use UserType;

App::import('Test/Case', 'GlofoxTestCase');

class AppointmentsAvailabilityTransformerTest extends GlofoxTestCase
{
    public $fixtures = [];

    /**
     * @dataProvider dataProviderTransformTimeStartAndTimeFinish
     * @param string $timezone
     * @param string $startTime
     * @param string $finishTime
     * @param string $expectedTimeStartIso
     * @throws InvalidBranchIdException
     */
    public function testTransform(
        string $timezone,
        string $startTime,
        string $finishTime,
        string $expectedTimeStartIso
    ): void {
        // mock the branch fetch
        $branchId = '49a7011a05c677b9a916612a';
        $branchesRepoMock = \Mockery::mock(BranchesRepository::class);
        $branchesRepoMock->shouldReceive('addCriteria')->andReturnSelf();
        $branchesRepoMock
            ->shouldReceive('firstOrFail')
            ->andReturn(Branch::make([
                'id' => $branchId,
                'address' => [
                    'timezone_id' => $timezone,
                ],
            ]))
            ->times(1);
        app()->forgetInstance(BranchesRepository::class);
        app()->instance(BranchesRepository::class, $branchesRepoMock);

        $appointmentId =  'appointment-id';
        $appointment = TimeSlotPattern::make([
            'id' => $appointmentId,
            'name' => 'test appointment',
            'branch_id' => $branchId,
        ]);

        // create the virtual appointment slot
        $virtualAppointmentSlot = new VirtualAppointmentSlot(
            $appointmentId,
            'staff-id',
            Carbon::parse($startTime)->getTimestamp(),
            Carbon::parse($finishTime)->getTimestamp(),
           $appointment
        );
        $trainer = User::make([
            '_id' => 'trainer-id',
            'branch_id' => $branchId,
            'type' => UserType::TRAINER,
            'namespace' => 'glofox',
            'first_name' => 'Jay',
            'last_name' => 'Dee',
            'description' => 'Trainer description',

        ]);
        $virtualAppointmentSlot->setTrainer($trainer);

        $transformer = new AppointmentsAvailabilityTransformer(
            app()->make(AvatarUrlFactory::class)
        );
        $transformed = $transformer->transform([$virtualAppointmentSlot]);

        $this->assertNotEmpty($transformed);
        $transformedDate = current($transformed['data'])['time_start_iso'];
        $this->assertEquals($expectedTimeStartIso, $transformedDate);
    }

    public function dataProviderTransformTimeStartAndTimeFinish(): array
    {
        $timeStart = "27-04-2025T13:00:00Z"; // 1_745_758_800, Sunday, April 27, 2025 1:00:00 PM UTC
        $timeFinish = "27-04-2025T14:00:00Z"; //1_745_762_400, Sunday, April 27, 2025 2:00:00 PM UTC

        return [
            '1. When the location is in UTC, then the start date is received in UTC.' => [
                'timezone' => 'UTC',
                'timeStart' => $timeStart,
                'timeFinish' => $timeFinish,
                'expectedTimeStartIso' => '2025-04-27T13:00:00+00:00',
            ],
            '2. When the location is in Dublin Ireland, then the start date is received in this timezone.' => [
                'timezone' => 'Europe/Dublin',
                'timeStart' => $timeStart,
                'timeFinish' => $timeFinish,
                'expectedTimeStartIso' => '2025-04-27T13:00:00+01:00',
            ],
            '3. When the location is in New York America, then the start date is received in this timezone.' => [
                'timezone' => 'America/New_York',
                'timeStart' => $timeStart,
                'timeFinish' => $timeFinish,
                'expectedTimeStartIso' => '2025-04-27T13:00:00-04:00',
            ],
            '4. When the location is in New York America (different times), then the start date is received in this timezone' => [
                'timezone' => 'America/New_York',
                'timeStart' => "18-04-2025T09:00:00Z", // 1_744_966_800, April 18, 2025, at 09:00:00 UTC
                'timeFinish' => "18-04-2025T10:00:00Z", // 1_744_970_400, April 18, 2025, at 10:00:00 UTC
                'expectedTimeStartIso' => '2025-04-18T09:00:00-04:00',
            ],
            '5. When the location is in Melbourne Australia, then the start date is received in this timezone.' => [
                'timezone' => 'Australia/Melbourne',
                'timeStart' => $timeStart,
                'timeFinish' => $timeFinish,
                'expectedTimeStartIso' => '2025-04-27T13:00:00+10:00',
            ],
            '6. When the location is in Seoul Korea, then the start date is received in this timezone.' => [
                'timezone' => 'Asia/Seoul',
                'timeStart' => $timeStart,
                'timeFinish' => $timeFinish,
                'expectedTimeStartIso' => '2025-04-27T13:00:00+09:00',
            ],
        ];
    }
}
