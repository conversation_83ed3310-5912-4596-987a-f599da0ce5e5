<?php

App::uses('Component', 'Controller');
App::uses('Folder', 'Utility');

use Glofox\CdnProvider;
use Glofox\Domain\Users\Services\Avatar\ImageModerationRequest;
use Glofox\Domain\Users\Services\Avatar\ImageModeratorClientInterface;
use Glofox\Storage\CloudStorageInterface;
use Intervention\Image\Gd\Encoder;
use Intervention\Image\ImageManager;
use League\Flysystem\AwsS3v3\AwsS3Adapter;
use League\Flysystem\FilesystemInterface;

/**
 * This Component will allow you to manage files in Amazon S3.
 */
class S3Component extends Component
{
    /**
     * @var GlofoxEnvironment::*
     */
    public string $environment;

    public Controller $controller;

    private CloudStorageInterface $storage;

    private ImageModeratorClientInterface $imageModeratorClient;

    private AwsS3Adapter $awsS3Adapter;

    public function initialize(Controller $controller): void
    {
        parent::initialize($controller);

        $this->environment = GlofoxEnvironment::currentEnvironment();
        $this->controller = $controller;

        $this->storage = app()->make(CloudStorageInterface::class);
        $this->imageModeratorClient = app()->make(ImageModeratorClientInterface::class);
        $this->awsS3Adapter = app()->make(FilesystemInterface::class)->getAdapter();
    }

    public function deleteFile(string $s3FilePath): void
    {
        $this->storage->delete($s3FilePath);
    }

    public function saveCrudImage(
        array $imageData,
        string $namespace,
        string $branchId = '',
        string $feature = '',
        string $id = '',
        string $imageName = ''
    ): array {
        try {
            $this->validateUsingCakeImageData($imageData);

            $temporaryPath = $imageData['tmp_name'];

            $imageName = empty($imageName) ? 'default' : $imageName;
            $imageName .= '.png';

            $s3Path = $this->getFolderPath($namespace, $branchId, $feature, $id) . $imageName;

            $manager = new ImageManager(['driver' => 'gd']);
            $image = $manager->make($temporaryPath);

            $size = $image->filesize();
            $sizeInMegaBytes = $size / 1_048_576;

            if ($sizeInMegaBytes > 3) {
                throw new RuntimeException('MAX_FILE_SIZE_IS_3MB');
            }

            $encoder = new Encoder();
            $encodedImage = $encoder->process(clone $image);

            $imageModerationRequest = new ImageModerationRequest($encodedImage);
            $this->imageModeratorClient->validate($imageModerationRequest);

            $fileSystemPath = TMP . $imageName;

            // Rename the file or convert image to '.png' (if applicable)
            $image->save($fileSystemPath);

            $this->saveFile($fileSystemPath, $s3Path);
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage(),
                'message_code' => $e->getMessage()
            ];
        }

        return [
            'success' => true,
            'message' => 'Image successfully uploaded'
        ];
    }

    public function saveImage(array $imageData, string $path, string $imageName): array
    {
        try {
            $this->validateUsingCakeImageData($imageData);

            $temporaryPath = $imageData['tmp_name'];

            $imageName = empty($imageName) ? 'default' : $imageName;
            $imageName .= '.png';

            $awsFolder = $this->getFolderPath() . $path . $imageName;

            $fileSystemPath = TMP . $imageName;

            // Rename the file or convert image to '.png' (if applicable)
            move_uploaded_file($temporaryPath, $fileSystemPath);

            $this->saveFile($fileSystemPath, $awsFolder);
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage(),
                'message_code' => $e->getMessage()
            ];
        }

        return [
            'success' => true,
            'message' => 'Image successfully uploaded'
        ];
    }

    public function saveMobileImage(array $imageData, string $path, string $imageName): array
    {
        try {
            $this->validateUsingCakeImageData($imageData);

            $temporaryPath = $imageData['tmp_name'];

            $awsFolder = $path;

            $fileSystemPath = TMP . $imageName;

            // Rename the file
            move_uploaded_file($temporaryPath, $fileSystemPath);

            $this->saveFile($fileSystemPath, $awsFolder);
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage(),
                'message_code' => $e->getMessage()
            ];
        }

        return [
            'success' => true,
            'message' => 'Image successfully uploaded'
        ];
    }

    public function getCrudImageUrlOrPlaceholder(
        string $namespace,
        string $branchId,
        string $feature = '',
        string $id = '',
        string $imageName = '',
        int $width = 640,
        int $height = 300
    ): ?string {
        $imageUrl = $this->getCrudImageUrl($namespace, $branchId, $feature, $id, $imageName);

        if ($this->remoteFileExists($imageUrl)) {
            return $imageUrl;
        }

        return $this->getPlaceHolderImageUrl($width, $height);
    }

    public function getCrudImageUrl(
        string $namespace,
        string $branchId,
        string $feature,
        string $id = '',
        string $imageName = ''
    ): string {
        $folderPath = $this->getFolderPath($namespace, $branchId, $feature, $id);

        $imageName = empty($imageName) ? 'default' : $imageName;
        $imageName .= '.png';

        $s3FilePath = $folderPath . $imageName;

        return $this->storage->url($s3FilePath);
    }

    /**
     * @param int|string $cache
     */
    public function getCrudCdnImageUrl(
        string $namespace,
        string $branchId,
        string $feature,
        string $id = '',
        string $imageName = '',
        $cache = null
    ): string {
        $folderPath = $this->getFolderPath($namespace, $branchId, $feature, $id);

        $imageName = empty($imageName) ? 'default' : $imageName;
        $imageName .= '.png';

        $fullPath = $this->awsS3Adapter->getPathPrefix() . $folderPath . $imageName;

        $url = $this->getCdnUrl($fullPath);

        if ($cache) {
            $url .= '?v=' . $cache;
        }

        return $url;
    }

    public function crudImageExists(
        string $namespace,
        string $branchId,
        string $feature,
        string $id,
        string $imageName = ''
    ) {
        $folderPath = $this->getFolderPath($namespace, $branchId, $feature, $id);

        $imageName = empty($imageName) ? 'default' : $imageName;
        $imageName .= '.png';

        $s3FilePath = $folderPath . $imageName;

        $imageUrl = $this->storage->url($s3FilePath);

        return $this->remoteFileExists($imageUrl);
    }

    public function getImageUrl(string $filePath = ''): string
    {
        $folderPath = filter_var($filePath, FILTER_VALIDATE_URL) ? '' : $this->getFolderPath();

        return $this->storage->url($folderPath . $filePath);
    }

    public function getPlaceHolderImageUrl(int $width = 640, int $height = 300): string
    {
        return sprintf('https://placehold.it/%dx%d', $width, $height);
    }

    // this was already here
    public function remoteFileExists(string $url): bool
    {
        $curl = curl_init($url);
        // don't fetch the actual page, you only want to check the connection is ok
        curl_setopt($curl, CURLOPT_NOBODY, true);
        //do request
        $result = curl_exec($curl);
        $ret = false;
        //if request did not fail
        if (false !== $result) {
            //if request was ok, check response code
            $statusCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
            if (200 == $statusCode) {
                $ret = true;
            }
        }
        curl_close($curl);

        return $ret;
    }

    private function saveFile(string $fileSystemPath, string $s3FilePath, string $permission = 'public-read'): void
    {
        $success = $this->storage->put($s3FilePath, file_get_contents($fileSystemPath), [
            'ACL' => $permission
        ]);

        if (!$success) {
            throw new RuntimeException(
                \sprintf('Could not upload file to %s', $s3FilePath)
            );
        }
    }

    private function getCdnUrl($path): string
    {
        return \sprintf('%s/%s', CdnProvider::getUrl(), $path);
    }

    private function getFolderPath(
        string $namespace = '',
        string $branchId = '',
        string $feature = '',
        string $id = ''
    ): string {
        if (empty($branchId)) {
            return sprintf('%s/', $namespace);
        }

        if (empty($feature)) {
            return sprintf('%s/branches/%s/', $namespace, $branchId);
        }

        if (empty($id)) {
            return sprintf('%s/branches/%s/%s/', $namespace, $branchId, $feature);
        }

        return ('users' == $feature)
            ? sprintf('%s/branches/%s/%s/', $namespace, $branchId, $feature)
            : sprintf('%s/branches/%s/%s/%s/', $namespace, $branchId, $feature, $id);
    }

    private function mapErrorCodeToErrorMessage(int $code): string
    {
        $code = empty($code) ? 4 : $code;
        $error = [
            0 => 'There is no error, the file uploaded with success',
            1 => 'The uploaded file exceeds the upload_max_filesize directive in php.ini',
            2 => 'The uploaded file exceeds the MAX_FILE_SIZE directive that was specified in the HTML form',
            3 => 'The uploaded file was only partially uploaded',
            4 => 'No file was uploaded',
            6 => 'Missing a temporary folder',
        ];

        return $error[$code] ?? 'Unknown image upload error';
    }

    private function validateUsingCakeImageData(array $imageData): void
    {
        if (isset($imageData['error']) && $imageData['error'] !== 0) {
            throw new RuntimeException($this->mapErrorCodeToErrorMessage($imageData['error']));
        }

        $temporaryPath = $imageData['tmp_name'];
        $actualName = $imageData['name'];

        if (!is_uploaded_file($temporaryPath) && trim($actualName) === '') {
            throw new RuntimeException('Could not read the uploaded file from the tmp storage');
        }

        $extension = explode('/', $imageData['type'])[1];

        if (in_array($extension, ['exe', 'bat', 'js'], true)) {
            throw new RuntimeException(
                \sprintf('Invalid file extension %s', $extension)
            );
        }
    }
}
