<?php

namespace CakeTestCases\Helper\Sns;

use League\Flysystem\FileNotFoundException;

class SnsPayloadFactory
{
    private static string $wrapperPath = (APP . 'Test/Case/Helper/Sns/Payloads/wrapper.json');

    public function create(string $filepath): array
    {
        if (!file_exists($filepath)) {
            throw new FileNotFoundException($filepath);
        }

        $payload = str_replace("\n", '', file_get_contents($filepath));
        $wrapper = json_decode(file_get_contents(self::$wrapperPath), true);

        $error = json_last_error();

        if ($error) {
            throw new \Exception($error);
        }

        $wrapper['Message'] = $payload;

        return $wrapper;
    }
}
