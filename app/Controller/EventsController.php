<?php

use Glofox\Datasource\Exceptions\InvalidMongoIdException;
use Glofox\Domain\Authentication\Auth;
use Glofox\Domain\Bookings;
use Glofox\Domain\Bookings\Repositories\BookingsRepository;
use Glofox\Domain\Bookings\Search\Expressions\EventId;
use Glofox\Domain\Bookings\Search\Expressions\Status;
use Glofox\Domain\Branches\Exceptions\BranchNotFoundException;
use Glofox\Domain\Branches\Models\Branch;
use Glofox\Domain\Branches\Repositories\BranchesRepository;
use Glofox\Domain\Events\EventMemberListPdfUrlGeneratorInterface;
use Glofox\Domain\Events\Exceptions\EventNotFoundException;
use Glofox\Domain\Events\Export\Builders\MemberListGeneratorBuilder;
use Glofox\Domain\Events\Export\FileNameGenerator;
use Glofox\Domain\Events\Internal\Parameters\PricingForUserParameters;
use Glofox\Domain\Events\Models\Event;
use Glofox\Domain\Events\Repositories\EventsRepository;
use Glofox\Domain\Events\Requests\DeleteByIdRequest;
use Glofox\Domain\Events\Requests\GenerateEventMemberListPdfUrl as GenerateEventMemberListPdfUrlRequest;
use Glofox\Domain\Events\Requests\SubmitAttendanceRequest;
use Glofox\Domain\Events\Requests\UpsertEventRequest;
use Glofox\Domain\Events\Services\EventsPublisher;
use Glofox\Domain\Events\Services\EventUpdatedEventSender;
use Glofox\Domain\Events\Transformers\AddVirtualOnlineFieldTransformer;
use Glofox\Domain\Events\UseCase\CreateOrUpdateOne;
use Glofox\Domain\Events\UseCase\CreateOrUpdateOneParams;
use Glofox\Domain\Events\UseCase\DeleteOne;
use Glofox\Domain\Events\Validation\UserAuthorization\CreateOrUpdateValidator;
use Glofox\Domain\Events\Validation\UserAuthorization\DeleteValidator;
use Glofox\Domain\Events\Validation\Validator;
use Glofox\Domain\Pricing\Requests\GetPrice as GetPriceRequest;
use Glofox\Domain\Pricing\Strategies\GuestPricingStrategy;
use Glofox\Domain\Pricing\Transformers\PricingTransformer;
use Glofox\Domain\Programs\Exceptions\ProgramNotFoundException;
use Glofox\Domain\Programs\Repositories\ProgramsRepository;
use Glofox\Domain\Trainers\Repositories\TrainersRepository;
use Glofox\Domain\Users\Exceptions\UserNotFoundException;
use Glofox\Domain\Users\Models\User;
use Glofox\Domain\Users\Repositories\UsersRepository;
use Glofox\Export\Strategies\DomPdfStrategy;
use Glofox\Http\LegacyRedirectorContract;
use Glofox\Http\Responses\Serializers\DataArraySerializer;
use Glofox\Repositories\Search\Expressions\Shared\Id;
use Illuminate\Http\JsonResponse;
use League\Fractal\Resource;
use Psr\Log\LoggerInterface;

/**
 * EventsController class.
 *
 * @uses          AppController
 *
 * @property Event Event
 * @property Branch $Branch
 * @property Client $Client
 * @property User $User
 * @property Booking $Booking
 * @property Membership $Membership
 * @property StripeCharge $StripeCharge
 * @property Facility $Facility
 * @property JWTComponent $JWT
 */
class EventsController extends \Glofox\Domain\Events\Http\EventsController implements LegacyRedirectorContract
{
    /**
     * @var string
     */
    public $name = 'Events';
    /**
     * @var array
     */
    public $uses = [
        'Branch',
        'Event',
        'Program',
        'User',
        'Facility',
        'Booking',
        'Membership',
        'UserCredit',
        'Client',
        'StripeCharge',
    ];
    /**
     * @var array
     */
    public $components = [
        'S3',
        'Paginator',
        'Utility',
        'JWT',
    ];
    /**
     * @var EventsRepository
     */
    protected $events;
    /**
     * @var EventsRepository
     */
    protected $eventsRepository;
    /**
     * @var UsersRepository
     */
    protected $usersRepository;
    /**
     * @var BranchesRepository
     */
    protected $branchesRepository;
    /**
     * @var ProgramsRepository
     */
    protected $programsRepository;
    /**
     * @var \Branch
     */
    protected $branchCakeModel;
    /**
     * @var \Booking
     */
    protected $bookingCakeModel;
    /**
     * @var \User
     */
    protected $userCakeModel;
    /**
     * @var \User
     */
    protected $userCreditCakeModel;
    /**
     * @var EventsPublisher
     */
    protected $eventsPublisher;
    private EventUpdatedEventSender $eventUpdatedEventSender;
    private $logger;

    /**
     * EventsController constructor.
     *
     * @param null $request
     * @param null $response
     */
    public function __construct($request = null, $response = null)
    {
        parent::__construct($request, $response);

        $this->logger = app()->make(LoggerInterface::class);
        $this->events = app()->make(EventsRepository::class);
        $this->eventsPublisher = app()->make(EventsPublisher::class);
        $this->eventUpdatedEventSender = app()->make(EventUpdatedEventSender::class);
    }

    /*******************************************************************************************
     *                                API 2.0 New Dashboard
     ******************************************************************************************/

    /**
     * Dispatcher for Model.
     *
     * @param string $identifier [description]
     *
     * @return string
     */
    public function dispatcher($identifier = null)
    {
        return parent::dispatch($identifier, $this->Event);
    }

    /**
     * {@inheritdoc}
     */
    public function redirector(\Glofox\Request $request)
    {
        $template = \Router::currentRoute()->template;

        $user = Auth::user();
        $branchId = $user->currentBranchId();

        $qs = $request->query();
        unset($qs['url']);

        $qs = http_build_query($qs);

        if ('/:apiVersion/events' === $template) {
            $route = sprintf('/2.0/branches/%s/events?%s', $branchId, $qs);
            $this->redirect($route, 307);

            return;
        } elseif ('/:apiVersion/events/:id' === $template) {
            $eventId = $request->cakeRouteParams()->get('id');

            $route = sprintf('/2.0/branches/%s/events/%s?%s', $branchId, $eventId, $qs);
            $this->redirect($route, 307);

            return;
        } elseif ('/:apiVersion/events/:id/pdf' === $template) {
            $eventId = $request->cakeRouteParams()->get('id');

            $route = sprintf('/2.0/branches/%s/events/%s/pdf?%s', $branchId, $eventId, $qs);
            $this->redirect($route, 307);

            return;
        }

        throw new Exception('Redirector couldnt not find a suitable route to dispatch this request');
    }

    /**
     * Get events by specifying all in branch, all in branch or by specific id. If an id is specified, the event iself is brought
     * if no id is specified, we ge the events from here to the visible date displayed.
     *
     * @param string $id Event id
     *
     * @return array List of events that match the entry params
     * @internal   This convention is a little boring for clients, but helps us keep clean backend
     *
     * @example    https://www.glofoxlogin/events/view/{id}
     *
     */
    public function view($id = null)
    {
        $user = $this->JWT->parseToken();

        $namespaces = $this->Client->getNamespacesInBundle($user['namespace']);
        $branchIds = $this->Branch->getBranchesIdsByNamespaces($namespaces);

        $filters = json_decode($this->request->input(), true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        $filters = !empty($filters) ? $filters : [];
        if (!empty($id)) {
            $result = $this->Event->findAllByBranchesIdsAndEventId($branchIds, $id);

            return response()->json($result);
        }

        $result = $this->Event->findAllByBranchesIdsAndStartDateAndFilters($branchIds, $filters);

        return response()->json($result);
    }

    public function upsert(UpsertEventRequest $req): JsonResponse
    {
        app()->make(CreateOrUpdateValidator::class)
            ->validate(Auth::user(), $req->branchId(), $req->namespace(), $req->id());

        app()->make(Validator::class)
            ->withData($req->data()->toArray())->validate();

        $params = new CreateOrUpdateOneParams(
            $req->id(),
            $req->branchId(),
            $req->namespace(),
            $req->name(),
            $req->description(),
            $req->programId(),
            $req->scheduleCode(),
            $req->facility(),
            $req->level(),
            $req->externalProviderStreamUrl(),
            $req->region(),
            $req->weekDay(),
            $req->size(),
            $req->duration(),
            $req->timestamp(),
            $req->isActive(),
            $req->isPrivate(),
            $req->isFeatured(),
            $req->isNew(),
            $req->isAttendanceSubmitted(),
            $req->isManuallyDeleted(),
            $req->trainers(),
            $req->date(),
            $req->timeStart(),
            $req->timeFinish(),
            $req->openBookingTime()
        );

        $useCase = app()->make(CreateOrUpdateOne::class);
        $result = $useCase->execute($params);

        return response()->json($result);
    }

    /**
     * @param DeleteByIdRequest $req
     * @return JsonResponse
     * @throws InvalidMongoIdException
     * @throws UnsuccessfulOperation
     */
    public function remove(DeleteByIdRequest $req): JsonResponse
    {
        app()->make(DeleteValidator::class)
            ->validate(Auth::user(), $req->eventId());

        $useCase = app()->make(DeleteOne::class);
        $result = $useCase->execute($req->eventId());

        return response()->json($result);
    }

    /****************************** REST WEBSERVICE ACTIONS  *******************************************/

    public function attendance(
        SubmitAttendanceRequest $request,
        BookingsRepository $bookingsRepository,
        EventsRepository $eventsRepository
    ): JsonResponse {
        $eventId = $request->getEventId();

        /** @var Event $event */
        $event = $eventsRepository
            ->addCriteria(new Id($eventId))
            ->firstOrFail();

        /** @var Bookings\Models\Booking[] $bookings */
        $bookings = $bookingsRepository
            ->addCriteria(new EventId($eventId))
            ->addCriteria(new Status(Bookings\Status::BOOKED()))
            ->find();

        if (count($bookings) > 0 && !$event->attendanceSubmitted()) {
            foreach ($bookings as $booking) {
                if ($booking->attended()) {
                    continue;
                }

                if ($booking->hasNoAttendanceReported()) {
                    $booking->setAsNotAttended();
                    $bookingsRepository->legacySaveOrFail($booking->toLegacy());
                }

                $this->User->incrementStrike($booking->toArray());
            }
        }

        $event = $event->toArray();
        $event['attendance_submitted'] = true;

        return response()->json([
            'success' => $this->Event->save($event),
        ]);
    }

    /**
     * Get all events for a given branch /branch/<branch_id>/events/<start>/<end>/<filter>.
     *
     * @param type $id
     * @param null $start
     *
     * @return string
     */
    public function findByBranchId($id, $start = null)
    {
        $originalStart = $start;

        //<editor-fold desc="Rushed hotfix added to support Buddhist Calendars requested urgently by the Product Team.">
        // Plan of Action: https://glofox.atlassian.net/wiki/spaces/PF/pages/580255749/Get+Classes
        // @TODO: Remove this after legacy apps are deprecated
        if ($start) {
            $start = new \Carbon\Carbon($start);

            /** @var \Glofox\Calendar\CurrentYearConverter $converter */
            $converter = app()->make(\Glofox\Calendar\CurrentYearConverter::class);
            $start = $converter->convert($start)->format('Y-m-d');
        }
        //</editor-fold>

        // Check if Branch Id is empty
        $is_valid_mongoid = $this->Utility->is_valid_mongoid($id);

        if (empty($is_valid_mongoid)) {
            return response()->json(['success' => false, 'message' => __('Invalid Branch Id')]);
        }

        if (!empty($start)) {
            $date = new DateTimeImmutable($start);
            $startTimestamp = $date->getTimestamp();
            $endOfDayTimestamp = $date->setTime(23, 59, 59)->getTimestamp();

            $startMongoDate = new MongoDate($startTimestamp);
            $endOfDayMongoDate = new MongoDate($endOfDayTimestamp);
        } else {
            $date_format = $this->Event->get_date_format('date');
            $today = $this->Branch->getCurrentDate($date_format);
            $weeks_to_display = $this->Branch->findWeeksToDisplay($id, 'events');
            $end_date = $this->Branch->addNumberOfWeeks($today, $weeks_to_display, $date_format);

            $startMongoDate = new MongoDate(strtotime($today));

            // Make Sure the end date is at the end of that day
            $endOfDayMongoDate = new MongoDate(strtotime($this->Event->getEndOfDay($end_date)));
        }

        /** @var TrainersRepository $trainersRepository */
        $trainersRepository = new TrainersRepository();
        $trainers = $trainersRepository->withFetchType(\Glofox\Repositories\FetchType::ALL())->findActive($id);
        $trainers = Hash::extract($trainers, '{n}.User._id');

        $query = [
            'conditions' => [
                'branch_id' => $id,
                'date' => [
                    '$gte' => $startMongoDate,
                    '$lte' => $endOfDayMongoDate,
                ],
                'active' => true,
                '$or' => [
                    ['private' => false],
                    ['private' => null],
                ],
            ],
            'order' => ['time_start' => 1],
            'limit' => 1000,
            'page' => 1,
        ];

        $events = $this->Event->find('all', $query);
        $events = $this->Event->formatTimeStartAndTimeEnd($events);

        // @see https://glofox.atlassian.net/browse/DASH2-2637
        // @see https://glofox.atlassian.net/browse/DASH2-2762
        // @see https://glofox.atlassian.net/browse/DASH2-2776
        // Only show trainers that exist in the database.
        foreach ($events as &$event) {
            $event['Event']['trainers'] = array_filter($event['Event']['trainers'], fn ($item) => in_array($item, $trainers));
            $event['Event']['date'] = $originalStart;
        }

        return response()->json($events);
    }

    /************************************************ NEW DASHBOARD METHODS *******************************************/

    /**
     * Find one by event id and branch id /branch/<branch_id>/event/<event_id>.
     *
     * @param $branchId
     * @param $eventId
     *
     * @return type
     *
     * @internal param type $id
     * @internal param type $id_fac
     */
    public function findByBranchIdAndEventId($branchId, $eventId)
    {
        if (!$this->Utility->is_valid_mongoid($branchId)) {
            $result = [
                'success' => false,
                'message' => __('Invalid Branch Id'),
            ];

            return response()->json($result);
        }

        if (!$this->Utility->is_valid_mongoid($eventId)) {
            $result = [
                'success' => false,
                'message' => __('Invalid Event Id'),
            ];

            return response()->json($result);
        }

        $query = [
            'conditions' => [
                '_id' => $eventId,
                'branch_id' => $branchId,
            ],
        ];

        $events = $this->Event->find('all', $query);

        foreach ($events as $key => $event) {
            if (isset($event['Event']['time_start'])) {
                $events[$key]['Event']['time_start'] = $this->Event->formatDate(
                    $events[$key]['Event']['time_start'],
                    'time'
                );
            }
            if (isset($event['Event']['time_finish'])) {
                $events[$key]['Event']['time_finish'] = $this->Event->formatDate(
                    $events[$key]['Event']['time_finish'],
                    'time'
                );
            }
            if (isset($event['Event']['week_day'])) {
                if (is_int($event['Event']['week_day'])) {
                    $events[$key]['Event']['week_day'] = (string)($events[$key]['Event']['week_day']);
                }
            }
        }

        if (!empty($events)) {
            return response()->json($events);
        }

        $result = [
            'success' => false,
            'message' => __('No Event'),
        ];

        return response()->json($result);
    }

    /**
     * Get all events for a given branch /branch/<branch_id>/events/<start>/<end>/<filter> from start date to finish date.
     *
     * @param type $branchId
     * @param null $start TODO: Should this be null by default?
     * @param null $finish TODO: Should this be null by default?
     *
     * @return string
     */
    public function dateRange($branchId, $start = null, $finish = null)
    {
        $user = $this->JWT->parseToken();

        if (!$this->Utility->is_valid_mongoid($branchId)) {
            $result = [
                'success' => false,
                'message' => __('Invalid Branch Id'),
            ];

            $response = response()->json($result);

            return $response;
        }

        $events = $this->Event->findAllByBranchIdAndDateRange($branchId, $start, $finish, $user);

        /** @var AddVirtualOnlineFieldTransformer $onlineFieldTransformer */
        $onlineFieldTransformer = app()->make(AddVirtualOnlineFieldTransformer::class);
        $events = $onlineFieldTransformer->execute($events);

        return response()->json($events);
    }

    /**
     * Find the next events for this branch_id.
     *
     * @param $branch_id
     * @param int $limit [description]
     *
     * @return string
     */
    public function findNextEvents($branch_id, $limit = 6)
    {
        $timezone = $this->Branch->getTimeZone($branch_id);

        $result = $this->Event->findNextEvents($branch_id, $timezone, $limit);

        $response = response()->json($result);

        return $response;
    }

    public function generateEventMemberListPdfUrl(
        GenerateEventMemberListPdfUrlRequest $request,
        EventMemberListPdfUrlGeneratorInterface $urlGenerator,
        EventsRepository $eventsRepository
    ) {
        $user = Auth::user();
        $eventId = $request->getEventId();

        /** @var Event $event */
        $event = $eventsRepository->findByIdOrFail($eventId);

        $url = $urlGenerator->generate($event, $user);

        $data = [
            'url' => $url,
        ];

        return response()->json($data);
    }

    /**
     * @param string $eventId
     *
     * @throws Exception
     */
    public function pdf(string $eventId)
    {
        $user = $this->JWT->parseToken();

        $allowedRoles = [
            UserType::SUPERADMIN,
            UserType::ADMIN,
            UserType::RECEPTIONIST,
            UserType::TRAINER,
        ];
        $this->JWT->validateOrFail($allowedRoles, $user['namespace'], $user['branch_id']);

        $event = $this->Event->findById($eventId);

        if (!$event) {
            throw new Exception("EventNotFoundException - $eventId not found");
        }

        $programId = $event['Event']['program_id'];
        $program = $this->Program->findById($programId);

        if (!$program) {
            throw new Exception("ProgramNotFoundException - $programId not found");
        }

        $branchId = $event['Event']['branch_id'];
        $branch = $this->Branch->findById($branchId);

        $builder = (new MemberListGeneratorBuilder())->withBranch($branch)
            ->withEvent($event)
            ->withProgram($program)
            ->withBookingModel($this->Booking)
            ->withUserModel($this->User)
            ->withBranchModel($this->Branch)
            ->withMembershipModel($this->Membership)
            ->withStripeChargeModel($this->StripeCharge)
            ->withFacilityModel($this->Facility);

        $content = $builder->build()->generate();

        $filename = (new FileNameGenerator($branch, $event))->ext('pdf')
            ->generate();

        /** @var DomPdfStrategy $strategy */
        $strategy = app()->make(DomPdfStrategy::class);
        $strategy->download($content, $filename);

        $this->response->type('application/pdf');
    }

    /**
     * @throws BranchNotFoundException
     * @throws EventNotFoundException
     * @throws InvalidMongoIdException
     * @throws ProgramNotFoundException
     * @throws UserNotFoundException
     * @throws Exception
     */
    public function price(
        GetPriceRequest $request,
        EventsRepository $eventsRepository,
        UsersRepository $usersRepository,
        BranchesRepository $branchesRepository,
        \Event $eventCakeModel
    ): JsonResponse {
        $event = $eventsRepository->getById($request->eventId());

        if ($request->isGuest()) {
            $event = Event::make($event);
            $user = null;

            $branch = $branchesRepository->getById($request->branchId());

            $pricingResolver = new GuestPricingStrategy($branch, $user, $request->quantity());
            $pricing = $pricingResolver->resolve($event);
        } else {
            $user = $usersRepository->getById($request->userId());
            $user['branch_id'] = $request->branchId();

            $pricingParams = new PricingForUserParameters(
                $event,
                $user,
                $request->guestBookings(),
                false,
                false
            );
            $pricing = $eventCakeModel->pricingForUser($pricingParams)->pricing();
        }

        $resource = new Resource\Item($pricing, new PricingTransformer());
        $response = fractal(DataArraySerializer::class)->createData($resource)->toArray();

        return response()->json($response);
    }
}
