post:
  tags:
    - Bookings
  summary: Book a list of booking slot in batch
  operationId: batch-recurring-bookings
  description:
    Use this call to book or reserve a list of booking slots that were created in batch from checking availability endpoint.
    Use the batchId provided in checking availability endpoint (/2.2/programs/{programId}/recurring-bookings/slots).
    This endpoint will process all slots in background in the apiworker.
    Finally, this call will eventually update the slots with the proper status (`BOOKED`, `RESERVED` or `FAILED` with error message).
  security:
    - coreapi_auth: [ ]
  parameters:
    - schema:
        type: string
        pattern: '^[a-f\d]{24}$'
      name: branchId
      in: path
      required: true
      description: The unique identifier of the branch where the bookings were created.
    - schema:
        type: string
      name: batchId
      in: path
      required: true
      description: The unique identifier to fetch bookings of the requested batch. It is provided in checking availability slot.
  responses:
    '204':
      description: No Content
    '401':
      description: Unauthorized
      content:
        application/json:
          schema:
            $ref: '../../schemas/error.yml'
get:
  tags:
    - Bookings
  summary: Get the list of booking slots by batch with their current result.
  operationId: get-batch-recurring-bookings
  description:
    Use this call to get all the booking slots result by batch.
    The async booking process could take some time, so we can use this endpoint to get the result for those booking slots.
    Slots can be `PENDING`, `BOOKED`, `RESERVED` or `FAILED`.
  security:
    - coreapi_auth: [ ]
  parameters:
    - schema:
        type: string
        pattern: '^[a-f\d]{24}$'
      name: branchId
      in: path
      required: true
      description: The unique identifier of the branch where the bookings were created.
    - schema:
        type: string
      name: batchId
      in: path
      required: true
      description: The unique identifier to fetch bookings of the requested batch. It is provided in checking availability slot.
  responses:
    '200':
      description: OK
      content:
        application/json:
          schema:
            $ref: '../../schemas/booking_slots_result.yml'
    '401':
      description: Unauthorized
      content:
        application/json:
          schema:
            $ref: '../../schemas/error.yml'

