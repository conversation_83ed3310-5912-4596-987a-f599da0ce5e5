#!/bin/sh

config_file="/usr/local/etc/php/conf.d/xdebug.ini.dist"

# we add the env vars as string (placeholders) to be replaced later in entrypoint.sh

echo "" > "$config_file"
{
    echo "zend_extension=$(find /usr/local/lib/php/extensions/ -name xdebug.so)"
    echo "xdebug.mode=\${PHP_XDEBUG_MODE}"
    echo "xdebug.client_host=\${PHP_XDEBUG_HOST}"
    echo "xdebug.client_port=9003"
    echo "xdebug.start_with_request=\${PHP_XDEBUG_ENABLE}"
    echo "xdebug.discover_client_host=\${PHP_XDEBUG_DISCOVER_CLIENT_HOST}"
    echo "xdebug.log=\"/tmp/xdebug.log\""
    echo "xdebug.output_dir=\"/var/www/profiler\""
} > "$config_file"
