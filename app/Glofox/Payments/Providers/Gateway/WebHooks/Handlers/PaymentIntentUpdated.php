<?php

namespace Glofox\Payments\Providers\Gateway\WebHooks\Handlers;

use Carbon\Carbon;
use Glofox\Domain\Charges\Exceptions\ChargeNotFoundException;
use Glofox\Domain\Charges\Models\Charge;
use Glofox\Domain\Charges\Repositories\ChargesRepository;
use Glofox\Domain\Charges\Search\Expressions\TransactionProviderId;
use Glofox\Domain\Charges\Services\ChargeSynchronizer;
use Glofox\Domain\Charges\Type as ChargeType;
use Glofox\Payments\Contracts\PaymentProviderContract;
use Glofox\Payments\Entities\Transaction\Exceptions\ChargeRetrievalException;
use Glofox\Payments\Entities\Transaction\Models\Transaction;
use Illuminate\Support\Collection;
use Psr\Log\LoggerInterface;
use Glofox\Domain\Charges\Events\ChargeReceiptWasRequested;

/**
 * Class PaymentIntentUpdated.
 */
class PaymentIntentUpdated extends EventHandler
{
    /** @var ChargesRepository */
    private $chargesRepository;

    public function __construct(Collection $eventPayload, PaymentProviderContract $paymentHandler)
    {
        parent::__construct($eventPayload, $paymentHandler);
        $this->chargesRepository = app()->make(ChargesRepository::class);
    }

    /**
     * @throws ChargeRetrievalException|ChargeNotFoundException
     */
    public function handle(): int
    {
        /** @var LoggerInterface $logger */
        $logger = app()->make(LoggerInterface::class);

        $logger->info(sprintf('starting %s...', static::class));

        $status = 200;
        $intentId = data_get(collect($this->eventPayloadPSP), 'data.object.id');
        $logger->info(sprintf('payment intent id: %s', $intentId));

        try {
            $transaction = $this->paymentHandler->charges()->getByIntentId($intentId);
        } catch (\Exception $exception) {
            $logger->error(sprintf('payment intent %s, not found', $intentId));
            throw new ChargeRetrievalException($exception->getMessage());
        }

        try {
            /** @var Charge $previousChargeState */
            $previousChargeState = $this->chargesRepository
                ->addCriteria(new TransactionProviderId($transaction->id()))
                ->firstOrFail(function () use ($logger, $transaction) {
                    $logger->error(
                        sprintf('charge with transaction provider id %s, not found', $transaction->id())
                    );
                    throw ChargeNotFoundException::withTransactionProviderId($transaction->id());
                });
        } catch (ChargeNotFoundException $exception) {
            if ($this->shouldIgnoreChargeNotFoundException($transaction)) {
                $logger->info(sprintf(
                    'the charge with transaction-provider-id[%s] was not found but it was determined that event can be skipped',
                    $transaction->id(),
                ));

                return $status;
            }
            throw $exception;
        }

        /** @var ChargeSynchronizer $chargeSynchronizer */
        $chargeSynchronizer = app()->make(ChargeSynchronizer::class);
        $currentChargeState = $chargeSynchronizer->byCharge($this->paymentHandler, $previousChargeState);
        
        if ($currentChargeState === null) {
            return 409;
        }

        if ($previousChargeState->isPendingAuthorization() && $currentChargeState->isPaid()) {
            $logger->info('emitting ChargeReceiptWasRequested event', [
                'chargeId' => $currentChargeState->id(),
            ]);

            event()->emit(ChargeReceiptWasRequested::class, [$currentChargeState, false]);
        }

        return $status;
    }

    private function shouldIgnoreChargeNotFoundException(Transaction $transaction): bool
    {
        // if it's a subscription payment that was just created, the event will be handled by a different handler
        if ($transaction->isRecurring() && $this->isTransactionCreationWithinTolerance($transaction)) {
            return true;
        }

        // from the point, only failed transactions are the ones that could potentially not be in the DB
        // non-error transactions must be present and the absence should not be ignored
        if (!$transaction->isError()) {
            return false;
        }

        // as the last resource, we will read the metadata and use the glofox_event (if present)
        $metadata = collect($transaction->metadata());
        if (!$metadata->has('glofox_event')) {
            return false;
        }

        // these transaction never get saved in mongo when they fail
        return \in_array(
            $metadata->get('glofox_event'),
            [
                ChargeType::BUY_PRODUCT,
                ChargeType::BOOK_CLASS,
                ChargeType::BOOK_COURSE,
                ChargeType::BOOK_TIME_SLOT,
                ChargeType::UPFRONT_PAYMENT,
                ChargeType::SUBSCRIPTION_PRORATE,
                ChargeType::CUSTOM_CHARGE,
                ChargeType::ADD_ON_PREPAID,
            ],
        );
    }

    private function isTransactionCreationWithinTolerance(Transaction $transaction): bool
    {
        $createdAt = Carbon::createFromTimestampUTC($transaction->createdAt());

        return $createdAt->isAfter(Carbon::now()->subMinute());
    }
}
