# LANGUAGE translation of CakePHP Application
# <AUTHOR> <EMAIL>
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PROJECT VERSION\n"
"POT-Creation-Date: 2014-08-12 08:18+0000\n"
"PO-Revision-Date: YYYY-mm-DD HH:MM+ZZZZ\n"
"Last-Translator: NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <EMAIL@ADDRESS>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=INTEGER; plural=EXPRESSION;\n"

#: View/Errors/error400.ctp:21
#: View/Errors/error500.ctp:21
msgid "Error"
msgstr ""

#: View/Errors/error400.ctp:23
msgid "The requested address %s was not found on this server."
msgstr ""

#: View/Errors/error500.ctp:22
#: Error/ExceptionRenderer.php:231
msgid "An Internal Error Has Occurred."
msgstr ""

#: Controller/Scaffold.php:128
msgid "Scaffold :: "
msgstr ""

#: Controller/Scaffold.php:167;234;303
msgid "Invalid %s"
msgstr ""

#: Controller/Scaffold.php:222
msgid "updated"
msgstr ""

#: Controller/Scaffold.php:225
msgid "saved"
msgstr ""

#: Controller/Scaffold.php:245
msgid "The %1$s has been %2$s"
msgstr ""

#: Controller/Scaffold.php:255
msgid "Please correct errors below."
msgstr ""

#: Controller/Scaffold.php:306
msgid "The %1$s with id: %2$s has been deleted."
msgstr ""

#: Controller/Scaffold.php:309
msgid "There was an error deleting the %1$s with id: %2$s"
msgstr ""

#: Controller/Component/AuthComponent.php:429
msgid "You are not authorized to access that location."
msgstr ""

#: Error/ExceptionRenderer.php:209
msgid "Not Found"
msgstr ""

#: Model/Validator/CakeValidationSet.php:286
msgid "This field cannot be left blank"
msgstr ""

#: Network/CakeResponse.php:1270
msgid "The requested file was not found"
msgstr ""

#: Utility/CakeNumber.php:119
msgid "%s KB"
msgstr ""

#: Utility/CakeNumber.php:121
msgid "%s MB"
msgstr ""

#: Utility/CakeNumber.php:123
msgid "%s GB"
msgstr ""

#: Utility/CakeNumber.php:125
msgid "%s TB"
msgstr ""

#: Utility/CakeNumber.php:117
msgid "%d Byte"
msgid_plural "%d Bytes"
msgstr[0] ""
msgstr[1] ""

#: Utility/CakeTime.php:398
msgid "Today, %s"
msgstr ""

#: Utility/CakeTime.php:401
msgid "Yesterday, %s"
msgstr ""

#: Utility/CakeTime.php:404
msgid "Tomorrow, %s"
msgstr ""

#: Utility/CakeTime.php:409
msgid "Sunday"
msgstr ""

#: Utility/CakeTime.php:410
msgid "Monday"
msgstr ""

#: Utility/CakeTime.php:411
msgid "Tuesday"
msgstr ""

#: Utility/CakeTime.php:412
msgid "Wednesday"
msgstr ""

#: Utility/CakeTime.php:413
msgid "Thursday"
msgstr ""

#: Utility/CakeTime.php:414
msgid "Friday"
msgstr ""

#: Utility/CakeTime.php:415
msgid "Saturday"
msgstr ""

#: Utility/CakeTime.php:421
msgid "On %s %s"
msgstr ""

#: Utility/CakeTime.php:742
msgid "%s ago"
msgstr ""

#: Utility/CakeTime.php:743
msgid "on %s"
msgstr ""

#: Utility/CakeTime.php:795
msgid "just now"
msgstr ""

#: Utility/CakeTime.php:911
msgid "about a second ago"
msgstr ""

#: Utility/CakeTime.php:912
msgid "about a minute ago"
msgstr ""

#: Utility/CakeTime.php:913
msgid "about an hour ago"
msgstr ""

#: Utility/CakeTime.php:914
msgid "about a day ago"
msgstr ""

#: Utility/CakeTime.php:915
msgid "about a week ago"
msgstr ""

#: Utility/CakeTime.php:916
msgid "about a year ago"
msgstr ""

#: Utility/CakeTime.php:925
msgid "in about a second"
msgstr ""

#: Utility/CakeTime.php:926
msgid "in about a minute"
msgstr ""

#: Utility/CakeTime.php:927
msgid "in about an hour"
msgstr ""

#: Utility/CakeTime.php:928
msgid "in about a day"
msgstr ""

#: Utility/CakeTime.php:929
msgid "in about a week"
msgstr ""

#: Utility/CakeTime.php:930
msgid "in about a year"
msgstr ""

#: Utility/CakeTime.php:952;974
msgid "days"
msgstr ""

#: Utility/CakeTime.php:884
msgid "%d year"
msgid_plural "%d years"
msgstr[0] ""
msgstr[1] ""

#: Utility/CakeTime.php:887
msgid "%d month"
msgid_plural "%d months"
msgstr[0] ""
msgstr[1] ""

#: Utility/CakeTime.php:890
msgid "%d week"
msgid_plural "%d weeks"
msgstr[0] ""
msgstr[1] ""

#: Utility/CakeTime.php:893
msgid "%d day"
msgid_plural "%d days"
msgstr[0] ""
msgstr[1] ""

#: Utility/CakeTime.php:896
msgid "%d hour"
msgid_plural "%d hours"
msgstr[0] ""
msgstr[1] ""

#: Utility/CakeTime.php:899
msgid "%d minute"
msgid_plural "%d minutes"
msgstr[0] ""
msgstr[1] ""

#: Utility/CakeTime.php:902
msgid "%d second"
msgid_plural "%d seconds"
msgstr[0] ""
msgstr[1] ""

#: View/Helper/FormHelper.php:702
msgid "Error in field %s"
msgstr ""

#: View/Helper/FormHelper.php:891
#: View/Scaffolds/form.ctp:48
#: View/Scaffolds/index.ctp:79;94
#: View/Scaffolds/view.ctp:65;80;190
msgid "New %s"
msgstr ""

#: View/Helper/FormHelper.php:897
#: View/Scaffolds/view.ctp:53;111
msgid "Edit %s"
msgstr ""

#: View/Helper/FormHelper.php:1462
msgid "empty"
msgstr ""

#: View/Helper/FormHelper.php:1834
#: View/Scaffolds/form.ctp:23
msgid "Submit"
msgstr ""

#: View/Helper/FormHelper.php:2820
msgid "January"
msgstr ""

#: View/Helper/FormHelper.php:2821
msgid "February"
msgstr ""

#: View/Helper/FormHelper.php:2822
msgid "March"
msgstr ""

#: View/Helper/FormHelper.php:2823
msgid "April"
msgstr ""

#: View/Helper/FormHelper.php:2824
msgid "May"
msgstr ""

#: View/Helper/FormHelper.php:2825
msgid "June"
msgstr ""

#: View/Helper/FormHelper.php:2826
msgid "July"
msgstr ""

#: View/Helper/FormHelper.php:2827
msgid "August"
msgstr ""

#: View/Helper/FormHelper.php:2828
msgid "September"
msgstr ""

#: View/Helper/FormHelper.php:2829
msgid "October"
msgstr ""

#: View/Helper/FormHelper.php:2830
msgid "November"
msgstr ""

#: View/Helper/FormHelper.php:2831
msgid "December"
msgstr ""

#: View/Helper/HtmlHelper.php:766
msgid "Home"
msgstr ""

#: View/Helper/PaginatorHelper.php:629
msgid " of "
msgstr ""

#: View/Scaffolds/form.ctp:27
#: View/Scaffolds/index.ctp:26;77
#: View/Scaffolds/view.ctp:49
msgid "Actions"
msgstr ""

#: View/Scaffolds/form.ctp:31
#: View/Scaffolds/index.ctp:51
#: View/Scaffolds/view.ctp:175
msgid "Delete"
msgstr ""

#: View/Scaffolds/form.ctp:34
#: View/Scaffolds/index.ctp:54
#: View/Scaffolds/view.ctp:57;178
msgid "Are you sure you want to delete # %s?"
msgstr ""

#: View/Scaffolds/form.ctp:37
msgid "List"
msgstr ""

#: View/Scaffolds/form.ctp:44
#: View/Scaffolds/index.ctp:87
#: View/Scaffolds/view.ctp:61;74
msgid "List %s"
msgstr ""

#: View/Scaffolds/index.ctp:48
#: View/Scaffolds/view.ctp:163
msgid "View"
msgstr ""

#: View/Scaffolds/index.ctp:49
#: View/Scaffolds/view.ctp:169
msgid "Edit"
msgstr ""

#: View/Scaffolds/index.ctp:65
msgid "Page {:page} of {:pages}, showing {:current} records out of {:count} total, starting on record {:start}, ending on {:end}"
msgstr ""

#: View/Scaffolds/index.ctp:70
msgid "previous"
msgstr ""

#: View/Scaffolds/index.ctp:72
msgid "next"
msgstr ""

#: View/Scaffolds/view.ctp:20
msgid "View %s"
msgstr ""

#: View/Scaffolds/view.ctp:57
msgid "Delete %s"
msgstr ""

#: View/Scaffolds/view.ctp:95;135
msgid "Related %s"
msgstr ""

