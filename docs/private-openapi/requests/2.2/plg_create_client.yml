type: object
properties:
  user:
    type: object
    properties:
      firstName:
        type: string
      lastName:
        type: string
      phone:
        type: string
      email:
        type: string
      password:
        type: string
  company:
    type: object
    properties:
      businessName:
        type: string
      address:
        type: object
        properties:
          continent:
            type: string
          city:
            type: string
          country:
            type: string
          countryCode:
            type: string
          currency:
            type: string
          latitude:
            type: string
          longitude:
            type: string
          state:
            type: string
          street:
            type: string
          timezoneId:
            type: string
  mobileApp:
    type: object
    properties:
      logo:
        type: string
      primaryColor:
        type: string
      secondaryColor:
        type: string
required:
  - user
  - company
  - mobileApp