<?php

namespace CakeTestCases\Glofox\Payments\Providers\Gateway\WebHooks\Handlers;

use Carbon\Carbon;
use Glofox\Domain\Charges\Models\Charge;
use Glofox\Domain\Charges\Repositories\ChargesRepository;
use Glofox\Domain\Refunds\Models\Refund;
use Glofox\Domain\Refunds\Repositories\RefundsRepository;
use Glofox\Payments\Contracts\PaymentProviderContract;
use Glofox\Payments\Entities\Payout\Contracts\PayoutHandlerContract;
use Glofox\Payments\Entities\Payout\Models\Payout;
use Glofox\Payments\Entities\Payout\Models\PayoutTransaction;
use Glofox\Payments\Entities\Transaction\Models\Transaction;
use Glofox\Payments\Providers\Gateway\WebHooks\Handlers\PayoutPaid;
use Illuminate\Support\Collection;
use Mockery\MockInterface;

\App::import('Test/Case', 'GlofoxTestCase');

class PayoutPaidTest extends \GlofoxTestCase
{
    public function tearDown()
    {
        \Mockery::close();
        parent::tearDown();
    }

    public function testGocardlessPayoutEvent()
    {
        $payout = $this->build_payout('CHARGE');
        /** @var PaymentProviderContract $paymentHandler */
        $paymentHandler = $this->build_payment_handler_mock($payout);

        $mockedChargesRepository = \Mockery::mock(ChargesRepository::class);
        $mockedChargesRepository
            ->shouldReceive('addCriteria')
            ->andReturnSelf()
            ->once()
            ->getMock()
            ->shouldReceive('first')
            ->andReturn(Charge::make(['metadata' => ['branch_id' => 'branch_id']]))
            ->once()
            ->getMock()
            ->shouldReceive('legacySaveOrFail')
            ->withArgs(fn(array $data) => $data['payout']['id'] == $payout->payoutTransactions()[0]->id()
                && $data['payout']['transaction_id'] == $payout->statement())
            ->andReturn(['Payout' => []])
            ->once()
            ->getMock();

        app()->instance(ChargesRepository::class, $mockedChargesRepository);

        $handler = new PayoutPaid($this->build_event(), $paymentHandler);
        $handler->waitTime = 0;

        $status = $handler->handle();

        self::assertEquals(200, $status);
        app()->forgetInstance(ChargesRepository::class);
    }

    public function testStripePayoutEvent()
    {
        $payout = $this->build_payout('CHARGE');
        /** @var PaymentProviderContract $paymentHandler */
        $paymentHandler = $this->build_payment_handler_mock($payout);

        $mockedChargesRepository = \Mockery::mock(ChargesRepository::class);
        $mockedChargesRepository
            ->shouldReceive('addCriteria')
            ->andReturnSelf()
            ->once()
            ->getMock()
            ->shouldReceive('first')
            ->andReturn(Charge::make(['metadata' => ['branch_id' => 'branch_id']]))
            ->once()
            ->getMock()
            ->shouldReceive('legacySaveOrFail')
            ->withArgs(fn(array $data) => $data['payout']['id'] == $payout->id()
                && $data['payout']['transaction_id'] == 'py_STRIPE')
            ->andReturn(['Payout' => []])
            ->once()
            ->getMock();

        app()->instance(ChargesRepository::class, $mockedChargesRepository);

        $handler = new PayoutPaid($this->build_stripe_event(), $paymentHandler);
        $handler->waitTime = 0;

        $status = $handler->handle();

        self::assertEquals(200, $status);
        app()->forgetInstance(ChargesRepository::class);
    }

    public function testHandleJPY()
    {
        // a payout with 1 charge 1 refunds is received
        // both exist in the DB, both amounts match
        // both entries get updated with the payout data
        $dbCharge = ['transaction_provider_id' => 'py_11', 'refunds' => ['5fa0aa0be3b9423bb3021853']];
        $data1 = ['_id' => '5fa0aa0be3b9423bb3021853', 'amount' => 1500, 'created' => '2021-01-04 00:00:00', 'metadata' => ['branch_id' => 'branch_id']];
        $refund1 = Refund::make($data1);
        $refunds = [$refund1];

        // Mocks the GetPayout call to the grpc transaction service
        $payout = $this->build_payout_jpy();
        /** @var PaymentProviderContract $paymentHandler */
        $paymentHandler = $this->build_payment_handler_mock($payout);

        $mockedChargesRepository = $this->build_charges_repo_mock($dbCharge);
        $mockedChargesRepository
            ->shouldReceive('legacySaveOrFail')
            ->withArgs(fn(array $data) => $data['payout']['gross_amount'] == 5999
                && $data['payout']['fee'] == -999
                && $data['payout']['id'] == 'py_11'
                && $data['payout']['transaction_id'] == 'py_123XYZ')
            ->andReturn(['payout' => []])
            ->once()
            ->getMock();

        $mockedRefundRepository = $this->build_refunds_repo_mock($refunds);
        // TODO: with the correct amount in the args
        $mockedRefundRepository
            ->shouldReceive('legacySaveOrFail')
            ->andReturn(['payout' => []])
            ->once()
            ->getMock();

        app()->instance(ChargesRepository::class, $mockedChargesRepository);
        app()->instance(RefundsRepository::class, $mockedRefundRepository);

        $handler = new PayoutPaid($this->build_stripe_event(), $paymentHandler);
        $handler->waitTime = 0;

        $status = $handler->handle();

        // should fail
        self::assertEquals(200, $status);

        app()->forgetInstance(ChargesRepository::class);
        app()->forgetInstance(RefundsRepository::class);
    }

    public function testHandleRefund()
    {
        $dbCharge = ['transaction_provider_id' => 'py_11', 'refunds' => ['5fa0aa0be3b9423bb3021853', '5f80ad9238cfa329da2eed07']];
        $data1 = ['_id' => '5fa0aa0be3b9423bb3021853', 'amount' => 100.00, 'created' => '2019-09-05 00:00:00', 'metadata' => ['branch_id' => 'branch_id']];
        $data2 = ['_id' => '5f80ad9238cfa329da2eed07', 'amount' => 200.00, 'created' => '2019-09-05 00:00:00', 'metadata' => ['branch_id' => 'branch_id']];
        $refund1 = Refund::make($data1);
        $refund2 = Refund::make($data2);
        $refunds = [$refund1, $refund2];

        $mockedChargesRepository = $this->build_charges_repo_mock($dbCharge);
        $mockedRefundRepository = $this->build_refunds_repo_mock($refunds);

        // expect updating the refund
        // TODO: with the correct amount in the args
        $mockedRefundRepository
            ->shouldReceive('legacySaveOrFail')
            ->andReturn(['payout' => []])
            ->once()
            ->getMock();

        app()->instance(ChargesRepository::class, $mockedChargesRepository);
        app()->instance(RefundsRepository::class, $mockedRefundRepository);

        /** @var PaymentProviderContract $paymentHandler */
        $paymentHandler = $this->build_payment_handler_refund_mock();

        $handler = new PayoutPaid($this->build_event(), $paymentHandler);
        $handler->waitTime = 0;

        $status = $handler->handle();

        self::assertEquals(200, $status);

        app()->forgetInstance(ChargesRepository::class);
        app()->forgetInstance(RefundsRepository::class);
    }

    public function testHandleMultipleRefundsMatchOnCreatedDate()
    {
        $dbCharge = ['transaction_provider_id' => 'py_11', 'refunds' => ['5fa0aa0be3b9423bb3021853', '5f80ad9238cfa329da2eed07']];
        $data1 = ['_id' => '5fa0aa0be3b9423bb3021853', 'amount' => 100.00, 'created' => '2019-09-05 00:00:00', 'metadata' => ['branch_id' => 'branch_id']];
        $data2 = ['_id' => '5f80ad9238cfa329da2eed07', 'amount' => 100.00, 'created' => '2019-09-06 00:00:00', 'metadata' => ['branch_id' => 'branch_id']];
        $refund1 = Refund::make($data1);
        $refund2 = Refund::make($data2);
        $refunds = [$refund1, $refund2];

        $mockedChargesRepository = $this->build_charges_repo_mock($dbCharge);
        $mockedRefundRepository = $this->build_refunds_repo_mock($refunds);
        $mockedRefundRepository
            ->shouldReceive('legacySaveOrFail')
            ->withArgs(fn(array $refunds) => $refunds['amount'] == 100.00 && $refunds['created'] == '2019-09-05 00:00:00')
            ->andReturn(['payout' => ['id' => '5fa0aa0be3b9423bb3021853', 'gross_amount' => 100.00]])
            ->once()
            ->getMock();

        app()->instance(ChargesRepository::class, $mockedChargesRepository);
        app()->instance(RefundsRepository::class, $mockedRefundRepository);

        /** @var PaymentProviderContract $paymentHandler */
        $paymentHandler = $this->build_payment_handler_refund_mock();

        $handler = new PayoutPaid($this->build_event(), $paymentHandler);
        $handler->waitTime = 0;

        $status = $handler->handle();

        self::assertEquals(200, $status);

        app()->forgetInstance(ChargesRepository::class);
        app()->forgetInstance(RefundsRepository::class);
    }

    public function testHandleMultipleRefundsMatchOnAmount()
    {
        $dbCharge = ['transaction_provider_id' => 'py_11', 'refunds' => ['5be443a65e269f1d374540dd', '5efc4e35f94a66092b62b2cc']];
        $data1 = ['_id' => '5be443a65e269f1d374540dd', 'amount' => 100.00, 'created' => '2019-09-05 00:00:00', 'metadata' => ['branch_id' => 'branch_id']];
        $data2 = ['_id' => '5efc4e35f94a66092b62b2cc', 'amount' => 50.00, 'created' => '2019-09-05 00:00:00', 'metadata' => ['branch_id' => 'branch_id']];
        $refund1 = Refund::make($data1);
        $refund2 = Refund::make($data2);
        $refunds = [$refund1, $refund2];

        $mockedChargesRepository = $this->build_charges_repo_mock($dbCharge);
        $mockedRefundRepository = $this->build_refunds_repo_mock($refunds);
        $mockedRefundRepository
            ->shouldReceive('legacySaveOrFail')
            ->withArgs(fn(array $refunds) => $refunds['amount'] == 100.00 && $refunds['created'] == '2019-09-05 00:00:00')
            ->andReturn(['payout' => ['id' => '5be443a65e269f1d374540dd', 'gross_amount' => 100.00]])
            ->once()
            ->getMock();

        app()->instance(ChargesRepository::class, $mockedChargesRepository);
        app()->instance(RefundsRepository::class, $mockedRefundRepository);

        /** @var PaymentProviderContract $paymentHandler */
        $paymentHandler = $this->build_payment_handler_refund_mock();

        $handler = new PayoutPaid($this->build_event(), $paymentHandler);
        $handler->waitTime = 0;

        $status = $handler->handle();

        self::assertEquals(200, $status);

        app()->forgetInstance(ChargesRepository::class);
        app()->forgetInstance(RefundsRepository::class);
    }

    public function testHandleChargeNotFound()
    {
        $payout = $this->build_payout('CHARGE');
        /** @var PaymentProviderContract $paymentHandler */
        $paymentHandler = $this->build_payment_handler_mock($payout);

        $data = ['_id' => '5fa0aa0be3b9423bb3021853', 'amount' => 100.00, 'created' => '2019-09-05 00:00:00'];
        $refund = Refund::make($data);
        $refunds = [$refund];

        $mockedRefundsRepository = \Mockery::mock(RefundsRepository::class);
        $mockedRefundsRepository
            ->shouldReceive('addCriteria')
            ->andReturn($mockedRefundsRepository);
        $mockedRefundsRepository
            ->shouldReceive('find')
            ->andReturn($refunds);

        $handler = new PayoutPaid($this->build_event(), $paymentHandler);
        $handler->waitTime = 0;

        $status = $handler->handle();

        self::assertEquals(200, $status);
        app()->forgetInstance(ChargesRepository::class);
    }

    private function build_payment_handler_mock(Payout $payout): MockInterface
    {
        return \Mockery::mock(PaymentProviderContract::class)
            ->shouldReceive('payouts')
            ->andReturn(
                \Mockery::mock(PayoutHandlerContract::class)
                    ->expects('getById')
                    ->with($payout->id())
                    ->withAnyArgs()
                    ->once()
                    ->andReturn($payout)
                    ->getMock()
            )
            ->getMock();
    }

    private function build_charges_repo_mock(array $chargeData): MockInterface
    {
        $mockedChargesRepository = \Mockery::mock(ChargesRepository::class);
        $mockedChargesRepository->shouldReceive('addCriteria')->andReturn($mockedChargesRepository);
        $mockedChargesRepository
            ->shouldReceive('first')
            ->andReturn(Charge::make($chargeData));

        return $mockedChargesRepository;
    }

    private function build_refunds_repo_mock(array $refunds): MockInterface
    {
        $mockedRefundRepository = \Mockery::mock(RefundsRepository::class);
        $mockedRefundRepository->shouldReceive('addCriteria')
            ->times(1)
            ->andReturn($mockedRefundRepository);
        $mockedRefundRepository
            ->shouldReceive('find')
            ->andReturn($refunds);

        return $mockedRefundRepository;
    }

    private function build_payment_handler_refund_mock(): MockInterface
    {
        return \Mockery::mock(PaymentProviderContract::class)
            ->shouldReceive('payouts')
            ->andReturn(
                \Mockery::mock(PayoutHandlerContract::class)
                    ->expects('getById')
                    ->withAnyArgs()
                    ->andReturn($this->build_payout('REFUND'))
                    ->getMock()
            )
            ->getMock();
    }

    private function build_payout(string $type): Payout
    {
        $now = Carbon::now()->getTimestamp();

        $transaction = new Transaction();
        $transaction->setId('transaction-id-1');
        $transaction->setType($type);
        $transaction->setServiceProviderID('py_STRIPE');
        $transaction->setCreatedAt(Carbon::parse('2019-09-05 00:00:00')->getTimestamp());

        $payoutTransactions = [];
        $payoutTransaction = new PayoutTransaction();
        $payoutTransaction->setId('payout-transaction-id-1');
        // After parsing a payout from the transaction service that comes in cents 
        $payoutTransaction->setGrossAmount(100); 
        $payoutTransaction->setFee(10);
        $payoutTransaction->setNetAmount(90);
        $payoutTransaction->setTransaction($transaction);
        $payoutTransactions[0] = $payoutTransaction;

        return (new Payout())
            ->setId('py_11')
            ->setAmount(100.00)
            ->setStatement('statement')
            ->setStatus('paid')
            ->setCurrency('eur')
            ->setArrivalDate($now)
            ->setPayoutTransactions($payoutTransactions)
            ->setCreatedAt(Carbon::parse('2019-09-05 00:00:00')->getTimestamp());
    }

    private function build_payout_jpy(): Payout
    {
        $now = Carbon::now()->getTimestamp();
        $payoutTransactions = [];

        // The main charge for the payout
        $transaction = new Transaction();
        $transaction->setId('transaction-id-charge-1'); // The transaction serivice is returning in here the payout (DB) id
        $transaction->setType('CHARGE');
        $transaction->setServiceProviderID('py_123XYZ');
        $transaction->setAmount(5999);
        $transaction->setFee(999);
        $transaction->setCreatedAt(Carbon::parse('2021-01-01 00:00:00')->getTimestamp());
        $payoutTransaction = new PayoutTransaction();
        $payoutTransaction->setId('payout-transaction-id-charge-1');
        $payoutTransaction->setGrossAmount(5999);
        $payoutTransaction->setFee(-999);
        $payoutTransaction->setNetAmount(5000);
        $payoutTransaction->setTransaction($transaction);
        $payoutTransactions[0] = $payoutTransaction;

        // A refund for the first charge
        $transaction = new Transaction();
        $transaction->setId('transaction-id-refund-1');
        $transaction->setType('REFUND-PARTIAL');
        $transaction->setParentAction('transaction-id-charge-1');
        $transaction->setServiceProviderID('re_123XYZ');
        $transaction->setAmount(1500);
        $transaction->setFee(0);
        $transaction->setCreatedAt(Carbon::parse('2021-01-05 00:00:00')->getTimestamp());
        $payoutTransaction = new PayoutTransaction();
        $payoutTransaction->setId('payout-transaction-id-charge-1');
        $payoutTransaction->setGrossAmount(-1500);
        $payoutTransaction->setFee(0);
        $payoutTransaction->setNetAmount(-1500);
        $payoutTransaction->setTransaction($transaction);
        $payoutTransactions[1] = $payoutTransaction;

        return (new Payout())
            ->setId('py_11')
            ->setAmount(4500)
            ->setStatement('')
            ->setStatus('paid')
            ->setCurrency('jpy')
            ->setArrivalDate($now)
            ->setPayoutTransactions($payoutTransactions)
            ->setCreatedAt(Carbon::parse('2021-02-01 00:00:00')->getTimestamp());
    }

    private function build_event(): Collection
    {
        return collect(['id' => 'evt_123',
            'psp_response' => '{"data":{"object":{"id":"pi_123456"}},"links":{"payout":"py_11"}}', ]);
    }

    private function build_stripe_event(): Collection
    {
        return collect(['id' => 'evt_456',
            'psp_response' => '{"data":{"object":{"id":"py_11"}}}', ]);
    }
}
