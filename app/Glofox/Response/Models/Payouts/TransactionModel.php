<?php

namespace Glofox\Response\Models\Payouts;

use Glofox\Response\BaseModel;

/**
 * @SuppressWarnings(PHPMD)
 */
class TransactionModel extends BaseModel
{
    /**
     * @var string|int
     */
    public $id = 0;
    /**
     * @var int
     */
    public $customer_account_id = 0;
    /**
     * @var int
     */
    public $destination_account_id = 0;
    /**
     * @var int
     */
    public $parent_action = 0;
    /**
     * @var float
     */
    public $amount = 0;
    /**
     * @var float
     */
    public $fee = 0;
    /**
     * @var string
     */
    public $type = '';
    /**
     * @var string
     */
    public $currency = '';
    /**
     * @var string
     */
    public $status = '';
    /**
     * @var int
     */
    public $created = 0;
    /**
     * @var int
     */
    public $updated = 0;

    protected array $allowedFields = [
        'id',
        'customer_account_id',
        'destination_account_id',
        'parent_action',
        'amount',
        'fee',
        'type',
        'currency',
        'status',
        'created',
        'updated'
    ];

    public function __construct(array $data)
    {
        $this->setModelFields($data);
    }
}
