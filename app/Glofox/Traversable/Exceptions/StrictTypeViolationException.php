<?php

namespace Glofox\Traversable\Exceptions;

use Glofox\Exception;

class StrictTypeViolationException extends Exception
{
    public static function withExpectedAndActual(string $expected, string $actual)
    {
        $message = 'Strict type violation - expected %s got %s';

        return new self(
            sprintf(
                $message,
                $expected,
                $actual
            )
        );
    }
}
