<?php

namespace Glofox\Infrastructure\Cache;

use Illuminate\Container\Container;
use Predis\Client as PredisClient;

class PredisClientFactory
{
    public const DEFAULT_CONTAINER_INSTANCE_KEY = 'PredisClient.ClientFromEnvironment';

    private \Illuminate\Container\Container $container;

    public function __construct(Container $container)
    {
        $this->container = $container;
    }

    public function create(array $clusterHosts, bool $isCluster): PredisClient
    {
        $parameters = $clusterHosts;
        $options = [];

        if ($isCluster) {
            $options['cluster'] = 'redis';
        }

        return new PredisClient($parameters, $options);
    }

    public function createFromEnvironment(): PredisClient
    {
        if ($this->container->offsetExists($this->containerKey())) {
            return $this->container->make($this->containerKey());
        }

        $environmentRedisUrl = env('GLOFOX_REDIS_URL');
        $redisClusterEnabled = env('GLOFOX_REDIS_CLUSTER_ENABLED');

        if (!$environmentRedisUrl) {
            throw new \Exception('`GLOFOX_REDIS_URL` environment variable is not set');
        }

        $clusterHosts = explode(',', $environmentRedisUrl);
        $isCluster = '1' === $redisClusterEnabled;

        $client = $this->create($clusterHosts, $isCluster);

        $this->container->instance($this->containerKey(), $client);

        return $client;
    }

    private function containerKey(): string
    {
        return self::DEFAULT_CONTAINER_INSTANCE_KEY;
    }
}
