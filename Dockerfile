FROM 108272622497.dkr.ecr.eu-west-1.amazonaws.com/gf-php-nginx:php_7.4.24_20241126

ARG ENV
ARG GITHUB_ACCESS_TOKEN

# Commit number
ARG VERSION
ENV VERSION $VERSION

# copy configuration files
COPY docker/entrypoint.sh /entrypoint.sh
COPY docker/supervisord.conf /etc/supervisord.conf

# Tuning nginx
COPY docker/nginx/nginx.conf /etc/nginx/nginx.conf
COPY docker/nginx/default.conf.dist /etc/nginx/conf.d/default.conf

# Tuning php-fpm
COPY ./docker/php/ /usr/local/etc/php/conf.d
RUN rm /usr/local/etc/php-fpm.d/www.conf
COPY ./docker/php-fpm/www-${ENV}.conf /usr/local/etc/php-fpm.d/www.conf

# Tuning sysctl
RUN rm /etc/sysctl.conf
COPY ./docker/sysctl.conf.dist /etc/sysctl.conf

# xdebug set up
COPY ./docker/xdebug.sh /tmp/xdebug.sh
RUN chmod a+x /tmp/xdebug.sh && /tmp/xdebug.sh

# NewRelic set up
RUN /opt/newrelic/newrelic-install install
RUN sed -i \
    -e "s/newrelic.appname =.*/newrelic.appname = \${NEW_RELIC_APP_NAME}/" \
    -e "s/;newrelic.enabled =.*/newrelic.enabled = \${NEW_RELIC_ENABLED}/" \
    -e "s/newrelic.license =.*/newrelic.license = \${NEW_RELIC_LICENCE}/" \
    /usr/local/etc/php/conf.d/newrelic.ini.dist
# Disable distributed tracing (too much data $$$)
RUN echo 'newrelic.distributed_tracing_enabled = false' >> /usr/local/etc/php/conf.d/newrelic.ini.dist

RUN docker-php-ext-install exif

WORKDIR /var/www

# Add Github Authentication Token
RUN composer config --global --auth github-oauth.github.com $GITHUB_ACCESS_TOKEN

COPY composer.json /var/www/composer.json
COPY composer.lock /var/www/composer.lock

RUN composer install --no-scripts --no-progress --profile --prefer-dist --no-autoloader

COPY . /var/www

RUN composer dump-autoload --no-scripts --optimize

COPY ./docker/cronjobs.sh /bin/cronjobs
RUN chmod a+x /bin/cronjobs

RUN chown -R www-data:www-data /var/log/newrelic

EXPOSE 80 9003

ENTRYPOINT ["/entrypoint.sh"]

CMD ["/usr/bin/supervisord", "--nodaemon", "--configuration", "/etc/supervisord.conf"]
