<?php

namespace CakeTestCases\Glofox\Domain\Cart\Http;

use Carbon\Carbon;
use Exception;
use Glofox\Domain\Authentication\Auth;
use Glofox\Domain\FeatureFlags\FeatureFlagInterface;
use Glofox\Domain\Memberships\Services\Purchase\PurchaseService;
use Glofox\Domain\PaymentMethods\Models\PaymentMethod;
use Glofox\Domain\PaymentMethods\Models\Provider;
use Glofox\Domain\PaymentMethods\Type;
use Glofox\Domain\Wallets\Services\Settings\OverdraftValidatorService;
use Glofox\Infrastructure\Flags\Flag;
use Glofox\Payments\Entities\Capability\Models\Capability;
use Glofox\Payments\Entities\Customer\Resolvers\IdResolver;
use GlofoxControllerTestCase;
use Illuminate\Support\Collection;
use Glofox\Domain\Memberships\Validation\Purchase\MembershipPurchasedBeforeValidator;
use Glofox\Domain\Memberships\Exceptions\MembershipPurchasedBeforeException;
use Mockery;

\App::import('Test/Case', 'GlofoxControllerTestCase');

class CartControllerTest extends GlofoxControllerTestCase
{
    public $fixtures = [
        'app.user',
        'app.product',
        'app.branch',
        'app.membership',
    ];

    public function tearDown(): void
    {
        parent::tearDown();

        Mockery::close();
    }

    public function test_validate_unlimited_membership_returns_attributes_successfully(): void
    {
        $adminUser = $this->fetchUser('59a7011a05c677bda512212a');
        $this->loginAsUser($adminUser);

        $branchId = '49a7011a05c677b9a916612a';
        $userId = '5b19430ea0d988945a164337';
        $membershipId = '54107c1cd7b6ddc3a98b4577';
        $planCode = '*************'; // This is an unlimited plan code

        $data = [
            'branchId' => $branchId,
            'userId' => $userId,
            'membershipId' => $membershipId,
            'planCode' => $planCode,
            'payment_method' => 'cash',
            'start_date' => strtotime(date('Y-m-d')),
        ];

        $mockValidator = Mockery::mock(MembershipPurchasedBeforeValidator::class);
        $mockValidator
            ->shouldReceive('execute')
            ->andReturn();

        app()->instance(MembershipPurchasedBeforeValidator::class, $mockValidator);

        $purchaseService = \Mockery::mock(PurchaseService::class);
        $purchaseService
            ->shouldReceive('buildParams')
            ->andReturn();
        $purchaseService
            ->shouldReceive('buildLegacyPurchaseRequest')
            ->andReturn(
                new Collection([
                    'membership_code' => $membershipId,
                    'plan_code' => $planCode,
                    'branch_id' => $branchId,
                    'user_id' => $userId,
                    'payment_method' => 'CARD',
                    'price' => 10.0,
                    'is_terms_and_conditions_accepted' => true,
                    'future_flexible_payments' => false,
                    'discounts' => [],
                    'merchant_account_id' => 123,
                    'customer_account_id' => 456,
                    'membership_name' => 'A restricted membership',
                    'currency' => 'EUR',
                    'is_prorated' => false,
                    'is_subscription' => false,
                    'use_available_account_balance' => false,
                    'allow_overdraft' => false,
                    'payer' => [],
                    'membership_type' => 'time',
                    'prorated_percentage' => null,
                ])
            );

        app()->instance(PurchaseService::class, $purchaseService);

        $result = $this->testAction(
            '/2.2/cart/validate/membership',
            [
                'method' => 'POST',
                'data' => $data,
            ]
        );

        $result = json_decode($result, $assoc = true, 512, JSON_THROW_ON_ERROR);

        $this->assertEquals(
            [
                'membership_code' => $membershipId,
                'plan_code' => $planCode,
                'branch_id' => $branchId,
                'user_id' => $userId,
                'payment_method' => 'CARD',
                'price' => 10.0,
                'is_terms_and_conditions_accepted' => true,
                'future_flexible_payments' => false,
                'discounts' => [],
                'merchant_account_id' => 123,
                'customer_account_id' => 456,
                'membership_name' => 'A restricted membership',
                'currency' => 'EUR',
                'is_prorated' => false,
                'is_subscription' => false,
                'use_available_account_balance' => false,
                'allow_overdraft' => false,
                'payer' => [],
                'membership_type' => 'time',
                'prorated_percentage' => null,
            ],
            $result['attributes']
        );

        app()->forgetInstance(PurchaseService::class);
    }

    public function test_validate_membership_returns_attributes_with_complimentary_payment_method_when_plan_is_free(): void
    {
        $adminUser = $this->fetchUser('59a7011a05c677bda512212a');
        $this->loginAsUser($adminUser);

        $branchId = '49a7011a05c677b9a916612a';
        $userId = '5b19430ea0d988945a164337';
        $membershipId = '5f159e696222aaa4d0ee4cef';
        $planCode = '*********'; // This is an unlimited plan code

        $data = [
            'branchId' => $branchId,
            'userId' => $userId,
            'membershipId' => $membershipId,
            'planCode' => $planCode,
            'payment_method' => 'cash',
            'start_date' => strtotime(date('Y-m-d')),
        ];

        $mockValidator = Mockery::mock(MembershipPurchasedBeforeValidator::class);
        $mockValidator
            ->shouldReceive('execute')
            ->andReturn();

        app()->instance(MembershipPurchasedBeforeValidator::class, $mockValidator);

        $purchaseService = \Mockery::mock(PurchaseService::class);
        $purchaseService
            ->shouldReceive('buildParams')
            ->andReturn();
        $purchaseService
            ->shouldReceive('buildLegacyPurchaseRequest')
            ->andReturn(
                new Collection([
                    'membership_code' => $membershipId,
                    'plan_code' => $planCode,
                    'branch_id' => $branchId,
                    'user_id' => $userId,
                    'payment_method' => 'COMPLIMENTARY',
                    'price' => 0.0,
                    'is_terms_and_conditions_accepted' => true,
                    'future_flexible_payments' => false,
                    'discounts' => [],
                    'merchant_account_id' => 123,
                    'customer_account_id' => 456,
                    'membership_name' => 'A membership',
                    'currency' => 'EUR',
                    'is_prorated' => false,
                    'is_subscription' => false,
                    'use_available_account_balance' => false,
                    'allow_overdraft' => false,
                    'payer' => [],
                    'membership_type' => 'time',
                    'prorated_percentage' => null,
                ])
            );

        app()->instance(PurchaseService::class, $purchaseService);

        $result = $this->testAction(
            '/2.2/cart/validate/membership',
            [
                'method' => 'POST',
                'data' => $data,
            ]
        );

        $result = json_decode($result, $assoc = true, 512, JSON_THROW_ON_ERROR);

        $this->assertEquals(
            [
                'membership_code' => $membershipId,
                'plan_code' => $planCode,
                'branch_id' => $branchId,
                'user_id' => $userId,
                'payment_method' => 'COMPLIMENTARY',
                'price' => 0.0,
                'is_terms_and_conditions_accepted' => true,
                'future_flexible_payments' => false,
                'discounts' => [],
                'merchant_account_id' => 123,
                'customer_account_id' => 456,
                'membership_name' => 'A membership',
                'currency' => 'EUR',
                'is_prorated' => false,
                'is_subscription' => false,
                'use_available_account_balance' => false,
                'allow_overdraft' => false,
                'payer' => [],
                'membership_type' => 'time',
                'prorated_percentage' => null,
            ],
            $result['attributes']
        );

        app()->forgetInstance(PurchaseService::class);
    }

    public function test_validate_membership_returns_error_on_member_using_complimentary(): void
    {
        $memberUser = $this->fetchUser('59a3011a05c677bda916711c');
        $this->loginAsUser($memberUser);

        $branchId = '49a7011a05c677b9a916612a';
        $userId = '59a3011a05c677bda916711c';
        $membershipId = '54107c1cd7b6ddc3a98b4577';
        $planCode = '*************'; // This is an unlimited plan code

        $data = [
            'branchId' => $branchId,
            'userId' => $userId,
            'membershipId' => $membershipId,
            'planCode' => $planCode,
            'payment_method' => 'complimentary',
            'start_date' => strtotime(date('Y-m-d')),
            'terms_conditions_accepted' => true,
        ];

        $mockValidator = Mockery::mock(MembershipPurchasedBeforeValidator::class);
        $mockValidator
            ->shouldReceive('execute')
            ->andReturn();

        app()->instance(MembershipPurchasedBeforeValidator::class, $mockValidator);

        $result = $this->testAction(
            sprintf('/2.2/cart/validate/membership'),
            [
                'method' => 'POST',
                'data' => $data,
            ]
        );

        $result = json_decode($result, $assoc = true, 512, JSON_THROW_ON_ERROR);

        $this->assertEquals("CART_VALIDATOR_MEMBERSHIP_MEMBER_NOT_ALLOWED_COMPLIMENTARY", $result['message_code']);
        $this->assertEquals("CART_VALIDATOR_MEMBERSHIP_MEMBER_NOT_ALLOWED_COMPLIMENTARY", $result['message']);
    }

    public function test_validate_membership_returns_error_on_member_purchasing_single_purchase_membership_again(): void
    {
        $adminUser = $this->fetchUser('59a7011a05c677bda512212a');
        $this->loginAsUser($adminUser);

        $branchId = '49a7011a05c677b9a916612a';
        $userId = '5b19430ea0d988945a164337';
        $membershipId = '5f159e696222aaa4d0ee4ce1'; // This is a single purchase membership, buy_just_once = true
        $planCode = '1506335805977';

        $mockValidator = Mockery::mock(MembershipPurchasedBeforeValidator::class);
        $mockValidator
            ->shouldReceive('execute')
            ->andThrow(new MembershipPurchasedBeforeException())
            ->once();
        app()->instance(MembershipPurchasedBeforeValidator::class, $mockValidator);

        $data = [
            'branchId' => $branchId,
            'userId' => $userId,
            'membershipId' => $membershipId,
            'planCode' => $planCode,
            'payment_method' => 'cash',
            'start_date' => strtotime(date('Y-m-d')),
        ];

        $result = $this->testAction(
            '/2.2/cart/validate/membership',
            [
                'method' => 'POST',
                'data' => $data,
            ]
        );

        $result = json_decode($result, $assoc = true, 512, JSON_THROW_ON_ERROR);

        $this->assertEquals(
            "THIS_MEMBERSHIP_CAN_ONLY_BE_PURCHASES_ONCE",
            $result['message_code']
        );
        $this->assertEquals("THIS_MEMBERSHIP_CAN_ONLY_BE_PURCHASES_ONCE", $result['message']);

        app()->forgetInstance(PurchaseService::class);
    }

    public function test_validate_membership_returns_error_on_request_with_start_date_for_dofb_membership(): void
    {
        $memberUser = $this->fetchUser('59a3011a05c677bda916711c');
        $this->loginAsUser($memberUser);

        $branchId = '49a7011a05c677b9a916612a';
        $userId = '59a3011a05c677bda916711c';
        $membershipId = '5a146888d721b079504aa0ee';
        $planCode = '5a1430309b264e6a99186003'; // This is DOFB plan code

        $data = [
            'branchId' => $branchId,
            'userId' => $userId,
            'membershipId' => $membershipId,
            'planCode' => $planCode,
            'payment_method' => 'cash',
            'start_date' => Carbon::today()->addDay(1)->getTimestamp(),
            'terms_conditions_accepted' => true,
        ];

        $result = $this->testAction(
            sprintf('/2.2/cart/validate/membership'),
            [
                'method' => 'POST',
                'data' => $data,
            ]
        );

        $result = json_decode($result, $assoc = true, 512, JSON_THROW_ON_ERROR);

        $this->assertEquals("CART_VALIDATOR_MEMBERSHIP_WITH_DOFB_DOES_NOT_ALLOW_START_DATE", $result['message_code']);
        $this->assertEquals("CART_VALIDATOR_MEMBERSHIP_WITH_DOFB_DOES_NOT_ALLOW_START_DATE", $result['message']);

        app()->forgetInstance(PurchaseService::class);
    }

    public function test_validate_restricted_membership_returns_attributes_successfully(): void
    {
        $adminUser = $this->fetchUser('59a7011a05c677bda512212a');
        $this->loginAsUser($adminUser);

        $branchId = '49a7011a05c677b9a916612a';
        $userId = '5b19430ea0d988945a164337';
        $membershipId = '54107c1cd7b6ddc3a98b4577';
        $planCode = '54107aced7b6dd7aab8b4567'; // this a restricted plan code

        $data = [
            'branchId' => $branchId,
            'userId' => $userId,
            'membershipId' => $membershipId,
            'planCode' => $planCode,
            'payment_method' => 'cash',
            'start_date' => strtotime(date('Y-m-d')),
        ];

        $mockValidator = Mockery::mock(MembershipPurchasedBeforeValidator::class);
        $mockValidator
            ->shouldReceive('execute')
            ->andReturn();

        app()->instance(MembershipPurchasedBeforeValidator::class, $mockValidator);

        $purchaseService = \Mockery::mock(PurchaseService::class);
        $purchaseService
            ->shouldReceive('buildParams')
            ->andReturn();
        $purchaseService
            ->shouldReceive('buildLegacyPurchaseRequest')
            ->andReturn(new Collection([
                'membership_code' => $membershipId,
                'plan_code' => $planCode,
                'branch_id' => $branchId,
                'user_id' => $userId,
                'payment_method' => 'CARD',
                'price' => 10.0,
                'is_terms_and_conditions_accepted' => true,
                'future_flexible_payments' => false,
                'discounts' => [],
                'merchant_account_id' => 123,
                'customer_account_id' => 456,
                'membership_name' => 'A membership',
                'currency' => 'EUR',
                'is_prorated' => false,
                'is_subscription' => false,
                'use_available_account_balance' => false,
                'allow_overdraft' => false,
                'payer' => [],
                'membership_type' => 'time_classes',
                'prorated_percentage' => null,
            ]));

        app()->instance(PurchaseService::class, $purchaseService);

        $result = $this->testAction(
            '/2.2/cart/validate/membership',
            [
                'method' => 'POST',
                'data' => $data,
            ]
        );

        $result = json_decode($result, $assoc = true, 512, JSON_THROW_ON_ERROR);

        $this->assertEquals(
            [
                'membership_code' => $membershipId,
                'plan_code' => $planCode,
                'branch_id' => $branchId,
                'user_id' => $userId,
                'payment_method' => 'CARD',
                'price' => 10.0,
                'is_terms_and_conditions_accepted' => true,
                'future_flexible_payments' => false,
                'discounts' => [],
                'merchant_account_id' => 123,
                'customer_account_id' => 456,
                'membership_name' => 'A membership',
                'currency' => 'EUR',
                'is_prorated' => false,
                'is_subscription' => false,
                'use_available_account_balance' => false,
                'allow_overdraft' => false,
                'payer' => [],
                'membership_type' => 'time_classes',
                'prorated_percentage' => null,
            ],
            $result['attributes']
        );

        app()->forgetInstance(PurchaseService::class);
    }

    public function test_validate_membership_throws_error_on_non_existent_plan(): void
    {
        $adminUser = $this->fetchUser('59a7011a05c677bda512212a');
        $this->loginAsUser($adminUser);

        $branchId = '49a7011a05c677b9a916612a';
        $userId = '5b19430ea0d988945a164337';
        $membershipId = '5b8eb03b10e2c82a4d443f9c';
        $planCode = '*************doenstexist';

        $data = [
            'branchId' => $branchId,
            'userId' => $userId,
            'membershipId' => $membershipId,
            'planCode' => $planCode,
            'payment_method' => 'cash',
            'start_date' => strtotime(date('Y-m-d')),
        ];

        $result = $this->testAction(
            '/2.2/cart/validate/membership',
            [
                'method' => 'POST',
                'data' => $data,
            ]
        );

        $result = json_decode($result, $assoc = true, 512, JSON_THROW_ON_ERROR);

        $this->assertEquals(
            "PLAN_DOES_NOT_EXISTS",
            $result['message_code']
        );
        $this->assertEquals("PLAN_DOES_NOT_EXISTS", $result['message']);

        app()->forgetInstance(PurchaseService::class);
    }

    public function test_validate_membership_returns_error_on_adding_another_DOFB_credit_pack(): void
    {
        $adminUser = $this->fetchUser('59a7011a05c677bda512212a');
        $this->loginAsUser($adminUser);

        $branchId = '49a7011a05c677b9a916612a';
        $userId = '5a1445fbc836ed252aba7c23'; // this is a user with a DOFB membership
        $membershipId = '5b8eb03b10e2c82a4d443f9c';
        $planCode = '1536078615117'; // this is a credit pack plan code

        $data = [
            'branchId' => $branchId,
            'userId' => $userId,
            'membershipId' => $membershipId,
            'planCode' => $planCode,
            'payment_method' => 'cash',
            'start_date' => strtotime(date('Y-m-d')),
        ];

        $result = $this->testAction(
            '/2.2/cart/validate/membership',
            [
                'method' => 'POST',
                'data' => $data,
            ]
        );

        $result = json_decode($result, $assoc = true, 512, JSON_THROW_ON_ERROR);

        $this->assertEquals(
            "YOU_CAN_ONLY_HAVE_A_FIRST_BOOKING_MEMBERSHIP_AT_A_TIME",
            $result['message_code']
        );
        $this->assertEquals("YOU_CAN_ONLY_HAVE_A_FIRST_BOOKING_MEMBERSHIP_AT_A_TIME", $result['message']);

        app()->forgetInstance(PurchaseService::class);
    }

    public function test_validate_credit_pack_does_not_throw_when_feature_flag_enabled(): void
    {
        $adminUser = $this->fetchUser('59a7011a05c677bda512212a');
        $this->loginAsUser($adminUser);

        $branchId = '49a7011a05c677b9a916612a';
        $userId = '5b19430ea0d988945a164337';
        $membershipId = '5b8eb03b10e2c82a4d443f9c';
        $planCode = '1536078615117';

        $data = [
            'branchId' => $branchId,
            'userId' => $userId,
            'membershipId' => $membershipId,
            'planCode' => $planCode,
            'payment_method' => 'cash',
            'start_date' => strtotime(date('Y-m-d')),
        ];

        $purchaseService = \Mockery::mock(PurchaseService::class);
        $purchaseService
            ->shouldReceive('buildParams')
            ->andReturn();
        $purchaseService
            ->shouldReceive('buildLegacyPurchaseRequest')
            ->andReturn(new Collection([
                'membership_code' => $membershipId,
                'plan_code' => $planCode,
                'branch_id' => $branchId,
                'user_id' => $userId,
                'payment_method' => 'CARD',
                'price' => 10.0,
                'is_terms_and_conditions_accepted' => true,
                'future_flexible_payments' => false,
                'discounts' => [],
                'merchant_account_id' => 123,
                'customer_account_id' => 456,
                'membership_name' => 'A membership',
                'currency' => 'EUR',
                'is_prorated' => false,
                'is_subscription' => false,
                'use_available_account_balance' => false,
                'allow_overdraft' => false,
                'payer' => [],
                'membership_type' => 'time_classes',
                'prorated_percentage' => null,
            ]));

        app()->instance(PurchaseService::class, $purchaseService);

        $result = $this->testAction(
            '/2.2/cart/validate/membership',
            [
                'method' => 'POST',
                'data' => $data,
            ]
        );

        $result = json_decode($result, $assoc = true, 512, JSON_THROW_ON_ERROR);

        $this->assertEquals(
            [
                'membership_code' => $membershipId,
                'plan_code' => $planCode,
                'branch_id' => $branchId,
                'user_id' => $userId,
                'payment_method' => 'CARD',
                'price' => 10.0,
                'is_terms_and_conditions_accepted' => true,
                'future_flexible_payments' => false,
                'discounts' => [],
                'merchant_account_id' => 123,
                'customer_account_id' => 456,
                'membership_name' => 'A membership',
                'currency' => 'EUR',
                'is_prorated' => false,
                'is_subscription' => false,
                'use_available_account_balance' => false,
                'allow_overdraft' => false,
                'payer' => [],
                'membership_type' => 'time_classes',
                'prorated_percentage' => null,
            ],
            $result['attributes']
        );

        app()->forgetInstance(PurchaseService::class);
    }

    public function test_validate_membership_returns_error_on_price_validation_error(): void
    {
        $adminUser = $this->fetchUser('59a7011a05c677bda512212a');
        $this->loginAsUser($adminUser);

        $branchId = '49a7011a05c677b9a916612a';
        $userId = '5b19430ea0d988945a164337';
        $membershipId = '54107c1cd7b6ddc3a98b4577';
        $planCode = '*************'; // This is an unlimited plan code

        $mockValidator = Mockery::mock(MembershipPurchasedBeforeValidator::class);
        $mockValidator
            ->shouldReceive('execute')
            ->andReturn();

        app()->instance(MembershipPurchasedBeforeValidator::class, $mockValidator);

        $data = [
            'branchId' => $branchId,
            'userId' => $userId,
            'membershipId' => $membershipId,
            'planCode' => $planCode,
            'payment_method' => 'cash',
            'start_date' => strtotime(date('Y-m-d')),
            'price' => -10.0
        ];

        $result = $this->testAction(
            '/2.2/cart/validate/membership',
            [
                'method' => 'POST',
                'data' => $data,
            ]
        );

        $result = json_decode($result, $assoc = true, 512, JSON_THROW_ON_ERROR);

        $this->assertEquals("PLAN_MINIMUM_PRICE_VALIDATION_ERROR", $result['message_code']);
        $this->assertEquals("PLAN_MINIMUM_PRICE_VALIDATION_ERROR", $result['message']);

        app()->forgetInstance(PurchaseService::class);
    }

    public function test_validate_membership_returns_error_on_purchase_service_error(): void
    {
        $adminUser = $this->fetchUser('58568a8fa875ab19630041a7');
        $this->loginAsUser($adminUser);

        $branchId = '49a7011a05c677b9a916612a';
        $userId = '5b19430ea0d988945a164337';
        $membershipId = '54107c1cd7b6ddc3a98b4577';
        $planCode = '*************'; // This is an unlimited plan code

        $data = [
            'branchId' => $branchId,
            'userId' => $userId,
            'membershipId' => $membershipId,
            'planCode' => $planCode,
            'payment_method' => 'cash',
            'start_date' => strtotime(date('Y-m-d')),
        ];

        $mockValidator = Mockery::mock(MembershipPurchasedBeforeValidator::class);
        $mockValidator
            ->shouldReceive('execute')
            ->andReturn();

        app()->instance(MembershipPurchasedBeforeValidator::class, $mockValidator);

        $purchaseService = \Mockery::mock(PurchaseService::class);
        $purchaseService
            ->shouldReceive('buildParams')
            ->andThrow(new Exception("PURCHASE_SERVICE_DIED"));

        app()->instance(PurchaseService::class, $purchaseService);

        $result = $this->testAction(
            '/2.2/cart/validate/membership',
            [
                'method' => 'POST',
                'data' => $data,
            ]
        );

        $result = json_decode($result, $assoc = true, 512, JSON_THROW_ON_ERROR);

        $this->assertEquals("PURCHASE_SERVICE_DIED", $result['message_code']);
        $this->assertEquals("PURCHASE_SERVICE_DIED", $result['message']);

        app()->forgetInstance(PurchaseService::class);
    }

    /**
     * @dataProvider payloadProvider
     */
    public function test_it_validates_request($data): void
    {
        $user = $this->fetchUser('59a3011a05c677bda916711c');
        $this->loginAsUser($user);

        $result = $this->testAction(
            '/2.2/cart/validate/product',
            [
                'method' => 'POST',
                'data' => $data,
            ]
        );

        $result = json_decode($result, null, 512, JSON_THROW_ON_ERROR);
        $responseCode = $this->response->statusCode();

        $this->assertEquals(400, $responseCode);
        $this->assertFalse($result->success);
    }

    public function test_it_validates_product_stock(): void
    {
        $user = $this->fetchUser('59a3011a05c677bda916711c');
        $this->loginAsUser($user);

        $data = [
            'product_id' => '6261051cbfee9e51e32e54a1',
            'presentation_id' => '1506964157789',
            'user_id' => '59a3011a05c677bda916711c',
            'price' => 10,
            'payment_method' => 'credit_card',
            'quantity' => 2,
        ];

        $result = $this->testAction(
            '/2.2/cart/validate/product',
            [
                'method' => 'POST',
                'data' => $data,
            ]
        );

        $result = json_decode($result, null, 512, JSON_THROW_ON_ERROR);
        $responseCode = $this->response->statusCode();

        $this->assertEquals(400, $responseCode);
        $this->assertFalse($result->success);
        $this->assertSame('INSUFFICIENT_STOCK_FOR_PRODUCT', $result->message);
    }

    public function test_it_validates_product_not_found(): void
    {
        $user = $this->fetchUser('59a3011a05c677bda916711c');
        $this->loginAsUser($user);

        $data = [
            'product_id' => '6267fbfecdcfa0496895ea5d',
            'presentation_id' => '1506964157789',
            'user_id' => '59a3011a05c677bda916711c',
            'price' => 10,
            'payment_method' => 'credit_card',
            'quantity' => 2,
        ];

        $result = $this->testAction(
            '/2.2/cart/validate/product',
            [
                'method' => 'POST',
                'data' => $data,
            ]
        );

        $result = json_decode($result, null, 512, JSON_THROW_ON_ERROR);
        $responseCode = $this->response->statusCode();

        $this->assertEquals(400, $responseCode);
        $this->assertFalse($result->success);
        $this->assertSame('Product not found - id: 6267fbfecdcfa0496895ea5d', $result->message);
    }

    public function test_it_validates_product_presentation_not_found(): void
    {
        $user = $this->fetchUser('59a3011a05c677bda916711c');
        $this->loginAsUser($user);

        $data = [
            'product_id' => '6261051cbfee9e51e32e54a1',
            'presentation_id' => 'test',
            'user_id' => '59a3011a05c677bda916711c',
            'price' => 10,
            'payment_method' => 'credit_card',
            'quantity' => 2,
        ];

        $result = $this->testAction(
            '/2.2/cart/validate/product',
            [
                'method' => 'POST',
                'data' => $data,
            ]
        );

        $result = json_decode($result, null, 512, JSON_THROW_ON_ERROR);
        $responseCode = $this->response->statusCode();

        $this->assertEquals(400, $responseCode);
        $this->assertFalse($result->success);
        $this->assertSame('Presentation 0 not found within Product 6261051cbfee9e51e32e54a1', $result->message);
    }

    public function test_it_validates_product_user_not_found(): void
    {
        $user = $this->fetchUser('59a3011a05c677bda916711c');
        $this->loginAsUser($user);

        $data = [
            'product_id' => '6261051cbfee9e51e32e54a1',
            'presentation_id' => '1506964157789',
            'user_id' => '62691c3e68b92b8fe580133f',
            'price' => 10,
            'payment_method' => 'credit_card',
            'quantity' => 2,
        ];

        $result = $this->testAction(
            '/2.2/cart/validate/product',
            [
                'method' => 'POST',
                'data' => $data,
            ]
        );

        $result = json_decode($result, null, 512, JSON_THROW_ON_ERROR);
        $responseCode = $this->response->statusCode();

        $this->assertEquals(400, $responseCode);
        $this->assertFalse($result->success);
        $this->assertSame('MEMBER_NOT_FOUND', $result->message);
    }

    public function test_pre_checkout_returns_successfully(): void
    {
        $userId = '59a7011a05c677bda916612a';

        $user = $this->fetchUser($userId);
        $this->loginAsUser($user);

        $data = [
            'user_id' => '59a3011a05c677bda916612d',
            'payment_method' => 'CARD',
            'cart_user_role' => 'ADMIN'
        ];

        $idResolverMock = \Mockery::mock(IdResolver::class);
        $idResolverMock
            ->shouldReceive('fromUserIdAndPaymentMethodId')
            ->once()
            ->andReturn('456')
            ->once();
        app()->instance(IdResolver::class, $idResolverMock);

        $overdraftValidator = \Mockery::mock(OverdraftValidatorService::class);
        $overdraftValidator->shouldReceive('validateByRole')->andReturn(false)->once();
        app()->instance(OverdraftValidatorService::class, $overdraftValidator);

        $paymentMethodType = Type::CARD;
        $paymentMethodId = new \MongoId();
        $paymentMethod = PaymentMethod::make([
            '_id' => $paymentMethodId,
            'type_id' => $paymentMethodType,
            'provider' => Provider::make([
                'account_id' => '54321',
            ]),
        ]);

        $capability = \Mockery::mock(Capability::class);
        $capability->shouldReceive('isEnabled')->andReturn(true);
        $paymentProvider = \Mockery::mock(Provider::class);
        $paymentProvider->shouldReceive('paymentMethod')->andReturn($paymentMethod);
        $paymentProvider->shouldReceive('id')->andReturn($paymentMethodId);
        $paymentProvider->shouldReceive('capabilities')->andReturnSelf()->shouldReceive('getCapability')->andReturn(
            $capability
        );

        Auth::loginAs($user);
        $auth = app()->make(Auth::class);
        $auth->paymentMethods[$paymentMethodType] = $paymentProvider;
        app()->instance(Auth::class, $auth);

        $result = $this->testAction(
            sprintf('/2.2/cart/pre-checkout'),
            [
                'method' => 'POST',
                'data' => $data,
            ]
        );

        $result = json_decode($result, $assoc = true, 512, JSON_THROW_ON_ERROR);

        $this->assertEquals(
            [
                'merchant_account_id' => 54321,
                'customer_account_id' => 456,
                'use_available_account_balance' => true,
                'allow_overdraft' => false,
                'user_tax_id' => null,
            ],
            $result['result']
        );

        app()->forgetInstance(PurchaseService::class);
        app()->forgetInstance(OverdraftValidatorService::class);
        app()->forgetInstance(IdResolver::class);
        app()->forgetInstance(Auth::class);
    }

    public function payloadProvider(): array
    {
        return [
            [
                [
                    'presentation_id' => 'test-presentation-id',
                    'user_id' => 'test-user-id',
                    'price' => 10,
                    'payment_method' => 'card',
                    'quantity' => 1,
                ],
            ],
            [
                [
                    'product_id' => 'test-product-id',
                    'user_id' => 'test-user-id',
                    'price' => 10,
                    'payment_method' => 'card',
                    'quantity' => 1,
                ],
            ],
            [
                [
                    'product_id' => 'test-product-id',
                    'presentation_id' => 'test-presentation-id',
                    'price' => 10,
                    'payment_method' => 'card',
                    'quantity' => 1,
                ],
            ],
            [
                [
                    'product_id' => 'test-product-id',
                    'presentation_id' => 'test-presentation-id',
                    'user_id' => 'test-user-id',
                    'payment_method' => 'card',
                    'quantity' => 1,
                ],
            ],
            [
                [
                    'product_id' => 'test-product-id',
                    'presentation_id' => 'test-presentation-id',
                    'user_id' => 'test-user-id',
                    'price' => 10,
                    'quantity' => 1,
                ],
            ],
            [
                [
                    'product_id' => 'test-product-id',
                    'presentation_id' => 'test-presentation-id',
                    'user_id' => 'test-user-id',
                    'price' => 10,
                    'payment_method' => 'card',
                ],
            ],
        ];
    }
}
