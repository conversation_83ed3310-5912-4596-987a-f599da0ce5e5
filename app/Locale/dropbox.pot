# LANGUAGE translation of CakePHP Application
# <AUTHOR> <EMAIL>
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PROJECT VERSION\n"
"POT-Creation-Date: 2014-08-12 08:18+0000\n"
"PO-Revision-Date: YYYY-mm-DD HH:MM+ZZZZ\n"
"Last-Translator: NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <EMAIL@ADDRESS>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=INTEGER; plural=EXPRESSION;\n"

#: Plugin/Dropbox/Controller/Component/DropboxApiComponent.php:97
msgid "Please create your dropbox_token and dropbox_token_secret fields in your user model."
msgstr ""

#: Plugin/Dropbox/Model/Datasource/DropboxSource.php:193
msgid "Error decoding json, run json_last_error() after to see the error code."
msgstr ""

#: Plugin/Dropbox/Model/Datasource/DropboxSource.php:223
msgid "Sorry, that find method is not supported."
msgstr ""

