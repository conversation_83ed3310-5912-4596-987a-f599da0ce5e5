<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\PaymentProviders\Models;

\App::import('Test/Case', 'GlofoxTestCase');

use Glofox\Domain\PaymentProviders\Models\PaymentProvider;
use Glofox\Domain\PaymentProviders\Repositories\PaymentProvidersRepository;
use Glofox\Repositories\Search\Expressions\Shared\Id;

/**
 * Class PaymentProviderTest.
 */
class PaymentProviderTest extends \GlofoxTestCase
{
    public $fixtures = [
        'app.payment_provider',
    ];
    protected PaymentProvidersRepository $paymentProviderRepo;

    public function setUp()
    {
        parent::setUp();
        $this->paymentProviderRepo = app()->make(PaymentProvidersRepository::class);
    }

    public function test_it_handles_no_features(): void
    {
        /** @var PaymentProvider $provider */
        $provider = $this->paymentProviderRepo
            ->addCriteria(new Id('5b2a3489c7805f005e37dcd4'))
            ->firstOrFail();

        $ieProvider = $provider->availableCountries()->findByCountryCode("IE");
        self::assertEquals(2.4, $ieProvider->defaultChargePercentage());
        self::assertEquals([], $ieProvider->features());
        self::assertFalse($ieProvider->isLegacyOnboarding());
    }

    public function test_it_handles_features(): void
    {
        /** @var PaymentProvider $provider */
        $provider = $this->paymentProviderRepo
            ->addCriteria(new Id('5b2a3489c7805f005e37dcb3'))
            ->firstOrFail();

        $ieProvider = $provider->availableCountries()->findByCountryCode("IE");
        self::assertEquals(2.4, $ieProvider->defaultChargePercentage());
        self::assertEquals(['GLOBAL_PAYOUTS', 'LEGACY_ONBOARDING'], $ieProvider->features());
        self::assertTrue($ieProvider->isLegacyOnboarding());
    }

    public function test_it_handles_default_scheme_identifier(): void
    {
        // missing metadata
        $provider = PaymentProvider::make([]);
        $this->assertEquals('', $provider->defaultSchemeIdentifier());

        // missing url
        $provider = PaymentProvider::make([
            'metadata' => [],
        ]);
        $this->assertEquals('', $provider->defaultSchemeIdentifier());

        // success
        $provider = PaymentProvider::make([
            'metadata' => [
                'default_scheme_identifier' => 'identifier'
            ],
        ]);
        $this->assertEquals('identifier', $provider->defaultSchemeIdentifier());
    }

    public function test_it_handles_direct_debit_guarantee_url(): void
    {
        // missing metadata
        $provider = PaymentProvider::make([]);
        $this->assertEquals('', $provider->directDebitGuaranteeUrl());

        // missing url
        $provider = PaymentProvider::make([
            'metadata' => [],
        ]);
        $this->assertEquals('', $provider->directDebitGuaranteeUrl());

        // success
        $provider = PaymentProvider::make([
            'metadata' => [
                'direct_debit_guarantee_url' => 'http://url'
            ],
        ]);
        $this->assertEquals('http://url', $provider->directDebitGuaranteeUrl());
    }

}
