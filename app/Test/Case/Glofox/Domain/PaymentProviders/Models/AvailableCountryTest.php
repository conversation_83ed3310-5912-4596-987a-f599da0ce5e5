<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\PaymentProviders\Models;

\App::import('Test/Case', 'GlofoxTestCase');

use Carbon\Carbon;
use Glofox\Domain\PaymentProviders\Models\AvailableCountry;

/**
 * Class PaymentProviderTest.
 */
class AvailableCountryTest extends \GlofoxTestCase
{
    public $fixtures = [];

    public function setUp()
    {
        parent::setUp();
    }

    public function test_it_handles_adding_features(): void
    {
        $availableCountry = AvailableCountry::make([]);
        $this->assertEquals([], $availableCountry->features());
        $availableCountry->addFeature(["feature1"]);
        $this->assertEquals(["feature1"], $availableCountry->features());
        $availableCountry->addFeature(["feature2"]);
        $this->assertEquals(["feature1", "feature2"], $availableCountry->features());
    }
}
