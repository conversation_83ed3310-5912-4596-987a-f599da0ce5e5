# @see http://blog.michaelperrin.fr/2017/04/14/docker-for-mac-on-a-symfony-app/
OS := $(shell uname)

PRIVATE_OPENAPI_ROOT_FILE = "./docs/private-openapi/openapi.yml"
PRIVATE_OPENAPI_OUTPUT_DIST = "./docs/private-openapi/dist"
APP_ENV_FILE = "./docker/config/local/override/app.env"
APP_ENV_DIST_FILE = "./docker/config/local/override/app.env.dist"

.PHONY: help
help: ## Makefile manual
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-30s\033[0m %s\n", $$1, $$2}'

build: ## Build docker compose images
	@test -f ${APP_ENV_FILE} || cp ${APP_ENV_DIST_FILE} ${APP_ENV_FILE}
	@mkdir -p data/redis
	@docker compose build
	@make up

rebuild-container: | down ## Rebuilds a single container. Params: container = container service name
ifndef container
	$(error container is required)
endif
	@docker compose build ${container}
	@make up

up: ## Turn up docker compose containers and install composer dependencies
	@docker compose up -d
	@make composer-install

down: ## Turn down docker composer containers
	@docker compose down

stop: ## Stops all containers
	@docker compose stop

composer-install: ## Installs / updates composer dependencies
	@docker exec glofox_app composer install --no-scripts --profile --prefer-dist

install-swagger-cli: ## Install swagger-cli dependencies
	@npm list -g swagger-cli || npm install -g swagger-cli

private-docs: | install-swagger-cli ## Generate swagger private docs
	@swagger-cli bundle ${PRIVATE_OPENAPI_ROOT_FILE} --outfile ${PRIVATE_OPENAPI_OUTPUT_DIST}/openapi.json --type json
	@swagger-cli bundle ${PRIVATE_OPENAPI_ROOT_FILE} --outfile ${PRIVATE_OPENAPI_OUTPUT_DIST}/openapi.yml --type yaml

private-docs-lint: | install-swagger-cli ## Validate swagger private docs
	@swagger-cli validate ${PRIVATE_OPENAPI_ROOT_FILE}

test: ## Run specific test. Params: file = test file relative path; test (opt) = specific test to run
ifndef file
	$(error file is required)
endif

ifdef test
	docker exec glofox_app ./app/Console/cake test app ${file} --filter ${test}
else
	docker exec glofox_app ./app/Console/cake test app ${file}
endif

test-ci: ## Run multiple tests like CCI jobs. Params: nodeTotal = total number of nodes; nodeIndex = node index to run
ifndef nodeTotal
	$(error nodeTotal is required)
endif

ifndef nodeIndex
	$(error nodeIndex is required)
endif

	docker exec -e CIRCLE_NODE_TOTAL=$(nodeTotal) -e CIRCLE_NODE_INDEX=$(nodeIndex) glofox_app ./app/Console/cake test --debug --no-colors app AllTests

test-all: ## Run all tests
	docker exec -e CIRCLE_NODE_TOTAL=1 -e CIRCLE_NODE_INDEX=0 glofox_app ./app/Console/cake test --debug --no-colors app AllTests

check-compatibility: ## Run PHPCompatibility library. Params: version = php version to check; report (opt) = file to store the full report
ifndef version
	$(error version is required)
endif

ifdef report
	docker exec glofox_app ./vendor/bin/phpcs -p ./app --standard=PHPCompatibility --runtime-set testVersion ${version} --report-full=${report}
else
	docker exec glofox_app ./vendor/bin/phpcs -p ./app --standard=PHPCompatibility --runtime-set testVersion ${version}
endif

# TODO manual formatting file
# format:

generate-rector-paths: ## This command will generate rector-paths.json based on the GIT DIFF
	@sh ./contrib/scripts/generate-rector-paths.sh

mongo-upgrade: ## This command will upgrade the MongoDB version of the application
ifndef version
	$(error version is required)
endif

# Installing yq if not installed
ifeq (, $(shell which yq))
	brew install yq
endif

# Detecting current mongo version from circleci config
	$(eval CURRENT_MONGO_VERSION := $(shell yq '.parameters.mongo-version.default' .circleci/config.yml))

# Checking if the version is already in use
ifeq ($(CURRENT_MONGO_VERSION), $(version))
	$(error Version already in use)
endif

# Updating mongo version references to the new version
	sed -i '' 's/default: $(CURRENT_MONGO_VERSION)/default: $(version)/g' .circleci/config.yml
	sed -i '' 's/mongo-$(CURRENT_MONGO_VERSION)/mongo-$(version)/g' docker-compose.yml
