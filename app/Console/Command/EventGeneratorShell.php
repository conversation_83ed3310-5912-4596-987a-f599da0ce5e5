<?php

ini_set('max_execution_time', 600);

App::uses('AppShell', 'Console/Command');
App::uses('ComponentCollection', 'Controller');
App::uses('Controller', 'Controller');
App::uses('EventGeneratorComponent', 'Controller/Component');

/**
 * Class EventGeneratorShell
 *
 * @property EventGeneratorComponent $EventGeneratorComponent
 * @property User $User
 * @property Branch $Branch
 * @property Program $Program
 * @property Event $Event
 */
class EventGeneratorShell extends Shell
{
    public $uses = ['User','Branch', 'Program', 'Event'];

    protected $EventGenerator;

    use \Glofox\Console\RegionSpecific;

    public function main()
    {
        $this->validateRegionParameter();

        $environment = $this->Branch->get_current_environment();

        if ($environment != 'local') {
            App::import('ConnectionManager');
        }


        error_reporting(0);
        $this->log("~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~", LOG_INFO);
        $this->log("Cron Started", LOG_INFO);

        $collection = new ComponentCollection();
        $this->EventGenerator = new EventGeneratorComponent($collection);
        $this->EventGenerator->loadModels();

        $conditions = [
            'active' => true
        ];

        // @TODO use Criteria classes here
        /** @var \Glofox\Domain\Region\ContinentResolver $continentResolver */
        $continentResolver = $this->container()->make(\Glofox\Domain\Region\ContinentResolver::class);
        $conditions['address.continent'] = ['$in' => $continentResolver->resolve($this->region) ];

        $branches = $this->Branch->find('all', ['conditions' => $conditions ]);

        foreach ($branches as $branch) {
            $this->log("Generating for Branch: ". $branch['Branch']['_id'], LOG_INFO);
            $this->EventGenerator->generateByBranch($branch);
        }

        $this->log("Cron Finished", LOG_INFO);
    }
}
