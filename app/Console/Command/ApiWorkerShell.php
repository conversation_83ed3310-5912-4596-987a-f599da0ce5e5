<?php

use Glofox\Eventkit\Consumer\DomainEventConsumer;

class ApiWorkerShell extends Shell
{
    public function main()
    {
        $count = 0;
        while (!newrelic_set_appname(ini_get('newrelic.appname')) && $count < 100) {
            ++$count;
            sleep(1);
            $this->log('Attempt to connect to New Relic daemon #' . $count, LOG_INFO);
        }
        newrelic_start_transaction(ini_get('newrelic.appname'));

        $this->log('Starting API SQS Worker', LOG_INFO);

        /** @var DomainEventConsumer $consumer */
        $consumer = app()->make(DomainEventConsumer::class);
        $consumer->startConsumingDomainEvents();
    }
}
