
## Switch Branch [/2.1/branches/{branchId}/users/{userId}/switch?to={targetBranchId}]

+ Parameters
    + branchId: 507f191e810c19729de860ea (string) - The identification of the branch is switching from.
    + userId: 5b55e5742fc1bbef53293352 (string) - The identification of the user.
    + targetBranchId (string) - The identification of the branch the user is switching to.

+ Attributes (Authentication Token)

### Generate Token of Romaing Branches for User [POST]

+ Request
    + Headers
          Authorization: Bearer {authToken}

+ Response 200 (application/json)
    + Attributes (Authentication Token)

+ Response 400 (application/json)
    + Attributes (Generic Error)