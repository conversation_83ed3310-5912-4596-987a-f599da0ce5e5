<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Export\Strategies;

use App;
use Dompdf\Dompdf;
use Glofox\Export\FileExport;
use Glofox\Export\Strategies\DomPdfStrategy;
use GlofoxTestCase;
use Mockery;

App::import('Test/Case', 'GlofoxTestCase');

class DomPdfStrategyTest extends GlofoxTestCase
{
    public $fixtures = [];

    public function test_export_loads_renders_and_returns_a_file(): void
    {
        $content = '<div class="page">DummyHtml</div>';

        $dompdfMockOne = Mockery::mock(Dompdf::class);
        $dompdfMockOne
            ->shouldReceive('loadHtml')
            ->withArgs(static function (string $params) use ($content) {
                self::assertEquals($params, $content);

                return true;
            })
            ->andReturnSelf()
            ->once()
            ->getMock()
            ->shouldReceive('render')
            ->andReturnSelf()
            ->once()
            ->getMock()
            ->shouldReceive('output')
            ->andReturn($content)
            ->once()
            ->getMock();

        $dompdfMockTwo = Mockery::mock(Dompdf::class);
        $strategyOne = new DomPdfStrategy($dompdfMockOne);
        app()->instance(Dompdf::class, $dompdfMockTwo);

        $pdfPropertyOne = new \ReflectionProperty($strategyOne, 'pdf');
        $pdfPropertyOne->setAccessible(true);
        $newInstance = $pdfPropertyOne->getValue($strategyOne);
        $this->assertSame(spl_object_id($newInstance), spl_object_id($dompdfMockOne));

        $file = $strategyOne->export($content);
        self::assertEquals(new FileExport($content, 'pdf'), $file);

        $pdfPropertyOne = new \ReflectionProperty($strategyOne, 'pdf');
        $pdfPropertyOne->setAccessible(true);
        $newInstance = $pdfPropertyOne->getValue($strategyOne);
        $this->assertSame(spl_object_id($newInstance), spl_object_id($dompdfMockTwo));
        app()->forgetInstance(Dompdf::class);
    }

    public function test_download_streams_from_dompdf(): void
    {
        $filename = 'bar';
        $content = '<div class="page">DummyHtml</div>';

        $dompdfMock = Mockery::mock(Dompdf::class);
        $dompdfMock
            ->shouldReceive('loadHtml')
            ->withArgs(static function (string $params) use ($content) {
                self::assertEquals($params, $content);

                return true;
            })
            ->andReturnSelf()
            ->once()
            ->getMock()
            ->shouldReceive('render')
            ->andReturnSelf()
            ->once()
            ->getMock()
            ->shouldReceive('stream')
            ->withArgs(static function (string $name, array $attachment) use ($filename) {
                self::assertEquals($name, $filename);
                self::assertEquals(['Attachment' => 1], $attachment);

                return true;
            })
            ->andReturnSelf()
            ->once()
            ->getMock();

        $strategy = new DomPdfStrategy($dompdfMock);
        $strategy->download($content, $filename);
    }
}
