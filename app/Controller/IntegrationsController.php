<?php

use Glofox\Domain\Integrations\ActionManager;
use Glofox\Domain\Integrations\Contracts\TokenizableIntegration;
use Glofox\Domain\Integrations\Factory as IntegrationFactory;
use Glofox\Domain\Integrations\Http\Authorization\RequestAuthorizer;
use Glofox\Domain\Integrations\Http\IntegrationsController as HttpIntegrationsController;
use Glofox\Request;

App::uses('AppController', 'Controller');

class IntegrationsController extends HttpIntegrationsController
{
    /**
     * @var string
     */
    public $name = 'Integrations';

    /**
     * @var array
     */
    public $components = [
        'Utility',
        'JWT',
    ];

    /**
     * @var array
     */
    public $uses = [
        'Integration',
        'Branch',
    ];

    private array $validationHeaders = [
        'HTTP_X_HOOK_SECRET' => 'X-Hook-Secret',
    ];

    public function dispatcher($identifier = null)
    {
        foreach ($this->request->query as $param) {
            if (is_array($param)) {
                throw new \Glofox\Exception('Invalid query parameter exception');
            }
        }

        return parent::dispatch($identifier, $this->Integration);
    }

    public function confirm($identifier)
    {
        return json_encode($this->Integration->confirm($identifier, $this->getPayload()), JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    public function sync($identifier)
    {
        $this->Integration->sync($identifier, $this->getPayload());
        // Make sure to always return the validation headers if they are passed
        foreach ((array) $_SERVER as $key => $value) {
            if (isset($this->validationHeaders[$key])) {
                $this->response->header($this->validationHeaders[$key], $value);
            }
        }
        $this->response->header('Content-Type', 'text/plain');
        $this->response->statusCode(200);

        return 'OK';
    }

    public function entrypoint()
    {
        try {
            (app()->make(RequestAuthorizer::class))
                ->authorize(Request::capture(), $this->getRequestParameter('branch_id'));

            $this->integrationFactory = new IntegrationFactory();
            $service = 'ClassPass'; // HARDCODED TO TEST
            $integration = $this->integrationFactory->create($service);
            // Pending Authentication
            if ($integration instanceof TokenizableIntegration) {
                $this->setIntegrationToken($integration);
            }

            return (new ActionManager($integration, $this->request))->resolve()->handle();
        } catch (Exception $e) {
            $this->response->statusCode('400'); // Improve Set Status Code

            $response = [
                'success' => false,
                'message' => $e->getMessage(),
                'message_code' => $e->getMessage(),
            ];

            return json_encode($response, JSON_PARTIAL_OUTPUT_ON_ERROR);
        }
    }

    private function getRequestParameter(string $paramName)
    {
        $payload = $this->getPayload();

        return $payload[$paramName] ?? null;
    }

    private function getBranchByIdOrNamespace($branchId, $namespace)
    {
        $fields = ['_id', 'namespace'];
        $conditions = [];

        if (!empty($branchId)) {
            $conditions['_id'] = $branchId;
        }
        if (!empty($namespace)) {
            $conditions['namespace'] = $namespace;
        }

        return $this->Branch->find('first', ['conditions' => $conditions, 'fields' => $fields]);
    }

    private function setIntegrationToken(TokenizableIntegration $integration): void
    {
        $reqBranchId = $this->getRequestParameter('branch_id');
        $reqNamespace = $this->getRequestParameter('namespace');
        if (!$reqBranchId && !$reqNamespace) {
            throw new BadRequestException('NO_BRANCH_ID_OR_NAMESPACE_PROVIDED');
        }

        $branch = $this->getBranchByIdOrNamespace($reqBranchId, $reqNamespace);
        if (!$branch) {
            throw new NotFoundException('BRANCH_NOT_FOUND');
        }

        $namespace = $branch['Branch']['namespace'];
        $branchId = $branch['Branch']['_id'];
        $token = $this->JWT->generateFromIntegration($integration, $namespace, $branchId);
        $_SERVER['HTTP_AUTHORIZATION'] = $token; // TO BE REFACTORED
    }
}
