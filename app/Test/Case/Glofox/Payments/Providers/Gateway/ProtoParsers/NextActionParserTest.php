<?php

namespace CakeTestCases\Glofox\Payments\Providers\Gateway\ProtoParsers;

use Glofox\Payments\Providers\Gateway\ProtoParsers\NextActionParser;
use GRPC\Payments\AccountService\NextAction;

\App::import('Test/Case', 'GlofoxTestCase');

class NextActionParserTest extends \GlofoxTestCase
{
    public function testParseFromProtoMessage()
    {
        $a = new NextAction();
        $a->setType('type');

        $parser = new NextActionParser();

        // no redirect url
        $parsed = $parser->parseFromProtoMessage($a);
        self::assertEquals('type', $parsed->getType());
        self::assertNull($parsed->getRedirectURL());

        // with redirect url
        $a->setRedirectURL('url');

        $parsed = $parser->parseFromProtoMessage($a);
        self::assertEquals('type', $parsed->getType());
        self::assertEquals('url', $parsed->getRedirectURL());
    }
}
