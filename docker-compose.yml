# DO NOT OVERWRITE OR CH<PERSON><PERSON> THIS FILE
# Any local change must be implemented into docker-compose.override.yml file
services:
  app:
    container_name: glofox_app
    image: glofox_app
    build:
      context: .
      dockerfile: "./Dockerfile"
      args:
        ENV: "non-platform"
        GITHUB_ACCESS_TOKEN: ${GITHUB_ACCESS_TOKEN}
    tty: true
    ports:
      - "8889:80"
    volumes:
      - .:/var/www
    working_dir: /var/www
    env_file:
      - docker/config/local/aws.env
      - docker/config/local/app.env
      - docker/config/local/override/app.env
    depends_on:
      - db
      - cache
    links:
      - db
      - cache
    networks:
      - default

  db:
    container_name: glofox_db
    image: mongo:7.0.18
    tty: true
    volumes:
      - ./db:/db
    ports:
      - "27036:27017"
    command: mongod --setParameter logLevel=1
    networks:
      - default

  cache:
    image: redis:5.0.3-alpine
    volumes:
      - ./data/redis/:/data
    ports:
      - 6380:6379
    networks:
      - default

networks:
  default:
    driver: bridge
