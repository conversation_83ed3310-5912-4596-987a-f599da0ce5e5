type: object
properties:
  success:
    type: boolean
    default: false
  message:
    type: string
    enum:
      - You must select an event
      - The selected event type is invalid
      - You must select a rate type
      - You must provide a rate price
      - The selected event type is invalid
      - The rate price must be a number
      - The rate price cannot be less than one
      - The rate price is too large
  message_code:
    type: string
    enum:
      - PAYROLL_RATE_MISSING_EVENT_ID_FIELD
      - PAYROLL_RATE_EVENT_ID_MUST_BE_A_STRING
      - PAYROLL_RATE_MISSING_RATE_TYPE_FIELD
      - PAYROLL_RATE_MISSING_RATE_PRICE_FIELD
      - PAYROLL_RATE_INVALID_EVENT_TYPE
      - PAYROLL_RATE_PRICE_MUST_BE_AN_INTEGER
      - PAYROLL_RATE_PRICE_MIN_ERROR
      - PAYROLL_RATE_PRICE_MAX_ERROR
  message_data:
    type: array
    items:
      type: string
  errors:
    type: array
    items:
      type: string
