## Event Pricing [/2.1/branches/{branchId}/events/{eventId}/price?for={userId}&guestBookings={guestBookings}]

+ Parameters
    + branchId: 507f191e810c19729de860ea (string) - The identification of the Branch.
    + eventId - The identification of the Event.
    + userId: 5b55e5742fc1bbef53293352 (string) - The user who's fetching the price for the Event.
    + guestBookings (optional) - The number of guests joining the user.

+ Attributes
    + credits: 3 (number, nullable)
    + price: 15 (number, nullable)
    + currency: EUR (string)

### Retrieve Price for Event [GET]

+ Request
    + Headers
          Authorization: Bearer {authToken}

+ Response 200 (application/json)
    + Attributes
        + data (Event Pricing)

+ Response 400 (application/json)
    + Attributes (Generic Error)