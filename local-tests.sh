#!/bin/bash

#Get Secret Values from SSM
GITHUB_ACCESS_TOKEN=$(aws ssm get-parameter --name "/glofox/api/local-tests/GITHUB_ACCESS_TOKEN" --with-decryption | jq -r .'[].Value')
SENDGRID_PASSWORD=$(aws ssm get-parameter --name "/glofox/api/local-tests/SENDGRID_PASSWORD" --with-decryption | jq -r .'[].Value')

CIRCLE_SHA1=test1234

export CIRCLE_SHA1=$CIRCLE_SHA1

#Kill all running containers
docker kill $(docker ps -q)
#Remove Docker Network
docker network rm db-test1234
#Login to ECR
aws ecr get-login-password | docker login -u AWS --password-stdin 108272622497.dkr.ecr.eu-west-1.amazonaws.com
#Build Docker Image
docker build --build-arg ENV=non-platform --build-arg GITHUB_ACCESS_TOKEN=$GITHUB_ACCESS_TOKEN --build-arg VERSION=local-tests -t 108272622497.dkr.ecr.eu-west-1.amazonaws.com/api:local-build .
#Create Docker Network
docker network create db-$CIRCLE_SHA1
#Run Redis
docker run --rm -d --name redis-$CIRCLE_SHA1 --net=db-$CIRCLE_SHA1 --expose 6379 redis:5.0.3-alpine
#Run Mongo
docker run --rm -d --name db-$CIRCLE_SHA1 --net=db-$CIRCLE_SHA1 mongo:4.0.23
#Wait for Mongo to come up
./docker/mongowait.sh
#Run API Container
docker run --rm --net=db-$CIRCLE_SHA1 -e GLOFOX_DATABASE_HOST=db-$CIRCLE_SHA1 -e GLOFOX_REDIS_URL=redis-$CIRCLE_SHA1:6379 -e SENDGRID_PASSWORD=$SENDGRID_PASSWORD --env-file docker/test/localhost.env --name tests-$CIRCLE_SHA1 -d 108272622497.dkr.ecr.eu-west-1.amazonaws.com/api:local-build
#Run Tests
docker exec tests-$CIRCLE_SHA1 bash -c "./contrib/scripts/xdebug.sh disable && ./app/Console/cake test --debug --no-colors app AllTests"
