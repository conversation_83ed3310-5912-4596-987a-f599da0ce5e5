<?php

declare(strict_types=1);

App::import('Test/Case', 'GlofoxTestCase');

use Glofox\Response\Models\Branches\Configuration\Webportal\Features\EventDetailsModel;

/**
 * Test cases
 * 1 Model contains all fields with data
 * 2 Missing fields and returns correct structure
 * 3 Additional fields and return correct structure
 * 4 Empty array fields and return correct structure.
 *
 * Class EventDetailsModelTest
 */
class EventDetailsModelTest extends GlofoxTestCase
{
    public $fixtures = [];

    /**
     * @dataProvider modelProvider
     */
    public function testModel($dataSent, $result): void
    {
        $model = new EventDetailsModel($dataSent);
        $this->assertEquals($model, $result);
    }

    public function modelProvider(): array
    {
        return [
            '1 Model contains all fields with data' => [
                [
                    'facility_name' => false,
                    'level' => false,
                    'price' => false,
                    'size' => false,
                    'total_bookings' => false,
                    'total_waiting_list' => false,
                    'trainer_name' => true,
                ],
                new EventDetailsModel(
                    [
                        'facility_name' => false,
                        'level' => false,
                        'price' => false,
                        'size' => false,
                        'total_bookings' => false,
                        'total_waiting_list' => false,
                        'trainer_name' => true,
                    ]
                ),
            ],
            '2 Missing fields and returns correct structure' => [
                [
                    // facilities missing
                    'level' => false,
                    'price' => false,
                    'size' => false,
                    'total_bookings' => false,
                    'total_waiting_list' => false,
                    'trainer_name' => true,
                ],
                new EventDetailsModel(
                    [
                        'facility_name' => false,
                        'level' => false,
                        'price' => false,
                        'size' => false,
                        'total_bookings' => false,
                        'total_waiting_list' => false,
                        'trainer_name' => true,
                    ]
                ),
            ],
            '3 Additional fields and return correct structure' => [
                [
                    'test' => '',
                    'facility_name' => false,
                    'level' => false,
                    'price' => false,
                    'size' => false,
                    'total_bookings' => false,
                    'total_waiting_list' => false,
                    'trainer_name' => true,
                ],
                new EventDetailsModel(
                    [
                        'facility_name' => false,
                        'level' => false,
                        'price' => false,
                        'size' => false,
                        'total_bookings' => false,
                        'total_waiting_list' => false,
                        'trainer_name' => true,
                    ]
                ),
            ],
            '4 Empty array fields and return correct structure' => [
                [],
                new EventDetailsModel(
                    [
                        'facility_name' => false,
                        'level' => false,
                        'price' => false,
                        'size' => false,
                        'total_bookings' => false,
                        'total_waiting_list' => false,
                        'trainer_name' => false,
                    ]
                ),
            ],
        ];
    }
}
