<?php

namespace Glofox\Payments\Providers\Gateway\Merchants\DataModellers;

use Carbon\Carbon;
use Glofox\Domain\Branches\Models\Branch;
use Glofox\Domain\PaymentProviders\FieldOriginType;
use Glofox\Domain\PaymentProviders\FieldType;
use Glofox\Domain\PaymentProviders\Models\PaymentProvider;
use Glofox\Payments\Util;
use GuzzleHttp\Client;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Validator;
use libphonenumber\NumberParseException;
use libphonenumber\PhoneNumberFormat;
use libphonenumber\PhoneNumberUtil;
use Saritasa\Laravel\Validation\Rule;

/**
 * Class StripeCustomLegacyOnboardingDataModeller.
 */
class StripeCustomLegacyOnboardingDataModeller implements DataModellerContract
{
    /**
     * @param PaymentProvider $paymentProvider
     * @param Branch          $branch
     * @param Collection      $payload
     *
     * @return Collection
     */
    public function modelForCreating(PaymentProvider $paymentProvider, Branch $branch, Collection $payload): Collection
    {
        Validator::make($payload->all(), [
            // company
            'business_name' => Rule::required()->toArray(),
            'business_email' => Rule::required()->toArray(),
            'business_phone' => Rule::required()->toArray(),
            'business_address_street' => Rule::required()->toArray(),
            'business_address_city' => Rule::required()->toArray(),
            'business_address_state' => Rule::required()->toArray(),
            'business_address_postal_code' => Rule::required()->toArray(),
            'business_address_country' => Rule::required()->toArray(),
            'business_mcc' => Rule::sometimes()->required()->min(4)->max(4)->toArray(),
            'external_account' => Rule::required()->toArray(),
            'payout_statement' => Rule::sometimes()->required()->min(1)->toArray(),
            'statement' => Rule::sometimes()->required()->min(1)->toArray(),
            'verification_file_name' => Rule::required()->toArray(),
            'verification_file_base64' => Rule::required()->toArray(),
            //'verification_back_file_name' => Rule::required()->toArray(), // uncomment when the golang stripe api is updated to handle this
            //'verification_back_file_base64' => Rule::required()->toArray(), // uncomment when the golang stripe api is updated to handle this

            // company representative
            'owner.date_of_birth' => Rule::required()->date()->toArray(),
            'owner.first_name' => Rule::required()->toArray(),
            'owner.last_name' => Rule::required()->toArray(),
            'owner.phone_number' => Rule::required()->toArray(),
            'owner.gender' => Rule::required()->in(['male', 'female'])->toArray(),
        ])->validate();

        return collect($this->buildFieldsForCreate($paymentProvider, $branch, $payload));
    }

    /**
     * @param PaymentProvider $paymentProvider
     * @param Branch          $branch
     * @param Collection      $payload
     *
     * @return Collection
     */
    public function modelForUpdating(PaymentProvider $paymentProvider, Branch $branch, Collection $payload): Collection
    {
        Validator::make($payload->all(), [
            // company
            'external_account' => 'sometimes|required|min:1',
            'payout_statement' => 'sometimes|required|min:1',
            'statement' => 'sometimes|required|min:1',
        ])->validate();

        $updatingFields = [
            'Email' => $branch->get('email'),
            'PhoneNumber' => $branch->get('phone'),
        ];

        if (!empty($payload->get('external_account'))) {
            $updatingFields['ExternalAccount'] = ['Token' => $payload->get('external_account')];
        }

        if (!empty($payload->get('statement'))) {
            $updatingFields['Statement'] = $payload->get('statement');
        }

        if (!empty($payload->get('payout_statement'))) {
            $updatingFields['PayoutStatement'] = $payload->get('payout_statement');
        }

        return collect($updatingFields);
    }

    /**
     * @param PaymentProvider $paymentProvider
     * @param Branch          $branch
     *
     * @return Collection
     */
    public function modelForRegisterUrl(PaymentProvider $paymentProvider, Branch $branch): Collection
    {
        return collect([
            'provider_id' => $paymentProvider->id(),
            'publishable_key' => env('GLOFOX_' . $paymentProvider->name() . '_PUBLISHABLE_KEY'),
            'features' => $paymentProvider->availableCountries()->findByCountryCode($branch->address()->countryCode())->features()
        ]);
    }

    /**
     * @param PaymentProvider $paymentProvider
     * @param Branch     $branch
     * @param Collection $payload
     *
     * @return array
     */
    private function buildFieldsForCreate(PaymentProvider $paymentProvider, Branch $branch, Collection $payload): array
    {
        $companyRepBirthDate = Carbon::parse(data_get($payload, 'owner.date_of_birth'));

        $registrationFields = [
            'Email' => $payload->get('business_email'),
            'PhoneNumber' => $this->formatPhoneNumber($payload->get('business_phone'), $branch),
            'ExternalAccount' => ['Token' => $payload->get('external_account')],
            'Statement' => $payload->get('statement'),
            'PayoutStatement' => $payload->get('payout_statement', 'Glofox Payment'),
            'BusinessName' => $payload->get('business_name'),
            'Country' => $branch->address()->countryCode(),
            'LegalEntity' => [
                'Type' => 'company',
                'AdditionalOwnersEmpty' => true,
                'AdditionalOwners' => null,
                'BusinessName' => $payload->get('business_name'),
                'First' => data_get($payload, 'owner.first_name'),
                'Last' => data_get($payload, 'owner.last_name'),
                'PhoneNumber' => $this->formatPhoneNumber(data_get($payload, 'owner.phone_number'), $branch),
                'Gender' => data_get($payload, 'owner.gender'),
                'DOB' => [
                    'Day' => (int) $companyRepBirthDate->format('d'),
                    'Month' => (int) $companyRepBirthDate->format('m'),
                    'Year' => (int) $companyRepBirthDate->format('Y'),
                ],
                'Address' => [
                    'Line1' => $payload->get('business_address_street'),
                    'City' => $payload->get('business_address_city'),
                    'State' => $payload->get('business_address_state'),
                    'Country' => $payload->get('business_address_country'),
                    'PostalCode' => $payload->get('business_address_postal_code'),
                ],
                'PersonalAddress' => [
                    'Line1' => $branch->address()->street(),
                    'City' => $branch->address()->city(),
                    'State' => $branch->address()->state(),
                    'Country' => $branch->address()->countryCode(),
                    'PostalCode' => $branch->address()->postalCode(),
                ],
            ],
            'Meta' => [
                'verification_file_name' => $payload->get('verification_file_name'),
                'verification_file_base64' => $payload->get('verification_file_base64'),
                //'verification_back_file_name' => $payload->get('verification_back_file_name'), // uncomment when the golang stripe api is updated to handle this
                //'verification_back_file_base64' => $payload->get('verification_back_file_base64'), // uncomment when the golang stripe api is updated to handle this
                'BranchId' => $branch->id(),
            ],
            'TOSAcceptance' => [
                'Date' => Carbon::now()->getTimestamp(),
                'UserAgent' => $_SERVER['HTTP_USER_AGENT'],
                'IP' => Util::getRequestClientIP(),
            ],
            'Features' => $paymentProvider->availableCountries()->findByCountryCode($branch->address()->countryCode())->features()
        ];

        if (!empty($payload->get('business_vat_id'))) {
            $registrationFields['LegalEntity']['BusinessVatID'] = $payload->get('business_vat_id');
        }

        if (!empty($payload->get('business_tax_id'))) {
            $registrationFields['LegalEntity']['BusinessTaxID'] = $payload->get('business_tax_id');
        }

        if (!empty(data_get($payload, 'business_mcc'))) {
            $registrationFields['LegalEntity']['MCC'] = data_get($payload, 'business_mcc');
        }

        if (!empty($payload->get('ssn_last_4'))) {
            $registrationFields['LegalEntity']['SSN'] = $payload->get('ssn_last_4');
        }

        if (!empty(data_get($payload, 'owner.person_id_number'))) {
            $registrationFields['LegalEntity']['PersonalID'] = data_get($payload, 'owner.person_id_number');
        }

        if (!empty(data_get($payload, 'owner.email_address'))) {
            $registrationFields['LegalEntity']['Email'] = data_get($payload, 'owner.email_address');
        }

        if (!empty(data_get($payload, 'owner.title'))) {
            $registrationFields['LegalEntity']['Title'] = data_get($payload, 'owner.title');
        }

        return $registrationFields;
    }

    private function formatPhoneNumber(string $phoneString, Branch $branch): string
    {
        $parser = PhoneNumberUtil::getInstance();

        try {
            $phoneNumber = $parser->parse($phoneString, $branch->address()->countryCode());
        } catch (NumberParseException $exception) {
            throw new \BadRequestException(sprintf(
                'The phone %s is not considered as a valid %s number',
                $phoneString,
                $branch->address()->countryCode()
            ));
        }

        return $parser->format($phoneNumber, PhoneNumberFormat::E164);
    }
}
