parameters:
  - schema:
      type: string
    name: branchId
    in: path
    required: true
  - schema:
      type: string
    name: eventId
    in: path
    required: true
post:
  tags:
    - Events
  summary: ''
  operationId: calculate-price-of-event-by-id
  description: This endpoint calculates the price of an event for a specific member
  x-internal: true
  security:
    - coreapi_auth: [ ]
  requestBody:
    required: true
    content:
      application/json:
        schema:
          type: object
          properties: 
            member_id:
              type: string
            number_of_bookings:
              type: integer
          required:
            - member_id
            - number_of_bookings
  responses:
    '200':
      description: OK
      content:
        application/json:
          schema:
            type: object
            oneOf:
              - type: object
                description: Successful response
                properties:
                  success:
                    type: boolean
                  data:
                    type: object
                    properties:
                      credits:
                        type: integer
                      price:
                        type: integer
                      currency:
                        type: string
              - $ref: '../../schemas/exceptions/user_not_found.yml'
              - $ref: '../../schemas/exceptions/branch_not_found.yml'
              - $ref: '../../schemas/exceptions/event_not_found.yml'
              - $ref: '../../schemas/exceptions/program_not_found.yml'
              - $ref: '../../schemas/exceptions/not_valid_id.yml'
              - $ref: '../../schemas/exceptions/guest_not_eligible_for_events.yml'
              - $ref: '../../schemas/exceptions/staff_not_eligible_for_events.yml'
              - $ref: '../../schemas/exceptions/membership_not_supported.yml'
              - $ref: '../../schemas/exceptions/membership_is_locked.yml'
              - $ref: '../../schemas/exceptions/not_eligible_and_not_enough_credits.yml'
              - $ref: '../../schemas/exceptions/no_credits_left.yml'
              - $ref: '../../schemas/exceptions/guest_bookings_limit_reached.yml'
    '400':
      description: Bad request
      content:
        application/json:
          schema:
            $ref : "../../schemas/error.yml"
    '404':
      description: Not found
      content:
        application/json:
          schema:
            $ref: "../../schemas/error.yml"
