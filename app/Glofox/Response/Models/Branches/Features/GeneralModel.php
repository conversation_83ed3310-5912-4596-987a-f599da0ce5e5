<?php

namespace Glofox\Response\Models\Branches\Features;

use Glofox\Response\BaseModel;

/**
 * @SuppressWarnings(PHPMD)
 */
class GeneralModel extends BaseModel
{
    /**
     * @var string
     */
    public $login_method = '';

    /**
     * @var bool
     */
    public $private_access = false;

    /**
     * @var int
     */
    public $order = 0;

    /**
     * @var bool
     */
    public $enabled = false;

    /**
     * @var int
     */
    public $minimum_age = 0;

    protected array $allowedFields = [
        'login_method',
        'private_access',
        'order',
        'enabled',
        'minimum_age',
    ];

    public function __construct(array $data)
    {
        $this->setModelFields($data);
    }
}
