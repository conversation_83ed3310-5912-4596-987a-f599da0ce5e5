<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: action_request.proto

namespace GRPC\Payments\TransactionService\ImportTransactionReq;

use UnexpectedValueException;

/**
 * Protobuf type <code>grpc.ImportTransactionReq.TransactionType</code>
 */
class TransactionType
{
    /**
     * Generated from protobuf enum <code>Charge = 0;</code>
     */
    const Charge = 0;
    /**
     * Generated from protobuf enum <code>Refund = 1;</code>
     */
    const Refund = 1;

    private static $valueToName = [
        self::Charge => 'Charge',
        self::Refund => 'Refund',
    ];

    public static function name($value)
    {
        if (!isset(self::$valueToName[$value])) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no name defined for value %s', __CLASS__, $value));
        }
        return self::$valueToName[$value];
    }


    public static function value($name)
    {
        $const = __CLASS__ . '::' . strtoupper($name);
        if (!defined($const)) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no value defined for name %s', __CLASS__, $name));
        }
        return constant($const);
    }
}

