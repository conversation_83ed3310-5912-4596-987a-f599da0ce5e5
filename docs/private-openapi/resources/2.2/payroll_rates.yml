parameters:
  - schema:
      type: string
      pattern: '^[a-f\d]{24}$'
    name: branchId
    in: path
    required: true
get:
  tags:
    - Payroll Rates
  summary: ''
  operationId: get-payroll-rates
  description: This endpoint returns filtered payroll rates of a given branch
  x-internal: true
  security:
    - coreapi_auth: [ ]
  parameters:
    - schema:
        type: string
      in: query
      name: sort-by
      description: field | -field
    - schema:
        type: integer
        minimum: 1
        default: 1
      in: query
      name: page
    - schema:
        type: integer
        minimum: 1
        maximum: 100
        default: 50
      in: query
      name: limit
    - schema:
        type: integer
      name: utc-time-start
      in: query
      description: UTC time of the starting point to fetch pay rates. If provided, the rates active during utc-time-start and forward will be fetched.
    - schema:
        type: integer
      name: utc-time-finish
      in: query
      description: UTC time of the ending point to fetch pay rates. If provided, the rates active during utc-time-finish and backward will be fetched.
    - schema:
        type: string
      name: group_id
      in: query
      description: Fetch the rates for a given group ID.
  responses:
    '200':
      description: OK
      content:
        application/json:
          schema:
            allOf:
              - $ref: "../../schemas/pagination.yml"
              - type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: "../../schemas/payroll_rate.yml"
post:
  tags:
    - Payroll Rates
  summary: ''
  operationId: create-payroll-rate
  description: This endpoint will create a payroll rate
  x-internal: true
  security:
    - coreapi_auth: [ ]
  requestBody:
    required: true
    content:
      application/json:
        schema:
          $ref: '../../requests/2.2/create_payroll_rate.yml'
  responses:
    '201':
      description: OK
      content:
        application/json:
          schema:
            $ref: '../../schemas/payroll_rate.yml'
    '400':
      description: Bad request
      content:
        application/json:
          schema:
            $ref: "../../schemas/error_payroll_rate.yml"
