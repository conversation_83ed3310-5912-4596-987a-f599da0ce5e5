<?php

App::import('Test/Case', 'GlofoxTestCase');

use Glofox\Response\Models\Branches\Configuration\WebportalModel;

/**
 * Test cases
 * 1 Model contains all fields with data
 * 2 Missing fields and returns correct structure
 * 3 Additional fields and return correct structure
 * 4 Empty array fields and return correct structure.
 *
 * Class WebportalModelTest
 */
class WebportalModelTest extends GlofoxTestCase
{
    public $fixtures = [];

    /**
     * @dataProvider modelProvider
     */
    public function testModel($dataSent, $result): void
    {
        $model = new WebportalModel($dataSent);
        $this->assertEquals($model, $result);
    }

    public function modelProvider(): array
    {
        return [
            '1 Model contains all fields with data' => [
                [
                    'classes_view' => 'day',
                    'colors' => null,
                    'features' => null,
                ],
                new WebportalModel(
                    [
                        'classes_view' => 'day',
                        'colors' => null,
                        'features' => null,
                    ]
                ),
            ],
            '2 Missing fields and returns correct structure' => [
                [
                    // classes_view missing
                    'colors' => null,
                    'features' => null,
                ],
                new WebportalModel(
                    [
                        'classes_view' => '',
                        'colors' => null,
                        'features' => null,
                    ]
                ),
            ],
            '3 Additional fields and return correct structure' => [
                [
                    'extra_fields' => '',
                    'classes_view' => 'day',
                    'colors' => null,
                    'features' => null,
                ],
                new WebportalModel(
                    [
                        'classes_view' => 'day',
                        'colors' => null,
                        'features' => null,
                    ]
                ),
            ],
            '4 Empty array fields and return correct structure' => [
                [],
                new WebportalModel(
                    [
                        'classes_view' => '',
                        'colors' => null,
                        'features' => null,
                    ]
                ),
            ],
        ];
    }
}
