## Notifications [/2.1/branches/{branchId}/users/{userId}/notifications?page={page}&limit={limit}]

+ Parameters
    + branchId: 507f191e810c19729de860ea (string) - The identification of the branch
    + userId: 5b55e5742fc1bbef53293352 (string) - The identification of the user
    + page (number)
    + limit (number, optional)

+ Attributes
    + _id: 507f1f77bcf86cd799439011 (string)
    + branch_id: 507f191e810c19729de860ea (string)
    + user_id: 5b55e5742fc1bbef53293352 (string)
    + type: (enum[string])
        + Members
            + ASSET_REFERSH
            + BROADCAST
            + CRUD_TASK
            + EMAIL
            + PUSH
    + created: 1532352489 (number)
    + is_marketing: true (boolean)
    + message: "A nice message" (string, nullable) - A nullable string

### Retrieve Notifications of Customers [GET]

+ Request
    + Headers
          Authorization: Bearer {authToken}

+ Response 200 (application/json)
    + Attributes
        + success: true (boolean)
        + data (array, fixed)
            + (Notifications)
        + meta (object)
            + pagination (Pagination)