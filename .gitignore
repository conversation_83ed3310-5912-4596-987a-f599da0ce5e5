#.htaccess

# MAC
.DS_Store
.DS

# Windows
Thumbs.db

# Eclipse project files
/.cache
.project
/.settings
.buildpath

# Netbeans project files
/nbproject/

# PhpStorm
.idea
*.iml

# Sublime
*.sublime-project
*.sublime-workspace

# Cake TMP Dirs
*.log
/app/tmp/*
!/app/tmp/

app/tmp/cache/*
/app/tmp/cache/*
!/app/tmp/cache/
!/app/tmp/cache/empty

/app/tmp/cache/models/*
!/app/tmp/cache/models/
!/app/tmp/cache/models/.gitkeep
!/app/tmp/cache/models/empty

/app/tmp/cache/persistent/*
!/app/tmp/cache/persistent/
!/app/tmp/cache/persistent/.gitkeep
!/app/tmp/cache/persistent/empty

/app/tmp/cache/dompdf/*
!/app/tmp/cache/dompdf/
!/app/tmp/cache/dompdf/empty

/app/tmp/cache/views/*
!/app/tmp/cache/views/
!/app/tmp/cache/views/.gitkeep
!/app/tmp/cache/views/empty

/app/tmp/logs/*
!/app/tmp/logs/
!/app/tmp/logs/empty

/app/tmp/sessions/*
!/app/tmp/sessions/
!/app/tmp/sessions/empty

/app/tmp/tests/*
!/app/tmp/tests/
!/app/tmp/tests/empty

/app/webroot/dashboard/node_modules/
!/app/webroot/dashboard/bower_components

/app/webroot/portal

!/app/Plugin/empty
/app/Plugin

/app/webroot/coverage

# Vim
*.swp
/vendor/
/vendors/

sandbox.php
sandbox.html
app/webroot/cli.php
/db/
/data/

*.cache
.php_cs
app/coverage.xml

.docker-sync/
node_modules
cghooks.lock
supervisord.pid

docs/*.html

docs/gateway.apib
docs/internal.apib

# VS Code
.vscode/*
!.vscode/launch.json
!.vscode/extensions.json

global-db-settings

docker/config/local/override/*.env
bootstrap.sh
test.sh
composer.phar
docker-compose.override.yml

# Keeping them as we can still have these folders in our local env
.blackfire/
.blackfire/*

.envrc
phpunit.xml.dist

profiler/*
!profiler/.gitkeep

phpcompatiblity/*
!phpcompatiblity/.gitkeep

rector-paths.json

app/View/Fonts/installed-fonts.json

core
