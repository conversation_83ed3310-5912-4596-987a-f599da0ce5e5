<?php

namespace Glofox\Repositories\Traits;

use Glofox\Repositories\Filters\ConditionsCollection;
use Glofox\Repositories\Repository;
use Glofox\Repositories\Search\Expressions\Conditionable;
use Glofox\Repositories\Search\Expressions\ConditionableCollection;
use Illuminate\Support\Collection;

trait HasCriteria
{
    /**
     * @var ConditionableCollection
     */
    protected $criteria;

    /**
     * @return $this
     */
    public function startCriteria(): self
    {
        $this->criteria = ConditionableCollection::make();

        $this->addDefaultCriteria();

        return $this;
    }

    /**
     * @return $this
     */
    public function flushCriteria(): self
    {
        return $this->startCriteria();
    }

    /**
     * @param ConditionableCollection $criteria
     * @param ConditionsCollection $conditions
     * @param Repository|null $repository
     * @return void
     */
    public function applyCriteria(
        ConditionableCollection $criteria,
        ConditionsCollection $conditions,
        Repository $repository = null
    ): void {
        $generatedConditions = new ConditionsCollection();

        $criteria->each(function (Conditionable $criterion) use ($generatedConditions, $repository) {
            /** @var Conditionable $queryable */
            $criterion->applyToConditions($generatedConditions, $repository);
        });

        if (!$generatedConditions->isEmpty()) {
            $conditions->put('$and', $generatedConditions->toArray());
        }

        $this->flushCriteria();
    }

    public function getConditionsFromCriteria(): ConditionsCollection
    {
        $conditions = new ConditionsCollection();
        $generatedConditions = new ConditionsCollection();

        $this->criteria->each(function (Conditionable $criterion) use ($generatedConditions) {
            /** @var Conditionable $queryable */
            $criterion->applyToConditions($generatedConditions);
        });

        $conditions->put('$and', $generatedConditions->toArray());

        return $conditions;
    }

    /**
     * Placeholder method for support default criteria for repositories
     *
     * @return void
     */
    public function addDefaultCriteria(): void
    {
        return;
    }

    /**
     * @param $criteria
     *
     * @return $this
     */
    public function addCriteria($criteria): self
    {
        $this->criteria->push($criteria);

        return $this;
    }

    public function addOrCriteria(array $criteria): self
    {
        $criteria = ConditionableCollection::make($criteria)->withOperator('$or');

        $this->criteria->push($criteria);

        return $this;
    }

    public function createCriteriaFromArray(array $criteria): Collection
    {
        return ConditionableCollection::make($criteria);
    }
}
