<?php

namespace CakeTestCases\Glofox\Domain\BookingSpots\Http;

use Glofox\Domain\BookingSpots\Models\BookingSpot;
use Glofox\Domain\BookingSpots\Models\BookingSpotsCollection;
use Glofox\Domain\BookingSpots\UseCase\ListBookingSpotsByModelId;
use Glofox\Domain\BookingSpots\UseCase\ListBookingSpotsResult;
use Glofox\Domain\BookingSpots\UseCase\ReserveBookingSpot;
use Glofox\Exception;

\App::import('Test/Case', 'GlofoxControllerTestCase');

class BookingSpotsControllerTest extends \GlofoxControllerTestCase
{
    public function tearDown()
    {
        parent::tearDown();

        \Mockery::close();
    }

    public function test_it_should_return_list_of_spots_given_an_event(): void
    {
        $reservedSpots = BookingSpotsCollection::make([
            BookingSpot::make(['spot_id' => '2', 'booking_id' => 'booking-id', 'user_id' => 'user-id'])
        ]);
        $nonReservedSpotIds = ['1', '3'];

        $useCase = \Mockery::mock(ListBookingSpotsByModelId::class)
            ->shouldReceive('execute')
            ->andReturn(new ListBookingSpotsResult($reservedSpots, $nonReservedSpotIds))
            ->getMock();

        app()->instance(ListBookingSpotsByModelId::class, $useCase);

        $this->authenticateAsMember();
        $response = $this->testAction('/2.2/branches/49a7011a05c677b9a916612a/events/event-id/spots', [
            'method' => 'get'
        ]);
        $result = json_decode($response, true, 512, JSON_THROW_ON_ERROR);

        self::assertFalse($result['has_more']);
        self::assertEquals(
            [
                [
                    'reserved' => false,
                    'spot_id' => '1',
                    'booking_id' => '',
                    'user_id' => '',
                    'is_auto_assigned' => false
                ],
                [
                    'reserved' => true,
                    'spot_id' => '2',
                    'booking_id' => 'booking-id',
                    'user_id' => 'user-id',
                    'is_auto_assigned' => false
                ],
                [
                    'reserved' => false,
                    'spot_id' => '3',
                    'booking_id' => '',
                    'user_id' => '',
                    'is_auto_assigned' => false
                ],
            ],
            $result['data']
        );

        app()->forgetInstance(ListBookingSpotsByModelId::class);
    }

    public function test_it_should_fail_to_reserve_booking_spot_when_usecase_fails(): void
    {
        $useCase = \Mockery::mock(ReserveBookingSpot::class)
            ->shouldReceive('execute')
            ->andThrow(Exception::class, 'UNABLE_TO_RESERVE_BOOKING_SPOT')
            ->getMock();

        app()->instance(ReserveBookingSpot::class, $useCase);

        $this->authenticateAsMember();
        $response = $this->testAction('/2.2/branches/49a7011a05c677b9a916612a/events/event-id/spots', [
            'data' => [
                'booking_id' => 'booking-id',
                'spot_id' => 'spot-id'
            ],
            'method' => 'post'
        ]);

        $result = json_decode($response, true, 512, JSON_THROW_ON_ERROR);
        self::assertFalse($result['success']);
        self::assertEquals('UNABLE_TO_RESERVE_BOOKING_SPOT', $result['message']);

        app()->forgetInstance(ReserveBookingSpot::class);
    }

    public function test_it_should_fail_to_reserve_booking_spot_when_required_parameter_is_missing(): void
    {
        $this->authenticateAsMember();
        $requests = [
            [
              'spot_id' => '',
              'booking_id' => 'booking_id',
            ],
            [
                'spot_id' => 'spot_id',
                'booking_id' => '',
            ]
        ];
        foreach ($requests as $request) {
            $response = $this->testAction('/2.2/branches/49a7011a05c677b9a916612a/events/event-id/spots', [
                'data' => $request,
                'method' => 'post'
            ]);

            $result = json_decode($response, true, 512, JSON_THROW_ON_ERROR);
            self::assertFalse($result['success']);
            self::assertContains('field is required.', $result['message']);
        }
    }

    public function test_it_should_reserve_a_booking_spot(): void
    {
        $useCase = \Mockery::mock(ReserveBookingSpot::class)
            ->shouldReceive('execute')
            ->andReturn()
            ->getMock();

        app()->instance(ReserveBookingSpot::class, $useCase);

        $this->authenticateAsMember();
        $response = $this->testAction('/2.2/branches/49a7011a05c677b9a916612a/events/event-id/spots', [
            'data' => [
                'booking_id' => 'booking-id',
                'spot_id' => 'spot-id'
            ],
            'method' => 'post'
        ]);

        self::assertEmpty($response, $response);

        app()->forgetInstance(ReserveBookingSpot::class);
    }
}