<?php

namespace CakeTestCases\Glofox\Payments\Providers\Gateway\WebHooks\Event;

use Carbon\Carbon;
use Glofox\Payments\Entities\Subscription\Models\SubscriptionCycleStyle;
use Glofox\Payments\Entities\WebHook\Models\WebhookEventFactory;

\App::import('Test/Case', 'GlofoxTestCase');

class WebhookEventFactoryTest extends \GlofoxTestCase
{
    public function test_factory_makes_webhook_event_from_gateway_payload(): void
    {
        $expectedPeriodStart = Carbon::now();
        $expectedPeriodEnd = Carbon::now()->addDay();

        $payload = collect([
            'id' => 'event-id',
            'account_id' => 3,
            'psp_response' => json_encode([
                'subscription_cycle_style' => 'LOCK-SUB',
                'retry' => [
                    'style' => 'LOCK-SUB',
                    'number' => 1,
                    'total_automated_retries' => 1,
                    'next_attempt' => $expectedPeriodStart->copy()->addHour()->getTimestamp(),
                ],
                'data' => [
                    'object' => [
                        'period_start' => $expectedPeriodStart->getTimestamp(),
                        'period_end' => $expectedPeriodEnd->getTimestamp(),
                    ],
                ],
            ]),
        ]);

        /** @var WebhookEventFactory $factory */
        $factory = app()->make(WebhookEventFactory::class);
        $event = $factory->makeFromGatewayWebhookPayload($payload);

        // high level event details
        $this->assertEquals('event-id', $event->getEventId());

        // subscription cycle style
        $this->assertEquals(SubscriptionCycleStyle::LOCK_SUB(), $event->getDetail()->getSubscriptionCycleStyle());

        // retry
        $this->assertEquals(1, $event->getDetail()->getRetry()->getNumber());
        $this->assertEquals(1, $event->getDetail()->getRetry()->getTotalAutomated());
        $this->assertEquals($expectedPeriodStart->copy()->addHour()->toDateTimeString(), $event->getDetail()->getRetry()->getNextAttempt()->toDateTimeString());

        // period
        $this->assertEquals($expectedPeriodStart->toDateTimeString(), $event->getDetail()->getPeriod()->getStart()->toDateTimeString());
        $this->assertEquals($expectedPeriodEnd->toDateTimeString(), $event->getDetail()->getPeriod()->getEnd()->toDateTimeString());
    }
}
