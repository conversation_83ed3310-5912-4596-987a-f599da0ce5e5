<?php

namespace Glofox\Response\Transformers;

use Glofox\Response\Models\BasicModels\MainModel;

/**
 * Class BaseTransformer.
 * @package Glofox\Response\Transformers
 */
class BaseTransformer
{
    /**
     * @param string $data
     * @return mixed
     */
    protected function setArrayFromJson($data = '')
    {
        return json_decode($data, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    /**
     * @param array $data
     * @param array $dataModel
     * @return MainModel
     */
    public function setMainModelFromArray(array $data, array $dataModel = [])
    {
        return new MainModel(
            [
                'object' => (string) (!empty($data['object']) ? $data['object'] : ''),
                'page' => (int) (!empty($data['page']) ? $data['page'] : 0),
                'limit' => (int) (!empty($data['limit']) ? $data['limit'] : 0),
                'has_more' => (bool) (!empty($data['has_more']) ? $data['has_more'] : false),
                'total_count' => (int) (!empty($data['total_count']) ? $data['total_count'] : 0),
                'data' => $dataModel,
            ]
        );
    }
}
