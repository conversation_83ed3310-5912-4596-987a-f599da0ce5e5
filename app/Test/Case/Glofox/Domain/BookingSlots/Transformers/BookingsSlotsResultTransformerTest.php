<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\BookingSlots\Transformers;

use Carbon\Carbon;
use Glofox\Domain\BookingSlots\BookingSlotStatus;
use Glofox\Domain\BookingSlots\Models\BookingSlot;
use Glofox\Domain\BookingSlots\Transformers\BookingsSlotsResultTransformer;
use Glofox\Domain\BookingSlots\Transformers\ProgramCalendarBookingsSlotsTransformer;

\App::import('Test/Case', 'GlofoxTestCase');

class BookingsSlotsResultTransformerTest extends \GlofoxTestCase
{
    public $fixtures = [];
    private BookingsSlotsResultTransformer $transformer;

    public function setUp(): void
    {
        parent::setUp();

        $this->transformer = new BookingsSlotsResultTransformer();
    }

    public function testItShouldTransformManyBookingSlotsSuccessfully(): void
    {
        $slots = [
            BookingSlot::make([
                '_id' => 'some_id',
                'start_time' => 1_636_912_800,
                'bookable' => true,
                'reservable' => true,
                'end_time' => 1_636_916_400,
                'errors' => [],
                'schedule_code' => '123abc',
                'batch_id' => 'coreapi-668e8c50e4e8c63831043',
                'member_id' => '66910a801ac44b1d0fb69f90',
                'program_id' => '66910aa7d0e287594039684c',
                'status' => BookingSlotStatus::FAILED,
                'error' => 'YOU_HAVE_BOOKED_FOR_THIS_EVENT',
            ]),
            BookingSlot::make([
                '_id' => 'some_id',
                'start_time' => 1_637_517_600,
                'bookable' => true,
                'reservable' => true,
                'end_time' => 1_637_521_200,
                'errors' => [],
                'schedule_code' => '123abc',
                'batch_id' => 'coreapi-668e8c50e4e8c63831043',
                'member_id' => '66910a801ac44b1d0fb69f90',
                'program_id' => '66910aa7d0e287594039684c',
                'status' => BookingSlotStatus::PENDING,
                'error' => '',
            ]),
        ];

        $expected = [
            [
                'id' => 'some_id',
                'status' => BookingSlotStatus::FAILED,
                'time_start' => 1_636_912_800,
                'failure_reason' => 'YOU_HAVE_BOOKED_FOR_THIS_EVENT',
            ],
            [
                'id' => 'some_id',
                'status' => BookingSlotStatus::PENDING,
                'time_start' => 1_637_517_600,
                'failure_reason' => '',
            ],
        ];

        $result = $this->transformer->transformMany($slots);

        $this->assertEquals($expected, $result);
    }
}
