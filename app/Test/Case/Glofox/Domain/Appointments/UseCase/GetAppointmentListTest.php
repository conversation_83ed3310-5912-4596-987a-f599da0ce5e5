<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\Appointments\UseCase;

use App;
use Glofox\Domain\Appointments\UseCase\GetAppointmentList;
use Glofox\Domain\Appointments\UseCase\GetAppointmentListParams;
use Glofox\Domain\Http\Request\BaseCriteria;
use Glofox\Domain\TimeSlotPatterns\Models\TimeSlotPattern;
use Glofox\Domain\TimeSlotPatterns\Repositories\TimeSlotPatternsRepository;
use Glofox\Domain\Users\Models\User;
use Glofox\Domain\Users\Repositories\UsersRepository;
use GlofoxTestCase;
use Illuminate\Support\Collection;
use Mockery;
use UserType;

App::import('Test/Case', 'GlofoxTestCase');

class GetAppointmentListTest extends GlofoxTestCase
{
    public $fixtures = [];
    private TimeSlotPatternsRepository $timeSlotPatternsRepository;
    private UsersRepository $usersRepository;

    public function setUp()
    {
        parent::setUp();

        $this->timeSlotPatternsRepository = Mockery::mock(TimeSlotPatternsRepository::class);
        $this->usersRepository = Mockery::mock(UsersRepository::class);
    }

    public function tearDown()
    {
        parent::tearDown();

        Mockery::close();
    }

    public function testGettingList(): void
    {
        $branchId = 'test-branch-id';
        $query = 'search query';
        $sortBy = 'name';
        $sortByDirection = -1;
        $page = 1;
        $limit = 50;
        $totalCount = 134;
        $active = true;
        $includeStaff = true;
        $requestedBy = User::make([
            'type' => UserType::ADMIN,
        ]);

        $this->timeSlotPatternsRepository
            ->shouldReceive('getList')
            ->withArgs(
                function (
                    string $branchIdParam,
                    string $queryParam,
                    string $staffId,
                    bool $activeParam,
                    array $sortParam,
                    int $pageParam,
                    int $limitParam,
                    ?bool $private
                ) use ($branchId, $query, $active, $sortBy, $sortByDirection, $page, $limit) {
                    self::assertEquals($branchIdParam, $branchId);
                    self::assertEquals($queryParam, $query);
                    self::assertEmpty($staffId);
                    self::assertEquals($sortParam, [$sortBy => $sortByDirection]);
                    self::assertEquals($pageParam, $page);
                    self::assertEquals($limitParam, $limit);
                    self::assertEquals($activeParam, $active);
                    self::assertNull($private);

                    return true;
                }
            )
            ->andReturn($this->mockTimeSlotPattern())
            ->getMock()
            ->shouldReceive('getActiveCount')
            ->withArgs(function ($branchIdParam, $queryParam, $staffId, $activeParam) use ($branchId, $active) {
                self::assertEquals($branchIdParam, $branchId);
                self::assertEquals($activeParam, $active);

                return true;
            })
            ->andReturn($totalCount);

        $this->usersRepository
            ->shouldReceive('findStaffByBranchId')
            ->withArgs(function (string $branchIdParam, array $types) use ($branchId) {
                self::assertEquals($branchIdParam, $branchId);
                self::assertEquals($types, [UserType::TRAINER()]);

                return true;
            })
            ->once()
            ->andReturn($this->mockBranchStaff())
            ->getMock();

        $useCase = new GetAppointmentList(
            $this->timeSlotPatternsRepository,
            $this->usersRepository
        );

        $params = new GetAppointmentListParams(
            $branchId,
            $query,
            '',
            $active,
            $includeStaff,
            new BaseCriteria(
                $page,
                $limit,
                [$sortBy => $sortByDirection]
            ),
            $requestedBy
        );

        $appointmentsList = $useCase->execute($params);
        $expectedAppointments = [
            'test-appointment-id-1' => [
                '_id' => 'test-appointment-id-1',
                'staff_ids' => ['test-user-id-1', 'test-user-id-2', 'test-user-id-6'],
                'staff' => ['test-user-id-1', 'test-user-id-2'],
            ],
            'test-appointment-id-2' => [
                '_id' => 'test-appointment-id-2',
                'staff_ids' => ['test-user-id-4'],
                'staff' => ['test-user-id-4'],
            ],
            'test-appointment-id-3' => [
                '_id' => 'test-appointment-id-3',
                'staff' => [],
            ],
            'test-appointment-id-4' => [
                '_id' => 'test-appointment-id-4',
                'staff' => [],
            ],
        ];

        self::assertEquals($totalCount, $appointmentsList->totalCount());
        self::assertCount(4, $appointmentsList->data());

        foreach ($appointmentsList->data() as $appointment) {
            self::assertArrayHasKey($appointment->id(), $expectedAppointments);
            $expectedAppointmentStaff = $expectedAppointments[$appointment->id()]['staff'];
            self::assertCount(count($expectedAppointmentStaff), $appointment['staff']);

            foreach ($appointment['staff'] as $staff) {
                self::assertContains($staff->id(), $expectedAppointmentStaff);
            }
        }
    }

    private function mockTimeSlotPattern(): array
    {
        return [
            TimeSlotPattern::make([
                '_id' => 'test-appointment-id-1',
                'staff_ids' => ['test-user-id-1', 'test-user-id-2', 'test-user-id-6'],
            ]),
            TimeSlotPattern::make([
                '_id' => 'test-appointment-id-2',
                'staff_ids' => ['test-user-id-4'],
            ]),
            TimeSlotPattern::make([
                '_id' => 'test-appointment-id-3',
                'name' => 'Appointment 3',
            ]),
            TimeSlotPattern::make([
                '_id' => 'test-appointment-id-4',
                'staff_ids' => 'invalid-staff-ids',
            ]),
        ];
    }

    private function mockBranchStaff(): Collection
    {
        $users = [
            User::make([
                '_id' => 'test-user-id-1',
                'name' => 'Trainer 1',
            ]),
            User::make([
                '_id' => 'test-user-id-2',
                'name' => 'Trainer 2',
            ]),
            User::make([
                '_id' => 'test-user-id-3',
                'name' => 'Trainer 3',
            ]),
            User::make([
                '_id' => 'test-user-id-4',
                'name' => 'Trainer 4',
            ]),
            User::make([
                '_id' => 'test-user-id-5',
                'name' => 'Trainer 5',
            ]),
        ];
        return collect($users);
    }

    public function testGettingListWithStaff(): void
    {
        $branchId = 'test-branch-id';
        $query = 'search query';
        $sortBy = 'name';
        $sortByDirection = -1;
        $page = 1;
        $limit = 50;
        $active = true;
        $includeStaff = true;
        $requestedBy = User::make([
            'type' => UserType::STAFF,
        ]);

        $this->timeSlotPatternsRepository
            ->shouldReceive('getList')
            ->getMock()
            ->shouldReceive('getActiveCount');

        $this->usersRepository
            ->shouldReceive('findActiveByIds');

        $useCase = new GetAppointmentList(
            $this->timeSlotPatternsRepository,
            $this->usersRepository
        );

        $params = new GetAppointmentListParams(
            $branchId,
            $query,
            '',
            $active,
            $includeStaff,
            new BaseCriteria(
                $page,
                $limit,
                [$sortBy => $sortByDirection]
            ),
            $requestedBy
        );

        $useCase->execute($params);
    }

    public function testGettingListFilteringWithStaffId(): void
    {
        $branchId = 'test-branch-id';
        $query = 'search query';
        $staffFilterId = 'mock-staff-id';
        $sortBy = 'name';
        $sortByDirection = -1;
        $page = 1;
        $limit = 50;
        $active = true;
        $includeStaff = true;
        $requestedBy = User::make([
            'type' => UserType::STAFF,
        ]);

        $this->timeSlotPatternsRepository
            ->shouldReceive('getList')
            ->withArgs(
                function (
                    string $branchIdParam,
                    string $queryParam,
                    string $staffId,
                    bool $activeParam,
                    array $sortParam,
                    int $pageParam,
                    int $limitParam,
                    ?bool $private
                ) use ($branchId, $query, $staffFilterId, $active, $sortBy, $sortByDirection, $page, $limit) {
                    self::assertEquals($branchIdParam, $branchId);
                    self::assertEquals($queryParam, $query);
                    self::assertEquals($staffFilterId, $staffId);
                    self::assertEquals($sortParam, [$sortBy => $sortByDirection]);
                    self::assertEquals($pageParam, $page);
                    self::assertEquals($limitParam, $limit);
                    self::assertEquals($activeParam, $active);
                    self::assertFalse($private);

                    return true;
                }
            )
            ->getMock()
            ->shouldReceive('getActiveCount')
            ->withArgs(function ($branchIdParam, $queryParam, $staffId, $activeParam) use ($branchId, $active) {
                self::assertEquals($branchIdParam, $branchId);
                self::assertEquals($activeParam, $active);

                return true;
            });

        $this->usersRepository
            ->shouldReceive('findActiveByIds');

        $useCase = new GetAppointmentList(
            $this->timeSlotPatternsRepository,
            $this->usersRepository
        );

        $params = new GetAppointmentListParams(
            $branchId,
            $query,
            $staffFilterId,
            $active,
            $includeStaff,
            new BaseCriteria(
                $page,
                $limit,
                [$sortBy => $sortByDirection]
            ),
            $requestedBy
        );

        $useCase->execute($params);
    }
}
