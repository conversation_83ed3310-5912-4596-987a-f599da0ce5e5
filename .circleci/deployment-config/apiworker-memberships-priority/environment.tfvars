service_name                   = "apiworker-memberships-priority-service"
image_name                     = "apiworker"
component_name                 = "apiworker-memberships-priority"
container_port                 = 80
max_capacity                   = 1
containers_count               = 1
ecs_network_mode               = "awsvpc"
fargate_private_service_enable = true
ecs_service_enable             = 0
container_memory               = 1024
container_cpu                  = 512

accessible_s3_buckets_arn = ["arn:aws:s3:::glofox-assets-${ENV_SHORT_NAME}"]
accessible_sqs_queues_arn = ["arn:aws:sqs:${AWS_REGION}:${AWS_ACCOUNT_ID}:membership-priority-${ENV_SHORT_NAME}"]
accessible_sns_topics_arn = ["arn:aws:sns:${AWS_REGION}:${AWS_ACCOUNT_ID}:glofox-events-topic-${ENV_SHORT_NAME}", "arn:aws:sns:${AWS_REGION}:${AWS_ACCOUNT_ID}:glofox-communications-commands-topic-${ENV_SHORT_NAME}"]
accessible_aws_services   = ["rekognition"]

env_vars = {
  GLOFOX_DATABASE_NAME                 = "${ENVIRONMENT}",
  GLOFOX_DATABASE_SSL                  = "1",
  GLOFOX_DATABASE_USERNAME             = "api-service-non-plat",
  GLOFOX_DATABASE_REPLICASET_NAME      = "${MONGO_REPLICA_SET}",
  GLOFOX_DATABASE_PORT                 = "27017",
  GLOFOX_DATABASE_USE_ENVIRONMENT      = "1",
  GLOFOX_DATABASE_READ_PREFERENCE      = "nearest",
  NEW_RELIC_APP_NAME                   = "Platform-${ENV_SHORT_NAME}",
  NEW_RELIC_ENABLED                    = false,
  NEW_RELIC_LICENCE                    = "",
  REGION                               = "${AWS_REGION}",
  SERVER                               = "development",
  SERVICE_NAME                         = "apiworker-memberships-priority",
  PHP_XDEBUG_ENABLE                    = "0",
  GLOFOX_DEBUG_LEVEL                   = "0",
  OPENSHIFT_APP_NAME                   = "${ENVIRONMENT}",
  PHP_OPCACHE_ENABLE                   = "1",
  GLOFOX_ROOT_DOMAIN                   = "https://${ENVIRONMENT}.${BASE_DOMAIN}",
  GLOFOX_AWS_S3_BUCKET                 = "glofox-assets-${ENV_SHORT_NAME}",
  GLOFOX_AWS_S3_HOST                   = "s3.amazonaws.com",
  GLOFOX_GRPC_API_CLIENT_HOST          = "payments-api.${ENV_SHORT_NAME}.service.private:44440",
  GLOFOX_GRPC_ACCOUNT_CLIENT_HOST      = "payments-account.${ENV_SHORT_NAME}.service.private:44441",
  GLOFOX_GRPC_TRANSACTION_CLIENT_HOST  = "payments-transaction.${ENV_SHORT_NAME}.service.private:44442",
  GLOFOX_GRPC_SUBSCRIPTION_CLIENT_HOST = "payments-subscription.${ENV_SHORT_NAME}.service.private:44443",
  GLOFOX_WEB_COMPONENT_HTTP_HOST       = "https://payments-web-service-${ENV_SHORT_NAME}.aws.${BASE_DOMAIN}",
  GLOFOX_PUSHER_URL                    = "http://pusher-service-${ENV_SHORT_NAME}.aws.${BASE_DOMAIN}/",
  GLOFOX_PUSHER_URL_NEW                = "http://push-notifications.${ENV_SHORT_NAME}.service.private/",
  GLOFOX_SENTRY_URL                    = "https://<EMAIL>/1268734"
  GLOFOX_ASYNC_REGION                  = "${AWS_REGION}"
  GLOFOX_SNS_NAME                      = "glofox-events-topic-${ENV_SHORT_NAME}"
  GLOFOX_MEMBERSHIPS_API_URL           = "http://memberships.${ENV_SHORT_NAME}.service.private:8080"
  GLOFOX_WALLETS_API_URL               = "http://memberships.${ENV_SHORT_NAME}.service.private:8080"
  GLOFOX_TAX_API_URL                   = "http://tax.${ENV_SHORT_NAME}.service.private:80"
  GLOFOX_SQS_API_QUEUE_URL             = "https://sqs.${AWS_REGION}.amazonaws.com/${AWS_ACCOUNT_ID}/membership-priority-${ENV_SHORT_NAME}"
  GLOFOX_SNS_REGION                    = "${AWS_REGION}"
  GLOFOX_REDIS_CLUSTER_ENABLED         = "1"
  GLOFOX_EXPERIMENTS_API_URL           = "https://iris-${ENV_SHORT_NAME}.aws.${BASE_DOMAIN}"
  LIVE_STREAM_SERVICE_URL              = "https://${ENV_SHORT_NAME}-live.${BASE_DOMAIN}/v3"
  LIVE_STREAM_INSTALLER_URL            = "https://api.internal-gateway.${ENV_SHORT_NAME}.service.private/live-stream-installer"
  ELECTRONIC_AGREEMENTS_SERVICE_URL    = "https://api.internal-gateway.${ENV_SHORT_NAME}.service.private"
  REPORTS_SERVICE_URL                  = "http://reports.${ENV_SHORT_NAME}.service.private:8080"
  INVOICE_SERVICE_URL                  = "http://invoice.${ENV_SHORT_NAME}.service.private:80/invoice"
  RECEIPTS_SERVICE_URL                 = "http://receipts.${ENV_SHORT_NAME}.service.private:80/receipts-api"
  SALES_TAX_SERVICE_URL                = "http://sales-tax.${ENV_SHORT_NAME}.service.private:80/sales-tax"
  ADDON_SERVICE_URL                    = "http://services.${ENV_SHORT_NAME}.service.private:8083/services-api"
  COMMUNICATIONS_INTERNAL_SERVICE_URL  = "http://communications.${ENV_SHORT_NAME}.service.private:80/communications-internal"
  CART_SERVICE_URL                     = "http://cart.${ENV_SHORT_NAME}.service.private:80"
  SENDGRID_USERNAME                    = "apikey"
  PASSWORD_HASH_COST                   = "10"
  EAGREEMENT_MUST_SIGN_DATE            = "2021-05-20 00:00:01"
  PASSWORD_RESET_TOKEN_VALIDITY        = "60"
  PASSWORD_RESET_TOKEN_SIZE            = "24"
  LAUNCH_DARKLY_API_URL                = "https://app.launchdarkly.com/api/"
  AUTHENTICATION_SERVICE_URL           = "https://auth-${ENV_SHORT_NAME}.aws.${BASE_DOMAIN}"
  HONEYCOMB_DATASET_NAME               = "glofox-${ENV_SHORT_NAME}"
  HONEYCOMB_BASE_URL                   = "https://api.honeycomb.io"
  ENVIRONMENT_SHORT_NAME               = "${ENV_SHORT_NAME}"
  GOOGLE_RECAPTCHA_BASE_URL            = "https://www.google.com/recaptcha/api"
  COMMUNICATIONS_SERVICE_TOPIC         = "arn:aws:sns:${AWS_REGION}:${AWS_ACCOUNT_ID}:glofox-communications-commands-topic-${ENV_SHORT_NAME}"
  DELAY_TASK_SLEEP_TIME_MICROSEC       = 100000
}
secret_vars = {
  GLOFOX_DATABASE_HOST                                = "glofox/apiworker/${ENV_SHORT_NAME}/GLOFOX_DATABASE_HOST"
  GLOFOX_DATABASE_PASSWORD                            = "glofox/apiworker/${ENV_SHORT_NAME}/GLOFOX_DATABASE_PASSWORD"
  GLOFOX_DATABASE_FOR_AUTHENTICATION                  = "glofox/apiworker/${ENV_SHORT_NAME}/GLOFOX_DATABASE_FOR_AUTHENTICATION"
  GLOFOX_STRIPE_CUSTOM_EU_PUBLISHABLE_KEY             = "glofox/apiworker/${ENV_SHORT_NAME}/GLOFOX_STRIPE_CUSTOM_EU_PUBLISHABLE_KEY"
  GLOFOX_STRIPE_CUSTOM_US_PUBLISHABLE_KEY             = "glofox/apiworker/${ENV_SHORT_NAME}/GLOFOX_STRIPE_CUSTOM_US_PUBLISHABLE_KEY"
  GLOFOX_STRIPE_CUSTOM_US_LIFT_BRANDS_PUBLISHABLE_KEY = "glofox/apiworker/${ENV_SHORT_NAME}/GLOFOX_STRIPE_CUSTOM_US_LIFT_BRANDS_PUBLISHABLE_KEY"
  GLOFOX_STRIPE_CUSTOM_UK_LIFT_BRANDS_PUBLISHABLE_KEY = "glofox/apiworker/${ENV_SHORT_NAME}/GLOFOX_STRIPE_CUSTOM_UK_LIFT_BRANDS_PUBLISHABLE_KEY"
  GLOFOX_ANDROID_GCM_KEY                              = "glofox/apiworker/${ENV_SHORT_NAME}/GLOFOX_ANDROID_GCM_KEY"
  GLOFOX_IOS_APNS_KEY                                 = "glofox/apiworker/${ENV_SHORT_NAME}/GLOFOX_IOS_APNS_KEY"
  GLOFOX_AWS_S3_ACCESS_KEY                            = "glofox/apiworker/${ENV_SHORT_NAME}/GLOFOX_AWS_S3_ACCESS_KEY"
  GLOFOX_AWS_S3_SECRET_KEY                            = "glofox/apiworker/${ENV_SHORT_NAME}/GLOFOX_AWS_S3_SECRET_KEY"
  GLOFOX_WEB_COMPONENT_SECRET                         = "glofox/apiworker/${ENV_SHORT_NAME}/GLOFOX_WEB_COMPONENT_SECRET"
  PUSHER_AUTH_KEY                                     = "glofox/apiworker/${ENV_SHORT_NAME}/PUSHER_AUTH_KEY"
  GLOFOX_INTEGRATIONS_CLASSPASS                       = "glofox/apiworker/${ENV_SHORT_NAME}/GLOFOX_INTEGRATIONS_CLASSPASS"
  GLOFOX_ASYNC_ACCOUNT_ID                             = "glofox/apiworker/${ENV_SHORT_NAME}/GLOFOX_ASYNC_ACCOUNT_ID"
  GLOFOX_REDIS_URL                                    = "glofox/apiworker/${ENV_SHORT_NAME}/GLOFOX_REDIS_URL"
  GLOFOX_EXPERIMENTS_API_KEY                          = "glofox/apiworker/${ENV_SHORT_NAME}/GLOFOX_EXPERIMENTS_API_KEY"
  LIVE_STREAM_SERVICE_SALT                            = "glofox/apiworker/${ENV_SHORT_NAME}/LIVE_STREAM_SERVICE_SALT"
  GLOFOX_MIXPANEL_TOKEN                               = "glofox/apiworker/${ENV_SHORT_NAME}/GLOFOX_MIXPANEL_TOKEN"
  JWT_SALT_GENERATE                                   = "glofox/apiworker/${ENV_SHORT_NAME}/JWT_SALT_GENERATE"
  JWT_SALT_VERIFY                                     = "glofox/apiworker/${ENV_SHORT_NAME}/JWT_SALT_VERIFY"
  URL_SIGNATURE_KEY                                   = "glofox/apiworker/${ENV_SHORT_NAME}/URL_SIGNATURE_KEY"
  SENDGRID_PASSWORD                                   = "glofox/apiworker/${ENV_SHORT_NAME}/SENDGRID_PASSWORD"
  ADDON_SERVICE_API_TOKEN                             = "glofox/apiworker/${ENV_SHORT_NAME}/ADDON_SERVICE_API_TOKEN"
  LAUNCH_DARKLY_API_TOKEN                             = "glofox/apiworker/${ENV_SHORT_NAME}/LAUNCH_DARKLY_API_TOKEN"
  GLOFOX_MAILCHIMP_WEBHOOK_SECRET_KEY                 = "glofox/apiworker/${ENV_SHORT_NAME}/GLOFOX_MAILCHIMP_WEBHOOK_SECRET_KEY"
  GLOFOX_TWILIO_WEBHOOK_SECRET_KEY                    = "glofox/apiworker/${ENV_SHORT_NAME}/GLOFOX_TWILIO_WEBHOOK_SECRET_KEY"
  LAUNCH_DARKLY_SDK_KEY                               = "glofox/apiworker/${ENV_SHORT_NAME}/LAUNCH_DARKLY_SDK_KEY"
  GLOFOX_ZENDESK_TOKEN                                = "glofox/apiworker/${ENV_SHORT_NAME}/GLOFOX_ZENDESK_TOKEN"
  GLOFOX_ZENDESK_USERNAME                             = "glofox/apiworker/${ENV_SHORT_NAME}/GLOFOX_ZENDESK_USERNAME"
  HONEYCOMB_API_KEY                                   = "glofox/HONEYCOMB_APIKEY"
  GOOGLE_RECAPTCHA_SECRET                             = "glofox/recaptcha_service_account_key"
  RECEIPTS_HTTP_API_KEY                               = "receipts/${ENV_SHORT_NAME}/HTTP_API_KEY"
  APPLE_DEV_KEY                                       = "glofox/standalone-apps/APPLE_DEV_KEY"
  ACTIVE_BRANCHES_COMMUNICATIONS                      = "glofox/api/${ENV_SHORT_NAME}/ACTIVE_BRANCHES_COMMUNICATIONS"
  COMMUNICATIONS_HTTP_API_KEY                         = "communications/${ENV_SHORT_NAME}/HTTP_API_KEY"
}
