<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\MigrationDataPool\Search\Expressions;

use Glofox\Domain\MigrationDataPool\Search\Expressions\GlofoxInternalValue;
use Webmozart\Expression\Expression;

\App::import('Test/Case', 'GlofoxTestCase');

class GlofoxInternalValueTest extends \GlofoxTestCase
{
    public $fixtures = [];
    protected Expression $evaluable;

    public function setUp()
    {
        parent::setUp();
        
        $this->evaluable = new GlofoxInternalValue(['field_name' => 'mock-internal-value']);
    }

    public function testEvaluationPasses(): void
    {
        $data = [
            'internal_value' => ['field_name' => 'mock-internal-value'],
        ];

        $this->assertTrue($this->evaluable->evaluate($data));
    }
}
