<?php

namespace CakeTestCases\Glofox\Domain\Subscription\Metadata;

use CakeTestCases\Glofox\Domain\Users\Traits\FetchUsersTrait;
use Glofox\Domain\Charges\Metadata\SubscriptionChargeMetadata;
use Glofox\Domain\Charges\Type;
use Glofox\Domain\Users\Models\User;
use PaymentMethods;

\App::import('Test/Case', 'GlofoxTestCase');

define('PLAN_CODE', '56ceca59dccde');
define('MEMBERSHIP_ID', '56ceca057cad3653598b4582');
define('NON_PAYG_MEMBER', 'a9a2222a05c677bda916611c');
define('PAYG_MEMBER', '5a2aad49e1608aa72b004443');

class SubscriptionChargeMetadataTest extends \GlofoxTestCase
{
    use FetchUsersTrait;

    public function test_serializes_successful_payment_with_non_payg_member(): void
    {
        $user = $this->fetchUser(NON_PAYG_MEMBER);
        $paymentMethod = PaymentMethods::CREDIT_CARD();
        $chargeType = Type::SUBSCRIPTION_PAYMENT();
        $branchId = $user->currentBranchId();
        $subscriptionId = 'stubSubId';
        $environment = 'test';
        $resourceId = 'stubResId';

        $metadata = new SubscriptionChargeMetadata(
            $user,
            $paymentMethod,
            $chargeType,
            $branchId,
            $subscriptionId,
            $environment,
            $resourceId
        );

        $expected = [
            'namespace' => 'glofox',
            'branch_id' => '49a7011a05c677b9a916612a',
            'glofox_event' => 'subscription_payment',
            'stripe_subscription_id' => 'stubSubId',
            'user_id' => $user->id(),
            'membership_id' => MEMBERSHIP_ID,
            'user_name' => $user->fullName(),
            'environment' => 'test',
            'payment_method' => 'credit_card',
            'plan_code' => PLAN_CODE,
            'resource_id' => $resourceId
        ];

        self::assertEquals($expected, $metadata->toArray());

        /** @var \StripeCharge $stripeChargeCakeModel */
        $stripeChargeCakeModel = app(\StripeCharge::class);
        $metadata = $stripeChargeCakeModel->build_subscription_payment_metadata(
            $user,
            $branchId,
            $subscriptionId,
            true,
            $paymentMethod->getValue(),
            $resourceId
        );

        self::assertEquals($expected, $metadata);
    }

    public function test_serializes_failed_payment_with_non_payg_member(): void
    {
        $user = $this->fetchUser(NON_PAYG_MEMBER);
        $paymentMethod = PaymentMethods::CREDIT_CARD();
        $chargeType = Type::SUBSCRIPTION_PAYMENT_FAILED();
        $branchId = $user->currentBranchId();
        $subscriptionId = 'stubSubId';
        $environment = 'test';
        $resourceId = 'stubResId';

        $metadata = new SubscriptionChargeMetadata(
            $user,
            $paymentMethod,
            $chargeType,
            $branchId,
            $subscriptionId,
            $environment,
            $resourceId
        );

        $expected = [
            'namespace' => 'glofox',
            'branch_id' => '49a7011a05c677b9a916612a',
            'glofox_event' => 'subscription_payment_failed',
            'stripe_subscription_id' => 'stubSubId',
            'user_id' => $user->id(),
            'membership_id' => MEMBERSHIP_ID,
            'user_name' => $user->fullName(),
            'environment' => 'test',
            'payment_method' => 'credit_card',
            'plan_code' => PLAN_CODE,
            'resource_id' => $resourceId
        ];

        self::assertEquals($expected, $metadata->toArray());

        /** @var \StripeCharge $stripeChargeCakeModel */
        $stripeChargeCakeModel = app(\StripeCharge::class);
        $metadata = $stripeChargeCakeModel->build_subscription_payment_metadata(
            $user,
            $branchId,
            $subscriptionId,
            false,
            $paymentMethod->getValue(),
            $resourceId
        );

        self::assertEquals($expected, $metadata);
    }

    public function test_serializes_successful_payment_with_payg_member(): void
    {
        $user = $this->fetchUser(PAYG_MEMBER);
        $paymentMethod = PaymentMethods::CREDIT_CARD();
        $chargeType = Type::SUBSCRIPTION_PAYMENT_FAILED();
        $branchId = $user->currentBranchId();
        $subscriptionId = 'stubSubId';
        $environment = 'test';
        $resourceId = 'stubResId';

        $metadata = new SubscriptionChargeMetadata(
            $user,
            $paymentMethod,
            $chargeType,
            $branchId,
            $subscriptionId,
            $environment,
            $resourceId
        );

        $expected = [
            'namespace' => 'glofox',
            'branch_id' => '49a7011a05c677b9a916612a',
            'glofox_event' => 'subscription_payment_failed',
            'stripe_subscription_id' => 'stubSubId',
            'user_id' => $user->id(),
            'membership_id' => null,
            'user_name' => $user->fullName(),
            'environment' => 'test',
            'payment_method' => 'credit_card',
            'plan_code' => null,
            'resource_id' => $resourceId
        ];

        self::assertEquals($expected, $metadata->toArray());

        /** @var \StripeCharge $stripeChargeCakeModel */
        $stripeChargeCakeModel = app(\StripeCharge::class);
        $metadata = $stripeChargeCakeModel->build_subscription_payment_metadata(
            $user,
            $branchId,
            $subscriptionId,
            false,
            $paymentMethod->getValue(),
            $resourceId
        );

        self::assertEquals($expected, $metadata);
    }

    public function test_serializes_passing_membership_id_as_param_for_payg_member(): void
    {
        $user = $this->fetchUser(PAYG_MEMBER);
        $branchId = $user->currentBranchId();
        $subscriptionId = 'stubSubId';
        $paymentMethod = PaymentMethods::CREDIT_CARD();
        $resourceId = 'stubResId';
        $membershipId = 'stubMembershipId';

        $chargeType = Type::SUBSCRIPTION_PAYMENT_FAILED();
        $environment = 'test';
        

        $metadata = new SubscriptionChargeMetadata(
            $user,
            $paymentMethod,
            $chargeType,
            $branchId,
            $subscriptionId,
            $environment,
            $resourceId,
            $membershipId
        );

        $expected = [
            'namespace' => 'glofox',
            'branch_id' => '49a7011a05c677b9a916612a',
            'glofox_event' => 'subscription_payment_failed',
            'stripe_subscription_id' => 'stubSubId',
            'user_id' => $user->id(),
            'membership_id' => 'stubMembershipId',
            'user_name' => $user->fullName(),
            'environment' => 'test',
            'payment_method' => 'credit_card',
            'plan_code' => null,
            'resource_id' => $resourceId
        ];

        self::assertEquals($expected, $metadata->toArray());

        /** @var \StripeCharge $stripeChargeCakeModel */
        $stripeChargeCakeModel = app(\StripeCharge::class);
        $metadata = $stripeChargeCakeModel->build_subscription_payment_metadata(
            $user,
            $branchId,
            $subscriptionId,
            false,
            $paymentMethod->getValue(),
            $resourceId,
            $membershipId
        );

        self::assertEquals($expected, $metadata);
    }

    public function test_serializes_passing_membership_plan_code_as_param_for_payg_member(): void
    {
        $user = $this->fetchUser(PAYG_MEMBER);
        $branchId = $user->currentBranchId();
        $subscriptionId = 'stubSubId';
        $paymentMethod = PaymentMethods::CREDIT_CARD();
        $resourceId = 'stubResId';
        $membershipPlanCode = 'stubMembershipPlanCode';

        $chargeType = Type::SUBSCRIPTION_PAYMENT_FAILED();
        $environment = 'test';
        

        $metadata = new SubscriptionChargeMetadata(
            $user,
            $paymentMethod,
            $chargeType,
            $branchId,
            $subscriptionId,
            $environment,
            $resourceId,
            null,
            $membershipPlanCode
        );

        $expected = [
            'namespace' => 'glofox',
            'branch_id' => '49a7011a05c677b9a916612a',
            'glofox_event' => 'subscription_payment_failed',
            'stripe_subscription_id' => 'stubSubId',
            'user_id' => $user->id(),
            'membership_id' => null,
            'user_name' => $user->fullName(),
            'environment' => 'test',
            'payment_method' => 'credit_card',
            'plan_code' => 'stubMembershipPlanCode',
            'resource_id' => $resourceId
        ];

        self::assertEquals($expected, $metadata->toArray());

        /** @var \StripeCharge $stripeChargeCakeModel */
        $stripeChargeCakeModel = app(\StripeCharge::class);
        $metadata = $stripeChargeCakeModel->build_subscription_payment_metadata(
            $user,
            $branchId,
            $subscriptionId,
            false,
            $paymentMethod->getValue(),
            $resourceId,
            null,
            $membershipPlanCode
        );

        self::assertEquals($expected, $metadata);
    }

    public function test_serializes_null_membershipId_for_non_payg_member(): void
    {
        $user = $this->fetchUser(NON_PAYG_MEMBER);
        $branchId = $user->currentBranchId();
        $subscriptionId = 'stubSubId';
        $paymentMethod = PaymentMethods::CREDIT_CARD();
        $resourceId = 'stubResId';
        $membershipPlanCode = 'stubMembershipPlanCode';

        $chargeType = Type::SUBSCRIPTION_PAYMENT_FAILED();
        $environment = 'test';
        

        $metadata = new SubscriptionChargeMetadata(
            $user,
            $paymentMethod,
            $chargeType,
            $branchId,
            $subscriptionId,
            $environment,
            $resourceId,
            null,
            $membershipPlanCode
        );

        $expected = [
            'namespace' => 'glofox',
            'branch_id' => '49a7011a05c677b9a916612a',
            'glofox_event' => 'subscription_payment_failed',
            'stripe_subscription_id' => 'stubSubId',
            'user_id' => $user->id(),
            'membership_id' => MEMBERSHIP_ID,
            'user_name' => $user->fullName(),
            'environment' => 'test',
            'payment_method' => 'credit_card',
            'plan_code' => 'stubMembershipPlanCode',
            'resource_id' => $resourceId
        ];

        self::assertEquals($expected, $metadata->toArray());

        /** @var \StripeCharge $stripeChargeCakeModel */
        $stripeChargeCakeModel = app(\StripeCharge::class);
        $metadata = $stripeChargeCakeModel->build_subscription_payment_metadata(
            $user,
            $branchId,
            $subscriptionId,
            false,
            $paymentMethod->getValue(),
            $resourceId,
            null,
            $membershipPlanCode
        );

        self::assertEquals($expected, $metadata);
    }

    public function test_serializes_null_membership_planCode_for_non_payg_member(): void
    {
        $user = $this->fetchUser(NON_PAYG_MEMBER);
        $branchId = $user->currentBranchId();
        $subscriptionId = 'stubSubId';
        $paymentMethod = PaymentMethods::CREDIT_CARD();
        $resourceId = 'stubResId';

        $chargeType = Type::SUBSCRIPTION_PAYMENT_FAILED();
        $environment = 'test';
        

        $metadata = new SubscriptionChargeMetadata(
            $user,
            $paymentMethod,
            $chargeType,
            $branchId,
            $subscriptionId,
            $environment,
            $resourceId,
            null,
            null
        );

        $expected = [
            'namespace' => 'glofox',
            'branch_id' => '49a7011a05c677b9a916612a',
            'glofox_event' => 'subscription_payment_failed',
            'stripe_subscription_id' => 'stubSubId',
            'user_id' => $user->id(),
            'membership_id' => MEMBERSHIP_ID,
            'user_name' => $user->fullName(),
            'environment' => 'test',
            'payment_method' => 'credit_card',
            'plan_code' => PLAN_CODE,
            'resource_id' => $resourceId
        ];

        self::assertEquals($expected, $metadata->toArray());

        /** @var \StripeCharge $stripeChargeCakeModel */
        $stripeChargeCakeModel = app(\StripeCharge::class);
        $metadata = $stripeChargeCakeModel->build_subscription_payment_metadata(
            $user,
            $branchId,
            $subscriptionId,
            false,
            $paymentMethod->getValue(),
            $resourceId,
            null,
            null
        );

        self::assertEquals($expected, $metadata);
    }

    public function test_serializes_with_membership_not_synced(): void
    {
        $user = $this->fetchUser(NON_PAYG_MEMBER);
        $branchId = $user->currentBranchId();
        $subscriptionId = 'stubSubId';
        $paymentMethod = PaymentMethods::CREDIT_CARD();
        $resourceId = 'stubResId';
        $membershipId = 'stubMembershipId';
        $membershipPlanCode = 'stubMembershipPlanCode';

        $chargeType = Type::SUBSCRIPTION_PAYMENT_FAILED();
        $environment = 'test';

        self::assertNotEquals($user->membership()->id(), $membershipId);
        
        $metadata = new SubscriptionChargeMetadata(
            $user,
            $paymentMethod,
            $chargeType,
            $branchId,
            $subscriptionId,
            $environment,
            $resourceId,
            $membershipId,
            $membershipPlanCode
        );

        $expected = [
            'namespace' => 'glofox',
            'branch_id' => '49a7011a05c677b9a916612a',
            'glofox_event' => 'subscription_payment_failed',
            'stripe_subscription_id' => 'stubSubId',
            'user_id' => $user->id(),
            'membership_id' => 'stubMembershipId',
            'user_name' => $user->fullName(),
            'environment' => 'test',
            'payment_method' => 'credit_card',
            'plan_code' => 'stubMembershipPlanCode',
            'resource_id' => $resourceId
        ];

        self::assertEquals($expected, $metadata->toArray());

        /** @var \StripeCharge $stripeChargeCakeModel */
        $stripeChargeCakeModel = app(\StripeCharge::class);
        $metadata = $stripeChargeCakeModel->build_subscription_payment_metadata(
            $user,
            $branchId,
            $subscriptionId,
            false,
            $paymentMethod->getValue(),
            $resourceId,
            $membershipId,
            $membershipPlanCode
        );

        self::assertEquals($expected, $metadata);
    }

}
