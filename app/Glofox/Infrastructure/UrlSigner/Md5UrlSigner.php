<?php

namespace Glofox\Infrastructure\UrlSigner;

use Glofox\Domain\Users\Models\User;
use Glofox\Request;
use Spatie\UrlSigner\MD5UrlSigner as SpatieMD5UrlSigner;

class Md5UrlSigner implements UrlSignerInterface
{
    private \Spatie\UrlSigner\MD5UrlSigner $signer;

    private \Glofox\Domain\Users\Models\User $user;

    public function __construct(SpatieMD5UrlSigner $signer, User $user)
    {
        $this->signer = $signer;
        $this->user = $user;
    }

    public function sign(string $url, int $days): string
    {
        $signedUrl = $this->signer->sign($url, $days);

        return sprintf('%s&u=%s&b=%s', $signedUrl, $this->user->id(), $this->user->currentBranchId());
    }

    public function validate(string $url): bool
    {
        foreach ($this->user->branches() as $branchId) {
            $searchFor = sprintf('&u=%s&b=%s', $this->user->id(), $branchId);
            $replaceWith = '';

            $url = str_replace($searchFor, $replaceWith, $url);
        }

        return $this->signer->validate($url);
    }
}