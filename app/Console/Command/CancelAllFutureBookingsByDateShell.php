<?php

declare(strict_types=1);

use Carbon\Carbon;
use Glofox\Domain\Bookings\Models\Booking as BookingModel;
use Glofox\Domain\Bookings\Repositories\BookingsRepository;
use Glofox\Domain\Bookings\UseCase\CancelBooking;
use Glofox\Domain\Bookings\UseCase\CancelBookingParams;
use Glofox\Domain\Bookings\UseCase\CancelBookingsByBatchId;
use Glofox\Domain\Bookings\UseCase\CancelBookingsByBatchIdParams;
use Glofox\Domain\Users\Models\User as UserModel;
use Glofox\Domain\Users\Services\BotGenerator;
use Psr\Log\LoggerInterface;

App::uses('Shell', 'Console');

class CancelAllFutureBookingsByDateShell extends Shell
{
    private LoggerInterface $logger;
    private BookingsRepository $bookingsRepository;
    private CancelBookingsByBatchId $cancelBookingsByBatchId;
    private CancelBooking $cancelBooking;
    private BotGenerator $botGenerator;

    public function __construct($stdout = null, $stderr = null, $stdin = null)
    {
        parent::__construct($stdout, $stderr, $stdin);

        $this->logger = app()->make(LoggerInterface::class);
        $this->bookingsRepository = app()->make(BookingsRepository::class);
        $this->cancelBookingsByBatchId = app()->make(CancelBookingsByBatchId::class);
        $this->cancelBooking = app()->make(CancelBooking::class);
        $this->botGenerator = app()->make(BotGenerator::class);
    }

    public function getOptionParser(): ConsoleOptionParser
    {
        $parser = parent::getOptionParser();

        $parser->addOption('branchId', [
            'short' => 'b',
            'help' => 'Branch ID',
            'required' => true
        ]);

        $parser->addOption('date', [
            'short' => 'd',
            'help' => 'Date to start cancelling',
            'required' => true
        ]);

        $parser->addOption('dryRun', [
            'short' => 'r',
            'help' => 'Dry run',
            'required' => true
        ]);

        return $parser;
    }

    public function main(): void
    {
        $this->logger->info('[CancelAllFutureBookingsByDateShell] Started');

        $alreadyProcessedBatchId = [];
        $date = Carbon::parse($this->params['date']);
        $branchId = $this->params['branchId'];
        $isDryRun = (bool) filter_var($this->params['dryRun'], FILTER_VALIDATE_BOOLEAN);
        $bot = $this->botGenerator->generateForBranch($branchId);

        $bookingsToCancel = $this->bookingsRepository->getFromDate($branchId, $date);

        $this->logger->info(
            sprintf(
                '[CancelAllFutureBookingsByDateShell] %d bookings are going to be cancelled for branch %s',
                count($bookingsToCancel),
                $branchId
            )
        );

        foreach ($bookingsToCancel as $booking) {
            try {
                if (!$booking->isFromBatch()) {
                    $this->cancelBooking($booking, $bot, $isDryRun);

                    continue;
                }

                if ($this->isBatchIdAlreadyProcessed($booking->batchId(), $alreadyProcessedBatchId)) {
                    continue;
                }

                $this->cancelBookingsByBatchId($booking, $bot, $date, $isDryRun);

                $alreadyProcessedBatchId[] = $booking->batchId();
            } catch (Throwable $exception) {
                $this->logger->error('[CancelAllFutureBookingsByDateShell] Error cancelling booking', [
                    'bookingId' => $booking->id(),
                    'message' => $exception->getMessage(),
                ]);

                continue;
            }
        }

        $this->logger->info('[CancelAllFutureBookingsByDateShell] Finished');
    }

    private function cancelBookingsByBatchId(BookingModel $booking, UserModel $bot, Carbon $date, bool $isDryRun): void
    {
        if ($isDryRun) {
            $this->logger->info(
                sprintf(
                    '[CancelAllFutureBookingsByDateShell] Dry run: booking %s would be cancelled with batchId %s',
                    $booking->id(),
                    $booking->batchId()
                )
            );

            return;
        }
        $params = new CancelBookingsByBatchIdParams($booking->batchId(), $date->getTimestamp(), $bot->id());
        $this->cancelBookingsByBatchId->execute($params);
    }

    private function cancelBooking(BookingModel $booking, UserModel $bot, bool $isDryRun): void
    {
        if ($isDryRun) {
            $this->logger->info(
                sprintf(
                    '[CancelAllFutureBookingsByDateShell] Dry run: booking %s would be cancelled',
                    $booking->id()
                )
            );

            return;
        }

        $params = new CancelBookingParams($bot, $booking->id(), $booking->userId(), null);
        $this->cancelBooking->execute($params);
    }

    private function isBatchIdAlreadyProcessed(string $batchId, array $alreadyProcessedBatchId): bool
    {
        return in_array($batchId, $alreadyProcessedBatchId, true);
    }
}
