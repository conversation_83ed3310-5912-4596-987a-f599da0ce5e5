<?php

/**
 * CardsTokensHandlerController is used to handler cards tokenization for those providers
 * which works different than the standardized flow Glofox support.
 */
class CardsTokensHandlerController extends AppController
{
    /**
     * @var string
     */
    public $name = 'CardsTokensHandler';

    /**
     * @var array
     */
    public $uses = ['CardTokenHandler'];

    /**
     * payTabsTokenHandler is a webhook that processes cad tokenization from PayTabs.
     */
    public function payTabsTokenHandler()
    {
        $this->render();
        $this->response->type('html');

        if (!$this->request->is(['post'])) {
            return;
        }

        if (!isset($this->request->data['pt_token'])) {
            return;
        }

        if (!isset($this->request->data['pt_customer_password'])) {
            return;
        }

        if (!isset($this->request->data['card_brand'])) {
            return;
        }

        if (!isset($this->request->data['last_4_digits'])) {
            return;
        }

        if (!isset($this->request->data['secure_sign'])) {
            return;
        }

        $params = [
            'order_id' => $this->request->data['order_id'],
            'response_code' => $this->request->data['response_code'],
            'customer_name' => $this->request->data['customer_name'],
            'transaction_currency' => $this->request->data['transaction_currency'],
            'last_4_digits' => $this->request->data['last_4_digits'],
            'customer_email' => $this->request->data['customer_email'],
        ];

        $requestSecureSign = $this->request->data['secure_sign'];
        $generatedSecureSign = $this->createPaytabsSecureHash($params);

        if ($generatedSecureSign !== $requestSecureSign) {
            return;
        }

        $token = $this->request->data['pt_token'];
        $password = $this->request->data['pt_customer_password'];
        $brand = $this->request->data['card_brand'];
        $lastFour = $this->request->data['last_4_digits'];
        $userId = $this->request->data['order_id'];

        $cardTokenHandler = [
            'user_id' => $userId,
            'card_token' => $token,
            'options' => [
                'brand' => $brand,
                'last_four' => $lastFour,
                'password' => $password,
            ],
        ];

        $this->CardTokenHandler->save($cardTokenHandler);
    }

    /**
     * getPayTabsToken is to fetch PayTabs card's tokens by Glofox's User ID.
     */
    public function getPayTabsTokenByUserId(string $userId)
    {
        $cardTokenHandler = $this->CardTokenHandler->getLastTokenByUserId($userId);

        return json_encode($cardTokenHandler, JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    private function createPaytabsSecureHash(array $params)
    {
        $string = '';
        $shainPhrase = 'secure@paytabs#@aaes11%%';
        ksort($params);

        foreach ($params as $key => $value) {
            $string .= strtoupper($key) . '=' . $value . $shainPhrase;
        }

        $secureSign = sha1($string);

        return $secureSign;
    }
}
