<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\MigrationDataPool\Service;

use Glofox\Domain\MigrationDataPool\Models\DataPool;
use Glofox\Domain\MigrationDataPool\Models\DataPoolType;
use Glofox\Domain\MigrationDataPool\Models\MigrationDataPool;
use Glofox\Domain\MigrationDataPool\Models\MigrationMapping;
use Glofox\Domain\MigrationDataPool\Repositories\MigrationDataPoolRepository;
use Glofox\Domain\MigrationDataPool\Repositories\MigrationMappingRepository;
use Glofox\Domain\MigrationDataPool\Search\Expressions\LiftExternalId;
use Glofox\Domain\MigrationDataPool\Search\Expressions\UserForeignId;
use Glofox\Domain\MigrationDataPool\Service\UserForeignIdFinderServiceV1;
use Glofox\Domain\MigrationDataPool\Service\UserForeignIdFinderServiceV2;
use Glofox\Domain\MigrationDataPool\Service\UserForeignIdFinderServiceVersionHandler;
use Glofox\Domain\MigrationDataPool\UseCase\UserForeignIdFinderUseCase;
use Mockery\MockInterface;

\App::import('Test/Case', 'GlofoxTestCase');

class UserForeignIdFinderServiceTest extends \GlofoxTestCase
{
    public $fixtures = [];
    private MockInterface $migrationDataPoolRepository;
    private ?UserForeignIdFinderServiceVersionHandler $service = null;
    private MockInterface $migrationMappingRepository;

    public function setUp()
    {
        parent::setUp();

        $this->migrationDataPoolRepository = \Mockery::mock(
            MigrationDataPoolRepository::class
        );

        $this->migrationMappingRepository = \Mockery::mock(
            MigrationMappingRepository::class
        );

        $this->service = new UserForeignIdFinderServiceVersionHandler(
            new UserForeignIdFinderServiceV1($this->migrationDataPoolRepository),
            new UserForeignIdFinderServiceV2($this->migrationMappingRepository)
        );
    }

    public function tearDown()
    {
        parent::tearDown();
        \Mockery::close();
    }

    public function test_it_all_the_users_across_set_of_branches_that_share_the_same_user_foreign_id_v1(): void
    {
        $userForeignId = 'userFgId1';

        $userId1 = 'userId1';
        $userId2 = 'userId2';

        $branchId1 = 'branchId1';
        $branchId2 = 'branchId2';

        $this->migrationDataPoolRepository
            ->shouldReceive('addOrCriteria')
            ->withArgs([
                [
                    new LiftExternalId($userForeignId),
                    new UserForeignId($userForeignId),
                ],
            ])
            ->andReturn($this->migrationDataPoolRepository);

        $this->migrationDataPoolRepository
            ->shouldReceive('addCriteria')
            ->andReturn($this->migrationDataPoolRepository);

        $this->migrationDataPoolRepository
            ->shouldReceive('find')
            ->andReturn([
                new MigrationDataPool('poolId1', 'userId1', 'branchId1', new DataPool(collect())),
                new MigrationDataPool('poolId1', 'userId2', 'branchId2', new DataPool(collect())),
            ]);

        $userIds = $this->service->handle(
            new UserForeignIdFinderUseCase($userForeignId, [$branchId1, $branchId2], null)
        );

        $this->assertEquals([$userId1, $userId2], $userIds->toArray());
    }

    public function test_it_all_the_users_across_set_of_branches_that_share_the_same_user_foreign_id_v2(): void
    {
        $userForeignId = 'userFgId1';

        $userId1 = 'userId1';
        $userId2 = 'userId2';

        $branchId1 = 'branchId1';
        $branchId2 = 'branchId2';

        $dataPool1 = new MigrationMapping(
            'mock-pool-id-1',
            collect($userId1),
            collect($userForeignId),
            $branchId1,
            DataPoolType::USER_ID()
        );

        $dataPool2 = new MigrationMapping(
            'mock-pool-id-2',
            collect($userId2),
            collect($userForeignId),
            $branchId2,
            DataPoolType::USER_ID()
        );

        $this->migrationMappingRepository
            ->shouldReceive('addCriteria')
            ->andReturn($this->migrationMappingRepository);

        $this->migrationMappingRepository
            ->shouldReceive('find')
            ->andReturn([$dataPool1, $dataPool2])
            ->once();

        $userIds = $this->service->handle(
            new UserForeignIdFinderUseCase($userForeignId, [$branchId1, $branchId2], '2')
        );

        $this->assertEquals([$userId1, $userId2], $userIds->toArray());
    }
}
