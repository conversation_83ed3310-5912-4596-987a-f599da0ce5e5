<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\AsyncEvents\Events;

use Glofox\Domain\AsyncEvents\Events\MobileNotificationInitiatedEventPayload;

\App::import('Test/Case', 'GlofoxTestCase');

class MobileNotificationInitiatedEventPayloadTest extends \GlofoxTestCase
{
    public $fixtures = [];

    public function test_it_should_serialize_to_json(): void
    {
        $payload = new MobileNotificationInitiatedEventPayload([
            'namespace' => 'glofox',
            'devices' => 'test',
            'message' => 'test',
            'isFCM' => true,
            'authKey' => 'authkey',
            'refresh_action' => 'action',
            'is_stand_alone' => false,
        ]);

        $this->assertTextContains('"authKey":"authkey"', json_encode($payload, JSON_THROW_ON_ERROR));
    }

    public function test_it_should_throw_exception_if_schema_not_valid()
    {
        $exception = null;

        try {
            $payload = new MobileNotificationInitiatedEventPayload(['message' => 'test']);
        } catch (\Exception $e) {
            $exception = $e;
        }

        $this->assertEquals(new \Exception('Data is missing fields ["namespace","devices","isFCM","authKey","refresh_action","is_stand_alone"]'), $exception);
    }
}
