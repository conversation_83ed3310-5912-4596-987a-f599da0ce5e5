<?php

namespace Glofox\Repositories\Search\Expressions;

use Illuminate\Support\Collection;

class Key extends \Webmozart\Expression\Selector\Key
{
    use HasMongoResolvers;

    /**
     * {@inheritdoc}
     */
    public function evaluate($value)
    {
        if ($value instanceof Collection) {
            $value = $value->toArray();
        }

        // If the key contains a dot, it means that we'll need to evaluate
        // the expression using the dot notation.
        if (strstr($this->getKey(), '.')) {
            return $this->evaluateUsingDotNotation($value, $this->getKey());
        }

        return parent::evaluate($value);
    }

    /**
     * Evaluates an expression with the support of the dot notation
     * for access to multi level arrays.
     * @see https://laravel.com/docs/5.6/helpers#method-data-get
     *
     * @param        $value
     * @param string $key
     *
     * @return bool
     */
    protected function evaluateUsingDotNotation($value, string $key)
    {
        $valueToBeEvaluated = data_get($value, $key);

        return $this->expr->evaluate($valueToBeEvaluated);
    }
}
