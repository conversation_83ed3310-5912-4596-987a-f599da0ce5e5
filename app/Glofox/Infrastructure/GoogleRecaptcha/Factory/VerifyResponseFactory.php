<?php

declare(strict_types=1);

namespace Glofox\Infrastructure\GoogleRecaptcha\Factory;

use Glofox\Infrastructure\GoogleRecaptcha\VerifyResponse;
use Psr\Http\Message\ResponseInterface;

class VerifyResponseFactory
{
    public function create(ResponseInterface $httpResponse): VerifyResponse
    {
        $httpResponse->getBody()->rewind();

        $responseData = $this->deserialize($httpResponse->getBody()->getContents());

        return new VerifyResponse(
            $responseData['success'],
            $responseData['error-codes']
        );
    }

    /**
     * @see https://developers.google.com/recaptcha/docs/verify#api-response
     */
    private function deserialize(string $contents): array
    {
        return \json_decode($contents, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
    }
}
