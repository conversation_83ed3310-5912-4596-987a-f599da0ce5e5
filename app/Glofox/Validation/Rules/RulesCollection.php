<?php

namespace Glofox\Validation\Rules;

use Illuminate\Support\Collection;
use Saritasa\Laravel\Validation\Rule;
use Saritasa\Laravel\Validation\RuleSet;

class RulesCollection extends Collection
{
    /**
     * @param mixed $key
     * @param mixed $value
     *
     * @return $this
     * @throws \Exception
     */
    public function put($key, $value)
    {
        if (!($value instanceof RuleSet)) {
            throw new \Exception('RulesCollection only accept RuleSet objects');
        }

        return parent::put($key, $value);
    }

    /**
     * @param mixed $key
     * @param null  $default
     *
     * @return RuleSet
     */
    public function get($key, $default = null)
    {
        return parent::get($key, $default);
    }

    public function addFromRulesAggregator(RulesAggregator $ruleSet) : self
    {
        foreach ($ruleSet->toArray() as $key => $value) {
            $this->put($key, $value);
        }

        return $this;
    }
}
