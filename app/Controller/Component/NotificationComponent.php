<?php
/**
 * Component to find and load seo data and inject it into the view.
 *
 * <AUTHOR> <<EMAIL>>
 */
App::uses('Component', 'Controller');

use Glofox\Domain\AsyncEvents\Events\MobileNotificationInitiatedEventMeta;
use Glofox\Domain\AsyncEvents\Events\MobileNotificationInitiatedEventPayload;
use Glofox\Domain\Branches\Exceptions\BranchNotFoundException;
use Glofox\Domain\Branches\Models\Branch;
use Glofox\Domain\Branches\Repositories\BranchesRepository;
use Glofox\Domain\Notifications\Action as NotificationAction;
use Glofox\Domain\Notifications\Services\PushNotificationsServiceInterface;
use Glofox\Domain\Notifications\AndroidDeliveryMethod;
use Glofox\Domain\Notifications\Services\NotificationsPublisher;
use Glofox\Repositories\Search\Expressions\Shared\Id;
use GuzzleHttp\Exception\ClientException;
use Psr\Log\LoggerInterface;

/**
 * Class NotificationComponent.
 */
class NotificationComponent extends Component
{
    // assigns values of referencing controller
    public const GLOFOX_BUNDLE = '_glofox';
    public $components = ['Queue'];

    /**
     * @param Controller $controller
     */
    public $controller = null;
    /** @var PushNotification PushNotification */
    protected $PushNotification;
    /** @var \Branch Branch */
    protected $Branch;
    /** @var User User */
    protected $User;
    /** @var Client Client */
    protected $Client;

    /**
     * @var PushNotificationsServiceInterface pushNotificationsService
     */
    private $pushNotificationsService;

    public function initialize(Controller $controller)
    {
        parent::initialize($controller);

        $this->pushNotificationsService = app()->make(PushNotificationsServiceInterface::class);
        $this->PushNotification = app()->make(PushNotification::class);

        $this->Branch = app()->make(Branch::class);
        $this->User = app()->make(User::class);
        $this->Client = app()->make(Client::class);
    }

    /**
     * [broadcast description].
     *
     * @param $branch_id
     * @param $message
     *
     * @return array [type]            [description]
     *
     * @internal param $ [type] $branch_id [description]
     * @internal param $ [type] $message   [description]
     */
    public function broadcast($branch_id, $message)
    {
        $this->Branch->getPushNotificationConf($branch_id);

        if (empty($branch_id)) {
            return ['success' => false, 'message' => 'Empty Branch Id'];
        }

        if (empty($message)) {
            return ['success' => false, 'message' => 'Empty Message'];
        }

        // Enqueue
        $type = 'Component';
        $name = 'Notification';
        $method = 'broadcast_push_message';
        $parameters = [$branch_id, $message];

        return $this->Queue->sendMessage($type, $name, $method, $parameters);
    }

    /*!
     * List of device ids by user ids list. Created to support OLD DASHBOARD FUNCTIONALITIES, not part of the core new version
     * @param      [type]                   $os        "android","ios"
     * @param      [type]                   $uIds [description]
     * @return     [type]                         [description]
     */

    /**
     * @param $branchId
     *
     * @return array
     */
    public function deviceIdsByBranchId($branchId)
    {
        //Find Users with ids
        $users = $this->User->find(
            'all',
            [
                'fields' => 'device.id',
                'conditions' => [
                    'branch_id' => $branchId,
                    'device.id' => ['$exists' => true],
                ],
            ]
        );

        return array_map(
            [
                $this,
                'mapDeviceIds',
            ],
            $users
        );
    }

    /*!
     * Map Device Ids. Because mongo query brings information about the User model additional to the device.id we need
     * to clean it up.
     * @param      [type]                   $user   User model to extract the device.id from
     * @return     [type]                   array of device ids
     */

    /**
     * @param $user
     *
     * @return mixed
     */
    public function mapDeviceIds($user)
    {
        return $user['User']['device']['id'];
    }

    /**
     * [broadcast_push_message description].
     *
     * @param $branch_id
     * @param $message
     *
     * @return array [type]            [description]
     *
     * @internal param $ [type] $branch_id [description]
     * @internal param $ [type] $message   [description]
     */
    public function broadcast_push_message($branchId, $message)
    {
        $this->cakeBranchModel = ClassRegistry::init('Branch');
        $cakeBranch = (is_array($branchId)) ? $branchId : $this->cakeBranchModel->getPushNotificationConf($branchId);

        if (!$this->isBranchConfiguredForPush($cakeBranch)) {
            return ['success' => false, 'message' => 'The branch is not configured to receive push notifications.'];
        }

        $branchesRepository = app()->make(BranchesRepository::class);
        /** @var \Glofox\Domain\Branches\Models\Branch $branch */
        $branch = $branchesRepository->addCriteria(new Id($branchId))
            ->firstOrFail(function () use ($branchId) {
                throw BranchNotFoundException::withId($branchId);
            });
        $client = $this->Client->getByNamespace($branch->namespace())['Client'];
        $tokens = $this->deviceIdsByBranchId($branchId);

        if (isset($tokens)) {
            $this->send($message, $tokens, $client['namespace'], $client['bundles'], $branch);
        }
    }

    /**
     * [ios_push description].
     *
     * @param [type] $token          [description]
     * @param [type] $pem            [description]
     * @param [type] $password       [description]
     * @param [type] $message_key    [description]
     * @param [type] $is_development [description]
     * @param [type] $namespace      [description]
     * @param [type] $message        [description]
     * @param [type] $badge          [description]
     *
     * @return [type] [description]
     */
    public function ios_push($tokens, $pem, $password, $message_key, $is_development, $namespace, $message, $badge)
    {
        $this->IOSPush->setMessage($message);
        $this->IOSPush->setBadge($badge);
        $this->IOSPush->setPem($pem);
        $this->IOSPush->setPassword($password);
        $this->IOSPush->setMessageKey($message_key);
        $this->IOSPush->setIsDevelopment($is_development);
        $this->IOSPush->setNamespace($namespace);

        return $this->IOSPush->push($tokens);
    }

    /**
     * @param $branch
     * @param $token
     * @param $message
     * @param $message_key
     *
     * @return array
     */
    public function build_ios_push($branch, $token, $message, $message_key)
    {
        $branch_config = $branch['Branch']['push_config'];

        $environment = $branch_config['environment'];
        $is_development = ('development' == $environment) ? true : false;
        $pem = $branch_config[$environment]['ios']['pem'];
        $password = $branch_config[$environment]['ios']['password'];
        $badge = 1;

        return [
            'message' => $message,
            'pem' => $pem,
            'password' => $password,
            'message_key' => $message_key,
            'is_development' => $is_development,
            'namespace' => $branch['Branch']['namespace'],
            'token' => $token,
            'badge' => $badge,
        ];
    }

    /**
     * @param $branch
     * @param $registration_ids
     * @param $message
     *
     * @return array
     */
    public function build_android_push($branch, $registration_ids, $message)
    {
        $branch_config = $branch['Branch']['push_config'];
        $environment = $branch_config['environment'];
        $is_development = ('development' == $environment) ? true : false;
        $api_key = $branch_config[$environment]['android']['api_key'];

        $message = ['data' => $message];

        return [
            'message' => $message,
            'api_key' => $api_key,
            'is_development' => $is_development,
            'registration_ids' => $registration_ids,
        ];
    }

    /**
     * @param $branch
     *
     * @return bool
     */
    public function isBranchConfiguredForPush($branch)
    {
        return isset($branch['Branch']['push_config']);
    }

    /**
     * @param $branch_id
     * @param $user_id
     * @param $message
     *
     * @return array
     */
    public function push_message($branchId, $userId, $message)
    {
        // Message must be the message string
        if (is_array($message) && isset($message['msg'])) {
            $message = $message['msg'];
        }

        if (!is_string($message) || empty($message)) {
            return [
                'success' => false,
                'message' => 'Invalid message',
            ];
        }

        if (!is_array($userId)) {
            $this->User = ClassRegistry::init('User');
        }
        $user = (is_array($userId)) ? $userId : $this->User->getDeviceIdByUserId($userId);
        if (!is_array($branchId)) {
            $this->cakeBranchModel = ClassRegistry::init('Branch');
        }
        $cakeBranch = (is_array($branchId)) ? $branchId : $this->cakeBranchModel->getPushNotificationConf($branchId);

        if (!$this->isBranchConfiguredForPush($cakeBranch)) {
            return ['success' => false, 'message' => 'The branch is not configured to receive push notifications.'];
        }
        if (!isset($user['User']['device']['os'])) {
            return ['success' => false, 'message' => 'The user doesn\'t have the device id set up for push notifications.'];
        }

        $result = ['success' => false];

        $branchesRepository = app()->make(BranchesRepository::class);
        /** @var \Glofox\Domain\Branches\Models\Branch $branch */
        $branch = $branchesRepository->addCriteria(new Id($branchId))
            ->firstOrFail(function () use ($branchId) {
                throw BranchNotFoundException::withId($branchId);
            });

        $user = $this->User->findById($userId)['User'];
        $client = $this->Client->getByNamespace($user['namespace'])['Client'] ?? null;
        $deviceId = $user['device']['id'] ?? null;
        $result = $this->send($message, $deviceId ? [$deviceId] : [], $user['namespace'], $client['bundles'] ?? [], $branch);

        return $result;
    }

    /********************************************* DASHBOARD V2 *********************************************
     *
     * @param $message
     * @param $devices
     * @param $namespace
     * @param $bundle
     *
     * @return array
     */

    /*!
     * Send Push Notification to Glofox Pusher Platform. Assuming bundles is an array of one
     * @param      [type]                   $message   String
     * @param      [type]                   $devices   Array of token both android and ios
     * @param      [Array]                   $bundles    bundle  ex. [_glofox] (lowest priority)
     * @param      [type]                   $namespace namespace ex fitnessampm (highest priority)
     * @return     [type]                              [description]
     */
    public function send($message, $devices, $namespace, $bundles, Branch $branch)
    {
        $logger = app()->make(LoggerInterface::class);

        $androidDeliveryMethod = $branch->pushConfiguration()->androidDeliveryMethod();

        //Set to bundle cert default
        $data = [
            'namespace' => $namespace, //ex. _glofox
            'devices' => $devices,
            'message' => $message,
            'isFCM' => $androidDeliveryMethod->is(AndroidDeliveryMethod::FCM()),
            'authKey' => env('PUSHER_AUTH_KEY'),
            'refresh_action' => $this->getPushNotificationAction()->getValue(),
            'is_stand_alone' => false,
        ];

        //ex: [_glofox, _wffactory] or [_glofox] or [_glofox, _wffactory, _somethingelse]
        // if we have any element which is not _glofox, then we consider it as stand alone app.
        // This consideration includes single location clients and multiple location clients(bundled apps) as well.
        if (is_array($bundles) && count($bundles) > 0) {
            $bundleName = $bundles[0];
            foreach ($bundles as $bundle) {
                $bundleName = $bundle;
                if (self::GLOFOX_BUNDLE != $bundle) {
                    break;
                }
            }
            $data['bundle'] = $bundleName;
            if (self::GLOFOX_BUNDLE != $bundleName) {
                $data['is_stand_alone'] = true;
            }
        }

        $error = null;
        try {
            $publisher = app()->make(NotificationsPublisher::class);
            $publisher->sendMobileNotificationInitiatedEvent(
                new MobileNotificationInitiatedEventMeta([
                    'branch_id' => $branch->id(),
                    'branch_name' => $branch->name(),
                ]),
                new MobileNotificationInitiatedEventPayload($data)
            );
        } catch (\Exception $e) {
            $error = $e->getMessage();
            $logger->warning(sprintf('Failed sending push notifications: %s', $error));
        }

        return ['success' => true, 'err' => $error, 'response' => null];
    }

    /*!
     * Report per date, format date is a string of YYYYMMDD
     * @param      [type]                   $namespace [description]
     * @param      [type]                   $date      [description]
     * @return     [type]                              [description]
     */

    /**
     * @param $namespace
     * @param $startTime
     * @param $endTime
     * @param string|null $branchId
     * @return array
     */
    public function status($namespace, $startTime, $endTime, ?string $branchId): array
    {
        $response = $this->pushNotificationsService->getMessagesStatusesInDateRange($namespace, $branchId, $startTime, $endTime);
        
        return ['success' => true, 'response' => $response];
    }

    /**
     * @throws ClientException
     *
     * @return int
     */
    public function getPusherStatusCode(): int
    {
        $response = $this->pushNotificationsService->health();

        return $response->getStatusCode();
    }

    public function getPushNotificationAction(): NotificationAction
    {
        /** @var \Illuminate\Support\Collection $pushMessageCollection */
        $pushMessageCollection = app()->make('push-message-bag');
        $notificationAction = $pushMessageCollection->get('PushAction');
        $pushMessageCollection->forget('PushMessage');

        if (empty($notificationAction)) {
            $notificationAction = NotificationAction::NONE();
        }

        return $notificationAction;
    }
}
