FORMAT: 1A
HOST: http://glofox.com/

# Glofox API Gateway

Headers to be included in every API call:
* x-glofox-branch-id
* x-api-key
* x-glofox-api-token

.

## Group Clients

### Get All Clients [/clients]

#### Get All Clients [GET]

+ Response 200 (application/json)

        {
            "object": "list",
            "page": 1,
            "limit": 50,
            "has_more": false,
            "total_count": 1,
            "data": [
                {
                    "_id" : "53fb0a22fe25f0c84c8b4574",
                    "name" : "Glofox",
                    "namespace" : "glofox"
                }
            ]
        }


### Get Client by Id [/clients/{client_id}]

#### Get Client by Id [GET]


+ Response 200 (application/json)

        {
            "_id" : "53fb0a22fe25f0c84c8b4574",
            "name" : "Glofox",
            "namespace" : "glofox"
        }

## Group Branches

### Get All Branches [/branches]

#### Get All Branches [GET]

+ Response 200 (application/json)

        {
            "object": "list",
            "page": 1,
            "limit": 50,
            "has_more": false,
            "total_count": 2,
            "data": [
                {
                    "_id": "53fb0a22fe25f0c84c8b4575",
                    "address": {
                        "street": "Unit 7, Sandyford Business Centre, Sandyford, D18 AX29",
                        "city": "Sandyford",
                        "state": "County Dublin",
                        "country": "Ireland",
                        "country_code": "IE",
                        "district": "Dublin",
                        "latitude": 53.276322,
                        "longitude": -6.207031,
                        "currency": "EUR",
                        "continent": "EU",
                        "timezone_id": "Europe/Dublin"
                    },
                    "email": "<EMAIL>",
                    "facebook": "https://www.facebook.com/Weareglofox/",
                    "features": {
                        "classes": {
                            "enabled": true,
                            "weeks_display": 9
                        },
                        "booking": {
                            "enabled": true,
                            "strike_system": {
                                "enabled": true,
                                "max_strikes": 3
                            },
                            "cancel_credit_option": "model_id",
                            "refunded_credit_expiry": 90,
                            "guest_bookings": 4,
                            "booking_open_window": 168,
                            "waiting_list_enabled": true,
                            "auto_booking_enabled": false,
                            "waiting_list": 5,
                            "booking_cancel_window": 1,
                            "late_cancellation_enabled": true
                        },
                        "news": {
                            "enabled": true
                        },
                        "courses": {
                            "enabled": true,
                            "guest_bookings": 1
                        },
                        "store": {
                            "enabled": true
                        },
                        "memberships": {
                            "enabled": true,
                            "payg": {
                                "enabled": true
                            }
                        },
                        "payments": {
                            "stripe_custom": {
                                "allowed": true
                            }
                        }
                    },
                    "name": "Glofox - Dublin",
                    "namespace": "glofox",
                    "phone": "019010449",
                    "twitter": "@weareglofox",
                    "website": "https://www.glofox.com/",
                    "stripe_plan_code": "platinum",
                    "type": "branch",
                    "image_url": "https://cdn.glofox.com/glofox/platform/glofox/branches/53fb0a22fe25f0c84c8b4575/logo.png"
                },
                {
                    "_id": "57061ab7778f3bed478b4568",
                    "address": {
                        "street": "2 Grand Central, 140 East 45th Street, New York, NY 10017",
                        "city": "New York",
                        "state": "New York",
                        "country": "United States",
                        "country_code": "US",
                        "district": "New York",
                        "latitude": 40.752981,
                        "longitude": -73.974033,
                        "currency": "USD",
                        "continent": "NA",
                        "timezone_id": "America/New_York"
                    },
                    "email": "<EMAIL>",
                    "facebook": "https://www.facebook.com/Weareglofox/",
                    "features": {
                        "classes": {
                            "enabled": true,
                            "weeks_display": 9
                        },
                        "booking": {
                            "enabled": true,
                            "strike_system": {
                                "enabled": true,
                                "max_strikes": 3
                            },
                            "cancel_credit_option": "model_id",
                            "refunded_credit_expiry": 90,
                            "guest_bookings": 4,
                            "booking_open_window": 168,
                            "waiting_list_enabled": true,
                            "auto_booking_enabled": false,
                            "waiting_list": 5,
                            "booking_cancel_window": 1,
                            "late_cancellation_enabled": true
                        },
                        "news": {
                            "enabled": true
                        },
                        "courses": {
                            "enabled": true,
                            "guest_bookings": 1
                        },
                        "store": {
                            "enabled": true
                        },
                        "memberships": {
                            "enabled": true,
                            "payg": {
                                "enabled": true
                            }
                        },
                        "payments": {
                            "stripe_custom": {
                                "allowed": true
                            }
                        }
                    },
                    "name": "Glofox - New York",
                    "namespace": "glofox",
                    "phone": "00123456789",
                    "twitter": "@weareglofox",
                    "website": "https://www.glofox.com/",
                    "stripe_plan_code": "platinum",
                    "type": "branch",
                    "image_url": "https://cdn.glofox.com/glofox/platform/glofox/branches/57061ab7778f3bed478b4568/logo.png"
                }
            ]
        }

+ Response 400 (application/json)

        {
            "success": false,
            "message": "Invalid Request, invalid ID",
            "message_code": "INVALID_REQUEST_INVALID_ID",
            "message_data": [],
            "errors": [
                "INVALID_REQUEST_INVALID_ID"
            ]
        }

+ Response 401 (application/json)

        {
            "success": false,
            "message": "Unauthorized - The token is empty",
            "message_code": "UNAUTHORIZED__THE_TOKEN_IS_EMPTY",
            "message_data": [],
            "errors": [
                "UNAUTHORIZED__THE_TOKEN_IS_EMPTY"
            ]
        }

### Get Branch by Id  [/branches/{branchId}]

#### Get Branch by Id [GET]

+ Response 200 (application/json)

        {
            "_id": "53fb0a22fe25f0c84c8b4575",
            "address": {
                "street": "Unit 7, Sandyford Business Centre, Sandyford, D18 AX29",
                "city": "Sandyford",
                "state": "County Dublin",
                "country": "Ireland",
                "country_code": "IE",
                "district": "Dublin",
                "latitude": 53.276322,
                "longitude": -6.207031,
                "currency": "EUR",
                "continent": "EU",
                "timezone_id": "Europe/Dublin"
            },
            "email": "<EMAIL>",
            "facebook": "https://www.facebook.com/Weareglofox/",
            "features": {
                "classes": {
                    "enabled": true,
                    "weeks_display": 9
                },
                "booking": {
                    "enabled": true,
                    "strike_system": {
                        "enabled": true,
                        "max_strikes": 3
                    },
                    "cancel_credit_option": "model_id",
                    "refunded_credit_expiry": 90,
                    "guest_bookings": 4,
                    "booking_open_window": 168,
                    "waiting_list_enabled": true,
                    "auto_booking_enabled": false,
                    "waiting_list": 5,
                    "booking_cancel_window": 1,
                    "late_cancellation_enabled": true
                },
                "news": {
                    "enabled": true
                },
                "courses": {
                    "enabled": true,
                    "guest_bookings": 1
                },
                "store": {
                    "enabled": true
                },
                "memberships": {
                    "enabled": true,
                    "payg": {
                        "enabled": true
                    }
                },
                "payments": {
                    "stripe_custom": {
                        "allowed": true
                    }
                }
            },
            "name": "Glofox - Dublin",
            "namespace": "glofox",
            "phone": "019010449",
            "twitter": "@weareglofox",
            "website": "https://www.glofox.com/",
            "stripe_plan_code": "platinum",
            "type": "branch",
            "image_url": "https://cdn.glofox.com/glofox/platform/glofox/branches/53fb0a22fe25f0c84c8b4575/logo.png"
        }

+ Response 400 (application/json)

        {
            "success": false,
            "message": "Invalid Request, invalid ID",
            "message_code": "INVALID_REQUEST_INVALID_ID",
            "message_data": [],
            "errors": [
                "INVALID_REQUEST_INVALID_ID"
            ]
        }

+ Response 401 (application/json)

        {
            "success": false,
            "message": "Unauthorized - The token is empty",
            "message_code": "UNAUTHORIZED__THE_TOKEN_IS_EMPTY",
            "message_data": [],
            "errors": [
                "UNAUTHORIZED__THE_TOKEN_IS_EMPTY"
            ]
        }


## Group Members

Members can have 4 different types of membership. This is stred in the user under membership.type
* payg
    * This is a Drop In member with no membership
* time
    * This is a membership with no limit on the number of bookings made.
    * Most commonly found in Unlimited memberships
    * This membership has a start_date and an expiry_date
* num_classes
    * This is a credits based membership, the user will have credits which you can retirve with Get Credit Packs by Member ID
    * Note Get Credit Packs by Member ID could return no credits, meaning the members credits are expired
* time_classes
    * This is a subscription membership that limits the number of classes the user can go to
    * Eg; Max 12 Classes a month

### Create Member [/register]

#### Create Member [POST]

+ Response 401
    + Headers

            Authorization: Bearer {token}
            Content-Type: application/json

+ Request (application/json)

        {
            "email" : "<EMAIL>",
            "first_name" : "Glo",
            "last_name" : "Fox",
            "password" : "123456",
            "phone" : "087123456789",
            "receive_marketing" : true
        }

+ Response 200 (application/json)

        {
        "user": {
            "_id": "5af2fb9f91898f533b34fba2",
            "membership": {
                "type" : "payg"
            },
            "first_name" : "Glo",
            "last_name" : "Fox",
            "phone" : "087123456789",
            "email" : "<EMAIL>",
            "branch_id" : "53fb0a22fe25f0c84c8b4575",
            "birth" : null,
            "gender" : {
                "name" : "M",
                "label": "Male"
            },
            "answers": [],
            "type": "member",
            "active": true,
            "emergency_contact": null,
            "receive_marketing": true,
            "login": "<EMAIL>",
            "namespace": "glofox",
            "modified": **********,
            "created": **********,
            "lead": true,
            "origin_branch_id": "53fb0a22fe25f0c84c8b4575",
            "region": "europe-1",
            "timestamp": {
                "sec": **********,
                "inc": 0
            },
            "name": "Glo Fox",
            "image_url": "https://cdn.glofox.com/production/glofox/branches/53fb0a22fe25f0c84c8b4575/users/5af2fb9f91898f533b34fba2.png?v=**********"
        }


### Get All Members [/members]

#### Get All Members by branch_id [GET]

+ Response 200 (application/json)

        {
            "object": "list",
            "page": 1,
            "limit": 50,
            "has_more": false,
            "total_count": 4,
            "data": [
                {
                    "_id": "5af2fb9f91898f533b34fba2",
                    "membership": {
                        "type" : "payg"
                    },
                    "first_name" : "Glo",
                    "last_name" : "Fox",
                    "phone" : "087123456789",
                    "email" : "<EMAIL>",
                    "branch_id" : "53fb0a22fe25f0c84c8b4575",
                    "birth" : null,
                    "gender" : {
                        "name" : "M",
                        "label": "Male"
                    },
                    "answers": [],
                    "type": "member",
                    "active": true,
                    "emergency_contact": null,
                    "receive_marketing": true,
                    "login": "<EMAIL>",
                    "namespace": "glofox",
                    "modified": **********,
                    "created": **********,
                    "lead": true,
                    "origin_branch_id": "53fb0a22fe25f0c84c8b4575",
                    "region": "europe-1",
                    "timestamp": {
                        "sec": **********,
                        "inc": 0
                    },
                    "payments" : {
                        "stripe_custom" : {
                            "id" : 24,
                            "type_id" : 2,
                            "provider_id" : 1,
                            "cards" : [
                                {
                                    "id" : 15,
                                    "brand" : "Visa",
                                    "last4" : "4242",
                                    "exp_year" : 2020,
                                    "exp_month" : 11,
                                    "provider_id" : 1
                                }
                            ]
                        }
                    },
                    "name": "Glo Fox",
                    "image_url": "https://cdn.glofox.com/production/glofox/branches/53fb0a22fe25f0c84c8b4575/users/5af2fb9f91898f533b34fba2.png?v=**********"
                },
                {
                    "_id": "5af2fb9f91898f533b34fba3",
                    "membership": {
                        "_id" : "547315e4d7b6dd09474a1a5c",
                        "type" : "time",
                        "start_date" : **********,
                        "expiry_date" : **********,
                        "plan_code" : 1525961160892
                    },
                    "first_name" : "Glo",
                    "last_name" : "Fox",
                    "phone" : "087123456789",
                    "email" : "<EMAIL>",
                    "branch_id" : "53fb0a22fe25f0c84c8b4575",
                    "birth" : null,
                    "gender" : {
                        "name" : "M",
                        "label": "Male"
                    },
                    "answers": [],
                    "type": "member",
                    "active": true,
                    "emergency_contact": null,
                    "receive_marketing": true,
                    "login": "<EMAIL>",
                    "namespace": "glofox",
                    "modified": **********,
                    "created": **********,
                    "lead": true,
                    "origin_branch_id": "53fb0a22fe25f0c84c8b4575",
                    "region": "europe-1",
                    "timestamp": {
                        "sec": **********,
                        "inc": 0
                    },
                    "name": "Glo Fox",
                    "image_url": "https://cdn.glofox.com/production/glofox/branches/53fb0a22fe25f0c84c8b4575/users/5af2fb9f91898f533b34fba2.png?v=**********"
                },
                {
                    "_id": "5af2fb9f91898f533b34fba4",
                    "membership": {
                        "_id" : "547315e4d7b6dd09474a1a6c",
                        "type" : "num_classes",
                        "start_date" : **********,
                        "plan_code" : 1525961160892
                    },
                    "first_name" : "Glo",
                    "last_name" : "Fox",
                    "phone" : "087123456789",
                    "email" : "<EMAIL>",
                    "branch_id" : "53fb0a22fe25f0c84c8b4575",
                    "birth" : null,
                    "gender" : {
                        "name" : "M",
                        "label": "Male"
                    },
                    "answers": [],
                    "type": "member",
                    "active": true,
                    "emergency_contact": null,
                    "receive_marketing": true,
                    "login": "<EMAIL>",
                    "namespace": "glofox",
                    "modified": **********,
                    "created": **********,
                    "lead": true,
                    "origin_branch_id": "53fb0a22fe25f0c84c8b4575",
                    "region": "europe-1",
                    "timestamp": {
                        "sec": **********,
                        "inc": 0
                    },
                    "name": "Glo Fox",
                    "image_url": "https://cdn.glofox.com/production/glofox/branches/53fb0a22fe25f0c84c8b4575/users/5af2fb9f91898f533b34fba2.png?v=**********"
                },
                {
                    "_id": "5af2fb9f91898f533b34fba5",
                    "membership": {
                        "_id" : "547315e4d7b6dd09474a1a6c",
                        "type" : "time_classes",
                        "start_date" : **********,
                        "expiry_date" : **********,
                        "plan_code" : 1525961160892
                    },
                    "first_name" : "Glo",
                    "last_name" : "Fox",
                    "phone" : "087123456789",
                    "email" : "<EMAIL>",
                    "branch_id" : "53fb0a22fe25f0c84c8b4575",
                    "birth" : null,
                    "gender" : {
                        "name" : "M",
                        "label": "Male"
                    },
                    "answers": [],
                    "type": "member",
                    "active": true,
                    "emergency_contact": null,
                    "receive_marketing": true,
                    "login": "<EMAIL>",
                    "namespace": "glofox",
                    "modified": **********,
                    "created": **********,
                    "lead": true,
                    "origin_branch_id": "53fb0a22fe25f0c84c8b4575",
                    "region": "europe-1",
                    "timestamp": {
                        "sec": **********,
                        "inc": 0
                    },
                    "name": "Glo Fox",
                    "image_url": "https://cdn.glofox.com/production/glofox/branches/53fb0a22fe25f0c84c8b4575/users/5af2fb9f91898f533b34fba2.png?v=**********"
                }
            ]
        }

### Get a Members [/members/:user_id]

#### Get a Members by Id [GET]

+ Response 200 (application/json)

        {
                    "_id": "5af2fb9f91898f533b34fba5",
                    "membership": {
                        "_id" : "547315e4d7b6dd09474a1a6c",
                        "type" : "time_classes",
                        "start_date" : **********,
                        "expiry_date" : **********,
                        "plan_code" : 1525961160892
                    },
                    "first_name" : "Glo",
                    "last_name" : "Fox",
                    "phone" : "087123456789",
                    "email" : "<EMAIL>",
                    "branch_id" : "53fb0a22fe25f0c84c8b4575",
                    "birth" : null,
                    "gender" : {
                        "name" : "M",
                        "label": "Male"
                    },
                    "answers": [],
                    "type": "member",
                    "active": true,
                    "emergency_contact": null,
                    "receive_marketing": true,
                    "login": "<EMAIL>",
                    "namespace": "glofox",
                    "modified": **********,
                    "created": **********,
                    "lead": true,
                    "origin_branch_id": "53fb0a22fe25f0c84c8b4575",
                    "region": "europe-1",
                    "timestamp": {
                        "sec": **********,
                        "inc": 0
                    },
                    "name": "Glo Fox",
                    "image_url": "https://cdn.glofox.com/production/glofox/branches/53fb0a22fe25f0c84c8b4575/users/5af2fb9f91898f533b34fba2.png?v=**********"
                }
        }


### Search Members [/2.1/branches/{branchId}/users?page={page}&filters[email]={email}}]

#### Search Members by e-mail [GET]

+ Parameters
    + branchId - Branch Id
    + page - Page number
    + email - Match any user by email (strict match)

+ Response 200 (application/json)

        {
            "success": true,
            "data": [
                {
                    "_id": "59a3011a05c677bda916611c",
                    "branch_id": "49a7011a05c677b9a916612a",
                    "first_name": "Conor",
                    "last_name": "O'Loughlin",
                    "email": "<EMAIL>",
                    "phone": "083-8599851",
                    "source": "dashboard",
                    "lead_status": "WARM"
                }
            ],
            "meta": {
                "pagination": {
                    "count": 1,
                    "perPage": 50,
                    "currentPage": 1,
                    "links": {
                        "previous": null,
                        "next": null
                    }
                }
            }
        }

## Group Credits

### Get Credit Packs [/user_credits]

#### Get Credit Packs by Member ID [GET]


+ Response 200 (application/json)

        {
            "object": "list",
            "page": 1,
            "limit": 50,
            "has_more": false,
            "total_count": 1,
            "data": [
            {
                "_id" : "551d3880d7b6dd6c7c8b45c5",
                "namespace" : "glofox",
                "branch_id" : "53fb0a22fe25f0c84c8b4575",
                "num_sessions" : 10,
                "active" : true,
                "start_date" : **********,
                "end_date": **********,
                "bookings" : [
                    "5af3121f990d384873009092"
                ],
                "model" : "programs",
                "user_id" : "5af2fb9f91898f533b34fba5",
                "membership_id" : "545cb56bd7b6dd51218b4567",
                "modified": **********,
                "created": **********,
            },
            {
                "_id" : "551d3880d7b6dd6c7c8b45c3",
                "namespace" : "glofox",
                "branch_id" : "53fb0a22fe25f0c84c8b4575",
                "num_sessions" : 1,
                "active" : true,
                "start_date" : **********,
                "end_date": **********,
                "bookings" : [

                ],
                "model" : "programs",
                "user_id" : "5af2fb9f91898f533b34fba5",
                "modified": 1525873967,
                "created": 1525873967,
            }
            ]
        }




## Group Membership

### Get Memberships [/memberships]

#### Get Memberships by branch_id [GET]


+ Response 200 (application/json)

        {
            "object": "list",
            "page": 1,
            "limit": 50,
            "has_more": false,
            "total_count": 1,
            "data": [
            {
                "_id" : "542728fbd7b6dd8d475c9b2b",
                "branch_id" : "53fb0a22fe25f0c84c8b4575",
                "modified": **********,
                "created": **********,
                "description" : "This is a membership description. The recommended character limit is 160, though clients can input more characters if needed",
                "name" : "Membership - Non Subscription",
                "namespace" : "glofox",
                "plans" : [
                    {
                        "type" : "time",
                        "code" : "5451119eed0dc",
                        "name" : "One Month",
                        "upfront_fee" : 100.0,
                        "duration_time_unit" : "month",
                        "duration_time_unit_count" : 1,
                        "branch_id" : "53fb0a22fe25f0c84c8b4575",
                        "namespace" : "glofox"
                    },
                    {
                        "type" : "time_classes",
                        "name" : "Expiry + Credits",
                        "upfront_fee" : 110.0,
                        "credits" : [
                            {
                                "model" : "users",
                                "num_sessions" : 5
                            }
                        ],
                        "branch_id" : "53fb0a22fe25f0c84c8b4575",
                        "namespace" : "glofox",
                        "code" : "55f1b0fc2ec42"
                    },
                    {
                        "type" : "num_classes",
                        "name" : "10 Class Pack",
                        "upfront_fee" : 110.0,
                        "credits" : [
                            {
                                "model" : "programs",
                                "num_sessions" : 10
                            }
                        ],
                        "branch_id" : "53fb0a22fe25f0c84c8b4575",
                        "namespace" : "glofox",
                        "code" : "55f1b0fc2ec42"
                    }
                ]
            },
            {
                "_id" : "542728fbd7b6dd8d475c9b2b",
                "branch_id" : "53fb0a22fe25f0c84c8b4575",
                "modified": **********,
                "created": **********,
                "description" : "This is a membership description. The recommended character limit is 160, though clients can input more characters if needed",
                "name" : "Subscription Membership",
                "namespace" : "glofox",
                "plans" : [
                    {
                        "type" : "time",
                        "code" : "5451119eed0dc",
                        "name" : "Monthly",
                        "upfront_fee" : 100.0,
                        "subscription_amount" : 150,
                        "duration_time_unit" : "month",
                        "duration_time_unit_count" : 1,
                        "branch_id" : "53fb0a22fe25f0c84c8b4575",
                        "namespace" : "glofox"
                    },
                    {
                        "type" : "time_classes",
                        "code" : "5451119eed0cd",
                        "name" : "12 Credits a Month Rolling Membership",
                        "credits" : [
                            {
                                "model" : "programs",
                                "num_sessions" : 5
                            }
                        ],
                        "upfront_fee" : 0.0,
                        "subscription_amount" : 80,
                        "duration_time_unit" : "month",
                        "duration_time_unit_count" : 1,
                        "branch_id" : "53fb0a22fe25f0c84c8b4575",
                        "namespace" : "glofox"
                    }
                ]
            }


            ]
        }


## Group Classes

Classes can have 6 different statuses.
* BOOKING_WINDOW_PASSED
    * The class can no longer be booked. This is usually for classes that are in the past.
    * The studio can close bookings a certain number of hours before the class starts
* BOOKING_WINDOW_NOT_OPEN
    * The studio can set that all classes are bookable within a certain number of hours
    * Eg; if set to 24 hours a class at 6pm can only be booked after 6pm the day before
* JOIN_WAITING_LIST
    * Class is full and there are spaces left on the waiting list
* FULLY_BOOKED
    * Class is full and there are no spaces left on the waiting list
* AVAILABLE
    * There is space in the class to book
* ALREADY_BOOKED
    * The user is already booked into this class


### Get Classes [/events?start=1525215600&end=1529535599&sort_by=time_start]

#### Get Classes [GET]


+ Response 200 (application/json)

        {
            "object": "list",
            "page": 1,
            "limit": 50,
            "has_more": false,
            "total_count": 2,
            "data": [
                {
                    "_id": "5a937ea054e4e008425923df",
                    "namespace": "glofox",
                    "branch_id": "53fb0a22fe25f0c84c8b4575",
                    "program_id": "59808159d8cfeab032000000",
                    "active": true,
                    "name": "Small Group Class",
                    "description": "Small Group Class",
                    "time_start": 1525672800,
                    "level": "All Levels",
                    "size": 14,
                    "booked":1,
                    "waiting": 0,
                    "facility": "58e4eb2d1da368c200000008",
                    "trainers": [
                        "58e4cf398b8c354a0c000007"
                    ],
                    "private": false,
                    "has_booked": false,
                    "booking_status": null,
                    "duration": 60,
                    "close_booking_time": 1525672800,
                    "status" : "BOOKING_WINDOW_PASSED",
                    "type": "event",
                    "program_obj": {
                        "_id": "59808159d8cfeab032000000",
                        "branch_id": "53fb0a22fe25f0c84c8b4575",
                        "description": "Small Group Class",
                        "modified": 1517662231,
                        "name": "Small Group Class",
                        "pricing": [
                            {
                                "price": 0,
                                "type": "member",
                            "name": "PAYG"
                            }
                        ],
                        "type": "program",
                        "image_url": "https://cdn.glofox.com/production/glofox/branches/58e4cf398b8c354a0c000003/programs/59808159d8cfeab032000000/default.png?v=1517662231"
                    },
                    "facility_obj": {
                        "_id": "58e4eb2d1da368c200000008",
                        "branch_id": "53fb0a22fe25f0c84c8b4575",
                        "description": "Our boxing gym is a state of the art boxing facility. We offer 4 rings as well as multiple training bags and other equipment. Classes are routinely run by our esteemed boxing coach, Conor McGregor",
                        "modified": 1525367591,
                        "name": "Studio 1",
                        "type": "facility",
                        "image_url": "https://cdn.glofox.com/production/glofox/branches/58e4cf398b8c354a0c000003/facilities/58e4eb2d1da368c200000008/default.png?v=1525367591"
                    },
                    "trainers_obj": [
                        {
                            "_id": "58e4cf398b8c354a0c000007",
                            "branch_id": "53fb0a22fe25f0c84c8b4575",
                            "first_name": "Timmy",
                            "last_name": "Fisher",
                            "name": "Timmy Fisher",
                            "type": "user",
                            "image_url": "https://s3-eu-west-1.amazonaws.com/production/glofox/branches/58e4cf398b8c354a0c000003/trainers/58e4cf398b8c354a0c000007/default.png"
                        }
                    ],
                    "image_url": "https://cdn.glofox.com/platform/glofox/branches/58e4cf398b8c354a0c000003/programs/59808159d8cfeab032000000/default.png?v=1517662231"
                },
                {
                    "_id": "5a937ea054e4e008425923df",
                    "namespace": "glofox",
                    "branch_id": "53fb0a22fe25f0c84c8b4575",
                    "program_id": "59808159d8cfeab032000000",
                    "active": true,
                    "name": "Small Group Class",
                    "description": "Small Group Class",
                    "time_start": 1525676400,
                    "level": "All Levels",
                    "size": 14,
                    "booked":14,
                    "waiting": 5,
                    "facility": "58e4eb2d1da368c200000008",
                    "trainers": [
                        "58e4cf398b8c354a0c000007"
                    ],
                    "private": false,
                    "has_booked": false,
                    "booking_status": null,
                    "duration": 60,
                    "status" : "FULLY_BOOKED",
                    "close_booking_time": 1525676400,
                    "type": "event",
                    "program_obj": {
                        "_id": "59808159d8cfeab032000000",
                        "branch_id": "53fb0a22fe25f0c84c8b4575",
                        "description": "Small Group Class",
                        "modified": 1517662231,
                        "name": "Small Group Class",
                        "pricing": [
                            {
                                "price": 0,
                                "type": "member",
                            "name": "PAYG"
                            }
                        ],
                        "type": "program",
                        "image_url": "https://cdn.glofox.com/production/glofox/branches/58e4cf398b8c354a0c000003/programs/59808159d8cfeab032000000/default.png?v=1517662231"
                    },
                    "facility_obj": {
                        "_id": "58e4eb2d1da368c200000008",
                        "branch_id": "53fb0a22fe25f0c84c8b4575",
                        "description": "Our boxing gym is a state of the art boxing facility. We offer 4 rings as well as multiple training bags and other equipment. Classes are routinely run by our esteemed boxing coach, Conor McGregor",
                        "modified": 1525367591,
                        "name": "Studio 1",
                        "type": "facility",
                        "image_url": "https://cdn.glofox.com/production/glofox/branches/58e4cf398b8c354a0c000003/facilities/58e4eb2d1da368c200000008/default.png?v=1525367591"
                    },
                    "trainers_obj": [
                        {
                            "_id": "58e4cf398b8c354a0c000007",
                            "branch_id": "53fb0a22fe25f0c84c8b4575",
                            "first_name": "Timmy",
                            "last_name": "Fisher",
                            "name": "Timmy Fisher",
                            "type": "user",
                            "image_url": "https://s3-eu-west-1.amazonaws.com/production/glofox/branches/58e4cf398b8c354a0c000003/trainers/58e4cf398b8c354a0c000007/default.png"
                        }
                    ],
                    "image_url": "https://cdn.glofox.com/platform/glofox/branches/58e4cf398b8c354a0c000003/programs/59808159d8cfeab032000000/default.png?v=1517662231"
                }
            ]
        }



## Group Bookings

### Create Booking [/bookings]

#### Create Booking [POST]


+ Response 401
    + Headers

            Authorization: Bearer {token}
            Content-Type: application/json

+ Request (application/json)

        {
            "guest_bookings" : 0,
            "model" : "event",
            "model_id" : "5a937ea054e4e008425923df",
            "pay_gym" : false
        }

+ Response 200 (application/json)

        {
            "success": true,
            "Booking": {
                "_id": "5af3121f990d384873009092"
                "namespace": "glofox",
                "branch_id": "53fb0a22fe25f0c84c8b4575",
                "user_id": "5af2fb9f91898f533b34fba2",
                "user_name": "Glo Fox",
                "program_id": "59808159d8cfeab032000000",
                "event_id": "5aa7c480a39f7000d60acb97",
                "event_name": "Small Group Class",
                "model_name": "Small Group Class",
                "status": "BOOKED",
                "type": "events",
                "time_start": {
                    "sec": 1526112000,
                    "usec": 0
                },
                "time_finish": {
                    "sec": 1526115600,
                    "usec": 0
                },
                "guest_bookings": 0,
                "timezone": "Europe/Dublin",
                "modified": **********,
                "created": **********,
            }
        }
