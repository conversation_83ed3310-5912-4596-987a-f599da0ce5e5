<?php

/**
 * Class ImportsController.
 */
class ImportsController extends \Glofox\Domain\Imports\Http\ImportsController
{
    public $name = 'Imports';
    public $uses = ['Branch', 'User', 'UserCredit'];
    protected $restActions = ['uploadFile', 'getImportedFile', 'importFromMindBody'];

    public function uploadFile()
    {
        // Insecure Method
        throw new Exception('blocked');
        $file = $_FILES['File'];

        $content = file_get_contents($file['tmp_name']);
        if (
            !mkdir($concurrentDirectory = (ROOT . '/vendor/import_folder/' . $folder), 0777, true) && !is_dir(
                $concurrentDirectory
            )
        ) {
            throw new \RuntimeException(sprintf('Directory "%s" was not created', $concurrentDirectory));
        }

        file_put_contents((ROOT . '/vendor/import_folder/' . $folder . '/' . $file['name']), $content);
    }

    /**
     * @param null $folder
     * @param null $file
     * @param null $file_type
     *
     * @return string
     *
     * @throws Exception
     */
    public function getImportedFile($folder = null, $file = null, $file_type = null)
    {
        // Insecure Method
        throw new Exception('blocked');
        $result = null;

        $dir = ROOT . '/vendor/import_folder/' . $folder . '/';
        $imported_file = new File($dir . $file);
        $elements = preg_split('&\r\n|\r|\n&', $imported_file->read());

        if ('MEMBERS' == $file_type) {
            foreach ($elements as $member) {
                $result_tmp = [];
                $member_exploded = explode(',', $member);
                $result_tmp['first_name'] = $member_exploded['1'];
                $result_tmp['last_name'] = $member_exploded['0'];
                $result_tmp['mindbody_id'] = $member_exploded['3'];
                $result_tmp['email'] = strtolower($member_exploded['4']);
                $result_tmp['phone'] = !empty($member_exploded['5']) ? $member_exploded['5'] : $result_tmp['email'];

                if (!empty($result_tmp['email'])) {
                    $result[] = $result_tmp;
                }
            }
        } elseif ('MEMBERSHIPS' == $file_type) {
            foreach ($elements as $membership) {
                $result_tmp = [];
                $member_exploded = explode(',', $membership);
                $result_tmp['mindbody_id'] = $member_exploded['0'];
                $result_tmp['name'] = $member_exploded['4'];
                $result_tmp['expiry_date'] = $member_exploded['6'];
                $result_tmp['credits'] = $member_exploded['8'];

                if (!empty($result_tmp['mindbody_id']) || !empty($result_tmp['email'])) {
                    $result[] = $result_tmp;
                }
            }
        }

        return json_encode($result, JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    /**
     * @return string
     */
    public function importFromMindBody()
    {
        $result = null;

        try {
            $data = file_get_contents('php://input');
            $data = json_decode($data, true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
            $data ??= $this->request->data;

            $elements = json_decode(
                $this->getImportedFile($data['folder'], $data['file'], $data['file_type']),
                null,
                512,
                JSON_PARTIAL_OUTPUT_ON_ERROR
            );

            if ('MEMBERS' == $data['file_type']) {
                $result = $this->importMindBodyMembers($elements, $data['branch_id']);
            } elseif ('MEMBERSHIPS' == $data['file_type']) {
                $result = $this->importMindBodyMemberships($elements, $data['branch_id'], $data['memberships']);
            }
        } catch (Exception $e) {
            $result = $e->getMessage();
        }

        return json_encode($result, JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    /**
     * @param $elements
     * @param $branch_id
     *
     * @return array|null
     */
    private function importMindBodyMembers($elements, $branch_id)
    {
        $result = null;

        $branch = $this->Branch->findById($branch_id);
        $success = true;

        foreach ($elements as $element) {
            $element = (array)$element;

            $this->User->create();

            $member['active'] = true;
            $member['type'] = 'MEMBER';
            $member['namespace'] = $branch['Branch']['namespace'];
            $member['branch_id'] = $branch['Branch']['_id'];
            $member['first_name'] = $element['first_name'];
            $member['last_name'] = $element['last_name'];
            $member['answers'] = [$element['mindbody_id']];
            $member['email'] = $element['email'];
            $member['login'] = $element['email'];
            $member['phone'] = $element['phone'];
            $member['membership']['type'] = 'payg';

            if (false == $this->User->save($member)) {
                $success = false;
            }
        }

        $result = [
            'success' => $success,
            'message' => $success ?
                'Members have been imported.' :
                'Some Members could not been imported.',
        ];

        return $result;
    }

    /**
     * @param $elements
     * @param $branch_id
     * @param $memberships
     *
     * @return array|null
     */
    private function importMindBodyMemberships($elements, $branch_id, $memberships)
    {
        $result = null;
        $success = true;

        foreach ($elements as $element) {
            $element = (array)$element;

            $user = $this->User->find(
                'first',
                [
                    'conditions' => [
                        'branch_id' => $branch_id,
                        'type' => 'MEMBER',
                        'active' => true,
                        'answers' => $element['mindbody_id'],
                    ],
                ]
            );

            if (null != $user && !empty($user)) {
                $expiry_date = explode('/', $element['expiry_date']);
                $user['User']['membership']['expiry_date'] = date(
                    $expiry_date[2] . '-' . $expiry_date[0] . '-' . $expiry_date[1]
                );
                $user['User']['last_name'] = empty($user['User']['last_name']) ? '.' : $user['User']['last_name'];

                foreach ($memberships as $membership) {
                    foreach ($membership['droppedObjects'] as $mindbody_membership) {
                        if ($element['mindbody_id'] == $mindbody_membership['mindbody_id']) {
                            $user['User']['membership']['_id'] = $membership['_id'];

                            if ($mindbody_membership['credits'] > 0 && $mindbody_membership['credits'] <= 365) {
                                $user['User']['membership']['type'] = 'num_classes';

                                $this->UserCredit->create();

                                $new_credit['namespace'] = $user['User']['namespace'];
                                $new_credit['branch_id'] = $user['User']['branch_id'];
                                $new_credit['user_id'] = $user['User']['_id'];
                                $new_credit['model'] = 'programs';
                                $new_credit['num_sessions'] = $mindbody_membership['credits'];
                                $new_credit['active'] = true;
                                $new_credit['bookings'] = [];
                                $new_credit['start_date'] = date('Y-m-d');
                                $new_credit['end_date'] = $user['User']['membership']['expiry_date'];
                                $new_credit['membership_id'] = $user['User']['membership']['_id'];

                                $this->UserCredit->save($new_credit);
                            } else {
                                $user['User']['membership']['type'] = 'time';
                            }

                            if (false == $this->User->save($user)) {
                                $success = false;
                            }
                        }
                    }
                }
            }
        }

        $result = [
            'success' => $success,
            'message' => $success ?
                'Memberships have been imported.' :
                'Some Memberships could not been imported.',
        ];

        return $result;
    }
}
