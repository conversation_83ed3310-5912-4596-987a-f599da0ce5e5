<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Payments\Providers\Gateway\WebHooks\Handlers;

use Glofox\Domain\Branches\Models\Branch;
use Glofox\Domain\Branches\Repositories\BranchesRepository;
use Glofox\Domain\Transactional\Sender;
use Glofox\Domain\Users\Models\User;
use Glofox\Domain\Users\Repositories\UsersRepository;
use Glofox\Payments\Contracts\PaymentProviderContract;
use Glofox\Payments\Providers\Gateway\WebHooks\Handlers\ExternalAccountUpdatedHandler;

\App::import('Test/Case', 'GlofoxTestCase');

class ExternalAccountUpdatedHandlerTest extends \GlofoxTestCase
{
    public function tearDown()
    {
        parent::tearDown();
        \Mockery::close();
    }

    public function test_it_handles_external_account_updated_webhook_and_sends_email_to_admin(): void
    {
        $event = collect([
            'id' => 'evt_1Pd9UTPXXnikQnMfYlPfmTsr',
            'branch_id' => 'branch_1234',
            'psp_response' => collect([
                'data' => [
                    'object' => [
                        'id' => 'ba_1PY7JePXXnikQnMfbbfHwXWy',
                        'account_holder_name' => 'ABC Fitness',
                        'account' => 'acc_12434',
                        'bank_name' => 'Test Bank',
                        'bank_token' => 'tok_12345',
                        'last4' => '4897',
                        'routing_number' => '*********',
                    ]
                ],
            ]),
        ]);

        $adminUsers = [
            User::make(['_id' => 'user_123','type' => 'SUPERADMIN','email' => '<EMAIL>']),
        ];

        $branchesRepositoryMocked = \Mockery::mock(BranchesRepository::class)
            ->shouldReceive('findById')
            ->andReturn(Branch::make(["_id"=>'branch_123','name'=>'ABC Fitness','namespace'=>'branch123','email'=>'<EMAIL>'])->toLegacy())
            ->getMock();
        app()->instance(BranchesRepository::class, $branchesRepositoryMocked);

        $usersRepositoryMocked = \Mockery::mock(UsersRepository::class)
            ->shouldReceive('getActiveAdminsAndSuperAdminsByBranchId')
            ->andReturn($adminUsers)
            ->getMock();
        app()->instance(UsersRepository::class, $usersRepositoryMocked);

        $senderMocked = \Mockery::mock(Sender::class);
        $senderMocked
            ->shouldReceive('addAttachments')
            ->getMock()
            ->shouldReceive('setSenderNameByBranch')
            ->getMock()
            ->shouldReceive('send')
            ->andReturnTrue();
        app()->instance(Sender::class, $senderMocked);

        /** @var PaymentProviderContract $paymentHandler */
        $paymentHandler = \Mockery::mock(PaymentProviderContract::class);

        $handler = new ExternalAccountUpdatedHandler(
            $event,
            $paymentHandler
        );

        $statusCode = $handler->handle();

        assert(200, $statusCode);
    }
}
