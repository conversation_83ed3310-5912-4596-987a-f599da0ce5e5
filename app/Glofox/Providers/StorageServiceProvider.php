<?php

declare(strict_types=1);

namespace Glofox\Providers;

use Aws\S3\S3Client;
use Carbon\Laravel\ServiceProvider;
use Glofox\Application;
use GlofoxEnvironment;
use Glofox\Providers\Contracts\Provider;
use Glofox\Storage\AwsCloudStorage;
use Glofox\Storage\CloudStorageInterface;
use Glofox\Storage\StreamableCloudStorageInterface;
use Intervention\Image\ImageManager;
use League\Flysystem\AwsS3v3\AwsS3Adapter;
use League\Flysystem\Filesystem;
use League\Flysystem\FilesystemInterface;

class StorageServiceProvider extends ServiceProvider implements Provider
{
    public function register(): void
    {
        $this->registerImageManager();
        $this->registerCloudStorage();
    }

    private function registerImageManager(): void
    {
        $this->app->singleton(ImageManager::class, fn() => new ImageManager(['driver' => 'gd']));
    }

    private function registerCloudStorage(): void
    {
        $config = $this->getCloudConfig();

        $this->app->bind(FilesystemInterface::class, fn() => new Filesystem(
            new AwsS3Adapter(
                $client = $this->createS3ClientInstance($config),
                $bucket = $config['bucket_name'],
                $urlPrefix = GlofoxEnvironment::currentEnvironment()
            )
        ));

        $this->app->bind(CloudStorageInterface::class, fn(Application $app) => $app->make(AwsCloudStorage::class));

        $this->app->bind(StreamableCloudStorageInterface::class, fn(Application $app) => $app->make(AwsCloudStorage::class));
    }

    private function getCloudConfig(): array
    {
        return app()
            ->make('config')
            ->get('storage.cloud.s3');
    }

    private function createS3ClientInstance(array $config): S3Client
    {
        return new S3Client([
            'credentials' => [
                'key' => $config['access_key'],
                'secret' => $config['secret_key'],
            ],
            'signature_version' => 'v4',
            'region' => $config['region'],
            'version' => '2006-03-01',
        ]);
    }
}
