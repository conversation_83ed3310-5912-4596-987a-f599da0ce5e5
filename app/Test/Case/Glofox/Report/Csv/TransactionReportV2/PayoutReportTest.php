<?php

namespace Glofox\Report\Csv\TransactionReportV2;


use Glofox\Domain\Branches\Models\Branch;

\App::import('Test/Case', 'GlofoxTestCase');

class PayoutReportTest extends \GlofoxTestCase
{
    public function test_it_set_default_value_if_payout_not_set()
    {
        $branch = new Branch();
        $transaction = [
            'currency' => 'EUR',
            'amount' => '50',
            'paid' => 'PAID',
            'metadata' => [
                'payment_method' => 'CARD'
            ]
        ];

        $payoutReport = new PayoutReport($transaction, new TransactionReportConfig(), $branch, false);
        $payoutReportDetail = $payoutReport->toArray(false,[], [], false, [], []);

        $this->assertEquals(50.00, $payoutReportDetail[0]);
        $this->assertEquals(0.00, $payoutReportDetail[1]);
        $this->assertEquals('', $payoutReportDetail[2]);
        $this->assertEquals('', $payoutReportDetail[3]);
        $this->assertEquals('PAID', $payoutReportDetail[4]);
        $this->assertEquals('Pending', $payoutReportDetail[5]);
        $this->assertEquals('', $payoutReportDetail[6]);
        $this->assertEquals('', $payoutReportDetail[7]);
    }

    public function test_it_generate_payout_detail()
    {
        $branch = new Branch();
        $transaction = $this->getTransaction();

        $payoutReport = new PayoutReport($transaction, new TransactionReportConfig(), $branch, false);
        $payoutReportDetail = $payoutReport->toArray(false, [], [], false, [], []);

        $this->assertEquals(100.00, $payoutReportDetail[0]);
        $this->assertEquals(0, $payoutReportDetail[1]);
        $this->assertEquals('10.50', $payoutReportDetail[2]);
        $this->assertEquals('90.50', $payoutReportDetail[3]);
        $this->assertEquals('PAID', $payoutReportDetail[4]);
        $this->assertEquals('12/12/2020', $payoutReportDetail[5]);
        $this->assertEquals('PO_id', $payoutReportDetail[6]);
        $this->assertEquals('t_id', $payoutReportDetail[7]);
    }

    public function test_it_generate_amounts_formatted_correctly()
    {
        $branch = new Branch();
        $transaction = [
            'currency' => 'JPY',
            'amount' => '10000',
            'paid' => 'PAID',
            'metadata' => [
                'payment_method' => 'CARD'
            ],
            'payout' => [
                'gross_amount' => '10000',
                'fee' => '1050',
                'net_amount' => '9050',
                'date' => '2020/12/12 23:45',
                'id' => 'PO_id',
                'transaction_id' => 't_id'
            ]
        ];

        $payoutReport = new PayoutReport($transaction, new TransactionReportConfig(), $branch, false);
        $payoutReportDetail = $payoutReport->toArray(false, [], [], false, [], []);

        $this->assertEquals(10000, $payoutReportDetail[0]);
        $this->assertEquals(0, $payoutReportDetail[1]);
        $this->assertEquals('1050', $payoutReportDetail[2]);
        $this->assertEquals('9050', $payoutReportDetail[3]);
        $this->assertEquals('PAID', $payoutReportDetail[4]);
        $this->assertEquals('12/12/2020', $payoutReportDetail[5]);
        $this->assertEquals('PO_id', $payoutReportDetail[6]);
        $this->assertEquals('t_id', $payoutReportDetail[7]);
    }

    public function test_it_should_use_amount_if_gross_amount_is_not_set()
    {
        $branch = new Branch();
        $transaction = $this->getTransaction();
        unset($transaction['payout']['gross_amount']);

        $payoutReport = new PayoutReport($transaction, new TransactionReportConfig(), $branch, true, false);
        $payoutReportDetail = $payoutReport->toArray(false, [], [], false, [], []);
        $this->assertEquals(50.00, $payoutReportDetail[0]);

        $payoutReport = new PayoutReport($transaction, new TransactionReportConfig(), $branch, false, false);
        $payoutReportDetail = $payoutReport->toArray(false, [], [], false, [], []);
        $this->assertEquals(50.00, $payoutReportDetail[0]);
    }

    public function test_it_generate_pending_payout_detail()
    {
        $branch = new Branch();
        $transaction = $this->getTransaction();
        unset($transaction['payout']['date']);

        $payoutReport = new PayoutReport($transaction, new TransactionReportConfig(), $branch, false);
        $payoutReportDetail = $payoutReport->toArray(false, [], [], false, [], []);

        $this->assertEquals(100.00, $payoutReportDetail[0]);
        $this->assertEquals(0, $payoutReportDetail[1]);
        $this->assertEquals('', $payoutReportDetail[2]);
        $this->assertEquals('', $payoutReportDetail[3]);
        $this->assertEquals('PAID', $payoutReportDetail[4]);
        $this->assertEquals('Pending', $payoutReportDetail[5]);
        $this->assertEquals('', $payoutReportDetail[6]);
        $this->assertEquals('', $payoutReportDetail[7]);
    }
    
    public function test_it_generate_failed_payout_detail()
    {
        $branch = new Branch();
        $transaction = $this->getTransaction();
        $transaction['failed_amount'] = 50;
        unset($transaction['paid']);

        $payoutReport = new PayoutReport($transaction, new TransactionReportConfig(), $branch, false);
        $payoutReportDetail = $payoutReport->toArray(false, [], [], false, [], []);

        $this->assertEquals(0, $payoutReportDetail[0]);
        $this->assertEquals(50.00, $payoutReportDetail[1]);
        $this->assertEquals('-', $payoutReportDetail[2]);
        $this->assertEquals('-', $payoutReportDetail[3]);
        $this->assertEquals('FAILED', $payoutReportDetail[4]);
        $this->assertEquals("12/12/2020", $payoutReportDetail[5]);
        $this->assertEquals('PO_id', $payoutReportDetail[6]);
        $this->assertEquals('t_id', $payoutReportDetail[7]);
    }

    public function test_it_set_account_balance_if_wallet_is_enabled()
    {
        $branch = new Branch();
        $transaction = $this->getTransaction();
        $transaction['failed_amount'] = 50;
        $transaction['metadata']['balance'] = 30;

        $payoutReport = new PayoutReport($transaction, new TransactionReportConfig(), $branch, false);
        $payoutReportDetail = $payoutReport->toArray(false, [], [], false, [], []);
        $this->assertEquals(8, sizeof($payoutReportDetail));

        $payoutReport = new PayoutReport($transaction, new TransactionReportConfig(), $branch, true);
        $payoutReportDetail = $payoutReport->toArray(false, [], [], false, [], []);

        $this->assertEquals(9, sizeof($payoutReportDetail));
        $this->assertEquals(30, $payoutReportDetail[4]);
    }

    /**
     * @return array
     */
    private function getTransaction(): array
    {
        $transaction = [
            'currency' => 'EUR',
            'amount' => '50',
            'paid' => 'PAID',
            'metadata' => [
                'payment_method' => 'CARD',
                'wallet_balance_after' => 50,
            ],
            'payout' => [
                'gross_amount' => '100',
                'fee' => '10.5',
                'net_amount' => '90.5',
                'date' => '2020/12/12 23:45',
                'id' => 'PO_id',
                'transaction_id' => 't_id'
            ]
        ];
        return $transaction;
    }
}
