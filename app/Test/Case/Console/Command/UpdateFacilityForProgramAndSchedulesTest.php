<?php

declare(strict_types=1);

namespace CakeTestCases\Console\Command;

use App;
use CakeTestCases\Glofox\Domain\Logger\Traits\MockedLoggerTrait;
use CakeTestCases\Glofox\Domain\Users\Traits\AuthenticateUsersTrait;
use Carbon\Carbon;
use Glofox\Domain\Events\Repositories\EventsRepository;
use Glofox\Domain\Events\Services\EventsPublisher;
use Glofox\Domain\EventTrackers\Services\TrackEventPublisher;
use Glofox\Domain\Programs\Repositories\ProgramsRepository;
use Glofox\Domain\Programs\Services\ProgramsPublisher;
use Glofox\Eventkit\Publisher\DomainEventPublisher;
use Glofox\Repositories\Search\Expressions\Shared\BranchId;
use Glofox\Repositories\Search\Filters\Shared\IsActive;
use GlofoxTestCase;
use Mockery;
use UpdateFacilityForProgramAndSchedulesShell;

App::import('Test/Case', 'GlofoxTestCase');
App::import('Console/Command', 'UpdateFacilityForProgramAndSchedulesShell');

class UpdateFacilityForProgramAndSchedulesTest extends GlofoxTestCase
{
    use MockedLoggerTrait;
    use AuthenticateUsersTrait;

    public $fixtures = [
        'app.booking',
        'app.program',
        'app.facility',
        'app.event',
        'app.branch',
        'app.user'
    ];

    public function setUp(): void
    {
        parent::setUp();
        $this->mockLogger();
    }

    public function tearDown(): void
    {
        parent::tearDown();

        Mockery::close();
    }

    public function test_it_should_update_facility_for_programs_and_schedules(): void
    {
        $eventPublisherMock = Mockery::mock(EventsPublisher::class);
        $eventPublisherMock->shouldReceive('sendEventCreatedEvent');
        $eventPublisherMock->shouldReceive('sendEventUpdatedEvent')->zeroOrMoreTimes();
        app()->instance(EventsPublisher::class, $eventPublisherMock);

        $domainEventPublisher = Mockery::mock(DomainEventPublisher::class);
        $domainEventPublisher->shouldReceive('publish');
        app()->instance(DomainEventPublisher::class, $domainEventPublisher);

        $programsPublisherMock = Mockery::mock(ProgramsPublisher::class);
        $programsPublisherMock->shouldReceive('sendProgramUpdatedEvent')
            ->zeroOrMoreTimes();
        app()->instance(ProgramsPublisher::class, $programsPublisherMock);

        $trackEventPublisher = Mockery::mock(TrackEventPublisher::class);
        $trackEventPublisher
            ->shouldReceive('sendEventToTrack')
            ->andReturn()
            ->zeroOrMoreTimes();
        app()->instance(TrackEventPublisher::class, $trackEventPublisher);

        $this->authenticateAsAdmin();
        
        Carbon::setTestNow('2024-01-01 08:00:00');

        $command = new UpdateFacilityForProgramAndSchedulesShell();

        $command->params = [
            'branchId' => '675acdcdf1aef12a46e66bd2',
            'facilityId' => '674262905c91aea9b550c000',
            'onlyActivePrograms' => false,
            'programIds' => [],
        ];

        $command->main();

        $programsRepository = app()->make(ProgramsRepository::class);
        $programs = $programsRepository->addCriteria(new IsActive())
            ->addCriteria(new BranchId($command->params['branchId']))
            ->find();

        $program = current($programs);

        $this->assertSame($command->params['facilityId'], $program['schedule_default']['facility']);

        foreach ($program['schedule'] as $schedule) {
            $this->assertSame($command->params['facilityId'], $schedule['facility']);
        }

        $eventRepository = app()->make(EventsRepository::class);
        $events = $eventRepository->getEventsByProgramIdAndTimeStart(
            $program['_id'],
            Carbon::parse('2024-01-01 08:00:00')
        );

        foreach ($events as $event) {
            $this->assertSame($command->params['facilityId'], $event['facility']);
        }
    }
}
