<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\Appointments\Http\Requests;

use CakeTestCases\Glofox\Domain\Users\Traits\AuthenticateUsersTrait;
use CakeTestCases\GlofoxRequestTestCase;
use CakeTestCases\GlofoxRequestTestParams;
use Glofox\Domain\Appointments\Http\Requests\CalculateAppointmentPriceRequest;

class CalculateAppointmentPriceRequestTest extends GlofoxRequestTestCase
{
    use AuthenticateUsersTrait;

    public array $fixtures = [
        'app.branch',
        'app.user',
    ];

    private const BRANCH_ID = '49a7011a05c677b9a916612a';
    private const APPOINTMENT_ID = '49a7011a05c677b9a916612a';

    public function requestClassName(): string
    {
        return CalculateAppointmentPriceRequest::class;
    }

    /**
     * @dataProvider testRulesDataProvider
     */
    public function testRules(array $payload, array $expectedErrors): void
    {
        $this->authenticateAsAdmin();

        $this->validateRules(
            new GlofoxRequestTestParams(
                sprintf('/2.1/branches/%s/appointments/%s/calculate-price', self::BRANCH_ID, self::APPOINTMENT_ID),
                'POST',
                $payload
            ),
            $expectedErrors
        );
    }

    public function testRulesDataProvider(): array
    {
        return [
            '1. When request is well formed, Then it should return no error.' => [
                'payload' => [
                    'member_id' => '6791e6ee161f655ad007ef0f',
                    'time_start' => '**********',
                    'number_of_bookings' => 1,
                ],
                'expectedErrors' => [],
            ],
        ];
    }
}
