<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Models\Behaviours;

use Glofox\Models\Contracts\ComparableInterface;
use Glofox\Models\Behaviours\CompareWithInitialState;
use GlofoxTestCase;

\App::import('Test/Case', 'GlofoxTestCase');

class CompareWithInitialStateTest extends GlofoxTestCase
{
    public $fixtures = [];

    /**
     * @dataProvider compareWithInitialStateDataProvider
     */
    public function testCompareWithInitialState(array $initialState, array $finalState, array $expected): void
    {
        $initialStateObj = $this->getTraitObject($initialState);
        $finalStateObj = $this->getTraitObject($finalState);

        $this->assertEquals($expected, $finalStateObj->compareWithInitialState($initialStateObj));
    }

    public function compareWithInitialStateDataProvider():array
    {
        return [
            '1. When checking simple non array types, Then should find the difference for changed values only' => [
                'initialState' => [
                    'field1' => 'value1',
                    'field2' => 1256,
                    'field3' => 45.07,
                    'field4' => true,
                ],
                'finalState' => [
                    'field1' => 'value2',
                    'field2' => 1257,
                    'field3' => 45.07,
                    'field4' => false,
                ],
                'expected' => [
                    'field1' => ['old' => 'value1', 'new' => 'value2'],
                    'field2' => ['old' => 1256, 'new' => 1257],
                    'field4' => ['old' => true, 'new' => false],
                ],
            ],
            '2. When checking simple array types with non changed values, but reordered,
             Then the difference is empty' => [
                'initialState' => [
                    'field1' => ['value1', 'value2'],
                    'field2' => ['key1' => 'value1', 'key2' => 'value2'],
                    'field3' => ['key1' => 'value1', 'key2' => 'value2', 'key3' => 'value3'],
                ],
                'finalState' => [
                    'field1' => ['value2', 'value1'],
                    'field2' => ['key1' => 'value1', 'key2' => 'value2'],
                    'field3' => ['key1' => 'value1', 'key3' => 'value3', 'key2' => 'value2'],
                ],
                'expected' => [],
            ],
            '3. When checking simple array types with changed values,
             Then should find the difference for changed values only' => [
                'initialState' => [
                    'field1' => ['value1', 'value2'],
                    'field2' => ['value1', 'value2'],
                    'field3' => ['key1' => 'value1', 'key2' => 'value2'],
                    'field4' => [
                        'key1' => 123,
                        'key2' => 124,
                    ],
                ],
                'finalState' => [
                    'field1' => ['value2', 'value1', 'value3'],
                    'field2' => ['value1', 'value2'],
                    'field3' => ['key3', 'key1' => 'value1', 'key2' => 'value2'],
                    'field4' => [
                        'key1' => 123,
                        'key2' => 125,
                    ],
                ],
                'expected' => [
                    'field1' => [
                        'old' => ['value1', 'value2'],
                        'new' => ['value2', 'value1', 'value3'],
                    ],
                    'field3' => [
                        'old' => ['key1' => 'value1', 'key2' => 'value2'],
                        'new' => ['key3', 'key1' => 'value1', 'key2' => 'value2'],
                    ],
                    'field4' => [
                        'old' => [
                            'key1' => 123,
                            'key2' => 124,
                        ],
                        'new' => [
                            'key1' => 123,
                            'key2' => 125,
                        ],
                    ],
                ],
            ],
        ];
    }

    private function getTraitObject(array $comparableFields): ComparableInterface
    {
        return new class($comparableFields) implements ComparableInterface {
            use CompareWithInitialState;

            private array $comparableFields;

            public function __construct(array $comparableFields)
            {
                $this->comparableFields = $comparableFields;
            }

            public function comparableFieldsValues(): array
            {
                return $this->comparableFields;
            }
        };
    }
}
