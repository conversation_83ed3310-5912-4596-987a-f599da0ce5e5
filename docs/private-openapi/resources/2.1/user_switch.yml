parameters:
  - schema:
      type: string
    name: branchId
    in: path
    required: true
    description: Id of the current branch
  - schema:
      type: string
    name: userId
    in: path
    required: true
get:
  tags:
    - Auth
  summary: Switching a new branch
  operationId: switch-branch
  description: |-
    Authenticating the user for a new branch. Errors like "YOU_DO_NOT_HAVE_ACCESS_TO_THAT_BRANCH" are returned
    as a 200 status code.
  x-internal: true
  security:
    - coreapi_auth: [ ]
  parameters:
    - schema:
        type: string
      in: query
      name: to
      description: Id of the branch to switch
  responses:
    '200':
      description: OK
      content:
        application/json:
          schema:
            type: object
            properties:
              success:
                type: boolean
              message:
                type: string
              message_code:
                type: string
              data:
                type: object
                properties:
                  token:
                    type: string
                    format: jwt
                  refresh_token:
                    type: string
                    format: base64
    '400':
      description: Bad request
      content:
        application/json:
          schema:
            $ref: "../../schemas/error.yml"
    '403':
      description: Forbidden
      content:
        application/json:
          schema:
            $ref: "../../schemas/error.yml"