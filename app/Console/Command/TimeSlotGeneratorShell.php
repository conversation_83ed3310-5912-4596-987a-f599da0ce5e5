<?php

declare(strict_types=1);

use Glofox\Domain\Branches\Models\Branch as BranchModel;
use Glofox\Domain\Branches\Repositories\BranchesRepository;
use Glofox\Domain\Branches\Search\Expressions\BranchesWithinContinents;
use Glofox\Domain\Region\ContinentResolver;
use Glofox\Repositories\Search\Expressions\Shared\Active;
use Glofox\Repositories\Search\Expressions\Shared\InIds;
use Psr\Log\LoggerInterface;

ini_set('max_execution_time', '600');

App::uses('AppShell', 'Console/Command');
App::uses('ComponentCollection', 'Controller');
App::uses('Controller', 'Controller');
App::uses('TimeSlotGeneratorComponent', 'Controller/Component');

class TimeSlotGeneratorShell extends Shell
{
    private string $region;
    private array $branchIds;

    public function getOptionParser(): ConsoleOptionParser
    {
        $parser = parent::getOptionParser();

        $parser->addOption('region', [
            'short' => 'r',
            'help' => __('region in two characters format (us, eu, ap)'),
        ]);

        $parser->addOption('branch-ids', [
            'short' => 'b',
            'help' => __('the list of branches ids split by comma'),
        ]);

        return $parser;
    }

    public function main(): void
    {
        $this->initParameters();

        $logger = app()->make(LoggerInterface::class);

        $logger->info("~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~");
        $logger->info(sprintf('Cron Started: %s', self::class));

        $continentResolver = app()->make(ContinentResolver::class);
        $branchesRepo = app()->make(BranchesRepository::class);
        $branchesRepo
            ->addCriteria(new Active(true))
            ->addCriteria(new BranchesWithinContinents($continentResolver->resolve($this->region)));

        if (!empty($this->branchIds)) {
            $branchIds = array_map(static fn (string $branchId) => new MongoId($branchId), $this->branchIds);
            $branchesRepo->addCriteria(new InIds(collect($branchIds)));
        }

        $branches = $branchesRepo->find();
        if (empty($branches)) {
            $logger->info(sprintf('The list of found branches is empty: %s', self::class));
            $logger->info(sprintf('Cron Finished: %s', self::class));

            return;
        }

        $collection = new ComponentCollection();
        $timeSlotGenerator = new TimeSlotGeneratorComponent($collection);
        $timeSlotGenerator->initialize(new Controller());

        $processedBranchesIds = [];
        foreach ($branches as $branch) {
            $logger->info(sprintf('Generating timeslots for branch %s', $branch->get('_id')));

            $branchModel = BranchModel::make($branch->toArray());
            $timeSlotGenerator->generateByBranch($branchModel);

            $processedBranchesIds[] = $branchModel->id();
        }

        $totalBranchesFound = is_countable($branches) ? count($branches) : 0;
        $logger->info(sprintf('Total branches found %s: %s', $totalBranchesFound, self::class));
        $logger->info(sprintf(
            'The list of processed branches: %s: %s',
            implode(', ', $processedBranchesIds),
            self::class
        ));

        $logger->info(sprintf('Cron Finished: %s', self::class));
    }

    /**
     * @throws Exception
     */
    private function initParameters(): void
    {
        $this->region = $this->param('region');

        if (!$this->region) {
            throw new \RuntimeException('No region was specified, please use "us", "eu" or "ap"');
        }

        $this->branchIds = $this->param('branch-ids')
            ? explode(',', $this->param('branch-ids'))
            : [];
    }
}
