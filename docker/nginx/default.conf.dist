server {
      listen 80 default_server;
      root /var/www/app/webroot;
      index index.php index.html index.htm;
      server_name _;

      large_client_header_buffers 4 16k;
      
      real_ip_header X-Forwarded-For;
      set_real_ip_from 0.0.0.0/0;

      location /proxy-health {
          access_log off;
          default_type application/json;
          return 200 '{"success": true, "message": "Server Health"}';
      }

      location / {
        try_files $uri /index.php$is_args$args;
      }

      location ~ ^/index\.php(/|$) {
          fastcgi_pass 127.0.0.1:9000;

          fastcgi_split_path_info ^(.+\.php)(/.*)$;
          include fastcgi_params;

          fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
          fastcgi_param DOCUMENT_ROOT $realpath_root;
      }
      
      location ~ /\. {
          deny all;
      }
}
