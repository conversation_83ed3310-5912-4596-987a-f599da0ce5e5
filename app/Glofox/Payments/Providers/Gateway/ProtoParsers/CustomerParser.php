<?php

namespace Glofox\Payments\Providers\Gateway\ProtoParsers;

use Carbon\Carbon;
use Glofox\Payments\Entities\Customer\Models\Customer;
use Google\Protobuf\Internal\RepeatedField;
use GRPC\Payments\AccountService\Account;

/**
 * Class CustomerParser
 * @package Glofox\Payments\ProtoParsers
 */
class CustomerParser
{
    /**
     * @param Account $account
     * @return Customer
     */
    public function parseFromProtoMessage($account): Customer
    {
        $providerCustomer = new Customer();

        $deletedAt = null;
        if ($account->getDeletedAt()) {
            $deletedAt = Carbon::parse($account->getDeletedAt())->getTimestamp();
        }

        return $providerCustomer->setId($account->getID())
            ->setFirstName($account->getFirstName())
            ->setLastName($account->getLastName())
            ->setProviderId($account->getProviderID())
            ->setCurrency($account->getCurrency())
            ->setEmail($account->getEmail())
            ->setPhone($account->getPhone())
            ->setServiceProviderId($account->getPSPUUID())
            ->setServiceProviderResponse($account->getPSPResponse())
            ->setExternalRef($account->getExternalRef())
            ->setCreatedAt(
                Carbon::parse($account->getCreatedAt())->getTimestamp()
            )
            ->setUpdatedAt(
                Carbon::parse($account->getUpdatedAt())->getTimestamp()
            )
            ->setDeletedAt($deletedAt);
    }


    /**
     * @param RepeatedField|Account[] $customers
     * @return Customer[]
     */
    public function parseFromProtoMessageCollection(RepeatedField $customers): ?array
    {
        $providerCustomers = null;

        if (empty($customers)) {
            return $providerCustomers;
        }

        foreach ($customers as $customer) {
            $providerAccounts[] = $this->parseFromProtoMessage($customer);
        }

        return $providerCustomers;
    }
}
