<?php

declare(strict_types=1);

namespace Glofox\Domain\Memberships\Services\Save;

use Glofox\Domain\Memberships\Models\Membership;
use Glofox\Domain\Memberships\Models\Plan;
use Glofox\Domain\Memberships\Repositories\MembershipsRepository;
use Glofox\Repositories\Search\Expressions\Shared\Id;
use Illuminate\Support\Collection;

class MergeMissingRemovedPlansService
{
    private \Glofox\Domain\Memberships\Repositories\MembershipsRepository $membershipsRepository;

    public function __construct(MembershipsRepository $membershipsRepository)
    {
        $this->membershipsRepository = $membershipsRepository;
    }

    public function execute(Membership $membership): Membership
    {
        $plans = $membership->plans()->merge(
            $this->missingRemovedPlans($membership)
        );

        return $membership->put('plans', $plans);
    }

    private function missingRemovedPlans(Membership $membership): Collection
    {
        $currentPlanCodes = $membership->plans()->pluck('code')->toArray();
        $internalMembership = $this->fetchMembership($membership->id());

        if (empty($internalMembership)) {
            return new Collection();
        }

        return $internalMembership->plans()
            ->filter(fn(Plan $plan) => !\in_array($plan->code(), $currentPlanCodes) &&
                $plan->isRemoved())
            ->values();
    }

    private function fetchMembership(?string $id): ?Membership
    {
        if (empty($id)) {
            return null;
        }

        return $this->membershipsRepository
            ->addCriteria(new Id($id))
            ->first();
    }
}
