## Mobile Configuration  [/2.1/branches/{branchId}/configuration/mobile]

+ Parameters
    + branchId: 507f191e810c19729de860ea (string) - The identification of the branch

+ Attributes
    + images (object)
        + primaryLogo: https://cdn.glofox.com/logo.png (string, nullable) - A nullable string field
        + secondaryLogo: https://cdn.glofox.com/logo.png (string, nullable) - A nullable string field
    + colors (object)
        + background: #FF0000 (string)
        + foreground: #FF0000 (string)
        + text: #FF0000 (string)
    + settings (object)
        + displayPastEvents (boolean)
        + displayClassesOnSchedule (boolean)
        + displayCoursesOnSchedule (boolean)
        + displayTrainersAppointmentsOnSchedule (boolean)
        + displayFacilitiesAppointmentsOnSchedule (boolean)
        + displayCreditHistoryTab (boolean)
        + displayStoreTab (boolean)
        + displayNewsTab (boolean)
        + displayTrainersNamesOnEvent (boolean)
        + displayFacilityNameOnEvent (boolean)
        + displayLevelOnEvent (boolean)
        + displaySizeOnEvent (boolean)
        + displayWaitlingSizeOnEvent (boolean)
        + eventSizeFormat: SpacesRemaining (enum[string]) - 
            + Members
                + SpacesRemaining
                + SpacesBookedOfTotal
    + filters (object)
        + displayClassesFilter (boolean)
        + displayCoursesFilter (boolean)
        + displayFacilitiesFilter (boolean)
        + displayTrainersFilter (boolean)
        + displayCategoriesFilter (boolean)
    + dev_settings (object)
            + apple_dev_api_key: hhArccPTZevB68icjGkFrUpwckJrSkFaZm5jVVhHNHRETXJ0d1E9PQ== (string, nullable) - A nullable string field
            + is_apple_dev_api_key_added (boolean)
            + apple_dev_api_key_secret_name: APPLE_DEV_KEY (string, nullable) - A nullable string field

### Retrieve Mobile Configuration [GET]

+ Request
    + Headers
          Authorization: Bearer {authToken}

+ Response 200 (application/json)
    + Attributes (Mobile Configuration)
    

+ Response 400 (application/json)
    + Attributes (Generic Error)

### Update Mobile Configuration [PATCH]

+ Request  (application/json)
    + Headers
          Authorization: Bearer {authToken}
    + Attributes (Mobile Configuration)

+ Response 200 (application/json)
    + Attributes (Mobile Configuration)

+ Response 400 (application/json)
    + Attributes (Generic Error)
