<?php

declare(strict_types=1);

namespace Glofox\Repositories\Search\Expressions\Shared;

use Glofox\Repositories\Filters\Evaluable;
use Glofox\Repositories\Search\Expressions\Conditionable;
use Glofox\Repositories\Search\Expressions\HasConditions;
use Webmozart\Expression\Expr;
use Glofox\Repositories\Search\Expressions\Key;

class BranchNamespace extends Key implements Evaluable, Conditionable
{
    use HasConditions;

    private const FIELD = 'namespace';
    public function __construct(string $namespace)
    {
        parent::__construct(self::FIELD, Expr::equals($namespace));
    }

    public function field() : string
    {
        return self::FIELD;
    }
}
