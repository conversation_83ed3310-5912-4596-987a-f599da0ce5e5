<?php


namespace CakeTestCases\Glofox\Payments\Console\Gateway;

use Glofox\Console\CommandInputType;
use Glofox\Console\CommandParameter;
use Glofox\Domain\Cards\Models\Card;
use Glofox\Domain\Cards\Repositories\CardsRepository;
use Glofox\Domain\PaymentMethodUsers\Models\PaymentMethodUser;
use Glofox\Domain\PaymentMethodUsers\Repository\PaymentMethodUsersRepository;
use Glofox\Payments\Console\Gateway\RecordMissingCards;
use Glofox\Payments\Console\Gateway\RecordMissingTransactions;
use Glofox\Payments\Providers\Gateway\GRPC\Connection;
use Glofox\Storage\CloudStorageInterface;
use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use GRPC\Payments\AccountService\AccountServiceClient;
use GRPC\Payments\AccountService\ListCardsByAccountReq;
use GRPC\Payments\AccountService\Card as GRPCCard;
use GRPC\Payments\AccountService\Cards as GRPCCards;
use GRPC\Payments\TransactionService\TransactionServiceClient;
use Grpc\UnaryCall;
use League\Csv\Reader;
use Psr\Log\LoggerInterface;

\App::import('Test/Case', 'GlofoxTestCase');

final class RecordMissingCardsTest extends \GlofoxTestCase
{
    /** @var CardsRepository */
    private $cardsRepository;

    public function setUp()
    {
        parent::setUp();

        $this->cardsRepository = app()->make(CardsRepository::class);

        app()->forgetInstance(CloudStorageInterface::class);
        app()->forgetInstance(LoggerInterface::class);
        app()->forgetInstance(Connection::class);
    }

    public function tearDown()
    {
        \Mockery::close();
        parent::tearDown();

        app()->forgetInstance(CloudStorageInterface::class);
        app()->forgetInstance(LoggerInterface::class);
        app()->forgetInstance(Connection::class);
    }

    public function test_it_should_provide_the_right_command_parameters()
    {
        /** @var RecordMissingTransactions $commandHandler */
        $commandHandler = app()->make(RecordMissingCards::class);

        $this->assertNotEmpty($commandHandler->parameters());

        $this->assertEquals(
            ['method', 'branch-id', 'upload-report'],
            $commandHandler->parameters()->map(fn(CommandParameter $commandParameter) => $commandParameter->name())->toArray()
        );

        $commandsThatShouldBeBoolean = $commandHandler->parameters()->filter(fn(CommandParameter $commandParameter) => \in_array($commandParameter->name(), ['upload-report']));

        /** @var CommandParameter $commandThatShouldBeBoolean */
        foreach ($commandsThatShouldBeBoolean as $commandThatShouldBeBoolean) {
            $this->assertTrue($commandThatShouldBeBoolean->type()->is(CommandInputType::BOOLEAN()));
        }
    }

    public function test_it_should_skip_if_user_already_has_a_card()
    {
        $branchId = '49a7011a05c677b9a916612a';

        $tester = $this;

        $cloudStorageMocked = \Mockery::mock(CloudStorageInterface::class)
            ->shouldReceive('put')
            ->andReturnUsing(function ($fileName, $fileContent) use ($tester) {
                $tester->assertNotEmpty(basename($fileName));
                $tester->assertNotEmpty($fileContent);

                $report = iterator_to_array(Reader::createFromString($fileContent)->fetchAssoc(), false);
                $tester->assertCount(1, $report);

                $tester->assertEquals('User mocked_user_id has 1 card(s) already', $report[0]['result']);
                $tester->assertEquals('n/a', $report[0]['card_id']);
                $tester->assertEquals('mocked_user_id', $report[0]['user_id']);
                $tester->assertEquals('', $report[0]['dry_run']);

                return true;
            })
            ->getMock();

        $mockedPaymentMethodUsersRepository = \Mockery::mock(PaymentMethodUsersRepository::class);
        $mockedPaymentMethodUsersRepository->shouldReceive('addCriteria')->andReturn($mockedPaymentMethodUsersRepository);
        $mockedPaymentMethodUsersRepository
            ->shouldReceive('find')
            ->andReturn([PaymentMethodUser::make([
                'user_id' => 'mocked_user_id',
                'customer_provider_id' => '11111',
                'payment_method_id' => 'mocked_payment_method_id',
                'branch_id' => $branchId,
            ])]);

        $mockedCardsRepository = \Mockery::mock(CardsRepository::class);
        $mockedCardsRepository->shouldReceive('addCriteria')->andReturn($mockedCardsRepository);
        $mockedCardsRepository->shouldReceive('find')->andReturn([
            Card::make([
                '_id' => 'mocked_card_already_present'
            ])
        ]);

        app()->instance(PaymentMethodUsersRepository::class, $mockedPaymentMethodUsersRepository);

        app()->instance(CardsRepository::class, $mockedCardsRepository);

        app()->instance(LoggerInterface::class, $this->getLoggerMocked());

        app()->instance(CloudStorageInterface::class, $cloudStorageMocked);

        app()->instance(Connection::class, $this->getGRPCConnectionMocked());

        $commandParams = [
            'method' => 'execute',
            'branch-id' => $branchId,
        ];

        /** @var RecordMissingTransactions $commandHandler */
        $commandHandler = app()->make(RecordMissingCards::class);

        $commandHandler->invoke($commandParams);

        app()->forgetInstance(CloudStorageInterface::class);
        app()->forgetInstance(LoggerInterface::class);
        app()->forgetInstance(Connection::class);
        app()->forgetInstance(PaymentMethodUsersRepository::class);
        app()->forgetInstance(CardsRepository::class);
    }

    public function test_it_should_skip_on_dry_run()
    {
        $branchId = '49a7011a05c677b9a916612a';

        $tester = $this;

        $cloudStorageMocked = \Mockery::mock(CloudStorageInterface::class)
            ->shouldReceive('put')
            ->andReturnUsing(function ($fileName, $fileContent) use ($tester) {
                $tester->assertNotEmpty(basename($fileName));
                $tester->assertNotEmpty($fileContent);

                $report = iterator_to_array(Reader::createFromString($fileContent)->fetchAssoc(), false);
                $tester->assertCount(1, $report);

                $tester->assertEquals('SUCCESS', $report[0]['result']);
                $tester->assertEquals('1234', $report[0]['card_id']);
                $tester->assertEquals('mocked_user_id', $report[0]['user_id']);
                $tester->assertEquals('1', $report[0]['dry_run']);

                return true;
            })
            ->getMock();

        $mockedPaymentMethodUsersRepository = \Mockery::mock(PaymentMethodUsersRepository::class);
        $mockedPaymentMethodUsersRepository->shouldReceive('addCriteria')->andReturn($mockedPaymentMethodUsersRepository);
        $mockedPaymentMethodUsersRepository
            ->shouldReceive('find')
            ->andReturn([PaymentMethodUser::make([
                'user_id' => 'mocked_user_id',
                'customer_provider_id' => '11111',
                'payment_method_id' => 'mocked_payment_method_id',
                'branch_id' => $branchId,
            ])]);

        app()->instance(PaymentMethodUsersRepository::class, $mockedPaymentMethodUsersRepository);

        app()->instance(LoggerInterface::class, $this->getLoggerMocked());

        app()->instance(CloudStorageInterface::class, $cloudStorageMocked);

        app()->instance(Connection::class, $this->getGRPCConnectionMocked());

        $commandParams = [
            'method' => 'dry-run',
            'branch-id' => $branchId,
        ];

        /** @var RecordMissingTransactions $commandHandler */
        $commandHandler = app()->make(RecordMissingCards::class);

        $commandHandler->invoke($commandParams);

        app()->forgetInstance(CloudStorageInterface::class);
        app()->forgetInstance(LoggerInterface::class);
        app()->forgetInstance(Connection::class);
        app()->forgetInstance(PaymentMethodUsersRepository::class);
    }

    public function test_it_should_record_a_missing_card_successfully()
    {
        $branchId = '49a7011a05c677b9a916612a';

        $tester = $this;

        $cloudStorageMocked = \Mockery::mock(CloudStorageInterface::class)
            ->shouldReceive('put')
            ->andReturnUsing(function ($fileName, $fileContent) use ($tester) {
                $tester->assertNotEmpty(basename($fileName));
                $tester->assertNotEmpty($fileContent);

                $report = iterator_to_array(Reader::createFromString($fileContent)->fetchAssoc(), false);
                $tester->assertCount(1, $report);

                $tester->assertEquals('SUCCESS', $report[0]['result']);
                $tester->assertEquals('mocked_card_id', $report[0]['card_id']);
                $tester->assertEquals('mocked_user_id', $report[0]['user_id']);
                $tester->assertEquals('', $report[0]['dry_run']);

                return true;
            })
            ->getMock();

        $mockedPaymentMethodUsersRepository = \Mockery::mock(PaymentMethodUsersRepository::class);
        $mockedPaymentMethodUsersRepository->shouldReceive('addCriteria')->andReturn($mockedPaymentMethodUsersRepository);
        $mockedPaymentMethodUsersRepository
            ->shouldReceive('find')
            ->andReturn([PaymentMethodUser::make([
                'user_id' => 'mocked_user_id',
                'customer_provider_id' => '11111',
                'payment_method_id' => 'mocked_payment_method_id',
                'branch_id' => $branchId,
            ])]);

        $mockedCardsRepository = \Mockery::mock(CardsRepository::class);
        $mockedCardsRepository->shouldReceive('addCriteria')->andReturn($mockedCardsRepository);
        $mockedCardsRepository->shouldReceive('find')->andReturn(null);
        $mockedCardsRepository->shouldReceive('legacySaveOrFail')->andReturn([
            'Card' => [
                '_id' => 'mocked_card_id'
            ]
        ]);

        app()->instance(PaymentMethodUsersRepository::class, $mockedPaymentMethodUsersRepository);

        app()->instance(CardsRepository::class, $mockedCardsRepository);

        app()->instance(LoggerInterface::class, $this->getLoggerMocked());

        app()->instance(CloudStorageInterface::class, $cloudStorageMocked);

        app()->instance(Connection::class, $this->getGRPCConnectionMocked());

        $commandParams = [
            'method' => 'execute',
            'branch-id' => $branchId,
        ];

        /** @var RecordMissingTransactions $commandHandler */
        $commandHandler = app()->make(RecordMissingCards::class);

        $commandHandler->invoke($commandParams);

        app()->forgetInstance(CloudStorageInterface::class);
        app()->forgetInstance(LoggerInterface::class);
        app()->forgetInstance(Connection::class);
        app()->forgetInstance(PaymentMethodUsersRepository::class);
        app()->forgetInstance(CardsRepository::class);
    }

    private function getGRPCConnectionMocked(): Connection
    {
        /** @var Connection $grpcConnectionMocked */
        $grpcConnectionMocked = $this->getMock(Connection::class);

        $grpcConnectionMocked->accountService = \Mockery::mock(AccountServiceClient::class)
            ->shouldReceive('ListCardsByAccount')
            ->andReturnUsing(function (...$args) {
                /** @var ListCardsByAccountReq $request */
                $request = $args[0];

                $status = new \StdClass();
                $status->code = 0;

                $card = new GRPCCard();
                $card->setID(1234);
                $card->setAccountID(4567);
                $card->setBrand('Visa');
                $card->setYear(2050);
                $card->setLastFour(1234);

                $cards = new RepeatedField(GPBType::MESSAGE, GRPCCard::class);
                $cards->offsetSet(null, $card);

                $response = new GRPCCards();
                $response->setCards($cards);

                return \Mockery::mock(UnaryCall::class)
                    ->shouldReceive('wait')
                    ->andReturn([$response, $status])
                    ->getMock();

            })->getMock();

        return $grpcConnectionMocked;
    }

    private function getLoggerMocked(): LoggerInterface
    {
        return \Mockery::mock(LoggerInterface::class)
            ->shouldReceive('info', 'error')
            ->andReturnNull()
            ->getMock();
    }
}