parameters:
  - schema:
      type: string
    name: branchId
    in: path
    required: true
get:
  tags:
    - Branches
  summary: ''
  operationId: get-store-sales-by-branch
  description: This end point returns filtered store sales of a given branch
  x-internal: true
  security:
    - coreapi_auth: [ ]
  parameters:
    - schema:
        type: string
      in: query
      name: status
      description: COLLECTED | UNCOLLECTED
    - schema:
        type: string
      in: query
      name: sort-by
      description: field | -field
    - schema:
        type: integer
      in: query
      name: page
    - schema:
        type: integer
        maximum: 50
      in: query
      name: limit
      description: <= 50
    - schema:
        type: string
      in: query
      name: user-id
      description: id of the member who purchased the product
    - schema:
        type: string
      in: query
      name: invoice-id
      description: invoice of the purchase
    - schema:
        type: string
      in: query
      name: search-query
      description: User, product or presentation name
  responses:
    '200':
      description: OK
      content:
        application/json:
          schema:
            allOf:
              - $ref: "../../schemas/pagination.yml"
              - type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: "../../schemas/store_sales.yml"