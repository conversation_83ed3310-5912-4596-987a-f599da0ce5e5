<?php

declare(strict_types=1);

namespace Glofox\Repositories\Search\Expressions\Shared;

use Glofox\Repositories\Filters\Evaluable;
use Glofox\Repositories\Search\Expressions\Conditionable;
use Glofox\Repositories\Search\Expressions\HasConditions;
use Glofox\Repositories\Search\Expressions\Key;
use Webmozart\Expression\Expr;

class BranchId extends Key implements Evaluable, Conditionable
{
    use HasConditions;

    private const FIELD = 'branch_id';

    public function __construct(string $branchId)
    {
        parent::__construct(self::FIELD, Expr::equals($branchId));
    }

    public function field(): string
    {
        return self::FIELD;
    }
}
