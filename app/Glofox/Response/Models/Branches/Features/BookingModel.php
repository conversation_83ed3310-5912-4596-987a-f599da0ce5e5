<?php

namespace Glofox\Response\Models\Branches\Features;

use Glofox\Response\BaseModel;
use Glofox\Response\Models\Branches\Features\Booking\StripePaymentModel;

/**
 * @SuppressWarnings(PHPMD)
 */
class BookingModel extends BaseModel
{
    /**
     * @var int
     */
    public $order = 0;

    /**
     * @var bool
     */
    public $enabled = false;

    /**
     * @var StripePaymentModel
     */
    public $strike_system;

    /**
     * @var bool
     */
    public $_booking_open_window = false;

    /**
     * @var bool
     */
    public $limit_booking_enabled = false;

    /**
     * @var bool
     */
    public $_cancel_credit_option = false;

    /**
     * @var string
     */
    public $cancel_credit_option = '';

    /**
     * @var int
     */
    public $refunded_credit_expiry = 0;

    /**
     * @var int
     */
    public $guest_bookings = 0;

    /**
     * @var int
     */
    public $booking_cancel_window = 0;

    /**
     * @var int
     */
    public $booking_open_window = 0;

    /**
     * @var bool
     */
    public $waiting_list_enabled = false;

    /**
     * @var bool
     */
    public $auto_booking_enabled = false;

    /**
     * @var int
     */
    public $waiting_list = 0;

    /**
     * @var string
     */
    public $limit_booking_period = '';

    /**
     * @var int
     */
    public $limit_booking = 0;

    /**
     * @var bool
     */
    public $late_cancellation_enabled = false;

    /**
     * $var bool
     */
    public $overbooking_enabled = false;

    protected array $allowedFields = [
        'order',
        'enabled',
        'strike_system',
        '_booking_open_window',
        'limit_booking_enabled',
        '_cancel_credit_option',
        'cancel_credit_option',
        'refunded_credit_expiry',
        'guest_bookings',
        'booking_cancel_window',
        'booking_open_window',
        'waiting_list_enabled',
        'auto_booking_enabled',
        'waiting_list',
        'limit_booking_period',
        'limit_booking',
        'late_cancellation_enabled',
        'overbooking_enabled'
    ];

    public function __construct(array $data)
    {
        $this->setModelFields($data);
    }
}
