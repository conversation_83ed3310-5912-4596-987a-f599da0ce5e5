<?php

namespace Glofox\Infrastructure\PushNotifications;

use Glofox\Domain\Notifications\Services\PushNotificationsServiceInterface;
use GuzzleHttp\Client;
use Psr\Log\LoggerInterface;

class PushNotificationsHttpClientNew implements PushNotificationsServiceInterface
{
    private const STATUS_RANGE_ENDPOINT = "v1/status_range/{studioID}/{startDate}/{endDate}";
    private const HEALTH_ENDPOINT = "v1/health";

    /**
     * @var LoggerInterface
     */
    private LoggerInterface $logger;

    /**
     * @var Client
     */
    private Client $client;


    /**
     * PushNotificationsHttpClient constructor.
     * @param LoggerInterface $logger
     * @param Client $client
     */
    public function __construct(
        LoggerInterface $logger,
        Client $client
    ) {
        $this->logger = $logger;
        $this->client = $client;
    }
    /**
     * @return mixed
     */
    public function health()
    {
        return $this->get(self::HEALTH_ENDPOINT);
    }
    /**
     * @param string|null $namespace
     * @param string|null $branchId
     * @param int $startDate
     * @param int|null $endDate
     * @return mixed
     */
    public function getMessagesStatusesInDateRange(?string $namespace, ?string $branchId, int $startDate, ?int $endDate)
    {
        $uri = str_replace("{studioID}", $branchId, self::STATUS_RANGE_ENDPOINT);
        $uri = str_replace("{startDate}", $startDate, $uri);
        $uri = str_replace("{endDate}", $endDate, $uri);

        return $this->getJSON($uri);
    }

    /**
     * @param string $uri
     * @return mixed
     */
    private function getJSON(string $uri)
    {
        $this->logger->info(sprintf('New Push Notifications requesting %s...', $uri));
        $res = $this->client->get($uri, [
            'verify' => false,
            'headers' => [
                'Content-Type' => 'application/json',
            ],
        ]);
        $body = $res->getBody()->getContents();
        $this->logger->info(sprintf('New Push Notifications response [%s]: %s', $uri, $body));

        return $body;
    }

    /**
     * @param string $uri
     * @return mixed
     */
    private function get(string $uri)
    {
        $this->logger->info(sprintf('New Push Notifications requesting %s...', $uri));
        $res = $this->client->get($uri, [
            'verify' => false,
            'headers' => [
                'Content-Type' => 'application/json',
            ],
        ]);
        $this->logger->info(sprintf('New Push Notifications response status code [%s]: %d', $uri, $res->getStatusCode()));
        return $res;
    }
}
