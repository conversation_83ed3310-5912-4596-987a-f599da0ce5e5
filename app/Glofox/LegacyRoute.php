<?php

namespace Glofox;

class LegacyRoute
{
    private $route;
    private $defaults;
    private $options;

    public function __construct($route, $defaults = [], $options = [])
    {
        $this->route = $route;
        $this->defaults = $defaults;
        $this->options = $options;
    }

    public function route(): string
    {
        return $this->route;
    }

    public function defaults(): array
    {
        return $this->defaults;
    }

    public function options(): array
    {
        return $this->options;
    }
}