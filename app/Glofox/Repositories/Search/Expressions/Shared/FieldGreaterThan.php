<?php

namespace Glofox\Repositories\Search\Expressions\Shared;

use Glofox\Repositories\Filters\Evaluable;
use Glofox\Repositories\Search\Expressions\Conditionable;
use Glofox\Repositories\Search\Expressions\HasConditions;
use Webmozart\Expression\Expr;
use Glofox\Repositories\Search\Expressions\Key;

class FieldGreaterThan extends Key implements Evaluable, Conditionable
{
    use HasConditions;

    private string $field;

    public function __construct(string $field, $value)
    {
        $this->field = $field;
        parent::__construct($field, Expr::greaterThan($value));
    }

    public function field() : string
    {
        return $this->field;
    }
}
