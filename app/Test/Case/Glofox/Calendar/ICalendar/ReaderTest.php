<?php

namespace CakeTestCases\Glofox\Calendar\ICalendar;

use Glofox\Calendar\ICalendar\EventsCollection;
use Glofox\Calendar\ICalendar\Reader;
use Glofox\Payments\Providers\Gateway\Subscriptions\Description;

\App::import('Test/Case', 'GlofoxTestCase');

class ReaderTest extends \GlofoxTestCase
{
    public function test_it_should_decode_an_ical_string_into_the_right_set_of_objects()
    {
        $icalString = \file_get_contents(
            sprintf('%s/IcalFixtures/%s', __DIR__, 'ical_with_2_simple_events.ical')
        );

        $calendar = Reader::read($icalString);

        $this->assertInstanceOf(EventsCollection::class, $calendar->events());
        $this->assertEquals(2, $calendar->events()->count());

        $this->assertInstanceOf(Description::class, $calendar->events()->first()->getDescription());
        $this->assertEquals('5d2d0b8defbdb', $calendar->events()->first()->getUniqueId());
        $this->assertEquals('CHARGE', $calendar->events()->first()->getDescription()->get('event_type'));
        $this->assertEquals('6000', $calendar->events()->first()->getDescription()->get('amount'));
        $this->assertEquals('20', $calendar->events()->first()->getDescription()->get('fee'));
        $this->assertEquals('NZD', $calendar->events()->first()->getDescription()->get('currency'));
        $this->assertEquals('CARD', $calendar->events()->first()->getDescription()->get('payment_method'));
        $this->assertEquals('20190715T232705Z', $calendar->events()->first()->getDtStart()->format('Ymd\THis\Z'));
        $this->assertEquals('20190715T232705Z', $calendar->events()->first()->getDtEnd()->format('Ymd\THis\Z'));
    }
}