parameters:
  - schema:
      type: string
      pattern: '^[a-f\d]{24}$'
    name: branchId
    in: path
    required: true
    description: The branch ID the users are searched in.
get:
  tags:
    - Users
  summary: Search members by filter (email, search string, gympassId)
  operationId: users-search
  description: |
    If you apply filter by email, you have to consider some points.
    Emails in Glofox are unique for each studio so you might need to see if an email exists in a studio.
    This search checks if a full email address exists. It can not be used for searching for part of an email.
  x-internal: true
  security:
    - coreapi_auth: [ ]
  parameters:
    - in: query
      name: filters
      schema:
        type: object
        properties:
          email:
            type: string
            description: Filer by email. If used, then search is made by email.
          search:
            type: string
            description: |
              Filer by search string.
              If used, then search string is used to find matches in user's name, email, phone, access barcode.
          gympassId:
            type: string
            description: Filer by gympassId. If used, then search is made by gympassId.
      style: deepObject
      explode: true
      required: true
      description: |
        The filter applied to search for the users.
        If you need search by email, then you need to pass parameter like ?filters[email]=<EMAIL>.
        If you need search by search string, for example user name, then you need to pass parameter like ?filters[search]=UserName.
    - schema:
        type: array
        items:
          type: string
          enum:
            - membership
            - aggregated_credits
            - profile_rules
      name: includes
      in: query
      style: form
      explode: true
      required: false
      description: |
        If response should include additional information (about membership, credits, etc).
        Profile rules include the rules that are applied to the user in order to restrict their access to some resources.
        For instance it calculates restricted_profile showing if the user has restricted access to the details page of the profile.
        If you need to include profile_rules, then you need to pass parameter like ?includes[]=profile_rules:branch_id({branchId}),
        where {branchId} is the branch ID you are checking access to.
    - schema:
        type: integer
        minimum: 1
      name: page
      in: query
      required: true
    - schema:
        type: integer
        minimum: 1
        maximum: 50
        default: 50
      name: limit
      in: query
      required: false
    - schema:
        type: integer
        default: 0
        enum:
          - 0
          - 1
      name: namespace-search
      in: query
      required: false
      description: |
        If 1, then search is made within the namespace of the client. All users from all roaming branches are searched.
        If 0, then search is made within the branch only.

  responses:
    '200':
      description: OK
      content:
        application/json:
          schema:
            type: object
            properties:
              success:
                type: boolean
                description: Indicates if the request was successful.
              meta:
                type: object
                properties:
                  pagination:
                    type: object
                    properties:
                      count:
                        type: integer
                      perPage:
                        type: integer
                      currentPage:
                        type: integer
                      links:
                        type: object
                        properties:
                            previous:
                              type: string
                            next:
                              type: string
                      total:
                        type: integer
              data:
                type: array
                items:
                  $ref: "../../schemas/user_search.yml"

