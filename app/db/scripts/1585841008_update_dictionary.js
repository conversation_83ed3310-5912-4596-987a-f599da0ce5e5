// Dictionary key values to add or update
const data = {
    'DASHBOARD_CLASSES_EMPTY_STATE_TITLE': 'No upcoming classes today or tomorrow',
    'DASHBOARD_CLASSES_EMPTY_STATE_BUTTON': 'Create a new class',
    'CLASSES_EMPTY_STATE_TITLE': 'You have no classes set up yet - let’s create our first one by clicking below',
    'CLASSES_EMPTY_STATE_BUTTON': 'Find out more?',
    'CLASSES_EMPTY_STATE_LINK_KNOWLEDGE_BASE': 'Add a class',
    'MEMBERS_EMPTY_STATE_TITLE': 'You have no clients yet but this space will fill out as people join your classes',
    'MEMBERS_EMPTY_STATE_LINK_KNOWLEDGE_BASE': 'Find out more?',
    'REPORT_EMPTY_STATE_TITLE': 'Your report will appear here',
    'REPORT_TRAINER_PERFORMANCE_EMPTY_STATE_TITLE': 'Your Class Performance report will appear here',
    'REPORT_TRAINER_PERFORMANCE_EMPTY_STATE_LINK_KNOWLEDGE_BASE': 'Find out more?',
    'DASHBOARD_CLASSES_ADD_URL': 'Add class URL',
    'CLASS_STREAM_URL_TITLE': 'Class URL',
    'CLASS_STREAM_URL_SAVE_BUTTON': 'Save',
    'CALENDAR_EVENT_NO_URL_ERROR': 'The online link has not been added to this class - click the class here to resolve',
    'DASHBOARD_WIDGET_BOOKINGS_TITLE': 'Class Bookings',
    'DASHBOARD_BOOKING_VS_CAPACITY_WIDGET_TITLE': 'Bookings vs Capacity',
    'DASHBOARD_BOOKING_VS_CAPACITY_WIDGET_EMPTY_STATE_TITLE': 'Data not available',
};

// Please add here any new needed language
const languages = {
    'BRAZILIAN_PORTUGUESE': {
        'NAME': 'Brazilian-Portuguese',
        'CODE': 'br',
    },
};

// Update the language as needed
const query = {
    // 'language.name': languages.BRAZILIAN_PORTUGUESE.NAME, // Depending on the intention this is not necessary at run time
    'language.code': languages.BRAZILIAN_PORTUGUESE.CODE,
};

// Function that performs the database update based on the dictionary id and new dictionary keys-values provided by parameters
const updateFunction = function(dictionaryId, updatedDict) {
    db.getCollection('dictionaries').updateOne(
        {
            '_id': dictionaryId
        },
        {
            '$set': {
                'dictionary': updatedDict
            }
        }
    )
};

// Current dictionaries for the selected language
const dictionaries = db.getCollection('dictionaries').find(query);

// Add the new keys-values to each dictionary for the selected language
dictionaries.forEach((dictElement) => {
    const mergedDict = { ...dictElement.dictionary, ...data };

    updateFunction(dictElement._id, mergedDict);
});
