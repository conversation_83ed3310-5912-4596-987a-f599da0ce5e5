<?php

declare(strict_types=1);

use Glofox\Domain\Users\Formatters\PhoneNumberFormatter;
use Glofox\Domain\Users\Models\User as UserModel;
use Glofox\Domain\Users\Repositories\UsersRepository;
use Glofox\Domain\Users\Search\Expressions\BranchIdNotEqual;
use Glofox\Domain\Users\Search\Expressions\HasPhoneNumber;
use Glofox\Domain\Users\Search\Expressions\HasTwilioPhoneNumber;
use Glofox\Domain\Users\Search\Expressions\Type;
use Glofox\Domain\Users\UserTypesCollection;
use MongoDB\BSON\ObjectId;
use MongoDB\Collection;
use MongoDB\Database;
use MongoDB\Driver\ReadConcern;
use Psr\Log\LoggerInterface;

App::uses('AppShell', 'Console/Command');

class FormatPhoneNumberShell extends AppShell
{
    private const DEFAULT_QUERY_LIMIT = 1000;
    private const MAX_QUERY_LIMIT = 2000;

    /** @var UsersRepository */
    private $usersRepository;

    /** @var Collection */
    private $usersCollection;

    /** @var PhoneNumberFormatter */
    private $formatter;

    /** @var int */
    private $limit;

    /** @var bool */
    private $logUserId;

    /** @var LoggerInterface */
    private $logger;

    public function __construct($stdout = null, $stderr = null, $stdin = null)
    {
        parent::__construct($stdout, $stderr, $stdin);

        $this->usersRepository = app()->make(UsersRepository::class);
        $db = app()->make(Database::class);
        $this->usersCollection = $db
            ->selectCollection('users')
            ->withOptions([
                'readConcern' => new ReadConcern(ReadConcern::AVAILABLE),
            ]);
        $this->formatter = app()->make(PhoneNumberFormatter::class);
        $this->logger = app()->make(LoggerInterface::class);
    }

    public function getOptionParser()
    {
        $parser = parent::getOptionParser();

        $parser->addOption('limit', [
            'short' => 'l',
            'help' => 'The max number of members at a time',
            'default' => static::DEFAULT_QUERY_LIMIT,
        ]);

        $parser->addOption('user-id', [
            'short' => 'i',
            'help' => 'Log the id of the users',
            'default' => false,
        ]);

        return $parser;
    }

    public function init(): void
    {
        $this->limit = (int) $this->param('limit');
        if ($this->limit > static::MAX_QUERY_LIMIT) {
            $this->limit = static::DEFAULT_QUERY_LIMIT;
        }

        $this->logUserId = 'true' == $this->param('user-id');
    }

    public function main(): void
    {
        $this->init();

        $this->logger->info('Starting phone number format for users');

        $users = $this->fetchUsers();
        if (empty($users)) {
            $this->logger->info('There are not members to format');
        }

        /** @var UserModel $user */
        foreach ($users as $user) {
            if ($this->logUserId) {
                $this->logger->info(sprintf('Processing user id %s', $user->id()));
            }

            $currentMetadata = $user->metadata();

            try {
                $formattedPhone = $this->formatter->format($user);
            } catch (Exception $exception) {
                $formattedPhone = '';
            }

            $twilioData = ['twilio' => ['phone_number' => $formattedPhone]];

            $data = ['metadata' => $currentMetadata->merge($twilioData)->toArray()];

            if (empty($formattedPhone)) {
                $data['phone'] = $formattedPhone;
            }

            try {
                if (!$this->updateUser($user->id(), $data)) {
                    $this->logger->error('Could not update user', ['userId' => $user->id()]);
                    exit(1);
                }
            } catch (Exception $exception) {
                $this->logger->error('Could not update user', ['userId' => $user->id(), 'msg' => $exception->getMessage()]);
                exit(1);
            }
        }

        $this->logger->info('Finishing phone number format for users');
    }

    private function fetchUsers(): array
    {
        return $this->usersRepository
            ->addCriteria(new Type(new UserTypesCollection([UserType::MEMBER()])))
            ->addCriteria(new BranchIdNotEqual(null))
            ->addCriteria(new HasPhoneNumber(true))
            ->addCriteria(new HasTwilioPhoneNumber(false))
            ->limit($this->limit)
            ->order(['active' => 1])
            ->find();
    }

    private function updateUser(string $id, array $data): bool
    {
        $result = $this->usersCollection
            ->updateOne(
                ['_id' => new ObjectId($id)],
                ['$set' => $data],
            );

        return $result->getModifiedCount() === 1;
    }
}
