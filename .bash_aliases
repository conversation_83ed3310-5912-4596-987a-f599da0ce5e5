alias glofox-app-bash="docker exec glofox_app bash"
alias glofox-app-composer-update="docker exec glofox_app bash -c 'composer update'"
alias glofox-app-composer-install="docker exec glofox_app bash -c 'rm -f composer.lock && composer install'"
alias glofox-app-composer-dumpautoload="docker exec glofox_app bash -c 'composer dump-autoload'"
alias glofox-app-db="docker exec -it glofox_db mongo"
alias glofox-app-db-bash="docker exec -it glofox_db bash"
#alias glofox-app-run="docker exec glofox_app bash -c '\"$@\"'"
