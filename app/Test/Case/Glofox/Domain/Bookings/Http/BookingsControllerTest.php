<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\Bookings\Http;

use Aws\Result;
use CakeTestCases\Glofox\Domain\Users\Traits\AuthenticateUsersTrait;
use Carbon\Carbon;
use ClassRegistry;
use Glofox\Domain\BookingRequests\Models\BookingRequest;
use Glofox\Domain\BookingRequests\Models\BookingRequestStatus;
use Glofox\Domain\BookingRequests\Models\BookingRequestType;
use Glofox\Domain\BookingRequests\Services\BookingRequestCreatorService;
use Glofox\Domain\BookingRequests\Services\BookingRequestsPollingService;
use Glofox\Domain\Bookings\Models\Booking;
use Glofox\Domain\Bookings\Repositories\BookingsRepository;
use Glofox\Domain\Bookings\Services\BookingsPublisher;
use Glofox\Domain\Bookings\Status;
use Glofox\Domain\FeatureFlags\FeatureFlagInterface;
use Glofox\Domain\Locker\EventBookingLockerInterface;
use Glofox\Domain\Wallets\Services\Responses\SettingsResponse;
use Glofox\Domain\Wallets\Services\WalletsClient;
use Glofox\Eventkit\Publisher\DomainEventPublisher;
use Glofox\Infrastructure\Flags\Flag;
use Glofox\Infrastructure\Flags\Flagger;
use MabeEnum\Enum;
use Mockery;

\App::import('Test/Case', 'GlofoxControllerTestCase');

class BookingsControllerTest extends \GlofoxControllerTestCase
{
    use AuthenticateUsersTrait;

    public $fixtures = [
        'app.payment_method',
        'app.payment_provider',
        'app.branch',
        'app.user',
        'app.client',
        'app.membership',
        'app.event',
        'app.booking',
        'app.program',
        'app.time_slot_pattern',
        'app.time_slot',
        'app.user_credit',
        'app.integrator',
        'app.course',
        'app.facility',
    ];

    public function setUp(): void
    {
        parent::setUp();

        $this->mockBookingAppointmentLocker();
    }

    public function tearDown(): void
    {
        parent::tearDown();

        app()->forgetInstance(FeatureFlagInterface::class);
        app()->forgetInstance(DomainEventPublisher::class);
        app()->forgetInstance(Flagger::class);
        app()->forgetInstance(BookingsRepository::class);

        Mockery::close();
    }

    public function test_creating_booking_or_reservation_returns_400_if_program_id_is_not_sent(): void
    {
        $this->authenticateAsAdmin();
        $memberId = '5e7cb75c3d36311bf52c63c9';
        $response = $this->testAction("/2.2/users/$memberId/bookings", [
            'data' => [
                'start_time' => 123,
                'schedule_code' => 'foo',
                'bookable' => true,
                'reservable' => false,
            ],
            'method' => 'post',
        ]);

        $result = json_decode($response, true, 512, JSON_THROW_ON_ERROR);
        self::assertFalse($result['success']);
        self::assertEquals('The program id field is required.', $result['message']);
        self::assertSame(400, $this->response->statusCode());
    }

    public function test_creating_booking_or_reservation_returns_400_if_start_time_is_not_sent(): void
    {
        $this->authenticateAsAdmin();
        $memberId = '5e7cb75c3d36311bf52c63c9';
        $response = $this->testAction("/2.2/users/$memberId/bookings", [
            'data' => [
                'program_id' => '123',
                'schedule_code' => 'foo',
                'bookable' => true,
                'reservable' => false,
            ],
            'method' => 'post',
        ]);

        $result = json_decode($response, true, 512, JSON_THROW_ON_ERROR);
        self::assertFalse($result['success']);
        self::assertEquals('The start time field is required.', $result['message']);
        self::assertSame(400, $this->response->statusCode());
    }

    public function test_creating_booking_or_reservation_returns_400_if_bookable_is_not_sent(): void
    {
        $this->authenticateAsAdmin();
        $memberId = '5e7cb75c3d36311bf52c63c9';
        $response = $this->testAction("/2.2/users/$memberId/bookings", [
            'data' => [
                'program_id' => '123',
                'schedule_code' => 'foo',
                'start_time' => 123,
                'reservable' => true,
            ],
            'method' => 'post',
        ]);

        $result = json_decode($response, true, 512, JSON_THROW_ON_ERROR);
        self::assertFalse($result['success']);
        self::assertEquals('The bookable field is required.', $result['message']);
        self::assertSame(400, $this->response->statusCode());
    }

    public function test_creating_booking_or_reservation_returns_400_if_reservable_is_not_sent(): void
    {
        $this->authenticateAsAdmin();
        $memberId = '5e7cb75c3d36311bf52c63c9';
        $response = $this->testAction("/2.2/users/$memberId/bookings", [
            'data' => [
                'program_id' => '123',
                'schedule_code' => 'foo',
                'start_time' => 123,
                'bookable' => true,
            ],
            'method' => 'post',
        ]);

        $result = json_decode($response, true, 512, JSON_THROW_ON_ERROR);
        self::assertFalse($result['success']);
        self::assertEquals('The reservable field is required.', $result['message']);
        self::assertSame(400, $this->response->statusCode());
    }

    public function test_creating_booking_or_reservation_returns_400_if_schedule_code_is_not_sent(): void
    {
        $this->authenticateAsAdmin();
        $memberId = '5e7cb75c3d36311bf52c63c9';
        $response = $this->testAction("/2.2/users/$memberId/bookings", [
            'data' => [
                'program_id' => '123',
                'start_time' => 123,
                'bookable' => true,
                'reservable' => false,
            ],
            'method' => 'post',
        ]);

        $result = json_decode($response, true, 512, JSON_THROW_ON_ERROR);
        self::assertFalse($result['success']);
        self::assertEquals('The schedule code field is required.', $result['message']);
        self::assertSame(400, $this->response->statusCode());
    }

    public function test_creating_booking_or_reservation_returns_404_if_the_user_does_not_exist(): void
    {
        $this->authenticateAsAdmin();
        $memberId = '1e7cb75c3d36311bf52c63c9';

        $response = $this->testAction("/2.2/users/$memberId/bookings", [
            'data' => [
                'program_id' => '123',
                'start_time' => 123,
                'schedule_code' => 'foo',
                'bookable' => true,
                'reservable' => false,
            ],
            'method' => 'post',
        ]);

        $result = json_decode($response, true, 512, JSON_THROW_ON_ERROR);
        self::assertFalse($result['success']);
        self::assertEquals('User not found - id: 1e7cb75c3d36311bf52c63c9', $result['message']);
        self::assertSame(404, $this->response->statusCode());
    }

    public function test_creating_booking_or_reservation_returns_404_if_the_program_does_not_exist(): void
    {
        $this->authenticateAsAdmin();
        $memberId = '5e7cb75c3d36311bf52c63c9';

        $response = $this->testAction("/2.2/users/$memberId/bookings", [
            'data' => [
                'program_id' => '1e7cb75c3d36311bf52c63c9',
                'start_time' => 123,
                'schedule_code' => 'foo',
                'bookable' => true,
                'reservable' => false,
            ],
            'method' => 'post',
        ]);

        $result = json_decode($response, true, 512, JSON_THROW_ON_ERROR);
        self::assertFalse($result['success']);
        self::assertEquals('Program not found - id: 1e7cb75c3d36311bf52c63c9', $result['message']);
        self::assertSame(400, $this->response->statusCode());
    }

    public function test_creating_booking_or_reservation_returns_exception_if_the_event_is_not_found(): void
    {
        $this->authenticateAsAdmin();
        $memberId = '5e7cb75c3d36311bf52c63c9';
        $startTimeForEventThatDoesNotExist = new \DateTime('last Sunday 8am');

        $response = $this->testAction("/2.2/users/$memberId/bookings", [
            'data' => [
                'program_id' => '5b5b28a73355dafa1a354d88',
                'start_time' => $startTimeForEventThatDoesNotExist->getTimestamp(),
                'schedule_code' => 'foo',
                'bookable' => true,
                'reservable' => false,
            ],
            'method' => 'post',
        ]);

        $result = json_decode($response, true, 512, JSON_THROW_ON_ERROR);
        self::assertFalse($result['success']);
        self::assertEquals('EVENT_NOT_FOUND_AT_REQUESTED_TIME', $result['message']);
        self::assertSame(404, $this->response->statusCode());
    }

    public function test_creating_booking_or_reservation_returns_booking_when_booking_is_made(): void
    {
        $this->authenticateAsAdmin();
        $memberId = '5e7cb75c3d36311bf52c63c9';
        $startTimeForEventThatExists = Carbon::parse('this Sunday 8am', 'Europe/Dublin');
        $startTimeForEventThatExists = Carbon::createFromTimestamp($startTimeForEventThatExists->getTimestamp(), 'UTC');

        $walletClient = Mockery::mock(WalletsClient::class);
        $walletClient
            ->shouldReceive('getWalletSettings')
            ->andReturn(SettingsResponse::fromArray(collect()));

        app()->instance(WalletsClient::class, $walletClient);

        $bookingRequest = new BookingRequest();
        $bookingRequest->put('status', BookingRequestStatus::PROCESSED);

        $bookingRequestCreatorService = Mockery::mock(BookingRequestCreatorService::class);
        $bookingRequestCreatorService
            ->shouldReceive('execute')
            ->andReturn($bookingRequest)
            ->once();

        app()->instance(BookingRequestCreatorService::class, $bookingRequestCreatorService);

        $bookingsPublisher = Mockery::mock(BookingsPublisher::class);
        $bookingsPublisher->shouldReceive('sendBookingFinishedEvent');

        app()->instance(BookingsPublisher::class, $bookingsPublisher);

        $response = $this->testAction("/2.2/users/$memberId/bookings", [
            'data' => [
                'program_id' => '5b5b28a73355dafa1a354d88',
                'start_time' => $startTimeForEventThatExists->getTimestamp(),
                'schedule_code' => '5856c03455fd1',
                'bookable' => true,
                'reservable' => false,
            ],
            'method' => 'post',
        ]);

        $result = json_decode($response, true, 512, JSON_THROW_ON_ERROR);
        self::assertEquals(Status::BOOKED, $result['status']);
        self::assertEquals($memberId, $result['user_id']);
        self::assertEquals($startTimeForEventThatExists->getTimestamp(), $result['time_start']);
        self::assertSame(201, $this->response->statusCode());

        app()->forgetInstance(WalletsClient::class);
        app()->forgetInstance(BookingsPublisher::class);
        app()->forgetInstance(BookingRequestCreatorService::class);
    }

    public function test_creating_booking_or_reservation_returns_failure_if_booking_request_is_rejected(): void
    {
        $this->authenticateAsAdmin();
        $memberId = '5e7cb75c3d36311bf52c63c9';
        $startTimeForEventThatExists = Carbon::parse('this Sunday 8am', 'Europe/Dublin');
        $startTimeForEventThatExists = Carbon::createFromTimestamp($startTimeForEventThatExists->getTimestamp(), 'UTC');

        $bookingRequest = new BookingRequest();
        $bookingRequest->put('status', BookingRequestStatus::REJECTED);

        $bookingRequestCreatorService = Mockery::mock(BookingRequestCreatorService::class);
        $bookingRequestCreatorService
            ->shouldReceive('execute')
            ->andReturn($bookingRequest)
            ->once();

        app()->instance(BookingRequestCreatorService::class, $bookingRequestCreatorService);

        $bookingsPublisher = Mockery::mock(BookingsPublisher::class);
        $bookingsPublisher->shouldReceive('sendBookingFinishedEvent');

        app()->instance(BookingsPublisher::class, $bookingsPublisher);

        $response = $this->testAction("/2.2/users/$memberId/bookings", [
            'data' => [
                'program_id' => '5b5b28a73355dafa1a354d88',
                'start_time' => $startTimeForEventThatExists->getTimestamp(),
                'schedule_code' => '5856c03455fd1',
                'bookable' => true,
                'reservable' => false,
            ],
            'method' => 'post',
        ]);

        $result = json_decode($response, true, 512, JSON_THROW_ON_ERROR);
        self::assertFalse($result['success']);
        self::assertEquals('Booking request has been rejected while processing.', $result['message']);
        self::assertEquals('BOOKING_REQUEST_REJECTED', $result['message_code']);

        app()->forgetInstance(BookingRequestCreatorService::class);
        app()->forgetInstance(BookingsPublisher::class);
    }

    public function test_creating_booking_or_reservation_returns_400_if_bookable_and_reservable_are_false(): void
    {
        $this->authenticateAsAdmin();
        $memberId = '5e7cb75c3d36311bf52c63c9';
        $startTimeForEventThatExists = new \DateTime('this Sunday 8am');

        $response = $this->testAction("/2.2/users/$memberId/bookings", [
            'data' => [
                'program_id' => '5b5b28a73355dafa1a354d88',
                'start_time' => $startTimeForEventThatExists->getTimestamp(),
                'schedule_code' => '5856c03455fd1',
                'bookable' => false,
                'reservable' => false,
            ],
            'method' => 'post',
        ]);

        $result = json_decode($response, true, 512, JSON_THROW_ON_ERROR);
        self::assertFalse($result['success']);
        self::assertEquals('BOOKABLE_AND_RESERVABLE_ARE_NOT_TRUE', $result['message']);
        self::assertSame(200, $this->response->statusCode());
    }

    public function test_creating_reservation_when_event_exists(): void
    {
        $this->authenticateAsAdmin();
        $memberId = '5e7cb75c3d36311bf52c63c9';
        $startTimeForEventThatExists = Carbon::parse('this Sunday 8am', 'Europe/Dublin');
        $startTimeForEventThatExists = Carbon::createFromTimestamp($startTimeForEventThatExists->getTimestamp(), 'UTC');

        $bookingRequest = new BookingRequest();
        $bookingRequest->put('status', BookingRequestStatus::PROCESSED);

        $pollingService = Mockery::mock(BookingRequestsPollingService::class);
        $pollingService->shouldReceive('poll')
            ->withArgs(
                function (string $userId, string $correlationId, Enum $type, string $messageId) use ($memberId) {
                    $this->assertSame($memberId, $userId);
                    $this->assertSame('79b7012a05c677c9a512504d', $correlationId);
                    $this->assertSame(BookingRequestType::BOOKING(), $type);
                    $this->assertSame('1-2-3', $messageId);

                    return true;
                }
            )
            ->andReturn($bookingRequest);

        app()->instance(BookingRequestsPollingService::class, $pollingService);

        $response = $this->testAction("/2.2/users/$memberId/bookings", [
            'data' => [
                'program_id' => '5b5b28a73355dafa1a354d88',
                'start_time' => $startTimeForEventThatExists->getTimestamp(),
                'schedule_code' => '5856c03455fd1',
                'bookable' => false,
                'reservable' => true,
            ],
            'method' => 'post',
        ]);

        $result = json_decode($response, true, 512, JSON_THROW_ON_ERROR);
        $this->assertSame(201, $this->response->statusCode());
        $this->assertEquals('RESERVED', $result['status']);
        $this->assertEquals('5856c03455fd1', $result['schedule_code']);
        $this->assertEquals('79b7012a05c677c9a512504d', $result['event_id']);
        $this->assertArrayHasKey('_id', $result);

        $bookingId = $result['_id'];
        $savedBooking = $this->getSavedBooking($bookingId);

        $this->assertEquals($memberId, $savedBooking->userId());
        $this->assertEquals($startTimeForEventThatExists->getTimestamp(), $savedBooking->startingTime()->getTimestamp());
        $this->assertEquals('5856c03455fd1', $savedBooking->scheduleCode());
        $this->assertEquals('5b5b28a73355dafa1a354d88', $savedBooking->programId());
        $this->assertEquals('79b7012a05c677c9a512504d', $savedBooking->eventId());
        $this->assertEquals('RESERVED', $savedBooking->status()->getValue());
        $this->assertEquals('program for testing recurring bookings', $savedBooking->eventName());
        $this->assertEquals('program for testing recurring bookings', $savedBooking->modelName());
        $this->assertEmpty($savedBooking->batchId());

        app()->forgetInstance(BookingRequestsPollingService::class);
    }

    public function test_creating_reservation_when_event_does_not_exists(): void
    {
        $this->authenticateAsAdmin();
        $memberId = '5e7cb75c3d36311bf52c63c9';
        $startTimeForEventThatExists = new \DateTime('Sunday 8am + 4 weeks');

        $response = $this->testAction("/2.2/users/$memberId/bookings", [
            'data' => [
                'program_id' => '5b5b28a73355dafa1a354d88',
                'start_time' => $startTimeForEventThatExists->getTimestamp(),
                'schedule_code' => '5856c03455fd1',
                'bookable' => false,
                'reservable' => true,
            ],
            'method' => 'post',
        ]);

        $result = json_decode($response, true, 512, JSON_THROW_ON_ERROR);
        $this->assertSame(201, $this->response->statusCode());
        $this->assertEquals('RESERVED', $result['status']);
        $this->assertEquals('5856c03455fd1', $result['schedule_code']);
        $this->assertNull($result['event_id']);
        $this->assertArrayHasKey('_id', $result);

        $bookingId = $result['_id'];
        $savedBooking = $this->getSavedBooking($bookingId);

        $this->assertEquals($memberId, $savedBooking->userId());
        $this->assertEquals($startTimeForEventThatExists->getTimestamp(), $savedBooking->startingTime()->getTimestamp());
        $this->assertEquals('5856c03455fd1', $savedBooking->scheduleCode());
        $this->assertEquals('5b5b28a73355dafa1a354d88', $savedBooking->programId());
        $this->assertNull($savedBooking->eventId());
        $this->assertEquals('RESERVED', $savedBooking->status()->getValue());
    }

    public function testBookATimeSlotWithoutCharging(): void
    {
        Carbon::setTestNow("2024-10-25 09:00:00");

        $this->authenticateAsAdmin();

        $appointmentId = '62a70487b9be8ef0bf86d521';
        $userId = '59ba576ef09e52064e23414d';
        $staffId = '59a7011a05c677bda916612c';
        $branchId = '49a7011a05c677b9a916612a';

        $userCreditCakeModel = ClassRegistry::init('UserCredit');
        $creditsBeforeBooking = $userCreditCakeModel->findAllByBranchIdAndUserId($branchId, $userId);
        $this->assertCount(4, $creditsBeforeBooking);

        $bookingsPublisher = Mockery::mock(BookingsPublisher::class);
        $bookingsPublisher->shouldReceive('sendBookingCreatedEvent');
        app()->instance(BookingsPublisher::class, $bookingsPublisher);

        $response = $this->testAction('/2.1/bookings', [
            'method' => 'post',
            'data' => [
                'create_slot' => 'appointment',
                'user_id' => $userId,
                'model' => 'timeslot',
                'model_id' => $appointmentId,
                'time_start' => Carbon::now()->getTimestamp(),
                'staff_id' => $staffId,
                'private' => false,
                'branch_id' => $branchId,
                'payment_method' => 'credit_card',
                'price' => 5,
                'charge' => false,
            ],
        ]);

        $result = json_decode($response, true, 512, JSON_THROW_ON_ERROR);

        $this->assertEquals(200, $this->response->statusCode());
        $this->assertTrue($result['success']);
        $this->assertFalse($result['Booking']['paid']);
        $this->assertEquals('BOOKED', $result['Booking']['status']);
        $this->assertEquals('appointments', $result['Booking']['model']);
        $this->assertEquals($appointmentId, $result['Booking']['model_id']);
        $this->assertEquals($userId, $result['Booking']['user_id']);
        $this->assertNotEmpty($result['Booking']['time_slot_id']);
        $this->assertArrayNotHasKey('payment_method', $result['Booking']['metadata']);

        $creditsAfterBooking = $userCreditCakeModel->findAllByBranchIdAndUserId($branchId, $userId);
        $this->assertCount(4, $creditsAfterBooking);
        $this->assertSame($creditsBeforeBooking, $creditsAfterBooking);

        $bookingId = $result['Booking']['_id'];
        $savedBooking = $this->getSavedBooking($bookingId);

        $this->assertEquals($userId, $savedBooking->userId());
        $this->assertEquals($appointmentId, $savedBooking->modelId());
        $this->assertEquals('appointments', $savedBooking->model());
        $this->assertEquals($branchId, $savedBooking->branchId());
        $this->assertEquals('BOOKED', $savedBooking->status()->getValue());
        $this->assertFalse($savedBooking->paid());
        $this->assertNotEmpty($savedBooking->timeSlotId());
        $this->assertEmpty($savedBooking->paymentMethod());
        $this->assertEmpty($savedBooking->metadataObject()->getPaymentMethod());

        app()->forgetInstance(BookingsPublisher::class);
    }

    public function testItShouldFailWhenIntegratorTriesToBookAnEventFromOtherCorporateId(): void
    {
        Carbon::setTestNow("2024-10-25 09:00:00");

        $branchId = '49a7011a05c677b9a916612a';
        $this->authenticateAsIntegrator($branchId);
        $this->mockIntegratorChargeEnabledFlagger();

        $userId = '59ba576ef09e52064e23414d';
        $eventIdFromOtherCorporation = '62790d43840f2b6eee694503';

        $response = $this->testAction(sprintf('/2.3/branches/%s/bookings', $branchId), [
            'method' => 'post',
            'data' => [
                'user_id' => $userId,
                'model' => 'event',
                'model_id' => $eventIdFromOtherCorporation,
                'branch_id' => $branchId,
            ],
        ]);

        $result = json_decode($response, true, 512, JSON_THROW_ON_ERROR);

        self::assertEquals(400, $this->response->statusCode());
        self::assertFalse($result['success']);
        self::assertEquals('EVENT_DOES_NOT_BELONG_TO_BRANCH_CORPORATION', $result['message_code']);
        self::assertEquals('The event does not belong to the branch corporation', $result['message']);
    }

    public function testItShouldReturnA400StatusWhenUserIdInInvalidIn2dot1OrHigherApiVersion(): void
    {
        $this->authenticateAsAdmin();
        $branchId = '49a7011a05c677b9a916612a';

        $response = $this->testAction(sprintf('/2.3/branches/%s/bookings', $branchId), [
            'method' => 'post',
            'data' => [
                'user_id' => 'invalid_user_id',
                'model' => 'event',
                'model_id' => '62790d43840f2b6eee694503',
                'branch_id' => $branchId,
            ],
        ]);

        $result = json_decode($response, true, 512, JSON_THROW_ON_ERROR);
        $this->assertResponseReturnsStatusAndMessage($result, 400, 'Invalid User Id', 'INVALID_USER_ID');
    }

    public function testItShouldReturnA200StatusWhenUserIdInInvalidIn2dot0ApiVersion(): void
    {
        $this->authenticateAsAdmin();

        $response = $this->testAction('/2.0/bookings', [
            'method' => 'post',
            'data' => [
                'user_id' => 'invalid_user_id',
                'model' => 'event',
                'model_id' => '62790d43840f2b6eee694503',
                'branch_id' => '49a7011a05c677b9a916612a',
            ],
        ]);

        $result = json_decode($response, true, 512, JSON_THROW_ON_ERROR);

        $this->assertResponseReturnsStatusAndMessage($result, 200, 'Invalid User Id', 'INVALID_USER_ID');
    }

    public function testItShouldReturnA200StatusWhenUserIdInInvalidInNotVersionedApi(): void
    {
        $this->authenticateAsAdmin();

        $response = $this->testAction('bookings/add_web_admin', [
            'method' => 'post',
            'data' => [
                'user_id' => 'invalid_user_id',
                'model' => 'event',
                'event_id' => '62790d43840f2b6eee694503',
                'branch_id' => '49a7011a05c677b9a916612a',
            ],
        ]);

        $result = json_decode($response, true, 512, JSON_THROW_ON_ERROR);

        $this->assertResponseReturnsStatusAndMessage($result, 200, 'Invalid User Id', 'INVALID_USER_ID');
    }

    /**
     * @dataProvider bookAgnosticPayloadsValidationProvider
     */
    public function testBookAgnosticPayloadsValidation(array $payload, array $expectedValidations, bool $impersonateAsMember = false, ?string $memberId = null): void
    {
        Carbon::setTestNow("2024-10-25 09:00:00");

        $branchId = '49a7011a05c677b9a916612a';
        $memberId ??= '59ba576ef09e52064e23414d';

        if ($impersonateAsMember) {
            $this->mockActiveLimitFlagger();
            $this->authenticateAsIntegratorMember($branchId, $memberId);
        } else {
            $this->mockIntegratorChargeEnabledFlagger();
            $this->authenticateAsIntegrator($branchId);
        }

        $this->mockBookingFinishedEventFeatureFlagForCourses();
        $this->mockDomainEventPublisher();

        $response = $this->testAction(sprintf('/2.3/branches/%s/bookings', $branchId), [
            'method' => 'post',
            'data' => $payload,
        ]);

        $result = json_decode($response, true, 512, JSON_THROW_ON_ERROR);
        if (empty($expectedValidations)) {
            self::assertEquals(200, $this->response->statusCode());
            self::assertArrayNotHasKey('errors', $result);
        } else {
            self::assertEquals(400, $this->response->statusCode());
            self::assertFalse($result['success']);
            self::assertEquals($expectedValidations, $result['errors']);
        }
    }

    public function bookAgnosticPayloadsValidationProvider(): array
    {
        $timeslotId = '5ba8d934c4942047b524733a';
        $eventId = '29b7012a05c677c9a512503d';
        $courseId = '63e4a5eb27cbc28dc5b99ce5';
        $timeSlotFacilityId = '59832e82e1608a08210041b3';

        return [
            '1. It should return no validation when booking appointments payload is correct as member' => [
                [
                    'model_id' => $timeslotId,
                    'model' => 'timeslot',
                    'time_start' => Carbon::now()->getTimestamp(),
                    'staff_id' => '59a7011a05c677bda916612c',
                ],
                [],
                true
            ],
            '2. It should return no validation when booking appointments payload is correct as integrator' => [
                [
                    'model_id' => $timeslotId,
                    'model' => 'timeslot',
                    'time_start' => Carbon::now()->getTimestamp(),
                    'payment_method' => 'credit_card',
                    'staff_id' => '59a7011a05c677bda916612c',
                    'user_id' => '59ba576ef09e52064e23414d',
                ],
                [],
            ],
            '3. It should return validation error when model data is not provided' => [
                [],
                [
                    'MODEL_IS_REQUIRED',
                    'MODEL_ID_IS_REQUIRED',
                ],
            ],
            '4. It should return validation errors when booking appointments with invalid payload as member' => [
                [
                    'model' => 'timeslot',
                    'model_id' => $timeslotId,
                ],
                [
                    'STAFF_ID_IS_REQUIRED',
                    'TIME_START_IS_REQUIRED',
                ],
                true
            ],
            '5. It should return validation errors when booking appointments with invalid payload as integrator' => [
                [
                    'model' => 'timeslot',
                    'model_id' => $timeslotId,
                ],
                [
                    'STAFF_ID_IS_REQUIRED',
                    'TIME_START_IS_REQUIRED',
                    'USER_ID_IS_REQUIRED',
                ],
            ],
            '6. It should return no validation when booking events payload is correct as member' => [
                [
                    'model_id' => $eventId,
                    'model' => 'event',
                    'guest_bookings' => 1,
                    'join_waiting_list' => false,
                ],
                [],
                true
            ],
            '7. It should return no validation when booking events payload is correct as integrator' => [
                [
                    'model_id' => $eventId,
                    'model' => 'event',
                    'user_id' => '59ba576ef09e52064e23414d',
                    'payment_method' => 'complimentary',
                    'price' => 0,
                    'guest_bookings' => 0,
                    'join_waiting_list' => false,
                    'charge' => true,
                ],
                [],
            ],
            '8. It should return validation errors when booking events with invalid payload as member' => [
                [
                    'model_id' => $eventId,
                    'model' => 'event',
                    'guest_bookings' => 'one',
                    'join_waiting_list' => 'yes',
                ],
                [
                    'INVALID_GUEST_BOOKINGS',
                    'JOIN_WAITING_LIST_MUST_BE_A_BOOLEAN',
                ],
                true
            ],
            '9. It should return validation errors when booking events with invalid payload as integrator' => [
                [
                    'model_id' =>  $eventId,
                    'model' => 'event',
                    'payment_method' => 'card',
                    'price' => '99,99',
                    'guest_bookings' => -1,
                    'join_waiting_list' => 'yes',
                    'charge' => 'no',
                ],
                [
                    'USER_ID_IS_REQUIRED',
                    'INVALID_PAYMENT_METHOD',
                    'INVALID_PRICE',
                    'GUEST_BOOKINGS_MUST_BE_AT_LEAST_0',
                    'JOIN_WAITING_LIST_MUST_BE_A_BOOLEAN',
                    'CHARGE_MUST_BE_A_BOOLEAN',
                ],
            ],
            '10. It should return no validation when booking courses payload is correct as member' => [
                [
                    'model_id' => $courseId,
                    'model' => 'course',
                    'schedule_id' => 1_663_080_966_272,
                    'guest_bookings' => 1,
                ],
                [],
                true,
                '5a1445fbc836ed252aba7c35'
            ],
            '11. It should return no validation when booking courses payload is correct as integrator' => [
                [
                    'model_id' => $courseId,
                    'model' => 'course',
                    'schedule_id' => '1663080966272',
                    'user_id' => '5a1445fbc836ed252aba7c35',
                    'payment_method' => 'credit_card',
                    'guest_bookings' => 1,
                    'price' => 5.05,
                ],
                [],
            ],
            '12. It should return validation errors when booking courses with invalid payload as member' => [
                [
                    'model_id' => $courseId,
                    'model' => 'course',
                    'guest_bookings' => '1.5',
                ],
                [
                    'SCHEDULE_ID_IS_REQUIRED',
                    'INVALID_GUEST_BOOKINGS',
                ],
                true
            ],
            '13. It should return validation errors when booking courses with invalid payload as integrator' => [
                [
                    'model_id' => $courseId,
                    'model' => 'course',
                    'payment_method' => 'cash',
                    'guest_bookings' => 1.5,
                    'price' => '5.05€',
                ],
                [
                    'SCHEDULE_ID_IS_REQUIRED',
                    'USER_ID_IS_REQUIRED',
                    'INVALID_PAYMENT_METHOD',
                    'INVALID_GUEST_BOOKINGS',
                    'INVALID_PRICE',
                ],
            ],
            '14. It should return no validation when booking facilities payload is correct as member' => [
                [
                    'model_id' => $timeSlotFacilityId,
                    'model' => 'facility',
                    'guest_bookings' => 0,
                ],
                [],
                true
            ],
            '15. It should return no validation when booking facilities payload is correct as integrator' => [
                [
                    'model_id' => $timeSlotFacilityId,
                    'model' => 'facility',
                    'guest_bookings' => 0,
                    'payment_method' => 'credit_card',
                    'user_id' => '59ba576ef09e52064e23414d',
                ],
                [],
            ],
            '16. It should return validation errors when booking facilities with invalid payload as member' => [
                [
                    'model' => 'facility',
                ],
                [
                    'MODEL_ID_IS_REQUIRED',
                ],
                true
            ],
            '17. It should return validation errors when booking facilities with invalid payload as integrator' => [
                [
                    'model' => 'facility',
                    'model_id' => $timeSlotFacilityId,
                ],
                [
                    'USER_ID_IS_REQUIRED',
                ],
            ],
        ];
    }

    private function mockBookingAppointmentLocker(): void
    {
        $bookingAppointmentLocker = Mockery::mock(EventBookingLockerInterface::class);
        $bookingAppointmentLocker->shouldReceive('lock')->zeroOrMoreTimes();
        $bookingAppointmentLocker->shouldReceive('unlock')->zeroOrMoreTimes();
        $bookingAppointmentLocker
            ->shouldReceive('getEventIdFromAppointment')
            ->andReturn('test-event')
            ->zeroOrMoreTimes();

        app()->forgetInstance(EventBookingLockerInterface::class);
        app()->instance(EventBookingLockerInterface::class, $bookingAppointmentLocker);
    }

    private function mockActiveLimitFlagger(): void
    {
        $irisActiveBookingsLimitFlagger = Mockery::mock(Flagger::class);
        $irisActiveBookingsLimitFlagger
            ->shouldReceive('withFlag')
            ->with(Flag::ACTIVE_BOOKINGS_LIMIT())
            ->andReturnSelf()
            ->shouldReceive('hasByBranchId')
            ->withAnyArgs()
            ->andReturnFalse()
            ->zeroOrMoreTimes();
        app()->instance(Flagger::class, $irisActiveBookingsLimitFlagger);
    }

    private function mockIntegratorChargeEnabledFlagger(): void
    {
        $irisActiveBookingsLimitFlagger = Mockery::mock(Flagger::class);
        $irisActiveBookingsLimitFlagger
            ->shouldReceive('withFlag')
            ->with(Flag::INTEGRATOR_ENABLE_CHARGE_IRIS())
            ->andReturnSelf()
            ->shouldReceive('hasByBranchId')
            ->withAnyArgs()
            ->andReturnFalse()
            ->zeroOrMoreTimes();
        app()->instance(Flagger::class, $irisActiveBookingsLimitFlagger);
    }

    private function mockBookingFinishedEventFeatureFlagForCourses(): void
    {
        $featureFlag = Mockery::mock(FeatureFlagInterface::class);
        $featureFlag
            ->shouldReceive('withFlag')
            ->andReturnSelf()
            ->getMock()
            ->shouldReceive('hasByBranchId')
            ->andReturnTrue();

        app()->instance(FeatureFlagInterface::class, $featureFlag);
    }

    private function mockDomainEventPublisher(): void
    {
        $awsResult = new Result(['MessageId' => 'test-result-message']);

        $domainEventPublisher = Mockery::mock(DomainEventPublisher::class);
        $domainEventPublisher
            ->shouldReceive('publish')
            ->withAnyArgs()
            ->andReturn($awsResult)
            ->zeroOrMoreTimes();
        app()->instance(DomainEventPublisher::class, $domainEventPublisher);
    }

    private function getSavedBooking($bookingId): Booking
    {
        return app()->make(BookingsRepository::class)->getById($bookingId);
    }

    private function assertResponseReturnsStatusAndMessage(array $result, int $status, string $message, string $messageCode): void
    {
        self::assertEquals($status, $this->response->statusCode());
        self::assertFalse($result['success']);
        self::assertEquals($message, $result['message']);
        self::assertEquals($messageCode, $result['message_code']);
    }
}
