<?php

namespace Glofox\Authentication\PasswordRules;

use Glofox\Domain\Users\Models\User;
use UnsuccessfulOperation;

class PasswordIsStrong implements PasswordRuleInterface
{
    private ?string $message = null;
    private ?string $messageCode = null;
    private int $minLength = 8;

    public function check(User $target, string $password): bool
    {
        if (strlen($password) < $this->minLength) {
            $this->message = 'Your password must be at least 8 characters long';;
            $this->messageCode = 'PASSWORD_RULE_LENGTH';

            return false;
        }

        if (!preg_match("/[A-Z]+/", $password)) {
            $this->message = 'Your password must contain at least an upper case letter';
            $this->messageCode = 'PASSWORD_RULE_UPPER_CASE';

            return false;
        }

        if (!preg_match("/[a-z]+/", $password)) {
            $this->message = 'Your password must contain at least a lower case letter';
            $this->messageCode = 'PASSWORD_RULE_LOWER_CASE';

            return false;
        }

        if (!preg_match("/[0-9]+/", $password)) {
            $this->message = 'Your password must contain at least one digit';
            $this->messageCode = 'PASSWORD_RULE_DIGIT';

            return false;
        }

        // @see https://stackoverflow.com/a/8359631/1891542
        if (!preg_match("/[!$%^&#@*()_\-+|~=`{}\[\]:\";'<>?,.\/]/", $password)) {
            $this->message = 'Your password must contain at least a special character';
            $this->messageCode = 'PASSWORD_RULE_SPECIAL_CHARACTER';

            return false;
        }

        return true;
    }

    public function getMessage(): string
    {
        return $this->message;
    }

    public function getMessageCode(): string
    {
        return $this->messageCode;
    }
}
