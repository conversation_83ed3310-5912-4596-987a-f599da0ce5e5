<?php

namespace Glofox\Repositories\Search\Expressions;

use Glofox\Repositories\Filters\ConditionsCollection;
use Glofox\Repositories\Repository;
use Illuminate\Support\Collection;

trait HasConditions
{
    abstract public function field() : string;

    abstract protected function operatorResolver() : MongoOperatorResolver;

    abstract protected function dbValueFromExpression();

    public function generateConditions() : Collection
    {
        $operator = $this->operatorResolver()->resolve(
            $this->getExpression()
        );

        $value = $this->dbValueFromExpression();

        $condition = collect([
            $this->field() => $this->generateValueObject($operator, $value)
        ]);

        return $condition;
    }

    public function applyToConditions(ConditionsCollection $conditions, Repository $repository = null)
    {
        $condition = $this->generateConditions();

        $conditions->push($condition->toArray());
    }

    public function generateValueObject(string $operator, $value)
    {
        // mongo 2.4 compatibility
        if ($operator === '$eq') {
            return $value;
        }

        if (is_array($value)) {
            if (array_key_exists('$regex', $value)) {
                return $value;
            }
        }

        return [
            $operator => $value
        ];
    }
}
