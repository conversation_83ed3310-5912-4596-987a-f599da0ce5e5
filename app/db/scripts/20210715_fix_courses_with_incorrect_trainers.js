var limitNo = 1000;
var bulk = db.courses.initializeUnorderedBulkOp();
var noOfDocsToProcess = db.courses.find({
    "active": true, "schedule.end_date": {$gte: new ISODate("2021-07-14T00:00:00Z")}
}).count();

db.courses.find({
    "active": true,
    "schedule.end_date": {$gte: new ISODate("2021-07-14T00:00:00Z")}
}).sort({$natural: 1}).limit(limitNo).forEach(function (document) {
    noOfDocsToProcess--;
    limitNo--;
    var toUpdate = false;

    _.each(document.schedule, function (schedule) {
        _.each(schedule.days, function (day) {
            var trainerArr = [];
            if (day._trainers != undefined) {
                _.each(day._trainers, function (trainer) {
                    trainerArr.push(trainer.id);
                });
                day.trainers = trainerArr;
                toUpdate = true
            }
        });
    });

    if (toUpdate) {
        bulk.find({_id: document._id}).updateOne({$set: document});
    }

    if (limitNo === 0 || noOfDocsToProcess === 0) {
        bulk.execute();
        bulk = db.courses.initializeUnorderedBulkOp();
        limitNo = 1000;
    }
});