<?php

use Glofox\Response\Models\Payouts\PayoutModel;

class PayoutModelTest extends GlofoxTestCase
{
    /**
     * @param $comparisonArray
     * @param $result
     * @dataProvider modelProvider
     */
    public function testModel($comparisonArray, $result)
    {
        $model = new PayoutModel($comparisonArray);
        $this->assertEquals($model, $result);
    }

    public function modelProvider(): array
    {
        $time = strtotime('now');

        return [
            'Simple model, without transaction or totals' => [
                [
                    'id' => '123',
                    'amount' => 353,
                    'status' => 'paid',
                    'currency' => 'eur',
                    'statement' => 'test',
                    'arrival_date' => $time,
                    'created' => $time,
                    'transactions' => null,
                    'totals' => null,
                ],
                new PayoutModel(
                    [
                        'id' => '123',
                        'amount' => 353,
                        'status' => 'paid',
                        'currency' => 'eur',
                        'statement' => 'test',
                        'arrival_date' => $time,
                        'created' => $time,
                        'transactions' => null,
                        'totals' => null,
                    ]
                ),
            ],
        ];
    }
}
