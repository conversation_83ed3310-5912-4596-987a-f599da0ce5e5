<?php

namespace Glofox\Repositories\Search\Expressions;

use Glofox\Repositories\Filters\ConditionsCollection;
use Glofox\Repositories\Repository;
use Illuminate\Support\Collection;

interface Conditionable
{
    public function field() : string;

    public function evaluate($value);

    public function generateConditions() : Collection;

    public function applyToConditions(ConditionsCollection $conditions, Repository $repository = null);
}
