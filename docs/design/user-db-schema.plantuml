@startjson
!theme cerulean-outline

{
  "_id": "String",
  "email": "String",
  "login": "String",
  "password": "String",
  "first_name": "String",
  "last_name": "String",
  "name": "String",
  "phone": "String",
  "emergency_contact": "String",
  "birth": "Date",
  "gender": {
    "name": "String",
    "label": "String"
  },
  "type": "String",
  "active": "Boolean",
  "answers": ["String"],
  "categories": ["String"],
  "lead_status": "String",
  "WAIVER": "Boolean",
  "MEMBERPURCHASE": "Boolean",
  "PAYGPAYMENT": "Boolean",
  "SMS_WAIVER": "Boolean",
  "user_id": "String",
  "parent_id": "String",
  "use_parent_phone": "Boolean",
  "use_parent_email": "Boolean",
  "device": {
    "os": "String",
    "id": "String",
    "bundle": "String",
    "version": {
      "mayor": "Integer",
      "minor": "Integer",
      "revision": "Integer"
    }
  },
  "source": "String",
  "namespace": "String",
  "branch_id": "String | Array",
  "origin_branch_id": "String",
  "created": "Date",
  "modified": "Date",
  "joined_at": "Date",
  "consent": {
    "email": {
      "active": "Boolean",
      "modified_at": "Date",
      "modified_by_user_id": "String",
      "modified_from_id_address": ["String"],
      "message": "String"
    },
    "sms": {
      "active": "Boolean",
      "modified_at": "Date",
      "modified_by_user_id": "String",
      "modified_from_id_address": ["String"],
      "message": "String"
    },
    "push": {
      "active": "Boolean",
      "modified_at": "Date",
      "modified_by_user_id": "String",
      "modified_from_id_address": ["String"],
      "message": "String"
    }
  },
  "metadata": {
    "insurance": {
      "policy_number": "String",
      "company_id": "String",
      "group_id": "String"
    },
    "gympass": {
      "id": "String"
    },
    "classpass": {
      "_id": "String"
    },
    "fiscal": {
      "tax_id": "String"
    },
    "twilio": {
      "phone_number": "String"
    }
  },
  "membership": {
    "_id": "String",
    "type": "String",
    "userMembershipId": "String",
    "startDate": "Date",
    "status": "String",
    "membershipGroupId": "String",
    "planCode": "String",
    "planPrice": "Double",
    "planUpfrontFee": "Double",
    "branchId": "String",
    "branchName": "String",
    "roamingEnabled": "Boolean",
    "branches": ["String"],
    "startOn": "String",
    "trial": "Boolean",
    "expiryDate": "Date",
    "bookedEvents": "Integer",
    "durationTimeUnit": "String",
    "durationTimeUnitCount": "Integer",
    "subscription": {
      "subscriptionPlanId": "String",
      "interval": "String",
      "intervalCount": "Integer",
      "price": "Double",
      "upfrontFee": "Double",
      "stripeId": "String",
      "paymentGatewayId": "String",
      "paymentMethodTypeId": "String",
      "autoRenewal": "Boolean",
      "forceStart": "Boolean",
      "pause": "Boolean",
      "duration": "Integer",
      "freeTimeUnitCount": "Integer",
      "endDate": "Date",
      "credits": {
        "branchId": "String",
        "namespace": "String",
        "active": "Boolean",
        "model": "String",
        "categoryId": "String",
        "numSesions": "Integer",
        "modelIds": "Array",
        "expiry": {
          "interval": "String",
          "intervalCount": "Integer"
        },
        "endDate": "Date"
      }
    }
  }
}

@endjson
