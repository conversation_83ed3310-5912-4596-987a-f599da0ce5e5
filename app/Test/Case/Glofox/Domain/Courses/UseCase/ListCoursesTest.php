<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\Courses\UseCase;

use App;
use Glofox\Domain\Branches\Exceptions\BranchNotFoundException;
use Glofox\Domain\Branches\Models\Branch;
use Glofox\Domain\Branches\Repositories\BranchesRepository;
use Glofox\Domain\Courses\Repositories\CoursesRepository;
use Glofox\Domain\Courses\UseCase\ListCourses;
use Glofox\Domain\Courses\UseCase\ListCoursesParams;
use Glofox\Domain\Courses\Repositories\GetAllByLocationParams;
use Glofox\Domain\Http\Request\BaseCriteria;
use GlofoxTestCase;
use Mockery;

App::import('Test/Case', 'GlofoxTestCase');

class ListCoursesTest extends GlofoxTestCase
{
    public $fixtures = [];
    private CoursesRepository $coursesRepo;
    private BranchesRepository $branchesRepo;

    public function setUp(): void
    {
        parent::setUp();
        $this->coursesRepo = Mockery::mock(CoursesRepository::class);
        $this->branchesRepo = Mockery::mock(BranchesRepository::class);
    }

    public function tearDown(): void
    {
        parent::tearDown();
        Mockery::close();
    }

    /**
     * @dataProvider executeDataProvider
     */
    public function testExecute(array $params, ?string $expectedException, array $expectations): void
    {
        $criteria = new BaseCriteria(
            $params['page'],
            $params['limit'],
            $params['sortBy'] ?? []
        );

        $listParams = new ListCoursesParams($params['location'], $criteria);
        $useCase = new ListCourses($this->branchesRepo, $this->coursesRepo);

        if ($expectedException) {
            $this->branchesRepo->shouldReceive('getById')
                ->once()
                ->with($params['location'])
                ->andThrow(
                    BranchNotFoundException::class,
                    sprintf('Branch not found - id: %s', $params['location'])
                );

            $this->expectExceptionMessage($expectedException);
            $useCase->execute($listParams);
            return;
        }

        $this->mockSuccessfulBranchLookup($params);
        $this->mockCoursesRepositoryResponses($expectations);

        $result = $useCase->execute($listParams);

        $this->assertEquals(count($expectations), $result->totalCount());
        $this->assertEquals($expectations, $result->data());
    }

    private function mockSuccessfulBranchLookup(array $params): void
    {
        $branch = Branch::make([
            '_id' => $params['location'],
            'timezone' => new \DateTimeZone($params['timezone'] ?? 'UTC'),
        ]);

        $this->branchesRepo->shouldReceive('getById')
            ->once()
            ->with($params['location'])
            ->andReturn($branch);
    }

    private function mockCoursesRepositoryResponses(array $courses): void
    {
        $this->coursesRepo->shouldReceive('getAllByLocationId')
            ->once()
            ->andReturn($courses);

        $this->coursesRepo->shouldReceive('countAllByLocationId')
            ->once()
            ->andReturn(count($courses));
    }

    public function executeDataProvider(): array
    {
        return [
            '1. Invalid location ID' => [
                'params' => [
                    'location' => 'invalid-location',
                    'timezone' => 'UTC',
                    'page' => 1,
                    'limit' => 50,
                ],
                'expectedException' => 'Branch not found - id: invalid-location',
                'expectations' => []
            ],
            '2. Valid location with no courses' => [
                'params' => [
                    'location' => 'valid-location-id',
                    'timezone' => 'UTC',
                    'page' => 1,
                    'limit' => 50,
                ],
                'expectedException' => null,
                'expectations' => []
            ],
            '3. Valid location with courses' => [
                'params' => [
                    'location' => 'valid-location-id',
                    'timezone' => 'UTC',
                    'page' => 1,
                    'limit' => 50,
                ],
                'expectedException' => null,
                'expectations' => [
                    ['_id' => 'course-1', 'name' => 'Course 1'],
                    ['_id' => 'course-2', 'name' => 'Course 2']
                ]
            ],
            '4. Courses with pagination (page 2)' => [
                'params' => [
                    'location' => 'valid-location-id',
                    'timezone' => 'UTC',
                    'page' => 2,
                    'limit' => 1,
                ],
                'expectedException' => null,
                'expectations' => [
                    ['_id' => 'course-3', 'name' => 'Course 3']
                ]
            ],
            '5. Courses with limit applied' => [
                'params' => [
                    'location' => 'valid-location-id',
                    'timezone' => 'UTC',
                    'page' => 1,
                    'limit' => 1,
                ],
                'expectedException' => null,
                'expectations' => [
                    ['_id' => 'course-1', 'name' => 'Course 1']
                ]
            ],
            '6. Courses sorted by name' => [
                'params' => [
                    'location' => 'valid-location-id',
                    'timezone' => 'UTC',
                    'page' => 1,
                    'limit' => 50,
                    'sortBy' => ['name']
                ],
                'expectedException' => null,
                'expectations' => [
                    ['_id' => 'course-a', 'name' => 'A Course'],
                    ['_id' => 'course-b', 'name' => 'B Course']
                ]
            ],
            '7. Courses sorted by multiple fields' => [
                'params' => [
                    'location' => 'valid-location-id',
                    'timezone' => 'UTC',
                    'page' => 1,
                    'limit' => 50,
                    'sortBy' => ['name', 'description']
                ],
                'expectedException' => null,
                'expectations' => [
                    ['_id' => 'course-x', 'name' => 'Alpha', 'description' => 'Zeta'],
                    ['_id' => 'course-y', 'name' => 'Beta', 'description' => 'Epsilon']
                ]
            ],
            '8. Location with schedule time string format' => [
                'params' => [
                    'location' => 'valid-location-id',
                    'timezone' => 'UTC',
                    'page' => 1,
                    'limit' => 50,
                ],
                'expectedException' => null,
                'expectations' => [
                    [
                        '_id' => 'course-10',
                        'name' => 'Morning Yoga',
                        'schedule' => [
                            [
                                'start_time' => '08:00',
                                'end_time' => '09:00'
                            ]
                        ]
                    ]
                ]
            ]
        ];
    }
}
