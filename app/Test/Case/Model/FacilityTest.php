<?php

App::import('Test/Case', 'GlofoxTestCase');
App::uses('Facility', 'Model');

use CakeTestCases\Glofox\Domain\Users\Traits\FetchUsersTrait;
use Glofox\Domain\Dictionaries\Models\Dictionary as DictionaryModel;
use Glofox\Domain\Dictionaries\Repositories\DictionariesRepository;
use Glofox\Domain\Facilities\Translation\TranslateOnlineFacility;

class FacilityTest extends GlofoxTestCase
{
    use FetchUsersTrait;

    public $fixtures = [
        'app.facility',
        'app.user',
    ];

    /** @var Facility */
    private $facilityCakeModel;

    /** @var TranslateOnlineFacility */
    private $translateOnlineFacility;

    public function setUp()
    {
        parent::setUp();

        $this->facilityCakeModel = ClassRegistry::init('Facility');
        $this->translateOnlineFacility = Mockery::mock(TranslateOnlineFacility::class);
    }

    public function tearDown()
    {
        parent::tearDown();

        unset($this->facilityCakeModel);
        Mockery::close();
    }

    public function test_after_find_translates_name_and_description_of_online_facilities_when_get_by_id()
    {
        $user = $this->fetchUser('5e9ed06cd575e6003c38648e');
        $facilityId = '5e9eb077d6d9123bef56c031';
        $expectedName = 'This is the name translation';
        $expectedDescription = 'This is the description translation';

        $this->translateOnlineFacility
            ->shouldReceive('translate')
            ->andReturn(\Glofox\Domain\Facilities\Models\Facility::make([
                'name' => $expectedName,
                'description' => $expectedDescription,
            ]))
            ->once();

        app()->instance(TranslateOnlineFacility::class, $this->translateOnlineFacility);

        $this->facilityCakeModel->setUpInjector([], $facilityId, $user);
        $result = $this->facilityCakeModel->getById($facilityId);

        self::assertSame($expectedName, $result['name']);
        self::assertSame($expectedDescription, $result['description']);

        app()->forgetInstance(TranslateOnlineFacility::class);
    }

    public function test_after_find_does_not_translates_neither_name_nor_description_of_on_site_facilities_when_get_by_id()
    {
        $user = $this->fetchUser('5e9ed06cd575e6003c38648e');
        $facilityId = '5e9eb077d6d9223bef56c031';
        $expectedName = 'EXPECT_TO_NOT_TRANSLATE_NAME';
        $expectedDescription = 'EXPECT_TO_NOT_TRANSLATE_DESCRIPTION';

        $this->translateOnlineFacility
            ->shouldNotReceive('translate');

        app()->instance(TranslateOnlineFacility::class, $this->translateOnlineFacility);

        $this->facilityCakeModel->setUpInjector([], $facilityId, $user);
        $result = $this->facilityCakeModel->getById($facilityId);

        self::assertSame($expectedName, $result['name']);
        self::assertSame($expectedDescription, $result['description']);

        app()->forgetInstance(TranslateOnlineFacility::class);
    }

    public function test_after_find_translates_name_and_description_of_online_facilities_when_get_all()
    {
        $user = $this->fetchUser('5e9ed06cd575e6003c38648e');
        $expectedTranslatedName = 'This is the name translation';
        $expectedTranslatedDescription = 'This is the description translation';
        $expectedName = 'EXPECT_TO_NOT_TRANSLATE_NAME';
        $expectedDescription = 'EXPECT_TO_NOT_TRANSLATE_DESCRIPTION';

        $this->translateOnlineFacility
            ->shouldReceive('translate')
            ->andReturn(\Glofox\Domain\Facilities\Models\Facility::make([
                'name' => $expectedTranslatedName,
                'description' => $expectedTranslatedDescription,
            ]))
            ->once();

        app()->instance(TranslateOnlineFacility::class, $this->translateOnlineFacility);

        $this->facilityCakeModel->setUpInjector([], null, $user);
        $result = $this->facilityCakeModel->getAll([]);

        foreach ($result['data'] as $facility) {
            if (true === $facility['is_online']) {
                self::assertSame($expectedTranslatedName, $facility['name']);
                self::assertSame($expectedTranslatedDescription, $facility['description']);
            } else {
                self::assertSame($expectedName, $facility['name']);
                self::assertSame($expectedDescription, $facility['description']);
            }
        }

        app()->forgetInstance(TranslateOnlineFacility::class);
    }

    public function test_after_find_translates_name_and_description_of_all__online_facilities_provided()
    {
        $user = $this->fetchUser('5e9ed06cd575e6003c38648e');

        $expectedTranslatedName = 'This is the name translation';
        $expectedTranslatedDescription = 'This is the description translation';
        $nameOnlineDictionaryKey = 'FACILITY_ONLINE_NAME';
        $descriptionOnlineDictionaryKey = 'FACILITY_ONLINE_DESCRIPTION';
        $expectedName = 'EXPECT_TO_NOT_TRANSLATE_NAME';
        $expectedDescription = 'EXPECT_TO_NOT_TRANSLATE_DESCRIPTION';

        $this->translateOnlineFacility
            ->shouldReceive('translate')
            ->andReturn(\Glofox\Domain\Facilities\Models\Facility::make([
                'name' => $expectedTranslatedName,
                'description' => $expectedTranslatedDescription,
            ]));

        app()->instance(TranslateOnlineFacility::class, $this->translateOnlineFacility);

        $this->facilityCakeModel->setUpInjector([], null, $user);
        $result = $this->facilityCakeModel->afterFind([
            [
                'Facility' => [
                    'is_online' => true,
                    'name' => $nameOnlineDictionaryKey,
                    'description' => $descriptionOnlineDictionaryKey
                ]
            ],
            [
                'Facility' => [
                    'is_online' => false,
                    'name' => 'EXPECT_TO_NOT_TRANSLATE_NAME',
                    'description' => 'EXPECT_TO_NOT_TRANSLATE_DESCRIPTION'
                ]
            ],
            [
                'Facility' => [
                    'is_online' => true,
                    'name' => $nameOnlineDictionaryKey,
                    'description' => $descriptionOnlineDictionaryKey
                ]
            ]
        ], true);

        foreach ($result as $facility) {
            if (true === $facility['Facility']['is_online']) {
                self::assertSame($expectedTranslatedName, $facility['Facility']['name']);
                self::assertSame($expectedTranslatedDescription, $facility['Facility']['description']);
            } else {
                self::assertSame($expectedName, $facility['Facility']['name']);
                self::assertSame($expectedDescription, $facility['Facility']['description']);
            }
        }

        app()->forgetInstance(TranslateOnlineFacility::class);
    }
}
