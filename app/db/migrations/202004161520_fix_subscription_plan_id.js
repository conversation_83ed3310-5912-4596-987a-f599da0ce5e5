
db.getCollection("subscription_plans").find({
	_id:{$type:"string"}
}).forEach(sub => {
    let copy = db.getCollection("subscription_plans").findOne({_id: sub._id},{_id:0});
    let newPlan = db.getCollection("subscription_plans").insertOne(copy);
    let objID = newPlan.insertedId.str;

    // Log the old and new subscription plan ids
    print("Old subscription plan id: " + sub._id)
    print("New subscription plan id: " + objID)

	db.getCollection("users").find(
	    {"membership.subscription.subscription_plan_id":sub._id}
    ).forEach(user => {
        // Log changed user
        print("Changed User id: "+user._id);
		db.getCollection("users").updateOne(
			{
				_id:user._id
			}, {
			$set:{
				"membership.subscription.subscription_plan_id": objID
			}
		});
	});

	db.getCollection("memberships").find(
			{"plans.subscription_plan_id":sub._id}
	).forEach(member => {
		for (x in member.plans) {
			if (member.plans[x].subscription_plan_id == sub._id) {
			    // Log changed membership
				print("Changed Membership " + member._id + " with plan code: " + member.plans[x].code);
				db.getCollection("memberships").updateOne(
					{ _id:member._id },
					{ $set:{ "plans.$[elem].subscription_plan_id": objID }},
					{ arrayFilters: [ { "elem.code": member.plans[x].code } ]}
				);
			}
		}
	});

 	db.getCollection("subscription_plans").deleteOne({_id:sub._id})
 	print("Subscription plan deleted")
	printjson(sub)
	print("_____________________");
});
