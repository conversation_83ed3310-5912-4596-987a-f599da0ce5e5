<?php

declare(strict_types=1);

namespace Glofox\Infrastructure\Services\Avatar;

use Glofox\Datasource\Exceptions\InvalidMongoIdException;
use Glofox\Domain\Authentication\Auth;
use Glofox\Request;
use Illuminate\Http\UploadedFile;
use Intervention\Image\Image;
use Intervention\Image\ImageManager;
use Psr\Log\LoggerInterface;
use Glofox\Domain\Users\Models\User;

abstract class BaseRequestImageParser
{
    private const HORIZONTAL = 'h';
    private const VERTICAL = 'v';
    private ImageManager $imageManager;
    private LoggerInterface $logger;

    public function __construct(
        ImageManager $imageManager,
        LoggerInterface $logger
    ) {
        $this->imageManager = $imageManager;
        $this->logger = $logger;
    }

    /**
     * @throws InvalidMongoIdException
     */
    public function parse(Request $request): ?Image
    {
        $file = $this->parseFromFile($request) ?? $this->parseFromBase64($request);
        if (empty($file)) {
            return null;
        }

        $exifData = [];
        if (function_exists('exif_read_data')) {
            $exifData = $this->getExifData($file);
        }

        $image = $this->imageManager->make($file);

        if (!empty($exifData) && isset($exifData['Orientation'])) {
            $image = $this->applyImageOrientation($image, $exifData['Orientation']);
        }
        return $image;
    }

    protected function getExifData($file): array
    {
        $exifData = false;
        try {
            $imageType = $this->readExifImageType($file);
            if ($imageType === IMAGETYPE_JPEG) {
                $exifData = $this->readExifData($file);
            }
        } catch (\Exception $e) {
            $error = $e->getMessage();
            $this->logger->info(sprintf('Error while reading exif data: %s', $error));
        }

        return $exifData !== false ? $exifData : [];
    }

    protected function readExifImageType($file)
    {
        $file = is_string($file) ? 'data://image/jpeg;base64,' . $file : $file->getRealPath();

        return exif_imagetype($file);
    }

    protected function readExifData($file)
    {
        $file = is_string($file) ? 'data://image/jpeg;base64,' . $file : $file->getRealPath();

        return exif_read_data($file);
    }

    protected function applyImageOrientation(Image $image, int $orientation): Image
    {
        switch ($orientation) {
            case 2:
                $image = $image->flip(self::HORIZONTAL);
                break;
            case 3:
                $image = $image->rotate(180);
                break;
            case 4:
                $image = $image->flip(self::VERTICAL);
                break;
            case 5:
                $image = $image->flip(self::HORIZONTAL);
                $image = $image->rotate(270);
                break;
            case 6:
                $image = $image->rotate(270);
                break;
            case 7:
                $image = $image->flip(self::VERTICAL);
                $image = $image->rotate(270);
                break;
            case 8:
                $image = $image->rotate(90);
                break;
            default:
                break;
        }
        return $image;
    }

    private function getAuthUser(): ?User
    {
        try {
            return Auth::user();
        } catch (\Exception $e) {
            return null;
        }
    }

    protected function parseFromFile(Request $request): ?UploadedFile
    {
        foreach ($this->getFileKeys() as $fileKey) {
            $file = $request->file($fileKey);
            if ($file instanceof UploadedFile) {
                return $file;
            }
        }

        return null;
    }

    protected function parseFromBase64(Request $request): ?string
    {
        $data = $request->data();

        foreach ($this->getBase64Parameters() as $base64Parameter) {
            $value = $request->get($base64Parameter) ?? data_get($data, $base64Parameter);
            if (\is_string($value)) {
                return $value;
            }
        }

        return null;
    }

    /**
     * @return array<int, string>
     */
    abstract protected function getFileKeys(): array;

    /**
     * @return array<int, string>
     */
    abstract protected function getBase64Parameters(): array;
}
