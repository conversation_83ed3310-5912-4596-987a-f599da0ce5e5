<?php

namespace CakeTestCases\Glofox\Payments\Services;

use CakeTestCases\Glofox\Domain\Users\Traits\FetchUsersTrait;
use Glofox\Domain\Authentication\Auth;
use Glofox\Domain\PaymentMethods\Models\PaymentMethod;
use Glofox\Domain\PaymentMethods\Models\Provider;
use Glofox\Domain\PaymentMethods\Repositories\PaymentMethodsRepository;
use Glofox\Domain\PaymentMethods\Type;
use Glofox\Domain\PaymentMethodUsers\Models\PaymentMethodUser;
use Glofox\Domain\PaymentMethodUsers\Repository\PaymentMethodUsersRepository;
use Glofox\Domain\SalesTaxes\ServiceType;
use Glofox\Domain\SalesTaxes\Services\PriceCalculatorClient;
use Glofox\Domain\Users\Lock\Locker;
use Glofox\Domain\Users\Lock\Resource;
use Glofox\Domain\Users\Models\User;
use Glofox\Domain\Users\Repositories\UsersRepository;
use Glofox\Domain\Wallets\Services\Responses\SettingsResponse;
use Glofox\Domain\Wallets\Services\WalletsClient;
use Glofox\Payments\Entities\Invoice\Contracts\InvoiceHandlerContract;
use Glofox\Payments\Entities\Invoice\Internal\CreateInvoice;
use Glofox\Payments\Entities\Invoice\Models\InvoiceEntity;
use Glofox\Payments\Entities\Invoice\Models\InvoiceEntityAutoCollectDetails;
use Glofox\Payments\Entities\Invoice\Models\InvoiceEntityLatestTransactionGroup;
use Glofox\Payments\Entities\Invoice\Models\InvoiceEntityLineItem;
use Glofox\Payments\Entities\Invoice\Models\InvoiceEntityLineItems;
use Glofox\Payments\Entities\Invoice\Models\InvoiceStatus;
use Glofox\Payments\Entities\Transaction\Models\Transaction;
use Glofox\Payments\Entities\Transaction\Status;
use Glofox\Payments\Exceptions\ChargeLockFailedException;
use Glofox\Payments\Services\CustomChargeService;
use Glofox\Payments\Services\CustomChargeUseCase;
use Ramsey\Uuid\Uuid;

\App::import('Test/Case', 'GlofoxTestCase');

class CustomChargeServiceTest extends \GlofoxTestCase
{

    use FetchUsersTrait;

    public $fixtures = [
        'app.user',
        'app.branch',
        'app.payment_provider',
        'app.payment_method',
    ];

    public function tearDown()
    {
        \Mockery::close();
        parent::tearDown();
    }

    public function test_could_not_acquire_lock(): void
    {
        $userId = 'mock-user-id';
        $branchId = 'mock-branch-id';

        $mockUser = \Mockery::mock(\User::class);
        $mockUser->shouldReceive('findById')
            ->withArgs(fn($id) => $id === $userId)
            ->andReturn([]);
        app()->instance(\User::class, $mockUser);

        $mockPriceCalculatorClient = \Mockery::mock(PriceCalculatorClient::class);
        app()->instance(PriceCalculatorClient::class, $mockPriceCalculatorClient);

        $userLocker = \Mockery::mock(Locker::class);
        $userLocker->shouldReceive('lock')
            ->withArgs(function (Resource $resource) use ($userId) {
                $this->assertSame($userId, $resource->getId());

                return true;
            })
            ->andReturn(false);
        app()->instance(Locker::class, $userLocker);

        /** @var CustomChargeService $service */
        $service = app()->make(CustomChargeService::class);

        $this->setExpectedException(ChargeLockFailedException::class);

        try {
            $service->execute(new CustomChargeUseCase(
                $userId,
                $branchId,
                'CASH',
                'description',
                null,
                10.99,
                false,
                [],
                [],
                User::make()
            ));
        } finally {
            // exception will be thrown above so make sure to forget the User after
            app()->forgetInstance(\User::class);
            app()->forgetInstance(PriceCalculatorClient::class);
            app()->forgetInstance(Locker::class);
        }
    }

    public function test_it_executes_charge(): void
    {
        $userId = '59a3011a05c677bda916612d';
        $paymentMethodType = Type::CASH;

        $user = $this->fetchUser($userId);

        $mockWalletClient = \Mockery::mock(WalletsClient::class);
        $mockWalletClient
            ->shouldReceive('getWalletSettings')
            ->andReturn(SettingsResponse::fromArray(collect()));

        $mockPriceCalculatorClient = \Mockery::mock(PriceCalculatorClient::class);
        $mockPriceCalculatorClient->shouldReceive('post')
            ->withArgs(function (string $endpointKey, array $endpointParams, string $endpointBody) {
                self::assertEquals('SET_ASSIGNED_TAXES_BY_ITEM', $endpointKey);
                self::assertEquals('49a7011a05c677b9a916612a', $endpointParams['studioId']); // TODO
                self::assertEquals(ServiceType::CUSTOM_CHARGES, $endpointParams['serviceType']);
                self::assertNotEquals('', $endpointParams['serviceId']);
                self::assertEquals(json_encode(['tax_ids' => ['tax-1']]), $endpointBody);

                return true;
            })
            ->andReturn(\Mockery::mock(\Illuminate\Http\JsonResponse::class));


        $paymentMethodId = new \MongoId();
        $paymentMethod = Paymentmethod::make([
            '_id' => $paymentMethodId,
            'type_id' => $paymentMethodType,
            'provider' => Provider::make([
                'account_id' => '54321',
            ]),
        ]);

        $paymentMethodUser = PaymentMethodUser::make([
            'customer_provider_id' => '12345'
        ]);

        $invoiceId = Uuid::uuid4()->toString();
        $transactionResponse = (new Transaction())
            ->setId(Uuid::uuid4()->toString())
            ->setStatus(Status::PAYMENT_STATUS_SUCCESS)
            ->setType(\Glofox\Payments\Entities\Transaction\Type::TRANSACTION_TYPE_CHARGE)
            ->setAmount(1099)
            ->setCurrency('EUR')
            ->setInvoiceId($invoiceId);
        $transactionGroup = (new InvoiceEntityLatestTransactionGroup())->setTransactions([$transactionResponse]);
        $invoiceResponse = (new InvoiceEntity())
            ->setId($invoiceId)
            ->setStatus(InvoiceStatus::PAID())
            ->setAmount(1099)
            ->setCurrency('EUR')
            ->setDestinationAccountId($paymentMethod->provider()->accountId())
            ->setCustomerAccountId($paymentMethodUser->customerProviderId())
            ->setLatestTrxGroup($transactionGroup);

        $paymentMethodRepo = \Mockery::mock(PaymentMethodsRepository::class);
        $paymentMethodRepo->shouldReceive('setReadConcern')->andReturnSelf();
        $paymentMethodRepo->shouldReceive('addCriteria')->andReturn($paymentMethodRepo);
        $paymentMethodRepo->shouldReceive('firstOrFail')->andReturn($paymentMethod);

        $paymentMethodUsersRepo = \Mockery::mock(PaymentMethodUsersRepository::class);
        $paymentMethodUsersRepo->shouldReceive('addCriteria')->andReturn($paymentMethodRepo);
        $paymentMethodUsersRepo->shouldReceive('skipCallbacks')->andReturn($paymentMethodUsersRepo);
        $paymentMethodUsersRepo->shouldReceive('withFetchType')->andReturn($paymentMethodUsersRepo);
        $paymentMethodUsersRepo->shouldReceive('find')->andReturn($paymentMethodUser);

        $mockInvoiceHandler = \Mockery::mock(InvoiceHandlerContract::class)
            ->shouldReceive('create')
            ->withArgs(function (CreateInvoice $params) {
                self::assertEquals('12345', $params->getCustomerAccountId());
                self::assertEquals('54321', $params->getDestinationAccountId());

                self::assertEquals(1, count($params->getLineItems()->getIterator()));

                /** @var InvoiceEntityLineItem $lineItem */
                $lineItem = $params->getLineItems()->getIterator()->offsetGet(0);
                self::assertEquals(1099, $lineItem->getAmount());
                self::assertNotEquals('', $lineItem->getExternalRef());

                return true;
            })
            ->andReturn($invoiceResponse)
            ->getMock();

        $paymentProvider = payments()->provider($user->currentBranchId(), $paymentMethodType);
        $paymentProvider = \Mockery::mock($paymentProvider);
        $paymentProvider->shouldReceive('paymentMethod')->andReturn($paymentMethod);
        $paymentProvider->shouldReceive('invoices')->andReturn($mockInvoiceHandler);

        Auth::loginAs($user);
        $auth = app()->make(Auth::class);
        $auth->paymentMethods[$paymentMethodType] = $paymentProvider;

        app()->instance(PaymentMethodsRepository::class, $paymentMethodRepo);
        app()->instance(PaymentMethodUsersRepository::class, $paymentMethodUsersRepo);
        app()->instance(Auth::class, $auth);
        app()->instance(PriceCalculatorClient::class, $mockPriceCalculatorClient);
        app()->instance(WalletsClient::class, $mockWalletClient);

        /** @var CustomChargeService $service */
        $service = app()->make(CustomChargeService::class);
        $result = $service->execute(new CustomChargeUseCase(
            $userId,
            $user->originBranchId(),
            $paymentMethodType,
            'description',
            null,
            10.99,
            false,
            [],
            ['tax-1'],
            User::make(['type' => \UserType::ADMIN])
        ));
        self::assertNotEmpty($result);
        self::assertNotEmpty($result->transaction());
        self::assertNotEmpty($result->charge());

        app()->forgetInstance(PaymentMethodsRepository::class);
        app()->forgetInstance(PaymentMethodUsersRepository::class);
        app()->forgetInstance(Auth::class);
        app()->forgetInstance(PriceCalculatorClient::class);
        app()->forgetInstance(WalletsClient::class);

    }

    public function test_it_fails_to_executes_charge_on_staff(): void
    {
        $userId = '59a7011a05c677bda512212a';
        $paymentMethodType = Type::CASH;

        $user = $this->fetchUser($userId);

        Auth::loginAs($user);

        /** @var CustomChargeService $service */
        $service = app()->make(CustomChargeService::class);

        $this->expectExceptionMessage("CANNOT_CUSTOM_CHARGE_STAFF_MEMBER");
        $result = $service->execute(new CustomChargeUseCase(
            $userId,
            $user->originBranchId(),
            $paymentMethodType,
            'description',
            null,
            10.99,
            false,
            [],
            ['tax-1'],
            User::make(['type' => \UserType::ADMIN])
        ));
        self::assertNotEmpty($result);
    }
}