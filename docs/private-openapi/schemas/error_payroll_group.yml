type: object
properties:
  success:
    type: boolean
    default: false
  message:
    type: string
    enum:
      - You must provide a group name
      - You must provide a rate id
      - You must provide a rate group id
      - You must provide a rate price
      - The selected event is invalid
      - The must provide an event id
      - You must select an event type
      - The selected event type is invalid
      - The rate price cannot be less than one
      - The rate price is too large
      - The rate price must be a number
  message_code:
    type: string
    enum:
      - PAYROLL_GROUP_MISSING_NAME_FIELD
      - PAYROLL_RATE_MISSING_RATE_ID_FIELD
      - PAYROLL_RATE_MISSING_RATE_GROUP_ID_FIELD
      - PAYROLL_RATE_MISSING_RATE_PRICE_FIELD
      - PAYROLL_RATE_EVENT_ID_MUST_BE_A_STRING
      - PAYROLL_RATE_MISSING_EVENT_ID_FIELD
      - PAYROLL_RATE_MISSING_EVENT_TYPE_FIELD
      - PAYROLL_RATE_INVALID_EVENT_TYPE
      - PAYROLL_RATE_PRICE_MIN_ERROR
      - PAYROLL_RATE_PRICE_MAX_ERROR
      - PAYROLL_RATE_PRICE_MUST_BE_AN_INTEGER
      - PAYROLL_RATE_BONUS_SINGLE_THRESHOLD_THRESHOLD_MUST_BE_AN_INTEGER
      - PAYROLL_RATE_BONUS_SINGLE_THRESHOLD_THRESHOLD_MIN_ERROR
      - PAYROLL_RATE_BONUS_SINGLE_THRESHOLD_THRESHOLD_MAX_ERROR
      - PAYROLL_RATE_BONUS_SINGLE_THRESHOLD_BONUS_MUST_BE_AN_INTEGER
      - PAYROLL_RATE_BONUS_SINGLE_THRESHOLD_BONUS_MIN_ERROR
      - PAYROLL_RATE_BONUS_SINGLE_THRESHOLD_BONUS_MAX_ERROR
  message_data:
    type: array
    items:
      type: string
  errors:
    type: array
    items:
      type: string
