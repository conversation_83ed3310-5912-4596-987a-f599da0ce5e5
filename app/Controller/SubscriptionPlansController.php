<?php

/**
 * MembershipsController class.
 *
 * @uses          AppController
 */
class SubscriptionPlansController extends AppController
{
    /**
     * @var string
     */
    public $name = 'SubscriptionPlans';

    /**
     * @var array
     */
    public $components = [
        'Utility',
        'JWT',
    ];

    /**
     * @var array
     */
    public $uses = [
        'SubscriptionPlan',
    ];

    /*******************************************************************************************
     *                                API 2.0 New Dashboard
     ******************************************************************************************/

    /**
     * Dispatcher for Model.
     *
     * @param $identifier
     *
     * @return string
     */
    public function dispatcher($identifier = null)
    {
        return parent::dispatch($identifier, $this->SubscriptionPlan);
    }

    /**
     * Get plans by specifying all in branch, all in branch or by specific id.
     *
     * @internal   This convention is a little boring for clients, but helps us keep clean backend
     *
     * @example    https://www.glofoxlogin/view/{id}
     *
     * @param string $id SubscriptionPlan id
     *
     * @return array List of plans that match the entry params
     */
    public function view($id = null)
    {
        $user = $this->JWT->parseToken();
        $params = ['branch_id' => $user['branch_id']];
        if (isset($id)) {
            $params['_id'] = $id;
        }

        return json_encode($this->SubscriptionPlan->find('all', ['conditions' => array_merge($params, ['active' => true])]), JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    /**
     * Save a plan object including schedules and patterns, if object does not have an _id
     * it will add it new, else it will update that current instance, This does not upload images
     * to AWS. You must use the image upload service as a separate request to upload images.
     *
     * @internal   Some data validation required since its a complex object. TO_DO
     *
     * @example    https://www.glofoxlogin/courses/upsert Payload: SubscriptionPlan object directly
     *
     * @return object {Course:object,success:boolean}
     */
    public function upsert()
    {
        $user = $this->JWT->parseToken();
        $plan = json_decode(@file_get_contents('php://input'), true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        return json_encode($this->SubscriptionPlan->save($plan), JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    public function settings_subscription_plans()
    {
        $user = $this->JWT->getUser();
        $allowed_roles = ['admin', 'superadmin'];
        $this->JWT->validateOrFail($allowed_roles, $user['User']['namespace']);
        $this->layout = 'dashboard';
        $this->set('branch_id', $user['User']['branch_id']);
        $this->set('namespace', $user['User']['namespace']);
    }

    /**
     * @param $branch_id
     *
     * @return string
     */
    public function findAllByBranchId($branch_id)
    {
        // Validate Branch Id
        if (empty($branch_id)) {
            return json_encode(['success' => false, 'message' => __('The Branch Id is empty')], JSON_PARTIAL_OUTPUT_ON_ERROR);
        }
        $is_valid_mongoid = $this->Utility->is_valid_mongoid($branch_id);
        if (empty($is_valid_mongoid)) {
            return json_encode(['success' => false, 'message' => __('Invalid Branch Id')], JSON_PARTIAL_OUTPUT_ON_ERROR);
        }

        $find_subscription_plans = $this->SubscriptionPlan->findAllByBranchId($branch_id, true);

        $subscription_plans = [];
        $subscription_plans_list = [];

        foreach ($find_subscription_plans as $subscription_plan) {
            $subscription_plans[] = $subscription_plan['SubscriptionPlan'];
            $subscription_plans_list[$subscription_plan['SubscriptionPlan']['_id']] = $subscription_plan['SubscriptionPlan']['name'];
        }

        return json_encode(['success' => true, 'subscriptionPlans' => $subscription_plans, 'subscriptionPlansList' => $subscription_plans_list], JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    /**
     * @param $branch_id
     * @param $subscription_plan_id
     *
     * @return string
     */
    public function findByBranchIdAndId($branch_id, $subscription_plan_id)
    {
        // Validate Branch Id
        if (empty($branch_id)) {
            return json_encode(['success' => false, 'message' => __('The Branch Id is empty')], JSON_PARTIAL_OUTPUT_ON_ERROR);
        }
        $is_valid_mongoid = $this->Utility->is_valid_mongoid($branch_id);
        if (empty($is_valid_mongoid)) {
            return json_encode(['success' => false, 'message' => __('Invalid Branch Id')], JSON_PARTIAL_OUTPUT_ON_ERROR);
        }

        // Validate Subscription Plan Id
        if (empty($subscription_plan_id)) {
            return json_encode(['success' => false, 'message' => __('The Subscription Plan Id is empty')], JSON_PARTIAL_OUTPUT_ON_ERROR);
        }
        $is_valid_mongoid = $this->Utility->is_valid_mongoid($subscription_plan_id);
        if (empty($is_valid_mongoid)) {
            return json_encode(['success' => false, 'message' => __('Invalid Subscription Plan Id')], JSON_PARTIAL_OUTPUT_ON_ERROR);
        }

        $subscription_plan = $this->SubscriptionPlan->findByBranchIdAndId($branch_id, $subscription_plan_id);
        if (empty($subscription_plan)) {
            return json_encode(['success' => false, 'message' => __('There is no subscription plan with that id')], JSON_PARTIAL_OUTPUT_ON_ERROR);
        }

        return json_encode(['success' => true, 'subscriptionPlan' => $subscription_plan['SubscriptionPlan']], JSON_PARTIAL_OUTPUT_ON_ERROR);
    }
}
