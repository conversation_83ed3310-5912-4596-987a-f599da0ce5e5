get:
  tags:
    - Reports
  summary: ''
  operationId: member-kpis
  description: This endpoint returns member kpis of a given branch and user
  x-internal: true
  security:
    - coreapi_auth: [ ]
  parameters:
    - schema:
        type: string
        pattern: '^[a-f\d]{24}$'
      name: branchId
      in: path
      required: true
    - schema:
        type: string
        pattern: '^[a-f\d]{24}$'
      name: userId
      in: path
      required: true
    - schema:
        type: string
        format: date
      in: query
      required: false
      name: endPeriod
    - schema:
        type: string
        format: date
      in: query
      required: false
      name: startPeriod
  responses:
    '200':
      description: OK
      content:
        application/json:
          schema:
            $ref: "../../schemas/member_kpis.yml"