<?php

declare(strict_types=1);

use Carbon\Carbon;
use Glofox\Domain\AsyncEvents\Events\ReservationConfirmationRequestedEventMeta;
use Glofox\Domain\AsyncEvents\Events\ReservationConfirmationRequestedEventPayload;
use Glofox\Domain\Bookings\Repositories\BookingsRepository;
use Glofox\Domain\Bookings\Services\BookingsPublisher;
use Glofox\Domain\Bookings\Validation\Validators\Traits\BookingOpenWindowTrait;
use Glofox\Domain\Branches\Models\Branch;
use Glofox\Domain\Branches\Repositories\BranchesRepository;
use Glofox\Domain\Bookings\Models\Booking;
use Glofox\Infrastructure\Honeycomb\HoneycombTracker;
use Psr\Log\LoggerInterface;

App::uses('Shell', 'Console');

/**
 * <AUTHOR>
 */
class ReservationProcessorShell extends Shell
{
    use BookingOpenWindowTrait;

    private LoggerInterface $logger;
    private BookingsRepository $bookingsRepository;
    private BranchesRepository $branchesRepository;
    private BookingsPublisher $bookingsPublisher;
    private HoneycombTracker $honeycombTracker;

    public function __construct($stdout = null, $stderr = null, $stdin = null)
    {
        parent::__construct($stdout, $stderr, $stdin);

        $this->logger = app()->make(LoggerInterface::class);
        $this->bookingsRepository = app()->make(BookingsRepository::class);
        $this->branchesRepository = app()->make(BranchesRepository::class);
        $this->bookingsPublisher = app()->make(BookingsPublisher::class);
        $this->honeycombTracker = app()->make(HoneycombTracker::class);
    }

    public function main(): void
    {
        $this->logger->info('[ReservationProcessor] Started');
        $startTime = time();
        $branches = $this->branchesRepository->findAllActive();
        $processedCount = 0;

        foreach ($branches as $branch) {
            $this->logger->info(sprintf('[ReservationProcessor] Branch started %s', $branch->id()));

            $reservations = $this->getReservations($branch);

            try {
                foreach ($reservations as $reservation) {
                    $this->logger->info(
                        sprintf(
                            '[ReservationProcessor] Reservation confirmation for %s',
                            $reservation->id()
                        ),
                        [
                            'branch_id' => $branch->id(),
                        ]
                    );
                    $this->publishEvent($reservation);
                    $processedCount++;
                }
            } catch (Exception $ex) {
                $this->logger->error('[ReservationProcessor] An error occurred', [
                    'message' => $ex->getMessage(),
                    'branch_id' => $branch->id(),
                    'booking_id' => $reservation->id(),
                ]);
            }

            $this->logger->info(sprintf('[ReservationProcessor] Branch completed %s', $branch->id()), [
                'count' => count($reservations),
            ]);
        }

        $endTime = time();
        $honeycombData = [
            'service.name' => 'reservation-processor-batch',
            'name' => 'reservation-processor',
            'numberOfBranches' => count($branches),
            'processedReservations' => $processedCount,
            'duration_ms' => ($endTime - $startTime) / 1000, // return milliseconds
        ];
        $this->honeycombTracker->track($honeycombData);

        $this->logger->info('[ReservationProcessor] Completed');
    }

    /**
     * @throws Exception
     */
    private function getReservations(Branch $branch): array
    {
        return $this->bookingsRepository->findNonConfirmedBookingsInBookingOpenWindowByBranchId(
            $branch->id(),
            $this->getMaxStartTime($branch)
        );
    }

    private function getMaxStartTime(Branch $branch): Carbon
    {
        $bookingOpenWindow = $branch->features()->bookingOpenWindow();

        $branchTime = Carbon::now($branch->timezone());

        if ($this->shouldBeBasedOnClassWeeksDisplay($bookingOpenWindow)) {
            return $branchTime->addHours($this->getMaximumWeeksDisplayInHours($branch));
        }

        return $branchTime->addHours($bookingOpenWindow);
    }

    private function publishEvent(Booking $reservation): void
    {
        $this->bookingsPublisher->sendReservationConfirmationRequestedEvent(
            new ReservationConfirmationRequestedEventMeta([]),
            new ReservationConfirmationRequestedEventPayload([
                'branchId' => $reservation->branchId(),
                'bookingId' => $reservation->id(),
            ])
        );
    }
}
