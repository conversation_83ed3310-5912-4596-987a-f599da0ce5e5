<?php

namespace Glofox\Datasource;

/**
 * <AUTHOR>
 */
class MongoMaxQueryDurationThrottler
{
    private int $defaultMaxTimeMs;

    public function __construct(int $defaultMaxTimeMs = 0)
    {
        $this->defaultMaxTimeMs = $defaultMaxTimeMs;
    }

    public function getDefaultMaxTimeMs(): int
    {
        return $this->defaultMaxTimeMs;
    }

    public function apply(array $query): array
    {
        if (!isset($query['maxTimeMS'])) {
            $query['maxTimeMS'] = $this->defaultMaxTimeMs;
        }

        return $query;
    }
}
