<?php

namespace CakeTestCases\Glofox\Domain\Integrations\ClassPass\Actions;

use Glofox\Domain\Integrations\ClassPass\Actions\UpdateBooking;
use Glofox\Domain\Integrations\Origin;

\App::import('Test/Case', 'GlofoxTestCase');

/**
 * Class UpdateBookingTest.
 */
class UpdateBookingTest extends \GlofoxTestCase
{
    /**
     * Fixtures.
     *
     * @var array
     */
    public $fixtures = [
        'app.booking',
    ];

    /**
     * setUp method.
     */
    public function setUp()
    {
        parent::setUp();
        $this->UpdateBooking = new UpdateBooking();
    }

    /**
     * tearDown method.
     */
    public function tearDown()
    {
        parent::tearDown();
        unset($this->UpdateBooking);
    }

    public function testGetBooking()
    {
        $origin = Origin::get('classpass');
        $reservationId = 'classpasBookingId';
        $result = $this->UpdateBooking->getBooking($origin, $reservationId);
        $expectedContains = ['_id' => '5ade081290a4bdaf1e3efa47'];
        $this->assertArrayContainsArray($expectedContains, $result, 'Booking found by integration id');

        $reservationId = 'nonExistentClasspassId';
        $this->setExpectedException(\NotFoundException::class);
        $result = $this->UpdateBooking->getBooking($origin, $reservationId);
    }
}
