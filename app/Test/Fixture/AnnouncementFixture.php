<?php

class AnnouncementFixture extends CakeTestFixture
{
    public $import = 'Announcement';

    public function init()
    {
        $this->records = [
            $this->createBaseAnnouncement(),
            //announcements to test restricttions fetch on dashboard below
            $this->createBaseAnnouncement(['branch_id' => '5e8f09f23fee44196f010f24', 'active' => false]),
            $this->createBaseAnnouncement(['branch_id' => '49a7011a05c677b9a916612a', 'active' => true, 'restrictions' => ['memberships' => []]]),
            $this->createBaseAnnouncement(['branch_id' => '49a7011a05c677b9a916612a', 'active' => true]),
            //announcements to test behavior of 2.0 endpoint below
            $this->createBaseAnnouncement(['branch_id' => '49a7011a05c677b9a916612b', 'namespace' => 'test', 'active' => false, 'restrictions' => ['memberships' => []]]),
            $this->createBaseAnnouncement(['branch_id' => '49a7011a05c677b9a916612b', 'namespace' => 'test', 'active' => true, 'restrictions' => ['memberships' => []]]),
            $this->createBaseAnnouncement(['branch_id' => '49a7011a05c677b9a916612b', 'namespace' => 'test', 'active' => true, 'title' => 'Available not restricted', 'restrictions' => []]),
            $this->createBaseAnnouncement(['branch_id' => '49a7011a05c677b9a916612b', 'namespace' => 'test', 'active' => true, 'title' => 'Available not restricted']),
            $this->createBaseAnnouncement(['branch_id' => '49a7011a05c677b9a916612b', 'namespace' => 'test', 'active' => false]),
            $this->createBaseAnnouncement(['branch_id' => '49a7011a05c677b9a916612b', 'namespace' => 'test', 'active' => false, 'restrictions' => []]),
        ];

        parent::init();
    }

    private function createBaseAnnouncement(array $override = []): array
    {
        $default = [
            '_id' => new MongoId(),
            'branch_id' => '5e8f09f23fee44096f010f24',
            'active' => true,
            'title' => 'Default Announcement Title',
            'content' => 'Default Announcement Content',
        ];

        return array_replace_recursive($default, $override);
    }
}
