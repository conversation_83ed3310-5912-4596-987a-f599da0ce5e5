<?php

namespace CakeTestCases\Glofox\Domain\Transactional;

use CakeTestCases\Glofox\Domain\Logger\Traits\MockedLoggerTrait;
use Glofox\Domain\Communications\Services\CommunicationsPublisher;
use Glofox\Domain\Transactional\Messages\ParsedMessage;
use Glofox\Domain\Transactional\Sender;
use Glofox\Export\FileExport;

\App::import('Test/Case', 'GlofoxTestCase');

class SenderTest extends \GlofoxTestCase
{
    use MockedLoggerTrait;
    private Sender $sender;
    private CommunicationsPublisher $publisher;

    private array $mockUser;

    public function setUp()
    {
        parent::setUp();
        $this->publisher = \Mockery::mock(CommunicationsPublisher::class);
        $this->sender = new Sender($this->publisher, $this->loggerMock);

        $this->mockUser = [
            'User' => [
                '_id' => '5c8b0642deb2eae76ec28123',
                'email' => '<EMAIL>',
                'parent_id' => '5c8b0615deb2eae76ec28123',
                'use_parent_card' => false,
            ],
        ];
    }

    /**
     * Fixtures.
     *
     * @var array
     */
    public $fixtures = [
        'app.user',
    ];

    public function testParentEmailUsedIfFlagTrue()
    {
        $client = \Mockery::mock(\CakeEmail::class);
        $client->shouldReceive('subject');
        $client->shouldReceive('to')
            ->withArgs(function (string $to) {
                self::assertSame($to, '<EMAIL>');
                return true;
            });
        $client->shouldReceive('cc')->andReturn([]);
        $client->shouldReceive('bcc')->andReturn([]);
        $client->shouldReceive('attachments')->andReturn([]);
        $client->shouldReceive('send');
        $this->publisher->shouldReceive('publish');

        $this->sender->setClient($client);

        $parsedMessage = (new ParsedMessage())->withContent('test content')->withSubject('test subject');
        $this->sender->send($this->mockUser, $parsedMessage);
    }

    public function test_it_adds_attachments(): void
    {
        $client = \Mockery::mock(\CakeEmail::class);
        $client->shouldReceive('attachments')
            ->withArgs(function (array $files) {
                foreach ($files as $filename => $file) {
                    $this->assertTrue(\is_string($filename) || \is_int($filename));
                    $this->assertRegExp('/.+\/[a-z0-9]{13}\.pdf/', $file);
                }
                return true;
            })
            ->once();

        $this->sender->setClient($client);

        $this->sender->addAttachments([
            new FileExport('foo', 'pdf'),
            'baz' => new FileExport('bar', 'pdf'),
        ]);
    }

    public function test_it_sends_email_via_php_if_send_email_event_is_not_emitted(): void
    {
        $this->publisher
            ->shouldReceive('publish')
            ->andReturn(false);

        $client = \Mockery::mock(\CakeEmail::class);
        $client->shouldReceive('subject');
        $client->shouldReceive('to')
            ->withArgs(function (string $to) {
                self::assertSame($to, '<EMAIL>');
                return true;
            });
        $client->shouldReceive('cc')->andReturn([]);
        $client->shouldReceive('bcc')->andReturn([]);
        $client->shouldReceive('attachments')->andReturn([]);
        $client->shouldReceive('send');

        $this->sender->setClient($client);

        $parsedMessage = (new ParsedMessage())->withContent('test content')->withSubject('test subject');
        $this->sender->send($this->mockUser, $parsedMessage);

        $client->shouldHaveReceived('subject');
        $client->shouldHaveReceived('to');
        $client->shouldHaveReceived('send');
    }

    public function test_it_does_not_send_email_via_php_if_send_email_event_is_emitted(): void
    {
        $this->publisher
            ->shouldReceive('publish')
            ->andReturn(true);

        $client = \Mockery::mock(\CakeEmail::class);
        $client->shouldReceive('cc')->andReturn([]);
        $client->shouldReceive('bcc')->andReturn([]);
        $client->shouldReceive('attachments')->andReturn([]);

        $this->sender->setClient($client);

        $parsedMessage = (new ParsedMessage())->withContent('test content')->withSubject('test subject');
        $this->sender->send($this->mockUser, $parsedMessage);

        $client->shouldNotHaveReceived('subject');
        $client->shouldNotHaveReceived('to');
        $client->shouldNotHaveReceived('send');
    }
}
