<?php

namespace Glofox\Response\Transformers;

use Glofox\Response\Models\Register\User\GenderModel;
use Glofox\Response\Models\Register\User\Membership\MembershipModel;
use Glofox\Response\Models\Register\User\TimestampModel;
use Glofox\Response\Models\Register\UserModel;
use Glofox\Response\Models\RegisterModel;

/**
 * Class RegisterTransformer
 * @package Glofox\Response\Transforems
 * @SuppressWarnings(PHPMD)
 */
class RegisterTransformer extends BaseTransformer
{
    /**
     * @param array $registration
     * @return RegisterModel
     */
    public function setRegistrationModel(array $registration = []) : RegisterModel
    {
        $userModel = null;
        if (isset($registration['user']) && !empty($registration['user'])) {
            $userModel = $this->setUserModel($registration['user']);
        }
        return new RegisterModel(
            [
                'success' => $registration['success'] ?? false,
                'user' => $userModel,
            ]
        );
    }

    /**
     * @param array $user
     * @return UserModel
     */
    public function setUserModel(array $user) : UserModel
    {
        $membershipModel = null;
        if (isset($user['membership']) && !empty($user['membership'])) {
            $membershipModel = $this->setMembershipModel($user['membership']);
        }

        $timestampModel = null;
        if (isset($user['timestamp']) && !empty($user['timestamp'])) {
            $timestampModel = $this->setTimestampModel($user['timestamp']);
        }

        $genderModel = null;
        if (isset($user['gender']) && !empty($user['gender'])) {
            $genderModel = $this->setGenderModel($user['gender']);
        }

        return new UserModel(
            [
                'email' => $user['email'] ?? '',
                'membership' => $membershipModel,
                'birth' => $user['birth'] ?? '',
                'gender' => $genderModel,
                'answers' => $user['answers'] ?? [],
                'active' => $user['active'] ?? '',
                'emergency_contact' => $user['emergency_contact'] ?? '',
                'receive_marketing' => $user['receive_marketing'] ?? false,
                'login' => $user['login'] ?? '',
                'modified' => $user['modified'] ?? 0,
                'created' => $user['created'] ?? 0,
                'categories' => $user['categories'] ?? [],
                'origin_branch_id' => $user['origin_branch_id'] ?? '',
                'region' => $user['region'] ?? '',
                'timestamp' => $timestampModel,
                'name' => $user['name'] ?? '',
                'image_url' => $user['image_url'] ?? '',
                // inherited from other class
                '_id' => $user['_id'] ?? '',
                'first_name' => $user['first_name'] ?? '',
                'last_name' => $user['last_name'] ?? '',
                'phone' => $user['phone'] ?? '',
                'namespace' => $user['namespace'] ?? '',
                'branch_id' => $user['branch_id'] ?? '',
                'type' => $user['type'] ?? '',
            ]
        );
    }

    /**
     * @param array $membership
     * @return MembershipModel
     */
    public function setMembershipModel(array $membership) : MembershipModel
    {
        return new MembershipModel(
            [
              'type' => $membership['type'] ?? ''
            ]
        );
    }

    /**
     * @param $timestamp
     * @return TimestampModel
     */
    public function setTimestampModel($timestamp = null) : TimestampModel
    {
        return new TimestampModel(
            [
                'sec' => $timestamp->sec ?? 0,
                'inc' => $timestamp->inc ?? 0,
            ]
        );
    }

    /**
     * @param array $gender
     * @return GenderModel
     */
    public function setGenderModel(array $gender) : GenderModel
    {
        return new GenderModel(
            [
                'name' => $gender['name'] ?? 0,
                'label' => $gender['label'] ?? 0,
            ]
        );
    }
}
