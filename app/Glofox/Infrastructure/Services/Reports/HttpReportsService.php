<?php

declare(strict_types=1);

namespace Glofox\Infrastructure\Services\Reports;

use Glofox\Domain\Reports\Services\ReportsServiceInterface;
use Glofox\Http\Requests\TracingContext;
use Glofox\Http\Requests\TracingContextInterface;
use Glofox\Infrastructure\Exception\HttpClientResponseException;
use Glofox\Infrastructure\Services\Reports\Requests\GenerateEmbedReportUrlRequest;
use Glofox\Infrastructure\Services\Reports\Requests\GetDiscountInsightsRequest;
use Glofox\Infrastructure\Services\Reports\Requests\GetMemberKPIsRequest;
use Glofox\Infrastructure\Services\Reports\Requests\GetMoneyOwedRequest;
use Glofox\Infrastructure\Services\Reports\Requests\GetReportsRequest;
use Glofox\Infrastructure\Services\Reports\Requests\GetSalesMetricsRequest;
use Glofox\Infrastructure\Services\Reports\Requests\GetVisitsInsightsRequest;
use Glofox\Infrastructure\Services\Reports\Responses\GenerateEmbedReportUrlResponse;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\BadResponseException;
use GuzzleHttp\Exception\ClientException;
use GuzzleHttp\Exception\ServerException;
use Psr\Http\Message\ResponseInterface;
use Psr\Log\LoggerInterface;

class HttpReportsService implements ReportsServiceInterface
{
    private const GENERATE_EMBED_LOOK_URL = 'reports/v1/embed-report-url';

    private \GuzzleHttp\Client $httpClient;

    private \Psr\Log\LoggerInterface $logger;

    private \Glofox\Http\Requests\TracingContextInterface $tracingContext;

    public function __construct(Client $httpClient, LoggerInterface $logger, TracingContextInterface $tracingContext)
    {
        $this->httpClient = $httpClient;
        $this->logger = $logger;
        $this->tracingContext = $tracingContext;
    }

    public function getMoneyOwedReport(GetMoneyOwedRequest $params): ResponseInterface
    {
        $this->logger->info(sprintf('Requesting Money Owed Report for branch id %s', $params->branchId()));

        $headers = $this->generateDefaultHeaders([]);
        $response = null;

        try {
            $response = $this->httpClient->send($params->getRequest(), ['headers' => $headers]);

            $this->logger->info(
                sprintf('Money Owed Report requested successfully for branch id %s', $params->branchId())
            );

            return $response;
        } catch (ServerException|ClientException $exception) {
            throw $this->handleException($exception);
        }
    }

    public function getSalesMetricsReport(GetSalesMetricsRequest $params): ResponseInterface
    {
        $this->logger->info(sprintf('Requesting Sales Metrics Report for branch id %s', $params->branchId()));

        $headers = $this->generateDefaultHeaders([]);
        $response = null;

        try {
            $response = $this->httpClient->send($params->getRequest(), ['headers' => $headers]);

            $this->logger->info(
                sprintf('Sales Metrics Report requested successfully for branch id %s', $params->branchId())
            );

            return $response;
        } catch (ServerException|ClientException $exception) {
            throw $this->handleException($exception);
        }
    }

    public function getMemberKPIsReport(GetMemberKPIsRequest $params): ResponseInterface
    {
        $this->logger->info(
            sprintf('Requesting Member KPIs for branch id %s and user id %s', $params->branchId(), $params->userId())
        );

        $headers = $this->generateDefaultHeaders([]);
        $response = null;

        try {
            $response = $this->httpClient->send($params->getRequest(), ['headers' => $headers]);

            $this->logger->info(
                sprintf(
                    'Member KPIs requested successfully for branch id %s and user id %s',
                    $params->branchId(),
                    $params->userId()
                )
            );

            return $response;
        } catch (ServerException|ClientException $exception) {
            throw $this->handleException($exception);
        }
    }

    public function getVisitsInsightsReport(GetVisitsInsightsRequest $params): ResponseInterface
    {
        $this->logger->info(sprintf('Requesting Visits Insights for branch id %s', $params->branchId()));

        $headers = $this->generateDefaultHeaders([]);
        $response = null;

        try {
            $response = $this->httpClient->send($params->getRequest(), ['headers' => $headers]);

            $this->logger->info(
                sprintf('Visits Insights requested successfully for branch id %s', $params->branchId())
            );

            return $response;
        } catch (ServerException|ClientException $exception) {
            throw $this->handleException($exception);
        }
    }

    public function generateEmbedReportUrl(GenerateEmbedReportUrlRequest $request): GenerateEmbedReportUrlResponse
    {
        $url = self::GENERATE_EMBED_LOOK_URL;

        $headers = $this->generateDefaultHeaders([]);
        $options = [
            'verify' => true,
            'headers' => $headers,
            'body' => $request->toJson(),
        ];

        $this->logger->info(
            sprintf(
                'Requesting the generation of an Embed Look Url using the following request: %s',
                $request->toJson()
            )
        );

        try {
            $response = $this->httpClient->post(
                $url,
                $options
            );

            $response = $response->getBody()->getContents();

            $this->logger->info(
                sprintf('Response for generating an Embed Look Url received: %s', $response)
            );

            $decodedResponse = json_decode($response, null, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

            return new GenerateEmbedReportUrlResponse($decodedResponse->url);
        } catch (ServerException|ClientException $exception) {
            throw $this->handleException($exception);
        }
    }

    public function getDiscountInsightsReport(GetDiscountInsightsRequest $params): ResponseInterface
    {
        $this->logger->info(sprintf('Requesting Discount Insights Report for branch id %s', $params->branchId()));

        $headers = $this->generateDefaultHeaders([]);
        $response = null;

        try {
            $response = $this->httpClient->send($params->getRequest(), ['headers' => $headers]);

            $this->logger->info(
                sprintf('Discount Insights Report requested successfully for branch id %s', $params->branchId())
            );

            return $response;
        } catch (ServerException|ClientException $exception) {
            throw $this->handleException($exception);
        }
    }

    public function getReport(GetReportsRequest $params): ResponseInterface
    {
        $this->logger->info(sprintf('Requesting Report for branch id %s', $params->getBranchId()));

        $headers = $this->generateDefaultHeaders([]);
        $response = null;

        try {
            $response = $this->httpClient->send($params->getRequest(), ['headers' => $headers]);

            $this->logger->info(sprintf('Report requested successfully for branch id %s', $params->getBranchId()));

            return $response;
        } catch (ServerException|ClientException $exception) {
            throw $this->handleException($exception);
        }
    }

    private function generateDefaultHeaders(array $headers): array
    {
        if (!isset($headers[TracingContext::TRACING_HEADER])) {
            $headers[TracingContext::TRACING_HEADER] = $this->tracingContext->getTracingHeaderForPropagation();
        }

        $headers['Content-Type'] = 'application/json';

        // Set by Glofox\Http\Requests\Middlewares\AuthorizeExternalIntegration
        // This would be potentially member app or dashboard.
        if (isset($_SERVER['HTTP_X_GLOFOX_SOURCE'])) {
            $headers['X-Glofox-Source'] = $_SERVER['HTTP_X_GLOFOX_SOURCE'];
        }

        if (isset($_SERVER['HTTP_X_GLOFOX_BRANCH_ID'])) {
            $headers['x-glofox-branch-id'] = $_SERVER['HTTP_X_GLOFOX_BRANCH_ID'];
        }

        if (isset($_SERVER['HTTP_X_REQUEST_ID'])) {
            $headers['x-request-id'] = $_SERVER['HTTP_X_REQUEST_ID'];
        }

        return $headers;
    }

    private function handleException(BadResponseException $exception): HttpClientResponseException
    {
        $response = $exception->getResponse();
        $statusCode = $response ? $response->getStatusCode() : 'UNKNOWN';
        $body = $response ? (string)$response->getBody() : '';
        $responseData = json_decode($body, $assoc = true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);

        $this->logger->error('Error received from the Reports Service', [
            'status' => $statusCode,
            'response' => $responseData,
        ]);

        $errorCode = $responseData['code'] ?? $exception->getMessage();

        return new HttpClientResponseException($statusCode, $errorCode);
    }
}
