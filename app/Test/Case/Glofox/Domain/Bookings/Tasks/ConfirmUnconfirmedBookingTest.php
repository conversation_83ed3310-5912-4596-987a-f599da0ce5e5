<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\Bookings\Tasks;

use App;
use CakeTestCases\Glofox\Domain\Logger\Traits\MockedLoggerTrait;
use CakeTestCases\Glofox\Domain\Users\Traits\AuthenticateUsersTrait;
use Glofox\Domain\AsyncEvents\Events\ReservationBookingRequestedEvent;
use Glofox\Domain\AsyncEvents\Events\ReservationBookingRequestedEventMeta;
use Glofox\Domain\AsyncEvents\Events\ReservationBookingRequestedEventPayload;
use Glofox\Domain\Authentication\Auth;
use Glofox\Domain\Bookings\Commands\GenerateBooking;
use Glofox\Domain\Bookings\Events\ReservationCouldNotBeBooked;
use Glofox\Domain\Bookings\Events\ReservationWasBooked;
use Glofox\Domain\Bookings\Internal\Parameters\GenerateBookingParameters;
use Glofox\Domain\Bookings\Models\Booking;
use Glofox\Domain\Bookings\Repositories\BookingsRepository;
use Glofox\Domain\Bookings\Search\Expressions\EventId;
use Glofox\Domain\Bookings\Search\Expressions\UserId;
use Glofox\Domain\Bookings\Services\BookingsPublisher;
use Glofox\Domain\Bookings\Status;
use Glofox\Domain\Bookings\Tasks\ConfirmUnconfirmedBooking;
use Glofox\Domain\Branches\Models\Branch;
use Glofox\Domain\Events\Models\Event;
use Glofox\Domain\Locker\EventBookingLockerInterface;
use Glofox\Domain\Users\Models\User;
use Glofox\Eventkit\DomainEvent\DomainEventInterface;
use Glofox\Eventkit\DomainEvent\DomainEventPayloadInterface;
use Glofox\Events\EventManager;
use Glofox\Repositories\Search\Expressions\Shared\Id;
use GlofoxTestCase;
use Mockery;
use Mockery\MockInterface;

App::import('Test/Case', 'GlofoxTestCase');

final class ConfirmUnconfirmedBookingTest extends GlofoxTestCase
{
    use MockedLoggerTrait;
    use AuthenticateUsersTrait;

    public $fixtures = [
        'app.branch',
        'app.user',
        'app.program',
        'app.event',
        'app.booking',
    ];

    private BookingsRepository $bookingsRepository;
    private GenerateBooking $generateBooking;
    private EventManager $eventManager;

    public function setUp(): void
    {
        parent::setUp();

        $this->bookingsRepository = Mockery::mock(BookingsRepository::class);
        $this->generateBooking = Mockery::mock(GenerateBooking::class);
        $this->eventManager = Mockery::mock(EventManager::class);

        $this->mockBookingAppointmentLocker();
    }

    public function tearDown(): void
    {
        parent::tearDown();

        Mockery::close();
    }

    public function testUnsuccessfulBooking(): void
    {
        $booking = Mockery::mock(Booking::class);
        $booking->shouldReceive('namespace')
            ->andReturn('the-booking-namespace')
            ->shouldReceive('branchId')
            ->andReturn('the-booking-branch-id');

        $this->bookingsRepository
            ->shouldReceive('addCriteria')
            ->once()
            ->withArgs(
                function (Id $id) {
                    $this->assertEquals(new Id('61a4a90e9c955c32980a31fe'), $id);

                    return true;
                }
            )
            ->andReturnSelf();
        $this->bookingsRepository
            ->shouldReceive('firstOrFail')
            ->once()
            ->andReturn(
                $booking->allows([
                        'event' => Event::make(),
                        'user' => User::make(),
                        'branch' => Branch::make(),
                        'markAsFailed' => null,
                        'failureReason' => 'FAILED_TO_BOOK',
                        'setFailureReason' => 'FAILED_TO_BOOK',
                        'setAllFailureReasons' => ['FAILED_TO_BOOK'],
                        'status' => Status::FAILED(),
                        'toArray' => [],
                    ])
            );
        $this->bookingsRepository
            ->shouldReceive('updateStatusAndFailureReason')
            ->once()
            ->withArgs(
                function (Booking $booking) {
                    $this->assertSame(Status::FAILED, $booking->status()->getValue());
                    $this->assertSame('FAILED_TO_BOOK', $booking->failureReason());

                    return true;
                }
            );

        $this->generateBooking
            ->shouldReceive('generate')
            ->once()
            ->withArgs(
                function (GenerateBookingParameters $params) {
                    $this->assertFalse($params->getPayAtTheGym());
                    $this->assertNull($params->getPaymentMethod());

                    return true;
                }
            )
            ->andReturn([
                'success' => false,
                'message_code' => 'FAILED_TO_BOOK'
            ]);

        $this->eventManager
            ->shouldReceive('emit')
            ->once()
            ->withArgs(
                function (string $listener, array $params) {
                    self::assertSame(ReservationCouldNotBeBooked::class, $listener);
                    self::assertInstanceOf(Booking::class, $params[0]);

                    return true;
                }
            );

        /** @var DomainEventInterface $domainEvent */
        $domainEvent = Mockery::mock(DomainEventInterface::class)
            ->allows([
                'payload' => Mockery::mock(DomainEventPayloadInterface::class)
                    ->allows([
                        'jsonSerialize' => [
                            'branchId' => '61a4a9269179186c5713170e',
                            'bookingId' => '61a4a90e9c955c32980a31fe',
                        ]
                    ]),
                'type' => 'FOO_BAR',
            ]);

        $this->newInstance()->execute($domainEvent);
    }

    public function testSuccessfulBooking(): void
    {
        $booking = Mockery::mock(Booking::class);
        $booking->shouldReceive('namespace')
            ->andReturn('the-booking-namespace')
            ->shouldReceive('branchId')
            ->andReturn('the-booking-branch-id');

        $this->bookingsRepository
            ->shouldReceive('addCriteria')
            ->once()
            ->andReturnSelf();
        $this->bookingsRepository
            ->shouldReceive('firstOrFail')
            ->once()
            ->andReturn(
                $booking->allows([
                        'event' => Event::make(),
                        'user' => User::make(),
                        'branch' => Branch::make(),
                        'toArray' => [],
                    ])
            );
        $this->bookingsRepository
            ->shouldNotReceive('updateStatusAndFailureReason');

        $this->generateBooking
            ->shouldReceive('generate')
            ->once()
            ->andReturn([
                'success' => true,
            ]);

        $this->eventManager
            ->shouldReceive('emit')
            ->once()
            ->withArgs(
                function (string $listener, array $params) {
                    self::assertSame(ReservationWasBooked::class, $listener);
                    self::assertInstanceOf(Booking::class, $params[0]);

                    return true;
                }
            );

        /** @var DomainEventInterface $domainEvent */
        $domainEvent = Mockery::mock(DomainEventInterface::class)
            ->allows([
                'payload' => Mockery::mock(DomainEventPayloadInterface::class)
                    ->allows([
                        'jsonSerialize' => [
                            'branchId' => '61a4a9269179186c5713170e',
                            'bookingId' => '61a4a90e9c955c32980a31fe',
                        ]
                    ]),
                'type' => 'FOO_BAR',
            ]);

        $this->newInstance()->execute($domainEvent);
    }

    public function testIntegrationSuccessful(): void
    {
        Auth::loginAs($this->fetchUser($this->adminId));

        /** @var BookingsRepository $repository */
        $repository = app()->make(BookingsRepository::class);

        $bookingId = '61a4ee412df7cc24846a5d45'; // from BookingFixture
        $booking = $repository->findById($bookingId);
        $booking = Booking::make($booking['Booking']);

        $this->assertFalse($booking->isConfirmed());

        /** @var BookingsPublisher|MockInterface $bookingsPublisher */
        $bookingsPublisher = Mockery::mock(BookingsPublisher::class);
        $bookingsPublisher->allows(['sendBookingFinishedEvent' => null]);
        app()->instance(BookingsPublisher::class, $bookingsPublisher);

        $domainEvent = new ReservationBookingRequestedEvent(
            new ReservationBookingRequestedEventMeta([]),
            new ReservationBookingRequestedEventPayload([
                'bookingId' => $booking->id(),
                'branchId' => $booking->branchId(),
            ])
        );

        /** @var ConfirmUnconfirmedBooking $task */
        $task = app()->make(ConfirmUnconfirmedBooking::class);
        $task->execute($domainEvent);

        /** @var Booking[] $bookings */
        $bookings = $repository
            ->addCriteria(new EventId($booking->eventId()))
            ->addCriteria(new UserId($booking->userId()))
            ->find();

        $this->assertCount(1, $bookings);
        $this->assertTrue($bookings[0]->isConfirmed());
        $this->assertSame($bookingId, $bookings[0]->id());
        $this->assertSame(Status::BOOKED, $bookings[0]->status()->getValue());

        app()->forgetInstance(BookingsPublisher::class);
    }

    public function testIntegrationUnsuccessful(): void
    {
        Auth::loginAs($this->fetchUser($this->adminId));

        /** @var BookingsRepository $repository */
        $repository = app()->make(BookingsRepository::class);

        $bookingId = '61a4ee412df7cc24846a5d45'; // from BookingFixture
        $booking = $repository->findById($bookingId);
        $booking = Booking::make($booking['Booking']);

        $this->assertFalse($booking->isConfirmed());

        /** @var BookingsPublisher|MockInterface $bookingsPublisher */
        $bookingsPublisher = Mockery::mock(BookingsPublisher::class);
        $bookingsPublisher->allows(['sendBookingFinishedEvent' => null]);
        app()->instance(BookingsPublisher::class, $bookingsPublisher);

        $domainEvent = new ReservationBookingRequestedEvent(
            new ReservationBookingRequestedEventMeta([]),
            new ReservationBookingRequestedEventPayload([
                'bookingId' => $booking->id(),
                'branchId' => $booking->branchId(),
            ])
        );

        /** @var GenerateBooking|MockInterface $bookingsPublisher */
        $generateBooking = Mockery::mock(GenerateBooking::class);
        $generateBooking->allows([
            'generate' => [
                'success' => false,
                'message' => 'You have already booked this',
                'message_code' => 'YOU_HAVE_BOOKED_FOR_THIS_EVENT',
            ],
        ]);
        app()->instance(GenerateBooking::class, $generateBooking);

        /** @var ConfirmUnconfirmedBooking $task */
        $task = app()->make(ConfirmUnconfirmedBooking::class);
        $task->execute($domainEvent);

        /** @var Booking[] $bookings */
        $bookings = $repository
            ->addCriteria(new EventId($booking->eventId()))
            ->addCriteria(new UserId($booking->userId()))
            ->find();

        $this->assertCount(1, $bookings);
        $this->assertFalse($bookings[0]->isConfirmed());
        $this->assertSame($bookingId, $bookings[0]->id());
        $this->assertSame(Status::FAILED, $bookings[0]->status()->getValue());
        $this->assertSame('YOU_HAVE_BOOKED_FOR_THIS_EVENT', $bookings[0]->failureReason());

        app()->forgetInstance(BookingsPublisher::class);
    }

    private function newInstance(): ConfirmUnconfirmedBooking
    {
        return new ConfirmUnconfirmedBooking(
            $this->bookingsRepository,
            $this->generateBooking,
            $this->eventManager,
            $this->loggerMock
        );
    }

    private function mockBookingAppointmentLocker(): void
    {
        $bookingAppointmentLocker = Mockery::mock(EventBookingLockerInterface::class);
        $bookingAppointmentLocker->shouldReceive('lock')->zeroOrMoreTimes();
        $bookingAppointmentLocker->shouldReceive('unlock')->zeroOrMoreTimes();
        $bookingAppointmentLocker
            ->shouldReceive('getEventIdFromAppointment')
            ->andReturn('test-event')
            ->zeroOrMoreTimes();

        app()->forgetInstance(EventBookingLockerInterface::class);
        app()->instance(EventBookingLockerInterface::class, $bookingAppointmentLocker);
    }
}
