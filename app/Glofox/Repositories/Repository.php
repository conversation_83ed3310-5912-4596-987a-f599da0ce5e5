<?php

namespace Glofox\Repositories;

use Glofox\Repositories\Filters\ConditionsCollection;
use Glofox\Repositories\Traits\ControlsCakeEvents;
use Glofox\Repositories\Traits\HasCriteria;
use Glofox\Repositories\Traits\HasFetchType;
use Glofox\Traversable\Hydrators\ModelHydrator;
use MongoDB\BSON\ObjectId;
use MongoDB\BSON\UTCDateTime;
use MongoDB\Driver\ReadConcern;
use MongoException;
use MongoId;
use MongoDB\Collection as MongoCollection;
use MongoDB\Database;

abstract class Repository
{
    use HasFetchType;
    use HasCriteria;
    use ControlsCakeEvents;

    /**
     * @var \AppModel
     */
    protected $collection;

    /**
     * @var int
     */
    protected $page = 1;

    /**
     * @var int
     */
    protected $limit;

    /**
     * @var array
     */
    protected $order;

    /**
     * @var array
     */
    protected $fields;

    protected ?int $maxTimeMs = null;

    protected string $hint = '';

    public function __construct()
    {
        $this->startCriteria();
    }

    public function fields(array $fields = null)
    {
        if ($fields === null) {
            return $this->fields;
        }

        $this->fields = $fields;
        return $this;
    }

    public function page(int $page = null)
    {
        if ($page === null) {
            return $this->page;
        }

        $this->page = $page;
        return $this;
    }

    public function limit(int $limit = null)
    {
        if ($limit === null) {
            return $this->limit;
        }

        $this->limit = $limit;
        return $this;
    }

    public function order(array $order = null)
    {
        if ($order === null) {
            return $this->order;
        }

        $this->order = $order;
        return $this;
    }

    public function maxTimeMs(?int $maxDurationMs = null)
    {
        if ($maxDurationMs === null) {
            return $this->maxTimeMs;
        }

        $this->maxTimeMs = $maxDurationMs;
        return $this;
    }

    public function hint(string $hint = null)
    {
        if ($hint === null) {
            return $this->hint;
        }

        $this->hint = $hint;
        return $this;
    }

    protected function handleFail($result, \Closure $exceptionCallback = null): void
    {
        if ($result) {
            return;
        }

        if ($exceptionCallback) {
            $exceptionCallback->call($this);

            return;
        }

        throw new \NotFoundException('Resource not found');
    }

    public function count()
    {
        $this->withFetchType(FetchType::COUNT());

        return $this->find();
    }

    public function firstOrFail(\Closure $exceptionCallback = null)
    {
        $this->withFetchType(FetchType::FIRST());

        $result = $this->find();

        $this->handleFail($result, $exceptionCallback);

        return $result;
    }

    public function first()
    {
        $this->withFetchType(FetchType::FIRST());

        $result = $this->find();

        return $result;
    }

    public function findOrFail(\Closure $exceptionCallback = null)
    {
        $result = $this->find();

        $this->handleFail($result, $exceptionCallback);

        return $result;
    }

    public function find()
    {
        return $this->fetch();
    }

    public function fetch()
    {
        $conditions = new ConditionsCollection();

        $this->applyCriteria($this->criteria, $conditions, $this);

        $options = [
            'conditions' => $conditions->toArray(),
            'fields' => $this->fields(),
            'page' => $this->page(),
            'limit' => $this->limit(),
            'order' => $this->order(),
            'callbacks' => (!$this->skipCallbacks),
            'hint' => $this->hint(),
        ];

        if (!is_null($this->maxTimeMs())) {
            $options['maxTimeMS'] = $this->maxTimeMs();
        }

        $result = $this->dbCollection()->find($this->fetchType(), $options);

        if ($this->fetchType() === FetchType::FIRST && !(is_countable($result) && count($result))) {
            $this->resetFetchType();
            return null;
        }

        if ($this->dbCollection() instanceof \AppModel
            && $this->fetchType() !== FetchType::COUNT) {
            if ($this->fetchType() === FetchType::FIRST) {
                $result = collect($result)->pop();
            } elseif ($this->fetchType() === FetchType::ALL) {
                $path = sprintf('{n}.%s', get_class($this->dbCollection()));
                $result = \Hash::extract($result, $path);
            }
        }

        $result = $this->hydrate($result);

        $this->resetFetchType();

        return $result;
    }

    /**
     * @param $result
     *
     * @return \Illuminate\Support\Collection
     */
    protected function hydrate($result)
    {
        if (method_exists($this, 'model')) {
            /** @var ModelHydrator $modelHydrator */
            $modelHydrator = app()->make(ModelHydrator::class);
            $modelClass = $this->model();

            switch ($this->fetchType()) {
                case FetchType::FIRST:
                    $result = $modelHydrator->expects($modelClass)->fill($result);
                    break;

                case FetchType::ALL:
                    foreach ($result as &$row) {
                        $row = $modelHydrator->expects($modelClass)->fill($row);
                    }
                    break;
            }
        }

        return $result;
    }

    protected function isValidMongoId(string $id): bool
    {
        try {
            if (!empty($id)) {
                return new MongoId($id) !== null;
            }

            return false;
        } catch (MongoException $ex) {
            return false;
        }
    }

    public function setReadConcern(ReadConcern $concern): self
    {
        $this->dbCollection()->setReadConcern($concern);

        return $this;
    }

    protected function deleteOne(string $id, array $options = []): bool
    {
        $options['maxTimeMS'] = $this->maxTimeMs();
        return $this->getMongoCollection()->deleteOne(['_id' => new ObjectId($id)], $options)->isAcknowledged();
    }

    protected function updateOne(string $id, array $update, array $options = []): int
    {
        $options['maxTimeMS'] = $this->maxTimeMs();
        $update['modified'] = new UTCDateTime();

        return $this->getMongoCollection()
            ->updateOne(['_id' => new ObjectId($id)], ['$set' => $update], $options)
            ->getModifiedCount();
    }

    protected function insertOne(array $attributes, array $options = []): string
    {
        $now = new UTCDateTime();
        $attributes['created'] = $now;
        $attributes['modified'] = $now;
        $options['maxTimeMS'] = $this->maxTimeMs();

        return $this->getMongoCollection()->insertOne($attributes, $options)->getInsertedId();
    }

    protected function insertMany(array $documents, array $options = []): int
    {
        $now = new UTCDateTime();
        foreach ($documents as $document) {
            $document['created'] = $now;
            $document['modified'] = $now;
        }

        $options['maxTimeMS'] = $this->maxTimeMs();
        return $this->getMongoCollection()->insertMany($documents, $options)->getInsertedCount();
    }

    protected function updateMany(array $ids, array $update, array $options = []): int
    {
        $query = ['_id' => ['$in' => []]];
        foreach ($ids as $id) {
            $query['_id']['$in'][] = new ObjectId($id);
        }

        $options['maxTimeMS'] = $this->maxTimeMs();
        $update['modified'] = new UTCDateTime();

        return $this->getMongoCollection()->updateMany($query, ['$set' => $update], $options)->getModifiedCount();
    }

    protected function deleteMany(array $filter, array $options = []): int
    {
        $options = array_merge(['maxTimeMS' => $this->maxTimeMs()], $options);
        return $this->getMongoCollection()->deleteMany($filter, $options)->getDeletedCount();
    }

    /**
     * @throws \InvalidArgumentException
     */
    protected function getMongoCollection(): MongoCollection
    {
        $collectionName = $this->getCollectionName();
        if ($collectionName === "") {
            throw new \InvalidArgumentException('Collection name cannot be empty');
        }
        return app()->make(Database::class)
            ->selectCollection($collectionName)
            ->withOptions([
                'readConcern' => new ReadConcern(ReadConcern::AVAILABLE),
            ]);
    }

    protected function getCollectionName(): string
    {
        return $this->dbCollection()->useTable;
    }

    /**
     * @return \AppModel
     */
    abstract protected function dbCollection();
}
