<?php

use Zendesk\API\HttpClient as ZendeskAPI;

App::uses('Component', 'Controller');

/**
 * This Component will manage the integration of Glofox with Zendesk.
 */
class ZendeskComponent extends Component
{
    public $controller = null;
    public $config = null;
    public $environment = null;
    protected $Category;

    /**
     * assigns values of referencing controller.
     *
     * @param Controller $controller [description]
     */
    public function initialize(Controller $controller)
    {
        $this->controller = $controller;
        $this->environment = $this->get_current_environment();
        $this->Category = ClassRegistry::init('Category');

        if ('platform' == $this->environment) {
            $this->config = Configure::read('zendesk');
        }
        $this->API = new ZendeskAPI($this->config['subdomain'] ?? null);
        $this->API->setAuth('basic', [
            'username' => $this->config['username'] ?? null,
            'token' => $this->config['token'] ?? null,
        ]);
    }

    //Use this to build the array to input to Zendesk for organizations

    /**
     * @param $branch
     *
     * @return bool
     */
    public function formatOrganization($branch)
    {
        $this->environment = $this->get_current_environment();
        if ('platform' != $this->environment) {
            return true;
        }
        if (!empty($branch['Branch']['visible']['0'])) {
            $bundle = str_replace('_', '', $branch['Branch']['visible']['0']);
        } else {
            $bundle = 'glofox';
        }

        if (isset($branch['Branch']['categories']['0'])) {
            $category = $this->Category->getNameById($branch['Branch']['categories']['0']);
        } else {
            $category = '';
        }

        $organization['tags'] = [$bundle, str_replace(' ', '_', $category)];
        $organization['external_id'] = $branch['Branch']['_id'];
        $organization['name'] = $branch['Branch']['name'];
        $organization['shared_tickets'] = true;
        $organization['shared_comments'] = true;

        $organization_fields['namespace'] = $branch['Branch']['namespace'];
        $organization_fields['timezone_name'] = $branch['Branch']['address']['timezone_name'];

        //these variables will only be set on update, not creation
        if (!empty($branch['Branch']['stripe_customer_id'])) {
            $organization_fields['stripe_customer_id'] = $branch['Branch']['stripe_customer_id'];
        }

        $organization_fields['stripe_plan_code'] = ($branch['Branch']['stripe_plan_code'] ?? 'gold');
        $organization_fields['glofox_creation_date'] = $this->Category->formatDate($branch['Branch']['created'], 'datetime', $branch['Branch']['address']['timezone_name']);
        $organization['organization_fields'] = $organization_fields;

        return $organization;
    }

    //Use this to build the array to input to Zendesk for users. have to pass branch for timezone and language

    /**
     * @param $user
     *
     * @return bool
     */
    public function formatUser($user)
    {
        $this->environment = $this->get_current_environment();
        if ('platform' != $this->environment) {
            return true;
        }
        //first find organization_id in Zendesk
        $organization = $this->getOrganizationByExternalId($user['User']['branch_id']);
        $iuser['external_id'] = $user['User']['_id'];
        $iuser['email'] = $user['User']['email'];
        $iuser['phone'] = $user['User']['phone'];
        $iuser['name'] = $user['User']['first_name'] . ' ' . $user['User']['last_name'];
        $iuser['organization_id'] = $organization->organizations['0']->id;
        $iuser['tags'] = [strtolower($user['User']['type'])];
        $iuser['role'] = 'end-user';

        return $iuser;
    }

    /**
     * @param $branch_id
     *
     * @return bool
     */
    public function getOrganizationByExternalId($branch_id)
    {
        $this->environment = $this->get_current_environment();
        if ('platform' != $this->environment) {
            return true;
        }
        try {
            $organization = $this->API->organizations()->search($branch_id);
        } catch (\Zendesk\API\Exceptions\ApiResponseException $e) {
            $this->log($e->__toString());
        }

        return $organization;
    }

    /**
     * @param $organization
     *
     * @return bool
     */
    public function createOrganization($organization)
    {
        $this->environment = $this->get_current_environment();
        if ('platform' != $this->environment) {
            return true;
        }
        try {
            $organization = $this->API->organizations()->create($organization);
        } catch (\Zendesk\API\Exceptions\ApiResponseException $e) {
            $this->log($e->__toString());
        }

        return $organization;
    }

    /**
     * @param $organization
     *
     * @return bool
     */
    public function updateOrganization($organization)
    {
        $this->environment = $this->get_current_environment();
        if ('platform' != $this->environment) {
            return true;
        }
        $zendesk_org = $this->getOrganizationByExternalId($organization['external_id']);
        try {
            $organization = $this->API->organizations()->update($zendesk_org->organizations['0']->id, $organization);
        } catch (\Zendesk\API\Exceptions\ApiResponseException $e) {
            $this->log($e->__toString());
        }

        return $organization;
    }

    /**
     * @param $user
     *
     * @return bool
     */
    public function createUser($user)
    {
        $this->environment = $this->get_current_environment();
        if ('platform' != $this->environment) {
            return true;
        }
        try {
            $user = $this->API->users()->create($user);
        } catch (\Zendesk\API\Exceptions\ApiResponseException $e) {
            $this->log($e->__toString());
        }

        return $user;
    }

    //using this to test connection, it should return all tickets in Zendesk

    /**
     * @return bool
     */
    public function getAllTickets()
    {
        $this->environment = $this->get_current_environment();
        if ('platform' != $this->environment) {
            return true;
        }
        try {
            // Get all tickets
            $tickets = $this->API->tickets()->findAll();
            print_r($tickets);
        } catch (\Zendesk\API\Exceptions\ApiResponseException $e) {
            echo 'Please check your credentials. Make sure to change the $subdomain, $username, and $token variables in this file.';
        }
        die();
    }

    /**
     * [get_current_environment description].
     *
     * @return string [type] [description]
     */
    private function get_current_environment()
    {
        if (!isset($_ENV['OPENSHIFT_APP_NAME'])) {
            return 'local';
        } elseif ('platform' == $_ENV['OPENSHIFT_APP_NAME']) {
            return 'platform';
        } elseif ('staging' == $_ENV['OPENSHIFT_APP_NAME']) {
            return 'staging';
        } elseif ('development' == $_ENV['OPENSHIFT_APP_NAME']) {
            return 'development';
        }
    }
}
