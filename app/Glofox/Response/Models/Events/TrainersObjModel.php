<?php

namespace Glofox\Response\Models\Events;

use Glofox\Response\BaseModel;

/**
 * @SuppressWarnings(PHPMD)
 */
class TrainersObjModel extends BaseModel
{
    /**
     * @var string
     */
    public $_id = '';

    /**
     * @var string
     */
    public $first_name = '';

    /**
     * @var string
     */
    public $last_name = '';

    /**
     * @var string
     */
    public $branch_id = '';

    /**
     * @var string
     */
    public $type = '';

    /**
     * @var string
     */
    public $image_url = '';

    /**
     * @var string
     */
    public $name = '';

    /**
     * @var string
     */
    public $description = '';

    protected array $allowedFields = [
        '_id',
        'first_name',
        'last_name',
        'branch_id',
        'type',
        'image_url',
        'name',
        'description'
    ];

    public function __construct(array $data)
    {
        $this->setModelFields($data);
    }
}
