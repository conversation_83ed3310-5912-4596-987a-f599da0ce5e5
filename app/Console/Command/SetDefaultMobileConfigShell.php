<?php

ini_set('max_execution_time', 600);

App::uses('Shell', 'Console');

class SetDefaultMobileConfigShell extends AppShell
{
    protected $branches;

    protected $branchMobileConfig;

    protected $program;

    protected $course;

    protected $facility;

    /**
     * FirstBookingCreditsShell constructor.
     * @param null $stdout
     * @param null $stderr
     * @param null $stdin
     */
    public function __construct($stdout = null, $stderr = null, $stdin = null)
    {
        parent::__construct($stdout, $stderr, $stdin);

        $this->branches = ClassRegistry::init('Branch');
        $this->branchMobileConfig = ClassRegistry::init('BranchConfigurationMobile');
        $this->program = ClassRegistry::init('Program');
        $this->course = ClassRegistry::init('Course');
        $this->facility = ClassRegistry::init('Facility');
        $this->updatedBranches = 0;
    }

    public function main()
    {
        $this->log("~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~", LOG_INFO);
        $this->log("Cron Started: " . self::class, LOG_INFO);

        $conditions = ['active' => true];
        $fields = ['namespace', 'configuration.webportal', 'features.store'];
        $page = 1;
        $limit = 100;
        $credits = null;
        $branchId = $this->args[0] ?? null;
        if (!empty($branchId)) {
            $conditions['_id'] = $branchId;
        }

        $branches = $this->branches->find('all', ['conditions' => $conditions, 'fields' => $fields]);
        foreach ($branches as $branch) {
            $this->log(
                sprintf("namespace: %s, branchId: %s", $branch['Branch']['namespace'], $branch['Branch']['_id']),
                LOG_INFO
            );
            $conditions = ['branch_id' => $branch['Branch']['_id']];
            $mobileConfig = $this->branchMobileConfig->find('first', ['conditions' => $conditions]);
            $requireSaving = false;
            $mobileConfigPreChanges = ($mobileConfig) ? $mobileConfig['BranchConfigurationMobile'] : [];

            $this->log(
                sprintf(
                    "mobileConfigPreChanges: %s",
                    json_encode($mobileConfigPreChanges, JSON_PARTIAL_OUTPUT_ON_ERROR)
                ),
                LOG_INFO
            );

            if (empty($mobileConfig)) {
                $mobileConfig['BranchConfigurationMobile'] = [
                    'branch_id' => $branch['Branch']['_id'],
                    'namespace' => $branch['Branch']['namespace']
                ];
                $requireSaving = true;
            }

            if (empty($mobileConfig['BranchConfigurationMobile']['images'])) {
                $mobileConfig['BranchConfigurationMobile']['images'] = $this->imageDefault();
                $requireSaving = true;
            }

            if ($mobileConfig['BranchConfigurationMobile']['images']['programsImage'] == "") {
                $mobileConfig['BranchConfigurationMobile']['images']['programsImage'] = $this->getDefaultS3Url(
                    $branch,
                    "program"
                );
                $requireSaving = true;
            }

            if ($mobileConfig['BranchConfigurationMobile']['images']['coursesImage'] == "") {
                $mobileConfig['BranchConfigurationMobile']['images']['coursesImage'] = $this->getDefaultS3Url(
                    $branch,
                    "course"
                );
                $requireSaving = true;
            }

            if ($mobileConfig['BranchConfigurationMobile']['images']['facilitiesImage'] == "") {
                $mobileConfig['BranchConfigurationMobile']['images']['facilitiesImage'] = $this->getDefaultS3Url(
                    $branch,
                    "facility"
                );
                $requireSaving = true;
            }

            if (empty($mobileConfig['BranchConfigurationMobile']['colors'])) {
                $mobileConfig['BranchConfigurationMobile']['colors'] = $this->colorsDefault($branch);
                $requireSaving = true;
            }

            if (empty($mobileConfig['BranchConfigurationMobile']['settings'])) {
                $mobileConfig['BranchConfigurationMobile']['settings'] = $this->settingsDefault($branch);
                $requireSaving = true;
            }

            if (empty($mobileConfig['BranchConfigurationMobile']['filters'])) {
                $mobileConfig['BranchConfigurationMobile']['filters'] = $this->filtersDefault();
                $requireSaving = true;
            }

            if ($requireSaving) {
                $this->branchMobileConfig->clear();
                $save = $this->branchMobileConfig->save($mobileConfig);
                if ($save) {
                    $this->updatedBranches++;
                    $this->log(
                        sprintf(
                            "mobileConfigAfterChanges: %s",
                            json_encode($save['BranchConfigurationMobile'], JSON_PARTIAL_OUTPUT_ON_ERROR)
                        ),
                        LOG_INFO
                    );
                }
            }
        }
        $this->log(sprintf("%d branches updated", $this->updatedBranches), LOG_INFO);
        $this->log("Cron Finished", LOG_INFO);
    }

    private function colorsDefault($branch)
    {
        $background = $branch['Branch']['configuration']['webportal']['colors']['background'] ?? '';
        $foreground = $branch['Branch']['configuration']['webportal']['colors']['accent'] ?? '';
        $text = $branch['Branch']['configuration']['webportal']['colors']['text'] ?? '';

        return [
            "background" => $this->formatColor($background),
            "foreground" => $this->formatColor($foreground),
            "text" => $this->formatColor($text)
        ];
    }

    /**
     * This is to make sure the color starts
     * with a # character
     * @return [type] [description]
     */
    private function formatColor(string $color)
    {
        if (!empty($color)) {
            $color = ($color[0] === '#') ? $color : '#' . $color;
        }
        return $color;
    }

    private function filtersDefault()
    {
        return [
            "displayClassesFilter" => true,
            "displayCoursesFilter" => true,
            "displayFacilitiesFilter" => false,
            "displayTrainersFilter" => true,
            "displayCategoriesFilter" => false
        ];
    }

    private function imageDefault()
    {
        return [
            "primaryLogo" => "",
            "secondaryLogo" => "",
            "programsImage" => "",
            "coursesImage" => "",
            "trainersImage" => "",
            "facilitiesImage" => "",
        ];
    }

    private function getDefaultS3Url($branch, $type)
    {
        $s3_base_url = "https://s3-eu-west-1.amazonaws.com/glofox/";
        $environment = GlofoxEnvironment::currentEnvironment();
        $namespace = $branch['Branch']['namespace'];
        $branchId = $branch['Branch']['_id'];
        $conditions = [
            'active' => true,
            'branch_id' => $branchId
        ];
        $image_url = "";

        if ($type == "program") {
            $first_program = $this->program->find('first', ['conditions' => $conditions]);
            $image_url = $s3_base_url . $environment . "/" . $namespace . '/branches/' . $branchId . "/programs/" . $first_program["Program"]["_id"] . '/default.png';
        }

        if ($type == "course") {
            $first_course = $this->course->find('first', ['conditions' => $conditions]);
            $image_url = $s3_base_url . $environment . "/" . $namespace . '/branches/' . $branchId . "/courses/" . $first_course["Course"]["_id"] . '/default.png';
        }

        if ($type == "facility") {
            $first_facility = $this->facility->find('first', ['conditions' => $conditions]);
            $image_url = $s3_base_url . $environment . "/" . $namespace . '/branches/' . $branchId . "/facilities/" . $first_facility["Facility"]["_id"] . '/default.png';
        }

        return $image_url;
    }

    private function settingsDefault($branch)
    {
        $webportalConfig = $branch['Branch']['configuration']['webportal'] ?? [];
        $displayClassesOnSchedule = $webportalConfig['features']['general']['classes'] ?? false;
        $displayCoursesOnSchedule = $webportalConfig['features']['general']['courses'] ?? false;
        $displayTrainersAppointmentsOnSchedule = $webportalConfig['features']['general']['trainers'] ?? false;
        $displayFacilitiesAppointmentsOnSchedule = $webportalConfig['features']['general']['facilities'] ?? false;
        $displayStoreTab = $branch['Branch']['features']['store']['enabled'] ?? false;

        return [
            "displayPastEvents" => false,
            "displayClassesOnSchedule" => $displayClassesOnSchedule,
            "displayCoursesOnSchedule" => $displayCoursesOnSchedule,
            "displayTrainersAppointmentsOnSchedule" => $displayTrainersAppointmentsOnSchedule,
            "displayFacilitiesAppointmentsOnSchedule" => $displayFacilitiesAppointmentsOnSchedule,
            "displayCreditHistoryTab" => false,
            "displayStoreTab" => $displayStoreTab,
            "displayNewsTab" => false,
            "displayTrainersNamesOnEvent" => false,
            "displayFacilityNameOnEvent" => false,
            "displayLevelOnEvent" => true,
            "displaySizeOnEvent" => true,
            "displayWaitlingSizeOnEvent" => false,
            "eventSizeFormat" => "SpacesRemaining"
        ];
    }
}
