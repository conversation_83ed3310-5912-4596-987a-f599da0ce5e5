<?php

namespace CakeTestCases\Glofox\Authentication;

use App;
use Glofox\Authentication\PasswordHasher;

App::import('Test/Case', 'GlofoxTestCase');

class PasswordHasherTest extends \GlofoxTestCase
{
    public function testShouldReturnTrueForValidPasswordAndHash()
    {
        $passwordHasher = app()->make(PasswordHasher::class);
        $password = 'password123';
        $bcryptHash = password_hash($password, PASSWORD_ARGON2I, [
            'cost' => 10,
        ]);
        self::assertTrue($passwordHasher->verify($password, $bcryptHash));
    }

    public function testShouldReturnFalseForWrongPasswordAndHash()
    {
        $passwordHasher = app()->make(PasswordHasher::class);
        $password = 'password123';
        $bcryptHash = password_hash($password, PASSWORD_ARGON2I, [
            'cost' => 10,
        ]);
        self::assertFalse($passwordHasher->verify('123467', $bcryptHash));

        $md5Hash = md5($password);
        $hash = $passwordHasher->make($md5Hash);
        self::assertFalse($passwordHasher->verify(md5('wrongPassword'), $hash));
    }

    public function testShouldCreateArgon2IHash()
    {
        $password = 'secret';
        $passwordHasher = app()->make(PasswordHasher::class);
        $hash = $passwordHasher->make($password);
        self::assertTrue($passwordHasher->verify('secret', $hash));

        $md5Hash = md5($password);
        $hash = $passwordHasher->make($md5Hash);
        self::assertTrue($passwordHasher->verify($md5Hash, $hash));
    }
}
