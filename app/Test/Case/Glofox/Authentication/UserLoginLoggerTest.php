<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Authentication;

use Glofox\Authentication\UserLoginLogger;
use Glofox\Domain\Users\Models\User;
use Glofox\Request;
use Psr\Log\LoggerInterface;

\App::import('Test/Case', 'GlofoxTestCase');

class UserLoginLoggerTest extends \GlofoxTestCase
{
    public function test_it_logs_all_the_required_data(): void
    {
        $request = \Mockery::mock(Request::class);
        $request->shouldReceive('header')
            ->with('X-Forwarded-For')
            ->andReturn('***********, ***********');

        $logger = \Mockery::mock(LoggerInterface::class);
        $logger->shouldReceive('info')
            ->once()
            ->withArgs(
                function (string $message, array $context) {
                    $this->assertSame('User user-1234567890 logged in', $message);
                    $this->assertSame([
                        'userId' => 'user-1234567890',
                        'branchId' => 'branch-1234567890',
                        'namespace' => 'foo',
                        'userType' => 'ADMIN',
                        'remoteIps' => [
                            '***********',
                            '***********',
                        ],
                    ], $context);

                    return true;
                }
            );

        $instance = new UserLoginLogger($request, $logger);
        $instance->log(
            new User([
                '_id' => 'user-1234567890',
                'branch_id' => 'branch-1234567890',
                'namespace' => 'foo',
                'type' => 'ADMIN',
            ])
        );
    }

    public function test_it_logs_an_empty_array_of_ips_if_no_header_was_found(): void
    {
        $request = \Mockery::mock(Request::class);
        $request->shouldReceive('header')->andReturnNull();

        $logger = \Mockery::mock(LoggerInterface::class);
        $logger->shouldReceive('info')
            ->once()
            ->withArgs(
                function (string $message, array $context) {
                    $this->assertSame('User user-1234567890 logged in', $message);
                    $this->assertSame([], $context['remoteIps']);

                    return true;
                }
            );

        $instance = new UserLoginLogger($request, $logger);
        $instance->log(
            new User([
                '_id' => 'user-1234567890',
                'branch_id' => 'branch-1234567890',
                'namespace' => 'foo',
                'type' => 'ADMIN',
            ])
        );
    }
}
