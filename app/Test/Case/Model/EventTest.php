<?php

use CakeTestCases\Glofox\Domain\Authentication\Token\TokenGeneration;
use CakeTestCases\Glofox\Domain\Users\Traits\FetchUsersTrait;
use Carbon\Carbon;
use Glofox\Datasource\Exceptions\InvalidMongoIdException;
use Glofox\Domain\Authentication\Auth;
use Glofox\Domain\Events\Internal\Parameters\GetStatusParameters;
use Glofox\Domain\Events\Internal\Parameters\PricingForUserParameters;
use Glofox\Domain\Events\Internal\Parameters\SetCalculatedFieldsParameters;
use Glofox\Domain\Events\MemberEligibility;
use Glofox\Domain\Events\Models\Event as EventModel;
use Glofox\Domain\Events\Services\EventsPublisher;
use Glofox\Domain\Events\Status as EventStatus;
use Glofox\Domain\Events\Transformers\AddVirtualOnlineFieldTransformer;
use Glofox\Domain\Events\Validation\EventIsOnlineValidatorInterface;
use Glofox\Domain\Integrations\Identifier;
use Glofox\Domain\Memberships\Models\Addon;
use Glofox\Domain\Memberships\Services\AddonEligibilityServiceInterface;
use Glofox\Domain\Programs\Exceptions\ProgramNotFoundException;
use Glofox\Domain\TimeSlots\Models\TimeSlot;
use Glofox\Domain\Users\Models\User as UserModel;

App::import('Test/Case', 'GlofoxControllerTestCase');
App::uses('Event', 'Model');

/**
 * EventTest Test Case.
 *
 * @property Event Event
 */
class EventTest extends GlofoxControllerTestCase
{
    use FetchUsersTrait;
    use TokenGeneration;

    public $fixtures = [
        'app.branch_configuration_mobile',
        'app.payment_method',
        'app.payment_provider',
        'app.access_control_list',
        'app.branch',
        'app.user',
        'app.client',
        'app.membership',
        'app.interaction',
        'app.event',
        'app.booking',
        'app.program',
        'app.facility',
        'app.role',
        'app.time_slot_pattern',
        'app.time_slot',
        'app.push_notification',
        'app.integration',
        'app.integrator',
        'app.user_credit'
    ];

    private const DEFAULT_DATE_FORMAT = 'Y-m-d H:i:s';
    private const PROGRAM_WITH_CATEGORY_REQUIREMENTS = '67e508db1f2ea29d1b38cbc1';
    private const USER_ID_FOR_CATEGORY_AND_CLASS_SUCCESSFUL_VALIDATION = '77207c1cd7b6ddc3a98b4589';
    private const USER_ID_FOR_CATEGORY_AND_CLASS_FAILED_VALIDATION = '77207c1cd7b6ddc3a98b4588';

    private const BRANCH_ID = '49a7011a05c677b9a916612a';

    /** @var Event */
    private $Event;

    /** @var User */
    private $User;

    /**
     * setUp method.
     */
    public function setUp()
    {
        parent::setUp();
        $this->Event = ClassRegistry::init('Event');
        $this->User = ClassRegistry::init('User');
    }

    /**
     * tearDown method.
     */
    public function tearDown()
    {
        parent::tearDown();
        unset($this->Event);

        Mockery::close();
    }

    /**
     * testAutoBook.
     */
    public function testAutoBookForGetTotalBookingsAndTotalWaiting(): void
    {
        $eventIds = [
            '5adf43c0db6b6587ebcb2c5b',
            '5ae1b7a0df7366855fd28702',
            '5b043d0d45702d48c57deda7'
        ];
        $result = $this->Event->getTotalBookingsAndTotalWaiting($eventIds);

        $message = 'There are 2 bookings for this event, 1 from glofox and 1 from classpass';

        $this->assertEquals('5adf43c0db6b6587ebcb2c5b', $result[0]['_id'], $message);
        $this->assertEquals(2, $result[0]['value']['totalBookings'], $message);
        $this->assertEquals(0, $result[0]['value']['totalWaitings'], $message);
    }

    public function testIfGetTotalBookingsAndTotalWaitingCountsProperly(): void
    {
        $expected = [
            '49b7012a05c677c9a512503c' => ['totalBookings' => 11, 'totalWaitings' => 0, 'usersCount' => 6],
            '29b7012a05c677c9a512503d' => ['totalBookings' => 0, 'totalWaitings' => 2, 'usersCount' => 2],
            '49b7012a05c677c9a5125023' => ['totalBookings' => 1, 'totalWaitings' => 0, 'usersCount' => 1],
            '59e63079e71da189c2b0ea00' => ['totalBookings' => 2, 'totalWaitings' => 0, 'usersCount' => 2],
            '5ade08479293121c1f9b4245' => ['totalBookings' => 1, 'totalWaitings' => 0, 'usersCount' => 1],
            '5adf43c0db6b6587ebcb2c5b' => ['totalBookings' => 2, 'totalWaitings' => 0, 'usersCount' => 2],
            '5ae1b7a0df7366855fd28702' => ['totalBookings' => 1, 'totalWaitings' => 0, 'usersCount' => 1],
            '5ae99803c53fbc7079cfd7fc' => ['totalBookings' => 3, 'totalWaitings' => 0, 'usersCount' => 3],
            '5b043d0d45702d48c57deda7' => ['totalBookings' => 3, 'totalWaitings' => 1, 'usersCount' => 4],
            '5e7cb6713d36321bf52c63d7' => ['totalBookings' => 1, 'totalWaitings' => 0, 'usersCount' => 1],
            '63d93312ece438f37486ee2c' => ['totalBookings' => 0, 'totalWaitings' => 32, 'usersCount' => 1],
            '63d9408704d422118e7116e0' => ['totalBookings' => 32, 'totalWaitings' => 0, 'usersCount' => 1],
        ];

        $results = $this->Event->getTotalBookingsAndTotalWaiting(array_keys($expected));

        $this->assertEquals(12, is_countable($results) ? count($results) : 0);

        foreach ($results as $result) {
            $expectedValue = $expected[$result['_id']];

            $this->assertEquals($expectedValue['totalBookings'], $result['value']['totalBookings']);
            $this->assertEquals($expectedValue['totalWaitings'], $result['value']['totalWaitings']);
            $this->assertCount($expectedValue['usersCount'], $result['value']['users']);
        }
    }

    public function testSavingEventProvidesUpdatedTotalBookings(): void
    {
        $event = $this->Event->findByIdNoModel('5adf43c0db6b6587ebcb2c5b');

        $result = $this->Event->save($event);

        $this->assertEquals(2, $result['Event']['total_bookings']);
    }

    public function testGetEventSize(): void
    {
        // 2 available spots, ClassPass size 3 and 1 classpass booking
        $eventAvailableSpots = 2;
        $integrationEventSize = 3;
        $integrationTotalBookings = 1;
        $result = $this->Event->getIntegrationEventSize(
            $eventAvailableSpots,
            $integrationEventSize,
            $integrationTotalBookings
        );
        $expected = 3;
        $this->assertEquals($expected, $result, '2 available spots, ClassPass size 3 and 1 classpass booking');

        // 4 available spots, ClassPass size 3 and 2 classpass booking
        $eventAvailableSpots = 4;
        $integrationEventSize = 3;
        $integrationTotalBookings = 2;
        $result = $this->Event->getIntegrationEventSize(
            $eventAvailableSpots,
            $integrationEventSize,
            $integrationTotalBookings
        );
        $expected = 3;
        $this->assertEquals($expected, $result, '4 available spots, ClassPass size 3 and 2 classpass booking');

        // 1 available spots, ClassPass size 3 and 2 classpass booking
        $eventAvailableSpots = 1;
        $integrationEventSize = 3;
        $integrationTotalBookings = 2;
        $result = $this->Event->getIntegrationEventSize(
            $eventAvailableSpots,
            $integrationEventSize,
            $integrationTotalBookings
        );
        $expected = 3;
        $this->assertEquals($expected, $result, '1 available spots, ClassPass size 3 and 2 classpass booking');

        // 1 available spots, ClassPass size 3 and 1 classpass booking
        $eventAvailableSpots = 1;
        $integrationEventSize = 3;
        $integrationTotalBookings = 1;
        $result = $this->Event->getIntegrationEventSize(
            $eventAvailableSpots,
            $integrationEventSize,
            $integrationTotalBookings
        );
        $expected = 2;
        $this->assertEquals($expected, $result, '1 available spots, ClassPass size 3 and 1 classpass booking');

        // No available spots, ClassPass size 3 and 1 classpass booking
        $eventAvailableSpots = 0;
        $integrationEventSize = 3;
        $integrationTotalBookings = 1;
        $result = $this->Event->getIntegrationEventSize(
            $eventAvailableSpots,
            $integrationEventSize,
            $integrationTotalBookings
        );
        $expected = 1;
        $this->assertEquals($expected, $result, 'No available spots, ClassPass size 3 and 1 classpass booking');
    }

    /**
     * @dataProvider getOpenBookingTimeProvider
     */
    public function testGetOpenBookingTime(int $timeStart, ?int $openBookingWindow, ?int $expected): void
    {
        $result = $this->Event->getOpenBookingTime($timeStart, $openBookingWindow);
        $this->assertEquals($result, $expected);
    }

    public function getOpenBookingTimeProvider(): array
    {
        return [
            '12 hours open booking window' => [
                1_527_169_456, // Thursday, May 24, 2018 1:44:16 PM
                12,
                1_527_126_256, // Thursday, May 24, 2018 01:44:16 AM
            ],
            'No open booking window set' => [
                1_527_169_456, // Thursday, May 24, 2018 1:44:16 PM
                null,
                null,
            ],
        ];
    }

    /**
     * @dataProvider getCloseBookingTimeProvider
     */
    public function testGetCloseBookingTime(int $timeStart, ?int $closeBookingWindow, ?int $expected): void
    {
        $result = $this->Event->getCloseBookingTime($timeStart, $closeBookingWindow);
        $this->assertEquals($result, $expected);
    }

    public function getCloseBookingTimeProvider(): array
    {
        return [
            '20 hours open booking window' => [
                1_527_169_456, // Thursday, May 24, 2018 1:44:16 PM
                20,
                1_527_097_456, // Wednesday, May 23, 2018 5:44:16 PM
            ],
            'No open booking window set' => [
                1_527_169_456, // Thursday, May 24, 2018 1:44:16 PM
                null,
                1_527_169_456,
            ],
        ];
    }

    /**
     * @dataProvider getStatusProvider
     */
    public function testGetStatus($parameters, $expected): void
    {
        $result = $this->Event->getStatus($parameters);
        $this->assertEquals($result, $expected);
    }

    public function getStatusProvider(): array
    {
        return [
            'Inside booking windows and there is capacity and the member already booked' => [
                new GetStatusParameters(
                    [
                        'openBookingTime' => 1_527_138_000, // Thursday, May 24, 2018 5:00:00 AM
                        'closeBookingTime' => 1_527_199_200, // Thursday, May 24, 2018 10:00:00 PM
                        'booked' => 1,
                        'waiting' => 0,
                        'size' => 12,
                        'waitingListSize' => 3,
                        'currentTime' => 1_527_188_400, // Thursday, May 24, 2018 7:00:00 PM
                        'memberAlreadyBooked' => true,
                    ]
                ),
                EventStatus::ALREADY_BOOKED(),
            ],
            'Inside booking windows and there is capacity' => [
                new GetStatusParameters(
                    [
                        'openBookingTime' => 1_527_138_000, // Thursday, May 24, 2018 5:00:00 AM
                        'closeBookingTime' => 1_527_199_200, // Thursday, May 24, 2018 10:00:00 PM
                        'booked' => 1,
                        'waiting' => 0,
                        'size' => 12,
                        'waitingListSize' => 3,
                        'currentTime' => 1_527_188_400, // Thursday, May 24, 2018 7:00:00 PM
                    ]
                ),
                EventStatus::AVAILABLE(),
            ],
            'Inside booking windows but there is no capacity' => [
                new GetStatusParameters(
                    [
                        'openBookingTime' => 1_527_138_000, // Thursday, May 24, 2018 5:00:00 AM
                        'closeBookingTime' => 1_527_199_200, // Thursday, May 24, 2018 10:00:00 PM
                        'booked' => 12,
                        'waiting' => 3,
                        'size' => 12,
                        'waitingListSize' => 3,
                        'currentTime' => 1_527_188_400, // Thursday, May 24, 2018 7:00:00 PM
                    ]
                ),
                EventStatus::FULLY_BOOKED(),
            ],
            'Before open booking window and there is capacity' => [
                new GetStatusParameters(
                    [
                        'openBookingTime' => 1_527_138_000, // Thursday, May 24, 2018 5:00:00 AM
                        'closeBookingTime' => 1_527_199_200, // Thursday, May 24, 2018 10:00:00 PM
                        'booked' => 1,
                        'waiting' => 0,
                        'size' => 12,
                        'waitingListSize' => 3,
                        'currentTime' => 1_527_130_800, // Thursday, May 24, 2018 3:00:00 AM
                    ]
                ),
                EventStatus::BOOKING_WINDOW_NOT_OPEN(),
            ],
            'After close booking window and there is capacity' => [
                new GetStatusParameters(
                    [
                        'openBookingTime' => 1_527_138_000, // Thursday, May 24, 2018 5:00:00 AM
                        'closeBookingTime' => 1_527_199_200, // Thursday, May 24, 2018 10:00:00 PM
                        'booked' => 1,
                        'waiting' => 0,
                        'size' => 12,
                        'waitingListSize' => 3,
                        'currentTime' => 1_527_202_800, // Thursday, May 24, 2018 11:00:00 PM
                    ]
                ),
                EventStatus::BOOKING_WINDOW_PASSED(),
            ],
            'Inside booking windows and the class is full but there a waiting list spots available' => [
                new GetStatusParameters(
                    [
                        'openBookingTime' => 1_527_138_000, // Thursday, May 24, 2018 5:00:00 AM
                        'closeBookingTime' => 1_527_199_200, // Thursday, May 24, 2018 10:00:00 PM
                        'booked' => 12,
                        'waiting' => 1,
                        'size' => 12,
                        'waitingListSize' => 3,
                        'currentTime' => 1_527_188_400, // Thursday, May 24, 2018 7:00:00 PM
                    ]
                ),
                EventStatus::JOIN_WAITING_LIST(),
            ],
            'After close booking window and there is no capacity' => [
                new GetStatusParameters(
                    [
                        'openBookingTime' => 1_527_138_000, // Thursday, May 24, 2018 5:00:00 AM
                        'closeBookingTime' => 1_527_199_200, // Thursday, May 24, 2018 10:00:00 PM
                        'booked' => 12,
                        'waiting' => 3,
                        'size' => 12,
                        'waitingListSize' => 3,
                        'currentTime' => 1_527_202_800, // Thursday, May 24, 2018 11:00:00 PM
                    ]
                ),
                EventStatus::FULLY_BOOKED(),
            ],
            'Before open booking window and there is no capacity' => [
                new GetStatusParameters(
                    [
                        'openBookingTime' => 1_527_138_000, // Thursday, May 24, 2018 5:00:00 AM
                        'closeBookingTime' => 1_527_199_200, // Thursday, May 24, 2018 10:00:00 PM
                        'booked' => 12,
                        'waiting' => 3,
                        'size' => 12,
                        'waitingListSize' => 3,
                        'currentTime' => 1_527_130_800, // Thursday, May 24, 2018 3:00:00 AM
                    ]
                ),
                EventStatus::FULLY_BOOKED(),
            ],
        ];
    }

    public function testSetCalculatedFieldsAvailable(): void
    {
        $user = $this->fetchUser('59a3011a05c677bda916611c');
        $token = $this->loginAsUser($user);

        //Set Class token for future use
        $_SERVER['HTTP_AUTHORIZATION'] = $token;

        $event = $this->Event->findById('5b1ffbb93ea27bbd65e91760')['Event'];
        $event['time_start'] = 1_527_138_000;
        $event['booked'] = false;
        $event['has_booked'] = false;

        $parameters = new SetCalculatedFieldsParameters(
            [
                'openBookingWindow' => 10,
                'closeBookingWindow' => 2,
                'waitingListSize' => 0,
                'currentTime' => 1_527_130_800, // Thursday, May 24, 2018 3:00:00 AM
            ]
        );

        $parameters->setEvent($event);

        $result = $this->Event->setCalculatedFields($parameters);

        $expected = [
            'time_start' => 1_527_138_000, // Thursday, May 24, 2018 5:00:00 AM
            'size' => 20,
            'has_booked' => false,
            'open_booking_time' => 1_527_102_000, // Wednesday, May 23, 2018 7:00:00 PM
            'close_booking_time' => 1_527_130_800, // Thursday, May 24, 2018 3:00:00 AM
            'status' => 'AVAILABLE',
            'booked' => $event['booked'],
        ];

        foreach ($expected as $key => $expectation) {
            $this->assertEquals($expectation, $result[$key]);
        }
    }

    public function testSetCalculatedFieldsGuestEligible(): void
    {
        $token = $this->loginAsGuest('glofox', self::BRANCH_ID);

        //Set Class token for future use
        $_SERVER['HTTP_AUTHORIZATION'] = $token;

        $event = $this->Event->findById('5b1ffbb93ea27bbd65e91761')['Event'];
        $event['time_start'] = 1_527_138_000;
        $event['booked'] = false;
        $event['has_booked'] = false;

        $parameters = new SetCalculatedFieldsParameters(
            [
                'openBookingWindow' => 10,
                'closeBookingWindow' => 2,
                'waitingListSize' => 0,
                'currentTime' => 1_527_130_800, // Thursday, May 24, 2018 3:00:00 AM
            ]
        );

        $parameters->setEvent($event);

        $result = $this->Event->setCalculatedFields($parameters);

        $expected = [
            'time_start' => 1_527_138_000, // Thursday, May 24, 2018 5:00:00 AM
            'size' => 20,
            'has_booked' => false,
            'open_booking_time' => 1_527_102_000, // Wednesday, May 23, 2018 7:00:00 PM
            'close_booking_time' => 1_527_130_800, // Thursday, May 24, 2018 3:00:00 AM
            'status' => 'AVAILABLE',
            'booked' => $event['booked'],
        ];

        foreach ($expected as $key => $expectation) {
            $this->assertEquals($expectation, $result[$key]);
        }
    }

    public function test_get_by_id_return_event_with_is_online_field(): void
    {
        $admin = $this->fetchUser('59a7011a05c677bda916612a');
        $this->loginAsUser($admin);

        $validator = Mockery::mock(EventIsOnlineValidatorInterface::class);
        $validator->shouldReceive('validate')->andReturnTrue();

        app()->instance(EventIsOnlineValidatorInterface::class, $validator);

        $result = $this->testAction('/2.0/events/5b1ffbb93ea27bbd65e91761', [
            'method' => 'GET',
        ]);
        $result = json_decode($result, true, 512, JSON_THROW_ON_ERROR);

        self::assertArrayHasKey('is_online', $result);
        self::assertTrue($result['is_online']);

        app()->forgetInstance(EventIsOnlineValidatorInterface::class);
    }

    public function test_get_all_return_events_with_is_online_fields(): void
    {
        $admin = $this->fetchUser('59a7011a05c677bda916612a');
        $this->loginAsUser($admin);

        $onlineFieldTransformer = Mockery::mock(AddVirtualOnlineFieldTransformer::class);
        $onlineFieldTransformer->shouldReceive('execute')->andReturn([
            [
                'branch_id' => 'branch_id',
                'namespace' => 'foo',
                'is_online' => false
            ],
            [
                'branch_id' => 'branch_id',
                'namespace' => 'foo',
                'is_online' => true
            ]
        ]);

        app()->instance(AddVirtualOnlineFieldTransformer::class, $onlineFieldTransformer);

        $result = $this->testAction('/2.0/events', [
            'method' => 'GET',
        ]);
        $result = json_decode($result, true, 512, JSON_THROW_ON_ERROR);

        self::assertFalse($result['data'][0]['is_online']);
        self::assertTrue($result['data'][1]['is_online']);

        app()->forgetInstance(AddVirtualOnlineFieldTransformer::class);
    }

    public function test_get_by_id_return_event_with_external_provider_url_field(): void
    {
        $admin = $this->fetchUser('59a7011a05c677bda512212a');
        $this->loginAsUser($admin);

        $result = $this->testAction('/2.0/events/5b17f057720d8541a113f0fb', [
            'method' => 'GET',
        ]);
        $result = json_decode($result, true, 512, JSON_THROW_ON_ERROR);

        self::assertEquals('https://youtu.be/oavMtUWDBTM', $result['external_provider_stream_url']);
    }

    public function test_get_by_id_return_event_with_external_provider_url_field_null_if_it_is_empty(): void
    {
        $admin = $this->fetchUser('59a7011a05c677bda512212a');
        $this->loginAsUser($admin);

        $result = $this->testAction('/2.0/events/5b181037876d8793067a880c', [
            'method' => 'GET',
        ]);
        $result = json_decode($result, true, 512, JSON_THROW_ON_ERROR);

        self::assertNull($result['external_provider_stream_url']);
    }

    public function test_it_sets_the_modified_field_when_generating_events(): void
    {
        $branchId = (string)(new MongoId());

        $program = [
            'Program' => [
                '_id' => new MongoId(),
                'branch_id' => $branchId,
                'namespace' => 'foo',
                'active' => true,
                'name' => 'Foo program',
                'description' => 'Just a foo',
                'schedule' => [
                    [
                        'code' => uniqid('', false),
                        'start_time' => '12:00',
                        'end_time' => '13:00',
                        'level' => 'All levels',
                        'size' => 15,
                        'facility' => (string)(new MongoId()),
                        'trainers' => [
                            (string)(new MongoId()),
                        ],
                        'days_week' => date('w'),
                    ],
                ],
                'schedule_default' => [],
            ],
        ];

        $branch = [
            'Branch' => [
                '_id' => new MongoId($branchId),
                'namespace' => 'foo',
            ],
        ];

        $startDate = date(self::DEFAULT_DATE_FORMAT, strtotime('+5 minutes'));
        $endDate = date(self::DEFAULT_DATE_FORMAT, strtotime('+2 days'));

        $eventModel = $this->givenEventModelMock(
            function (array &$events, array $config) {
                $this->assertSame(['w' => 1], $config);
                foreach ($events as $key => $event) {
                    $this->assertInstanceOf(MongoDate::class, $event['modified']);
                    $events[$key]['_id'] = new MongoId();
                }

                return true;
            }
        );

        $publisher = \Mockery::mock(EventsPublisher::class);
        $publisher->shouldReceive('sendEventCreatedEvent');
        app()->instance(EventsPublisher::class, $publisher);

        $eventModel->generateFrom($program, $branch, $startDate, $endDate);

        app()->forgetInstance(EventsPublisher::class);
    }

    public function testValidationFailsIfNotSupportedCategory(): void
    {
        Carbon::setTestNow(Carbon::parse('2025-04-01 07:00:00'));

        $event = new EventModel([
            '_id' => new MongoId('67e508c0eb798f7699a6ae69'),
            'branch_id' => self::BRANCH_ID,
            'program_id' => self::PROGRAM_WITH_CATEGORY_REQUIREMENTS,
            'date' => new MongoDate(Carbon::parse('2025-04-10 10:00:00')->getTimestamp()),
            'time_start' => new MongoDate(Carbon::parse('2025-04-10 10:00:00')->getTimestamp()),
            'time_finish' => new MongoDate(Carbon::parse('2025-04-10 11:00:00')->getTimestamp()),
        ]);
        $userWithUnlimitedMembership = new UserModel([
            '_id' => self::USER_ID_FOR_CATEGORY_AND_CLASS_FAILED_VALIDATION,
            'branch_id' => self::BRANCH_ID,
            'namespace' => 'glofox',
            'email' => '<EMAIL>',
            'login' => '<EMAIL>',
            'membership' => [
                '_id' => '54107c1cd7b6ddc3a98b4577',
                'plan_code' => '1506335805020',
                'type' => 'time',
            ],
        ]);

        $this->expectExceptionMessage('YOUR_MEMBERSHIP_IS_NOT_ALLOWED');

        $params = new PricingForUserParameters($event, $userWithUnlimitedMembership);
        $this->Event->pricingForUser($params)->pricing();
    }

    public function testValidationSucceedsIfCategoryIsSupported(): void
    {
        Carbon::setTestNow(Carbon::parse('2025-03-01 07:00:00'));

        $event = new EventModel([
            '_id' => new MongoId('67e508c0eb798f7699a6ae69'),
            'branch_id' => self::BRANCH_ID,
            'program_id' => self::PROGRAM_WITH_CATEGORY_REQUIREMENTS,
            'date' => new MongoDate(Carbon::parse('2025-03-10 10:00:00')->getTimestamp()),
            'time_start' => new MongoDate(Carbon::parse('2025-03-10 10:00:00')->getTimestamp()),
            'time_finish' => new MongoDate(Carbon::parse('2025-03-10 11:00:00')->getTimestamp()),
        ]);
        $userWithUnlimitedMembership = new UserModel([
            '_id' => self::USER_ID_FOR_CATEGORY_AND_CLASS_SUCCESSFUL_VALIDATION,
            'branch_id' => self::BRANCH_ID,
            'namespace' => 'glofox',
            'email' => '<EMAIL>',
            'login' => '<EMAIL>',
        ]);

        $params = new PricingForUserParameters($event, $userWithUnlimitedMembership);
        $pricing = $this->Event->pricingForUser($params)->pricing();

        self::assertEquals(1, $pricing->credits());
        self::assertEquals(0, $pricing->price());
    }

    public function testValidationFailsIfNotSupportedClass(): void
    {
        Carbon::setTestNow(Carbon::parse('2025-04-03 07:00:00'));

        $event = new EventModel([
            '_id' => new MongoId('67e508c0eb798f7699a6ae68'),
            'branch_id' => self::BRANCH_ID,
            'program_id' => self::PROGRAM_WITH_CATEGORY_REQUIREMENTS,
            'date' => new MongoDate(Carbon::parse('2025-04-10 11:00:00')->getTimestamp()),
            'time_start' => new MongoDate(Carbon::parse('2025-04-10 11:00:00')->getTimestamp()),
            'time_finish' => new MongoDate(Carbon::parse('2025-04-10 12:00:00')->getTimestamp()),
        ]);
        $userWithUnlimitedMembership = new UserModel([
            '_id' => self::USER_ID_FOR_CATEGORY_AND_CLASS_FAILED_VALIDATION,
            'branch_id' => self::BRANCH_ID,
            'namespace' => 'glofox',
            'email' => '<EMAIL>',
            'login' => '<EMAIL>',
            'membership' => [
                '_id' => '54107c1cd7b6ddc3a98b4577',
                'plan_code' => '1506335805020',
                'type' => 'time',
            ],
        ]);

        $this->expectExceptionMessage('YOUR_MEMBERSHIP_IS_NOT_ALLOWED');

        $params = new PricingForUserParameters($event, $userWithUnlimitedMembership);
        $this->Event->pricingForUser($params)->pricing();
    }

    public function testItReturnsPriceWithoutActiveAddon(): void
    {
        $event = new EventModel([
            '_id' => new MongoId('59ca3b6ac75de1633c2b972b'),
            'branch_id' => self::BRANCH_ID,
            'namespace' => 'glofox',
            'active' => true,
            'program_id' => '59ca3a2f570b4bde3da3decd',
            'schedule_code' => uniqid('', false),
            'level' => 'Advanced',
            'name' => 'Test',
            'description' => 'Class',
            'size' => 20,
            'duration' => 60,
            'date' => new MongoDate(strtotime('+1 weeks')),
            'time_start' => new MongoDate(strtotime('+1 weeks 8 hours')),
            'time_finish' => new MongoDate(strtotime('+1 weeks 9 hours')),
            'trainers' => ['58568a8fa875ab19530041a7'],
            'facility' => '52a7011a05c677bda826611b',
            'total_bookings' => 0,
        ]);
        $user = new UserModel([
            '_id' => '5b19430ea0d988945a164337',
            'branch_id' => self::BRANCH_ID,
            'namespace' => 'glofox',
            'email' => '<EMAIL>',
            'login' => '<EMAIL>',
            'membership' => [
                'type' => 'payg',
            ],
        ]);

        $params = new PricingForUserParameters($event, $user);
        $pricing = $this->Event->pricingForUser($params)->pricing();
        self::assertEquals(0, $pricing->credits());
        self::assertEquals(10, $pricing->price());
    }

    public function testItReturnsPriceWithActiveAddon(): void
    {
        $event = new EventModel([
            '_id' => new MongoId('59ca3b6ac75de1633c2b972b'),
            'branch_id' => self::BRANCH_ID,
            'namespace' => 'glofox',
            'active' => true,
            'program_id' => '59ca3a2f570b4bde3da3decd',
            'schedule_code' => uniqid('', false),
            'level' => 'Advanced',
            'name' => 'Test',
            'description' => 'Class',
            'size' => 20,
            'duration' => 60,
            'date' => new MongoDate(strtotime('+1 weeks')),
            'time_start' => new MongoDate(strtotime('+1 weeks 8 hours')),
            'time_finish' => new MongoDate(strtotime('+1 weeks 9 hours')),
            'trainers' => ['58568a8fa875ab19530041a7'],
            'facility' => '52a7011a05c677bda826611b',
            'total_bookings' => 0,
        ]);
        $user = new UserModel([
            '_id' => '5b19430ea0d988945a164337',
            'branch_id' => self::BRANCH_ID,
            'namespace' => 'glofox',
            'email' => '<EMAIL>',
            'login' => '<EMAIL>',
            'membership' => [
                'type' => 'payg',
            ],
        ]);

        $addon = new Addon(
            'service-id',
            'service-definition-id',
            'service-definition-plan-id',
            'service-definition-name',
            'service-definition-plan-name',
            'TRAINER',
            Carbon::yesterday()->getTimestamp(),
            false
        );

        $addonEligibilityService = Mockery::mock();
        $addonEligibilityService->shouldReceive('validate')
            ->andReturn($addon);

        app()->instance(AddonEligibilityServiceInterface::class, $addonEligibilityService);

        $params = new PricingForUserParameters($event, $user);
        $pricing = $this->Event->pricingForUser($params)->pricing();

        self::assertEquals(0, $pricing->credits());
        self::assertEquals(0, $pricing->price());
        app()->forgetInstance(AddonEligibilityServiceInterface::class);
    }

    public function testItReturnsPriceWithActiveQuantityAddon(): void
    {
        $event = new EventModel([
            '_id' => new MongoId('59ca3b6ac75de1633c2b972b'),
            'branch_id' => self::BRANCH_ID,
            'namespace' => 'glofox',
            'active' => true,
            'program_id' => '59ca3a2f570b4bde3da3decd',
            'schedule_code' => uniqid('', false),
            'level' => 'Advanced',
            'name' => 'Test',
            'description' => 'Class',
            'size' => 20,
            'duration' => 60,
            'date' => new MongoDate(strtotime('+1 weeks')),
            'time_start' => new MongoDate(strtotime('+1 weeks 8 hours')),
            'time_finish' => new MongoDate(strtotime('+1 weeks 9 hours')),
            'trainers' => ['58568a8fa875ab19530041a7'],
            'facility' => '52a7011a05c677bda826611b',
            'total_bookings' => 0,
        ]);
        $user = new UserModel([
            '_id' => '5b19430ea0d988945a164337',
            'branch_id' => self::BRANCH_ID,
            'namespace' => 'glofox',
            'email' => '<EMAIL>',
            'login' => '<EMAIL>',
            'membership' => [
                'type' => 'payg',
            ],
        ]);

        $addon = new Addon(
            'service-id',
            'service-definition-id',
            'service-definition-plan-id',
            'service-definition-name',
            'service-definition-plan-name',
            'TRAINER',
            Carbon::yesterday()->getTimestamp(),
            true
        );
        $addon->setServiceAvailableCredits(3);

        $addonEligibilityService = Mockery::mock();
        $addonEligibilityService->shouldReceive('validate')
            ->andReturn($addon);

        app()->instance(AddonEligibilityServiceInterface::class, $addonEligibilityService);

        $params = new PricingForUserParameters($event, $user);
        $pricing = $this->Event->pricingForUser($params)->pricing();

        self::assertEquals(1, $pricing->credits());
        self::assertEquals(0, $pricing->price());

        app()->forgetInstance(AddonEligibilityServiceInterface::class);
    }

    /**
     * @dataProvider validateSkipsCreditValidationsFromClassPassMoholdingIntegratorsDataProvider
     * @throws ProgramNotFoundException
     * @throws InvalidMongoIdException
     */
    public function testValidateSkipsCreditValidationsFromClassPassMoholdingIntegrators(
        EventModel $event,
        UserModel $user,
        string $identifier
    ): void {
        $addon = null;

        $addonEligibilityService = Mockery::mock();
        $addonEligibilityService->shouldReceive('validate')
            ->andReturn($addon);

        app()->instance(AddonEligibilityServiceInterface::class, $addonEligibilityService);

        $params = new PricingForUserParameters(
            $event,
            $user,
            0,
            true,
            true,
            false,
            $identifier
        );
        $pricing = $this->Event->pricingForUser($params)->pricing();

        self::assertEquals(0, $pricing->price());

        app()->forgetInstance(AddonEligibilityServiceInterface::class);
    }

    public function validateSkipsCreditValidationsFromClassPassMoholdingIntegratorsDataProvider(): array
    {
        $event = new EventModel([
            '_id' => new MongoId('59ca3b6ac75de1633c2b972b'),
            'branch_id' => self::BRANCH_ID,
            'namespace' => 'glofox',
            'active' => true,
            'program_id' => '59ca3a2f570b4bde3da3decd',
            'schedule_code' => uniqid('', false),
            'level' => 'Advanced',
            'name' => 'Test',
            'description' => 'Class',
            'size' => 20,
            'duration' => 60,
            'date' => new MongoDate(strtotime('+1 weeks')),
            'time_start' => new MongoDate(strtotime('+1 weeks 8 hours')),
            'time_finish' => new MongoDate(strtotime('+1 weeks 9 hours')),
            'trainers' => ['58568a8fa875ab19530041a7'],
            'facility' => '52a7011a05c677bda826611b',
            'total_bookings' => 0,
        ]);
        $user = new UserModel([
            '_id' => '6268fbe2c301457c212709f9',
            'branch_id' => self::BRANCH_ID,
            'namespace' => 'glofox',
            'email' => '<EMAIL>',
            'login' => '<EMAIL>',
            'membership' => [
                'type' => 'num_classes',
            ],
        ]);
        return [
            '1. Given a branch that book through an integrator as classpass, When we book a member into an event,
            Then credit validation must be skipped' => [
                'event' => $event,
                'user' => $user,
                'integrator' => IDENTIFIER::CLASSPASS
            ],
            '2. Given a branch that book through an integrator as moholding, When we book a member into an event,
            Then credit validation must be skipped' => [
                'event' => $event,
                'user' => $user,
                'integrator' => IDENTIFIER::MOHOLDING
            ],
        ];
    }


    /**
     * @throws ReflectionException
     */
    public function testNotEligibleWhenEventIdIsNull(): void
    {
        $identifier = 'value';
        $eventModelMock = [
            'model' => 'T',
            'current_user_eligibility' => MemberEligibility::UNKNOWN
        ];
        $user = [
            '_id' => '-1',
            'branch_id' => '-1',
            'namespace' => 'glofox',
            'email' => '<EMAIL>',
            'login' => '<EMAIL>',
            'membership' => [
                'type' => 'num_classes',
            ],
            'type' => 'SOME_TYPE'
        ];

        $event = \Mockery::mock(Event::class)->makePartial()
            ->shouldReceive(
                [
                    'find' => [],
                    'getUser' => $user,
                    'loadModel' => null,
                    'setCalculatedFieldsToTimeSlots' => [$eventModelMock]
                ]
            )
            ->getMock();

        $event->API = 'value';

        $injector = Mockery::mock()
            ->shouldReceive('getSingleRecordConditions')->andReturn([])->getMock();
        $event->injector = $injector;

        $branch = Mockery::mock()
            ->shouldReceive(['findById' => null, 'getTimezone' => "T"])
            ->getMock();
        $event->Branch = $branch;

        $userMock = Mockery::mock(UserModel::class)
            ->shouldReceive(['user' => $user, 'isGuest' => false, 'isStaff' => false])->getMock();

        $authMock = Mockery::mock()
            ->shouldReceive('user')->andReturn($userMock)->getMock();

        $authMock->user = $userMock;

        app()->instance(Auth::class, $authMock);

        $timeslot = Mockery::mock(TimeSlot::class)
            ->shouldReceive(['setUpInjector' => null, 'find' => []])->getMock();
        $event->TimeSlot = $timeslot;

        $reflection = new ReflectionClass($event);
        $property = $reflection->getProperty('loggedUser');
        $property->setAccessible(true);
        $property->setValue($event, $user);

        $result = $event->getById($identifier);
        $this->assertEquals(MemberEligibility::NOT_ELIGIBLE, $result['current_user_eligibility']);

        app()->forgetInstance(Auth::class);
    }

    public function testGetOpenBookingTimeWithTimezoneAndDaylightSavingBetweenTodayAndWindow(): void
    {
        $timezone = 'America/Denver';
        /**
         * November 07, 2023 04:15:00 (am)
         */
        $eventStartTime = 1_699_355_700;
        /**
         * Seven days in advance represented in hours
         */
        $openBookingWindow = 168;
        /**
         * October 31, 2023 05:15:00 (am)
         */
        $noExpectedOpenBookingTime = 1_698_750_900;
        /**
         * October 31, 2023 04:15:00 (am)
         */
        $expectedOpenBookingTime = 1_698_747_300;

        // Test not passing timezone we get one hour off
        $wrongResult = $this->Event->getOpenBookingTime($eventStartTime, $openBookingWindow);
        $this->assertEquals($noExpectedOpenBookingTime, $wrongResult);

        // Test the fix passing the expected timezone
        $correctResult = $this->Event->getOpenBookingTime($eventStartTime, $openBookingWindow, $timezone);
        $this->assertEquals($expectedOpenBookingTime, $correctResult);
    }

    public function testGetCloseBookingTimeWithTimezoneAndDaylightSavingBetweenTodayAndWindow(): void
    {
        $timezone = 'America/Denver';
        /**
         * November 07, 2023 04:15:00 (am)
         */
        $eventStartTime = 1_699_355_700;
        /**
         * Seven days in advance represented in hours
         */
        $closeBookingWindow = 168;
        /**
         * October 31, 2023 05:15:00 (am)
         */
        $noExpectedCloseBookingTime = 1_698_750_900;
        /**
         * October 31, 2023 04:15:00 (am)
         */
        $expectedCloseBookingTime = 1_698_747_300;

        // Test not passing timezone we get one hour off
        $wrongResult = $this->Event->getCloseBookingTime($eventStartTime, $closeBookingWindow);
        $this->assertEquals($noExpectedCloseBookingTime, $wrongResult);

        // Test the fix passing the expected timezone
        $correctResult = $this->Event->getCloseBookingTime($eventStartTime, $closeBookingWindow, $timezone);
        $this->assertEquals($expectedCloseBookingTime, $correctResult);
    }

    public function testDefaultValues(): void
    {
        $branchId = (string)(new MongoId());

        $program = [
            'Program' => [
                '_id' => new MongoId(),
                'branch_id' => $branchId,
                'namespace' => 'foo',
                'active' => true,
                'name' => 'Foo program',
                'description' => 'Just a foo',
                'schedule' => [
                    [
                        'code' => uniqid('', false),
                        'start_time' => '12:00',
                        'end_time' => '13:00',
                        'level' => null,
                        'facility' => (string)(new MongoId()),
                        'trainers' => [
                            'some-non-valid-id',
                        ],
                        'days_week' => date('w'),
                    ],
                ],
                'schedule_default' => [],
            ],
        ];

        $branch = [
            'Branch' => [
                '_id' => new MongoId($branchId),
                'namespace' => 'foo',
            ],
        ];

        $startDate = date(self::DEFAULT_DATE_FORMAT, strtotime('+5 minutes'));
        $endDate = date(self::DEFAULT_DATE_FORMAT, strtotime('+2 days'));

        $eventModel = $this->givenEventModelMock(
            function (array &$events) {
                foreach ($events as $key => $event) {
                    $this->assertEquals('', $event['level']);
                    $this->assertEquals(10, $event['size']);
                    $this->assertFalse($event['featured']);
                    $this->assertFalse($event['private']);
                    $this->assertTrue($event['active']);
                    $this->assertCount(0, $event['trainers']);
                    $events[$key]['_id'] = new MongoId();
                }

                return true;
            }
        );

        $publisher = \Mockery::mock(EventsPublisher::class);
        $publisher->shouldReceive('sendEventCreatedEvent');
        app()->instance(EventsPublisher::class, $publisher);

        $eventModel->generateFrom($program, $branch, $startDate, $endDate);

        app()->forgetInstance(EventsPublisher::class);
    }

    /** @return  Event|\Mockery\Mock */
    private function givenEventModelMock(Closure $callback)
    {
        $eventModel = \Mockery::mock(\Event::class)->makePartial();
        $eventModel->shouldReceive('getMongoDb')
            ->andReturn(
                \Mockery::mock(\MongoDB::class)
                    ->shouldReceive('selectCollection')
                    ->with('events')
                    ->andReturn(
                        \Mockery::mock(MongoCollection::class)
                            ->shouldReceive('batchInsert')
                            ->withArgs($callback)
                            ->getMock()
                    )
                    ->getMock()
            );

        return $eventModel;
    }
}
