// agreed with <PERSON> and <PERSON><PERSON> on 15/11/2019
var users = db.users.find({
    "active": true,
    "membership.type": {
        $in: ['time', 'time_classes', 'num_classes']
    },
    $or: [
        {
            "membership.plan_code": {
                $exists: false
            }
        },
        {
            "membership.plan_code": {
                $in: ["", 0, null]
            }
        }
    ],
    "membership.expiry_date": {
        $lt: new ISODate('2019-01-01')
    }
});

var i = 1;
var error = 0;
var success = 0;
var total = users.count();
var detailedErrorList = {};

users.forEach((user) => {

    try {

        var membership = {
            "type": "payg"
        };

        var query = {
            _id: user._id
        };

        var instruction = {
            $set: {
                "membership": membership
            }
        };

        console.info(`User ${user._id.toString()} will have its membership set to`, instruction);

        db.users.update(query, instruction);

        success++;

        console.info(`[${i}/${total}] Membership reverted to payg successfully (s: ${success} / e: ${error})`);

    }
    catch (err) {

        // console.error(err.message, err.stack);
        error++;

        console.info(`[${i}/${total}] Membership reverted to payg failed (s: ${success} / e: ${error}) -  ${err.message} (${user.namespace}, ${user.created})`);

        var branchId = (Array.isArray(user.branch_id) ? user.branch_id[0] : user.branch_id);
        var originBranchId =  user.origin_branch_id;
        var namespace = user.namespace;

        var errorDetail = {
            userId: user._id+"",
            branchId: originBranchId || branchId,
            namespace: namespace,
            error: err.message
        }

        if (!detailedErrorList[branchId]) {
            detailedErrorList[branchId] = {
                details: []
            };
        }

        detailedErrorList[branchId].details.push(errorDetail);
    }

    i++;
});

console.info(`.`);
console.info(`.`);
console.info(`[${success}/${total}] Membership reverted to payg recovered successfully`);
console.info(`[${error}/${total}] Membership reverted to payg recovery failed`);

print("branchId,namespace,totalErrors")
for (var branchId in detailedErrorList) {
    print([
        branchId,
        detailedErrorList[branchId].details[0].namespace,
        detailedErrorList[branchId].details.length
    ].join(','))
}