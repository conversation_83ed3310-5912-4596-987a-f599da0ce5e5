<?php

use Carbon\Carbon;
use Glofox\Domain\Bookings\Status as BookingStatus;
use Glofox\Domain\Bookings\Type as BookingType;
use Glofox\Domain\TimeSlots\ModelList;

App::uses('GlofoxTestFixture', 'Test/Fixture');

class BookingFixture extends GlofoxTestFixture
{
    private const GLOFOX_BRANCH_ID = '49a7011a05c677b9a916612a';
    private const GLOFOX_NAMESPACE = 'glofox';
    private const GLOFOX_USER_ID = '59a7011a05c677bda916612a';
    private const GLOFOX_USER_ID_2 = '6742681581c1229f515974b6';
    private const GLOFOX_USER_ID_3 = '6742680dff4eefeb139d4347';
    private const GLOFOX_USER_ID_4 = '674267e2e765beb37def5fd1';
    private const GLOFOX_USER_NAME = 'Test User';
    private const GLOFOX_STAFF_ID = '59a7011a05c677bda916612c';
    private const GLOFOX_PROGRAM_ID = '13b7411a15c676bda824611b';
    private const GLOFOX_EVENT_ID = '49b7012a05c677c9a512503c';

    private const CANCEL_TA_APPOINTMENT_ID = '636e036092c5cd5655bbdf33';

    private const VIRTUAL_SLOT_BRANCH_ID = '6461ed6fdef3e8f4c6d4b00c';
    private const VIRTUAL_SLOT_BRANCH_NAMESPACE = 'virtualslotstest';
    private const VIRTUAL_SLOT_PROGRAM_ID = '64620b36de9120be8b00ed33';
    private const VIRTUAL_SLOT_EVENT_ID = '64620e8a3bfae9d753b2200a';
    private const VIRTUAL_SLOT_MEMBER_ID = '6461eeba3e6feb790c7ec607';
    private const VIRTUAL_SLOT_APPOINTMENT_ID = '6461ef0d1f0de25d31b39abc';
    private const VIRTUAL_SLOT_APPOINTMENT_NAME = 'Appointment to test virtual slots';
    private const VIRTUAL_SLOT_APPOINTMENT_SLOT_ID_1 = '64621d13981385cb99474aa0';
    private const VIRTUAL_SLOT_APPOINTMENT_SLOT_ID_2 = '64621da12de9f5bf8de31c7e';

    public $import = 'Booking';

    protected $indexes = [
        [
            'user_id' => 1,
            'status' => 1,
            'time_start' => 1,
            'time_finish' => 1,
        ],
        [
            'branch_id' => 1,
            'model' => 1,
            'status' => 1,
        ],
        [
            'branch_id' => 1,
            'status' => 1,
            'confirmed' => 1,
            'time_start' => 1,
            'type' => 1,
        ]
    ];

    public function init()
    {
        $this->records = [
            [
                '_id' => new MongoId('584ac642dd3c2fbfe13e911b'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'event_id' => '49b7012a05c677c9a512503c',
                'program_id' => '13b7411a15c676bda824611b',
                'status' => 'BOOKED',
                'type' => 'events',
                'user_name' => 'Test User',
                'model_name' => 'Test',
                'user_id' => '59a7011a05c677bda916612a',
                'time_start' => date('Y-m-d H:m:i', strtotime('+30 minutes')),
                'time_finish' => date('Y-m-d H:m:i', strtotime('+2 hour')),
                'paid' => false,
                'attended' => false,
                'date_start' => date('Y-m-d', strtotime('+30 minutes')),
                'date_finish' => date('Y-m-d', strtotime('+2 hour')),
                'is_from_waiting_list' => null,
                'guest_bookings' => 0,
                'session_id' => null,
                'payment_method' => 'cash',
            ],
            [
                '_id' => new MongoId('59a7010a05d377b3a916612d'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'event_id' => '49b7012a05c677c9a512503c',
                'program_id' => '13b7411a15c676bda824611b',
                'status' => 'BOOKED',
                'type' => 'events',
                'user_name' => 'Test User',
                'model_name' => 'Test',
                'user_id' => '59a7010a05c677b3a916612d',
                'time_start' => date('Y-m-d H:m:i', strtotime('+1 hour')),
                'time_finish' => date('Y-m-d H:m:i', strtotime('+2 hour')),
                'paid' => false,
                'attended' => false,
                'date_start' => date('Y-m-d', strtotime('+1 hour')),
                'date_finish' => date('Y-m-d', strtotime('+2 hour')),
                'is_from_waiting_list' => null,
                'guest_bookings' => 0,
                'session_id' => null,
                'payment_method' => 'cash',
            ],
            [
                '_id' => new MongoId('59e636f04fb5d107461ca84a'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'event_id' => '59e63079e71da189c2b0ea00',
                'program_id' => '13b7411a15c676bda824611b',
                'status' => 'BOOKED',
                'type' => 'events',
                'user_name' => 'Test User',
                'model_name' => 'Test',
                'user_id' => '59e63717eca9107dbdece1ba',
                'time_start' => date('Y-m-d H:m:i', strtotime('+1 hour')),
                'time_finish' => date('Y-m-d H:m:i', strtotime('+2 hour')),
                'paid' => false,
                'date_start' => date('Y-m-d', strtotime('+1 hour')),
                'date_finish' => date('Y-m-d', strtotime('+2 hour')),
                'is_from_waiting_list' => null,
                'guest_bookings' => 0,
                'session_id' => null,
                'payment_method' => 'cash',
            ],
            [
                '_id' => new MongoId('58986abfdd3c2fe2588b4568'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'event_id' => '49b7012a05c677c9a512503c',
                'program_id' => '13b7411a15c676bda824611b',
                'status' => 'BOOKED',
                'type' => 'events',
                'user_name' => 'Test User',
                'model_name' => 'Test',
                'user_id' => '59a3011a05c677bda916611c',
                'time_start' => date('Y-m-d H:m:i', strtotime('-1 hour')),
                'time_finish' => date('Y-m-d H:m:i', strtotime('-2 hour')),
                'paid' => false,
                'attended' => false,
                'date_start' => date('Y-m-d', strtotime('-1 hour')),
                'date_finish' => date('Y-m-d', strtotime('-2 hour')),
                'is_from_waiting_list' => null,
                'guest_bookings' => 0,
                'session_id' => null,
                'payment_method' => 'cash',
            ],
            [
                '_id' => new MongoId('538bb35bfe25f013c48b4567'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'event_id' => '29b7012a05c677c9a512503d',
                'program_id' => '13b7411a15c676bda824611b',
                'status' => 'WAITING',
                'type' => 'events',
                'user_name' => 'Test User',
                'model_name' => 'Test',
                'user_id' => '59a3011a05c677bda916611c',
                'time_start' => date('Y-m-d H:m:i', strtotime('-1 hour')),
                'time_finish' => date('Y-m-d H:m:i', strtotime('-2 hour')),
                'attended' => false,
                'date_start' => date('Y-m-d', strtotime('-1 hour')),
                'date_finish' => date('Y-m-d', strtotime('-2 hour')),
                'is_from_waiting_list' => null,
                'guest_bookings' => 0,
                'session_id' => null,
            ],
            [
                '_id' => new MongoId('5a7c46fcd148aa7a03000000'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'event_id' => '29b7012a05c677c9a512503d',
                'program_id' => '13b7411a15c676bda824611b',
                'status' => 'WAITING',
                'type' => 'events',
                'user_name' => 'Test User',
                'model_name' => 'Test',
                'user_id' => 'a9a2222a05c677bda916611c',
                'time_start' => date('Y-m-d H:m:i', strtotime('-1 hour')),
                'time_finish' => date('Y-m-d H:m:i', strtotime('-2 hour')),
                'attended' => false,
                'date_start' => date('Y-m-d', strtotime('-1 hour')),
                'date_finish' => date('Y-m-d', strtotime('-2 hour')),
                'is_from_waiting_list' => null,
                'guest_bookings' => 0,
                'session_id' => null,
            ],
            [
                '_id' => new MongoId('584ac642dd3c2fbfe13e91ab'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'event_id' => '49b7012a05c677c9a512503c',
                'program_id' => '13b7411a15c676bda824611b',
                'status' => 'BOOKED',
                'type' => 'events',
                'user_name' => 'Test User',
                'model_name' => 'Test',
                'user_id' => '59c92229c3fad88b481add96',
                'time_start' => date('Y-m-d H:m:i', strtotime('+1 hour')),
                'time_finish' => date('Y-m-d H:m:i', strtotime('+2 hour')),
                'paid' => false,
                'date_start' => date('Y-m-d', strtotime('+1 hour')),
                'date_finish' => date('Y-m-d', strtotime('+2 hour')),
                'is_from_waiting_list' => null,
                'guest_bookings' => 0,
                'session_id' => null,
                'payment_method' => 'cash',
            ],
            [
                '_id' => new MongoId('584ac642dd3c2fbfe13e91bb'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'time_slot_id' => '59832e82e1608a08210041bb',
                'status' => 'BOOKED',
                'type' => 'time_slots',
                'user_name' => 'Test User',
                'model_name' => 'Test',
                'user_id' => '59c92229c3fad88b481add96',
                'time_start' => date('Y-m-d H:m:i', strtotime('+1 hour')),
                'time_finish' => date('Y-m-d H:m:i', strtotime('+2 hour')),
                'paid' => false,
                'attended' => false,
                'date_start' => date('Y-m-d', strtotime('+1 hour')),
                'date_finish' => date('Y-m-d', strtotime('+2 hour')),
                'payment_method' => 'cash',
            ],
            [
                '_id' => new MongoId('584ac642dd3c2fbfe13e91bc'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'time_slot_id' => '59832e82e1608a08210041b9',
                'status' => 'BOOKED',
                'type' => 'time_slots',
                'user_name' => 'Test User',
                'model_name' => 'Test',
                'user_id' => '59c92229c3fad88b481add96',
                'time_start' => date('Y-m-d H:m:i', strtotime('+1 hour')),
                'time_finish' => date('Y-m-d H:m:i', strtotime('+2 hour')),
                'paid' => false,
                'attended' => false,
                'date_start' => date('Y-m-d', strtotime('+1 hour')),
                'date_finish' => date('Y-m-d', strtotime('+2 hour')),
                'payment_method' => 'cash',
            ],
            [
                '_id' => new MongoId('584ac642dd3c2fbfe13e91b3'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'event_id' => '49b7012a05c677c9a5125023',
                'status' => 'BOOKED',
                'type' => 'events',
                'user_name' => 'Testing FirstBookingMembership',
                'model_name' => 'Test',
                'user_id' => '5a2aad49e1608aa72b0041b4',
                'time_start' => date('Y-m-d H:m:i', strtotime('13 day')),
                'time_finish' => date('Y-m-d H:m:i', strtotime('13 day, 2 hour')),
                'paid' => false,
                'attended' => false,
                'date_start' => date('Y-m-d', strtotime('13 day')),
                'date_finish' => date('Y-m-d', strtotime('13 day')),
                'is_from_waiting_list' => null,
                'guest_bookings' => 0,
                'session_id' => null,
                'payment_method' => 'cash',
            ],
            [
                '_id' => new MongoId('5ade081290a4bdaf1e3efa47'),
                'branch_id' => '5addc25383266f65abf515c4',
                'namespace' => 'classpass_integrated',
                'event_id' => '5ade08479293121c1f9b4245',
                'status' => 'BOOKED',
                'type' => 'events',
                'user_id' => '5adde464b5cf4ad32eb7597e',
                'time_start' => date('Y-m-d H:m:i', strtotime('13 day')),
                'time_finish' => date('Y-m-d H:m:i', strtotime('13 day, 2 hour')),
                'paid' => false,
                'attended' => false,
                'date_start' => date('Y-m-d', strtotime('13 day')),
                'date_finish' => date('Y-m-d', strtotime('13 day')),
                'is_from_waiting_list' => null,
                'guest_bookings' => 0,
                'session_id' => null,
                'payment_method' => 'cash',
                'origin' => 'classpass',
                'metadata' => [
                    'classpass' => [
                        '_id' => 'classpasBookingId',
                        'user_id' => 'classpassUserId',
                    ],
                ],
            ],
            // Normal booking for a class set with classpass
            [
                '_id' => new MongoId('5ae075df222e25a5d9a06521'),
                'branch_id' => '5addc25383266f65abf515c4',
                'namespace' => 'classpass_integrated',
                'event_id' => '5adf43c0db6b6587ebcb2c5b',
                'status' => 'BOOKED',
                'type' => 'events',
                'user_id' => '5adde464b5cf4ad32eb7597e',
                'time_start' => date('Y-m-d H:m:i', strtotime('13 day')),
                'time_finish' => date('Y-m-d H:m:i', strtotime('13 day, 2 hour')),
                'paid' => false,
                'attended' => false,
                'date_start' => date('Y-m-d', strtotime('13 day')),
                'date_finish' => date('Y-m-d', strtotime('13 day')),
                'is_from_waiting_list' => null,
                'guest_bookings' => 0,
                'session_id' => null,
                'payment_method' => 'cash',
            ],
            // Classpass booking for a class set with classpass
            [
                '_id' => new MongoId('5ae1ced3ec3b8dacea6f35ff'),
                'branch_id' => '5addc25383266f65abf515c4',
                'namespace' => 'classpass_integrated',
                'event_id' => '5ae1b7a0df7366855fd28702',
                'status' => 'BOOKED',
                'type' => 'events',
                'user_id' => '5adde464b5cf4ad32eb7597e',
                'time_start' => date('Y-m-d H:m:i', strtotime('13 day')),
                'time_finish' => date('Y-m-d H:m:i', strtotime('13 day, 2 hour')),
                'paid' => false,
                'attended' => false,
                'date_start' => date('Y-m-d', strtotime('13 day')),
                'date_finish' => date('Y-m-d', strtotime('13 day')),
                'is_from_waiting_list' => null,
                'guest_bookings' => 0,
                'session_id' => null,
                'payment_method' => 'cash',
                'origin' => 'classpass',
                'metadata' => [
                    'classpass' => [
                        '_id' => 'classpasBookingId3',
                        'user_id' => 'classpassUserId3',
                    ],
                ],
            ],
            // Classpass booking for a class set with classpass
            [
                '_id' => new MongoId('5ae8982dac2c0a8d9f467d6f'),
                'branch_id' => '5addc25383266f65abf515c4',
                'namespace' => 'classpass_integrated',
                'event_id' => '5adf43c0db6b6587ebcb2c5b',
                'status' => 'BOOKED',
                'type' => 'events',
                'user_id' => '5ae898402ac13ab10d339b99',
                'time_start' => date('Y-m-d H:m:i', strtotime('13 day')),
                'time_finish' => date('Y-m-d H:m:i', strtotime('13 day, 2 hour')),
                'paid' => false,
                'attended' => false,
                'date_start' => date('Y-m-d', strtotime('13 day')),
                'date_finish' => date('Y-m-d', strtotime('13 day')),
                'is_from_waiting_list' => null,
                'guest_bookings' => 0,
                'session_id' => null,
                'payment_method' => 'cash',
                'origin' => 'classpass',
                'metadata' => [
                    'classpass' => [
                        '_id' => 'classpasBookingId4',
                        'user_id' => 'classpassUserId4',
                    ],
                ],
            ],
            // Classpass booking for a class set with classpass
            [
                '_id' => new MongoId('5ae898595d4e244e7ecb61eb'),
                'branch_id' => '5addc25383266f65abf515c4',
                'namespace' => 'classpass_integrated',
                'event_id' => '5ae99803c53fbc7079cfd7fc',
                'status' => 'BOOKED',
                'type' => 'events',
                'user_id' => '5ae898618e56dc9405adf1de',
                'time_start' => date('Y-m-d H:m:i', strtotime('13 day')),
                'time_finish' => date('Y-m-d H:m:i', strtotime('13 day, 2 hour')),
                'paid' => false,
                'attended' => false,
                'date_start' => date('Y-m-d', strtotime('13 day')),
                'date_finish' => date('Y-m-d', strtotime('13 day')),
                'is_from_waiting_list' => null,
                'guest_bookings' => 0,
                'session_id' => null,
                'payment_method' => 'cash',
                'origin' => 'classpass',
                'metadata' => [
                    'classpass' => [
                        '_id' => 'classpasBookingId5',
                        'user_id' => 'classpassUserId5',
                    ],
                ],
            ],
            // Normal booking for a class set with classpass
            [
                '_id' => new MongoId('5ae99b10d5e2232bb11213b3'),
                'branch_id' => '5addc25383266f65abf515c4',
                'namespace' => 'classpass_integrated',
                'event_id' => '5ae99803c53fbc7079cfd7fc',
                'status' => 'BOOKED',
                'type' => 'events',
                'user_id' => '5adde464b5cf4ad32eb7597e',
                'time_start' => date('Y-m-d H:m:i', strtotime('13 day')),
                'time_finish' => date('Y-m-d H:m:i', strtotime('13 day, 2 hour')),
                'paid' => false,
                'attended' => false,
                'date_start' => date('Y-m-d', strtotime('13 day')),
                'date_finish' => date('Y-m-d', strtotime('13 day')),
                'is_from_waiting_list' => null,
                'guest_bookings' => 0,
                'session_id' => null,
                'payment_method' => 'cash',
            ],
            [
                '_id' => new MongoId('5b043d752a1a3a1657e6db5e'),
                'branch_id' => '5addc25383266f65abf515c4',
                'namespace' => 'classpass_integrated',
                'event_id' => '5b043d0d45702d48c57deda7',
                'program_id' => '5b057507b8e809c018bb33c1',
                'status' => 'BOOKED',
                'type' => 'events',
                'user_id' => '5adde862426413b9e9b4be9e',
                'time_start' => date('Y-m-d H:m:i', strtotime('13 day')),
                'time_finish' => date('Y-m-d H:m:i', strtotime('13 day, 2 hour')),
                'paid' => true,
                'attended' => false,
                'date_start' => date('Y-m-d', strtotime('13 day')),
                'date_finish' => date('Y-m-d', strtotime('13 day')),
                'is_from_waiting_list' => null,
                'guest_bookings' => 0,
                'session_id' => null,
                'payment_method' => 'cash',
            ],
            [
                '_id' => new MongoId('5b043de53d0fe66ac4fb2438'),
                'branch_id' => '5addc25383266f65abf515c4',
                'namespace' => 'classpass_integrated',
                'event_id' => '5b043d0d45702d48c57deda7',
                'program_id' => '5b057507b8e809c018bb33c1',
                'status' => 'WAITING',
                'type' => 'events',
                'user_id' => '5adf4d52f35bbcfb4251f960',
                'time_start' => date('Y-m-d H:m:i', strtotime('13 day')),
                'time_finish' => date('Y-m-d H:m:i', strtotime('13 day, 2 hour')),
                'paid' => false,
                'attended' => false,
                'date_start' => date('Y-m-d', strtotime('13 day')),
                'date_finish' => date('Y-m-d', strtotime('13 day')),
                'is_from_waiting_list' => null,
                'guest_bookings' => 0,
                'session_id' => null,
                'payment_method' => 'cash',
            ],
            // Fist Booking Flag
            [
                '_id' => new MongoId('5b3cd9eea2ceecc6c02a3bc7'),
                'branch_id' => '5addc25383266f65abf515c4',
                'namespace' => 'classpass_integrated',
                'event_id' => '5b043d0d45702d48c57deda7',
                'status' => 'CANCELED',
                'type' => 'events',
                'user_id' => '5b3cd978d47b164572a65efa',
                'time_start' => date('Y-m-d H:m:i', strtotime('7 day')),
                'time_finish' => date('Y-m-d H:m:i', strtotime('7 day, 2 hour')),
                'paid' => false,
                'guest_bookings' => 0,
                'payment_method' => 'cash',
                'is_first' => true,
            ],
            [
                '_id' => new MongoId('5b3cda56520b4792503a9def'),
                'branch_id' => '5addc25383266f65abf515c4',
                'namespace' => 'classpass_integrated',
                'event_id' => '5ae99803c53fbc7079cfd7fc',
                'status' => 'BOOKED',
                'type' => 'events',
                'user_id' => '5b3cd978d47b164572a65efa',
                'time_start' => date('Y-m-d H:m:i', strtotime('10 day')),
                'time_finish' => date('Y-m-d H:m:i', strtotime('10 day, 2 hour')),
                'paid' => false,
                'guest_bookings' => 0,
                'payment_method' => 'cash',
            ],
            [
                '_id' => new MongoId('5b3e01904a9b7505d9d3fb78'),
                'branch_id' => '5addc25383266f65abf515c4',
                'namespace' => 'classpass_integrated',
                'event_id' => '5b043d0d45702d48c57deda7',
                'status' => 'CANCELED',
                'type' => 'events',
                'user_id' => '5b3cd978d47b164572a65efa',
                'time_start' => date('Y-m-d H:m:i', strtotime('12 day')),
                'time_finish' => date('Y-m-d H:m:i', strtotime('12 day, 2 hour')),
                'paid' => false,
                'guest_bookings' => 0,
                'payment_method' => 'cash',
            ],
            [
                '_id' => new MongoId('5b3e01904a9b7505d9d3fb79'),
                'branch_id' => '5addc25383266f65abf515c4',
                'namespace' => 'classpass_integrated',
                'event_id' => '5b043d0d45702d48c57deda7',
                'status' => 'CANCELED',
                'type' => 'events',
                'user_id' => '5b3cd978d47b164572a65efa',
                'time_start' => date('Y-m-d H:m:i', strtotime('12 day')),
                'time_finish' => date('Y-m-d H:m:i', strtotime('12 day, 2 hour')),
                'paid' => false,
                'guest_bookings' => 0,
                'payment_method' => 'cash',
                'created' => new MongoDate(strtotime('+7 days')),
                'modified' => new MongoDate(strtotime('+7 days')),
            ],
            [
                '_id' => new MongoId('5b3e01904a9b7505d9d3fb71'),
                'branch_id' => '5d56d214be51ef060f38408f',
                'namespace' => 'namespace-5d56d214be51ef060f38408f',
                'event_id' => '5e7cb6713d36321bf52c63d7',
                'status' => 'BOOKED',
                'program_id' => '5b7bede071c02a37fc6b3699',
                'type' => 'events',
                'user_id' => '5b3cd978d47b164572a65eff',
                'time_start' => date('Y-m-d H:i:s', strtotime('2020-01-01 13:05')),
                'time_finish' => date('Y-m-d H:i:s', strtotime('2020-01-01 14:05')),
                'paid' => false,
                'guest_bookings' => 0,
                'payment_method' => 'cash',
                'created' => new MongoDate(strtotime('+7 days')),
                'modified' => new MongoDate(strtotime('+7 days')),
            ],
            [
                '_id' => new MongoId('5b3e01914a9b7505d9d3fb73'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'event_id' => '5e7cb6703d37321bf52c63d7',
                'status' => 'BOOKED',
                'program_id' => '13b7411a15c676bda824611b',
                'type' => 'events',
                'user_id' => '5b7beae94813d9602de2dda5',
                'time_start' => date('Y-m-d H:i:s', strtotime('2020-01-01 13:05')),
                'time_finish' => date('Y-m-d H:i:s', strtotime('2020-01-01 14:05')),
                'paid' => true,
                'guest_bookings' => 0,
                'payment_method' => 'cash',
                'attended' => false,
                'created' => new MongoDate(strtotime('+7 days')),
                'modified' => new MongoDate(strtotime('+7 days')),
            ],
            [
                '_id' => new MongoId('5b3e01914a9b7405d9d3fb73'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'event_id' => '5e7cb6703d37321bf52c63d7',
                'status' => 'BOOKED',
                'program_id' => '13b7411a15c676bda824611b',
                'type' => 'events',
                'user_id' => '5b7beae94813d9632de2dda5',
                'time_start' => date('Y-m-d H:i:s', strtotime('2020-01-01 13:05')),
                'time_finish' => date('Y-m-d H:i:s', strtotime('2020-01-01 14:05')),
                'paid' => true,
                'guest_bookings' => 0,
                'payment_method' => 'cash',
                'attended' => true,
                'created' => new MongoDate(strtotime('+7 days')),
                'modified' => new MongoDate(strtotime('+7 days')),
            ],
            [
                '_id' => new MongoId('5b3e01915a9b7505d9d3fb73'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'event_id' => '5e7cb6703d37321bf52c63d7',
                'status' => 'WAITING',
                'program_id' => '13b7411a15c676bda824611b',
                'type' => 'events',
                'user_id' => '5b7beae94823d9602de2dda5',
                'time_start' => date('Y-m-d H:i:s', strtotime('2020-01-01 13:05')),
                'time_finish' => date('Y-m-d H:i:s', strtotime('2020-01-01 14:05')),
                'paid' => true,
                'guest_bookings' => 0,
                'payment_method' => 'cash',
                'attended' => false,
                'created' => new MongoDate(strtotime('+7 days')),
                'modified' => new MongoDate(strtotime('+7 days')),
            ],
            [
                '_id' => new MongoId('5b3e01916a9b7505d9d3fb73'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'event_id' => '5e7cb6703d37321bf52c63d7',
                'status' => 'CANCELED',
                'program_id' => '13b7411a15c676bda824611b',
                'type' => 'events',
                'user_id' => '5b7beae94833d9602de2dda5',
                'time_start' => date('Y-m-d H:i:s', strtotime('2020-01-01 13:05')),
                'time_finish' => date('Y-m-d H:i:s', strtotime('2020-01-01 14:05')),
                'paid' => true,
                'guest_bookings' => 0,
                'payment_method' => 'cash',
                'attended' => false,
                'created' => new MongoDate(strtotime('+7 days')),
                'modified' => new MongoDate(strtotime('+7 days')),
            ],
            [
                '_id' => new MongoId('5b3e01916a9b7505d9d3fb74'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'event_id' => '5e7cb6703d37321bf52c63d9',
                'status' => 'BOOKED',
                'program_id' => '13b7411a15c676bda824611b',
                'type' => 'events',
                'user_id' => '5e7cb75c3d36311bf52c63d9',
                'created' => new MongoDate(Carbon::yesterday()->addHours(12)->getTimestamp()),
            ],
            [
                '_id' => new MongoId('5b3e01916a9b7505d9d3fb77'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'event_id' => '5e7cb6703d37321bf52c63d9',
                'status' => 'BOOKED',
                'program_id' => '13b7411a15c676bda824611b',
                'type' => 'events',
                'user_id' => '5e7cb75c3d36311bf52c63d0',
                'created' => new MongoDate(Carbon::yesterday()->addHours(12)->getTimestamp()),
            ],

            [
                '_id' => new MongoId('584ac642dd3c2fbfe13e913c'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'event_id' => '59e63079e71da189c2b0ea00',
                'program_id' => '59ca3a2f570b4bde3da3decd',
                'status' => 'BOOKED',
                'type' => 'events',
                'user_name' => 'Test User',
                'model_name' => 'Test',
                'user_id' => '59a7010a05c677b3a9166111',
                'time_start' => date('Y-m-d H:m:i', strtotime('+1 hour')),
                'time_finish' => date('Y-m-d H:m:i', strtotime('+2 hour')),
                'paid' => false,
                'date_start' => date('Y-m-d', strtotime('+1 hour')),
                'date_finish' => date('Y-m-d', strtotime('+2 hour')),
                'is_from_waiting_list' => null,
                'guest_bookings' => 0,
                'session_id' => null,
                'payment_method' => 'cash',
            ],

            [
                '_id' => new MongoId('5b3e01916a9b7505d9d3fb11'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'event_id' => '5e7cb6703d37321bf52c63d9',
                'status' => 'BOOKED',
                'program_id' => '13b7411a15c676bda824611b',
                'type' => 'events',
                'user_id' => '5e7cb75c3d36311bf52c63d1',
                'created' => new MongoDate(Carbon::yesterday()->addHours(12)->getTimestamp()),
            ],
            [
                '_id' => new MongoId('5b3e01916a9b7505d9d3fb22'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'event_id' => '5e7cb6703d37321bf52c63d9',
                'status' => 'BOOKED',
                'program_id' => '13b7411a15c676bda824611b',
                'type' => 'events',
                'user_id' => '5e7cb75c3d36311bf52c63d2',
                'created' => new MongoDate(Carbon::yesterday()->addHours(12)->getTimestamp()),
            ],

            $this->createBooking([
                'branch_id' => '5e7cb6a53d36311bf52c63d8',
                'event_id' => '5e7cb66a3d36311bf52c63d6',
                'user_id' => '59a7010a05c677b3a916715d',
                'time_start' => new MongoDate(Carbon::now()->addDay()->getTimestamp()),
                'time_finish' => new MongoDate(Carbon::now()->addDay()->addHour()->getTimestamp()),
            ]),
            $this->createBooking([
                'branch_id' => '5e7cb6a53d36311bf52c63d8',
                'event_id' => '5e7cb6713d36311bf52c63d7',
                'user_id' => '59a7010a05c677b3a916715d',
                'time_start' => new MongoDate(Carbon::now()->addDays(2)->getTimestamp()),
                'time_finish' => new MongoDate(Carbon::now()->addDays(2)->addHour()->getTimestamp()),
            ]),
            $this->createBooking([
                'branch_id' => '5e7cb6a53d36311bf52c63d8',
                'event_id' => '5e7cb66a3d36311bf52c63d6',
                'user_id' => '59a7010a05c677b3a916715d',
                'time_start' => new MongoDate(Carbon::now()->addDays(3)->getTimestamp()),
                'time_finish' => new MongoDate(Carbon::now()->addDays(3)->addHour()->getTimestamp()),
            ]),
            $this->createBooking([
                'branch_id' => '5e7cb6a53d36311bf52c63d8',
                'event_id' => '5e7cb6713d36311bf52c63d7',
                'user_id' => '59a7010a05c677b3a916715d',
                'time_start' => new MongoDate(Carbon::now()->addDays(4)->getTimestamp()),
                'time_finish' => new MongoDate(Carbon::now()->addDays(4)->addHour()->getTimestamp()),
            ]),
            $this->createBooking([
                '_id' => new MongoId('61a4ee412df7cc24846a5d45'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'event_id' => '61a4f20c2df7cc24846a5d46',
                'user_id' => '59a3011a05c677bda916611c',
                'time_start' => new MongoDate(Carbon::now()->addDays(4)->getTimestamp()),
                'time_finish' => new MongoDate(Carbon::now()->addDays(4)->addHour()->getTimestamp()),
                'confirmed' => false,
            ]),
            $this->createBooking([
                'branch_id' => '5e7cb6a53d36311bf52c63d8',
                'type' => 'time_slots',
                'model' => 'facility',
                'model_id' => '52a7011a05c677bda826611b',
                'time_slot_id' => '59832e82e1708a08210041b9',
                'user_id' => '59a7010a05c677b3a916715d',
                'time_start' => new MongoDate(Carbon::now()->addDays(5)->getTimestamp()),
                'time_finish' => new MongoDate(Carbon::now()->addDays(5)->addHour()->getTimestamp()),
            ]),
            [
                '_id' => new MongoId('5b3e01916a9b8505d9d3fb74'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'program_id' => (string)new MongoId(),
                'event_id' => (string)new MongoId(),
                'status' => 'BOOKED',
                'type' => 'events',
                'user_id' => '61f921834b4fc528013ef1e0',
                'time_start' => new MongoDate(Carbon::now()->subDay()->getTimestamp()),
                'time_finish' => new MongoDate(Carbon::now()->subDay()->addHour()->getTimestamp()),
            ],
            $this->createBooking([
                '_id' => new MongoId('6241b8449437b27491979752'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'event_id' => '5b043d0d45702d48c57deda7',
                'program_id' => '5b057507b8e809c018bb33c1',
                'status' => 'BOOKED',
                'type' => 'events',
                'user_id' => '6243236a107bebc0a065fc22',
                'metadata' => [
                    'service' => [
                        'type' => 'addOn',
                        'id' => 'test-add-on-id',
                    ],
                ],
            ]),
            $this->createBooking([
                '_id' => new MongoId('62bd9756f438994d5bcb4926'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'time_slot_id' => '62ac6f00ce337a1e6fae131e',
                'model_id' => '62a70487b9be8ef0bf86d521',
                'model' => 'appointments',
                'status' => 'BOOKED',
                'user_id' => '62bd96eb69d55476cf8fa051',
                'metadata' => [
                    'service' => [
                        'type' => 'credit',
                        'id' => 'test-credit-id',
                    ],
                ],
            ]),
            ...$this->addBookingForCi1808UseCase(),
            ...$this->addBookingForTestingMetadataUpdateWhenChargePayLater(),
            ...$this->addBookingsForTestingCancelBookingTrainerAvailabilitySlice1(),
            ...$this->addAppointmentSlotsForTestingBookingsOverlappingValidations(),
            ...$this->addBookingsForTimeFiltering(),
            ...$this->addBookingsForGetTotalBookingsAndTotalWaiting(),
            ...$this->addBookingsForBookingsControllerDateRangeTest(),
            ...$this->addBookingsForTimeSlotGetTotalBookingsTest(),
            ...$this->addBookingsForCourseTotalBookingsAndTotalWaiting(),
            ...$this->addBookingsForTimeStartTimeFinishFiltering(),
            ...$this->addBookingsListAllBookingsTest(),
            ...$this->addBookingSpots(),
            ...$this->forVirtualSlotTest(),
            ...$this->forPayroll(),
            ...$this->forGetAppointmentStaffTest(),
            ...$this->forClassPassOverBooking(),
            ...$this->addBookingsForCountEventIdWithGuests(),
            ...$this->forActiveLimitBookings(),
            ...$this->forEventForcedOverBookingSizeChange(),
            ...$this->forIntegrationTests(),
            ...$this->addBookingsForNonConfirmedBookingsInBookingOpenWindowByBranchId(),
            ...$this->forFindFutureReservationByScheduleCode(),
            ...$this->forCountingAllWaitingBookings(),
            ...$this->forCountAllBookingsIncludingGuests(),
            ...$this->forCuntBookingsByMemberIdInPeriod(),
            ...$this->forGetByEventAndMemberId(),
            ...$this->forEventTotalBookingsCount(),
            ...$this->forEventDeletedEventHandle(),
            ...$this->forFailureReason(),
            ...$this->forRestrictedMembershipNoCreditsNextCycle(),
            ...$this->forSemiPrivateAppointments(),
            ...$this->forHardDelete(),
            ...$this->forUpdateIsFirst(),
            ...$this->forUpdateStatus(),
            ...$this->forUpdateReservationStatus(),
            ...$this->forUpdateEventId(),
            ...$this->forCancellingInAdvanceShell(),
            ...$this->forEventsUpdate(),
            ...$this->forGetAllBookings(),
            ...$this->forProgramsUpdateEventGenerator(),
            ...$this->forUpdateDatesOnCancel(),
            ...$this->forWaitingListEmailOnRoaming(),
            ...$this->forAnalyticsClassBookings(),
            ...$this->forProgramUpsertService(),
            ...$this->forSkippingMembershipLockedBookingFailures(),
            ...$this->forNonDeleteEventsWithCanceledBookings(),
            ...$this->forCountReservationsFromEventsNotGenerated(),
            ...$this->forCancelBookingsPausedMembership()
        ];

        parent::init();
    }

    private function createBooking(array $override): array
    {
        $default = [
            '_id' => new MongoId(),
            'branch_id' => (string)(new MongoId()),
            'namespace' => 'testnamespace',
            'type' => 'events',
            'status' => 'BOOKED',
            'user_id' => (string)(new MongoId()),
            'user_name' => 'Test User',
            'time_start' => new MongoDate(Carbon::now()->addDay()->getTimestamp()),
            'time_finish' => new MongoDate(Carbon::now()->addDay()->addHour()->getTimestamp()),
            'created' => new MongoDate(),
            'modified' => new MongoDate(),
            'paid' => true,
            'guest_bookings' => 0,
            'payment_method' => 'cash',
        ];

        return array_replace_recursive($default, $override);
    }

    private function addBookingForCi1808UseCase(): array
    {
        return [
            [
                '_id' => new MongoId('61f88b27eb0c9b38d616d8c8'),
                'namespace' => 'glofox',
                'branch_id' => '5a9591bcdb07bce527400717',
                'user_id' => '619a00b75f4c3108b25cc8d8',
                'user_name' => 'XXX',
                'program_id' => '60e27f21b35f5115330cf25b',
                'schedule_code' => '1639551829370',
                'event_id' => '61b993566536aa7a3f542201',
                'event_name' => 'Barre Special BTS',
                'model_name' => 'Barre Special BTS',
                'membership_name' => 'Regular 20 Class Pack [Activate on Purchase Date]',
                'plan_name' => 'Regular 20 Class Pack ( Activate on purchase date)',
                'plan_code' => 1_609_069_899_843,
                'status' => 'BOOKED',
                'confirmed' => true,
                'type' => 'events',
                'time_start' => new MongoDate(strtotime('2021-12-24T12:30:00.000+00:00')),
                'time_finish' => new MongoDate(strtotime('2021-12-24T13:25:00.000+00:00')),
                'guest_bookings' => 0,
                'modified' => new MongoDate(strtotime('2021-12-24T04:14:01.607+00:00')),
                'created' => new MongoDate(strtotime('2021-12-21T04:30:06.000+00:00')),
                'canceled_at' => new MongoDate(strtotime('2021-12-21T12:30:35.000+00:00')),
                'is_late_cancellation' => false,
            ],
            [
                '_id' => new MongoId('61f88b27eb0c9b38d616d8c9'),
                'namespace' => 'glofox',
                'branch_id' => '5a9591bcdb07bce527400717',
                'user_id' => '61bd82b0e0057065cf296fc0',
                'user_name' => 'May Shann Loh',
                'program_id' => '61c2ee6eff7d573d344c7445',
                'schedule_code' => '1642671588560',
                'event_id' => '61e92de584f9284bae3ee72d',
                'event_name' => 'Reformer Abs Arms Ass',
                'model_name' => 'Reformer Abs Arms Ass',
                'membership_name' => 'Flow 25 Class Pack [Activate on purchase]',
                'plan_name' => 'Flow 25 Class Pack [Activate on purchase]',
                'plan_code' => 1_640_228_345_304,
                'status' => 'BOOKED',
                'confirmed' => true,
                'type' => 'events',
                'time_start' => new MongoDate(strtotime('+1 day')),
                'time_finish' => new MongoDate(strtotime('+2 day')),
                'guest_bookings' => 0,
                'modified' => new MongoDate(strtotime('2022-02-01 01:21:43')),
                'created' => new MongoDate(strtotime('2022-02-01 01:21:43')),
            ],
        ];
    }

    private function addBookingForTestingMetadataUpdateWhenChargePayLater(): array
    {
        return [
            [
                '_id' => new MongoId('63380dd716d490132841dd33'),
                'branch_id' => '633ad68b69f7280b4564aee3',
                'namespace' => 'glofox',
                'user_id' => '633ad7c5e757e6c240ee55ba',
                'user_name' => 'Payg Member Test',
                'event_id' => '5b043d0d45702d48c57deda7',
                'program_id' => '5b057507b8e809c018bb33c1',
                'type' => 'time_slots',
                'time_slot_id' => '633af03f3a55a4ed41f5ea87',
                'model' => 'appointments',
                'model_id' => '62a70487b9be8ef0bf86d523',
                'status' => 'BOOKED',
                'time_start' => new MongoDate(strtotime('+1 day')),
                'time_finish' => new MongoDate(strtotime('+2 day')),
                'event_name' => 'Test Appointment pilates',
                'metadata' => [
                    'source' => 'DASHBOARD',
                    'payment_method' => 'PAY_LATER',
                    'membership_type' => 'payg',
                ],
                'paid' => false,
                'created' => new MongoDate(strtotime('-1 day')),
                'created_user_id' => '633ad7b33e8c7958abc3e372',
            ],
            [
                '_id' => new MongoId('63998aec21086362d619b0c5'),
                'branch_id' => '633ad68b69f7280b4564aee3',
                'namespace' => 'bookingsmetadata',
                'user_id' => '63998828ca5fbb0e575cb46a', //user with credits
                'user_name' => 'Payg Member With Credits',
                'program_id' => '5b057507b8e809c018bb33c1',
                'type' => 'time_slots',
                'time_slot_id' => '6399dbd2dd678fd74669e562',
                'model' => 'appointments',
                'model_id' => '633bc4e9368fcc708a18617c',
                'status' => 'BOOKED',
                'time_start' => new MongoDate(Carbon::now()->addDays(5)->getTimestamp()),
                'time_finish' => new MongoDate(Carbon::now()->addDays(5)->addHours(1)->getTimestamp()),
                'event_name' => 'Trainer Appointment',
                'metadata' => [
                    'source' => 'DASHBOARD',
                    'payment_method' => 'PAY_LATER',
                    'membership_type' => 'time',
                ],
                'paid' => false,
                'created' => new MongoDate(Carbon::now()->getTimestamp()),
                'created_user_id' => '633ad7b33e8c7958abc3e372',
            ],
        ];
    }

    private function addBookingsForTestingCancelBookingTrainerAvailabilitySlice1(): array
    {
        return [
            // Test cancel booking trainer availability service slice1
            //      Test cancel slot TA not created by TA
            [
                '_id' => new MongoId('636e25bf0560b6ca2a22cbfb'),
                'namespace' => static::GLOFOX_NAMESPACE,
                'branch_id' => static::GLOFOX_BRANCH_ID,
                'user_id' => static::GLOFOX_USER_ID,
                'user_name' => static::GLOFOX_USER_NAME,
                'type' => BookingType::TIME_SLOTS,
                'time_slot_id' => '636e15ab91a57deb4136f6b7',
                'model' => ModelList::APPOINTMENTS,
                'model_id' => static::CANCEL_TA_APPOINTMENT_ID,
                'status' => BookingStatus::BOOKED,
                'time_start' => new MongoDate(strtotime("+100 days 15 hours")),
                'time_finish' => new MongoDate(strtotime("+100 days 16 hours")),
                'event_name' => 'Test cancel slot TA not created by TA',
                'paid' => true,
                'modified' => new MongoDate(),
                'created' => new MongoDate(),
                'created_user_id' => static::GLOFOX_STAFF_ID,
                'is_first' => false,

                'event_id' => static::GLOFOX_EVENT_ID,
                'program_id' => static::GLOFOX_PROGRAM_ID,
            ],
            //      Test cancel slot TA created by TA
            [
                '_id' => new MongoId('636e25ce13e7a7a91dbd063f'),
                'namespace' => static::GLOFOX_NAMESPACE,
                'branch_id' => static::GLOFOX_BRANCH_ID,
                'user_id' => static::GLOFOX_USER_ID,
                'user_name' => static::GLOFOX_USER_NAME,
                'type' => BookingType::TIME_SLOTS,
                'time_slot_id' => '636e15bb09329aa9c364ada4',
                'model' => ModelList::APPOINTMENTS,
                'model_id' => static::CANCEL_TA_APPOINTMENT_ID,
                'status' => BookingStatus::BOOKED,
                'time_start' => new MongoDate(strtotime("+108 days 15 hours")),
                'time_finish' => new MongoDate(strtotime("+108 days 16 hours")),
                'event_name' => 'Test cancel slot TA created by TA',
                'paid' => true,
                'modified' => new MongoDate(),
                'created' => new MongoDate(),
                'created_user_id' => static::GLOFOX_STAFF_ID,
                'is_first' => false,

                'event_id' => static::GLOFOX_EVENT_ID,
                'program_id' => static::GLOFOX_PROGRAM_ID,
            ],
        ];
    }

    private function addAppointmentSlotsForTestingBookingsOverlappingValidations(): array
    {
        return [
            [
                '_id' => new MongoId('637b727154edd0776109dd9e'),
                'namespace' => static::GLOFOX_NAMESPACE,
                'branch_id' => static::GLOFOX_BRANCH_ID,
                'user_id' => '59a3011a05c677bda916612d',
                'user_name' => static::GLOFOX_USER_NAME,
                'type' => BookingType::TIME_SLOTS,
                'time_slot_id' => '637b7e0f7404b8495f144f26',
                'model' => ModelList::APPOINTMENTS,
                'model_id' => '637b70c21d8141da3fab4d4e',
                'status' => BookingStatus::BOOKED,
                'time_start' => new MongoDate(strtotime("2022-01-01 15:00:00")),
                'time_finish' => new MongoDate(strtotime("2022-01-01 16:00:00")),
                'event_name' => 'Test overlapping validations 1',
                'paid' => true,
                'modified' => new MongoDate(),
                'created' => new MongoDate(),
                'created_user_id' => static::GLOFOX_STAFF_ID,
                'is_first' => false,

                'event_id' => static::GLOFOX_EVENT_ID,
                'program_id' => static::GLOFOX_PROGRAM_ID,
            ],
            [
                '_id' => new MongoId('6384ac291d32d141f5e7850c'),
                'namespace' => static::GLOFOX_NAMESPACE,
                'branch_id' => static::GLOFOX_BRANCH_ID,
                'user_id' => '59a3011a05c677bda916611c',
                'user_name' => static::GLOFOX_USER_NAME,
                'type' => BookingType::TIME_SLOTS,
                'time_slot_id' => '637ddae04ed391dda797588a',
                'model' => ModelList::APPOINTMENTS,
                'model_id' => '6384ad09ba0783fea4e9c902',
                'status' => BookingStatus::RESERVED,
                'time_start' => new MongoDate(strtotime("2022-01-01 15:00:00")),
                'time_finish' => new MongoDate(strtotime("2022-01-01 16:00:00")),
                'event_name' => 'Test overlapping validations 2',
                'paid' => true,
                'modified' => new MongoDate(),
                'created' => new MongoDate(),
                'created_user_id' => static::GLOFOX_STAFF_ID,
                'is_first' => false,

                'event_id' => '637ddb3ec20e797066dbdb9e',
                'program_id' => static::GLOFOX_PROGRAM_ID,
            ],
        ];
    }

    private function addBookingsForTimeFiltering(): array
    {
        return [
            [
                '_id' => new MongoId('637b727154edd0776109dd91'),
                'namespace' => static::GLOFOX_NAMESPACE,
                'branch_id' => static::GLOFOX_BRANCH_ID,
                'user_id' => '59a7010a05c677b3a916612e',
                'user_name' => static::GLOFOX_USER_NAME,
                'type' => BookingType::TIME_SLOTS,
                'time_slot_id' => '637b7e0f7404b8495f144f26',
                'model' => ModelList::APPOINTMENTS,
                'model_id' => '637b70c21d8141da3fab4d4e',
                'status' => BookingStatus::BOOKED,
                'time_start' => new MongoDate(strtotime("2023-08-15 01:00:00")),
                'time_finish' => new MongoDate(strtotime("2023-08-15 02:00:00")),
                'event_name' => 'TimeFilter1',
                'paid' => true,
                'modified' => new MongoDate(),
                'created' => new MongoDate(),
                'created_user_id' => static::GLOFOX_STAFF_ID,
                'is_first' => false,
                'event_id' => static::GLOFOX_EVENT_ID,
                'program_id' => static::GLOFOX_PROGRAM_ID,
            ],
            [
                '_id' => new MongoId('637b727154edd0776109dd92'),
                'namespace' => static::GLOFOX_NAMESPACE,
                'branch_id' => static::GLOFOX_BRANCH_ID,
                'user_id' => '59a7010a05c677b3a916612e',
                'user_name' => static::GLOFOX_USER_NAME,
                'type' => BookingType::TIME_SLOTS,
                'time_slot_id' => '637ddae04ed391dda797588a',
                'model' => ModelList::APPOINTMENTS,
                'model_id' => '6384ad09ba0783fea4e9c902',
                'status' => BookingStatus::BOOKED,
                'time_start' => new MongoDate(strtotime("2023-08-15 04:00:00")),
                'time_finish' => new MongoDate(strtotime("2023-08-15 05:00:00")),
                'event_name' => 'TimeFilter2',
                'paid' => true,
                'modified' => new MongoDate(),
                'created' => new MongoDate(),
                'created_user_id' => static::GLOFOX_STAFF_ID,
                'is_first' => false,
                'event_id' => '637ddb3ec20e797066dbdb9e',
                'program_id' => static::GLOFOX_PROGRAM_ID,
            ],
            [
                '_id' => new MongoId('637b727154edd0776109dd93'),
                'branch_id' => static::GLOFOX_BRANCH_ID,
                'namespace' => static::GLOFOX_NAMESPACE,
                'event_id' => '49b7012a05c677c9a512503c',
                'program_id' => '13b7411a15c676bda824611b',
                'status' => BookingStatus::BOOKED,
                'type' => BookingType::EVENTS,
                'user_name' => static::GLOFOX_USER_NAME,
                'model_name' => 'TimeFilter3',
                'user_id' => '59a7010a05c677b3a916612e',
                'time_start' => new MongoDate(strtotime("2023-08-15 08:00:00")),
                'time_finish' => new MongoDate(strtotime("2023-08-15 09:00:00")),
                'paid' => true,
                'attended' => false,
                'date_start' => new MongoDate(strtotime("2023-08-15 08:00:00")),
                'date_finish' => new MongoDate(strtotime("2023-08-15 09:00:00")),
                'is_from_waiting_list' => null,
                'guest_bookings' => 0,
                'session_id' => null,
                'payment_method' => 'cash',
            ],
            [
                '_id' => new MongoId('637b727154edd0776109dd94'),
                'branch_id' => static::GLOFOX_BRANCH_ID,
                'namespace' => static::GLOFOX_NAMESPACE,
                'event_id' => '49b7012a05c677c9a512503c',
                'program_id' => '13b7411a15c676bda824611b',
                'status' => BookingStatus::BOOKED,
                'type' => BookingType::EVENTS,
                'user_name' => static::GLOFOX_USER_NAME,
                'model_name' => 'TimeFilter4-NextDay',
                'user_id' => '59a7010a05c677b3a916612e',
                'time_start' => new MongoDate(strtotime("2023-08-16 01:00:00")),
                'time_finish' => new MongoDate(strtotime("2023-08-16 02:00:00")),
                'paid' => true,
                'attended' => false,
                'date_start' => new MongoDate(strtotime("2023-08-16 01:00:00")),
                'date_finish' => new MongoDate(strtotime("2023-08-16 02:00:00")),
                'is_from_waiting_list' => null,
                'guest_bookings' => 0,
                'session_id' => null,
                'payment_method' => 'cash',
            ],
        ];
    }

    private function addBookingsForTimeStartTimeFinishFiltering(): array
    {
        return [
            [
                '_id' => new MongoId('6425b15ae0531a7dba1d1c4a'),
                'branch_id' => '5addc25383266f65abf515c4',
                'namespace' => 'namespace-5d56d214be51ef060f38408f',
                'event_id' => '6422c809c8378d292ac42518',
                'status' => 'BOOKED',
                'program_id' => '6422c81c7f3cb12e2e3f51f3',
                'type' => 'events',
                'user_id' => '5b3cd978d47b164572a65eff',
                'time_start' => new MongoDate(strtotime('+13 days 8 hours')),
                'time_finish' => new MongoDate(strtotime('+13 days 9 hours')),
                'paid' => false,
                'guest_bookings' => 0,
                'payment_method' => 'cash',
                'created' => new MongoDate(strtotime('+10 days')),
                'modified' => new MongoDate(strtotime('+11 days')),
            ],
            [
                '_id' => new MongoId('6422c7f680a9208d01d3e86c'),
                'branch_id' => '5addc25383266f65abf515c4',
                'namespace' => 'namespace-5d56d214be51ef060f38408f',
                'event_id' => '6422c809c8378d292ac42518',
                'status' => 'BOOKED',
                'program_id' => '6422c81c7f3cb12e2e3f51f3',
                'type' => 'events',
                'user_id' => '5b3cd978d47b164572a65eff',
                'time_start' => new MongoDate(strtotime('+11 days 8 hours')),
                'time_finish' => new MongoDate(strtotime('+11 days 9 hours')),
                'paid' => false,
                'guest_bookings' => 0,
                'payment_method' => 'cash',
                'created' => new MongoDate(strtotime('+10 days')),
                'modified' => new MongoDate(strtotime('+11 days')),
            ],
            [
                '_id' => new MongoId('6424648bc2de42ef7a8e2b31'),
                'branch_id' => '5addc25383266f65abf515c4',
                'namespace' => 'namespace-5d56d214be51ef060f38408f',
                'event_id' => '6422c809c8378d292ac42518',
                'status' => 'WAITING',
                'program_id' => '6422c81c7f3cb12e2e3f51f3',
                'type' => 'events',
                'user_id' => '5b3cd978d47b164572a65eff',
                'time_start' => new MongoDate(strtotime('+11 days 8 hours')),
                'time_finish' => new MongoDate(strtotime('+11 days 9 hours')),
                'paid' => false,
                'guest_bookings' => 0,
                'payment_method' => 'cash',
                'created' => new MongoDate(strtotime('+10 days')),
                'modified' => new MongoDate(strtotime('+11 days')),
            ],
        ];
    }

    private function addBookingsForGetTotalBookingsAndTotalWaiting()
    {
        return [
            [
                '_id' => new MongoId('63d936b80abdbe446d2f04cb'),
                'branch_id' => static::GLOFOX_BRANCH_ID,
                'namespace' => static::GLOFOX_NAMESPACE,
                'event_id' => '63d93312ece438f37486ee2c',
                'program_id' => self::GLOFOX_PROGRAM_ID,
                'status' => BookingStatus::WAITING,
                'type' => BookingType::EVENTS,
                'user_name' => static::GLOFOX_USER_NAME,
                'model_name' => 'Abs intense workout',
                'user_id' => '59a7010a05c677b3a916612e',
                "time_start" => new MongoDate(strtotime("+3 days 4 hours")),
                "time_finish" => new MongoDate(strtotime("+3 days 5 hours")),
                'paid' => true,
                'attended' => false,
                'date_start' => new MongoDate(strtotime("+3 days 4 hours")),
                'date_finish' => new MongoDate(strtotime("+3 days 5 hours")),
                'is_from_waiting_list' => null,
                'guest_bookings' => 31,
                'session_id' => null,
                'payment_method' => 'cash',
            ],
            [
                '_id' => new MongoId('63d93ff751da91a28cfa5670'),
                'branch_id' => static::GLOFOX_BRANCH_ID,
                'namespace' => static::GLOFOX_NAMESPACE,
                'event_id' => '63d9408704d422118e7116e0',
                'program_id' => self::GLOFOX_PROGRAM_ID,
                'status' => BookingStatus::BOOKED,
                'type' => BookingType::EVENTS,
                'user_name' => static::GLOFOX_USER_NAME,
                'model_name' => 'Abs intense workout',
                'user_id' => '59a7010a05c677b3a916612e',
                "time_start" => new MongoDate(strtotime("+4 days 4 hours")),
                "time_finish" => new MongoDate(strtotime("+4 days 5 hours")),
                'paid' => true,
                'attended' => false,
                'date_start' => new MongoDate(strtotime("+4 days 4 hours")),
                'date_finish' => new MongoDate(strtotime("+4 days 5 hours")),
                'is_from_waiting_list' => null,
                'guest_bookings' => 31,
                'session_id' => null,
                'payment_method' => 'cash',
            ],
        ];
    }

    private function addBookingsForBookingsControllerDateRangeTest(): array
    {
        return [
            [
                '_id' => new MongoId('63e0cba6cecaf33be192c682'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'event_id' => '63e0ca1064f091a1d8eaf376',
                'status' => 'CANCELED',
                'program_id' => '63e0ca85ef7ce00f799d1c65',
                'type' => 'events',
                'user_id' => '5b7beae94813d9602de2dda5',
                'time_start' => date('Y-m-d H:i:s', strtotime('2020-01-01 08:00:00')),
                'time_finish' => date('Y-m-d H:i:s', strtotime('2020-01-01 09:00:00')),
                'paid' => true,
                'guest_bookings' => 0,
                'payment_method' => 'cash',
                'attended' => false,
                'created' => new MongoDate(strtotime('+7 days')),
                'modified' => new MongoDate(strtotime('+7 days')),
            ],
            [
                '_id' => new MongoId('63e0cc0eec6f9c76435920d4'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'event_id' => '63e0ca1064f091a1d8eaf376',
                'status' => 'BOOKED',
                'program_id' => '63e0ca85ef7ce00f799d1c65',
                'type' => 'events',
                'user_id' => '5b7beae94813d9602de2dda5',
                'time_start' => date('Y-m-d H:i:s', strtotime('2020-01-01 08:00:00')),
                'time_finish' => date('Y-m-d H:i:s', strtotime('2020-01-01 09:00:00')),
                'paid' => true,
                'guest_bookings' => 2,
                'payment_method' => 'cash',
                'attended' => false,
                'created' => new MongoDate(strtotime('+7 days')),
                'modified' => new MongoDate(strtotime('+7 days')),
            ],
            [
                '_id' => new MongoId('63e0d061141f8124f3c861a9'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'event_id' => '63e0cb32a7064e73bd5833d6',
                'status' => 'BOOKED',
                'program_id' => '63e0ca85ef7ce00f799d1c65',
                'type' => 'events',
                'user_id' => '5b7beae94813d9602de2dda5',
                'time_start' => date('Y-m-d H:i:s', strtotime('2020-01-02 08:00:00')),
                'time_finish' => date('Y-m-d H:i:s', strtotime('2020-01-02 09:00:00')),
                'paid' => true,
                'guest_bookings' => 5,
                'payment_method' => 'cash',
                'attended' => false,
                'created' => new MongoDate(strtotime('+7 days')),
                'modified' => new MongoDate(strtotime('+7 days')),
            ],
            [
                '_id' => new MongoId('63e2a2609dcdbc4f07c75cdf'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'event_id' => '63e0cb32a7064e73bd5833d6',
                'status' => 'BOOKED',
                'program_id' => '63e0ca85ef7ce00f799d1c65',
                'type' => 'events',
                'user_id' => '59a7010a05c677b3a916612e',
                'time_start' => date('Y-m-d H:i:s', strtotime('2020-01-02 08:00:00')),
                'time_finish' => date('Y-m-d H:i:s', strtotime('2020-01-02 09:00:00')),
                'paid' => true,
                'guest_bookings' => 2,
                'payment_method' => 'cash',
                'attended' => false,
                'created' => new MongoDate(strtotime('+7 days')),
                'modified' => new MongoDate(strtotime('+7 days')),
            ],
            [
                '_id' => new MongoId('63e3b4aab0d0af41c0ee2626'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'status' => 'BOOKED',
                'event_name' => 'Facility appointment',
                'type' => 'time_slots',
                'model' => 'facilities',
                'model_id' => '5baa63007da70701ce7e4743',
                'user_id' => '59a7010a05c677b3a916612e',
                'time_slot_id' => '63e3a6c4e568e75ff0846392',
                'time_start' => new MongoDate(strtotime('2020-09-01 15:00:00')),
                'time_finish' => new MongoDate(strtotime('2020-09-01 15:30:00')),
                'created' => new MongoDate(strtotime('+7 days')),
                'modified' => new MongoDate(strtotime('+7 days')),
            ],
            [
                '_id' => new MongoId('63e4a3175f6efba2b73b6210'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'event_id' => '63e0cb32a7064e73bd5833d6',
                'status' => 'BOOKED',
                'program_id' => '63e0ca85ef7ce00f799d1c65',
                'type' => 'events',
                'user_id' => '59a7010a05c677b3a916612e',
                'time_start' => date('Y-m-d H:i:s', strtotime('2015-01-02 08:00:00')),
                'time_finish' => date('Y-m-d H:i:s', strtotime('2015-01-02 09:00:00')),
                'paid' => true,
                'guest_bookings' => 2,
                'payment_method' => 'cash',
                'attended' => false,
                'created' => new MongoDate(strtotime('+7 days')),
                'modified' => new MongoDate(strtotime('+7 days')),
            ],
            [
                '_id' => new MongoId('63e4a796f5b248b1bd6a199b'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'user_id' => '59a7010a05c677b3a916612e',
                'user_name' => 'Timmy Fisher',
                'type' => 'courses',
                'model' => 'courses',
                'paid' => true,
                'model_name' => 'Test course',
                'model_id' => '63e4a5eb27cbc28dc5b99ce5',
                'status' => 'BOOKED',
                'course_id' => '63e4a5eb27cbc28dc5b99ce5',
                'session_id' => 1_663_080_966_272,
                'date_start' => date('Y-m-d H:i:s', strtotime('2016-07-08T13:00:00.000Z')),
                'date_finish' => date('Y-m-d H:i:s', strtotime('2016-07-08T14:00:00.000Z')),
                'guest_bookings' => 0,
                'created' => new MongoDate(),
                'modified' => new MongoDate(),
                'is_first' => true,
            ],
        ];
    }

    private function addBookingsForCourseTotalBookingsAndTotalWaiting()
    {
        return [
            $this->createCourseBooking([
                '_id' => new MongoId('63e4a796f5b248b1bd6a19c1'),
                'user_id' => '59a7010a05c677b3a916612e',
                'model_id' => '52a7011a05c677bda826611b',
                'course_id' => '63e4a5eb27cbc28dc5b99ce5',
                'session_id' => '4e0a121e958c4582b4c9327e6baee9c1',
            ]),
            $this->createCourseBooking([
                '_id' => new MongoId('63e4a796f5b248b1bd6a19c2'),
                'user_id' => '59a7010a05c677b3a916612e',
                'model_id' => '52a7011a05c677bda826611c',
                'course_id' => '63e4a5eb27cbc28dc5b99ce5',
                'session_id' => '4e0a121e958c4582b4c9327e6baee9c2',
            ]),
            $this->createCourseBooking([
                '_id' => new MongoId('63e4a796f5b248b1bd6a19c3'),
                'user_id' => '59a7010a05c677b3a916612e',
                'model_id' => '60f934927a75506e58089e81',
                'course_id' => '63e4a5eb27cbc28dc5b99ce5',
                'session_id' => 1_626_944_650_130,
                'guest_bookings' => 2,
            ]),
            $this->createCourseBooking([
                '_id' => new MongoId('63e4a796f5b248b1bd6a19c4'),
                'user_id' => self::GLOFOX_USER_ID,
                'model_id' => '63e4a5eb27cbc28dc5b99ce5',
                'course_id' => '63e4a5eb27cbc28dc5b99ce5',
                'session_id' => 1_663_080_966_231,
            ]),
            $this->createCourseBooking([
                '_id' => new MongoId('63e4a796f5b248b1bd6a19c5'),
                'user_id' => self::GLOFOX_USER_ID,
            ]),
            $this->createCourseBooking([
                '_id' => new MongoId('63e4a796f5b248b1bd6a19c6'),
                'user_id' => '69a3011a05c677bda916712d',
            ]),
            $this->createCourseBooking([
                '_id' => new MongoId('63e4a796f5b248b1bd6a19c7'),
                'user_id' => 'a9a3321a05c677bda916611c',
                'guest_bookings' => 2,
            ]),
            $this->createCourseBooking([
                '_id' => new MongoId('63e4a796f5b248b1bd6a19c8'),
                'user_id' => 'a9a2222a05c677bda916613d',
            ]),
            $this->createCourseBooking([
                '_id' => new MongoId('63e4a796f5b248b1bd6a19c9'),
                'user_id' => '63074e6944eb47f8fd61a035',
                'guest_bookings' => 1,
            ]),
        ];
    }

    private function createCourseBooking(array $override = []): array
    {
        $default = [
            '_id' => new MongoId(),
            'branch_id' => '49a7011a05c677b9a916612a',
            'namespace' => 'glofox',
            'user_id' => '63074e6944eb47f8fd61a035',
            'user_name' => 'Jay Dee',
            'type' => 'courses',
            'model' => 'courses',
            'paid' => true,
            'model_name' => 'Test Many Bookings',
            'model_id' => '63e4a5eb27cbc28dc5b99ce6',
            'status' => 'BOOKED',
            'course_id' => '63e4a5eb27cbc28dc5b99ce6',
            'session_id' => '4e0a121e958c4582b4c9327e6baee9s3',
            'time_start' => date('Y-m-d H:m:i', strtotime('+1 hour')),
            'time_finish' => date('Y-m-d H:m:i', strtotime('+2 hour')),
            'date_start' => date('Y-m-d', strtotime('+1 hour')),
            'date_finish' => date('Y-m-d', strtotime('+2 hour')),
            'guest_bookings' => 0,
            'created' => new MongoDate(),
            'modified' => new MongoDate(),
            'is_first' => true,
        ];

        return array_replace_recursive($default, $override);
    }

    private function addBookingsForTimeSlotGetTotalBookingsTest(): array
    {
        return [
            [
                '_id' => new MongoId('63e3b4aab0d0af41c0ee2627'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'status' => 'BOOKED',
                'event_name' => 'Facility appointment 1',
                'type' => 'time_slots',
                'model' => 'facilities',
                'model_id' => '5baa63007da70701ce7e4743',
                'user_id' => '59a7010a05c677b3a916612e',
                'time_slot_id' => '63e3a6c4e568e75ff0846393',
                'time_start' => new MongoDate(strtotime('2023-09-01 15:00:00')),
                'time_finish' => new MongoDate(strtotime('2023-09-01 15:30:00')),
                'created' => new MongoDate(strtotime('+7 days')),
                'modified' => new MongoDate(strtotime('+7 days')),
            ],
            [
                '_id' => new MongoId('63e3b4aab0d0af41c0ee2628'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'status' => 'BOOKED',
                'event_name' => 'Facility appointment 2',
                'type' => 'time_slots',
                'model' => 'facilities',
                'model_id' => '5baa63007da70701ce7e4743',
                'user_id' => '59a7011a05c677bda916619b',
                'time_slot_id' => '63e3a6c4e568e75ff0846394',
                'time_start' => new MongoDate(strtotime('2023-09-02 15:00:00')),
                'time_finish' => new MongoDate(strtotime('2023-09-02 15:30:00')),
                'created' => new MongoDate(strtotime('+7 days')),
                'modified' => new MongoDate(strtotime('+7 days')),
            ],
            [
                '_id' => new MongoId('63e3b4aab0d0af41c0ee2629'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'status' => 'BOOKED',
                'event_name' => 'Facility appointment 3',
                'type' => 'time_slots',
                'model' => 'facilities',
                'model_id' => '5baa63007da70701ce7e4743',
                'user_id' => '59a3011a05c677bda916612d',
                'time_slot_id' => '63e3a6c4e568e75ff0846395',
                'time_start' => new MongoDate(strtotime('2023-09-03 15:00:00')),
                'time_finish' => new MongoDate(strtotime('2023-09-03 15:30:00')),
                'created' => new MongoDate(strtotime('+7 days')),
                'modified' => new MongoDate(strtotime('+7 days')),
            ],
            [
                '_id' => new MongoId('63e4a796f5b248b1bd6a1991'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'user_id' => '59a3011a05c677bda916612d',
                'user_name' => 'Jay Dee',
                'type' => 'courses',
                'model' => 'courses',
                'paid' => true,
                'model_name' => 'Test course',
                'model_id' => '63e4a5eb27cbc28dc5b99ce5',
                'status' => 'BOOKED',
                'course_id' => '63e4a5eb27cbc28dc5b99ce5',
                'session_id' => 1_663_080_966_272,
                'date_start' => date('Y-m-d H:i:s', strtotime('2023-07-08T13:00:00.000Z')),
                'date_finish' => date('Y-m-d H:i:s', strtotime('2023-07-08T14:00:00.000Z')),
                'guest_bookings' => 0,
                'created' => new MongoDate(),
                'modified' => new MongoDate(),
                'is_first' => true,
            ],
            [
                '_id' => new MongoId('637b727154edd0776109dd99'),
                'namespace' => static::GLOFOX_NAMESPACE,
                'branch_id' => static::GLOFOX_BRANCH_ID,
                'user_id' => '59a7010a05c677b3a916612e',
                'user_name' => static::GLOFOX_USER_NAME,
                'type' => BookingType::TIME_SLOTS,
                'time_slot_id' => null,
                'model' => ModelList::APPOINTMENTS,
                'model_id' => '6384ad09ba0783fea4e9c902',
                'status' => BookingStatus::BOOKED,
                'time_start' => new MongoDate(strtotime("+10 days 4 hours")),
                'time_finish' => new MongoDate(strtotime("+10 days 5 hours")),
                'event_name' => 'Appointment With Invalid Timeslot ID',
                'paid' => true,
                'modified' => new MongoDate(),
                'created' => new MongoDate(),
                'created_user_id' => static::GLOFOX_STAFF_ID,
                'is_first' => false,
                'event_id' => '637ddb3ec20e797066dbdb9e',
                'program_id' => static::GLOFOX_PROGRAM_ID,
            ],
        ];
    }

    private function addBookingsListAllBookingsTest(): array
    {
        return [
            [
                '_id' => new MongoId('6435581e1b4f203a48397624'),
                'branch_id' => '6435563a6272b68401e85728',
                'namespace' => 'listallbookings',
                'status' => 'BOOKED',
                'event_name' => 'Class 1',
                'event_id' => '643559a08b753acba764c6c4',
                'type' => 'events',
                'model_name' => 'Class 1',
                'user_id' => '59a7010a05c677b3a916612e',
                'time_start' => new MongoDate(strtotime('2022-09-01 15:00:00')),
                'time_finish' => new MongoDate(strtotime('2022-09-01 15:30:00')),
                'created' => new MongoDate(strtotime('+7 days')),
                'modified' => new MongoDate(strtotime('+7 days')),
            ],
            [
                '_id' => new MongoId('643569da5656d16b30337f32'),
                'branch_id' => '6435563a6272b68401e85728',
                'namespace' => 'listallbookings',
                'status' => 'BOOKED',
                'type' => 'time_slots',
                'time_slot_id' => null,
                'model' => 'appointments',
                'model_id' => '64365bc0aa5a635ccacd1a28',
                'time_start' => new MongoDate(strtotime('2022-09-01 15:00:00')),
                'time_finish' => new MongoDate(strtotime('2022-09-01 15:30:00')),
                'event_name' => 'Appointment 1',
                'paid' => true,
                'modified' => new MongoDate(),
                'created' => new MongoDate(),
                'is_first' => false,
            ],
            [
                '_id' => new MongoId('64357a6af3efd9bc846f14ae'),
                'branch_id' => '6435563a6272b68401e85728',
                'namespace' => 'listallbookings',
                'status' => 'CANCELED',
                'type' => 'time_slots',
                'time_slot_id' => null,
                'model' => 'appointments',
                'model_id' => '64365bc0aa5a635ccacd1a28',
                'time_start' => new MongoDate(strtotime('2022-09-01 15:00:00')),
                'time_finish' => new MongoDate(strtotime('2022-09-01 15:30:00')),
                'event_name' => 'Appointment 2',
                'paid' => true,
                'modified' => new MongoDate(),
                'created' => new MongoDate(),
                'is_first' => false,
            ],
            [
                '_id' => new MongoId('643569e7aaeb91e7684ae516'),
                'branch_id' => '6435563a6272b68401e85728',
                'namespace' => 'listallbookings',
                'status' => 'BOOKED',
                'type' => 'time_slots',
                'time_slot_id' => null,
                'model' => 'appointments',
                'model_id' => '64365bc0aa5a635ccacd1a28',
                'time_start' => new MongoDate(strtotime('2022-09-02 15:00:00')),
                'time_finish' => new MongoDate(strtotime('2022-09-02 15:30:00')),
                'event_name' => 'Appointment 3',
                'paid' => true,
                'modified' => new MongoDate(),
                'created' => new MongoDate(),
                'is_first' => false,
            ],
            [
                '_id' => new MongoId('64356aa7a605e4a2ab4e613d'),
                'branch_id' => '6435563a6272b68401e85728',
                'namespace' => 'listallbookings',
                'status' => 'BOOKED',
                'event_name' => 'Facility 1',
                'type' => 'time_slots',
                'model' => 'facilities',
                'model_id' => '5baa63007da70701ce7e4743',
                'user_id' => '59a3011a05c677bda916612d',
                'time_slot_id' => null,
                'time_start' => new MongoDate(strtotime('2022-09-03 15:00:00')),
                'time_finish' => new MongoDate(strtotime('2022-09-03 15:30:00')),
                'created' => new MongoDate(strtotime('+7 days')),
                'modified' => new MongoDate(strtotime('+7 days')),
            ],
            [
                '_id' => new MongoId('64356ab5500a5c7372ba5514'),
                'branch_id' => '6435563a6272b68401e85728',
                'namespace' => 'listallbookings',
                'user_id' => '59a3011a05c677bda916612d',
                'user_name' => 'Jay Dee',
                'type' => 'courses',
                'model' => 'courses',
                'paid' => true,
                'model_name' => 'Course 1',
                'model_id' => '63e4a5eb27cbc28dc5b99ce5',
                'status' => 'BOOKED',
                'course_id' => '63e4a5eb27cbc28dc5b99ce5',
                'session_id' => 1_663_080_966_272,
                'date_start' => new MongoDate(strtotime('2022-09-03 15:00:00')),
                'date_finish' => new MongoDate(strtotime('2022-09-03 15:30:00')),
                'guest_bookings' => 0,
                'created' => new MongoDate(),
                'modified' => new MongoDate(),
                'is_first' => true,
            ],
            [
                '_id' => new MongoId('64356b344911dbc18e0a081d'),
                'branch_id' => '6435563a6272b68401e85728',
                'namespace' => 'listallbookings',
                'status' => 'BOOKED',
                'model' => 'users',
                'model_id' => '64356ba8219f042b5adf2bea',
                'event_name' => 'PT with trainer',
                'type' => 'time_slots',
                'user_id' => '59a3011a05c677bda916612d',
                'paid' => true,
                'time_start' => new MongoDate(strtotime('2022-09-01 15:00:00')),
                'time_finish' => new MongoDate(strtotime('2022-09-01 15:30:00')),
                'created' => new MongoDate(strtotime('+7 days')),
                'modified' => new MongoDate(strtotime('+7 days')),
            ],
        ];
    }

    public function addBookingSpots(): array
    {
        return [
            [
                '_id' => new MongoId('636e25bf0560b6ca2a22cbcc'),
                'namespace' => static::GLOFOX_NAMESPACE,
                'branch_id' => static::GLOFOX_BRANCH_ID,
                'user_id' => static::GLOFOX_USER_ID,
                'user_name' => static::GLOFOX_USER_NAME,
                'status' => BookingStatus::BOOKED,
                'type' => 'events',
                'model_name' => 'Test',
                'time_start' => new MongoDate(strtotime("+100 days 15 hours")),
                'time_finish' => new MongoDate(strtotime("+100 days 16 hours")),
                'event_name' => 'Test cancel slot TA not created by TA',
                'paid' => true,
                'modified' => new MongoDate(),
                'created' => new MongoDate(),
                'created_user_id' => static::GLOFOX_STAFF_ID,
                'is_first' => false,

                'event_id' => static::GLOFOX_EVENT_ID,
                'program_id' => static::GLOFOX_PROGRAM_ID,
            ],
        ];
    }

    private function forVirtualSlotTest(): array
    {
        return [
            [
                '_id' => new MongoId('64620fa4a255bbf45701ee4b'),
                'branch_id' => static::VIRTUAL_SLOT_BRANCH_ID,
                'namespace' => static::VIRTUAL_SLOT_BRANCH_NAMESPACE,
                'event_id' => static::VIRTUAL_SLOT_EVENT_ID,
                'status' => BookingStatus::BOOKED,
                'program_id' => static::VIRTUAL_SLOT_PROGRAM_ID,
                'type' => BookingType::EVENTS,
                'user_id' => static::VIRTUAL_SLOT_MEMBER_ID,
                'time_start' => date('Y-m-d H:i:s', strtotime('2023-04-28 08:00:00')),
                'time_finish' => date('Y-m-d H:i:s', strtotime('2023-04-28 09:00:00')),
                'paid' => true,
                'guest_bookings' => 0,
                'payment_method' => 'cash',
                'attended' => false,
                'created' => new MongoDate(),
                'modified' => new MongoDate(),
            ],
            [
                '_id' => new MongoId('64621e15d1ee44c729697f3b'),
                'branch_id' => static::VIRTUAL_SLOT_BRANCH_ID,
                'namespace' => static::VIRTUAL_SLOT_BRANCH_NAMESPACE,
                'status' => BookingStatus::BOOKED,
                'event_name' => static::VIRTUAL_SLOT_APPOINTMENT_NAME,
                'type' => BookingType::TIME_SLOTS,
                'model' => ModelList::APPOINTMENTS,
                'model_id' => static::VIRTUAL_SLOT_APPOINTMENT_ID,
                'user_id' => static::VIRTUAL_SLOT_MEMBER_ID,
                'time_slot_id' => static::VIRTUAL_SLOT_APPOINTMENT_SLOT_ID_1,
                'time_start' => new MongoDate(strtotime('2023-04-28 09:00:00')),
                'time_finish' => new MongoDate(strtotime('2023-04-28 10:00:00')),
                'created' => new MongoDate(),
                'modified' => new MongoDate(),
            ],
            [
                '_id' => new MongoId('646221d56bc670caa5e698f3'),
                'branch_id' => static::VIRTUAL_SLOT_BRANCH_ID,
                'namespace' => static::VIRTUAL_SLOT_BRANCH_NAMESPACE,
                'status' => BookingStatus::BOOKED,
                'event_name' => '',
                'type' => BookingType::TIME_SLOTS,
                'model' => ModelList::APPOINTMENTS,
                'model_id' => static::VIRTUAL_SLOT_APPOINTMENT_ID,
                'user_id' => static::VIRTUAL_SLOT_MEMBER_ID,
                'time_slot_id' => static::VIRTUAL_SLOT_APPOINTMENT_SLOT_ID_2,
                'time_start' => new MongoDate(strtotime('2023-04-28 17:00:00')),
                'time_finish' => new MongoDate(strtotime('2023-04-28 18:00:00')),
                'created' => new MongoDate(),
                'modified' => new MongoDate(),
            ],
        ];
    }

    private function forPayroll(): array
    {
        return [
            [
                '_id' => new MongoId('64b80982869258e73a1ee221'),
                'branch_id' => '64b803ccfc8ba4d6f8d99c9c',
                'namespace' => 'payroll',
                'status' => BookingStatus::BOOKED,
                'event_name' => 'Appointment for Payroll 1',
                'type' => BookingType::TIME_SLOTS,
                'model' => ModelList::APPOINTMENTS,
                'model_id' => '64b8068173b491198db332bc',
                'user_id' => '64b80a302cccbf9c9db7ba16',
                'time_slot_id' => '64b808d559485aba4e71a4c1',
                'time_start' => new MongoDate(Carbon::today()->subDays(3)->subHours(2)->getTimestamp()),
                'time_finish' => new MongoDate(Carbon::today()->subDays(3)->subHours(1)->getTimestamp()),
                'created' => new MongoDate(),
                'modified' => new MongoDate(),
            ],
        ];
    }

    private function forGetAppointmentStaffTest(): array
    {
        return [
            [
                '_id' => new MongoId('59a7010a05d3e7b3a916612e'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'event_id' => '49b7012a05c677c9a512503e',
                'program_id' => '13b7411a15c676bda824611d',
                'status' => 'BOOKED',
                'type' => 'events',
                'user_name' => 'Test User',
                'model_name' => 'Test',
                'model' => 'appointments',
                'user_id' => '59a7010a05c677b3a916612d',
                //2023-07-28 17:00:00
                'time_start' => new MongoDate(1_690_574_400),
                //2023-07-28 18:00:00
                'time_finish' => new MongoDate(1_690_578_000),
                'paid' => false,
                'attended' => false,
                //2023-07-28
                'date_start' => new MongoDate(1_690_513_200),
                //2023-07-28
                'date_finish' => new MongoDate(1_690_513_200),
                'is_from_waiting_list' => null,
                'guest_bookings' => 0,
                'session_id' => null,
                'payment_method' => 'cash',
            ],
        ];
    }

    private function forClassPassOverBooking(): array
    {
        return [
            [
                '_id' => new MongoId(),
                'branch_id' => '64784ace2c8a824784ab4cc4',
                'namespace' => 'classpassoverbooking',
                'event_id' => '6477510f6938600d745c4edb',
                'program_id' => '6477520fb96e96bf7792d62d',
                'status' => 'BOOKED',
                'event_name' => 'Class 1',
                'type' => 'events',
                'model_name' => 'Class 1',
                'user_id' => (string)new MongoId(),
                'time_start' => new MongoDate(Carbon::now()->AddHours(2)->getTimestamp()),
                'time_finish' => new MongoDate(Carbon::now()->AddHours(3)->getTimestamp()),
                'created' => new MongoDate(strtotime('+7 days')),
                'modified' => new MongoDate(strtotime('+7 days')),
            ],
            [
                '_id' => new MongoId(),
                'branch_id' => '64784ace2c8a824784ab4cc4',
                'namespace' => 'classpassoverbooking',
                'event_id' => '6477510f6938600d745c4edb',
                'program_id' => '6477520fb96e96bf7792d62d',
                'status' => 'BOOKED',
                'event_name' => 'Class 1',
                'type' => 'events',
                'model_name' => 'Class 1',
                'user_id' => (string)new MongoId(),
                'time_start' => new MongoDate(Carbon::now()->AddHours(2)->getTimestamp()),
                'time_finish' => new MongoDate(Carbon::now()->AddHours(3)->getTimestamp()),
                'created' => new MongoDate(strtotime('+7 days')),
                'modified' => new MongoDate(strtotime('+7 days')),
            ],
        ];
    }

    private function addBookingsForCountEventIdWithGuests(): array
    {
        $shared = [
            'branch_id' => static::GLOFOX_BRANCH_ID,
            'namespace' => static::GLOFOX_NAMESPACE,
            'program_id' => self::GLOFOX_PROGRAM_ID,
            'status' => BookingStatus::BOOKED,
            'type' => BookingType::EVENTS,
            'user_name' => static::GLOFOX_USER_NAME,
            'time_start' => new MongoDate(strtotime('2023-06-18 08:00:00')),
            'time_finish' => new MongoDate(strtotime('2023-06-18 09:00:00')),
            'paid' => true,
            'attended' => true,
            'date_start' => new MongoDate(strtotime('2023-06-18 08:00:00')),
            'date_finish' => new MongoDate(strtotime('2023-06-18 09:00:00')),
            'is_from_waiting_list' => null,
            'guest_bookings' => 0,
            'session_id' => null,
            'payment_method' => 'cash',
        ];

        return [
            $this->createBooking(array_merge($shared, [
                'event_id' => '63d93312ece438f37486eeg1',
            ])),
            $this->createBooking(array_merge($shared, [
                'event_id' => '63d93312ece438f37486eeg1',
            ])),
            $this->createBooking(array_merge($shared, [
                'event_id' => '63d93312ece438f37486eeg1',
            ])),
            $this->createBooking(array_merge($shared, [
                'event_id' => '63d93312ece438f37486eeg2',
                'attended' => false,
            ])),
            $this->createBooking(array_merge($shared, [
                'event_id' => '63d93312ece438f37486eeg3',
                'guest_bookings' => null,
            ])),
            $this->createBooking(array_merge($shared, [
                'event_id' => '63d93312ece438f37486eeg4',
                'attended' => false,
                'status' => BookingStatus::WAITING(),
            ])),
            $this->createBooking(array_merge($shared, [
                'event_id' => '63d93312ece438f37486eeg4',
                'attended' => false,
                'status' => BookingStatus::WAITING(),
            ])),
            $this->createBooking(array_merge($shared, [
                'event_id' => '63d93312ece438f37486eeg5',
                'guest_bookings' => 1,
            ])),
            $this->createBooking(array_merge($shared, [
                'event_id' => '63d93312ece438f37486eeg5',
                'guest_bookings' => 2,
                'status' => BookingStatus::CANCELED(),
            ])),
            $this->createBooking(array_merge($shared, [
                'event_id' => '63d93312ece438f37486eeg6',
                'guest_bookings' => 2,
                'type' => BookingType::TIME_SLOTS,
            ])),
            $this->createBooking(array_merge($shared, [
                'event_id' => '63d93312ece438f37486eeg6',
                'guest_bookings' => 2,
                'type' => BookingType::TIME_SLOTS,
            ])),
            $this->createBooking(array_merge($shared, [
                'event_id' => '63d93312ece438f37486eeg7',
            ])),
            $this->createBooking(array_merge($shared, [
                'event_id' => '63d93312ece438f37486eeg7',
                'guest_bookings' => 2,
            ])),
        ];
    }

    private function forEventForcedOverBookingSizeChange(): array
    {
        $start = Carbon::tomorrow()->setTime(8, 0);
        return [
            $this->createBooking([
                'branch_id' => '64784ace2c8a824784ab4cc4',
                'event_id' => '6538ded562890c7a3f572b19',
                'user_id' => '646e05b498dff227b9683452',
                'time_start' => new MongoDate($start->getTimestamp()),
                'time_finish' => new MongoDate($start->addHour()->getTimestamp()),
            ]),
        ];
    }

    private function getSharedBookingData(): array
    {
        return [
            'branch_id' => static::GLOFOX_BRANCH_ID,
            'namespace' => static::GLOFOX_NAMESPACE,
            'program_id' => '6672862317c4f80ebdb0ea51',
            'status' => BookingStatus::BOOKED,
            'type' => BookingType::EVENTS,
            'user_name' => static::GLOFOX_USER_NAME,
            'paid' => true,
            'is_from_waiting_list' => null,
            'guest_bookings' => 0,
            'session_id' => null,
            'payment_method' => 'cash',
        ];
    }

    private function forActiveLimitBookings(): array
    {
        $shared = $this->getSharedBookingData();
        $userId = '66727f8956a5e42aa5294836';
        $userId2 = '6672886ecbde2a9cec5d5df2';
        $userId3 = '66728eef22db8a41d451fdad';

        return [
            $this->createBooking(array_merge($shared, [
                'user_id' => $userId,
                'event_id' => '66728bdd22ffb1d8a4dd06de',
                'time_start' => new MongoDate(Carbon::yesterday()->addDays(-4)->setTime(8, 0)->getTimestamp()),
                'time_finish' => new MongoDate(Carbon::yesterday()->addDays(-4)->setTime(9, 0)->getTimestamp()),
            ])),
            $this->createBooking(array_merge($shared, [
                'user_id' => $userId,
                'event_id' => '66728eb2949dfbc725dfc63d',
                'time_start' => new MongoDate(Carbon::yesterday()->addDays(-2)->setTime(8, 0)->getTimestamp()),
                'time_finish' => new MongoDate(Carbon::yesterday()->addDays(-2)->setTime(9, 0)->getTimestamp()),
            ])),
            $this->createBooking(array_merge($shared, [
                'user_id' => $userId,
                'event_id' => '667289bfd03be8240fea6c27',
                'time_start' => new MongoDate(Carbon::yesterday()->setTime(8, 0)->getTimestamp()),
                'time_finish' => new MongoDate(Carbon::yesterday()->setTime(9, 0)->getTimestamp()),
            ])),
            $this->createBooking(array_merge($shared, [
                'user_id' => $userId2,
                'event_id' => '667289bfd03be8240fea6c27',
                'time_start' => new MongoDate(Carbon::yesterday()->setTime(8, 0)->getTimestamp()),
                'time_finish' => new MongoDate(Carbon::yesterday()->setTime(9, 0)->getTimestamp()),
            ])),
            $this->createBooking(array_merge($shared, [
                'user_id' => $userId2,
                'event_id' => '66728c1340ad856f51d52639',
                'time_start' => new MongoDate(Carbon::tomorrow()->setTime(8, 0)->getTimestamp()),
                'time_finish' => new MongoDate(Carbon::tomorrow()->setTime(9, 0)->getTimestamp()),
            ])),
            $this->createBooking(array_merge($shared, [
                'user_id' => $userId3,
                'event_id' => '66728c577602a0aba68e5802',
                'time_start' => new MongoDate(Carbon::tomorrow()->addDays(2)->setTime(8, 0)->getTimestamp()),
                'time_finish' => new MongoDate(Carbon::tomorrow()->addDays(2)->setTime(9, 0)->getTimestamp()),
            ])),
            $this->createBooking(array_merge($shared, [
                'user_id' => $userId3,
                'event_id' => '66728c679528d5ede7cc5a38',
                'time_start' => new MongoDate(Carbon::tomorrow()->addDays(4)->setTime(8, 0)->getTimestamp()),
                'time_finish' => new MongoDate(Carbon::tomorrow()->addDays(4)->setTime(9, 0)->getTimestamp()),
            ])),
            $this->createBooking(array_merge($shared, [
                'user_id' => $userId3,
                'event_id' => '66728c991c57b6dcccfa9fa6',
                'time_start' => new MongoDate(Carbon::tomorrow()->addDays(6)->setTime(8, 0)->getTimestamp()),
                'time_finish' => new MongoDate(Carbon::tomorrow()->addDays(6)->setTime(9, 0)->getTimestamp()),
            ])),
        ];
    }

    private function forIntegrationTests(): array
    {
        return [
            $this->createBooking(array_merge(
                $this->getSharedBookingData(),
                [
                    '_id' => new MongoId('64b80982869258e73a1ee223'),
                    'user_id' => '66727f8956a5e42aa5294836',
                    'event_id' => '66728bdd22ffb1d8a4dd06de',
                    'origin' => 'classpass',
                    'metadata' => [
                        'classpass' => [
                            '_id' => 'ref-booking-id',
                        ],
                    ],
                    'time_start' => new MongoDate(Carbon::yesterday()->subDays(4)->setTime(8, 0)->getTimestamp()),
                    'time_finish' => new MongoDate(Carbon::yesterday()->subDays(4)->setTime(9, 0)->getTimestamp()),
                ]
            )),
        ];
    }

    private function addBookingsForNonConfirmedBookingsInBookingOpenWindowByBranchId(): array
    {
        return [
            $this->createBooking(array_merge(
                $this->getSharedBookingData(),
                [
                    '_id' => new MongoId('99999982869258e73a1ee220'),
                    'user_id' => '66727f8956a5e42aa5294836',
                    'event_id' => '66728bdd22ffb1d8a4dd06de',
                    'event_name' => 'Test Event',
                    'model_name' => 'Test Model',
                    'status' => 'BOOKED',
                    'type' => 'events',
                    'confirmed' => false,
                    'time_start' => new MongoDate((new Carbon("2024-07-01 15:30:00"))->getTimestamp()),
                    'time_finish' => new MongoDate((new Carbon("2024-07-01 16:30:00"))->getTimestamp()),
                ],
            )),
            $this->createBooking(array_merge(
                $this->getSharedBookingData(),
                [
                    '_id' => new MongoId('99999982869258e73a1ee221'),
                    'user_id' => '66727f8956a5e42aa5294836',
                    'event_id' => '66728bdd22ffb1d8a4dd06de',
                    'event_name' => 'Test Event',
                    'model_name' => 'Test Model',
                    'status' => 'BOOKED',
                    'type' => 'events',
                    'confirmed' => false,
                    'time_start' => new MongoDate((new Carbon("2024-07-12 15:30:00"))->getTimestamp()),
                    'time_finish' => new MongoDate((new Carbon("2024-07-12 16:30:00"))->getTimestamp()),
                ],
            )),
            $this->createBooking(array_merge(
                $this->getSharedBookingData(),
                [
                    '_id' => new MongoId('99999982869258e73a1ee222'),
                    'user_id' => '66727f8956a5e42aa5294836',
                    'event_id' => '66728bdd22ffb1d8a4dd06de',
                    'event_name' => 'Test Event',
                    'model_name' => 'Test Model',
                    'status' => 'BOOKED',
                    'type' => 'events',
                    'confirmed' => false,
                    'time_start' => new MongoDate((new Carbon("2024-07-15 13:00:00"))->getTimestamp()),
                    'time_finish' => new MongoDate((new Carbon("2024-07-15 14:00:00"))->getTimestamp()),
                ],
            )),
            $this->createBooking(array_merge(
                $this->getSharedBookingData(),
                [
                    '_id' => new MongoId('99999982869258e73a1ee223'),
                    'user_id' => '66727f8956a5e42aa5294836',
                    'event_id' => '66728bdd22ffb1d8a4dd06de',
                    'event_name' => 'Test Event',
                    'model_name' => 'Test Model',
                    'status' => 'BOOKED',
                    'type' => 'events',
                    'confirmed' => false,
                    'time_start' => new MongoDate((new Carbon("2024-07-16 15:30:00"))->getTimestamp()),
                    'time_finish' => new MongoDate((new Carbon("2024-07-16 16:30:00"))->getTimestamp()),
                ],
            )),
            $this->createBooking(array_merge(
                $this->getSharedBookingData(),
                [
                    '_id' => new MongoId('99999982869258e73a1ee224'),
                    'user_id' => '66727f8956a5e42aa5294836',
                    'event_id' => '66728bdd22ffb1d8a4dd06de',
                    'event_name' => 'Test Event',
                    'model_name' => 'Test Model',
                    'status' => 'BOOKED',
                    'type' => 'events',
                    'confirmed' => true,
                    'time_start' => new MongoDate((new Carbon("2024-07-11 15:30:00"))->getTimestamp()),
                    'time_finish' => new MongoDate((new Carbon("2024-07-11 16:30:00"))->getTimestamp()),
                ]
            )),
        ];
    }

    private function forFindFutureReservationByScheduleCode(): array
    {
        return [
            [
                '_id' => new MongoId('00a99a777788880000000001'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'schedule_code' => '00a99a7700000',
                'program_id' => '00a99a888887777000000000',
                'status' => BookingStatus::RESERVED,
                'time_start' => new MongoDate((new Carbon("2024-07-01 15:30:00.000", new \DateTimeZone('America/New_York')))->getTimestamp()),
                'time_finish' => new MongoDate((new Carbon("2024-07-01 16:30:00.000", new \DateTimeZone('America/New_York')))->getTimestamp()),
            ],
            [
                '_id' => new MongoId('00a99a777788880000000002'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'schedule_code' => '00a99a7700000',
                'program_id' => '00a99a888887777000000000',
                'status' => BookingStatus::RESERVED,
                'date_start' => new MongoDate((new Carbon("2024-07-01 15:31:00.000", new \DateTimeZone('America/New_York')))->getTimestamp()),
                'date_finish' => new MongoDate((new Carbon("2024-07-01 16:31:00.000", new \DateTimeZone('America/New_York')))->getTimestamp()),
            ],
            [
                '_id' => new MongoId('00a99a777788880000000003'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'schedule_code' => '00a99a7700000',
                'program_id' => '00a99a888887777000000000',
                'status' => BookingStatus::BOOKED,
                'time_start' => new MongoDate((new Carbon("2024-07-01 15:30:00.000", new \DateTimeZone('America/New_York')))->getTimestamp()),
                'time_finish' => new MongoDate((new Carbon("2024-07-01 16:30:00.000", new \DateTimeZone('America/New_York')))->getTimestamp()),
            ],
        ];
    }

    /**
     * @return array
     */
    private function forCountingAllWaitingBookings(): array
    {
        $eventId = '66c5d790bce106b30869e706';
        $eventId2 = '66c5d939b214df315d9a1a59';

        return [
            [
                '_id' => new MongoId('66c5d60f448d84de825bfd31'),
                'event_id' => $eventId,
                'guest_bookings' => 1,
                'status' => BookingStatus::WAITING,
            ],
            [
                '_id' => new MongoId('66c5d60f448d84de825bfd32'),
                'event_id' => $eventId,
                'guest_bookings' => 0,
                'status' => BookingStatus::BOOKED,
            ],
            [
                '_id' => new MongoId('66c5d60f448d84de825bfd33'),
                'event_id' => $eventId,
                'guest_bookings' => 0,
                'status' => BookingStatus::WAITING,
            ],
            [
                '_id' => new MongoId('66c5d60f448d84de825bfd34'),
                'event_id' => $eventId2,
                'guest_bookings' => 0,
                'status' => BookingStatus::BOOKED,
            ],
            [
                '_id' => new MongoId('66c5d60f448d84de825bfd35'),
                'event_id' => $eventId2,
                'guest_bookings' => 0,
                'status' => BookingStatus::BOOKED,
            ],
            [
                '_id' => new MongoId('66c5d60f448d84de825bfd36'),
                'event_id' => $eventId2,
                'guest_bookings' => 0,
                'status' => BookingStatus::CANCELED,
            ],
        ];
    }

    private function forCountAllBookingsIncludingGuests(): array
    {
        return [
            [
                '_id' => new MongoId('00a99a777788880000000004'),
                'event_id' => '00a99a444447777000000000',
                'guest_bookings' => 0,
                'status' => BookingStatus::RESERVED,
            ],
            [
                '_id' => new MongoId('00a99a777788880000000005'),
                'event_id' => '00a99a444447777000000000',
                'guest_bookings' => 2,
                'status' => BookingStatus::CANCELED,
            ],
            [
                '_id' => new MongoId('00a99a777788880000000006'),
                'event_id' => '00a99a444447777000000000',
                'guest_bookings' => 0,
                'status' => BookingStatus::BOOKED,
            ],
            [
                '_id' => new MongoId('00a99a777788880000000007'),
                'event_id' => '00a99a444447777000000000',
                'guest_bookings' => 0,
                'status' => BookingStatus::BOOKED,
            ],
            [
                '_id' => new MongoId('00a99a777788880000000008'),
                'event_id' => '00a99a444447777000000001',
                'guest_bookings' => 1,
                'status' => BookingStatus::RESERVED,
            ],
            [
                '_id' => new MongoId('00a99a777788880000000009'),
                'event_id' => '00a99a444447777000000001',
                'guest_bookings' => 1,
                'status' => BookingStatus::CANCELED,
            ],
            [
                '_id' => new MongoId('00a99a777788880000000010'),
                'event_id' => '00a99a444447777000000001',
                'guest_bookings' => 1,
                'status' => BookingStatus::BOOKED,
            ],
        ];
    }

    private function forCuntBookingsByMemberIdInPeriod(): array
    {
        $userId = '66c7551513b4c5fee04cc5d0';
        $shared = $this->getSharedBookingData();

        return [
            $this->createBooking(array_merge($shared, [
                '_id' => new MongoId('66c7578665234babdee6649c'),
                'user_id' => $userId,
                'event_id' => '66c75458b18bb1b4a3efd7d3',
                'time_start' => new MongoDate(Carbon::yesterday()->subDays(2)->setTime(12, 0)->getTimestamp()),
                'time_finish' => new MongoDate(Carbon::yesterday()->subDays(2)->setTime(13, 0)->getTimestamp()),
            ])),
            $this->createBooking(array_merge($shared, [
                '_id' => new MongoId('66c7577faf2693d29faa4a28'),
                'user_id' => $userId,
                'event_id' => '66c755ea480118c6ba0cedce',
                'time_start' => new MongoDate(Carbon::now()->setTime(11, 0)->getTimestamp()),
                'time_finish' => new MongoDate(Carbon::now()->setTime(12, 0)->getTimestamp()),
            ])),
            $this->createBooking(array_merge($shared, [
                '_id' => new MongoId('66c75777106c9dee0433a99a'),
                'user_id' => $userId,
                'event_id' => '66c754f55cb9d567cd7c6125',
                'time_start' => new MongoDate(Carbon::now()->setTime(14, 0)->getTimestamp()),
                'time_finish' => new MongoDate(Carbon::now()->setTime(15, 0)->getTimestamp()),
            ])),
            $this->createBooking(array_merge($shared, [
                '_id' => new MongoId('66c75770e639dc51607faab2'),
                'user_id' => $userId,
                'event_id' => '66c752fcb2106f6bbbc8bd11',
                'status' => 'WAITING',
                'time_start' => new MongoDate(Carbon::yesterday()->subDays(2)->setTime(12, 0)->getTimestamp()),
                'time_finish' => new MongoDate(Carbon::yesterday()->subDays(2)->setTime(13, 0)->getTimestamp()),
            ])),
            $this->createBooking(array_merge($shared, [
                '_id' => new MongoId('66c75760bc7dcc270a0efaf7'),
                'user_id' => $userId,
                'event_id' => '66c753a6d20f6634c5e82cc7',
                'status' => 'WAITING',
                'time_start' => new MongoDate(Carbon::yesterday()->subDays(2)->setTime(10, 0)->getTimestamp()),
                'time_finish' => new MongoDate(Carbon::yesterday()->subDays(2)->setTime(11, 0)->getTimestamp()),
            ])),
            $this->createBooking(array_merge($shared, [
                '_id' => new MongoId('66c757579ac80a82d037d5c9'),
                'user_id' => $userId,
                'event_id' => '66c753064148188307c84f87',
                'status' => 'CANCELED',
                'time_start' => new MongoDate(Carbon::yesterday()->subDays(3)->setTime(8, 0)->getTimestamp()),
                'time_finish' => new MongoDate(Carbon::yesterday()->subDays(3)->setTime(9, 0)->getTimestamp()),
            ])),
        ];
    }

    private function forGetByEventAndMemberId(): array
    {
        return [
            [
                '_id' => new MongoId('00a99a777788880000000011'),
                'event_id' => '00a99a444447777000000003',
                'user_id' => '00a99a333337777000000001',
            ],
            [
                '_id' => new MongoId('00a99a777788880000000012'),
                'event_id' => '00a99a444447777000000002',
                'user_id' => '00a99a333337777000000002',
            ],
            [
                '_id' => new MongoId('00a99a777788880000000013'),
                'event_id' => '00a99a444447777000000002',
                'user_id' => '00a99a333337777000000001',
            ],
        ];
    }

    private function forEventTotalBookingsCount(): array
    {
        $start = Carbon::yesterday()->setTime(8, 0);
        return [
            [
                '_id' => new MongoId('63e0cc0eec6f9c76435920d5'),
                'branch_id' => self::GLOFOX_BRANCH_ID,
                'namespace' => self::GLOFOX_NAMESPACE,
                'event_id' => '667289bfd03be8240fea6c19',
                'status' => BookingStatus::BOOKED,
                'program_id' => '63e0ca85ef7ce00f799d1c65',
                'type' => BookingType::EVENTS,
                'user_id' => '66727f8956a5e42aa5294836',
                'time_start' => new MongoDate($start->getTimestamp()),
                'time_finish' => new MongoDate($start->addHour()->getTimestamp()),
                'paid' => true,
                'guest_bookings' => 3,
                'payment_method' => 'cash',
                'attended' => false,
                'batch_id' => 'coreapi-batchcode',
            ],
            [
                '_id' => new MongoId('63e0cc0eec6f9c76435920d6'),
                'branch_id' => self::GLOFOX_BRANCH_ID,
                'namespace' => self::GLOFOX_NAMESPACE,
                'event_id' => '667289bfd03be8240fea6c20',
                'status' => BookingStatus::BOOKED,
                'program_id' => '63e0ca85ef7ce00f799d1c65',
                'type' => BookingType::EVENTS,
                'user_id' => '66727f8956a5e42aa5294836',
                'time_start' => new MongoDate($start->getTimestamp()),
                'time_finish' => new MongoDate($start->addHour()->getTimestamp()),
                'paid' => true,
                'guest_bookings' => 2,
                'payment_method' => 'cash',
                'attended' => true,
            ],
            [
                '_id' => new MongoId('63e0cc0eec6f9c76435920d7'),
                'branch_id' => self::GLOFOX_BRANCH_ID,
                'namespace' => self::GLOFOX_NAMESPACE,
                'event_id' => '667289bfd03be8240fea6c20',
                'status' => BookingStatus::CANCELED,
                'program_id' => '63e0ca85ef7ce00f799d1c65',
                'type' => BookingType::EVENTS,
                'user_id' => '66727f8956a5e42aa5294836',
                'time_start' => new MongoDate($start->getTimestamp()),
                'time_finish' => new MongoDate($start->addHour()->getTimestamp()),
                'paid' => true,
                'guest_bookings' => 3,
                'payment_method' => 'cash',
                'attended' => false,
            ],
            [
                '_id' => new MongoId('63e0cc0eec6f9c76435920d8'),
                'branch_id' => self::GLOFOX_BRANCH_ID,
                'namespace' => self::GLOFOX_NAMESPACE,
                'event_id' => '667289bfd03be8240fea6c21',
                'status' => BookingStatus::WAITING,
                'program_id' => '63e0ca85ef7ce00f799d1c65',
                'type' => BookingType::EVENTS,
                'user_id' => '66727f8956a5e42aa5294836',
                'time_start' => new MongoDate($start->getTimestamp()),
                'time_finish' => new MongoDate($start->addHour()->getTimestamp()),
                'paid' => true,
                'guest_bookings' => 1,
                'payment_method' => 'cash',
                'attended' => false,
            ],
            [
                '_id' => new MongoId('63e0cc0eec6f9c76435920d9'),
                'branch_id' => self::GLOFOX_BRANCH_ID,
                'namespace' => self::GLOFOX_NAMESPACE,
                'event_id' => '667289bfd03be8240fea6c21',
                'status' => BookingStatus::BOOKED,
                'program_id' => '63e0ca85ef7ce00f799d1c65',
                'type' => BookingType::EVENTS,
                'user_id' => '66727f8956a5e42aa5294836',
                'time_start' => new MongoDate($start->getTimestamp()),
                'time_finish' => new MongoDate($start->addHour()->getTimestamp()),
                'paid' => true,
                'guest_bookings' => 0,
                'payment_method' => 'cash',
                'attended' => false,
            ],
        ];
    }

    public function forEventDeletedEventHandle(): array
    {
        return [
            [
                '_id' => new MongoId('00a99a777788880000000014'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'event_id' => '66aa128eb9f5579999990000',
                'user_id' => '00a99a333337777000000001',
                'guest_bookings' => 2,
                'status' => BookingStatus::RESERVED,
                'time_start' => '2024-09-01 08:00:00',
                'time_finish' => '2024-09-01 09:00:00',
            ],
            [
                '_id' => new MongoId('00a99a777788880000000015'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'event_id' => '66aa128eb9f5579999990000',
                'user_id' => '00a99a333337777000000002',
                'guest_bookings' => 2,
                'status' => BookingStatus::CANCELED,
                'time_start' => '2024-09-01 08:00:00',
                'time_finish' => '2024-09-01 09:00:00',
            ],
            [
                '_id' => new MongoId('00a99a777788880000000016'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'event_id' => '66aa128eb9f5579999990000',
                'user_id' => '00a99a333337777000000003',
                'guest_bookings' => 1,
                'status' => BookingStatus::BOOKED,
                'time_start' => '2024-09-01 08:00:00',
                'time_finish' => '2024-09-01 09:00:00',
            ],
        ];
    }

    private function forRestrictedMembershipNoCreditsNextCycle(): array
    {
        return [
            $this->createBooking([
                '_id' => new MongoId('61a4ee412df7cc24846a5d02'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'event_id' => '997289bfd03be8240fea6c20',
                'user_id' => '59cd0e4b2a04b921fb568cc6',
                'time_start' => new MongoDate(Carbon::today()->addMonthsNoOverflow(1)->setTime(8, 0)->getTimestamp()),
                'time_finish' => new MongoDate(Carbon::today()->addMonthsNoOverflow(1)->setTime(9, 0)->getTimestamp()),
                'confirmed' => false,
                'batch_id' => 'coreapi-batchcode',
            ]),
        ];
    }

    private function forSemiPrivateAppointments(): array
    {
        $eventName = 'SemiPrivate Appointment I';
        $modelId = '64b8068173b491198db332bd';

        return [
            [
                '_id' => new MongoId('61a4ee412df7cc24846a5d03'),
                'branch_id' => static::GLOFOX_BRANCH_ID,
                'namespace' => static::GLOFOX_NAMESPACE,
                'status' => BookingStatus::BOOKED,
                'event_name' => $eventName,
                'type' => BookingType::TIME_SLOTS,
                'model' => ModelList::APPOINTMENTS,
                'model_id' => $modelId,
                'user_id' => static::VIRTUAL_SLOT_MEMBER_ID,
                'time_slot_id' => '651c331399573dced43b7db6',
                'time_start' => new MongoDate(strtotime('2024-09-18 10:00:00')),
                'time_finish' => new MongoDate(strtotime('2024-09-18 11:00:00')),
                'created' => new MongoDate(),
                'modified' => new MongoDate(),
            ],
            [
                '_id' => new MongoId('61a4ee412df7cc24846a5d04'),
                'branch_id' => static::GLOFOX_BRANCH_ID,
                'namespace' => static::GLOFOX_NAMESPACE,
                'status' => BookingStatus::BOOKED,
                'event_name' => 'SemiPrivate Appointment II',
                'type' => BookingType::TIME_SLOTS,
                'model' => ModelList::APPOINTMENTS,
                'model_id' => $modelId,
                'user_id' => static::VIRTUAL_SLOT_MEMBER_ID,
                'time_slot_id' => '651c331399573dced43b7db7',
                'time_start' => new MongoDate(strtotime('2024-09-18 10:00:00')),
                'time_finish' => new MongoDate(strtotime('2024-09-18 11:00:00')),
                'created' => new MongoDate(),
                'modified' => new MongoDate(),
            ],
            [
                '_id' => new MongoId('61a4ee412df7cc24846a5d05'),
                'branch_id' => static::GLOFOX_BRANCH_ID,
                'namespace' => static::GLOFOX_NAMESPACE,
                'status' => BookingStatus::BOOKED,
                'event_name' => 'SemiPrivate Appointment II',
                'type' => BookingType::TIME_SLOTS,
                'model' => ModelList::APPOINTMENTS,
                'model_id' => $modelId,
                'user_id' => '66727f8956a5e42aa5294836',
                'time_slot_id' => '651c331399573dced43b7db7',
                'time_start' => new MongoDate(strtotime('2024-09-18 10:00:00')),
                'time_finish' => new MongoDate(strtotime('2024-09-18 11:00:00')),
                'created' => new MongoDate(),
                'modified' => new MongoDate(),
            ],
            [
                '_id' => new MongoId('66f269f5e3176188a19f6138'),
                'branch_id' => static::GLOFOX_BRANCH_ID,
                'namespace' => static::GLOFOX_NAMESPACE,
                'status' => BookingStatus::BOOKED,
                'event_name' => $eventName,
                'type' => BookingType::TIME_SLOTS,
                'model' => ModelList::APPOINTMENTS,
                'model_id' => $modelId,
                'user_id' => static::VIRTUAL_SLOT_MEMBER_ID,
                'time_slot_id' => '66f2689e13eae867e90e1b47',
                'time_start' => new MongoDate(strtotime('2024-11-18 10:00:00')),
                'time_finish' => new MongoDate(strtotime('2024-11-18 11:00:00')),
                'created' => new MongoDate(),
                'modified' => new MongoDate(),
            ],
            [
                '_id' => new MongoId('66f26a697a4d7980669b5db7'),
                'branch_id' => static::GLOFOX_BRANCH_ID,
                'namespace' => static::GLOFOX_NAMESPACE,
                'status' => BookingStatus::BOOKED,
                'event_name' => $eventName,
                'type' => BookingType::TIME_SLOTS,
                'model' => ModelList::APPOINTMENTS,
                'model_id' => $modelId,
                'user_id' => static::VIRTUAL_SLOT_MEMBER_ID,
                'time_slot_id' => '66f268c8dfbb0c616687881c',
                'time_start' => new MongoDate(strtotime('2024-11-25 10:00:00')),
                'time_finish' => new MongoDate(strtotime('2024-11-25 11:00:00')),
                'created' => new MongoDate(),
                'modified' => new MongoDate(),
            ],
            [
                '_id' => new MongoId('66f26a77494cf207ae44835a'),
                'branch_id' => static::GLOFOX_BRANCH_ID,
                'namespace' => static::GLOFOX_NAMESPACE,
                'status' => BookingStatus::BOOKED,
                'event_name' => $eventName,
                'type' => BookingType::TIME_SLOTS,
                'model' => ModelList::APPOINTMENTS,
                'model_id' => $modelId,
                'user_id' => static::VIRTUAL_SLOT_MEMBER_ID,
                'time_slot_id' => '66f268c8dfbb0c616687881c',
                'time_start' => new MongoDate(strtotime('2024-11-25 10:00:00')),
                'time_finish' => new MongoDate(strtotime('2024-11-25 11:00:00')),
                'created' => new MongoDate(),
                'modified' => new MongoDate(),
            ],
            [
                '_id' => new MongoId('66f3dd466cf72ba3166f7a6f'),
                'branch_id' => static::GLOFOX_BRANCH_ID,
                'namespace' => static::GLOFOX_NAMESPACE,
                'status' => BookingStatus::BOOKED,
                'event_name' => $eventName,
                'type' => BookingType::TIME_SLOTS,
                'model' => ModelList::APPOINTMENTS,
                'model_id' => $modelId,
                'user_id' => static::VIRTUAL_SLOT_MEMBER_ID,
                'time_slot_id' => '66f3dd78db2b934d22e69079',
                'time_start' => new MongoDate(strtotime('2024-11-30 10:00:00')),
                'time_finish' => new MongoDate(strtotime('2024-11-30 11:00:00')),
                'created' => new MongoDate(),
                'modified' => new MongoDate(),
            ],
        ];
    }

    public function forFailureReason(): array
    {
        return [
            [
                '_id' => new MongoId('59a7010a05d377b3a917712c'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'event_id' => '66aa128eb9f5579999990900',
                'program_id' => '13b7411a15c676bda824611b',
                'status' => 'FAILED',
                'type' => 'events',
                'user_name' => 'Test User',
                'model_name' => 'Test',
                'user_id' => '59a7010a05c677b3a916613c',
                'time_start' => date('Y-m-d H:m:i', strtotime('+1 hour')),
                'time_finish' => date('Y-m-d H:m:i', strtotime('+2 hour')),
                'paid' => false,
                'attended' => false,
                'date_start' => date('Y-m-d', strtotime('+1 hour')),
                'date_finish' => date('Y-m-d', strtotime('+2 hour')),
                'is_from_waiting_list' => null,
                'guest_bookings' => 0,
                'session_id' => null,
                'payment_method' => 'cash',
                'failure_reason' => 'Something is wrong here',
            ],
        ];
    }

    public function forHardDelete(): array
    {
        return [
            [
                '_id' => new MongoId('66f3c3d684825c6f49299126'),
                'branch_id' => '49a7011a05c677b9a916612a',
                'namespace' => 'glofox',
                'event_id' => '66aa128eb9f5579999990900',
                'program_id' => '63e0ca85ef7ce00f799d1c65',
                'schedule_code' => '5856c03455ff0',
                'status' => BookingStatus::RESERVED,
                'type' => 'events',
                'user_name' => 'Test User',
                'model_name' => 'Test',
                'user_id' => '59a7010a05c677b3a916613c',
                'time_start' => date('Y-m-d H:m:i', strtotime('+1 hour')),
                'time_finish' => date('Y-m-d H:m:i', strtotime('+2 hour')),
                'paid' => false,
                'attended' => false,
                'date_start' => date('Y-m-d', strtotime('+1 hour')),
                'date_finish' => date('Y-m-d', strtotime('+2 hour')),
                'is_from_waiting_list' => null,
                'guest_bookings' => 0,
                'session_id' => null,
                'payment_method' => 'cash',
            ],
        ];
    }

    public function forUpdateIsFirst(): array
    {
        return [
            [
                '_id' => new MongoId('78a7009a05d377b3a918812c'),
                'branch_id' => static::GLOFOX_BRANCH_ID,
                'namespace' => static::GLOFOX_NAMESPACE,
                'event_id' => '66aa128eb9f5579999990900',
                'program_id' => '13b7411a15c676bda824611b',
                'status' => BookingStatus::BOOKED,
                'type' => 'events',
                'user_name' => 'Test User',
                'model_name' => 'Test',
                'user_id' => '59a7010a05c677b3a916613c',
                'time_start' => date('Y-m-d H:m:i', strtotime('+1 hour')),
                'time_finish' => date('Y-m-d H:m:i', strtotime('+2 hour')),
                'paid' => true,
                'attended' => false,
                'date_start' => date('Y-m-d', strtotime('+1 hour')),
                'date_finish' => date('Y-m-d', strtotime('+2 hour')),
                'modified' => date('Y-m-d', strtotime('-1 month')),
                'is_from_waiting_list' => null,
                'guest_bookings' => 0,
                'session_id' => null,
                'payment_method' => 'cash',
                'is_first' => false,
            ],
        ];
    }

    public function forUpdateStatus(): array
    {
        return [
            [
                '_id' => new MongoId('78a7009a05d377b3a918812d'),
                'branch_id' => static::GLOFOX_BRANCH_ID,
                'namespace' => static::GLOFOX_NAMESPACE,
                'event_id' => '66aa128eb9f5579999990900',
                'program_id' => '13b7411a15c676bda824611b',
                'status' => BookingStatus::WAITING,
                'type' => 'events',
                'user_name' => 'Test User',
                'model_name' => 'Test',
                'user_id' => '59a7010a05c677b3a916613c',
                'time_start' => date('Y-m-d H:m:i', strtotime('+1 hour')),
                'time_finish' => date('Y-m-d H:m:i', strtotime('+2 hour')),
                'paid' => true,
                'attended' => false,
                'date_start' => date('Y-m-d', strtotime('+1 hour')),
                'date_finish' => date('Y-m-d', strtotime('+2 hour')),
                'modified' => date('Y-m-d', strtotime('-1 month')),
                'is_from_waiting_list' => null,
                'guest_bookings' => 0,
                'session_id' => null,
                'payment_method' => 'cash',
            ],
        ];
    }

    public function forUpdateReservationStatus(): array
    {
        return [
            [
                '_id' => new MongoId('78a7009a05d377b3a918813e'),
                'branch_id' => static::GLOFOX_BRANCH_ID,
                'namespace' => static::GLOFOX_NAMESPACE,
                'event_id' => '66aa128eb9f5579999990900',
                'program_id' => '13b7411a15c676bda824611b',
                'status' => BookingStatus::WAITING,
                'type' => 'events',
                'user_name' => 'Test User',
                'model_name' => 'Test',
                'user_id' => '59a7010a05c677b3a916613c',
                'time_start' => date('Y-m-d H:m:i', strtotime('+1 hour')),
                'time_finish' => date('Y-m-d H:m:i', strtotime('+2 hour')),
                'paid' => true,
                'attended' => false,
                'date_start' => date('Y-m-d', strtotime('+1 hour')),
                'date_finish' => date('Y-m-d', strtotime('+2 hour')),
                'modified' => date('Y-m-d', strtotime('-1 month')),
                'is_from_waiting_list' => null,
                'guest_bookings' => 0,
                'session_id' => null,
                'payment_method' => 'cash',
                'confirmed' => true,
            ],
        ];
    }

    public function forUpdateEventId(): array
    {
        return [
            [
                '_id' => new MongoId('78a7009a05d377b3a918814c'),
                'branch_id' => static::GLOFOX_BRANCH_ID,
                'namespace' => static::GLOFOX_NAMESPACE,
                'event_id' => '66aa128eb9f5579999990900',
                'program_id' => '13b7411a15c676bda824611b',
                'status' => BookingStatus::WAITING,
                'type' => 'events',
                'user_name' => 'Test User',
                'model_name' => 'Test',
                'user_id' => '59a7010a05c677b3a916613c',
                'time_start' => date('Y-m-d H:m:i', strtotime('+1 hour')),
                'time_finish' => date('Y-m-d H:m:i', strtotime('+2 hour')),
                'paid' => true,
                'attended' => false,
                'date_start' => date('Y-m-d', strtotime('+1 hour')),
                'date_finish' => date('Y-m-d', strtotime('+2 hour')),
                'modified' => date('Y-m-d', strtotime('-1 month')),
                'is_from_waiting_list' => null,
                'guest_bookings' => 0,
                'session_id' => null,
                'payment_method' => 'cash',
                'confirmed' => true,
            ],
        ];
    }

    public function forCancellingInAdvanceShell(): array
    {
        return [
            $this->createBooking([
                '_id' => new MongoId('6728b420dcf776ea3d5a48c1'),
                'branch_id' => '5e9ed069e0a7da003254711d',
                'namespace' => 'glofox',
                'event_id' => '997289bfd03be8240fea6c20',
                'user_id' => '59cd0e4b2a04b921fb568cc6',
                'status' => BookingStatus::BOOKED,
                'time_start' => new MongoDate(Carbon::parse('2024-11-05 08:00:00')->getTimestamp()),
                'time_finish' => new MongoDate(Carbon::parse('2024-11-05 09:00:00')->getTimestamp()),
                'confirmed' => true,
                'batch_id' => 'coreapi-668e8c50e4e8c63831043',
            ]),
            $this->createBooking([
                '_id' => new MongoId('6728b4e68f5ec84d7bdcb3e5'),
                'branch_id' => '5e9ed069e0a7da003254711d',
                'namespace' => 'glofox',
                'event_id' => '997289bfd03be8240fea6c20',
                'user_id' => '59cd0e4b2a04b921fb568cc6',
                'status' => BookingStatus::RESERVED,
                'time_start' => new MongoDate(Carbon::parse('2024-11-06 10:00:00')->getTimestamp()),
                'time_finish' => new MongoDate(Carbon::parse('2024-11-06 11:00:00')->getTimestamp()),
                'confirmed' => true,
                'batch_id' => 'coreapi-668e8c50e4e8c63831043',
            ]),
            $this->createBooking([
                '_id' => new MongoId('6728b53beafc12bd2e9e8843'),
                'branch_id' => '5e9ed069e0a7da003254711d',
                'namespace' => 'glofox',
                'event_id' => '997289bfd03be8240fea6c20',
                'user_id' => '59cd0e4b2a04b921fb568cc6',
                'status' => BookingStatus::WAITING,
                'time_start' => new MongoDate(Carbon::parse('2024-11-30 15:00:00')->getTimestamp()),
                'time_finish' => new MongoDate(Carbon::parse('2024-11-30 16:00:00')->getTimestamp()),
                'confirmed' => true,
            ]),
        ];
    }

    private function forEventsUpdate(): array
    {
        return [
            [
                '_id' => new MongoId('6742656e8c64c71bb62d8571'),
                'branch_id' => '67425b982a70f7afae14dc75',
                'namespace' => 'events-update-plus-utc',
                'event_id' => '6742631de09448fa155f9e18',
                'program_id' => '674261b978cfad5769eadf3a',
                'schedule_code' => '5866c03455fs4',
                'status' => BookingStatus::BOOKED,
                'type' => 'events',
                'user_id' => self::GLOFOX_USER_ID_4,
                'time_start' => date('Y-m-d H:m:i', strtotime('2024-12-11T08:00:00+00:00')),
                'time_finish' => date('Y-m-d H:m:i', strtotime('2024-12-11T09:00:00+00:00')),
                'paid' => false,
                'attended' => false,
                'guest_bookings' => 0,
                'payment_method' => 'cash',
            ],
            [
                '_id' => new MongoId('674265741e725c2b8be0e4c7'),
                'branch_id' => '67425ec9a14b1b40651b4990',
                'namespace' => 'events-update-plus-utc',
                'event_id' => '67426323a338619555c318a8',
                'program_id' => '674261be71678f5e860b3eee',
                'schedule_code' => '5833c03665fs4',
                'status' => BookingStatus::BOOKED,
                'type' => 'events',
                'user_id' => self::GLOFOX_USER_ID_3,
                'time_start' => date('Y-m-d H:m:i', strtotime('2024-12-18T08:00:00+00:00')),
                'time_finish' => date('Y-m-d H:m:i', strtotime('2024-12-18T09:00:00+00:00')),
                'paid' => false,
                'attended' => false,
                'guest_bookings' => 0,
                'payment_method' => 'cash',
            ],
            [
                '_id' => new MongoId('674265782486ffdbffd4bdef'),
                'branch_id' => '67425ec9a14b1b40651b4990',
                'namespace' => 'events-update-plus-utc',
                'event_id' => '67426323a338619555c318a8',
                'program_id' => '674261be71678f5e860b3eee',
                'schedule_code' => '5833c03665fs4',
                'status' => BookingStatus::BOOKED,
                'type' => 'events',
                'user_id' => self::GLOFOX_USER_ID_2,
                'time_start' => date('Y-m-d H:m:i', strtotime('2024-12-18T08:00:00+00:00')),
                'time_finish' => date('Y-m-d H:m:i', strtotime('2024-12-18T09:00:00+00:00')),
                'paid' => false,
                'attended' => false,
                'guest_bookings' => 0,
                'payment_method' => 'cash',
              ]
          ];
    }
  
    public function forGetAllBookings(): array
    {
        $branchId = '6745f7abd22f105c2aac5693';
        $namespace = 'get-all-bookings';
        $userId = '6748276aa77c5601e7052a89';

        return [
            [
                '_id' => new MongoId('6746d5e537f76f2fb724ebb4'),
                'branch_id' => $branchId,
                'namespace' => $namespace,
                'type' => 'events',
                'status' => 'BOOKED',
                'user_id' => $userId,
                'user_name' => 'Test User',
                'program_id' => '6746d92dc2dd88f467a2d70d',
                'schedule_code' => 'test-schedule-code',
                'event_id' => '6746d87f49112d10e4dfcb22',
                'event_name' => 'Test event',
                'model_name' => 'Test event',
                'model' => 'events',
                'model_id' => '6746d889e1dd84572df497a6',
                'membership_name' => 'Test membership',
                'plan_name' => 'Test plan name',
                'plan_code' => 'Test plan code',
                'confirmed' => true,
                'guest_bookings' => 0,
                'batch_id' => 'test-batch-id',
                'origin' => 'classpass',
                'metadata' => [
                    'service' => [
                        'type' => 'credit',
                        'id' => 'test-credit-id'
                    ]
                ],
                'time_start' => new MongoDate(Carbon::parse('2024-11-28 17:00:00')->getTimestamp()),
                'time_finish' => new MongoDate(Carbon::parse('2024-11-28 18:00:00')->getTimestamp()),
                'created' => new MongoDate(Carbon::parse('2024-11-26 15:00:00')->getTimestamp()),
            ]
        ];
    }

    public function forProgramsUpdateEventGenerator(): array
    {
        $branch_id = '49a7011a05c677b9a916612a';
        $program_id = '677d35c0ca57fd9d73e9dbb8';
        $event_id_1 = '677d3af823fc3d1b3b2ec151';
        $event_id_2 = '677d3b5ada40a40fc53eccf6';
        $schedule_code = '5211b01775et1';
        $type = 'events';
        return [
            [
                '_id' => new MongoId('677d3f38ec022c78ccb5c832'),
                'branch_id' => $branch_id,
                'namespace' => self::GLOFOX_NAMESPACE,
                'event_id' => $event_id_1,
                'program_id' => $program_id,
                'schedule_code' => $schedule_code,
                'status' => BookingStatus::CANCELED,
                'type' => $type,
                'user_id' => self::GLOFOX_USER_ID_4,
                'time_start' => date('Y-m-d H:m:i', strtotime('2024-01-30T12:00:00+00:00')),
                'time_finish' => date('Y-m-d H:m:i', strtotime('2024-01-30T13:00:00+00:00')),
                'paid' => false,
                'attended' => false,
                'guest_bookings' => 0,
                'payment_method' => 'cash',
            ],
            [
                '_id' => new MongoId('677d3f469fa904b83eebdd14'),
                'branch_id' => $branch_id,
                'namespace' => self::GLOFOX_NAMESPACE,
                'event_id' => $event_id_1,
                'program_id' => $program_id,
                'schedule_code' => $schedule_code,
                'status' => BookingStatus::CANCELED,
                'type' => $type,
                'user_id' => self::GLOFOX_USER_ID_2,
                'time_start' => date('Y-m-d H:m:i', strtotime('2024-01-30T012:00:00+00:00')),
                'time_finish' => date('Y-m-d H:m:i', strtotime('2024-01-30T013:00:00+00:00')),
                'paid' => false,
                'attended' => false,
                'guest_bookings' => 0,
                'payment_method' => 'cash',
            ],
            [
                '_id' => new MongoId('677d3f4c4c6ad8e46c865192'),
                'branch_id' => $branch_id,
                'namespace' => self::GLOFOX_NAMESPACE,
                'event_id' => $event_id_1,
                'program_id' => $program_id,
                'schedule_code' => $schedule_code,
                'status' => BookingStatus::WAITING,
                'type' => $type,
                'user_id' => self::GLOFOX_USER_ID_3,
                'time_start' => date('Y-m-d H:m:i', strtotime('2024-01-30T12:00:00+00:00')),
                'time_finish' => date('Y-m-d H:m:i', strtotime('2024-01-30T13:00:00+00:00')),
                'paid' => false,
                'attended' => false,
                'guest_bookings' => 0,
                'payment_method' => 'cash',
            ],
            [
                '_id' => new MongoId('677d3cabb96ad6080947d097'),
                'branch_id' => $branch_id,
                'namespace' => self::GLOFOX_NAMESPACE,
                'event_id' => $event_id_2,
                'program_id' => $program_id,
                'schedule_code' => $schedule_code,
                'status' => BookingStatus::CANCELED,
                'type' => $type,
                'user_id' => self::GLOFOX_USER_ID_4,
                'time_start' => date('Y-m-d H:m:i', strtotime('2024-02-06T12:00:00+00:00')),
                'time_finish' => date('Y-m-d H:m:i', strtotime('2024-02-06T13:00:00+00:00')),
                'paid' => false,
                'attended' => false,
                'guest_bookings' => 0,
                'payment_method' => 'cash',
            ],
            [
                '_id' => new MongoId('677d3cb3e468125f40516735'),
                'branch_id' => $branch_id,
                'namespace' => self::GLOFOX_NAMESPACE,
                'event_id' => $event_id_2,
                'program_id' => $program_id,
                'schedule_code' => $schedule_code,
                'status' => BookingStatus::CANCELED,
                'type' => $type,
                'user_id' => self::GLOFOX_USER_ID_2,
                'time_start' => date('Y-m-d H:m:i', strtotime('2024-02-06T012:00:00+00:00')),
                'time_finish' => date('Y-m-d H:m:i', strtotime('2024-02-06T013:00:00+00:00')),
                'paid' => false,
                'attended' => false,
                'guest_bookings' => 0,
                'payment_method' => 'cash',
            ],
            [
                '_id' => new MongoId('677d3cbc0856d23597d8b6c3'),
                'branch_id' => $branch_id,
                'namespace' => self::GLOFOX_NAMESPACE,
                'event_id' => $event_id_2,
                'program_id' => $program_id,
                'schedule_code' => $schedule_code,
                'status' => BookingStatus::WAITING,
                'type' => $type,
                'user_id' => self::GLOFOX_USER_ID_3,
                'time_start' => date('Y-m-d H:m:i', strtotime('2024-02-06T12:00:00+00:00')),
                'time_finish' => date('Y-m-d H:m:i', strtotime('2024-02-06T13:00:00+00:00')),
                'paid' => false,
                'attended' => false,
                'guest_bookings' => 0,
                'payment_method' => 'cash',
            ]
        ];
    }

    public function forUpdateDatesOnCancel(): array
    {
        return [
            [
                '_id' => new MongoId('78a7999a05d377b3a918814c'),
                'branch_id' => '99984ace2c8a824784ab4cc4',
                'namespace' => static::GLOFOX_NAMESPACE,
                'event_id' => '66aa128eb9f5579999990900',
                'program_id' => '13b7411a15c676bda824611b',
                'status' => BookingStatus::BOOKED,
                'type' => 'events',
                'user_name' => 'Test User',
                'model_name' => 'Test',
                'user_id' => '77e1b5a8b5bda44ec0c9c0e2',
                'time_start' => Carbon::parse('2025-01-31 12:00:00')->toDateTimeString(),
                'time_finish' => Carbon::parse('2025-02-01 12:00:00')->toDateTimeString(),
                'paid' => true,
                'attended' => false,
                'date_start' => Carbon::parse('2025-01-31 12:00:00')->toDateTimeString(),
                'date_finish' => Carbon::parse('2025-02-01 12:00:00')->toDateTimeString(),
                'modified' => date('Y-m-d', strtotime('-1 month')),
                'is_from_waiting_list' => null,
                'guest_bookings' => 0,
                'session_id' => null,
                'payment_method' => 'cash',
                'confirmed' => true,
            ],
        ];
    }

    public function forWaitingListEmailOnRoaming(): array
    {
        return [
            $this->createBooking([
                '_id' => new MongoId('677d3cbc0856d23597d8b6c4'),
                'branch_id' => '49a7011a05c677b9a916612b',
                'namespace' => 'glofox',
                'event_id' => '6780dd7791140cc446a52e70',
                'user_id' => '675acdc70b53b54da1c0c660',
                'status' => BookingStatus::BOOKED,
                'time_start' => new MongoDate(Carbon::parse('2024-11-05 08:00:00')->getTimestamp()),
                'time_finish' => new MongoDate(Carbon::parse('2024-11-05 09:00:00')->getTimestamp()),
                'confirmed' => true,
            ]),
            $this->createBooking([
                '_id' => new MongoId('677d3cbc0856d23597d8b6c5'),
                'branch_id' => '49a7011a05c677b9a916612b',
                'namespace' => 'glofox',
                'event_id' => '6780dd7791140cc446a52e70',
                'user_id' => '675acdc70b53b54da1c0c660',
                'status' => BookingStatus::WAITING,
                'time_start' => new MongoDate(Carbon::parse('2024-11-05 08:00:00')->getTimestamp()),
                'time_finish' => new MongoDate(Carbon::parse('2024-11-05 09:00:00')->getTimestamp()),
                'confirmed' => false,
            ]),
        ];
    }

    public function forAnalyticsClassBookings(): array
    {
        $branchId = '5d794d5c7efab3813a2dfe58';
        $namespace = 'namespace-for-analytics-reports';

        return [
            $this->createBooking([
                '_id' => new MongoId('67a39a92cf3c57abfa45ee8e'),
                'branch_id' => $branchId,
                'namespace' => $namespace,
                'event_id' => '6780dd7791140cc446a52e70',
                'status' => BookingStatus::BOOKED,
                'time_start' => new MongoDate(Carbon::parse('2024-12-31 08:00:00')->getTimestamp()),
                'time_finish' => new MongoDate(Carbon::parse('2024-12-31 09:00:00')->getTimestamp()),
            ]),
            $this->createBooking([
                '_id' => new MongoId('67a39b582eba4e79b7f21b03'),
                'branch_id' => $branchId,
                'namespace' => $namespace,
                'event_id' => '6780dd7791140cc446a52e70',
                'status' => BookingStatus::BOOKED,
                'time_start' => new MongoDate(Carbon::parse('2024-12-28 08:00:00')->getTimestamp()),
                'time_finish' => new MongoDate(Carbon::parse('2024-12-28 09:00:00')->getTimestamp()),
            ]),
            $this->createBooking([
                '_id' => new MongoId('67a39b91752545127d1832c0'),
                'branch_id' => $branchId,
                'namespace' => $namespace,
                'event_id' => '6780dd7791140cc446a52e70',
                'status' => BookingStatus::BOOKED,
                'time_start' => new MongoDate(Carbon::parse('2024-12-10 08:00:00')->getTimestamp()),
                'time_finish' => new MongoDate(Carbon::parse('2024-12-10 09:00:00')->getTimestamp()),
            ]),
            $this->createBooking([
                '_id' => new MongoId('67a39ba4e382be8238bfd457'),
                'branch_id' => $branchId,
                'namespace' => $namespace,
                'event_id' => '6780dd7791140cc446a52e70',
                'status' => BookingStatus::BOOKED,
                'time_start' => new MongoDate(Carbon::parse('2024-10-10 08:00:00')->getTimestamp()),
                'time_finish' => new MongoDate(Carbon::parse('2024-10-10 09:00:00')->getTimestamp()),
            ]),
        ];
    }

    private function forProgramUpsertService(): array
    {
        $branchId = '49a7011a05c677b9a916612b';
        $eventId1 = '6780dd7791140cc446a52e71';
        $eventId2 = '6780dd7791140cc446a52e72';
        $userId = '675acdc70b53b54da1c0c660';

        return [
            $this->createBooking([
                '_id' => new MongoId('677d3cbc0856d23597d8b6c6'),
                'branch_id' => $branchId,
                'namespace' => 'glofox',
                'event_id' => $eventId1,
                'user_id' => $userId,
                'status' => BookingStatus::BOOKED,
                'time_start' => new MongoDate(Carbon::parse('2024-01-22 08:15:00')->getTimestamp()),
                'time_finish' => new MongoDate(Carbon::parse('2024-01-22 09:15:00')->getTimestamp()),
                'confirmed' => true,
            ]),
            $this->createBooking([
                '_id' => new MongoId('677d3cbc0856d23597d8b6c7'),
                'branch_id' => $branchId,
                'namespace' => 'glofox',
                'event_id' => $eventId2,
                'user_id' => $userId,
                'status' => BookingStatus::BOOKED,
                'time_start' => new MongoDate(Carbon::parse('2024-01-24 08:15:00')->getTimestamp()),
                'time_finish' => new MongoDate(Carbon::parse('2024-01-24 09:15:00')->getTimestamp()),
                'confirmed' => true,
            ]),
        ];
    }

    private function forSkippingMembershipLockedBookingFailures(): array
    {
        $start = Carbon::tomorrow()->setTime(8, 0);
        return [
            [
                '_id' => new MongoId('63e0cc0eec6f9c76435920b5'),
                'branch_id' => self::GLOFOX_BRANCH_ID,
                'namespace' => self::GLOFOX_NAMESPACE,
                'event_id' => '6780dd7791140cc446a52e81',
                'status' => BookingStatus::BOOKED,
                'confirmed' => false,
                'program_id' => '6672862317c4f80ebdb0e178',
                'type' => BookingType::EVENTS,
                'user_id' => '77e1b5a8b5bda44ec0c9c0e3',
                'time_start' => new MongoDate(Carbon::parse('2024-11-05 08:00:00')->getTimestamp()),
                'time_finish' => new MongoDate(Carbon::parse('2024-11-05 09:00:00')->getTimestamp()),
                'paid' => false,
                'guest_bookings' => 0,
                'payment_method' => 'cash',
                'attended' => false,
                'batch_id' => 'coreapi-668e8c50e4e8c63831041',
            ],
            [
                '_id' => new MongoId('63e0cc0eec6f9c76435920f1'),
                'branch_id' => self::GLOFOX_BRANCH_ID,
                'namespace' => self::GLOFOX_NAMESPACE,
                'event_id' => '6780dd7791140cc446a52e81',
                'status' => BookingStatus::BOOKED,
                'confirmed' => false,
                'program_id' => '6672862317c4f80ebdb0e178',
                'type' => BookingType::EVENTS,
                'user_id' => '77c2e5a8b5bda44ec0c9c0f1',
                'time_start' => new MongoDate(Carbon::parse('2024-11-05 08:00:00')->getTimestamp()),
                'time_finish' => new MongoDate(Carbon::parse('2024-11-05 09:00:00')->getTimestamp()),
                'paid' => false,
                'guest_bookings' => 0,
                'payment_method' => 'cash',
                'attended' => false,
                'batch_id' => 'coreapi-668e8c50e4e8c63831041',
            ],
            [
                '_id' => new MongoId('67c6e128ef7bcae249e0adec'),
                'branch_id' => self::GLOFOX_BRANCH_ID,
                'namespace' => self::GLOFOX_NAMESPACE,
                'event_id' => '67c6de478b2e0eee4851d77b',
                'status' => BookingStatus::RESERVED,
                'type' => BookingType::EVENTS,
                'user_id' => '77c2e5a8b5bda44ec0c9c0f1',
                'time_start' => new MongoDate(Carbon::parse('2024-11-06 08:00:00')->getTimestamp()),
                'time_finish' => new MongoDate(Carbon::parse('2024-11-06 09:00:00')->getTimestamp()),
                'paid' => true,
                'guest_bookings' => 0,
                'payment_method' => 'cash',
                'attended' => false,
                'batch_id' => 'coreapi-668e8c50e4e8c63831041',
            ],
            [
                '_id' => new MongoId('67c6e1ddd380406a2bb4e6aa'),
                'branch_id' => self::GLOFOX_BRANCH_ID,
                'namespace' => self::GLOFOX_NAMESPACE,
                'event_id' => '67c6de50d619c7de12a7ecaa',
                'status' => BookingStatus::BOOKED,
                'confirmed' => false,
                'type' => BookingType::EVENTS,
                'user_id' => '77c2e5a8b5bda44ec0c9c0f1',
                'time_start' => new MongoDate(Carbon::parse('2024-11-06 08:00:00')->getTimestamp()),
                'time_finish' => new MongoDate(Carbon::parse('2024-11-06 09:00:00')->getTimestamp()),
                'paid' => false,
                'guest_bookings' => 0,
                'payment_method' => 'cash',
                'attended' => false,
                'batch_id' => 'coreapi-668e8c50e4e8c63831041',
            ],
            [
                '_id' => new MongoId('67c70401b557ad1b75746ecb'),
                'branch_id' => self::GLOFOX_BRANCH_ID,
                'namespace' => self::GLOFOX_NAMESPACE,
                'event_id' => '67c6de50d619c7de12a7ecaa',
                'status' => BookingStatus::BOOKED,
                'confirmed' => true,
                'type' => BookingType::EVENTS,
                'user_id' => '77c2e5a8b5bda44ec0c9c0f1',
                'time_start' => new MongoDate(Carbon::parse('2024-11-06 08:00:00')->getTimestamp()),
                'time_finish' => new MongoDate(Carbon::parse('2024-11-06 09:00:00')->getTimestamp()),
                'paid' => false,
                'guest_bookings' => 0,
                'payment_method' => 'cash',
                'attended' => false,
                'batch_id' => 'coreapi-668e8c50e4e8c63831041',
            ],
            [
                '_id' => new MongoId('67c81877534028c019c2c70f'),
                'branch_id' => self::GLOFOX_BRANCH_ID,
                'namespace' => self::GLOFOX_NAMESPACE,
                'event_id' => '67c6de50d619c7de12a7ecaa',
                'status' => BookingStatus::RESERVED,
                'confirmed' => false,
                'type' => BookingType::EVENTS,
                'user_id' => '77e1b5a8b5bda44ec0c9c0e3',
                'time_start' => new MongoDate(Carbon::parse('2024-11-06 08:00:00')->getTimestamp()),
                'time_finish' => new MongoDate(Carbon::parse('2024-11-06 09:00:00')->getTimestamp()),
                'paid' => false,
                'guest_bookings' => 0,
                'payment_method' => 'cash',
                'attended' => false,
                'batch_id' => 'coreapi-668e8c50e4e8c63831041',
            ],
            [
                '_id' => new MongoId('67c81867829530eab56388d9'),
                'branch_id' => self::GLOFOX_BRANCH_ID,
                'namespace' => self::GLOFOX_NAMESPACE,
                'event_id' => '67c6de50d619c7de12a7ecaa',
                'status' => BookingStatus::BOOKED,
                'confirmed' => false,
                'type' => BookingType::EVENTS,
                'user_id' => '77e1b5a8b5bda44ec0c9c0e3',
                'time_start' => new MongoDate(Carbon::parse('2024-11-06 08:00:00')->getTimestamp()),
                'time_finish' => new MongoDate(Carbon::parse('2024-11-06 09:00:00')->getTimestamp()),
                'paid' => false,
                'guest_bookings' => 0,
                'payment_method' => 'cash',
                'attended' => false,
                'batch_id' => 'coreapi-668e8c50e4e8c63831041',
            ],
            [
                '_id' => new MongoId('67c7063278a93a338f831859'),
                'branch_id' => self::GLOFOX_BRANCH_ID,
                'namespace' => self::GLOFOX_NAMESPACE,
                'event_id' => '67c6de50d619c7de12a7ecaa',
                'status' => BookingStatus::BOOKED,
                'confirmed' => true,
                'type' => BookingType::EVENTS,
                'user_id' => '77c2e5a8b5bda44ec0c9c0f1',
                'time_start' => new MongoDate(Carbon::parse('2024-11-13 08:00:00')->getTimestamp()),
                'time_finish' => new MongoDate(Carbon::parse('2024-11-13 09:00:00')->getTimestamp()),
                'paid' => false,
                'guest_bookings' => 0,
                'payment_method' => 'cash',
                'attended' => false,
                'batch_id' => 'coreapi-668e8c50e4e8c63831041',
            ],
        ];
    }

    private function forNonDeleteEventsWithCanceledBookings(): array
    {
        $programId = '67e508db1f2ea29d1b38cbc0';
        $userId1 = '77e1b5a8b5bda44ec0c9c0e3';
        $userId2 = '77e1b5a8b5bda44ec0c9c0e4';
        $userId3 = '77e1b5a8b5bda44ec0c9c0e5';
        return [
            [
                '_id' => new MongoId('67e661ac2ad43881dc841dd7'),
                'branch_id' => self::GLOFOX_BRANCH_ID,
                'namespace' => self::GLOFOX_NAMESPACE,
                'event_id' => '67e508c0eb798f7699a6ae62',
                'status' => BookingStatus::CANCELED,
                'confirmed' => true,
                'program_id' => $programId,
                'type' => BookingType::EVENTS,
                'user_id' => $userId1,
                'time_start' => new MongoDate(Carbon::parse('2024-02-24 18:00:00')->getTimestamp()),
                'time_finish' => new MongoDate(Carbon::parse('2024-02-24 19:00:00')->getTimestamp()),
                'paid' => false,
                'guest_bookings' => 0,
                'payment_method' => 'cash',
                'attended' => false,
            ],
            [
                '_id' => new MongoId('67e57c9d9244e6b5d556d80f'),
                'branch_id' => self::GLOFOX_BRANCH_ID,
                'namespace' => self::GLOFOX_NAMESPACE,
                'event_id' => '67e508c0eb798f7699a6ae63',
                'status' => BookingStatus::BOOKED,
                'confirmed' => true,
                'program_id' => $programId,
                'type' => BookingType::EVENTS,
                'user_id' => $userId1,
                'time_start' => new MongoDate(Carbon::parse('2024-03-02 18:00:00')->getTimestamp()),
                'time_finish' => new MongoDate(Carbon::parse('2024-03-02 19:00:00')->getTimestamp()),
                'paid' => false,
                'guest_bookings' => 0,
                'payment_method' => 'cash',
                'attended' => false,
            ],
            [
                '_id' => new MongoId('67e57ca3d11b35bea06efae4'),
                'branch_id' => self::GLOFOX_BRANCH_ID,
                'namespace' => self::GLOFOX_NAMESPACE,
                'event_id' => '67e508c0eb798f7699a6ae63',
                'status' => BookingStatus::BOOKED,
                'confirmed' => true,
                'program_id' => $programId,
                'type' => BookingType::EVENTS,
                'user_id' => $userId2,
                'time_start' => new MongoDate(Carbon::parse('2024-03-02 18:00:00')->getTimestamp()),
                'time_finish' => new MongoDate(Carbon::parse('2024-03-02 19:00:00')->getTimestamp()),
                'paid' => true,
                'guest_bookings' => 0,
                'payment_method' => 'cash',
                'attended' => false,
            ],
            [
                '_id' => new MongoId('67e53939b1c9efa61f578f21'),
                'branch_id' => self::GLOFOX_BRANCH_ID,
                'namespace' => self::GLOFOX_NAMESPACE,
                'event_id' => '67e508c0eb798f7699a6ae64',
                'status' => BookingStatus::BOOKED,
                'confirmed' => true,
                'program_id' => $programId,
                'type' => BookingType::EVENTS,
                'user_id' => $userId1,
                'time_start' => new MongoDate(Carbon::parse('2024-03-09 18:00:00')->getTimestamp()),
                'time_finish' => new MongoDate(Carbon::parse('2024-03-09 19:00:00')->getTimestamp()),
                'paid' => false,
                'guest_bookings' => 0,
                'payment_method' => 'cash',
                'attended' => false,
            ],
            [
                '_id' => new MongoId('67e5394161b5a8385e09a857'),
                'branch_id' => self::GLOFOX_BRANCH_ID,
                'namespace' => self::GLOFOX_NAMESPACE,
                'event_id' => '67e508c0eb798f7699a6ae64',
                'status' => BookingStatus::BOOKED,
                'confirmed' => true,
                'program_id' => $programId,
                'type' => BookingType::EVENTS,
                'user_id' => $userId2,
                'time_start' => new MongoDate(Carbon::parse('2024-03-09 18:00:00')->getTimestamp()),
                'time_finish' => new MongoDate(Carbon::parse('2024-03-09 19:00:00')->getTimestamp()),
                'paid' => true,
                'guest_bookings' => 0,
                'payment_method' => 'cash',
                'attended' => false,
            ],
            [
                '_id' => new MongoId('67e5394e7df4a5d45e3c3a98'),
                'branch_id' => self::GLOFOX_BRANCH_ID,
                'namespace' => self::GLOFOX_NAMESPACE,
                'event_id' => '67e508c0eb798f7699a6ae65',
                'status' => BookingStatus::WAITING,
                'confirmed' => true,
                'program_id' => $programId,
                'type' => BookingType::EVENTS,
                'user_id' => $userId1,
                'time_start' => new MongoDate(Carbon::parse('2024-03-16 18:00:00')->getTimestamp()),
                'time_finish' => new MongoDate(Carbon::parse('2024-03-16 19:00:00')->getTimestamp()),
                'paid' => true,
                'guest_bookings' => 0,
                'payment_method' => 'cash',
                'attended' => false,
            ],
            [
                '_id' => new MongoId('67e539573c49f14e0c21bff4'),
                'branch_id' => self::GLOFOX_BRANCH_ID,
                'namespace' => self::GLOFOX_NAMESPACE,
                'event_id' => '67e508c0eb798f7699a6ae66',
                'status' => BookingStatus::CANCELED,
                'confirmed' => true,
                'program_id' => $programId,
                'type' => BookingType::EVENTS,
                'user_id' => $userId2,
                'time_start' => new MongoDate(Carbon::parse('2024-03-23 18:00:00')->getTimestamp()),
                'time_finish' => new MongoDate(Carbon::parse('2024-03-23 19:00:00')->getTimestamp()),
                'paid' => false,
                'guest_bookings' => 0,
                'payment_method' => 'cash',
                'attended' => false,
            ],
            [
                '_id' => new MongoId('67e539603f682b38d80f2b7d'),
                'branch_id' => self::GLOFOX_BRANCH_ID,
                'namespace' => self::GLOFOX_NAMESPACE,
                'event_id' => '67e508c0eb798f7699a6ae66',
                'status' => BookingStatus::CANCELED,
                'confirmed' => true,
                'program_id' => $programId,
                'type' => BookingType::EVENTS,
                'user_id' => $userId3,
                'time_start' => new MongoDate(Carbon::parse('2024-03-23 18:00:00')->getTimestamp()),
                'time_finish' => new MongoDate(Carbon::parse('2024-03-23 19:00:00')->getTimestamp()),
                'paid' => false,
                'guest_bookings' => 0,
                'payment_method' => 'cash',
                'attended' => false,
            ],
        ];
    }

    public function forCountReservationsFromEventsNotGenerated(): array
    {
        return [
            $this->createBooking([
                '_id' => new MongoId(),
                'branch_id' => self::GLOFOX_BRANCH_ID,
                'namespace' => 'glofox',
                'program_id' => 'my-program-id',
                'schedule_code' => 'my-schedule-code',
                'event_id' => null,
                'status' => BookingStatus::RESERVED,
                'time_start' => new MongoDate(Carbon::parse('2025-02-01 09:00:00')->getTimestamp()),
                'time_finish' => new MongoDate(Carbon::parse('2024-02-01 10:00:00')->getTimestamp()),
            ]),
        ];
    }

    private function forCancelBookingsPausedMembership(): array
    {
        $userId = '6814d3419e5eedaa97f9638a';
        $programId = '67e508c0eb798f7011a6ae61';
        $planCode = '1506223805842';

        return [
            [
                '_id' => new MongoId('67e539603f682b38d80f2b7e'),
                'branch_id' => self::GLOFOX_BRANCH_ID,
                'namespace' => self::GLOFOX_NAMESPACE,
                'event_id' => '55e539603f682b38d79w1s3a',
                'status' => BookingStatus::BOOKED,
                'confirmed' => true,
                'program_id' => $programId,
                'type' => BookingType::EVENTS,
                'user_id' => $userId,
                'time_start' => new MongoDate(Carbon::parse('2024-04-01 10:00:00')->getTimestamp()),
                'time_finish' => new MongoDate(Carbon::parse('2024-04-01 11:00:00')->getTimestamp()),
                'plan_code' => $planCode,
                'paid' => false,
                'guest_bookings' => 0,
                'payment_method' => 'cash',
                'attended' => false,
            ],
            [
                '_id' => new MongoId('67e539603f682b38d80f2b7f'),
                'branch_id' => self::GLOFOX_BRANCH_ID,
                'namespace' => self::GLOFOX_NAMESPACE,
                'event_id' => '55e539603f682b38d79w1s3b',
                'status' => BookingStatus::BOOKED,
                'confirmed' => true,
                'program_id' => $programId,
                'type' => BookingType::EVENTS,
                'user_id' => $userId,
                'time_start' => new MongoDate(Carbon::parse('2024-04-08 14:00:00')->getTimestamp()),
                'time_finish' => new MongoDate(Carbon::parse('2024-04-08 15:00:00')->getTimestamp()),
                'plan_code' => $planCode,
                'paid' => false,
                'guest_bookings' => 0,
                'payment_method' => 'cash',
                'attended' => false,
            ],
            [
                '_id' => new MongoId('67e539603f682b38d80f2b80'),
                'branch_id' => self::GLOFOX_BRANCH_ID,
                'namespace' => self::GLOFOX_NAMESPACE,
                'event_id' => '55e539603f682b38d79w1s3c',
                'status' => BookingStatus::BOOKED,
                'confirmed' => true,
                'program_id' => $programId,
                'type' => BookingType::EVENTS,
                'user_id' => $userId,
                'time_start' => new MongoDate(Carbon::parse('2024-04-15 16:00:00')->getTimestamp()),
                'time_finish' => new MongoDate(Carbon::parse('2024-04-15 17:00:00')->getTimestamp()),
                'plan_code' => $planCode,
                'paid' => false,
                'guest_bookings' => 0,
                'payment_method' => 'cash',
                'attended' => false,
            ],
            [
                '_id' => new MongoId('67e539603f682b38d80f2b81'),
                'branch_id' => self::GLOFOX_BRANCH_ID,
                'namespace' => self::GLOFOX_NAMESPACE,
                'event_id' => '55e539603f682b38d79w1s3c',
                'status' => BookingStatus::BOOKED,
                'confirmed' => true,
                'program_id' => $programId,
                'type' => BookingType::EVENTS,
                'user_id' => $userId,
                'time_start' => new MongoDate(Carbon::parse('2024-04-30 23:00:00')->getTimestamp()),
                'time_finish' => new MongoDate(Carbon::parse('2024-04-30 23:30:00')->getTimestamp()),
                'plan_code' => $planCode,
                'paid' => false,
                'guest_bookings' => 0,
                'payment_method' => 'cash',
                'attended' => false,
            ],
            [
                '_id' => new MongoId('67e539603f682b38d80f2b82'),
                'branch_id' => self::GLOFOX_BRANCH_ID,
                'namespace' => self::GLOFOX_NAMESPACE,
                'event_id' => '55e539603f682b38d79w1s3c',
                'status' => BookingStatus::BOOKED,
                'confirmed' => true,
                'program_id' => $programId,
                'type' => BookingType::EVENTS,
                'user_id' => $userId,
                'time_start' => new MongoDate(Carbon::parse('2024-04-01 09:00:00')->getTimestamp()),
                'time_finish' => new MongoDate(Carbon::parse('2024-04-01 09:30:00')->getTimestamp()),
                'plan_code' => $planCode,
                'paid' => false,
                'guest_bookings' => 0,
                'payment_method' => 'cash',
                'attended' => false,
            ],
            [
                '_id' => new MongoId('67e539603f682b38d80f2b83'),
                'branch_id' => self::GLOFOX_BRANCH_ID,
                'namespace' => self::GLOFOX_NAMESPACE,
                'event_id' => '55e539603f682b38d79w1s3c',
                'status' => BookingStatus::BOOKED,
                'confirmed' => true,
                'program_id' => $programId,
                'type' => BookingType::EVENTS,
                'user_id' => $userId,
                'time_start' => new MongoDate(Carbon::parse('2024-05-01 00:00:00')->getTimestamp()),
                'time_finish' => new MongoDate(Carbon::parse('2024-05-01 00:30:00')->getTimestamp()),
                'plan_code' => $planCode,
                'paid' => false,
                'guest_bookings' => 0,
                'payment_method' => 'cash',
                'attended' => false,
            ],
            [
                '_id' => new MongoId('67e539603f682b38d80f2b84'),
                'branch_id' => self::GLOFOX_BRANCH_ID,
                'namespace' => self::GLOFOX_NAMESPACE,
                'event_id' => '55e539603f682b38d79w1s3c',
                'status' => BookingStatus::BOOKED,
                'confirmed' => true,
                'program_id' => $programId,
                'type' => BookingType::EVENTS,
                'user_id' => $userId,
                'time_start' => new MongoDate(Carbon::parse('2024-04-01 08:45:00')->getTimestamp()),
                'time_finish' => new MongoDate(Carbon::parse('2024-04-01 09:45:00')->getTimestamp()),
                'plan_code' => $planCode,
                'paid' => false,
                'guest_bookings' => 0,
                'payment_method' => 'cash',
                'attended' => false,
            ],
        ];
    }
}
