<?php

App::import('Test/Case', 'GlofoxTestCase');

use Glofox\Response\Models\Branches\Features\CoursesModel;

/**
 * Test cases
 * 1 Model contains all fields with data
 * 2 Missing fields and returns correct structure
 * 3 Additional fields and return correct structure
 * 4 Empty array fields and return correct structure.
 *
 * Class CoursesModelTest
 */
class CoursesModelTest extends GlofoxTestCase
{
    public $fixtures = [];

    /**
     * @dataProvider modelProvider
     */
    public function testModel($dataSent, $result): void
    {
        $model = new CoursesModel($dataSent);
        $this->assertEquals($model, $result);
    }

    public function modelProvider(): array
    {
        return [
            '1 Model contains all fields with data' => [
                [
                    'order' => 1,
                    'enabled' => false,
                    'guest_bookings' => 0,
                ],
                new CoursesModel(
                    [
                        'order' => 1,
                        'enabled' => false,
                        'guest_bookings' => 0,
                    ]
                ),
            ],
            '2 Missing fields and returns correct structure' => [
                [
                    // order missing
                    'enabled' => false,
                ],
                new CoursesModel(
                    [
                        'order' => 0,
                        'enabled' => false,
                        'guest_bookings' => 0,
                    ]
                ),
            ],
            '3 Additional fields and return correct structure' => [
                [
                    'extra' => 'test', // additional field
                    'order' => 1,
                    'enabled' => false,
                    'guest_bookings' => 0,
                ],
                new CoursesModel(
                    [
                        'order' => 1,
                        'enabled' => false,
                        'guest_bookings' => 0,
                    ]
                ),
            ],
            '4 Empty array fields and return correct structure' => [
                [],
                new CoursesModel(
                    [
                        'order' => 0,
                        'enabled' => false,
                        'guest_bookings' => 0,
                    ]
                ),
            ],
        ];
    }
}
