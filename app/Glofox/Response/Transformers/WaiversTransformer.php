<?php

namespace Glofox\Response\Transformers;

use Glofox\Domain\ElectronicAgreements\Service;
use Glofox\Domain\Terms\Type;
use Glofox\Domain\Terms\Validation\Validators\EmptyHtmlStringValidatorInterface;
use Glofox\Response\Models\BasicModels\MainModel;
use Glofox\Response\Models\Waivers\WaiversModel;

/**
 * Class WaiversTransformer.
 *
 * @package Glofox\Response\Transformers
 */
class WaiversTransformer extends BaseTransformer
{
    /**
     * @param array
     * @return MainModel
     */
    public function setFromArray(array $results)
    {
        $dataModelArrayCollection = [];
        if (!empty($results) && is_array($results) && isset($results['data'])) {
            foreach ($results['data'] as $data) {
                $dataModelArrayCollection[] = $this->setWaiverModelFromData($data);
            }
        }

        return $this->setMainModelFromArray($results, $dataModelArrayCollection);
    }

    /**
     * @param array
     * @return WaiversModel
     */
    public function setWaiverModelFromData(array $data)
    {
        $emptyHtmlStringValidator = app()->make(EmptyHtmlStringValidatorInterface::class);
        $content = (string)($data['content'] ?? '');

        if (isset($data['type']) && strtoupper($data['type']) === Type::SMS_WAIVER && $emptyHtmlStringValidator->validate($content)) {
            $content = '';
        }

        return new WaiversModel(
            [
                '_id' => (string)($data['_id'] ?? ''),
                'namespace' => (string)($data['namespace'] ?? ''),
                'branch_id' => (string)($data['branch_id'] ?? ''),
                'type' => (string)($data['type'] ?? ''),
                'content' => $content,
                'image_url' => (string)($data['image_url'] ?? ''),
                'requires_new_agreement' => (bool)($data['requires_new_agreement'] ?? false),
                'service' => (string)($data['service'] ?? Service::SERVICE_LEGACY)
            ]
        );
    }
}
