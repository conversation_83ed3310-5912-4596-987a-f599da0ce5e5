<?php

namespace Glofox\Payments\Providers\Gateway\TerminalReaders;

use Glofox\Domain\PaymentMethods\Models\PaymentMethod;
use Glofox\Domain\PaymentProviders\Models\PaymentProvider;
use Glofox\Payments\Entities\Merchant\Exceptions\MerchantRetrievalException;
use Glofox\Payments\Entities\TerminalReader\Contracts\TerminalReaderHandlerContract;
use Glofox\Payments\Entities\TerminalReader\Internal\CreateTerminalReader;
use Glofox\Payments\Entities\TerminalReader\Models\TerminalReader;
use Glofox\Payments\Providers\Gateway\GRPC\GRPC;
use Glofox\Payments\Providers\Gateway\GRPC\Metadata;
use Glofox\Payments\Providers\Gateway\ProtoParsers\TerminalReaderParser;
use GRPC\Payments\AccountService\CreateTerminalReaderReq;
use GRPC\Payments\AccountService\DeleteTerminalReaderReq;
use GRPC\Payments\AccountService\EmptyAccountObject;

class TerminalReaders implements TerminalReaderHandlerContract
{
    use GRPC;

    private PaymentMethod $paymentMethod;

    private PaymentProvider $paymentProvider;

    private TerminalReaderParser $terminalReaderParser;

    private Metadata $metadata;

    public function __construct(PaymentMethod $paymentMethod, PaymentProvider $paymentProvider)
    {
        $this->paymentMethod = $paymentMethod;
        $this->paymentProvider = $paymentProvider;
        $this->terminalReaderParser = app()->make(TerminalReaderParser::class);
        $this->metadata = app()->make(Metadata::class);
    }

    public function create(CreateTerminalReader $params): TerminalReader
    {
        $request = new CreateTerminalReaderReq();
        $request->setMerchantId((int) $this->paymentMethod->provider()->accountId());
        $request->setRegistrationCode($params->getRegistrationCode());
        $request->setLabel('');

        /** @var $reply \GRPC\Payments\AccountService\TerminalReader */
        [$reply, $status] = $this->GRCPClient()->accountService->CreateTerminalReader($request, $this->metadata->get())->wait();

        if (0 != $status->code || !($reply instanceof \GRPC\Payments\AccountService\TerminalReader)) {
            throw new MerchantRetrievalException($this->getErrorFromStatus($status));
        }

        return $this->terminalReaderParser->parseFromProtoMessage($reply);
    }

    public function delete(string $id): void
    {
        $request = new DeleteTerminalReaderReq();
        $request->setID((int) $id);

        /** @var $reply EmptyAccountObject */
        [$reply, $status] = $this->GRCPClient()->accountService->DeleteTerminalReader($request, $this->metadata->get())->wait();

        if (0 != $status->code || !($reply instanceof EmptyAccountObject)) {
            throw new MerchantRetrievalException($this->getErrorFromStatus($status));
        }
    }
}