<?php

namespace Glofox\Payments\Entities\Subscription\Models;

use Glofox\Payments\Entities\BaseEntity;

/**
 * Class Subscription.
 */
class Subscription extends BaseEntity implements \JsonSerializable
{
    /**
     * @var string
     */
    protected $id;

    /**
     * @var int
     */
    protected $customerId;

    /**
     * @var int
     */
    protected $planId;

    /**
     * @var int
     */
    protected $merchantId;

    /**
     * @var int
     */
    protected $fee = 0;

    /**
     * @var int
     */
    protected $amount;

    /**
     * @var string
     */
    protected $status;

    /**
     * @var int
     */
    protected $start = 0;

    /**
     * @var int
     */
    protected $end = 0;

    /**
     * @var int
     */
    protected $trialStart;

    /**
     * @var int
     */
    protected $trialEnd;

    /**
     * @var int
     */
    protected $paymentServiceId;

    /**
     * @var string
     */
    protected $paymentServiceResponse;

    /**
     * @var array|null
     */
    protected $metadata;

    /**
     * @var int
     */
    protected $canceledAt;

    /**
     * @var int
     */
    protected $endedAt;

    /**
     * @var int
     */
    protected $createdAt;

    /**
     * @var int
     */
    protected $updatedAt;

    /**
     * @var int
     */
    protected $lastExecutedAt;

    /**
     * @var string|null
     */
    protected $externalRef;

    /**
     * @var string
     */
    protected $transactionUUID;

    /** @var int */
    protected $currentRetries = 0;

    /**
     * @var string[]
     */
    protected $discounts = [];

    /**
     * @var bool
     */
    protected $discountsPreApplied = false;

    /**
     * @return string
     */
    public function id(): string
    {
        return (string) $this->id;
    }

    /**
     * @param mixed $id
     *
     * @return self
     */
    public function setId($id): self
    {
        $this->id = $id;

        return $this;
    }

    /**
     * @return string
     */
    public function customerId(): string
    {
        return (string) $this->customerId;
    }

    /**
     * @param mixed $customerId
     *
     * @return self
     */
    public function setCustomerId($customerId): self
    {
        $this->customerId = $customerId;

        return $this;
    }

    /**
     * @return string
     */
    public function planId(): string
    {
        return (string) $this->planId;
    }

    /**
     * @param $planId
     *
     * @return self
     */
    public function setPlanId($planId): self
    {
        $this->planId = $planId;

        return $this;
    }

    /**
     * @return mixed
     */
    public function merchantId(): string
    {
        return (string) $this->merchantId;
    }

    /**
     * @param $merchantId
     *
     * @return self
     */
    public function setMerchantId($merchantId): self
    {
        $this->merchantId = $merchantId;

        return $this;
    }

    /**
     * @return int
     */
    public function fee(): int
    {
        return $this->fee;
    }

    /**
     * @param int $fee
     *
     * @return self
     */
    public function setFee(int $fee): self
    {
        $this->fee = $fee;

        return $this;
    }

    /**
     * @return int|null
     */
    public function amount(): ?int
    {
        return $this->amount;
    }

    /**
     * @param int|null $amount
     *
     * @return self
     */
    public function setAmount(?int $amount): self
    {
        $this->amount = $amount;

        return $this;
    }

    /**
     * @return string
     */
    public function status(): ?string
    {
        return $this->status;
    }

    /**
     * @param string $status
     *
     * @return self
     */
    public function setStatus(?string $status): self
    {
        $this->status = $status;

        return $this;
    }

    /**
     * @return int
     */
    public function startAt(): int
    {
        return $this->start;
    }

    /**
     * @return int
     */
    public function endAt(): int
    {
        return $this->end;
    }

    /**
     * @return int
     */
    public function nextPaymentAt(): int
    {
        return $this->startAt();
    }

    /**
     * @param int $start
     *
     * @return self
     */
    public function setStartAt(int $start): self
    {
        $this->start = $start;

        return $this;
    }

    /**
     * @param int $end
     *
     * @return self
     */
    public function setEndAt(int $end): self
    {
        $this->end = $end;

        return $this;
    }

    /**
     * @return int
     */
    public function trialStartAt(): ?int
    {
        return $this->trialStart;
    }

    /**
     * @param int $trialStart
     *
     * @return self
     */
    public function setTrialStartAt(int $trialStart): self
    {
        $this->trialStart = $trialStart;

        return $this;
    }

    /**
     * @return int
     */
    public function trialEndAt(): ?int
    {
        return $this->trialEnd;
    }

    /**
     * @param int $trialEnd
     *
     * @return self
     */
    public function setTrialEndAt(int $trialEnd): self
    {
        $this->trialEnd = $trialEnd;

        return $this;
    }

    /**
     * @return mixed
     */
    public function paymentServiceId(): string
    {
        return (string) $this->paymentServiceId;
    }

    /**
     * @param int $paymentServiceId
     *
     * @return self
     */
    public function setPaymentServiceId($paymentServiceId): self
    {
        $this->paymentServiceId = $paymentServiceId;

        return $this;
    }

    /**
     * @return string
     */
    public function paymentServiceResponse(): string
    {
        return (string) $this->paymentServiceResponse;
    }

    /**
     * @param string $paymentServiceResponse
     *
     * @return self
     */
    public function setPaymentServiceResponse(string $paymentServiceResponse): self
    {
        $this->paymentServiceResponse = $paymentServiceResponse;

        return $this;
    }

    /**
     * @param int $lastExecutedAt
     *
     * @return Subscription
     */
    public function setLastExecutedAt(int $lastExecutedAt): self
    {
        $this->lastExecutedAt = $lastExecutedAt;

        return $this;
    }

    /**
     * @return int|null
     */
    public function lastExecutedAt(): ?int
    {
        return $this->lastExecutedAt;
    }

    /**
     * @return array|null
     */
    public function metadata(): ?array
    {
        return $this->metadata;
    }

    /**
     * @param array $metadata
     *
     * @return self
     */
    public function setMetadata(array $metadata): self
    {
        $this->metadata = $metadata;

        return $this;
    }

    /**
     * @return int
     */
    public function canceledAt(): ?int
    {
        return $this->canceledAt;
    }

    /**
     * @param int $canceledAt
     *
     * @return self
     */
    public function setCanceledAt(?int $canceledAt): self
    {
        $this->canceledAt = $canceledAt;

        return $this;
    }

    /**
     * @return int
     */
    public function endedAt(): int
    {
        return $this->endedAt;
    }

    /**
     * @param int $endedAt
     *
     * @return self
     */
    public function setEndedAt(int $endedAt): self
    {
        $this->endedAt = $endedAt;

        return $this;
    }

    /**
     * @return int
     */
    public function createdAt(): int
    {
        return $this->createdAt;
    }

    /**
     * @param int $createdAt
     *
     * @return self
     */
    public function setCreatedAt(int $createdAt): self
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    /**
     * @return int
     */
    public function updatedAt(): int
    {
        return $this->updatedAt;
    }

    /**
     * @param int $updatedAt
     *
     * @return self
     */
    public function setUpdatedAt(int $updatedAt): self
    {
        $this->updatedAt = $updatedAt;

        return $this;
    }

    /**
     * @return string|null
     */
    public function externalRef(): ?string
    {
        return $this->externalRef;
    }

    /**
     * @param string|null $externalRef
     *
     * @return self
     */
    public function setExternalRef(?string $externalRef): self
    {
        $this->externalRef = $externalRef;

        return $this;
    }

    public function transactionUUID(): string
    {
        return (string) $this->transactionUUID;
    }

    public function setTransactionUUID(string $transactionUUID): self
    {
        $this->transactionUUID = $transactionUUID;

        return $this;
    }

    public function currentRetries(): int
    {
        return (int) $this->currentRetries;
    }

    public function setCurrentRetries(int $currentRetries): self
    {
        $this->currentRetries = $currentRetries;

        return $this;
    }

    public function discounts(): array
    {
        return $this->discounts;
    }

    public function setDiscounts(array $discounts): self
    {
        $this->discounts = $discounts;

        return $this;
    }

    public function discountsPreApplied(): bool
    {
        return $this->discountsPreApplied;
    }

    public function setDiscountsPreApplied(bool $discountsPreApplied): self
    {
        $this->discountsPreApplied = $discountsPreApplied;

        return $this;
    }

    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'customerId' => $this->customerId,
            'planId' => $this->planId,
            'merchantId' => $this->merchantId,
            'fee' => $this->fee,
            'amount' => $this->amount,
            'status' => $this->status,
            'start' => $this->start,
            'end' => $this->end,
            'trialStart' => $this->trialStart,
            'trialEnd' => $this->trialEnd,
            'currentRetries' => $this->currentRetries,
            'paymentServiceId' => $this->paymentServiceId,
            'paymentServiceResponse' => $this->paymentServiceResponse,
            'metadata' => $this->metadata,
            'canceledAt' => $this->canceledAt,
            'endedAt' => $this->endedAt,
            'createdAt' => $this->createdAt,
            'updatedAt' => $this->updatedAt,
            'externalRef' => $this->externalRef,
            'discounts' => $this->discounts,
            'discountsPreApplied' => $this->discountsPreApplied,
        ];
    }

    public function jsonSerialize(): array
    {
        return $this->toArray();
    }
}
