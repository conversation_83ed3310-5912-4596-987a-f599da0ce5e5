type: object
description: NotEligibleForEventAndNotEnoughCredits
properties:
  success:
    type: boolean
    default: false
  message:
    type: string
    default: 'NOT_ELIGIBLE_FOR_EVENT_AND_NOT_ENOUGH_CREDITS'
  message_code:
    type: string
    default: 'NOT_ELIGIBLE_FOR_EVENT_AND_NOT_ENOUGH_CREDITS'
  message_data:
    type: array
    items:
      type: string
  errors:
    type: string
    default: 'NOT_ELIGIBLE_FOR_EVENT_AND_NOT_ENOUGH_CREDITS'
