<?php

App::uses('CakeEmail', 'Network/Email');
App::uses('AppController', 'Controller');

use Glofox\CdnProvider;
use Glofox\Domain\Branches\Enums\MobileAppColor;
use Glofox\Domain\Branches\Exceptions\BranchNotFoundException;
use Glofox\Domain\Branches\Models\Branch;
use Glofox\Domain\Branches\Repositories\BranchesRepository;
use Glofox\Domain\Branches\Search\Expressions\BranchId;
use Glofox\Domain\Clients\Exceptions\ClientNotFoundException;
use Glofox\Domain\Clients\Http\Requests\CreateClientRequest;
use Glofox\Domain\Clients\Http\Requests\GetClientsBranchesRequest;
use Glofox\Domain\Clients\Http\Requests\RemoveClientRequest;
use Glofox\Domain\Clients\Http\Responses\{RemoveClientResponse};
use Glofox\Domain\Clients\Legacy\LegacyCreateClientHandler;
use Glofox\Domain\Clients\Repositories\ClientsRepository;
use Glofox\Domain\Users\Exceptions\UserNotFoundException;
use Glofox\Domain\Users\Repositories\UsersRepository;
use Glofox\Domain\Users\UserTypesCollection;
use Glofox\Http\Responses\ResponseInterface;
use Glofox\Repositories\Search\Expressions\Shared\Id;
use Glofox\Validation\Exceptions\ValidationException;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\JsonResponse;

/**
 * @deprecated 3.0 All new features must go inside Glofox\Domain\Clients\Http\ClientsController. We keep
 * it for compatibility with legacy endpoints
 *
 * @property Client Client
 * @property \Branch Branch
 *
 * Class ClientsController
 */
class ClientsController extends Glofox\Domain\Clients\Http\ClientsController
{
    public $name = 'Clients';
    public $components = [
        'S3',
        'Paginator',
        'Utility',
        'Notification',
        'Zendesk',
        'JWT',
    ];
    public $uses = [
        'Booking',
        'Access',
        'Announcement',
        'Client',
        'Branch',
        'Utility',
        'Facility',
        'Access',
        'Booking',
        'Course',
        'Dictionary',
        'Event',
        'Feature',
        'Membership',
        'MembershipGroup',
        'Product',
        'Program',
        'Report',
        'StripeCharge',
        'SubscriptionPlan',
        'TimeSlot',
        'TimeSlotPattern',
        'UserCredit',
    ];
    private ClientsRepository $clientsRepository;
    private BranchesRepository $branches;
    private LoggerInterface $logger;

    public function __construct($request, $response)
    {
        parent::__construct($request, $response);

        $this->clientsRepository = app()->make(ClientsRepository::class);
        $this->branches = app()->make(BranchesRepository::class);
        $this->logger = app()->make(LoggerInterface::class);
    }

    public function dispatcher($identifier = null)
    {
        if (!$this->isUserLogged() && 'GET' == $this->request->method()) {
            return $this->container()->call([$this, 'getClientsBranches']);
        }

        return parent::dispatch($identifier, $this->Client);
    }

    public function getClientsBranches(GetClientsBranchesRequest $request)
    {
        $currentEnv = $this->Client->get_current_environment();
        $bundle = $request->get('bundle');

        try {
            $parent = $this->clientsRepository->findByNamespace(str_replace('_', '', $bundle));
            $parent = ['Client' => $parent->toArray()];
        } catch (ClientNotFoundException $e) {
            $parent = [];
        }

        $clients = $this->clientsRepository->findByBundle($bundle);
        $clients = array_column($clients, 'Client');

        $namespaces = array_column($clients, 'namespace');
        $branches = $this->branches->findByNamespaces($namespaces, [], $request->memberFacingOnly());

        $clientMap = [];
        foreach ($clients as $client) {
            $clientMap[$client['namespace']] = $client;
        }

        $entities = array_map(function ($branch) use ($clientMap, $currentEnv) {
            $branch = Branch::make($branch['Branch']);

            $namespace = $branch->namespace();
            $client = collect($clientMap[$namespace]);

            /**
             * $colors = $branch->configuration()->mobile()->colors();
             * $branchColors = [
             * 'background' => str_replace('#', '', $colors->backgroundColor()),
             * 'accent' => str_replace('#', '', $colors->foregroundColor()),
             * 'text' => str_replace('#', '', $colors->textColor()),
             * ];
             */
            $branchId = (string)$branch->id();

            $configuration = $branch->configuration()->toArray();

            $backgroundColor = $configuration['webportal']['colors']['background'] ?? MobileAppColor::DEFAULT_BACKGROUND_COLOR;
            $foregroundColor = $configuration['webportal']['colors']['accent'] ?? MobileAppColor::DEFAULT_ACCENT_COLOR;
            $textColor = $configuration['webportal']['colors']['text'] ?? MobileAppColor::DEFAULT_TEXT_COLOR;

            $branchColors = [
                'background' => $backgroundColor,
                'accent' => $foregroundColor,
                'text' => $textColor,
            ];

            return [
                'end_date' => $client->get('end_date'),
                'bundles' => $client->get('bundles'),
                'client_id' => $client->get('_id'),
                'client_name' => $client->get('name'),
                'branch_name' => $branch->name(),
                'branch_id' => $branchId,
                'branch_namespace' => $namespace,
                'logo' => \sprintf('%s/%s/%s/logo.png', CdnProvider::getUrl(), $currentEnv, $namespace),
                'address' => $branch->address(),
                'branch_colors' => $branchColors,
            ];
        }, $branches);

        return response()->json([
            'success' => true,
            'entities' => $entities,
            'bundle' => $parent,
        ]);
    }

    public function remove(
        RemoveClientRequest $request,
        BranchesRepository $branchesRepository,
        UsersRepository $usersRepository
    ): ResponseInterface {
        /** @var \Glofox\Domain\Clients\Models\Client $client */
        $client = $this->clientsRepository
            ->addCriteria(new Id($request->getClientId()))
            ->firstOrFail(function () use ($request) {
                throw new Exception(sprintf('Client %s not found', $request->getClientId()));
            });

        /** @var Branch $branch */
        $branch = $branchesRepository
            ->addCriteria(new Id($request->getBranchId()))
            ->firstOrFail(function () use ($request) {
                throw BranchNotFoundException::withId($request->getBranchId());
            });

        $type = UserTypesCollection::make([
            \UserType::ADMIN(),
            \UserType::SUPERADMIN(),
        ]);

        /** @var \Glofox\Domain\Users\Models\User $user */
        $admin = $usersRepository
            ->addCriteria(new Id($request->getAdminId()))
            ->addCriteria(new BranchId($request->getBranchId()))
            ->addCriteria(new Glofox\Domain\Users\Search\Expressions\Type($type))
            ->firstOrFail(function () use ($request) {
                throw new UserNotFoundException(
                    sprintf('Admin %s not found with branch %s', $request->getAdminId(), $request->getBranchId())
                );
            });

        if ($client->isGlofoxRoot()) {
            throw new \Exception(sprintf('Cannot remove the `glofox` root client %s', $client->id()));
        }

        if (!$client->wasRecentlyCreated()) {
            throw new \Exception('Cannot remove the client since its removable timeframe has passed');
        }

        $this->logger->alert(
            sprintf(
                'Preparing to PERMANENTLY delete client %s, branch %s and admin %s',
                $client->id(),
                $branch->id(),
                $admin->id()
            )
        );

        $usersRepository->delete($admin, true);

        $this->logger->alert(
            sprintf('Admin %s PERMANENTLY deleted', $admin->id())
        );

        $branchesRepository->delete($branch, true);

        $this->logger->alert(
            sprintf('Branch %s PERMANENTLY deleted', $branch->id())
        );

        $this->clientsRepository->delete($client, true);

        $this->logger->alert(
            sprintf('Client %s PERMANENTLY deleted', $client->id())
        );

        $this->logger->alert(
            sprintf(
                'Successfully PERMANENTLY deleted client %s, branch %s and admin %s',
                $client->id(),
                $branch->id(),
                $admin->id()
            )
        );

        return new RemoveClientResponse($client, $branch, $admin);
    }

    /**
     * @throws ValidationException
     * @throws UnsuccessfulOperation
     */
    public function create(
        CreateClientRequest $request,
        LegacyCreateClientHandler $legacyCreateClientHandler
    ): JsonResponse {
        return $legacyCreateClientHandler->create($request, $this->JWT, $this->Zendesk);
    }

    /**
     * Setup bundles for this user.
     *
     * @param null $bundle
     *
     * @return string
     */
    public function bundles($bundle = null)
    {
        $user = $this->JWT->parseToken();
        $data = json_decode(file_get_contents('php://input'), true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR);
        if (empty($data)) {
            $data = [];
        }
        //Use if exists
        $client = $this->Client->findByNamespace($user['namespace']);
        $client['Client']['bundles'] = $data;

        return json_encode($this->Client->save($client), JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    /**
     * List all namespaces for specific bundle. EX bundle mass update in Mongodb
     * db.clients.update({ bundle : { $exists: false}},{ $set : { bundle: ['_glofox']}},{ upsert: true, multi: true })
     * if visualization field is specificed for example bundles or visible it will use that field to query for the presence of
     * the name specified.
     *
     * @param null $bundle
     * @param string $visualization
     *
     * @return string
     */
    public function list_clients($bundle = null, $visualization = 'bundles')
    {
        $this->response->type('json');
        $this->autoRender = false;
        $conditions = ['active' => true];

        $parent = null;
        if (!empty($bundle)) {
            $conditions[$visualization] = $bundle;
            $parent = $this->Client->findByNamespace(str_replace('_', '', $bundle));
        }
        $params = [
            'conditions' => $conditions,
            'order' => ['name' => 1],
            'fields' => ['name', 'namespace', 'description', 'bundles', 'bundle_config', 'end_date'],
        ];

        $results = $this->Client->find('all', $params);

        return json_encode(['success' => true, 'clients' => $results, 'bundle' => $parent], JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    /**
     * Save a client object including bundle and patterns, ONLY REGISTERED BUNDLE ADMINS FOR THE
     * NAMESPACE UNDER "ADMIN" TYPE can execute this command.
     *
     * @return object {Course:object,success:boolean}
     * @example    https://www.glofoxlogin/client/upsert Payload: Course object directly
     *
     * @internal   Some data validation required since its a complex object. TO_DO
     *
     */
    public function upsert()
    {
        $user = $this->JWT->parseToken();
        //Is Glofox Admin? if not, don't allow
        if (!in_array($user['type'], [UserType::ADMIN, UserType::SUPERADMIN])) {
            throw new UnauthorizedException(json_encode($user, JSON_PARTIAL_OUTPUT_ON_ERROR));
        }
        $client = $this->getPayload();

        $clientInDb = $this->clientsRepository->findById($client['_id']);

        if (empty($clientInDb)) {
            throw new NotFoundException('Client cannot be found.');
        }

        if ($clientInDb['Client']['namespace'] !== $user['namespace']) {
            throw new UnauthorizedException('You cannot update a client in a different namespace.');
        }

        $save_client = $this->Client->save($client);
        if ($save_client) {
            return json_encode($save_client, JSON_PARTIAL_OUTPUT_ON_ERROR);
        } else {
            $error_message = $this->Client->get_latest_error();

            return json_encode(['success' => false, 'message' => $error_message, 'message_code' => $error_message],
                JSON_PARTIAL_OUTPUT_ON_ERROR);
        }
    }

    /**
     * @return string
     */
    public function getAllBundles()
    {
        $conditions = [
            'active' => true,
            'bundle_config' => ['$exists' => true],
        ];
        $fields = ['namespace', 'bundle_config.billing_plans.code'];
        $bundles = $this->Client->find('all', ['conditions' => $conditions, 'fields' => $fields]);

        return json_encode(['success' => !empty($bundles), 'data' => $bundles], JSON_PARTIAL_OUTPUT_ON_ERROR);
    }


    public function getPreClientById($id = null)
    {
        $this->loadModel('PreClient');

        if (!$id) {
            return json_encode([
                'success' => false,
                'message' => 'PRE_CLIENT_ID_CAN_NOT_BE_NULL',
                'message_code' => 'PRE_CLIENT_ID_CAN_NOT_BE_NULL',
            ]);
        }

        $preclient = $this->PreClient->findById($id);

        return json_encode(['success' => true, 'data' => $preclient], JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    public function index()
    {
        $params = [
            'conditions' => [
                'active' => true,
            ],
            'order' => [
                '_id' => -1,
            ],
            'limit' => 1000,
            'page' => 1,
        ];

        $results = $this->Client->find('all', $params);

        return json_encode($results, JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    public function findByNamespace($id = null)
    {
        $params = [
            'conditions' => [
                'namespace' => $id,
                'active' => true,
            ],
            'order' => [
                '_id' => -1,
            ],
            'limit' => 1,
            'page' => 1,
        ];
        $results = $this->Client->find('all', $params);

        return json_encode($results, JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    public function upload_pem($namespace = null)
    {
        if (null == $namespace) {
            return json_encode(['success' => false]);
        }

        $path = ROOT . '/vendor/' . $namespace . '/';
        $file = $this->request->params['form']['pem'];
        $upload = $this->Utility->upload_file($file, $path, 'release_cert.pem', true);

        return json_encode(['success' => true, 'message' => 'pem uploaded']);
    }

    private function getPathByEnv(string $env): string
    {
        return $env === 'platform' || $env === 'staging' ? 'app.glofox.com' : 'dev.glofox.com';
    }
}

