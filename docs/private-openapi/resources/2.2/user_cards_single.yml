delete:
  tags:
    - UserCards
  summary: ''
  operationId: delete-user-card
  description: This endpoint to delete card from a user
  x-internal: true
  security:
    - coreapi_auth: [ ]
  parameters:
    - schema:
        type: string
      name: branchId
      in: path
      required: true
    - schema:
        type: string
      name: userId
      in: path
      required: true
    - schema:
        type: string
      name: cardId
      in: path
      required: true
  responses:
    '200':
      description: OK
      content:
        application/json:
          schema:
            type: object
            properties:
              data:
                type: array
                items:
                  $ref: "../../schemas/card.yml"
    '404':
      description: Card not found
      content:
        application/json:
          schema:
            $ref: "../../schemas/exceptions/card_not_found.yml"

put:
  tags:
    - UserCards
  summary: ''
  operationId: update-user-card
  description: This endpoint update an existing card for a user
  x-internal: true
  security:
    - coreapi_auth: [ ]
  parameters:
    - schema:
        type: string
      name: branchId
      in: path
      required: true
    - schema:
        type: string
      name: userId
      in: path
      required: true
    - schema:
        type: string
      name: cardId
      in: path
      required: true
  requestBody:
    required: true
    content:
      application/json:
        schema:
          type: object
          properties:
            zip_code:
              type: string
  responses:
    '200':
      description: OK
      content:
        application/json:
          schema:
            type: object
            properties:
              data:
                type: array
                items:
                  $ref: "../../schemas/card.yml"
    '400':
      description: Bad request
      content:
        application/json:
          schema:
            $ref: "../../schemas/error.yml"
    '404':
      description: Card not found
      content:
        application/json:
          schema:
            $ref: "../../schemas/exceptions/card_not_found.yml"
