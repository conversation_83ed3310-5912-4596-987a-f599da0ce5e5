<?php

namespace Glofox\Response\Transformers\Branches;

use Glofox\Response\Transformers\BaseTransformer;
use Glofox\Response\Models\Branches\Configuration\ColorsModel;
use Glofox\Response\Models\Branches\Configuration\WebportalModel;
use Glofox\Response\Models\Branches\Configuration\Webportal\FeaturesModel;
use Glofox\Response\Models\Branches\Configuration\Webportal\Features\EventDetailsModel;
use Glofox\Response\Models\Branches\Configuration\Webportal\Features\FiltersModel;
use Glofox\Response\Models\Branches\Configuration\Webportal\Features\GeneralModel;

/**
 * Class WebportalTransformer
 * @package Glofox\Response\Transformers
 */
class WebportalTransformer extends BaseTransformer
{
    /**
     * @param array
     * @return WebportalModel
     */
    public function setModelFromArray(array $webportal)
    {
        $colorsModel = null;
        if (!empty($webportal['colors'])) {
            $colorsModel = $this->setColorsModel($webportal['colors']);
        }

        $featureModel = null;
        if (!empty($webportal['features'])) {
            $featureModel = $this->setFeaturesModel($webportal['features']);
        }

        return new WebportalModel([
            'classes_view' => (string) ($webportal['classes_view'] ?? ''),
            'colors' => $colorsModel,
            'features' => $featureModel

        ]);
    }

    /**
     * @param array
     * @return ColorsModel
     */
    public function setColorsModel(array $colors)
    {
        $configTransformer = new ConfigurationTransformer();
        return $configTransformer->setColorsModel($colors);
    }

    /**
     * @param array
     * @return FeaturesModel
     */
    public function setFeaturesModel(array $features)
    {
        $filtersModel = null;
        if (!empty($features['filters'])) {
            $filtersModel = $this->setFiltersModel($features['filters']);
        }

        $eventDetailsModel = null;
        if (!empty($features['event_details'])) {
            $eventDetailsModel = $this->setEventDetailsModel($features['event_details']);
        }

        $generalModels = null;
        if (!empty($features['general'])) {
            $generalModels = $this->setGeneralModel($features['general']);
        }

        return new FeaturesModel(
            [
                'filters' => $filtersModel,
                'event_details' => $eventDetailsModel,
                'general' => $generalModels
            ]
        );
    }

    /**
     * @param array
     * @return FiltersModel
     */
    public function setFiltersModel(array $filters)
    {
        return new FiltersModel(
            [
                'classes' => (bool) ($filters['classes'] ?? false),
                'courses' => (bool) ($filters['courses'] ?? false),
                'facilities' => (bool) ($filters['facilities'] ?? false),
                'trainers' => (bool) ($filters['trainers'] ?? false),
            ]
        );
    }

    /**
     * @param array
     * @return GeneralModel
     */
    public function setGeneralModel(array $general)
    {
        return new GeneralModel(
            [
                'app_download_link' => (bool)  ($general['app_download_link'] ?? false),
                'classes' => (bool) ($general['classes'] ?? false),
                'courses' => (bool) ($general['courses'] ?? false),
                'facilities' => (bool) ($general['facilities'] ?? false),
                'memberships' => (bool) ($general['memberships'] ?? false),
                'show_past_events' => (bool) ($general['show_past_events'] ?? false),
                'trainers' => (bool) ($general['trainers'] ?? false),
            ]
        );
    }

    /**
     * @param array
     * @return EventDetailsModel
     */
    public function setEventDetailsModel(array $eventDetails)
    {
        return new EventDetailsModel(
            [
                'facility_name' => (bool) ($eventDetails['facility_name'] ?? false),
                'level' => (bool) ($eventDetails['level'] ?? false),
                'price' => (bool) ($eventDetails['price'] ?? false),
                'size' => (bool) ($eventDetails['size'] ?? false),
                'total_bookings' => (bool) ($eventDetails['total_bookings'] ?? false),
                'total_waiting_list' => ($eventDetails['total_waiting_list'] ?? false),
                'trainer_name' => (bool) ($eventDetails['trainer_name'] ?? false),
            ]
        );
    }
}
