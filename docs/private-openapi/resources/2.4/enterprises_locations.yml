get:
  summary: Get Enterprises Locations
  description: This endpoint retrieves a list of active locations associated with the corporate ID derived from the authenticated user.
  security:
    - coreapi_auth: [ ]
  parameters:
    - in: query
      name: limit
      required: false
      schema:
        type: integer
        default: 50
        maximum: 1000
      description: The number of locations to return. Defaults to 50, with a maximum of 1000.
    - in: query
      name: page
      required: false
      schema:
        type: integer
        default: 1
      description: The page number to return.
  tags:
    - Enterprises
  responses:
    '200':
      description: A list of locations objects
      content:
        application/json:
          schema:
            allOf:
                  - $ref: '../../schemas/pagination.yml'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          $ref: '../../schemas/enterprise_location.yml'
    '400':
      description: Bad Request
    '401':
      description: Unauthorized
    '403':
      description: Forbidden
      content:
        application/json:
          schema:
            type: object
            properties:
              error:
                type: string
                enum: [ 'Forbidden: No Corporate ID associated with this account' ]
    '500':
          description: Internal server error
