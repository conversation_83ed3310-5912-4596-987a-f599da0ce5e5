type: object
properties:
  id:
    type: string
    example: discount-id-1
  name:
    type: string
    example: 10% Off Special
  promo_code:
    $ref: './calculate_taxes_applied_promo_code.yml'
  num_cycles:
    type: number
    description: The number of cycles the discount applies for (against a membership subscription).
  rate_type:
    type: string
    description: The type of discount. This can be either percentage or fixed
  rate_value:
    type: number
    description: |
      Based on the rate_type, this is either the percentage value or the fixed amount of the discount.
      - for percentage, this is the standard percentage value (0.001% - 100.000%) of the discount (e.g., 12.34 represents 12.34%)
      - for fixed, this is the fixed amount of the discount. Represented as a decimal value (e.g., 12.34 represents $12.34)
    example: 10
  discount_amount:
    type: number
    description: The amount that has been discounted.
    example: 0.4
required:
  - rate_type
