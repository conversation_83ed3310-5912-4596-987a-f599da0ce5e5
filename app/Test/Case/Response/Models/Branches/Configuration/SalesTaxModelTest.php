<?php

declare(strict_types=1);

App::import('Test/Case', 'GlofoxTestCase');

use Glofox\Response\Models\Branches\Configuration\SalesTaxModel;

/**
 * Test cases
 * 1 Model contains all fields with data
 * 2 Missing fields and returns correct structure
 * 3 Additional fields and return correct structure
 * 4 Empty array fields and return correct structure.
 *
 * Class SalesTaxModel
 */
class SalesTaxModelTest extends GlofoxTestCase
{
    /**
     * @dataProvider modelProvider
     */
    public function testModel($dataSent, $result): void
    {
        $model = new SalesTaxModel($dataSent);
        $this->assertEquals($model, $result);
    }

    public function modelProvider(): array
    {
        return [
            '1 Model contains all fields with data' => [
                [
                    'legal_name' => 'ACME Corp',
                    'tax_number' => '123abc456',
                    'company_id' => 'Comp123',
                ],
                new SalesTaxModel(
                    [
                        'legal_name' => 'ACME Corp',
                        'tax_number' => '123abc456',
                        'company_id' => 'Comp123',
                    ]
                ),
            ],
            '2 Missing fields and returns correct structure' => [
                [
                    // legal_name and company_id missing
                    'tax_number' => '123abc456',

                ],
                new SalesTaxModel(
                    [
                        'legal_name' => '',
                        'company_id' => '',
                        'tax_number' => '123abc456',
                    ]
                ),
            ],
            '3 Additional fields and return correct structure' => [
                [
                    'other' => '', // additional
                    'legal_name' => 'ACME Corp',
                    'tax_number' => '123abc456',
                    'company_id' => 'Comp123',

                ],
                new SalesTaxModel(
                    [
                        'legal_name' => 'ACME Corp',
                        'tax_number' => '123abc456',
                        'company_id' => 'Comp123',

                    ]
                ),
            ],
            '4 Empty array fields and return correct structure' => [
                [],
                new SalesTaxModel(
                    [
                        'legal_name' => '',
                        'tax_number' => '',
                        'company_id' => '',
                    ]
                ),
            ],
        ];
    }
}
