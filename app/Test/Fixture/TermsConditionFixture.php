<?php

class TermsConditionFixture extends CakeTestFixture
{
    public $import = ['model' => 'TermsCondition'];

    public function init()
    {
        $this->records = [
            [
                '_id' => new MongoId('54259f18d7b6ddd9a25c9b2a'),
                'namespace' => 'glofox',
                'branch_id' => '49a7011a05c677b9a916612a',
                'type' => 'WAIVER',
                'content' => '<p>Fancy terms</p>',
            ],
            [
                '_id' => new MongoId('5ba2e879d441d76dcfa3e4bb'),
                'namespace' => 'glofox',
                'branch_id' => '49a7011a05c677b9a916612a',
                'type' => 'MEMBERPURCHASE',
                'content' => '<p>Fancy terms2</p>',
            ],
        ];
        parent::init();
    }
}
