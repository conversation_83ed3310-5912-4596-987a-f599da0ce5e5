service_name       = "apiworker-batch-service"
image_name         = "apiworker"
component_name     = "apiworker-batch"
container_port     = 80
max_capacity       = 1
containers_count   = 1
ecs_network_mode   = "awsvpc"
ecs_service_enable = 0
container_memory   = 1024
container_cpu      = 512

accessible_s3_buckets_arn = ["arn:aws:s3:::glofox"]
accessible_sqs_queues_arn = ["arn:aws:sqs:eu-west-1:108272622497:api-stg"]
accessible_sns_topics_arn = ["arn:aws:sns:eu-west-1:108272622497:glofox-events-topic-stg", "arn:aws:sns:eu-west-1:108272622497:glofox-communications-commands-topic-stg"]
accessible_aws_services   = ["rekognition"]

batch_job_config = {
  enabled = true
  schedules = [
    {
      name       = "event-generator-eu"
      command    = "/bin/cronjobs,eu"
      cron       = "cron(30 2 * * ? *)"
      is_enabled = true
    },
    {
      name       = "event-generator-ap"
      command    = "/bin/cronjobs,ap"
      cron       = "cron(0 3 * * ? *)"
      is_enabled = true
    },
    {
      name       = "event-generator-us"
      command    = "/bin/cronjobs,us"
      cron       = "cron(30 3 * * ? *)"
      is_enabled = true
    },
    {
      name       = "add-user-id"
      command    = "app/Console/cake,AddUserId"
      cron       = "cron(0/30 * * * ? *)"
      is_enabled = true
    },
    {
      name       = "reservation-processor"
      command    = "app/Console/cake,ReservationProcessor"
      cron       = "cron(0 10 * * ? *)"
      is_enabled = true
    },
    {
        name = "booking-spot-assigner"
        command = "app/Console/cake,BookingSpotAutoAssigner"
        cron = "cron(0/30 * * * ? *)"
        is_enabled = true
    }
  ]
}

env_vars = {
  GLOFOX_DATABASE_NAME                 = "staging",
  GLOFOX_DATABASE_SSL                  = "1",
  GLOFOX_DATABASE_USERNAME             = "api-service-non-plat",
  GLOFOX_DATABASE_REPLICASET_NAME      = "atlas-3x71ov-shard-0",
  GLOFOX_DATABASE_PORT                 = "27017",
  GLOFOX_DATABASE_USE_ENVIRONMENT      = "1",
  GLOFOX_DATABASE_READ_PREFERENCE      = "nearest",
  NEW_RELIC_APP_NAME                   = "Platform-Dev",
  NEW_RELIC_ENABLED                    = false,
  NEW_RELIC_LICENCE                    = "",
  REGION                               = "eu-west-1",
  SERVER                               = "staging",
  SERVICE_NAME                         = "apiworker-default",
  PHP_XDEBUG_ENABLE                    = "0",
  GLOFOX_DEBUG_LEVEL                   = "0",
  OPENSHIFT_APP_NAME                   = "staging",
  PHP_OPCACHE_ENABLE                   = "1",
  GLOFOX_ROOT_DOMAIN                   = "https://staging.glofox.com"
  GLOFOX_AWS_S3_BUCKET                 = "glofox",
  GLOFOX_AWS_S3_HOST                   = "s3.amazonaws.com",
  GLOFOX_GRPC_API_CLIENT_HOST          = "payments-api.stg.service.private:44440",
  GLOFOX_GRPC_ACCOUNT_CLIENT_HOST      = "payments-account.stg.service.private:44441",
  GLOFOX_GRPC_TRANSACTION_CLIENT_HOST  = "payments-transaction.stg.service.private:44442",
  GLOFOX_GRPC_SUBSCRIPTION_CLIENT_HOST = "payments-subscription.stg.service.private:44443",
  GLOFOX_WEB_COMPONENT_HTTP_HOST       = "https://payments-web-service-stg.aws.glofox.com",
  GLOFOX_PUSHER_URL                    = "http://pusher-service-stg.aws.glofox.com/",
  GLOFOX_PUSHER_URL_NEW                = "http://push-notifications.stg.service.private/",
  GLOFOX_SENTRY_URL                    = "https://<EMAIL>/1268734"
  GLOFOX_ASYNC_REGION                  = "eu-west-1"
  GLOFOX_SNS_NAME                      = "glofox-events-topic-stg"
  GLOFOX_MEMBERSHIPS_API_URL           = "http://memberships.stg.service.private:8080"
  GLOFOX_WALLETS_API_URL               = "http://memberships.stg.service.private:8080"
  GLOFOX_TAX_API_URL                   = "http://tax.stg.service.private:80"
  GLOFOX_SQS_API_QUEUE_URL             = "https://sqs.eu-west-1.amazonaws.com/108272622497/api-stg"
  GLOFOX_SNS_REGION                    = "eu-west-1"
  GLOFOX_REDIS_CLUSTER_ENABLED         = "1"
  GLOFOX_EXPERIMENTS_API_URL           = "https://iris-stg.aws.glofox.com"
  LIVE_STREAM_SERVICE_URL              = "https://stg-live.glofox.com/v3"
  LIVE_STREAM_INSTALLER_URL            = "https://api.internal-gateway.stg.service.private/live-stream-installer"
  ELECTRONIC_AGREEMENTS_SERVICE_URL    = "https://api.internal-gateway.stg.service.private"
  REPORTS_SERVICE_URL                  = "http://reports.stg.service.private:8080"
  INVOICE_SERVICE_URL                  = "http://invoice.stg.service.private:80/invoice"
  RECEIPTS_SERVICE_URL                 = "http://receipts.stg.service.private:80/receipts-api"
  SALES_TAX_SERVICE_URL                = "http://sales-tax.stg.service.private:80/sales-tax"
  ADDON_SERVICE_URL                    = "http://services.stg.service.private:8083/services-api"
  COMMUNICATIONS_INTERNAL_SERVICE_URL  = "http://communications.stg.service.private:80/communications-internal"
  CART_SERVICE_URL                     = "http://cart.stg.service.private:80"
  SENDGRID_USERNAME                    = "apikey"
  PASSWORD_HASH_COST                   = "10"
  EAGREEMENT_MUST_SIGN_DATE            = "2021-05-20 00:00:01"
  PASSWORD_RESET_TOKEN_VALIDITY        = "60"
  PASSWORD_RESET_TOKEN_SIZE            = "24"
  LAUNCH_DARKLY_API_URL                = "https://app.launchdarkly.com/api/"
  AUTHENTICATION_SERVICE_URL           = "https://auth-stg.aws.glofox.com/"
  HONEYCOMB_DATASET_NAME               = "glofox-stg"
  HONEYCOMB_BASE_URL                   = "https://api.honeycomb.io"
  ENVIRONMENT_SHORT_NAME               = "stg"
  GOOGLE_RECAPTCHA_BASE_URL            = "https://www.google.com/recaptcha/api"
  COMMUNICATIONS_SERVICE_TOPIC         = "arn:aws:sns:eu-west-1:108272622497:glofox-communications-commands-topic-stg"
  DELAY_TASK_SLEEP_TIME_MICROSEC       = 100000
}
secret_vars = {
  GLOFOX_DATABASE_HOST                                = "glofox/apiworker/stg/GLOFOX_DATABASE_HOST"
  GLOFOX_DATABASE_PASSWORD                            = "glofox/apiworker/stg/GLOFOX_DATABASE_PASSWORD"
  GLOFOX_DATABASE_FOR_AUTHENTICATION                  = "glofox/apiworker/stg/GLOFOX_DATABASE_FOR_AUTHENTICATION"
  GLOFOX_STRIPE_CUSTOM_EU_PUBLISHABLE_KEY             = "glofox/apiworker/stg/GLOFOX_STRIPE_CUSTOM_EU_PUBLISHABLE_KEY"
  GLOFOX_STRIPE_CUSTOM_US_PUBLISHABLE_KEY             = "glofox/apiworker/stg/GLOFOX_STRIPE_CUSTOM_US_PUBLISHABLE_KEY"
  GLOFOX_STRIPE_CUSTOM_US_LIFT_BRANDS_PUBLISHABLE_KEY = "glofox/apiworker/stg/GLOFOX_STRIPE_CUSTOM_US_LIFT_BRANDS_PUBLISHABLE_KEY"
  GLOFOX_STRIPE_CUSTOM_UK_LIFT_BRANDS_PUBLISHABLE_KEY = "glofox/apiworker/stg/GLOFOX_STRIPE_CUSTOM_UK_LIFT_BRANDS_PUBLISHABLE_KEY"
  GLOFOX_ANDROID_GCM_KEY                              = "glofox/apiworker/stg/GLOFOX_ANDROID_GCM_KEY"
  GLOFOX_IOS_APNS_KEY                                 = "glofox/apiworker/stg/GLOFOX_IOS_APNS_KEY"
  GLOFOX_AWS_S3_ACCESS_KEY                            = "glofox/apiworker/stg/GLOFOX_AWS_S3_ACCESS_KEY"
  GLOFOX_AWS_S3_SECRET_KEY                            = "glofox/apiworker/stg/GLOFOX_AWS_S3_SECRET_KEY"
  GLOFOX_WEB_COMPONENT_SECRET                         = "glofox/apiworker/stg/GLOFOX_WEB_COMPONENT_SECRET"
  PUSHER_AUTH_KEY                                     = "glofox/apiworker/stg/PUSHER_AUTH_KEY"
  GLOFOX_INTEGRATIONS_CLASSPASS                       = "glofox/apiworker/stg/GLOFOX_INTEGRATIONS_CLASSPASS"
  GLOFOX_ASYNC_ACCOUNT_ID                             = "glofox/apiworker/stg/GLOFOX_ASYNC_ACCOUNT_ID"
  GLOFOX_REDIS_URL                                    = "glofox/apiworker/stg/GLOFOX_REDIS_URL"
  GLOFOX_EXPERIMENTS_API_KEY                          = "glofox/apiworker/stg/GLOFOX_EXPERIMENTS_API_KEY"
  LIVE_STREAM_SERVICE_SALT                            = "glofox/apiworker/stg/LIVE_STREAM_SERVICE_SALT"
  GLOFOX_MIXPANEL_TOKEN                               = "glofox/apiworker/stg/GLOFOX_MIXPANEL_TOKEN"
  JWT_SALT_GENERATE                                   = "glofox/apiworker/stg/JWT_SALT_GENERATE"
  JWT_SALT_VERIFY                                     = "glofox/apiworker/stg/JWT_SALT_VERIFY"
  URL_SIGNATURE_KEY                                   = "glofox/apiworker/dev/URL_SIGNATURE_KEY"
  SENDGRID_PASSWORD                                   = "glofox/apiworker/stg/SENDGRID_PASSWORD"
  ADDON_SERVICE_API_TOKEN                             = "glofox/apiworker/stg/ADDON_SERVICE_API_TOKEN"
  LAUNCH_DARKLY_API_TOKEN                             = "glofox/apiworker/stg/LAUNCH_DARKLY_API_TOKEN"
  GLOFOX_MAILCHIMP_WEBHOOK_SECRET_KEY                 = "glofox/apiworker/stg/GLOFOX_MAILCHIMP_WEBHOOK_SECRET_KEY"
  GLOFOX_TWILIO_WEBHOOK_SECRET_KEY                    = "glofox/apiworker/stg/GLOFOX_TWILIO_WEBHOOK_SECRET_KEY"
  LAUNCH_DARKLY_SDK_KEY                               = "glofox/apiworker/stg/LAUNCH_DARKLY_SDK_KEY"
  GLOFOX_ZENDESK_TOKEN                                = "glofox/apiworker/stg/GLOFOX_ZENDESK_TOKEN"
  GLOFOX_ZENDESK_USERNAME                             = "glofox/apiworker/stg/GLOFOX_ZENDESK_USERNAME"
  HONEYCOMB_API_KEY                                   = "glofox/HONEYCOMB_APIKEY"
  GOOGLE_RECAPTCHA_SECRET                             = "glofox/recaptcha_service_account_key"
  RECEIPTS_HTTP_API_KEY                               = "receipts/stg/HTTP_API_KEY"
  APPLE_DEV_KEY                                       = "glofox/standalone-apps/APPLE_DEV_KEY"
  COMMUNICATIONS_HTTP_API_KEY                         = "communications/stg/HTTP_API_KEY"
}
