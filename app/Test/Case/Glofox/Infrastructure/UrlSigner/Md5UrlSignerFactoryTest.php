<?php

namespace CakeTestCases\Glofox\Infrastructure\UrlSigner;

use Glofox\Domain\Users\Models\User;
use Glofox\Infrastructure\UrlSigner\Md5UrlSigner;
use Glofox\Infrastructure\UrlSigner\Md5UrlSignerFactory;
use Spa<PERSON>\UrlSigner\MD5UrlSigner as SpatieMD5UrlSigner;

\App::import('Test/Case', 'GlofoxTestCase');

class Md5UrlSignerFactoryTest extends \GlofoxTestCase
{
    private string $mockedSignatureKey = 'MOCKED-SIGNATURE-KEY';

    public function test_it_can_be_built_by_the_container(): void
    {
        $instance = app()->make(Md5UrlSignerFactory::class);

        $this->assertTrue($instance instanceof Md5UrlSignerFactory);
    }

    public function test_it_generates_an_user_id_based_signature_key(): void
    {
        $userMock = new User([
            '_id' => '59a7011a05c677bda512212a'
        ]);

        $factory = new Md5UrlSignerFactory($this->mockedSignatureKey);

        $signer = $factory->make($userMock);

        $property = new \ReflectionProperty(Md5UrlSigner::class, 'signer');
        $property->setAccessible(true);
        $signerEngine = $property->getValue($signer);

        $property = new \ReflectionProperty(SpatieMD5UrlSigner::class, 'signatureKey');
        $property->setAccessible(true);
        $generatedSignatureKey = $property->getValue($signerEngine);

        self::assertEquals('MOCKED-SIGNATURE-KEY::59a7011a05c677bda512212a', $generatedSignatureKey);
    }
}