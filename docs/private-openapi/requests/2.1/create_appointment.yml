type: object
properties:
  name:
    type: string
  description:
    type: string
  pricing:
    type: string
    enum:
      - SINGLE
      - FREE
      - CUSTOM
  time_slot_length:
    type: integer
  staff_ids:
    type: array
    items:
      type: string
      description: Staff id
  allowed_member_types:
    type: array
    items:
      type: object
      properties:
        price:
          type: number
        type:
          type: string
          enum:
            - payg
            - member
        membership_id:
          type: string
        memberships:
          type: array
          items:
            type: object
            properties:
              id:
                type: string
              label:
                type: string
      required:
        - price
        - type
  categories:
    type: array
    items:
      type: string
      description: Category id
  private:
    type: boolean
    items:
      type: boolean
      description: Private toggle
      nullable: true
  size:
    type: integer
    maximum: 5
    default: 1
    description: This will set the capacity of the appointment to a maximum of five. If this field is not sent, the
      default value of one will be used.
required:
  - name
  - description
  - pricing
  - time_slot_length
  - staff_ids