<?php

use Glofox\Domain\Bookings\Repositories\BookingsRepository;
use Glofox\Domain\Bookings\Status;
use Glofox\Domain\Branches\Repositories\BranchesRepository;
use Glofox\Domain\Courses\Repositories\CoursesRepository;
use Glofox\Domain\Events\Repositories\EventsRepository;
use Glofox\Domain\TimeSlots\Repositories\TimeSlotRepository;

App::uses('AppController', 'Controller');

/**
 * Class AttendancesController.
 */
class AttendancesController extends AppController
{
    /**
     * @var string
     */
    public $name = 'Attendances';
    /**
     * @var array
     */
    public $components = [
        'JWT',
    ];
    private array $allowedModels = [
        'bookings' => BookingsRepository::class,
        'events' => EventsRepository::class,
        'timeslots' => TimeSlotRepository::class,
        'courses' => CoursesRepository::class,
    ];
    /**
     * @var TimeSlotRepository|EventsRepository|CoursesRepository
     */
    private $originRepository;
    /**
     * @var BookingsRepository
     */
    private $bookingsRepository;
    /**
     * @var BranchesController
     */
    private $branchRepository;
    /**
     * @var User
     */
    private $userModel;
    /**
     * @var Booking
     */
    private $bookingModel;
    /**
     * @var string
     */
    private $branchTimezone = 'UTC';

    /**
     * AttendancesController constructor.
     *
     * @param null $request
     * @param null $response
     */
    public function __construct($request = null, $response = null)
    {
        parent::__construct($request, $response);

        $this->bookingsRepository = app()->make(BookingsRepository::class);
        $this->branchRepository = new BranchesRepository();

        $this->userModel = ClassRegistry::init('User');
        $this->bookingModel = ClassRegistry::init('Booking');
    }

    public function save()
    {
        $this->validateRequest();
        $this->setBranchTimezone();
        $payload = $this->getPayload();

        // Set up the injector in the provider
        $this->bookingModel->setUpInjector([], null, $this->getUser());

        // Locking up the attendance should not modify the bookings
        if (!empty($payload['attendance_submitted'])) {
            $this->lockAttendance();

            return json_encode([
                'success' => true,
            ]);
        }

        $bookings = $this->saveAttendance();
        $bookings = array_map([$this->bookingModel, 'mapObject'], $bookings);

        return json_encode([
            'success' => true,
            'bookings' => $bookings,
        ], JSON_PARTIAL_OUTPUT_ON_ERROR);
    }

    /**
     * @return array|null
     */
    private function saveAttendance()
    {
        $payload = $this->getPayload();
        $bookings = [];
        $modelIDs = $payload['model_ids'];

        $this->originRepository = app()->make($this->allowedModels[$payload['model']]);

        // If you are marking attendance only based on bookings
        if (!$this->isFullAttendance()) {
            $bookings = $this->bookingsRepository->findByIDs($modelIDs);
            $bookings = $this->setBookingsAttendance($bookings);

            return $bookings;
        }

        // If you are marking attendance based on the parent event
        $bookingOrigins = $this->originRepository->findByIDs($modelIDs);

        foreach ($bookingOrigins as $origin) {
            $alias = array_keys($origin)[0];
            $bookings = $this->bookingsRepository->findByEventID($origin[$alias]['_id']);
            $this->setBookingsAttendance($bookings, true);
        }

        return $bookings;
    }

    /**
     * Set the attendance to its opposite value
     * The alias inside each booking is expected.
     *
     * @param array $bookings
     * @param bool $forceAttended
     *
     * @return array
     */
    private function setBookingsAttendance(array $bookings, $forceAttended = false)
    {
        $bookings = Hash::extract($bookings, '{n}.Booking');

        foreach ($bookings as &$booking) {
            $isNotBooked = $booking['status'] !== 'BOOKED';

            if ($isNotBooked) {
                continue;
            }

            $booking['attended'] = $forceAttended ?: empty($booking['attended']);
        }

        $this->bookingsRepository->saveMany($bookings);

        return $bookings;
    }

    /**
     * TODO: Replace with the request validators from the arch improvements.
     *
     * @throws UnsuccessfulOperation
     */
    private function validateRequest()
    {
        $user = $this->getUser();
        $payload = $this->getPayload();

        if (!in_array($user['type'], UserType::ADMINISTRATORS)) {
            throw new UnauthorizedException('You are not allowed to use this resource');
        }

        if (empty($payload['model']) || empty($payload['model_ids']) || !is_array($payload['model_ids'])) {
            throw new UnsuccessfulOperation('Invalid model or model_ids');
        }

        if (!isset($this->allowedModels[$payload['model']])) {
            throw new BadRequestException('The model is not implemented');
        }
    }

    /**
     * If the request model is not `bookings`, then
     * the behaviour is to set the whole class attendance.
     *
     * @return bool
     */
    private function isFullAttendance()
    {
        return !$this->originRepository instanceof BookingsRepository;
    }

    private function hasAttendanceAlreadyBeenSubmitted(array $record): void
    {
        if (!empty($record['attendance_submitted'])) {
            $message = 'SUBMIT_ATTENDANCE_ALREADY_SUBMIT_ERROR';
            throw new UnsuccessfulOperation($message);
        }
    }

    private function lockAttendance()
    {
        $payload = $this->getPayload();

        $this->originRepository = app()->make($this->allowedModels[$payload['model']]);

        $bookingOrigins = $this->originRepository->findByIDs($payload['model_ids']);

        foreach ($bookingOrigins as &$origin) {
            $alias = array_keys($origin)[0];

            $this->hasAttendanceAlreadyBeenSubmitted($origin[$alias]);

            $origin[$alias]['attendance_submitted'] = true;

            $bookings = $this->bookingsRepository->findByEventID($origin[$alias]['_id']);

            $this->setBookingStrikes($bookings);
        }

        unset($origin);

        $this->originRepository->saveMany($bookingOrigins);
    }

    /**
     * @param array $bookings
     */
    private function setBookingStrikes(array $bookings)
    {
        $bookings = Hash::extract($bookings, '{n}.Booking');

        foreach ($bookings as $booking) {
            if (Status::BOOKED != $booking['status']) {
                continue;
            }

            if (empty($booking['attended'])) {
                $this->userModel->incrementStrike($booking);
                continue;
            }

            $this->userModel->decrementStrike($booking);
        }
    }

    private function setBranchTimezone()
    {
        $user = $this->getUser();

        /** @var Branch $branchModel */
        $branchModel = ClassRegistry::init('Branch');

        $branch = $this->branchRepository->findById($user['branch_id']);
        $this->branchTimezone = $branchModel->extractTimezone($branch);
    }
}
