<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\Authorization;

use Glofox\Domain\Authorization\Abilities\BranchAbility;
use Glofox\Domain\Authorization\Abilities\TimeSlotAbility;
use Glofox\Domain\Authorization\AbstractAbility;

\App::import('Test/Case', 'GlofoxTestCase');

class AbstractAbilityTest extends \GlofoxTestCase
{
    public $fixtures = [];

    /** @test */
    public function it_guess_the_ability_class_for_single_word_namespace(): void
    {
        $class = AbstractAbility::guessAbilityClass('branches.foo');
        $this->assertEquals(BranchAbility::class, $class);
    }

    /** @test */
    public function it_guess_the_ability_class_for_multiples_words_namespace(): void
    {
        $class = AbstractAbility::guessAbilityClass('time_slots.foo');
        $this->assertEquals(TimeSlotAbility::class, $class);
    }
}
