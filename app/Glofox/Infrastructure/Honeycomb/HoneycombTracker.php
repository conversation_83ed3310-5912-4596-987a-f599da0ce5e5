<?php

declare(strict_types=1);

namespace Glofox\Infrastructure\Honeycomb;

use GuzzleHttp\Client as GuzzleHttpClient;

class HoneycombTracker
{
    private const HONEYCOMB_TRACT_URL = '1/events/%s';

    private GuzzleHttpClient $client;

    private string $datasetName;

    /**
     * HoneycombTracker constructor.
     *
     * @param GuzzleHttpClient $client
     */
    public function __construct(GuzzleHttpClient $client, string $datasetName)
    {
        $this->client = $client;
        $this->datasetName = $datasetName;
    }

    public function track(array $data)
    {
        $this->client->post(
            sprintf(self::HONEYCOMB_TRACT_URL, $this->datasetName),
            [
                'body' =>json_encode($data, JSON_PARTIAL_OUTPUT_ON_ERROR) ,
            ]
        );
    }
}
