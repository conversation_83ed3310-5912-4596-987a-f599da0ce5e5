<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\Transactional\Filters;

use Glofox\Domain\Branches\Models\Branch;
use Glofox\Domain\Branches\Repositories\BranchesRepository;
use Glofox\Domain\Transactional\Filters\OnlineBookingConfirmationTemplateFilter;
use Glofox\Infrastructure\Flags\FlaggerInterface;

\App::import('Test/Case', 'GlofoxTestCase');

class OnlineBookingConfirmationTemplateFilterTest extends \GlofoxTestCase
{
    public $fixtures = [];
    /** @var FlaggerInterface */
    private $flagger;

    /** @var BranchesRepository */
    private $branchesRepository;

    public function setUp()
    {
        parent::setUp();

        $this->flagger = \Mockery::mock(FlaggerInterface::class);
        $this->branchesRepository = \Mockery::mock(BranchesRepository::class);
    }
    
    public function tearDown()
    {
        parent::tearDown();
        
        \Mockery::close();
    }


    public function test_it_sends_online_message_if_glofox_digital_true(): void
    {
        $this->branchesRepository->shouldReceive('addCriteria')->andReturnSelf();
        $this->branchesRepository->shouldReceive('firstOrFail')->andReturn(
            new Branch(
                ['_id' => '49a7011a05c677b9a9166111']
            )
        );
        $this->flagger->shouldReceive('has')->andReturnTrue();
        $filter = $this->createFilter();

        $result = $filter->execute([
            ['identifier' => 'NOT_ONLINE_CLASS_BOOKING_CONFIRMATION'],
            ['identifier' => 'ONLINE_CLASS_BOOKING_CONFIRMATION'],
        ], '49a7011a05c677b9a9166111');

        self::assertEquals(2, \count($result));
    }

    public function test_it_does_not_send_online_message_if_glofox_digital_false(): void
    {
        $this->branchesRepository->shouldReceive('addCriteria')->andReturnSelf();
        $this->branchesRepository->shouldReceive('firstOrFail')->andReturn(
            new Branch(
                ['_id' => '49a7011a05c677b9a9166111']
            )
        );
        $this->flagger->shouldReceive('has')->andReturnFalse();
        $filter = $this->createFilter();

        $result = $filter->execute([
            ['identifier' => 'NOT_ONLINE_CLASS_BOOKING_CONFIRMATION'],
            ['identifier' => 'ONLINE_CLASS_BOOKING_CONFIRMATION'],
        ], '49a7011a05c677b9a9166111');

        self::assertEquals(1, \count($result));
    }

    private function createFilter(): OnlineBookingConfirmationTemplateFilter
    {
        return new OnlineBookingConfirmationTemplateFilter(
            $this->flagger,
            $this->branchesRepository
        );
    }
}
