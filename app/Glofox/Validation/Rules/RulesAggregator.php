<?php

namespace Glofox\Validation\Rules;

use Glofox\Traversable\AlwaysAccessibleCollection;
use Illuminate\Support\Collection;
use Glofox\Domain\Branches\Validation\Rules\BranchId as BranchIdRule;
use Glofox\Domain\Clients\Validation\Rules\ClientNamespace as ClientNamespaceRule;

abstract class RulesAggregator
{
    private \Glofox\Validation\Rules\RulesCollection $rules;

    /**
     * @var AlwaysAccessibleCollection
     */
    protected $data;

    private array $messages = [];

    /**
     * RuleSet constructor.
     *
     * @param array $data
     */
    public function __construct(array $data = [])
    {
        $this->rules = new RulesCollection($data);

        $this->boot();
    }

    /**
     * @return RulesCollection
     */
    public function rules()
    {
        return $this->rules;
    }

    public function messages()
    {
        return $this->messages;
    }

    public function keys()
    {
        return $this->rules()->keys();
    }

    public function toArray()
    {
        return $this->rules()->toArray();
    }

    public function withBranchIdRule()
    {
        $this->rules()->put('branch_id', ( new BranchIdRule() )->get()->required());

        return $this;
    }

    public function withNamespaceRule()
    {
        $this->rules()->put('namespace', ( new ClientNamespaceRule() )->get()->required());

        return $this;
    }

    abstract protected function boot();
}
