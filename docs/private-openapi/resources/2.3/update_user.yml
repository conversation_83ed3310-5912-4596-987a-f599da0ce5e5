post:
  summary: ''
  operationId: user-restore
  tags:
    - Users
  description: Restores a deleted user
  parameters:
    - schema:
        type: string
        pattern: '^[a-f\d]{24}$'
      name: branchId
      in: path
      required: true
    - schema:
        type: string
        pattern: '^[a-f\d]{24}$'
      name: userId
      in: path
      required: true
  responses:
    '204':
      description: No Content
    '404':
      description: Not Found
      content:
        application/json:
          schema:
            $ref: "../../schemas/error.yml"
    '400':
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: "../../schemas/error.yml"
    '403':
      description: Forbidden
      content:
        application/json:
          schema:
            $ref: "../../schemas/error.yml"
