<?php

use Carbon\Carbon;
use Glofox\AuditLog\Resolvers\OriginByRequestResolver;
use Glofox\Datasource\MongoCollection as GlofoxMongoCollection;
use Glofox\Date\DateTransformer;
use Glofox\Date\Model\RangeDateTime;
use Glofox\Date\RangeDateTimeTransformer;
use Glofox\Domain\Appointments\Search\Expressions\PrivateField;
use Glofox\Domain\AppointmentSlots\Exceptions\PrivateAppointmentSlotCanNotBeDisplayedException;
use Glofox\Domain\AppointmentSlots\Http\Transformers\VirtualAppointmentSlotTransformer;
use Glofox\Domain\AppointmentSlots\UseCase\GetVirtualAppointmentSlots;
use Glofox\Domain\AppointmentSlots\Validation\Validators\CanDisplayPrivateAppointmentSlotValidator;
use Glofox\Domain\AsyncEvents\Events\EventCreatedEventMeta;
use Glofox\Domain\AsyncEvents\Events\EventCreatedEventPayload;
use Glofox\Domain\Authentication\Auth;
use Glofox\Domain\Bookings\Internal\Parameters\UserMembershipValidParameters;
use Glofox\Domain\Bookings\Status as BookingStatus;
use Glofox\Domain\Branches\Exceptions\BranchNotFoundException;
use Glofox\Domain\Branches\Models\Branch;
use Glofox\Domain\Branches\Repositories\BranchesRepository;
use Glofox\Domain\Branches\Search\Expressions\BranchId;
use Glofox\Domain\Events\Cancellation\EventCancellatorInterface;
use Glofox\Domain\Events\Events\EventHadDateChanged;
use Glofox\Domain\Events\Events\EventWasDeleted;
use Glofox\Domain\Events\Exceptions\EventNotFoundException;
use Glofox\Domain\Events\Internal\Parameters\GetStatusParameters;
use Glofox\Domain\Events\Internal\Parameters\PricingForUserParameters;
use Glofox\Domain\Events\Internal\Parameters\SetCalculatedFieldsParameters;
use Glofox\Domain\Events\MemberEligibility;
use Glofox\Domain\Events\Models\Event as EventModel;
use Glofox\Domain\Events\Repositories\EventsRepository;
use Glofox\Domain\Events\Services\EventsPublisher;
use Glofox\Domain\Events\Status as EventStatus;
use Glofox\Domain\Events\Transformers\AddVirtualOnlineFieldTransformer;
use Glofox\Domain\Events\Validation\EventIsOnlineValidatorInterface;
use Glofox\Domain\Facilities\Repositories\FacilitiesRepository;
use Glofox\Domain\Integrations\Identifier;
use Glofox\Domain\Memberships\Models\Addon;
use Glofox\Domain\Memberships\Services\AddonEligibilityServiceInterface;
use Glofox\Domain\Pricing\Models\Builders\PricingBuilder;
use Glofox\Domain\Pricing\Models\Pricing;
use Glofox\Domain\Pricing\Models\PricingWrapper;
use Glofox\Domain\Pricing\Strategies\Factory as PricingResolverFactory;
use Glofox\Domain\Programs\Exceptions\ProgramNotFoundException;
use Glofox\Domain\Programs\Repositories\ProgramsRepository;
use Glofox\Domain\TimeSlotPatterns\Repositories\TimeSlotPatternsRepository;
use Glofox\Domain\TimeSlots\ModelList as TimeSlotModelList;
use Glofox\Domain\TimeSlots\Models\TimeSlot;
use Glofox\Domain\TimeSlots\Repositories\TimeSlotRepository;
use Glofox\Domain\Users\Exceptions\UserNotFoundException;
use Glofox\Domain\Users\Models\User;
use Glofox\Domain\Users\Repositories\UsersRepository;
use Glofox\Domain\Users\Search\Filters\IsStaff;
use Glofox\Domain\Users\Services\StrikeValidationServiceInterface;
use Glofox\Events\EventManager;
use Glofox\Http\Source;
use Glofox\Repositories\Search\Expressions\Shared\Active;
use Glofox\Repositories\Search\Expressions\Shared\FieldExists;
use Glofox\Repositories\Search\Expressions\Shared\FieldLessThanOrEqual;
use Glofox\Repositories\Search\Expressions\Shared\Id;
use Glofox\Repositories\Search\Expressions\Shared\InNamespaces;
use Glofox\Response\Transformers\EventsTransformer;
use Glofox\Traversable\AlwaysAccessibleCollection;
use Illuminate\Support\Collection;
use Psr\Log\LoggerInterface;

class Event extends AppModel
{
    public $primaryKey = '_id';
    public $mongoSchema = [
        'branch_id' => ['type' => 'string'],
        'namespace' => ['type' => 'string'],
        'name' => ['type' => 'string'],
        'description' => ['type' => 'string'],
        'program_id' => ['type' => 'string'],
        'schedule_code' => ['type' => 'string'],
        'facility' => ['type' => 'string'],
        'level' => ['type' => 'string'],
        'external_provider_stream_url' => ['type' => 'string'],
        'week_day' => ['type' => 'integer'],
        'size' => ['type' => 'integer'],
        'total_bookings' => ['type' => 'integer'],
        'total_waitings' => ['type' => 'integer'],
        'duration' => ['type' => 'integer'],
        'active' => ['type' => 'boolean'],
        'private' => ['type' => 'boolean'],
        'featured' => ['type' => 'boolean'],
        'new' => ['type' => 'boolean'],
        'attendance_submitted' => ['type' => 'boolean'],
        'manually_deleted' => ['type' => 'boolean'],
        'manually_created' => ['type' => 'boolean'],
        'trainers' => ['type' => 'array'],
        'date' => ['type' => 'date'],
        'time_start' => ['type' => 'datetime'],
        'time_finish' => ['type' => 'datetime'],

        'created' => ['type' => 'datetime'],
        'modified' => ['type' => 'datetime'],

        // Shard Keys
        'region' => ['type' => 'string'],
        'timestamp' => ['type' => 'timestamp'],

        // Calculated fields
        'open_booking_time' => ['type' => 'datetime'],
        'close_booking_time' => ['type' => 'datetime'],
        'status' => ['type' => 'string'],
    ];
    public $parentProgram;
    private array $bookMap = [];

    public function find($type = 'first', $query = [])
    {
        if (array_key_exists('id', $query)) {
            $logger = app()->make(LoggerInterface::class);
            $logger->info('Event.find filter by id', debug_backtrace(DEBUG_BACKTRACE_PROVIDE_OBJECT, 5));
        }

        return parent::find($type, $query);
    }

    /**
     * @throws \Glofox\Exception
     * @throws JsonException
     * @throws Exception
     */
    public function getAll($params)
    {
        $source = [];
        $total = 0;

        //Retrieve variables
        $injector = $this->injector;
        $page = $injector->getPage();
        $limit = $injector->getLimit();
        $filter = $injector->getFilters();
        $user = $this->getUser();

        $branchesRepo = app()->make(BranchesRepository::class);
        $branch = $branchesRepo->getById($user['branch_id']);
        $timezone = $branch->timezone()->getName();
        $features = AlwaysAccessibleCollection::make($branch->features());

        $user = Auth::user();
        $additionalCondition = [];
        if ($user->isTrainer()) {
            $additionalCondition['$or'][] = ['trainers' => ['$in' => [$user->id()]]];
            $additionalCondition['$or'][] = ['trainers.id' => ['$in' => [$user->id()]]];
            $additionalCondition['$or'][] = ['model_id' => ['$in' => [$user->id()]], 'model' => 'users'];
            $additionalCondition['$or'][] = ['staff_id' => ['$in' => [$user->id()]], 'model' => 'appointments'];
        }

        if (isset($params['utc_modified_start_date']) && is_numeric($params['utc_modified_start_date'])) {
            $additionalCondition['$and'][] = [
                'modified' => [
                    '$gte' => new \MongoDate(
                        $params['utc_modified_start_date']
                    ),
                ],
            ];
        }
        if (isset($params['utc_modified_end_date']) && is_numeric($params['utc_modified_end_date'])) {
            $additionalCondition['$and'][] = [
                'modified' => [
                    '$lte' => new \MongoDate(
                        $params['utc_modified_end_date']
                    ),
                ],
            ];
        }

        $commonFields = [
            'time_start',
            'time_finish',
            'size',
            'program_id',
            'name',
            'description',
            'facility',
            'trainers',
            'branch_id',
            'time_slot_pattern_id',
            'booked',
            'active',
            'schedule',
            'private',
            'level',
            'active',
            'namespace',
            'modified',
            'model',
            'model_id',
            'staff_id',
        ];

        //Events if no filter or if filter by class -> can be filtered with filter=event
        if (empty($filter) || in_array('event', $filter)) {
            $events = $this->paginate(
                $this->buildPaginatedWithAdditionalConditions($commonFields, $additionalCondition)
            );
            $events['data'] = $this->setCalculatedFieldsToEvents($events['data'], $features, $timezone);

            // Product Decision
            foreach ($events['data'] as &$event) {
                $event['current_user_eligibility'] = MemberEligibility::UNKNOWN;
            }

            $source = array_merge($source, $events['data']);
            $total += $events['total_count'];
        }

        //Timeslots -> can be filtered with filter=timeslot
        if (!empty($filter) && in_array('timeslot', $filter)) {
            $originResolver = app()->make(OriginByRequestResolver::class);
            $requestSource = $originResolver->resolve(Router::getRequest());

            if (
                array_key_exists('model', $params)
                && $params['model'] === TimeSlotModelList::APPOINTMENTS
                && ($requestSource->is(Source::MEMBER_APP()) || $requestSource->is(Source::WEBPORTAL()))
            ) {
                $dateTransformer = new DateTransformer();
                $branchTZ = new DateTimeZone($timezone);
                $staffId = null;
                if (array_key_exists('staff_id', $params)) {
                    $staffId = (string)$params['staff_id'];
                }

                $getVirtualAppointmentSlots = app()->make(GetVirtualAppointmentSlots::class);
                $virtualAppointmentSlots = $getVirtualAppointmentSlots->execute(
                    $branch->id(),
                    $dateTransformer->addBranchTZOffsetToTimestamp((int)$params['start'], $branchTZ),
                    $dateTransformer->addBranchTZOffsetToTimestamp((int)$params['end'], $branchTZ),
                    $staffId
                );
                $timeSlots = VirtualAppointmentSlotTransformer::transformForEvents20(
                    $virtualAppointmentSlots,
                    $branchTZ
                );
                $hasMore = false;
            } else {
                $this->TimeSlot = ClassRegistry::init('TimeSlot');
                $this->TimeSlot->API = $this->API;
                $this->TimeSlot->setUpInjector($params, null, $this->loggedUser);

                $this->filterOutDeactivatedFacilities($additionalCondition, $branch->toLegacy());
                $this->filterOutDeactivatedTrainers($additionalCondition, $branch->toLegacy());

                // remove non-bookable trainers who have timeslots created
                $unBookableTrainers = $this->filterOutUnBookableTrainers($branch->toLegacy());
                if ($unBookableTrainers) {
                    $additionalCondition['model_id']['$nin'] = array_filter(
                        array_unique(
                            array_merge(
                                (array)$additionalCondition['model_id']['$nin'],
                                $unBookableTrainers
                            )
                        )
                    );
                }

                $canDisplayPrivateAppointmentSlotValidator = app()->make(
                    CanDisplayPrivateAppointmentSlotValidator::class
                );
                try {
                    $canDisplayPrivateAppointmentSlotValidator->validate($user);
                } catch (PrivateAppointmentSlotCanNotBeDisplayedException $e) {
                    $this->filterOutPrivateSlots($additionalCondition, $branch);
                }

                $timeSlotsConditions = $this->TimeSlot->buildPaginatedWithAdditionalConditions(
                    $commonFields,
                    $additionalCondition
                );
                $timeSlots = $this->TimeSlot->paginate($timeSlotsConditions);

                // add description, update name
                $timeSlots['data'] = $this->addRelatedModelData($timeSlots['data']);
            }

            $timeSlots['data'] = $this->setCalculatedFieldsToTimeSlots($timeSlots['data'], $features, $timezone);
            $source = array_merge($source, $timeSlots['data']);
            $total += $timeSlots['total_count'];
        }

        //Courses -> can be filtered with filter=course
        if (!empty($filter) && in_array('course', $filter)) {
            $this->Course = ClassRegistry::init('Course');
            $this->Course->API = $this->API;
            $this->Course->setInjector($this->injector);

            $courseConditions = $this->Course->buildPaginatedWithAdditionalConditions(
                $commonFields,
                $additionalCondition
            );

            $courses = $this->Course->sourceList(
                $courseConditions,
                $this->injector->getStart()->sec,
                $this->injector->getEnd()->sec
            );
            $source = array_merge($source, $courses['list']);
            $total += $courses['total'];
        }

        //Merge datasources from events/courses/appointments and sort by get param sort,
        //Sort them by time_start, limit to search
        $content = array_merge($source);
        $parsed = Hash::sort($content, '{n}.time_start', 'asc');

        /** @var AddVirtualOnlineFieldTransformer $onlineFieldTransformer */
        $onlineFieldTransformer = app()->make(AddVirtualOnlineFieldTransformer::class);
        $parsed = $onlineFieldTransformer->execute($parsed);

        //Prepare envelope
        $response = [
            'object' => 'list',
            'page' => $page,
            'limit' => $limit,
            'has_more' => $hasMore ?? $total > ($limit * $page),
            'total_count' => $total,
            'data' => $parsed,
        ];

        $dataTransformer = new EventsTransformer();

        return $dataTransformer->setFromArrayCollection($response);
    }

    /**
     * @param array $timeSlots
     * @return array
     */
    private function addRelatedModelData(array $timeSlots = []): array
    {
        if (empty($timeSlots)) {
            return $timeSlots;
        }

        $models = [];
        foreach ($timeSlots as &$timeSlotData) {
            $timeSlotModel = TimeSlot::make($timeSlotData);

            // stop duplicate calls for the same parent
            $modelId = $timeSlotModel->modelId();
            if (!isset($models[$modelId])) {
                $models[$modelId] = $timeSlotModel->relatedModel();
            }

            // set the description
            $timeSlotModel->set('description', $models[$modelId]->description());
            $timeSlotData['description'] = $timeSlotModel->description();

            // update the name in-case it has changed for appointments
            if (
                $timeSlotModel->model()->is(TimeSlotModelList::APPOINTMENTS) ||
                $timeSlotModel->model()->is(TimeSlotModelList::USERS)
            ) {
                $timeSlotData['name'] = $models[$modelId]->name();
            }
        }

        return $timeSlots;
    }

    public function getById($identifier, $params = [])
    {
        //This method is used by legacy and new code, taking into consideration this next snippet maintains compatibility
        if (empty($this->API)) {
            return $this->find('first', ['conditions' => ['Event._id' => new MongoId($identifier)]]);
        }

        //Retrieve variables
        $conditions = $this->injector->getSingleRecordConditions();

        $conditions = [
            'conditions' => $conditions,
            'fields' => [
                'namespace',
                'time_start',
                'time_finish',
                'size',
                'program_id',
                'name',
                'description',
                'facility',
                'trainers',
                'branch_id',
                'time_slot_pattern_id',
                'booked',
                'allowed_member_types',
                'schedule',
                'active',
                'model_id',
                'model',
                'schedule_code',
                'weekday',
                'created',
                'updated',
                'external_provider_stream_url',
                'staff_id',
                'modified',
                'attendance_submitted',
            ],
        ];

        $user = $this->getUser();
        $this->loadModel('Branch');
        $branch = $this->Branch->findById($user['branch_id']);
        $timezone = $this->Branch->getTimeZone($branch);
        $features = AlwaysAccessibleCollection::make($branch['Branch']['features'] ?? []);
        $authUser = Auth::user();

        //Attempt event search by id
        $event = $this->find('first', $conditions);

        // We need to recalculate duration here if the event ends at the midnight,
        // because we are saving the finish time as the day it starts if it is midnight
        if (!empty($event)) {
            $eventModel = EventModel::make($event['Event']);
            if ($eventModel->timeFinish()->toTimeString() === '00:00:00') {
                $finishTimestamp = $eventModel->timeFinish()->copy()->addDay()->getTimestamp();
                $startTimestamp = $eventModel->timeStart()->getTimestamp();
                $event['Event']['duration'] = abs(($finishTimestamp - $startTimestamp) / 60);
                unset($event['Event']['time_finish']);
            }
        }

        $event = empty($event) ? [] : $this->mapObject($event);

        // it is an event
        if (!empty($event)) {
            $event = $this->setCalculatedFieldsToEvents([$event], $features, $timezone)[0];

            if ($authUser->isGuest() || $authUser->isStaff()) {
                $event['current_user_eligibility'] = MemberEligibility::UNKNOWN;
            } else {
                /** @var UsersRepository $usersRepository */
                $usersRepository = app()->make(UsersRepository::class);

                $eventsRepository = app()->make(EventsRepository::class);

                try {
                    $eventId = $event['_id'];
                    $userId = $user['_id'];

                    // Search existing user
                    $user = $usersRepository->addCriteria(new Id($userId))
                        ->firstOrFail(function () use ($userId) {
                            throw UserNotFoundException::withId($userId);
                        });

                    // Search existing event
                    $eventObj = $eventsRepository->addCriteria(new Id($eventId))
                        ->firstOrFail(function () use ($eventId) {
                            throw EventNotFoundException::withId($eventId);
                        });

                    $user = User::make($user);

                    // This will throw an exception if the user is not eligible.
                    $pricingParams = new PricingForUserParameters(
                        $eventObj,
                        $user,
                        0
                    );
                    $this->pricingForUser($pricingParams);
                    $event['current_user_eligibility'] = MemberEligibility::ELIGIBLE;
                } catch (Exception $e) {
                    $event['current_user_eligibility'] = MemberEligibility::NOT_ELIGIBLE;
                }
            }

            /** @var EventIsOnlineValidatorInterface $validator */
            $validator = app()->make(EventIsOnlineValidatorInterface::class);
            $event['is_online'] = $validator->validate(EventModel::make($event));
        }

        //If not found try timeslot by id
        if (empty($event)) {
            $this->TimeSlot = ClassRegistry::init('TimeSlot');
            $this->TimeSlot->API = $this->API;
            $this->TimeSlot->setUpInjector($params, $identifier, $this->loggedUser);
            $event = $this->TimeSlot->find('first', $conditions);
            $event = empty($event) ? [] : $this->TimeSlot->mapObject($event);
            if (!empty($event)) {
                $event = $this->setCalculatedFieldsToTimeSlots([$event], $features, $timezone)[0];
            }

            // return the trainer image for appointments
            if ($event['model'] === TimeSlotModelList::APPOINTMENTS) {
                $appointmentSlot = TimeSlot::make($event);
                $trainer = $appointmentSlot->staff();
                $event['image_url'] = $trainer->avatar();
            }

            if ($authUser->isGuest() || $authUser->isStaff()) {
                $event['current_user_eligibility'] = MemberEligibility::UNKNOWN;
            } else {
                /** @var UsersRepository $usersRepository */
                $usersRepository = app()->make(UsersRepository::class);

                /** @var TimeSlotRepository $timeSlotRepository */
                $timeSlotRepository = app()->make(TimeSlotRepository::class);

                try {
                    if (empty($event['_id'])) {
                        throw EventNotFoundException::withNullId();
                    }

                    if (empty($user['_id'])) {
                        throw UserNotFoundException::withNullId();
                    }
                    $timeSlotId = $event['_id'];
                    $userId = $user['_id'];

                    // Search existing user
                    $user = $usersRepository->addCriteria(new Id($userId))
                        ->firstOrFail(function () use ($userId) {
                            throw UserNotFoundException::withId($userId);
                        });

                    $user = User::make($user);

                    // Search existing event
                    $timeSlot = $timeSlotRepository->addCriteria(new Id($timeSlotId))
                        ->firstOrFail(function () use ($timeSlotId) {
                            throw EventNotFoundException::withId($timeSlotId);
                        });
                    $timeSlot = TimeSlot::make($timeSlot);

                    $branch = Branch::make($branch['Branch']);

                    $pricingResolverFactory = new PricingResolverFactory();
                    $quantity = 1;
                    $pricingResolver = $pricingResolverFactory->create(
                        $timeSlot->pricingType(),
                        $user,
                        $branch,
                        $quantity
                    );
                    $pricing = $pricingResolver->resolve($timeSlot);

                    // This will throw an exception if the user is not eligible.
                    $event['current_user_eligibility'] = MemberEligibility::ELIGIBLE;
                } catch (Exception $e) {
                    $event['current_user_eligibility'] = MemberEligibility::NOT_ELIGIBLE;
                }
            }
        }

        //If not found course by id
        if (empty($event)) {
            $this->Course = ClassRegistry::init('Course');
            $this->Course->API = $this->API;
            $this->Course->setUpInjector($params, $identifier, $this->loggedUser);
            $event = $this->Course->find('first', $conditions);
            $event = empty($event) ? [] : $this->Course->mapObject($event);
        }

        return $event;
    }

    /**
     * @param string $identifier
     *
     * @return array
     *
     * @throws \Glofox\Datasource\Exceptions\InvalidMongoIdException
     */
    public function deleteById($identifier)
    {
        $eventsRepository = app()->make(EventsRepository::class);
        $eventCancellator = app()->make(EventCancellatorInterface::class);

        $event = $eventsRepository
            ->addCriteria(new Id($identifier))
            ->firstOrFail(function () use ($identifier) {
                throw EventNotFoundException::withId($identifier);
            });

        $event = EventModel::make($event);

        $result = $eventCancellator->cancel($event);

        return [
            'deleted' => (bool)$result,
            'id' => $identifier,
        ];
    }

    /**
     * @param string $identifier [description]
     * @param array $fields [description]
     *
     * @return array
     */
    public function patchById($identifier, $fields)
    {
        $event = $this->getById($identifier);
        $event = array_merge($event, $fields);
        $this->save($event);

        return $this->getById($identifier);
    }

    /**
     * @param array $data [description]
     *
     * @return array
     */
    public function post($data)
    {
        $this->create();

        return $this->save($data)['Event'];
    }

    /**
     * Find events in date range.
     *
     * @param string $branchId The id of the current branch
     * @param string $start Start timestamp
     * @param string $end End timestamp
     * @param string $trainerId The id of the trainer assigned to the event
     *
     * @return array List of bookings
     */
    public function findByDateRangeAndTrainerId(array $branch, int $start, int $end, string $trainerId = null): array
    {
        $dates = [
            '$gte' => new MongoDate($start),
            '$lte' => new MongoDate($end),
        ];

        $conditions = [
            'branch_id' => $branch['_id'],
            'active' => true,
            'time_start' => $dates,
        ];

        if ($trainerId) {
            $conditions['trainers'] = $trainerId;
        }

        $list = $this->find('all', [
            'conditions' => $conditions,
            'order' => [
                'time_start' => 1,
            ],
            'fields' => [
                'name',
                'time_start',
                'time_finish',
                'size',
                'program_id',
                'description',
                'facility',
                'trainers',
                'program_id',
                'private',
                'level',
                'schedule_code',
                'external_provider_stream_url',
            ],
        ]);

        return $list;
    }

    /********************************* EVENT GENERATION ***************************************/

    /*!
     * Events in all schedules of the received program are set for deletion, No need to cancel bookings
     * because in the case there are, people are gonna see they are booked to a cancelled event. Keep track of
     * schedule codes in case you need to REFUND?
     * @example    https://www.glofoxlogin/
     * @param      [type]                   $program [description]
     * @return     [array]                  this will return an array with the ids of all the events that has bookings to be canceled
     */
    public function deleteFrom($program, $start_date = null, $end_date = null)
    {
        $start_date = !empty($start_date) ? $start_date : $this->getCurrentDate('Y-m-d'); // Today
        $end_date = !empty($end_date) ? $end_date : null;
        $all_event_ids_with_bookings = [];
        $program = $program['Program'] ?? $program;

        $branchesRepository = app()->make(BranchesRepository::class);
        $branchId = $program['branch_id'];
        $branch = $branchesRepository->addCriteria(new Id($branchId))
            ->firstOrFail(function () use ($branchId) {
                throw BranchNotFoundException::withId($branchId);
            });

        $eventManager = new EventManager();

        foreach ($program['schedule'] as $schedule) {
            $events = $this->findByProgramIdAndDateRangeAndScheduleCode(
                $program['branch_id'],
                $program['_id'],
                $start_date,
                $end_date,
                $schedule['code']
            );
            $event_ids = $this->getIds($events);
            $getTotal = $this->getTotalBookingsByStatus($event_ids, [
                BookingStatus::BOOKED,
                BookingStatus::WAITING,
                BookingStatus::CANCELED
            ]);
            $booked_event_ids = [];
            $not_booked_event_ids = [];

            foreach ($events as &$event) {
                $key = array_search($event['Event']['_id'], array_column($getTotal, '_id'));
                $total = ((false !== $key) && isset($getTotal[$key])) ? $getTotal[$key] : null;

                if (($total) && ($getTotal[$key]['value']['total'] > 0)) {
                    $booked_event_ids[] = $event['Event']['_id'];
                } else {
                    $not_booked_event_ids[] = $event['Event']['_id'];
                }
            }

            //First Delete non booked, then set active false those who have bookings
            $this->deleteNonBookedBy($program, $not_booked_event_ids);
            $this->cancelBookedBy($program, $booked_event_ids);
            $all_event_ids_with_bookings = [...$all_event_ids_with_bookings, ...$booked_event_ids];

            foreach ($events as &$event) {
                $eventManager->emit(EventWasDeleted::class, [EventModel::make($event['Event']), $branch]);
            }
        }

        return $all_event_ids_with_bookings;
    }

    /**
     * Remove all future events from a program.
     *
     * @see https://glofox.atlassian.net/browse/DASH2-3445
     * @see https://glofox.atlassian.net/browse/DASH2-3632
     *
     * @param array $program
     */
    public function deleteAllFutureEventsFromProgram(array $program): void
    {
        $id = $program['_id'];

        $this->loadModel('Branch');
        $timezone = $this->Branch->getTimeZone($program['branch_id']);

        $currentTime = \Carbon\Carbon::now($timezone)->getTimestamp();

        $result = $this->find('all', [
            'conditions' => [
                'active' => true,
                'program_id' => $id,
                'time_start' => [
                    '$gte' => new MongoDate($currentTime),
                ],
            ],
            'fields' => ['_id'],
        ]);

        if (!$result) {
            return;
        }

        $events = Hash::extract($result, '{n}.Event._id');

        $this->cancelBookedBy($program, $events);
    }

    /*!
     * Events in all schedules of the received programs have changed information
     * @param      [type]                   $program [description]
     * @return     [type]                            [description]
     */
    public function updateFrom($program, $branch, ?array $oldProgram = null): ?int
    {
        $generated = 0;

        // if ff enabled
        // compare new program and old program, and return changes only
        $newSchedules = $program['schedule'];
        if (!is_null($oldProgram)) {
            $oldSchedules = $this->addScheduleCodeIndex($oldProgram['schedule'] ?? []);
            $newFullSchedules = $this->addScheduleCodeIndex($newSchedules);
            $newSchedules = $this->getModifiedScheduleFields($oldSchedules, $newFullSchedules);
        }

        foreach ($newSchedules as $schedule) {
            //First Delete not booked, then set active false those who have bookings
            $updatedEvents = $this->updateFromSchedule($program, $schedule);

            // Collect only dates from updated events to skip when generating
            $skipDates = collect($updatedEvents)->map(fn ($event) => date('Y-m-d', $event['date']->sec))->toArray();

            // Restore not updated schedule fields
            if (isset($newFullSchedules[$schedule['code']])) {
                $schedule = $newFullSchedules[$schedule['code']];
            }

            // Generate only events that are not in the $updatedEvents array
            $tempProgram = $program;
            $tempProgram['schedule'] = [$schedule]; // Create a single schedule to avoid duplication

            // Sum the number of events that were generated
            $generated += $this->generateFrom(
                $tempProgram,
                $branch,
                $tempProgram['date_start'],
                $tempProgram['date_finish'],
                [],
                $skipDates
            );
        }

        return $generated;
    }

    private function getModifiedScheduleFields(array $oldSchedules, array $newSchedules): array {
        $mandatoryFields = ['code', 'date', 'days_week'];
        foreach ($newSchedules as $code => $schedule) {
            foreach ($schedule as $field => $value) {
                if (in_array($field, $mandatoryFields)) {
                    continue; // we never unset mandatory fields
                }
                if (isset($oldSchedules[$code][$field]) && $oldSchedules[$code][$field] === $value) {
                    unset($newSchedules[$code][$field]);
                }
            }
        }
        return $newSchedules;
    }

    private function addScheduleCodeIndex($schedules): array {
        $result = [];
        if (empty($schedules)) {
            return [];
        }

        foreach ($schedules as $schedule) {
            if (!isset($schedule['code'])) {
                continue;
            }
            $result[$schedule['code']] = $schedule;
        }
        return $result;
    }

    /**
     * @deprecated MOVE THIS FUNCTION ENTIRELY TO ASYNC UpdateEventsBasedOnProgramChange TASK
     */
    public function updateBasicProgramInfo($program, $changes)
    {
        $today = $this->getCurrentDate('date');

        if (array_key_exists('date_start', $changes) || array_key_exists('date_finish', $changes)) {
            if (array_key_exists('date_start', $changes)) {
                if (isset($changes['date_start']['greater'])) {
                    $this->deleteFrom(
                        $program,
                        $today,
                        $this->subtractNumberOfDays($program['Program']['date_start'], 1)
                    );
                } elseif (isset($changes['date_start']['less'])) {
                    // @see https://glofox.atlassian.net/browse/DASH2-3032
                    // Check first item in the task.
                    // This date needs to be subtracted by 1 so we don't overlap existing events,
                    // and therefore generating duplications.
                    $dateStart = $changes['date_start']['less'];
                    $programDateStart = $program['Program']['date_start'];
                    if (
                        strtotime($dateStart) > time()
                        && strtotime($programDateStart) > time()
                    ) { // create only for future date start
                        $this->loadModel('Branch');
                        $branch = $this->Branch->findById($program['Program']['branch_id']);

                        $dateStart = (new DateTime($dateStart))->modify('-1 day')->format('Y-m-d');
                        $this->generateFrom($program, $branch, $program['Program']['date_start'], $dateStart);
                    }
                }
            }
            if (array_key_exists('date_finish', $changes)) {
                // @see https://glofox.atlassian.net/browse/DASH2-2433
                // If the date_finish of the program is null, we are not going to delete anything, because this means that the
                // date_finish was unset in the dashboard. Therefore, there are no events to be deleted, only to be generated.
                if (null !== $program['Program']['date_finish']) {
                    if (null === $changes['date_finish']['greater']) {
                        $this->deleteFrom($program, $this->addNumberOfDays($program['Program']['date_finish'], 1));
                    } elseif (isset($changes['date_finish']['greater'])) {
                        $this->loadModel('Branch');
                        $branch = $this->Branch->findById($program['Program']['branch_id']);

                        // Avoid generating events in the past, when the previous date finish (from $changes) is in the past
                        // In this case we use the program start date as start date for creating events
                        $startDate = $this->addNumberOfDays($changes['date_finish']['greater'], 1);
                        if (strtotime($startDate) < time()) {
                            $startDate = $program['Program']['date_start'];
                        }
                        $this->generateFrom(
                            $program,
                            $branch,
                            $startDate,
                            $program['Program']['date_finish']
                        );
                    } elseif (isset($changes['date_finish']['less'])) {
                        $this->deleteFrom(
                            $program,
                            $program['Program']['date_finish'],
                            $changes['date_finish']['less']
                        );
                    }
                }
            }
        }

        return true;
    }

    /*!
     * Events in all schedules of the received programs have changed DATE and need to be rescheduled, meanin
     * that current ones must be cancelled and regenerated. First delete and cancel current ones by using
     * schedule_code as reference, then use the Program data to generate new ones.
     * @param      [type]                   $program [description]
     * @return     [type]                            [description]
     */
    public function rescheduleFrom($program, $branch, $start_date, $end_date)
    {
        $this->deleteFrom($program);
        return $this->generateFrom($program, $branch, $start_date, $end_date);
    }

    /*!
     * ADD NEW -> Generate events based on a given program. We will first build the Events Map and then
     * Batch insert on Mongo. Better performance.
     * @param      [type]                   $program    Cake Program Model instance
     * @param      [type]                   $branch     Cake Branch Model instance
     * @param      [type]                   $start_date start date as string YYYY-MM-DD
     * @param      [type]                   $end_date   end date as string YYYY-MM-DD
     * @param      [type]                   $banned     Array of dates NOT TO GENERATE EVENTS
     * @return     [type]                               [description]
     */
    public function generateFrom(
        $program,
        $branch,
        $startDate = null,
        $endDate = null,
        $banned = null,
        array $skipDates = []
    ) {
        $program = $program['Program'] ?? $program;

        // @see https://glofox.atlassian.net/browse/DASH2-3482
        // Forbid event generation for Deleted programs
        if (!$program['active']) {
            return;
        }

        $hasEndDate = !empty($endDate);
        if ($hasEndDate) {
            // Since DatePeriod does not count the last interval we need to increment by 1 day the end date
            // TODO Refactor this to use Carbon or even DateTime
            // TODO Or use EXCLUDE_START_DATE = 0 on http://php.net/manual/en/class.dateperiod.php
            $endDate = $this->addNumberOfDays($endDate, 1);
        }

        $hasProgramsToAdd = !empty($program['schedule']);
        if (!$hasProgramsToAdd) {
            return;
        }

        $isEndDateInThePast = strtotime($endDate) < Carbon::now()->getTimestamp();
        if ($isEndDateInThePast) {
            return;
        }

        $isStartDateInThePast = strtotime($startDate) < Carbon::now()->getTimestamp();
        if (!$startDate || $isStartDateInThePast) {
            $startDate = $this->getCurrentDate();
        }

        // Events of what program are we gonna generate?
        $this->parentProgram = $program;

        $end = !empty($endDate) ? $endDate : date('Y-m-d', strtotime($this->getCurrentDate() . ' + 12 weeks'));
        //Build date patterns and match dates with program schedules to generate an output array of events to store in Mongo
        $pattern = new DatePeriod(new DateTime($startDate), new DateInterval('P1D'), new DateTime($end));
        $dates = iterator_to_array($pattern);
        $valDates = $this->filterNonHolidays(
            $dates,
            $banned
        );            //Filter banned dates from branch.closing_times

        // Remove $skipDates dates from $valDates
        $valDates = collect($valDates)
            ->filter(fn (DateTime $valDate) => !in_array($valDate->format('Y-m-d'), $skipDates))
            ->all();

        $data = array_map([$this, 'mapSchedules'], $valDates); //Events in multidimensional array, need flatten
        //Flatten
        $events = $this->flatten_array($data);

        foreach ($events as &$event) {
            $event['created'] = new MongoDate();
            $event['modified'] = $event['created'];
        }

        if (!empty($events)) {
            $this->getMongoDb()->selectCollection('events')->batchInsert($events, ['w' => 1]);

            /** @var AddVirtualOnlineFieldTransformer $transformer */
            $transformer = app()->make(AddVirtualOnlineFieldTransformer::class);
            $events = $transformer->execute($events);

            /** @var EventsPublisher $eventsPublisher */
            $eventsPublisher = app()->make(EventsPublisher::class);

            foreach ($events as $event) {
                $eventsPublisher->sendEventCreatedEvent(
                    new EventCreatedEventMeta([
                        'branchId' => $branch['Branch']['_id'],
                        'branchTimezone' => $branch['Branch']['address']['timezone_id'] ?? 'UTC',
                        'namespace' => $branch['Branch']['namespace'],
                        'gympassProductId' => $branch['Branch']['features']['gympass']['product_id'] ?? null,
                        'gympassGymId' => $branch['Branch']['features']['gympass']['id'] ?? null,
                        'gympassPassTypeNumber' => $branch['Branch']['features']['gympass']['pass_type_number'] ?? null,
                        'gympassValidationApiAuthToken' => $branch['Branch']['features']['gympass']['validation_api_auth_token'] ?? null,
                        'gympassClientId' => $branch['Branch']['features']['gympass']['client_id'] ?? null,
                        'gympassClientSecret' => $branch['Branch']['features']['gympass']['client_secret'] ?? null,
                    ]),
                    new EventCreatedEventPayload([
                        'programId' => $event['program_id'],
                        'eventId' => (string)$event['_id'],
                        'isOnline' => $event['is_online'],
                        'timeStart' => $event['time_start']->toDateTime()->format(\DateTimeInterface::ATOM),
                        'timeFinish' => $event['time_finish']->toDateTime()->format(\DateTimeInterface::ATOM),
                        'size' => $event['size'],
                        'active' => $event['active'],
                        'gympassEnabled' => $program['metadata']['gympass'] ?? false,
                        'name' => $event['name'],
                        'description' => $event['description'],
                        'namespace' => $event['namespace'],
                        'level' => $event['level'] ?? '',
                        'externalProviderStreamUrl' => $event['external_provider_stream_url'] ?? null,
                        'created' => $event['created']->toDateTime()->format(\DateTimeInterface::ATOM),
                        'modified' => $event['modified']->toDateTime()->format(\DateTimeInterface::ATOM),
                        'scheduleCode' => $event['schedule_code'] ?? null,
                    ])
                );
            }
        }

        return $events === null ? 0 : count($events);
    }

    /*!
     * Map Event Dates to programs
     * @param      [type]                   $dates   [description]
     * @param      [type]                   $program [description]
     * @return     [type]                            [description]
     */
    private function mapSchedules($date, array $program = []): array
    {
        $result = [];
        $program = $program ?: $this->parentProgram; //<--- set in the "generate method"
        $weekDay = $date->format('w');

        $rangeDateTransformer = app()->make(RangeDateTimeTransformer::class);

        //Any event for this date within the program schedules?
        foreach ($program['schedule'] as $schedule) {
            //This $date day of week matches $schedule day of week? if so... generate an event for this
            if ((int)($schedule['days_week']) == $weekDay) {
                $sched = array_merge($this->filterSchedule($program['schedule_default']), $this->filterSchedule($schedule));

                $private = false;
                if (isset($sched['private'])) {
                    $private = filter_var($sched['private'], FILTER_VALIDATE_BOOLEAN);
                } elseif (isset($program['private'])) {
                    $private = filter_var($program['private'], FILTER_VALIDATE_BOOLEAN);
                }

                $rangeDateTime = $rangeDateTransformer->transform(
                    new RangeDateTime(
                        strtotime($date->format('Y-m-d') . ' ' . $sched['start_time']),
                        strtotime($date->format('Y-m-d') . ' ' . $sched['end_time'])
                    )
                );

                $tmp_result = [
                    'namespace' => $program['namespace'],
                    'branch_id' => $program['branch_id'],
                    'program_id' => $program['_id'],
                    'schedule_code' => $sched['code'],
                    'active' => true,
                    'name' => $program['name'],
                    'description' => $program['description'],
                    'date' => new MongoDate(strtotime($date->format('Y-m-d'))),
                    'time_start' => new MongoDate($rangeDateTime->startTimestamp()),
                    'time_finish' => new MongoDate($rangeDateTime->finishTimestamp()),
                    'week_day' => $weekDay,
                    'level' => $sched['level'] ?? '',
                    'size' => (int)($sched['size'] ?? EventModel::DEFAULT_EVENT_SIZE),
                    'facility' => $sched['facility'],
                    'trainers' => $this->removeInvalidTrainersIds($sched['trainers']),
                    'featured' => $sched['featured'] ?? false,
                    'private' => $private,
                ];

                $result[] = $tmp_result;
            }
        }

        return $result;
    }

    /*!
     * DELETE all events of a program given its ID and $schedule_code that don't have any bookings
     * @param      [type]                   $program    [description]
     * @param      [type]                   $schedule   [description]
     * @return     [type]                               [description]
     */
    public function deleteNonBookedBy($program, $event_ids)
    {
        $conditions = [
            'program_id' => $program['_id'],
            '_id' => ['$in' => $event_ids],
        ];

        $logger = app()->make(LoggerInterface::class);
        $logger->info('[EventDeleteNonBookedBy] Deleting non booked events.', $conditions);
        return $this->deleteAll($conditions);
    }

    /*!
     * CANCEL -> SET ACTIVE 'FALSE' all events of a program given its ID and $schedule_code that have bookings
     * @param      [type]                   $program    [description]
     * @param      [type]                   $schedule   [description]
     * @return     [type]                                  [description]
     */
    public function cancelBookedBy($program, $event_ids)
    {
        $logger = app()->make(LoggerInterface::class);
        $conditions = [
            'program_id' => $program['_id'],
            '_id' => ['$in' => $event_ids],
        ];

        // For some reason the update All was not working
        $events = $this->find('all', ['conditions' => $conditions]);

        foreach ($events as $event) {
            $event['Event']['active'] = false;
            $this->save($event);

            $bookings = $this->Booking->findAllByEventIdAndStatus(
                $event['Event']['_id'],
                $this->Booking->get_status('booked')
            );

            if (!empty($bookings)) {
                foreach ($bookings as $booking) {
                    $bookingId = $booking['Booking']['_id'];
                    $userId = $booking['Booking']['user_id'];

                    // yes, unfortunately it needs to be a string @todo change this to boolean after implementing the new Dispatcher
                    $validatePassword = 'false';

                    // yes, unfortunately it needs to be a string @todo change this to boolean after implementing the new Dispatcher
                    $isAdmin = 'true';

                    $logger->info('[EventCancelBookedBy] Request to cancel booking ['.$bookingId.'].', [
                        'booking_id' => $bookingId,
                        'user_id' => $userId,
                        'event_id' => $booking['Booking']['event_id'],
                        'event_name' => $booking['Booking']['event_name'],
                    ]);
                    $this->requestAction(
                        sprintf('/bookings/cancel/%s/%s/%s/%s', $bookingId, $userId, $validatePassword, $isAdmin)
                    );
                }
            }
        }

        return true;
    }

    /**
     * Update basic event data from schedule
     */
    public function updateFromSchedule($program, $schedule)
    {
        // @see https://glofox.atlassian.net/browse/DASH2-3482
        // Forbid event updates for Deleted programs
        if (!$program['active']) {
            return true;
        }

        $branchCakeModel = ClassRegistry::init('Branch');

        $user = $this->getUser();
        $timezone = $branchCakeModel->getTimeZone($user['branch_id']);
        $today = $this->getCurrentDate('Y-m-d', $timezone);

        $conditions = [
            'program_id' => $program['_id'],
            'schedule_code' => $schedule['code'],
            'date' => ['$gte' => new MongoDate(strtotime($today))],
            '$or' => [
                ['active' => true],
                ['active' => false, 'manually_deleted' => true]
            ]
        ];

        $events = $this->find('all', [
            'conditions' => $conditions,
        ]);

        $events_updated = [];
        $modifiedDate = new MongoDate(Carbon::now()->getTimestamp());

        $eventManager = new EventManager();

        $eventsIds = [];

        foreach ($events as &$event) {
            $event['Event']['_id'] = new MongoId($event['Event']['_id']);
            $event_date = $event['Event']['date'];

            $startTime = $schedule['start_time'] ?? Carbon::parse($event['Event']['time_start'])->format("H:i:s");
            $endTime = $schedule['end_time'] ?? Carbon::parse($event['Event']['time_finish'])->format("H:i:s");

            $eventStartTime = $event_date . ' ' . $startTime;
            $eventEndTime = $event_date . ' ' . $endTime;

            $event['Event']['date'] = new MongoDate(strtotime($event['Event']['date']));
            $event['Event']['time_start'] = new MongoDate(strtotime($eventStartTime));
            $event['Event']['time_finish'] = new MongoDate(strtotime($eventEndTime));
            $event['Event']['size'] = (int)($schedule['size'] ?? $event['Event']['size']);
            $event['Event']['trainers'] = $schedule['trainers'] ?? $event['Event']['trainers'];
            $event['Event']['facility'] = $schedule['facility'] ?? $event['Event']['facility'];
            $event['Event']['level'] = $schedule['level'] ?? $event['Event']['level'];
            if (isset($schedule['private'])) {
                $event['Event']['private'] = filter_var($schedule['private'], FILTER_VALIDATE_BOOLEAN);
            }

            $hasChangedTimeStart = $event['Event']['time_start'] !== $eventStartTime;
            $hasChangedTimeFinish = $event['Event']['time_finish'] !== $eventEndTime;
            if ($hasChangedTimeStart || $hasChangedTimeFinish) {
                $eventManager->emit(EventHadDateChanged::class, [EventModel::make($event['Event'])]);
            }

            $event['Event']['modified'] = $modifiedDate;
            $events_updated[] = $event['Event'];
            $eventsIds[] = $event['Event']['_id'];
        }
        unset($event);

        $logger = app()->make(LoggerInterface::class);
        $logger->info('[EventUpdateFromSchedule] Deleting ('.count($eventsIds).') event(s) to be updated.', [
            'events_to_delete' => array_map(fn($item) => $item->{'$id'}, $eventsIds),
            'conditions' => $conditions
        ]);

        if (!empty($events_updated)) {
            // After Remove
            $conditions['_id'] = ['$in' => $eventsIds];
            $this->getMongoDb()->selectCollection('events')->remove($conditions, ['j' => true, 'w' => 1]);

            // After Insert
            $logger->info('[EventUpdateFromSchedule] Inserting ('.count($events_updated).') event(s).');
            $this->getMongoDb()->selectCollection('events')->batchInsert($events_updated, ['w' => 1]);
        }

        return $events_updated;
    }

    // CALLBACKS

    /**
     * [afterFind description].
     *
     * @param [type] $results [description]
     * @param bool $primary [description]
     *
     * @return [type] [description]
     */
    public function afterFind($results, $primary = false)
    {
        //New API
        if (isset($this->API)) {
            $results = parent::afterFind($results, $primary, $this);
            $events = Hash::extract($results, '{n}.Event._id');
            //Obtain total booked / waiting
            $this->bookMap = $this->getTotalBookingsAndTotalWaiting($events);
            $this->bookMap = array_combine(array_column($this->bookMap, '_id'), $this->bookMap);
            $results = array_map([$this, 'mapTotals'], $results);

            return $results;
        }

        //Legacy post processing
        $results = $this->convertFieldsToDate($results, ['date'], 'date');
        $results = $this->convertFieldsToDate($results, ['time_start', 'time_finish'], 'datetime');

        $event_ids = isset($results['Event']) ? [$results['Event']['_id']] : $this->getIds($results);
        $getTotal = $this->getTotalBookingsAndTotalWaiting($event_ids);

        if (isset($results['Event'])) {
            $key = array_search($results['Event']['_id'], array_column($getTotal, '_id'));
            $total = ((false !== $key) && isset($getTotal[$key])) ? $getTotal[$key] : null;

            $results['Event']['total_bookings'] = ($total) ? $getTotal[$key]['value']['totalBookings'] : 0;
            $results['Event']['total_waitings'] = ($total) ? $getTotal[$key]['value']['totalWaitings'] : 0;
        } elseif (!isset($results['Event']['count'])) {
            foreach ($results as &$event) {
                if (!isset($event['Event']['_id'])) {
                    continue;
                }
                $key = array_search($event['Event']['_id'], array_column($getTotal, '_id'));
                $total = ((false !== $key) && isset($getTotal[$key])) ? $getTotal[$key] : null;

                $event['Event']['total_bookings'] = ($total) ? $getTotal[$key]['value']['totalBookings'] : 0;
                $event['Event']['total_waitings'] = ($total) ? $getTotal[$key]['value']['totalWaitings'] : 0;
            }
        }

        return $results;
    }

    public function afterSave($created, $options = [])
    {
        parent::afterSave($created, $options);

        if (empty($this->data[$this->alias])) {
            return;
        }

        $eventId = $this->data[$this->alias]['_id'];

        $totals = $this->getTotalBookingsAndTotalWaiting([$eventId]);

        $this->data[$this->alias]['total_bookings'] = $totals[0]['value']['totalBookings'] ?? 0;
        $this->data[$this->alias]['total_waitings'] = $totals[0]['value']['totalWaitings'] ?? 0;
    }

    /*!
    * Add totals to each Event by reading the bookMap
    * @param  [type] $object [description]
    * @return [type]         [description]
    */
    public function mapTotals($object)
    {
        if (!isset($object['Event']['_id'])) {
            return $object;
        }
        $identifier = $object['Event']['_id'];

        $object['Event']['booked'] = isset($this->bookMap[$identifier]['value']['totalBookings']) ?
            (int)$this->bookMap[$identifier]['value']['totalBookings'] : 0;

        $object['Event']['waiting'] = isset($this->bookMap[$identifier]['value']['totalWaitings']) ?
            (int)$this->bookMap[$identifier]['value']['totalWaitings'] : 0;

        if (!isset($this->loggedUser['_id'])) {
            return $object;
        }

        // $users = isset($this->bookMap[$identifier]["value"]['users']) ? $this->bookMap[$identifier]["value"]['users'] : [];
        // $object["Event"]["has_booked"] = in_array($this->loggedUser['_id'], $users);

        $loggedUserId = $this->loggedUser['_id'];

        $users = $this->bookMap[$identifier]['value']['users'] ?? [];
        $object['Event']['has_booked'] = isset($users[$loggedUserId]);
        $object['Event']['booking_status'] = $users[$loggedUserId]['status'] ?? null;
        $object['Event']['booking_id'] = (isset($users[$loggedUserId]['_id']) && ($users[$loggedUserId]['_id'] instanceof MongoId))
            ? $users[$loggedUserId]['_id']->__toString()
            : null;

        if (BookingStatus::WAITING === $object['Event']['booking_status']) {
            $object['Event']['position'] = $this->getWaitingPosition($object['Event']['_id'], $this->loggedUser['_id']);
        }

        return $object;
    }

    /**
     * [beforeSave description].
     *
     * @param array $options [description]
     *
     * @return [type] [description]
     */
    public function beforeSave($options = [])
    {
        if (!isset($this->API)) {
            //Legacy pre processing
            if (isset($this->data['Event']['name'])) {
                $this->data['Event']['name'] = ucfirst($this->data['Event']['name']);
            }
            if (
                (empty($this->data['Event']['level']) || !is_string($this->data['Event']['level']))
                && $this->mongoNoSetOperator !== '$inc'
            ) {
                $this->data['Event']['level'] = '';
            }

            try {
                if (isset($this->data['Event']['time_start'])) {
                    $timeStart = $this->data['Event']['time_start'];
                    $timeStart = ($timeStart instanceof \MongoDate) ? $timeStart->toDateTime() : new DateTime(
                        $timeStart
                    );

                    $formattedDate = $timeStart->format('Y-m-d');
                    $this->data['Event']['date'] = $formattedDate;
                }
                // @TODO add time_start_utc and time_finish_utc later on
            } catch (Exception $e) {
                // @TODO notify
            }

            $this->castMongoDate(['date', 'time_start', 'time_finish', 'open_booking_time']);
            $this->castInteger(['week_day', 'size', 'total_bookings', 'total_waitings']);
            $this->castBoolean(['new', 'attendance_submitted', 'manually_deleted']);

            if ($this->mongoNoSetOperator !== '$inc') {
                $this->castBoolean(['private'], true);
            }
        } else {
        }

        if ($this->isInserting()) {
            return true;
        }

        $this->setModifiedDate();

        return true;
    }

    // CUSTOM FUNCTIONS

    /**
     * [hasSpotLeft description].
     *
     * @param [type] $id [description]
     *
     * @return bool [description]
     */
    public function hasSpotLeft($branchId, $id, $guestBookings = 0, $isAdmin = false, $forceOverbook = false)
    {
        $canOverbook = false;

        if ($isAdmin && $forceOverbook) {
            $branchModel = ClassRegistry::init('Branch');
            $branch = is_array($branchId) ? $branchId : $branchModel->findById($branchId);
            $branch = AlwaysAccessibleCollection::make($branch);
            $branchBooking = $branch->get('Branch')->get('features')->get('booking')->toArray();

            $canOverbook = $branchBooking['overbooking_enabled'] ?? false;
        }

        $bookingModel = ClassRegistry::init('Booking');
        $bookings = $bookingModel->find(
            'all',
            ['conditions' => ['status' => 'BOOKED', 'event_id' => $id], 'fields' => ['guest_bookings']]
        );
        $totalBookings = is_countable($bookings) ? count($bookings) : 0;

        foreach ($bookings as $booking) {
            $totalBookings = $totalBookings + $booking['Booking']['guest_bookings'];
        }

        $event = $this->find('first', ['conditions' => ['_id' => $id], 'fields' => ['size']]);
        $eventSize = isset($event['Event']['size']) ? (int)($event['Event']['size']) : 0;

        return (($totalBookings + $guestBookings) < $eventSize) || $canOverbook;
    }

    public function formatTimeStartAndTimeEnd($events, $format = 'time')
    {
        if (isset($events[0])) {
            foreach ($events as $key => $val) {
                if (isset($val['Event']['time_start'])) {
                    $events[$key]['Event']['time_start'] = $this->formatDate($val['Event']['time_start'], $format);
                }
                if (isset($val['Event']['time_finish'])) {
                    $events[$key]['Event']['time_finish'] = $this->formatDate($val['Event']['time_finish'], $format);
                }
                if (isset($val['Event']['private'])) {
                    if (null == $val['Event']['private']) {
                        $val['Event']['private'] = false;
                    }
                }
                if (isset($val['Event']['week_day'])) {
                    if (!is_string($val['Event']['week_day'])) {
                        $val['Event']['week_day'] = (string)($val['Event']['week_day']);
                    }
                }
            }
        } else {
            if (isset($events['Event']['time_start'])) {
                $events['Event']['time_start'] = $this->formatDate($events['Event']['time_start'], $format);
            }
            if (isset($events['Event']['time_finish'])) {
                $events['Event']['time_finish'] = $this->formatDate($events['Event']['time_finish'], $format);
            }
            if (isset($events['Event']['private'])) {
                if (null == $events['Event']['private']) {
                    $events['Event']['private'] = false;
                }
            }
            if (isset($events['Event']['week_day'])) {
                if (!is_string($events['Event']['week_day'])) {
                    $events['Event']['week_day'] = (string)($events['Event']['week_day']);
                }
            }
        }

        return $events;
    }

    public function findAllByBranchIdAndDateRange($branch_id = null, $start = null, $end = null, $user = null)
    {
        $conditions = [
            'branch_id' => $branch_id,
            'time_start' => [
                '$gte' => new MongoDate(strtotime($start)),
                '$lte' => new MongoDate(strtotime($end)),
            ],
            'active' => true,
        ];

        if (!empty($user)) {
            $user = $user['User'] ?? $user;
            if ('TRAINER' === $user['type']) {
                $conditions['trainers'] = $user['_id'];
            }
        }

        $params = [
            'conditions' => $conditions,
            'order' => [
                'time_start' => 1,
            ],
            'limit' => 1000,
            'page' => 1,
        ];

        return $this->find('all', $params);
    }

    public function getNameById($id)
    {
        $params = [
            'conditions' => ['Event._id' => $id],
            'fields' => ['name'],
        ];
        $result = $this->find('first', $params);

        return $result['Event']['name'];
    }

    public function getProgramIdById($id)
    {
        $params = [
            'conditions' => [
                'Event._id' => $id,
            ],
        ];
        $result = $this->find('first', $params);

        return $result['Event']['program_id'];
    }

    public function getByIdAndBranchId($id, $branch_id)
    {
        $params = [
            'conditions' => [
                'Event._id' => $id,
                'Event.branch_id' => $branch_id,
            ],
            'fields' => [
                'Event._id',
                'Event.date',
                'Event.active',
                'Event.name',
                'Event.branch_id',
                'Event.program_id',
                'Event.schedule_code',
                'Event.total_bookings',
                'Event.size',
                'Event.total_waitings',
                'Event.membership',
                'Event.time_start',
                'Event.time_finish',
            ],
        ];

        $event = $this->find('first', $params);

        return $this->formatTimeStartAndTimeEnd($event, 'datetime');
    }

    public function getNextEvent($namespace, $branch_id, $date, $current_time, $user)
    {
        if ('TRAINER' == $user['type']) {
            $params = [
                'conditions' => [
                    'namespace' => $namespace,
                    'branch_id' => $branch_id,
                    'trainers' => $user['_id'],
                    'time_start' => ['$gte' => new MongoDate(strtotime($current_time))],
                    'date' => new MongoDate(strtotime($date)),
                ],
                'order' => ['time_start' => 1],
            ];
        } else {
            $params = [
                'conditions' => [
                    'namespace' => $namespace,
                    'branch_id' => $branch_id,
                    'time_start' => ['$gte' => new MongoDate(strtotime($current_time))],
                    'date' => new MongoDate(strtotime($date)),
                ],
                'order' => ['time_start' => 1],
            ];
        }

        $event = $this->find('first', $params);

        return $this->formatTimeStartAndTimeEnd($event, 'datetime');
    }

    public function findByScheduleCodeAndStartTime(string $scheduleCode, Carbon $startTime)
    {
        $start = new \MongoDate(strtotime($startTime->toDateTimeString()));
        $params = [
            'conditions' => [
                'schedule_code' => $scheduleCode,
                'time_start' => $start,
            ],
        ];

        return $this->find('first', $params);
    }

    /**
     *  Return all the events for a given number of weeks.
     */
    public function findByProgramIdAndDateRangeAndScheduleCode(
        $branch_id,
        $program_id,
        $date_start,
        $date_end,
        $schedule_code = null
    ) {
        date_default_timezone_set('UTC');
        $params = [
            'conditions' => [
                'branch_id' => $branch_id,
                'program_id' => $program_id,
                'date' => [
                    '$gte' => new MongoDate(strtotime($date_start))
                ],
            ],
        ];

        if (!empty($date_end)) {
            $params['conditions']['date']['$lte'] = new MongoDate(strtotime($date_end));
        }

        if ($schedule_code) {
            $params['conditions']['schedule_code'] = $schedule_code;
        }

        return $this->find('all', $params);
    }

    /**
     * This function will find the next events for the specified branch.
     *
     * @param [type] $branch_id [description]
     * @param string $timezone [description]
     * @param int $limit [description]
     *
     * @return [type] [description]
     */
    public function findNextEvents($branch_id = null, $timezone = 'UTC', $limit = 6)
    {
        date_default_timezone_set($timezone);
        $conditions = [
            'branch_id' => $branch_id,
            'time_start' => ['$gt' => new MongoDate(strtotime('now'))],
            'active' => true,
        ];
        $order = ['time_start' => 1];
        $limit = (int)($limit);

        return $this->find('all', ['conditions' => $conditions, 'order' => $order, 'limit' => $limit]);
    }

    public function isEventEditable($event_date, $event_time_start)
    {
        //Using this to  compare times
        $time_now = strtotime($this->getCurrentDate('datetime'));
        $start_time = strtotime($event_time_start);
        $start_time_diff = $start_time - $time_now;
        $today = $this->getCurrentDate('date');

        if (strtotime($today) < strtotime($event_date)) {
            // Event in futre so can edit
            return true;
        } else {
            // It's today so check the time
            if (($today == $event_date) && ($start_time_diff > 0)) {
                return true;
            } else {
                return false;
            }
        }
    }

    public function isPrivate($event_id)
    {
        $event = is_array($event_id) ? $event_id : $this->findByIdSetFields($event_id, ['private']);
        if (empty($event)) {
            return false;
        }

        $is_private = (isset($event['Event']['private'])) ? filter_var(
            $event['Event']['private'],
            FILTER_VALIDATE_BOOLEAN
        ) : false;

        return $is_private;
    }

    /**
     * Search for a event given the event id and a scope (branches_ids).
     *
     * @param array $branches_id [description]
     * @param string $event_id [description]
     *
     * @return [type] [description]
     */
    public function findAllByBranchesIdsAndEventId($branches_id = [], $event_id = '')
    {
        $conditions = ['branch_id' => ['$in' => $branches_id], '_id' => $event_id];

        return $this->find('first', ['conditions' => $conditions]);
    }

    /**
     * [findAllByBranchesIdsAndStartDateAndFilters description].
     *
     * @param array $branches_ids [description]
     * @param array $filters [description]
     * @param null $user
     *
     * @return [type] [description]
     */
    public function findAllByBranchesIdsAndStartDateAndFilters($branches_ids = [], $filters = [], $user = null)
    {
        $weeks_to_display = 4;
        $conditions = ['private' => false, 'active' => true, 'branch_id' => ['$in' => $branches_ids]];
        $conditions = array_merge(
            $this->convertFiltersToConditions($branches_ids, $filters, $weeks_to_display),
            $conditions
        );
        $conditions['date'] ??= $this->getDateCondition(null, $weeks_to_display);
        if (!empty($user)) {
            $user = $user['User'] ?? $user;
            if ('TRAINER' == $user['type']) {
                $conditions['trainers'] = $user['_id'];
            }
        }
        $fields = [];
        $order = ['time_start' => 1];
        $limit = 1000;

        $events = $this->find(
            'all',
            ['conditions' => $conditions, 'fields' => $fields, 'order' => $order, 'limit' => $limit]
        );

        return $this->formatTimeStartAndTimeEnd($events);
    }

    /**
     * This methods will receive an array of filters and it will return and array of conditions
     * that can be used in the model->find method.
     *
     * @param array $filters array('address' => array('city' => string, 'country' => 'string', 'districts' => array), 'location' => array("longitude" => float, "latitude" => float, "maxDistance" => int *optional))
     *
     * @return [type] [description]
     */
    public function convertFiltersToConditions($branches_ids = [], $filters = [], $weeks_to_display = 4)
    {
        $conditions = [];
        foreach ($filters as $filter_key => $filter_value) {
            switch ($filter_key) {
                case 'address':
                    if (isset($filter_value['city'])) {
                        $conditions['Branch.address.city'] = $filter_value['city'];
                    }
                    if (isset($filter_value['country'])) {
                        $conditions['Branch.address.country'] = $filter_value['country'];
                    }
                    if (isset($filter_value['district'])) {
                        $conditions['Branch.address.district'] = $filter_value['district'];
                    }
                    if (isset($filter_value['districts'])) {
                        $conditions['Branch.address.district'] = ['$in' => $filter_value['districts']];
                    }
                    if (isset($filter_value['location']['longitude']) && isset($filter_value['location']['latitude'])) {
                        $conditions['Branch.address.location'] = $this->getAddressLocationCondition(
                            $filter_value['location']
                        );
                    }
                    break;
                case 'categories':
                    if (is_array($filter_value)) {
                        $this->Program = ClassRegistry::init('Program');
                        $programs_ids = $this->Program->getProgramIdsByBranchesIdsAndCategories(
                            $branches_ids,
                            $filter_value
                        );
                        $conditions['program_id'] = ['$in' => $programs_ids];
                    }
                    break;
                case 'date':
                    $conditions['date'] = $this->getDateCondition($filter_value, $weeks_to_display);
                    break;
            }
        }

        return $conditions;
    }

    /**
     * This method will return the query condition for geolocation, given a location variable.
     *
     * @param array $location array( "longitude" => float, "latitude" => float, "maxDistance" => int *optional)
     *
     * @return array query condition
     */
    public function getAddressLocationCondition($location = [])
    {
        return [
            '$near' => [
                '$geometry' => [
                    'type' => 'Point',
                    'coordinates' => [(float)($location['longitude']), (float)($location['latitude'])],
                ],
                '$maxDistance' => (isset($location['maxDistance'])) ? (int)($location['maxDistance']) : 5000,
            ],
        ];
    }

    /**
     * [getDateCondition description].
     *
     * @param string $start_date [description]
     * @param int $weeks_to_display [description]
     *
     * @return [type] [description]
     */
    public function getDateCondition($start_date = '', $weeks_to_display = 4)
    {
        if (!empty($start_date)) {
            return new MongoDate(strtotime($start_date));
        } else {
            $date_format = $this->get_date_format('date');
            $today = $this->getCurrentDate($date_format);
            $end_date = $this->addNumberOfWeeks($today, $weeks_to_display, $date_format);

            // Make Sure the end date is at the end of that day
            return [
                '$gte' => new MongoDate(strtotime($today)),
                '$lte' => new MongoDate(strtotime($this->getEndOfDay($end_date))),
            ];
        }
    }

    public function getTotalBookingsAndTotalWaiting($ids = []): array
    {
        return $this->getTotalBookingsByStatus($ids, ['BOOKED', 'WAITING']);
    }

    private function getTotalBookingsByStatus(array $ids, array $statuses = []): array
    {
        if (empty($ids)) {
            return [];
        }

        $params = [
            [
                '$match' => [
                    'event_id' => [
                        '$in' => $ids,
                    ],
                ],
            ],
            [
                '$project' => [
                    '_id' => 1,
                    'event_id' => 1,
                    'status' => 1,
                    'user_id' => 1,
                    'bookedCount' => [
                        '$cond' => [
                            ['$eq' => ['$status', 'BOOKED']],
                            ['$add' => [1, ['$ifNull' => ['$guest_bookings', 0]]]],
                            0,
                        ],
                    ],
                    'waitingCount' => [
                        '$cond' => [
                            ['$eq' => ['$status', 'WAITING']],
                            ['$add' => [1, ['$ifNull' => ['$guest_bookings', 0]]]],
                            0,
                        ],
                    ],
                ],
            ],
            [
                '$group' => [
                    '_id' => '$event_id',
                    'booked' => ['$sum' => '$bookedCount'],
                    'waiting' => ['$sum' => '$waitingCount'],
                    'total' => ['$sum' => 1],
                    'users' => [
                        '$push' => [
                            'k' => '$user_id',
                            'v' => [
                                '$mergeObjects' => [
                                    'status' => '$status',
                                    '_id' => '$_id',
                                ],
                            ],
                        ],
                    ],
                ],
            ],
            [
                '$project' => [
                    '_id' => 1,
                    'value' => [
                        '$mergeObjects' => [
                            'totalBookings' => '$booked',
                            'totalWaitings' => '$waiting',
                            'total' => '$total',
                            'users' => [
                                '$arrayToObject' => '$users',
                            ],
                        ],
                    ],
                ],
            ],
            [
                '$sort' => [
                    '_id' => 1,
                ],
            ],
        ];

        if (!empty($statuses)) {
            $params[0]['$match']['status'] = [
                '$in' => $statuses,
            ];
        }

        $this->Booking = ClassRegistry::init('Booking');
        $mongo = $this->getDataSource();
        $customCollection = new GlofoxMongoCollection($mongo->getMongoCollection($this->Booking));
        return $customCollection->aggregation($params);
    }

    /**
     * This method will return the effective event size for an integration.
     *
     * @param int $eventAvailableSpots The complete number of free spots in the event
     * @param int $integrationEventSize The size set in the event for the integration
     * @param int $integrationTotalBookings The amount of bookings done by the integration for this event
     *
     * @return [type] [description]
     */
    public function getIntegrationEventSize(
        int $eventAvailableSpots,
        int $integrationEventSize,
        int $integrationTotalBookings
    ): int {
        $eventSize = $integrationEventSize;
        $integrationAvailableSpots = ($integrationEventSize - $integrationTotalBookings);
        if ($eventAvailableSpots < $integrationAvailableSpots) {
            $eventSize = $eventAvailableSpots + $integrationTotalBookings;
        }

        return $eventSize;
    }

    /**
     * [setCalculatedFields description].
     *
     * @param SetCalculatedFieldsParameters $parameters
     * @return array
     */
    // public function setCalculatedFields(array $events, array $branch) : array
    public function setCalculatedFields(SetCalculatedFieldsParameters $parameters): array
    {
        $event = $parameters->getEvent();
        $openBookingWindow = $parameters->getOpenBookingWindow();
        $closeBookingWindow = $parameters->getCloseBookingWindow();
        $waitingListSize = $parameters->getWaitingListSize();

        $openBookingTime = $this->getOpenBookingTime(
            $event['time_start'],
            $openBookingWindow,
            $parameters->getTimezone()
        );
        $closeBookingTime = $this->getCloseBookingTime(
            $event['time_start'],
            $closeBookingWindow,
            $parameters->getTimezone()
        );

        $getStatusParameters = new GetStatusParameters();
        $getStatusParameters->setOpenBookingTime($openBookingTime);
        $getStatusParameters->setCloseBookingTime($closeBookingTime);
        $getStatusParameters->setBooked($event['booked']);
        $getStatusParameters->setSize($event['size']);
        $getStatusParameters->setWaitingListSize($waitingListSize);
        $getStatusParameters->setCurrentTime($parameters->getCurrentTime());
        $getStatusParameters->setMemberAlreadyBooked($event['has_booked']);

        $status = $this->getStatus($getStatusParameters);

        $event['open_booking_time'] = $openBookingTime;
        $event['close_booking_time'] = $closeBookingTime;
        $event['status'] = $status->getValue();

        return $event;
    }

    /**
     * Given an array of events, it will add the calculated fields
     * to those events.
     *
     * @param array $events [description]
     * @param AlwaysAccessibleCollection $features [description]
     * @param string $timezone [description]
     */
    public function setCalculatedFieldsToEvents(
        array $events,
        AlwaysAccessibleCollection $features,
        string $timezone
    ): array {
        $events = !empty($events) ? $events : [];
        $setCalculatedFieldsParams = new SetCalculatedFieldsParameters();
        $setCalculatedFieldsParams->setOpenBookingWindow($features->get('booking')->get('booking_open_window', 0));
        $setCalculatedFieldsParams->setCloseBookingWindow($features->get('booking')->get('booking_close_window', 0));
        $setCalculatedFieldsParams->setWaitingListSize($features->get('booking')->get('waiting_list', 0));
        $setCalculatedFieldsParams->setCurrentTime(Carbon::now($timezone)->timestamp);
        $setCalculatedFieldsParams->setTimezone($timezone);

        foreach ($events as &$event) {
            $setCalculatedFieldsParams->setEvent($event);
            $event = $this->setCalculatedFields($setCalculatedFieldsParams);
        }

        return $events;
    }

    /**
     * Given an array of time slot events, it will add the calculated fields
     * to those events.
     *
     * @param array $events [description]
     * @param AlwaysAccessibleCollection $features [description]
     * @param string $timezone [description]
     *
     * @return array it will return the same events received but with the new fields set
     */
    public function setCalculatedFieldsToTimeSlots(
        array $events,
        AlwaysAccessibleCollection $features,
        string $timezone
    ): array {
        $events = !empty($events) ? $events : [];
        $setCalculatedFieldsParams = new SetCalculatedFieldsParameters();
        $setCalculatedFieldsParams->setWaitingListSize(0); // timeslots does not support waiting list
        $setCalculatedFieldsParams->setCurrentTime(Carbon::now($timezone)->timestamp);
        $setCalculatedFieldsParams->setTimezone($timezone);

        $bookingWindowMap = [
            TimeSlotModelList::FACILITIES => [
                'open' => $features->get('facilities')->get('booking_open_window', 0),
                'close' => $features->get('facilities')->get('booking_close_window', 0),
            ],
            TimeSlotModelList::USERS => [
                'open' => $features->get('trainers')->get('booking_open_window', 0),
                'close' => $features->get('trainers')->get('booking_close_window', 0),
            ],
            TimeSlotModelList::APPOINTMENTS => [
                'open' => $features->get('trainers')->get('booking_open_window', 0),
                'close' => $features->get('trainers')->get('booking_close_window', 0),
            ],
        ];

        foreach ($events as &$event) {
            $setCalculatedFieldsParams->setEvent($event);
            $model = !empty($event['model']) ? $event['model'] : null;
            $openBookingWindow = $bookingWindowMap[$model]['open'] ?? 0;
            $closeBookingWindow = $bookingWindowMap[$model]['close'] ?? 0;

            $setCalculatedFieldsParams->setOpenBookingWindow($openBookingWindow);
            $setCalculatedFieldsParams->setCloseBookingWindow($closeBookingWindow);
            $setCalculatedFieldsParams->setTimezone($timezone);
            $event = $this->setCalculatedFields($setCalculatedFieldsParams);
        }

        return $events;
    }

    /**
     * Given a time start and a open booking window in hours,
     * it will return the time when that event is going to start
     * accepting bookings.
     *
     * @param int $timeStart start of the event in a timestamp format
     * @param ?int $openBookingWindow amount of hours before the event or null
     *
     * @return int time when the event starts accepting bookings in timestamp format
     */
    public function getOpenBookingTime(int $timeStart, ?int $openBookingWindow, string $timezone = 'UTC'): ?int
    {
        if ($openBookingWindow <= 0) {
            return null;
        }

        return Carbon::createFromTimestamp($timeStart)
            ->setTimezone($timezone)
            ->subHours($openBookingWindow)
            ->getTimestamp();
    }

    /**
     * Given a time start and a close booking window in hours,
     * it will return the time when that event is going to stop
     * accepting bookings.
     *
     * @param int $timeStart start of the event in a timestamp format
     * @param int|null $closeBookingWindow amount of hours before the event or the start of the event
     * @param string $timezone
     * @return int time when the event stops accepting bookings in timestamp format
     */
    public function getCloseBookingTime(int $timeStart, ?int $closeBookingWindow, string $timezone = 'UTC'): int
    {
        if ($closeBookingWindow <= 0) {
            return $timeStart;
        }

        return Carbon::createFromTimestamp($timeStart)
            ->setTimezone($timezone)
            ->subHours($closeBookingWindow)
            ->getTimestamp();
    }

    /**
     * It determines the status of a specific event based on the
     * EventStatus enum.
     *
     * @param GetStatusParameters $parameters [description]
     *
     * @return EventStatus Status of that particular event
     */
    public function getStatus(GetStatusParameters $parameters): EventStatus
    {
        $carbonCurrentTime = Carbon::createFromTimestamp($parameters->getCurrentTime());
        $openBookingTime = $parameters->getOpenBookingTime();
        $carbonOpenBookingTime = ($openBookingTime) ? Carbon::createFromTimestamp($openBookingTime) : null;
        $closeBookingTime = $parameters->getCloseBookingTime();
        $carbonCloseBookingTime = ($closeBookingTime) ? Carbon::createFromTimestamp($closeBookingTime) : null;
        $booked = $parameters->getBooked();
        $size = $parameters->getSize();
        $waiting = $parameters->getWaiting();
        $waitingListSize = $parameters->getWaitingListSize();
        $status = EventStatus::AVAILABLE();
        $memberAlreadyBooked = $parameters->isMemberAlreadyBooked();

        if (true === $memberAlreadyBooked) {
            $status = EventStatus::ALREADY_BOOKED();
        } elseif (($booked >= $size) && ($waiting >= $waitingListSize)) {
            $status = EventStatus::FULLY_BOOKED();
        } elseif ($carbonOpenBookingTime && $carbonOpenBookingTime->gt($carbonCurrentTime)) {
            $status = EventStatus::BOOKING_WINDOW_NOT_OPEN();
        } elseif ($carbonCloseBookingTime && $carbonCloseBookingTime->lt($carbonCurrentTime)) {
            $status = EventStatus::BOOKING_WINDOW_PASSED();
        } elseif (($booked >= $size) && ($waiting < $waitingListSize)) {
            $status = EventStatus::JOIN_WAITING_LIST();
        }

        return $status;
    }

    // Pricing

    /**
     * @throws \Glofox\Datasource\Exceptions\InvalidMongoIdException
     * @throws Exception
     * @throws ProgramNotFoundException
     */
    public function pricingForUser(PricingForUserParameters $params): PricingWrapper
    {
        $event = $params->getEvent();
        $user = $params->getUser();
        $guestBookings = $params->getGuestBookings();
        $skipValidation = $params->getSkipValidation();
        $skipGrantCreditsInAdvance = $params->getSkipGrantCreditsInAdvance();
        $skipCheckCreditsInNextPaymentCycle = $params->getSkipCheckCreditsInNextPaymentCycle();
        $isChargeable = $params->getIsChargeable();

        if ($user->isGuest()) {
            throw new Exception('GUEST_NOT_ELIGIBLE_FOR_EVENTS');
        }

        if ($user->isStaff()) {
            throw new Exception('STAFF_NOT_ELIGIBLE_FOR_EVENTS');
        }

        /** @var Booking $bookingCakeModel */
        $bookingCakeModel = app()->make(\Booking::class);
        $userCakeModel = ClassRegistry::init('User');

        $logger = app()->make(LoggerInterface::class);
        $branchesRepository = app()->make(BranchesRepository::class);
        $programsRepository = app()->make(ProgramsRepository::class);

        /** @var AddonEligibilityServiceInterface $addonsEligibilityService */
        $addonsEligibilityService = app()->make(AddonEligibilityServiceInterface::class);

        /** @var StrikeValidationServiceInterface $strikeValidationService */
        $strikeValidationService = app()->make(StrikeValidationServiceInterface::class);

        $branchId = $event->branchId();

        // Search existing branch
        /** @var Branch $branch */
        $branch = $branchesRepository->addCriteria(new Id($branchId))
            ->firstOrFail(function () use ($branchId) {
                throw BranchNotFoundException::withId($branchId);
            });

        // Search existing program
        $programId = $event['program_id'];

        $program = $programsRepository->addCriteria(new BranchId($branchId))
            ->addCriteria(new Id($programId))
            ->firstOrFail(function () use ($programId) {
                throw ProgramNotFoundException::withId($programId);
            });

        $currency = $branch->currency();

        // Because we are calling legacy functions we need to convert the user data to the version those methods expect
        $legacyUser = $this->getLegacyCompatibleUser($user->toArray(), $userCakeModel);

        $strikeValidationResult = $strikeValidationService->validate($branch, $user);

        if (!$strikeValidationResult['success']) {
            throw new Exception($strikeValidationResult['message_code']);
        }

        $membershipValidationResult = false;

        $addonsValidationResult = $addonsEligibilityService->validate($user, $event);

        if (!$addonsValidationResult) {
            $logger->info(
                sprintf(
                    'User %s has no eligible addons for event %s, starting membership check',
                    $user->id(),
                    $event->id()
                )
            );

            // Check Eligibility related to membership range and subscription
            $userMembershipValidParameters = new UserMembershipValidParameters();
            $userMembershipValidParameters->setBranch($branch)
                ->setMember($legacyUser)
                ->setProgram($program)
                ->setEvent($event)
                ->setGuestBookings($guestBookings);

            $userMembershipValidParameters->setSkipGrantCreditsInAdvance($skipGrantCreditsInAdvance);

            $validateMembership = $bookingCakeModel->userMembershipValid($userMembershipValidParameters);

            $membershipValidationResult = (bool)$validateMembership['success'];
        }

        $userCredits = $this->getMemberCreditsForEvent(
            $legacyUser,
            $program,
            $event,
            $skipCheckCreditsInNextPaymentCycle
        );

        if (!$skipValidation && !$addonsValidationResult && !$membershipValidationResult && empty($userCredits['num_sessions'])) {
            throw new Exception($validateMembership['message_code']);
        }

        $allowedMemberTypes = $program['allowed_member_types'];
        $defaultPrice = $program['default_price'] ?? null;
        $asPayg = false;

        if (!$skipValidation) {
            // Check Eligibility guest bookings limit
            $this->checkEligibilityByGuestBookingsLimit($branch, $guestBookings);
        }

        // Check Eligibility related to allowed memberships for the event and credits
        // Add group when class specific add-ons are available
        $validateAllowedGroups = $userCakeModel->validateAllowedGroups(
            $user->id(),
            $program['branch_id'],
            $allowedMemberTypes,
            $asPayg,
            $defaultPrice,
            $programId,
            $program['categories'] ?? []
        );

        // @see https://glofox.slack.com/archives/GBGPEF21E/p1530626816000136 (Norma, Timmy, Finn, Fernando, Felipe)
        // While there are no docs for this yet, I'll reference the slack discussion.
        if (!$validateAllowedGroups['success'] && empty($userCredits['num_sessions']) && !$skipValidation && !$addonsValidationResult) {
            throw new Exception($validateAllowedGroups['message_code']);
        }

        $validateAllowedGroups = collect($validateAllowedGroups);
        $price = $validateAllowedGroups->get('price');

        // @see https://glofox.atlassian.net/browse/CI-47
        // @see https://glofox.atlassian.net/browse/CI-2447
        if (!$isChargeable || $this->isQualifiedIntegratorToChargeNothing($params)) {
            $price = 0;
        }

        // Calculate Total price
        $totalBookings = $guestBookings + 1;
        $availableCredits = $userCredits['num_sessions'];

        $hasUnlimitedAddon = $addonsValidationResult !== null && !$addonsValidationResult->isCreditsBased();
        if (null === $price && !($hasUnlimitedAddon)) {
            $hasInsufficientLegacyCredits = true;
            $hasInsufficientAddonCredits = true;
            if (!empty($userCredits['num_sessions'])) {
                $hasInsufficientLegacyCredits = $totalBookings > $availableCredits;
            }

            if ($addonsValidationResult !== null && $addonsValidationResult->hasCreditsLeft()) {
                $hasInsufficientAddonCredits = $totalBookings > $addonsValidationResult->serviceAvailableCredits();
            }
            if ($hasInsufficientAddonCredits && $hasInsufficientLegacyCredits) {
                throw new Exception('NOT_ELIGIBLE_FOR_EVENT_AND_NOT_ENOUGH_CREDITS');
            }
        }

        if (
            !$addonsValidationResult
            && $user->membership()->isTimeAndCreditBased()
            && $price === 0
            && $totalBookings > $availableCredits
            && $isChargeable
            && !$this->isQualifiedIntegratorToChargeNothing($params)
        ) {
            throw new Exception('YOU_HAVE_NO_CREDITS_LEFT');
        }

        $pricing = $this->calculatePriceForUser(
            $user,
            $totalBookings,
            $availableCredits,
            $currency,
            $price,
            $addonsValidationResult
        );

        return new PricingWrapper($pricing, $userCredits, $addonsValidationResult, $price);
    }

    private function isQualifiedIntegratorToChargeNothing(PricingForUserParameters $params): bool
    {
        return \mb_strtolower($params->getOrigin() ?? '') === \mb_strtolower(Identifier::CLASSPASS) ||
            \mb_strtolower($params->getOrigin() ?? '') === \mb_strtolower(Identifier::MOHOLDING);
    }

    private function getWaitingPosition($eventId, $userId)
    {
        $this->Booking = ClassRegistry::init('Booking');
        $conditions = ['event_id' => $eventId, 'user_id' => $userId, 'status' => BookingStatus::WAITING];
        $booking = $this->Booking->find('first', ['conditions' => $conditions]);
        $booking = $this->Booking->setWaitingListPosition($booking);

        return isset($booking['Booking']) ? $booking['Booking']['position'] : $booking['position'];
    }

    /**
     * TO BE MOVED.
     *
     * @param User $user
     * @param int $totalBookings [description]
     * @param int $availableCredits [description]
     * @param string $currency [description]
     * @param float $unitPrice [description]
     * @param Addon $addon [description]
     *
     * @return Pricing [type]                [description]
     */
    private function calculatePriceForUser(
        User $user,
        int $totalBookings,
        int $availableCredits,
        string $currency,
        ?float $unitPrice,
        ?Addon $addon
    ): Pricing {
        $logger = app()->make(LoggerInterface::class);
        $logger->info(
            sprintf(
                'Starting Event::calculatePriceForUser using [totalBookings=%u, availableCredits=%u, currency=%s, unitPrice=%s, addonServiceId=%s]',
                $totalBookings,
                $availableCredits,
                $currency,
                $unitPrice ?? 'null',
                ($addon !== null) ? $addon->serviceId() : 'null'
            )
        );

        if ($user->membership()->isTimeAndCreditBased() && !$unitPrice) {
            $creditsToUse = $totalBookings;
            $amountToPay = $unitPrice;
        } else {
            $pricing = $this->calculateCreditConsumption($totalBookings, $availableCredits, $unitPrice, $currency);
            $amountToPay = $pricing->price();
            $creditsToUse = $pricing->credits();
        }

        if ($addon !== null && !$addon->isCreditsBased()) {
            $amountToPay = 0;
            $creditsToUse = 0;
        }

        if ($addon !== null && $addon->hasCreditsLeft()) {
            $addonPricing = $this->calculateCreditConsumption(
                $totalBookings,
                $addon->serviceAvailableCredits(),
                $unitPrice,
                $currency
            );
            if ($addonPricing->price() !== null || $addonPricing->credits() >= $totalBookings) {
                $creditsToUse = $addonPricing->credits();
                $amountToPay = $addonPricing->price();
            }
        }

        /** @var Pricing $pricing */
        $pricing = (new PricingBuilder())
            ->withCredits($creditsToUse)
            ->withPrice($amountToPay)
            ->withCurrency($currency)
            ->build();

        $logger->info(
            sprintf(
                'Finishing price calculation by Event::calculatePriceForUser, pricing defined as [creditsToUse=%s, amountToPay=%s, currency=%s]',
                $creditsToUse ?? 'null',
                $amountToPay ?? 'null',
                $currency
            )
        );

        return $pricing;
    }

    private function calculateCreditConsumption(
        int $totalBookings,
        int $availableCredits,
        ?float $unitPrice,
        string $currency
    ): Pricing {
        $logger = app()->make(LoggerInterface::class);
        $logger->info(
            sprintf(
                'Starting Event::calculateCreditConsumption using [totalBookings=%u, availableCredits=%u, currency=%s]',
                $totalBookings,
                $availableCredits,
                $currency,
            )
        );

        $creditsToUse = ($availableCredits > $totalBookings) ? $totalBookings : $availableCredits;
        $bookingsToPay = $totalBookings - $creditsToUse;

        $amountToPay = null;

        if (null !== $unitPrice) {
            $amountToPay = $bookingsToPay * $unitPrice;
        }

        /** @var Pricing $pricing */
        $pricing = (new PricingBuilder())
            ->withCredits($creditsToUse)
            ->withPrice($amountToPay)
            ->withCurrency($currency)
            ->build();

        $logger->info(
            sprintf(
                'Finishing price calculation by Event::calculateCreditConsumption, pricing defined as [creditsToUse=%s, amountToPay=%s, currency=%s]',
                $creditsToUse ?? 'null',
                $amountToPay ?? 'null',
                $currency
            )
        );

        return $pricing;
    }

    /**
     * To be moved.
     *
     * @param Branch $branch [description]
     * @param int $guestBookings [description]
     *
     * @return bool [type]                [description]
     */
    private function checkEligibilityByGuestBookingsLimit(Branch $branch, int $guestBookings)
    {
        $branchCollection = AlwaysAccessibleCollection::make($branch);
        $guestBookingsLimit = $branchCollection->get('features')->get('booking')->get('guest_bookings', 0);
        if (!empty($guestBookingsLimit) && ($guestBookingsLimit < $guestBookings)) {
            throw new \ForbiddenException('GUEST_BOOKINGS_LIMIT_REACHED');
        }

        return true;
    }

    /**
     * @param $user
     * @param $program
     * @param $event
     *
     * @return array
     */
    private function getMemberCreditsForEvent(
        $user,
        $program,
        $event,
        $skipCheckCreditsInNextPaymentCycle = false
    ): array {
        /** @var UserCredit $userCreditCakeModel */
        $userCreditCakeModel = ClassRegistry::init('UserCredit');

        $memberIsUnderSubscriptionAndBookingForNextPeriod = false;
        $memberSubscription = $user['membership']['subscription'] ?? null;

        if ($memberSubscription && !empty($memberSubscription['credits'])) {
            $membershipExpiryDate = strtotime($user['membership']['expiry_date']);
            $eventDate = strtotime($event['date']);
            $subscriptionHasEndDate = !empty($memberSubscription['end_date']);
            $isLastPeriod = $subscriptionHasEndDate ?
                $membershipExpiryDate >= strtotime($memberSubscription['end_date']) :
                false;

            if ($eventDate > $membershipExpiryDate && !$isLastPeriod) {
                $memberIsUnderSubscriptionAndBookingForNextPeriod = true;
            }
        }

        if ($skipCheckCreditsInNextPaymentCycle) {
            $memberIsUnderSubscriptionAndBookingForNextPeriod = false;
        }

        $categories = $program['categories'] ?? [];
        $userCredits = $userCreditCakeModel->findCreditsForBooking(
            $program['branch_id'],
            $user['_id'],
            $this->formatDate($event['date'], 'date'),
            'programs',
            $program['_id'],
            $categories,
            $memberIsUnderSubscriptionAndBookingForNextPeriod
        );

        return $userCredits;
    }

    /**
     * [getLegacyCompatibleUser description].
     *
     * @param array $user [description]
     * @param [type] $userCakeModel [description]
     *
     * @return [type] [description]
     */
    private function getLegacyCompatibleUser(array $user, $userCakeModel)
    {
        return $userCakeModel->afterFind([['User' => $user]])[0]['User'];
    }

    private function filterOutDeactivatedFacilities(array &$additionalConditions, array $branch): void
    {
        $facilitiesRepository = app()->make(FacilitiesRepository::class);

        // needs to be namespace-based to cover roaming branches
        $deactivatedFacilities = $facilitiesRepository
            ->addCriteria(new Active(false))
            ->addCriteria(new InNamespaces(Collection::make([$branch['Branch']['namespace']])))
            ->find();

        if (!is_countable($deactivatedFacilities) || count($deactivatedFacilities) === 0) {
            return;
        }

        $deactivatedFacilityIds = array_map(
            fn (\Glofox\Domain\Facilities\Models\Facility $facility) => $facility->id(),
            $deactivatedFacilities
        );

        if (!isset($additionalConditions['model_id'])) {
            $additionalConditions['model_id'] = [];
        }

        $existingModelIdCriteria = $additionalConditions['model_id']['$nin'] ?? [];
        $additionalConditions['model_id']['$nin'] = array_merge($existingModelIdCriteria, $deactivatedFacilityIds);
    }

    private function filterOutDeactivatedTrainers(array &$additionalConditions, array $branch): void
    {
        $usersRepository = app()->make(UsersRepository::class);

        // needs to be namespace-based to cover roaming branches
        $deactivatedTrainers = $usersRepository
            ->addCriteria(new Active(false))
            ->addCriteria(new IsStaff())
            ->addCriteria(new InNamespaces(Collection::make([$branch['Branch']['namespace']])))
            ->find();

        if (!is_countable($deactivatedTrainers) || count($deactivatedTrainers) === 0) {
            return;
        }

        $deactivatedTrainerIds = array_map(fn (User $trainer) => $trainer->id(), $deactivatedTrainers);

        if (!isset($additionalConditions['model_id'])) {
            $additionalConditions['model_id'] = [];
        }

        $existingModelIdCriteria = $additionalConditions['model_id']['$nin'] ?? [];
        $additionalConditions['model_id']['$nin'] = array_merge($existingModelIdCriteria, $deactivatedTrainerIds);
    }

    private function filterOutPrivateSlots(array &$additionalConditions, Branch $branch): void
    {
        $timeSlotPatternRepository = app()->make(TimeSlotPatternsRepository::class);

        // needs to be namespace-based to cover roaming branches
        $privateTimeSlots = $timeSlotPatternRepository
            ->addCriteria(new Active(true))
            ->addCriteria(new InNamespaces(Collection::make([$branch->namespace()])))
            ->addCriteria(new PrivateField(true))
            ->find();

        if (!is_countable($privateTimeSlots) || count($privateTimeSlots) === 0) {
            return;
        }

        $privateIds = array_map(
            fn (\Glofox\Domain\TimeSlotPatterns\Models\TimeSlotPattern $timeSlotPattern) => $timeSlotPattern->id(),
            $privateTimeSlots
        );

        if (!isset($additionalConditions['model_id'])) {
            $additionalConditions['model_id'] = [];
        }

        $existingModelIdCriteria = $additionalConditions['model_id']['$nin'] ?? [];
        $additionalConditions['model_id']['$nin'] = array_merge($existingModelIdCriteria, $privateIds);
    }

    /**
     * @param array $branch
     * @return array
     */
    private function filterOutUnBookableTrainers(array $branch): array
    {
        $usersRepository = app()->make(UsersRepository::class);

        // needs to be namespace-based to cover roaming branches
        $trainers = $usersRepository
            ->addCriteria(new Active(true))
            ->addCriteria(new IsStaff())
            ->addCriteria(new InNamespaces(Collection::make([$branch['Branch']['namespace']])))
            ->addCriteria(new FieldExists('bookable'))
            ->addCriteria(new FieldLessThanOrEqual('bookable', false))
            ->find();

        if (empty($trainers)) {
            return [];
        }

        return array_map(fn (User $trainer) => $trainer->id(), $trainers);
    }

    /**
     * @param array $trainersIds
     * @return string[]
     */
    private function removeInvalidTrainersIds(array $trainersIds): array
    {
        $validTrainersIds = array_filter($trainersIds, static fn ($id) => MongoId::isValid($id));

        return array_values($validTrainersIds);
    }

    private function filterSchedule(array $schedule): array
    {
        // We apply a callback function to prevent booleans with false value from being removed
        return array_filter($schedule, fn ($v) => is_bool($v) || !empty($v), ARRAY_FILTER_USE_BOTH);
    }
}
