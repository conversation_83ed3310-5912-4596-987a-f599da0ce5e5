<?php


use Glofox\Domain\Memberships\Repositories\MembershipsRepository;
use Glofox\Domain\Users\Models\User;
use Glofox\Domain\Users\Repositories\UsersRepository;
use Glofox\Domain\Memberships\Models\Membership;
use Glofox\Repositories\Search\Expressions\Shared\Id;
use Glofox\Storage\CloudStorageInterface;
use League\Csv\Reader;
use League\Csv\Writer;
use Psr\Log\LoggerInterface;

/**
 * Class MoveMembershipToOriginBranchShell
 *
 * For a given userID, moves their membership from it's current branch to the
 * user's origin branch.
 */
class MoveMembershipToOriginBranchShell extends \AppShell
{

    /**
     * @var string
     */
    private $userID;

    // -------------------------------------------------

    /**
     * @var \Glofox\Domain\Users\Models\User
     */
    private $user;

    /**
     * @var Membership
     */
    private $membership;

    // -------------------------------------------------

    /**
     * @var UsersRepository
     */
    private $userRepository;

    private \Glofox\Domain\Memberships\Repositories\MembershipsRepository $membershipsRepository;

    private ?string $csvReportLocation = null;

    /** @var CloudStorageInterface */
    private $cloudStorage;

    /**
     * @var LoggerInterface
     */
    private $logger;

    public function __construct(?ConsoleOutput $stdout = null, ?ConsoleOutput $stderr = null, ?ConsoleInput $stdin = null)
    {
        parent::__construct($stdout, $stderr, $stdin);

        $this->userRepository = app()->make(UsersRepository::class);
        $this->membershipsRepository = new MembershipsRepository();
        $this->cloudStorage = app()->make(CloudStorageInterface::class);
        $this->logger = app()->make(LoggerInterface::class);
    }

    /**
     * @return ConsoleOptionParser
     */
    public function getOptionParser()
    {
        $parser = parent::getOptionParser();

        $parser->addOption('method', [
            'short' => 'm',
            'help' => 'The method to execute',
            'default' => 'dry-run',
        ]);

        $parser->addOption('user-id', [
            'short' => 'u',
            'help' => 'The user to use in this execution',
            'default' => '',
        ]);

        return $parser;
    }

    /**
     * @throws Exception
     */
    public function main()
    {
        $this->init($this->params);
        $writer = $this->bootstrapCsv();

        $dryRun = $this->params['method'] !== "migrate";

        if ($this->migrate($dryRun)) {
            $this->logger->info("Success");
            $this->writeSuccessToCsv($writer, $dryRun, $this->user);
        } else {
            $this->writeErrorToCsv($writer, $dryRun, $this->user);
        }

        $this->uploadReport($this->user, $writer->newReader());
    }

    /**
     * @param array $parameters
     * @throws Exception
     */
    private function init(array $parameters)
    {
        $this->userID = $parameters['user-id'];
        if (empty($this->userID)) {
            throw new \Exception('User ID is missing');
        }

        $this->user = $this->userRepository
            ->addCriteria(new Id($this->userID))
            ->firstOrFail();

        $this->membership = $this->membershipsRepository
            ->addCriteria(new Id($this->user->membership()->id()))
            ->firstOrFail();
    }

    private function migrate(bool $dryRun): bool {
        $this->logger->info(sprintf("[%s] Migrating membership [%s] on user [%s] to branch [%s]",
            $dryRun ? "DRY RUN" : "MIGRATE", $this->membership->id(), $this->user->id(), $this->user->originBranchId()));

        // If the origin branch ID is already a match, there's nothing to do.
        $originBranchId = $this->user->originBranchId();
        if ($originBranchId === $this->membership->branchId()) {
            $this->logger->info("Membership already belongs to origin branch");
            return true;
        }

        // Subscriptions not supported.
        if ($this->user->membership()->hasSubscription()) {
            $this->logger->warning(sprintf("Cannot migrate membership, there is a subscription: %s", $this->user->membership()->subscription()->id()));
            return false;
        }

        // Create a copy of the membership under the origin branch
        $newMembership = $this->copyMembershipToBranch($dryRun, $originBranchId);

        // Reassign the user's membership to the new membership at the origin branch
        $this->reassignMembership($dryRun, $newMembership);

        return true;
    }

    private function copyMembershipToBranch(bool $dryRun, string $newBranch): Membership
    {
        // Copy the existing membership and set the new branch ID
        $newMembership = $this->membership->toArray();
        unset($newMembership['_id']);
        $newMembership['branch_id'] = $newBranch;
        $newMembership['private'] = true;

        $this->logger->info(sprintf("New membership: %s", json_encode($newMembership, JSON_PARTIAL_OUTPUT_ON_ERROR)));

        // Save to memberships collection
        if (!$dryRun) {
            $this->logger->info("Saving membership...");
            $newMembership = $this->membershipsRepository->save($newMembership);
            $newMembership = $newMembership['Membership'];
        }

        return Membership::make($newMembership);
    }


    private function reassignMembership(bool $dryRun, Membership $newMembership)
    {
        // Assign to the user
        $user = $this->user->toArray();
        $user['membership']['_id'] = $newMembership->id();
        $user['membership']['branch_id'] = $this->user->originBranchId();
        $this->logger->info(sprintf("New user: %s", json_encode($user, JSON_PARTIAL_OUTPUT_ON_ERROR)));

        // Save to users collection
        if (!$dryRun) {
            $this->logger->info("Saving user...");
            $this->userRepository->legacySaveOrFail($user);
        }

        // There's no API and only a couple of samples are on the membership service,
        // so a manual fix in the memberships DB is required after processing.
        $this->logger->warning(
            'This user is on the membership service, a manual update is required to set the branch ID!'
        );
    }

    private function bootstrapCsv(): Writer
    {
        $this->csvReportLocation = sprintf(ROOT . '/app/tmp/move-membership-%s.csv', time());

        $file = new \SplFileObject($this->csvReportLocation, 'a+');
        $writer = Writer::createFromFileObject($file);

        $line = [
            'Dry Run',
            'Status',
            'Origin Branch ID',
            'Origin Branch Namespace',
            'User ID',
            'User Membership ID',
        ];

        $writer->insertOne($line);
        return $writer;
    }

    private function writeSuccessToCsv(Writer $writer, bool $dryRun, User $user): void
    {
        $branch = $user->branch();

        $writer->insertOne([
            $dryRun,
            "Success",
            $user->originBranchId(),
            $branch->namespace(),
            $user->id(),
            $user->membership()->id(),
        ]);
    }

    private function writeErrorToCsv(Writer $writer, bool $dryRun, User $user): void
    {
        $branch = $user->branch();

        $writer->insertOne([
            $dryRun,
            "Error",
            $user->originBranchId(),
            $branch->namespace(),
            $user->id(),
            $user->membership()->id(),
        ]);
    }

    private function uploadReport(User $user, Reader $reader): void
    {
        $branch = $user->branch();

        $uploadPath = sprintf(
            '%s/branches/%s/scripts/%s',
            $branch->namespace(),
            $branch->id(),
            basename($this->csvReportLocation)
        );

        $this->cloudStorage->put($uploadPath, (string) $reader);
        $this->logger->info(sprintf('Report uploaded to: %s', $uploadPath));
    }

}
