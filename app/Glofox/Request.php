<?php

namespace Glofox;

use Glofox\Domain\Authentication\Token\TokenUserResolver;
use Glofox\Domain\Branches\Models\Branch;
use Glofox\Domain\Branches\Repositories\BranchesRepository;
use Glofox\Domain\Users\Models\User;
use Glofox\Exception as AuthorizationException;
use Glofox\Http\Requests\RequestFilterResolver;
use Glofox\Http\Requests\RequestIdGeneratorInterface;
use Glofox\Repositories\Search\Expressions\Shared\Id;
use Glofox\Validation\Rules\RulesAggregator;
use Illuminate\Contracts\Container\Container;
use Illuminate\Contracts\Validation\Factory as ValidationFactory;
use Illuminate\Contracts\Validation\ValidatesWhenResolved;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request as HttpRequest;
use Illuminate\Routing\Redirector;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Validation\ValidatesWhenResolvedTrait;
use Saritasa\Laravel\Validation\Rule;

class Request extends HttpRequest implements ValidatesWhenResolved
{
    use ValidatesWhenResolvedTrait;

    /**
     * The container instance.
     *
     * @var \Illuminate\Contracts\Container\Container
     */
    protected $container;

    /**
     * The redirector instance.
     *
     * @var \Illuminate\Routing\Redirector
     */
    protected $redirector;

    /**
     * The URI to redirect to if validation fails.
     *
     * @var string
     */
    protected $redirect;

    /**
     * The route to redirect to if validation fails.
     *
     * @var string
     */
    protected $redirectRoute;

    /**
     * The controller action to redirect to if validation fails.
     *
     * @var string
     */
    protected $redirectAction;

    /**
     * The key to be used for the view error bag.
     *
     * @var string
     */
    protected $errorBag = 'default';

    /**
     * The input keys that should not be flashed on redirect.
     *
     * @var array
     */
    protected $dontFlash = ['password', 'password_confirmation'];

    protected $preparedCakeRouteParams;

    private static ?string $currentRequestId = null;

    public function __construct(
        array $query = [],
        array $request = [],
        array $attributes = [],
        array $cookies = [],
        array $files = [],
        array $server = [],
        $content = null
    ) {
        parent::__construct($query, $request, $attributes, $cookies, $files, $server, $content);

        $generator = app()->make(RequestIdGeneratorInterface::class);
        $this->setupCurrentRequestId($generator);
    }

    /**
     * Get the proper failed validation response for the request.
     *
     * @param array $errors
     *
     * @return \Symfony\Component\HttpFoundation\Response
     */
    public function response(array $errors)
    {
        if ($this->expectsJson()) {
            return new JsonResponse($errors, 422);
        }

        return $this->redirector->to($this->getRedirectUrl())
            ->withInput($this->except($this->dontFlash))
            ->withErrors($errors, $this->errorBag);
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array
     */
    public function attributes()
    {
        return [];
    }

    /**
     * @return Collection
     */
    public function allow(): Collection
    {
        return new Collection();
    }

    /**
     * @return Collection
     */
    public function defaults(): Collection
    {
        return new Collection();
    }

    /**
     * Set the Redirector instance.
     *
     * @param \Illuminate\Routing\Redirector $redirector
     *
     * @return $this
     */
    public function setRedirector(Redirector $redirector)
    {
        $this->redirector = $redirector;

        return $this;
    }

    /**
     * Set the container implementation.
     *
     * @param \Illuminate\Contracts\Container\Container $container
     *
     * @return $this
     */
    public function setContainer(Container $container)
    {
        $this->container = $container;

        return $this;
    }

    /**
     * Alias for filteredInputWithDefaults.
     *
     * @return Collection
     */
    public function data(): Collection
    {
        $data = $this->filteredInputWithDefaults()
                     ->forget('url');

        return $data;
    }

    public function cakeRouteParams(): Collection
    {
        if ($this->preparedCakeRouteParams) {
            return $this->preparedCakeRouteParams;
        }

        $params = Collection::make(
            $this->parseParams()
        );

        $params->forget('named');
        $params->forget('controller');
        $params->forget('action');
        $params->forget('plugin');
        $params->forget('apiVersion');

        $this->preparedCakeRouteParams = $params;

        return $params;
    }

    /**
     * [getAuthenticationToken description].
     *
     * @return [type] [description]
     */
    public function getAuthenticationToken(): ?string
    {
        $tokenWithBearer = $this->header('authorization') ?: $this->header('Authorization');

        if (!$tokenWithBearer) {
            $tokenWithBearer = $this->data()->get('authToken');
        }

        $token = str_replace('Bearer ', '', $tokenWithBearer);

        return $token;
    }

    public function rules()
    {
        return [];
    }

    public function authorize()
    {
        return true;
    }

    public function filters(): Collection
    {
        $filters = $this->data()->get('filters') ?? [];

        return Collection::make($filters);
    }

    public function newFilterResolver(): RequestFilterResolver
    {
        return new RequestFilterResolver(
            $this->filters()
        );
    }

    public function includes()
    {
        return $this->data()->get('includes') ?? '';
    }

    public function currentBranch(): Branch
    {
        // Grab the parameter from the route
        $branchId = $this->cakeRouteParams()->get('branchId');

        /** @var BranchesRepository $branchesRepository */
        $branchesRepository = app()->make(BranchesRepository::class);

        return $branchesRepository->addCriteria(new Id($branchId))->first();
    }

    public function getCurrentRequestUuid(): string
    {
        return (string) self::$currentRequestId;
    }

    public function debugLevel(): int
    {
        return $this->header('x-glofox-debug-level', 0);
    }

    public function mobileAppVersion(): ?string
    {
        return $this->header(GLOFOX_APP_VERSION_HEADER);
    }

    public function isSentryEnabled(): bool
    {
        return (bool) $this->request->get('isSentryEnabled');
    }

    public function hasAuthorizationToken(): bool
    {
        return (bool) ($this->headers->has('authorization') ?? false);
    }

    public function loggedUser(): User
    {
        return app()->make(TokenUserResolver::class)->user();
    }

    /**
     * Get the validator instance for the request.
     *
     * @return \Illuminate\Contracts\Validation\Validator
     */
    protected function getValidatorInstance()
    {
        $factory = $this->container->make('validator');

        if (method_exists($this, 'validator')) {
            $validator = $this->container->call([$this, 'validator'], compact('factory'));
        } else {
            $validator = $this->createDefaultValidator($factory);
        }

        if (method_exists($this, 'withValidator')) {
            $this->withValidator($validator);
        }

        return $validator;
    }

    /**
     * Create the default validator instance.
     *
     * @param \Illuminate\Contracts\Validation\Factory $factory
     *
     * @return \Illuminate\Contracts\Validation\Validator
     */
    protected function createDefaultValidator(ValidationFactory $factory)
    {
        $rules = $this->container->call([$this, 'rules']);

        return $factory->make(
            $this->validationData(),
            $rules,
            $this->messages(),
            $this->attributes()
        );
    }

    /**
     * @param RulesAggregator $aggregator
     *
     * @return RulesAggregator
     */
    protected function importRulesFromAggregator(RulesAggregator $aggregator)
    {
        $fields = $aggregator->rules()->keys();

        // I'm testing this architectural approach of how we can reduce code duplication and
        // ensure consistency across model validation and request validation. Let's
        // apply the defaults & allowed rules based on the chosen aggregator.
        foreach ($fields as $field) {
            // If there's a a default set for this field, we're going to make it optional by
            // reusing the rule that was already in the aggregator, but adding the rule
            // ->sometimes() to it.
            if ($this->defaults()->isNotEmpty() && $this->defaults()->keys()->contains($field)) {
                $rules = $aggregator->rules()->get($field)->toArray();
                $rules = Collection::make($rules)->reject('required')->toArray();

                /** @var Rule[] $rules */
                $newOptionalRule = Rule::sometimes();

                foreach ($rules as $rule) {
                    $newOptionalRule = $newOptionalRule->custom($rule);
                }

                $aggregator->rules()->forget($field);
                $aggregator->rules()->put($field, $newOptionalRule);

                continue;
            }

            // If there's no defaults and the field is NOT allowed in this request, let's remove
            // the rule from aggregator as there's no reason to validate it.
            if ($this->allow()->isNotEmpty() && !$this->allow()->contains($field)) {
                $optionalRule = $aggregator->rules()->forget($field);
                continue;
            }
        }

        return $aggregator;
    }

    /**
     * Get data to be validated from the request.
     *
     * @return array
     */
    protected function validationData()
    {
        return $this->data()->toArray();
    }

    /**
     * Handle a failed validation attempt.
     *
     * @param \Illuminate\Contracts\Validation\Validator $validator
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    protected function failedValidation(Validator $validator)
    {
        throw new \Illuminate\Validation\ValidationException(
            $validator
        );
    }

    /**
     * Format the errors from the given Validator instance.
     *
     * @param \Illuminate\Contracts\Validation\Validator $validator
     *
     * @return array
     */
    protected function formatErrors(Validator $validator)
    {
        return $validator->getMessageBag()->toArray();
    }

    /**
     * Get the URL to redirect to on a validation error.
     *
     * @return string
     */
    protected function getRedirectUrl()
    {
        $url = $this->redirector->getUrlGenerator();

        if ($this->redirect) {
            return $url->to($this->redirect);
        } elseif ($this->redirectRoute) {
            return $url->route($this->redirectRoute);
        } elseif ($this->redirectAction) {
            return $url->action($this->redirectAction);
        }

        return $url->previous();
    }

    /**
     * Determine if the request passes the authorization check.
     *
     * @return bool
     */
    protected function passesAuthorization()
    {
        if (method_exists($this, 'authorize')) {
            return $this->container->call([$this, 'authorize']);
        }

        return false;
    }

    /**
     * Handle a failed authorization attempt.
     *
     * @throws Exception
     */
    protected function failedAuthorization()
    {
        throw new AuthorizationException('This action is unauthorized.');
    }

    private function setupCurrentRequestId(RequestIdGeneratorInterface $generator): void
    {
        $isCurrentRequestIdAlreadySet = (bool) self::$currentRequestId;

        if ($isCurrentRequestIdAlreadySet) {
            return;
        }

        self::$currentRequestId = $generator->getGeneratedUuid();

        // Set raw headers, as cakephp often uses it bypassing the headers in this class.
        $_SERVER['HTTP_X_REQUEST_ID'] = self::$currentRequestId;

        // Set this illuminate execution code
        $this->headers->set('X-Request-Id', self::$currentRequestId);
    }

    /**
     * Get a subset containing the provided keys with values from the input data
     * without adding NULL defaults.
     *
     * @param array|mixed $keys
     *
     * @return array
     */
    private function filterWithoutDefaults($keys)
    {
        $keys = \is_array($keys) ? $keys : \func_get_args();

        $results = [];

        $input = $this->input();

        foreach ($keys as $key) {
            if (Arr::has($input, $key)) {
                Arr::set($results, $key, data_get($input, $key));
            }
        }

        return $results;
    }

    private function parseParams(): array
    {
        $path = $this->path();

        if ('/' === $path) {
            $path = $this->cakePath();
        }

        return \Router::parse($path);
    }

    private function cakePath(): string
    {
        $cakePath = \Router::getRequest($current = true)->url;

        return trim($cakePath, '/');
    }

    /**
     * Includes defaults() and auto-filters according to allow().
     *
     * @return Collection
     */
    private function filteredInputWithDefaults(): Collection
    {
        if ($this->allow()->isNotEmpty()) {
            $allowed = $this->allow()->toArray();

            $filtered = $this->filterWithoutDefaults($allowed);
            $filtered = Collection::make($filtered);

            $final = $this->defaults()->merge($filtered);

            // We do it to ensure JSON objects are returned as Arrays, otherwise
            // we will end up with a structure that CakePHP can't handle
            return collect(json_decode($final->toJson(), true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR));
        }

        $all = Collection::make($this->all());
        $final = $this->defaults()->merge($all);

        // We do it to ensure JSON objects are returned as Arrays, otherwise
        // we will end up with a structure that CakePHP can't handle
        return collect(json_decode($final->toJson(), true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR));
    }

    public function getUrlPathWithQueryString(): string
    {
        $qs = [];

        foreach ($this->query->all() as $k => $v) {
            $qs[] = sprintf("%s=%s", $k, $v);
        }

        $query = implode('&', $qs);

        $question = $this->getPathInfo() === '/' ? '/?' : '?';

        return $query ? $this->getPathInfo() . $question . $query : '';
    }
}
