<?php

use Glofox\Domain\Clients\Repositories\ClientsRepository;
use Glofox\Domain\Clients\Search\Expressions\ClientNamespace;

class AddBundleConfigurationShell extends AppShell
{
    private $clientsRepository;

    private $clientModel;

    public function __construct($stdout = null, $stderr = null, $stdin = null)
    {
        parent::__construct($stdout, $stderr, $stdin);

        $this->clientsRepository = app()->make(ClientsRepository::class);
        $this->clientModel = \ClassRegistry::init(Client::class);
    }

    public function getOptionParser()
    {
        $parser = parent::getOptionParser();
        $parser->addOption('namespace', [
            'short' => 'n',
            'help' => 'The namespace into which the configuration will be added',
            'default' => '',
        ]);

        $parser->addOption('is-multi-location', [
            'short' => 'i',
            'help' => 'Is multi location',
            'boolean' => false,
        ]);

        return $parser;
    }

    public function main()
    {
        $namespace = $this->params['namespace'] ?? null;
        $isMultiLocation = $this->params['is-multi-location'] ?? false;

        if (empty($namespace)) {
            $this->log('Please specify the namespace in arguments', LOG_ERR);

            return;
        }

        $this->log(sprintf('Starting creation of bundle configuration for client on namespace %s', $namespace), LOG_INFO);

        $client = $this->clientsRepository
            ->startCriteria()
            ->addCriteria(new ClientNamespace($namespace))
            ->firstOrFail();

        $bundles = $client['bundles'] ?? [];
        array_push($bundles, '_' . $namespace);
        $client['bundles'] = array_unique($bundles);

        if ($isMultiLocation) {
            $client['bundle_config'] = [
                'billing_plans' => [
                    [
                        'code' => 'silver',
                        'features' => [
                            'classes' => true,
                            'booking' => true,
                            'news' => true,
                            'trainers' => true,
                            'facilities' => true,
                            'club_info' => true,
                            'courses' => true,
                            'store' => false,
                            'memberships' => true,
                            'reports' => true,
                            'report_builder' => false,
                            'push_notifications' => true,
                            'bundle_app' => true,
                            'custom_app' => false,
                            'billing' => true,
                        ],
                        'icon' => 'https://s3-eu-west-1.amazonaws.com/glofox/platform/silver-plan.png',
                        'theme' => '#ff039be4',
                    ],
                    [
                        'code' => 'gold',
                        'features' => [
                            'classes' => true,
                            'booking' => true,
                            'news' => true,
                            'trainers' => true,
                            'facilities' => true,
                            'club_info' => true,
                            'courses' => true,
                            'store' => true,
                            'memberships' => true,
                            'reports' => true,
                            'report_builder' => true,
                            'push_notifications' => true,
                            'bundle_app' => true,
                            'custom_app' => false,
                            'billing' => true,
                        ],
                        'icon' => 'https://s3-eu-west-1.amazonaws.com/glofox/platform/gold-plan.png',
                        'theme' => '#ff039be4',
                    ],
                    [
                        'code' => 'platinum',
                        'features' => [
                            'classes' => true,
                            'booking' => true,
                            'news' => true,
                            'trainers' => true,
                            'facilities' => true,
                            'club_info' => true,
                            'courses' => true,
                            'store' => true,
                            'memberships' => true,
                            'reports' => true,
                            'report_builder' => true,
                            'push_notifications' => true,
                            'bundle_app' => false,
                            'custom_app' => true,
                            'billing' => true,
                        ],
                        'icon' => 'https://s3-eu-west-1.amazonaws.com/glofox/platform/platinum-plan.png',
                        'theme' => '#ff039be4',
                    ],
                ],
                'code' => '_' . $namespace,
                'colors' => [
                    'theme' => '373c74',
                ],
                'master' => false,
                'trial_days' => '2',
            ];
        }

        $this->clientModel->save($client);

        $this->log(sprintf('Finished creation of bundle configuration for client on namespace %s', $namespace), LOG_INFO);
    }
}
