<?php

declare(strict_types=1);

namespace CakeTestCases\Glofox\Domain\Memberships\Services\Validate;

use Glofox\Domain\Memberships\Models\ValidateRestrictedMembershipPurchasePayload;
use Glofox\Domain\Memberships\Services\MembershipsClient;
use Glofox\Domain\Memberships\Services\Validate\ValidateRestrictedMembershipPurchaseViaMembershipsService;
use Mockery\MockInterface;

\App::import('Test/Case', 'GlofoxTestCase');

class ValidateRestrictedMembershipPurchaseViaMembershipsServiceTest extends \GlofoxTestCase
{
    public $fixtures = [];
    private MockInterface $membershipsClient;

    public function setUp(): void
    {
        parent::setUp();
        $this->membershipsClient = \Mockery::mock(MembershipsClient::class);
    }
    public function testValidatorHasAValidResponse(): void
    {
        $branchId = 'mock-branch-id';
        $this->membershipsClient
            ->shouldReceive('validateRestrictedMembershipPurchase')
            ->andReturn([
                'valid' => 'true',
            ]);
        app()->instance(MembershipsClient::class, $this->membershipsClient);
        $membershipServiceValidator = app()->make(ValidateRestrictedMembershipPurchaseViaMembershipsService::class);
        $payload = new ValidateRestrictedMembershipPurchasePayload(
            $branchId,
            'mock-user-id',
            'mock-membership-id',
            'mock-plan-code',
            'cash',
            5,
            10,
            1_640_995_200,
            'mock-sold-by-user-id',
            ['mock-discount-id'],
            'Australia/Sydney',
            false,
            true
        );
        $response = $membershipServiceValidator->execute($payload);

        self::assertTrue($response->isValid());
    }
}
