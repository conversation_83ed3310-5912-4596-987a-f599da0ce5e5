<?php

namespace Glofox\Payments\Providers\Gateway\WebHooks\Handlers;

use Glofox\Payments\Contracts\PaymentProviderContract;
use Glofox\Payments\Entities\WebHook\Models\WebhookEvent;
use Glofox\Payments\Entities\WebHook\Models\WebhookEventFactory;
use Illuminate\Support\Collection;
use Psr\Log\LoggerInterface;

/**
 * Class EventHandler.
 */
abstract class EventHandler
{
    /** @var Collection */
    protected $eventPayload;

    /** @var Collection */
    protected $eventPayloadPSP;

    /** @var WebhookEvent */
    protected $webhookEvent;

    /** @var PaymentProviderContract */
    protected $paymentHandler;

    /** @var LoggerInterface */
    private $logger;

    /**
     * EventHandler constructor.
     *
     * @param $eventPayload
     * @param PaymentProviderContract $paymentHandler
     */
    public function __construct(Collection $eventPayload, PaymentProviderContract $paymentHandler)
    {
        $this->eventPayload = $eventPayload;
        $this->eventPayloadPSP = Collection::make(json_decode($eventPayload->get('psp_response'), true, 512, JSON_PARTIAL_OUTPUT_ON_ERROR));

        $this->webhookEvent = app()
            ->make(WebhookEventFactory::class)
            ->makeFromGatewayWebhookPayload($eventPayload);

        $this->paymentHandler = $paymentHandler;
        $this->logger = app()->make(LoggerInterface::class);

        // force int casting for webhooks coming from the payments gateway
        if ($this->eventPayload->has('id')) {
            $this->eventPayload->put('id', (int) $this->eventPayload->get('id'));
        }
    }

    /**
     * @return int response status
     */
    abstract public function handle(): int;

    /**
     * @return bool
     */
    protected function checkAlreadyProcessed()
    {
        $dbEvent = app()->make(\StripeCharge::class)->findByEventId($this->eventPayload->get('id'));
        if (!empty($dbEvent)) {
            $this->logger->warning('This event has already been processed event_id: ' . $this->eventPayload->get('id'));

            return true;
        }

        return false;
    }
}
