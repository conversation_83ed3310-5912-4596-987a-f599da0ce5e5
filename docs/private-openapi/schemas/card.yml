type: array
items:
  type: object
  properties:
    _id:
      type: object
      properties:
        $oid:
          type: string
    user_id:
      type: string
    branch_id:
      type: string
    brand:
      type: string
    last_four:
      type: integer
    expiry_month:
      type: integer
    expiry_year:
      type: integer
    zip_code:
      type: string
    zip_code_valid:
      type: boolean
    zip_code_check_status:
      type: string
    card_provider_id:
      type: string
    payment_method_user_id:
      type: string
    payment_method_id:
      type: string
    created:
      type: object
      properties:
        $date:
          type: string
          format: date-time
    modified:
      type: object
      properties:
        $date:
          type: string
          format: date-time
